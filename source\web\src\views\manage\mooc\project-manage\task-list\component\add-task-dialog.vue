<template>
  <el-dialog :visible.sync="visible" width="432px" title="添加任务" :show-close="true" :close-on-click-modal="false"
    custom-class="tasks-type-dialog none-border-dialog" :before-close="cancel">
    <div class="tasy-type-box">
      <p class="tips" v-show="info.task_name">{{ info.label }}<span>{{ info.task_name }}</span></p>
      <div class="content">
        <div class="item-box" @click="choseType(1)">
          <img src="@/assets/mooc-img/line-task.png" />
          <p>线上课程</p>
        </div>
        <div class="item-box" @click="choseType(8)">
          <img src="@/assets/mooc-img/line-task.png" />
          <p>问卷</p>
        </div>
        <div class="item-box" @click="choseType(2)">
          <img src="@/assets/mooc-img/exam-task-icon.png" />
          <p>考试</p>
        </div>
        <div class="item-box" @click="choseType(3)">
          <img src="@/assets/mooc-img/lx-task.png" />
          <p>练习</p>
        </div>
        <div class="item-box" @click="choseType(4)">
          <img src="@/assets/mooc-img/outlink-task.png" />
          <p>外部链接</p>
        </div>
        <div class="item-box" @click="choseType(5)">
          <img src="@/assets/mooc-img/materal-task.png" />
          <p>课程素材</p>
        </div>
        <div class="item-box" @click="choseType(6)">
          <img src="@/assets/mooc-img/work-task.png" />
          <p>作业</p>
        </div>
        <div class="item-box" @click="choseType(7)">
          <img src="@/assets/mooc-img/third-party-task.png" />
          <p>第三方任务</p>
        </div>
      </div>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentNode: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      info: {}
    }
  },
  computed: {

  },
  mounted() {
    const { task_type, task_name } = this.currentNode
    const label = task_type === 'stage' ? '归属阶段：' : '归属任务组：'
    this.info = {
      label,
      task_name
    }
  },
  methods: {
    choseType(type) {
      this.$emit('receiveType', type)
      this.$emit('update:visible', false)
    },
    cancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.tasks-type-dialog {
  .tasy-type-box {
    .tips {
      text-align: left;
      margin-bottom: 32px;
      color: rgba(0, 0, 0, 0.6)
    }

    .content {
      display: flex;
      flex-wrap: wrap;
      padding-left: -20px;

      .item-box {
        width: 56px;
        margin-left: 20px;
        margin-bottom: 16px;
        text-align: center;
        cursor: pointer;

        img {
          width: 32px;
          height: 32px;
        }

        p {
          text-align: center;
          margin-top: 10px;
          font-size: 12px;
          color: rgba(0, 0, 0, 1);
          font-weight: 400;
          height: 20px;
          line-height: 20px;
          white-space: nowrap
        }
      }
    }
  }
}
</style>
