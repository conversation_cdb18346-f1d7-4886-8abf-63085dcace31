<template>
  <div class="net-approve-container">
    <p class="title">网络课上传审核</p>
    <DirectlyUpload class="approve-directly-upload" :approveStatus="true"></DirectlyUpload>
  </div>
</template>
<script>
import DirectlyUpload from '../../user/netcourse/course-make/directly-upload'
export default {
  components: {
    DirectlyUpload
  },
  data() {
    return {
      
    }
  }
}
</script>
<style lang="less">
@import '~@/assets/css/graphic-common.less';
@import '~@/assets/css/ai-common.less';
@import '~@/assets/css/center.less';
.net-approve-container {
  background-color: #fff;
  margin-bottom: 50px;
  .title {
    font-size: 18px;
    font-weight: bold;
    padding: 20px 20px 0;
  }
  .approve-directly-upload {
    padding-bottom: 20px;
  }
}
</style>
