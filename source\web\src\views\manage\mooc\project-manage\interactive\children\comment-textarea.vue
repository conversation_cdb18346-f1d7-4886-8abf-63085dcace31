<template>
  <div class="comment-textarea">
    <div class="top">
      <el-image :src="avatarSrc" class="avatar">
        <div class="image-slot" slot="placeholder">
          <i class="el-icon-loading"></i>
        </div>
        <div class="error-avatar" slot="error">
          <img :src="avatar" alt="" />
        </div>
      </el-image>
      <textarea
        class="text-area"
        v-model="val"
        :placeholder="reply_staff_name ? `回复${reply_staff_name}：` : ''"
        @input="onInput"
      ></textarea>
      <span :class="['text-count', { 'text-red': count > 140 }]"
        >{{ count }}/140</span
      >
    </div>
    <div class="bottom">
      <el-button
        type="primary"
        size="small"
        @click="onComment"
        :disabled="val.length === 0 || count > 140"
        >发表{{ textType ? '评论' : '回复' }}</el-button
      >
    </div>
  </div>
</template>
<script>
import { getAvatar } from 'utils/tools'

export default {
  name: 'comment-textarea',
  data() {
    return {
      val: '',
      count: 0,
      xt: require('@/assets/mooc-img/comment/xt.png'),
      avatar: require('@/assets/mooc-img/comment/avatar.png')
    }
  },
  props: {
    // 评论还是回复
    textType: {
      type: String,
      default: ''
    },
    // 回复的目标
    reply_staff_name: {
      type: String,
      default: ''
    },
    // 当前登陆用户
    userName: {
      type: String,
      default: ''
    }
  },
  computed: {
    avatarSrc() {
      return this.userName
        ? this.userName === '小腾老师'
          ? this.xt
          : getAvatar(this.userName)
        : this.avatar
    }
  },
  methods: {
    onInput() {
      const arr = this.val.match(/[\u4e00-\u9fa5]/g)
      const len = (arr && arr.length) || 0
      this.count = len + Math.ceil((this.val.length - len) / 2)
    },
    onComment() {
      // const getNowDate = () => {
      //   let date = new Date()
      //   let year = date.getFullYear() // 年
      //   let month = date.getMonth() + 1 // 月
      //   let day = date.getDate() // 日
      //   let hour = date.getHours() // 时
      //   let minutes = date.getMinutes() // 分
      //   let seconds = date.getSeconds() //秒
      //   // 给一位数的数据前面加 “0”
      //   month = month <= 9 ? '0' + month : month
      //   day = day <= 9 ? '0' + day : day
      //   hour = hour <= 9 ? '0' + hour : hour
      //   minutes = minutes <= 9 ? '0' + minutes : minutes
      //   seconds = seconds <= 9 ? '0' + seconds : seconds
      //   return `${year}-${month}-${day} ${hour}:${minutes}:${seconds}`
      // }
      this.$emit('onHandleComment', {
        date: '刚刚',
        value: this.val,
        reply_staff_name: this.reply_staff_name
      })
      this.count = 0
      this.val = ''
    }
  }
}
</script>
<style lang="less" scoped>
.comment-textarea {
  .avatar {
    width: 48px;
    height: 48px;
    margin-right: 20px;
    border-radius: 24px;
    line-height: 48px;
    text-align: center;
    color: #999;
    img {
      width: 48px;
      height: 48px;
      line-height: 48px;
      border-radius: 24px;
    }
  }
  .top {
    display: flex;
    height: 100px;
    position: relative;
    .text-area {
      width: 100%;
      display: block;
      resize: vertical;
      height: 102px;
      padding: 12px;
      line-height: 1.5;
      box-sizing: border-box;
      background-color: #fff;
      border-radius: 4px;
      border: 1px solid rgb(239, 239, 239);
      resize: none;
      &:focus {
        outline: none;
        border-color: #3464e0;
      }
    }
    .text-area::-webkit-input-placeholder {
      /* WebKit browsers，webkit内核浏览器 */
      color: #999;
      font-size: 14px;
      font-family: 'PingFang SC', '-apple-system', 'Microsoft YaHei',
        'Helvetica Neue', Helvetica, 'BlinkMacSystemFont', 'tahoma', Arial,
        'Open Sans', 'Hiragino Sans GB', ' Heiti SC', 'WenQuanYi Micro Hei',
        sans-serif;
    }
    .text-area:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #999;
      font-size: 14px;
      font-family: 'PingFang SC', '-apple-system', 'Microsoft YaHei',
        'Helvetica Neue', Helvetica, 'BlinkMacSystemFont', 'tahoma', Arial,
        'Open Sans', 'Hiragino Sans GB', ' Heiti SC', 'WenQuanYi Micro Hei',
        sans-serif;
    }
    .text-area::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #999;
      font-size: 14px;
    }
    .text-area:-ms-input-placeholder {
      /* Internet Explorer 10+ */
      color: #999;
      font-size: 14px;
    }
    .text-count {
      position: absolute;
      color: #999;
      position: absolute;
      font-size: 14px;
      bottom: 10px;
      right: 12px;
    }
    .text-red {
      color: red;
    }
  }
  .bottom {
    margin-top: 12px;
    text-align: right;
    .el-button {
      width: 88px;
      border-radius: 3px;
      color: #fff;
      background-color: #0052d9;
      border-color: #0052d9;
    }
    .is-disabled {
      background-color: #a0cfff;
      border-color: #a0cfff;
    }
  }
}
</style>
