<template>
  <div class="manage-page">
    <div class="top-title">
      <span>配置列表</span>
    </div>
    <div class="content-top-wrap">
    </div>
    
    <emailList v-if="pageScreen.page === 'emit'"></emailList>
    <detailPage v-else-if="pageScreen.page === 'detailed'"></detailPage>
  </div>
</template>
<script>
import emailList from './emailList/index.vue'
import detailPage from './detailPage/index.vue'

export default {
  components: {
    emailList,
    detailPage
  },
  data () {
    return {
      // pageType: [
      //   { label: '邮件运营位', type: 'emit', id: '1' },
      //   { label: '详情运营位', type: 'detailed', id: '2' }
      // ],
      pageScreen: {
        page: ''
      }
    }
  },
  watch: {
  },
  computed: {
    actType() {
      return this.$route.query.act_type
    }
  },
  created() {
    if (['2', '5', '202'].includes(this.actType)) {
      this.pageScreen.page = 'detailed'
    } else if (this.actType === '31') {
      // 邮件
      this.pageScreen.page = 'emit'
    }
  },
  mounted () {
  },
  methods: {
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/graphic-manage.less';
.manage-page {
  padding: 4px 0;
  height: 100%;
  background: #fff;
  border-radius: 4px;
  opacity: 1;
  box-shadow: 0 0 8px 0 #eeeeeeff;
  .top-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f3f3f3ff;
    padding: 16px 20px;
    span {
      color: #000000e6;
      font-size: 24px;
      font-weight: 600;
    }
  }
  .content-top-wrap {
    margin-top: 20px;
    padding: 0 20px;
  }
}
</style>
