<template>
  <div class="work-source">
    <el-form :model="course_statement" :rules="rules" ref="workForm" label-width="124px">
      <el-radio-group v-model="course_statement.creation_source" @change="creationSourceChange">
        <el-radio :label="item.value" v-for="item in creation_source_Options" :key="item.value">{{item.label}}</el-radio>
      </el-radio-group>
      <div class="origin-warm" v-if="course_statement.creation_source === 0">(1)「创作组织」从拟定内容主题、提纲、访谈、收集素材到制作课件，一条龙完成。<br />(2) 内容创作人所属的最小组织单元，优先填"组"。</div>
      <div class="origin-warm" v-if="course_statement.creation_source === 3">
          符合以下任一的共创模式：
          <p>（1）「创作组织」拟定主题、提供提纲和素材、组织立项，邀约创作者，由创作者主导内容生产，「创作组织」参与辅助和赋能。创作者拟定主题、提供提纲和素材。</p>
          （2）「创作组织」辅助立项，由创作者主导内容制作，「创作组织」参与辅助和赋能。
      </div>

      <div class="creation_source_sub_content">
        <div v-if="course_statement.creation_source === 0" key="PGC">
          <el-form-item label="创作组织" :rules="pgcCreationOrgRules" prop="pgc_creation_org" label-width="100px" key="pgc_creation_org">
            <sdc-unit-selector
              class="dep-selector"
              ref="pgcCreationOrgRef"
              v-model="course_statement.pgc_creation_org"
              multiple
              @change="validateField($event, 'pgc_creation_org')"
              placeholder="请选择创作组织"
            />
          </el-form-item>
        </div>
        <div v-if="course_statement.creation_source === 3" key="PUGC">
          <el-form-item label="创作组织" :rules="pugcCreationOrgRules" prop="pugc_creation_org" label-width="100px" key="pugc_creation_org">
            <sdc-unit-selector
              class="dep-selector"
              ref="pugcCreationOrgRef"
              v-model="course_statement.pugc_creation_org"
              multiple
              @change="validateField($event, 'pugc_creation_org')"
              placeholder="请选择创作组织"
            />
          </el-form-item>
          <el-form-item label="联合创建组织" label-width="100px" style="margin-bottom: 0px">
            <sdc-unit-selector
              class="dep-selector" 
              ref="pugcJointCreationRef"
              v-model="course_statement.pugc_joint_creation"
              multiple
              placeholder="请选择联合创建组织"
              @change="validateField($event, 'pugc_joint_creation')"
              />
          </el-form-item>
        </div>
        <div v-if="course_statement.creation_source === 2" class="ugc-content">
          <el-form-item label="是否原创" label-width="100px" prop="ugc_link_url">
            <el-radio-group v-model="course_statement.ugc_is_original" @change="ugcOriginalChange"> 
              <el-radio :label="1">原创</el-radio>
              <el-radio :label="0">转载</el-radio>
            </el-radio-group>
            <el-input style="width: 300px;margin-left: 25px;" v-if="course_statement.ugc_is_original === 0" v-model="course_statement.ugc_link_url" placeholder="请输入原文链接" clearable></el-input>
          </el-form-item>
        </div>
        <div v-if="course_statement.creation_source === 1" key="OGC" class="ogc-content">
          <el-form-item label="供应商名称" :rules="supplierNameRules" prop="ogc_supplier_name" label-width="100px" class="course-texTarea-input">
            <el-input 
              v-model.trim="course_statement.ogc_supplier_name" 
              placeholder="请填写供应商名称"
              clearable>
            </el-input>
            <span class="custom-el-input-count">{{handleValidor(course_statement.ogc_supplier_name, 100, '4')}}/100</span>
          </el-form-item>
          <el-form-item label="采购组织" :rules="purchaseOrgRules" prop="ogc_purchase_org" label-width="100px" key="ogc_purchase_org">
            <sdc-unit-selector
              class="dep-selector" 
              ref="purchaseOrgRef"
              v-model="course_statement.ogc_purchase_org"
              multiple
              @change="validateField($event, 'ogc_purchase_org')"
              placeholder="请选择采购组织"
              />
          </el-form-item>
          <el-form-item label="采购方式" label-width="100px">
            <el-radio-group v-model="course_statement.ogc_purchase_type">
              <el-radio :label="item.value" v-for="item in purchase_type_Options" :key="item.value">{{item.label}}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="外部讲师" label-width="100px">
            <el-input v-model="course_statement.ogc_out_teachers" placeholder="请输入外部人员名称，用多个；隔开" clearable></el-input>
          </el-form-item>
          <el-form-item label="采购成本" prop="ogc_purchase_amount" label-width="100px">
            <el-input-number class="ogc_purchase_amount" v-model="course_statement.ogc_purchase_amount" label="请输入采购成本"></el-input-number> 元
          </el-form-item>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
const URL_EXP = /^(http|https):\/\/.*$/
export default {
  props: {
    baseInfo: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    baseInfo: {
      handler(newVal) {
        if (newVal) {
          this.setBaseInfo(newVal)
        }
      }
    }
  },
  data() {
    return {
      rules: {
        ugc_link_url: [
          { required: true, trigger: 'blur', validator: this.validUgcUrl }
        ]
      },
      creation_source_Options: [
        { label: 'PGC-来自组织闭环创作', value: 0 },
        { label: 'PUGC-由组织和创作者合作创作', value: 3 },
        { label: 'UGC-来自个人创作', value: 2 },
        { label: 'OGC-来自外部采购', value: 1 }
      ],
      purchase_type_Options: [
        { label: '个人按需购买', value: 0 },
        { label: '公司统一采购', value: 1 },
        { label: '账号采购', value: 2 }
      ],
      course_statement: {
        parent_content_act_type: null, // 父内容类型
        parent_content_id: '', // 父内容ID
        creation_source: 2, // 创作来源
        pgc_creation_org: '', // PGC创作组织
        pgc_creation_dept_id: '', // PGC创作组织id
        pgc_creation_dept_name: '', // PGC创作组织名称
        pgc_joint_creation: '', // PGC联合创作组织
        // pugc
        pugc_creation_org: '', // PUGC创作组织
        pugc_joint_creation: '', // PUGC联合创作组织
        pugc_creation_dept_id: '', // PUGC创作组织id
        pugc_creation_dept_name: '', // PUGC创作组织名称
        pugc_joint_creation_id: '', // PUGC联合创作组织id
        pugc_joint_creation_name: '', // PUGC联合创作组织名称
        // UGC
        ugc_is_original: 1, // UGC是否原创
        ugc_link_url: '', // UGC链接
        // OGC
        ogc_supplier_name: '', // 供应商名称
        ogc_purchase_org: '', // 采购组织
        ogc_out_teachers: '', // 外部讲师
        ogc_purchase_type: null, // 采购方式
        ogc_purchase_amount: undefined, // 采购成本
        ogc_purchase_org_name: ''
      },
      isEdit: false
    }
  },
  computed: {
    pgcCreationOrgRules() {
      return { required: this.course_statement.creation_source === 0, trigger: 'blur', validator: this.validPgcCreationOrg }
    },
    // PUGC创作组织校验规则
    pugcCreationOrgRules() {
      return { required: this.course_statement.creation_source === 3, trigger: 'blur', validator: this.validPugcCreationOrg }
    },
    supplierNameRules() {
      return { required: this.course_statement.creation_source === 1, message: '请输入供应商名称', trigger: 'blur' }
    },
    purchaseOrgRules() {
      return { required: this.course_statement.creation_source === 1, message: '请选择采购组织', trigger: 'blur' }
    }
  },
  methods: {
    ugcOriginalChange(val) {
      if (val === 1) {
        this.$refs['workForm'].clearValidate(['ugc_link_url'])
      }
    },
    // 手动检验字段
    validateField(value, file) {
      if (file !== 'pgc_joint_creation' || file !== 'pugc_joint_creation') {
        this.$refs['workForm'].validateField(`${file}`)
      }
      if (['project_creator', 'developer', 'pgc_creation_org', 'pgc_joint_creation', 'pugc_creation_org', 'pugc_joint_creation', 'ogc_purchase_org'].includes(file)) {
        this.course_statement[file] = Array.isArray(value) ? value : [value]
      }
      // 处理pgc，创作组织
      if (file === 'pgc_creation_org') {
        let { idList, nameList } = this.parseOrg(value)
        this.course_statement.pgc_creation_dept_id = idList
        this.course_statement.pgc_creation_dept_name = nameList
      }
      // 处理pugc，创作组织
      if (file === 'pugc_creation_org') {
        let { idList, nameList } = this.parseOrg(value)
        this.course_statement.pugc_creation_dept_id = idList
        this.course_statement.pugc_creation_dept_name = nameList
      }
      if (file === 'pugc_joint_creation') {
        let { idList, nameList } = this.parseOrg(value)
        this.course_statement.pugc_joint_creation_id = idList
        this.course_statement.pugc_joint_creation_name = nameList
      }
      if (file === 'ogc_purchase_org') {
        this.course_statement.ogc_purchase_org_name = value?.UnitFullName
      }
    },
    // 校验PGC创作组织 只能单选，但是要兼容旧数据回显
    validPgcCreationOrg(rule, value, callback) {
      if (!this.course_statement.pgc_creation_org.length) {
        return callback(new Error('请选择创作组织'))
      } else if (this.course_statement.pgc_creation_org.length > 1) {
        return callback(new Error('创作组织仅能选择一个组织'))
      } else {
        callback()
      }
    },
    // 校验PUGC创作组织 只能单选，但是要兼容旧数据回显
    validPugcCreationOrg(rule, value, callback) {
      if (!this.course_statement.pugc_creation_org.length) {
        return callback(new Error('请选择创作组织'))
      } else if (this.course_statement.pugc_creation_org.length > 1) {
        return callback(new Error('创作组织仅能选择一个组织'))
      } else {
        callback()
      }
    },
    validUgcUrl(rule, value, callback) {
      if (this.course_statement.ugc_is_original === 0 && !value) {
        return callback(new Error('请输入原文链接'))
      } else if (value && !URL_EXP.test(value)) {
        callback(new Error('请输入正确格式的地址，以http或者https开头'))
      } else {
        callback()
      }
    },
    handleValidor(value, num, type) {
      if (value) {
        const china = value.match(/[\u4e00-\u9fa5]/g)
        const zhCount = china && china.join('').length
        const enCount = Math.ceil((value.length - zhCount) / 2)
        const total = zhCount + enCount
        if (total > num) {
          switch (type) {
            case '1':
              this.course_statement.course_title = value.slice(0, -1)
              break
            case '3':
              this.course_statement.course_desc = value.slice(0, -1)
              break
            case '4':
              this.course_statement.ogc_supplier_name = value.slice(0, -1)
              break
            default:
              break
          }
        }
        return zhCount + enCount 
      }
      return 0
    },
    // 切换"创作来源时"，清除所属分类下的表单校验 编辑时逻辑需要完善
    creationSourceChange(value) {
      switch (value) {
        case 0:
          this.initPUGC()
          this.initOGC()
          this.initUGC()
          break
        case 3:
          this.initPGC()
          this.initOGC()
          this.initUGC()
          break
        case 2:
          this.initPGC()
          this.initPUGC()
          this.initOGC()
          this.initUGC()
          break
        case 1:
          this.initPGC()
          this.initPUGC()
          this.initUGC()
          break
        default:
          break
      }
      this.$refs['workForm'].clearValidate(['pgc_creation_org', 'pugc_creation_org', 'ogc_supplier_name', 'ogc_purchase_org'])
    },
    // 重置PGC
    initPGC() {
      this.course_statement.pgc_creation_org = ''
      this.course_statement.pgc_joint_creation = ''
    },
    // 重置PUGC
    initPUGC() {
      this.course_statement.pugc_creation_org = ''
      this.course_statement.pugc_joint_creation = ''
    },
    // 重置OGC
    initOGC() {
      this.course_statement.ogc_supplier_name = ''
      this.course_statement.ogc_purchase_org = ''
      this.course_statement.ogc_purchase_type = null
      this.course_statement.ogc_out_teachers = ''
      this.course_statement.ogc_purchase_amount = undefined
    },
    initUGC() {
      this.course_statement.ugc_is_original = 1
      this.course_statement.ugc_link_url = ''
    },
    getWorkFormInfo() {
      let obj = {}
      this.$refs['workForm'].validate((valid) => {
        if (valid) {
          // 创建一个新的对象，不直接修改原始数据
          const formattedData = {
            ...this.course_statement,
            pgc_creation_org: this.course_statement.pgc_creation_org ? JSON.stringify(this.course_statement.pgc_creation_org) : '',
            pugc_creation_org: this.course_statement.pugc_creation_org ? JSON.stringify(this.course_statement.pugc_creation_org) : '',
            pugc_joint_creation: this.course_statement.pugc_joint_creation ? JSON.stringify(this.course_statement.pugc_joint_creation) : '',
            ogc_purchase_org: this.course_statement.ogc_purchase_org ? JSON.stringify(this.course_statement.ogc_purchase_org) : '',
            ogc_out_teachers: this.course_statement.ogc_out_teachers ? JSON.stringify(this.course_statement.ogc_out_teachers.split(';').map(item => ({ StaffName: item }))) : ''
          }
          
          obj = {
            info: formattedData,
            isPass: true
          }
        } else {
          obj = {
            isPass: false
          }
        }
      })
      return obj
    },
    convertStaffData(data) {
      if (typeof data !== 'string') {
        return data
      }

      try {
        const parsed = JSON.parse(data)
        if (Array.isArray(parsed)) {
          const names = parsed
            .map(item => item.StaffName)
            .filter(name => name != null)
            .map(String)
          return names.join(';')
        }
      } catch (e) {
        // JSON解析失败，继续处理普通字符串
      }

      // 处理普通字符串，移除多余空格（可选）
      return data.replace(/\s*;\s*/g, ';').trim()
    },
    setBaseInfo(newVal) {
      let useData = JSON.parse(JSON.stringify(newVal))
      if (!useData?.course_statement) return

      // 设置创作来源
      this.course_statement.creation_source = useData.course_statement.creation_source !== undefined ? 
        useData.course_statement.creation_source : 2
      
      // 确保在nextTick中处理所有数据
      this.$nextTick(() => {
        // 处理UGC相关数据
        this.course_statement.ugc_is_original = useData.course_statement.ugc_is_original ? 1 : 0
        this.course_statement.ugc_link_url = useData.course_statement.ugc_link_url || ''
        
        // 处理PGC创作组织数据
        if (useData.course_statement.pgc_creation_org) {
          try {
            let pgcOrg = useData.course_statement.pgc_creation_org
            if (typeof pgcOrg === 'string') {
              pgcOrg = JSON.parse(pgcOrg)
            }
            // 确保是数组格式
            pgcOrg = Array.isArray(pgcOrg) ? pgcOrg : [pgcOrg]
            this.course_statement.pgc_creation_org = pgcOrg
            this.course_statement.pgc_creation_dept_id = useData.course_statement.pgc_creation_dept_id
            this.course_statement.pgc_creation_dept_name = useData.course_statement.pgc_creation_dept_name
            
            if (this.$refs.pgcCreationOrgRef) {
              this.$refs.pgcCreationOrgRef.setSelected(pgcOrg)
            }
          } catch (e) {
            console.error('Error parsing pgc_creation_org:', e)
          }
        }

        // 处理PUGC创作组织数据
        if (useData.course_statement.pugc_creation_org) {
          try {
            let pugcOrg = useData.course_statement.pugc_creation_org
            if (typeof pugcOrg === 'string') {
              pugcOrg = JSON.parse(pugcOrg)
            }
            // 确保是数组格式
            pugcOrg = Array.isArray(pugcOrg) ? pugcOrg : [pugcOrg]
            
            this.course_statement.pugc_creation_org = pugcOrg
            this.course_statement.pugc_creation_dept_id = useData.course_statement.pugc_creation_dept_id
            this.course_statement.pugc_creation_dept_name = useData.course_statement.pugc_creation_dept_name
            
            if (this.$refs.pugcCreationOrgRef) {
              this.$refs.pugcCreationOrgRef.setSelected(pugcOrg)
            }
          } catch (e) {
            console.error('Error parsing pugc_creation_org:', e)
          }
        }

        // 处理联合创作组织数据
        if (useData.course_statement.pugc_joint_creation) {
          try {
            let jointCreation = useData.course_statement.pugc_joint_creation
            if (typeof jointCreation === 'string') {
              jointCreation = JSON.parse(jointCreation)
            }
            // 确保是数组格式
            jointCreation = Array.isArray(jointCreation) ? jointCreation : [jointCreation]
            
            this.course_statement.pugc_joint_creation = jointCreation
            this.course_statement.pugc_joint_creation_id = useData.course_statement.pugc_joint_creation_id
            this.course_statement.pugc_joint_creation_name = useData.course_statement.pugc_joint_creation_name
            
            if (this.$refs.pugcJointCreationRef) {
              this.$refs.pugcJointCreationRef.setSelected(jointCreation)
            }
          } catch (e) {
            console.error('Error parsing pugc_joint_creation:', e)
          }
        }

        // 处理采购组织数据
        if (useData.course_statement.ogc_purchase_org) {
          try {
            let purchaseOrg = useData.course_statement.ogc_purchase_org
            if (typeof purchaseOrg === 'string') {
              purchaseOrg = JSON.parse(purchaseOrg)
            }
            // 确保是数组格式
            purchaseOrg = Array.isArray(purchaseOrg) ? purchaseOrg : [purchaseOrg]
            
            this.course_statement.ogc_purchase_org = purchaseOrg
            this.course_statement.ogc_purchase_org_name = useData.course_statement.ogc_purchase_org_name
            
            if (this.$refs.purchaseOrgRef) {
              this.$refs.purchaseOrgRef.setSelected(purchaseOrg)
            }
          } catch (e) {
            console.error('Error parsing ogc_purchase_org:', e)
          }
        }

        // 处理其他数据
        this.course_statement.ogc_supplier_name = useData.course_statement.ogc_supplier_name || ''
        this.course_statement.ogc_purchase_type = useData.course_statement.ogc_purchase_type
        this.course_statement.ogc_out_teachers = useData.course_statement.ogc_out_teachers ? 
          this.convertStaffData(useData.course_statement.ogc_out_teachers) : ''
        this.course_statement.ogc_purchase_amount = useData.course_statement.ogc_purchase_amount
      })
    },
    parseOrg(item) {
      let idList = item.map(item => item.UnitID).join(';')
      let nameList = item.map(item => item.UnitName).join(';')
      return {
        idList,
        nameList
      }
    }
  }
}
</script>
<style lang="less">
.sdc-selector .selector-container {
  height: 32px;
}
.sdc-selector .suffix-open {
  height: 32px;
  line-height: 32px;
}
.sdc-selector .suffix-open .el-button {
  height: 32px;
}
.el-input__inner {
  height: 32px;
  line-height: 32px;
}
.sdc-selector .selector-container--small {
  padding-top: 0;
  }
.sdc-selector .selector-container--normal {
  padding-top: 0;
}
</style>
<style scoped>
input[aria-hidden=true] {
  display: none !important;
}
</style>
<style lang="less" scoped>

.origin-warm {
  margin: 0 0 10px;
  font-size: 12px;
  line-height: 18px;
  color: #999999;
}

.creation_source_sub_content {
  background: #f9f9f9;
  border-radius: 4px;
  margin-top: 5px;
  padding: 8px 16px;
  width: 100%;
  .ugc-content {
    display: flex;
    align-items: center;
    :deep(.el-form-item__error) {
      margin-left: 150px;
    }
    :deep(.el-input__suffix) {
      .el-input__suffix-inner {
        .el-icon-circle-close.el-input__clear {
          line-height: 30px;
        }
      }
    }
  }
  .ogc-content {
    :deep(.el-input__suffix) {
      .el-input__suffix-inner {
        .el-icon-circle-close.el-input__clear {
          line-height: 30px;
        }
      }
    }
  }
  .ogc_purchase_amount.el-input-number,.human_cost.el-input-number {
    :deep(.el-input__inner) {
      height: 32px !important;
      line-height: 32px !important;
    }
    :deep(.el-input-number__decrease), :deep(.el-input-number__increase) {
      width: 40px !important;
      height: 30px !important;
      line-height: 30px !important;
      top: 4px;
    }
  }
  .course-texTarea-input {
    position: relative;
    :deep(.el-input) {
      .el-input__inner {
        padding-right: 74px;
      }
      .el-input__suffix {
        position: absolute;
        right: 50px;
        top: 50%;
        transform: translateY(-50%);
      }
    }
  }
  .custom-el-input-count {
    color: #ACACAC;
    position: absolute;
    font-size: 12px;
    right: 6px;
    line-height: 32px;
  }
  .el-form-item {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
