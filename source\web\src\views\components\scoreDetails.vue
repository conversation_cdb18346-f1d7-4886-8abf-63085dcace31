<template>
  <div class="review-content" v-if="currentInfo.has_mark || detailType === 'user'">
    <div class="det-title" v-if="markTypeTwo('1') && markTypeTwo('2')">
      <span class="text">评分详情</span>
      <span class="text-tips">（总分{{detailData.total_score || 0}}；合格分{{detailData.pass_score || 0}}）</span>
      <div class="definite">
        <p v-for="item in markDetail.mark_list" :key="item.mark_type">
          <span class="value-label">{{item.mark_name}}分数：</span>
          <span class="value-item">
            <span>({{item.mark_weight}}%)</span> <span class="num-r num-r1">{{item.score || '-'}}</span>
          </span>
        </p>
        <!-- <p>
          <span class="value-label">老师分数：</span>
          <span class="value-item">
            <span>({{markDetail.teacher_mark_weight}}%)</span> <span class="num-r num-r2">{{markDetail.teacher_score || '-'}}</span>
          </span>
        </p> -->
        <p>
          <span class="value-label">总成绩：</span>
          <span class="value-item">
            <span class="num-r num-r3">{{markDetail.total_score}}</span>
          </span>
        </p>
      </div>
      <div v-if="detailType !== 'user'">
        <p class="click-details" v-if="!markDetail.student_score" @click="handleClickStaff('staff')">点击查看学员互评详情</p>
        <p class="tips" v-if="!markDetail.student_score && markDetail.teacher_score"><i class="el-icon-warning-outline"></i> 互评分数为空时，先按照老师分数100%权重计算总成绩</p>
        <p class="tips" v-if="!markDetail.teacher_score && markDetail.student_score"><i class="el-icon-warning-outline"></i> 老师分数为空时，先按照互评分数100%权重计算总成绩</p>
      </div>
    </div>
    <div class="det-title" :class="!onlyStuents ? 'none-border' : ''  " v-if="markTypeTwo('1')">
      <p class="teacher-title">
        <span class="text">老师批阅</span> 
        <span v-if="detailType !== 'user'" class="click-details tal" @click="handleClickStaff('teacher')">查看批阅记录</span>
      </p>
      <!-- 老师批阅用户端 -->
      <div class="definite" v-if="detailType === 'user'">
        <div v-if="lastTeacherInfo?.updated_at">
          <p>
            <span style="padding: 0 4px;margin-left:0;" class="value-label num-r1 num-r">{{lastTeacherInfo.score}}</span>
            <span class="value-item ">{{lastTeacherInfo.updated_at}}</span>
          </p>
          <p>{{lastTeacherInfo.content || ''}}</p>
        </div>
        <div class="empty" v-else>
          <div class="empty-text">暂无老师批阅~</div>
        </div>
      </div>
      <!-- 老师批阅管理端 -->
      <div v-else>
        <el-form :inline="true" :model="form" :rules="rules">
          <el-form-item label="分数" prop="score" class="score-form-item">
            <el-input-number
            style="width:100px;"
            size="small" 
            v-model="form.score"
            controls-position="right" 
            :min="0" 
            :disabled="isReadOver"
            >
            </el-input-number>
          </el-form-item>
        </el-form>
        <span class="tips-staff"><i class="el-icon-warning-outline"></i>总分{{detailData.total_score}}，合格分{{detailData.pass_score}}；输入整数</span>
        <div class="input-inner">
          <p class="padding">作业评语</p>
          <el-input type="textarea" :disabled="isReadOver" :autosize="{ minRows: 10, maxRows: 10}" placeholder="请输入评语" v-model="form.content">
          </el-input>
        </div>
      </div>
    </div>
    <div class="det-title  none-border margin-r" v-if="markTypeTwo('2')">
      <span class="text">互评详情({{staffListRight.length}})</span>
      <p class="tips-staff" v-if="detailType !== 'user' && currentInfo.enabled_student_mark_anonymous" ><i class="el-icon-warning-outline"></i>学员侧为匿名互评，请注意信息保密</p>
      <div class="det-title-staff" v-if="staffListRight.length">
        <div class="definite" v-for="(item, index) in staffListRight" :key="index">
          <p>
            <span class="comment-label ">
              <span style="color:#000000E6">{{item.staff_name}}</span>
              <span class="num-r2 num-r">{{item.score}}</span>
            </span>
            <span class="value-item ">
              {{item.updated_at}}
            </span>
          </p>
          <p>
            {{item.content ? item.content : '暂无评语'}}
          </p>
        </div>
      </div>
      <div class="empty" v-else>
        <div class="empty-text">暂无互评详情~</div>
      </div>
    </div>

    <!-- 互评详情弹窗  作业批阅记录 -->
    <el-dialog class="details-evaluation" :title="clickType === 'staff' ? `互评详情（${total}）` : '作业批阅记录' " :visible.sync="dialogVisibleEvaluationDetail" width="30%">
      <div v-if="total > 0">
        <p class="tips" v-if="clickType === 'staff' && currentInfo.enabled_student_mark_anonymous"><i class="el-icon-warning-outline"></i>学员侧为匿名互评，请注意信息保密</p>
        <div class="infinite-list" @scroll="handleScroll">
          <div class="definite" v-for="(item, index) in  list" :key="index">
            <p>
              <span style="padding: 0 4px" class="value-label ">
                <span class="name">{{item.staff_name}}</span>
                <span class="num-r2 num-r" v-if="item.mark_type !== 3">{{item.score}}</span>
                <span class="back" v-else>退回作业</span>
              </span>
              <span class="value-item">
                {{item.updated_at}}
              </span>
            </p>
            <p>
              {{item.content ? item.content : '暂无评语'}}
            </p>
          </div>
          <p v-if="loading" class="load-ing">加载中...</p>
          <p v-if="noMore" class="load-ing">没有更多了</p>
        </div>
      </div>
      <div class="empty dialog-empty" v-else>
        <img class="empty-img" src="@/assets/img/empty.png" alt="" />
        <div class="empty-text">暂无数据~</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" type="primary" @click="dialogVisibleEvaluationDetail = false">知道了</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  getHomeworkMarkDetail,
  getDetailsMutualEvaluation,
  getTeacherReviewRecord,
  getTeacherMark
} from 'config/api.conf'
export default {
  props: {
    detailType: {
      type: String,
      default: 'user'
    },
    detailData: {
      type: Object,
      default: () => {}
    },
    currentInfo: {
      type: Object,
      default: () => ({})
    },
    isReadOver: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const scoreValid = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入分数'))
      } else {
        if (value > this.detailData.total_score) {
          callback(new Error('请输入小于总分的分数'))
        }
        callback()
      }
    }
    return {
      dialogVisibleEvaluationDetail: false,
      clickType: 'staff',
      staffList: [],
      teacherList: [],
      markDetail: {},
      lastTeacherInfo: {
        updated_at: '',
        score: 0,
        content: ''
      },
      loading: false,
      total: 0,
      size: 10,
      pageCur: 1,
      form: {
        score: 0,
        content: ''
      },
      staffListRight: [],
      scoreParams: {},
      rules: {
        score: [
          { required: true, validator: scoreValid, trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    // 老师批阅 1  学员互评 2
    markTypeTwo() {
      return (type) => {
        return this.detailData?.mark_type?.split(';').includes(type)
      }
    },
    // 评分方式仅勾选“学员互评”
    onlyStuents() {
      return this.detailData?.mark_type === '1'
    },
    list() {
      return this.clickType === 'staff' ? this.staffList : this.teacherList
    },
    noMore() {
      return this.list.length >= this.total
    },
    disabled() {
      return this.loading || this.noMore
    }
  },
  methods: {
    // 管理端
    initScoreData(data) {
      this.scoreParams = data
      this.detailType = 'manage'
      this.getScoreworkDetail()
      this.getrightList()
      this.getMark()
    },
    // 用户端
    initUserScore(data) {
      this.scoreParams = data
      this.detailType = 'user'
      this.getTeacherReview()
      this.getScoreworkDetail()
      this.getrightList()
    },
    handleScroll(e) {
      const { scrollTop, clientHeight, scrollHeight } = e.target
      if (
        Math.ceil(scrollTop) + clientHeight >= scrollHeight &&
        !this.disabled
      ) {
        this.loading = true
        this.pageCur++
        this.getDetailsMutualEvaluation(
          this.clickType === 'staff' ? '2' : '1;3'
        )
      }
    },
    // 评分详情
    getScoreworkDetail() {
      getHomeworkMarkDetail(this.detailType, this.scoreParams).then((res) => {
        this.markDetail = res
      })
    },
    // 互评详情
    getrightList() {
      let params = {
        homework_id: this.scoreParams.homework_id,
        record_id: this.scoreParams.record_id,
        mark_type: '2',
        page_size: 10,
        page_no: 1
      }
      getDetailsMutualEvaluation(this.detailType, params).then((res) => {
        this.staffListRight = res.records
      })
    },
    // 互拼详情弹窗
    // 互评详情列表 、老师批阅记录
    getDetailsMutualEvaluation(mark_type = '2') {
      let markType = mark_type
      let params = {
        homework_id: this.scoreParams.homework_id,
        record_id: this.scoreParams.record_id,
        mark_type: markType,
        page_size: 10,
        page_no: this.pageCur
      }
      getDetailsMutualEvaluation(this.detailType, params).then((res) => {
        this.total = res.total
        if (markType === '2') {
          this.staffList = this.staffList.concat(res.records)
        } else {
          this.teacherList = this.teacherList.concat(res.records)
        }
        this.loading = false
      })
    },
    // 我的作业需要
    // 最后一个老师批阅记录
    getTeacherReview() {
      let params = {
        homework_id: this.scoreParams.homework_id,
        record_id: this.scoreParams.record_id
      }
      getTeacherReviewRecord(this.detailType, params).then((res) => {
        this.lastTeacherInfo = res
      })
    },
    // 查看记录
    handleClickStaff(type) {
      this.staffList = []
      this.teacherList = []
      this.clickType = type
      this.pageCur = 1
      this.total = 0
      this.getDetailsMutualEvaluation(type === 'staff' ? '2' : '1;3')
      this.dialogVisibleEvaluationDetail = true
    },
    // 获取老师评价
    getMark() {
      let params = {
        homework_id: this.scoreParams.homework_id,
        record_id: this.scoreParams.record_id,
        markType: '1'
      }
      getTeacherMark(params).then((res) => {
        this.form.content = res?.content || ''
        this.form.score = res?.score || ''
      })
    }
  }
}
</script>

<style lang="less" scoped>
.review-content {
  width: 320px;
  margin-left: 16px;
  // padding: 24px;
  padding: 24px 0 24px 24px;
  background-color: #fff;
  .det-title {
    border-bottom: 1px solid #e7e7e7ff;
    padding-top: 12px;
    margin-right: 24px;
    .text {
      font-size: 16px;
      font-weight: 600;
    }
    .text-tips {
      color: #00000099;
      padding-left: 8px;
    }
  }
  .det-title-staff {
    height: 400px;
    overflow: auto;
    padding-right: 20px;
    margin-right: 4px;
  }
  .none-border {
    border: none;
    // padding-top: 0px;

    // padding-right: 24px;
  }
  .margin-r {
    margin-right: 0;
  }
  .definite {
    padding: 4px 0 12px 0;

    line-height: 22px;
    p {
      display: flex;
      justify-content: space-between;
      padding-top: 10px;
      color: #00000099;

      .value-item {
        font-size: 12px;
      }
    }
    .num-r {
      display: inline-block;
      width: 40px;
      height: 20px;
      line-height: 20px;
      margin-left: 8px;
      border-radius: 2px;
      text-align: center;
      font-size: 12px;
    }
    .num-r1 {
      background-color: #ccf2e2ff;
      color: #00b368ff;
    }
    .num-r2 {
      background-color: #fef3e6ff;
      color: #ed7b2fff;
    }
    .num-r3 {
      color: #0052d9ff;
      background-color: #d4e3fcff;
    }
  }
  .input-inner {
    margin-top: 25px;
    .padding {
      padding-bottom: 12px;
    }
  }
  .tips {
    color: #ed7b2f;
    font-size: 12px;
    padding-bottom: 15px;
    line-height: 14px;
  }
  .click-details {
    cursor: pointer;
    color: #3464e0;
    font-size: 12px;
    margin-bottom: 15px;
  }
  .tips-staff {
    color: #ed7b2f;
    font-size: 12px;
    padding: 10px;
    background-color: #fcf6ed;
    margin-top: 16px;
    margin-right: 24px;
    border-radius: 3px;
    display: inline-block;
    i {
      margin-right: 4px;
    }
  }
  :deep(.score-form-item) {
    margin-bottom: 4px;
    .el-form-item__error {
      width: 120px;
    }
  }
  .teacher-title {
    display: flex;
    justify-content: space-between;
  }
}
.details-evaluation {
  .tips {
    display: inline-block;
    color: #ed7b2f;
    font-size: 12px;
    padding: 10px;
    background-color: #fcf6ed;
  }
  .infinite-list {
    height: 400px;
    overflow: auto;
    padding: 0 10px;
    .load-ing {
      text-align: center;
      height: 50px;
      line-height: 50px;
      color: #00000099;
    }
  }
  .definite {
    padding: 4px 0 12px 0;
    border-bottom: 1px solid #e7e7e7;
    line-height: 22px;

    p {
      display: flex;
      justify-content: space-between;
      padding-top: 10px;
      color: #00000099;

      .value-item {
        font-size: 12px;
      }
      .value-label {
        .name {
          font-weight: 600;
          font-size: 14px;
          color: #000000e6;
        }
      }
    }
    .num-r {
      display: inline-block;
      width: 40px;
      height: 18px;
      line-height: 18px;
      margin-left: 8px;
      border-radius: 2px;
      text-align: center;
      font-size: 12px;
    }
    .back {
      display: inline-block;
      background-color: #fdecee;
      width: 56px;
      height: 18px;
      line-height: 18px;
      font-size: 12px;
      text-align: center;
      margin-left: 8px;
      color: #e34d59;
    }
    .num-r1 {
      background-color: #ccf2e2ff;
      color: #00b368ff;
    }
    .num-r2 {
      background-color: #fef3e6ff;
      color: #ed7b2fff;
    }
    .num-r3 {
      color: #0052d9ff;
      background-color: #d4e3fcff;
    }
  }
}
.empty {
  padding-top: 20px;
  color: #999;
  text-align: left;
  .empty-img {
    margin-bottom: 20px;
    width: 178px;
    height: 130px;
  }
}
.dialog-empty {
  text-align: center
}
</style>
