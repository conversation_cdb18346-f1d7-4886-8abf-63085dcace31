<template>
  <sdc-layout class="tutor-layout" :header-layout="['logo', 'links']" :sidebar-menus="sidebarMenus">
    <div slot="header-logo" class="left-logo">
      <img :src="logoImg" alt="">
      <div class="title">导师系统管理后台</div>
    </div>
    <div slot="header-links" class="right-avatar">
      <el-avatar size="small" class="user-avatar" :src="avatar">
        <img :src="defaultAvatar" />
      </el-avatar>
      <span class="staff-name">{{ staffName }}</span>
      <!-- <div class="identity">学员端</div> -->
      <sdc-link @click="exit" text="退出" />
    </div>
  </sdc-layout>
</template>
  
<script>
import { refresh, resize } from 'sdc-vue'
import { getAvatar } from '../utils/tools'
import { getUserRole } from '../api/tutor.api.conf'
export default {
  name: 'manage',
  mixins: [refresh, resize],
  data() {
    return {
      staffName: '',
      avatar: require('@/assets/img/avatar.png'),
      defaultAvatar: require('@/assets/img/avatar.png'),
      logoImg: require('@/assets/tutor/logo-blue.png'),
      sidebarMenus: {}
    }
  },
  watch: {
    '$store.state.userInfo'() {
      this.avatar = getAvatar(this.$store.state.userInfo.staff_name)
      this.staffName = this.$store.state.userInfo.staff_name
    }
  },
  created() {
    this.getUserRole()
  },
  methods: {
    getUserRole() {
      let menu = {
        active: '1',
        map: {
          key: 'id',
          text: 'name',
          url: 'link',
          pid: 'parentId',
          root: '0'
        },
        data: [
          {
            id: '3',
            name: '规则配置',
            icon: 'el-icon-none',
            parentId: '0',
            level: 1,
            click: () => {
              this.menuChange('ruleSet')
            }
          }
        ]
      }
      getUserRole().then((res) => {
        this.$store.commit('setUserRole', res)
        if (res.super_admin || res.bg_admin || res.dept_admin) {
          menu.data.unshift({
            id: '1',
            name: '导师管理',
            icon: 'el-icon-none',
            parentId: '0',
            level: 1,
            click: () => {
              this.menuChange('addManage')
            }
          },
          {
            id: '2',
            name: '辅导记录',
            icon: 'el-icon-none',
            parentId: '0',
            level: 1,
            click: () => {
              this.menuChange('record')
            }
          })
        }
        this.sidebarMenus = menu
        this.initPage()
      }).catch(() => {
        this.sidebarMenus = menu
        this.initPage()
      })
    },
    initPage() {
      const path = {
        'addManage': '1',
        'record': '2',
        'ruleSet': '3'
      }
      const arr = window.location.pathname.split('/')
      this.sidebarMenus.active = path[arr[arr.length - 1] || 'addManage']
    },
    // 菜单切换
    menuChange(name) {
      this.$router.push({ name })
    },
    exit() {
      if (process.env.NODE_ENV === 'development') {
        // 测试环境
        window.location.href = `//passtest.oa.com/modules/passport/signout.ashx?url=${location.href}`
      } else {
        // 生产环境
        window.location.href = `${location.origin}/_logout/?url=${location.href}`
      }
    }
  }
}
</script>
<style lang="less">
@import '~assets/css/el-style.less';
@import '~assets/css/center.less';
@import '~assets/css/common.less';
.tutor-layout {
  .page-content {
    .sdc-router-view {
      padding: 20px
    }
  }
  .sdc-content {
    .page-nav {
      width: 160px;
      padding-top: 20px;
      .sdc-sidebar {
        width: 160px;
      }
    }
    .el-menu-item {
      color: #666666;
    }
    // .is-active {
    //   color: #0052D9;
    // }
    .el-icon-none {
      width: unset !important;
    }
  }
  .sdc-header .header-inner {
    .header-left {
      padding-left: 40px;
      width: unset;
    }
    .header-right {
      padding-right: 22px;
    }
  } 
  .header-right {
    padding-right: 22px;
  }
  .left-logo {
    display: flex;
    align-items: center;
    img {
      width: 98px;
      height: 32px;
    }
    .title {
      color: #ffffff;
      font-family: "TencentSans";
      font-size: 16px;
      font-weight: bold;
      margin-left: 40px;
    }
  }
  .right-avatar {
    display: flex;
    align-items: center;
    .staff-name {
      margin-left: 5px;
      margin-right: 30px;
    }
    .identity {
      margin-left: 37px;
      margin-right: 30px;
    }
  }
  .sdc-content .sdc-back-top {
    bottom: 40px !important;
  }
}
</style>
