<template>
  <el-Dialog 
  :visible="visible" 
  width="800px" 
  :close-on-click-modal="false"
  @close="cancel" 
  class="operate-dialog"
  >
    <template #title>
      <div class="title-box">
        <span class="title-icon"></span>
        <span class="custom-title">{{ dialogData.title }}</span> 
      </div>
    </template>
    <div class="body-tips">{{ dialogData.tips }}</div>
    <el-form ref="form" :model="form" inline :rules="rule" label-position="top">
      <el-form-item :label="dialogData.label" class="operate-form-mce" prop="entry_reason">
        <sdc-mce-editor 
        ref="operateTutorEditRef" 
        :env="editorEnv" 
        :content="form.entry_reason"
        :catalogue.sync="editorConfig.catalogue"
        :urlConfig="editorConfig.urlConfig"
        :options="editorConfig.options"
        :insertItems="insertItems"
        @getWordCount="getWordCount"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel" size="small">取消</el-button>
      <el-button  :disabled="disaBledBtn" @click="confirm" :type="dialogData.btnType" size="small">{{ dialogData.confirmBtn }}</el-button>
    </span>
  </el-Dialog>
</template>
<script>
import { disabledTutor, restId } from '../../../api/tutor.api.conf'
export default {
  props: {
    visible: Boolean,
    detailInfo: Object
  },
  data() {
    return {
      mceChangeVal: '',
      visibleShow: false,
      dialogData: {
        title: '',
        label: '',
        tips: '',
        confirmBtn: '',
        btnType: ''
      },
      form: {
        entry_reason: ''
      },
      rule: {
        entry_reason: [
          { required: true, message: '请输入原因', trigger: 'blur' }
        ]
      },
      param: {},
      insertItems: [
        'link',
        'table',
        'image',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorConfig: {
        catalogue: false,
        options: { // 非必传字段
          selector: '#tutor_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo |
              formatselect fontsizeselect lineheight |
              bold italic underline strikethrough |
              dent align numlist`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      },
      editorEnv: process.env.NODE_ENV
    }
  },
  watch: {
    detailInfo: {
      deep: true,
      immediate: true,
      handler(val) {
        this.dialogData = {
          ...val
        }
      }
    }
  },
  computed: {
    disaBledBtn() {
      return !this.mceChangeVal
    }
  },
  mounted() {
  },
  methods: {
    initData(row) {
      this.param = row
    },
    cancel() {
      this.form.entry_reason = ''
      this.$emit('update:visible', false)
    },
    confirm() {
      const entry_reason = this.$refs['operateTutorEditRef'].getContent()
      const commAPI = this.dialogData.btnType === 'danger' ? disabledTutor : restId
      let params = {}
      if (this.dialogData.btnType === 'danger') {
        params = {
          tutor_staff_id: this.param.tutor_staff_id,
          disable_reason: this.mceChangeVal ? entry_reason : ''
        }
      } else {
        params = {
          tutor_staff_id: this.param.tutor_staff_id,
          recertification_reason: this.mceChangeVal ? entry_reason : ''
        }
      }
      commAPI(params).then(() => {
        this.cancel()
        const msg = this.dialogData.btnType === 'danger' ? '禁用成功' : '重新认证成功'
        this.$message.success(msg)
        this.$emit('onSearch')
      })
    },
    getWordCount(val) {
      this.mceChangeVal = val
    }
  }
}
</script>
<style lang="less">
.operate-form-mce {
  width: 736px;

  .tox.tox-tinymce {
    border: 1px solid #DCDCDC !important;
    height: 200px;
    border-radius: 3px;

    .tox-sidebar-wrap .tox-edit-area {
      min-height: 160px !important;
    }
  }
  .tox .tox-editor-header {
    text-align: left;
  }
}
</style>
<style lang="less" scoped>
.operate-dialog {
  :deep(.el-dialog__header) {
    padding: 24px 32px 20px;
  }

  :deep(.el-dialog__body) {
    padding: 24px 32px;
  }

  :deep(.el-dialog__footer) {
    padding: 0 32px 24px;
  }
  .title-box {
    display: flex;
    align-items: center;
    .title-icon {
      display: inline-block;
      width: 24px;
      height: 24px;
      background: url('~@/assets/tutor/or-tips.png') no-repeat center / cover;
      margin-right: 8px;
    }
    .custom-title {
      font-weight: bold;
      font-size: 16px;
      color: #000000e6;
    }
  }
  .body-tips {
    color: #d54941;
    font-size: 14px;
    line-height: 22px;
    margin-bottom: 16px;
  }
  :deep(.el-form-item__label) {
    color: #00000099;
  }
}
</style>
