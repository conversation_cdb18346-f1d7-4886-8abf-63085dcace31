/* eslint-disable */
import { render } from 'sdc-vue'
import { ele, sdc } from './plugins'
import sdcMceEditor from 'sdc-ui-rte'
import 'sdc-ui-rte/lib/sdc-ui-rte.css'
import { init } from '@sdc-monitor/browser'
import { vuePlugin } from '@sdc-monitor/vue'
import { StaffSelector, UnitSelector } from 'sdc-webui'
// 应用配置
import App from 'views/tutor/app'
import router from './router'
import store from './store'
import env from '../../config/env.conf'
// 插件配置
import './plugins/styles'
console.log('APPPPPPPPPPPPPPPPP', App)
// 权限控制
import Vconsole from 'vconsole'
if (window.location.search.indexOf('debugger=true') > -1) {
  // eslint-disable-next-line no-new
  new Vconsole()
}
// 路由变化
router.beforeEach((to, from, next) => {
  document.title = to.meta.title || ''
  next()
})
export default render(App, {
  router,
  store,
  env,
  plugins: [ele, sdc],
  lazy: {
    preLoad: 1,
    loading: require('assets/img/loading-spinning-bubbles.svg')
  },
  init: Vue => {
    Vue.use(sdcMceEditor)
    Vue.use(StaffSelector)
    Vue.use(UnitSelector)

    if (process.env.NODE_ENV === 'production') {
      init({
        apikey: 'tutor',
        debug: false,
        vue: Vue,
        dsn: env[process.env.NODE_ENV].commonPath + 'training-portal-common/api/front/log/upload',
        maxBreadcrumbs: 2
      }, [vuePlugin])
    }
  }
})