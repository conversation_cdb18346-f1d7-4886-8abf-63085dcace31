<template>
  <div class="tutorial-record">
    <div class="top-nav">
        <span class="nav-item" :class="{'nav-item-active': item.value === activeKey}" v-for="item in navList" :key="item.value" @click="changeNav(item.value)">{{ item.label }}</span>
    </div>
    <div class="main-content">
      <div class="record-filter">
        <el-form ref="form" :model="form" inline>
          <el-form-item label="导师名称：">
            <el-input style='width: 160px;' v-model="form.tutor_name" placeholder="请输入导师名称" suffix-icon="el-icon-search" clearable></el-input>
          </el-form-item>
          <el-form-item label="导师所属组织：">
            <sdc-unit-selector placeholder="请选择所属组织" ref="tutorSelectRef" v-model="form.tutor_org_id" :includeUnitSortIDs="includeUnitSortIDs" selectClass="custom-select-unit" showFullTag/>
          </el-form-item>
          <el-form-item label="辅导生效时间：" v-if="activeKey === 'employed'">
            <el-date-picker
              style="width:240px"
              size="small"
              v-model="effectiveTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00','23:59:59']"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="是否有效：" v-if="activeKey === 'employed'">
            <el-select v-model="form.tutoring_valid" placeholder="请选择" style="width:100px" clearable>
              <el-option
                v-for="item in effectiveOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="匹配时间：" v-if="activeKey === 'notEmployed'">
            <el-date-picker
              style="width:240px"
              size="small"
              v-model="matchingTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00','23:59:59']"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="新员工名称：">
            <el-input style='width: 160px;' v-model="form.staff_name" placeholder="请输入新员工名称" suffix-icon="el-icon-search" clearable></el-input>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select v-model="form.staff_status" placeholder="请选择状态" style="width:120px" clearable>
              <el-option
                v-for="item in (activeKey === 'employed' ? statusOptions : statusOptionsNo)"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="新员工所属组织：">
            <sdc-unit-selector placeholder="请选择所属组织" ref="studentSelectRef" v-model="form.staff_org_id" :includeUnitSortIDs="includeUnitSortIDs" selectClass="custom-select-unit" showFullTag/>
          </el-form-item>
          <el-form-item class="right-btn">
            <!-- <el-button style="width:60px" @click="handleExport" size='small'>导出</el-button> -->
            <el-button style="width:80px" @click="handleReset" class="custom-plain" size='small' icon="el-icon-refresh">重置</el-button>
            <el-button style="width:60px" @click="handleSearch()" type="primary" size='small'>搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="record-table">
        <el-table
        :data="tableData.records"
        header-row-class-name="tutor-table-header"
        row-class-name="tutor-table-row"
        class='tutor-table employed-table'
        >
          <el-table-column key="tutor_name" width="120" prop="tutor_name" label="导师名称" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.tutor_name || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="certification_status" width="80" v-if="activeKey === 'notEmployed'" prop="certification_status" label="认证要求" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span class="strong-font" v-if="[null, -1].includes(row.certification_status)">-</span>
              <span v-else-if="row.certification_status === 1" class="color-green">满足</span>
              <div v-else class="ds-flex">
                <span class="color-yellow">不满足</span>
                <el-popover
                  placement="top"
                  popper-class="tutor-popover-customer tutor-popover-special"
                  trigger="hover"
                >
                  <div class="change-item">认证要求校验</div>
                  <!-- 认证中 -->
                  <div class="tips" v-if="row.tutor_status === 0">（重新认证时需要再次通过考试）</div>
                  <div class="item-scroll-content">
                    <div 
                      v-for="(item, index) in resolveCertRiskList(row)" 
                      :key="index" 
                      :class="[item.meet_requirements ? 'item-green' : 'item-red', 'common-bg']"
                      @click="toRequireDetail(item)"
                    >
                      <span :class="[item.meet_requirements ? 'item-icon-green' : 'item-icon-red', 'item-icon-common']"></span>
                      <span :class="[item.meet_requirements? 'link-green' : 'link-red', 'link', 'link-pointer']">{{ item.requirements_content }}</span>
                    </div>
                  </div>
                  <i slot="reference" class="icon-tips"></i>
                </el-popover>
              </div>
            </template>
          </el-table-column>
          <el-table-column key="tutor_org_full_name" width="160" prop="tutor_org_full_name" label="导师所属组织" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.tutor_org_full_name || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="tutor_base_location_name" :width="activeKey === 'employed' ? 128 : 80" prop="tutor_base_location_name" label="工作地" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.tutor_base_location_name || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="tutor_match_time" :width="activeKey === 'employed' ? 173 : 120" prop="tutor_match_time" label="匹配时间" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.tutor_match_time || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="tutor_match_status" width="80" prop="tutor_match_status" label="匹配风险" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span :class="resolveStatusClass(row)">{{ resolveStatusText(row) }}</span>
              <i v-if="showRiskPopover(row)" class="icon-tips" @click="openRiskDialog(row)"></i>
              <el-popover v-if="showDisabledPopover(row)"
                placement="top"
                popper-class="tutor-popover-customer"
                trigger="hover"
              >
                <div class="change-item">匹配时导师处于禁用状态</div>
                <i slot="reference" class="icon-tips"></i>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column key="tutoring_start_time" width="173" v-if="activeKey === 'employed'" prop="tutoring_start_time" label="辅导生效时间" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.tutoring_start_time || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="tutoring_end_time" width="173" v-if="activeKey === 'employed'" prop="tutoring_end_time" label="辅导结束时间" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.tutoring_end_time || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="tutor_match_type" width="80" prop="tutor_match_type" label="匹配来源" show-overflow-tooltip>
            <!-- 匹配来源包括 招聘 入职准备 入职入场 人才透视 workday系统 -->
            <template slot-scope="{row}">
              <span>{{ tutorMatchOptions[row.tutor_match_type] || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="last_tutor_record_id" width="150" v-if="activeKey === 'employed'" prop="last_tutor_record_id" label="是否为变更后的导师" show-overflow-tooltip>
            <template slot-scope="{row}">
              <el-popover
                effect="light"
                placement="bottom"
                popper-class="tutor-popover-customer tutor-popover-special"
                trigger="hover"
                :disabled="!row.last_tutor_record_id"
                >
                <div class="tutor-change">
                  <div class="change-item">导师变更详情如下：</div>
                  <div class="item-scroll-content">
                    <div v-for="(item, index) in resolveToturChanged(row)" :key="index">
                      <div class="change-item">原导师：{{ item.former_tutor_name || '-' }}</div>
                      <div class="change-item">辅导生效时间：{{ item.tutoring_start_time || '-' }}</div>
                      <div class="change-item">辅导结束时间：{{ item.tutoring_end_time || '-' }}</div>
                      <div class="change-item">导师变更原因：{{ item.tutoring_end_reason || '-' }}</div>
                    </div>
                  </div>
                </div>
                <span slot="reference">
                  {{ row.last_tutor_record_id ? '是' : '否' }}<i v-if="row.last_tutor_record_id" class="icon-tips"></i>
                </span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column key="opt_staff_name" :width="activeKey === 'employed' ? 169 : 120" prop="opt_staff_name" label="操作人" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.opt_staff_name || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="tutoring_end_reason" width="120" v-if="activeKey === 'employed'" prop="tutoring_end_reason" label="结束辅导原因" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.tutoring_end_reason || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="meet_one_time_certification" width="136" v-if="activeKey === 'employed'" prop="meet_one_time_certification" label="满足一次认证要求" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span :class="getIsPassClass(row.meet_one_time_certification)">{{ resolveIsPassText(row.meet_one_time_certification) }}</span>
            </template>
          </el-table-column>
          <el-table-column key="tutoring_valid" width="80" v-if="activeKey === 'employed'" prop="tutoring_valid" label="是否有效" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span class="color-green" v-if="[1, '1'].includes(row.tutoring_valid)">有效</span>
              <template v-else-if="[0, '0'].includes(row.tutoring_valid)">
                <el-popover
                  effect="light"
                  placement="bottom"
                  popper-class="tutor-popover-customer"
                  trigger="hover"
                >
                  <div class="tutor-no-effict">
                    <div class="change-item">辅导记录无效原因如下：</div>
                    <div class="common-bg item-red" v-if="![1, '1', 2, '2'].includes(row.meet_one_time_certification)">
                      <span class="item-icon-common item-icon-red"></span>
                      <span class="link link-red">满足一次认证要求</span>
                    </div>
                    <div class="common-bg item-red" v-if="!isMoreThan30(row)">
                      <span class="item-icon-common item-icon-red"></span>
                      <span class="link link-red">辅导时长超过30天</span>
                    </div>
                  </div>
                  <span slot="reference" class="color-yellow">
                    无效<i class="icon-tips"></i>
                  </span>
                </el-popover>
              </template>
              <span v-else class="strong-font">-</span>
            </template>
          </el-table-column>
          <el-table-column key="staff_name" width="120" prop="staff_name" label="新员工名称" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.staff_name || '-' }}</span>
            </template>
          </el-table-column>
          <!-- 新员工状态：-2-放弃入职；-1：离职；0-即将入职；1-在职；2-试用； -->
          <el-table-column key="staff_status" width="80" prop="staff_status" label="状态" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ status_info[row.staff_status] }}</span>
            </template>
          </el-table-column>
          <!-- 1-正式;2-毕业生;3-实习生;4-顾问; -->
          <el-table-column key="staff_type" width="80" prop="staff_type" label="员工类型" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ staffTypeMap[row.staff_type] || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="staff_from" width="80" prop="staff_from" label="招聘类型" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ staffFromMap[row.staff_from] || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="staff_join_time" width="120" v-if="activeKey === 'employed'" prop="staff_join_time" label="入职日期" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.staff_join_time || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="staff_joining_appointment" width="120" v-if="activeKey === 'notEmployed'" prop="staff_joining_appointment" label="预约入职日期" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.staff_joining_appointment || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="trial_date" width="118" v-if="activeKey === 'employed'" prop="trial_date" label="试用期时长(月)" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.trial_date ? Number(row.trial_date).toFixed(1) : 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column key="staff_regularization_time" width="173" v-if="activeKey === 'employed'" prop="staff_regularization_time" label="转正日期" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.staff_regularization_time || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="staff_org_full_name" width="160" prop="staff_org_full_name" label="新员工所属组织" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.staff_org_full_name || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column key="staff_base_location_name" width="80" prop="staff_base_location_name" label="工作地" show-overflow-tooltip>
            <template slot-scope="{row}">
              <span>{{ row.staff_base_location_name || '-' }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="tableData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="current"
          :page-sizes="[5, 10, 20, 30, 50, 100]"
          :page-size="size"
          layout="total,  prev, pager, next, sizes, jumper"
          :total="tableData.total"
        >
        </el-pagination>
        <!-- 风险匹配-风险 -->
        <commonDialog v-if="commonDialogShow" :visible.sync="commonDialogShow" :detailInfo="commonDialogInfo" ref="commonDialogRef"></commonDialog>
      </div>
    </div>
  </div>
</template>

<script>
import pager from '@/mixins/pager'
import commonDialog from './child/commonDialog.vue'
import { getTutorCounselPageOnBoard, exportTutorCounselListOnBoard, getTutorCounselPageUnBoard, exportTutorCounselListUnBoard } from '../../api/tutor.api.conf'
const effectiveOptions = [
  { value: '', label: '全部' },
  { value: 1, label: '是' },
  { value: 0, label: '否' }
]
const statusOptions = [
  { value: '', label: '全部' },
  { value: '2', label: '试用' },
  { value: '1', label: '在职' },
  { value: '-1', label: '离职' }
]
const statusOptionsNo = [
  { value: '', label: '全部' },
  { value: '0', label: '即将入职' },
  { value: '-2', label: '放弃入职' }
]
const status_info = {
  '-2': '放弃入职',
  '-1': '离职',
  '0': '即将入职',
  '1': '在职',
  '2': '试用'
}
const tutorMatchOptions = {
  1: '招聘',
  2: '入职准备',
  3: '入职入场',
  4: '人才透视',
  5: '异动'
}
const staffTypeMap = {
  1: '正式',
  2: '毕业生',
  3: '实习生',
  4: '顾问'
}
const staffFromMap = {
  1: '校园招聘',
  2: '社会招聘'
}
export default {
  components: {
    commonDialog
  },
  mixins: [pager],
  data() {
    return {
      navList: [
        { label: '已入职', value: 'employed' },
        { label: '未入职', value: 'notEmployed' }
      ],
      activeKey: 'employed',
      includeUnitSortIDs: [0, 6, 8, 1], // 组织筛选最小到组织
      form: {
        tutor_name: '', // 导师名称
        tutor_org_id: '', // 导师所属组织
        tutoring_valid: '', // 是否有效：'':全部 1:是 0:否
        staff_name: '', // 新员工名称
        tutor_status: '', // 状态
        staff_org_id: '' // 新员工所属组织
      },
      effectiveTime: [], // 辅导生效时间
      matchingTime: [], // 匹配时间
      tableData: {
        records: [],
        total: 0
      },
      commonDialogShow: false, // 是否显示风险匹配-风险弹窗
      commonDialogInfo: {},
      effectiveOptions,
      statusOptions,
      statusOptionsNo,
      status_info,
      tutorMatchOptions,
      staffTypeMap,
      staffFromMap
    }
  },
  computed: {},
  mounted() {
    this.handleReset()
  },
  methods: {
    changeNav(value) {
      this.activeKey = value
      this.handleReset()
    },
    // 搜索数据
    onSearch(page_no = 1) {
      // 辅导生效时间开始日期
      const tutor_start_time = this.effectiveTime?.length ? this.effectiveTime[0] : ''
      // 辅导生效时间结束日期
      const tutor_end_time = this.effectiveTime?.length ? this.effectiveTime[1] : ''
      // 匹配时间开始日期
      const match_start_time = this.matchingTime?.length ? this.matchingTime[0] : ''
      // 匹配时间结束日期
      const match_end_time = this.matchingTime?.length ? this.matchingTime[1] : ''
      const params = {
        ...this.form,
        [this.activeKey === 'employed' ? 'tutor_start_time' : 'match_start_time']: this.activeKey === 'employed' ? tutor_start_time : match_start_time,
        [this.activeKey === 'employed' ? 'tutor_end_time' : 'match_end_time']: this.activeKey === 'employed' ? tutor_end_time : match_end_time,
        page_no,
        page_size: this.size
      }
      let api = getTutorCounselPageOnBoard
      if (this.activeKey === 'notEmployed') {
        api = getTutorCounselPageUnBoard
        delete params.tutoring_valid
      }
      api(params).then((res) => {
        this.tableData.records = res.records.map((e) => {
          let data = {}
          let dataNow = {}
          try {
            // 风险匹配(切片)
            data = e.tutor_risk_reason ? JSON.parse(e.tutor_risk_reason) : {}
            data.tutor_match_reason = e.tutor_match_reason || null
            // (实时)
            dataNow = e.risk_check_content ? JSON.parse(e.risk_check_content) : {}
          } catch (error) {
            data = { tutor_match_reason: e.tutor_match_reason || null }
          }
          return {
            ...e,
            tutor_risk_reason: data,
            risk_check_content: dataNow
          }
        })
        this.tableData.total = res.total
      })
    },
    // 搜索
    handleSearch() {
      this.current = 1
      this.size = 10
      this.onSearch(1)
    },
    // 导出
    handleExport() {
      const tutor_start_time = this.effectiveTime?.length ? this.effectiveTime[0] : ''
      const tutor_end_time = this.effectiveTime?.length ? this.effectiveTime[1] : ''
      const match_start_time = this.matchingTime?.length ? this.matchingTime[0] : ''
      const match_end_time = this.matchingTime?.length ? this.matchingTime[1] : ''
      const params = {
        ...this.form,
        [this.activeKey === 'employed' ? 'tutor_start_time' : 'match_start_time']: this.activeKey === 'employed' ? tutor_start_time : match_start_time,
        [this.activeKey === 'employed' ? 'tutor_end_time' : 'match_end_time']: this.activeKey === 'employed' ? tutor_end_time : match_end_time,
        page_no: this.current,
        page_size: this.size
      }
      let api = exportTutorCounselListOnBoard
      if (this.activeKey === 'notEmployed') {
        api = exportTutorCounselListUnBoard
        delete params.tutoring_valid
      }
      api(params).then((res) => {
        this.$message.success('导出成功')
      })
    },
    // 重置
    handleReset() {
      this.effectiveTime = []
      this.matchingTime = []
      this.$refs.tutorSelectRef.clearSelected()
      this.$refs.studentSelectRef.clearSelected()
      for (let pre in this.form) {
        this.form[pre] = ''
      }
      this.handleSearch()
    },
    toRequireDetail(e) {
      if (!e.course_url) return
      window.open(e.course_url, '_blank')
    },
    resolveIsPassText(code) { // 0-不满足;1满足;2-免认证 
      let text = ''
      switch (code) {
        case 0: // 不满足
          text = '不满足'
          break
        case 1: // 满足
          text = '满足'
          break
        case 2: // 免认证
          text = '免认证'
          break
        default:
          text = '-'
          break
      }
      return text
    },
    getIsPassClass(code) {
      let colorCalss = ''
      switch (code) {
        case 0: // 不满足
          colorCalss = 'color-yellow'
          break
        case 1: // 满足
          colorCalss = 'color-green'
          break
        default:
          colorCalss = 'strong-font'
          break
      }
      return colorCalss
    },
    // 风险匹配class 0-不存在;1-存在;-1-严重
    resolveStatusClass(item) {
      let str = ''
      if ([-1, '-1'].includes(item.tutor_match_status)) {
        str = 'color-red under-line'
      } else if ([1, '1'].includes(item.tutor_match_status)) {
        str = 'color-yellow under-line'
      } else {
        str = 'color-green'
      }
      return str
    },
    // 风险匹配文本 0-不存在;1-存在;-1-严重
    resolveStatusText(item) {
      let text = ''
      if ([-1, '-1'].includes(item.tutor_match_status)) {
        text = '严重'
      } else if ([1, '1'].includes(item.tutor_match_status)) {
        text = '存在'
      } else {
        text = '不存在'
      }
      return text
    },
    // 是否显示匹配-风险提示
    showRiskPopover(item) {
      return !![1, '1'].includes(item.tutor_match_status)
    },
    // 是否显示风险匹配-禁用提示
    showDisabledPopover(item) {
      return !![-1, '-1'].includes(item.tutor_match_status)
    },
    // 打开风险匹配-风险
    openRiskDialog(item) {
      this.commonDialogInfo = item.tutor_risk_reason || {}
      this.$nextTick(() => {
        this.commonDialogShow = true
      })
    },
    // 辅导时长是否超过30天
    isMoreThan30(row) {
      const { tutoring_start_time = '', tutoring_end_time = '' } = row
      if (!tutoring_start_time || !tutoring_end_time) {
        return false
      }
      try {
        return this.isIntervalGreaterThan30Days(tutoring_start_time, tutoring_end_time)
      } catch (error) {
        return false
      }
    },
    isIntervalGreaterThan30Days(dateStr1, dateStr2) {
      const date1 = new Date(dateStr1)
      const date2 = new Date(dateStr2)
      if (isNaN(date1.getTime()) || isNaN(date2.getTime())) {
        throw new Error('Invalid date format')
      }
      const timeDiff = Math.abs(date2.getTime() - date1.getTime())
      const daysDiff = timeDiff / (1000 * 3600 * 24)
      return daysDiff > 30
    },
    // 认证要求校验
    resolveCertRiskList(row) {
      let list = row.risk_check_content?.cert_risk_detail || []
      return list
    },
    // 导师变更记录
    resolveToturChanged(row) {
      const { last_tutor_record_id = null, last_tutor_counsel_record = null } = row
      if (!last_tutor_record_id || !last_tutor_counsel_record) {
        return []
      }
      return [last_tutor_counsel_record]
    }
  }
}
</script>

<style lang="less">
.tutor-popover-special {
  padding-right: 0 !important;
}
.tutor-popover-customer {
  display: flex;
  padding: 8px 10px 0px;
  flex-direction: column;
  border-radius: 6px;
  box-shadow: 2px 12px 32px 0 #0000001a;
  max-width: 224px;
  .item-scroll-content {
    padding-right: 10px;
    max-height: 500px;
    overflow-y: auto;
  }
  .change-item {
    margin-bottom: 8px;
    color: #000000e6;
    font-size: 12px;
    line-height: 20px;
  }
  .tips {
    margin-bottom: 8px;
    color: #00000099;
    font-size: 12px;
    line-height: 20px;
  }
  .common-bg {
    // display: flex;
    // align-items: center;
    margin-bottom: 8px;
    padding: 2px 8px;
    border-radius: 3px;
  }
  .item-icon-common {
    display: inline-block;
    margin-right: 4px;
    width: 14px;
    height: 14px;
    flex-shrink: 0;
    vertical-align: middle;
  }
  .item-green {
    background: #E3F9E9;
    .item-icon-green {
      background: url('~@/assets/tutor/right-icon.png') no-repeat center / cover;
    }
  }
  .item-red {
    background: #FFF0ED;
    .item-icon-red {
      background: url('~@/assets/tutor/error-icon.png') no-repeat center / cover;
    }
  }
  .link {
    font-size: 12px;
    line-height: 20px;
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
  }
  .link-pointer {
    cursor: pointer;
  }
  .link-green {
    color: #2ba471;
  }
  .link-red {
    color: #d54941;
  }
  .strong-font {
    font-weight: 550;
  }
  .color-green {
    color: #2ba471;
    font-weight: 550;
  }
  .color-yellow {
    color: #e37318;
    font-weight: 550;
  }
}
</style>
<style lang="less" scoped>
* {
 box-sizing: border-box;
}
.tutorial-record {
  // min-height: 100%;
  background: #FFF;
  border-radius: 4px;
  box-shadow: 0 0 8px 0 #EEE;
  .top-nav {
    display: flex;
    padding: 0 36px;
    height: 48px;
    border-bottom: 1px solid #E7E7E7;
    .nav-item {
      margin-right: 24px;
      padding: 0 16px;
      height: 100%;
      line-height: 48px;
      text-align: center;
      color: #00000099;
      font-size: 14px;
      cursor: pointer;
    }
    .nav-item-active {
      color: #0052d9;
      border-bottom: 3px solid #0052D9;
    }
  }
  :deep(.custom-select-unit) {
    width: 240px;
  }
  .main-content {
    padding: 20px 20px 28px;
    .record-filter {
      padding: 16px 16px 0px;
      border-radius: 4px;
      background: #FAFAFA;
      .el-form-item {
        margin: 0 20px 16px 0;
        :deep(.el-form-item__label) {
          color: #00000099;
        }
      }
      .right-btn {
        padding-left: 35px;
        .el-button+.el-button {
          margin-left: 16px;
        }
      }
    }
    :deep(.el-table__body-wrapper::-webkit-scrollbar) {
      height: 7px;
    }
    .record-table {
      margin-top: 20px;
      .employed-table {
        :deep(table.el-table__header) {
          min-width: max-content !important;
        }
        :deep(table.el-table__body) {
          min-width: max-content !important;
        }
      }
      :deep(.tutor-table) {
        .tutor-table-header {
          height: 48px;
    
          th {
            background: #F5F5F5;
            // font-weight: bold;
            color: #00000099;
            border-bottom: none;
          }
        }
    
        .tutor-table-row {
          height: 60px;
          td {
            height: 60px;
            color: #000000e6;
            padding: 10px 0;
            border-color: #eee;
          }
        }

        .mgl-6 {
          margin-left: 6px;
        }

        .ds-flex {
          display: flex;
          align-items: center;
        }

        .strong-font {
          font-weight: 550;
        }

        .color-green {
          color: #2ba471;
          font-weight: 550;
        }

        .color-yellow {
          color: #e37318;
          font-weight: 550;
        }

        .color-red {
          color: #e34d59;
          font-weight: 550;
        }

        .under-line {
          text-decoration: underline;
          text-underline-offset: 0.25em;
        }

        .icon-tips {
          display: inline-block;
          width: 12px;
          height: 12px;
          background: url('~@/assets/tutor/tips.png') no-repeat center / cover;
          margin-left: 6px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
