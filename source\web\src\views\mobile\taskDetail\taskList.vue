<template>
  <div class="task-list">
    <div :class="[moocLang === 'en-us' ? 'en-filter-box' : '' , 'filter','clearfix']">
      <div class="switch-box">
        <div class="checked-top">
          <van-switch
            class="switch"
            v-model="required"
            active-color="#0052D9"
            inactive-color="#c5c5c5ff"
            size="18px"
            @change="getTask"
          />
          <span class="text required-text">{{ $langue('Mooc_ProjectDetail_TaskList_OnlyRequire', { defaultText: '只看应学任务' }) }}</span>
        </div>
        <div>
          <van-switch
            class="switch"
            v-model="unfinished"
            active-color="#0052D9"
            inactive-color="#c5c5c5ff"
            size="18px"
            @change="getTask"
          />
          <span class="text">{{ $langue('Mooc_ProjectDetail_TaskList_OnlyNotFinished', { defaultText: '只看未完成任务' }) }}</span>
        </div>
      </div>

      <img
        class="expand"
        v-if="collapseList.length > 0"
        :src="expandIcon"
        @click="onExpandAll"
        alt=""
      />
    </div>
    <van-collapse
      v-model="activeCollapse"
      @change="onCollapseChange"
      class="collapse"
    >
      <template v-for="stage in taskList">
        <task-item
          :key="stage.task_id"
          :taskData="stage"
          v-if="stage.task_type === 'task'"
          :mooc_course_id="mooc_course_id"
          :isVertical="isVertical"
          :moocPreview="moocPreview"
        />
        <van-collapse-item
          :key="stage.task_id"
          v-else
          class="stage-style"
          :title="stage.task_name || ' '"
          :name="stage.task_id"
        >
          <van-collapse v-model="activeCollapse" @change="onCollapseChange">
            <template v-for="group in stage.sub_tasks">
              <task-item
                :key="group.task_id"
                :taskData="group"
                v-if="group.task_type === 'task'"
                :mooc_course_id="mooc_course_id"
                :isVertical="isVertical"
                :moocPreview="moocPreview"
              />
              <van-collapse-item
                :key="group.task_id"
                v-else
                class="group-style"
                :icon="icon"
                :title="group.task_name || ' '"
                :name="group.task_id"
              >
                <task-item
                  v-for="task in group.sub_tasks"
                  :key="task.task_id"
                  class="third-task"
                  :taskData="task"
                  :mooc_course_id="mooc_course_id"
                  :isVertical="isVertical"
                  :moocPreview="moocPreview"
                />
              </van-collapse-item>
            </template>
          </van-collapse>
        </van-collapse-item>
      </template>
    </van-collapse>
  </div>
</template>

<script>
import { getTaskList, getTaskListSpoc } from '@/config/mooc.api.conf.js'
import taskItem from './taskItem'
import { mapState } from 'vuex'
export default {
  components: {
    taskItem
  },
  props: {
    // 课程id
    mooc_course_id: {
      type: String,
      value: ''
    },
    // 任务id
    task_id: {
      type: Number,
      value: 0
    },
    // 是否按顺序解锁
    unlocked_by_step: {
      type: Boolean,
      value: false
    },
    // 作用于刷新列表
    dateNum: {
      type: Number,
      value: 0
    },
    // 是否横屏
    isVertical: {
      type: Boolean,
      value: false
    },
    moocPreview: {
      type: Number,
      value: 0
    }
  },
  data() {
    return {
      required: false,
      unfinished: false,
      expand: true,
      taskList: [],
      activeCollapse: [],
      collapseList: [], // 所有的阶段和任务组id
      icon: require('@/assets/mooc-img/group.png'),
      status: {}
    }
  },
  watch: {
    mooc_course_id: {
      handler(val) {
        val && this.getTask()
      },
      immediate: true
    },
    dateNum: {
      handler(val) {
        val && this.mooc_course_id && this.getTask()
      }
    }
  },
  computed: {
    ...mapState(['moocLang']),
    expandIcon() {
      return require(`@/assets/mooc-img/${
        this.expand ? 'up-double' : 'down-double'
      }.png`)
    }
  },
  methods: {
    getTask() {
      let { from = 'mooc', class_id } = this.$route.query
      let handleApi = ''
      let params = ''
      if (from === 'spoc') {
        handleApi = getTaskListSpoc
        params = {
          semesterId: this.mooc_course_id,
          classId: class_id
        }
      } else if (from === 'mooc') {
        handleApi = getTaskList
        params = {
          mooc_course_id: this.mooc_course_id
        }
      }
      if (this.required) {
        params.required = true
      }
      if (this.unfinished) {
        params.unfinished = true
      }
      handleApi(params).then((res) => {
        // 拿到数据递归遍历处理
        let nextLock = false
        this.collapseList = []
        const deepMap = (arr) => {
          arr.forEach((item) => {
            // 获取字数/分钟
            if (item.task_status === 2) {
              item.durationStr = this.$langue('Mooc_ProjectDetail_TaskList_TaskDisable', { defaultText: '任务已失效，无法学习' })
            } else if (['22', '23'].includes(item.act_type)) {
              // 任务类型为第三方、作业时，不显示
              item.durationStr = ''
            } else {
              if (item.act_type === '10' || item.act_type === '18') {
                item.durationStr = (item.word_number || 0) + this.$langue('Mooc_ProjectDetail_TaskList_Words', { defaultText: '字' })
              } else if (item.act_type === '1' && from === 'spoc') {
                item.durationStr = ''
                item.resource_type = 'facecourse'
              } else if (item.act_type === '98' && from === 'spoc') {
                item.durationStr = ''
                item.resource_type = 'activity'
              } else {
                item.durationStr = (item.duration || 0) + this.$langue('Mooc_ProjectDetail_TaskList_Minute', { defaultText: '分钟' })
              }
            }
            item.typeIcon =
              ((item.resource_type && item.resource_type.toLowerCase()) ||
                'doc') + '-icon'
            let icon
            // lock_status 任务是否锁定，2是锁定，1不是，如果返回了时间代表是按时间解锁，否则是管理员锁定
            // is_finished, null 未开始，false进行中，true已完成
            // 如果项目开启了顺序解锁模式，点击应学任务需判定之前是否有未完成的应学任务，如果存在则锁定任务，选学任务和被管理员锁定均不参与解锁的判定逻辑
            if (item.lock_status === 2) {
              icon = 'lock'
            } else if (nextLock) {
              if (item.required) {
                item.unlocked_by_step = true
                icon = 'lock'
              } else {
                icon =
                  item.is_finished === null ? 'wait' : item.is_finished === false ? 'loading' : 'done'
              }
            } else {
              if (
                (item.is_finished === null || item.is_finished === false) &&
                this.unlocked_by_step &&
                item.required
              ) {
                nextLock = true
              }
              icon =
                item.is_finished === null ? 'wait' : item.is_finished === false ? 'loading' : 'done'
            }
            if (item.task_type !== 'task') {
              this.collapseList.push(item.task_id)
            } else if (item.task_id === this.task_id * 1) {
              item.activeTask = true
            }
            item.statusIcon = `${icon}-icon`
            if (item.sub_tasks) {
              deepMap(item.sub_tasks)
            }
          })
        }
        deepMap(res || [])
        this.taskList = res
        this.activeCollapse = this.expand ? this.collapseList : []
      })
    },
    onExpandAll() {
      this.activeCollapse = this.expand ? [] : this.collapseList
      this.expand = !this.expand
    },
    onCollapseChange({ detail }) {
      this.activeCollapse = detail
    }
  }
}
</script>

<style lang='less' scoped>
.task-list {
  max-height: 450px;
  overflow-y: auto;
  .filter {
    padding: 8px 16px;
    // height: 56px;
    line-height: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .switch-box {
      display: flex;
    }
    .switch,
    .text {
      float: left;
    }
    .text {
      margin-left: 8px;
      color: #00000099;
      font-size: 12px;
    }
    .required-text {
      margin-right: 24px;
    }
    .expand {
      float: right;
      margin-top: 2px;
      width: 16px;
      height: 16px;
    }
  }
  .en-filter-box {
    display: flex;
    align-items:center;
    justify-content: space-between;
    height: unset;
    .switch-box {
      display: flex;
      flex-direction: column
    }
    .checked-top {
      margin-bottom: 8px;
    }
  }
  .van-collapse:after,
  .van-cell:after,
  .van-hairline--top:after,
  .van-collapse-item__wrapper:after {
    border-top: unset;
  }
  :deep(.collapse) {
    .stage-style {
      .van-collapse-item__title {
        padding: 6px 16px !important;
        line-height: unset !important;
        background: #f9fbfcff;
        .van-cell__title {
          color: #000000e6;
          font-size: 12px;
          font-weight: bold;
          line-height: 24px;
        }
      }
      > .van-collapse-item__wrapper {
        .van-collapse-item__content {
          padding: 0;
          .group-style {
            .van-cell {
              padding: 6px 16px !important;
              background: #fcfcfcff;
              .van-cell__left-icon {
                margin-right: 12px;
                position: relative;
                top: 4px;
              }
              .van-cell__left-icon,
              .van-cell__right-icon {
                height: unset;
              }
              .van-cell__title {
                color: #000000e6;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
    .van-icon-arrow {
      padding: 0 3px;
      font-weight: bold;
      color: #00000099;
      font-size: 10px;
    }
  }
  :deep(.third-task) {
    .task-title {
      padding-left: 28px;
    }
    .task-bottom {
      padding-left: 48px;
    }
  }
}
</style>
