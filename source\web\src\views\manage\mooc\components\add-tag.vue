<template>
  <div class="addTagContainer">
    <el-tag class="default-el-tag custom-dark-tag" v-for="(tag, index) in tags" closable :key="index" 
      @close="handleClose(tag)">
      {{ tag.label_name }}
    </el-tag>
    <span class="add-tag-btn" @click="add"><i class="el-icon-plus"></i>添加标签</span>
  </div>
</template>
<script>
export default {
  props: {
    tags: {
      type: Array,
      default: () => ([])
    }
  },
  methods: {
    add() {
      this.$emit('addTag')
    },
    handleClose(tag) {
      this.tags.splice(this.tags.indexOf(tag), 1)
    }
  }
}
</script>
<style lang="less" scoped>
.addTagContainer {
  .el-tag {
    margin-right: 16px;
  }
  .add-tag-btn {
    color:#0052D9;
    cursor: pointer;
  }
}
</style>
