<template>
  <div class="list-dialog">
    <el-dialog
      width="900px"
      :visible="visible"
      title="添加考试"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <div class="course-body">
        <div class="course-body-title">
          <div class="check-label-warning">
            <span>
              <i class="el-icon-warning-outline"></i>
              仅支持添加本人创建/负责的考试，若无考试创建权限，请联系v_huiahuang
            </span>
          </div>
          <el-button type="primary" size="small" @click="createdExam">创建考试</el-button>
        </div>
        <el-form ref="form" :model="form" inline>
          <el-form-item>
            <el-cascader
              placeholder="请选择考试分类"
              v-model="form.category_id" 
              :options="classifiyOptions" 
              :props="classifyProps"
              label="classify_name" 
              clearable
              @change="handleTopCategoryId"
              >
            </el-cascader>
          </el-form-item>
          <el-form-item>
            <el-input
              clearable
              v-model="form.exam_name"
              placeholder="请输入考试名称"
              suffix-icon="el-icon-search"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="onSearch(1)">搜索</el-button>
            <el-button size="small" plain @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table
          :data="tableData.records"
          @selection-change="selectionChange"
          max-height="551px"
        >
          <!-- 单选 -->
          <el-table-column width="55" v-if="entryType === 'change'"> 
            <template slot-scope="scope">
              <el-radio @change="handleRowChange(scope.row)" v-model="tableRadio" :label="scope.row.id">&nbsp;</el-radio>
            </template>
          </el-table-column>
          <!-- 多选 -->
          <el-table-column width="55" type="selection" v-else></el-table-column>
          <el-table-column prop="id" label="考试id" width="100"></el-table-column>
          <el-table-column prop="exam_name" label="考试名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="updated_at" label="考试时间" show-overflow-tooltip>
            <template slot-scope="scope">
              <span>{{ scope.row.exam_time_type === 1 ? '不限制' : `${scope.row.exam_start_time} 至 ${scope.row.exam_end_time}` }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="category_name" label="考试分类" show-overflow-tooltip></el-table-column>
          <el-table-column prop="created_at" label="创建时间"></el-table-column>
        </el-table>
        <el-pagination
          v-if="tableData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="current"
          :page-sizes="[5, 10, 20, 30, 50, 100]"
          :page-size="size"
          layout="total,  prev, pager, next, sizes, jumper"
          :total="tableData.total"
          >
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="cancel">取 消</el-button>
        <el-button size="small" @click="submit" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <addErrorDialog :visible.sync="addErrorDialogShow" :taskNameList="taskNameList"></addErrorDialog>
  </div>
</template>
<script>
import pager from '@/mixins/pager'
import { getExamClassify } from 'config/api.conf'
import { getExamList } from 'config/mooc.api.conf'
import addErrorDialog from './add-error-dialog.vue'
import { mapState } from 'vuex'
export default {
  mixins: [pager],
  components: {
    addErrorDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentNode: {
      type: Object
    },
    treeNode: {
      type: Array,
      default: () => ([])
    },
    entryType: {
      type: String
    }
  },
  data() {
    return {
      tableRadio: '',
      form: {
        category_id: '',
        exam_name: '',
        top_parent_category_id: ''
      },
      classifiyOptions: [],
      classifyProps: {
        multiple: false,
        label: 'category_name', 
        value: 'id',
        children: 'sub_categorys',
        checkStrictly: true, 
        emitPath: false
      },
      tableData: {
        records: [],
        total: 0
      },
      selectionList: [],
      taskNameList: [],
      addErrorDialogShow: false,
      size: 5
    }
  },
  computed: {
    ...mapState(['userInfo'])
  },
  created() {
    this.getClassifyData()
    this.onSearch(1)
  },
  methods: {
    handleRowChange(val) {
      this.selectionList = [val]
    },
    handleTopCategoryId(val) {
      this.form.category_id = val
      const getdata = (list) => {
        list.forEach((v) => {
          if (val === v.id) {
            this.form.top_parent_category_id = v.parentId
          }
          if (v.sub_categorys?.length) {
            getdata(v.sub_categorys)
          }
        })
      }
      getdata(this.classifiyOptions)
    },
    onSearch(page_no = 1) {
      const { category_id, exam_name, top_parent_category_id } = this.form
      const data = {
        app_id: 'QLearningService',
        tenant_code: 'tencent',
        category_id, // category_id
        top_parent_category_id: category_id && top_parent_category_id ? top_parent_category_id : '',
        exam_name: exam_name,
        page_index: page_no,
        page_size: this.size
      }
      getExamList(data).then(res => {
        this.tableData.records = res.records.map((v) => {
          const time = v.created_at.split('.')[0]
          const exam_name = v.exam_name ? v.exam_name : v.exam_name_en
          return {
            ...v,
            created_at: time,
            exam_name
          }
        })
        this.tableData.total = res.record_count
      })
    },
    getClassifyData() {
      getExamClassify({ category_type: 3 }).then((res) => {
        const forMat = (list, parentId) => {
          list.forEach((v) => {
            v.parentId = parentId || v.id
            if (v.sub_categorys?.length) {
              forMat(v.sub_categorys, v.parentId)
            }
          })
          return list
        }
        this.classifiyOptions = forMat(res)
        // 分类再次操作卡死处理
        setTimeout(() => {
          const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]')
          if ($el.length > 0) {
            Array.from($el).map(item => item.removeAttribute('aria-owns'))
          }
        }, 200)
      })
    },
    selectionChange(val) {
      this.selectionList = val
    },
    submit() {
      if (this.selectionList.length === 0) {
        this.$message.error('请选择一个考试')
        return
      }
      // this.taskNameList = []
      // let newTaskList = []
      // if (this.currentNode?.id) { // 当前阶段
      //   newTaskList = (this.currentNode?.sub_tasks || []).filter((e) => e.task_type === 'task')
      // } else { // 当前任务树
      //   newTaskList = JSON.parse(JSON.stringify(this.treeNode)) 
      // }
      // newTaskList.forEach((v) => {
      //   this.selectionList.forEach((a) => {
      //     if (v.task_name === a.exam_name) {
      //       this.taskNameList.push({
      //         name: v.task_name
      //       })
      //     }
      //   })
      // })
      // if (this.taskNameList?.length) {
      //   this.addErrorDialogShow = true
      //   return
      // }
      this.selectionList = this.selectionList.map((e) => {
        return {
          ...e,
          id: this.currentNode?.id || '',
          resource_type: 'Exam',
          resource_type_name: '考试',
          required: true,
          module_name: '考试',
          module_id: 11,
          act_id: e.id,
          task_name: e.exam_name,
          duration: e.exam_duration,
          act_type: 20,
          act_name: e.exam_name
        }
      })
      this.$emit('updateTreeList', this.selectionList)
      this.$emit('update:visible', false)
      this.$message.success('添加考试成功')
      if (this.entryType === 'change') {
        this.$emit('changeSingleData', ...this.selectionList)
      }
    },
    handleReset() {
      this.form = {
        category_id: '',
        exam_name: ''
      }
      this.current = 1
      this.size = 5
      this.onSearch()
    },
    createdExam() {
      const url = process.env.NODE_ENV === 'production' ? '//exam.woa.com/exam/manage/examAddNew' : '//dev.ntsapps.oa.com/exam/manage/examAddNew'
      window.open(url)
    },
    cancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.list-dialog {
  .course-body-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .check-label-warning {
    color: #ff7548;
    background-color: rgba(253, 246, 236, 1);
    padding: 10px 16px;
    margin-bottom: 10px;
    display: inline-block;
    i {
      margin-right: 10px;
      font-size: 20px;
    }
  }
}

</style>
