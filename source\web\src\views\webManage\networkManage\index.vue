<template>
  <div class="network-container">
    <div class="header-title">
      <p class="title">评审管理</p>
    </div>
    <div>
      <el-tabs type="border-card" class="border-card-header">
        <el-tab-pane label="入库审核">
          <el-form label-width="130px" :model="form" ref="searchForm" :inline="true" class="search-form-style">
            <el-row :gutter="12">
              <el-col :span="4.8">
                <el-form-item label="内容ID：" prop="content_id">
                  <el-input v-model="form.content_id" placeholder="请输入内容ID" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="4.8">
                <el-form-item label="内容名称：" prop="course_name">
                  <el-input v-model="form.course_name" placeholder="请输入内容名称" clearable></el-input>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="4.8">
                <el-form-item label="创作者：" prop="inner_teacher_names">
                  <el-input v-model="form.inner_teacher_names" placeholder="请输入创作者名称" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="4.8">
                <el-form-item label="内容管理员：" prop="admin">
                  <el-input v-model="form.admin" placeholder="请输入内容管理员" clearable></el-input>
                </el-form-item>
              </el-col> -->
              <el-col :span="4.8">
                <el-form-item label="审批人：" prop="last_approve_user_name">
                  <el-input v-model="form.last_approve_user_name" placeholder="请输入审批人" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="4.8">
                <!-- <el-form-item label="创作来源：" prop="creation_source">
                  <el-select clearable v-model="form.creation_source" placeholder="请选择" collapse-tags>
                    <el-option label="PGC" :value="0"></el-option>
                    <el-option label="PUGC" :value="3"></el-option>
                    <el-option label="UGC" :value="2"></el-option>
                    <el-option label="OGC" :value="1"></el-option>
                  </el-select>
                </el-form-item> -->
                <el-form-item label="创建者：" prop="creator_name">
                  <el-input v-model="form.creator_name" placeholder="请输入创建者" clearable></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="4.8">
                <el-form-item label="素材类型：">
                  <el-select clearable v-model="form.course_type" placeholder="请选择" collapse-tags>
                    <el-option v-for="item in uploadOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="4.8">
                <el-form-item label="创作/采购组织：" prop="creator_org">
                  <el-input v-model="form.creator_org" placeholder="请输入组织名称" clearable></el-input>
                </el-form-item>
              </el-col> -->
              <el-col :span="4.8">
                <el-form-item label="内容状态：">
                  <el-select clearable v-model="form.course_status" placeholder="请选择" collapse-tags>
                    <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="4.8">
                <el-form-item label="QL审批：">
                  <el-select clearable v-model="form.approve_status" placeholder="请选择" collapse-tags>
                    <el-option v-for="item in approveOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="4.8">
                <el-form-item label="信安审核：">
                  <el-select clearable v-model="form.sec_status" placeholder="请选择" collapse-tags>
                    <el-option v-for="item in safeOptions" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="18">
                <el-radio-group v-model="form.act_type" size="medium" @input="actTypeChange">
                  <el-radio-button v-for="item in actTypeOptions" :key="item.value" :label="item.value">{{item.label}}</el-radio-button>
                </el-radio-group>
              </el-col>
              <el-col :span="6" style="position: relative; height:50px;">
                <el-form-item class="serach-btn-item">
                  <el-button type="primary" @click="onSearch(1)" size="small">搜索</el-button>
                  <el-button @click="handleExport" type="primary" size="small">导出报表</el-button>
                  <el-button @click="handleReset" size="small">重置</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <!-- <div class="status-box"> 
      <div>
        <span>状态：</span>
        <el-radio-group v-model="status">
          <el-radio :label="''">不限</el-radio>
          <el-radio :label="0">待审核</el-radio>
          <el-radio :label="1">已通过</el-radio>
          <el-radio :label="2">已拒绝</el-radio>
        </el-radio-group>
      </div>
      
      <div class="upload-type">
        <span>上传类型：</span>
        <el-radio-group v-model="upload_type">
          <el-radio :label="''">不限</el-radio>
          <el-radio :label="0">AI做课</el-radio>
          <el-radio :label="1">直接上传</el-radio>
        </el-radio-group>
      </div>
    </div> -->
          <div class="table-content">
            <el-table ref="approveTable" :data="records" @sort-change="sortChange" border style="width:100%">
              <el-table-column prop="course_id" label="内容id"></el-table-column>
              <el-table-column prop="course_name" label="内容名称" show-overflow-tooltip>
                <template slot-scope="scope">
                  <!-- <el-link :href="href(scope.row)" target="_blank" type="primary"></el-link> -->
                  <span class="link_href" @click="handelrLink(scope.row)"> {{ scope.row.course_name }} </span>
                </template>
              </el-table-column>
              <el-table-column prop="act_type" label="内容类型" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>{{ actType[scope.row.act_type] }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="course_type" label="素材类型">
                <template slot-scope="scope">
                  <span>{{ upload_info[scope.row.course_type] || '-' }}</span>
                  <!-- <span>{{ scope.row.course_type }}</span> -->
                </template>
              </el-table-column>
              <el-table-column prop="info_sec_status" label="信安审核">
                <template slot-scope="scope">
                  <span :class="[{'no-pass-infoStatus':2 === scope.row.info_sec_status}, {'deal-color': scope.row.info_sec_status === 0}]">{{ safeInfo[scope.row.info_sec_status] }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="approve_status" label="QL审核">
                <template slot-scope="scope">
                  <span :class="[{'deal-color': scope.row.approve_status === '6'}]">{{ approve_status_info[scope.row.approve_status] || '-'}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="status" label="内容状态">
                <template slot-scope="scope">
                  <span :class="[{'deal-color': scope.row.status === '6'}]">{{ status_info[scope.row.status] }}</span>
                </template>
              </el-table-column>
             
              <el-table-column prop="creation_source" label="创作来源">
                <template slot-scope="scope">
                  <span>{{ scope.row.creation_source }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="inner_teacher_names" label="组织/创作者" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span style="width: 160px;display: inline-block; overflow: hidden; text-overflow: ellipsis;">{{`${pgcCreationOrg(scope.row)}`}}{{shareName(scope.row) && pgcCreationOrg(scope.row) ? '/' : '' }} {{ shareName(scope.row) }} {{!shareName(scope.row) && !pgcCreationOrg(scope.row) ? '-' : '' }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="inner_teacher_names" label="创建人" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span style="width: 160px;display: inline-block; overflow: hidden; text-overflow: ellipsis;">{{scope.row.creator_name || '-'}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="update_name" label="修改人" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span style="width: 160px;display: inline-block; overflow: hidden; text-overflow: ellipsis;">{{scope.row.update_name || '-'}}</span>
                </template>
              </el-table-column>
              <el-table-column prop="admin_name" label="内容管理员" show-overflow-tooltip>
                  <template slot-scope="scope"> {{scope.row.admin_name || '-'}}</template>
              </el-table-column>
              <el-table-column prop="approve_name" label="审批人">
                <template slot-scope="scope"> {{scope.row.approve_name || '-'}}</template>
              </el-table-column>
              <el-table-column prop="order_by_creat" label="创建时间" show-overflow-tooltip sortable="custom" :sort-orders="['descending', 'ascending', null]">
                <template slot-scope="scope">{{ scope.row.created_at || '-'}}</template>
              </el-table-column>
              <el-table-column prop="order_by_approve" label="审批时间" show-overflow-tooltip sortable="custom" :sort-orders="['descending', 'ascending', null]">
                <template slot-scope="scope">{{ scope.row.approve_time || '-'}}</template>
              </el-table-column>
              <el-table-column prop="order_by_update" label="更新时间" show-overflow-tooltip sortable="custom" :sort-orders="['descending', 'ascending', null]">
                <template slot-scope="scope">{{ scope.row.updated_at || '-'}}</template>
              </el-table-column>
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <!-- <el-button :disabled="!['1'].includes(scope.row.status)" type="text" @click="toDetail(scope.row)">详情</el-button> -->
                  <el-button :disabled="![2].includes(scope.row.info_sec_status) && ([0].includes(scope.row.info_sec_status) || ['6'].includes(scope.row.approve_status))" type="text" @click="toEdit(scope.row)">编辑</el-button>
                  <!-- 最后换成 approve_status 禁用 -->
                  <el-button type="text" :disabled="[0, 2].includes(scope.row.info_sec_status) || !['6'].includes(scope.row.approve_status)" @click="handleApprove(scope.row)">审批</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <el-pagination v-if="tableData.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="current" :page-sizes="[5, 10, 20, 30, 50, 100]" :page-size="size" layout="total,  prev, pager, next, sizes, jumper" :total="tableData.total">
          </el-pagination>
        </el-tab-pane>
        <!-- <el-tab-pane label="质量评审">质量评审</el-tab-pane>
        <el-tab-pane label="认证评审">认证评审</el-tab-pane> -->
      </el-tabs>
    </div>

  </div>
</template>
<script>
import { courseReviewList } from '@/config/api.conf.js'
import { getSpocCourseList } from '@/config/mooc.api.conf.js'
import pager from '@/mixins/pager.vue'
import axios from 'axios'
import env from 'config/env.conf.js'
const status_info = {
  0: '待发布',
  1: '上架',
  2: '已结束',
  3: '下架',
  4: '草稿',
  5: '处理中',
  6: '待审核',
  7: '审核通过',
  8: '审核未通过',
  9: '合成失败'
}
const upload_info = {
  Video: '视频',
  Audio: '音频',
  'Video-ppt': 'AI做课-PPT',
  'Video-2d': 'AI做课-PPT(2D)',
  'Video-3d': 'AI做课-PPT(3D)'
}
const approve_status_info = {
  6: '待审核',
  7: '审核通过',
  8: '不通过'
}
const actType = {
  1: '面授课',
  4: '活动',
  2: '网络课',
  11: 'MOOC',
  5: '直播',
  18: '文章',
  10: '文档',
  27: 'SPOC'
}
const safeInfo = {
  null: '审核通过',
  0: '待审核',
  1: '审核通过',
  2: '审核不通过'
}
const defaultFrom = {
  act_type: 1,
  content_id: '', // 课程id
  course_name: '', // 课程名称
  inner_teacher_names: '', // 创作者
  admin: '', // 管理员
  last_approve_user_name: '', // 审批人
  // creation_source: '', // 创作来源枚举值：“PGC”“UGC” "OGC" "PUGC"
  creator_name: '', // 创建者
  course_type: '', // 课程类型
  creator_org: '', // 创作组织
  course_status: '', // 课程状态
  approve_status: 6, // 审核状态：6, "待审核" 7, "审核通过" 8, "不通过"
  order_by_creat: '',
  order_by_approve: '',
  order_by_update: '',
  sec_status: ''
  // upload_type: []
}
export default {
  mixins: [pager],
  data() {
    return {
      radio2: '',
      form: Object.assign({}, defaultFrom),
      status: [],
      records: [],
      upload_type: [], // null-全部 0 Ai做课 1 直接上传
      tableData: {
        total: 0,
        records: []
      },
      status_info,
      upload_info,
      approve_status_info,
      safeInfo,
      actType,
      actTypeOptions: [
        { value: 1, label: '面授课' },
        { value: 2, label: '网络课' },
        { value: 11, label: 'MOOC' },
        // { value: 5, label: '直播' },
        { value: 18, label: '文章' },
        { value: 4, label: '活动' },
        { value: 27, label: 'SPOC' },
        { value: 10, label: '文档' }
      ],
      approveOptions: [
        { value: 6, label: '待审核' },
        { value: 7, label: '审核通过' },
        { value: 8, label: '不通过' }
      ],
      safeOptions: [
        { value: 0, label: '待审核' },
        { value: 1, label: '审核通过' },
        { value: 2, label: '审核不通过' }
      ],
      statusOptions: [
        // { value: '', label: '不限' },
        { value: 0, label: '待发布' },
        { value: 1, label: '上架' },
        { value: 2, label: '已结束' },
        { value: 3, label: '下架' },
        { value: 4, label: '草稿' },
        { value: 5, label: '处理中' },
        // { value: 6, label: '待审核' },
        // { value: 7, label: '审核通过' },
        // { value: 8, label: '审核未通过' },
        { value: 9, label: '合成失败' }
      ],
      uploadOptions: [
        // { value: '', label: '不限' },
        // { value: 0, label: '直接上传' },
        // { value: 1, label: 'AI做课-PPT' },
        // { value: 2, label: 'AI做课-PPT(2D)' },
        // { value: 3, label: 'AI做课-PPT(3D)' }
        { value: 'Video', label: '视频' },
        { value: 'Audio', label: '音频' },
        { value: 'Video-ppt', label: 'AI做课-PPT' }
      ]
    }
  },
  computed: {
    href() {
      return ({ net_course_id }) => {
        let url = ''
        let isProduction = process.env.NODE_ENV === 'production'
        if (this.form.act_type === 1) {
          // 面授课跳转
          if (isProduction) {
            url = `//learn.woa.com/user/face?course_id=${net_course_id}`
          } else {
            url = `//test-learn.woa.com/user/face?course_id=${net_course_id}`
          }
        } else if (this.form.act_type === 2) {
          if (isProduction) {
            url = `/training/netcourse/play?course_id=${net_course_id}`
          } else {
            url = `//test-portal-learn.woa.com/training/netcourse/play?course_id=${net_course_id}`
          }
        }
        return url
      }
    },
    shareName() {
      return (row) => {
        // let name = ''
        // Array.isArray(row.inner_teacher_names) &&
        //   row.inner_teacher_names.forEach((e) => {
        //     return (name += `${e.teacher_name};`)
        //   })
        return row.inner_teacher_names || ''
      }
    },
    pgcCreationOrg() {
      // pgc_creation_org
      return (row) => {
        let creation_org = ''
        if (row.content_type === 'OGC') {
          creation_org = row.ogc_purchase_org
        } else if (row.content_type === 'PGC') {
          creation_org = row.pgc_creation_org
        } else if (row.content_type === 'PUGC') {
          creation_org = row.pugc_creation_org
        }
        if (!creation_org) return ''
        let org = JSON.parse(creation_org)
        if (Array.isArray(org)) {
          console.log(org, 'creation_orgcreation_orgcreation_org')
          let orgNames = ''
          org.forEach((item, index) => {
            orgNames += `${index ? '/' : ''}${item.UnitName}`
          })
          return orgNames
        } else {
          return ''
        }
      }
    }
  },
  created() {
    window.workReConnect = (workData) => {
      this.onSearch()
    }
  },
  mounted() {
    this.onSearch()
  },
  methods: {
    handelrLink(row) {
      const { course_id } = row
      let url = ''
      let isProduction = process.env.NODE_ENV === 'production'
      if (this.form.act_type === 1) {
        // 面授课跳转
        if (isProduction) {
          url = `//learn.woa.com/user/face?course_id=${course_id}`
        } else {
          url = `//test-learn.woa.com/user/face?course_id=${course_id}`
        }
      } else if (this.form.act_type === 2) {
        if (isProduction) {
          url = `/training/netcourse/play?course_id=${course_id}`
        } else {
          url = `//test-portal-learn.woa.com/training/netcourse/play?course_id=${course_id}`
        }
      } else if (this.form.act_type === 18) { // 图文
        if (isProduction) {
          url = `/training/graphic/user/preview?graphic_id=${course_id}`
        } else {
          url = `//test-portal-learn.woa.com/training/graphic/user/preview?graphic_id=${course_id}`
        }
      } else if (this.form.act_type === 4) { // 活动
        if (isProduction) {
          url = `//learn.woa.com/user/activity/detail?activity_id=${course_id}`
        } else {
          url = `//test-learn.woa.com/user/activity/detail?activity_id=${course_id}`
        }
      } else if (this.form.act_type === 11) {
        url = `/training/mooc/projectDetail?mooc_course_id=${course_id}`
      } else if (this.form.act_type === 10) { // 文档
        if (isProduction) {
          url = `//learn.woa.com/user/profile?wordId=${course_id}`
        } else {
          url = `//test-learn.woa.com/user/profile?wordId=${course_id}`
        }
      } else if (this.form.act_type === 27) {
        url = isProduction ? `https://portal.learn.woa.com/training/management/#/user/semester-detail?semesterId=${course_id}` : `https://test-learn.woa.com/training/management/#/user/semester-detail?semesterId=${course_id}`
      }
      window.open(url, '_blank')
    },
    onSearch(page_no = 1) {
      // const { course_name, author_name, admin_name } = this.form
      // let status = ''
      // let upload_type = ''
      // this.status.forEach((e) => {
      //   status += `${e},`
      // })
      // this.upload_type.forEach((e) => {
      //   upload_type += `${e},`
      // })
      let searchApi = this.form.act_type !== 27 ? courseReviewList : getSpocCourseList
      let params = {
        page_no,
        page_size: this.size
        // status,
        // upload_type
      }
      for (let key in this.form) {
        if (this.form[key] !== '' && !Array.isArray(this.form[key])) {
          params[key] = this.form[key]
        } else if (Array.isArray(this.form[key]) && this.form[key].length > 0) {
          params[key] = this.form[key].join(',')
        }
      }
      searchApi(params).then((res) => {
        this.$forceUpdate()
        console.log(res, 'res')
        const { records } = res
        // this.records = []
        this.records = JSON.parse(JSON.stringify(records))
        this.tableData.total = res.total
      })
    },
    actTypeChange() {
      this.size = 10
      this.current = 1
      this.onSearch()
    },
    sortChange(val) {
      ;['order_by_creat', 'order_by_approve', 'order_by_update'].forEach((item) => {
        this.form[item] = ''
      })
      const { order, prop } = val
      if (order === 'descending') {
        this.form[prop] = 'desc'
      } else if (order === 'ascending') {
        this.form[prop] = 'asc'
      } else {
        delete this.form[prop]
      }
      this.onSearch()
    },
    // 详情
    toDetail({ net_course_id }) {
      const { href } = this.$router.resolve({
        name: 'play',
        query: { course_id: net_course_id }
      })
      window.open(href, '_blank')
    },
    // 编辑
    toEdit(row) {
      const { course_id } = row
      let isProduction = process.env.NODE_ENV === 'production'
      let url = ''
      if (this.form.act_type === 1) {
        isProduction
          ? (url = `//learn.woa.com/manage/face/bgcourse?course_id=${course_id}`)
          : (url = `//test-learn.woa.com/manage/face/bgcourse?course_id=${course_id}`)
      } else if (this.form.act_type === 2) {
        if (['Video-ppt'].includes(row.course_type)) {
          url = `/creator-center/course-ppt?net_course_id=${course_id}`
          isProduction ? (url = `//learn.woa.com/training` + url) : (url = `//test-portal-learn.woa.com/training` + url)
        } else if (['Video-2d', 'Video-3d'].includes(row.course_type)) {
          url = `/creator-center/course-2d?net_course_id=${course_id}`
          isProduction ? (url = `//learn.woa.com/training` + url) : (url = `//test-portal-learn.woa.com/training` + url)
        } else {
          isProduction ? (url = `//learn.woa.com/manage/net/eidt?net_course_id=${course_id}`) : (url = `//test-learn.woa.com/manage/net/eidt?net_course_id=${course_id}`)
        }
      } else if (this.form.act_type === 18) { // 图文
        isProduction
          ? (url = `//learn.woa.com/training/graphic/user/create?graphic_id=${course_id}&from=networkManage`)
          : (url = `//test-learn.woa.com/training/graphic/user/create?graphic_id=${course_id}&from=networkManage`)
      } else if (this.form.act_type === 4) { // 活动
        isProduction
          ? (url = `//learn.woa.com/manage/activity/edit?activity_id=${course_id}`)
          : (url = `//test-learn.woa.com/manage/activity/edit?activity_id=${course_id}`)
      } else if (this.form.act_type === 11) {
        url = `/training/mooc/manage/basic-setting?mooc_course_id=${course_id}&approve=0`
      } else if (this.form.act_type === 10) { // 文档
        isProduction
          ? (url = `//learn.woa.com/manage/word/edit?word_id=${course_id}`)
          : (url = `//test-learn.woa.com/manage/word/edit?word_id=${course_id}`)
      } else if (this.form.act_type === 27) {
        const host = isProduction ? 'https://portal.learn.woa.com' : 'https://test-learn.woa.com'
        // 区分新旧学期
        url = row.training_organization ? host + `/training/management/#/semester-handle/new?type=edit&semesterId=${course_id}` : host + `/training/management/#/semester-handle?type=edit&semesterId=${course_id}`
      }
      window.open(url, '_blank')
      // 2
      // https://test-learn.woa.com/manage/net/eidt?net_course_id=10991
      // 1
      // https://test-learn.woa.com/manage/face/bgcourse?course_id=101059
    },
    // 审批
    handleApprove(row) {
      // { course_type, net_course_id }
      // if (course_type === 'Video') {
      //   const { href } = this.$router.resolve({
      //     name: 'directyleApprove',
      //     query: { net_course_id }
      //   })
      //   window.open(href, '_blank')
      //   return
      // }
      // const { href } = this.$router.resolve({
      //   name: 'aiApprove',
      //   query: { net_course_id }
      // })
      // window.open(href, '_blank')
      const { course_id, course_level } = row
      let isProduction = process.env.NODE_ENV === 'production'
      let url = ''
      if (this.form.act_type === 1) {
        isProduction
          ? (url = `//learn.woa.com/manage/face/${Number(course_level) === 1 ? 'company' : 'bgcourse'}?approve=1&course_id=${course_id}`)
          : (url = `//test-learn.woa.com/manage/face/${Number(course_level) === 1 ? 'company' : 'bgcourse'}?approve=1&course_id=${course_id}`)
      } else if (this.form.act_type === 2) {
        if (['Video-ppt', 'Video-2d', 'Video-3d'].includes(row.course_type)) {
          isProduction
            ? (url = `/training/manage/ai-approve?net_course_id=${course_id}`)
            : (url = `//test-portal-learn.woa.com/training/manage/ai-approve?net_course_id=${course_id}`)
        } else {
          isProduction
            ? (url = `/training/manage/directyleApprove?net_course_id=${course_id}`)
            : (url = `//test-portal-learn.woa.com/training/manage/directyleApprove?net_course_id=${course_id}`)
        }
      } else if (this.form.act_type === 18) { // 图文
        isProduction
          ? (url = `/training/manage/graphic-approve?graphic_id=${course_id}`)
          : (url = `//test-portal-learn.woa.com/training/manage/graphic-approve?graphic_id=${course_id}`)
      } else if (this.form.act_type === 4) { // 活动
        isProduction
          ? (url = `//learn.woa.com/manage/activity/edit?activity_id=${course_id}&approve=1`)
          : (url = `//test-learn.woa.com/manage/activity/edit?activity_id=${course_id}&approve=1`)
      } else if (this.form.act_type === 11) {
        url = `/training/mooc/manage/basic-setting?mooc_course_id=${course_id}&approve=1`
      } else if (this.form.act_type === 10) { // 文档
        isProduction
          ? (url = `//learn.woa.com/manage/word/edit?word_id=${course_id}&approve=1`)
          : (url = `//test-learn.woa.com/manage/word/edit?word_id=${course_id}&approve=1`)
      } else if (this.form.act_type === 27) { // SPOC
        isProduction
          ? (url = `https://portal.learn.woa.com/training/management/#/semester-approval?semesterId=${course_id}`)
          : (url = `https://test-portal-learn.woa.com/training/management/#/semester-approval?semesterId=${course_id}`)
      }
      window.open(url, '_blank')
    },
    handleReset() {
      this.form = Object.assign({}, defaultFrom)
      this.size = 10
      this.current = 1
      this.$refs['approveTable'].clearSort()
      this.onSearch()
    },
    // 导出
    handleExport() {
      // const { course_name, author_name, admin_name } = this.form
      // const params = {
      //   page_size: this.size,
      //   course_name,
      //   author_name,
      //   admin_name,
      //   status: this.status,
      //   upload_type: this.upload_type
      // }
      let params = {
        page_size: this.size
      }
      for (let key in this.form) {
        if (this.form[key] && !Array.isArray(this.form[key])) {
          params[key] = this.form[key]
        } else if (Array.isArray(this.form[key]) && this.form[key].length > 0) {
          params[key] = this.form[key].join(',')
        }
      }
      let url
      switch (this.form.act_type) {
        case 27:
          url = `${
            env[process.env.NODE_ENV].spocHost.replace('/mobile', '')}semesterReview/exportCourseList?page_no=${this.current}`
          break
        default:
          url = `${
            env[process.env.NODE_ENV].trainingPath
          }api/businessCommon/manage/courseReview/exportCourseList?page_no=${
            this.current
          }`
      }

      for (let pre in params) {
        url += `&${pre}=${params[pre]}`
      }
      axios({
        url,
        method: 'get',
        responseType: 'blob'
      }).then((response) => {
        if (response.status === 200 && response.data) {
          const url = window.URL.createObjectURL(new Blob([response.data]))
          const link = document.createElement('a')
          link.href = url
          link.setAttribute('download', '审核列表.xlsx')
          document.body.appendChild(link)
          link.click()
          this.$message.success('导出成功')
        } else {
          this.$message.error('导出失败')
        }
      })
    }
  }
}
</script>
<style lang="less">
.main-content-manage {
  margin: unset;
  background-color: #f6f7f9;
  padding: 20px 24px;
}
</style>
<style lang="less" scoped>
.network-container {
  background-color: #fff;
  padding: 20px;
  min-width: 1200px;
  .header-title {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
  }
  .title {
    font-size: 18px;
    font-weight: bold;
    // line-height: 40px;
  }
  .border-card-header {
    /deep/.el-tabs__item {
      padding: 0 20px;
      height: 40px;
      line-height: 40px;
      margin: 0;
      margin-left: -1px;
    }
  }
  .table-content {
    // max-height: 1500px;
  }
  .link_href {
    color: #0052d9;
    cursor: pointer;
  }
  .serach-btn-item {
    position: absolute;
    right: 0;
  }
  .upload-type {
    margin-left: 100px;
  }
  .status-box {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    line-height: 32px;
  }
  .no-pass-infoStatus {
    color: red;
  }
  .deal-color {
    color:rgba(0,82,217,1);
  }
}
:deep(.el-select .el-tag.el-tag--info .el-tag__close) {
  display: none;
}
</style>
