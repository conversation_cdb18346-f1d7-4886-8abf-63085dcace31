/**
 * 按需加载
 */
import Vue from 'vue'
import { render } from 'sdc-vue'
import { ele, sdc, vant } from 'plugins'
import sdcComment from 'sdc-comment'
import 'sdc-comment/lib/sdc-comment.css'
import busPlugin from './bus'
/* eslint-disable */
import sdcMceEditor from 'sdc-ui-rte'
import sdcMcePreview from 'sdc-ui-rte'
import VueQr from 'vue-qr'
/* eslint-disable */
import VueAwesomeSwiper from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'
import sdcLearnAiBot from '@tencent/sdc-learn-ai-bot'
import '@tencent/sdc-learn-ai-bot/lib/sdc-learn-ai-bot.css'
// @tencent/sdc-addlabel 后台/管理员打标签组件

// @tencent/sdc-img-cover 一键封面组件
import sdcImgCover from 'sdc-img-cover' // 正式包
import 'sdc-img-cover/lib/sdc-img-cover.css' // 正式包
// import sdcImgCover from '@tencent/sdc-img-cover-test' // 测试包
// import '@tencent/sdc-img-cover-test/lib/sdc-img-cover-test.css' // 测试包

window.envChatAI = process.env.NODE_ENV === 'production' ? 'production': 'test'
// @tencent/sdc-label-show 标签展示组件
import sdcLabelShow from '@tencent/sdc-label-show'
import '@tencent/sdc-label-show/lib/sdc-label-show.css'

import '@tencent/sdc-addlabel/lib/sdc-addlabel.css'
import sdcAddLabel from '@tencent/sdc-addlabel'

// @tencent/sdc-labeling 用户端打标签组件
import sdcLabeling from '@tencent/sdc-labeling'
import '@tencent/sdc-labeling/lib/sdc-labeling.css'

// @tencent/sdc-search-label 搜索组件
import sdcSearchLabel from '@tencent/sdc-search-label'
import '@tencent/sdc-search-label/lib/sdc-search-label.css'

// 偏好设置
import sdcSubLabelManage from '@tencent/sdc-sub-label-manage'
import '@tencent/sdc-sub-label-manage/lib/sdc-sub-label-manage.css'

// 文件导出
import sdcExportData from '@tencent/sdc-export-data'
import '@tencent/sdc-export-data/lib/sdc-export-data.css'

import 'sdc-ui-rte/lib/sdc-ui-rte.css'
import i18n from '../locales/i18n'
import { init } from '@sdc-monitor/browser'
import { vuePlugin } from '@sdc-monitor/vue'

import { StaffSelector, UnitSelector, AreaSelector } from 'sdc-webui'

// 应用配置
import App from 'views/app'
import router from '../router'
import store from '../store'
import env from '../config/env.conf'
import { SDCPointReward, SDCPointRewardMob } from '@tencent/sdc-point-reward'
// 插件配置
import 'plugins/svg/'
import 'plugins/styles'
import AutoTrackBeacon from '@tencent/autotracker-beacon-oa';
import exposure from '@tencent/autotracker-beacon-oa/dist/plugin/exposure';

import regionalExposureReporting from '@tencent/sdc-regional-exposure-reporting'
// 权限控制
import sdcPermissionControl from '@tencent/sdc-permission-control'
import Vconsole from 'vconsole'
if (window.location.search.indexOf('debugger=true') > -1) {
  // eslint-disable-next-line no-new
  new Vconsole()
}

// import sdcMentorTest from '@tencent/sdc-mentor-test'
// console.log('sdcMentorTest: ', sdcMentorTest)

Vue.prototype.autoInstance = function(data) {
  const appkey = process.env.NODE_ENV === 'production' ? '0WEB05I0WC0H1J6I' : 'test0WEB05I0WC0H1J6I'
  const autoInstance = new AutoTrackBeacon({
    report: {
      enableReport: () => true,
      appkey, // 从datahub获取的appkey
      consolelog: process.env.NODE_ENV === 'production',
      commonParams: { // 自定义的上报公共参数, 每条上报都会携带
        uid: data?.staff_id || '', // 业务用户身份标示，推荐使用uid作为key
      },
      beforeReport: (type, reportParams, commonParams) => {
        if (type === 'at_imp') {
          let info = window?.$dtRegionalExposurePeporting.getPagePublicExposureInfo() || ''
          reportParams.A316 = ''
          if (info) reportParams.A316 = info
        }
        return {
          reportEventCode: type,
          reportParams
        }
      }
    },
    uselib: ['element'], // 预设了ui库track规则，包括omui,antd,element,tdesign等；不设置该项则没有预设规则，完全依据传入的track配置
  });
  autoInstance.use(exposure)
  // ！！！！初始化，注意这里要显式调用init方法
  autoInstance.init();
}

// 路由变化
router.beforeEach((to, from, next) => {
  // 修改title
  document.title = to.meta.title || ''
  // 无鉴权
  const { superAdminList, userLimitInfo } = store._vm._data.$$state
  const { supper_admin, staff_id } = userLimitInfo
  if (superAdminList !== undefined && staff_id && !supper_admin && to.fullPath.substr(0, 5) === '/mooc') {
    const len = superAdminList.length
    for (let i = 0; i < len; i++) {
      if (superAdminList[i].path === to.name) {
        next({ path: '/401' })
      }
    }
  }
  // 发布 直接审批 ai-审批，播放详情页在企微/微信提示用其他浏览器打开
  if (['aiCoursePublish', 'directyleApprove', 'aiApprove', 'coursePublish'].includes(to.name) && window.navigator.userAgent.indexOf('wxwork') > -1) {
    sessionStorage.setItem('mobile-err-href', window.location.href)
    next({ path: '/mobile-err' })
  }
  // if (['taskDetail', 'projectDetail'].includes(to.name) && window.navigator.userAgent.indexOf('wxwork') > -1) {
  //   sessionStorage.setItem('mobile-err-href', window.location.href)
  //   next({
  //     path: '/mobile-err',
  //     query: {
  //       label: 'mooc-error',
  //       page: to.name,
  //       ...to.query
  //     }
  //   })
  // }
  next()
})

// commentLocale.i18n((key, value) => i18n.t(key, value))

export default render(App, {
  router,
  store,
  env,
  i18n,
  plugins: [ele, sdc, vant],
  lazy: {
    preLoad: 1,
    loading: require('assets/img/loading-spinning-bubbles.svg')
  },
  init: Vue => {
    // 初始化...
    Vue.use(sdcComment, {
      i18n: (key, value) => i18n.t(key, value)
    })
    Vue.use(sdcImgCover)
    Vue.use(sdcAddLabel)
    Vue.use(sdcSearchLabel)
    Vue.use(sdcLabeling)
    Vue.use(sdcLabelShow)
    Vue.use(sdcMceEditor)
    Vue.use(sdcMcePreview)
    Vue.use(StaffSelector)
    Vue.use(UnitSelector)
    Vue.use(SDCPointReward)
    Vue.use(SDCPointRewardMob)
    Vue.use(VueQr)
    Vue.use(VueAwesomeSwiper)
    Vue.use(busPlugin),
    Vue.use(sdcLearnAiBot)
    Vue.use(sdcPermissionControl)
    Vue.use(sdcSubLabelManage)
    Vue.use(sdcExportData)
    Vue.use(regionalExposureReporting)
    // Vue.use(sdcMentorTest)
    Vue.use(AreaSelector)

    Vue.prototype.$langue = function (key, obj = {}) {
      if (typeof obj !== 'object' && !obj.defaultText) {
        const errorMsg = {
          message: `$langue(): 'defaultText' parameter cannot be empty。`,
          PC_key: key,
          data: obj
        }
        throw errorMsg
      }
      let config = JSON.parse(JSON.stringify(obj))
      delete config.defaultText
      const langue = this.$t(key, config)
      if (langue !== key) {
        return langue
      }
      return obj.defaultText
    }

    if (process.env.NODE_ENV === 'production') {
      init({
        apikey: 'mooc',
        debug: false,
        vue: Vue,
        dsn: env[process.env.NODE_ENV].commonPath + 'training-portal-common/api/front/log/upload',
        maxBreadcrumbs: 2
      }, [vuePlugin])
    }
  }
})
