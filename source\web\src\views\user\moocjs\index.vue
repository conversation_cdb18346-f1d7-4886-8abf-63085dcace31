<template>
  <div class="test-mooc-main">
    <div class="father-box">父级测试mooc.js</div>
    <el-button @click="handleFather">父级按钮</el-button>
    <iframe id="taskIframe" src="/training/moocChild" frameborder="0" allowfullscreen></iframe>
  </div>
</template>
<script>
import { SDKUtils } from 'sdc-moocjs'
export default {
  data() {
    return {
    }
  },
  mounted() {
    SDKUtils.registerMessageListener()
    SDKUtils.onload(() => {
      // 当任务完成条件为至少学习多少分钟时，通知v8系统隐藏视频完成状态
      let iframeDOM = this.getIframeDom()
      SDKUtils.hideFinishStatus(iframeDOM)
    })
    SDKUtils.onPlay(() => {
    })
    SDKUtils.onPause(() => {
    })
    SDKUtils.onComplete(res => {
      console.log('任务完成', res)
    })
    SDKUtils.onErrorInfo(res => {
    })
    SDKUtils.onStratAnswer(() => {
    })
    SDKUtils.onEndAnswer(res => {
    })
    SDKUtils.onAnswerDetail(() => {
    })
    SDKUtils.onDetailBackHome(() => {
    })
    // 所有来自iframe的消息事件
    SDKUtils.messageListener((res) => {
      console.log('子级事件', res)
    })
  },
  beforeDestroy() {
    SDKUtils.removeMessageListener()
  },
  methods: {
    handleFather() {
      const iframeDOM = this.getIframeDom()
      SDKUtils.postMessage(iframeDOM, 'father', { label: 'fasfd' })
    },
    getIframeDom() {
      return document.getElementById('taskIframe')
    }
  }
}
</script>
<style lang="less">
.test-mooc-main {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  .father-box {
    background-color: red;
    height: 500px;
    width: 500px;
    color: #fff;
    font-size: 40px;
    line-height: 500px;
    text-align: center;
  }
}
</style>
