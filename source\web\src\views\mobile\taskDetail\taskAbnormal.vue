<template>
  <div class="task-abnormal">
    <img class="img" :src="icon" alt="" />
    <p class="msg">{{ msg[type] }}</p>
    <div class="application-btn">
    <van-button v-if="type === '-2'" class="operator-btn" @click="addRegister" type="info">
      立即报名加入项目
    </van-button>
    </div>
    <div class="back" @click="onBack" v-if="from === 'mooc' && type !== '-1'">{{ $langue('Mooc_TaskDetail_BackProjectHomepage', { defaultText: '返回培养项目首页' }) }}</div>
    <div class="back" @click="onBack" v-else-if="from === 'spoc'">{{ $langue('Mooc_TaskDetail_BackClass', { defaultText: '返回班级详情' }) }}</div>
    <div class="admin" v-if="showLabel">
      <p class="label">{{ label[type] }}</p>
      <div class="admin-list">
        <div class="item" v-for="item in adminList" :key="item.admin_id">
          <img class="avatar" :src="item.url" alt="" />
          <span>{{ item.admin_name }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  personDetail,
  moocEnroll
} from '@/config/mooc.api.conf.js'
import { Toast, Dialog } from 'vant'
import { debounce } from '@/utils/tools.js'
export default {
  data() {
    return {
      mooc_course_id: '',
      type: '',
      from: '',
      class_id: '',
      adminList: [],
      icon: require('@/assets/img/mobile/task-permission.png'),
      // msg: {
      //   '-99': '',
      //   '-98': '任务已失效，无法学习，请联系管理员调整',
      //   '-97': '完成上一个应学任务后解锁',
      //   '-2': '请先报名加入项目',
      //   '-1': '暂无此课程的访问权限，请联系项目管理员进行调整',
      //   0: '项目未开始，无法开始学习',
      //   1: '项目已结束，无法继续学习',
      //   2: '已逾期，无法继续学习',
      //   3: '任务已被管理员锁定，无法学习',
      //   4: '项目未解锁，无法开始学习',
      //   5: '不支持PC端',
      //   6: '不支持移动端',
      //   7: '项目未发布'
      // },
      // label: {
      //   '-99': '',
      //   '-98': '联系项目管理员',
      //   '-97': '',
      //   '-2': '',
      //   '-1': '联系项目管理员',
      //   'errorInfo': '联系项目管理员',
      //   0: '',
      //   1: '',
      //   2: '如有需要，请联系项目管理员延长学习结束时间。',
      //   3: '',
      //   4: '',
      //   5: '',
      //   6: '',
      //   7: ''
      // },
      showLabel: false
    }
  },
  computed: {
    msg() {
      return {
        '-99': '',
        '-98': this.$langue('Mooc_Common_Authority_TaskDIsabled', { defaultText: '任务已失效，无法学习，请联系管理员调整' }),
        '-97': this.$langue('Mooc_ProjectDetail_TaskList_UnlockByPreTask', { defaultText: '完成上一个应学任务后解锁' }),
        '-2': this.$langue('Mooc_ProjectDetail_BasicInfo_CannotStudyNotInProj', { defaultText: '暂未加入培养项目，无法进行学习' }),
        '-1': this.$langue('Mooc_Common_Authority_NoArthority', { defaultText: '暂无此课程的访问权限，请联系项目管理员进行调整' }),
        0: this.$langue('Api_Mooc_Project_ProjectNotStart', { defaultText: '项目未开始，无法开始学习' }),
        1: this.$langue('Mooc_ProjectDetail_TrainingProgress_CannotStudyProjEnd', { defaultText: '项目已结束，无法继续学习' }),
        2: this.$langue('Api_Mooc_Project_ProjectDelayed', { defaultText: '已逾期，无法继续学习' }),
        3: this.$langue('Mooc_Common_Authority_NotStudyByAdminLocked', { defaultText: '任务已被管理员锁定，无法学习' }),
        4: this.$langue('Mooc_TaskDetail_ProgramLocked', { defaultText: '项目未解锁，无法开始学习' }),
        5: this.$langue('Mooc_TaskDetail_NotSupportedPC', { defaultText: '不支持PC端' }),
        6: this.$langue('Mooc_TaskDetail_NotSupportedMobile', { defaultText: '不支持移动端' }),
        7: this.$langue('Mooc_ProjectDetail_ProgramNotPublished', { defaultText: '项目未发布' })
      }
    },
    label() {
      return {
        '-99': '',
        '-98': this.$langue('Mooc_Common_Authority_ConnectAdmin', { defaultText: '联系项目管理员' }),
        '-97': '',
        '-2': '',
        '-1': this.$langue('Mooc_Common_Authority_ConnectAdmin', { defaultText: '联系项目管理员' }),
        'errorInfo': this.$langue('Mooc_Common_Authority_ConnectAdmin', { defaultText: '联系项目管理员' }),
        0: '',
        1: '',
        2: this.$langue('Mooc_Common_Authority_ExtendLearnTime', { defaultText: '如有需要，请联系项目管理员延长学习结束时间。' }),
        3: '',
        4: '',
        5: '',
        6: '',
        7: ''
      }
    }
  },
  created() {
    this.mooc_course_id = this.$route.query.mooc_course_id || ''
    this.type = this.$route.query.type + '' || '-99'
    this.from = this.$route.query.from
    console.log('页面type', this.type)
    this.class_id = this.$route.query.class_id
    this.showLabel = ['-98', '-1', '2', 'errorInfo'].includes(this.type) && this.from === 'mooc'
    if (this.type === 'errorInfo') {
      this.msg.errorInfo = this.$route.query.info + this.$langue('Mooc_TaskDetail_ContactAdmin', { defaultText: '请联系管理员调整' })
    }
    if (this.$route.query.admin) {
      this.adminList = JSON.parse(this.$route.query.admin).map((item) => {
        return {
          url: `https://learn.woa.com/rhrc/photo/150/${
            item.admin_name && item.admin_name.split('(')[0]
          }.png`,
          ...item
        }
      })
    }
    if (this.mooc_course_id) {
      this.getPersonDetail()
    }
  },
  methods: {
    getPersonDetail() {
      personDetail({ mooc_course_id: this.mooc_course_id }).then(res => {
        console.log('personDetail--autoSignUpParams', res)
        const { register, register_confirm, resource_from } = res
        if (register_confirm === 0 && !resource_from && !register && this.type === '-2') {
          this.debounceAutoSignUp()
        }
      })
    },
    // 自动报名防抖
    debounceAutoSignUp: debounce(function () {
      this.autoSignUp()
    }, 500),
    // 自动报名
    autoSignUp() {
      const params = {
        mooc_course_id: this.mooc_course_id || '',
        join_type: '2' // 自动报名为2
      }
      moocEnroll(params).then((res) => {
        Toast.loading({ duration: 0, forbidClick: true, message: this.$langue('Mooc_ProjectDetail_BasicInfo_RegistSucessedAuto', { default: '已自动报名加入培养项目，请留意项目的学习时间' }) })
        this.onBack()
      })
    },
    // 确认报名
    addRegister () {
      Dialog({
        className: 'custom-dialog',
        showCancelButton: true,
        confirmButtonColor: '#0052D9',
        title: this.$langue('Mooc_ProjectDetail_BasicInfo_RegistProj', { default: '报名加入项目' }),
        message: this.$langue('Mooc_ProjectDetail_BasicInfo_SureRegist', { default: '报名后将加入项目进行学习，请留意项目的学习起止时间，是否确定报名？' }),
        confirmButtonText: this.$langue('Mooc_Home_SureRegist', { default: '确认报名' }),
        cancelButtonText: this.$langue('Mooc_Common_Alert_Cancel', { default: '取消' })
      }).then(() => {
        const params = {
          mooc_course_id: this.mooc_course_id,
          join_type: 3
        }
        moocEnroll(params).then(res => {
          Toast.loading({ duration: 0, forbidClick: true, message: this.$langue('Mooc_TaskDetail_RegistSuceessed', { default: '报名加入项目成功！' }) })
          this.onBack()
        })
      })
    },
    onBack() {
      if (this.from === 'mooc') {
        window.wx.miniProgram.reLaunch({
          url: `/pages/mooc/projectDetails/index?mooc_course_id=${this.mooc_course_id}`
        })
      } else if (this.from === 'spoc') {
        window.wx.miniProgram.reLaunch({
          url: `/pages/webview/spoc/coursePage?classId=${this.class_id}&semesterId=${this.mooc_course_id}`
        })
      }
    }
  }
}
</script>
<style lang="less">
  .custom-dialog {
    .van-dialog__header {
      padding-top: 32px;
      color: rgba(0, 0, 0, 0.9);
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
    }
    .van-dialog__message {
      line-height: 24px !important;
      padding: 8px 24px 33px;
      font-size: 16px;
      color: rgba(0, 0, 0, 0.6);
    }
    .van-button--default {
      color: rgba(0, 0, 0, 0.9);
      font-size: 16px;
      font-weight: 600;
    }
  }
</style>
<style lang='less' scoped>
.task-abnormal {
  height: 100%;
  background-color: #fff;
  text-align: center;
  // .operator-btn {
  //   color: #ffffff;
  //   font-size: 12px;
  //   font-weight: 600;
  //   line-height: 20px;
  //   // padding: 0;
  //   border-radius: 4px;
  //   background-color: #0052D9;
  //   margin-bottom: 20px;
  //   text {
  //     line-height: 20px;
  //     font-weight: 400;
  //     color: #ffffffe6;
  //   }
  // }

  .application-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
  }

  .operator-btn {
    padding: 16px 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: #ffffff;
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 500;
    border-radius: 4px;
    background-color: #0052D9;
  }

  .img {
    margin-top: 120px;
    width: 160px;
    height: 160px;
  }
  .msg {
    margin-bottom: 20px;
    color: #00000099;
    line-height: 22px;
  }
  .back {
    margin-top: 20px;
    color: #0052d9ff;
    font-weight: 600;
    line-height: 22px;
  }
  .admin {
    display: inline-block;
    padding: 16px;
    margin-top: 20px;
    width: 359px;
    box-sizing: border-box;
    border-radius: 4px;
    background: #fbfdffff;
    text-align: left;
    .label {
      color: #000000e6;
      line-height: 20px;
    }
    .admin-list {
      .item {
        display: inline-block;
        margin-top: 16px;
        width: 50%;
        height: 28px;
        line-height: 28px;
        color: #000000e6;
        font-size: 12px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        .avatar {
          margin-right: 8px;
          width: 28px;
          height: 28px;
          border-radius: 14px;
        }
      }
    }
  }
}
</style>
