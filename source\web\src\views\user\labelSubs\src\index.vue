<template>
  <div class="label-select-component">
    <div class="cascader-component">
      <el-dialog title="订阅管理" width="800px" top="8vh" class="dialogHome" :before-close="handleClose" :visible.sync="dialogFormVisible" :close-on-click-modal="false">
        <div class="subsTab">
          <el-radio-group v-model="radio" size="small" :text-color="clode" :fill="colors" @input="changeTab">
            <el-radio-button label="我订阅的标签" style="color: #0052D9;"></el-radio-button>
            <el-radio-button label="我订阅的专区" style="color: #0052D9;margin-left: 8px;"></el-radio-button>
            <el-radio-button label="订阅提醒管理" style="margin-left: 8px;"></el-radio-button>
          </el-radio-group>
          <el-button type="text" class="see-content-button" @click.stop="checkLabelInfo()">{{radio == '我订阅的标签'? '查看标签订阅内容':radio== '订阅提醒管理' ? '查看订阅内容':'查看专区订阅内容'}}</el-button>
        </div>
        <el-form :rules="rules" :inline="true" :model="form" v-show="radio === '订阅提醒管理'" class="subscribe-to-reminder-form">
          <el-form-item>
            <h3 class="switch-h3">订阅提醒开关</h3>
            <el-switch v-model="delivery"></el-switch><span class="switch-tips-text">开启订阅提醒后，当订阅的标签或专区下有新增内容时，将会给您发送消息提醒</span>
          </el-form-item>
          <el-form-item v-if="delivery" class="margin-t-22">
            <span class="label-item-text">订阅提醒的内容类型</span>
            <el-checkbox-group v-model="from.remind_content_types" class="checkbox-group">
              <el-checkbox v-for="item in contentTypesList" :key="item.label" :label="item.label" name="type">{{item.name}}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item v-if="delivery" class="margin-t-22">
            <span class="label-item-text">消息提醒时间</span>
            <el-form-item label="每周" class="week">
              <el-select size="small" v-model="from.remind_week">
                <el-option v-for="item in remindWeekList" :key="item.value" :value="item.value" :label="item.label"></el-option>
              </el-select>
              <!-- <span class="date-text-9">上午9:30</span> -->
              <el-time-select
                class="timeSelect"
                :editable="false"
                :clearable="false"
                placeholder="提醒时间"
                v-model="from.remind_day_time"
                :picker-options="{
                  start: '00:00',
                  step: '00:15',
                  end: '23:45'
                }">
              </el-time-select>
            </el-form-item>
            <span class="date-tips">
              在设定的时间点通过邮件/企微机器人给您推送最近一周订阅标签及专区的上新内容合集
            </span>
          </el-form-item>
          <el-form-item v-if="delivery" class="margin-t-22">
            <span class="label-item-text">消息提醒渠道</span>
            <el-checkbox-group v-model="from.remind_channels" class="checkbox-group">
              <el-checkbox label="mail" name="type">邮件</el-checkbox>
              <el-checkbox label="bot" name="type">企微机器人（小腾老师）</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
        <el-form :rules="rules" :inline="true" :model="form" v-show="radio === '我订阅的标签'" id="labelSubscribeTab" class="subscribe-form">
          <el-form-item class="my-subscribe-item1">
            <el-form-item class="my-subscribe-item2">
              <div style="width: 100%;line-height:24px;height:24px;">
                <span>
                  <span class="color-0">已订阅 <span class="number">{{ tags.length }}</span> 个内容标签</span>
                  <span class="paratext">完成标签订阅后，当标签下有新内容上架时，将会给您发送消息提醒</span>
                  <span class="addlLabel" @click="showInput">
                    <img src="../assets/img/setting.png" alt="">
                    订阅提醒管理</span>
                </span>
              </div>
              <div class="subscribed-tag-content" v-if="tags.length">
                <label-tag :tags="tags" @addToTaga="addToTaga" @handleTagClose="handleTagClose" :close="true" :clickAdd="false"></label-tag>
              </div>
            </el-form-item>
          </el-form-item>
          <el-form-item style="padding:0 12px;border-bottom:#E7E7E7 solid 1px">
            <!-- <el-form-item label="推荐标签 ： " class="tagsd" v-if="recommendList.length > 0">
              <label-tag :tags="recommendList" @addToTaga="addToTaga"></label-tag>
              <span class="switchover-btn" v-if="recommendList.length > 5" @click="getRecommendLabels()">换一换</span>
            </el-form-item>
            <el-form-item label="推荐标签 ： " class="tagsd" v-else>
              <span class="no-label">
                &nbsp;暂无推荐标签
              </span>
            </el-form-item> -->
            <el-form-item label="热门标签 ：" class="tagsd" v-if="recently.length > 0">
              <label-tag :tags="recently" @addToTaga="addToTaga"></label-tag>
            </el-form-item>
          </el-form-item>
          <el-form-item class="labelListTitle">
            <span class="label-tags-list" style="">标签列表</span>
            <span class="label-list-hint">点击标签即可完成订阅</span>
            <LabelSelectComponent v-model="tags" class="project-tag-box" :recommend="{
              title: this.form.course_name,
              desc: this.form.course_desc
              }" @getSelectedLabelList="getSelectedLabelList" :loadingId="loadingId" :labelNodeEnv="labelNodeEnvs" @custom-name="inputVisible = true" @update-labels="updateList" @keywrodIpt="keywrodIpt">
            </LabelSelectComponent>
          </el-form-item>
          <div>
            <template>
              <el-tabs v-model="activeTab" class="tags-label" @tab-click="handleClickButton" :class="{ 'show-star': bixuan }">
                <el-tab-pane :label="'全部'" :name="'全部'" :data-item="wholes">
                  <!-- 内容区域 -->
                </el-tab-pane>
                <el-tab-pane v-for="tab in labelOptions" :data-item="JSON.stringify(tab)" :key="tab.category_id" :label="tab.category_name" :name="tab.category_name">
                  <el-tabs v-model="activeTabs" @tab-click="handleClick" class="tabsto" v-if="tab.category_name !== '其他'">
                    <el-tab-pane :label="'全部'" :name="'全部'" :data-item="whole" :data-id="tab.category_id" class="tabquanbu">
                      <!-- 内容区域 -->
                    </el-tab-pane>
                    <el-tab-pane style="margin: 0;" v-for="tabs in tab.sub_categories" :data-item="JSON.stringify(tabs)" :key="tabs.category_id" :label="tabs.category_name" :name="tabs.category_name">
                      <!-- 内容区域 -->
                    </el-tab-pane>
                  </el-tabs>
                  <!-- 内容区域 -->
                </el-tab-pane>
              </el-tabs>
            </template>
          </div>
          <div id="label-list-content-s" class="label-list-content" v-if="visibleTags.length > 0" ref="tagContainer">
            <el-form-item id="content-form-item-infinte" class="content-form-item1" v-infinite-scroll="handleScroll" :infinite-scroll-disabled="disabled">
              <label-tag :deitailInfo="true" :tags="visibleTags" @addToTaga="addToTaga"></label-tag>
              <p class="infinite-bottom" v-if="infiniteLoading">加载中...</p>
              <p class="infinite-bottom" v-if="noMore && isShowNoMoer">没有更多了</p>
            </el-form-item>
          </div>
          <el-form-item class="no-data" v-else>
            <div class="no-data-center">
              <img src='../assets/img/Label.png' alt="" style="height: 200px;width: 200px;">
            </div>
            <div class="no-data-text">
              <span>
                暂无可订阅的标签
              </span>
            </div>
          </el-form-item>
        </el-form>
        <el-form :rules="rules" :inline="true" :model="form" v-show="radio === '我订阅的专区'" id="specialSubscribeTab" class="subscribe-form">
          <el-form-item class="my-subscribe-item1">
            <el-form-item class="my-subscribe-item2">
              <div style="width: 100%;">
                <span>
                   <span class="color-0">已订阅 <span class="number">{{ specialSubsData.length }}</span> 个专区</span>
                  <span class="paratext">完成专区订阅后，当专区下有新增内容时，将会给您发送消息提醒</span>
                  <span class="addlLabel" @click.stop="showInput">
                    <img src="../assets/img/setting.png" alt="">
                    订阅提醒管理</span>
                </span>
              </div>
              <div class="subscribed-tag-content subsSpecBox" v-if="specialSubsData.length">
                <specialCard :deitailInfo="true" :specialList="specialSubsData" @cancelSubs="subscribeLabel" :close="true" @addToTaga="addToTaga"></specialCard>
              </div>
            </el-form-item>
          </el-form-item>
          <el-form-item class="specialPanelTop">
            <div>
              <span class="label-tags-list" style="">专区列表</span>
              <span class="label-list-hint">仅显示有访问权限的专区</span>
            </div>
            <div class="specialSelect-input">
              <input type="text" v-model.trim="specialWord" class="input-select" spellcheck="false" maxlength="20" placeholder="请输入专区名称" @click.stop @keydown.enter.prevent>
              <i class="el-icon-error icon" v-if="specialWord" @click.stop="specialWord=''" style="margin:0 5px;color: #00000066;"></i>
              <img src="../assets/img/search.png" style="width:16px;height:16px;margin-right: 8px;">
            </div>
          </el-form-item>
          <div id="label-list-content-s2" class="label-list-content" v-show="specialList.length > 0" ref="tagContainer2">
            <el-form-item id="content-form-item-infinte2" class="content-form-item1" v-infinite-scroll="specialHandleScroll" :infinite-scroll-disabled="specialDisabled">
              <!-- <specialCard :deitailInfo="true" :specialList="specialList" @addToTaga="addToTaga"></specialCard> -->
              <ul class="specialList flex">
                <li v-for="(item, index) in specialList" :key="index">
                  <p v-html="item.name" :title="stripHtmlTagsButKeepCertainTags2(item.name)" @click.stop="specailJump(item.custom_link_url, item.page_id)"></p>
                  <div v-html="stripHtmlTagsButKeepCertainTags(item.description) || '暂无专区简介'" :title="stripHtmlTagsButKeepCertainTags2(item.description)"></div>
                  <section class="btns">
                    <el-button type="primary" class="subs" size="small" v-if="!item.isSubs" @click.stop="subscribeLabel(item, 1, 2)">立即订阅</el-button>
                    <el-button type="primary" class="cancelSubs" size="small" v-if="item.isSubs" @click.stop="subscribeLabel(item, 2, 2)">取消订阅</el-button>
                    <el-button type="primary" class="content" plain size="small" @click.stop="specailJump(item.custom_link_url, item.page_id)">查看专区内容</el-button>
                  </section>
                </li>
              </ul>
              <p class="infinite-bottom" v-if="specialInfiniteLoading">加载中...</p>
              <p class="infinite-bottom" v-if="specialNoMore">没有更多了</p>
            </el-form-item>
          </div>
          <el-form-item class="no-data2" v-show="specialList.length < 1">
            <div class="no-data-center">
              <img src='../assets/img/Label.png' alt="">
            </div>
            <div class="no-data-text">
              <span>
                暂无可订阅的专区
              </span>
            </div>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer dialogbotton" v-show="radio === '订阅提醒管理'">
          <el-button class="Submit-button" @click.stop="Canceldialog">取消</el-button>
          <el-tooltip style="z-index: 999999;" class="item" effect="dark" :content="addsss" placement="top" v-if="Addlabels">
            <el-button class="Submit-button Submit-button-save" :disabled="Addlabels" type="primary" @click.stop="Submitdialog">保存</el-button>
          </el-tooltip>
          <el-button class="Submit-button Submit-button-save" v-if="!Addlabels" :disabled="Addlabels" type="primary" @click.stop="Submitdialog">保存</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script src="https://unpkg.com/popper.js@2"></script>
<script>
import specailJump from '../specailJump'
import axios from 'axios'
// const httpHost = process.env.VUE_APP_PORTAL_HOST_WOA
import LabelSelectComponent from './LabelSelectComponent.vue'
import labelTag from './labelTag.vue'
import specialCard from './specialCard.vue'
const debounce = (fn, wait = 200) => {
  let timer = null
  return function () {
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(() => {
      fn.apply(this)
    }, wait)
  }
}
export default {
  name: 'sdc-addlabel',
  components: {
    LabelSelectComponent,
    labelTag,
    specialCard
  },
  props: {
    // radioType 展示标签 1 , 还是订阅提醒管理 2
    radioType: {
      type: String | Number,
      default: 1
    },
    // 双向绑定值
    value: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请输入标签名称'
    },
    // 禁止回车创建
    disableCreate: {
      type: Boolean,
      default: false
    },
    // 是否有推荐（课程名称-title、描述-desc）
    recommend: {
      type: Object,
      default() {
        return null
      }
    },
    // 最多选择数量
    maxNum: {
      type: Number,
      default: 5
    },
    // 可清空
    clearable: {
      type: Boolean,
      default: true
    },
    showLabelList: {
      type: Array,
      default: () => []
    },
    labelse: {
      type: Boolean,
      default: true
    },
    labelNodeEnv: {
      type: Object,
      default: () => ({})
    }
  },
  mixins: [specailJump],
  data() {
    return {
      isShowNoMoer: false,
      infiniteLoading: false,
      delivery: true,
      clode: '#0052D9',
      colors: '#F2F3FF',
      radio: '我订阅的标签',
      contentTypesList: [
        {
          label: '1',
          name: '网络课'
        },
        {
          label: '10',
          name: '培养项目'
        },
        {
          label: '8',
          name: '文章'
        },
        {
          label: '7',
          name: '案例'
        },
        {
          label: '15',
          name: '课单'
        },
        {
          label: '6',
          name: '行家'
        },
        {
          label: '3',
          name: '直播'
        },
        {
          label: '2',
          name: '面授课'
        },
        {
          label: '4',
          name: '活动'
        },
        {
          label: '16',
          name: '文档'
        },
        {
          label: '20',
          name: 'K吧文章（仅与标签关联，包含腾讯学堂运营的K吧内容）'
        },
        {
          label: '99',
          name: '外链课程（仅与专区关联）'
        }
      ],
      remindWeekList: [
        {
          value: 1,
          label: '周一'
        },
        {
          value: 2,
          label: '周二'
        },
        {
          value: 3,
          label: '周三'
        },
        {
          value: 4,
          label: '周四'
        },
        {
          value: 5,
          label: '周五'
        },
        {
          value: 6,
          label: '周六'
        },
        {
          value: 7,
          label: '周日'
        }
      ],
      bixuan: true,
      // tooltipPopperOptions: {},
      inputVal: '',
      addsss: '',
      nickFlag: true,
      Determines: false,
      nackname: false,
      nackList: [],
      errorMsg: '',
      searchList: [],
      recommendList: [],
      recently: [],
      tag: [],
      selectedOptions: [
        {
          category_id: '',
          label_name: ''
        }
      ], // 选中的选项
      labelOptions: [],
      cascaderProps: {
        value: 'category_id',
        label: 'category_name',
        children: 'sub_categories'
      },
      dialogFormVisible: false,
      selectedLabels: [], // 已选择标签
      isShowDropdown: false, // 显示下拉弹窗
      page: {
        current: 1,
        size: 50,
        total: 0,
        isRequesting: false
      },
      form: {
        name: '',
        region: '',
        date1: '',
        date2: '',
        delivery: false,
        type: [],
        resource: '1',
        desc: '',
        course_name: '',
        is_original: '',
        link_url: '', // 转载链接
        course_labels: [], // 标签
        photo_url: '',
        photo_id: '',
        photo_storage_type: '',
        course_admins: [],
        innerChecked: true,
        inner_teacher_names: [],
        outChecked: false,
        course_desc: '',
        out_teacher_names: '',
        show_caption: 0,
        limit: 0, // 权限
        target_list: '', // 权限人员
        status: '',
        dept_id: '', // 所属组织单元id
        dept_name: '', // 所属组织单元名
        extend_list: [], // 延伸学习
        caption_id: '', // 字幕文件id
        caption_name: '',
        caption_size: '',
        cl_ids: []
      },
      specialInfiniteLoading: false,
      specialSubsData: [],
      specialList: [],
      specialTotal: 0,
      specialPageFrom: 1,
      specialForm: {},
      visibleTags: [],
      tags: [],
      Dialogtag: [],
      activeTab: '全部', // 设置默认激活的标签
      inputTag: {
        label_name: '',
        type: ''
      },
      dynamicTags: [],
      inputVisible: false,
      inputValue: '',
      activeTabs: '全部', // 设置默认激活的标签
      whole: '481',
      wholes: '480',
      loadingId: '',
      dataitem: '',
      dataid: '',
      appStatus: true,
      Addlabels: true,
      tapds: [],
      Tabarr: '',
      paramd: {},
      content: '1',
      labelNodeEnvs: {},
      // VUE_APP_PORTAL_HOST_WOA = '//test-portal-learn.woa.com'
      // VUE_APP_PORTAL_HOST_WOA = '//portal.learn.woa.com'
      urlInfo: {
        production: '//learn.woa.com',
        test: '//test-portal-learn.woa.com'
      },
      rules: {
        // resource: [
        //   { required: true, message: ' ', trigger: 'change' }
        // ]
      },
      ruld: {
        label_name: [
          { required: true, message: '请输入名称', trigger: 'change' }
        ],
        category_id: [
          { required: true, message: '请选择分类', trigger: 'change' }
        ]
      },
      from: {
        remind_week: 1,
        remind_channels: ['mail', 'bot'],
        remind_content_types: ['1', '10', '8', '7', '15', '6', '3', '2', '4', '16', '20', '99'], // 初始时全部勾选
        remind_day_time: '09:00'
      },
      specialWord: '',
      search_name: ''
    }
  },
  computed: {
    httpHost() {
      let httpHosturl = ''
      if (process.env.NODE_ENV === 'production') {
        httpHosturl = window.location.hostname.endsWith('.woa.com')
          ? '//portal.learn.woa.com'
          : '//portal.learn.oa.com'
      } else {
        httpHosturl = window.location.hostname.endsWith('.woa.com')
          ? '//test-portal-learn.woa.com'
          : '//test.portal.learn.oa.com'
      }
      return httpHosturl
    },
    noMore() {
      return this.visibleTags.length >= this.page.total
    },
    disabled() {
      return this.infiniteLoading || this.noMore
    },
    specialNoMore() {
      return this.specialList.length >= this.specialTotal
    },
    specialDisabled() {
      return true
      // return this.specialInfiniteLoading || this.specialNoMore
    }
  },
  watch: {
    radioType: {
      immediate: true,
      handler(newValue) {
        console.log('newValue', newValue)
        Number(newValue) === 1
          ? (this.radio = '我订阅的标签')
          : Number(newValue) === 3 ? (this.radio = '我订阅的专区') : (this.radio = '订阅提醒管理')
      }
    },
    from: {
      handler(newVal) {
        console.log(newVal)
      },
      immediate: true
    },
    'from.remind_channels': {
      handler(newVal) {
        console.log(newVal)
        this.Proofreading()
      },
      immediate: true
    },
    'from.remind_content_types': {
      handler(newVal) {
        console.log(newVal)
        this.Proofreading()
      },
      immediate: true
    },
    labelse: {
      handler(newVal) {
        this.getSubscribeLabels()
        this.dialogFormVisible = newVal
        if (newVal === true) {
          this.getRecommendLabels()
          this.getSubscribeRemind()
        }
      },
      immediate: true
    },
    selectedOptions: {
      deep: true,
      handler() {
        this.checkButtonStatus() // 检查按钮状态
      }
    },
    tags: {
      deep: true, // 监听数组内部元素的变化
      handler(newArray) {
        console.log(this.activeTab, 'shenmshenm ')
        this.Proofreading()
        // 遍历整个数组，找到并清空空数组
        for (let i = 0; i < newArray.length; i++) {
          if (Array.isArray(newArray[i]) && newArray[i].length === 0) {
            newArray.splice(i, 1)
            i-- // 从当前位置重新检查，因为数组已经变化
          }
        }
      }
    },
    value: {
      handler(newVal) {
        this.tapds = newVal
        this.updateClickedStates(newVal)
      },
      immediate: true
    },
    'recommend.title'() {
      this.getRecommendLabels()
    },
    'recommend.desc'() {
      this.getRecommendLabels()
    },
    specialWord(val) {
      this.specialPageFrom=1
      this.specialList=[]
      this.getSpecialData(val)
    }
    // selectedLabels (newVal) {
    //   this.$emit('input', newVal)
    // }
  },
  created() {
    this.Infolabel()
  },
  async mounted() {
    this.Proofreading()
    if (this.radio === '我订阅的标签') {
      this.getSubscribeLabels()
      this.getLabelRecently()
      this.getLabelCategory()
    }
    if (this.radio === '我订阅的专区') {
      await this.getSpecialSubs()
      this.getSpecialData()
    }
    // this.getLabelLeaf()
    this.tapdsd()
    document.addEventListener('click', this.hideDropdown)
  },
  beforeDestroy() {
    document.removeEventListener('click', this.hideDropdown)
  },
  methods: {
    async changeTab(val) {
      if (this.radio === '我订阅的标签') {
        this.page.current = 1
        this.page.total = 0
        this.visibleTags = []
        this.getSubscribeLabels()
        this.getLabelRecently()
        this.getLabelCategory()
      }
      if (this.radio === '我订阅的专区') {
        await this.getSpecialSubs()
        this.getSpecialData()
      }
      if (this.radio === '订阅提醒管理') {
        this.getSubscribeRemind()
      }
    },
    // 获取订阅提醒管理保存的数据
    getSubscribeRemind() {
      axios
        .get(
          `${this.httpHost}/training/api/label/subscribe/getSubscribeRemind`,
          {
            withCredentials: true
          }
        )
        .then((res) => {
          const { data } = res.data
          this.delivery = data.status === 1
          this.from.remind_content_types = data.remind_content_types
            ? data.remind_content_types.split(',')
            : []
          this.from.remind_week = data.remind_week
          this.from.remind_channels = data.remind_channels
            ? data.remind_channels.split(',')
            : []
          this.from.remind_day_time = data.remind_day_time.substr(0, 5)
        })
    },
    // 查看订阅内容
    checkLabelInfo() {
      let type = this.radio === '我订阅的标签' ? 1 : this.radio === '订阅提醒管理' ? 0 : 2
      window.open(window.location.origin + window.location.pathname + '?subsType=' + type)
    },
    subscribeLabel(tag, opt_type, subsType = 1, index = null) {
      if (index !== null && subsType === 2 && opt_type === 2) {
        this.specialSubsData.splice(index, 1)
      }
      axios
        .post(
          `${this.httpHost}/training/api/label/subscribe/subscribeLabel`,
          {
            label_id: tag.label_id || tag.page_id,
            opt_type: opt_type,
            subscribe_type: subsType
          },
          {
            withCredentials: true
          }
        )
        .then(async (res) => {
          if (res.data.code === 200 && res.data.data) {
            if (subsType === 1) {
              if (opt_type === 2) {
                this.$message({
                  customClass: 'label--el-message',
                  message: `已取消订阅标签「${tag.label_name}」`,
                  iconClass: 'el-icon-warning'
                })
              } else {
                this.$message.success({
                  customClass: 'label--el-message',
                  message: `成功订阅标签「${tag.label_name}」`,
                  type: 'success'
                })
              }
            } else {
              if (opt_type === 2) {
                this.$message({
                  customClass: 'label--el-message label--el-message2',
                  message: `已取消订阅专区「${this.stripHtmlTagsButKeepCertainTags2(tag.label_name || tag.name)}」`,
                  type: 'success'
                })
                tag.isSubs = false
                for(let i = 0; i< this.specialList.length; i++) {
                  let item = this.specialList[i]
                  if (item.page_id === tag.label_id) {
                    item.isSubs = false
                    break
                  }
                }
              } else {
                this.$message.success({
                  customClass: 'label--el-message label--el-message2',
                  message: `成功订阅专区「${this.stripHtmlTagsButKeepCertainTags2(tag.label_name || tag.name)}」`,
                  type: 'success'
                })
                tag.isSubs = true
              }
              this.getSpecialSubs()    
            }
          } else {
            let message = res.data.message
            if (res.data.code === -50005) {
              message = '专区已失效'
              await this.getSpecialData()
              this.getSpecialSubs()
            }
            if (res.data.code === -50006) {
              message = '标签已失效'
              this.page.current = 1
              this.page.total = 0
              this.visibleTags = []
              this.getLabelLeaf(this.loadingId)
              this.getSubscribeLabels()
            }
            this.$message({
              customClass: 'label--el-message label--el-message2',
              message: message,
              type: 'error'
            })
          }
        })
    },
    getSubscribeLabels() {
      axios
        .get(
          `${this.httpHost}/training/api/label/subscribe/getSubscribeLabels`,
          {
            params: {
              subscribeType: 1
            },
            withCredentials: true
          }
        )
        .then((res) => {
          console.log(res)
          if (res.data.code === 200) {
            this.tags = res.data.data.map((item) => {
              return {
                ...item,
                clicked: true
              }
            })
          }
        })
    },
    async Infolabel() {
      this.labelNodeEnvs = this.labelNodeEnv
    },
    tapdsd() {
      setTimeout(() => {
        console.log(this.tapds)
        // this.selectedLabels = this.tapds
      }, 500)
    },
    Proofreading() {
      setTimeout(() => {
        const isRemindChannelsSelected = this.from.remind_channels.length > 0
        const isRemindContentTypesSelected =
          this.from.remind_content_types.length > 0
        if (!isRemindChannelsSelected || !isRemindContentTypesSelected) {
          this.Addlabels = true
          if (!isRemindChannelsSelected) {
            this.addsss = '请至少选择一个提醒渠道'
          }
          if (!isRemindContentTypesSelected) {
            this.addsss = '请至少选择一个内容类型'
          }
        } else {
          this.addsss = '' // 重置提示信息
          this.Addlabels = false
        }
      }, 500)
    },
    checkButtonStatus() {
      const id = this.$set(this.selectedOptions[0], 'category_id', '')
      const name = this.$set(this.selectedOptions[0], 'label_name', '')
      // 在这里设置逻辑来检查按钮状态
      if (id !== '' && name !== '') {
        this.appStatus = true // 当选定的类别和输入框都有值时，启用按钮
      } else {
        this.appStatus = false // 否则禁用按钮
      }
    },
    showNoMoer() {
      this.$nextTick(() => {
        const container = document.getElementById('label-list-content-s')
        const content = document.getElementById('content-form-item-infinte')
        if (container && content) {
          if (content.offsetHeight > container.offsetHeight) {
            this.isShowNoMoer = true
          } else {
            this.isShowNoMoer = false
          }
        }
      })
    },
    loadMoreTags(id) {
      console.log('2222222222222222222')
      this.page.isRequesting = true
      let params = {
        page_no: this.page.current,
        page_size: this.page.size,
        category_id: id,
        order_by: 'subscribeCount',
        label_type: '1',
        search_name: this.search_name
      }
      axios
        .get(
          `${this.httpHost}/training/api/label/user/labelinfo/get_label_basicinfo`,
          {
            params,
            withCredentials: true
          }
        )
        .then((res) => {
          if (res.data.code === 200) {
            this.visibleTags.push(...res.data.data.records)
            this.page.total = res.data.data.total
            this.updateClickedStates(this.visibleTags)
            this.infiniteLoading = false
            this.showNoMoer()
          }
        })
        .finally(() => {
          this.page.isRequesting = false
        })
    },
    handleScroll() {
      this.infiniteLoading = true
      if (this.page.total > this.visibleTags.length) {
        this.page.current++
        console.log('aaaaaa')
        this.loadMoreTags(this.loadingId)
      } else {
        this.infiniteLoading = false
      }
    },
    specialHandleScroll() {
      // this.specialInfiniteLoading = true
      // if (this.specialList.length < this.specialTotal) {
      //   this.specialPageFrom++
      //   this.getSpecialData(this.specialWord)
      // } else {
      //   this.specialInfiniteLoading = false
      // }
    },
    // 取消创建清除数据
    CancelCreation(formName) {
      // this.inputVisible = false
      this.$refs[formName].resetFields()
      this.selectedOptions = []
      this.nickFlag = false
      this.appStatus = false
      this.nackname = false
      this.Determines = false
    },
    SubmitCreation(formName) {
      if (!this.nackname) {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            let addSubmit = [
              {
                label_name: this.selectedOptions.label_name,
                category_id: this.selectedOptions.category_id[1],
                clicked: true
              }
            ]
            axios
              .post(
                `${this.httpHost}/training/api/label/user/labelinfo/user-insert-label`,
                {
                  label_name: this.selectedOptions.label_name,
                  category_id: this.selectedOptions.category_id[1],
                  label_type: '1'
                },
                {
                  withCredentials: true
                }
              )
              .then((res) => {
                if (res.data.code === 200) {
                  addSubmit[0].label_id = res.data.data
                  addSubmit[0].category_id = this.selectedOptions.category_id[1]
                  // this.inputVisible = false
                  this.tags.push(...addSubmit)
                  this.$refs[formName].resetFields()
                  this.nickFlag = false
                  this.appStatus = false
                  this.nackname = false
                  this.Determines = false
                } else {
                  this.$message({
                    type: 'warning',
                    dangerouslyUseHTMLString: true,
                    message: res.data.message
                  })
                }
              })
          } else {
            console.log('error submit!!')
            return false
          }
        })
      } else if (this.Determines) {
        this.selectedOptions = []
        // this.inputVisible = false
        this.$refs[formName].resetFields()
        this.Determines = false
        this.nackname = false
        this.nickFlag = false
        this.appStatus = false
      } else {
        this.selectedOptions = []
        // this.inputVisible = false
        this.tags.push(...this.nackList)
        this.$refs[formName].resetFields()
        this.Determines = false
        this.nackname = false
        this.nickFlag = false
        this.appStatus = false
      }
    },
    handleClose() {
      this.dialogFormVisible = false
      this.selectedLabels = []
      this.tags = []
      this.tags = this.Dialogtag
      this.selectedLabels.push(...this.Dialogtag)
      this.$emit('getSelectedLabelList', this.selectedLabels)
      this.$emit('input', this.dialogFormVisible)
    },
    Canceldialog() {
      this.dialogFormVisible = false
      console.log(this.from, '取消测试')
      this.$emit('input', this.dialogFormVisible)
    },
    Submitdialog() {
      axios
        .post(
          `${this.httpHost}/training/api/label/subscribe/addSubscribeRemind`,
          {
            remind_week: this.from.remind_week,
            remind_channels: this.from.remind_channels.join(','),
            remind_content_types: this.from.remind_content_types.join(','),
            remind_day_time: this.from.remind_day_time,
            status: this.delivery ? 1 : 0
          },
          {
            withCredentials: true
          }
        )
        .then((res) => {
          if (res.data.code === 200) {
            this.$message.success({
              customClass: 'label--el-message',
              message: '保存成功',
              type: 'success'
            })
          }
        })
      // this.dialogFormVisible = false
      // this.$emit('input', this.dialogFormVisible)
    },
    addToTaga(tag) {
      if (!tag.clicked) {
        this.subscribeLabel(tag, 1, 1)
        // this.tags = this.tags.unshift(tag);
        this.tags.unshift(tag) // 将标签数据添加到数组
        tag.clicked = true
        console.log(this.tags)
        this.$forceUpdate()
      } else {
        this.subscribeLabel(tag, 2, 1)
        this.tags = this.tags.filter(
          (item) => item.label_name !== tag.label_name
        )
        tag.clicked = false
        console.log(this.tags, '这个没有吗?')
        this.$forceUpdate()
      }
    },
    handleCascaderChange(value) {
      // 处理选中事件
      console.log(value)
    },
    processCategories(categories) {
      return categories.map((category) => ({
        ...category,
        sub_categories:
          category.sub_categories.length > 0
            ? this.processCategories(category.sub_categories)
            : undefined
      }))
    },
    showInput() {
      this.radio = '订阅提醒管理'
    },
    updateClickedStates(val) {
      for (const selectedLabel of val) {
        // 在 tagsArray 中查找具有相同 label_id 的标签
        const matchingTag = this.tags.find(
          (tag) => tag.label_id === selectedLabel.label_id
        )

        // 如果找到匹配的标签，设置 tag.clicked 为 true，否则设置为 false
        if (matchingTag) {
          //   matchingTag.clicked = true
          this.$set(selectedLabel, 'clicked', true) // 使用Vue.set来确保响应式更新
        } else {
          //   selectedLabel.clicked = false
          this.$set(selectedLabel, 'clicked', false) // 使用Vue.set来确保响应式更新
        }
      }
    },
    getSelectedLabelList(val) {
      this.tags = val
      this.updateClickedStates(this.recently)
      this.updateClickedStates(this.recommendList)
      this.updateClickedStates(this.visibleTags)
      this.form.course_labels = this.tags
    },
    handleTagClose(tag) {
      this.subscribeLabel(tag, 2, 1)
      tag.clicked = false
      // 执行删除操作
      const index = this.tags.indexOf(tag)
      if (index !== -1) {
        this.tags.splice(index, 1)
      }
    },
    getLabelCategory(id) {
      axios
        .get(
          `${this.httpHost}/training/api/label/user/category/category_tree`,
          {
            withCredentials: true,
            params: {
              order_type: id
            }
          }
        )
        .then((res) => {
          if (res.data.code === 200) {
            let list = []
            if (this.showLabelList.length > 0) {
              list = res.data.data.filter((item) => {
                return (
                  item.sub_categories.length &&
                  this.showLabelList.includes(item.category_name)
                )
              })
            } else {
              list = res.data.data.filter((item) => {
                return item.sub_categories.length
              })
            }
            //   this.labelOptions = list
            this.labelOptions = this.processCategories(list)
            if (this.labelOptions.length > 0) {
              if (id === '5') {
                this.activeTab = '全部'
                this.getLabelLeaf()
              } else {
                this.activeTabs = ''
                this.$nextTick(() => {
                  this.activeTabs = '全部'
                })
                this.activeTab = this.labelOptions[0].category_name
                // 设置默认选中第一个标签
                this.getLabelLeaf(this.labelOptions[0].category_id)
              }
            }
          }
        })
    },
    getLabelRecently() {
      // axios.get(`${httpHost}/training/api/label/user/labelinfo/recently_used`, {
      axios
        .get(`${this.httpHost}/training/api/label/subscribe/getHotLabels`, {
          withCredentials: true,
          // params: {
          //   show_count: '5'
          // }
          params: {}
        })
        .then((res) => {
          if (res.data.code === 200) {
            this.recently = res.data.data
            this.updateClickedStates(this.recently)
          }
        })
    },
    updateRequest(val) {
      console.log('333333333333333333333')
      if (this.Tabarr === '全部') {
        this.paramd = {
          page_no: 1,
          page_size: 6,
          label_name: val.label_name,
          search_name: this.search_name
        }
      } else {
        this.paramd = {
          page_no: 1,
          page_size: 6,
          order_by: 'subscribeCount',
          category_id:
            this.dataitem.category_id !== '' &&
            this.dataitem.category_id !== undefined
              ? this.dataitem.category_id
              : this.labelOptions[0].category_id,
          label_name: val.label_name,
          search_name: this.search_name
        }
      }
      axios
        .get(
          `${this.httpHost}/training/api/label/user/labelinfo/get_label_basicinfo`,
          {
            params: this.paramd,
            withCredentials: true
          }
        )
        .then((res) => {
          if (res.data.code === 200) {
            if (res.data.data.total !== 0) {
              this.visibleTags = [
                val,
                ...this.visibleTags.filter(
                  (label) =>
                    !(
                      label.category_id === val.category_id &&
                      label.label_name === val.label_name
                    )
                )
              ]
            }
          }
        })
    },
    updateList(val, newVal) {
      this.subscribeLabel(val, newVal, 1)
      if (this.dataitem !== 480) {
        if (this.dataitem !== 481) {
          let i = -1
          const hasDuplicate = this.visibleTags.some(
            (label, index) => {
              if (label.category_id === val.category_id && label.label_name === val.label_name) {
                i = index
              }
              return label.category_id === val.category_id &&
              label.label_name === val.label_name
            }
          )
          if (hasDuplicate) {
            let item = this.visibleTags.splice(i, 1)
            this.visibleTags.unshift(item[0])
            // debugger
            // this.visibleTags = [
            //   val,
            //   ...this.visibleTags.filter(
            //     (label) =>
            //       !(
            //         label.category_id === val.category_id &&
            //         label.label_name === val.label_name
            //       )
            //   )
            // ]
            // debugger
          } else {
            this.updateRequest(val)
          }
        }
      } else {
        this.Tabarr = '全部'
        this.updateRequest(val)
      }
    },
    handleClickButton(tab) {
      this.Proofreading()
      this.page.current = 1
      this.loadingId = JSON.parse(tab.$attrs['data-item']).category_id
      this.dataitem = JSON.parse(tab.$attrs['data-item'])
      console.log(JSON.parse(tab.$attrs['data-item']))
      if (JSON.parse(tab.$attrs['data-item']) !== 480) {
        let cateId = JSON.parse(tab.$attrs['data-item']).category_id
        console.log(JSON.parse(tab.$attrs['data-item']).category_id, '当前参数')
        this.getLabelLeaf(cateId)
        this.activeTabs = ''
        this.$nextTick(() => {
          this.activeTabs = '全部'
        })
        // this.handleClick(tab)
      } else {
        let id = ''
        this.getLabelLeaf(id)
      }
    },
    handleClick(tab) {
      this.Proofreading()
      this.page.current = 1
      this.loadingId = JSON.parse(tab.$attrs['data-item']).category_id
      this.dataitem = JSON.parse(tab.$attrs['data-item'])
      this.dataid = JSON.parse(tab.$attrs['data-item']) !== 481
      console.log(JSON.parse(tab.$attrs['data-item']))
      if (JSON.parse(tab.$attrs['data-item']) !== 481) {
        let cateId = JSON.parse(tab.$attrs['data-item']).category_id
        console.log(JSON.parse(tab.$attrs['data-item']).category_id, '当前参数')
        this.getLabelLeaf(cateId)
      } else {
        let id = JSON.parse(tab.$attrs['data-id'])
        this.getLabelLeaf(id)
      }
    },
    getLabelLeaf(id) {
      console.log('11111111111111111111')
      this.page.isRequesting = true
      axios
        .get(
          `${this.httpHost}/training/api/label/user/labelinfo/get_label_basicinfo`,
          {
            params: {
              page_no: 1,
              page_size: this.page.size,
              category_id: id,
              order_by: 'subscribeCount',
              label_type: '1',
              search_name: this.search_name
            },
            withCredentials: true
          }
        )
        .then((res) => {
          if (res.data.code === 200) {
            this.visibleTags = []
            this.visibleTags = res.data.data.records
            this.page.total = res.data.data.total
            this.updateClickedStates(this.visibleTags)
            if (this.page.total > this.visibleTags.length) {
              this.page.current++
              this.loadingId = id
              console.log('bbbbb')
              this.loadMoreTags(this.loadingId)
            }
            this.showNoMoer()
          }
        })
        .finally(() => {
          this.page.isRequesting = false
        })
    },
    // 推荐标签
    getRecommendLabels: debounce(function () {
      // if (!this.showRecommend) {
      //   return
      // }
      // axios.post(`${httpHost}/training/api/businessCommon/manage/label/get_recommend_labels_v2`, {
      axios
        .get(
          `${this.httpHost}/training/api/label/subscribe/findRecommendLabelPage`,
          {
            params: {},
            withCredentials: true
          }
        )
        .then((res) => {
          if (res.data.code === 200) {
            if (res.data.data) {
              this.recommendList = res.data.data.map((item) => {
                return {
                  ...item,
                  label_name: item.name
                }
              })
              this.updateClickedStates(this.recommendList)
            }
          }
        })
    }, 100),
    // 显示隐藏面板
    togglePopper(visible) {
      this.tapds.forEach((item) => {
        item.default = '100'
      })
      this.Dialogtag = this.tapds.filter((tag) => tag.default === '100')
      console.log(this.Dialogtag, 'gzsggszfdzaddfh')
      this.getRecommendLabels()
      this.dialogFormVisible = true
      const isDef = visible !== undefined && visible !== null
      this.isShowDropdown = isDef ? visible : !this.isShowDropdown
      this.tags = this.tapds || this.selectedLabels
      this.Proofreading()
      setTimeout(() => {
        this.getLabelRecently()
      }, 500)
    },
    // 下拉框隐藏事件
    hideDropdown() {
      // console.log('hideDropdown')
      this.isShowDropdown = false
      this.inputVal = ''
      this.searchList = []
      // this.$emit('hideDropdwon', this.selectedLabels)
    },
    // 获取已订阅专区
    async getSpecialSubs() {
      await axios
        .get(
          `${this.httpHost}/training/api/label/subscribe/getSubscribeLabels`,
          {
            params: {
              subscribeType: 2
            },
            withCredentials: true
          }
        )
        .then(async (res) => {
          this.specialSubsData = res.data.data || []
          return res
        })
    },
    // 获取专区详情
    async getSpecialDetail(ids) {
      let obj = {
        filters: {
          'module_id': ['=', 12],
          'item_id': ['in', ...ids]
        },
        newFlag: true,
        pageFrom: 1,
        pageSize: ids.length
      }
      let res = await axios.post(
        `${this.httpHost}/mat/api/user/search`,
        obj,
        {
          withCredentials: true
        }
      ).then((res) => {
        return res
      })
      return res.data?.data?.content || []
    },
    // 专区订阅
    specialSubs() {},
    // 获取专区列表
    getSpecialData: debounce(function() {
      axios
        .get(
          `${this.httpHost}/training/api/label/subscribe/searchSpecialPages`,
          {
            params: {
              keywords: this.specialWord
            },
            withCredentials: true
          }
        )
        .then((res) => {
          // this.specialInfiniteLoading = false
          if (res.data.code === 200) {
            let ids = this.specialSubsData.map(item => item.label_id)
            let data = res.data.data.map((item, index) => {
              if (ids.includes(item.page_id)) {
                item.isSubs = true
              } else {
                item.isSubs = false
              }
              return item
            })
            this.specialTotal = res.data.data.length
            this.specialList = data
          }
        }).catch(() => {
          // this.specialInfiniteLoading = false
        })
    }, 500),
    keywrodIpt(val) {
      this.page.current = 1
      this.page.total = 0
      this.visibleTags = []
      this.search_name = val
      this.getLabelLeaf(this.loadingId)
    }
  }
}
</script>
<style lang="less">
// 消息提示框
.label--el-message2.label--el-message {
  .el-icon-success {
    color: #0ad0b6 !important;
    margin-right: 8px;
  }
}
.label--el-message {
  top: 20px !important;
  min-width: 40px !important;
  padding: 14px;
  line-height: 20px;
  border-radius: 4px;
  opacity: 1;
  background-color: #fff !important;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  border: 0 !important;

  .el-message__icon {
    margin-right: 8px;
    font-size: 20px;
  }

  .el-icon-success {
    color: #2ba471 !important;
    margin-right: 8px;
  }

  .el-icon-warning:before,
  .el-icon-success:before {
    width: 20px;
    height: 20px;
    display: inline-block;
  }
  .el-icon-info {
    color: #0052d9 !important;
  }
  .el-icon-error {
    color: #f81d22 !important;
  }

  .el-icon-warning,
  .el-icon-warning-outline {
    color: #0052d9 !important;
    font-size: 20px;
    margin-right: 8px;
  }

  .el-icon-warning-outline {
    margin-right: 5px;
  }

  .el-message__content {
    color: #000000 !important;
  }
}
.checkbox-group {
  .el-checkbox__input.is-checked + .el-checkbox__label {
      color: rgba(0,0,0,0.9);
  }
}
</style>

<style lang="less" scoped>
/deep/.el-form {
  .el-form-item__label,.el-form-item__content{
   line-height: 40px;
  }
}
/* 订阅提醒弹窗tab */
.subscribe-to-reminder-form {
  height: 545px;
  overflow-y: auto;
  &.el-form--inline {
    /deep/.el-form-item__content{
      line-height: 40px;
    }
  }
  .margin-t-22 {
    margin-top: 22px;
  }
  .switch-h3 {
    color: #000000e6;
    text-align: left;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
    margin-bottom: 10px;
  }
  .switch-tips-text {
    color: #00000080;
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    margin-left: 16px;
  }
  .label-item-text {
    color: #000000e6;
    text-align: left;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }
  .date-text-9 {
    color: #000000e6;
    font-family: PingFang SC;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-left: 12px;
  }
  .date-tips {
    color: #00000080;
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }
  .timeSelect{
    width: 114px;
    margin-left: 12px;
    .el-input__inner {
      padding-left: 10px;
    }
    .el-input__prefix{
      display: none;
    }
  }
}
/** 标签订阅、专区订阅 */
.subscribe-form {
  .my-subscribe-item1 {
    padding: 15px 12px 16px;
    background: #f3f3f3;
    border-radius: 4px;
    width: 100%;
    margin: 0 !important;
  }
  .my-subscribe-item2 {
    background: #f3f3f3;
    border-radius: 5px;
    width: 100%;
    .color-0 {
      color: #000;
      // padding-left: 12px;
    }
    .number {
      font-weight: 600;
      font-family: 'PingFang SC';
      font-size: 16px;
      color: #0052d9;
    }
    .subscribed-tag-content {
      max-height: 117px;
      overflow-y: auto;
      // padding-left: 12px;
      .subscribed-img {
        height: 16px;
        display: inline-block;
        vertical-align: middle;
        margin-bottom: 2px;
        margin-left: 5px;
      }
    }
  }
  .recommend-hot {
    padding: 5px 10px 10px 10px;
    border-bottom: #e7e7e7 solid 1px;
  }
  .recommend-hot-guidance {
    border-radius: 6px;
    border: 2px dashed var(--brand-4, #96bbf8);
  }
  .dashed-guidance {
    position: absolute;
    width: 100px;
    height: 1px;
    border-bottom: 1px dashed #fff;
    right: -110px;
    top: 50%;
    transform: translateY(-50%);
  }
  .dashed-guidance-label-content {
    position: absolute;
    width: 100px;
    height: 1px;
    border-bottom: 1px dashed #fff;
    right: -65px;
    top: 75%;
    transform: translateY(-50%);
  }
  .outer-ing {
    position: absolute;
    top: -10px;
    right: -12px;
    width: 20px;
    height: 20px;
    background-color: #ffffff6e;
    border-radius: 50%;
    .inner-ring {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #fff;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .overlay-content {
    background-color: #fff;
    padding: 16px 20px;
    position: absolute;
    right: -120px;
    top: 20px;
    border-radius: 6px;
    border: 0.5px solid var(--gray-gray-4, #dcdcdc);
    p {
      color: #0052d9;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
    }
  }
  .overlay-content::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 16%;
    margin-top: -46px;
    border-width: 10px;
    border-style: solid;
    border-color: transparent transparent #fff transparent;
  }
  .label-list-content {
    height: 335px;
    overflow-y: auto;
    margin-top: 12px;
    // margin-bottom: 25px;
    // padding-left: 5px;
    .content-form-item1 {
      position: relative;
      z-index: 99999;
      overflow-y: hidden;
    }
    .infinite-bottom {
      margin: 20px 0;
      text-align: center;
      color: #00000080;
      font-size: 12px;
    }
    /deep/ .custom-tag {
      margin-top: 0 !important;
      margin-bottom: 20px !important;
    }
  }
  #label-list-content-s2{
    height: 440px;
    margin: 0;
    padding-right: 12px;
    width: calc(100% + 17px);
  }
  .specialList{
    display: flex;
    flex-wrap: wrap;
    li{
      width: 232px;
      height: 133px;
      padding: 12px;
      margin-right: 20px;
      margin-bottom: 16px;
      border: 0.5px solid #DCDCDC;
      overflow: hidden;
      &:nth-child(3n+0){
        margin-right: 0;
      }
      p{
        line-height: normal;
        font-family: "PingFang SC";
        color: #333333;
        font-size: 14px;
        font-weight: 500;
        cursor: pointer;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        align-self: stretch;
        overflow: hidden;
      }
      div{
        height: 48px;
        margin-top: 8px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        align-self: stretch;
        overflow: hidden;
        color: #777777;
        text-overflow: ellipsis;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
      }
      .btns{
        margin-top: 8px;
        button{
          width: 86px;
          height: 24px;
          padding: 3px 0px;
          border-radius: 4px;
          font-family: "PingFang SC";
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;
        }
        button.subs{
          background: #006FFF;
          border-color: #006FFF;
        }
        button.cancelSubs{
          background: none;
          color: #D54941;
          border-color: #D54941;
        }
        button.content{
          background: none;
          color: #006FFF;
          border-color: #006FFF;
        }
        button + button{
          margin-left: 32px;
        }
      }
    }
  }
}
/deep/ .subsBtn {
  border-radius: 3px;
  background: var(--brand-brand-7-normal, #0052d9);
  font-size: 12px;
}
.week {
  margin-top: 15px;
  :deep .el-input__inner {
    width: 114px;
  }
  :deep .el-form-item__label {
    width: 42px;
  }
}
.checkbox-group {
  margin-top: 10px;
  /deep/ .el-checkbox {
    margin-right: 23px; /* 调整底部间距 */
  }
  /deep/ .el-checkbox__label {
    padding-left: 8px;
    font-weight: 400;
  }
  /deep/.el-checkbox__input.is-checked+.el-checkbox__label {
    color: rgba(0,0,0,0.9);
  }
  /deep/.el-checkbox__inner:hover {
    border-color: #DCDCDC;
  }
  /deep/.is-checked {
    /deep/.el-checkbox__inner:hover{
      background-color: #0052D9;
      border-color: #fff;
    }
  }
  /deep/.is-focus {
    border-color: #DCDCDC!important;
  }
  // 多选框
    /deep/ .el-checkbox__inner {
      width: 16px;
      height: 16px;
    }
    /deep/.el-checkbox__input.is-checked+.el-checkbox__label {
      color: rgba(0,0,0,0.9);
    }
    /deep/.el-checkbox__input.is-checked .el-checkbox__inner,
    /deep/.el-checkbox__input.is-indeterminate .el-checkbox__inner {
      background-color: #0052D9;
      border-color: #fff
    }
    /deep/.el-checkbox__inner::after {
      position: absolute;
      border-color: #fff;
      left: 5px;
      top: 2px
    }
    /deep/.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
      background-color: #fff;
    }
}
:deep .el-radio-button__inner:not(.is-checked) {
  color: #000000e6;
  text-align: center;
  font-family: 'PingFang SC';
}
:deep .el-radio-button__inner {
  border: none !important;
  font-size: 14px;
  font-weight: 400 !important;
}
.item-tooltip[data-popper-reference-hidden] {
  visibility: hidden;
  pointer-events: none;
}
.input-label {
  :deep .el-input__inner {
    padding-right: 45px;
  }
}
.dialogHome {
  /deep/.el-dialog__title {
    font-size: 16px;
    font-weight: 600;
    color: #000;
  }
  /deep/.el-dialog__headerbtn {
    right: 27px;
  }
  /deep/.el-dialog__header {
    padding: 16px 32px;
    border-bottom: 1px solid #E7E7E7;
  }
  /deep/.el-dialog__footer {
    padding: 0;
  }
  :deep .el-dialog {
    min-height: 758px;
    .subsTab {
      height: 56px;
      font-size: 14px;
      padding-top:12px;
      margin-bottom:16px;
    }
    .see-content-button {
      float: right;
      color: #0052d9;
      font-family: PingFang SC;
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 10px;
    }
  }
  :deep .el-dialog__body {
    padding: 0 32px 19px;
  }

}

:deep .el-form-item {
  margin-bottom: 0px !important;
}
.dialogbotton {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  padding: 16px 32px;
  border-top: 1px solid #e7e7e7;
}
.cancel-button {
  width: 40px;
  height: 24px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.Submit-button {
  width: 60px;
  height: 32px;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  border-radius: 3px;
  &.el-button--default{
    background: none;
    color: #000000e6;
    border: 1px solid #DCDCDC;
    &:hover{
      color: #0052D9;
      background: none;
      border-color: #0052D9;
    }
  }
  &.Submit-button-save{
    color: #fff;
    background: #0052D9;
    border-color: #0052D9;
  }
  &.Submit-button-save + .Submit-button-save {
    margin-left: 12px;
  }
}
:deep .el-dialog {
  border-radius: 9px;
}
:deep .image-text-page .tag-form-item[data-v-5ebdae47] .el-form-item__label {
  margin-top: 0px;
}
.LabelResource {
  width: 66%;
  float: left;
  margin-right: 0px !important;
  :deep .el-form-item__label {
    margin-top: 0px !important ;
    white-space: nowrap;
    width: 75px;
  }
  :deep .el-form-item__label:after {
    content: ' *';
    color: red;
  }
}
.label {
  color: #000000e6;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
}
.paratext {
  color: #00000080;
  margin-left: 24px;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
.addlLabel {
  cursor: pointer;
  color: #ED7B2F;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  margin-left: 24px;
  img {
    width: 16px;
    height: 16px;
    vertical-align: sub;
  }
}
.dialogs :deep .el-dialog__body {
  padding: 11px 20px;
}
.dialogs :deep .el-dialog .el-dialog__header {
  border-bottom: 0px !important;
}
:deep .el-form-item__label {
  width: 89px;
}
:deep .el-form--inline .el-form-item__content {
  width: 100%;
  line-height: initial;
}
:deep .el-form--inline .el-form-item {
  display: flex;
  margin-right: 0px;
}
.no-data2 {
  .no-data-center {
    margin-top: 72px;
    text-align: center;
    img {
      height: 160px;
      width: 160px;
    }
  }
  .no-data-text {
    margin-top: 9px;
    text-align: center;
    color: #00000099;
    text-overflow: ellipsis;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
  }
}
.no-data {
  min-height: 350px;
  max-height: 450px;
  overflow-y: auto;
  width: 100%;
  .no-data-center {
    justify-content: center;
    align-items: center;
    height: 70%;
    img {
      height: 200px;
      width: 200px;
    }
  }
  .no-data-text {
    justify-content: center;
    align-items: center;
  }
  :deep .el-form-item__content {
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
  }
}

.tags-label {
  :deep .el-tabs__nav-wrap::after {
    height: 0px;
  }
  :deep .el-tabs__header {
    // border-bottom:#F8F8F8 solid
    border-bottom: 1px solid var(--color-border-2, #e5e6eb);
    margin: 0;
  }

  /deep/ .el-tabs__item {
    padding: 6px 10px;
    height: 20px;
    color: #4e5969;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400 !important;
    line-height: 20px;
    margin-bottom: 0;
    box-sizing: content-box;
  }
  /deep/ .el-tabs__item.is-active {
    color: #0052d9 ;
  }
  /deep/.el-tabs__item:hover {
    color: #0052d9 ;
  }
  /deep/.el-tabs__nav .el-tabs__active-bar {
    background-color: #0052d9;
  }
  :deep .el-tabs__nav-prev {
    /* 添加你的样式修改 */
    width: 0;
    height: 0;
  }
  :deep .el-icon-arrow-left:before {
    color: #fff;
  }
  :deep .el-tabs__nav-prev::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 6px 5px 0;
    border-color: transparent #00000099 transparent transparent;
    left: 1px;
    top: 11px;
  }

  :deep .el-tabs__nav-next {
    // position: relative;
    // border-style: solid;
    width: 0px; /* 调整按钮的宽度 */
    height: 20px; /* 调整按钮的高度 */
  }
  /* 创建一个实体三角形 */
  :deep .el-tabs__nav-next::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 0 5px 6px;
    border-color: transparent transparent transparent #00000099;
    right: 0px;
    top: 11px;
  }
}
.tabsto {
  height: 32px;
  :deep .el-tabs__nav.is-top {
    height: 32px;
  }
  :deep .el-tabs__item.is-active:after {
    content: '';
    color: inherit;
  }
  :deep .el-tabs__item.is-top:after {
    content: '';
    color: inherit;
  }
  :deep .el-tabs__nav-wrap::after {
    height: 0px;
  }
  :deep .el-tabs__header {
    // border-bottom:#F8F8F8 solid
    border-bottom: 0px !important;
  }
  :deep .el-icon-arrow-left:before {
    color: currentColor;
  }
  /* 内部 'tabsto' 类的样式 */
  /* 你可以在这里添加适用于 'tabsto' 类的样式 */
  :deep .el-tabs__nav-prev {
    /* 添加你的样式修改 */
    width: 20;
    height: 0;
  }
  :deep .el-tabs__nav-prev::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0px 0px 0px 0;
    border-color: transparent transparent transparent transparent;
    left: 1px;
    top: 11px;
  }

  :deep .el-tabs__nav-next {
    // position: relative;
    // border-style: solid;
    width: 10px; /* 调整按钮的宽度 */
    height: 20px; /* 调整按钮的高度 */
  }
  /* 创建一个实体三角形 */
  :deep .el-tabs__nav-next::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0px 0 0px 0px;
    border-color: transparent transparent transparent transparent;
    right: 0px;
    top: 11px;
  }
  /deep/ i{
    position: relative;
    top: -6px;
  }
}
.clicked {
  color: #0052d9;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
}
.cascader-class {
  // display: none;
  :deep .el-input__validateIcon {
    display: none;
  }
}
.tagsd {
  // min-height: 38px;
  padding: 16px 0;
  .switchover-btn {
    color: #0052d9;
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    cursor: pointer;
  }
  .no-label {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    line-height: 35px;
    font-size: 12px;
    color: #00000080;
  }
  /deep/ .el-form-item__label {
    color: #000000e6;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    // margin-top: 15px;
    padding-right: 0px;
    text-align: left;
    padding-left: 2px;
    width: 84px;
  }
  /deep/ .el-form-item__content {
    // margin-top: 5px;
    // line-height: 35px;
    .custom-tag{
      margin-top: 0;
    }
  }
}
.labelListTitle {
  padding:20px 0px 12px;
  /deep/.el-form-item__content{
    line-height: 32px;
  }
}
.label-tags-list {
  font-weight: bold;
  color: #000000e6;
  text-align: right;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  line-height: 22px;
}
.label-list-hint {
  margin-left: 24px;
  color: #00000080;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  font-weight: 400;
  line-height: 20px;
}
.label-select-component {
  font-family: "PingFang SC";
}
.label-select-component .project-tag-box {
  width: 240px;
  float: right;
  position: relative;
  top: 1px;
}
.specialPanelTop{
  margin-top: 16px;
  border-top: 1px solid #E7E7E7;
  padding: 16px 0;
  /deep/ .el-form-item__content{
    display: flex;
    align-items: center;
    position: relative;
    height: 32px;
    .specialSelect-input{
      position: absolute;
      width: 240px;
      height: 32px;
      padding: 4px 4px 4px 8px;
      border-radius: 3px;
      display: flex;
      align-items: center;
      right: 0;
      border: 1px solid #DCDCDC;
      overflow: hidden;
      input{
        height: 100%;
        flex: 1;
        outline: none;
        border: none;
        &::placeholder {
          color: #00000066;
          font-size: 12px;
        }
      }
    }
  }
}
</style>
