<template>
  <div class="activity-info-container">
    <el-form :model="form" :rules="rules" ref="form3" label-width="124px" key="form3">

      <el-form-item label="上传至" prop="category_id" v-if="pageId && !isEdit">
        <el-input v-model="specialName" placeholder="专区名称" disabled class="course-name-input"></el-input>
        <el-cascader :value="categoryId" :options="categoryOptions" :props="categoryProps" @change="handleCategoryChange" class="course-name-input"></el-cascader>
      </el-form-item>

      <el-form-item label="活动名称" prop="activity_name">
        <el-input v-model="form.activity_name" placeholder="请输入内容" class="course-name-input" @input="handleInputActivityName" maxlength="101"></el-input>
        <div class="normal-tips" style="margin-top: 8px;display: flex;align-items: center;">
          若需<span class="color-red">「课程测试」</span>体验效果，请先<span class="color-red"><a href="https://iwiki.woa.com/p/4012202789" target="_blank">查看填写说明</a></span>，以免耽误进度哦！
        </div>
      </el-form-item>

      <el-form-item label="内容标签" prop="labels">
        <sdc-addlabel
          v-model="form.labels"
          class="project-tag-box"
          :labelNodeEnv="labelNodeEnv"
          @getSelectedLabelList="getSelectedLabelList"
        />
      </el-form-item>

      <el-form-item label="活动封面" prop="photo_url">
        <cut-img-upload 
          ref="upload" 
          @handleSuccess="handleSuccessImage"
          :dialogImageUrl="form.photo_url" 
          :autoImgUrl="form.photo_url" 
          @handleClearImg="handleClearImg"
          @handleImgEdit="handleImgEdit"
          :cover_imgage_storage_type="form.photo_id ? 'zhihui' : 'contentcenter'"
          >
            <template v-slot:text>
              <p>建议图片尺寸：480*320px或3:2宽高比</p>
            </template>
            <template v-slot:createImg>
              <p class="text-orange" style="color: #E37318;cursor: pointer;display: flex; align-items: center" @click="handleAutoImg"><img class="icon" style="width:16px; height: 16px; margin-right: 4px" src="~@/assets/img/tips.png" alt="">快速生成封面图</p>
            </template>
        </cut-img-upload>
      </el-form-item>

      <el-form-item label="活动详情" prop="description" class="project-detail-tincy">
        <sdc-mce-editor
          ref="editor" 
          selector="activity_info1"
          :env="editorEnv" 
          :content="form.description"
          :urlParams="editorConfig.urlParams"
          :catalogue.sync="editorConfig.catalogue"
          :urlConfig="editorConfig.urlConfig"
          :options="editorConfig.options"
          :insertItems="insertItems"
          :key="3"
        />
        <!-- <o-team-rich-text ref="richTextEditor" :textValue="form.description">
          <div slot="container" class="custom-container" style="width: 700px; height: 600px;"></div>
        </o-team-rich-text> -->
      </el-form-item>

      <el-form-item label="活动负责人" prop="head_teacher_info" class="width785">
        <sdc-staff-selector 
          multiple 
          ref="adminsActivityHeadrRef" 
          v-model="head_teacher_info"
          size="small" 
          placeholder="请选择活动负责人"
          @change="changeCourseAuth"
        />
      </el-form-item>

      <el-form-item label="内部分享人员" prop="inner_teacher_info" class="width785">
        <sdc-staff-selector
          style="width: 785px"
          multiple 
          ref="adminsInnerRef" 
          v-model="inner_teacher_info"
          size="small"
          placeholder="请选择内部分享人员"
          @change="changeCourseInner"
        />
        <orange-tips title="若创建活动时活动分享人未确认，请务必在活动结束前将分享人补充完整，否则将影响分享人的个人积分" style="margin-top: 9px;"></orange-tips>
      </el-form-item>

      <el-form-item label="外部分享人员" prop="outer_teacher" class="width785">
        <el-input v-model="form.outer_teacher" placeholder="请输入外部分享人员" class="course-name-input"></el-input>
      </el-form-item>

      <el-form-item label="更多设置" >
        <div class="more-setting" @click="showMoreSettingFun">
          点击查看更多配置项
        </div>
      </el-form-item>
    </el-form>

    <more-setting-dialog 
      :visible.sync="moreSettingShow"
      :formData="form"
      :baseInfo="baseInfo"
      @confirm="moreSettingConfirm"
      @cancel="moreSettingCancel"
    />

    <el-dialog :visible.sync="systemDialog" title="选择系统" width="40%" top="20px" :show-close="true" :close-on-click-modal="false">
      <el-table :data="systemList" style="width: 100%;height: 500px;overflow-y: auto;">
        <el-table-column prop="item_id" label="序号" width="120" align="center"></el-table-column>
        <el-table-column prop="item_name" label="系统名称" align="center"></el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleSelectSystem(scope.row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>

  <!-- 一键生成项目封面 -->
  <sdc-img-cover 
    ref="sdcImgCoverRef" 
    :visible.sync="autoImgCoverShow"
    :imgInfo="imgInfo"
    @handleImgCoverOk="handleImgCoverOk"
  >
  </sdc-img-cover>
  </div>
</template>

<script>
import { CutImgUpload } from '@/components/index'
import orangeTips from './orangeTips.vue'
import MoreSettingDialog from './moreSettingDialog.vue'
import { operationInfo, checkActivityExist, getSpecialDetail, getSpecialCategorys } from 'config/classroom.api.conf'
import { debounce } from '@/utils/tools.js'
import { mapState } from 'vuex'
// import OTeamRichText from '@/components/o-team-rich-text.vue'

export default {
  components: {
    CutImgUpload,
    orangeTips,
    MoreSettingDialog
    // OTeamRichText
  },
  props: {
    baseInfo: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    baseInfo(newVal) {
      if (newVal) {
        this.isEdit = !!newVal.activity_id
        if (this.isEdit) {
          this.setBaseInfo()
        }
      }
    },
    userDepInfo: {
      handler(newVal) {
        if (newVal && !this.isEdit) {
          // 设置活动负责人默认值
          this.form.head_teacher_name = `${newVal.emp_name_en}(${newVal.emp_name_ch})`
          this.form.head_teacher_id = newVal.staff_id
          
          // 设置活动管理组织默认值
          if (newVal.dept_id && newVal.dept_full_name) {
            this.setActivityManageOrgDefault(newVal)
          }
          
          this.$nextTick(() => {
            let list = this.parseTeachers(this.form.head_teacher_id, this.form.head_teacher_name)
            if (list?.length > 0) {
              this.$refs.adminsActivityHeadrRef && this.$refs.adminsActivityHeadrRef.setSelected(list)
            }
          })
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState(['userInfo', 'userDepInfo']),
    labelNodeEnv() {
      return process.env.NODE_ENV === 'production' ? 'production' : 'test'
    },
    // 角色权限
    levelList() {
      // if (this.isApprove) {
      //   this.courseLevelList.forEach(item => item.disabled = true)
      // } else {
      //   const id = this.$route.query.activity_id || ''
      //   if (!id) {
      //     this.activityInfo.activity_level = 0
      //   }
      // }
      return this.courseLevelList
    },
    // 运营分级
    operation_type_options() {
      let list
      if (process.env.NODE_ENV !== 'production') {
        list = [
          { label: '非体系', value: 3, pid: 526 },
          { label: '基础', value: 2, pid: 569 },
          { label: '中坚', value: 1, pid: 571 },
          { label: '头部', value: 0, pid: 573 }
        ]
      } else {
        list = [
          { label: '非体系', value: 3, pid: 528 },
          { label: '基础', value: 2, pid: 525 },
          { label: '中坚', value: 1, pid: 526 },
          { label: '头部', value: 0, pid: 527 }
        ]
      }
      return list
    },
    pageId() {
      return this.$route.query.page_id || ''
    }
  },
  data() {
    return {
      specialName: '',
      categoryOptions: [],
      categoryProps: {
        label: 'category_name',
        value: 'category_id',
        children: 'children',
        checkStrictly: true
      },
      categoryId: [],
      form: {
        page_id: '',
        category_id: '',
        activity_name: '',
        labels: [],
        photo_url: '',
        photo_id: '',
        description: '',

        head_teacher_name: '', // 活动负责人名字
        head_teacher_id: '', // 活动负责人id

        inner_teacher: '', // 内部分享人员名字
        inner_teacher_ids: '', // 内部分享人员id

        outer_teacher: '',

        remark: '', // 运营信息备注
        system_name: '', // 所属系统
        is_show_recommend: true, // 是否展示课程推荐
        pdi_sub_level: '', // 活动类型
        activity_level: 0, // 认证等级
        
        course_statement: {
          operation_title: '',
          creation_source: 2,
          ugc_is_original: 1,
          human_cost: '',
          // 活动管理组织的拆分
          dept_id: '',
          dept_full_name: '',
          is_required: 0,
          join_recommend: 1, // 是否加入推荐池
          operation_level: 3, // 运营分级
          operation_project_name: '', // 分级项目具体名称
          expert_score: '' // 内容专家评分
        },
        nameExist: false // 添加标记是否存在重名的字段
      },
      // moreSettingForm: {
        
      // },
      // moreSettingRules: {
      //   human_cost: [{ trigger: 'blur', validator: this.validHumanCost }],
      //   operation_project_name: [{ required: true, message: '请选择运营分级', trigger: 'blur' }],
      //   activity_level: [{ required: true, trigger: 'blur', validator: this.validActivityLevel }],
      //   expert_score: [{ trigger: 'change', validator: this.validExpertScore }],
      //   activity_source: [{ required: true, message: '请选择活动来源', validator: this.validActivitySource }],
      //   activity_manage_org: [{ required: true, message: '请选择活动管理组织', trigger: 'change', validator: this.validActivityManageOrg }],
      //   system_name: [{ required: true, trigger: 'change', validator: this.validSystemName }]
      // },
      inner_teacher_info: [], // 内部分享人员信息
      // activity_manage_org: [], // 活动管理组织
      cover_image_id: '', // 智慧封面图片id
      rules: {
        category_id: [{ required: true, message: '请选择分类', trigger: 'change' }],
        activity_name: [
          { required: true, trigger: 'blur', validator: this.validActivityName }
        ],
        labels: [{ required: true, trigger: 'change', validator: this.validLabels }],
        head_teacher_info: [{ required: true, message: '请选择活动负责人', trigger: 'blur', validator: this.validHeadTeacher }],
        photo_url: [{ required: true, message: '请上传封面图片', trigger: 'blur' }]
      },
      head_teacher_info: [], // 活动负责人信息
      moreSettingShow: false,
      dialogImageUrl: '',
      autoImgUrl: '',
      autoImgCoverShow: false,
      imgInfo: {},
      editorEnv: process.env.NODE_ENV,
      editorConfig: {
        catalogue: false,
        options: { // 非必传字段
          selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
              formatselect fontsizeselect lineheight |
              bold italic underline strikethrough |
              forecolor backcolor |
              dent align bullist numlist |
              insert codesample |
              fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          // contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          contentinfo: `/content-center/api/v1/content/save_contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        urlParams: {
          is_public: true
        },
        is_open_catalogue: false
      },
      insertItems: [
        'link',
        'table',
        'image',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      courseLevelList: [
        { code: 0, name: '无', disabled: false }, 
        { code: 1, name: '公司级', disabled: false }, 
        { code: 2, name: 'BG级', disabled: false },
        { code: 3, name: '部门级', disabled: false }
      ],
      activityTypeList: [],
      // 分级目录选项
      operation_project_name_options: [],
      // 分级目录集合
      operation_project_name_options_map: {},
      isEdit: false,
      systemDialog: false,
      systemList: []
    }
  },
  mounted() {
    this.getSystemList()
    if (this.pageId) {
      this.form.page_id = this.pageId
      this.getSpecialDetailFn()
      this.getSpecialCategorysList()
    }
  },
  methods: {
    handleCheckMoreSetting() {
      let isPass = true
      if (this.form.course_statement.creation_source === 0) {
        let length = this.form.course_statement.pgc_creation_org ? this.form.course_statement.pgc_creation_org.length : 0
        if (length === 0) {
          isPass = false
        }
      }

      if (this.form.course_statement.creation_source === 3) {
        let length = this.form.course_statement.pugc_creation_org ? this.form.course_statement.pugc_creation_org.length : 0
        if (length === 0) {
          isPass = false
        }
      }

      if (this.form.course_statement.creation_source === 2 && this.form.course_statement.ugc_is_original === 0) {
        let length = this.form.course_statement.ugc_link_url ? this.form.course_statement.ugc_link_url.length : 0
        if (length === 0) {
          isPass = false
        }
      }

      if (this.form.course_statement.creation_source === 1) {
        let length = this.form.course_statement.ogc_supplier_name ? this.form.course_statement.ogc_supplier_name.length : 0
        let length2 = this.form.course_statement.ogc_purchase_org ? this.form.course_statement.ogc_purchase_org.length : 0
        if (length === 0 || length2 === 0) {
          isPass = false
        }
      }
      
      return isPass
    },
    handleInputActivityName: debounce(function (value) {
      if (value.length > 100) {
        this.$refs.form3.validateField('activity_name')
      } else {
        // 只在输入时调用查重接口
        this.checkActivityExist(value).then(res => {
          if (res) {
            this.form.nameExist = true
            this.$refs.form3.validateField('activity_name')
          } else {
            this.form.nameExist = false
            this.$refs.form3.clearValidate('activity_name')
          }
        })
      }
    }, 500),
    validActivityLevel(rule, value, callback) {
      // let v = this.form.activity_level
      // if (!v) {
      //   return callback(new Error('请选择认证等级'))
      // }
      callback()
    },
    validSystemName(rule, value, callback) {
      let v = this.form.system_name
      if (!v) {
        return callback(new Error('请选择所属系统'))
      }
      callback()
    },
    validHumanCost(rule, value, callback) {
      // 限制为数字即可
      if (!/^\d+(\.\d{1,2})?$/.test(value)) {
        return callback(new Error('请输入数字'))
      }
      callback()
    },
    validLabels(rule, value, callback) {
      if (!this.form.labels.length) {
        return callback(new Error('请选择内容标签'))
      }
      callback()
    },
    validActivityManageOrg(rule, value, callback) {
      let list = this.form.dept_id ? this.form.dept_id.split(';') : ''
      if (!list.length) {
        return callback(new Error('请选择活动管理组织'))
      }
      callback()
    },
    validHeadTeacher(rule, value, callback) {
      let list = this.form.head_teacher_id ? this.form.head_teacher_id.split(';') : ''
      if (!list || !list.length) {
        return callback(new Error('请选择活动负责人'))
      }
      callback()
    },
    validActivitySource(rule, value, callback) {
      callback()
    },
    handleAddSystem() {
      this.systemDialog = true
    },
    handleSelectSystem(row) {
      this.form.system_name = row.item_name
      this.systemDialog = false
    },
    getSystemList() {
      operationInfo({ key: 'class_system' }).then(res => {
        this.systemList = res
      })
    },
    checkActivityExist(name) {
      return new Promise(resolve => {
        let params = {
          activity_name: name
        }
        if (this.isEdit) {
          params.activity_id = this.baseInfo.activity_id
        }
        checkActivityExist(params).then(res => {
          resolve(res)
        })
      })
    },
    getFormOptions() {
      operationInfo({ key: 'Code_PDI' }).then(res => {
        this.activityTypeList = res
        if (this.form.pdi_sub_level) {
          this.$set(this.form, 'pdi_sub_level', String(this.form.pdi_sub_level))
        } else {
          this.$set(this.form, 'pdi_sub_level', String(res[0].item_value))
        }
      })
    },
    getSelectedLabelList(val) {
      this.form.labels = val.map(item => {
        return {
          ...item,
          label_type_association: 1
        }
      })
      // 手动触发校验
      this.$refs.form3.validateField('labels')
    },
    handleSuccessImage(res) {
      this.form.photo_url = res
      this.form.photo_id = ''
      this.$refs.form3.validateField('photo_url')
    },
    handleImgEdit() {
      this.$refs.sdcImgCoverRef.outEdit({
        id: this.cover_image_id,
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        type: 'outEdit'
      })
    },
    // 一键封面回传
    handleImgCoverOk(row) {
      this.form.photo_url = row.url
      this.form.photo_id = row.id
      this.$refs.form3.validateField('photo_url')
    },
    handleAutoImg() {
      this.autoImgCoverShow = true
      this.imgInfo = {
        title: this.form.activity_name,
        env: process.env.NODE_ENV === 'production' ? 'production' : 'test',
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        showEdit: true
      }
    },
    // 清空图片
    handleClearImg() {
      this.form.photo_url = ''
    },
    // 管理员
    changeCourseAuth(val) {
      this.form.head_teacher_id = val.map(item => item.StaffID).join(';')
      this.form.head_teacher_name = val.map(item => item.StaffName).join(';')
      this.head_teacher_info = val
      // 手动触发校验
      this.$refs.form3.validateField('head_teacher_info')
    },
    // 内部分享人员
    changeCourseInner(val) {
      this.form.inner_teacher_ids = val.map(item => item.StaffID).join(';')
      this.form.inner_teacher = val.map(item => item.StaffName).join(';')
    },
    // 运营分级改变
    changeOperationType(val) {
      this.form.course_statement.operation_project_name = ''
      let pid = this.operation_type_options.find(item => item.value === val).pid
      if (this.operation_project_name_options_map[pid]) {
        this.operation_project_name_options = this.operation_project_name_options_map[pid]
        return
      }
      operationInfo({ key: 'operations_level', pid }).then(res => {
        this.operation_project_name_options_map[pid] = res
        this.operation_project_name_options = res
      })
    },
    changeActivityManageOrg(val) {
      if (val.length > 0 && !val[0].UnitID) return
      this.form.dept_id = val.map(item => item.UnitID).join(';')
      this.form.dept_full_name = val.map(item => item.UnitFullName).join(';')
    },
    parseTeachers(id, name) {
      if (!id || !name) return
      const idList = String(id).split(';').filter(item => item)
      const nameList = name.split(';').filter(item => item)

      // 2. 创建结果数组
      const result = []

      // 3. 遍历 id 和 name 列表,并组合成对象
      for (let i = 0; i < idList.length; i++) {
        const id = idList[i]
        const name = nameList[i]

        // 4. 提取 id 和 name 中的实际值
        const StaffID = parseInt(id.split('(')[0])
        const StaffName = name

        // 5. 将数据添加到结果数组
        result.push({
          StaffID,
          StaffName
        })
      }

      return result
    },
    showMoreSettingFun() {
      // 在打开更多设置弹窗前，确保默认值已正确设置
      if (!this.form.dept_id && !this.form.dept_full_name && this.userDepInfo) {
        // 从course_statement复制到form顶层，以便moreSettingDialog使用
        this.form.dept_id = this.form.course_statement.dept_id
        this.form.dept_full_name = this.form.course_statement.dept_full_name
      }
      
      // 确保is_show_recommend存在且为布尔类型
      if (this.form.is_show_recommend === undefined) {
        this.form.is_show_recommend = true
      }
      
      this.moreSettingShow = true
    },
    moreSettingCancel() {
      this.moreSettingShow = false
    },
    moreSettingConfirm(updatedForm) {
      this.moreSettingShow = false
      
      // 深度合并表单数据，优先使用新数据
      const newForm = {
        ...this.form,
        ...updatedForm,
        course_statement: {
          ...this.form.course_statement,
          ...updatedForm.course_statement
        }
      }
      
      // 使用新的组织数据，而不是保留旧数据
      if (updatedForm.course_statement.pgc_creation_org) {
        newForm.course_statement.pgc_creation_org = updatedForm.course_statement.pgc_creation_org
        newForm.course_statement.pgc_creation_dept_id = updatedForm.course_statement.pgc_creation_dept_id
        newForm.course_statement.pgc_creation_dept_name = updatedForm.course_statement.pgc_creation_dept_name
      }
      if (updatedForm.course_statement.pugc_creation_org) {
        newForm.course_statement.pugc_creation_org = updatedForm.course_statement.pugc_creation_org
        newForm.course_statement.pugc_creation_dept_id = updatedForm.course_statement.pugc_creation_dept_id
        newForm.course_statement.pugc_creation_dept_name = updatedForm.course_statement.pugc_creation_dept_name
      }
      if (updatedForm.course_statement.pugc_joint_creation) {
        newForm.course_statement.pugc_joint_creation = updatedForm.course_statement.pugc_joint_creation
        newForm.course_statement.pugc_joint_creation_id = updatedForm.course_statement.pugc_joint_creation_id
        newForm.course_statement.pugc_joint_creation_name = updatedForm.course_statement.pugc_joint_creation_name
      }
      if (updatedForm.course_statement.ogc_purchase_org) {
        newForm.course_statement.ogc_purchase_org = updatedForm.course_statement.ogc_purchase_org
        newForm.course_statement.ogc_purchase_org_name = updatedForm.course_statement.ogc_purchase_org_name
      }
      
      this.form = newForm
    },
    async submit({ isDraft = false } = {}) {
      try {
        this.form.description = this.$refs['editor'].getContent()
        // this.form.description = await this.$refs['richTextEditor'].getContent()
        return new Promise(resolve => {
          let info = {
            data: {},
            isPass: false
          }
          this.$refs.form3.validate((valid) => {
            if (valid) {
              info.isPass = true
            } else {
              info.isPass = false
            }
            info.data = this.form
            info.data.is_show_recommend = info.data.is_show_recommend ? 1 : 0
            info.data.course_statement.is_required = info.data.course_statement.is_required === 1

            let isMoreSettingPass = this.handleCheckMoreSetting()
            if (!isMoreSettingPass && !isDraft) {
              // setTimeout(() => {
              //   this.$message.error('更多设置还有信息未完善')
              // }, 1000)
              info.isPass = false
            }

            resolve(info)
          })
        })
      } catch (error) {
        console.log(error, 'errorttyyy')
      }
    },
    // 限定0-100，且可以输入两位小数，且不能是0开头
    validExpertScore(rule, value, callback) {
      if (!value) {
        return callback()
      }
      if (value > 100 || value < 0 || !/^\d+(\.\d{1,2})?$/.test(value) || String(value).startsWith('0')) {
        return callback(new Error('请输入0-100之间的数字，且输入的数字要符合格式'))
      }
      callback()
    },
    validActivityName(rule, value, callback) {
      if (!value) {
        return callback(new Error('请输入活动名称'))
      }
      if (value.length > 100) {
        return callback(new Error('活动名称不能超过100个字符'))
      }
      // 只在存在重名时抛出错误
      if (this.form.nameExist) {
        return callback(new Error('活动名称重复'))
      }
      callback()
    },
    setBaseInfo() {
      this.form = {
        ...this.baseInfo
      }
      // 处理活动负责人
      let list = this.parseTeachers(this.baseInfo.head_teacher_id, this.baseInfo.head_teacher_name)
      if (list?.length > 0) {
        this.$refs.adminsActivityHeadrRef && this.$refs.adminsActivityHeadrRef.setSelected(list)
      }
      // 处理内部分享人员
      let list2 = this.parseTeachers(this.baseInfo.inner_teacher_ids, this.baseInfo.inner_teacher)
      if (list2?.length) {
        this.$refs.adminsInnerRef && this.$refs.adminsInnerRef.setSelected(list2)
      }
    },
    // 获取专区详情
    getSpecialDetailFn() {
      getSpecialDetail(this.pageId).then(res => {
        this.specialName = res.name || ''
      })
    },
    // 获取专区分类列表
    getSpecialCategorysList() {
      getSpecialCategorys(this.pageId).then(res => {
        this.categoryOptions = this.cleanEmptyChildren(res || [])

        if (this.$route.query?.categoryIds) {
          this.categoryId = this.$route.query.categoryIds.split(',').map(Number)
          this.form.category_id = this.categoryId[this.categoryId.length - 1]
        }
      })
    },
    cleanEmptyChildren(data) {
      data.forEach(item => {
        if (item.children && Array.isArray(item.children)) {
          this.cleanEmptyChildren(item.children)

          if (item.children.length === 0) {
            delete item.children
          }
        }
      })

      return data
    },
    handleCategoryChange(val) {
      this.form.category_id = val[val.length - 1]
    },
    processPath(str) {
      if (!str) return ''
      
      const segments = str.split('/').filter(segment => segment !== '')

      if (segments.length === 0) return str

      const lastSegment = segments[segments.length - 1]

      return lastSegment === 'nn' ? str : lastSegment
    },
    // 设置活动管理组织默认值
    setActivityManageOrgDefault(depInfo) {
      if (!depInfo || !depInfo.dept_full_name) return
      
      // 设置到course_statement
      this.form.course_statement.dept_id = depInfo.dept_id
      this.form.course_statement.dept_full_name = depInfo.dept_full_name
    }
  }
}
</script>
<style lang="less">
.el-form .el-form-item {
  margin-bottom: 24px;
}
.project-detail-tincy {
  .editor__area {
    background: #fff;
    .activity_info1.fullscreen .tox.tox-tinymce {
      width: unset;
    }
  }
  .tox.tox-tinymce {
    box-sizing: border-box;
    border: 1px solid #ccc !important;
    height: 451px;
    width: 785px;
    margin: initial;
    .tox-sidebar-wrap .tox-edit-area {
      min-height: 374px !important;
      box-sizing: border-box;
    }
  }
}
</style>
<style scoped lang="less">
.el-radio input[aria-hidden="true"] {
  display: none !important;
}

.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none !important;
}
.activity-info-container {
  margin-bottom: 72px;

  :deep(.el-dialog.activity-info-more-setting-dialog) {
    border-radius: 8px;
    margin-top: 60px !important;
    width: 966px !important;
    padding: 0 0 0 10px;
    height: 80%;
    overflow-y: hidden;
    .el-dialog__body {
      padding: 30px 0;
      overflow-y: auto;
      height: calc(100% - 65px - 70px);
    }
    .more-setting-container {
      padding-right: 40px;
    }
    .belongs-system-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .system-name-input {
        width: 90%;
      }
      .system-name-button {
        margin-left: 10px;
      }
    }
  }
  .remark-textarea {
    height: 100px;
    max-width: 800px;
    :deep(.el-textarea__inner) {
      height: 100%;
      max-width: 800px;
    }
  }
  background: #fff;
  padding: 32px 12px;
  .width785 {
    :deep(.el-form-item__content) {
      max-width: 785px;
    }
  }
  .width496 {
    :deep(.el-form-item__content) {
      max-width: 496px;
    }
  }
  .width80 {
    :deep(.el-form-item__content) {
      width: 80%;
    }
  }
  .more-setting {
    color: #0052d9;
    font-family: "PingFang SC";
    font-size: 14px;
    cursor: pointer;
  }
  .human-cost-unit {
    color: #00000066;
    font-family: "PingFang SC";
    font-size: 14px;
  }
  .normal-tips {
    color: #999999;
    font-family: "PingFang SC";
    font-size: 12px;
    line-height: initial;
    .color-red, a {
      color: #E34D59;
    }
    a {
      text-decoration: underline;
    }
  }
  .creation_source_sub_content {
    background: #f9f9f9;
    border-radius: 4px;
    margin-top: 12px;
    padding: 12px;
    width: 626px;
  }
}

.course-name-input {
  width: 785px;
}

.project-tag-box {
  // margin-top: -6px;
  :deep(.cascader-component .el-button) {
    padding: unset;
  }
}

</style>
