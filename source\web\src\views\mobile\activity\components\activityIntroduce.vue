<template>
  <div class="duce" id="duce-ele">
    <div class="mid-tag-box">
      <span class="score">{{ courseData.avg_score ? `${courseData.avg_score}分` : '暂无评分'}}</span>
      <!-- 标签展示 -->
      <template v-if="courseData.course_id">
        <sdc-label-show-mob 
          ref="labelShow" 
          :labelNodeEnv="labelNodeEnv" 
          :actType="4" 
          :courseId="course_id" 
          :isH5="false" 
          :isMock="false"
          :showAll="true"
          @toSearchPage="toSearchPage"
          >
        </sdc-label-show-mob>
      </template>
    </div>
    <!-- 简介 -->
    <div :class="['class-content', { 'class-content-moer': showMoerList }]">
      <div class="des-content-detail" v-html="courseData.course_desc"></div>
      <div :class="['show-more-brn', { 'show-more-brn-moer': showMoerList }]" v-if="descOverflow">
        <span @click="clickMoer" class="show-more-brn-text">{{!showMoerList ? '展开查看更多' : '收起' }} <i class="icon" :class="!showMoerList ? 'el-icon-arrow-down' : 'el-icon-arrow-up' "></i> </span>
      </div>
      <!-- <div class="more-bg" v-if="descOverflow">
        <span class="more-btn" @click="desDetailShow=true" :dt-remark="dtMore('remark', '更多', '简介')" :dt-areaid="dtMore('areaid', '更多', '简介')" :dt-eid="dtMore('eid', '更多', '简介')">更多</span>
      </div> -->

    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
export default {
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      paddingBottom: true,
      labelNodeEnv:
        process.env.NODE_ENV === 'production' ? 'production' : 'test',
      descOverflow: false,
      showMoerList: false
    }
  },
  watch: {
    // 'courseData.course_desc': {
    //   handler() {
    //     setTimeout(() => {
    //       const desEle = document.getElementById('duce-ele')
    //       const height = desEle.offsetHeight
    //       console.log(desEle, height, 'heightheightheight')
    //       if (height > 350) {
    //         this.descOverflow = true
    //       }
    //     }, 1000)
    //   }
    // }
  },
  computed: {
    ...mapState(['userInfo']),
    course_id() {
      return this.courseData.course_id || this.$route.query.course_id || this.$route.query.activity_id
    },
    previewLbael() {
      return this.courseData.course_labels || []
    }
  },
  mounted() {
    setTimeout(() => {
      const desEle = document.getElementById('duce-ele')
      const height = desEle.offsetHeight
      console.log(desEle, height, 'heightheightheight')
      if (height > 300) {
        this.descOverflow = true
      }
    }, 800)
  },
  methods: {
    // 获取当前环境是不是小程序
    getMiniProgramEnv() {
      // 通过判断navigator.userAgent中包含miniProgram字样
      let userAgent = navigator.userAgent
      return /miniProgram/i.test(userAgent)
    },
    toSearchPage({ url, item }) {
      let href = `${url}&fromNet=fromNetLabel`
      if (this.getMiniProgramEnv()) {
        let { type } = this.$route.query
        let isMookNet = type === 'taskContent' || false
        // 小程序内直接跳转
        if (!isMookNet) {
          // 如果是网络课跳转 不经过taskContent
          window.wx.miniProgram.navigateTo({
            url: href || ''
          })
          return
        }
        // 如果是mooc嵌套的网络课跳转 经过taskContent
        window.parent.postMessage({
          event: 'toSearch',
          href,
          item
        }, '*')
      } else {
        let keywords = item.label_name || ''
        window.location.href = `https://sdc.qq.com/s/yJyZMs?fromNet=fromNetLabel&keyword=${keywords}`
      }
    },
    clickMoer() {
      this.showMoerList = !this.showMoerList
    },
    dtMore() {
      return (type, val, container) => {
        const data = {
          page: this.courseData.course_name,
          page_type: '活动详情页面-移动新版',
          container: `详情-${container}`,
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: val,
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'H5'
        }
        if (type === 'remark') {
          return JSON.stringify(data)
        } else if (type === 'eid') {
          return `element_${this.course_id}_${val}`
        } else {
          return `area_${this.course_id}_${val}`
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>

.duce {
  padding: 8px 16px;
  margin-bottom: 10px;
  background-color: #fff;
  .mid-tag-box {
    display: flex;
    align-items: center;
    margin-top: 8px;
    margin-bottom: 8px;
    :deep(.label-show) {
      line-height: 20px;
      width: calc(100% - 60px);
      .empty {
        margin-bottom: unset;
      }
      .label-show-all {
        word-break: break-all;
        word-wrap: break-word;
        overflow: hidden;
        height: 20px;
        .tag-item {
          padding: 0 8px;
          border-radius: 4px;
          line-height: 20px;
          height: 20px;
        }
      }
    }
    .score {
      display: inline-block;
      color: #00000099;
      font-family: 'PingFang SC';
      font-size: 10px;
      font-weight: 500;
      line-height: 20px;
      padding: 0 8px;
      border-radius: 4px;
      background: #f7f8fa;
      flex-shrink: 0;
      margin-right: 8px;
    }
  }
  .class-content {
    max-height: 300px;
    padding: 0 12px;
    background-color: #fff;
    overflow: hidden;
    position: relative;
  }
  .class-content-moer {
    max-height: unset;
  }
  .show-more-brn {
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    height: 73px;
    background-image: linear-gradient(
      0deg,
      #fff 0,
      #fff 30%,
      rgba(255, 255, 255, 0.8) 65%,
      rgba(255, 255, 255, 0) 100%
    );
    .show-more-brn-text {
      width: 102px;
      height: 28px;
      line-height: 28px;
      font-size: 11px;
      font-weight: 500;
      border-radius: 36px;
      background-color: #fff;
      box-shadow: 0 0 8px 0 #a0a0a040;
      text-align: center;
      margin-top: 40px;
      color: #0052d9;
      .icon {
        font-weight: 500;
      }
    }
  }
  .show-more-brn-moer {
    position: relative;
    height: 40px;
    background-color: #fff;
    .show-more-brn-text {
      margin: 10px 0;
    }
  }
}
</style>
