<template>
  <div>
    <el-dialog
      :visible.sync="isShowCertificatePopup"
      custom-class="certificate-list-popup"
      title="证书详情"
      width="960px"
      :lock-scroll="false"
      :close-on-click-modal="false"
      :before-close="closeDialog">
      <div class="l-r">
        <p>
          <label>学员名称：</label>
          <span>{{ certificateData.staff_name }}</span>
        </p>
        <p><label>所属组织：</label><span>{{ certificateData.dept_full_name }}</span></p>
      </div>
      <div class="certificate-list">
        <div class="card" v-for="item in certificateList" :key="item.id">
          <div class="pic">
            <img :src="item.certificateStatus === -2 ? certificateData.certificate_teamplate_url : item.certificateImageUrl" class="image">
            <div class="look" @click="openDetailsDialog(item)">
              <img src="@/assets/mooc-img/search.png" alt="" srcset="">
              <span>{{ handleCertificateStatusText(item) }}</span>
            </div>
          </div>
          <div class="info">
            <p class="name">{{ item.certificateName || '-' }}</p>
            <p><label>状态：</label><el-tag :type="handleCertificateStatusType(item)" size="mini" effect="dark">{{ handleCertificateStatus(item) }}</el-tag></p>
            <p><label>证书编号：</label>{{ item.certificateBatchNo }}</p>
            <p><label>颁发时间：</label>{{ item.createdAt }}</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import pagination from '@/mixins/pager'
import { handleImgUrl, certificateView } from '@/utils/tools.js'
/* eslint-disable */
import { getManageStudentCertificateListAPI, getUserStudentCertificateListAPI } from '@/config/mooc.api.conf.js'
/* eslint-disable */
export default {
  mixins: [pagination],
  props: {
    isShowCertificatePopup: {
      type: Boolean,
      default: false
    },
    // user-用户端 manage-管理端
    moduleType: {
      type: String,
      default: 'manage'
    }
  },
  data () {
    return {
      certificateData: {
        staff_name: '-',
        dept_full_name: '-',
        course_title: ''
      },
      certificateList: []
    }
  },
  methods: {
    // 获取学员证书列表
    onSearch(staff_id) {
      const { mooc_course_id } = this.$route.query
      const params = {
        mooc_course_id,
        staff_id
      }
      let apiName = ''
      switch (this.moduleType) {
        case 'manage': 
          apiName = getManageStudentCertificateListAPI
          break
        case 'user':
          apiName = getUserStudentCertificateListAPI
          break
      }
      apiName(params).then(res => {
        if (res.certificate_teamplate_url) {
          res.certificate_teamplate_url = handleImgUrl(res.certificate_teamplate_url)
        }
        this.certificateData = res
        this.certificateList = res.certificate_info_list
      })
    },
    // 关闭弹窗
    closeDialog () {
      this.$emit('update:isShowCertificatePopup', false)
    },
    // 证书详情
    openDetailsDialog(row) {
      // -3生成失败，-2-待生成, -1 - 生成中 ,0-待领取，1-已经领取
      if (row.certificateStatus < 0) return
      certificateView(row.certificateBatchNo)
    },
    handleCertificateStatus(row) {
      let labels = ['生效中', '生效中', '过期', '回收']
      if ([0, 1, 2, 3].includes(row.certificateStatus)) {
        return labels[row.certificateStatus]
      }
      if (row.certificateStatus === -1) {
        return labels[0]
      } else if (row.certificateStatus === -2) {
        return '生成中'
      } else {
        return '生成失败'
      }
    },
    handleCertificateStatusType(row) {
      let types = ['success', 'warning', 'info', 'danger']
      switch (row.certificateStatus) {
        case -3:
          return types[3]
        case -2:
          return ''
          break
        case -1:
          return types[0]
          break
        case 0:
          return types[0]
          break
        case 1:
          return types[0]
          break
        case 2:
          return types[1]
          break
        case 3:
          return types[2]
          break
      }
    },
    handleCertificateStatusText(row) {
      if (row.certificateStatus === -3) return '生成失败'
      else if (row.certificateStatus === -2) return '证书生成中'
      else return '查看详情'
    }
  }
}
</script>

<style lang="less" scoped>
:deep(.certificate-list-popup) {
  .el-dialog__body {
    padding: 24px 32px;
  }
  .l-r {
    display: flex;
    align-items: center;
    padding: 16px;
    margin-bottom: 24px;
    background: #FCFCFC;
    & > p {
      color: #333;
      margin-right: 60px;
      & > span {
        color: #666;
      }
    }
  }
  .certificate-list {
    min-height: 452px;
    padding: 0 38px;
    max-height: 500px;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    overflow: auto;
    .card {
      width: 364px;
      height: 452px;
      border-radius: 8px;
      background: linear-gradient(180deg, #f9f9f9ff 0%, #f8f8f8ff 0%, #f9f9f9ff 100%);
      padding: 32px 62px;
      margin-bottom: 20px;
      .pic {
        width: 240px;
        height: 240px;
        position: relative;
        & > img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        & > .look {
          width: 93px;
          height: 24px;
          background: rgba(0, 0, 0, 0.72);
          border-radius: 63px;
          color: #fff;
          font-size: 12px;
          display: none;
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          margin: auto;
          cursor: pointer;
          & > img {
            width: 16px;
            height: 16px;
            margin-right: 5px;
          }
        }
        &:hover {
          & > .look {
            display: flex;
            justify-content: center;
            align-items: center;
          }
        }
      }
      .info {
        & > p {
          margin: 10px 0;
          line-height: 22px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          color: rgba(0, 0, 0, 0.9);
          & > label {
            display: inline-block;
            width: 70px;
            color: rgba(0, 0, 0, 0.6);
            margin-right: 8px;
          }
        }
        .name {
          color: #333;
          font-size: 14px;
          font-weight: 600;
          margin-top: 24px;
          line-height: 22px;
        }
        .el-tag--mini {
          width: 44px;
          height: 18px;
          padding: 0 4px;
          line-height: 18px;
          color: #fff;
          border-radius: 2px;
          background: rgb(204, 242, 226);
          color: rgb(0, 179, 104);
          font-size: 12px;
        }
      }
    }
  }
}
</style>
