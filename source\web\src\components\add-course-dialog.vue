<template>
    <el-Dialog
      :visible.sync="visible"
      width="1200px"
      top="50px"
      custom-class="add-course-dialog dialog-center"
      append-to-body
      :show-close="false"
      :close-on-click-modal="false"
      :before-close="close"
      v-loading="is_loaded"
    >
      <iframe
        id="addCourseiframe"
        style="height: 800px; width: 100%"
        :src="iframeSrc"
        frameborder="0"
      ></iframe>
    </el-Dialog>
</template>

<script>
import { mapState } from 'vuex'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    itemData: {}
  },
  data() {
    return {
      iframeSrc: window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/courselist/add` : `http:${process.env.VUE_APP_PORTAL_HOST}/courselist/add`,
      targetHost: window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}` : `http:${process.env.VUE_APP_PORTAL_HOST}`,
      is_loaded: true
    }
  },
  watch: {
    visible(newV) {
      if (newV) {
        // 添加message事件监听
        window.addEventListener('message', this.handleMessage, false)
        this.sendMessage()
      }
    }
  },
  computed: {
    ...mapState(['moocLang'])
  },
  beforeDestroy() {
    // 移除监听
    window.removeEventListener('message', this.handleMessage)
    this.$emit('update:visible', false)
  },
  methods: {
    handleMessage(event) {
      if (event.data === 'close') {
        this.$emit('update:visible', false)
      } else if (event.data === 'comfirm') {
        this.$message.success(this.$langue('Mooc_TaskDetail_AddSucessed', { defaultText: '添加成功' }))
        this.$emit('update:visible', false)
        this.$emit('addedHandle')
      }
    },
    sendMessage() {
      const params = this.itemData
      params.moocLang = this.moocLang
      this.is_loaded = false
      let _this = this
      this.$nextTick(() => {
        const iframe = document.getElementById('addCourseiframe')
        iframe.onload = () => {
          iframe.contentWindow.postMessage(
            params,
            _this.targetHost
          )
        }
      })
    },
    // 过滤html标签
    handleLabel(str) {
      var msg = str.replace(/<[^>]+>|&[^>]+;/g, '')
      msg = msg.replace(/[|]*\n/, '')
      msg = msg.replace(/&npsp;/ig, '')
      return msg
    },
    close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="less" scoped>
:deep(.add-course-dialog){
  position: absolute !important;
  margin: 0 !important;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body{
    padding: 0;
    height: 800px;
  }
}
</style>
