<template>
  <div class="floating-button-bar" :class="{'float': float}" v-if="config.length">
    <div class="left-bar">
      <convention-confirm v-model="isChooseConvention" v-if="conventionShow"/>
    </div>
    <div class="right-bar">
      <template v-for="(item, index) in config">
        <template v-if="item.type === 'submit' && item.show">
          <el-tooltip v-if="!isChooseConvention && conventionShow" effect="dark" content="如您已阅读并同意《腾讯学堂学习平台文明公约》，请勾选左侧勾选项后，再点此按钮发布活动" placement="top" :key="'tooltip_' + index">
            <el-button class="btn-box" type="primary" size="small" :disabled="item.disable" @click="handleClick(item)">{{ item.text }}</el-button>
          </el-tooltip>
          <el-button v-else class="btn-box" type="primary" size="small" :disabled="item.disable" :key="'primary_' + index" @click="handleClick(item)">{{ item.text }}</el-button>
        </template>
        <el-button v-else-if="item.btnType === 'default' && item.show" class="btn-box default-btn" :class="{'disable-btn': item.disable}" size="small" :disabled="item.disable" :key="'default_' + index" @click="handleClick(item)">{{ item.text }}</el-button>
        <el-button v-else-if="item.btnType === 'primary' && item.show" class="btn-box" type="primary" size="small" :disabled="item.disable" :key="'primary_' + index" @click="handleClick(item)">{{ item.text }}</el-button>
      </template>
    </div>
  </div>
</template>

<script>
import conventionConfirm from '@/views/components/convention-confirm.vue'

export default {
  name: '',
  components: {
    conventionConfirm
  },
  props: {
    float: {
      type: Boolean,
      default: false
    },
    config: {
      type: Array,
      default: () => []
    },
    conventionShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isChooseConvention: false
    }
  },
  watch: {
    isChooseConvention: {
      handler(val) {
        this.$emit('handleConventionClick', val)
      },
      immediate: true
    }
  },
  computed: {},
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {
    handleClick(item) {
      this.$emit('handleClick', {
        type: item.type,
        text: item.text
      })
    }
  }
}
</script>

<style lang="less" scoped>
  .floating-button-bar {
    width: 100%;
    height: 56px;
    padding: 12px 20px;
    background-color: #fff;
    display: flex;
    align-items: center;
    .left-bar {
      margin-right: 24px;
    }
    .right-bar {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
    .btn-box {
      min-width: 80px;
      font-size: 14px;
    }
    .default-btn {
      color: #000000e6;
    }
    .disable-btn {
      background-color: #e7e7e7;
      color: #666;
    }
  }
  .float {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 999;
  }
</style>
