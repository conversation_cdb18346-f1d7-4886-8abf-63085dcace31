<template>
  <div class="prize-item" @click="goDetails">
    <img class="goods-img" v-if="data.goods_type === -1" src="../../../../../assets/img/mobile/lottery/not-winning-the-lottery.png" alt="" srcset="">
    <van-image v-else class="goods-img" width="80" height="80" :src="data.img_url" />
    <div class="goods-info">
      <div class="goods-title van-multi-ellipsis--l2">{{ data.order_name }}</div>
      <div class="column">
        <div class="time">{{ data.redeem_time }}</div>
        <div v-if="data.goods_type !== -1" :class="['status', {'success': data.order_status === 2, 'fail': data.order_status === 1}]">{{ orderStatus[data.order_status] }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { 
  Image as VanImage
} from 'vant'

export default {
  props: {
    data: {
      type: Object,
      default: () => {},
      require: true
    }
  },
  components: {
    VanImage
  },
  data() {
    return {
      orderStatus: ['', '未发放', '已发放']
    }
  },
  mounted() {
  },
  methods: {
    goDetails() {
      console.log('点击1')
      this.$emit('goDetails')
    }
  },
  beforeDestroy() {
    console.log('销毁')
  }
}
</script>

<style lang='less' scoped>
  .prize-item {
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
    .goods-img {
      width: 80px;
      height: 80px;
      flex-shrink: 0;
      border-radius: 6px;
      margin-right: 12px;
    }
    .goods-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      .goods-title {
        color: #000000e6;
        font-size: 14px;
        line-height: 22px;
        flex-shrink: 0;
        margin-bottom: 12px;
      }
      .column {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
        .time {
          color: #00000066;
          font-size: 12px;
          line-height: 16px;
        }
        .status {
          height: 24px;
          padding: 2px 8px;
          text-align: center;
          font-size: 12px;
          line-height: 20px;
          justify-content: center;
          align-items: center;
          gap: 8px;
          border-radius: 3px;
        }
        .success {
          color: #2ba471;
          background: #E3F9E9;
        }
        .fail {
          color: #e37318;
          background: #FFF1E9;
        }
      }
    }
  }
</style>
