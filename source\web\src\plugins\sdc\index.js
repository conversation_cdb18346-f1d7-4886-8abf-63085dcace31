import {
  Layout,
  Content,
  Modal,
  Loading,
  ExceptionPage,
  Link
} from 'sdc-webui'
import locale from 'sdc-webui/lib/locale'
import zh from 'sdc-webui/lib/locale/lang/zh-CN'
import en from 'sdc-webui/lib/locale/lang/en-US'
export default {
  components: [
    Layout,
    Content,
    Modal,
    ExceptionPage,
    Link
  ],
  install(Vue) {
    Vue.prototype.$sdc = {}
    Vue.prototype.$sdc.loading = Loading
  },
  locale,
  langs: {
    zh,
    en
  }
}
