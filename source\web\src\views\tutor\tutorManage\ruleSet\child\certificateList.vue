<template>
  <el-dialog title="选择证书" :visible="visible" width="1000px" :close-on-click-modal="false" class="certificateDialog"
    :before-close="closeDialog">
    <el-form :model="form" :inline="true" @submit.native.prevent>
      <el-form-item label="名称">
        <el-input v-model="form.certificate_name" style="width: 200px" placeholder="请输入证书名称" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch()" size="small">搜索</el-button>
      </el-form-item>
    </el-form>
    <el-table max-height="551px" :data="tableData.records" header-row-class-name="table-header-style"
      row-class-name="table-row-style">
      <el-table-column width="55">
        <template slot-scope="scope">
          <el-radio v-model="tableRadio" :label="scope.row.certificateId" @change="handleTableRadio(scope.row)">{{ ''
          }}</el-radio>
        </template>
      </el-table-column>
      <el-table-column property="certificateName" label="证书名称" show-overflow-tooltip></el-table-column>
      <el-table-column property="certificateId" label="证书ID"></el-table-column>
      <el-table-column property="creatorName" label="创建人"></el-table-column>
      <el-table-column property="createdAt" label="创建时间"></el-table-column>
    </el-table>
    <el-pagination v-if="tableData.total" @size-change="handleSizeChange" @current-change="handleCurrentChange"
      :current-page="current" :page-sizes="[5, 10, 20, 30, 50, 100]" :page-size="size"
      layout="total,  prev, pager, next, sizes, jumper" :total="tableData.total">
    </el-pagination>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog" size="small">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" size="small">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import pager from '@/mixins/pager'
import { getCertificateList } from '../../../api/tutor.api.conf'
export default {
  mixins: [pager],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    cerInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      tableData: {
        records: [],
        total: 0
      },
      tableRadio: '',
      selectRow: null,
      form: {
        certificate_name: ''
      }
    }
  },
  watch: {
    cerInfo: {
      deep: true,
      immediate: true,
      handler(val) {
        if (val.certification_id) {
          this.tableRadio = val.certification_id
        }
      }
    }
  },
  created() {
    this.onSearch()
  },
  methods: {
    handleSearch() {
      this.current = 1
      this.size = 10
      this.onSearch(1)
    },
    // 获取证书列表
    onSearch(page_no = 1) {
      getCertificateList({
        page_no,
        page_size: this.size,
        certificate_name: this.form.certificate_name
      }).then((res) => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    // 确定
    handleConfirm() {
      this.closeDialog()
      this.$emit('confirmCerificateList', this.selectRow)
    },
    handleTableRadio(row) {
      this.tableRadio = row.certificateId
      this.selectRow = row
    },
    closeDialog() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.certificateDialog {
  :deep(.el-pagination) {
    .el-input__inner {
      padding: unset
    }
  }

  :deep(.el-radio__original) {
    display: none !important;
  }

  ::v-deep .el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
    box-shadow: none !important;
  }
}
</style>
