<template>
  <div class="des-main-container">
    <div class="des-main">
      <div class="title-box">
        <span class="title-left">
          <span class="tag">{{ titleTagName(courseData.course_type) }}</span>
          <span class="title">{{ courseData.course_name }}</span>
        </span>
        <span
          :class="[{ 'active-expand-des': showDesExpand }, 'expand-des-icon']"
          @click="showDesContent"
        ></span>
      </div>
      <div class="title-icon" v-if="!isPreview">
        <span class="item-icon"
          ><span class="play-i"></span>{{ courseData.view_count }}</span
        >
        <span class="item-name">{{ teacher_name }}</span>
        <span class="created-time">{{ courseData.created_at || `${$langue('NetCourse_CreateTime', { defaultText: '创建时间' })}：--` }}</span>
      </div>
      <!-- 简介 -->
      <div v-if="showDesExpand">
        <div v-if="showDes" class="des-content" @click="showDes = !showDes" v-html="courseData.course_desc"></div>
        <div
          v-else
          class="all-des-content"
          @click="showDes = !showDes"
          v-html="courseData.course_desc"
        ></div>
        <!-- 标签展示 -->
        <template v-if="courseData.net_course_id">
          <sdc-label-show-mob v-if="isPreview" class="mgt-10" ref="labelShow" :labelNodeEnv="labelNodeEnv" :actType="2" :courseId="course_id" :isPreview="true" :previewLbael="previewLbael"></sdc-label-show-mob>
          <sdc-label-show-mob v-else class="mgt-10" ref="labelShow" :labelNodeEnv="labelNodeEnv" :actType="2" :courseId="course_id" :isH5="false" :isMock="false" @toSearchPage="toSearchPage"></sdc-label-show-mob>
        </template>
        <!-- <div :class="['tag-list-box', 'clearfix', {'tag-height': showExpand}]">
          <span
            v-for="item in tagList"
            :key="item.label_id"
            :class="[
              { 'hot-tag': item.label_hot_enable === 1 },
              { 'official-tag': item.label_type === 1 },
              'item-tag'
            ]"
          >
            <span
              :class="[
                { 'hot-tag-i': item.label_hot_enable === 1 },
                { 'official-tag-i': item.label_type === 1 }
              ]"
            ></span>
            <span class="tag-label">{{ item.label_name }}</span>
          </span>
        </div> -->
        <div
          class="expand-and-open"
          v-show="isOverflow"
          @click="expandTag"
        >
          {{ showExpand ? $langue('NetCourse_Expand', { defaultText: '展开' }) : $langue('NetCourse_Retract', { defaultText: '收起' }) }}
          <span :class="[{ 'packUp-icon': !showExpand }, 'expand-icon']"></span>
        </div>
      </div>
      <div class="person-info-box" v-if="!isPreview">
        <div class="item-info" @click="handleLike" :dt-eid="dtdianzan('eid')" :dt-remark="dtdianzan('remark')">
          <span
            :class="[activePrise ? 'active-like' : 'like-i-name', 'icon']"
          ></span>
          <div :class="[{ 'active-label': activePrise }, 'label']">
            {{ courseData.praise_count || 0 }}
          </div>
        </div>
        <div class="item-info" @click="handleFavorited" :dt-eid='dtCollect("eid")' :dt-remark="dtCollect('remark')">
          <span
            :class="[activefav ? 'active-start' : 'start-i-name', 'icon']"
          ></span>
          <div :class="[{ 'active-label': activefav }, 'label']">
            {{ courseData.fav_count || 0 }}
          </div>
        </div>
        <div class="item-info" @click="toComment" :dt-eid='dtComments("eid")' :dt-remark="dtComments('remark')">
          <span class="chart-i-name icon"></span>
          <div class="label">{{ courseData.recomm_count || 0 }}</div>
        </div>
        <div class="item-info" @click="showAddCourse = true">
          <span class="add-i-name icon"></span>
          <div class="label">{{ $langue('Article_AddCourseList', { defaultText: '添加到课单' }) }}</div>
        </div>
        <!-- <div class="item-info">
          <span class="share-i-name icon"></span>
          <div class="label">分享</div>
        </div> -->
      </div>
    </div>
    <div class="card-list">
      <CategoryCard
        class="content-list-box"
        v-for="(item, index) in recommendList"
        :detailsInfo="item"
        :key="index"
      />
    </div>
    <addCourse :show.sync="showAddCourse" :courseData="courseData"></addCourse>
  </div>
</template>
<script>
import {
  netCheckFavorited,
  netCheckPraised,
  netDeletePraise,
  netAddPraise,
  netDeleteFavorite,
  netAddFavorite,
  getRecommendList,
  isSysBusy
} from 'config/api.conf'
import addCourse from './child/addCourse.vue'
import CategoryCard from '../components/CategoryCard.vue'
import { Toast } from 'vant'
export default {
  components: {
    addCourse,
    CategoryCard
  },
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    },
    isPreview: { // 是否是预览
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      labelNodeEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test',
      showAddCourse: false,
      recommendList: [],
      activePrise: false,
      activefav: false,
      tagList: [],
      showExpand: false,
      showDesExpand: true,
      showDes: true,
      isOverflow: false
    }
  },
  computed: {
    previewLbael() {
      return this.courseData.course_labels || []
    },
    teacher_name() {
      let { inner_teacher_names, out_teacher_names, creator_name } =
        this.courseData
      let name = ''
      if (inner_teacher_names?.length) {
        if (inner_teacher_names.length > 1) {
          name =
            inner_teacher_names[0].teacher_name + this.$langue('NetCourse_Teachers', { count: inner_teacher_names.length - 1, defaultText: `等${inner_teacher_names.length - 1}人` })
        } else {
          name = inner_teacher_names[0].teacher_name
        }
      }
      if (out_teacher_names?.length) {
        if (out_teacher_names.length > 1) {
          name =
            out_teacher_names[0].teacher_name + this.$langue('NetCourse_Teachers', { count: inner_teacher_names.length - 1, defaultText: `等${inner_teacher_names.length - 1}人` })
        } else {
          name = out_teacher_names[0].teacher_name
        }
      }
      // name = name.slice(0, -2)
      return name || creator_name
    },
    course_id() {
      return this.$route.query.course_id || ''
    },
    titleTagName() {
      return (val) => {
        if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(val)) {
          return this.$langue('Mooc_Common_ResourceType_Video', { defaultText: '视频' })
        } else if (val === 'Audio') {
          return this.$langue('Mooc_Common_ResourceType_Audio', { defaultText: '音频' })
        } else if (val === 'Article') {
          return this.$langue('Mooc_Common_ResourceType_Article', { defaultText: '文章' })
        } else if (val === 'Doc') {
          return this.$langue('Mooc_Common_ResourceType_Doc', { defaultText: '文档' })
        } else if (val === 'Scorm') {
          return 'scorm'
        } else if (val === 'Flash') {
          return this.$langue('Mooc_Common_ResourceType_Zip', { defaultText: '压缩包' })
        }
      }
    },
    dtdianzan() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name, // 任务名称
            page_type: '网课详情页', 
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '点赞',
            terminal: 'H5'
          })
        } else {
          let { mooc_course_id, task_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_${task_id}_like`
        }
      }
    },
    dtCollect() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页',
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '收藏',
            terminal: 'H5'
          })
        } else {
          let { mooc_course_id, task_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_${task_id}_collect`
        }
      }
    },
    dtComments() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页', 
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '评论',
            terminal: 'H5'
          })
        } else {
          let { mooc_course_id, task_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_${task_id}_comments`
        }
      }
    }
  },
  watch: {
    courseData() {
      this.$nextTick(() => {
        if (this.courseData.course_labels?.length) {
          this.tagList = this.courseData.course_labels
          const tag = document.querySelector('.item-tag')
          if (!tag) return
          const el = document.querySelector('.tag-list-box')
          if (el.clientHeight > tag.clientHeight * 2) {
            this.isOverflow = true
            this.showExpand = true
          }
        }
      })
      this.courseData.created_at = this.courseData.created_at && this.courseData.created_at.slice(0, 16)
      this.getSysBusyStatus()
    }
  },
  mounted() {
    if (!this.isPreview) {
      this.handleCheckPrise()
      this.handleCheckFav()
    }
  },
  methods: {
    // 获取当前环境是不是小程序
    getMiniProgramEnv() {
      // 通过判断navigator.userAgent中包含miniProgram字样
      let userAgent = navigator.userAgent
      return /miniProgram/i.test(userAgent)
    },
    toSearchPage({ url, item }) {
      if (this.getMiniProgramEnv()) {
        let { type } = this.$route.query
        let isMookNet = type === 'taskContent' || false
        // 小程序内直接跳转
        if (!isMookNet) {
          // 如果是网络课跳转 不经过taskContent
          window.wx.miniProgram.navigateTo({
            url: url || ''
          })
          return
        }
        // 如果是mooc嵌套的网络课跳转 经过taskContent
        window.parent.postMessage({
          event: 'toSearch',
          url,
          item
        }, '*')
      } else {
        let keywords = item.label_name || ''
        window.location.href = `https://sdc.qq.com/s/yJyZMs?scheme_type=search&keywords=${keywords}`
      }
    },
    getRouterQuery() {
      let { mooc_course_id, task_id } = this.$route.query
      return {
        mooc_course_id: mooc_course_id || '',
        task_id: task_id || ''
      }
    },
    expandTag() {
      this.showExpand = !this.showExpand
    },
    // 简介展开
    showDesContent() {
      this.showDesExpand = !this.showDesExpand
    },
    getSysBusyStatus() {
      isSysBusy().then(res => {
        if (res !== '1') {
          this.getConRecommendList()
        }
      })
    },
    getConRecommendList() {
      // 当天创建的数据凌晨12点之前，不请求数据
      let curDateLastTime = new Date().setHours(0, 0, 0, 0)
      let createTimeS = this.courseData?.created_at
        ? `${this.courseData.created_at.split(' ')[0]} 23:59:59`
        : ''
      const createTime = new Date(createTimeS).getTime()
      if (curDateLastTime > createTime) {
        const params = {
          module_id: 1,
          item_id: this.course_id
        }
        getRecommendList(params).then((data) => {
          this.recommendList = (data || []).map((e) => {
            return {
              ...e,
              content_name: e.title,
              content_id: e.item_id,
              description: e.brief,
              play_total_count: e.view_count,
              praise_count: e.origin_data.avg_score,
              created_time: e.created_at.split(' ')[0],
              duration: e.origin_data.est_dur,
              photo_url: e.thumbnail_url
            }
          })
          if (this.recommendList.length > 5) {
            this.recommendList = this.recommendList.splice(0, 5)
          }
        })
      }
    },
    handleCheckPrise() {
      const params = { net_course_id: this.course_id }
      netCheckPraised(params).then((res) => {
        this.activePrise = res
      })
    },
    handleCheckFav() {
      const params = { net_course_id: this.course_id }
      netCheckFavorited(params).then((res) => {
        this.activefav = res
      })
    },
    // 点赞
    handleLike() {
      let praiseCommonAPI = null
      const params = { net_course_id: this.course_id }
      let tip = null
      if (this.activePrise) {
        praiseCommonAPI = netDeletePraise
        this.courseData.praise_count--
        tip = this.$langue('Mooc_Common_Alert_CancelPraiseSucessed', { defaultText: '取消点赞成功' })
      } else {
        praiseCommonAPI = netAddPraise
        this.courseData.praise_count++
        tip = this.$langue('Mooc_Common_Alert_PraiseSucessed', { defaultText: '点赞成功' })
      }
      this.$emit('update: courseData', this.courseData)
      praiseCommonAPI(params).then((data) => {
        this.handleCheckPrise()
        if (data.credit && data.credit !== '0') {
          Toast(`${tip}, ${this.$langue('Mooc_Common_Alert_CommonPoint', { point: +data.credit, defaultText: `通用积分+${+data.credit}` })}`)
        } else {
          Toast(`${tip}`)
        }
      })
    },
    // 收藏
    handleFavorited() {
      const params = { net_course_id: this.course_id }
      let praiseCommonAPI = null
      let tip = null
      if (this.activefav) {
        praiseCommonAPI = netDeleteFavorite
        this.courseData.fav_count--
        tip = this.$langue('Mooc_Common_Alert_CancelCollectSucessed', { defaultText: '取消收藏成功' })
      } else {
        praiseCommonAPI = netAddFavorite
        this.courseData.fav_count++
        tip = this.$langue('Mooc_Common_Alert_CollectSucessed', { defaultText: '收藏成功' })
      }
      this.$emit('update: courseData', this.courseData)
      praiseCommonAPI(params).then((data) => {
        this.handleCheckFav()
        if (data.credit && data.credit !== '0') {
          Toast(`${tip}, ${this.$langue('Mooc_Common_Alert_CommonPoint', { point: +data.credit, defaultText: `通用积分+${+data.credit}` })}`)
        } else {
          Toast(`${tip}`)
        }
      })
    },
    toComment() {
      this.$emit('toComment')
    },
    // 标签跳转
    tagToPath(val) {
      let href = location.hostname.endsWith('.woa.com')
        ? process.env.VUE_APP_V8_HOST_WOA
        : process.env.VUE_APP_V8_HOST
      href = `${href}/mat/user/search?keywords=${val}&from_page=ql新首页&type=label`
      window.open(href)
    }
  }
}
</script>
<style lang="less" scoped>
.des-main-container {
  height: 100%;
  overflow-y: auto;
}
.des-main {
  background-color: #fff;
  padding: 16px;
  font-size: 12px;
  //清除浮动
  .clearfix:after {
    content: '';
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  .title-box {
    display: flex;
    align-items: flex-start;
    .title-left {
      display: inline-block;
      width: 310px;
    }
    .tag {
      display: inline-block;
      text-align: center;
      font-size: 12px;
      width: 50px;
      line-height: 16px;
      border-radius: 2px;
      border: 1px solid #0052d9ff;
      background: #ecf2feff;
      color: #0052d9;
      margin-right: 10px;
    }
    .title {
      font-size: 16px;
      font-weight: bold;
      line-height: 24px;
      word-break: break-all;
    }
    .expand-des-icon {
      background: url('~@/assets/img/mobile/arrow-down.png') no-repeat
        center/cover;
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-left: 16px;
      margin-top: 3px;
      transition: all 0.1s;
      transform: rotate(0deg);
    }
    .active-expand-des {
      transform: rotate(180deg);
    }
  }
  .title-icon {
    display: flex;
    align-items: center;
    line-height: 20px;
    margin-top: 8px;
    color: #00000066;
    .item-icon {
      display: flex;
      align-items: center;
      width: 56px;
    }
    .item-name {
      flex: 1;
      margin-right: 16px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .created-time {
      width: 110px;
    }
    .play-i {
      background: url('~@/assets/img/mobile/play.png') no-repeat center/cover;
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-right: 4px;
    }
  }
  .des-content {
    max-height: 44px;
    line-height: 20px;
    color: #00000066;
    margin-top: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    word-break: break-all;
    /*! autoprefixer: ignore next */
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    /* autoprefixer: on */
  }
  .all-des-content {
    line-height: 20px;
    color: #00000066;
    margin-top: 8px;
  }
  .mgt-10 {
    margin-top: 10px;
  }
  .tag-list-box {
    margin-top: 12px;
    .official-tag {
      font-size: 14px;
    }
    .item-tag {
      height: 24px;
      line-height: 24px;
      text-align: center;
      border-radius: 22px;
      opacity: 1;
      font-size: 10px;
      padding: 0px 12px;
      color: #00000099;
      background: #f7f8faff;
      float: left;
      margin-right: 8px;
      margin-bottom: 8px;
      .tag-label {
        line-height: 16px;
        height: 16px;
        max-width: 132px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    .official-tag {
      background: #ecf2feff;
      color: #0052d9ff;
      &-i {
        background: url('~@/assets/img/mobile/official-tag.png') no-repeat;
        background-size: 14px;
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
        position: relative;
        top: 3px;
      }
    }
    .hot-tag {
      background: #fbf2e8;
      color: #ff5923ff;
      &-i {
        background: url('~@/assets/img/mobile/hot-tag.png') no-repeat;
        background-size: 14px;
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
        position: relative;
        top: 3px;
      }
    }
  }
  .expand-and-open {
    text-align: center;
    color: #00000099;
    font-size: 12px;
    line-height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    // margin-top: 8px;
    .expand-icon {
      background: url('~@/assets/img/mobile/arrow-down.png') no-repeat
        center/cover;
      height: 16px;
      width: 16px;
      display: inline-block;
      transform: rotate(0deg);
      transition: all 0.3s;
      margin-top: 2px;
    }
    .packUp-icon {
      transform: rotate(180deg);
    }
  }
  .tag-height {
    height: 32px;
    overflow: hidden;
  }
  .person-info-box {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-top: 20px;
    font-size: 10px;
    .item-info {
      text-align: center;
    }
    .item-info + .item-info {
      margin-left: 32px;
    }
    .icon {
      display: inline-block;
      width: 24px;
      height: 24px;
    }
    .label {
      line-height: 16px;
      margin-top: 2px;
    }
    .active-label {
      color: #0052d9ff;
    }
    .like-i-name {
      background: url('~@/assets/img/mobile/like.png') no-repeat center/cover;
    }
    .active-like {
      background: url('~@/assets/img/mobile/like-active.png') no-repeat
        center/cover;
    }
    .start-i-name {
      background: url('~@/assets/img/mobile/start.png') no-repeat center/cover;
    }
    .active-start {
      background: url('~@/assets/img/mobile/start-active.png') no-repeat
        center/cover;
    }
    .chart-i-name {
      background: url('~@/assets/img/mobile/chart.png') no-repeat center/cover;
    }
    .add-i-name {
      background: url('~@/assets/img/mobile/add.png') no-repeat center/cover;
    }
    .share-i-name {
      background: url('~@/assets/img/mobile/share.png') no-repeat center/cover;
    }
  }
}
.card-list {
  margin-top: 8px;
  padding: 0px 16px;
  background: #fff;
  .content-list-box {
    border-bottom: 0.5px solid #e7e7e7;
  }
}
</style>
