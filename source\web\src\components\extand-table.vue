<template>
  <div class="extand-box">
    <div class="extand-title">
      <span class="title">延伸学习</span>
      <p class="tip">此处关联的内容将在图文详情页中展示</p> <span v-if="!approveStatus" class="addExtandItem" @click="showAddExtandLearn = true">+ 新增内容</span>
    </div>
    <div class="extand-table">
      <el-table :data="tableData.list" max-height="216px" style="width: 100%">
        <el-table-column prop="content_name" label="内容标题" width="306" show-overflow-tooltip>
          <template slot-scope="scope">
            <span
              :class="['tags', getModuleClass(scope.row.content_module_id)]">{{ scope.row.content_module_name }}</span><span>{{ scope.row.content_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" v-if="!approveStatus">
          <template slot-scope="scope">
            <div class="icon-btns">
              <i class="icon-up" @click="handleUp(scope.row, scope.$index)"></i>
              <i class="icon-delete" @click="handleDelete(scope.row, scope.$index)"></i>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!-- <div class="pagination-box">
                <el-pagination
                    :small="true"
                    layout="total, prev, pager, next"
                    @size-change="handleSizeChange"
                    @current-change="handlePageChange"
                    :current-page.sync="tableData.page_no"
                    :page-size="tableData.page_size"
                    :total="tableData.total">
                </el-pagination>
            </div> -->
    </div>
    <AddExtandLearnDialog v-if="showAddExtandLearn" :prodData.sync="prodData" :page="page"
      @closeAddExDialog="showAddExtandLearn = false" ref="extandLearn" />
  </div>
</template>

<script>
import { AddExtandLearnDialog } from './index'
import { qlearningModuleTypes } from 'utils/constant'
export default {
  components: {
    AddExtandLearnDialog
  },
  data() {
    return {
      tableData: {
        list: JSON.parse(sessionStorage.getItem('extend_contents')) || [],
        page_no: 1,
        page_size: 6,
        total: 0
      },
      showAddExtandLearn: false
    }
  },
  props: {
    formData: {
      type: Object
    },
    page: {
      type: String,
      default: ''
    },
    prodData: {
      type: Object
    },
    approveStatus: { // 是否是审批状态
      type: Boolean,
      default: false
    }
  },
  mounted() {

  },
  methods: {
    initData() {
      this.tableData.list = JSON.parse(sessionStorage.getItem('extend_contents')) || []
    },
    handleUp(row, index) {
      this.tableData.list.unshift(this.tableData.list.splice(index, 1)[0])
      sessionStorage.setItem('extend_contents', JSON.stringify(this.tableData.list))
    },
    handleDelete(row, index) {
      this.tableData.list.splice(index, 1)
      sessionStorage.setItem('extend_contents', JSON.stringify(this.tableData.list))
    },
    getModuleClass(module_id) {
      let cardType = qlearningModuleTypes.find((item) => module_id === item.module_id)
      if (cardType) {
        return cardType.moduleClassName
      }
    }
  }
}
</script>

<style lang="less" scoped>
.extand-box {
  .extand-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 16px;
    margin-bottom: 8px;

    .title {
      margin-left: 20px;
    }

    .tip {
      color: rgba(0, 0, 0, 0.4);
      font-size: 12px;
      margin-left: -40px;
    }

    .addExtandItem {
      color: #0052D9;
      font-size: 12px;
      cursor: pointer;
    }
  }

  .extand-table {
    padding: 0 16px;

    :deep(.el-table) {
      th {
        background: rgba(245, 245, 245, 1);
        font-weight: 400;
        color: rgba(0, 0, 0, 0.8);
        border-bottom: solid 1px #eeeeee;
      }

      th,
      td {
        height: 36px;
        padding: unset;
      }

      td {
        color: rgba(0, 0, 0, 0.6);
      }
    }

    .icon-btns {
      i {
        font-size: 16px;
        width: 16px;
        height: 16px;
        display: inline-block;
        font-size: 16px;
        cursor: pointer;
      }

      i:first-child {
        margin-right: 8px;
      }

      .icon-up {
        background: url('../assets/img/icon-up.png') no-repeat center / cover;
      }

      .icon-delete {
        background: url('../assets/img/icon-delete.png') no-repeat center / cover;
      }
    }

    :deep(.el-table--scrollable-x .el-table__body-wrapper) {
      overflow-x: hidden;
    }

    .pagination-box {
      margin-top: 12px;
      position: relative;

      :deep(.el-pagination) {
        display: flex;
        justify-content: flex-end;

        button,
        li {
          border: none;
        }

        .active {
          border-radius: 2px;
          background-color: #F5F6F7;
          color: #3464E0;
        }

        .el-pagination__total {
          position: absolute;
          left: 0;
        }
      }
    }
  }

}</style>
