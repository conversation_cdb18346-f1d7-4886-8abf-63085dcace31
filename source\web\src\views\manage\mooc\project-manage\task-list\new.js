export const newData = {
  'task_last_submit_time': '2023-09-06 11:27:44',
  'task_last_submitor_name': 'happyhu',
  'task_tree': [
    {
      'task_id': 165685,
      'mooc_course_id': 'UuHTwMAO',
      'task_name': '阶段一',
      'task_name_en': null,
      'resource_type': null,
      'resource_type_name': null,
      'task_type': 'stage',
      'task_type_name': '阶段',
      'act_id': null,
      'act_type': null,
      'act_name': null,
      'resource_from': null,
      'duration': null,
      'word_number': null,
      'task_desc': null,
      'task_desc_en': null,
      'content_id': null,
      'resource_url': null,
      'mobile_resource_type': null,
      'wechat_mini_appid': null,
      'resource_url_mobile': null,
      'cover_url': null,
      'extensions': null,
      'finished_condition': null,
      'finished_condition_desc': null,
      'third_party_interface': null,
      'task_status': 1,
      'required': null,
      'lock_status': null,
      'unlock_time': null,
      'pid': 0,
      'full_path': null,
      'order_no': 1,
      'total_students_count': null,
      'finished_count': 0,
      'resource_content_type': null,
      'support_pc': null,
      'support_mobile': null,
      'exam_time_type': null,
      'exam_start_time': null,
      'exam_end_time': null,
      'sub_tasks': [
        {
          'task_id': 165909,
          'mooc_course_id': 'UuHTwMAO',
          'task_name': '反垄断无小事，关系你我他',
          'task_name_en': null,
          'resource_type': 'Video',
          'resource_type_name': '视频',
          'task_type': 'task',
          'task_type_name': '任务',
          'act_id': '19105',
          'act_type': '2',
          'act_name': '反垄断无小事，关系你我他',
          'resource_from': null,
          'duration': 15,
          'word_number': null,
          'task_desc': null,
          'task_desc_en': null,
          'content_id': null,
          'resource_url': 'https://ihr.tencent.com/5FD89y/19105',
          'mobile_resource_type': null,
          'wechat_mini_appid': null,
          'resource_url_mobile': null,
          'cover_url': null,
          'extensions': null,
          'finished_condition': {
            'type': '1',
            'condition': 0
          },
          'finished_condition_desc': null,
          'third_party_interface': null,
          'task_status': 1,
          'required': true,
          'lock_status': 1,
          'unlock_time': null,
          'pid': 165685,
          'full_path': null,
          'order_no': 2,
          'total_students_count': null,
          'finished_count': 0,
          'resource_content_type': 'PGC',
          'support_pc': null,
          'support_mobile': true,
          'exam_time_type': null,
          'exam_start_time': null,
          'exam_end_time': null,
          'sub_tasks': null
        },
        {
          'task_id': 165911,
          'mooc_course_id': 'UuHTwMAO',
          'task_name': '研发增效改进中的管理思考 | 开聊啦.pdf',
          'task_name_en': null,
          'resource_type': 'Doc',
          'resource_type_name': '文档',
          'task_type': 'task',
          'task_type_name': '任务',
          'act_id': '248',
          'act_type': '10',
          'act_name': '研发增效改进中的管理思考 | 开聊啦.pdf',
          'resource_from': null,
          'duration': null,
          'word_number': null,
          'task_desc': null,
          'task_desc_en': null,
          'content_id': null,
          'resource_url': 'https://learn.woa.com/user/profile?wordId=248',
          'mobile_resource_type': null,
          'wechat_mini_appid': null,
          'resource_url_mobile': null,
          'cover_url': null,
          'extensions': null,
          'finished_condition': {
            'type': '2',
            'condition': 1
          },
          'finished_condition_desc': null,
          'third_party_interface': null,
          'task_status': 1,
          'required': true,
          'lock_status': 1,
          'unlock_time': null,
          'pid': 165685,
          'full_path': null,
          'order_no': 3,
          'total_students_count': null,
          'finished_count': 0,
          'resource_content_type': '',
          'support_pc': null,
          'support_mobile': null,
          'exam_time_type': null,
          'exam_start_time': null,
          'exam_end_time': null,
          'sub_tasks': null
        },
        {
          'task_id': 165925,
          'mooc_course_id': 'UuHTwMAO',
          'task_name': '割发代首',
          'task_name_en': null,
          'resource_type': 'Practice',
          'resource_type_name': '练习',
          'task_type': 'task',
          'task_type_name': '任务',
          'act_id': 'UGG5Goa9',
          'act_type': '20',
          'act_name': '割发代首',
          'resource_from': null,
          'duration': 1,
          'word_number': null,
          'task_desc': null,
          'task_desc_en': null,
          'content_id': null,
          'resource_url': null,
          'mobile_resource_type': null,
          'wechat_mini_appid': null,
          'resource_url_mobile': null,
          'cover_url': null,
          'extensions': null,
          'finished_condition': null,
          'finished_condition_desc': null,
          'third_party_interface': null,
          'task_status': 1,
          'required': true,
          'lock_status': 1,
          'unlock_time': null,
          'pid': 165685,
          'full_path': null,
          'order_no': 4,
          'total_students_count': null,
          'finished_count': 0,
          'resource_content_type': null,
          'support_pc': null,
          'support_mobile': null,
          'exam_time_type': 1,
          'exam_start_time': null,
          'exam_end_time': null,
          'sub_tasks': null
        }
      ]
    },
    {
      'task_id': 165917,
      'mooc_course_id': 'UuHTwMAO',
      'task_name': '阶段二',
      'task_name_en': null,
      'resource_type': null,
      'resource_type_name': null,
      'task_type': 'stage',
      'task_type_name': '阶段',
      'act_id': null,
      'act_type': null,
      'act_name': null,
      'resource_from': null,
      'duration': null,
      'word_number': null,
      'task_desc': null,
      'task_desc_en': null,
      'content_id': null,
      'resource_url': null,
      'mobile_resource_type': null,
      'wechat_mini_appid': null,
      'resource_url_mobile': null,
      'cover_url': null,
      'extensions': null,
      'finished_condition': null,
      'finished_condition_desc': null,
      'third_party_interface': null,
      'task_status': 1,
      'required': null,
      'lock_status': null,
      'unlock_time': null,
      'pid': 0,
      'full_path': null,
      'order_no': 5,
      'total_students_count': null,
      'finished_count': 0,
      'resource_content_type': null,
      'support_pc': null,
      'support_mobile': null,
      'exam_time_type': null,
      'exam_start_time': null,
      'exam_end_time': null,
      'sub_tasks': [
        {
          'task_id': 165919,
          'mooc_course_id': 'UuHTwMAO',
          'task_name': '大山里最亮的星，请照亮他们前行',
          'task_name_en': null,
          'resource_type': 'Article',
          'resource_type_name': '文章',
          'task_type': 'task',
          'task_type_name': '任务',
          'act_id': '35425',
          'act_type': '18',
          'act_name': '大山里最亮的星，请照亮他们前行',
          'resource_from': null,
          'duration': null,
          'word_number': 3804,
          'task_desc': null,
          'task_desc_en': null,
          'content_id': null,
          'resource_url': 'https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=35425',
          'mobile_resource_type': null,
          'wechat_mini_appid': null,
          'resource_url_mobile': null,
          'cover_url': null,
          'extensions': null,
          'finished_condition': {
            'type': '2',
            'condition': 1
          },
          'finished_condition_desc': null,
          'third_party_interface': null,
          'task_status': 1,
          'required': true,
          'lock_status': 1,
          'unlock_time': null,
          'pid': 165917,
          'full_path': null,
          'order_no': 6,
          'total_students_count': null,
          'finished_count': 0,
          'resource_content_type': '',
          'support_pc': null,
          'support_mobile': null,
          'exam_time_type': null,
          'exam_start_time': null,
          'exam_end_time': null,
          'sub_tasks': null
        },
        {
          'task_id': 165927,
          'mooc_course_id': 'UuHTwMAO',
          'task_name': '测试测试测试',
          'task_name_en': null,
          'resource_type': 'Exam',
          'resource_type_name': '考试',
          'task_type': 'task',
          'task_type_name': '任务',
          'act_id': 'j5GFAT5s',
          'act_type': '20',
          'act_name': '测试测试测试',
          'resource_from': null,
          'duration': 20,
          'word_number': null,
          'task_desc': null,
          'task_desc_en': null,
          'content_id': null,
          'resource_url': null,
          'mobile_resource_type': null,
          'wechat_mini_appid': null,
          'resource_url_mobile': null,
          'cover_url': null,
          'extensions': null,
          'finished_condition': null,
          'finished_condition_desc': null,
          'third_party_interface': null,
          'task_status': 1,
          'required': true,
          'lock_status': 1,
          'unlock_time': null,
          'pid': 165917,
          'full_path': null,
          'order_no': 7,
          'total_students_count': null,
          'finished_count': 0,
          'resource_content_type': null,
          'support_pc': null,
          'support_mobile': null,
          'exam_time_type': 2,
          'exam_start_time': '2023-05-09 00:00:00',
          'exam_end_time': '2023-06-30 00:00:00',
          'sub_tasks': null
        }
      ]
    }
  ]
}
