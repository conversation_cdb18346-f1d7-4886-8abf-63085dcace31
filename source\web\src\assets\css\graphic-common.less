// 左浮动
.f-left {
  float: left;
}

// 右浮动
.f-right {
  float: right;
}

//清楚浮动
.clearfix:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}

// 文字超出一行省略号
.overflow-l1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; // 默认不换行；
}

// 文字超出两行省略号
.overflow-l2 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden; //溢出内容隐藏
  text-overflow: ellipsis; //文本溢出部分用省略号表示
  display: -webkit-box; //特别显示模式
  -webkit-line-clamp: 2; //行数
  line-clamp: 2;
  -webkit-box-orient: vertical; //盒子中内容竖直排列
  word-break: break-all;
}

// 弹窗水平垂直居中
.dialog-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.cropper_model_dlg {
  margin-top: unset !important;
  .el-dialog__body {
    height: 550px;
  }
}
.add-course-self-dialog{
  border-radius: 6px;
  .el-dialog__header{
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
  .el-pagination{
    margin-top: 16px;
    padding: 0;
    text-align: right;
    .el-input,.el-select{
      height: 22px;
      line-height: 22px;
    }
    .el-pagination__jump{
      margin-left: 0;
    }
  }
}
.add-extand-learn-dialog{
  .el-dialog__body{
    padding: 24px 32px;
    height: 490px;
  }
  .el-input {
    height: 32px;
    .el-input__inner {
      line-height: 32px;
      height: 32px;
      padding: 6px 30px 6px 10px 
    }
    input{
      height: 100% !important;
    }
  }
  .el-select{
    height: 32px;
    line-height: 32px;
  }
  .el-pagination{
    margin-top: 16px;
    .el-pagination__editor{
      width: 60px !important;
    }
    .el-input,.el-pager li,.btn-prev,.btn-next,.el-pagination__total,.el-pagination__jump{
      height: 24px;
      line-height: 24px;
    }
  }
}
.text-blue {
  color:#0052D9;
}
.text-red {
  color:#D9001B
}
.text-orange {
  color: #E37318;
}
.text-blue,.text-red,.text-orange {
  cursor: pointer;
}


.text-warning {
  color: #F59A23;
}
.text-success {
  color: #0AD0B6;
}
.status-green{
  color: #00A870 !important;
}
.status-red{
  color: #E34D59 !important;
}
.course-box,.common-box,.extand-box,.related-content-item, .upload-table-box{
  .tags {
    margin: 0 12px 0 10px;
    display: inline-block;
    height: 18px;
    padding: 0 7px;
    font-size: 12px;
    font-weight: 400;
    line-height: 18px;
    border: 1px solid;
    border-radius: 4px;
    &.tag-net {
        color:#0b8bff;
    }
    &.tag-music {
        color:#04aef6;
    }
    &.tag-live {
        color:#fe5d34;
    }
    &.tag-article {
        color:#42c55b;
    }
    &.tag-note {
        color:#40c19d;
    }
    &.tag-exam {
        color:#ff6600;
    }
    &.tag-hangjia {
        color:#ff8900;
    }
    &.tag-face {
        color:#ffa31a;
    }
    &.tag-activity {
        color:#ffbc03;
    }
    &.tag-marker {
        color:#00a99d;
    }
    &.tag-word{
        color: #0052D9;
    }
    &.tag-link {
        color:#a65ad4;
    }
  }
}

.special-class{
  .el-input {
    height: 32px;
    .el-input__inner {
      line-height: 32px;
      height: 32px;
      padding: 6px 30px 6px 10px 
    }
    input{
      height: 100% !important;
    }
  }
  .el-select{
    height: 32px;
    line-height: 32px;
    .el-select__tags{
      .el-tag{
        height: 24px;
        display: flex;
        align-items: center;
        background: #e7e7e7;
        color: rgba(0, 0, 0, 0.9);
      }
      .el-tag__close{
        background: #e7e7e7;
        font-size: 15px;
        color: #909399;
        top: 2px;
      }
    }
  }
}