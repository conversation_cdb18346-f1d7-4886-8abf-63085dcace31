<template>
  <div :class="typeClass" class="upload-img">
    <el-upload
      ref="upLoad"
      :auto-upload="true"
      :before-upload="beforeAvatarUpload"
      :class="{hide:hideUpload}"
      :file-list="fileList"
      :http-request="upload"
      :limit="1"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove"
      accept="image/png, image/jpg, image/jpeg"
      action
      list-type="picture-card"
    >
      <div class="upload-icon">
        <i class="el-icon-upload-custom"></i>
        <p>上传图片</p>
      </div>
    </el-upload>
    <div class="prompt">
      <p>建议图片上传尺寸{{limit_width}}px * {{limit_height}}px&nbsp;&nbsp;({{proportion(limit_width,limit_height)}})</p>
      <p>图片小于500k</p>
    </div>
    <el-dialog :visible.sync="dialogVisible">
      <img :src="dialogImageUrl" alt width="100%" />
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    limit_width: {
      type: Number,
      default: 22
    },
    limit_height: {
      type: Number,
      default: 22
    },
    fileList: {
      type: Array,
      default: () => {
        return []
      }
    },
    typeClass: {
      type: String,
      default: 'icon'
    },
    file_id: {},
    loading: {
      type: Boolean,
      default: false
    },
    slotTip: {}
  },
  data() {
    return {
      dialogVisible: false,
      dialogImageUrl: '',
      limitCount: 1,
      hideUpload: false
    }
  },
  watch: {
    fileList: {
      handler(val) {
        this.hideUpload = Array.isArray(val) && val.length >= this.limitCount
      },
      immediate: true
    }
  },
  methods: {
    proportion(width, height) {
      let limit = width / height
      if (Number.isInteger(limit)) {
        return `${limit} : 1`
      } else {
        return `${Math.round(limit * 10)} : 10`
      }
    },
    // 图片上传
    upload({ file }) {
      // uploadCOS({
      //   file,
      //   onSuccess(data) {
      //     _this.$emit('update:file_id', data);
      //     _this.$emit('update:fileList', _this.$refs.upLoad.uploadFiles);
      //     _this.$message.success('图片上传成功');
      //     _this.$emit('update:loading', false);
      //   },
      //   onError(err) {
      //     _this.$message.error('上传图片失败');
      //     _this.$emit('update:fileList', []);
      //    _this.$emit('update:loading', false);
      //   },
      // });
      this.$sdc.loading('图片上传中')
      let _this = this
      /* eslint-disable*/
      let authUrl = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_PORTAL_HOST_WOA : process.env.VUE_APP_PORTAL_HOST
      new contentCenter.uploadFile({
        file: file,
        type: 0, // 0表示上传图片，1视频 2音频 3文档
        appId: 'QLearningService',
        operateAuthUrl:`${authUrl}/training-portal-common/api/v1/portal/user/common/uploadOperateSignature`,
        onSuccess(res) {
          _this.$sdc.loading.hide()
          _this.$emit('update:file_id', res[0].content_id);
          _this.$emit('update:fileList', _this.$refs.upLoad.uploadFiles);
          _this.$message.success('图片上传成功');
        },
        onError(err) {
          _this.$sdc.loading.hide()
          _this.$message.error('上传图片失败');
          _this.$emit('update:fileList', []);
        }
      })
      /* eslint-disable*/
    },
    handleRemove(file, fileList) {
      this.$emit('update:file_id', '');
      this.$emit('update:fileList', []);
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    //图片限制大小
    beforeAvatarUpload(file) {
      const isLt4M = file.size / 1024 / 1024 < 4;
      if (!isLt4M) {
        this.$message.error('上传图片大小不能超过 4MB!');
        return false;
      }
      let _this = this;
      const isSize = new Promise(function (resolve, reject) {
        let _URL = window.URL || window.webkitURL;
        let img = new Image();
        img.onload = function () {
          file.width = img.width;
          file.height = img.height;
          _this.proportion(_this.limit_width, _this.limit_height) ==
          _this.proportion(img.width, img.height)
            ? resolve()
            : reject();
        };
        img.src = _URL.createObjectURL(file);
      }).then(
        () => {
          return file;
        },
        () => {
          this.$message.warning('上传图片与建议尺寸不符，可能存在失真问题');
          return file;
        }
      );
      return isLt4M && isSize;
    },
  },
};
</script>
<style lang="less" scoped>
.upload-img {
  display: flex;
  align-items: flex-start;
  :deep(.el-upload--picture-card) {
    width: 120px;
    height: 120px;
    background-color: #FBFBF9;
    border: 1px dashed #DEDEDE;
  }
  :deep(.el-upload--picture-card:hover){
    border: 1px dashed #3464E0;
  }
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 120px;
    height: 120px;
  }
  .prompt{
    margin-left: 12px;
    font-size: 12px;
    color: rgba(0,0,0,0.4);
    p{
      height: 17px;
      line-height: 17px;
    }
    p:first-child{
      padding-top: 6px;
    }
    p:last-child{
      margin-top: 4px;
    }
  }
}
</style>
