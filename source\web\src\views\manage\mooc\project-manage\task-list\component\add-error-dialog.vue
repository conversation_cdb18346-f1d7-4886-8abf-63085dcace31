<template>
  <el-dialog
  class="error-dialog-main"
  width="560px" 
  :visible="visible" 
  title="添加失败提示" 
  :close-on-click-modal="false" 
  :before-close="cancel"
  >
    <div class="error-body">
      <div class="error-tips-box">
        <span class="error-icon"></span>
        <span>添加失败</span>
      </div>
      <div class="error-content">
        <div class="title">以下课程已经添加到该项目中，请勿重复添加：</div>
        <div class="task-name" v-for="(item, index) in taskNameList" :key="index">
          {{ item.name }}
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="cancel" type="primary">知道了</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: <PERSON>olean
    },
    taskNameList: {
      type: Array
    }
  },
  data() {
    return {
    }
  },
  methods: {
    cancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.error-dialog-main {
  .error-tips-box {
    display: flex;
    align-items: center;
    color: #000000e6;
    font-size: 16px;
    font-weight: 500;
    .error-icon {
      background: url('~@/assets/mooc-img/add-error.png') no-repeat center/cover;
      width: 36px;
      height: 36px;
      display: inline-block;
      margin-right: 8px;
    }
  }
  .error-content {
    margin-top: 16px;
    .title {
      color: #000000e6;
      font-size: 14px;
      margin-bottom: 20px;
    }
    .task-name {
      color: #000000e6;
      font-size: 14px;
      margin-bottom: 16px;
    }
    .task-name:last-of-type {
      margin-bottom: unset;
    }
  }
}
</style>
