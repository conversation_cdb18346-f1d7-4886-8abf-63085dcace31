<template>
  <div class="voice-select">
    <div class="header">
      <div class="left">
        <span>试听文本</span>
      </div>
      <div class="right">
        <span
          v-for="item in tabs"
          :key="item.key"
          :class="['tabs', { active: timbre_type === item.key }]"
          @click="changeClassify(item.key)"
          >{{ item.text }}</span
        >
      </div>
    </div>
    <div class="bottom">
      <div class="left">
        <el-input
          type="textarea"
          placeholder="请输入内容"
          v-model="textVal"
          @input="onInput"
          maxlength="200"
          resize="none"
        >
        </el-input>
        <span :class="['text-count', { 'text-red': count > 140 }]"
          >{{ count }}/140</span
        >
      </div>
      <div class="right">
        <div class="voice-tags">
          <div
            v-for="(item, index) in voiceList"
            :class="[
              'tag',
              { 'active-tag': timbre_id.toString() === item.id.toString() && timbre_source === item.source},
              { 'last-tag': index % 3 === 2 },
              {'disabledVoice': disabledVoice}
            ]"
            :key="item.id"
            @click="changeVoice(item)"
            ><img
              :src="timbre_type === 'male' ? maleAvatar : femaleAvatar"
              alt=""
            />

            <div class="voice-desc-content">
              <div class="voice-desc">{{ item.name }}</div>
              <div v-if="item.desc" class="voice-desc-tag">{{ item.desc }}</div>
            </div>
            <div class="recommend-tag" v-if="item.recommend === 1">推荐</div>
          </div>
        </div>
        <div class="speed-slider">
          <span class="label">语速</span>
          <el-slider
            v-model="speed"
            :show-tooltip="false"
            :min="50"
            :max="200"
          ></el-slider>
          <span class="count">{{ speed }}%</span>
        </div>
        <div class="voice-bottom">
          <el-button
            type="primary"
            size="mini"
            :disabled="textVal.length === 0 || count > 140"
            @click="composeVioceDemo"
            :loading="loading"
            >试听语音</el-button
          >
          <div class="player">
            <i :class="isPlay ? 'pause-icon' : 'play-icon'" @click="onPlay"></i>
            <span class="duration">
              <span
                >00:{{ currentTime &lt; 10 ? '0' + currentTime : currentTime }}</span
              >
              <span>/</span>
              <span>00:{{duration &lt; 10 ? '0' + duration : duration}}</span>
            </span>
            <el-slider
              v-model="currentTime"
              :show-tooltip="false"
              :max="duration"
              @change="onTimeChange"
            ></el-slider>
            <el-popover
              placement="top"
              width="40"
              trigger="click"
              popper-class="sound-popper-style"
            >
              <el-slider
                v-model="sound"
                vertical
                :show-tooltip="false"
                height="100px"
                @change="onRoundChange"
              ></el-slider>
              <span class="percent">{{ sound }}</span>
              <span slot="reference" class="sound-size"></span>
            </el-popover>
            <audio id="audio" ref="audio" :src="src"></audio>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getVoice, composeVioce } from 'config/api.conf'

export default {
  name: 'voice-select',
  props: {
    virtualInfo: {
      type: Object,
      default: () => {}
    },
    disabledVoice: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      voiceData: {
        male: [],
        female: []
      },
      voiceList: [],
      count: 57,
      textVal:
        '你好，欢迎使用AI快捷做课。在此文本框中，你可选择不同音色，并修改试听文本，此后，点击右下方的“试听语音”按钮，即可试听语音',
      tabs: [
        {
          key: 'male',
          text: '男声'
        },
        {
          key: 'female',
          text: '女声'
        }
      ],
      maleAvatar: require('@/assets/img/man.png'),
      femaleAvatar: require('@/assets/img/woman.png'),
      timbre_type: 'male',
      timbre_id: '',
      timbre_source: '',
      src: '', // 错误时的头像
      speed: 100,
      duration: 0,
      currentTime: 0,
      sound: 100,
      isPlay: false,
      loading: false
    }
  },
  watch: {
    virtualInfo(val) {
      this.timbre_type = val.timbre_type || 'male'
      this.timbre_id = val.timbre_id || ''
      this.timbre_source = val.tts_source || ''
      this.speed = val.tts_speed || 100
      this.voiceList = this.voiceData[this.timbre_type]
      this.$emit('onVoiceChange', {
        timbre_id: this.timbre_id,
        timbre_type: this.timbre_type,
        tts_source: this.timbre_source,
        tts_speed: this.speed
      })
    },
    speed(val) {
      this.$emit('onVoiceChange', {
        timbre_id: this.timbre_id,
        timbre_type: this.timbre_type,
        tts_source: this.timbre_source,
        tts_speed: val
      })
    }
  },
  created() {
    this.getVoiceData()
  },
  mounted() {
    const player = this.$refs.audio
    // 播放器准备就绪
    player.addEventListener('canplay', this.canplayEvent)
    // 监听正在播放
    player.addEventListener('timeupdate', this.timeupdateEvent)
  },
  beforeDestroy() {
    this.$refs.audio.removeEventListener('canplay', this.canplayEvent)
    this.$refs.audio.removeEventListener('timeupdate', this.timeupdateEvent)
  },
  methods: {
    canplayEvent() {
      this.duration = Math.ceil(this.$refs.audio.duration)
      this.$refs.audio.play()
      this.isPlay = true
    },
    timeupdateEvent() {
      const player = this.$refs.audio
      this.currentTime = Math.ceil(
        isNaN(player.currentTime) ? 0 : player.currentTime
      )
      if (this.duration === this.currentTime) {
        this.isPlay = false
      }
    },
    // 监听输入
    onInput() {
      const arr = this.textVal.match(/[\u4e00-\u9fa5]/g)
      const len = (arr && arr.length) || 0
      this.count = len + Math.ceil((this.textVal.length - len) / 2)
    },
    // 获取声音
    getVoiceData() {
      // this.voiceData = {
      //   // male: [],
      //   // female: []
      // }
      getVoice().then((res) => {
        console.log('获取声音', res)
        // 转换格式为数组，方便操作
        // for (let key in res) {
        //   for (let label in res[key]) {
        //     this.voiceData[key].push({
        //       label,
        //       id: res[key][label]
        //     })
        //   }
        // }
        this.voiceData = res
        if (this.$route.query.net_course_id) {
          this.voiceList = this.voiceData[this.timbre_type]
        } else {
          this.timbre_type =
            this.voiceData.male && this.voiceData.male.length > 0
              ? 'male'
              : 'female'
          this.voiceList = this.voiceData[this.timbre_type]
        }
        this.$emit('onVoiceChange', {
          timbre_id: this.timbre_id,
          timbre_type: this.timbre_type,
          tts_speed: this.speed,
          tts_source: this.timbre_source
        })
      })
    },
    // 选择分类
    changeClassify(timbre_type) {
      this.timbre_type = timbre_type
      this.voiceList = this.voiceData[timbre_type]
      if (this.voiceList.length > 0) {
        this.timbre_id = ''
        this.$emit('onVoiceChange', {
          timbre_id: this.timbre_id,
          timbre_type,
          tts_speed: this.speed,
          tts_source: this.timbre_source
        })
      }
    },
    // 选择音色
    changeVoice(row) {
      this.timbre_id = row.id
      this.timbre_source = row.source
      this.$emit('onVoiceChange', {
        timbre_type: this.timbre_type,
        tts_speed: this.speed,
        timbre_id: this.timbre_id,
        tts_source: this.timbre_source
      })
    },
    // 合成音色按钮
    composeVioceDemo() {
      this.loading = true
      this.$refs.audio.pause()
      composeVioce({
        text: this.textVal,
        speed: this.speed,
        voice_type: this.timbre_id,
        tts_source: this.timbre_source
      }).then((res) => {
        this.src = 'data:audio/wav;base64,' + res
        this.$refs.audio.load()
        this.isPlay = false
        this.loading = false
      })
    },
    // 播放/暂停
    onPlay() {
      if (this.duration === 0) return
      const player = this.$refs.audio
      if (this.isPlay) {
        player.pause()
      } else {
        player.play()
      }
      this.isPlay = !this.isPlay
    },
    // 修改播放时间
    onTimeChange(val) {
      this.$refs.audio.pause()
      this.isPlay = false
      setTimeout(() => {
        this.currentTime = val
        this.$refs.audio.currentTime = val
        // if (this.duration !== val && val !== 0) {
        //   this.$refs.audio.play()
        //   this.isPlay = true
        // }
      }, 200)
    },
    // 修改音量
    onRoundChange(val) {
      this.$refs.audio.volume = val / 100
    }
  }
}
</script>
<style lang="less" scoped>
.voice-select {
  width: 726px;
  height: 522px;
  border-radius: 3px;
  border: 1px solid #eee;
  background: #f5f7f9;
  .header {
    display: flex;
    height: 54px;
    line-height: 54px;
    color: #0052d9;
    font-size: 14px;
    font-weight: bold;
    .left {
      width: 210px;
      padding-left: 20px;
    }
    .right {
      padding-left: 16px;
      width: 422px;
      .tabs {
        display: inline-block;
        width: 68px;
        text-align: center;
        color: #666;
        cursor: pointer;
      }
      .active {
        color: #0052d9;
        border-bottom: 2px solid #0052d9;
      }
    }
  }
  .bottom {
    display: flex;
    margin-top: 2px;
    // height: 352px;
    border-top: 1px solid #eee;
    .left {
      position: relative;
      padding: 16px 16px 16px 20px;
      border-right: 1px solid #eee;
      width: 210px;
      :deep(.el-textarea) {
        height: 432px;
        width: 174px;
        border-radius: 2px;
        .el-textarea__inner {
          padding: 12px;
          height: 100%;
          border: unset;
        }
      }
      .text-count {
        position: absolute;
        color: #999;
        position: absolute;
        font-size: 12px;
        bottom: 25px;
        right: 25px;
      }
      .text-red {
        color: red;
      }
    }
    .right {
      width: 516px;
      padding: 16px 20px 16px 16px;
      position: relative;
      .voice-tags {
        padding: 16px;
        height: 340px;
        width: 480px;
        overflow-y: auto;
        background-color: #fff;
        border: 1px solid #eee;
        // display: flex;
        .tag {
          float: left;
          padding: 12px;
          height: 68px;
          width: 140px;
          font-size: 14px;
          font-weight: 400;
          margin: 0 10px 10px 0;
          border-radius: 2px;
          color: #666;
          cursor: pointer;
          display: flex;
          align-items: center;
          border: 1px solid #eeeeee;
          position: relative;
          img {
            float: left;
            width: 32px;
            height: 32px;
            margin: 2px 6px 0 0;
            border-radius: 24px;
          }
          .voice-desc-content {
            .voice-desc {
              width: 77px;
              color: #000000e6;
              overflow: hidden;
              text-overflow: ellipsis;
              word-break: break-all;
              white-space: nowrap;
              font-size: 12px;
              line-height: 20px;
            }
            .voice-desc-tag {
              width: 52px;
              height: 16px;
              border-radius: 2px;
              background: #e1fff6;
              font-size: 10px;
              text-align: center;
              line-height: 16px;
              border-radius: 2px;
              margin-top: 4px;
            }
          }
          .recommend-tag  {
            position: absolute;
            top: 0px;
            right: 0px;
            border-radius: 1px;
            background: #ed7b2f;
            width: 40px;
            color: #ffffff;
            font-size: 12px;
            text-align: center;
            line-height: 16px;
          }
        }
        .active-tag {
          border: 1px solid #0052D9;
        }
        .last-tag {
          margin-right: 0;
        }
        .disabledVoice {
          pointer-events: none;
        }
      }
      .speed-slider {
        display: flex;
        margin-top: 20px;
        height: 20px;
        line-height: 20px;
        .label {
          margin-right: 8px;
          color: #666;
        }
        :deep(.el-slider) {
          flex: 1;
          .el-slider__runway {
            margin: 9px 0;
            height: 2px;
            .el-slider__bar {
              height: 100%;
              background-color: #0052d9;
            }
            .el-slider__button-wrapper {
              .el-slider__button {
                width: 6px;
                height: 6px;
                position: relative;
                bottom: 2px;
                background-color: #0052d9;
              }
            }
          }
        }
        .count {
          margin-left: 8px;
          color: #666;
          text-align: right;
        }
      }
      .voice-bottom {
        position: absolute;
        bottom: 16px;
        width: calc(100% - 36px);
        display: flex;
        .player {
          padding: 8px;
          flex: 1;
          display: flex;
          margin-left: 8px;
          height: 28px;
          background-color: #fff;
          box-sizing: border-box;
          .play-icon,
          .pause-icon {
            margin-right: 8px;
            width: 12px;
            height: 12px;
            cursor: pointer;
          }
          .play-icon {
            background: url(~@/assets/img/play-icon.png) no-repeat center/cover;
          }
          .pause-icon {
            background: url(~@/assets/img/pause.png) no-repeat center/cover;
          }
          .duration {
            display: inline-block;
            margin-right: 8px;
            line-height: 13px;
            > span {
              float: left;
            }
          }
          :deep(.el-slider) {
            flex: 1;
            margin-right: 8px;

            .el-slider__runway {
              margin: 5px 0;
              height: 2px;
              .el-slider__bar {
                height: 100%;
                background-color: #0052d9;
              }
              .el-slider__button-wrapper {
                .el-slider__button {
                  width: 6px;
                  height: 6px;
                  position: relative;
                  bottom: 2px;
                  background-color: #0052d9;
                }
              }
            }
          }
          .sound-size {
            float: left;
            width: 12px;
            height: 12px;
            background: url(~@/assets/img/sound.png) no-repeat center/cover;
            cursor: pointer;
          }
        }
      }
    }
  }
}
</style>
