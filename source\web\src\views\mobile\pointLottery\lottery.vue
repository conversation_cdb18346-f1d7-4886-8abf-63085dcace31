<template>
  <div class="lottery-wrapper" :style="bgStyle">
    <div class="draw-prize-crad-wrap" v-if="drawPrizeList.length > 0">
      <div class="tip-column flex-box">
        <img class="adorn" src="../../../assets/img/mobile/lottery/adorn.png" />
        <div class="tip-crad flex-box">
          <img class="icon" src="../../../assets/img/mobile/lottery/warning-white.png" />
          <span>注意：抽奖次数当日有效，请及时使用</span>
        </div>
        <img class="adorn" src="../../../assets/img/mobile/lottery/adorn.png" />
      </div>
      <div class="draw-prize-crad">
        <div class="draw-prize-area">
          <div class="prize-list">
            <div v-for="(item, index) in drawPrizeList" :class="['prize-item', { 'start-btn': index === 4, active: currentIndex === index }]" :key="item.goods_id" @click="startLottery(index)">
              <div :class="['prize-bg-card', ([0, 2, 6, 8].includes(index) && item.goods_name !== '谢谢参与') ? `prize-bg-${index}` : item.goods_name !== '谢谢参与' ? 'prize-bg-public' : `prize-bg-no_prize prize-bg-no_prize_${index}` ]" v-if="index !== 4">
                <van-image v-if="item.goods_name === '谢谢参与'" class="goods-img" :src="noPrizeIcon" />
                <van-image v-else class="goods-img" :src="item.img_url" />
                <p>{{ item.goods_name }}</p>
              </div>

              <template v-else>
                <button type="button">
                  <img v-if="lotteryStartBtn" src="../../../assets/img/mobile/lottery/start-prize-allow.png" alt="">
                  <van-popover
                    v-else
                    v-model="showPopover"
                    trigger="click"
                    placement="top"
                  >
                    <div class="popover-text">已无可用抽奖次数</div>
                    <template #reference>
                      <img  src="../../../assets/img/mobile/lottery/start-prize-forbid.png" alt="">
                    </template>
                  </van-popover>
                </button>
              </template>
            </div>
          </div>
        </div>
        <div class="count">
          <div class="residue-number">可用抽奖次数：{{ numberOfLuckyDraws }}</div>
          <div class="get-more flex-box" @click="goGetMore">
            <span>查看抽奖规则</span>
            <img class="icon" src="../../../assets/img/mobile/lottery/arrow-right-white.png" alt="" srcset="">
          </div>
        </div>
      </div>
      <!-- 按钮栏 -->
      <div class="btn-column">
        <button type="button" @click="activityRulesPreview">活动规则</button>
        <button type="button" @click="lotteryDetailsPreview">抽奖次数明细</button>
        <button type="button" @click="lotteryRecordPreview">抽奖记录</button>
      </div>
      <!-- 无缝滚动卡片 -->
      <div class="scroll-card">
        <vue-seamless-scroll :data="listData" :class-option="classOption">
          <ul
            class="table-lie"
            v-for="(item, index) in listData"
            :key="index + item"
          >
            <li>
              <p class="name">{{ item.staffName }}</p>
              <p class="behaviour">{{ item.winAwards }}</p>
              <p class="goods">{{ item.goodsName }}</p>
              <!-- <p class="time">{{ item.time }}</p> -->
            </li>
          </ul>
        </vue-seamless-scroll>
      </div>
    </div>
    <!-- 抽奖结果弹窗 -->
    <result-popup v-if="resultPopupShow" v-model="resultPopupShow" :prize-result="prizeResult" @openPrizeDetail="openPrizeDetail"></result-popup>
    <!-- 活动规则 -->
    <activity-rules v-if="activityRulesShow" v-model="activityRulesShow" :explain-data="explainData"></activity-rules>
    <!-- 抽奖次数明细 -->
    <lottery-details-popup v-if="lotteryDetailsShow" v-model="lotteryDetailsShow" @goGetMore="goGetMore"></lottery-details-popup>
    <!-- 引导获取抽奖次数 -->
    <obtain-guidance-popup v-if="obtainguidanceShow" v-model="obtainguidanceShow"></obtain-guidance-popup>
    <!-- 抽奖记录 -->
    <lottery-record-popup ref="lotteryRecord" v-if="lotteryRecordShow" v-model="lotteryRecordShow"></lottery-record-popup>
  </div>
</template>

<script>
import vueSeamlessScroll from 'vue-seamless-scroll'
import { Toast, Popover } from 'vant'
import resultPopup from './components/resultPopup'
import activityRules from './components/activityRules'
import lotteryDetailsPopup from './components/lotteryDetailsPopup'
import obtainGuidancePopup from './components/obtainGuidancePopup'
import lotteryRecordPopup from './components/lotteryRecordPopup'
import { getLuckDrawListAPI, getLotteryRecordsAPI, getNumberOfLuckyDrawsAPI, getLuckDrawResultsAPI, getLotteryActivityInfoAPI } from '@/config/lottery.api.conf.js'

const bgImg = require('../../../assets/img/mobile/lottery/lottery-bg.png')
const noPrizeImg = require('../../../assets/img/mobile/lottery/no-prize.png')

export default {
  components: {
    vueSeamlessScroll,
    [Popover.name]: Popover,
    resultPopup,
    activityRules,
    lotteryDetailsPopup,
    obtainGuidancePopup,
    lotteryRecordPopup
  },
  data() {
    return {
      activityId: '',
      bgImgUrl: bgImg, // 背景图片
      prizeInfo: {},
      noPrizeIcon: noPrizeImg,
      drawPrizeList: [
        { goods_name: '谢谢参与', img_url: noPrizeImg },
        { goods_name: '谢谢参与', img_url: noPrizeImg },
        { goods_name: '谢谢参与', img_url: noPrizeImg },
        { goods_name: '谢谢参与', img_url: noPrizeImg },
        { goods_name: '谢谢参与', img_url: noPrizeImg },
        { goods_name: '谢谢参与', img_url: noPrizeImg },
        { goods_name: '谢谢参与', img_url: noPrizeImg },
        { goods_name: '谢谢参与', img_url: noPrizeImg },
        { goods_name: '谢谢参与', img_url: noPrizeImg }
      ],
      numberOfLuckyDraws: 0, // 抽奖次数
      isRunning: false, // 是否抽奖进行中
      speed: 100, // 抽奖转动速度
      currentIndex: -1, // 当前进度
      prizeSort: [0, 1, 2, 5, 8, 7, 6, 3], // 执行的下标顺序
      timerIns: null, // 定时器实例
      currentRunCount: 0, // 已跑次数
      totalRunCount: 32, // 总共跑动次数 8的倍数
      realityTotalRunCount: 0, // 实际总运行次数
      listData: [
        // { staffName: 'w*****t', winAwards: '抽中', goodsName: '小米 电磁炉', time: '2023-09-26' },
      ],
      prizeIndex: null, // 中奖奖品下标
      prizeResult: {}, // 中奖结果信息
      showPopover: false,
      resultPopupShow: false,
      activityRulesShow: false,
      explainData: {},
      lotteryDetailsShow: false,
      obtainguidanceShow: false,
      lotteryRecordShow: false
    }
  },
  computed: {
    bgStyle() {
      if (this.bgImgUrl) {
        return `background: url(${this.bgImgUrl}) no-repeat top/100% fixed;`
      }
      return `background: url(${bgImg}) no-repeat top/100% fixed;`
    },
    classOption () {
      return {
        step: 0.6, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        direction: 1, // 0向下 1向上 2向左 3向右
        openTouch: true, // 移动端开启touch滑动
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 0, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000 // 单步运动停止的时间(默认值1000ms)
      }
    },
    lotteryStartBtn() {
      if (!this.isRunning && this.numberOfLuckyDraws > 0) {
        return true
      }
      return false
    }
  },
  created() {
    this.activityId = this.$route.query.activity_id || 3
    if (this.activityId) {
      this.getPrizeInfo()
      this.getLotteryRecords()
      this.getNumberOfLuckyDraws()
      this.getLotteryActivityInfo()
    }
  },
  methods: {
    // 获取奖品信息
    getPrizeInfo() {
      getLuckDrawListAPI(this.activityId).then(res => {
        let start = {
          goods_name: '开始抽奖按钮',
          img_url: '../../../assets/img/mobile/start-prize-allow.png',
          goods_id: null
        }
        res.goods_infos.splice(4, 0, start)
        this.drawPrizeList = res.goods_infos
        this.prizeInfo = res
      })
    },
    // 获取中奖列表
    getLotteryRecords() {
      getLotteryRecordsAPI(this.activityId).then(res => {
        this.listData = res
      })
    },
    // 获取抽奖次数
    getNumberOfLuckyDraws() {
      getNumberOfLuckyDrawsAPI('staffid').then(res => {
        this.numberOfLuckyDraws = res
      })
    },
    // 获取抽奖活动信息
    getLotteryActivityInfo() {
      getLotteryActivityInfoAPI(this.activityId).then(res => {
        this.explainData = {
          title: '活动规则',
          explain: res.activity_remark
        }
      })
    },
    // 活动规则
    activityRulesPreview() {
      this.activityRulesShow = true
    },
    // 抽奖次数明细
    lotteryDetailsPreview() {
      this.lotteryDetailsShow = true
    },
    // 去获取更多
    goGetMore() {
      if (this.lotteryDetailsShow) this.lotteryDetailsShow = false
      this.obtainguidanceShow = true
    },
    // 抽奖记录
    lotteryRecordPreview() {
      this.lotteryRecordShow = true
    },
    // 开始抽奖
    startLottery(index) {
      // ****************** 自定义数据测试 ******************
      // if (index === 100) {
      //   getLuckDrawResultsAPI()
      //   Toast('抽奖次数不足，请先获取积分')
      // }
      // console.log(index, this.isRunning)

      if (index === 4 && this.lotteryStartBtn) {
        if (this.numberOfLuckyDraws <= 0) {
          Toast('抽奖次数不足')
          return
        }
        // 初始化抽奖数据
        this.isRunning = true
        this.currentRunCount = 0
        this.speed = 100
        // 获取抽奖结果
        getLuckDrawResultsAPI({ goods_id: this.activityId }).then(res => {
          console.log('抽奖结果', res)
          if (res.luck_draw_type === -2) {
            Toast('抽奖已结束~')
            return
          }
          this.prizeIndex = this.drawPrizeList.findIndex((item) => {
            return item.goods_id === res.goods_id
          })
          this.realityTotalRunCount = this.totalRunCount + this.prizeSort.indexOf(this.prizeIndex)
          this.prizeResult = res
          this.startRun()
        }).catch(err => {
          console.log(err)
          setTimeout(() => {
            this.isRunning = false
          }, 2000)
        })

        // ****************** 自定义数据测试 ******************
        // this.isRunning = true
        // this.currentRunCount = 0
        // this.speed = 100
        // let num = Math.floor(Math.random() * 10)
        // if (num === 4) ++num
        // if (num > 8) --num
        // this.prizeIndex = num
        // this.prizeResult = {
        //   acct_change: -5,
        //   goods_order_id: 201407,
        //   goods_id: 631,
        //   goods_name: '抽奖商品126031',
        //   goods_remark: '<p>测试测试测试</p>',
        //   img_url: 'http://test-contentcenter.woa.com/api/sso/content-center/api/v1/content/imgage/59b4d9d69cf8471fba40903e06412b89/preview',
        //   luck_draw_type: 0,
        //   redeem_questionnaire_url: '123',
        //   redeem_time: '2023-09-28 11:22:54',
        //   redeem_code: 'WEREAD2%25236xG02cJ5c9Y5T6Jk5DfsovCQatSZ3Nb4WEREAD2%25236xGFD66668888',
        //   redeem_pwd: 'sghs_hfdjhrSGAdfhSASSS2666',
        //   expire_time: '2023-09-30 23:59:59'
        // }
        // this.realityTotalRunCount = this.totalRunCount + this.prizeSort.indexOf(this.prizeIndex)
        // console.log('start', this.prizeIndex, this.prizeSort.indexOf(this.prizeIndex))
        // this.startRun()
      }
    },
    startRun() {
      this.stopRun()
      // 达到指定步数停止转动
      if (this.currentRunCount > this.realityTotalRunCount) {
        this.isRunning = false
        // 更新抽奖数
        this.getNumberOfLuckyDraws()
        this.resultPopupShow = true
        return
      }
      // 抽奖转动一次
      this.currentIndex = this.prizeSort[this.currentRunCount % 8]
      // 当前转动次数超过实际总转动次数三分之二，降低转动速度
      if (this.currentRunCount > Math.floor(this.totalRunCount / 3 * 2)) {
        this.speed = this.speed + Math.floor(this.currentRunCount / 3)
        console.log('速度>>>>', this.speed)
      }
      this.timerIns = setTimeout(() => {
        this.currentRunCount++
        this.startRun()
      }, this.speed)
    },
    stopRun() {
      this.timerIns && clearTimeout(this.timerIns)
    },
    openPrizeDetail(orderId) {
      this.lotteryRecordShow = true
      this.$nextTick(() => {
        console.log(this.$refs)
        this.$refs.lotteryRecord.openPrizeDetails({ goods_order_id: orderId })
      })
    }
  }
}
</script>

<style lang='less' scoped>
.lottery-wrapper {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  .draw-prize-crad-wrap {
    width: 343px;
    padding: 12px 7px;
    background: #4E76F6;
    margin: 101px auto 0;
    border-radius: 18px;
    .tip-column {
      padding: 0 5px 2px;
      .adorn {
        width: 12px;
        height: 12px;
        flex-shrink: 0;
      }
      .tip-crad {
        margin: 0 12px;
        flex: 1;
        height: 32px;
        border-radius: 16px;
        background: #1453D0;
        color: #ffffffe6;
        font-size: 12px;
        line-height: 16px;
        justify-content: center;
        .icon {
          width: 14px;
          height: 14px;
          margin-right: 9px;
        }
      }
    }
  }
  .draw-prize-crad {
    text-align: center;
    .draw-prize-area {
      padding: 8px 8px 4px 8px;
      border: 5px solid #6e92f7;
      border-radius: 24px;
      .prize-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        .prize-item {
          width: 98px;
          height: 98px;
          margin-bottom: 4.5px;
          font-size: 10px;
          line-height: 16px;
          color: #00256d;
          .prize-bg-card {
            width: 100%;
            height: 100%;
            background-size: 100% 100%;
            background-repeat: no-repeat;
            padding-top: 10px;
          }
          .prize-bg-0 {
            background-image: url('../../../assets/img/mobile/lottery/box-upper-left.png');
          }
          .prize-bg-2 {
            background-image: url('../../../assets/img/mobile/lottery/box-upper-right.png');
          }
          .prize-bg-6 {
            background-image: url('../../../assets/img/mobile/lottery/box-lower-left.png');
          }
          .prize-bg-8 {
            background-image: url('../../../assets/img/mobile/lottery/box-lower-right.png');
          }
          .prize-bg-public {
            background-image: url('../../../assets/img/mobile/lottery/box-center.png');
          }
          .prize-bg-no_prize {
            background-color: white;
            border-radius: 8px;
          }
          .prize-bg-no_prize_0 {
            border-radius: 16px 8px 8px 8px;
          }
          .prize-bg-no_prize_2 {
            border-radius: 8px 16px 8px 8px;
          }
          .prize-bg-no_prize_6 {
            border-radius: 8px 8px 8px 16px;
          }
          .prize-bg-no_prize_8 {
            border-radius: 8px 8px 16px 8px;
          }
          .goods-img {
            width: 60px;
            height: 60px;
            display: inline-block;
            border-radius: 4px;
            overflow: hidden;
          }
          p {
            padding: 2px 11px 0;
            text-align: center;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
          }

        }
        .start-btn {
          & button {
            background-color: transparent;
            border-color: transparent;
            border: 0px
          }
          img {
            width: 98px;
            height: 98px;
            display: inline-block;
          }
        }
        .active {
          .prize-bg-0,
          .prize-bg-no_prize_0 {
            background-image: url('../../../assets/img/mobile/lottery/box-upper-left-active.png');
          }
          .prize-bg-2,
          .prize-bg-no_prize_2 {
            background-image: url('../../../assets/img/mobile/lottery/box-upper-right-active.png');
          }
          .prize-bg-6,
          .prize-bg-no_prize_6 {
            background-image: url('../../../assets/img/mobile/lottery/box-lower-left-active.png');
          }
          .prize-bg-8,
          .prize-bg-no_prize_8 {
            background-image: url('../../../assets/img/mobile/lottery/box-lower-right-active.png');
          }
          .prize-bg-public,
          .prize-bg-no_prize {
            background-image: url('../../../assets/img/mobile/lottery/box-center-active.png');
          }
          .prize-bg-no_prize {
            background-color: transparent;
            // border: 5px solid #fbd048;
            // background-color: #faefce;
            // padding-top: 4px;
          }
        }
      }
    }
    .count {
      line-height: 16px;
      color: #ffffff;
      font-size: 14px;
      font-family: PingFangSC-regular;
      display: flex;
      justify-content: center;
      font-weight: normal;
      margin: 16px 20px;
      .residue-number {
        margin-right: 24px;
      }
      .get-more {
        justify-content: center;
        & > span {
          text-decoration-line: underline;
          text-underline-offset: 4px;
          text-decoration-thickness: 0.5px;
        }
        .icon {
          width: 16px;
          height: 16px;
          margin-left: 4px;
        }
      }
    }
  }
  .btn-column {
    padding: 0px 13px 20px;
    display: flex;
    justify-content: space-between;
    & button {
      width: 92px;
      height: 32px;
      border-radius: 4px;
      font-weight: 500;
      line-height: 20px;
      border-color: transparent;
      color: #FFFFFF;
      font-size: 11px;
      text-align: center;
      background-color: #1F4CEF;
      letter-spacing: 2px;
    }
  }
  .scroll-card {
    height: 78px;
    overflow: hidden;
    margin: 0 1.5px;
    border-radius: 10px;
    background-color: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 6px 0px rgba(0, 0, 0, 0.4);
    .table-lie {
      li {
        display: flex;
        color: rgba(154, 154, 154, 1);
        padding: 8px 12px 0;
        .name {
          min-width: 56px;
          max-width: 20%;
          margin-right: 8px;
          flex-shrink: 0;
        }
        .behaviour {
          min-width: 34px;
          max-width: 10%;
          margin-right: 8px;
        }
        .goods {
          flex: 1;
          margin-right: 20px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          flex-shrink: 0;
          color: #266FE8;
        }
        .time {
          min-width: 82px;
          max-width: 20%;
          flex-shrink: 0;
        }
      }
    }
  }
}
.van-popover--light {
  .van-popover__arrow {
    color: rgba(0, 0, 0, 0.75);
  }
  .van-popover__content {
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.75);
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
    font-family: Roboto;
  }
}
.popover-text {
  padding: 6px 16px;
  color: rgba(255, 255, 255, 1);
  font-size: 12px;
  line-height: 18px;
  text-align: center;
  font-family: PingFangSC-regular;
}
.flex-box {
  display: flex;
  align-items: center;
}
</style>
