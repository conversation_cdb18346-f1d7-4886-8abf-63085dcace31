<template>
    <van-popup class="redemption-popup" v-model="show" round z-index="1003" :close-on-click-overlay="false">
        <img class="top-img" src="../../../../../assets/img/mobile/geekBang/header-image.png" alt="" />
        <div class="content">
          <div class="content-main">
            <div class="warm">{{ currentPopInfo.warm }}</div>
            <!-- <div class="title">{{ currentPopInfo.title }}</div> -->
            <div class="title" v-html="currentPopInfo.title"></div>
            <div class="coupon-content">
                <div class="points-content">
                    <span class="points">900积分</span>
                    <span class="use-num">需使用 <span style="font-weight:500;color: #ed7b2f">{{ coursePurchaseInfo.course_val }}</span> 张学霸卡</span>
                </div>
                <div class="my-coupon">我的学霸卡：<span class="color-cupton-num">{{coursePurchaseInfo.user_account_num}}</span> 张</div>
                <div class="link" @click="linkTo">
                    <img class="icon" src="../../../../../assets/img/mobile/geekBang/help_circle.png" alt="" />
                    <span>想获得更多学霸卡？点此获取攻略</span>
                </div>
            </div>
          </div>
            <van-button v-if="currentPopInfo.code !== 3" class="btn" @click="userOperator(currentPopInfo.code)" :disabled="currentPopInfo.code === 2 && isSubscription">{{ currentPopInfo.btn }}</van-button>
            <van-button v-else class="btn btn-long" @click="userOperator(currentPopInfo.code)" :disabled="currentPopInfo.code === 2 && isSubscription">{{ currentPopInfo.btn }}</van-button>
          </div>
        <img class="close-icon" @click="$emit('input', false)" src="../../../../../assets/img/mobile/geekBang/right.png" alt="" />
    </van-popup>
</template>

<script>
export default {
  name: 'redemptionPopup',
  props: {
    value: {
      type: Boolean,
      default: false,
      require: true
    },
    currentPopInfo: {
      type: Object,
      default: () => {},
      require: true
    },
    coursePurchaseInfo: {
      type: Object,
      default: () => {},
      require: true 
    },
    isSubscription: {
      type: Boolean,
      default: false,
      require: true
    }
  },
  watch: {
    value(newValue) {
      this.show = newValue
    }
  },
  data () {
    return {
      show: true
    }
  },
  computed: {},
  created () {},
  mounted () {},
  methods: {
    // 用户操作
    userOperator(code) {
      this.$emit('userOperator', code)
    },
    // 链接跳转
    linkTo() {
      this.$emit('linkTo')
    }
  }
}
</script>

<style lang="less" scoped>
.redemption-popup {
    width: 311px;
    background-color: rgba(255,255,255,0);
    display: flex;
    flex-direction: column;
    align-items: center;
    .top-img {
        width: 100%;
    }
    .content {
        padding: 16px 0 28px;
        width: 100%;
        border-radius: 0 0 12px 12px;
        background-color: #fff;
        .content-main {
          padding: 0 32px;
          display: flex;
          flex-direction: column;
          align-items: center;
        }
        .color-cupton-num {
          color: #ed7b2f;
        }
        .warm {
            color: #0052d9;
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
        }
        .title {
            margin-top: 8px;
            color: #000000e6;
            font-size: 16px;
            line-height: 24px;
            text-align: center;
        }
        .coupon-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 16px;
            padding: 12px 8.5px;
            border-radius: 4px;
            background: #F4F8FF;
            color: #0052d9;
            font-size: 14px;
            .points-content {
                .points {
                    color: #00000066;
                    line-height: 22px;
                    text-decoration-line: line-through;
                }
                .use-num {
                    margin-left: 12px;
                    line-height: 22px;
                }
            }
            .extra-num {
                margin-top: 6px;
                line-height: 22px;
                span {
                    font-weight: 500;
                    color: #ed7b2f;
                }
            }
            .my-coupon {
                margin-top: 12px;
                line-height: 22px;
            }
            .link {
                cursor: pointer;
                margin-top: 6px;
                .icon {
                    width: 14px;
                    margin-right: 5px;
                }
                span {
                    line-height: 22px;
                    text-decoration-line: underline;
                }
            }
        }
        .btn {
            margin: 0 auto;
            color: #ffffff;
            margin-top: 16px;
            display: flex;
            width: 245px;
            height: 48px;
            padding: 9px 20px;
            justify-content: center;
            align-items: center;
            border-radius: 24px;
            background: linear-gradient(90deg, #488DFF 0%, #1766FF 100%);
        }
        .btn-long {
          width: auto;
        }
    }
    .close-icon {
        width: 36px;
        margin-top: 16px;
    }
}
</style>
