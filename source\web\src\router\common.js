const common = [
  {
    path: '/common/convention',
    name: 'convention',
    component: () => import('views/manage/convention/convention.vue'),
    meta: {
      title: '腾讯学堂学习平台文明公约'
    }
  },
  {
    path: '/common/reLoginTest',
    name: 'reLoginTest',
    component: () => import('views/user/reLogin/reloginTest.vue'),
    meta: {
      title: '登录测试页'
    }
  },
  {
    path: '/common/questionnair',
    name: 'questionnair',
    component: () => import('views/manage/mooc/project-manage/questionnair/index.vue'),
    meta: {
      title: '问卷'
    }
  },
  {
    path: '/common/user/survey',
    name: 'surveyJump',
    component: () => import('views/user/wjRedirect/emptyJumpPage.vue'),
    meta: {
      title: ''
    }
  }
]

export default common
