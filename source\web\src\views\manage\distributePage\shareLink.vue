<template>
  <el-dialog
    title="H5"
    :visible.sync="visible"
    top="25vh"
    :before-close="close"
    width="750px">
    <div slot="title" class="dialog-title">{{`${exam_name}  (${exam_id})`}}</div>
    <div class="dialog-content">
      <p class="top-text">请使用微信/QQ扫码下方二维码，或者复制下方链接</p>
      <div class="qr-code">
        <div>
          <p class="qr-text">移动端</p>
          <vue-qr :text="moblinks" :margin="0" colorDark="#000" colorLight="#fff" :logoScale="0.3" :size="150"></vue-qr>
        </div>
      </div>
      <p class="item">
        <span>页面链接：</span>
        <el-input v-model="moblinks" disabled ref="copyInput" class="input-with-select">
          <el-button slot="append" type="primary" @click="copy">复制</el-button>
        </el-input>
      </p>
    </div>
  </el-dialog>
</template>

<script>
import vueQr from 'vue-qr'
export default {
  name: '',
  components: {
    vueQr
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      moblinks: '',
      exam_name: '',
      exam_id: ''
    }
  },
  methods: {
    getData(row) {
      this.exam_name = row.exam_name
      this.exam_id = row.exam_id
      this.moblinks = process.env.NODE_ENV === 'production' ? `https://sdc.qq.com/s/MEgxkm?forward_id=${this.exam_id}` : `https://test-portal-learn.woa.com/training/api/businessCommon/user/forward?forward_id=${this.exam_id}`
    },
    copy(e) {
      let newInput = document.createElement('input')
      newInput.value = this.moblinks
      document.body.appendChild(newInput)
      newInput.select()
      document.execCommand('Copy')
      newInput.remove()
      this.$message.success('复制成功！')
    },
    close() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="less" scoped>
.dialog-title {
  text-align: center;
  padding: 20px 0;
  font-size: 20px;
  border-bottom: 1px solid #eee;
  color: #000;
}
.dialog-content{
  padding: 0 20px 15px 20px;
  .top-text {
    font-size: 15px;
    color: #000;
    text-align: center;
    line-height: 20px;
  }
  .qr-code{
    margin: 30px auto;
    display: flex;
    justify-content: space-around;
    .qr-text{
      font-size: 15px;
      text-align: center;
      margin-bottom: 15px;
    }
  }
  .item{
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    span{
      width: 100px;
    }
  }
}
  /deep/ .el-input-group__append button.el-button, .el-input-group__append div.el-select .el-input__inner, .el-input-group__append div.el-select:hover .el-input__inner, .el-input-group__prepend button.el-button, .el-input-group__prepend div.el-select .el-input__inner, .el-input-group__prepend div.el-select:hover .el-input__inner {
    color: #FFF;
    background-color: #3464E0;
    border-color: #3464E0;
  }
  /deep/ .el-dialog__header {
    padding: 0;
    border-bottom: transparent;
  }
</style>
