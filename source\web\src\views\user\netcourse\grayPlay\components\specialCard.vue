<template>
  <div class="special-main" v-if="specialList?.length">
    <div class="special-container">
      <div class="special-title-box">
        <span class="title overflow-l1">{{ $langue('NetCourse_Portal', { defaultText: '专区' }) }}：「 {{ specialInfo.name }} 」</span>
        <span 
        class="btn" 
        @click="toSpecial" 
        :dt-eid="dtSpecialBtn('eid')"
        :dt-remark="dtSpecialBtn('remark')"
        :dt-areaid="dtSpecialBtn('areaid')" 
        >
          <i class="el-icon-arrow-left"></i>
          <span>{{ $langue('NetCourse_ContainerTips', { defaultText: '回到专区查看更多内容' }) }}</span>
        </span>
      </div>
      <div ref="specialSwiperRef" class="special-swiper-box">
        <div 
        v-for="(e, index) in specialList" 
        :key="index" 
        class="item-special-swiper" 
        @click="toLink(e)"
        :dt-eid="dtSpecialList('eid', e)"
        :dt-remark="dtSpecialList('remark', e)"
        :dt-areaid="dtSpecialList('areaid', e)" 
        >
          <div class="item-left-img">
            <el-image lazy fit="fill" :src="e.photo_url" class="extend-cover">
              <div class="image-slot" slot="placeholder">
                <i class="el-icon-loading"></i>
              </div>
              <div class="error-cover" slot="error">
                <img :src="formatModuleMap(e.module_id)" alt="" />
              </div>
            </el-image>
            <span class="content-type"><span>{{ showModuleName(e) }}</span></span>
            <span class="time" v-if="showModuleTips(e)">
              {{ showModuleTips(e) }}
            </span>
          </div>
          <div class="item-right">
            <div class="content-name overflow-l2">{{ e.content_name }}</div>
            <div class="mid-tag" v-if="e.labels?.length">
              <span class="tag overflow-l1" v-for="(item, i) in e.labels" :key="i">
                {{ item }}
              </span>
            </div>
            <!-- 直播 -->
            <div class="bottom-icon" v-if="e.module_id === 3">
              <span class="view-span"><i class="card-time"></i>{{ forMatTime(e) }}</span>
            </div>
            <div class="bottom-icon" v-else>
              <span class="view-span">
                <i class="view-icon"></i>
                <span>{{ transformUnitW(e.play_total_count || 0) }}{{ $langue('NetCourse_Views', { defaultText: '次观看' })
                }}</span>
              </span>
              <!-- <span class="view-span">
                <i class="score-icon"></i>
                <span>{{ e.avg_score || 0 }}{{ $langue('Mooc_ProjectDetail_Score_Point', { defaultText: '分' }) }}</span>
              </span> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  specialAreaAPI,
  specialDetail
} from 'config/api.conf'
import { transformUnitW, formatModuleMap } from 'utils/tools'
import { actTypes } from '@/utils/moduleMap.js'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]
export default {
  props: {
    tabActiveName: {
      type: String,
      default: ''
    },
    courseData: {
      type: Object,
      default: () => ({})
    },
    dtPageType: {
      type: String,
      default: '网课详情页-新版'
    }
  },
  data() {
    return {
      transformUnitW,
      formatModuleMap,
      specialList: [],
      specialInfo: {
        name: '',
        page_id: ''
      },
      actTypes
    }
  },
  computed: {
    course_id() {
      return this.courseData.course_id || parseInt(this.$route.query.course_id)
    },
    forMatTime() {
      return ({ start_time, end_time }) => {
        const endTime = end_time ? end_time.split(' ')[1] : ''
        return `${start_time} - ${endTime}`
      }
    },
    showModuleName() {
      return (v) => {
        let row = this.actTypes.find((e) => v.module_id === e.module_id)
        return this.$langue(row.langKey || row.act_type_name, { defaultText: row.act_type_name })
      }
    },
    showModuleTips() {
      return ({ module_id, duration, word_num, origin_data }) => {
        let tips = ''
        if ([1, 2, 4].includes(module_id)) { // 网络课, 面授课，活动
          tips = `${duration || 0}` + this.$langue('Mooc_ProjectDetail_TaskList_Minute', { defaultText: '分钟' })
        } else if ([7, 8].includes(module_id)) { // 案例，文章
          tips = `${word_num || 0}` + this.$langue('Mooc_ProjectDetail_TaskList_Words', { defaultText: '字' })
        } else if ([10].includes(module_id)) { // 培养项目
          tips = `${origin_data?.task_count || 0}` + this.$langue('Mooc_ProjectDetail_BasicInfo_Tasks', { defaultText: '项任务' })
        }
        return tips
      }
    },
    dtSpecialBtn() {
      return (type) => {
        const { area_id } = this.$route.query
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: this.dtPageType, 
            container: '专区',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '回到专区查看更多内容',
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${area_id}_回到专区查看更多内容`
        } else {
          return `area_${this.course_id}_${area_id}_回到专区查看更多内容`
        }
      }
    },
    dtSpecialList() {
      return (type, row) => {
        const { area_id } = this.$route.query
        const actInfo = this.actTypes.find((e) => row.module_id === e.module_id)
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: this.dtPageType, 
            container: '专区',
            click_type: 'data',
            content_type: row.module_name,
            content_id: row.item_id,
            content_name: row.content_name,
            act_type: actInfo?.act_type || '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${area_id}_${row.item_id}`
        } else {
          return `area_${this.course_id}_${area_id}_${row.item_id}`
        }
      }
    }
  },
  mounted() {
    this.getSpecialDetail()
  },
  methods: {
    toLink(e) {
      if (!e.content_url) return
      let url = e.content_url
      if ([1, 2].includes(Number(e.module_id))) { // 网络课1, 面授课2-专区
        url = `${url}&from=SpecialArea&area_id=${this.specialInfo.page_id}`
      }
      window.open(url)
    },
    toSpecial() {
      if (!this.specialInfo.page_id) return
      if (this.specialInfo.custom_link_url) {
        window.open(this.specialInfo.custom_link_url)
        return
      }
      const url = `${envName.courseWoaHost}user/special?scheme_type=homepage&page_id=${this.specialInfo.page_id}`
      window.open(url)
    },
    getSpecialDetail() {
      const { area_id } = this.$route.query
      const param = {
        app_id: 'A9BiosXihR0h46ThNsAX'
      }
      specialDetail(area_id, param).then((e) => {
        this.specialInfo = e
        this.getSpcialList()
      })
    },
    getSpcialList() {
      // scheme_type=netcourse&course_id=23275&from=SpecialArea&area_id=107
      const { area_id } = this.$route.query
      const param = {
        app_id: 'A9BiosXihR0h46ThNsAX',
        terminal_type: 1,
        condition: {
          orderby: [{ column: 'view_count_total', type: 'desc' }]
        },
        current: 1,
        size: 3
      }
      specialAreaAPI(area_id, param).then((res) => {
        this.specialList = res?.records || []
      })
    }
  }
}
</script>
<style lang="less" scoped>
.special-main {
  margin-bottom: 20px;
  background: #fff;
  padding: 20px 24px;
  border-radius: 8px;

  .special-title-box {
    display: flex;
    flex-direction: column;

    .el-icon-arrow-left {
      font-size: 14px;
      margin-right: 4px;
      font-weight: bold;
    }

    .title {
      color: #333333;
      font-size: 16px;
      font-weight: bold;
      display: inline-block;
      margin-bottom: 12px;
    }

    .btn {
      color: #006FFF;
      cursor: pointer;
      font-size: 12px;
      display: flex;
      align-items: center;
      line-height: 16px;
    }
  }

  .special-swiper-box {

    .item-special-swiper {
      background-color: #fff;
      cursor: pointer;
      display: flex;
      align-items: center;
      margin-top: 16px;

      .item-left-img {
        position: relative;
        font-size: 12px;
        font-weight: 500;
        width: 128px;
        height: 84px;
        margin-right: 10px;

        .extend-cover {
          width: 128px;
          height: 84px;
          border-radius: 4px;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;

          .error-cover img {
            width: 128px;
            height: 84px;
          }
        }

        .content-type {
          position: absolute;
          top: 4px;
          left: 4px;
          background: #0052D9;
          color: #fff;
          padding: 0 4px;
          border-radius: 4px;
          height: 18px;
          display: flex;
          align-items: center;
        }

        .time {
          position: absolute;
          bottom: 4px;
          right: 4px;
          background: #00000099;
          color: #fff;
          padding: 0 4px;
          border-radius: 4px;
          height: 18px;
          display: flex;
          align-items: center;
        }
      }

      .item-right {
        .content-name {
          color: #333333;
          font-weight: bold;
          line-height: 20px;
          opacity: 0.8;
        }

        .mid-tag {
          margin-top: 2px;
          flex-wrap: wrap;
          align-items: center;
          overflow: hidden;
          width: 176px;
          height: 22px;

          .tag {
            height: 22px;
            line-height: 22px;
            padding: 0px 6px;
            border-radius: 4px;
            background: #F5F7FA;
            color: #777777;
            margin-right: 8px;
            max-width: 82px;
            display: inline-block;
            flex-shrink: 0;
            font-size: 12px;
          }
        }

        .bottom-icon {
          margin-top: 4px;
          display: flex;
          color: #777777;
          font-size: 12px;

          .view-span {
            display: flex;
            align-items: center;
            line-height: 16px;
          }

          i {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }

          .view-icon {
            background: url("~@/assets/img/watch.png") no-repeat center /cover;
          }

          .score-icon {
            background: url("~@/assets/img/score-line.png") no-repeat center /cover;
            margin-left: 20px;
          }
        }
      }
    }
  }
}
</style>
