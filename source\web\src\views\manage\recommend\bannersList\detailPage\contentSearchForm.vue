<template>
  <div>
    <el-form :class="['content-search-form', { 'inline': inline }]" :model="value" :inline="inline" label-width="90px">
      <el-form-item label="内容标题">
        <el-input v-model="value.keywords" placeholder="请输入名称" size="small"></el-input>
      </el-form-item>
      <!-- <el-form-item label="标签：" class="labels">
        <el-select
          class="w-inherit"
          v-model="value.labels"
          multiple
          filterable
          allow-create
          default-first-option
          no-data-text="请手动输入标签"
          placeholder="请手动输入标签"
          size="small">
        </el-select>
      </el-form-item>
      <el-form-item label="上传时间：">
        <el-date-picker v-model="value.updated_at" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" size="small"></el-date-picker>
        <el-date-picker v-model="value.updated_at" type="datetimerange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" value-format="yyyy-MM-dd HH:mm:ss" size="small">
        </el-date-picker>
      </el-form-item> -->
      <el-form-item label="内容类型" style="width: 100%">
        <el-radio v-model="value.moduleId" :label="undefined" @change="clearSelect(null)">全部</el-radio>
        <el-radio v-model="value.moduleId" v-for="item of moduleInfo" :class="[item.module_id === 15 ? 'showFalse' : '']"
          :key="item.module_id" :label="item.module_id" @change="clearSelect(null)">{{ item.module_name }}</el-radio>
      </el-form-item>
      <!-- 一级分类 -->
      <el-form-item v-if="classify_field && categoryLevel1.length > 0" :label="classify_field[0].text"
        style="width: 100%">
        <el-radio v-model="classifySelect[classify_field[0].field]" @change="clearSelect(0)"
          :label="undefined">全部</el-radio>
        <el-radio v-model="classifySelect[classify_field[0].field]" @change="clearSelect(0)"
          v-for="item of categoryLevel1" :key="item.item_id" :label="item.item_id">{{ item.item_name }}</el-radio>
      </el-form-item>
      <!-- 二级分类 -->
      <el-form-item v-if="classify_field && categoryLevel2.length > 0" :label="classify_field[1].text"
        style="width: 100%">
        <el-radio v-model="classifySelect[classify_field[1].field]" @change="clearSelect(1)"
          :label="undefined">全部</el-radio>
        <el-radio v-model="classifySelect[classify_field[1].field]" @change="clearSelect(1)"
          v-for="item of categoryLevel2" :key="item.item_id" :label="item.item_id">{{ item.item_name }}</el-radio>
      </el-form-item>
      <!-- 三级分类 -->
      <el-form-item v-if="classify_field && categoryLevel3.length > 0" :label="classify_field[2].text"
        style="width: 100%">
        <el-radio v-model="classifySelect[classify_field[2].field]" :label="undefined" @change="clearSelect(null)">全部</el-radio>
        <el-radio v-model="classifySelect[classify_field[2].field]" v-for="item of categoryLevel3" :key="item.item_id"
          :label="item.item_id" @change="clearSelect(null)">{{ item.item_name }}</el-radio>
      </el-form-item>

      <el-form-item label="排序维度" style="width: 100%" v-if="page === 'automatic'">
        <el-radio v-model="value.sortBy" :label="undefined">全部</el-radio>
        <el-radio v-model="value.sortBy" v-for="(item, index) of currentSortOptions" :key="index" :label="item.value">{{
      item.label }}</el-radio>
      </el-form-item>
      <slot></slot>
    </el-form>

    <!-- 搜索，重置 -->
    <el-form inline label-width="90px" class="mt-8">
      <el-form-item label="认证等级" style="margin-top: 16px;">
        <div class="j-space_between">
          <div class="">
            <el-select v-model="value.courseLevel" placeholder="请选择" size="small">
              <el-option
                v-for="item in certificationLevelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="">
            <el-button @click="onReset()" class="reset-btn" size="small">
              <i class="el-icon-refresh"></i><span>重置</span>
            </el-button>
            <el-button type="primary" @click="onSearch()" size="small">搜索</el-button>
          </div>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getModuleInfoApi, getNetClassifyApi } from '@/config/mooc.api.conf'

export default {
  props: {
    value: {
      type: Object,
      default() {
        return {}
      }
    },
    inline: {
      type: Boolean,
      default: false
    },
    page: {
      type: String
    }
  },
  data() {
    return {
      moduleInfo: [],
      netClassify: [],
      // 分类选项数据
      classifySelect: {},
      // 以module_id区分不同排序维度
      sortOptions: {
        default: [
          { label: '最新', value: 'created_at' }
        ],
        1: [
          { label: '热门', value: 'view_count_total' },
          { label: '最新', value: 'created_at' },
          { label: '评分', value: 'score' }
        ],
        2: [
          { label: '热门', value: 'view_count_total' },
          { label: '评分', value: 'score' }
        ],
        4: [
          { label: '热门', value: 'view_count_total' },
          { label: '最新', value: 'created_at' }
        ],
        6: [
          { label: '热门', value: 'view_count_total' },
          { label: '评分', value: 'score' }
        ],
        7: [
          { label: '热门', value: 'view_count_total' },
          { label: '最新', value: 'created_at' }
        ],
        8: [
          { label: '热门', value: 'view_count_total' },
          { label: '最新', value: 'created_at' }
        ]
      },
      certificationLevelOptions: [
        { label: '全部', value: '' },
        { label: '公司级', value: 1 },
        { label: 'BG级', value: 2 },
        { label: '部门级', value: 3 },
        { label: '个人分享', value: 4 }
      ]
    }
  },
  computed: {
    // moduleInfo() {
    //   // 本站内容搜索和规则配置不需要moduleId 99 外部链接模块
    //   let result = []
    //   for (let item of this.$store.state.qlmanage.moduleInfo) {
    //     if (item.module_id === 99) continue
    //     result.push(item)
    //   }
    //   return result
    // },
    // netClassify() {
    //   return this.$store.state.qlmanage.netClassify
    // },
    currentModuleInfoItem() {
      return this.moduleInfo.find(i => i.module_id === this.value.moduleId) || null
    },
    // 模块下的分类配置
    classify_field() {
      if (!this.currentModuleInfoItem || !this.currentModuleInfoItem.classify_field) {
        return null
      }
      return this.currentModuleInfoItem.classify_field
    },
    // 一级分类选项
    categoryLevel1() {
      if (this.value.moduleId === 1) {
        return this.netClassify
      } else if (this.moduleInfo.length !== 0 && this.value.moduleId) {
        return this.currentModuleInfoItem.classify_data || []
      }
      return []
    },
    // 二级分类选项
    categoryLevel2() {
      let fieldValue = this.classifySelect[this.classify_field[0].field]
      if (this.categoryLevel1.length > 0 && fieldValue) {
        let item = this.categoryLevel1.find(i => i.item_id === fieldValue)
        return (item && item.child) ? item.child : []
      }
      return []
    },
    // 三级分类选项
    categoryLevel3() {
      let fieldValue = this.classifySelect[this.classify_field[1].field]
      if (this.categoryLevel2.length > 0 && fieldValue) {
        let item = this.categoryLevel2.find(i => i.item_id === fieldValue)
        return (item && item.child) ? item.child : []
      }
      return []
    },
    currentSortOptions() {
      let options = this.sortOptions[this.value.moduleId]
      if (options) {
        return options
      }
      return this.sortOptions.default
    }
  },
  watch: {
    'value.moduleId': {
      immediate: true,
      handler(val) {
        this.value.classify = []
        if (!this.classify_field) {
          return
        }

        let classifySelect = {}
        for (let item of this.classify_field) {
          if (item.field) {
            classifySelect[item.field] = this.classifySelect[item.field] || undefined
          }
        }
        this.classifySelect = classifySelect
      }
    },
    classifySelect: {
      deep: true,
      handler() {
        let classify = []
        for (let field of Object.keys(this.classifySelect)) {
          if (this.classifySelect[field]) {
            classify.push({ field, id: this.classifySelect[field] })
          }
        }
        this.value.classify = classify
      }
    }
  },
  created() {
    this.getModuleInfo()
    this.getNetClassify()

    // 设置默认值
    // 模块默认全部
    if (!this.value.moduleId) {
      this.$set(this.value, 'moduleId', undefined)
    }
    // 排序默认最新，并且页面上不可选择
    if (this.page !== 'automatic') {
      this.$set(this.value, 'sortBy', 'created_at')
    }
    if (this.value.classify) {
      for (let item of this.value.classify) {
        this.classifySelect[item.field] = item.id
      }
    }
  },
  methods: {
    getModuleInfo() {
      getModuleInfoApi().then(res => {
        let moduleArr = [] 
        let result = []
        // 返回的moduleInfo是个对象，需要转换成数组
        for (let key of Object.keys(res.moduleInfo)) {
          // 0是综合，综合不需要作为选项，过滤掉
          if (key === '0') continue
          moduleArr.push(res.moduleInfo[key])
        }
        for (let item of moduleArr) {
          if (item.module_id !== 99) {
            result.push(item)
          }
        }
        this.moduleInfo = result
      })
    },
    getNetClassify() {
      getNetClassifyApi().then(res => {
        this.netClassify = res.data
      })
    },
    clearSelect(level) {
      this.$emit('chooseChange')
      if (level === null) return

      // 选择上级分类后需要把下级分类的选项清空，否则搜索会出错
      if (level === 0) {
        if (this.classify_field[1]) {
          this.classifySelect[this.classify_field[1].field] = undefined
        }
        if (this.classify_field[2]) {
          this.classifySelect[this.classify_field[2].field] = undefined
        }
      } else if (level === 1) {
        if (this.classify_field[2]) {
          this.classifySelect[this.classify_field[2].field] = undefined
        }
      }
    },
    onSearch() {
      this.$emit('onSearch')
    },
    onReset() {
      this.$emit('onReset')
    }
  }
}
</script>

<style lang="less" scoped>
.content-search-form {
  width: 100%;

  >.el-form-item:last-child {
    margin-bottom: 0 !important;
  }

  &.inline {
    .el-form-item {
      display: inline-flex;
      ::v-deep .el-form-item__label {
        flex-shrink: 0;
      }
    }

    .el-input {
      width: 200px;
    }
  }

  .showFalse {
    display: none;
  }
  .el-form-item ::v-deep .el-form-item__label {
    flex-shrink: 0;
  }

  .w-inherit {
    width: 100%;
    ::v-deep .el-select__tags {
      max-width: 100%;
    }
  }
}
.j-space_between {
  width: 720px;
  display: flex;
  justify-content: space-between;
  & /deep/ .el-button {
    min-width: 80px;
  }
}
</style>
