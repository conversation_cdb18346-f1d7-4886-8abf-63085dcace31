<template>
<div class="scorm-component" id="scorm-component">
   <iframe v-show="file_url" :src="file_url" width="100%" height="100%"  scrolling="no" id="iframe_show" allowfullscreen="true"></iframe>
   <img
      v-show="!file_url"
      :src="require('@/assets/img/default_bg_img.png')"
      class="image-default" />
    <Watermark
    ref="watermark"
    v-if="watermark.textContent"
    :targetId="watermark.targetId"
    :text="watermark.textContent"
    :canvasUserOptions="watermark.canvasUserOpt"
    :wmUserOptions="watermark.wmUserOpt"
    :isManualInit="false"
  />
</div>
</template>
<script>
import '@/utils/jq/jquery.scormAPI.js'
import { operatesignature, getContentInfo, getMobileContentInfo } from 'config/api.conf'
import Watermark from '@/components/watermark.vue'
import MoocJs from 'sdc-moocjs-integrator'
export default {
  components: {
    Watermark
  },
  props: {
    app_id: {
      type: String,
      default: 'QLearningService'
    },
    corp_name: {
      type: String,
      default: 'tencent'
    },
    courseData: {
      type: Object
    },
    scormType: {
      type: String
    }
  },
  data() {
    return {
      file_url: '',
      scormDomId: 'scorm-box-' + Math.random().toString().slice(-6),
      watermark: {
        targetId: 'scorm-component', // 水印目标元素id
        textContent: '',
        canvasUserOpt: {
          width: 200,
          height: 160,
          fillStyle: 'rgba(153, 153, 153, 0.1)',
          font: '20px Microsoft Yahei',
          rotateDegree: 39,
          translate: {
            x: 0,
            y: 0
          }
        },
        wmUserOpt: {
          'z-index': 99999
        }
      },
      copyWatermark: null,
      scormData: {
        act_id: 0,
        is_finish: 0,
        data_model: '',
        scorm_item_id: 0,
        total_study_time: 0
      },
      time: '',
      scormInit: false
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          this.watermark.textContent = val.staff_name
        }
      },
      immediate: true
    }
  },
  beforeCreate() {
    if (window.location.host.indexOf('tencent.com') !== -1) {
      document.domain = 'tencent.com'
    } else if (location.host.indexOf('.woa.com') > -1) {
      document.domain = 'woa.com'
    } else {
      document.domain = 'oa.com'
    }
  },
  created() {
    if (this.courseData.file_url) {
      this.file_url = this.courseData.file_url
      this.$nextTick(() => {
        this.initScorm()
      })
    } else if (this.courseData.content_id) {
      this.getOperatesignature()
    }
  },
  mounted() {
    this.$nextTick(() => {
      let iframeDOM = document.getElementById('iframe_show')
      iframeDOM.onload = () => {
        this.$nextTick(() => {
          !this.scormInit && this.loadDatamodel()
        })
      }
    })
  },
  methods: {
    getOperatesignature() {
      const signatureParams = {
        app_id: this.app_id,
        content_id: this.courseData.content_id,
        corp_name: this.corp_name,
        operate: 'visit'
      }
      operatesignature(signatureParams).then((signature) => {
        if (signature) this.getVideoFileInfo(signature)
      })
    },
    getVideoFileInfo(signature) {
      const params = {
        signature,
        app_id: this.app_id,
        show_water_word: false // 去掉文档水印
      }
      const api = window.location.pathname.includes('mobile') ? getMobileContentInfo : getContentInfo
      api(this.courseData.content_id, params).then((data) => {
        if (data.file_info) {
          if (data.content_type === 'doc') {
            this.file_url = data.file_info.doc_url
          } else {
            this.file_url = data.file_info.file_url
          }
          this.initScorm()
        }
      })
    },
    initScorm() {
      this.initWaterMark()
      this.handleScormFun()
    },
    saveRecord() {
      this.scormData.total_study_time = this.scormData.total_study_time + 15
      
      this.scormData.from = this.$route.query.jump_from || this.$route.query.from || ''
      this.scormData.area_id = this.$route.query.area_id || ''
      this.$emit('handleScormRecord', this.scormData)
      // 观看时间如果大于课时加20分钟就不再调学习记录接口并关闭定时器
      if ((this.scormData.total_study_time / 60) >= ((this.courseData.est_dur || 0) + 30)) {
        clearInterval(this.perTime)
        this.perTime = null
      }
    },
    uploadPerTime() {
      // 启动定时器，定时上传用户的学习记录
      if (this.perTime) clearInterval(this.perTime)
      this.scormData.data_model = window.$.fn.scormAPI.getScormModelString()
      this.scormData.is_finish = 0
      this.perTime = setInterval(() => {
        this.saveRecord()
      }, 15000)
    },
    loadDatamodel() {
      // 加载scorm数据源，并调用计时器函数
      let dataModel = this.courseData.scorm_data_model
      if (dataModel && dataModel !== '' && dataModel !== '""') {
        window.$.fn.scormAPI.setScormModelString(dataModel)
      }
      // this.startCountTime()
      this.uploadPerTime()
    },
    checkFinish() {
      let data = window.$.fn.scormAPI.getScormModelString()
      let model = JSON.parse(data)
      // 检查数据格式，若数据格式不对，此时放宽验证，返回true
      if (!model || model === '' || !model.cmi || !model.cmi.core) {
        return true
      }
      // 非scorm课件都返回true
      if (!model.cmi.core.lesson_location || model.cmi.core.lesson_location === '') {
        return true
      }                
      if (model.cmi.core.lesson_status === 'completed' || model.cmi.core.lesson_status === 'passed') {
        return true
      }
      const est_dur = this.courseData.est_dur || 0
      if (est_dur - (this.scormData.total_study_time / 60) <= 3) {
        return true
      }
      return false
    },
    initWaterMark() {
      this.$refs.watermark.init()
      // this.$nextTick(() => {
      //   let targetDom = document.getElementById(_this.watermark.targetId + '_watermark_xx512')
      //   let copyDomeId = _this.watermark.targetId + '_watermark_xx513'
      //   if (_this.copyWatermark == null) {
      //     _this.copyWatermark = targetDom.cloneNode(true)
      //     _this.copyWatermark.id = copyDomeId
      //     _this.copyWatermark.style.position = 'absolute'
      //     _this.copyWatermark.style.width = '100%'
      //     _this.copyWatermark.style.height = '100%'
      //     _this.copyWatermark.style.top = '0'
      //     let videoBox = document.querySelector(`#${this.scormDomId} video`)
      //     videoBox.parentNode.insertBefore(_this.copyWatermark, videoBox)
      //   }
      //   if (
      //     document
      //       .querySelector(`#${this.scormDomId} video`)
      //       .getAttribute('id')
      //       .indexOf('html5_api') === -1
      //   ) {
      //     document.getElementById(copyDomeId).style.display = 'none'
      //   }
      // })
    },
    handleScormFun() {
      /* eslint-disable*/
      let _this = this
      if (['mooc', 'spoc'].includes(this.$route.query.from)) {
        MoocJs.postMessage('scormInit')
      }
      window.$.fn.scormAPI({
        init: function () {
          _this.loadDatamodel()
          _this.scormInit = true
          // if (['mooc', 'spoc'].includes(_this.$route.query.from)) {
          //   MoocJs.postMessage('scormInit')
          // }
          return 'true'
        },
        finish: function () {
          if (_this.checkFinish()) {
              _this.scormData.data_model = window.$.fn.scormAPI.getScormModelString()
              _this.scormData.is_finish = 1
              _this.saveRecord()
              return 'true'
          }
          return 'false'
        }
      })
    }
  },
  beforeDestroy() {
    window.$.fn = null
    clearInterval(this.perTime)
    this.perTime = null
  }
}
</script>
<style lang="less" scoped>
.scorm-component {
  position: relative;
  border-radius: 3px;
  border: 1px solid #ececec;
}
iframe{
  border: none;
}
.image-default{
  width: 100%;
  height: 100%;
}
</style>
