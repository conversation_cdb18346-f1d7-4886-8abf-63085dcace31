<template>
  <div class="task-details">
    <el-form ref="form" :model="form" inline>
      <el-form-item label="名称">
        <el-input v-model="form.taskName" placeholder="请输入任务名称" size="small" clearable></el-input>
      </el-form-item>
      <el-form-item label="类型">
        <el-select v-model="form.taskType" placeholder="请选择任务类型" size="small">
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="性质">
        <el-select v-model="form.taskProperty" placeholder="请选择任务性质" size="small">
          <el-option
            v-for="item in propertyOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="onSearch(1)">搜索</el-button>
        <el-button size="small" @click="reset">重置</el-button>
        <el-tooltip :disabled="!exportDisabled" effect="dark" content="当前是系统使用高峰，暂不支持数据导出功能，有需要请联系graywu" placement="top">
          <span style="margin-left: 10px">
            <el-button @click="exportData" size='small' :disabled="exportDisabled || isApprove">导出</el-button>
          </span>
        </el-tooltip>
      </el-form-item>
    </el-form>
    <el-table :data="tableData.records" header-row-class-name="table-header-style">
      <el-table-column prop="task_name" label="任务名称" min-width="320"></el-table-column>
      <el-table-column prop="resource_type_name" label="类型" min-width="120"></el-table-column>
      <el-table-column prop="required" label="性质" min-width="120">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.required" type="warning" size="small">应学</el-tag>
          <el-tag v-else type="success" size="mini">选学</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="percentage" label="完成进度" sortable min-width="260">
        <template slot-scope="scope">
          <div class="finish-progress p-flex">
            <div class="p-1">
              <div class="p-row-between">
                <span>{{ `${scope.row.finished_students_count} / ${scope.row.sum}` }}人</span>
                <span>{{ scope.row.percentage || 0 }}%</span>
              </div>
              <el-popover
                popper-class="task-popover-box"
                placement="bottom"
                trigger="hover"
                :visible-arrow="false">
                  <div class="tooltip">
                    <p>已完成：{{ scope.row.finished_students_count }}</p>
                    <p>进行中：{{ scope.row.learning_students_count }}</p>
                    <p>未开始：{{ scope.row.not_started_students_count }}</p>
                  </div>
                <el-progress slot="reference" :percentage="scope.row.percentage" define-back-color="#E8E8E8" :color="scope.row.percentage >= 100 ? '#48C79C' : '#266FE8'" :show-text="false"></el-progress>
              </el-popover>
            </div>
            <el-popover
              popper-class="task-popover-box"
              placement="bottom"
              trigger="hover"
              :visible-arrow="false">
                <div class="tooltip">
                  <p>已完成：{{ scope.row.finished_students_count }}</p>
                  <p>进行中：{{ scope.row.learning_students_count }}</p>
                  <p>未开始：{{ scope.row.not_started_students_count }}</p>
                </div>
                <img slot="reference" class="info-circle" src="@/assets/mooc-img/info-circle.png" alt="" srcset="">
            </el-popover>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="total_study_time" label="总学习时长（分钟）" min-width="160">
        <template slot-scope="scope">{{ scope.row.total_study_time || '-' }}</template>
      </el-table-column>
      <el-table-column prop="average_study_time" label="人均学习时长（分钟）" min-width="180">
        <template slot-scope="scope">{{ scope.row.average_study_time || '-' }}</template>
      </el-table-column>
      <el-table-column label="操作" min-width="160" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="lookDetails(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="current"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="size"
      layout="total,  prev, pager, next, sizes, jumper"
      :total="tableData.total">
    </el-pagination>

    <!-- 详情弹窗 -->
    <task-details-popup
      v-if="isShowDetailsPopup"
      :isShowDetailsPopup.sync="isShowDetailsPopup"
      :details-info="currentDetails">
    </task-details-popup>
  </div>
</template>

<script>
import TaskDetailsPopup from './component/task-details-popup.vue'
import pagination from '@/mixins/pager'
import { getSummaryTaskListAPI, getSummaryTaskListExportAPI } from '@/config/mooc.api.conf.js'
import { isSysBusy } from '@/config/api.conf'
import { mapState } from 'vuex'
export default {
  components: {
    TaskDetailsPopup
  },
  mixins: [pagination],
  data () {
    return {
      form: {
        taskName: '',
        createTime: '',
        taskType: '',
        taskProperty: ''
      },
      typeOptions: [
        { label: '全部', value: '' },
        { label: '视频', value: 'Video' },
        { label: '音频', value: 'Audio' },
        { label: '图文', value: 'Article' },
        { label: '文档', value: 'Doc' },
        { label: 'scrom', value: 'Scorm' },
        { label: '压缩包', value: 'Zip' },
        { label: '考试', value: 'Exam' },
        { label: '练习', value: 'Practice' },
        { label: '外链', value: 'Other' },
        { label: '作业', value: 'HomeWork' }
      ],
      propertyOptions: [
        { label: '全部', value: '' },
        { label: '应学', value: true },
        { label: '选学', value: false }
      ],
      tableData: {
        records: [],
        total: 0
      },
      // 详情弹窗
      isShowDetailsPopup: false,
      currentDetails: null,
      exportDisabled: false
    }
  },
  computed: {
    ...mapState(['projectManageInfo']),
    isApprove() {
      // 审批-审批中
      return this.$route.query.approve === '1' || this.projectManageInfo.course_status === 6
    }
  },
  created () {
    this.onSearch(1)
    this.getSysBusyStatus()
  },
  mounted() {
    this.$nextTick(() => {
      this.getTableHeight()
    })
  },
  methods: {
    onSearch (page_no = 1) {
      this.current = page_no
      getSummaryTaskListAPI({
        course_id: this.$route.query.mooc_course_id,
        task_name: this.form.taskName,
        resource_type: this.form.taskType,
        required: this.form.taskProperty,
        page_no,
        page_size: this.size
      }).then(res => {
        if (res.records.length) {
          res.records.map(v => {
            const obj = this.handlePercentageVal(v)
            v.percentage = obj.percentage
            v.sum = obj.sum
          })
        }
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    reset () {
      this.form.taskName = ''
      this.form.taskType = ''
      this.form.taskProperty = ''
      this.onSearch(1)
    },
    getSysBusyStatus() {
      isSysBusy().then(res => {
        if (res === '1') {
          this.exportDisabled = true
        } else {
          this.exportDisabled = false
        }
      })
    },
    // 导出
    exportData () {
      const id = this.$route.query.mooc_course_id
      getSummaryTaskListExportAPI({
        course_id: id,
        task_name: this.form.taskName,
        resource_type: this.form.taskType,
        required: this.form.taskProperty
      })
    },
    // 查看详情
    lookDetails (info) {
      this.isShowDetailsPopup = true
      this.currentDetails = info
    },
    handlePercentageVal({ finished_students_count, learning_students_count, not_started_students_count }) {
      const sum = finished_students_count + learning_students_count + not_started_students_count
      return {
        percentage: Number((finished_students_count / sum * 100).toFixed(2)),
        sum
      }
    },
    getTableHeight () {
      let wrapHeight = document.getElementsByClassName('el-tabs__content')[0].offsetHeight
      const searchHeight = document.getElementsByClassName('el-form')[0].offsetHeight
      const paginationHeight = document.getElementsByClassName('el-pagination')[0]?.offsetHeight
      this.tableHeight = wrapHeight - 40 - searchHeight - paginationHeight - 20 - 10
    }
  }
}
</script>

<style lang="less" scoped>
  .task-details {
    :deep(.el-form-item) {
      margin-right: 30px;
      .el-input__inner {
        width: 220px;
      }
    }
    :deep(.el-tag) {
      width: 32px;
      height: 18px;
      line-height: 18px;
      font-size: 12px;
      font-weight: 400;
      padding: 0 4px;
      text-align: center;
      border-radius: 2px;
    }
    :deep(.el-tag.el-tag--warning) {
      background: rgb(253, 246, 236);
      color: rgba(255, 117, 72, 1);
    }
    :deep(.el-tag.el-tag--success) {
      background: rgb(204, 242, 226);
      color: rgb(0, 179, 104);
    }
    .p-row-between {
      display: flex;
      justify-content: space-between;
      align-content: center;
    }
    .p-flex {
      display: flex;
      align-items: flex-end;
    }
    .p-1 {
      flex: 1;
      margin-right: 10px;
    }
  }
  :deep(.el-popover.el-popper) {
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.9);
    padding: 5px 8px;
  }
  .task-popover-box {
    border-radius: 3px;
    background: rgba(0, 0, 0, 0.9);
    padding: 5px 8px;
  }
  .info-circle {
    width: 14px;
    height: 14px;
    cursor: pointer;
  }
  .finish-progress {
    padding-right: 12%;
  }
  :deep(.el-table) {
   .el-table__header-wrapper table, 
    .el-table__body-wrapper table {
      width: max-content !important;
    }
  }
</style> 
<style lang="less">
.task-popover-box {
  min-width: 50px;
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.9);
  padding: 5px 8px;
  font-size: 12px;
  font-weight: 400;
  line-height: 12px;
  color: #FFFFFF;
  .tooltip{
    p {
      padding-left: 11px;
      margin-bottom: 8px;
      position: relative;
      &:nth-last-child(1) {
        margin: 0;
      }
      &::before {
        content: '';
        width: 6px;
        height: 6px;
        border-radius: 50%;
        position: absolute;
        top: 3px;
        left: 0;
      }
      &:nth-child(1)::before {
        background: #48C79C;
      }
      &:nth-child(2)::before {
        background: #266FE8;
      }
      &:nth-child(3)::before {
        background: #FF9649;
      }
    }
  }
}
</style>
