<template>
  <div class="project-create-component">
    <el-form :model="form" :rules="rules" ref="form" label-width="146px">
      <convention-banner />
      <div class="base-info">
        <div class="bassinfo-class-box">
          <div class="title-box">
            <img src="@/assets/mooc-img/title-icon.png" />
            <span class="bassinfo-class-title">基本信息</span>
          </div>
        </div>
        <el-form-item label="项目名称" prop="course_title" class="input-style">
          <el-input 
            class="course-name-input" 
            v-model="form.course_title" 
            placeholder="请输入项目名称"
            clearable>
          </el-input>
          <span class="custom-el-input-count" style="top: 6px; bottom: auto;">{{handleValidor(form.course_title, 50, '1')}}/50</span>
          <p class="test-tips"><i class="el-icon-warning color-red mgr-5"></i>若需<span class="color-red">「课程测试」</span>体验效果，请先<span class="color-red text-undeline" @click="toInstructionsPage">查看填写说明</span>，以免耽误进度哦！</p>
        </el-form-item>
        <el-form-item label="运营标题" class="input-style">
          <el-input 
            class="course-name-input" 
            v-model.trim="form.course_statement.operation_title" 
            placeholder="请填写项目的运营标题，可为空"
            clearable>
          </el-input>
          <span class="custom-el-input-count">{{handleValidor(form.course_statement.operation_title, 50, '2')}}/50</span>
        </el-form-item>
        <el-form-item label="项目时间">
          <span class="warining-time-tips">请注意，项目发布后无法修改类型</span>
          <div class="project-time-box">
            <el-radio 
            :disabled="projectManageInfo.course_status === 1 || projectManageInfo.approve_status === 7" 
            v-model="form.course_period_type" 
            :label="1"
            @change="timeRadioChange('1')"
            >
            限定项目学习起止时间
            </el-radio>
            <span class="project-time-tips">所有学员的学习起止时间相同，到达项目开始时间后可进行学习，到达项目结束时间后无法继续学习。</span>
            <el-form-item>
              <div class="start-time-box">
                <span class="label">起止时间</span>
                <el-date-picker 
                v-model="form.studyTime"
                :disabled="form.course_period_type !== 1"
                type="datetimerange" 
                value-format="yyyy-MM-dd HH:mm:ss"
                :default-time="['00:00:00', '23:59:59']" 
                range-separator="至" 
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                >
                </el-date-picker>
              </div>
            </el-form-item>
          </div>
          <div class="project-time-box">
            <el-radio v-model="form.course_period_type" :disabled="true" :label="2">限定项目学习周期</el-radio>
            <span class="project-time-tips">项目发布后长期有效，每个学员加入项目后具备独立的学习起止时间，需要在指定天数内完成任务，逾期无法继续学习。</span>
          </div>
          <div class="project-time-box">
            <el-radio 
            :disabled="projectManageInfo.course_status === 1 || projectManageInfo.approve_status === 7" 
            v-model="form.course_period_type" 
            :label="3"
            @change="timeRadioChange('3')"
            >
            不限定项目学习时间
            </el-radio>
            <span class="project-time-tips">项目发布后，学员可在任意时间参与项目进行学习</span>
          </div>
        </el-form-item>
        <el-form-item label="封面图" prop="cover_image">
          <cut-img-upload 
          ref="upload" 
          @handleSuccess="handleSuccessImage"
          :dialogImageUrl="dialogImageUrl" 
          :autoImgUrl="autoImgUrl" 
          @handleClearImg="handleClearImg"
          @handleImgEdit="handleImgEdit"
          :cover_imgage_storage_type="form.cover_image_storage_type"
          >
            <template v-slot:text>
              <!-- <p>建议图片尺寸：375*250px或3:2宽高比</p> -->
              <p>建议图片尺寸：480*320px或3:2宽高比</p>
            </template>
            <template v-slot:createImg>
              <p class="text-orange" style="color: #E37318;cursor: pointer;display: flex; align-items: center" @click="handleAutoImg"><img class="icon" style="width:16px; height: 16px; margin-right: 4px" src="~@/assets/img/tips.png" alt="">快速生成封面图</p>
            </template>
          </cut-img-upload>
        </el-form-item>
        <el-form-item label="项目简介" class="course-texTarea-input">
          <el-input
            :autosize='{ minRows: 5, maxRows: 6}' 
            resize="none"
            type="textarea"
            v-model="form.course_desc" 
            placeholder="请输入项目简介" 
            size="small"
            clearable
          />
          <span class="custom-el-input-count">{{handleValidor(form.course_desc, 500, '3')}}/500</span>
        </el-form-item>
        <el-form-item label="项目详情" class="project-detail-tincy">
          <sdc-mce-editor 
          ref="editor" 
          :env="editorEnv" 
          :content="form.course_desc_detail"
          :catalogue.sync="editorConfig.catalogue"
          :urlConfig="editorConfig.urlConfig"
          :options="editorConfig.options"
          :insertItems="insertItems"
           />
        </el-form-item>
        <el-form-item label="项目标签" prop="course_labels">
          <sdc-addlabel 
          v-model="form.course_labels" 
          class="project-tag-box" 
          :recommend="{
            title: this.form.course_title,
            desc: this.form.course_desc
          }" 
          :labelNodeEnv="labelNodeEnv"
          @getSelectedLabelList="getSelectedLabelList" 
          />
        </el-form-item>
      </div>
      <div class="base-set">
        <div class="title-box">
          <img src="@/assets/mooc-img/title-icon.png" />
          <span class="bassinfo-class-title">基础设置</span>
        </div>
        <!-- <el-form-item label="项目级别" prop="course_level">
          <el-radio-group v-model="form.course_level">
            <el-radio v-for="item in levelList" :key="item.code" :label="item.code" :disabled="item.disabled">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="内容的管理组织" prop="dept_id">
          <div class="content-origaniztion">
            <sdc-unit-selector 
            class="dep-selector" 
            ref="deptSelectorRef" 
            v-model="form.dept_id"
            :disabled="specialFileDisabled"
            :props="deptProps" 
            @change="changeDept"
            placeholder="请选择项目内容的管理组织"
            />
            <span class="tip-warm">请选择负责项目运营/管理的组织，只能选择一个组织</span>
          </div>
          <div class="contact-person" v-if="specialFileDisabled">本字段若需修改，请联系graywu。</div>
          <!-- <span class="warning-tips" v-if="UnitSortID !== 6 && form.course_level === '2'">
            <i class="el-icon-warning-outline"></i>
            当前为BG级项目，请选择BG单元组织
          </span>
          <span class="warning-tips" v-else-if="UnitSortID !== 1 && form.course_level === '3'">
            <i class="el-icon-warning-outline"></i>
            当前为部门级项目，请选择部门单元组织
          </span> -->
        </el-form-item>
        <el-form-item label="管理员" prop="course_admins">
          <sdc-staff-selector 
          multiple 
          ref="adminsSelectorRef" 
          v-model="form.course_admins"
          size="small" 
          :props="adminProps" 
          placeholder="请选择项目管理员"
          @change="changeCourseAuth"
          />
        </el-form-item>
        <!-- <div class="submit-must-tips">
          <span class="submit-label">{{pageEntryType === 'baseSet' ? '提交前必检查' : '提交前必填'}}</span>
          <div>
            <el-link type="primary" href="https://doc.weixin.qq.com/sheet/e3_AFkAXgbdAFwlEq9ShItSTG8r20W9G?scode=AJEAIQdfAAo4QV7z7DAFkAXgbdAFw&tab=BB08J2" target="_blank">《请点击此打开问卷，补充填写相关统计字段》</el-link>
            <div class="tips">
              <p>说明：</p>
              <p>1、此问卷补充的字段信息，将用于新报表统计汇总，方便各级管理员随时统计使用。</p>
              <p>2、为保障统计准确，<span class="red-tips">请大家务必及时、准确无误填写。</span>（若有需要，请在各内容重编辑页修改。）</p> 
              <p>3、由于QL系统底层数据重构中，在功能上线前，将临时采用问卷方式进行信息收集；功能上线时便可统一入库，避免各管理员后续的二次返工补充。</p> 
            </div>
          </div>
        </div> -->
        <el-form-item label="展现形式">
          <el-radio-group v-model="form.study_type">
            <el-radio :label="1">列表样式</el-radio>
            <el-radio :label="2" disabled>地图样式</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="连载状态">
          <el-radio-group v-model="form.serial_type" @change="changeSerialType">
            <el-radio :label="1">非任务连载项目</el-radio>
            <el-radio class="is-update-status " :label="2">连载项目，任务持续更新中 <span class="project-time-tips">连载项目无法设置证书激励</span></el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="证书颁发" v-show="form.serial_type === 1">
          <div class="certify-switch-box">
            <el-switch :disabled="!userLimitInfo.supper_admin" v-model="form.enable_grant_certificate"></el-switch>
            <span class="project-time-tips">当学员完成培养项目时，系统将自动为其颁发证书；如调整证书设置，不会影响已获得的学员；请联系超级管理员graywu进行设置</span>
          </div>
          <div v-show="form.enable_grant_certificate" class="certificate-box">
            <span v-if="form.certificate_name" class="item-name"><span>{{ form.certificate_name }}</span></span>
            <el-button :disabled="!userLimitInfo.supper_admin" size="medium" @click="certificateDialog = true" class="certify-btn">选择证书</el-button>
          </div>
        </el-form-item>
        <el-form-item label="记录同步" class="records-sync-box">
          <el-switch :disabled="form.enable_study_record_sync" v-model="form.enable_study_record_sync" :active-color="userLimitInfo.supper_admin ? '#0052D9' : ''" :inactive-color="userLimitInfo.supper_admin ? '#dcdfe6' : ''"></el-switch>
          <span class="project-time-tips">开启后，如果学员在QL平台通过其他途径完成过相同课程或考试，结果记录将会自动同步至此项目对应的任务中</span>
        </el-form-item>
        <!-- 2024-3-22新增 -->
        <el-form-item label="HR助手通知" class="records-sync-box">
          <el-switch :disabled="!userLimitInfo.supper_admin" v-model="form.enable_hr_assistant"></el-switch>
          <span class="project-time-tips">开启后，可使用HR助手消息渠道进行加入提醒或催办，请联系超级管理员graywu进行设置</span>
        </el-form-item>
        <el-form-item label="企微机器人通知" class="records-sync-box">
          <el-switch :disabled="!userLimitInfo.supper_admin" v-model="form.enable_bot"></el-switch>
          <span class="project-time-tips">开启后，可使用企微机器人消息渠道进行加入提醒或催办，请联系超级管理员graywu进行设置</span>
        </el-form-item>

        <el-form-item label="催办抄送HRBP" class="records-sync-box">
          <el-switch :disabled="!userLimitInfo.supper_admin" v-model="form.enable_copy_bp"></el-switch>
          <span class="project-time-tips">开启后，项目管理员可以选择在设定的催办时间点给组织BP发送未完成学员的汇总信息邮件提醒；请联系超级管理员graywu进行设置</span>
        </el-form-item>
        <el-form-item label="MyOA催办" class="records-sync-box">
          <el-switch :disabled="!userLimitInfo.supper_admin" v-model="form.enable_myoa"></el-switch>
          <span class="project-time-tips">开启后，项目管理员可以通过MyOA渠道催办未完成的学员；请联系超级管理员graywu进行设置</span>
        </el-form-item>
        
        <!-- 2024-3-22新增 --> 
        <el-form-item label="催办抄送直接上级" class="records-sync-box">
          <!-- <el-switch :disabled="!userLimitInfo.supper_admin" v-model="form.enable_copy_leader" active-color="#0052D9" inactive-color="#dcdfe6"></el-switch> -->
          <el-switch :disabled="!userLimitInfo.supper_admin" v-model="form.enable_copy_leader"></el-switch>
          <span class="project-time-tips">开启后，可以选择在设定的催办时间点给直接上级发送未完成学员的汇总信息邮件提醒，请联系超级管理员graywu进行设置</span>
        </el-form-item>

        <el-form-item label="显示项目人数" class="records-sync-box">
          <el-switch v-model="form.show_join_count"></el-switch>
          <span class="project-time-tips">开启后，项目给用户展示的信息中会显"xx人参与学习"</span>
        </el-form-item>

        <el-form-item label="是否需要购买" class="records-sync-box" v-if="form.resource_from">
          <el-switch v-model="form.acquisition_type_flag"></el-switch>
          <span class="project-time-tips">开启后，项目需要使用对应类型学霸卡兑换</span>
        </el-form-item>
      </div>

      <div class="other-info">
        <div class="title-box">
          <img src="@/assets/mooc-img/title-icon.png" />
          <span class="bassinfo-class-title">其他内容信息</span>
        </div>
        <el-form-item label="父内容ID">
          <div class="parent-content-id">
            <div class="content-type">
              <el-select v-model="form.course_statement.parent_content_act_type" placeholder="请选择内容类型" clearable>
                <el-option
                  v-for="item in parent_content_act_type_options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
              <span class="error-tip-text-customer" v-show="showParentId && !form.course_statement.parent_content_act_type">请选择内容类型</span>
            </div>
            <div class="content-id">
              <el-input v-model.trim="form.course_statement.parent_content_id" placeholder="请输入内容ID" clearable></el-input>
              <span class="error-tip-text-customer" v-show="showParentId && !form.course_statement.parent_content_id">请输入内容ID</span>
            </div>
            <p class="get-content-id" @click="toGetContentId">如何获取内容ID</p>
          </div>
        </el-form-item>
        <el-form-item label="创作来源" prop="course_statement.creation_source">
          <el-radio-group v-model="form.course_statement.creation_source" @change="creationSourceChange" :disabled="specialFileDisabled">
            <el-radio :label="item.value" v-for="item in creation_source_Options" :key="item.value">{{item.label}}</el-radio>
          </el-radio-group>
          <div class="contact-person contact-person-radio" v-if="specialFileDisabled">本字段若需修改，请联系graywu。</div>
          <div class="origin-warm" v-if="form.course_statement.creation_source === 0">
            <p>(1)「创作组织」从拟定内容主题、提纲、访谈、收集素材到制作课件，一条龙完成。</p>
            <p>(2)内容创作人所属的最小组织单元，优先填“组”。</p>
          </div>
          <div class="origin-warm" v-if="form.course_statement.creation_source === 3">
              符合以下任一的共创模式：
              <p>（1）「创作组织」拟定主题、提供提纲和素材、组织立项，邀约创作者，由创作者主导内容生产，「创作组织」参与辅助和赋能。创作者拟定主题、提供提纲和素材。</p>
              （2）「创作组织」辅助立项，由创作者主导内容制作，「创作组织」参与辅助和赋能。
          </div>

          <div class="creation_source_sub_content" v-if="[0, 1, 3].includes(form.course_statement.creation_source)">
            <!-- PGC -->
            <div v-if="form.course_statement.creation_source === 0" key="PGC">
              <el-form-item class="no-form-item" label="创作组织" :rules="pgcCreationOrgRules" prop="course_statement.pgc_creation_org" label-width="100px" key="pgc_creation_org">
                <sdc-unit-selector
                class="dep-selector"
                ref="pgcCreationOrgRef"
                v-model="form.course_statement.pgc_creation_org"
                :disabled="specialFileDisabled"
                multiple
                @change="validateField($event, 'pgc_creation_org')"
                placeholder="请选择创作组织"
                />
                <div class="contact-person" v-if="specialFileDisabled">本字段若需修改，请联系graywu。</div>
              </el-form-item>
              <!-- <el-form-item label="联合创建组织" label-width="100px" style="margin-bottom: 0px">
                <sdc-unit-selector
                  class="dep-selector" 
                  ref="pgcJointCreationRef"
                  v-model="form.course_statement.pgc_joint_creation"
                  multiple
                  placeholder="请选择联合创建组织"
                  @change="validateField($event, 'pgc_joint_creation')"
                  />
              </el-form-item> -->
            </div>
            <!-- PUGC -->
            <div v-if="form.course_statement.creation_source === 3" key="PUGC">
              <el-form-item label="创作组织" :rules="pugcCreationOrgRules" prop="course_statement.pugc_creation_org" label-width="100px" key="pugc_creation_org">
                <sdc-unit-selector
                class="dep-selector"
                ref="pugcCreationOrgRef"
                v-model="form.course_statement.pugc_creation_org"
                :disabled="specialFileDisabled"
                multiple
                @change="validateField($event, 'pugc_creation_org')"
                placeholder="请选择创作组织"
                />
                <div class="contact-person" v-if="specialFileDisabled">本字段若需修改，请联系graywu。</div>
              </el-form-item>
              <el-form-item label="联合创建组织" label-width="100px" style="margin-bottom: 0px">
                <sdc-unit-selector
                  class="dep-selector" 
                  ref="pugcJointCreationRef"
                  v-model="form.course_statement.pugc_joint_creation"
                  :disabled="specialFileDisabled"
                  multiple
                  placeholder="请选择联合创建组织"
                  @change="validateField($event, 'pugc_joint_creation')"
                  />
                  <div class="contact-person" v-if="specialFileDisabled">本字段若需修改，请联系graywu。</div>
              </el-form-item>
            </div>
            <div v-if="form.course_statement.creation_source === 1" key="OGC">
              <el-form-item label="供应商名称" :rules="supplierNameRules" prop="course_statement.ogc_supplier_name" label-width="100px" class="course-texTarea-input">
                <el-input 
                  v-model.trim="form.course_statement.ogc_supplier_name" 
                  placeholder="请填写供应商公司全称或个人全名"
                  clearable>
                </el-input>
                <span class="custom-el-input-count">{{handleValidor(form.course_statement.ogc_supplier_name, 100, '4')}}/100</span>
              </el-form-item>
              <el-form-item label="采购组织" :rules="purchaseOrgRules" prop="course_statement.ogc_purchase_org" label-width="100px" key="ogc_purchase_org">
                <sdc-unit-selector
                  class="dep-selector" 
                  ref="purchaseOrgRef"
                  v-model="form.course_statement.ogc_purchase_org"
                  :disabled="specialFileDisabled"
                  multiple
                  @change="validateField($event, 'ogc_purchase_org')"
                  placeholder="请选择采购组织"
                  />
                  <div class="contact-person" v-if="specialFileDisabled">本字段若需修改，请联系graywu。</div>
              </el-form-item>
              <el-form-item label="采购方式" label-width="100px">
                <el-radio-group v-model="form.course_statement.ogc_purchase_type">
                  <el-radio :label="item.value" v-for="item in purchase_type_Options" :key="item.value">{{item.label}}</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="外部讲师" label-width="100px">
                <el-input v-model="form.course_statement.ogc_out_teachers" placeholder="请输入外部人员名称，用多个；隔开" clearable></el-input>
              </el-form-item>
              <el-form-item label="采购成本" prop="course_statement.ogc_purchase_amount" label-width="100px">
                <el-input-number class="ogc_purchase_amount" v-model="form.course_statement.ogc_purchase_amount" label="请输入采购成本"></el-input-number> 元
              </el-form-item>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="项目创建人" prop="course_statement.project_creator" class="project_creator">
          <sdc-staff-selector
            ref="projectCreatorRef" 
            v-model="form.course_statement.project_creator"
            size="small" 
            placeholder="请选择项目创建人"
            @change="validateField($event, 'project_creator')"
            />
        </el-form-item>
        <el-form-item label="人力成本" prop="course_statement.human_cost">
          <el-input-number class="human_cost" v-model="form.course_statement.human_cost" label="请输入人力成本"></el-input-number> 人天
        </el-form-item>
        <el-form-item label="内容开发人" :rules="developerRules" prop="course_statement.developer" class="developer">
          <sdc-staff-selector
            multiple
            ref="developerRef"
            v-model="form.course_statement.developer"
            size="small"
            placeholder="请选择内容开发人"
            @change="validateField($event, 'developer')"
            />
        </el-form-item>
        <el-form-item label="认证等级" prop="course_level">
          <el-radio-group v-model="form.course_level">
            <el-radio v-for="item in levelList" :key="item.code" :label="item.code" :disabled="item.disabled">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="运营分级" v-if="isSuperOrCompanyAdmin">
          <el-radio-group v-model="form.course_statement.operation_level">
            <el-radio v-for="item in operation_type_options" :key="item.value" :label="item.value" @change="changeOperationType(item)">{{ item.label }}</el-radio>
          </el-radio-group>
          <div class="creation_source_sub_content" v-if="operation_project_name_options.length">
            <el-form-item label="分级项目" label-width="100px" style="margin-bottom: 0px">
              <el-select v-model="form.course_statement.operation_project_name" placeholder="请选择分级项目" clearable>
                <el-option
                  v-for="item in operation_project_name_options"
                  :key="item.item_name"
                  :label="item.item_name"
                  :value="item.item_name">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
        </el-form-item>
        <el-form-item label="内容专家评分" prop="course_statement.expert_score" class="expert-rating">
          <el-input class="expert-rating-input"
            type="number"
            v-model="form.course_statement.expert_score" 
            placeholder="请输入评估分数"
            clearable>
          </el-input>
          <span class="tip-warm">请输入0-100的数字，可填写两位小数</span>
        </el-form-item>
        <el-form-item label="是否加入推荐池" class="is-required">
          <el-radio-group v-model="form.course_statement.join_recommend" :disabled="specialFileDisabled">
            <el-radio :label="false">否，不加入推荐池</el-radio>
            <el-radio :label="true">是，加入推荐池</el-radio>
          </el-radio-group>
          <div class="contact-person contact-person-radio" v-if="specialFileDisabled">本字段若需修改，请联系graywu。</div>
          <div class="red-tips-customer"><i class="el-icon-warning mgr-5"></i>合规类、应知应会类、应学类课程等请勿选择加入推荐流。</div>
        </el-form-item>
        <el-form-item label="是否必修" class="is-required">
          <el-radio-group v-model="form.course_statement.is_required" :disabled="specialFileDisabled">
            <el-radio :label="false">否，全员选修</el-radio>
            <el-radio :label="true">指定人群必修或全员必修</el-radio>
          </el-radio-group>
          <div class="contact-person contact-person-radio" v-if="specialFileDisabled">本字段若需修改，请联系graywu。</div>
        </el-form-item>
        <el-form-item label="是否同步给小Q同学" class="is-required" prop="ai_sync_flag" v-if="isSuperAdmin">
          <el-radio-group v-model="form.ai_sync_flag" :disabled="specialFileDisabled">
            <el-radio :label="1">同步
              <el-tooltip class="item" effect="dark" content="数据同步给小Q同学知识库，权限与本页面设置相同" placement="bottom-start">
                <i class="el-icon-warning-outline async-icon"></i>
              </el-tooltip>
            </el-radio>
            <el-radio :label="0">不同步</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="数据有效时间" class="is-required" prop="ai_expire_type" v-if="isSuperAdmin && form.ai_sync_flag === 1">
          <el-radio-group v-model="form.ai_expire_type" :disabled="specialFileDisabled">
            <el-radio :label="1">长期</el-radio>
            <el-radio :label="2">自定义到期时间
              <el-date-picker 
                v-if="form.ai_expire_type === 2"
                v-model="form.ai_expire_end_time"
                type="datetime" 
                value-format="yyyy-MM-dd HH:mm:ss"
                default-time="00:00:00" 
              >
              </el-date-picker>
              <el-tooltip class="item" effect="dark" content="到期后，数据将在小Q同学知识库不可使用" placement="bottom-start">
                <i class="el-icon-warning-outline async-icon"></i>
              </el-tooltip>
            </el-radio>
          </el-radio-group>
        </el-form-item>
      </div>

    </el-form>
    <!-- 底部按钮区域 -->
    <bottomFiexd @cancel="onCancel" @save="approveSafe" :isNeedConvention="true" :disabledBtn="courseInfo.info_sec_status === 0" :class="{'new-bottom': pageEntryType !== 'baseSet' }" :conventionStyle="pageEntryType !== 'baseSet' ? 'margin-left: 235px;' : '    margin-left: 202px;' "></bottomFiexd>
    <!-- 添加标签 -->
    <add-tag-dialog v-if="addCourseTagDialog" :visible.sync="addCourseTagDialog" @confirmTagList="confirmTagList"
      ref="addCourseTagRef" />
    <!-- 一键生成项目封面 -->
    <sdc-img-cover 
    ref="sdcImgCoverRef" 
    :visible.sync="autoImgCoverShow" 
    :imgInfo="imgInfo"
    @handleImgCoverOk="handleImgCoverOk"
    >
    </sdc-img-cover>
    <!-- 获取证书 -->
    <certificateList v-if="certificateDialog" :visible.sync="certificateDialog" @confirmCerificateList="confirmCerificateList"></certificateList>
    <!-- 信息审核再次编辑异步变化弹窗 -->
    <informationSafetyDialog :isShow.sync="informationSafetyShow" @safeConfirm="onSubmit()"/>
    <!-- 特殊字段的修改弹窗 -->
    <el-dialog
      class="update-dialog"
      title="提示"
      :visible.sync="showDialog"
      width="600px"
      :close-on-click-modal="false"
      :before-close="handleClose">
      <div class="update-content-box">
        <span class="dialog-label">修改内容:</span>
        <el-input class="update-text-input"
          v-model="updateText" 
          placeholder="请输入修改的内容"
          clearable>
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleOk">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
  
<script>
import { CutImgUpload } from '@/components/index'
import addTagDialog from './add-tag-dialog/index.vue'
import bottomFiexd from '@/views/components/botttomFixed.vue'
import certificateList from './certificateList.vue'
import { saveProject, getTrainInfo, getOperationApi } from '@/config/mooc.api.conf.js'
import { mapState } from 'vuex'
import conventionBanner from '@/views/components/convention-banner.vue'
import informationSafetyDialog from '@/components/information-safety-dialog'
const EXP = /^\s*(-?(\d+(\.\d{0,2})?|\.\d{1,2}))\s*$/
export default {
  components: {
    CutImgUpload,
    addTagDialog,
    certificateList,
    bottomFiexd,
    conventionBanner,
    informationSafetyDialog
  },
  props: {
    pageEntryType: {
      type: String,
      default: ''
    }
  },
  data() {
    const validImg = (rule, value, callback) => {
      if (!value && !this.form.cover_image_id) {
        return callback(new Error('请选择封面'))
      } else {
        callback()
      }
    }
    const validAmount = (rule, value, callback) => {
      if ((value && (value < 0 || !EXP.test(value))) || value === 0) {
        return callback(new Error('采购成本需大于0, 至多可填写两位小数'))
      } else {
        callback()
      }
    }
    const validHumanCost = (rule, value, callback) => {
      if ((value && (value <= 0 || !EXP.test(value))) || value === 0) {
        return callback(new Error('人力成本需大于0, 至多可填写两位小数'))
      } else {
        callback()
      }
    }
    const validExpertRating = (rule, value, callback) => {
      if (value && (value < 0 || value > 100 || !EXP.test(value))) {
        return callback(new Error('输入的值要大于等于0小于等于100, 至多可填写两位小数'))
      } else {
        callback()
      }
    }
    const validExpireType = (rule, value, callback) => {
      if (!value || (value === 2 && !this.form.ai_expire_end_time)) {
        return callback(new Error('请选择数据有效时间'))
      } else {
        callback()
      }
    }
    return {
      showDialog: false, // 是否显示特殊字段弹窗
      updateText: '', // 修改的内容
      bakArg: {}, // 提交时检验后的参数
      // 分级目录选项
      operation_project_name_options: [],
      // 分级目录集合
      operation_project_name_options_map: {},
      parent_content_act_type_options: [
        { label: '面授课', value: 1 },
        { label: '活动', value: 4 },
        { label: '网络课', value: 2 },
        { label: '培养项目', value: 11 },
        { label: 'SPOC', value: 27 },
        { label: '直播', value: 5 },
        // { label: '文章', value: 6 },
        { label: '文章', value: 18 },
        { label: '案例', value: 16 },
        { label: '课单', value: 15 },
        { label: '行家', value: 19 }
      ],
      // creation_source_Options: [
      //   { label: 'PGC-来自组织创作', value: 1 },
      //   { label: 'UGC-来自个人创作', value: 2 },
      //   { label: 'OGC-来自外部采购', value: 3 }
      // ],
      creation_source_Options: [
        { label: '培训or业务团队独立开发(PGC)', value: 0 },
        { label: '培训团队联合业务作者合作创作（PUGC）', value: 3 },
        { label: '员工自发原创（UGC）', value: 2 },
        { label: '外部引入（OGC）', value: 1 }
      ],
      purchase_type_Options: [
        { label: '个人按需购买', value: 0 },
        { label: '公司统一采购', value: 1 },
        { label: '账号采购', value: 2 }
      ],
      // operation_type_options: [
      //   { label: '非体系', value: 1 },
      //   { label: '基础', value: 2 },
      //   { label: '中坚', value: 3 },
      //   { label: '头部', value: 4 }
      // ],
      form: {
        course_title: '',
        course_statement: {
          operation_title: '', // 运营标题
          parent_content_act_type: null, // 父内容类型
          parent_content_id: '', // 父内容ID
          creation_source: 0, // 创作来源
          pgc_creation_org: [], // PGC创作组织
          // pgc_joint_creation: [], // PGC联合创作组织
          pugc_creation_org: [], // PUGC创作组织
          pugc_joint_creation: [], // PUGC联合创作组织
          ogc_supplier_name: '', // 供应商名称
          ogc_purchase_org: [], // 采购组织
          ogc_out_teachers: '', // 外部讲师
          ogc_purchase_type: null, // 采购方式
          ogc_purchase_amount: undefined, // 采购成本
          project_creator: [], // 项目创建人
          human_cost: undefined, // 人力成本
          developer: [], // 内容开发人
          operation_level: 3, // 运营分级
          operation_project_name: '', // 分级项目
          expert_score: null, // 内容专家评分
          join_recommend: true, // 是否加入推荐池
          is_required: false // 是否纳入应学
        },
        // course_category_id: '',
        course_period_type: 3, // 1-限定项目时间 2-限定项目周期 3-不限定时间
        course_desc: '',
        course_desc_detail: '',
        course_level: '0',
        dept_id: '',
        dept_name: '',
        course_admins: [],
        study_type: 1, // 1-列表样式 2-地图样式
        serial_type: 1, // 1-非连载 2-连载
        enable_grant_certificate: false,
        enable_study_record_sync: true,
        studyTime: '',
        cover_image_id: '',
        cover_image: '',
        cover_image_storage_type: '',
        course_classifies: [],
        certificate_id: '',
        certificate_name: '',
        certificate_teamplate_url: '',
        course_labels: [],
        enable_myoa: false,
        enable_hr_assistant: false, // 是否开启HR助手提醒
        enable_copy_bp: false, // 是否开启抄送BP通知
        enable_bot: false, // 是否开启企微机器人通知
        enable_copy_leader: false, // 是否开启抄送直接上级
        show_join_count: false, // 显示项目人数
        acquisition_type_flag: false,
        resource_from: '',
        ai_sync_flag: 1, // 是否同步给小Q同学 0-不同步 1-同步
        ai_expire_type: 1, // 数据有效时间 1-长期 2-自定义到期时间
        ai_expire_end_time: '' // 自定义到期时间
      },
      // labels: '',
      dialogImageUrl: '', // 内容中心
      autoImgUrl: '', // 一键封面
      autoImgCoverShow: false,
      addCourseTagDialog: false,
      certificateDialog: false,
      imgInfo: {},
      // tags: [],
      classifiyOptions: [],
      UnitSortID: '',
      labelNodeEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test',
      informationSafetyShow: false,
      courseInfo: {},
      courseLevelList: [
        { code: '0', name: '无', disabled: false }, 
        { code: '1', name: '公司级', disabled: false }, 
        { code: '2', name: 'BG级', disabled: false },
        { code: '3', name: '部门级', disabled: false }
      ],
      deptProps: {
        UnitFullName: 'dept_name',
        unitID: 'dept_id'
      },
      adminProps: {
        staffID: 'admin_id',
        staffName: 'admin_name'
      },
      classifyProps: {
        multiple: false,
        label: 'classify_name',
        value: 'classify_id',
        children: 'child',
        checkStrictly: true,
        emitPath: false
      },
      copyTime: [],
      insertItems: [
        'link',
        'table',
        'image',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorConfig: {
        catalogue: false,
        options: { // 非必传字段
          selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
              formatselect fontsizeselect lineheight |
              bold italic underline strikethrough |
              forecolor backcolor |
              dent align bullist numlist |
              insert codesample |
              fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      },
      editorEnv: process.env.NODE_ENV,
      rules: {
        course_title: [
          { required: true, message: '请输入项目名称', trigger: 'blur' }
        ],
        course_labels: [{ required: true, message: '请选择项目标签', trigger: 'blur' }],
        cover_image: [
          { required: true, validator: validImg, trigger: 'blur' }
        ],
        course_level: [{ required: true, message: '请选择认证等级', trigger: 'change' }],
        dept_id: [{ required: true, message: '请选择组织', trigger: 'blur' }],
        'course_statement.creation_source': [{ required: true, message: '请选择创作来源', trigger: 'blur' }],
        'course_statement.ogc_purchase_amount': [{ validator: validAmount, trigger: ['blur', 'change'] }],
        'course_statement.project_creator': [{ required: true, message: '请选择项目创建人', trigger: 'blur' }],
        'course_statement.human_cost': [{ validator: validHumanCost, trigger: ['blur', 'change'] }],
        'course_statement.expert_score': [{ validator: validExpertRating, trigger: ['blur', 'change'] }],
        ai_sync_flag: [{ required: true, message: '是否同步给小Q同学', trigger: 'change' }],
        ai_expire_type: [{ validator: validExpireType, required: true, message: '请选择数据有效时间', trigger: 'change' }]
      }
    }
  },
  computed: {
    ...mapState(['projectManageInfo', 'userLimitInfo', 'userInfo']),
    // 内容标题
    isTitleChanged() {
      return !!(this.form.course_title !== this.courseInfo.course_title)
    },
    // 运营标题
    isStateMentTitleChanged() {
      return !!(this.form.course_statement?.operation_title !== this.courseInfo.course_statement?.operation_title)
    },
    // 封面图
    isImgChanged() {
      return !!(this.form.cover_image !== this.courseInfo.cover_image)
    },
    // 简介
    isDesc() {
      return !!(this.form.course_desc !== this.courseInfo.course_desc)
    },
    // 详情
    isDescDetail() {
      return !!(this.$refs['editor'].getContent() !== this.courseInfo.course_desc_detail)
    },
    isFilesChange() {
      // 标题，运营标题，封面图，简介，详情
      return !!(this.isTitleChanged || this.isStateMentTitleChanged || this.isImgChanged || this.isDesc || this.isDescDetail)
    },
    // 角色权限
    levelList() {
      let { supper_admin, mooc_bgadmin, mooc_company_admin, mooc_dept_admin } = this.userLimitInfo
      const id = this.$route.query.mooc_course_id
      if (!id) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.form.course_level = '0'
      }
      if (supper_admin || mooc_company_admin) { // 公司
        this.courseLevelList.forEach((e) => e.disabled = false)
      } else if (mooc_bgadmin) { // bg
        this.courseLevelList.forEach((e) => e.disabled = e.code === '1')
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        // this.form.course_level = '2'
      } else if (mooc_dept_admin) { // 部门
        this.courseLevelList.forEach((e) => e.disabled = e.code !== '3' && e.code !== '0')
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        // this.form.course_level = '3'
      }
      return this.courseLevelList
    },
    showParentId() {
      return this.form.course_statement.parent_content_act_type || this.form.course_statement.parent_content_id
    },
    developerRules() {
      return {
        required: [0, 2].includes(this.form.course_statement.creation_source), message: '请选择内容开发人', trigger: 'blur'
      }
    },
    supplierNameRules() {
      return { required: this.form.course_statement.creation_source === 1, message: '请输入供应商名称', trigger: 'blur' }
    },
    purchaseOrgRules() {
      return { required: this.form.course_statement.creation_source === 1, message: '请选择采购组织', trigger: 'blur' }
    },
    // PGC创作组织校验规则
    pgcCreationOrgRules() {
      return { required: this.form.course_statement.creation_source === 0, trigger: 'blur', validator: this.validPgcCreationOrg }
    },
    // PUGC创作组织校验规则
    pugcCreationOrgRules() {
      return { required: this.form.course_statement.creation_source === 3, trigger: 'blur', validator: this.validPugcCreationOrg }
    },
    // 是否是公司管理员或者超级管理员
    isSuperOrCompanyAdmin() {
      let { supper_admin, mooc_company_admin } = this.userLimitInfo
      return supper_admin || mooc_company_admin
    },
    // 是否是超管
    isSuperAdmin() {
      let { supper_admin } = this.userLimitInfo
      return supper_admin
    },
    // 运营分级
    operation_type_options() {
      let list = [
        { label: '非体系', value: 3, pid: 528 },
        { label: '基础', value: 2, pid: 525 },
        { label: '中坚', value: 1, pid: 526 },
        { label: '头部', value: 0, pid: 527 }
      ]
      if (process.env.NODE_ENV !== 'production') {
        list = [
          { label: '非体系', value: 3, pid: 526 },
          { label: '基础', value: 2, pid: 569 },
          { label: '中坚', value: 1, pid: 571 },
          { label: '头部', value: 0, pid: 573 }
        ]
      }
      return list
    },
    // "创作来源、创作组织、联合创作组织、管理组织、采购组织、是否加入推荐池、是否必修"特殊字段禁用，发布后，只有超管才能编辑
    specialFileDisabled() {
      let status = this.courseInfo.course_status + ''
      return status === '1' && !this.isSuperAdmin
    },
    // 创作来源是否修改
    isCreationSourceChanged() {
      let creation_source = this.courseInfo.course_statement?.creation_source || 0
      return this.form.course_statement.creation_source !== creation_source
    },
    // PGC创作组织是否修改
    isPgcCreationOrgChanged() {
      try {
        let pgc_creation_org = this.courseInfo.course_statement?.pgc_creation_org || []
        return !this.areArraysEqual(pgc_creation_org, this.form.course_statement.pgc_creation_org, 'UnitID')
      } catch (error) {
        return false
      }
    },
    // PUGC创作组织是否修改
    isPugcCreationOrgChanged() {
      try {
        let pugc_creation_org = this.courseInfo.course_statement?.pugc_creation_org || []
        return !this.areArraysEqual(pugc_creation_org, this.form.course_statement.pugc_creation_org, 'UnitID')
      } catch (error) {
        return false
      }
    },
    // 联合创作组织是否修改
    isJointCreationOrgChanged() {
      try {
        let pugc_joint_creation = this.courseInfo.course_statement?.pugc_joint_creation || []
        return !this.areArraysEqual(pugc_joint_creation, this.form.course_statement.pugc_joint_creation, 'UnitID')
      } catch (error) {
        return false
      }
    },
    // 管理组织是否修改
    isDeptIdChanged() {
      return this.form.dept_id !== this.courseInfo.dept_id
    },
    // 采购组织是否修改
    isPurchaseOrgChanged() {
      try {
        let ogc_purchase_org = this.courseInfo.course_statement?.ogc_purchase_org || []
        return !this.areArraysEqual(ogc_purchase_org, this.form.course_statement.ogc_purchase_org, 'UnitID')
      } catch (error) {
        return false
      }
    },
    // 是否加入推荐池是否修改
    isJoinRecommendChanged() {
      let join_recommend = this.courseInfo.course_statement?.join_recommend || false
      return !!(this.form.course_statement?.join_recommend !== join_recommend)
    },
    // 是否必修是否修改
    isIsRequiredChanged() {
      let is_required = this.courseInfo.course_statement?.is_required || false
      return this.form.course_statement.is_required !== is_required
    },
    // 是否有特殊字段发生改变
    isSpecialFileChanged() {
      return this.isCreationSourceChanged || this.isPgcCreationOrgChanged || this.isPugcCreationOrgChanged || this.isJointCreationOrgChanged || this.isDeptIdChanged || this.isPurchaseOrgChanged || this.isJoinRecommendChanged || this.isIsRequiredChanged
    },
    // 是否需要弹出特殊字段的修改弹窗
    showSpecialFileDialog() {
      let status = this.courseInfo.course_status + ''
      return status === '1' && this.isSuperAdmin && this.isSpecialFileChanged
    }
  },
  watch: {
    userLimitInfo: {
      deep: true,
      handler(data) {
        const { supper_admin, mooc_bgadmin, mooc_company_admin, mooc_dept_admin } = data
        if (![supper_admin, mooc_bgadmin, mooc_company_admin, mooc_dept_admin].includes(true)) {
          this.$router.push({
            name: '401'
          })
        }
      }
    },
    userInfo: {
      handler(value) {
        const id = this.$route.query.mooc_course_id
        if (!id) {
          this.$nextTick(() => {
            this.$refs.projectCreatorRef && this.$refs.projectCreatorRef.setSelected({
              StaffID: value.staff_id,
              StaffName: value.staff_name
            })
          })
        }
      },
      immediate: true,
      deep: true
    },
    'form.acquisition_type_flag': {
      handler(val) {
        if (val) {
          this.form.course_acquisition_type = 2
        } else {
          this.form.course_acquisition_type = 1
        }
      }
    },
    deep: true
  },
  mounted() {
    this.getInfo()
    if (!this.$route.query.mooc_course_id) {
      this.getLevelObjectOption()
    }
  },
  methods: {
    // 判断两个对象数组是否相等，id相等极为相等，不考虑数组顺序问题
    areArraysEqual(arr1, arr2, key) {
      // 提取两个数组的id
      let ids1 = arr1.map(item => item[key])
      let ids2 = arr2.map(item => item[key])
      // 对id数组进行排序
      ids1.sort()
      ids2.sort()
      // 比较两个排序后的id数组是否相等
      return JSON.stringify(ids1) === JSON.stringify(ids2)
    },
    handleClose() {
      this.showDialog = false
      this.updateText = ''
    },
    handleOk() {
      if (!this.updateText.trim()) {
        return this.$message.warning('请输入修改内容！')
      }
      this.bakArg.admin_edit_content = this.updateText
      this.validCallBack(this.bakArg)
      this.showDialog = false
      this.updateText = ''
    },
    // 特殊字段修改后弹窗提示
    showSpecialDialogFn(params) {
      this.bakArg = params
      this.bakArg.admin_edit_content = ''
      if (this.showSpecialFileDialog) {
        this.showDialog = true
      } else {
        this.validCallBack(this.bakArg)
      }
    },
    // 查看填写说明
    toInstructionsPage() {
      window.open('https://iwiki.woa.com/p/4012202789')
    },
    // 校验PGC创作组织 只能单选，但是要兼容旧数据回显
    validPgcCreationOrg(rule, value, callback) {
      if (!this.form.course_statement.pgc_creation_org.length) {
        return callback(new Error('请选择创作组织'))
      } else if (this.form.course_statement.pgc_creation_org.length > 1) {
        return callback(new Error('创作组织仅能选择一个组织'))
      } else {
        callback()
      }
    },
    // 校验PUGC创作组织 只能单选，但是要兼容旧数据回显
    validPugcCreationOrg(rule, value, callback) {
      if (!this.form.course_statement.pugc_creation_org.length) {
        return callback(new Error('请选择创作组织'))
      } else if (this.form.course_statement.pugc_creation_org.length > 1) {
        return callback(new Error('创作组织仅能选择一个组织'))
      } else {
        callback()
      }
    },
    // 运营分级发生改变
    changeOperationType(item) {
      this.form.course_statement.operation_project_name = ''
      this.getOperationInfo(item)
    },
    // 获取分级项目
    async getOperationInfo(item) {
      if (!this.operation_project_name_options_map[item.value]) {
        getOperationApi(item.pid).then(res => {
          this.operation_project_name_options_map[item.value] = res || []
          this.operation_project_name_options = res || []
        })
      } else {
        this.operation_project_name_options = this.operation_project_name_options_map[item.value]
      }
    },
    getLevelObjectOption() {
      let obj = this.operation_type_options.find(item => item.value === this.form.course_statement.operation_level)
      if (obj) {
        this.getOperationInfo(obj)
      }
    },
    // 手动检验字段
    validateField(value, file) {
      if (file !== 'pugc_joint_creation') {
        this.$refs['form'].validateField(`course_statement.${file}`)
      }
      if (['project_creator', 'developer', 'pgc_creation_org', 'pugc_creation_org', 'pugc_joint_creation', 'ogc_purchase_org'].includes(file)) {
        this.form.course_statement[file] = Array.isArray(value) ? value : [value]
      }
    },
    // 切换"创作来源时"，清除所属分类下的表单校验 编辑时逻辑需要完善
    creationSourceChange(value) {
      switch (value) {
        case 0:
          this.initPUGC()
          this.initOGC()
          break
        case 3:
          this.initPGC()
          this.initOGC()
          break
        case 2:
          this.initPGC()
          this.initPUGC()
          this.initOGC()
          break
        case 1:
          this.initPGC()
          this.initPUGC()
          break
        default:
          break
      }
      this.$refs['form'].clearValidate(['course_statement.pgc_creation_org', 'course_statement.pugc_creation_org', 'course_statement.ogc_supplier_name', 'course_statement.ogc_purchase_org'])
    },
    // 重置PGC
    initPGC() {
      this.form.course_statement.pgc_creation_org = []
      // this.form.course_statement.pgc_joint_creation = []
    },
    // 重置PUGC
    initPUGC() {
      this.form.course_statement.pugc_creation_org = []
      this.form.course_statement.pugc_joint_creation = []
    },
    // 重置OGC
    initOGC() {
      this.form.course_statement.ogc_supplier_name = ''
      this.form.course_statement.ogc_purchase_org = []
      this.form.course_statement.ogc_purchase_type = null
      this.form.course_statement.ogc_out_teachers = ''
      this.form.course_statement.ogc_purchase_amount = undefined
    },
    toGetContentId() {
      window.open('https://iwiki.woa.com/p/4009876544')
    },
    getInfo() {
      const id = this.$route.query.mooc_course_id
      if (id) {
        getTrainInfo(id).then((res) => {
          this.courseInfo = res || {}
          if (res.course_statement) {
            const resolveArray = ['project_creator', 'developer', 'pgc_creation_org', 'pugc_creation_org', 'pugc_joint_creation', 'ogc_purchase_org']
            resolveArray.forEach(item => {
              try {
                if (!res.course_statement[item]) {
                  res.course_statement[item] = []
                } else {
                  res.course_statement[item] = JSON.parse(res.course_statement[item])
                }
              } catch (error) {
                res.course_statement[item] = []
              }
            })

            // 特殊处理OGC的外部讲师字段 ogc_out_teachers
            if (res.course_statement.ogc_out_teachers) {
              try {
                let ogc_out_teachers_array = JSON.parse(res.course_statement['ogc_out_teachers'])
                res.course_statement['ogc_out_teachers'] = ogc_out_teachers_array.map(item => item.StaffName).join(';')
              } catch (error) {
                res.course_statement['ogc_out_teachers'] = ''
              }
            }
          }
          const {
            course_title,
            course_classifies,
            course_period_type,
            end_time, start_time,
            cover_image_id,
            cover_image,
            cover_image_storage_type,
            course_desc,
            course_desc_detail,
            course_level,
            dept_id,
            dept_name,
            course_admins,
            study_type,
            serial_type,
            enable_grant_certificate,
            enable_study_record_sync,
            certificate_name,
            certificate_id,
            course_labels,
            enable_myoa,
            enable_hr_assistant,
            enable_bot,
            enable_copy_bp,
            enable_copy_leader,
            show_join_count,
            course_acquisition_type,
            resource_from,
            ai_sync_flag, 
            ai_expire_type,
            ai_expire_end_time
          } = res
          let course_statement = res.course_statement
          if (!course_statement) {
            course_statement = {
              operation_title: '',
              parent_content_act_type: null,
              parent_content_id: null,
              creation_source: 0,
              pgc_creation_org: [],
              // pgc_joint_creation: [],
              pugc_creation_org: [],
              pugc_joint_creation: [],
              ogc_supplier_name: '',
              ogc_purchase_org: [],
              ogc_out_teachers: '',
              ogc_purchase_type: null,
              ogc_purchase_amount: undefined,
              project_creator: [],
              human_cost: undefined,
              developer: [],
              operation_level: 3,
              operation_project_name: '',
              expert_score: null,
              join_recommend: null,
              is_required: false
            }
          }
          const {
            operation_title,
            parent_content_act_type,
            parent_content_id,
            creation_source,
            pgc_creation_org,
            // pgc_joint_creation,
            pugc_creation_org,
            pugc_joint_creation,
            ogc_supplier_name,
            ogc_purchase_org,
            ogc_out_teachers,
            ogc_purchase_type,
            ogc_purchase_amount,
            project_creator,
            human_cost,
            developer,
            operation_level,
            operation_project_name,
            expert_score,
            join_recommend,
            is_required
          } = course_statement
          const studyTime = start_time ? [start_time, end_time] : ''
          // 一键封面处理
          this.autoImgUrl = cover_image
          this.dialogImageUrl = ''
          // const course_category_id = course_classifies.find((e) => e.classify_id)
          // 标签
          // this.tags = course_labels
          this.form = {
            course_title,
            course_classifies,
            // course_category_id: course_classifies[0].classify_id,
            course_period_type,
            studyTime,
            cover_image_id,
            cover_image,
            cover_image_storage_type,
            course_desc,
            course_desc_detail: course_desc_detail ? course_desc_detail.replace(/\n/g, '<br/>') : '',
            course_level,
            dept_id,
            dept_name,
            course_admins,
            study_type,
            serial_type,
            enable_grant_certificate,
            enable_study_record_sync,
            certificate_name,
            certificate_id,
            course_labels,
            enable_hr_assistant,
            enable_bot,
            enable_myoa,
            enable_copy_bp,
            enable_copy_leader,
            show_join_count,
            course_acquisition_type,
            acquisition_type_flag: course_acquisition_type === 2,
            resource_from,
            ai_sync_flag: ai_sync_flag === '' ? 1 : ai_sync_flag ? 1 : 0, 
            ai_expire_type: !ai_expire_type ? 1 : ai_expire_type,
            ai_expire_end_time,
            course_statement: {
              operation_title,
              parent_content_act_type,
              parent_content_id,
              creation_source,
              pgc_creation_org,
              // pgc_joint_creation,
              pugc_creation_org,
              pugc_joint_creation,
              ogc_supplier_name,
              ogc_purchase_org,
              ogc_out_teachers: ogc_out_teachers || '',
              ogc_purchase_type,
              ogc_purchase_amount: [null, '', 0, '0'].includes(ogc_purchase_amount) ? undefined : ogc_purchase_amount,
              project_creator,
              human_cost: [null, '', 0, '0'].includes(human_cost) ? undefined : human_cost,
              developer,
              operation_level,
              operation_project_name,
              expert_score,
              join_recommend,
              is_required
            }
          }
          this.$nextTick(() => {
            if (dept_id) { // 归属组织
              this.$refs.deptSelectorRef.setSelected([{ dept_id, UnitName: dept_name, UnitFullName: dept_name }])
            }
            const adminList = course_admins.map((e) => ({ ...e, admin_id: Number(e.admin_id) }))
            if (adminList?.length) { // 管理员
              this.$refs.adminsSelectorRef && this.$refs.adminsSelectorRef.setSelected(adminList)
            }
            if (project_creator?.length) { // 项目创建人
              this.$refs.projectCreatorRef && this.$refs.projectCreatorRef.setSelected(project_creator)
            }
            if (developer?.length) { // 内容开发人
              this.$refs.developerRef && this.$refs.developerRef.setSelected(developer)
            }
            if (pgc_creation_org?.length) { // PGC创作组织
              this.$refs.pgcCreationOrgRef && this.$refs.pgcCreationOrgRef.setSelected(pgc_creation_org)
            }
            // if (pgc_joint_creation?.length) { // PGC联合创作组织
            //   this.$refs.pgcJointCreationRef && this.$refs.pgcJointCreationRef.setSelected(pgc_joint_creation)
            // }
            if (pugc_creation_org?.length) { // PUGC创作组织
              this.$refs.pugcCreationOrgRef && this.$refs.pugcCreationOrgRef.setSelected(pugc_creation_org)
            }
            if (pugc_joint_creation?.length) { // PUGC联合创作组织
              this.$refs.pugcJointCreationRef && this.$refs.pugcJointCreationRef.setSelected(pugc_joint_creation)
            }
            if (ogc_purchase_org?.length) { // 采购组织
              this.$refs.purchaseOrgRef && this.$refs.purchaseOrgRef.setSelected(ogc_purchase_org)
            }
          })
        }).finally(() => {
          this.getLevelObjectOption()
        })
      }
    },
    // 信息安全审核
    approveSafe() {
      const { mooc_course_id } = this.$route.query
      if (this.isFilesChange && mooc_course_id) {
        this.informationSafetyShow = true
        return
      }
      this.onSubmit()
    },
    // 完成
    onSubmit() {
      // 连载状态关闭
      if (this.form.serial_type === 2) {
        this.form.enable_grant_certificate = false
        this.form.certificate_name = ''
      }
      // 清空证书
      if (!this.form.enable_grant_certificate && this.pageEntryType !== 'baseSet') {
        this.form.certificate_name = ''
      }
      this.form.course_desc_detail = this.$refs['editor'].getContent()
      const { mooc_course_id } = this.$route.query
      const { studyTime, course_classifies, ai_expire_type, ai_expire_end_time } = this.form
      const start_time = studyTime?.length ? studyTime[0] : ''
      const end_time = studyTime?.length ? studyTime[1] : ''
      const params = {
        ...JSON.parse(JSON.stringify(this.form)),
        start_time,
        end_time,
        ai_expire_end_time: ai_expire_type === 2 ? ai_expire_end_time : '',
        course_classifies,
        mooc_course_id
      }

      if (this.form.course_period_type === 1 && !this.form.studyTime?.length) {
        this.$message.warning('请选择起止时间')
        return
      }
      if (this.form.enable_grant_certificate && !this.form.certificate_id) {
        this.$message.warning('请添加证书')
        return
      }
      if (this.showParentId) {
        if (!this.form.course_statement.parent_content_act_type) {
          this.$message.warning('请选择内容类型')
          return
        }
        if (!this.form.course_statement.parent_content_id) {
          this.$message.warning('请输入内容ID')
          return
        }
      }
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.showSpecialDialogFn(params)
        } else {
          this.$alert('存在必填项未填写，请检查填写信息。', '提示', {
            confirmButtonText: '好的',
            callback: action => {}
          })
          return false
        }
      })
    },
    validCallBack(params) {
      // 需要JSON.stringify处理的数据 回显时用
      const resolveArray = ['project_creator', 'developer', 'pgc_creation_org', 'pugc_creation_org', 'pugc_joint_creation', 'ogc_purchase_org']
      resolveArray.forEach(item => {
        if (params.course_statement[item] === null) {
          params.course_statement[item] = JSON.stringify([])
        } else {
          params.course_statement[item] = JSON.stringify(params.course_statement[item])
        }
      })

      // OGC-外部讲师
      if (params.course_statement['ogc_out_teachers']) {
        let strArray = params.course_statement['ogc_out_teachers'].split(';') ? params.course_statement['ogc_out_teachers'].split(';') : []
        strArray = strArray.map(item => {
          return { StaffName: item }
        })
        params.course_statement['ogc_out_teachers'] = JSON.stringify(strArray)
      } else {
        params.course_statement['ogc_out_teachers'] = JSON.stringify([])
      }

      if ([undefined, '', 0].includes(params.course_statement.human_cost)) {
        params.course_statement.human_cost = null
      }
      if ([undefined, '', 0].includes(params.course_statement.ogc_purchase_amount)) {
        params.course_statement.ogc_purchase_amount = null
      }
      // [undefined, '', 0].includes(params.course_statement.human_cost) && (params.course_statement.human_cost = null)
      // [undefined, '', 0].includes(params.course_statement.ogc_purchase_amount) && (params.course_statement.ogc_purchase_amount = null)
      params.course_statement.expert_score === '' && (params.course_statement.expert_score = null)
      if (typeof params.course_statement.expert_score === 'string') {
        params.course_statement.expert_score = +params.course_statement.expert_score
      }
      // 认证等级处理
      params.course_statement.certification_level = params.course_level

      saveProject(params).then((res) => {
        if (this.pageEntryType === 'baseSet') { // 基础设置页
          this.$message.success('保存成功')
          this.$emit('handleCancel')
          this.$store.dispatch('getProjectInfoData', params.mooc_course_id)
          return
        }
        this.$router.push({
          name: 'project-list'
        })
      })
    },
    // 证书回传数据
    confirmCerificateList(data) {
      this.form.certificate_id = data.certificateId
      this.form.certificate_name = data.certificateName
      this.form.certificate_teamplate_url = data.backImageId
    },
    // 取消
    onCancel() {
      if (this.pageEntryType === 'baseSet') {
        this.$emit('handleCancel')
        return
      }
      window.close()
    },
    changeDept(val) {
      this.form.dept_id = val.dept_id
      this.form.dept_name = val.UnitFullName
      this.UnitSortID = val.UnitSortID
      if (this.form.dept_id) {
        this.$refs.form.clearValidate('dept_id')
      }
    },
    // 管理员
    changeCourseAuth(val) {
      // if (val.length > 10) {
      //   this.$message.warning('最多添加10人')
      //   this.$refs.adminsSelectorRef.setSelected(val.slice(0, 10))
      // }
      // const arr = val.length > 10 ? val.slice(0, 10) : val
      this.form.course_admins = val
    },
    changeSerialType(val) {
      if (val === 2) {
        this.form.enable_grant_certificate = false
      } else if (val === 1) {
        if (this.form.certificate_name) {
          this.form.enable_grant_certificate = true
        }
      }
    },
    // 一键封面回传
    handleImgCoverOk(row) {
      this.autoImgUrl = row.url
      // 清空裁剪封面
      this.dialogImageUrl = ''
      this.form.cover_image = row.url
      this.form.cover_image_id = row.id
      this.form.cover_image_storage_type = 'zhihui'
      if (this.autoImgUrl) {
        this.$refs.form.clearValidate('cover_image')
      }
    },
    // 编辑一键封面
    handleImgEdit() {
      this.$refs.sdcImgCoverRef.outEdit({
        id: this.form.cover_image_id,
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        type: 'outEdit'
      })
    },
    handleClearImg() {
      this.autoImgUrl = ''
      this.dialogImageUrl = ''
    },
    handleSuccessImage(url, file, id) {
      this.dialogImageUrl = url
      this.form.cover_image = url
      this.form.cover_image_id = id
      // // 清空一键生成封面
      this.autoImgUrl = ''
      this.form.cover_image_storage_type = 'contentcenter'
      if (this.dialogImageUrl) {
        this.$refs.form.clearValidate('cover_image')
      }
    },
    // 项目时间
    timeRadioChange(val) {
      if (val !== '1') {
        this.copyTime = JSON.parse(JSON.stringify(this.form.studyTime))
        this.form.studyTime = ''
      } else {
        this.form.studyTime = this.copyTime
      } 
    },
    handleAutoImg() {
      this.autoImgCoverShow = true
      this.imgInfo = {
        title: this.form.course_title,
        env: process.env.NODE_ENV === 'production' ? 'production' : 'test',
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        showEdit: true
      }
    },
    getSelectedLabelList(val) {
      this.form.course_labels = val
      if (val) {
        this.$refs.form.clearValidate('course_labels')
      }
    },
    handleValidor(value, num, type) {
      if (value) {
        const china = value.match(/[\u4e00-\u9fa5]/g)
        const zhCount = china && china.join('').length
        const enCount = Math.ceil((value.length - zhCount) / 2)
        const total = zhCount + enCount
        if (total > num) {
          switch (type) {
            case '1':
              this.form.course_title = value.slice(0, -1)
              break
            case '2':
              this.form.course_statement.operation_title = value.slice(0, -1)
              break
            case '3':
              this.form.course_desc = value.slice(0, -1)
              break
            case '4':
              this.form.course_statement.ogc_supplier_name = value.slice(0, -1)
              break
            default:
              break
          }
        }
        return zhCount + enCount 
      }
      return 0
    }
  }
}
</script>
<style lang="less">
.project-create-component {
  .project-detail-tincy {
    width: 1204px;
  
    .tox.tox-tinymce {
      border: 1px solid #ccc !important;
      height: 450px;
  
      .tox-sidebar-wrap .tox-edit-area {
        min-height: 450px !important;
      }
    }
  }
  .ogc_purchase_amount.el-input-number,.human_cost.el-input-number {
    line-height: 32px;
    .el-input-number__decrease, .el-input-number__increase {
      width: 40px;
      height: 30px;
      line-height: 30px;
    }
  }
  .project_creator,.developer .sdc-selector {
    width: 500px;
  }
  .sdc-selector .selector-container .container-inner .tags {
    height: 38px;
  }
}
.project-create-component {
  @import '~@/assets/css/center.less';

  .el-radio {
    margin-right: unset;
  }

  .el-radio+.el-radio {
    margin-left: 16px;
  }

  .el-radio__inner {
    width: 16px;
    height: 16px;
  }

  .el-radio__inner::after {
    width: 8px;
    height: 8px
  }

  .el-radio__label {
    color: rgba(0, 0, 0, 0.9);
  }

  .el-radio__input.is-checked+.el-radio__label {
    color: rgba(0, 0, 0, 0.9);
  }
}
.sdc-selector {
  .el-input .el-input__inner {
    height: 28px;
    line-height: 28px;
  }
}
</style>
<style lang="less" scoped>
.project-create-component {
  background-color: white;
  padding: 16px 20px 80px 20px;
  // .project-tag-box {
  //   width: 496px;
  // }
  .edit-form {
    .warining-time-tips {
      color: #000000;
      font-size: 14px;
      opacity: 0.4;
    }
  }
  .text-blue {
    color: #0052D9;
    cursor: pointer;
  }

  .title-box {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    img {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .bassinfo-class-title {
      color: #000000;
      font-weight: bold;
      line-height: 22px;
      display: inline-block;
    }
  }

  .warining-time-tips {
    color: red; 
  }
  .project-time-tips {
    color: #000000;
    font-size: 14px;
    opacity: 0.4;
  }

  .project-time-tips {
    opacity: 0.4;
    margin-left: 16px;
  }

  .base-info {
    .test-tips {
      color: #9d9d9d;
      font-size: 12px;
      .color-red {
        color: #d63535;
      }
      .text-undeline {
        text-decoration: underline;
        cursor: pointer;
      }
    }
    .custom-el-input-count {
      color: #ACACAC;
      background: #FFF;
      position: absolute;
      font-size: 12px;
      bottom: 6px;
      right: 6px;
      line-height: 20px;
    }
    .input-style {
      position: relative;
      :deep(.el-form-item__content) {
        width: 496px;
      }
      :deep(.el-input) {
        .el-input__inner {
          padding-right: 70px;
        }
        .el-input__suffix {
          position: absolute;
          right: 43px;
        }
      }
    }
    .course-texTarea-input {
      position: relative;
      :deep(.el-form-item__content) {
        width: 1089px;
      }
      :deep(.el-input) {
        .el-input__inner {
          padding-right: 70px;
        }
        .el-input__suffix {
          position: absolute;
          right: 43px;
        }
      }
    }
    .course-name-input {
      width: 496px;
    }

    .course-tag-tips {
      color: #666666;
      font-size: 12px;
      line-height: 12px;
      margin-top: 5px;
    }

    .project-time-box {
      line-height: 22px;
      margin-bottom: 16px;

      .start-time-box {
        background: #f9f9f9;
        border-radius: 4px;
        padding: 8px 16px;
        margin-left: 25px;
        margin-top: 8px;
        width: 504px;

        .label {
          margin-right: 16px;
        }
      }
    }

    .project-time-box:last-of-type {
      margin-bottom: unset;
    }
  }

  .base-set {
    .warning-tips {
      color: #FF7548;
      i {
        margin-right: 3px;
      }
    }
    :deep(.sdc-selector) {
      width: 500px;
    }

    .certificate-box {
      margin-top: 12px;

      .item-name {
        background: #ecf2fe;
        border-radius: 4px;
        padding: 0px 16px;
        margin-right: 16px;
        height: 36px;
        line-height: 36px;
        display: inline-block;

        span {
          line-height: 22px;
          height: 22px;
          display: inline-block;
        }
      }
    }
    .content-origaniztion {
      display: flex;
      .tip-warm {
        opacity: 0.4;
        margin-left: 16px;
        color: #000000;
        font-size: 14px;  
      }
    }
  }
  .other-info {
    .parent-content-id {
      display: flex;
      .content-type {
        position: relative;
        width: 195px;
      }
      .content-id {
        position: relative;
        margin-left: 20px;
        width: 195px;
      }
      .error-tip-text-customer {
        position: absolute;
        bottom: -25px;
        left: 0;
        width: 100%;
        color: red;
        font-size: 12px;
      }
      .get-content-id {
        margin-left: 20px;
        color: #0052D9;
        cursor: pointer;
        font-weight: bold;
      }
    }
    .creation_source_sub_content {
      background: #f9f9f9;
      border-radius: 4px;
      margin-top: 5px;
      padding: 8px 16px;
      width: 600px;
      // height: 100px;
      .el-form-item {
        margin-bottom: 20px;
      }
      .no-form-item {
        margin-bottom: 0px;
      }
    }
    .origin-warm {
      margin: 0 0 10px;
      font-size: 12px;
      line-height: 18px;
      color: #999999;
    }
    .expert-rating, .is-required {
      .tip-warm {
        opacity: 0.4;
        margin-left: 16px;
        color: #000000;
        font-size: 14px;  
      }
    }
    .expert-rating-input {
      width: 180px;
    }
    .custom-el-input-count {
      color: #ACACAC;
      background: #FFF;
      position: absolute;
      font-size: 12px;
      bottom: 6px;
      right: 6px;
      line-height: 20px;
    }
    .course-texTarea-input {
      position: relative;
      :deep(.el-input) {
        .el-input__inner {
          padding-right: 74px;
        }
        .el-input__suffix {
          position: absolute;
          right: 50px;
        }
      }
    }
    .async-icon {
      color:#E34D59;
      margin-left: 6px;
    }
  }
  .red-tips-customer {
    color: #d63535;
    font-size: 12px;
    height: 12px;
    line-height: 12px;
  }
  .mgr-5 {
    margin-right: 5px;
  }
  .submit-must-tips {
    margin-bottom: 20px;
    display:flex;
    .submit-label {
      margin-left: 25px;
    }
    .submit-label::before {
      content: '*';
      color: red;
      margin-right: 2px;
    }
    .el-link {
      margin-left: 7px;
    }
    .tips {
      color: #0006;
      margin-top: 10px;
      margin-left: 12px;
      line-height: 24px
    }
    .red-tips {
      color: red
    }
    
  }
  :deep(.new-bottom) {
    padding-left: 30px;
    .inner {
      margin: initial;
    }
  }

  .contact-person {
    font-size: 12px;
    color: #999999;
    line-height: 18px;
    font-style: italic;
  }
  .contact-person-radio {
    margin: -8px 0 5px 0;
  }
}
.update-dialog {
  .update-content-box {
    position: relative;
    display: flex;
    align-items: center;
    &::after {
      position: absolute;
      left: 75px;
      bottom: -18px;
      content: '（注：修改内容将通过企微机器人同步提醒给课程管理员，请留意。）';
      color: #d63535;
      font-size: 12px;
    }
  }
  .dialog-label {
    flex-shrink: 0;
    margin-right: 20px;
  }
  .update-text-input {}
}
</style>
