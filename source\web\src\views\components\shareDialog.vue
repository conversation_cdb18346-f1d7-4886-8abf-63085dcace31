<template>
  <el-dialog
    :title="dialogTitle"
    width="430px"
    :close-on-click-modal="false"
    :before-close="closeDialog"
    custom-class="none-border-dialog"
    :visible.sync="isShow">
    <CustomTips
      v-if="initData.previewType"
      title="预览页面仅支持查看培训内容，不会更新培训进度" 
      IconName="el-icon-warning-outline" 
      backgroundColor="#fdf6ec" 
      color="#FF7548"
      lineHeight="40px"
      >
    </CustomTips>
    <p :class="[{'preview-tips': initData.previewType} ,'qrCode-tip']">{{ initData.taskTitle || '' }}</p>
    <div class="qrCode-box">
      <img v-if="qrUrl" :src="qrUrl" />
    </div>
    <el-input v-model="initData.url" type="text" disabled>
      <template slot="append">
        <el-button @click="doCopy()">{{ $langue('Mooc_ProjectDetail_Copy', { defaultText: '复制' }) }}</el-button>
      </template>
    </el-input>
  </el-dialog>
</template>

<script>
import { 
  getMobileQrcode
} from '@/config/mooc.api.conf.js'
import CustomTips from '@/components/tips.vue'
export default {
  name: 'authLogDialog',
  components: {
    CustomTips
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      qrUrl: '',
      initData: {}
    }
  },
  computed: {
    dialogTitle() {
      return this.title || this.$langue('Article_Share', { defaultText: '分享' })
    }
  },
  methods: {
    initCode(data) {
      this.initData = data
      const params = {
        scene: data.scene,
        page: data.page,
        env_version: process.env.NODE_ENV === 'production' ? 'release' : 'trial'
      }
      getMobileQrcode(params).then((res) => {
        this.qrUrl = `data:image/png;base64,${res}`
      })
    },
    closeDialog() {
      this.$emit('update:isShow', false)
    },
    doCopy() {
      const { customText, url } = this.initData
      const text = this.initData && this.initData.customText ? customText : url
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      input.value = text
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      this.$message.success(this.$langue('Mooc_Common_Alert_CopySucessed', { defaultText: '复制成功' }))
      // 复制后移除输入框
      input.remove()
    }
  }
}
</script>
<style lang="less" scoped>
.qrCode-box {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 28px;
  margin-top: 28px;
  img {
    width: 200px;
    height: 200px;
  }
}
// .qrCode{
//   width: 120px;
//   height: 120px;
// }
.qrCode-tip{
  color: #00000099;
  font-size: 16px;
  text-align: left; 
}
:deep(.el-input-group__append){
  background-color: #fff;
  color: #000000e6;
}
.custom-container {
  margin-bottom: 16px;
}
.preview-tips {
  text-align: center;
  line-height: 22px;
}
</style>
