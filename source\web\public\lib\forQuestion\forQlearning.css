#root-container .page_stat .nav.top,
#root-container .page_stat .nav.second {
  display: none;
}
#root-container .page_stat .second-nav {
  padding-top: 0;
}
#root-container .page_stat .layout-left .wj-side-nav-container {
  top: 10px;
}
#root-container .page_stat .layout-left .wj-side-nav-container .wj-side-nav-list li.wj-side-nav-list-item:nth-child(5) {
  display: none;
}
#root-container .page-edit .nav.top,
#root-container .page-edit .nav.second {
  display: none;
}
#root-container .page-edit .edit-main {
  padding: 10px 16px 16px;
}
/* 这里我想只有最后一个t-tabs__nav-item才显示，前面的其他t-tabs__nav-item都隐藏。怎么写呢？ */
#root-container .page-edit .edit-main .t-tabs .t-tabs__header .t-tabs__nav .t-tabs__nav-container .t-tabs__nav-wrap .t-tabs__nav-item {
  display: none;
}
#root-container .page-edit .edit-main .t-tabs .t-tabs__header .t-tabs__nav .t-tabs__nav-container .t-tabs__nav-wrap .t-tabs__nav-item:last-child {
  display: block;
}




