<!-- eslint-disable vue/no-unused-vars -->
<template>
  <div class="project-task-tree">
    <p class="task-title">任务组织</p>
    <div class="task-body">
      <div class="task-operate-btns">
        <div class="task-operate-btns-left" v-if="isShowOptBtn">
          <el-button type="primary" size="small" @click="addTask('top')">添加任务</el-button>
          <el-button plain size="small" @click="addTaskGroup({}, { type: 'add', task_type: 'stage' })">新增阶段</el-button>
          <el-button plain size="small" @click="addTaskGroup({}, { type: 'add', task_type: 'group' })">新增任务组</el-button>
          <el-dropdown class="batch-btn" @command="handleBatchCommand">
            <el-button plain size="small">
              批量操作<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in batchBtnList" :key="item.name" :command="item.evt">
                {{ item.name }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-select v-model="learnTypeValue" size="small" placeholder="学习模式" @change="handleStudyModel">
            <el-option v-for="item in learnTypeList" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <div @click="expandHandle" class="all-chioce">
            {{ isExpandAll ? '全部收起' : '全部展开' }}
            <span :class="[isExpandAll ? 'icon-arrow-right': 'icon-arrow-down', 'arrow-icon']"></span>
          </div>
        </div>
        <div class="task-operate-btns-left" v-else>
          <el-button :disabled="isApprove" type="primary" size="small" @click="handleEdit">编辑</el-button>
          <!-- <el-tooltip class="item" effect="dark" content="项目已结束，无法编辑任务" placement="top-start" :disabled="projectManageInfo.course_status !== 2">
            <span><el-button :disabled="projectManageInfo.course_status === 2" type="primary" size="small" @click="handleEdit">编辑</el-button></span>
          </el-tooltip> -->
        </div>
      <div  class="task-operate-btns-right">
        <el-tag class="tag" v-show="!isShowOptBtn">{{learnTypeValue ? '顺序解锁模式' : '自由学习模式'}}</el-tag>
        <span>包含{{stageTaskInfo.stageList?.length}}个阶段，{{stageTaskInfo.taskList?.length}}个任务</span>
      </div>
      </div>
      <div class="table-wrap">
        <div class="custom-table">
          <span :class="[{'edit-item-th': !isShowOptBtn}, 'item-th', 'first-item-th']" style="width: 30%">
            <el-checkbox v-if="isShowOptBtn" v-model="taskCheckAll" @change="handleChangeTable" class="task-checkall">任务名称</el-checkbox>
            <span v-else>任务名称</span>
          </span>
          <span class="item-th" style="width:10%">类型</span>
          <span class="item-th" style="width:10%">性质</span>
          <span class="item-th" style="width:10%">时长/字数</span>
          <span class="item-th" v-if="!isShowOptBtn" style="width:15%">任务进度</span>
          <span class="item-th" :style="operateWidth">操作</span>
        </div>
        <el-tree
        ref="taskTree"
        class="tree-line"
        :indent="0"
        :props="treeProps"
        :data="treeData"
        node-key="id"
        icon-class=" "
        :default-expand-all="isExpandAll"
        :expand-on-click-node="false"
        :draggable='isShowOptBtn'
        :allow-drop="allowDrop"
        @node-drop="allowDropSuccess"
        @node-drag-start="nodeDragStart"
        >
          <div slot-scope="{ node, data }"
          :class="
          [{'stage-row': data.task_type === 'stage'},
          {'group-row': data.task_type === 'group'},
           'tree-row']"
          >
            <div :class="
            ['item-tree-column',
            {'need-left-column': data.task_type === 'task' },
            {'first-tree-column': ['group', 'stage'].includes(data.task_type)},
            {'parent-stage-task': node.parent.level === 1 && data.task_type === 'task'},
            {'parent-group-task': node.parent.level === 2 && data.task_type === 'task' }
            ]"
            style="width: 30%;"
            >
              <!-- 图标 -->
              <span @click="expandTree(node, data)">
                <span
                v-if="['group', 'stage'].includes(data.task_type)"
                :class="[node.expanded ? 'tree-icon-down' : 'tree-icon-right', 'tree-icon']"
                >
                </span>
              </span>
              <!-- 任务复选框 -->
              <span class="task-check-box" v-if="isShowOptBtn">
                <el-checkbox v-if="data.task_type === 'task'" v-model="node.checked" @change="handleSingleCheck($event, data)"></el-checkbox>
                <img v-if="data.lock_status === 2" src="@/assets/mooc-img/lock-on.png" />
              </span>
              <!-- 替换复选框样式 -->
              <span class="task-check-box" v-else>
                <span v-if="data.task_type === 'task'" class="check-cover"></span>
                <img v-if="data.lock_status === 2" src="@/assets/mooc-img/lock-on.png" />
              </span>
              <!-- 输入框和文本展示 -->
              <span class="input-text-box">
                <span v-if="showInputNode != 'curNode' + node.id" :class="[data.task_type === 'task'? 'tree-text-label': 'show-tree-label']" @click="() => editTaskName(node, data)">
                  <span class="text">{{ node.label }}</span>
                  <span v-if="data.task_type != 'task'"> {{ nodeTaskCount(node, data) }}</span>
                </span>
                <!-- 输入框 -->
                <div v-else class="input-style">
                  <el-input :ref="'curNodeRef' + node.id" v-model="showInputNodeModel" @blur="handleBlur(node, data)" clearable></el-input>
                  <span class="custom-el-input-count">{{handleValidor(showInputNodeModel, 50, '1')}}/50</span>
                </div>
              </span>
            </div>
            <span class="item-tree-column" style="width:10%" v-if="data.task_type == 'task'">{{ node.data.resource_type_name}}</span>
            <span class="item-tree-column" style="width:10%" v-if="data.task_type == 'task'">
              <el-switch v-model="data.required" v-if="isShowOptBtn" :active-value="true" :inactive-value="false"
                active-color="#0052D9" inactive-color="#DCDCDC">
              </el-switch>
              <span :class="[!isShowOptBtn ? (data.required ? 'is-req req-tag' : 'un-req req-tag') : '']">
                {{ data.required ? '应学' : '选学' }}
              </span>
            </span>
            <span class="item-tree-column" style="width:10%" v-if="data.task_type == 'task'">
              {{ data.word_number ? `${data.word_number}字` : data.duration ? `${data.duration}分钟` : '-' }}
            </span>
            <!-- <span class="item-tree-column" style="width:168px" v-if="data.task_type == 'task'">
              {{ node.data.unlock_time }}
            </span> -->
            <!-- 任务进度 -->
            <div class="item-tree-column" v-if="!isShowOptBtn && data.task_type == 'task'" style="width:15%">
              <span>{{ data.finished_count || 0 }} / {{ projectManageInfo.user_count || 0 }}</span>
              <span>（{{ finishProgress(data.finished_count) }}）</span>
            </div>
            <div class="item-tree-column" :style="operateWidth">
              <div v-if="isShowOptBtn" class="btn-box">
                <el-button v-if="data.task_type != 'task'" type="text" size="mini" @click="() => addTask('right', data)">
                  添加任务
                </el-button>
                <el-button v-if="data.task_type == 'stage'" type="text" size="mini"
                  @click="() => addTaskGroup(data, { type: 'add', task_type: 'group', currentGroup: true })">
                  新增任务组
                </el-button>
                <el-button type="text" size="mini"
                  @click="() => editTaskGroup(node, { type: 'edit', task_type: data.task_type }, data)">
                  编辑
                </el-button>
                <el-button v-if="data.task_type != 'stage'" type="text" size="mini" @click="() => handleRemove(node, data)">
                  移动
                </el-button>
                <el-button v-if="data.task_type == 'task'" type="text" size="mini" @click="() => lock(node, data)">
                  {{ node.data.lock_status === 2 ? '解锁' : '锁定' }}
                </el-button>
                <!-- <el-button type="text" size="mini" @click="() => delNote(node, data)">
                  删除
                </el-button> -->
                <el-link :underline="false" type="danger" @click="() => delNote(node, data)">删除</el-link>
                <i class="el-icon-rank"></i>
              </div>
              <div v-else>
                <el-button v-if="data.task_type == 'task'" type="text" size="mini" @click="() => showDetail(node, data)">
                  详情
                </el-button>
                <el-button v-if="data.task_type == 'task'" type="text" size="mini" @click="() => showShare(node, data)">
                  分享
                </el-button>
              </div>
            </div>
          </div>
        </el-tree>
      </div>
      <bottomFiexd v-show="isShowOptBtn" @cancel="cancel" @save="saveTree"></bottomFiexd>
    </div>

    <!-- 任务详情弹窗 -->
    <TaskDetailsPopup v-if="isShowDetailsPopup" :isShowDetailsPopup.sync="isShowDetailsPopup"
      :details-info="currentDetails" />
    <!-- 分享弹窗 -->
    <shareDialog ref="shareDialog" title="分享任务" type="task" :isShow.sync="sharedialogShow" />
    <!-- 选择任务类型弹窗 -->
    <AddTaskDialog
    v-if="addTaskSceneShow"
    :visible.sync="addTaskSceneShow"
    @receiveType="receiveType"
    :currentNode="curOperateNode"
    />
    <!-- 添加阶段和任务组 -->
    <AddStageDialog v-if="addStageSceneShow" :visible.sync="addStageSceneShow" :taskInfo="taskInfo"
      :treeNode.sync="treeData" :currentNode="curOperateNode" @updateStageOrGroup="updateStageOrGroup" />
    <!-- 添加课程 -->
    <AddCourseDialog
    v-if="addLineCourseShow"
    ref="addLineCourse"
    :currentNode="curOperateNode"
    @updateTreeList="updateTreeList"
    :visible.sync="addLineCourseShow"
    @handleShowSetDialog="handleShowSetDialog"
    :treeNode.sync="treeData"
    />
    <!-- 添加问卷 -->
    <!-- <AddQuestionnairDialog v-if="addQuestionnaireShow" :visible.sync="addQuestionnaireShow" @openQuestionnair="openQuestionnair" /> -->
    <!-- 添加考试 -->
    <AddExamDialog
    ref="addExamDialog"
    v-if="addExamDialogShow"
    :currentNode="curOperateNode"
    @updateTreeList="updateTreeList"
    :visible.sync="addExamDialogShow"
    :treeNode.sync="treeData"
    />
    <!-- 添加练习 -->
    <AddPracticeDialog
    v-if="addPracticeShow"
    ref="addPracticeDialog"
    :currentNode="curOperateNode"
    @updateTreeList="updateTreeList"
    :visible.sync="addPracticeShow"
    :treeNode.sync="treeData"
    />
    <!-- 添加外链 -->
    <AddOutLinkShow ref="addOutLinkDialogRef" 
    v-if="addOutLinkShow" 
    :outLinkType="outLinkType"
    :currentNode="curOperateNode" 
    @updateTreeList="updateTreeList" 
    :visible.sync="addOutLinkShow"
    :treeNode.sync="treeData"
    />
    <!-- 添加课程素材 -->
    <AddMaterialDialog
    v-if="addMaterialShow"
    ref="addMaterial"
    :currentNode="curOperateNode"
    @updateTreeList="updateTreeList"
    :visible.sync="addMaterialShow"
    :treeNode.sync="treeData"
    @handleShowSetDialog="handleMaterialSetDialog"
    />
    <!-- 添加第三方任务 -->
    <AddThirdPartyShow
    v-if="addThirdPartyShow"
    ref="addThirdPartyDialog"
    :type="thirdPartyType"
    :currentNode="curOperateNode"
    @updateTreeList="updateTreeList"
    :visible.sync="addThirdPartyShow"
    :treeNode.sync="treeData"
    />
    <!-- 编辑考试--练习--线上课程 -->
    <EditTask
    :visible.sync="editTaskDialog"
    :currentNode="curOperateNode"
    ref="editTaskDialogRef"
    @updateTreeList="updateTreeList"
    @openQuestionConfirm="openQuestionConfirm"
    :treeNode.sync="treeData"
    />
    <!-- 批量锁定/解锁 -->
    <BatchOpenLock
    v-if="batchOpenLockShow"
    :visible.sync="batchOpenLockShow"
    @handleLockStatus="handleLockStatus"
    :initData="batchLockAndCourse"
    />
    <!-- 批量移动 -->
    <BatchMove
    v-if="batchMoveShow"
    :visible.sync="batchMoveShow"
    :treeNode.sync="treeData"
    :moveInfo="moveInfo"
    @handleBatchMove="handleBatchMove"
    />
    <AddCourseSetDialog
    v-if="addCourseSetShow"
    :visible.sync="addCourseSetShow"
    :tableData="addCourseSetData"
    :taskTypeInfo="taskTypeInfo"
    @updateTreeList="updateTreeList"
    />
    <ErrDialog
    v-if="errDialogShow"
    :visible.sync="errDialogShow"
    @handleRefresh="getTaskTreeList"
    :lastSubmitInfo="lastSubmitInfo"
    />
    <AddQuestionnairConfirmDialog
    :visible.sync="addQuestionnairConfirmDialogShow"
    :questionType="questionType"
    @continueCreateQuestionnaire="continueCreateQuestionnaire"
    v-if="addQuestionnairConfirmDialogShow"
    />
    <!-- <addErrorDialog :visible.sync="addErrorDialogShow" :taskNameList="taskNameList"></addErrorDialog> -->
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { taskTreeList, saveTreeData, setLockByStep } from '@/config/mooc.api.conf.js'
import {
  AddTaskDialog,
  AddStageDialog,
  AddCourseDialog,
  AddExamDialog,
  AddPracticeDialog,
  AddOutLinkShow,
  AddMaterialDialog,
  AddThirdPartyShow,
  EditTask,
  BatchOpenLock,
  BatchMove
  // AddQuestionnairDialog
} from './component/index'
import TaskDetailsPopup from '../report/component/task-details-popup.vue'
import shareDialog from '@/views/components/shareDialog.vue'
import AddCourseSetDialog from './component/add-course-set-dialog.vue'
import ErrDialog from './component/err-dialog.vue'
// import addErrorDialog from './component/add-error-dialog.vue'
import bottomFiexd from '@/views/components/botttomFixed.vue'
import AddQuestionnairConfirmDialog from './component/add-questionnairConfirmDialog.vue'
export default {
  components: {
    AddTaskDialog,
    AddStageDialog,
    AddCourseDialog,
    AddExamDialog,
    AddPracticeDialog,
    AddOutLinkShow,
    AddMaterialDialog,
    AddThirdPartyShow,
    TaskDetailsPopup,
    shareDialog,
    EditTask,
    BatchOpenLock,
    BatchMove,
    AddCourseSetDialog,
    ErrDialog,
    bottomFiexd,
    AddQuestionnairConfirmDialog
    // AddQuestionnairDialog
  },
  data() {
    return {
      isExpandAll: true,
      isShowOptBtn: false,
      batchBtnList: [
        { name: '移动', evt: 'move' },
        { name: '设置锁定/解锁', evt: 'isLock' },
        { name: '设置应学/选学', evt: 'isSetRequired' },
        { name: '删除', evt: 'del' }
        // { name: '导入课程', evt: 'import' }
      ],
      learnTypeList: [
        { label: '自由学习模式', value: false },
        { label: '顺序解锁模式', value: true }
      ],
      taskCheckAll: false,
      treeProps: {
        label: 'task_name',
        children: 'sub_tasks'
      },
      treeData: [],
      isShowDetailsPopup: false,
      currentDetails: null,
      sharedialogShow: false,
      addTaskSceneShow: false,
      checkedTaskType: 0,
      addStageSceneShow: false,
      showInputNode: '',
      showInputNodeModel: '',
      addLineCourseShow: false,
      addExamDialogShow: false,
      addPracticeShow: false,
      addOutLinkShow: false,
      addMaterialShow: false,
      addThirdPartyShow: false,
      addQuestionnaireShow: false, // 添加问卷弹窗
      operateSceneTitle: '添加外部链接',
      thirdPartyType: 'create',
      curOperateNode: null,
      editTaskDialog: false,
      taskInfo: {},
      batchOpenLockShow: false,
      batchMoveShow: false,
      batchLockAndCourse: {},
      learnTypeValue: false,
      checkedTaskList: [],
      addCourseSetData: [],
      addCourseSetShow: false,
      stageTaskInfo: {
        stageList: [],
        taskList: []
      },
      cacheDeleteTask: [],
      lastSubmitInfo: {},
      errDialogShow: false,
      treeChangeCount: 0,
      addQuestionnairConfirmDialogShow: false,
      questionType: 'add'
    }
  },
  computed: {
    ...mapState(['projectManageInfo', 'workInfoData']),
    mooc_course_id() {
      const { mooc_course_id } = this.projectManageInfo
      return mooc_course_id || this.$route.query.mooc_course_id
    },
    finishProgress() {
      return (finished_count) => {
        const user_count = this.projectManageInfo.user_count
        return finished_count && user_count ? ((finished_count / user_count) * 100).toFixed() + '%' : 0
      }
    },
    operateWidth() {
      const width = this.isShowOptBtn ? '280px' : '200px'
      return `min-width: ${width}`
    },
    isApprove() {
      // 审批-审批中
      return this.$route.query.approve === '1' || this.projectManageInfo.course_status === 6
    }
  },
  watch: {
    treeData: {
      deep: true,
      immediate: true,
      handler(data) {
        this.treeChangeCount++
      }
    },
    projectManageInfo: {
      deep: true,
      immediate: true,
      handler(data) {
        this.learnTypeValue = data?.unlocked_by_step ? Boolean(true) : Boolean(false)
      }
    }
  },
  mounted() {
    this.getTaskTreeList()
    // 添加message事件监听
    window.addEventListener('beforeunload', e => this.beforeunloadHandler(e))
  },
  beforeDestroy() {
    // 移除监听
    window.removeEventListener('beforeunload', this.beforeunloadHandler)
  },
  created() {
    // 添加作业窗口回传的数据
    window.workReConnect = (workData) => {
      this.updateTreeList([workData], workData.workType === 'edit' ? 'edit' : 'add')
    }
    window.questionReConnect = (questionData) => {
      this.updateTreeList([questionData], questionData.questionType)
    }
  },
  methods: {
    // 展开收起
    expandTree(node) {
      node.expanded = !node.expanded
      for (let i = 0; i < node.childNodes.length; i++) {
        node.childNodes[i].expanded = !node.childNodes[i].expanded
        if (node.childNodes[i].childNodes.length > 0) {
          this.expandTree(node.childNodes[i])
        }
      }
    },
    beforeunloadHandler(e) {
      let _this = this
      if (_this.$route.name === 'task-list' && _this.treeChangeCount > 2) {
        e = e || window.event
        // 兼容IE8和Firefox 4之前的版本
        if (e) {
          e.returnValue = '关闭提示'
        }
        // Chrome, Safari, Firefox 4+, Opera 12+ , IE 9+
        return '关闭提示'
      }
    },
    // 编辑
    handleEdit() {
      if (this.projectManageInfo.course_status === 1) { // 已发布
        this.$messageBox.confirm(`当前项目已发布，调整任务可能会影响学员的培训结果，确定编辑吗？`, '确认编辑', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          this.isShowOptBtn = true
        })
        return
      }
      this.isShowOptBtn = true
    },
    // 勾选全部
    handleChangeTable(val) {
      this.taskCheckAll = val
      if (val) {
        this.commonCheck()
        const ids = this.checkedTaskList.map((e) => e.id) // 所有任务节点
        this.$refs.taskTree.setCheckedKeys(ids, true)
      } else { // 清空所有节点
        this.checkedTaskList = [] // 清除所有勾选的数据
        this.$refs.taskTree.setCheckedKeys([], false)
      }
      // 勾选-取消勾选后更新节点，防止再次勾选没能选中
      this.treeData = JSON.parse(JSON.stringify(this.treeData))
    },
    // 单条勾选
    handleSingleCheck() {
      this.commonCheck()
      const formateChecked = (nodes = []) => {
        nodes.forEach((v) => {
          this.checkedTaskList.forEach((e, index) => {
            if (e.id === v.data.id && !v.checked) {
              this.checkedTaskList.splice(index, 1)
            } else {
              if (v.childNodes?.length) {
                formateChecked(v.childNodes)
              }
            }
          })
        })
      }
      formateChecked(this.$refs.taskTree.root.childNodes)
      // 判断全部复选框是否需要勾选
      this.taskCheckAll = !!this.checkedTaskList?.length
    },
    commonCheck() {
      const list = []
      // 获取所有的节点id
      const formateAllCheck = (newTree = []) => {
        newTree.forEach((e) => {
          if (e.task_type === 'task') {
            // 勾选的所有数据
            list.push(e)
          } else {
            if (e.sub_tasks?.length) {
              formateAllCheck(e.sub_tasks)
            }
          }
        })
      }
      formateAllCheck(this.treeData)
      this.checkedTaskList = JSON.parse(JSON.stringify(list)) // 勾选的数据
    },
    // 判定目标节点能否被放置
    allowDrop(draggingNode, dropNode, type) {
      if (draggingNode.data.task_type === dropNode.data.task_type) {
        return type !== 'inner'
      }
      if (draggingNode.data.task_type === 'stage' && dropNode.data.task_type !== 'stage' && draggingNode.level >= dropNode.level) {
        // 阶段只能前置后置不能插入到任何位置
        return type !== 'inner'
      } else if (draggingNode.data.task_type === 'group' && draggingNode.level >= dropNode.level) {
        if (['stage'].includes(dropNode.data.task_type)) {
          return true
        }
        return type !== 'inner'
      } else if (draggingNode.data.task_type === 'task' && dropNode.data.task_type !== 'task') {
        return true
      }
    },
    // 全部展开/收起
    expandHandle() {
      this.isExpandAll = !this.isExpandAll
      let node = this.$refs.taskTree.store.root
      this.nodeDragStart(node)
    },
    // 拖动节点前默认收起
    nodeDragStart(node) {
      node.expanded = this.isExpandAll
      for (let i = 0; i < node.childNodes.length; i++) {
        node.childNodes[i].expanded = !node.childNodes[i].expanded
        if (node.childNodes[i].childNodes.length > 0) {
          this.nodeDragStart(node.childNodes[i])
        }
      }
    },
    // 拖动成功后的回调
    allowDropSuccess() {
      this.handleNodeId(this.treeData)
    },
    nodeTaskCount(node, data) {
      const groupL = data?.sub_tasks?.filter((item) => { return item.task_type === 'group' }) || []
      const taskLen = data?.sub_tasks?.filter((item) => { return item.task_type === 'task' }) || []
      const groupTaskChildren = groupL?.map(item => { return item.task_type === 'task' }) || []
      if (node.level === 1) {
        const levelTip1 = taskLen.length > 0 ?
          (groupL.length > 0 ? `(包含${groupL.length}个任务组，${taskLen.length + groupTaskChildren.length}个任务)`
            : `(包含${taskLen.length}个任务)`)
          : ''
        return levelTip1
      } else if (node.level === 2) {
        const levelTip2 = taskLen.length > 0 ? `(包含${taskLen.length}个任务)` : ''
        return levelTip2
      }
    },
    getTaskTreeList(value) {
      const oldTreeList = JSON.parse(JSON.stringify(this.treeData))
      taskTreeList(this.mooc_course_id).then((res) => {
        const { task_tree, task_last_submit_time, task_last_submitor_name } = res
        this.lastSubmitInfo = {
          task_last_submit_time,
          task_last_submitor_name
        }
        let newTree = this.handleNodeId(task_tree || [])
        // 列表数据有更新时处理
        if (value === 'refresh') {
          if (oldTreeList?.length) {
            const merge = (oldTree = []) => {
              newTree.forEach((e) => {
                oldTree.forEach((v) => {
                  // 阶段，任务组
                  if (v.task_name !== e.task_name && v.task_type !== 'task') {
                    newTree.push(v)
                    const map_list = new Map()
                    newTree = newTree.filter((e) => !map_list.has(e.task_name) && map_list.set(e.task_name, 1))
                    // 重新更新数据
                    newTree = this.handleNodeId(newTree || [])
                    // 把新增的数据添加到最新的列表中去
                  } else if (!v.task_id && v.task_type === 'task') {
                    if (e.parent_Id_Diy === v.parent_Id_Diy && e.task_type !== 'task') {
                      if (e.sub_tasks?.length) {
                        e.sub_tasks.push(v)
                      } else {
                        e.sub_tasks = [v]
                      }
                    }
                  } else {
                    if (v.sub_tasks?.length) {
                      merge(v.sub_tasks)
                    }
                  }
                })
              })
              return newTree
            }
            this.treeData = this.handleNodeId(merge(oldTreeList))
          }
        }
      })
    },
    // 学习模式
    handleStudyModel() {
      const lockParam = {
        mooc_course_id: this.mooc_course_id,
        unlocked_by_step: this.learnTypeValue
      }
      setLockByStep(lockParam).then((res) => {
        this.$message.success('操作成功')
      })
    },
    showDetail(node, data) {
      const { act_id, resource_type, task_id, mooc_course_id, staff_name, id, act_type } = data
      if (act_type === '32') {
        window.open(`https://wj-learn.woa.com/stat/1/overview?sid=${act_id}`)
        return
      }
      if (resource_type === 'HomeWork') {
        this.$router.push({
          name: 'work-task-detail',
          query: {
            homework_id: act_id,
            task_id,
            mooc_course_id,
            staff_name,
            id
          }
        })
      } else {
        this.isShowDetailsPopup = true
        this.currentDetails = data
      }
    },
    showShare(node, data) {
      // const { act_type } = data
      // if (act_type === '29') {
      //   window.open('https://wj-learn.woa.com/stat/1/overview?sid=10006')
      //   return
      // }
      this.sharedialogShow = true
      const url = process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/TZELHU' : 'http://s.test.yunassess.com/s/urrd9E'
      this.$nextTick(() => {
        this.$refs.shareDialog.initCode({
          mooc_course_id: this.mooc_course_id,
          url: `${url}?scheme_type=mooc&mooc_course_id=${this.mooc_course_id}&task_id=${data.task_id}`,
          taskTitle: data.task_name,
          scene: `${this.mooc_course_id}_${data.task_id}_mooc`,
          page: 'pages/webview/mooc/taskDetail'
        })
      })
    },
    editTaskName(node, data) {
      if (!this.isShowOptBtn) return
      this.showInputNode = 'curNode' + node.id
      this.showInputNodeModel = node.label
      this.$nextTick(() => {
        this.$refs[`curNodeRef${node.id}`].focus()
      })
    },
    addTask(type, data) {
      this.addTaskSceneShow = true
      if (type === 'top') { // 头部新增任务
        this.curOperateNode = {}
        return
      }
      // 右测新增任务
      this.curOperateNode = data
    },
    // 新增
    addTaskGroup(data, info) {
      this.curOperateNode = data
      this.addStageSceneShow = true
      this.taskInfo = info
    },
    // 打开问卷编辑或新增页
    openQuestionnair({ type }) {
      const { href } = this.$router.resolve({
        name: 'questionnair',
        query: {
          mooc_course_id: this.mooc_course_id,
          sid: (this.curOperateNode && this.curOperateNode.act_id) || '',
          taskName: this.curOperateNode?.task_name || '',
          type,
          cutmonId: this.curOperateNode?.id || ''
        }
      })
      window.open(href)
      // this.$router.push({
      //   path: '/mooc/manage/questionnair',
      //   query: {
      //     mooc_course_id: this.mooc_course_id,
      //     type
      //   }
      // })
    },
    continueCreateQuestionnaire() {
      this.addQuestionnairConfirmDialogShow = false
      this.openQuestionnair({ type: this.questionType })
    },
    openQuestionConfirm(type) {
      this.addQuestionnairConfirmDialogShow = true
      this.questionType = type
    },
    editTaskGroup(node, info, data) {
      const { task_type, resource_type, act_id, task_id, mooc_course_id, id } = data
      if (resource_type === 'Other') { // 外链
        this.curOperateNode = data
        this.outLinkType = 'edit'
        this.addOutLinkShow = true
        this.$nextTick(() => {
          this.$refs.addOutLinkDialogRef.initData(data)
        })
      } else if (resource_type === 'ThirdParty') { // 任务
        this.curOperateNode = data
        this.thirdPartyType = 'edit'
        this.addThirdPartyShow = true
        this.$nextTick(() => {
          this.$refs.addThirdPartyDialog.initData(data)
        })
      } else if (resource_type === 'HomeWork') { // 作业
        const { href } = this.$router.resolve({
          name: 'work',
          query: {
            homework_id: act_id,
            task_id,
            mooc_course_id,
            id,
            workType: 'edit'
          }
        })
        window.open(href, '_blank')
      } else if (task_type === 'task') { // 任务
        this.editTaskDialog = true
        this.curOperateNode = data
        this.$nextTick(() => {
          this.$refs.editTaskDialogRef.initData(data)
        })
      } else { // 编辑阶段--任务组
        this.addTaskGroup(data, info)
      }
    },
    lock(node, data) {
      data.lock_status = data.lock_status === 2 ? 1 : 2
    },
    delNote(node, data) {
      const { task_type } = data
      this.cacheDeleteTask = []
      const getTaskLen = (list) => {
        (list || []).forEach((e) => {
          if (e.task_type === 'task') {
            this.cacheDeleteTask.push(e)
          }
          if (e.sub_tasks?.length) {
            getTaskLen(e.sub_tasks)
          }
        })
      }
      getTaskLen(data.sub_tasks)
      if (task_type === 'stage') { // 删除阶段
        const groupL = (data?.sub_tasks || []).filter((v) => v.task_type === 'group')
        this.$messageBox.confirm(`当前阶段包含的${groupL?.length || 0}个任务组，${this.cacheDeleteTask?.length || 0}个任务将一同删除，确定删除吗？`, '删除阶段', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
          .then(() => {
            this.$refs.taskTree.remove(node)
          })
      } else if (task_type === 'group') { // 删除任务组
        this.$messageBox.confirm(`当前任务组包含的${this.cacheDeleteTask?.length || 0}个任务将一同删除，确定删除吗？`, '删除任务组', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
          .then(() => {
            this.$refs.taskTree.remove(node)
          })
      } else { // 删除任务
        this.cacheDeleteTask.push(data)
        this.$messageBox.confirm(`任务删除后相关数据无法恢复，确定删除吗？`, '删除任务', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
          .then(() => {
            this.$refs.taskTree.remove(node)
          })
      }
    },
    handleRemove(node, data) {
      this.curOperateNode = data // 移动需要
      this.batchMoveShow = true
      this.moveInfo = {
        type: data.task_type,
        taskName: data.task_name
      }
    },
    handleBlur(node, data) {
      // 编辑任务名称
      if (!this.showInputNodeModel) {
        this.$message.warning('请输入名称')
        return
      }
      data.task_name = this.showInputNodeModel
      this.showInputNode = ''
      this.showInputNodeModel = ''
    },
    handleBatchCommand(val) {
      this.curOperateNode = {} // 清除单条数据
      if (!this.checkedTaskList?.length) {
        this.$message.warning('请先选择任务')
        return
      }
      if (val === 'del') { // 批量删除
        this.$messageBox.confirm(`已选中${this.checkedTaskList?.length}个任务，删除后相关数据无法恢复，确定删除吗？`, '批量删除任务', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
          .then(() => {
            // 页面缓存用户删除的数据，避免误删。
            this.cacheDeleteTask = JSON.parse(JSON.stringify(this.checkedTaskList))
            const formateDelete = (list = []) => {
              this.checkedTaskList.forEach((v) => {
                list.forEach((e) => {
                  if (v.id === e.id) {
                    this.$refs.taskTree.remove(e)
                  } else {
                    if (e.sub_tasks?.length) {
                      formateDelete(e.sub_tasks)
                    }
                  }
                })
              })
            }
            formateDelete(this.treeData)
          })
      } else if (val === 'move') { // 批量移动
        this.batchMoveShow = true
        this.moveInfo = {
          type: 'stage',
          num: this.checkedTaskList?.length
        }
      } else if (val === 'isLock') { // 批量锁定/解锁
        this.batchOpenLockShow = true
        this.batchLockAndCourse = {
          num: this.checkedTaskList.length,
          type: 'lock'
        }
      } else if (val === 'isSetRequired') { // 选学/应学
        this.batchOpenLockShow = true
        this.batchLockAndCourse = {
          num: this.checkedTaskList.length,
          type: 'course'
        }
      }
    },
    // 批量解锁--选学应学
    handleLockStatus(val, type) {
      const formateLock = (list = []) => {
        this.checkedTaskList.forEach((v) => {
          list.forEach((e) => {
            if (v.id === e.id) {
              if (type === 'lock') { // 解锁
                e.lock_status = val === 2 ? 2 : 1
              } else { // 选学应学
                e.required = !!val
              }
            } else {
              if (e.sub_tasks?.length) {
                formateLock(e.sub_tasks)
              }
            }
          })
        })
        return list
      }
      this.treeData = formateLock(this.treeData)
    },
    // 批量移动
    handleBatchMove(type, val) {
      // val 对应节点的id，type 所选择的需要移动的类型---阶段，任务组，根节点
      const newList = this.curOperateNode?.id ? [this.curOperateNode] : this.checkedTaskList
      newList.forEach((e) => {
        this.treeData.forEach((item, i) => {
          if (e.id !== item.id) {
            // 过滤掉相同的数据
            this.formateBatchRemove(item.sub_tasks, val, e)
          } else {
            // 删除根节点的任务
            this.formateBatchRemove(this.treeData, val, item)
          }
        })
      })
      // 根节点
      if (type === 3) this.treeData = this.treeData.concat(newList)
      // 每次更新完刷新数据
      this.handleNodeId(this.treeData)
    },
    // 开启任务
    receiveType(checkedTaskType) {
      this.checkedTaskType = checkedTaskType
      if (checkedTaskType === 1) this.addLineCourseShow = true
      if (checkedTaskType === 2) this.addExamDialogShow = true
      if (checkedTaskType === 3) this.addPracticeShow = true
      // 外部链接
      if (checkedTaskType === 4) {
        this.addOutLinkShow = true
        this.outLinkType = 'add'
      }
      if (checkedTaskType === 5) this.addMaterialShow = true
      // 作业
      if (checkedTaskType === 6) {
        const { href } = this.$router.resolve({
          name: 'work',
          query: {
            mooc_course_id: this.mooc_course_id,
            id: this.curOperateNode?.id || '',
            workType: 'add'
          }
        })
        window.open(href)
      }
      // 第三方
      if (checkedTaskType === 7) {
        this.addThirdPartyShow = true
        this.thirdPartyType = 'create'
      }

      // 问卷
      if (checkedTaskType === 8) {
        this.openQuestionConfirm('add')
      }
    },
  
    saveTree() {
      const params = {
        task_last_submit_time: this.lastSubmitInfo.task_last_submit_time,
        task_tree: this.treeData
      }
      saveTreeData(params, this.mooc_course_id).then((res) => {
        this.$message.success('保存成功')
        this.isShowOptBtn = false
        this.$store.dispatch('getProjectInfoData', this.mooc_course_id)
        this.getTaskTreeList()
      }).catch((err) => {
        const { code, message, data } = err
        if (code === -14003) {
          const { task_last_submit_time, task_last_submitor_name } = data
          this.lastSubmitInfo = {
            task_last_submit_time,
            task_last_submitor_name
          }
          this.errDialogShow = true
        } else {
          this.$Message.error({
            message: message || '网络异常，请稍后重试！',
            duration: 2000
          })
        }
      })
    },
    // 处理节点---把每个节点的id放到对应的数据中
    handleNodeId(list) {
      // 每次更新数据清空所有勾选
      this.handleChangeTable(false)
      this.stageTaskInfo = {
        stageList: [],
        taskList: []
      }
      let count = 1
      let deepFind = (data = [], id = '') => {
        if (!data?.length) return []
        data.forEach((item, index) => {
          // 获取阶段和任务的长度
          if (item.task_type === 'task') {
            this.stageTaskInfo.taskList.push(item)
          } else if (item.task_type === 'stage') {
            this.stageTaskInfo.stageList.push(item)
          }
          // 对每条数据进行排序
          item.order_no = count++
          if (id) { // 子级
            item.id = id + '_' + (index + 1)
            item.parent_Id_Diy = id
          } else { // 父级
            item.id = (index + 1) + ''
            item.parent_Id_Diy = (index + 1) + ''
          }
          if (item.sub_tasks?.length) {
            deepFind(item.sub_tasks, item.id)
          }
        })
        return data
      }
      this.treeData = JSON.parse(JSON.stringify(deepFind(list)))
      return this.treeData
    },
    // 添加或编辑阶段和任务组
    updateStageOrGroup(row, type, task_type) {
      if (type === 'edit') { // 编辑阶段-任务
        if (task_type === 'group') { // 任务组
          if (row.eidtId) { // 有id说明需要添加数据同时删除数据
            this.treeData.forEach((el) => {
              if (el.id === row.eidtId) { // 编辑任务组阶段
                if (el.sub_tasks?.length) {
                  if (this.curOperateNode.parent_Id_Diy === row.eidtId) {
                    const subIndex = el.sub_tasks.findIndex((v) => v.id === row.id)
                    if (subIndex >= 0) {
                      el.sub_tasks[subIndex].task_name = row.task_name
                    }
                  } else {
                    el.sub_tasks.push({ ...row, task_name: row.task_name })
                  }
                } else {
                  el.sub_tasks = [{ ...row, task_name: row.task_name }]
                }
              }
            })
            // 每次更新完刷新数据
            this.handleNodeId(this.treeData)
            // 删除push的row数据
            if (this.curOperateNode.parent_Id_Diy !== row.eidtId) {
              this.formateBatchRemove(this.treeData, row.id, row, false)
            }
          } else { // 只需要更改名字
            this.treeData.forEach((el) => {
              if (el.id === row.id) {
                el.task_name = row.task_name
              }
            })
            // 每次更新完刷新数据
          //  this.handleNodeId(this.treeData)
          }
        } else { // 编辑阶段
          this.treeData.forEach((el) => {
            if (el.id === row.id) {
              el.task_name = row.task_name
            }
          })
          // 每次更新完刷新数据
          this.handleNodeId(this.treeData)
        }
      } else { // 新增阶段
        const obj = {
          ...row,
          task_type,
          task_type_name: task_type === 'stage' ? '阶段' : '任务组',
          mooc_course_id: this.mooc_course_id
        }
        // 第一次添加--阶段添加
        if (!row?.id) {
          this.treeData.push(obj)
          // 每次更新完刷新数据
          this.handleNodeId(this.treeData)
          return
        }
        // 任务组添加
        this.treeData.forEach((e) => {
          if (e.id === row.id) {
            if (e.sub_tasks?.length) {
              e.sub_tasks.push(obj)
            } else {
              e.sub_tasks = [obj]
            }
          }
        })
        // 每次更新完刷新数据
        this.handleNodeId(this.treeData)
      }
    },
    // 添加任务--编辑任务
    updateTreeList(taskList, type) {
      console.log(taskList, type, '更新任务时')
      taskList.forEach((e) => {
        let taskObj = {
          ...e,
          mooc_course_id: this.mooc_course_id,
          task_type: 'task',
          task_type_name: '任务',
          required: e.required,
          lock_status: e.lock_status || 1,
          module_id: e.module_id,
          module_name: e.module_name,
          act_id: e.act_id, // 后端用来区分  在线课程---item_id  考试联系---id 外部链接不传
          act_type: e.act_type,
          task_name: e.task_name,
          act_name: e.act_name,
          resource_type_name: e.resource_type_name,
          resource_type: e.resource_type,
          // 在线课程
          course_type: e.course_type || '',
          support_mobile: e.support_mobile || '',
          // 考试--练习
          exam_end_time: e.exam_end_time || '',
          exam_start_time: e.exam_start_time || '',
          exam_time_type: e.exam_time_type || ''
        }
        // 从页面缓存的数据中查看是否有误删的，有把task_id重新赋值上去
        this.cacheDeleteTask.forEach((cache) => {
          if (e.task_name === cache.task_name) {
            taskObj.task_id = cache.task_id
          }
        })
        if (!e?.id) { // 顶部添加
          this.treeData.push(taskObj)
          return
        }
        // 右侧添加
        const formateData = (list = []) => {
          list.forEach((v) => {
            if (e.id === v.id) { // 通过比对如果是当前的节点添加数据，那么就添加
              if (type === 'edit') { // 右侧编辑
                v = Object.assign(v, taskObj)
              } else { // 新增任务
                if (v.sub_tasks?.length) {
                  v.sub_tasks.push(taskObj)
                } else {
                  v.sub_tasks = [taskObj]
                }
              }
            } else {
              if (v.sub_tasks?.length) {
                formateData(v.sub_tasks)
              }
            }
          })
          return list
        }
        this.treeData = formateData(this.treeData)
      })
      // 每次更新完刷新数据
      this.handleNodeId(this.treeData)
    },
    // 批量移除
    formateBatchRemove(list = [], val, e, add = true) {
      // val 对应节点的id
      if (list?.length) {
        for (let i = list.length - 1; i >= 0; i--) {
          let addlist = []
          if (list[i].id === e.id) {
            addlist = list.splice(i, 1)
            if (add) {
              this.addTreeNode(addlist, val)
            }
          }
          if (list[i]?.sub_tasks?.length) {
            this.formateBatchRemove(list[i].sub_tasks, val, e, add)
          }
        }
      }
    },
    //
    addTreeNode(addlist, val) {
      const a = (tree, addlist, val) => {
        tree.forEach(item => {
          if (item.id === val) {
            if (item.sub_tasks?.length) {
              item.sub_tasks.push(...addlist)
            } else {
              item.sub_tasks = [...addlist]
            }
          }
          if (item.sub_tasks?.length) {
            a(item.sub_tasks, addlist, val)
          }
        })
      }
      a(this.treeData, addlist, val)
    },
    cancel() {
      this.$messageBox.confirm(`取消后编辑的数据不会保存，确定取消吗？`, '取消编辑', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.isShowOptBtn = false
        this.getTaskTreeList()
      })
    },
    // 完成课程设置弹窗
    handleShowSetDialog(data) {
      this.addCourseSetShow = true
      this.addCourseSetData = data
      this.taskTypeInfo = {
        title: '添加线上课程',
        tips: '请设置任务完成条件，视频/音频/Scorm类型支持设置为“由课程完成条件决定”',
        type: 'course',
        name: '课程'
      }
    },
    // 素材任务完成条件设置弹框
    handleMaterialSetDialog(data) {
      this.addCourseSetShow = true
      this.addCourseSetData = data
      this.taskTypeInfo = {
        title: '添加课程素材',
        tips: '请设置任务完成条件，scorm类型支持设置为“由素材完成条件决定”',
        type: 'material',
        name: '素材'
      }
    },
    handleValidor(value, num, type) {
      if (!value) return 0
      const china = value.match(/[\u4e00-\u9fa5]/g)
      const zhCount = china && china.join('').length
      const enCount = Math.ceil((value.length - zhCount) / 2)
      const total = zhCount + enCount
      if (total > num) {
        if (type === '1') {
          this.showInputNodeModel = value.slice(0, -1)
        }
      }
      return zhCount + enCount
    }
  }
}
</script>

<style lang="less" scoped>
@import '~@/assets/css/task-list.less';
</style>
<style lang="less">
.tree-line {
  .el-tree-node {
    position: relative;
  }
  .el-tree-node__content {
    margin-top: 5px;
  }
  .el-tree-node__children {
    .el-tree-node {
      padding-left: 32px;
    }
  }

  .el-tree-node::before {
    content: '';
    height: 100%;
    width: 1px;
    position: absolute;
    left: 20px;
    top: -7px;
    border-width: 1px;
    border-left: 1px solid #eeeeeeff;
  }

  .el-tree-node:last-child::before {
    height: 38px;
  }

  .el-tree-node::after {
    content: '';
    width: 8px;
    height: 20px;
    position: absolute;
    left: 20px;
    top: 30px;
    border-width: 1px;
    // border-top: 1px solid red
    border-top: 1px solid #eeeeeeff;
  }

  & > .el-tree-node::after {
    border-top: none;
  }
  & > .el-tree-node::before {
    border-left: none;
  }

  .el-tree-node__expand-icon {
    font-size: 18px;
    color: #000;
    &.is-leaf {
      color: transparent;
      // display: none;
    }
  }
}
</style>
