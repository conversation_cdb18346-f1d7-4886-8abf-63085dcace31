<template>
  <div class="comment-manage">
    <el-form :model="form" inline ref="form">
      <el-form-item label="用户名">
        <el-input v-model="form.staff_name" size="small" placeholder="请输入用户名" clearable></el-input>
      </el-form-item>
      <el-form-item label="评论内容">
        <el-input v-model="form.content" placeholder="请输入评论内容" size="small" clearable></el-input>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="form.type" placeholder="请选择状态" size="small" clearable>
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="onSearch(1)">搜索</el-button>
        <el-button type="primary" size="small" plain icon="el-icon-refresh" @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
    <div class="sort-main">
      <span class="sort-type">排序方式</span>
      <el-radio-group v-model="order_type" @change="onSearch(1)">
        <el-radio :label="1">最近发表</el-radio>
        <el-radio :label="2">点赞最多</el-radio>
        <el-radio :label="3">回复最多</el-radio>
      </el-radio-group>
    </div>
    <div class="comment-content">
      <div class="item-card" v-for="(e, index) in commentData.records" :key="e.id">
        <el-image class="avatar_img" :src= "e.avatar" alt="">
          <div class="image-slot" slot="placeholder">
            <i class="el-icon-loading"></i>
          </div>
          <div class="error-avatar" slot="error">
            <img :src="avatar" alt="" />
          </div>
        </el-image>
        <div class="ri">
          <p class='user'>
            <span class="user-name">{{e.emp_name}}</span>
            <span v-if="e.up_pass" class="up-status default-status">置顶</span>
            <span v-if="!e.need_show" class="hide-status default-status">隐藏</span>
          </p>
          <p class='price-comment one'>{{e.content}}</p>
          <div class="bottom-btn">
            <div>
              <span>{{e.created_at}}</span>
              <span v-if="e.reply_count"  class="total-reply">共{{e.reply_count}}条回复 <span class="text-blue" @click="showMore(e)">点击查看</span></span>
              <span v-else class="total-reply">暂无回复</span>
            </div>
            <div class="btn-status">
              <span :class="[{'handle-like-active':e.is_like}, {'disabled-check': isApprove}, 'default-like']" @click="handleLike(e)">
                <span class='like'></span>{{e.praise_count || 0}}
              </span>
              <span :class="{'disabled-check': isApprove}" class="opera-btn" @click="handleTop(e, index)"><i :class="[e.up_pass ? 'no-top-reply' : 'top-reply']"></i>{{e.up_pass ? '取消置顶' : '置顶'}}</span>
              <span :class="{'disabled-check': isApprove}" class="opera-btn" @click="handleVisible(e, index)"><i :class="[e.need_show ? 'hide-reply' : 'visible-reply']"></i>{{e.need_show ? '隐藏' : '显示'}}</span>
              <span :class="{'disabled-check': isApprove}" class="opera-btn" @click="handleReply(index)" v-if="scene !== 1"  ><i class="reply-content"></i>回复</span>
              <span :class="{'disabled-check': isApprove}" class="opera-btn" @click="handleDelete(e)"><i class="delete-reply"></i>删除</span>
            </div>
          </div>
          <div class="reply-box" v-show="e.replyContentFlag">
            <div class="reply-textarea">
              <el-image class="avatar_img" :src= "replyAvatar" alt="">
                <div class="image-slot" slot="placeholder">
                  <i class="el-icon-loading"></i>
                </div>
                <div class="error-avatar" slot="error">
                  <img :src="avatar" alt="" />
                </div>
              </el-image>
              <el-input
                type="textarea"
                resize="none"
                :placeholder='placeholder(e.emp_name)'
                v-model="e.replyContent"
                maxlength="140"
                :autosize="{ minRows: 4, maxRows: 6}"
                show-word-limit
                >
              </el-input>
            </div>
            <div class="reply-answer">
              <el-button @click="sendReply(e,index)" type="primary" size="small" :disabled="!e.replyContent">发表回复</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="empty-style" v-show="commentData.records.length === 0">
      <img class="empty-img" :src="empty" alt="" />
      <p>暂无数据</p>
    </div>
    <el-pagination
      background
      :small="true"
      :hide-on-single-page="commentData.records.length === 0"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="current"
      :page-sizes="[10, 30, 50, 100]"
      :page-size="size"
      layout="total,  prev, pager, next, sizes, jumper"
      :total="commentData.total"
    >
    </el-pagination>
    <!-- 查看回复内容 -->
     <!-- 查看回复弹窗 -->
    <el-Dialog
      :visible.sync="commentDialogVisible"
      :title="`${winCommentData.reply_count}条回复`"
      width="960px"
      top="0"
      class="reply-dialog"
      custom-class="dialog-center"
      :close-on-click-modal="false"
    >
      <reply-content
        :winCommentData="winCommentData"
        @deleteWinComment="deleteWinComment"
        @setTotal="setTotal"
        @setLike="setLike"
        @setTop="setTop"
        @setShow="setShow"
        :type="type"
      />
    </el-Dialog>
  </div>
</template>

<script>
import ReplyContent from './children/reply-content.vue'
import pager from '@/mixins/pager'
import {
  // commentTableList,
  // topComment,
  // hideComment,
  // deleteComment,
  // likeComment,
  // replyComment,
  commentTableListManage,
  topCommentManage,
  hideCommentManage,
  deleteCommentManage,
  likeCommentManage,
  replyCommentManage
} from '@/config/mooc.api.conf.js'
import { getAvatar } from 'utils/tools'
import { mapState } from 'vuex'
const commentTableList = ''
const topComment = ''
const hideComment = ''
const deleteComment = ''
const likeComment = ''
const replyComment = ''

export default {
  mixins: [pager],
  components: {
    // CustomTips,
    ReplyContent
  },
  props: {
    title: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'manage'
    }
  },
  data() {
    return {
      form: {
        staff_name: '',
        content: '',
        type: ''
      },
      empty: require('@/assets/mooc-img/comment/empty.png'),
      avatar: require('@/assets/mooc-img/comment/avatar.png'),
      winCommentData: {},
      commentDialogVisible: false,
      commentData: {
        records: []
      },
      order_type: 1,
      statusOptions: [
        { value: 0, label: '置顶' },
        { value: 1, label: '隐藏' },
        { value: 2, label: '显示' }
      ]
    }
  },
  computed: {
    ...mapState(['projectManageInfo']),
    act_id() {
      return this.$route.query.mooc_course_id
    },
    scene () { // 用户端-2,管理端-1
      return this.$route?.query?.scene
    },
    replyAvatar() {
      const staff_name = this.$store.state.userInfo.staff_name
      return getAvatar(staff_name)
    },
    iframeFlag() {
      return this.$store.state.aiInfo.iframeFlag
    },
    isApprove() {
      // 审批-审批中
      return this.$route.query.approve === '1' || this.projectManageInfo.course_status === 6
    }
  },
  mounted() {
    this.onSearch(1)
  },
  methods: {
    // 获取评论列表
    onSearch (page_no = 1) {
      this.current = page_no
      const params = {
        act_id: this.act_id,
        page_no: this.current,
        page_size: this.size,
        ...this.form,
        order_type: this.order_type
      }
      const api = this.type === 'manage' ? commentTableListManage : commentTableList
      api(params).then((res) => {
        const records = res.records.map((e) => {
          const justTime = 3 * 60 * 1000
          return {
            ...e,
            replyContent: '',
            replyContentFlag: false,
            avatar: e.emp_name === '小腾老师' ? require('@/assets/mooc-img/comment/xt.png') : getAvatar(e.emp_name),
            created_at: new Date(e.created_at).getTime() - justTime > 0 ? e.created_at : '刚刚'
          }
        })
        this.commentData = { ...res, records }
      })
    },
    // 查看更多回复
    showMore(data) {
      this.winCommentData = {
        ...data,
        authorName: this.commentData.authorName,
        isShowArea: false,
        userName: this.type === 'manage' ? '小腾老师' : this.$store.state.userInfo.staff_name
      }
      this.commentDialogVisible = true
    },
    // 弹窗删除评论的回调
    deleteWinComment({ id, total }) {
      const index = this.commentData.records.findIndex((i) => i.id === id)
      this.commentData.records.splice(index, 1)
      this.commentData.records[index].reply_count = total - 1
      this.winCommentData.reply_count = total - 1
      this.commentDialogVisible = false
      this.$message.success('删除成功')
    },
    // 弹窗数据变动，设置条数
    setTotal({ id, total }) {
      const index = this.commentData.records.findIndex((i) => i.id === id)
      this.commentData.records[index].reply_count = total
      this.winCommentData.reply_count = total
    },
    // 弹窗数据变动，设置点赞状态与数量
    setLike({ id, is_like }) {
      const index = this.commentData.records.findIndex((i) => {
        return i.id === id
      })
      this.commentData.records[index].is_like = is_like
      if (is_like) {
        this.commentData.records[index].praise_count++
      } else {
        this.commentData.records[index].praise_count--
      }
    },
    // 弹窗数据变动，设置是否置顶
    setTop({ id, up_pass }) {
      const index = this.commentData.records.findIndex((i) => {
        return i.id === id
      })
      this.commentData.records[index].up_pass = up_pass
    },
    // 弹窗数据变动，设置是否显示
    setShow({ id, need_show, up_pass, Showflag }) {
      const index = this.commentData.records.findIndex((i) => {
        return i.id === id
      })
      this.commentData.records[index].need_show = need_show
      this.commentData.records[index].up_pass = up_pass
      this.commentData.records[index].Showflag = Showflag
    },
    // 重置
    handleReset() {
      for (let prep in this.form) {
        this.form[prep] = ''
      }
      this.order_type = 1
      this.size = 10
      this.onSearch(1)
    },
    // 置顶
    handleTop (row) {
      // sticky-1 置顶 -0 取消置顶
      const sticky = !row.up_pass ? 1 : 0
      const params = {
        id: row.id,
        sticky
      }
      const api = this.type === 'manage' ? topCommentManage : topComment
      api(params).then((res) => {
        if (sticky === 0) {
          this.$message.success('取消置顶成功')
          this.onSearch(1)
        } else {
          this.$message.success('置顶成功')
          this.onSearch(1)
        }
      })
    },
    // 发表回复
    sendReply(row, index) {
      const { app_id, act_id, id } = row
      const params = {
        app_id,
        act_id,
        pid: id,
        content: row.replyContent,
        reply_staff_id: row.staff_id,
        reply_staff_name: row.emp_name
      }
      const api = this.type === 'manage' ? replyCommentManage : replyComment
      api(params).then((res) => {
        this.handleReply(index)
        this.onSearch(1)
        this.$message.success('回复成功')
      })
    },
    // 显示隐藏
    async handleVisible(row) {
      // show -1 显示 0-隐藏
      const show = !row.need_show ? 1 : 0
      const mgsArr = ['隐藏成功', '取消隐藏成功']
      let api, topApi
      if (this.type === 'manage') {
        api = hideCommentManage
        topApi = topCommentManage
      } else {
        api = hideComment
        topApi = topComment
      }
      // 隐藏/显示
      await api({ id: row.id, show })
      // 隐藏的同时取消置顶
      // 或者在弹窗中操作过隐藏Showflag那么也要取消置顶
      if ((!show && row.up_pass) || row?.Showflag) {
        await topApi({ id: row.id, sticky: 0 })
      }
      this.$message.success(mgsArr[show])
      this.onSearch(1)
    },
    // 点赞
    handleLike(row) {
      const api = this.type === 'manage' ? likeCommentManage : likeComment
      const isLike = !row.is_like
      api({ id: row.id, act_id: row.act_id }).then((res) => {
        if (isLike) {
          this.$message.success('点赞成功')
        } else {
          this.$message.success('点赞取消成功')
        }
        this.onSearch(1)
      })
    },
    // 删除
    handleDelete(row) {
      this.$messageBox.confirm('删除后评论以及相关回复将无法恢复，确定要删除选中的评论吗？', '删除评论提醒', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        const api = this.type === 'manage' ? deleteCommentManage : deleteComment
        api(row.id).then((res) => {
          this.$message.success('删除成功')
          this.onSearch(1)
        })
      })
    },
    // 回复
    handleReply(index) {
      this.commentData.records[index].replyContentFlag = !this.commentData.records[index].replyContentFlag
    },
    handleBack() {
      this.$router.go(-1)
    },
    toCourseDetail() {
      const name = this.iframeFlag === 'ai' ? 'ai-course-detail' : 'course-detail'
      this.$router.push({ // 其他
        name,
        query: {
          id: this.act_id
        }
      })
    },
    placeholder(name) {
      return `回复 ${name}:`
    }
  }
}
</script>

<style lang="less" scoped>
.comment-manage {
  margin-top: 20px;
  .el-form {
    border-bottom: 1px solid #F5F7F9;
    .el-form-item {
      margin-right: 30px;
    }
  }
  .title {
    font-size: 18px;
    font-weight: bold;
  }

  .comment-source {
    margin-bottom: 20px;
    span:first-of-type {
      color: rgba(0,0,0,0.4);
    }
  }
  .sort-main {
    margin-top: 20px;
    margin-bottom: 20px;
    .sort-type {
      margin-right: 30px;
      color: rgba(0,0,0,0.4);
    }
  }
  .comment-content {
    .item-card {
      display: flex;
      align-items: flex-start;
      border-bottom: 1px solid #F5F7F9;
      margin-bottom: 10px;
      .avatar_img {
        height: 48px;
        width: 48px;
        border-radius: 50%;
        line-height: 48px;
        text-align: center;
      }
      .ri {
        margin-left: 20px;
        width: 100%;
        .price-comment {
          line-height: 20px;
          color: #333333;
          margin-bottom: 12px;
        }
        .user {
          margin-bottom: 8px;
          .user-name {
            color:#000000;
            font-weight: bold;
            font-size: 16px;
          }
          .default-status {
            color:#ffffff;
            font-size: 12px;
            border-radius: 3px;
            margin-left: 10px;
            height: 20px;
            width: 40px;
            line-height: 20px;
            padding: 2px 8px;
          }
          .up-status {
            background-color: #C82A29;
          }
          .hide-status {
            background:#DCDCDC
          }
        }
        .bottom-btn {
          display: flex;
          align-items: center;
          justify-content: space-between;
          color:#999999;
          margin-bottom: 12px;
          font-size: 12px;
          .total-reply {
            margin-left: 28px;
            span {
              margin-left: 8px;
            }
          }
          .default-like {
            margin-right: 20px;
            display: flex;
            align-items: center;
            cursor: pointer;
          }
          .handle-like-active {
            color:#0052D9;
            .like {
              background: url(~@/assets/mooc-img/comment/like-active.png) no-repeat center/cover;
            }
          }
          .like {
            background: url(~@/assets/mooc-img/comment/like.png) no-repeat center/cover;
            display: inline-block;
            width: 14px;
            height: 14px;
            margin-right: 4px;
          }
          .btn-status {
            display: flex;
            align-items: center;
            color: rgba(0,0,0,0.4);
            .disabled-check {
              pointer-events: none;
              cursor: not-allowed;
            }
            .opera-btn {
              margin-right: 20px;
              display: flex;
              align-items: center;
              cursor: pointer;
              i {
                display: inline-block;
                width: 14px;
                height: 14px;
                margin-right: 4px;
              }
            }
            .top-reply {
              background: url(~@/assets/mooc-img/comment/top-reply.png) no-repeat center/cover;
            }
            .no-top-reply {
              background: url(~@/assets/mooc-img/comment/no-top-reply.png) no-repeat center/cover;
            }
            .hide-reply {
              background: url(~@/assets/mooc-img/comment/hide.png) no-repeat center/cover;
            }
            .visible-reply {
              background: url(~@/assets/mooc-img/comment/visible-reply.png) no-repeat center/cover;
            }
            .reply-content {
              background: url(~@/assets/mooc-img/comment/reply.png) no-repeat center/cover;
            }
            .delete-reply {
              background: url(~@/assets/mooc-img/comment/delete.png) no-repeat center/cover;
            }
          }
        }
      }
    }

  }
  .reply-box {
    margin-top: 12px;
    .reply-textarea {
      display: flex;
      .el-textarea {
        margin-left: 18px;
      }
    }
    .reply-answer {
      text-align: right;
      margin-top: 12px;
      color:#fff;
      margin-bottom: 20px;
    }
  }
  .empty-style {
    margin: 40px 0 20px;
    color: #999;
    text-align: center;
    .empty-img {
      margin-bottom: 20px;
      width: 178px;
      height: 130px;
    }
  }
  .reply-dialog {
    :deep(.el-dialog__body) {
      max-height: 600px;
      overflow: auto;
      .content {
        white-space: pre-wrap;
        word-wrap: break-word;
        word-break: break-all;
      }
    }
  }
  .text-blue {
    cursor: pointer;
  }
}
</style>
