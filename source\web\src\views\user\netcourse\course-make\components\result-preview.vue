<template>
  <div class="result-preview">
    <Video
      class="video-box"
      :content_id.sync="videoData.content_id"
      :playTime="playTime"
      :autoPlay="false"
      @getCurrentTime="getCurrentTime"
    />
    <div class="tips tips-duration">
      课程时长：{{ transforTime(videoData.duration) }}
    </div>
    <div class="tips">视频大小：{{ videoData.file_size }}MB</div>
    <div class="word-title">字幕文本</div>
    <div
      id="word-area"
      @scroll="onWordScroll"
      @mousewheel="onMousewheel"
      v-if="captionList.length > 0"
    >
      <div class="word-scroll-box">
        <div class="word-mask"></div>
        <div
          v-for="(item, index) in captionList"
          :id="`time-${index}`"
          :key="index"
          :class="['word-item', { active: active === index }]"
        >
          <span class="time">{{ item.time }}</span>
          <div class="word" @dblclick="onDblclick(item.time)">
            {{ item.caption }}
          </div>
        </div>
      </div>
      <span class="top-btn" v-show="flag" @click="toTop">
        <span class="text">回到当前位置</span>
        <img class="icon" :src="topIcon" alt="" />
      </span>
    </div>
    <span v-else>暂无内容</span>
  </div>
</template>

<script>
import { Video } from '@/components/index'
import { transforTime } from 'utils/tools'

export default {
  name: 'result-preview',
  components: {
    Video
  },
  props: {
    // 视频信息
    videoData: {
      type: Object,
      default: () => {
        return {
          content_id: '',
          duration: 0,
          file_size: 0
        }
      }
    },
    // 字幕信息
    captionList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      active: -1,
      topIcon: require('@/assets/img/top.png'),
      flag: false,
      playTime: 0,
      behavior: 'smooth',
      transforTime
    }
  },
  watch: {
    captionList: {
      handler() {
        // 初始化遮罩层位置
        this.$nextTick(() => {
          this.onWordScroll()
        })
      },
      immediate: true
    }
  },
  methods: {
    // 视频播放时滚动到对应的字幕
    getCurrentTime(curTime) {
      console.log(curTime)
      if (this.flag) return
      this.isScroll = true
      let top = 12
      let active = -1
      let lastHeight = 0
      for (let i = 0; i < this.captionList.length; i++) {
        const time = this.captionList[i].time
        const arr = time.split(':')
        const count = arr[0] * 3600 + arr[1] * 60 + arr[2] * 1
        if (Math.floor(curTime) >= count) {
          const el = document.querySelector(`#time-${i}`)
          active = i
          if (el) {
            lastHeight = el.clientHeight + 20
            top += lastHeight
          }
        } else {
          break
        }
      }
      top -= lastHeight
      this.active = active
      const area = document.getElementById('word-area')
      area &&
        area.scrollTo({
          left: 0,
          top: curTime === 0 ? 0 : top,
          behavior: this.behavior
        })
      this.behavior = 'smooth'
    },
    // 滚动的时候固定遮罩
    onWordScroll() {
      const el = document.getElementById('word-area')
      if (el) {
        document.querySelector('.word-mask').style.top =
          el.scrollTop + (el.clientHeight - 112) + 'px'
      }
    },
    // 用户滚动鼠标，停止自动滚动，并显示“回到当前位置”按钮
    onMousewheel() {
      this.flag = true
    },
    // 双击字幕，视频跳到对应进度
    onDblclick(time) {
      const arr = time.split(':')
      this.playTime = arr[0] * 3600 + arr[1] * 60 + arr[2] * 1
      this.flag = false
      this.behavior = 'auto'
    },
    // 回到当前播放位置
    toTop() {
      this.flag = false
      this.behavior = 'auto'
    }
  }
}
</script>

<style lang="less" scoped>
.result-preview {
  position: relative;
  .video-box {
    width: 324px;
    height: 182px;
    object-fit: contain;
    border-radius: 3px;
  }
  .tips {
    color: #999;
  }
  .tips-duration {
    margin: 8px 0 4px;
  }
  .word-title {
    color: #333;
    margin: 20px 0 12px;
    font-size: 14px;
    font-weight: bold;
  }
  #word-area {
    padding: 12px;
    width: 726px;
    max-height: 306px;
    overflow-y: auto;
    border-radius: 3px;
    border: 1px solid #eee;
    .word-scroll-box {
      position: relative;
    }
    .word-item {
      display: flex;
      margin-bottom: 20px;

      &:last-of-type {
        margin-bottom: 0;
      }
      .time {
        color: #999;
      }
      .word {
        flex: 1;
        color: #666;
        margin-left: 13px;
        cursor: pointer;
      }
    }
    .active {
      .time,
      .word {
        color: #0052d9;
      }
    }
    .word-mask {
      position: absolute;
      width: 100%;
      height: 100px;
      top: 0;
      left: 0;
      background: linear-gradient(
        180deg,
        rgba(217, 217, 217, 0) 0%,
        rgba(249, 249, 249, 0.4) 100%
      );
      pointer-events: none;
    }
    .top-btn {
      position: absolute;
      left: 603px;
      bottom: 8px;
      padding: 0 12px;
      height: 32px;
      line-height: 32px;
      border-radius: 70px;
      background: #ffffffff;
      box-shadow: 0 0 12px 0 #ddddddff;
      cursor: pointer;
      .text {
        color: #0052d9ff;
        font-size: 12px;
      }
      .icon {
        width: 16px;
        height: 16px;
        position: relative;
        bottom: 1px;
      }
    }
  }
}
</style>
