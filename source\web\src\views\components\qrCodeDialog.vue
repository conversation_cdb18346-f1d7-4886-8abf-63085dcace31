
<template>
  <div class="qcode-box">
    <el-Dialog
      :visible.sync="visibleShow"
      title="分享链接"
      width="560px"
      top="0"
      custom-class="dialog-center share-win"
      :close-on-click-modal="false"
      :before-close="cancel"
      @close="cancel"
    >
      <p class="tips">请使用微信/企业微信扫码下方二维码，或者复制下方链接</p>
      <p class="title">移动端</p>
      <div class="qrcodeImg-box">
        <vue-qr
          v-if="!isAppletQrCode && url"
          ref="qrCode" 
          :text="url" 
          :size="qrSize"
          :logoScale="0.2"
          :logoSrc="logoSrc"
          :margin="0" 
        >
        </vue-qr>
        <img v-if="isAppletQrCode && appletQrUrl" :src="appletQrUrl" alt="小程序码" class="applet-qrcode">
      </div>
      <el-input v-model="url" class="share-input" disabled>
        <template slot="append">
          <el-button @click="share">{{ $langue('Mooc_ProjectDetail_Copy', { defaultText: '复制' }) }}</el-button>
        </template>
      </el-input>
    </el-Dialog>
  </div>
</template>
<script>
import { getMobileQrcode } from 'config/mooc.api.conf.js'

export default {
  name: 'qrcode-img',
  props: {
    visible: {
      type: Boolean
    },
    url: {
      type: String
    },
    width: {
      type: String,
      default: '120'
    },
    height: {
      type: String,
      default: '120'
    },
    copyTitle: {
      type: String,
      default: ''
    },
    isAppletQrCode: {
      type: Boolean,
      default: false
    },
    scene: {
      type: [String, Number],
      default: ''
    },
    appletPath: {
      type: String,
      default: ''
    }
  },
  watch: {
    url: function(url) {
      let _this = this
      this.$nextTick(() => {
        _this.initCode()
      })
    },
    // visible: function(newV) {
    //   console.log('newV', newV)
    //   this.visibleShow = newV
    //   console.log('isAppletQrCode', this.isAppletQrCode)
    //   if (newV && this.isAppletQrCode) {
    //     this.showAppletQrCode()
    //   }
    // },
    visible: {
      handler(newV) {
        this.visibleShow = newV
        console.log('newV', newV)
        console.log('isAppletQrCode', this.isAppletQrCode)
        if (newV && this.isAppletQrCode) {
          this.showAppletQrCode()
        }
      },
      immediate: true
    }
  },
  computed: {
    qrSize() {
      return Number(this.width)
    }
  },
  data() {
    return {
      qrcodeImg: '',
      visibleShow: true,
      logoSrc: require('@/assets/mooc-img/tencent-study.png'),
      appletQrUrl: ''
    }
  },
  mounted() {
  },
  methods: {
    share() {
      var tempInput = document.createElement('input')
      tempInput.value = this.copyTitle ? `【${this.copyTitle}】${this.url}` : this.url
      tempInput.id = 'creatDom'
      document.body.appendChild(tempInput)
      tempInput.select()
      if (document.execCommand('copy')) {
        document.execCommand('copy')
        this.$message.success(this.$langue('Mooc_Common_Alert_CopySucessed', { defaultText: '复制成功' }))
        let creatDom = document.getElementById('creatDom')
        creatDom.parentNode.removeChild(creatDom)
      }
    },
    cancel() {
      this.visibleShow = false
      this.$emit('update:visible', false)
    },
    showAppletQrCode() {
      const params = {
        scene: String(this.scene),
        page: this.appletPath,
        // scene: `${this.activity_id}`,
        // page: 'pages/activity/index',
        env_version: process.env.NODE_ENV === 'production' ? 'release' : 'trial'
      }
      getMobileQrcode(params).then((res) => {
        this.appletQrUrl = `data:image/png;base64,${res}`
      })
    }
  }
}
</script>

<style lang="less" scoped>
.qcode-box{
  :deep(.el-dialog__body) {
      text-align: center;
      .tips {
        color: #333;
      }
      .title {
        margin: 20px 0 10px;
        color: #666;
      }
      .qrcodeImg-box{
        display: flex;
        justify-content: center;
        .applet-qrcode {
          width: 120px;
          height: 120px;
        }
      }
      .share-input {
        margin: 10px 0;
        .el-input__inner {
          height: 32px;
          line-height: 32px;
          color: #919191;
        }
        // .el-input-group__append{
        //   bottom: -2px;
        //   border: unset;
        // }
        .el-button--primary {
          display: flex; 
          align-items: center;
          height: 32px;
          background-color: #0052d9;
          border-color: #0052d9;
          color: #fff;
          font-size: 12px;
          border-radius: 0 4px 4px 0;
        }

        .el-button--primary:hover {
          background-color: #266fe8;
          border-color: #266fe8;
          color: #fff;
        }

        .el-button--primary:active {
          background-color: #0034b5;
          border-color: #0034b5;
          color: #fff;
        }
      }
    }
}
</style>
