<template>
  <div class="sign-in-qr-code-popup activity-common">
    <el-dialog :visible.sync="visible" width="800px" :close-on-click-modal="false" :before-close="handlerClose">
      <div slot="title" class="dialog-header">查看签到二维码</div>
      <div class="dialog-content">
        <div class="qr-code-list">
          <div class="qr-code-item" v-for="(item, index) in qrCodeList" :key="index">
            <div class="qr-code-title">第{{ index + 1 }}次签到二维码</div>
            <div class="qrcode">
              <vue-qr :ref="'qrCode' + index" :text="item.url" :size="182" :margin="0"></vue-qr>
            </div>
            <div class="btn-column flex-box flex-j-c">
              <el-button class="confirm-btn" type="primary" @click="handlerDownload(index)" size="small">下载</el-button>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="handlerClose" size="small">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 引入活动状态管理器
import { mapState } from 'vuex'

export default {
  name: 'signInQRCodePopup',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      logoSrc: require('@/assets/mooc-img/tencent-study.png'),
      qrCodeList: []
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.qrCodeList = []
          const { activity_id, sign_count } = this.$activityInfo
          if (activity_id) {
            for (let i = 0; i < sign_count; i++) {
              let path = ''
              if (process.env.NODE_ENV === 'production') {
                path = `https://sdc.qq.com/s/JZH8Eg?scheme_type=activityDetail&activity_id=${activity_id}&jump_from=mp_qrcode&project=0&source=ql&sign_order=${i + 1}`
              } else {
                path = `https://test-learn.woa.com/training/mobile/activity/detail?activity_id=${activity_id}&sign_order=${i + 1}`
              }
              this.qrCodeList.push({ url: path })
            }
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState({
      $activityInfo: state => state.activity.activityInfo
    })
  },
  created() { },
  mounted() { },
  beforeDestroy() { },
  methods: {
    handlerClose() {
      this.$emit('update:visible', false)
    },
    // 下载二维码
    handlerDownload(index) {
      let imgUrl = this.$refs['qrCode' + index][0].imgUrl
      const link = document.createElement('a')
      link.download = `QR_sign-in_${index + 1}_${Date.now()}.png`
      link.href = imgUrl
      
      // 兼容移动端处理
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/activity-common.less';

.sign-in-qr-code-popup {
  .dialog-content {
    max-height: 618px;
    overflow-y: auto;
    padding: 3px 45px 24px;

    .qr-code-list {
      display: flex;
      flex-wrap: wrap;
      gap: 30px 47px;

      .qr-code-item {
        width: 182px;

        .qr-code-title {
          align-self: stretch;
          color: #00000099;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          line-height: 22px;
          margin-bottom: 20px;
        }

        .qrcode {
          width: 182px;
          height: 182px;
          background: #ccc;
          flex-shrink: 0;
          margin-bottom: 24px;
        }
      }
    }
  }
}
</style>
