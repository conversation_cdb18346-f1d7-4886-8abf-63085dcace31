<template>
  <div class="main-pop">
    <div class="pop-wrap" v-if="lotteryDrawShow">
      <div class="pop-body">
        <img class="pic" src="@/assets/img/mobile/raffle.png" />
        <div class="close-btn" @click="close"></div>
        <div class="look-btn" @click="toCheck"></div>
      </div>
    </div>
  </div>
</template>
<script>
// import { getObtainingUserLotteryTimesAPI, getCourseGotLotteryAPI, getNetCourseLearnRecordAPI, getLotteryChanceAPI } from 'config/api.conf'
export default {
  props: {
    courseName: {
      type: String
    }
  },
  data() {
    return {
      lotteryDrawShow: false,
      lotteryDrawTimer: null,
      isInit: false,
      courseDuration: 0,
      curTime: 0
    }
  },
  computed: {
    courseId() {
      return this.$route.query.course_id || ''
    }
  },
  methods: {
    /**
     * 活动已结束 功能已下架
     */
    // 今天是否获取五次抽奖
    // init() {
    //   if (this.isInit) return
    //   this.curTime = 0
    //   this.isInit = true
    //   this.clearLotteryDrawTimer()
    //   getObtainingUserLotteryTimesAPI().then(res => {
    //     if (res < 5) {
    //       this.getCourseGotLottery()
    //     }
    //   }).catch(() => {
    //     this.isInit = false
    //   })
    // },
    // 当前课程是否获取过抽奖
    // getCourseGotLottery() {
    //   const loginUser = JSON.parse(sessionStorage.getItem('login_user'))
    //   const params = {
    //     staffId: loginUser.staff_id,
    //     objectType: 'NetCourse',
    //     objectId: this.courseId
    //   }
    //   getCourseGotLotteryAPI(params).then(res => {
    //     if (res === false) {
    //       this.getNetCourseLearnRecord()
    //     }
    //   }).catch(() => {
    //     this.isInit = false
    //   })
    // },
    // 获取课程学习时间
    // getNetCourseLearnRecord() {
    //   getNetCourseLearnRecordAPI(this.courseId).then(res => {
    //     this.isInit = false
    //     this.courseDuration = res.est_dur
    //     this.initInterval()
    //   }).catch(() => {
    //     this.isInit = false
    //   })
    // },
    // initInterval() {
    //   if (this.lotteryDrawTimer) this.lotteryDrawTimer = null
    //   this.lotteryDrawTimer = setInterval(() => {
    //     ++this.curTime
    //     if (this.curTime >= 60) {
    //       this.clearLotteryDrawTimer()
    //       this.getLotteryChance()
    //     }
    //   }, 1000)
    // },
    // clearLotteryDrawTimer() {
    //   clearInterval(this.lotteryDrawTimer)
    //   this.lotteryDrawTimer = null
    // },
    // 获取抽奖次数
    // getLotteryChance() {
    //   const params = {
    //     net_course_id: this.courseId,
    //     net_course_name: this.courseName,
    //     est_dur: this.courseDuration,
    //     study_dur: this.curTime
    //   }
    //   getLotteryChanceAPI(params).then(res => {
    //     if (res.credit_point > 0) {
    //       this.lotteryDrawShow = true
    //     }
    //   })
    // },
    // close() {
    //   this.lotteryDrawShow = false
    // },
    // toCheck() {
    //   let url = process.env.NODE_ENV === 'production' ? '//learn.woa.com/training/mobile/pointLottery/lottery?activity_id=3' : '//test-portal-learn.woa.com/training/mobile/pointLottery/lottery?activity_id=3'
    //   window.location.href = url
    // }
  },
  beforeDestroy() {
    // this.clearLotteryDrawTimer()
  }
}
</script>
<style lang="less" scoped>
  .main-pop {
    .pop-wrap {
      width: 100vw;
      height: 100vh;
      background-color: #00000066;
      position: fixed;
      top: 0;
      left: 0;
      z-index: 9999;
      .pop-body {
        width: 320px;
        height: 340px;
        margin: 0 auto;
        transform: translateY(calc(50vh - (340px / 2)));
        .pic {
          width: 100%;
          height: 100%;
          display: inline-block;
        }
        .close-btn {
          width: 28px;
          height: 28px;
          position: absolute;
          top: 14px;
          right: 16px;
        }
        .look-btn {
          width: 178px;
          height: 40px;
          position: absolute;
          left: 72px;
          bottom: 45px;
        }
      }
    }
  }
</style>
