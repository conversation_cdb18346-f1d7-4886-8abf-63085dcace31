<template>
  <div class="task-list-content">
    <!-- 极客试学 -->
    <div class="learn-tip" v-if="geekInfo.isGeekFreeLearningSection">
      <div class="icon"></div>
      <div class="text">
        你可任选<span>{{` ${geekInfo.total} `}}</span>个任务进行试学，目前已试学任务数：<span>{{geekInfo.study}}</span> 个
      </div>
      <div class="exchange-btn" @click="purchaseCourses">无需试学，直接兑换</div>
    </div>

    <CustomTips
    v-if="registerBTtn && !geekInfo.isGeekFreeLearningSection && !isPreview"
    class="auto-tips"
    :title="$langue('Mooc_ProjectDetail_BasicInfo_CannotStudyNotInProj', { defaultText: '暂未加入培养项目，无法进行学习' })"
    IconName="el-icon-warning"
    backgroundColor="#fdf6ec"
    color="#FF7548"
    lineHeight="40px"
    :clickTitle="$langue('Mooc_ProjectDetail_BasicInfo_ClickRegistProj', { defaultText: '点击报名加入项目' })"
    @confirm="confirm"
    :dt-eid="dtTips('eid')"
    :dt-areaid="dtTips('area')"
    :dt-remark="dtTips('remark')"
    >
    </CustomTips>
    <div class="filter-data-box" v-if="!registerBTtn">
      <span @click="expandHandle" class="all-chioce" :class="{ 'btn-hiiden': !showExpandBtn }">
        {{ isExpandAll ? $langue('Mooc_ProjectDetail_TaskList_RetractAll', { defaultText: '全部收起' }) : $langue('Mooc_ProjectDetail_TaskList_ExpandAll', { defaultText: '全部展开' }) }}
        <span :class="[isExpandAll ? 'icon-arrow-right' : 'icon-arrow-down', 'arrow-icon']"></span>
      </span>
      <el-checkbox-group v-model="checkList" @change="formatTreeData">
        <el-checkbox label="1">{{ $langue('Mooc_ProjectDetail_TaskList_OnlyRequire', { defaultText: '只看应学任务' }) }}</el-checkbox>
        <el-checkbox label="2">{{ $langue('Mooc_ProjectDetail_TaskList_OnlyNotFinished', { defaultText: '只看未完成任务' }) }}</el-checkbox>
      </el-checkbox-group>
    </div>
    <el-tree
    v-if="newTreeInfo.result.length"
    ref="taskTree"
    :data="newTreeInfo.result"
    node-key="task_id"
    :indent="20"
    :props="{
      'label': 'task_name',
      'children': 'sub_tasks'
    }"
    icon-class=" "
    :default-expand-all="isExpandAll"
    :filter-node-method="filterNode"
    >
      <div class="custom-tree-node" slot-scope="{ node, data }">
        <div :class="[{ 'stage-row': data.task_type === 'stage' }, 'node-icon-box']">
          <span
            :class="[node.expanded && node.childNodes?.length ? 'tree-icon-down' : 'tree-icon-right', data.iconName]"></span>
          <!-- 阶段 -->
          <div class="task-stage" v-if="data.task_type === 'stage'">
            <span>{{ node.label }}</span>
          </div>
          <!-- 任务组 -->
          <div :class="[data.pid ? 'task-group' : '', 'task-node']" v-if="data.task_type === 'group'">
            <span class="before-icon group-icon"></span>
            <span>{{ node.label }}</span>
          </div>
        </div>
        <!-- 任务 -->
        <div :class="[node.parent.level === 1 ? 'parent-tage-task' :
          node.parent.level === 2 ? 'parent-group-task' : 'no-child-taks', 'task-item']" v-if="data.task_type === 'task'"
          @click="clickTask(data)" :dt-eid="dtTask('eid', {node, data})" :dt-areaid="dtTask('area', {node, data})" :dt-remark="dtTask('remark', {node, data})">
          <span class="task-item-left">
            <el-tooltip effect="dark" :content="$langue(resourceTypeIcon(data.resource_type).langName, { defaultText: resourceTypeIcon(data.resource_type).defaultText})" placement="top-start">
              <span class="before-icon task-icon" :class="resourceTypeIcon(data.resource_type).className"></span>
            </el-tooltip>
            <span class="task-item-info">
              <span class="task-absolute-left">
                <span class="task-item-title" :title="node.label">{{ node.label }}</span>
                <span class="label" :class="[data.required ? 'require' : 'norequire']">{{ data.required ? $langue('Mooc_ProjectDetail_TaskList_RequiredTask', { defaultText: '应学' }) : $langue('Mooc_ProjectDetail_TaskList_ElectiveTask', { defaultText: '选学' }) }}</span>
                <span v-if="data.latest_task_id" class="last-study">{{ $langue('Mooc_ProjectDetail_TrainingProgress_LastStudy', { defaultText: '上次学习' }) }}</span>
                <span class="exam-time" v-if="['Exam', 'Practice'].includes(data.resource_type)">
                  {{ data.resource_type === 'Exam' ? data.exam_time_type === 1 ? $langue('Mooc_ProjectDetail_TaskList_NotLimitedExamTime', { defaultText: '考试时间：不限制' }) :
                    `${$langue('Mooc_TaskDetail_ExamTime', { defaultText: '考试时间' })}${moocLang === 'en-us' ? ':' : '：'}${data.exam_start_time || ''} ${$langue('Mooc_Home_MyProject_To', { defaultText: ' 至 ' })} ${data.exam_end_time || ''}` :
                        data.resource_type === 'Practice' ? data.exam_time_type === 1 ? $langue('Mooc_ProjectDetail_TaskList_NotLimitedPracticeTime', { defaultText: '练习时间' }) :
                          `练习时间：${data.exam_start_time || ''} ${$langue('Mooc_Home_MyProject_To', { defaultText: ' 至 ' })} ${data.exam_end_time || ''}` : ''
                  }}
                </span>
                <span v-if="data.resource_type === 'Exam' && data.is_finished !== null">
                  <span :class="[data.is_finished ? 'pass' : 'nopass']">
                    {{
                      data.exam_status === 2 ? $langue('Mooc_ProjectDetail_TaskList_ToBeReviewed', {defaultText: '待批阅'}) :
                      data.is_finished ? $langue('Mooc_ProjectDetail_TaskList_Pass', { defaultText: '通过' }) : $langue('Mooc_ProjectDetail_TaskList_NotPass', { defaultText: '未通过' })
                    }}
                  </span>
                  <span class="cut-apart-line"> {{ data.is_cheat ? $langue('Mooc_ProjectDetail_TaskList_ExamCheat', { defaultText: '考试作弊' }) : (data.score || '-') }}</span>
                </span>
                <!-- 极客 已试学  -->
                <span v-if="geekInfo.isGeekFreeLearningSection && data.geek_learned_id" class="geek-learned">
                  <img class="icon" :src="require('@/assets/mooc-img/fulfil.png')" alt="">已试学
                </span>
              </span>
              <span class="task-absolute-right">
                <!-- 极客试学 -->
                <template v-if="geekInfo.isGeekFreeLearningSection">
                  <el-tooltip effect="dark" :content="taskCanPreviewIcon(data).tooltips" placement="top">
                    <span v-if="data.task_status !== 2" class="after-icon" :class="taskCanPreviewIcon(data).className"></span>
                  </el-tooltip>
                </template>
                <!-- 原mooc -->
                <template v-else>
                  <!-- 任务类型为第三方、作业时，不显示 -->
                  <span v-if="data.task_status !== 2 && !['22', '23'].includes(data.act_type)" class="duration-time">{{data.word_number ? `${data.word_number}${$langue('Mooc_ProjectDetail_TaskList_Words', { defaultText: '字' })}` : data.duration ? `${data.duration}${$langue('Mooc_ProjectDetail_TaskList_Minute', { defaultText: '分钟' })}` : '-' }}</span>
                  <el-tooltip effect="dark" :content="taskProcessIcon(data, register).tooltips" placement="top">
                    <span v-if="data.task_status !== 2 && !isPreview" class="after-icon" :class=" taskProcessIcon(data, register).className"></span>
                  </el-tooltip>
                  <span v-if="data.task_status === 2">{{ $langue('Mooc_ProjectDetail_TaskList_TaskDisable', { defaultText: '任务已失效，无法学习' }) }}</span>
                </template>
              </span>
            </span>
          </span>
        </div>
      </div>
    </el-tree>
    <div class="bottom-text" v-else>
      <img class="empty-img" :src="empty" alt="" />
      <div class="empty-text">{{$langue('Mooc_Common_NoData', { defaultText: '暂无数据' })}}~</div>
    </div>
  </div>
</template>
<script>
import CustomTips from '@/components/tips.vue'
import trainTaskList from 'mixins/trainTaskList'
import { mapState } from 'vuex'
// import { getActTypeByModuleId } from '@/utils/moduleMap.js'
export default {
  mixins: [trainTaskList],
  components: {
    CustomTips
  },
  props: {
    register: {
      type: Boolean
    },
    registerBTtn: {
      type: Boolean
    },
    personInfo: {
      type: Object
    },
    trainProgressInfo: {
      type: Object
    },
    // 极客课程信息
    geekInfo: {
      type: Object,
      default: () => {
        return {
          isGeekFreeLearningSection: false, // 是否极客试学
          isGeek: false, // 是否极客
          canPreview: false, // 是否支持预览
          courseAcquisitionType: 1, // 1,统一购买-用户不需要够买 2，体系课-需要购买才试学
          total: 0, // 试学总任务数
          study: 0, // 已试学任务数
          previewRecords: [], // 预览任务列表
          courseFrom: 'geekBang'
        }
      }
    },
    dtTitle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      checkList: [],
      empty: require('@/assets/img/empty.png')
    }
  },
  computed: {
    ...mapState(['moocLang', 'userInfo']),
    newTreeInfo() {
      // 极客试学的任务数据处理
      let geekList = []
      let isPreview = this.geekInfo?.previewRecords?.length > 0 ? 1 : 0
      if (this.geekInfo?.isGeekFreeLearningSection && isPreview) {
        geekList = this.geekInfo.previewRecords.map(v => v.outsourced_course_id)
      }

      // mooc 原逻辑处理
      const { latest_task_id } = this.trainProgressInfo
      const haveGroupTask = (data = []) => {
        data.forEach((v) => {
          // 极客试学数据展示
          if (this.geekInfo?.isGeekFreeLearningSection && isPreview && v.task_type === 'task' && geekList.includes(v.act_id)) {
            v.geek_learned_id = v.act_id
          }

          // 上一次学习
          if (v.task_type === 'task' && (latest_task_id && latest_task_id === v.task_id)) {
            v.latest_task_id = latest_task_id
          } else {
            if (v.sub_tasks?.length) {
              haveGroupTask(v.sub_tasks)
            }
          }
        })
        return JSON.stringify({ result: data })
      }
      console.log('任务列表：', JSON.parse(haveGroupTask(this.taskTreeData)))
      return JSON.parse(haveGroupTask(this.taskTreeData))
    },
    unlocked_by_step() {
      return this.trainProgressInfo.unlocked_by_step
    },
    register_confirm() {
      return this.personInfo.register_confirm === 0 ? 0 : 1
    },
    resource_from() {
      return this.personInfo.resource_from
    },
    isPreview() {
      return this.$route.query.previewType === 'preview'
    }
  },
  // watch: {
  //   trainProgressInfo(val) {
  //     this.unlocked_by_step = val.unlocked_by_step
  //     console.log('项目是送否是合法顺序解锁', val.unlocked_by_step)
  //   }
  // },
  mounted() {
    this.getTaskData()
  },
  methods: {
    dtTask(type, { node, data }) {
      if (type === 'eid') {
        return `element_${data.mooc_course_id || ''}_${data.act_id || ''}`
      }
      if (type === 'area') {
        return `area_${data.mooc_course_id || ''}_${data.act_id || ''}`
      }
      if (type === 'remark') {
        return JSON.stringify({
          page: this.trainProgressInfo.mooc_course_name || this.dtTitle,
          page_type: '项目详情页',
          container: '',
          content_type: data.resource_type_name || '',
          click_type: 'data',
          content_id: data.act_id || '',
          container_id: '',
          content_name: data.act_name || '',
          act_type: data.act_type || '',
          terminal: 'PC'
        })
      }
    },
    dtTips(type) {
      let tips = '点击报名加入项目'
      let { mooc_course_id } = this.$route.query
      if (type === 'eid') {
        return `element_${mooc_course_id}_${tips}`
      }
      if (type === 'area') {
        return `area_${mooc_course_id}_${tips}`
      }
      return JSON.stringify({
        page: this.trainProgressInfo.mooc_course_name || this.dtTitle,
        page_type: '项目详情页',
        container: '任务列表',
        click_type: 'button',
        content_type: '培养项目',
        content_id: mooc_course_id,
        container_id: '',
        content_name: tips,
        act_type: '11',
        terminal: 'PC'
      })
    },
    // 应学--选学过滤数据
    formatTreeData(chekList) {
      const required = !!chekList.includes('1')
      const unFinished = !!chekList.includes('2')
      this.$refs.taskTree.filter({
        // 只看应学任务
        required,
        // 只看未完成任务
        unFinished
      })
    },
    // 点击任务
    clickTask(data) {
      // 极客 && 需要购买 && 支持试学 && 未加入项目
      if (this.geekInfo.isGeek && this.geekInfo.courseAcquisitionType === 2 && this.geekInfo.canPreview && !this.register) {
        const { previewRecords, total, study } = this.geekInfo
        // 已试学完可试学章数
        if (study >= total) {
          let index = previewRecords.findIndex(e => e.outsourced_course_id === data.act_id)
          console.log(index)
          if (index === -1) {
            this.$emit('handleStudyTips', 3)
            return
          }
        }
        this.handleTask({ ...data, geekPreview: true }, this.geekInfo.courseFrom)
      } else {
        // mooc
        const { learn_status, proj_end_time, proj_start_time, course_period_type } = this.trainProgressInfo
        const proTime = new Date(proj_start_time).getTime()
        const endTime = new Date(proj_end_time).getTime()
        const currTime = new Date().getTime()
        if (!this.register) { // 没有报名
          console.log(this.register_confirm, 'this.register_confirm-')
          console.log(!this.resource_from, '!this.resource_from')

          if (this.register_confirm === 1 && !this.resource_from) { // 报名确认打开
            this.$emit('handleEnroll', {
              data: { ...data },
              msg: this.$langue('Mooc_ProjectDetail_BasicInfo_SureRegistTask', { defaultText: '如需进行学习，请先报名加入培养项目，请留意项目的学习起止时间。' }),
              title: this.$langue('Mooc_ProjectDetail_BasicInfo_RegistProjTask', { defaultText: '报名提醒' }),
              toast: this.$langue('Mooc_ProjectDetail_BasicInfo_RegistSucessedTASK', { defaultText: '报名成功，请开始学习' }),
              callback: this.handleTask,
              needJumpTo: true
            })
          } else {
            this.$message.warning(this.$langue('Mooc_Common_Alert_JoinProjectFirst', { defaultText: '请先报名加入培养项目' }))
          }
          return
        }
        if (learn_status === 1 && currTime <= proTime) {
          this.$message.warning(this.$langue('Api_Mooc_Project_ProjectNotStart', { defaultText: '项目未开始，无法开始学习' }))
          return
        }
        if (learn_status === 3) {
          this.$message.warning(this.$langue('Api_Mooc_Project_ProjectDelayed', { defaultText: '已逾期，无法继续学习' }))
          return
        }
        if (course_period_type !== 3 && currTime >= endTime) {
          this.$message.warning(this.$langue('Mooc_ProjectDetail_TrainingProgress_CannotStudyProjEnd', { defaultText: '项目已结束，无法继续学习' }))
          return
        }
        this.handleTask(data)
      }
    },
    // 点击报名
    confirm() {
      this.$emit('handleEnroll')
    },
    // 回到顶部
    handleBackTop() {
      this.$emit('handleBackTop')
    },
    // 极客 点击兑换
    purchaseCourses() {
      this.$emit('handlePurchase')
    }
  }
}
</script>
<style lang="less" scoped>
.task-list-content {
  font-size: 14px;
  padding: 0px 20px 20px;
  position: relative;

  .auto-tips {
    // margin-bottom: 12px;
    margin-top: 24px;

    :deep(.custom-tips-title) {
      .text-blue {
        margin-left: 28px;
      }
    }
  }
  .filter-data-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 16px 0px;

    .all-chioce {
      cursor: pointer;
      display: flex;
      align-items: center;

      .arrow-icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-left: 8px;
        background: url('~@/assets/mooc-img/bottom-arrow.png') no-repeat center/cover;
      }

      .icon-arrow-down {
        transform: rotate(-90deg);
        transition: transform .3s;
      }

      .icon-arrow-right {
        transform: rotate(0deg);
        transition: transform .3s;
      }
    }

    .btn-hiiden {
      opacity: 0;
      cursor: default;
    }
  }

  :deep(.el-tree) {
    border-radius: 4px;
    border: 0.5px solid #eeeeeeff;
    margin-top: 24px;
    // 隐藏字节点图标
    .tree-icon.is-leaf {
      cursor: default;
      display: none;
    }

    .el-tree-node__content>.el-tree-node__expand-icon {
      padding: unset
    }
  }
  .more-data-tips {
    background: #fff;
    color: #0052D9;
    padding: 3px 12px;
    border-radius: 16px;
    line-height: 22px;
    width: 184px;
    position: absolute;
    bottom: 40px;
    transform: translate(50%);
    right: 50%;
    i {
      margin-left: 4px;
    }
  }
  .before-icon {
    width: 16px;
    height: 16px;
    display: inline-block;
  }

  .after-icon {
    width: 16px;
    height: 16px;
    display: inline-block;
    margin-left: 16px;
  }

  .node-icon-box {
    // height: 48px;
    // line-height: 48px;
    display: flex;
    align-items: center;
    padding: 0 16px 0 12px;

    .tree-icon {
      cursor: pointer;
      width: 16px;
      height: 16px;
      // padding: 0 12px 0 16px;
      box-sizing: content-box;
      background: url("~@/assets/mooc-img/right.png") no-repeat center / cover;
    }

    .tree-icon-down {
      transform: rotate(90deg);
      transition: transform .3s;
    }

    .tree-icon-right {
      transform: rotate(0deg);
      transition: transform .3s;
    }
  }

  .stage-row {
    height: 48px;
    line-height: 48px;
    background: #f9fbfcff;
    border-bottom: 0.5px solid #eeeeeeff;
  }

  .custom-tree-node {
    width: 100%;
    overflow: hidden;
    // display: flex;
    // align-items: center;
  }

  :deep(.el-tree-node__content) {
    height: auto;
    cursor: default;
    background: transparent;
    height: 48px;

    // background: #f9fbfcff;
    &:hover {
      background: transparent;
    }
  }

  .el-tree>.el-tree-node>.el-tree-node__content {
    border-bottom: 1px solid #eeeeeeff;

  }

  .is-focusable>.el-tree-node__content {
    background: #F9FBFC;

  }

  .task-stage {
    flex: 1;
    color: #000000e6;
    font-weight: 600;
    cursor: pointer;
    padding-left: 10px;
  }

  .task-group {
    display: flex;
    align-items: center;
    line-height: 24px;
    height: 24px;
    color: #000000e6;
  }

  // .task-group.has-children {
  //   cursor: pointer;

  // }

  .task-node {
    display: flex;

    .group-icon {
      width: 14px;
      height: 14px;
      margin-right: 8px;
      background: url("~@/assets/mooc-img/group.png") no-repeat center / cover;
    }
  }

  .task-item {
    display: flex;
    width: 100%;
    border-bottom: 0.5px solid #eeeeeeff;
    justify-content: space-between;
    cursor: pointer;
    height: 48px;
    line-height: 48px;
    padding: 0px 12px 0px 16px;

    .task-item-left {
      overflow: hidden;
      display: flex;
      align-items: center;
      flex: 1;

      .task-item-info {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-left: 10px;
        flex: 1;
        overflow: hidden;
        font-size: 12px;

        .task-absolute-left {
          flex: 1
        }

        .task-absolute-right,
        .task-absolute-left {
          display: flex;
          align-items: center;
          overflow: hidden;
        }

        // .task-require-show {
        //   display: flex;
        //   align-items: center;
        // }
        // .task-item-going {
        //   display: flex;
        //   align-items: center;
        // }
        .last-study {
          border-radius: 2px;
          opacity: 1;
          background: #ebeffcff;
          height: 20px;
          line-height: 20px;
          // width: 64px;
          text-align: center;
          color: #0052D9;
          margin-right: 10px;
          padding: 0 4px
        }

        .label {
          padding: 0 4px;
          border-radius: 2px;
          margin-right: 10px;
          height: 18px;
          line-height: 18px;
        }

        .require {
          background: #fdf6ecff;
          color: #FF7548;
        }

        .norequire {
          background: #CCF2E2;
          color: #00B368;
        }

        .pass {
          color: #00B368;
        }

        .nopass {
          color: #FF7548;
        }

        .exam-time {
          margin-right: 16px;
          color: #00000099;
        }

        .cut-apart-line {
          padding-left: 24px;
          position: relative;
        }

        .cut-apart-line::before {
          content: '';
          width: 1px;
          height: 12px;
          background-color: #00000099;
          display: inline-block;
          position: absolute;
          margin-top: 18px;
          left: 12px;
        }

        .duration-time {
          margin-left: 16px;
        }

        .geek-learned {
          color: #2ba471;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
          display: flex;
          align-items: center;
          margin-right: 10px;
          .icon {
            width: 16px;
            height: 16px;
            display: inline-block;
            margin-right: 4px;
          }
        }
      }

      .task-item-title {
        // width: 100%;
        // width: 230px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        margin-right: 16px;
        font-size: 14px;
        display: inline-block;
        height: 24px;
        line-height: 24px;
        color: #000000e6;
      }
    }
  }

  .bottom-text {
    padding-top: 4px;
    color: #999;
    text-align: center;

    .empty-img {
      margin-bottom: 20px;
      width: 160px;
      height: 160px;
    }
  }
  .parent-tage-task,
  .parent-group-task {
    border-bottom: unset;
  }

  .parent-group-task {
    padding-left: 8px;
  }

  .no-child-taks {
    padding: 0px 12px 0px 14px;
  }

  .no-register-lock-con {
    background: url("~@/assets/mooc-img/lock.png") no-repeat center / cover;
  }

  .learn-tip {
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding: 0 13px;
    border-radius: 4px;
    background: #FDF6EC;
    margin: 24px 0 12px;
    display: flex;
    align-items: center;
    .icon {
      width: 18px;
      height: 18px;
      margin-right: 5px;
      background: url("~@/assets/mooc-img/warning-icon.png") no-repeat center / cover;
    }
    .text {
      color: #000000;
      & > span {
        color: #ED7B2F;
      }
    }
    .exchange-btn {
      margin-left: 28px;
      color: #0052d9;
      font-size: 14px;
      line-height: 22px;
      text-decoration-line: underline;
      cursor: pointer;
    }
  }
}
</style>
