<template>
  <div>
    <el-dialog class="give-course-dialog" :visible.sync="dialogVisible" width="552px">
      <div slot="title" class="title">
        <span>复制链接转发分享，邀请同事解锁价值<span class="blue">1200</span>元的“哈佛精品文库”</span>
      </div>
      <div class="harvard-shear-content">
        <div class="copy-content">
          <span class="url-text">{{urlText}}</span>
          <span class="copy-btn" @click="doCopy"><i class="el-icon-link"></i> 复制链接，转发分享</span>
        </div>
        <div class="tips"><i>*</i> 注意：此活动仅限腾讯员工参与，外部人士无法接受邀请</div>
        <!-- <div class="shear-dialog-img">
          <img :src="require('@/assets/outsourcedCourse/shear-dialog.png')" alt="">
        </div> -->
      </div>
    </el-dialog>
  </div>

</template>

<script>
export default {
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      urlText: ''
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        console.log(val.staff_name)
        if (val.staff_name) {
          this.urlText = `https://sdc.qq.com/s/PTUrrm?scheme_type=harvard&activityId=${this.activityId}&staff_name=${val.staff_name}&staff_id=${val.staff_id}`
        }
      },
      immediate: true
    }
  },
  mounted() {},
  computed: {
    dialogVisible: {
      set(val) {
        this.$emit('update:isShow', val)
      },
      get() {
        return this.isShow
      }
    },
    activityId() {
      return this.$route.query.activityId
    }
    // urlText() {
    //   let userInfo = JSON.parse(sessionStorage.getItem('login_user')) || {}
    //   console.log(userInfo, 'userInfouserInfo')
    //   let url = `https://sdc.qq.com/s/PTUrrm?scheme_type=harvard&activityId=${this.activityId}`
    //   return `${url}&staff_name=${userInfo.staff_name}&staff_id=${userInfo.staff_id}`
    // }
  },
  methods: {
    doCopy() {
      let userInfo = JSON.parse(sessionStorage.getItem('login_user')) || {}
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      let str = `【腾讯学堂】${userInfo.staff_name}向你赠送了价值1200元的【哈佛精品文库】阅读权限，点击链接即可领取500+篇优质文章的阅读权限[${this.urlText}]`
      input.value = str
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      this.$message.success('已复制链接，去粘贴分享吧')
      // 复制后移除输入框
      input.remove()
    }
  }
}
</script>
<style lang="less">
.content-xueba {
  padding: 0 32px;
  .content-xueba-title {
    color: #000000e6;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    padding: 16px 0;
  }
  .el-button--default {
    background-color: #fff;
    color: #0052d9;
    border: 1px solid #dcdcdc;
  }
  .el-button--default:hover {
    background-color: #fff;
    color: #0052d9;
    border: 1px solid #0052d9;
  }
}
</style>
<style lang="less" scoped>
.blue {
  color: #0052d9;
}
.give-course-dialog {
  font-family: 'PingFang SC';
  .title {
    color: #000000e6;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }
  /deep/.el-dialog {
    background: linear-gradient(180deg, #e3f2ff 0%, #e3f2ff 0%, #eaf5ff 35.99%);
  }
  /deep/.el-dialog__body {
    padding: 0 24px 24px;
  }
  /deep/.el-dialog__header {
    padding: 24px 24px 20px;
    border-bottom: unset;
  }
  /deep/.el-dialog__headerbtn {
    top: 20px;
    width: 20px;
    height: 20px;
    background-color: #fff;
    border-radius: 10px;
  }
  /deep/.el-dialog__close {
    color: #00000099;
    font-weight: 600;
  }
  /deep/.el-dialog__footer {
    padding: 0 32px 32px;
  }
  .harvard-shear-content {
    position: relative;
    padding: 16px;
    padding-bottom: 24px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 14px 0 #f0f5ff40;
    .copy-content {
      display: flex;
      padding: 7px 8px;
      padding-left: 16px;
      border: 1px solid #d2e2f1;
      border-radius: 44px;
      .url-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 20px;
        line-height: 32px;
      }
      .copy-btn {
        width: 170px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        background-color: #0052d9;
        color: #fff;
        border-radius: 53px;
        cursor: pointer;
      }
    }
    .tips {
      margin-top: 16px;
      color: #00000099;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      i {
        color: #e34d59;
      }
    }
    .shear-dialog-img {
      position: absolute;
      bottom: 8px;
      right: 16px;
      width: 56px;
      height: 56px;
      img {
        width: 56px;
        height: 56px;
      }
    }
  }
  .qr-code {
    display: flex;
    padding-top: 20px;
    margin-top: 20px;
    border-top: 1px solid #eee;
    .share-rq-code {
      width: 88px;
      height: 88px;
      background: #f5f5f5;
      border-radius: 6px;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 88px;
        height: 88px;
        border-radius: 6px;
      }
    }
  }
}
</style>
