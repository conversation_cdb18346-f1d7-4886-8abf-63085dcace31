<template>
  <el-dialog
    custom-class="add-page"
    width="800px"
    :title="isEdit ? '编辑分发页' : '添加分发页'"
    :visible.sync="examModal"
    top="25vh"
    :close-on-click-modal="false"
    destroy-on-close
  >
    <el-form ref="form" :rules="rules" :model="params" label-width="100px">
      <el-form-item label="跳转地址：" prop="url">
        <el-input
          placeholder="请输入跳转地址"
          class="input-search"
          v-model.trim="params.url"
        />
      </el-form-item>
      <el-form-item label="目标学员：" prop="authoriz_value">
        <AudienceSelector v-if="examModal"
          audience
          multiple
          v-model="params.authoriz_value"
          ref="selector"
          :createStudentID="false"
          appCode="qlearning"
          :env="audienceEnv"
        />
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="close">取 消</el-button>
      <el-button type="primary" @click="handleOk">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { AudienceSelector } from '@tencent/sdc-audience'
const URL_EXP = /^(http|https):\/\/.*$/

export default {
  name: 'examModel',
  components: {
    AudienceSelector
  },
  data () {
    var validateUrl = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请填写跳转地址'))
      } else if (!URL_EXP.test(value)) {
        callback(new Error('请输入正确格式的地址，以http或者https开头'))
      } else {
        callback()
      }
    }
    return {
      params: {
        url: '',
        authoriz_value: '',
        id: null
      },
      examModal: false,
      audienceEnv: process.env.NODE_ENV,
      rules: {
        url: [
          { required: true, message: '请填写跳转地址', trigger: 'blur' },
          { validator: validateUrl, trigger: 'blur' }
        ],
        authoriz_value: [
          { required: true, message: '请选择目标学员', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    isEdit () {
      return this.params.id || false
    }
  },
  watch: {
    'params.authoriz_value': {
      handler (newValue) {
        if (newValue) {
          this.$refs['form'].clearValidate('authoriz_value')
        }
      },
      deep: true
    }
  },
  methods: {
    handleOk () {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.$emit('selected', this.params)
          this.close()
        } else {
          return false
        }
      })
    },
    close () {
      this.examModal = false
      this.$refs['form'].resetFields()
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.add-page {
  border-radius: 8px;
}
.label {
  width: 70px;
  margin-right: 15px;
}
.listSerach {
  display: flex;
  align-items: center;
  .input-search {
    width: 85%;
  }
}
.tip-num {
  display: flex;
  align-items: center;
  margin: 15px 0;
}
</style>
