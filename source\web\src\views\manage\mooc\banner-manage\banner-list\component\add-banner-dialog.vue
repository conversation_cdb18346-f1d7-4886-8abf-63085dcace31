<template>
  <div class="add-banner-dialog">
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="visible"
       width="700px" 
      :close-on-click-modal="false"
      :before-close="closeDialog"
      >
        <div class="add-banner-body">
          <el-form ref="form" :model="form" :rules="rules" label-width="110px">
            <el-form-item label="轮播图名称：" prop="banner_name">
              <el-input
                class="width-420 pub-p-r48"
                type="text"
                placeholder="请输入轮播图名称"
                v-model="form.banner_name"
                maxlength="50"
                show-word-limit
              >
              </el-input>
            </el-form-item>
            <el-form-item label="跳转链接：" prop="link_url">
              <el-input
                class="width-420"
                type="text"
                placeholder="请输入http://或者https://开头的地址"
                v-model="form.link_url"
              >
              </el-input>
              <el-link
              class="link"
              style="margin-left: 10px;"
              :href="form.link_url"
              :disabled="form.link_url === ''"
              :underline="false"
              type="primary"
              target="_blank"
              >测试跳转</el-link>
            </el-form-item>
            <el-form-item label="封面图" prop="cover_image" required>
              <cut-img-upload 
              ref="upload"
              accept=".jpg,.jpeg,.png,.bmp" 
              :fixedNumber="[3, 1]"
              @handleSuccess="handleSuccessImage"
              :dialogImageUrl="form.cover_image" 
              :autoImgUrl="form.cover_image_id" 
              @handleClearImg="handleClearImg"
              >
                <template v-slot:text>
                  <p>建议图片尺寸：1200px * 400px  (3 : 1)</p>
                </template>
              </cut-img-upload>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="handleSave">{{ moduleName === 'add' ? '确定' : '保存' }}</el-button>
        </div>
      </el-dialog>
  </div>
</template>
<script>
import { CutImgUpload } from '@/components/index'
import { addBannerApi, updateBannerApi } from '@/config/mooc.api.conf'
import env from 'config/env.conf.js'
export default {
  components: {
    CutImgUpload
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogTitle() {
      return (this.moduleName === 'add' ? '新建' : '编辑') + '轮播图'
    }
  },
  data() {
    const validImg = (rule, value, callback) => {
      if (!value && !this.form.cover_image_id) {
        return callback(new Error('请选择轮播图'))
      } else {
        callback()
      }
    }
    // 跳转链接校验
    const validateUrl = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入'))
      } else {
        // 需要填写协议
        if (value.indexOf('http://') === 0 || value.indexOf('https://') === 0) {
          callback()
        } else {
          callback(new Error('格式错误，请输入http或https开头的链接地址'))
        }
      }
    }
    return {
      moduleName: 'add',
      form: {
        act_type: '11',
        id: '',
        banner_type: 2,
        banner_name: '',
        link_url: '',
        img_content_id: '',
        status: 1,
        cover_image: '',
        cover_image_id: ''
      },
      rules: {
        banner_name: [
          { required: true, message: '请输入轮播图名称', trigger: 'blur' },
          { min: 0, max: 50, message: '请输入小于50个字符', trigger: 'blur' }
        ],
        link_url: [
          { required: true, message: '请输入跳转链接', trigger: 'blur' },
          { validator: validateUrl, trigger: ['change', 'blur'] }
        ],
        cover_image: [
          { required: true, validator: validImg, trigger: 'blur' }
        ],
        dept_id: [{ required: true, message: '请选择组织', trigger: 'blur' }]
      },
      imgInfo: {},
      fileList: []
    }
  },
  mounted() {
  },
  methods: {
    initData(data) {
      this.moduleName = data.moduleName
      if (data.moduleName === 'edit') {
        this.form.id = data.id
        this.form.banner_type = data.banner_type
        this.form.banner_name = data.banner_name
        this.form.link_url = data.link_url
        this.form.img_content_id = data.img_content_id
        const envName = env[process.env.NODE_ENV]
        if (this.form.img_content_id) {
          this.form.cover_image = `${envName.contentcenter}content-center/api/v1/content/imgage/${this.form.img_content_id}/preview`
        } else {
          this.form.cover_image = ''
        }
        // this.form.cover_image = data.img_content_id
        this.form.status = data.status
      }
    },
    changeCourseAuth(e) {
      this.targetArr = e
    },
    // 保存
    handleSave() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const { mooc_course_id } = this.$route.query
          const { banner_name, banner_type, link_url, status, id, bg_color, img_content_id } = this.form
          const params = {
            act_type: '11',
            mooc_course_id,
            banner_name,
            banner_type,
            link_url,
            status,
            img_content_id: img_content_id,
            bg_color
          }
          if (this.moduleName === 'add') {
            this.addBanner(params)
          } else {
            this.editBanner({ ...params, id: Number(id) })
          }
        } else {
          return false
        }
      })
    },
    addBanner(params) {
      addBannerApi(params).then(res => {
        this.$message.success(this.dialogTitle + '成功')
        this.closeDialog()
        this.$emit('onSearch')
      })
    },
    editBanner(params) {
      updateBannerApi(params).then(res => {
        this.$message.success(this.dialogTitle + '成功')
        this.closeDialog()
        this.$emit('onSearch')
      })
    },
    handleClearImg(val) {
      this.form.cover_image_id = ''
      this.form.cover_image = ''
    },
    handleSuccessImage(val, file, id) {
      this.form.cover_image = val
      // 清空一键生成封面
      this.form.cover_image_id = ''
      this.form.img_content_id = id
      if (this.form.cover_image) {
        this.$refs.form.clearValidate('cover_image')
      }
      this.getImgBgColor(URL.createObjectURL(file))
    },
    getImgBgColor(url) {
      let that = this
      let image = new Image()
      // 处理跨源污染
      // image.setAttribute('crossOrigin', 'anonymous') // anonymous/use-credentials
      image.onload = function() {
        const canvas = document.createElement('canvas')
        canvas.width = image.naturalWidth
        canvas.height = image.naturalHeight
        const context = canvas.getContext('2d')
        context.drawImage(image, 0, 0, image.naturalWidth, image.naturalHeight)
        /**
         * 获取左右各两个边缘像素点的色值
         * 左边两点
         * p1 [x = 10, y = 10]
         * p2 [x = 10, y = height - 10]
         * 右边两点
         * p3 [x = width - 10, y = 10]
         * p4 [x = width - 10, y = height - 10]
         */
        const data = context.getImageData(0, 0, image.width, image.height).data
        const p1 = data.slice(
          (image.width * 10 + 10) * 4,
          (image.width * 10 + 10) * 4 + 4
        )
        const p2 = data.slice(
          (image.width * (image.height - 10) + 10) * 4,
          (image.width * (image.height - 10) + 10) * 4 + 4
        )
        const p3 = data.slice(
          (image.width * 10 + (image.width - 10)) * 4,
          (image.width * 10 + (image.width - 10)) * 4 + 4
        )
        const p4 = data.slice(
          (image.width * (image.height - 10) + (image.width - 10)) * 4,
          (image.width * (image.height - 10) + (image.width - 10)) * 4 + 4
        )
        // 获取像素平均值
        const average = []
        for (let i in p1) {
          average[i] = Math.round((p1[i] + p2[i] + p3[i] + p4[i]) / 4)
        }
        that.form.bg_color = `rgba(${average.join(',')})`
      }
      image.onerror = function (e) {
      } 
      image.src = url
    },
    closeDialog() {
      this.$refs.form.clearValidate()
      this.$refs.upload.clearImg()
      this.form = {
        act_type: '11',
        id: '',
        banner_type: 2,
        banner_name: '',
        link_url: '',
        img_content_id: '',
        status: 1,
        cover_image: '',
        cover_image_id: ''
      }
      this.$emit('update:visible', false)
      this.$emit('close')
    }
  }
}
</script>
<style lang="less" scoped>
.add-banner-dialog {
  :deep(.el-dialog) {
    .el-dialog__header {
      border-bottom: 0;
    }
    .el-dialog__body {
      padding: 0;
      .el-dialog__footer {
        padding: 8px 0 24px;
      }
    }
  }
  .add-banner-body {
    padding: 10px 32px 0;
    .banner-upload {
      display: flex;
      .success-box {
        position: relative;
        height: 102px;
        border: 1px dashed #dcdcdc;
        .banner {
          width: 350px;
          height: 100px;
          line-height: 100px;
          text-align: center;
        }
        .delete-icon {
          display: none;
          position: absolute;
          top: 30px;
          left: 115px;
          color: #fff;
          font-size: 16px;
          padding: 12px;
          border-radius: 20px;
          background-color: rgba(0, 0, 0, 0.3);
          cursor: pointer;
        }
      }
      .success-box:hover {
        .delete-icon {
            display: block;
        }
      }
      .upload-btn {
        width: 350px;
        height: 100px;
        border: 1px dashed #dcdcdc;
        border-radius: 6px;
        color: #666;
        text-align: center;

        .upload-icon {
          font-size: 40px;
          margin-top: 10px;
        }
        .text {
          line-height: 20px;
        }
      }
      .desc {
        flex: 1;
        margin-left: 20px;
        width: 280px;
        line-height: 18px;
        color: #999;
        p {
            margin-top: 15px;
        }
      }
    }
  }
  .width-420 {
    width: 420px;
  }
}
.pub-p-r48 {
  :deep(.el-input__inner) {
    padding-right: 48px;
  }
}
</style>
