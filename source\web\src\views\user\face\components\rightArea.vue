<template>
  <div class="right">
    <!-- 课单 -->
    <courseCard v-if="isShowCourse" :courseData="courseData" dtPageType="面授课详情页-新版"></courseCard>
    <!-- 专区 -->
    <specialCard v-if="isSpecialArea" :courseData="courseData" dtPageType="面授课详情页-新版"></specialCard>
    <!-- 推广 -->
    <!-- <advertisingCard actType="1" :isShowRecommend="courseData.is_show_recommend" :courseData="courseData"></advertisingCard> -->
    <!-- 延伸学习 -->
    <extandCard class="extand-card" v-if="extandList.length" :commonList="extandList" :courseData="courseData" dtPageType="面授课详情页-新版"></extandCard>
    <!-- 猜你喜欢 -->
    <card v-if="guessLikeList.length" dtPageType="面授课详情页-新版" :commonList="guessLikeList" :courseData="courseData" :commonTitle="$langue('NetCourse_GuessYouLike', { defaultText: '猜你喜欢' })" type="NetCourse_GuessYouLike"></card>
    <!-- 相关推荐 -->
    <card v-if="recommendList.length" dtPageType="面授课详情页-新版" :commonList="recommendList" :courseData="courseData" :commonTitle="$langue('NetCourse_RelatedRecommendations', { defaultText: '相关推荐' })" type="NetCourse_RelatedRecommendations"></card>
  </div>
</template>
<script>
import card from '../../netcourse/grayPlay/components/card.vue'
import courseCard from '../../netcourse/grayPlay/components/courseCard'
import specialCard from '../../netcourse/grayPlay/components/specialCard.vue'
import extandCard from '../../netcourse/grayPlay/components/extandCard.vue'
// import advertisingCard from '@/views/user/components/advertisingCard.vue'
import {
  getExtanContentList,
  getRecommendList,
  guessLikeAPI
} from 'config/api.conf'
import { mapState } from 'vuex'
export default {
  components: {
    card,
    courseCard,
    specialCard,
    extandCard
  },
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      extandList: [],
      recommendList: [],
      guessLikeList: []
    }
  },
  computed: {
    ...mapState(['isBusy']),
    course_id() {
      return this.$route.query.course_id ? parseInt(this.$route.query.course_id) : ''
    },
    isShowCourse() {
      return this.$route.query.area_id && this.$route.query.from === 'CourseList'
    },
    isSpecialArea() {
      // 490是首页不显示
      return this.$route.query.from === 'SpecialArea' && this.$route.query.area_id && this.$route.query.area_id !== '490'
    }
  },
  watch: {
    courseData: {
      handler(val) {
        if (val?.created_at) {
          this.getConRecommendList()
        }
      }
    }
  },
  mounted() {
    // this.initData()
    this.getExtandList()
    this.getGuessLike()
  },
  methods: {
    // 猜你喜欢
    getGuessLike() {
      const params = {
        app_id: 'A9BiosXihR0h46ThNsAX',
        size: 10,
        current: 1
      }
      guessLikeAPI(params).then((res) => {
        this.guessLikeList = (res && res.records) || []
        this.guessLikeList = this.guessLikeList.slice(0, 5)
      })
    },
    // 延伸学习
    getExtandList() {
      const params = {
        act_id: this.course_id,
        act_type: 1
      }
      getExtanContentList(params).then((data) => {
        this.extandList = data
      })
    },
    // 相关推荐
    async getConRecommendList() {
      if (this.isBusy !== '1') {
        // 当天创建的数据凌晨12点之前，不请求数据
        let curDateLastTime = new Date().setHours(0, 0, 0, 0)
        let createTimeS = this.courseData?.created_at ? `${this.courseData.created_at.split(' ')[0]} 23:59:59` : ''
        const createTime = new Date(createTimeS).getTime()
        if (curDateLastTime > createTime) {
          const params = {
            module_id: 2,
            item_id: this.course_id
          }
          getRecommendList(params).then((data) => {
            this.recommendList = data || []
            if (this.recommendList.length > 5) {
              this.recommendList = this.recommendList.splice(0, 5)
            }
          })
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
  .right {
    width: 400px;
    margin-left: 20px;
    :deep(.extand-card) {
      .common-content {
        height: 300px;
      }
    }
    .common-card + .common-card {
      margin-top: 20px;
    }
  }
</style>
