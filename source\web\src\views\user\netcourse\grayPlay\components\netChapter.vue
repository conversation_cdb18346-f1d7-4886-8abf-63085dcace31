<template>
  <div class="chapter-main">
    <div :class="['chapter-preview-box', { 'chapter-box-0': chapterSummaryList.length === 0 }]">
      <swiper :options="swiperOption" ref="mySwiper" class="swiper-preview-box">
        <swiper-slide 
        v-for="(item, index) in chapterSummaryList" 
        :key="index" 
        :class="['chapter-card-item', { 'playing-card-item': isPlaying(item.chapter_time_point, index) }]"
        >
          <el-image fit="fill" :src="item.imgUrl" class="preview-img" :data-time="item.chapter_time_point">
            <div class="image-slot" slot="placeholder">
              <i class="el-icon-loading"></i>
            </div>
            <div class="error-cover" slot="error">
              <img :data-time="item.chapter_time_point" :src="require('@/assets/mooc-img/default_bg_img.png')" alt="" />
            </div>
          </el-image>
          <span class="playing-btn" v-if="isPlaying(item.chapter_time_point, index)">
            <span class="playing-icon"></span>
            {{ $langue('NetCourse_Playing', { defaultText: '播放中' }) }}
          </span>
          <div class="time-progress-box" :data-time="item.chapter_time_point">
            <span class="chapter-time" :data-time="item.chapter_time_point">{{ item.chapter_time }}</span>
            <span class="time-line-icon" :data-time="item.chapter_time_point"></span>
          </div>
          <div class="chapter-title" :data-time="item.chapter_time_point">{{ item.chapter_title }}</div>
        </swiper-slide>
        <!-- 箭头 -->
        <!-- 左侧箭头 -->
        <div 
        class="swiper-button-prev" 
        slot="button-prev" 
        :dt-areaid="dtCommon('areaid', '上一节')"  
        @click="handleRight('上一节')"
        >
          <span class="icon-left"></span>
        </div>
        <!-- 右侧箭头 -->
        <div 
        class="swiper-button-next" 
        slot="button-next" 
        @click="handleRight('下一节')" 
        :dt-areaid="dtCommon('areaid', '下一节')"  
        >
          <span class="icon-right"></span>
        </div>
      </swiper>
    </div>
    <div class="ai-tips-box" v-if="showChapterTips">
      <i class="el-icon-warning" style="color: #0052D9"></i>
      <span class="tips">
        {{ $langue('NetCourse_ChapterTips', { defaultText: '当前显示的章节数据为智能生成，如有错漏' }) }}
        <a href="https://km.tencent.com/openkm/url/lpciih" target="_blank">{{ $langue('NetCourse_FeedbackTips', { defaultText: '可点此反馈' }) }}</a>
      </span>
    </div>
    <Empty :emptyShow="emptyShow" />
  </div>
</template>
<script>
import Empty from '@/views/user/components/empty.vue'
let vm
export default {
  components: {
    Empty
  },
  props: {
    chapterSummaryList: {
      type: Array,
      default: () => ([])
    },
    showChapterTips: {
      type: Boolean,
      default: false
    },
    studyRecordQuery: {
      type: Object
    },
    courseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {

    }
  },
  watch: {
    chapterSummaryList: {
      immediate: true,
      deep: true,
      handler(list) {
        if (list.length) {
          list.forEach((v) => {
            this.$nextTick(() => {
              window.BeaconReport('at_show_area', {
                eid: this.dtChapter('area', v),
                remark: this.dtChapter('remark', v)
              })
            })
          })
        }
      }
    }
  },
  computed: {
    course_id() {
      return this.courseData.course_id || parseInt(this.$route.query.course_id)
    },
    emptyShow() {
      return !this.chapterSummaryList.length
    },
    swiperOption() {
      return {
        loop: false,
        // autoplay: {
        //   delay: 3000,
        //   stopOnLastSlide: false,
        //   disableOnInteraction: false
        // },
        autoplay: false,
        direction: 'horizontal',
        slidesPerView: 'auto',
        freeMode: false,
        // 显示分页
        // pagination: {
        //   el: '.swiper-pagination',
        //   clickable: true // 允许分页点击跳转
        // },
        navigation: { // 设置点击箭头
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        on: {
          click: function (e) {
            if (e.target.getAttribute('data-time')) {
              let playTime = e.target.getAttribute('data-time') * 1
              vm.$emit('toChaptersPosition', playTime)
              const v = vm.chapterSummaryList.find((e) => e.chapter_time_point === playTime)
              vm.handleChapter(v)
            }
          }
        }
      }
    }
  },
  created() {
    vm = this
  },
  methods: {
    handleRight(val) {
      window.BeaconReport('at_click', {
        eid: this.dtCommon('eid', val),
        remark: this.dtCommon('remark', val)
      })
    },
    // 判断为播放中的章节
    isPlaying(chapter_time, index) {
      let next_chapter_time = 0
      if (index < this.chapterSummaryList.length - 1) {
        next_chapter_time = this.chapterSummaryList.length > 0 && this.chapterSummaryList[index + 1].chapter_time_point
      } else {
        next_chapter_time = this.studyRecordQuery.course_duration
      }
      // 网络课播放定位
      return (this.studyRecordQuery.my_study_progress >= chapter_time * 1) && (this.studyRecordQuery.my_study_progress < next_chapter_time * 1)
    },
    handleChapter(row) {
      window.BeaconReport('at_click', {
        eid: this.dtChapter('eid', row),
        remark: this.dtChapter('remark', row)
      })
    },
    dtChapter(type, row) {
      if (type === 'remark') {
        return JSON.stringify({
          page: this.courseData.course_name,
          page_type: '网课详情页-新版', 
          container: `章节`,
          click_type: 'data',
          content_type: '',
          content_id: row.id,
          content_name: row.chapter_title,
          act_type: '',
          page_id: '',
          container_id: '',
          terminal: 'PC'
        })
      } else if (type === 'eid') {
        return `element_${this.course_id}_${row.id}`
      } else {
        return `area_${this.course_id}_${row.id}`
      }
    },
    dtCommon(type, val) {
      if (type === 'remark') {
        return JSON.stringify({
          page: this.courseData.course_name,
          page_type: '网课详情页-新版', 
          container: '章节',
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: val,
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'PC'
        })
      } else if (type === 'eid') {
        return `element_${this.course_id}_${val}`
      } else {
        return `area_${this.course_id}_${val}`
      }
    }
  }
}
</script>
<style lang="less" scoped>
.chapter-main {
  margin-top: 10px;
  .ai-tips-box {
    color: #0052D9;
    margin-top: 10px;
    background: #F0F5FD;
    border-radius: 4px;
    height: 36px;
    line-height: 36px;
    padding-left: 12px;
    .tips {
      margin-left: 9px;

      a {
        text-decoration: underline;
        color: #0052D9;
        cursor: pointer;
      }
    }
  }

  .chapter-box-0 {
    display: none;
  }
  .chapter-preview-box {
    background: #F9F9F9;
    border-radius: 8px;
    padding-right: 12px;
  }
  .swiper-preview-box {
    display: flex;
    align-items: flex-start;
    border-radius: 6px;
    height: 190px;
    padding: 6px;

    ::v-deep .chapter-card-item {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 172px;
      padding: 6px;

      .preview-img.el-image {
        margin-bottom: 8px;
        width: 160px;
        height: 90px;
        border-radius: 4px;
      }

      .playing-btn {
        position: absolute;
        left: 17px;
        top: 15px;
        display: inline-block;
        width: 70px;
        height: 20px;
        line-height: 20px;
        border-radius: 4px;
        background: #D91A00;
        color: #ffffff;
        font-family: "PingFang SC";
        font-size: 14px;
      }

      .playing-icon {
        display: inline-block;
        width: 12px;
        height: 12px;
        vertical-align: middle;
        background: url('~@/assets/mooc-img/icon_live.png') no-repeat center / cover;
      }

      .time-progress-box {
        width: 160px;
        height: 24px;
        display: flex;
        align-items: center;
      }

      .chapter-time {
        display: flex;
        height: 24px;
        line-height: 24px;
        padding: 0 4px;
        justify-content: center;
        align-items: center;
        gap: 10px;
        border-radius: 4px;
        background: var(---Gray1-Hover, #F3F3F3);
        color: #6c7390;
        font-family: "PingFang SC";
        font-size: 14px;
        font-weight: 400;
      }

      .time-line-icon {
        width: 100%;
        height: 1px;
        background-color: #EFEFEF;
      }

      .chapter-title {
        margin-top: 4px;
        width: 160px;
        color: #000000e6;
        font-family: "PingFang SC";
        font-size: 12px;
        font-weight: 600;
        line-height: 20px;
        word-break: break-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
    .chapter-card-item + .chapter-card-item {
      margin-left: 6px;
    }
    .chapter-card-item:hover {
      position: relative;
      display: flex;
      width: 172px;
      flex-direction: column;
      align-items: flex-start;
      border-radius: 8px;
      background: #fff;
      box-shadow: 6px 6px 30px 5px #0000001a;
      cursor: pointer;
      z-index: 1000;

      .chapter-time {
        color: #000000e6;
      }

      .time-line-icon {
        display: none;
      }

      .chapter-title {
        color: #266fe8;
        font-weight: 600;
      }
    }

    ::v-deep .playing-card-item {
      .preview-img {
        border-radius: 4px;
        border: 1px solid #0052D9;
      }

      .chapter-time {
        border-radius: 4px;
        background: var(---Brand1-Light, #ECF2FE);
        color: #0034b5;
        font-size: 14px;
      }

      .chapter-title {
        color: #0034b5;
        font-weight: 600;
      }
    }

    ::v-deep .swiper-button-prev,
    .swiper-button-next {
      &:after {
        display: none;
      }

      &.swiper-button-disabled {
        display: none;
      }

      top: 69px;
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 12px;
      background: var(--Color, #FFF);
      box-shadow: 0 8px 10px -5px #00000014,
      0 16px 24px 2px #0000000a,
      0 6px 30px 5px #0000000d;

      .icon-left,
      .icon-right {
        display: inline-block;
        width: 20px;
        height: 20px;
        font-size: 20px;
      }

      .icon-left {
        background: url('~@/assets/mooc-img/chevron-left.png') no-repeat center / cover;
      }

      .icon-right {
        background: url('~@/assets/mooc-img/chevron-right.png') no-repeat center / cover;
      }
    }
  }
}
</style>
