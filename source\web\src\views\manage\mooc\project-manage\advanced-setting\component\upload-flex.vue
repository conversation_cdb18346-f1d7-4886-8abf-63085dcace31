<template>
  <div class="upload-img-warp">
    <div :class="typeClass" class="upload-img">
      <el-upload
        ref="upLoad"
        :auto-upload="true"
        :class="{hide:hideUpload}"
        :file-list="fileList"
        :limit="1"
        :multiple="false"
        :show-file-list="true"
        :accept="accepts"
        :http-request="upload"
        :before-upload="beforeAvatarUpload"
        :on-preview="handlePictureCardPreview"
        :on-remove="handleRemove"
        :on-change="handleChange"
        action
        :disabled="isDisabled"
      >
        <slot name="submit-btn">
          <div class="upload-icon" slot="submit-btn">
            <i class="el-icon-upload-custom"></i>
            <p>上传图片</p>
          </div>
        </slot>
      </el-upload>
      <div class="prompt">
        <p>最多支持上传20个附件，学员参与培训时可以查看或下载资料，支持类型：视频/音频/文档/图片/压缩包</p>
      </div>
    </div>
    <div class="tips-box">
      <p>1. 视频格式：支持wmv、mp4、flv、avi、rmvb、mpg、mkv、mov，单个视频小于2G</p>
      <p>2. 音频格式：支持w4v、m4a、wma、wav、mp3、amr，单个音频小于500M</p>
      <p>3. 文档格式：支持doc、docx、ppt、pptx、xls、xlsx、pdf，单个文档小于100M</p>
      <p>4. 图片格式：支持jpg、jpeg、gif、png、bmp、ico、svg，单个图片小于10M</p>
      <p>5. 压缩包格式：支持zip、rar，单个压缩包小于1G</p>
    </div>
    <el-dialog custom-class="media-dialog" :visible.sync="dialogVisible" width="1024px" @close="handleDialogClose">
      <div class="footer-close">
        <img class="footer-img" src="@/assets/img/close-circle.png"  @click="dialogVisible = false" alt="">
      </div>
      <div v-if="dialogVisible">
        <template v-if="viewFile.type === 1">
          <div id="view-video" ></div>
        </template>
        <template v-if="viewFile.type === 2">
          <div id="view-audio" ></div>
        </template>
        <template v-if="viewFile.type === 5">
          <div id="view-file" >
            <iframe class="view-iframe" id="view-iframe" :src="viewFile.url" width="100%" height="640"></iframe>
          </div>
        </template>
      </div>
      <div class="media-info">
        <p class="media-title">{{ viewFile.name }}</p>
        <span class="media-item">大小: <span class="media-desc">{{ viewFile.file_size }}</span></span>
        <span class="media-item media-margin">上传时间: <span class="media-desc">{{ viewFile.created_at }}</span></span>
        <span class="media-item"> <el-button size="mini" round :class="[!viewFile.allow_download? 'disable-view' : '','media-down']" @click="urlForDownload(viewFile)">下载</el-button></span>
      </div>
    </el-dialog>

    <!-- 查看照片 -->
    <ImageViewer v-if="viewerVisible"
      :urlList="viewFileList"
      :viewFile="viewFile"
      :on-close="closeViewer"
      @down="download"
    ></ImageViewer>
  </div>
</template>

<script>
import env from 'config/env.conf.js'
import ImageViewer from '@/views/components/image-viewer'
import { urlForDownloadApi } from '@/config/mooc.api.conf'
import { getContentInfo, operatesignature } from 'config/api.conf'
const envName = env[process.env.NODE_ENV]
export default {
  components: {
    ImageViewer
  },
  props: {
    accepts: {
      type: String,
      default: '.wmv,.mp4,.flv,.avi,.rmvb,.mpg,.mkv,.mov,.w4v,.m4a,.wma,.wav,.mp3,.amr,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.pdf,.jpg,.jpeg,.gif,.png,.bmp,.ico,.svg,.zip,.rar'
    },
    fileList: {
      type: Array,
      default: () => {
        return []
      }
    },
    typeClass: {
      type: String,
      default: 'icon'
    },
    file_id: {},
    loading: {
      type: Boolean,
      default: false
    },
    slotTip: {},
    isDisabled: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      limitCount: 1,
      hideUpload: false,
      fileTypeArr: [
        {
          suffix: ['wmv', 'mp4', 'flv', 'avi', 'rmvb', 'mpg', 'mkv', 'mov', 'mpeg', 'x-matroska', 'quicktime'],
          file_type: 'Video',
          update_type: 1,
          file_type_name: '视频',
          size: 2147483648,
          size_name: '2GB'
        },
        {
          suffix: ['w4v', 'm4a', 'wma', 'wav', 'mp3', 'amr', 'mpeg'],
          file_type: 'Audio',
          update_type: 2,
          file_type_name: '音频',
          size: 524288000,
          size_name: '500MB'
        },
        {
          suffix: ['jpg', 'jpeg', 'gif', 'png', 'bmp', 'ico', 'svg'],
          file_type: 'Image',
          update_type: 0,
          file_type_name: '图片',
          size: 10485760,
          size_name: '10MB'
        },
        {
          suffix: ['zip', 'rar'],
          file_type: 'Zip',
          update_type: 3,
          file_type_name: '压缩包',
          size: 1073741824,
          size_name: '1GB'
        },
        {
          suffix: ['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf', 'msword', 'vnd.ms-excel', 'vnd.ms-powerpoint'],
          file_type: 'Doc',
          update_type: 3,
          file_type_name: '文档',
          size: 104857600,
          size_name: '100MB'
        }
        
      ],
      curFile: {},
      viewFile: {
        name: '',
        type: '',
        url: '',
        created_at: '',
        file_size: '',
        allow_download: false
      },
      viewFileList: [],
      viewerVisible: false
    }
  },
  watch: {
    fileList: {
      handler(val) {
        this.hideUpload = Array.isArray(val) && val.length >= this.limitCount
      },
      immediate: true
    }
  },
  methods: {
    proportion(width, height) {
      let limit = width / height
      if (Number.isInteger(limit)) {
        return `${limit} : 1`
      } else {
        return `${Math.round(limit * 10)} : 10`
      }
    },
    // 图片上传
    upload({ file }) {
      this.$sdc.loading(`${this.curFile.file_type_name}上传中`)
      let _this = this
      /* eslint-disable*/
      let authUrl = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_PORTAL_HOST_WOA : process.env.VUE_APP_PORTAL_HOST
      new contentCenter.uploadFile({
        file: file,
        type: _this.curFile.update_type, // 0表示上传图片，1视频 2音频 3文档
        appId: 'QLearningService',
        operateAuthUrl:`${authUrl}/training-portal-common/api/v1/portal/user/common/uploadOperateSignature`,
        onSuccess(res) {
          _this.$sdc.loading.hide()
          _this.$emit('handleSuccess', { ...res[0], file_type: _this.curFile.file_type, file_size: _this.curFile.size });
          _this.$message.success(`${_this.curFile.file_type_name}上传成功`);
        },
        onError(err) {
          _this.$sdc.loading.hide()
          _this.$message.error('上传图片失败');
          _this.$emit('update:fileList', []);
        }
      })
      /* eslint-disable*/
    },
    handleRemove(file, fileList) {
      this.$emit('update:file_id', '');
      this.$emit('update:fileList', []);
    },
    handlePictureCardPreview(file) {
      this.viewFile = {
        type: 5,
        name: file.name,
        url: file.url
      }
      this.dialogVisible = true;
    },
    handleChange(file, fileList) {
      this.$refs.upLoad.clearFiles()
    },
    //图片限制大小
    beforeAvatarUpload(file) {
      this.curFile = {}
      let typeObj = JSON.parse(JSON.stringify(this.fileTypeArr))
      let arrL = typeObj.length
      let isSize = false
      let fileType = file.type ? file.type : file.name.substring(file.name.lastIndexOf('.') + 1)
      for (let i = 0;i < arrL;i++) {
        let suffixL = typeObj[i].suffix.length
        for (let v = 0;v < suffixL;v++) {
          let suffix = typeObj[i].suffix
          let reg = RegExp(suffix[v])
          if (reg.exec(fileType)) {
            let size = typeObj[i].size
            isSize = file.size < size
            if (isSize) {
              this.curFile = typeObj[i]
              this.curFile.size = file.size
              break
            } else {
              this.$message.error(`上传${typeObj[i].file_type_name}大小不能超过 ${typeObj[i].size_name}!`)
              return false
            }
          }
        }
      }
      return isSize
    },
    handleViewFile(obj) {
      const { content_id, resource_type, resource_name, created_at, file_size, mooc_course_id,allow_download } = obj
      this.viewFile.type = resource_type
      this.viewFile.name = resource_name
      this.viewFile.content_id = content_id
      this.viewFile.created_at = created_at
      this.viewFile.file_size = file_size
      this.viewFile.act_id = mooc_course_id
      this.viewFile.allow_download = allow_download
      switch (this.viewFile.type) {
        case 1:
          this.handleFilePreview(obj, '#view-video')
          break;
        case 2:
          this.handleFilePreview(obj, '#view-audio')
          break;      
        case 3:
          this.handleImg(obj)
          break;
        case 4:
          this.urlForDownload(obj)
          break;                    
        case 5:
          this.handleFilePreview(obj, '#view-file')
          break;
      }
    },
    handleFilePreview(obj, el) {
      this.dialogVisible = true
      if(this.viewFile.type == 5){
         operatesignature({
          app_id: 'QLearningService',
          content_id: obj.content_id,
          corp_name: 'tencent',
          operate: 'visit'
        }).then((signature) => {
          if (signature) {
            getContentInfo(obj.content_id, {
              app_id: 'QLearningService',
              signature: signature
            }).then((res) => {
              this.$nextTick(() => {
                this.viewFile.url = res.file_info  && res.file_info.doc_url
              })
            })
          }
        })
      } else { 
        this.$nextTick(() => {
          new contentCenter.filePreview({
            el,
            contentId: obj.content_id,
            appId: 'QLearningService',       
            contentInfoUrl: `${envName.trainingPath}api/businessCommon/common/content/previewInfo?app_id=QLearningService&corp_name=tencent&content_id=${obj.content_id}`,
            width: '1024px',
            height: '576px'
          })
        })
      }
    },
    // 图片拼接
    handleImg(data) {
      if (data) {
        this.viewerVisible = true
        this.$nextTick(() => {
          this.viewFile = data
          this.viewFileList = [`${envName.contentcenter}content-center/api/v1/content/imgage/${data.content_id}/preview`]
        })
      }
    },
    // 下载
    download() {
      this.urlForDownload(this.viewFile)
    },
    // 获取下载文件源地址
    urlForDownload(obj) {
      if(!obj.allow_download) return
      urlForDownloadApi(obj.content_id).then(res => {
        this.getBlob(res).then(bolb => {
          this.saveAs(bolb, obj.resource_name || obj.name)
        })
      })
    },
    // 获取 blob 格式文件
    getBlob(url) {
      return new Promise(resolve => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', url, true)
        xhr.responseType = 'blob'
        xhr.onload = () => {
          if (xhr.status === 200) {
              resolve(xhr.response)
          }
        }
        xhr.send()
      })
    },
    // 文件下载
    saveAs(blob, filename) {
      let link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      link.click()
    },
    // 预览弹窗关闭
    handleDialogClose() {
      switch (this.viewFile.type) {
        case 1:
          let video_id = document.getElementsByClassName('vjs-tech')[0].playerId
          let myVideo = document.getElementById(`${video_id}_html5_api`)
          if (myVideo && myVideo.play()) {
            myVideo.pause()
          }
          break
        case 2:
          let audio_id = document.getElementsByClassName('vjs-tech')[0].playerId
          let myAudio = document.getElementById(`${audio_id}_html5_api`)
          if (myAudio && myAudio.play()) {
            myAudio.pause()
          }
          break
        default:
          break
      }
    },
    closeViewer() {
      this.viewerVisible = false
    }
  }
};
</script>
<style lang="less" scoped>
.upload-img {
  display: flex;
  align-items: flex-start;
  :deep(.el-upload--picture-card) {
    width: 120px;
    height: 120px;
    background-color: #FBFBF9;
    border: 1px dashed #DEDEDE;
  }
  :deep(.el-upload--picture-card:hover){
    border: 1px dashed #3464E0;
  }
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    width: 120px;
    height: 120px;
  }
  .prompt{
    margin-left: 16px;
    font-size: 12px;
    color: rgba(0,0,0,0.4);
    p{
      height: 17px;
      line-height: 17px;
    }
    p:first-child{
      padding-top: 6px;
    }
    p:last-child{
      margin-top: 6px;
    }
  }
}
.tips-box {
  padding: 12px;
  margin-top: 16px;
  border-radius: 4px;
  background: #f8f8f8ff;
  color: #00000099;
  font-size: 14px;
  font-weight: 400;
  font-family: "PingFang SC";
  line-height: 22px;
}
.video-box {
  width: 100%;
}
.file-view .view-iframe {
  border: medium none;
}
.view-img {
  width: 758px;
  height: 445px;
  object-fit: cover;
  background: #cecece;
}

:deep .media-dialog{
  .el-dialog__header{
    display: none !important;
  }
  .el-dialog__body{
    padding: 0 !important;
  }
  .media-info{
    padding: 20px; 
    font-family: "PingFang SC";
    .media-title{
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 12px;
      color: #000000e6;
    }
    .media-item{
      color:#00000066;;
      font-size: 14px;
    }
    .media-margin{
      margin: 0 24px;
    }
    .media-desc{
      color: #00000099;
    }
    .media-down{
      background: #ecf2feff;
      color: #0052d9ff;
      font-size: 12px;
    }
    .disable-view{
      color: #00000042;
    }
    .disable-view:hover{
      color: #00000042;
      cursor: not-allowed;
    }
  }
  .footer-close{
    height: 42px;
    line-height: 42px;
    background: #3464E0;
    text-align: right;
    padding: 0 20px;
    .footer-img{
      width: 24px;
      cursor: pointer;
    }
  }
}
</style>
