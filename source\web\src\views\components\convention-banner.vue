<template>
  <div class="convention-banner" :style="bannerStyle">
    <template v-if="mode === '1' ">
      <p class="title">温馨提示</p>
      <p class="content">发布内容共同遵守<a href="javascript:;" class="link" @click="goConvention">《腾讯学堂学习平台文明公约》</a>，期待你的专业见解与分享。</p>
    </template>
    <template v-if="mode === '2' ">
      <p class="title" style="font-weight: 600;">感谢你的无私分享</p>
      <p class="content">期待你的专业见解与实战经验沉淀，共建良好友善的学习氛围，一起遵守<a href="javascript:;" class="link" @click="goConvention">《腾讯学堂学习平台文明公约》</a>。</p>
    </template>
  </div>
</template>

<script>
import env from 'config/env.conf.js'
export default {
  name: 'convention-banner',
  props: {
    bannerStyle: {
      type: Object,
      default: () => ({})
    },
    mode: {
      type: String,
      default: '1'
    }
  },
  data() {
    return {
      
    }
  },
  methods: {
    goConvention() {
      const envName = env[process.env.NODE_ENV]
      let url = `${envName.commonPath}common/convention.html`
      window.open(url, '_blank')
    }
  }
}
</script>

<style lang="less" scoped>
.convention-banner {
  background: #ECF2FE;
  height: 78px;
  padding: 16px 24px;
  margin-bottom: 20px;
  border-radius: 6px;
  .title {
    font-size: 16px;
    color: #ED7B2F;
    line-height: 24px;
    font-weight: 500;
    margin: 0 0 5px 0;
  }
  .content {
    font-size: 14px;
    color: #000000e6;
    line-height: 22px;
    .link {
      cursor: pointer;
      color: #0052D9;
      text-decoration: underline;
    }
  }
}
</style>
