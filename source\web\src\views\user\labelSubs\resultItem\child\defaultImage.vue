<template>
    <div>
        <img :src="info.thumbnail_url | defaultThumbnailUrl(that)"
            :onerror="onerrorUrl(info.module_id)"
            :dt-eid=dtEidCard(info)
            :dt-areaid="dataModuleAreaid(info)" 
            :dt-remark="dtRgihtModuleRemark(info)"
        >
    </div>
</template>
<script>
export default {
  props: ['info', 'curModuleId', 'curLabelId'],
  data() {
    return {
      that: this
    }
  },
  computed: {
    dtEidCard() {
      return (val) => {
        return `element_${val.item_id}_${val.module_id}`
      }
    },
    dataModuleAreaid() {
      return (val) => {
        return `area_${val.item_id}_${val.module_id}`
      }
    },
    dtRgihtModuleRemark() {
      return (val, container) => {
        return JSON.stringify({ 
          page: '标签订阅内容', 
          page_type: '标签订阅内容',
          container: '已上架内容',
          click_type: 'data',
          content_name: val.title,
          content_id: val.item_id,
          content_type: val.module_name,
          terminal: 'PC'
        })
      }
    },
    onerrorUrl() {
      return (id) => {
        let str = ''
        if (this.info.module_id !== 99) {
          str = `this.src='https://xue.m.tencent.com/mail/labelsub/default-photo-${id}.png'`
        } else {
          str = `this.src='https://xue.m.tencent.com/qlmini/image/default/link.png'`
        }
        return str
      }
    }
  },
  filters: {
    defaultThumbnailUrl(val, that) {
      if (!val || val === '') {
        return that.formatModuleMap(that.info.module_id)
      }
      return val
    }
  },
  methods: {
    formatModuleMap(id) {
      const moduleMap = {
        1: { pic: 'https://xue.m.tencent.com/mail/labelsub/default-photo-1.png', text: '网络课' },
        2: { pic: 'https://xue.m.tencent.com/mail/labelsub/default-photo-2.png', text: '面授课' },
        3: { pic: 'https://xue.m.tencent.com/mail/labelsub/default-photo-3.png', text: '直播' },
        4: { pic: 'https://xue.m.tencent.com/mail/labelsub/default-photo-4.png', text: '活动' },
        6: { pic: 'https://xue.m.tencent.com/mail/labelsub/default-photo-6.png', text: '行家' },
        7: { pic: 'https://xue.m.tencent.com/mail/labelsub/default-photo-7.png', text: '案例' },
        8: { pic: 'https://xue.m.tencent.com/mail/labelsub/default-photo-8.png', text: '文章' },
        10: { pic: 'https://xue.m.tencent.com/mail/labelsub/default-photo-10.png', text: '培养项目' },
        15: { pic: 'https://xue.m.tencent.com/mail/labelsub/default-photo-15.png', text: '课单' },
        16: { pic: 'https://xue.m.tencent.com/mail/labelsub/default-photo-16.png', text: '文档' },
        20: { pic: 'https://xue.m.tencent.com/mail/labelsub/default-photo-20.png', text: 'k吧文章' },
        99: { pic: 'https://xue.m.tencent.com/qlmini/image/default/link.png', text: '外链' }
      }
      return moduleMap[id].pic
    }
  }
}
</script>
<style lang="less" scoped>
    div{
        height: 167px;
        display: flex;
        align-items: center;
        justify-content: center;
        img{
            max-width: 100%;
            max-height: 100%;
        }
    }
</style>
