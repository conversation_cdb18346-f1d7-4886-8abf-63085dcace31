<template>
  <el-dialog title="" custom-class="none-border-dialog authorize-dialog" :visible="visible" width="500px"
    :close-on-click-modal="false" :show-close="false">
    <div class="dialog-body">
      <span class="title">个人信息授权确认</span>
      <div class="authorize-content">
        <p>1、确认上传的头像照片为本人头像信息，
          <span class="tips">不得上传他人照片信息。</span>
        </p>
        <p>2、同意并授权平台调用手机摄像头访问权限用于拍摄照片、
          上传头像，同意对人脸图片进行采集和分析，仅用于配合课程使用的AI智能人像动画输出。
        </p>
        <p>3、如不同意，则无法使用人像模式。</p>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" type="primary" @click="closeDialog">不同意</el-button>
      <el-button size="small" type="primary" @click="agree">同 意</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
    }
  },
  methods: {
    closeDialog() {
      this.$emit('update:visible', false)
    },
    agree() {
      this.$emit('handleAgree')
    }
  }
}
</script>
<style lang="less" scoped>
.authorize-dialog {
  .tips {
    color: red;
  }

  .title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 16px;
    display: inline-block;
  }

  .authorize-content {
    line-height: 20px;
    margin-bottom: 16px;
  }
}
</style>
