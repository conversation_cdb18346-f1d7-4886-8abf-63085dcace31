<template>
  <div class="LabelManagement">
    <div class="labebody">
      <div class="labe_header">
        <el-button type="primary" size="medium" @click="Addlabel()"
          >新建分类</el-button
        >
        <el-button size="medium" @click="export_label_data">导出明细</el-button>
      </div>
      <div class="labe_center">
        <div>
          <div class="theheade">
            <span style="margin-left: 10px">标签分类</span>
            <span style="margin-left: 10px">分类id</span>
            <span
              >标签数量<el-tooltip class="item" effect="dark" placement="top">
                <div slot="content">标签课程数实时更新</div>
                <i
                  class="el-icon-warning-outline"
                  style="margin-left: 6px"
                ></i> </el-tooltip
            ></span>

            <span style="margin-right: 128px">操作</span>
          </div>
          <el-tree
            :data="data"
            node-key="index"
            :expand-on-click-node="expand"
            @node-drag-start="handleDragStart"
            @node-drag-enter="handleDragEnter"
            @node-drag-leave="handleDragLeave"
            @node-drag-over="handleDragOver"
            @node-drag-end="handleDragEnd"
            @node-drop="handleDrop"
            draggable
            :allow-drop="allowDrop"
            :allow-drag="allowDrag"
            :render-content="renderContent"
          >
          </el-tree>
        </div>
      </div>
      <el-dialog
        :modal="false"
        top="500px"
        :visible.sync="changelabe_name"
        width="15%"
        :before-close="handleClose"
        :show-close="close"
        custom-class="labelschangename"
      >
        <el-input
          class="input-label"
          type="text"
          placeholder="请输入标签名称"
          v-model="labname"
          @input="checkNickName"
          size="small"
        >
        <template slot="suffix">
            <div style="height: 36px;line-height: 36px;">{{ labname ? handleValidor(labname, 25) : 0  }}/25</div>
          </template>
        </el-input>
        <span
          class="labelnameerror"
          style="color: red; display: block; margin-top: 10px"
          v-show="nickFlag"
          >{{ errorMsg }}</span
        >
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="outlabel">取 消</el-button>
          <el-button
            size="small"
            type="primary"
            :disabled="disabledbtn"
            @click="changelabe"
            >确 定</el-button
          >
        </span>
      </el-dialog>
    </div>
    <div class="labe_bottom">
      <el-button type="primary" class="bottoms" size="medium" @click="save()"
        >保存</el-button
      >
    </div>
    <Labelnumblist
      :visible.sync="showlabelnum"
      ref="Refinement"
      :itemData="showlistlabel"
    ></Labelnumblist>
  </div>
</template>

<script>
import {
  export_label_dataAPI,
  category_treeAPI,
  saveAPI
} from 'config/api.conf'
import Labelnumblist from './labelNumbList.vue'
export default {
  components: {
    Labelnumblist
  },
  data() {
    return {
      numbindex: 10000,
      nickFlag: false,
      disabledbtn: false,
      errorMsg: '',
      datalists: {},
      close: false,
      labname: '',
      changelabe_name: false,
      expand: false,
      showlabelnum: false,
      showlistlabel: [],
      tableParams: {
        page_no: 1,
        page_size: 10,
        total: 0
      },
      data: [],
      defaultProps: {
        children: 'childrenes',
        label: 'label'
      }
    }
  },
  created() {
    this.getlist()
  },
  methods: {
    getlist() {
      category_treeAPI().then((res) => {
        this.data = res.map((item, index) => {
          item.index = index
          item.key = 1
          item.label = item.category_name
          item.children = item.sub_categories
          item.sub_categories.map((es, indexs) => {
            es.index = index + '' + indexs
            es.label = es.category_name
            es.children = es.sub_categories
            return es
          })
          return item
        })
      })
    },
    // 检查备注名
    checkNickName() {
      let reg = /^[A-Za-z0-9-+#\\()（）\u4e00-\u9fa5\s]{0,1000}$/ // 中文，数字，字母，下划线
      // this.computedStrLen(this.remarkName)
      if (!reg.test(this.labname) && this.labname !== '') {
        this.errorMsg = '备注名称中不能包含特殊符号'
        this.nickFlag = true
        this.disabledbtn = true
      } else {
        if (this.handleValidor(this.labname) > 25) {
          this.errorMsg = '最多输入50个字符(1个汉字2个字符)'
          this.nickFlag = true
          this.disabledbtn = true
        } else {
          this.nickFlag = false
          this.disabledbtn = false
        }
      }
    },
    handleValidor(value, num) {
      if (value) {
        const china = value.match(/[\u4e00-\u9fa5]/g)
        const zhCount = china && china.join('').length
        const enCount = Math.ceil((value.length - zhCount) / 2)
        const total = zhCount + enCount
        if (total > num) {
          // this.label_name = value.slice(0, -1)
        }
        return total || 0
      }
      return 0
    },
    // computedStrLen(str) {
    //   var len = 0
    //   for (var i = 0; i < str.length; i++) {
    //     var c = str.charCodeAt(i)
    //     // 单字节加1
    //     if ((c >= 0x0001 && c <= 0x007e) || (c >= 0xff60 && c <= 0xff9f)) {
    //       len++
    //     } else {
    //       len += 2
    //     }
    //   }
    //   return len
    // },
    changelabe() {
      if (this.labname === '') {
        this.errorMsg = '请输入标签名称'
        this.nickFlag = true
      } else {
        this.nickFlag = false
        this.datalists.category_name = this.labname
        this.datalists.label = this.labname
        this.changelabe_name = false
      }
    },
    outlabel() {
      this.changelabe_name = false
      this.nickFlag = false
    },
    handleClose(done) {
      this.disabledbtn = false
    },
    handleDragStart(node, ev, e) {},
    // 树数据移动的几个回调
    handleDragEnter(draggingNode, dropNode, ev) {},
    handleDragLeave(draggingNode, dropNode, ev) {},
    handleDragOver(draggingNode, dropNode, ev) {},
    handleDragEnd(draggingNode, dropNode, dropType, ev) {
    },
    handleDrop(draggingNode, dropNode, dropType, ev) {
    },
    allowDrop(moveNode, inNode, type) {
      if (
        moveNode.level === 2 &&
        inNode.level === 2 &&
        moveNode.parent.id === inNode.parent.id
      ) {
        // 四种情况
        if (moveNode.nextSibling === undefined) {
          return type === 'prev'
        } else if (inNode.nextSibling === undefined) {
          return type === 'next'
        } else if (moveNode.nextSibling.id !== inNode.id) {
          return type === 'prev'
        } else {
          return type === 'next'
        }
      }
      if (
        moveNode.level === 1 &&
        inNode.level === 1 &&
        moveNode.data.category_id !== 7 &&
        inNode.data.category_id !== 7
      ) {
        return type === 'prev' || type === 'next'
      }
    },
    allowDrag(draggingNode) {
      return draggingNode.data.label.indexOf('三级 3-2-2') === -1
    },
    append(data) {
      const newChild = {
        label: '二级标签',
        category_id: null,
        category_name: '二级标签',
        parent_id: data.category_id,
        indexs: data.index + '' + this.numbindex + 1,
        children: []
      }
      this.numbindex = this.numbindex + 1
      if (!data.children) {
        this.$set(data, 'children', [])
        this.$set(data, 'sub_categories', [])
      }
      data.children.push(newChild)
      data.sub_categories = data.children
    },

    remove(node, data) {
      this.$messageBox
        .confirm('确认删除该条数据吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        .then(() => {
          if (data.label_count) {
            this.$message({
              type: 'warning',
              message: '分类下存在标签不能删除'
            })
          } else if (
            data.key !== 1 &&
            node.data.label_count > 0 &&
            node.parent.childNodes.length === 1
          ) {
            this.$message({
              type: 'warning',
              message: '仅剩一个标签无法删除'
            })
          } else {
            const parent = node.parent
            const children = parent.data.children || parent.data
            if (node.data.category_id !== null) {
              const index = children.findIndex(
                (d) => d.category_id === data.category_id
              )
              children.splice(index, 1)
            } else if (node.data.key !== 1) {
              const index = children.findIndex((d) => d.indexs === data.indexs)
              children.splice(index, 1)
            } else {
              const index = children.findIndex((d) => d.index === data.index)
              children.splice(index, 1)
            }

            this.$message({
              type: 'success',
              message: '已删除'
            })
          }
        })
        .catch(() => {})
    },
    renderContent(h, { node, data, store }) {
      return (
        <span class="custom-tree-node">
          <span class="custom-left">
            {' '}
            <span class="customname">{node.label}</span>
            <i class="el-icon-edits" on-click={() => this.changename(data)}></i>
          </span>
          <span>
            {' '}
            <span class="class_id">{data.category_id}</span>
            <span class="label_numb" on-click={() => this.labelnumblist(data)}>
              {data.label_count}
            </span>
            <span style="color:#2268dd；" class="labelcz">
              {data.key && (
                <el-button
                  class="append"
                  size="medium"
                  type="text"
                  on-click={() => this.append(data)}
                >
                  添加二级标签
                </el-button>
              )}
              <el-button
                size="medium"
                type="text"
                class="remove"
                on-click={() => this.remove(node, data)}
              >
                删除
              </el-button>
              <i class="el-icon-sorts"></i>
            </span>
          </span>
        </span>
      )
    },
    changename(data) {
      this.datalists = data
      this.changelabe_name = true
      this.labname = data.category_name
    },
    save() {
      this.$messageBox
        .confirm('保存后将于用户前端页面生效,请谨慎操作。', '确认保存', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          let params = this.data
          saveAPI(params).then((res) => {
            this.$message({
              type: 'success',
              message: '保存成功!'
            })
            this.getlist()
          })
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消保存'
          })
        })
    },
    Addlabel() {
      if (this.data.length === 50) {
        this.$message({
          type: 'warning',
          message: '您已经达到一级标签创建上限'
        })
      } else {
        let datas = {
          category_id: null,
          label: '一级标签',
          category_name: '一级标签',
          parent_id: null,
          key: 1,
          index: this.data.length + 1,
          children: [],
          sub_categories: []
        }
        this.data.splice(this.data.length - 0, 0, datas)
      }
    },
    export_label_data() {
      export_label_dataAPI().then((res) => {})
    },
    labelnumblist(data) {
      this.showlabelnum = true
      this.showlistlabel = data
    }
  }
}
</script>
<style lang="less" scoped>
.input-label{
   :deep .el-input__inner{
    padding-right: 45px;
   }
}
/deep/.theheade {
  font-weight: 600;
  font-size: 14px !important;
}
/deep/.labelschangename .el-dialog__header {
  display: none;
}
/deep/.labelschangename {
  margin-left: 280px;
}
/deep/.labelschangename .el-dialog__body {
  padding-bottom: 10px;
}
.LabelManagement {
  background: #f0f4f9;
  .labebody {
    padding-bottom: 12px;
    background-color: #fff;
  }
  .labe_bottom {
    margin-top: 20px;
    height: 70px;
    text-align: center;
    background-color: #fff;
    .bottoms {
      margin-top: 20px;
    }
  }
}
.labe_header {
  margin-left: 20px;
}
.labe_center {
  margin: 20px 0;
  padding: 0 20px 20px 20px;
  border: 2px solid #f5f5f5;
  // height: 600px;
  overflow: auto;
  font-size: 14px;
  .theheade {
    height: 50px;
    justify-content: space-between;
    display: flex;
    background: #f5f5f5;
    line-height: 50px;
  }
  :deep(.custom-left) {
    color: #000;
    display: inline-block;
    width: 58%;
  }
  font-size: 16px;
  :deep(.el-tree-node__content) {
    height: 50px;
    border-bottom: 1px solid #f5f5f5;
  }
  :deep(.custom-tree-node) {
    font-size: 14px;
    line-height: 31px;
    // height: 41px;

    display: flex;
    justify-content: space-between;
    width: 100%;
    :deep(.customname) {
      display: inline-block;
      height: 40px;
      line-height: 40px;
      width: 70%;
      margin-right: 15px;
    }
    .el-icon-edits {
      display: inline-block;
      width: 16px;
      height: 14px;
      background: url('../../../assets/img/edit_1.png') no-repeat;
      background-size: 16px 16px;
      margin-left: 20px;
    }
    .class_id {
      width: 50px;
      position: absolute;
      right: 66%;

    }
    .label_numb {
      width: 50px;
      color: #3464e0;
      position: absolute;
      right: 35.5%;
    }
    .el-icon-sorts {
      display: inline-block;
      margin-top: 8px;
      float: right;
      width: 14px;
      height: 16px;
      margin-left: 10px;
      margin-right: 10px;
      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAACXBIWXMAABYlAAAWJQFJUiTwAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAABkSURBVHgB7dSxDYAwEENRAwMwAQMg9mElVmMBxBBUMAGHSJHOzVXRf5KlK6wUKSzBWyNnZC93ar+T9z00lfuJzJn9Xt5Y3Xd2f5B3RRb9v7VFjuQ+YLED7ADADrADADvADrTtBZWcPgls++zhAAAAAElFTkSuQmCC)
        no-repeat;
      background-size: 16px 16px;
    }
  }
}
</style>
