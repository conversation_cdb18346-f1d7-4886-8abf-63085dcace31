<template>
  <div class="faceClass">
    <link rel="stylesheet" :href="linkHref" />
    <div class="container">
      <div class="content">
        <p class="title">面授班级助手功能暂仅支持移动端访问，扫码即可：</p>
        <p class="tips">1、查看你已报名班级的课前材料</p>
        <p class="tips">2、参加投票、提问、抢答等班级互动</p>
        <p class="tips">3、查看课堂PPT、课程录像等课后延伸学习材料</p>
        <vue-qr
          class="code"
          v-if="url"
          :text="url"
          :logoScale="0.2"
          :margin="0"
        >
        </vue-qr>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'error',
  data() {
    return {
      url: '',
      linkHref: ''
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (
          val.staff_name &&
          !document.getElementById('graphic-common-head') &&
          !['mooc', 'spoc'].includes(this.$route.query.from)
        ) {
          this.linkHref = window.location.hostname.endsWith('.woa.com')
            ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeaderStyle.css`
            : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeaderStyle.css`
          const commonHeaderJsUrl = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeader.js` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeader.js`
          this.loadHeadJS(commonHeaderJsUrl, function () {
            if (window.$qlCommonHeader) {
              window.$qlCommonHeader.create({
                staff_name: val.staff_name,
                dev: !(process.env.NODE_ENV === 'production')
              })
            }
          })
        }
      },
      immediate: true
    }
  },
  created() {
    this.url = `https://sdc.qq.com/s/xbdroo?scheme_type=classDetail&class_id=${this.$route.query.class_id}&tab=in`
  },
  methods: {
    // 获取登陆用户信息
    loadHeadJS(url, callback) {
      var script = document.createElement('script')
      var fn = callback || function () {}
      script.type = 'text/javascript'
      script.id = 'graphic-common-head'
      // IE
      if (script.readyState) {
        script.onreadystatechange = function () {
          if (
            script.readyState === 'loaded' ||
            script.readyState === 'complete'
          ) {
            script.onreadystatechange = null
            fn()
          }
        }
      } else {
        // 其他浏览器
        script.onload = function () {
          fn()
        }
      }
      script.src = url
      document.getElementsByTagName('head')[0].appendChild(script)
    }
  }
}
</script>

<style lang="less" scoped>
.faceClass {
  height: 100%;
  display: flex;
  justify-content: center;
  color: #333333;
  .container {
    height: 100%;
    display: flex;
    align-items: center;
    text-align: center;
    background-color: #fff;
    .content {
      width: 100%;
      .title {
        margin-bottom: 16px;
        font-size: 20px;
        font-weight: 600;
      }
      .tips {
        font-size: 18px;
        line-height: 26px;
      }
      .code {
        margin-top: 32px;
        width: 160px;
        height: 160px;
        border-radius: 4px;
      }
    }
  }
}
@media screen and (max-width: 1660px) {
  .container {
    width: 1148px;
  }
}
@media screen and (min-width: 1661px) {
  .container {
    width: 1440px;
  }
}
</style>
