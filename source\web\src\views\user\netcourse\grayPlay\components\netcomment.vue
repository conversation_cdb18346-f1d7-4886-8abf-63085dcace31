<template>
  <sdc-comment :params="commentParams" :courseInfo="courseInfo" class="comment-box" />
</template>
<script>
export default {
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    },
    dtPageType: {
      type: String,
      default: '网课详情页-新版'
    }
  },
  data () {
    return {
      commentParams: {}
    }
  },
  computed: {
    course_id() {
      return this.$route.query.course_id ? parseInt(this.$route.query.course_id) : ''
    },
    courseInfo() {
      return {
        mooc_course_id: this.course_id,
        page: this.courseData.course_name, // 任务名称
        page_type: this.dtPageType,
        container: '评论', // 板块的名称
        click_type: 'button',
        terminal: 'PC'
      }
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          const hostUrl = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_PORTAL_HOST_WOA : process.env.VUE_APP_PORTAL_HOST
          this.commentParams = {
            userName: val.staff_name,
            actId: this.course_id,
            appId: 'A9BiosXihR0h46ThNsAX',
            scrollTarget: '.graphic-user-page',
            urlConfig: {
              getComments: `${hostUrl}/training/api/netcourse/user/course-comment/get_comments`,
              addComment: `${hostUrl}/training/api/netcourse/user/course-comment/add`,
              deleteComment: `${hostUrl}/training/api/netcourse/user/course-comment/delete/`,
              like: `${hostUrl}/training/api/netcourse/user/course-comment/praised`,
              sticky: `${hostUrl}/training/api/netcourse/user/course-comment/sticky`,
              show: `${hostUrl}/training/api/netcourse/user/course-comment/show`
            }
          }
        }
      },
      immediate: true
    }
  }

}
</script>
<style lang="less" scoped>
.comment-box {
  background: #fff;
  padding: 16px 24px 24px;
}
</style>
