<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
<title></title>
<style>
  .tof-modal-root {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .tof-modal-wrap {
    width: 100%;
    height: 100%;
    overflow: auto;
    outline: 0;
  }
  .tof-modal {
    width: 80%;
    max-width: 720px;
    height: 100%;
    margin: auto;
    background-color: #fff;
    border-radius: 8px;
    /* box-shadow: 0 3px 6px rgba(0,0,0,.12), 0 6px 16px rgba(0,0,0,.08), 0 9px 28px rgba(0,0,0,.05); */
    padding-bottom: 0;
  }
  .tof-modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .tof-modal-body {
    padding: 32px;
    flex-grow: 1;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .countdown-container {
    text-align: center;
  }
  .countdown-icon svg {
    width: 64px;
    height: 64px;
  }
  .countdown-success-text {
    font-size: 1.2rem;
    margin-top: 16px;
  }
  .countdown-text {
    margin-top: 16px;
    margin-bottom: 32px;
  }
  .tof-btn {
    padding: 8px 16px;
    font-size: 1rem;
    border-radius: 4px;
    cursor: pointer;
    border: none;
  }
  .tof-btn-primary {
    color: #fff;
    border-color: #0052D9;
    background-color: #0052D9;
  }
  .tof-btn-primary:hover {
    border-color: #3676F6;
    background-color: #3676F6;
  }
  @media (max-width: 600px) {
    .tof-modal {
      width: 90%;
    }
  }
</style>
</head>
<body>

<div class="tof-modal-root">
  <div class="tof-modal-wrap">
    <div class="tof-modal">
      <div class="tof-modal-content">
        <div class="tof-modal-body">
          <div class="countdown-container">
            <div class="countdown-icon">
              <svg class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" width="300" height="300"><path d="M512 97.52381c228.912762 0 414.47619 185.563429 414.47619 414.47619s-185.563429 414.47619-414.47619 414.47619S97.52381 740.912762 97.52381 512 283.087238 97.52381 512 97.52381z m193.194667 218.331428L447.21981 581.315048l-103.936-107.812572-52.662858 50.761143 156.379429 162.230857 310.662095-319.683047-52.467809-50.956191z" fill="#00a870"></path></svg>
            </div>
            <div class="countdown-success-text">登录成功</div>
            <div class="countdown-text"><span id="countdown">3</span> 秒后自动关闭</div>
            <button class="tof-btn tof-btn-primary">直接关闭</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
let countdown = 3;
const countdownElement = document.getElementById('countdown');
const timer = setInterval(() => {
  if (countdown <= 1) {
    closeLogin();
    return;
  }
  countdown--;
  countdownElement.textContent = countdown;
}, 1000);

function closeLogin() {
  clearInterval(timer);
  countdown = 5;
  window.parent && window.parent.postMessage({ from: 'TaihuReloginPage', events: 'closeLoginPage' }, '*');
  console.log('重新登录窗口发起了-closeLogin')
}

document.querySelector('.tof-btn-primary').addEventListener('click', closeLogin);
</script>

</body>
</html>