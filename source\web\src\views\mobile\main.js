/**
 * 按需加载
 */
import 'whatwg-fetch'
import Vue from 'vue'
import {
  render
} from 'sdc-vue'
import {
  vant, ele
} from 'plugins'
import SDCWebUI from '@tencent/sdc-webui'
// 应用配置
import App from './app.vue'
import router from 'router/mobile.js'
import store from '../../store/index.js'
import env from 'config/env.conf'
import sdcCommentMob from 'sdc-comment-mob'
// 插件配置
import 'plugins/svg/'
import 'plugins/styles'
import Vconsole from 'vconsole'
import i18n from './locales/i18n'
import { init } from '@sdc-monitor/browser'
import { vuePlugin } from '@sdc-monitor/vue'
// @tencent/sdc-label-show-mob 标签展示组件移动端
import sdcLabelShowMob from '@tencent/sdc-label-show-mob'
import '@tencent/sdc-label-show-mob/lib/sdc-label-show-mob.css'
// import sdcLabelShowMob from '@tencent/sdc-label-show-mob-test'
// import '@tencent/sdc-label-show-mob-test/lib/sdc-label-show-mob-test.css'
import VueAwesomeSwiper from 'vue-awesome-swiper'
import 'swiper/css/swiper.css'

import AutoTrackBeacon from '@tencent/autotracker-beacon-oa'
import exposure from '@tencent/autotracker-beacon-oa/dist/plugin/exposure'
if (window.location.search.indexOf('debugger=true') > -1) {
  // eslint-disable-next-line no-new
  new Vconsole()
}

Vue.prototype.autoInstanceMob = function(data) {
  const autoInstance = new AutoTrackBeacon({
    report: {
      enableReport: () => true,
      appkey: process.env.NODE_ENV === 'production' ? '0WEB05I0WC0H1J6I' : 'test0WEB05I0WC0H1J6I', // 从datahub获取的appkey
      consolelog: process.env.NODE_ENV === 'production',
      commonParams: { // 自定义的上报公共参数, 每条上报都会携带
        uid: data?.staff_id || '' // 业务用户身份标示，推荐使用uid作为key
      },
      beforeReport: (type, reportParams, commonParams) => {
        if (type === 'at_imp') {
          let info = window?.$dtRegionalExposurePeporting.getPagePublicExposureInfo() || ''
          reportParams.A316 = ''
          if (info) reportParams.A316 = info
        }
        return {
          reportEventCode: type,
          reportParams
        }
      }
    },
    uselib: ['element'] // 预设了ui库track规则，包括omui,antd,element,tdesign等；不设置该项则没有预设规则，完全依据传入的track配置
  })
  autoInstance.use(exposure)
  // ！！！！初始化，注意这里要显式调用init方法
  autoInstance.init()
}

// 路由变化
router.beforeEach((to, from, next) => {
  // 修改title
  document.title = to.meta.title
  // 根据路由自动曝光上报
  if (window.BeaconReport) {
    window.$dtRegionalExposurePeporting && window.$dtRegionalExposurePeporting.pagePublicInfoAutomaticExposure()
  } else {
    setTimeout(() => {
      window.$dtRegionalExposurePeporting && window.$dtRegionalExposurePeporting.pagePublicInfoAutomaticExposure()
    }, 500)
  }
  next()
})

export default render(App, {
  router,
  store,
  env,
  i18n,
  plugins: [vant, ele],
  fastclick: false,
  lazy: {
    preLoad: 1,
    loading: require('assets/img/loading-spinning-bubbles.svg')
  },
  init: Vue => {
    Vue.use(sdcCommentMob, {
      i18n: (key, value) => i18n.t(key, value)
    })
    Vue.use(sdcLabelShowMob)
    Vue.use(SDCWebUI)
    Vue.use(VueAwesomeSwiper)
    Vue.prototype.$langue = function (key, obj = {}) {
      if (typeof obj !== 'object' && !obj.defaultText) {
        const errorMsg = { 
          message: `$langue(): 'defaultText' parameter cannot be empty。`,
          mobile_key: key,
          data: obj
        }
        throw errorMsg
      }
      let config = JSON.parse(JSON.stringify(obj))
      delete config.defaultText
      const langue = this.$t(key, config)
      if (langue !== key) {
        return langue
      }
      return obj.defaultText
    }
    if (process.env.NODE_ENV === 'production') {
      init({
        apikey: 'mooc',
        debug: false,
        vue: Vue,
        dsn: env[process.env.NODE_ENV].commonPath + 'training-portal-common/api/front/log/upload',
        maxBreadcrumbs: 2
      }, [vuePlugin])
    }
  }
})
