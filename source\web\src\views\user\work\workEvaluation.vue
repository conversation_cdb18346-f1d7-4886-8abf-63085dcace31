<template>
  <div class="work-eavlution">
    <!-- 作业互评显示 -->
    <div class="eavlution-top" v-if="!eavlutionHide">
      <p class="back-t"><i class="el-icon-warning-outline"></i>
        {{$langue('Mooc_TaskDetail_HomeWork_EvaluateCount', { defaultText: '互评份数' })}}：
        <span class="num">{{workInfo.student_review_count}}/{{workInfo.student_mark_count}}</span>
        <span class="date">
          {{$langue('Mooc_TaskDetail_HomeWork_EvaluateTime', { defaultText: '作业互评时间' })}}：
          <span class="num">{{workInfo.student_mark_start_time ? `${workInfo.student_mark_start_time} 至 ${workInfo.student_mark_end_time}` : '不限制'}}</span>
        </span>
      </p>
    </div>
    <div class="eavlution-cur">
      <workList 
      :total="total"
      :studentList="studentList"
      :activeStudent="activeStudent"
      :detailData="workInfo"
      :workListLoading.sync="workListLoading"
      @getStudentList="getStudentList"
      @handleCurrentActive="handleCurrentActive"
      listType="user-mutual"
      ref='workListRef'
      ></workList>
      <div class="right">
        <!-- 互评可以填写评价 -->
        <div class="explanation-item my-eavlution" v-if="!eavlutionHide">
          <div class="item-label">
            <span class="line"></span>
            <span class="label-text">{{ $langue('Mooc_TaskDetail_HomeWork_MyEvalution', { defaultText: '我的评价' }) }} <span class="name" v-if="anonymous">{{ $langue('Mooc_TaskDetail_HomeWork_Anonymous', { defaultText: '匿名' }) }}</span> </span>
          </div>
          <div class="item-value">
            <div class="eav-content" v-if="myMark?.created_at">
              <span class="num">{{myMark.score}}</span>
              <span class="date">{{myMark.created_at}}</span>
              <el-button size="mini" @click="handleEvaluate('edit')">{{ $langue('Mooc_TaskDetail_HomeWork_EditEvalute', { defaultText: '修改评价' }) }}</el-button>
              <div class="res" v-html="myMark.content">
              </div>
            </div>
            <div class="none" v-else>
              <span class="none-t">{{ $langue('Mooc_TaskDetail_HomeWork_NoComment', { defaultText: '暂无评价' }) }}</span> 
              <el-button type="primary" size="mini" @click="handleEvaluate">填写评价</el-button> 
            </div>
          </div>
        </div>
        <div class="explanation-item">
          <div class="item-label" v-if="!eavlutionHide">
            <span class="line"></span>
            <span class="label-text">{{ $langue('Mooc_TaskDetail_HomeWork_HoneworkContent', { defaultText: '作业内容' }) }}</span>
          </div>
          <div :class="[{'tab-work-list-vale': !eavlutionHide},'item-value']">
            <div class="value-c">
              <p>
                <span class="t-label">{{ $langue('Mooc_TaskDetail_HomeWork_Submitter', { defaultText: '提交人' }) }}：</span>
                <span class="t-value">{{info.staff_name}}</span>
              </p>
              <p style="padding-left:40px;">
                <span class="t-label">{{ $langue('Mooc_TaskDetail_HomeWork_SubmitTime', { defaultText: '提交时间' }) }}：</span>
                <span class="t-value">{{info.submit_time}}</span>
              </p>
            </div>

          </div>
        </div>
        <div class="preview-content">
          <!-- 附件内容 -->
          <div class="appendix">
            <appendix size="mini" :isPreview="true" :fileData="info.homework_attachments"></appendix>
          </div>
          <!-- 文本内容 -->
          <div class="p-cur">
            <sdc-mce-preview 
            ref="editor" 
            :urlConfig="editorConfig.urlConfig" 
            :catalogue.sync="editorConfig.catalogue" 
            :content="info.content"
            >
            </sdc-mce-preview>
          </div>
        </div>
        <div class="my-work-footer">
          <div class="footer-content-work">
            <el-button size="small" @click="handlepart('p')" v-if="disabledPart">上一份作业</el-button>
            <el-button size="small" @click="handlepart('n')" v-if="disabledNext">下一份作业</el-button>
          </div>
        </div>
      </div>
      <!-- 互评弹窗 -->
      <el-dialog v-if="dialogVisible" class="explanation-dialog" :visible.sync="dialogVisible" width="664px">
        <span slot="title"> {{!myMark?.created_at ? $langue('Mooc_TaskDetail_HomeWork_InputEvalute', { defaultText: '填写互评' }) : $langue('Mooc_TaskDetail_HomeWork_EditEvalute', { defaultText: '修改评价' }) }} <span class="name" v-if="anonymous">{{ $langue('Mooc_TaskDetail_HomeWork_Anonymous', { defaultText: '匿名' }) }}</span> </span>
        <el-form ref="formExplanation" :model="form" :rules="rules" label-width="80px">
          <el-form-item :label="$langue('Mooc_TaskDetail_HomeWork_Score', { defaultText: '评分' })" prop="score">
            <div class="score-form-item">
              <el-input-number style="width:100px;" size="small" v-model="form.score" controls-position="right" :min="0" @keydown.enter.native="scoreSubmit($event)"></el-input-number>
              <div class="tips"><i class="el-icon-warning-outline"></i> 总分{{workInfo.total_score}}，及格分{{workInfo.pass_score}}；请输入不高于总分的整数</div>
            </div>
          </el-form-item>
          <el-form-item :label="$langue('Mooc_TaskDetail_HomeWork_HomeworkComment', { defaultText: '评语' })">
            <el-input type="textarea" :placeholder="$langue('Mooc_TaskDetail_HomeWork_InputContent', { defaultText: '请输入内容' })" :autosize="{ minRows: 6}" v-model="form.content" maxlength="700" show-word-limit></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitComments('formExplanation')">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import appendix from '@/views/components/appendix'
import workList from '@/views/components/workList'
import {
  studentReviewHomework,
  getHomeworkRecord,
  workListHome,
  getStudentMark,
  saveAndEditMark
} from 'config/api.conf'

export default {
  props: {
    workInfo: {
      type: Object,
      default: () => {}
    },
    eavlutionHide: {
      type: Boolean,
      default: false
    },
    anonymous: {
      type: Boolean,
      default: false
    }
  },
  components: {
    appendix,
    workList
  },
  data() {
    const scoreValid = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$langue('Mooc_TaskDetail_HomeWork_InputScore', { defaultText: '请输入分数' })))
      } else {
        if (value > this.workInfo.total_score) {
          callback(new Error(this.$langue('Mooc_TaskDetail_HomeWork_InputLessTotalScore', { defaultText: '请输入小于总分的分数' })))
        }
        callback()
      }
    }
    return {
      pindexId: 0,
      dialogVisible: false,
      workListLoading: false,
      total: 0,
      form: {
        score: 1,
        content: ''
      },
      mceContent: '',
      input: '',
      studentList: [],
      curStudent: {},
      info: {},
      rules: {
        score: [
          // { required: true, message: '请输入分数', trigger: 'blur' }
          { required: true, validator: scoreValid, trigger: 'blur' }
        ]
      },
      serchValue: '',
      myMark: {},
      editorConfig: {
        catalogue: false,
        options: {
          // 非必传字段
          selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
                    formatselect fontsizeselect lineheight |
                    bold italic underline strikethrough |
                    forecolor backcolor |
                    dent align bullist numlist |
                    insert codesample |
                    fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      }
    }
  },
  computed: {
    disabledPart() {
      return this.activeStudent !== this.studentList[0]?.staff_id && this.studentList.length > 1
    },
    disabledNext() {
      return (this.activeStudent !== this.studentList[this.studentList.length - 1]?.staff_id) && this.studentList.length > 1
    },
    activeStudent() {
      return this.curStudent?.staff_id || 0
    }
  },
  created() {
    this.getStudentList()
  },
  methods: {
    scoreSubmit(e) {
      e.preventDefault()
      console.log('提交了')
    },
    // 获取评价
    getMarkRecord() {
      let { homework_id } = this.$route.query
      let params = {
        homework_id,
        record_id: this.curStudent?.record_id || '',
        markType: '2'
      }
      getStudentMark(params).then((res) => {
        this.myMark = res
        this.form = {
          score: res?.score || '',
          content: res?.content || ''
        }
      })
    },
    // 获取学员列表
    getStudentList(form, type) {
      let { homework_id, act_id } = this.$route.query
      let params = {
        act_id,
        homework_id,
        staff_name: form?.staff_name || '',
        page_no: form?.page_no || 1,
        page_size: 20
      }
      // 作业列表--作业互评
      let commAPI = this.eavlutionHide ? workListHome(params) : studentReviewHomework(params)
      commAPI.then((res) => {
        this.workListLoading = false
        this.total = res.total
        this.studentList = type === 'load' ? this.studentList.concat(res.records) : res.records
        if (type !== 'load') {
          // 查询不到就默认第一条
          const index = this.studentList.findIndex((e) => e.staff_id === this.activeStudent)
          this.curStudent = index >= 0 ? this.studentList[index] : this.studentList[0]
          if (this.curStudent?.record_id) {
            this.getMarkRecord()
            this.getdetail()
            this.$refs['scoreDetails'].initUserScore({ record_id: this.curStudent.record_id, homework_id })
          }
        }
      }).catch(() => {
        this.workListLoading = false
      })
    },
    handleEvaluate(type) {
      // 修改评价
      this.dialogVisible = true
    },
    // 提交互评
    submitComments(formName) {
      let { homework_id, task_id, act_id } = this.$route.query
      let params = {
        id: this.myMark?.id || '', // this.editEavlution 编辑
        record_id: this.curStudent?.record_id || '',
        mark_type: '2', // 学生 2 老师 1
        content: this.form.content,
        score: this.form.score,
        homework_id,
        task_id,
        mark_role_id: 2,
        mark_role_name: '学员互评',
        mooc_course_id: act_id
      }
      this.$refs[formName].validate((valid) => {
        if (valid) {
          saveAndEditMark('user', params).then((res) => {
            this.$message.success(this.$langue('Mooc_TaskDetail_HomeWork_SubmitSuccessed', { defaultText: '提交成功' }))
            this.dialogVisible = false
            this.$emit('update:dialogVisible', false)
            this.getMarkRecord()
            this.$emit('getExplainInfo')
            this.getStudentList()
          })
        }
      })
    },
    handlepart(val) {
      let pindex = 0
      if (!this.studentList.length) return
      this.studentList.forEach((item, index) => {
        if (item.staff_id === this.activeStudent) {
          pindex = index + (val === 'n' ? 1 : -1)
        }
      })
      this.curStudent = this.studentList[pindex] // 当前学员
      this.getdetail()
      this.getMarkRecord()
    },
    // 获取当前学员详情
    getdetail() {
      let { homework_id, act_id } = this.$route.query
      let params = {
        act_id,
        record_id: this.curStudent?.record_id || '',
        homework_id
      }
      getHomeworkRecord(params).then((res) => {
        this.info = res
        this.info.homework_attachments = (res?.homework_attachments || []).map((e) => {
          return {
            ...e,
            content_type: e.attachment_type
          }
        })
      })
    },
    // 选中当前学员
    handleCurrentActive(val) {
      this.curStudent = val
      this.studentList.forEach((item) => {
        if (item.staff_id === val.staff_id) {
          this.getMarkRecord()
          this.getdetail()
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.eavlution-top {
  padding: 12px;
  background-color: #fef3e6ff;
  margin-bottom: 16px;
  border-radius: 3px;
  color: #ff7548ff;
  .num {
    color: #000;
  }
  .date {
    padding-left: 28px;
  }
}
.my-eavlution {
  position: sticky;
  top: 0px;
  z-index: 101;
  padding-top: 23px;
  background-color: #fff;
}
.eavlution-cur {
  border-top: 1px solid #eeeeeeff;
  min-height: 80vh;
  display: flex;
  .right {
    padding: 0 20px 23px;
    flex: 1;
    .explanation-item {
      color: #00000099;
      .item-label {
        display: flex;
        align-items: center;
        .line {
          display: inline-block;
          width: 4px;
          height: 16px;
          background: #0052d9ff;
          margin-right: 12px;
        }
        .name {
          padding: 3px 4px;
          background-color: #00000042;
          color: #ffffffff;
          border-radius: 2px;
          font-size: 12px;
        }
      }
      .item-value {
        padding-left: 16px;
        margin-top: 16px;
        color: #000000e6;
        .none {
          color: #00000099;
          padding-bottom: 28px;
          .none-t {
            padding-right: 20px;
          }
        }
        .eav-content {
          .num {
            display: inline-block;
            font-size: 12px;
            width: 40px;
            height: 20px;
            line-height: 20px;
            text-align: center;
            background-color: #fef3e6ff;
            border-radius: 2px;
            color: #ed7b2fff;
          }
          .date {
            color: #00000099;
            padding: 0 24px 0 12px;
          }
          .res {
            padding: 12px 0 32px 0;
            color: #000000e6;
            line-height: 22px;
            white-space: pre-wrap;
          }
        }
        .value-c {
          display: flex;
          .t-label {
            color: #00000099;
          }
        }
      }
      .tab-work-list-vale {
        padding-left: 0px;
      }
    }
  }
  .preview-content {
    .p-cur {
      padding-top: 32px;
    }
  }
}
.score-form-item {
  display: flex;
  .tips {
    flex: 1;
    background-color: #fcf6ed;
    color: #ff7548;
    padding: 0 8px;
    border-radius: 3px;
    margin-left: 12px;
  }
}
/deep/.explanation-dialog {
  .name {
    margin-left: 8px;
    padding: 3px 4px;
    background-color: #00000042;
    color: #ffffffff;
    border-radius: 2px;
    font-weight: normal;
    font-size: 12px;
  }
}
.my-work-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 70px;
  line-height: 70px;
  background-color: #fff;
  z-index: 99;
  .footer-content-work {
    margin: auto;
    text-align: right;
  }
  @media screen and (max-width: 1660px) {
    .footer-content-work { 
      width: 1158px;
    }
  }
  @media screen and (min-width: 1661px) {
    .footer-content-work { 
      width: 1440px;
    }
  }
  .el-button {
    margin: 0 20px 0 0;
    width: 104px;
  }
}
</style>
