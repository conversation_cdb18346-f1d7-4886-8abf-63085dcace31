import http from 'utils/http'
import env from 'config/env.conf.js'

const envName = env[process.env.NODE_ENV]

// 获取运营分级/分级项目信息
export const operationInfo = (params) => http.get(`${envName.trainingPath}api/businessCommon/manage/dict/get-child-dict-items`, { params })
// 发布活动
export const saveActivityBaseInfo = (params) => http.post(`${envName.trainingPath}api/activity/manage/activity/publish`, { params, loading: true })
// 获取活动详情
export const getActivityDetail = (params) => http.get(`${envName.trainingPath}api/activity/manage/activity/get-activity-info`, { params, loading: true })
// 检测活动是否存在
export const checkActivityExist = (params) => http.get(`${envName.trainingPath}api/activity/manage/activity/check-activity-name`, { params })
// 获取活动活动分享设置
export const getShareSetting = (params) => http.get(`${envName.trainingPath}api/common/user/share/get-share-setting`, { params })
// 保存活动分享设置
export const saveShareSetting = (params) => http.post(`${envName.trainingPath}api/common/user/share/activity/save-share-setting`, { params, loading: true })
// 获取专区详情
export const getSpecialDetail = (page_id) => http.get(`${envName.commonPath}training-portal-area/api/area/user/page/detail/${page_id}?app_id=A9BiosXihR0h46ThNsAX`, { loading: false })
// 获取专区分类
export const getSpecialCategorys = (page_id) => http.get(`${envName.commonPath}training-portal-content/api/v1/portal/manage/simple/categorys?app_id=A9BiosXihR0h46ThNsAX&page_id=${page_id}`, { loading: false })
// 获取活动列表
export const getActivityList = (params) => http.get(`${envName.trainingPath}api/activity/manage/activity/get-activity-list`, { params, loading: true })
// 获取活动状态数量
export const getActivityCountApi = () => http.get(`${envName.trainingPath}api/activity/manage/activity/get-activity-count`, { loading: false })
// 活动上架
export const activityShelfApi = (activity_id) => http.post(`${envName.trainingPath}api/activity/manage/activity/available/${activity_id}`, { loading: true })
// 活动下架
export const activityOffShelfApi = (activity_id) => http.post(`${envName.trainingPath}api/activity/manage/activity/removed/${activity_id}`, { loading: true })
// 删除活动
export const activityDeleteApi = (activity_id) => http.post(`${envName.trainingPath}api/activity/manage/activity/delete/${activity_id}`, { loading: true })
// 保存草稿
export const activitySaveDraftApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/activity/draft`, { params })
// 获取关联会议室Rooms列表
export const getRoomsListApi = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/meeting/get-meeting-rooms`, { params, loading: true })
// 校验腾讯会议code
export const checkMeetingCodeApi = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/meeting/meeting-info`, { params, loading: true })
// 活动-学员列表
export const getActivityStudentListApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/members/get-activity-members`, { params, loading: true })
// 活动-添加学员
export const addActivityStudentApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/members/add-members-by-namelist`, { params, loading: true })
// 活动-学员状态修改
export const updateActivityStudentStatusApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/members/set-members-status`, { params, loading: true })
// 活动-是否接收问卷状态修改
export const updateActivityStudentQuestionnaireStatusApi = (params) => http.get(`${envName.trainingPath}api/activity/manage/members/is-receive-survey`, { params, loading: true })
// 活动-删除学员(批量)
export const deleteActivityStudentApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/members/remove-members`, { params, loading: true })
// 活动-导出全部学员
export const exportActivityStudentApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/members/export-members`, { params, loading: true })
// 活动-导出学员签到表
export const exportActivityStudentSignApi = (params) => http.get(`${envName.trainingPath}api/activity/manage/members/export-sign-list`, { params, loading: true })
// 活动-学员列表操作日志
export const getActivityStudentLogApi = (params) => http.get(`${envName.trainingPath}api/activity/manage/members/get-operation-log`, { params, loading: true })
// 活动-更新问卷管理的催办
export const updateQuestionRemindApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/setting/remind-setting`, { params, loading: true })
// 活动-取消日历邀请 
export const cancelCalendarInvitationApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/activity/cancel-appointment`, { params, loading: true })
// 活动-手动发送日程 
export const sendManualScheduleApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/activity/create-appointment`, { params, loading: true })
// 活动-发起群聊 
export const createGroupChatApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/activity/wechat-group-chat`, { params, loading: true })
// 活动-日程和企微群保存表单
export const seveScheduleApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/activity/schedule-and-wechat-group`, { params, loading: true })
// 活动-学员签到
export const activityStudentSignApi = (params) => http.get(`${envName.trainingPath}api/activity/user/save-student-sign`, { params, loading: true })
// 活动-手动修改评分
export const modifyScoreApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/activity/modify-score`, { params, loading: true })
// 活动-获取催办信息
export const getRemindInfoApi = (params) => http.get(`${envName.trainingPath}api/activity/manage/setting/get-remind-setting`, { params, loading: true })
// 活动-编辑-互动管理添加课程
export const addCourseApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/activity/save-relation-content`, { params, loading: true })
// 活动-编辑-互动管理删除课程
export const deleteCourseApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/activity/remove-relation-content`, { params, loading: true })
// 活动-编辑-互动管理获取课程列表
export const getCourseListApi = (params) => http.get(`${envName.trainingPath}api/activity/manage/activity/get-relation-content`, { params, loading: true })
// 活动-编辑-活动刷新评分
export const refreshScoreApi = (params) => http.get(`${envName.trainingPath}api/activity/manage/activity/refresh-score`, { params, loading: true })
// 活动-课前课后列表
export const getRelationContentsApi = (params) => http.get(`${envName.trainingPath}api/activity/user/get-relation-contents`, { params, loading: false })
// 活动-编辑-单个问卷刷新评分
export const getQuestionDataStatisticsApi = (params) => http.get(`${envName.trainingPath}api/activity/manage/activity/survery/report`, { params })
// 活动-编辑-问卷信息的汇总统计
export const getQuestionDataList = (params) => http.get(`${envName.trainingPath}api/activity/manage/activity/survery/report-list`, { params })
// 活动-编辑-获取活动关联的腾讯会议成员考勤信息
export const getMeetingAttendanceApi = (params) => http.get(`${envName.trainingPath}api/activity/manage/members/get-meeting-members-attendance`, { params, loading: true })
// 活动-编辑-保存会议成员考勤信息
export const saveMeetingAttendanceApi = (params) => http.get(`${envName.trainingPath}api/activity/manage/members/save-meeting-members-attendance`, { params, loading: true })
// 活动-编辑-创建问卷
export const createQuestionApi = (params) => http.post(`${envName.trainingPath}api/survey/manage/wenjuan/create`, { params, loading: true })
// 活动-编辑-活动绑定问卷
export const bindQuestionApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/activity/add-surveys`, { params, loading: true })
// 活动&班级审核列表
export const getActivityAuditListApi = (params) => http.post(`${envName.trainingPath}api/manage/trainingCheck/training-approve-list`, { params, loading: true })
// 活动&班级审核
export const activityAuditApi = (params) => http.get(`${envName.trainingPath}api/manage/trainingCheck/approve-apply`, { params, loading: true })
// 活动-编辑-会议录制详情
export const getMeetingRecordApi = (params) => http.get(`${envName.trainingPath}api/activity/manage/activity/get-meeting-record-info`, { params, loading: true })
// 活动-编辑-保存会议录制观看权限
export const saveMeetingRecordAuthApi = (params) => http.get(`${envName.trainingPath}api/activity/manage/activity/save-meeting-record-auth`, { params, loading: true })
// 活动-编辑-会议录制推送学员
export const pushMeetingRecordApi = (activity_id) => http.post(`${envName.trainingPath}api/activity/manage/activity/meeting-notify/all?activity_id=${activity_id}`, { loading: true })
// 活动-编辑-获取问卷反馈详情
export const getQuestionFeedbackApi = (survey_id) => http.get(`${envName.trainingPath}api/activity/manage/activity/feedback_detail?survey_id=${survey_id}`, { loading: true })
// 活动-编辑-结束活动问卷并发送反馈总结邮件
export const sendQuestionFeedbackApi = (params) => http.post(`${envName.trainingPath}api/activity/manage/activity/close_send_feedback`, { params, loading: true })

// 用户端 - 活动
// 活动-会议录制详情
export const getMeetingRecordUserApi = (params) => http.get(`${envName.trainingPath}api/activity/user/get-meeting-record-info`, { params, loading: true })
// 活动-修改问卷启动方式
export const saveSurveyStartTypeApi = (params) => http.get(`${envName.trainingPath}api/activity/manage/activity/save_survey_start_type`, { params, loading: true })
// 活动-启动问卷
export const startSurveyApi = (params) => http.get(`${envName.trainingPath}api/activity/manage/activity/start_survey`, { params, loading: true })
// 活动-空白页获取问卷信息
export const getSurveyInfoApi = (params) => http.get(`${envName.trainingPath}api/activity/user/get-survey`, { params, loading: true })
