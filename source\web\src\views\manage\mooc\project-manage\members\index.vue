<template>
  <div class="study-manage-page">
    <div class="header-top">
      <span class="title">学员管理</span>
    </div>
    <div class="content">
      <div class="table-top-btn">
        <AudienceSelector
          v-if="!projectStatus && !isApprove"
          audience
          btnText="添加学员"
          :showTab="['unitStaff', 'unit', 'group', 'import']"
          multiple
          v-model="target_list" 
          ref="selector"
          appCode="qlearning"
          :env="audienceEnv" 
          importNumber='1000'
          :isShowCount="true"
          class="add-study-btn"
          :createStudentID="true"
          @changeRule="handleStudentTarget"
          title="添加学员"
        />
        <el-tooltip :class="{'disabledStyle-add-btn': projectStatus || isApprove}" v-else :disabled="!projectStatus || !isApprove" effect="dark" content="项目已结束，无法操作" placement="top-start">
          <span><el-button :disabled="projectStatus || isApprove" size='small' type="primary">添加学员</el-button></span>
        </el-tooltip>
        <!-- <el-tooltip :disabled="!projectStatus" effect="dark" content="项目已结束，无法操作" placement="top-start">
          <span><el-button :disabled="projectStatus" @click="autoAddDialog=true" size='small'>自动加入</el-button></span>
        </el-tooltip> -->
        <el-tooltip :disabled="!autoJoinStatus.isDisabled" effect="dark" :content="autoJoinStatus.tipsContent" placement="top-start">
          <span><el-button :disabled="autoJoinStatus.isDisabled || isApprove" @click="autoAddDialog=true" size='small'>自动加入</el-button></span>
        </el-tooltip>
        <el-dropdown class="dropDown-btn" @command="handleBatchOperate">
          <el-button size='small'>批量操作<i class="el-icon-arrow-down el-icon--right"></i></el-button>
          <el-dropdown-menu slot="dropdown" >
            <el-tooltip :disabled="!projectStatus" effect="dark" content="项目已结束，无法操作" placement="top-start">
              <span><el-dropdown-item :disabled="projectStatus || isApprove" command="urge">催办</el-dropdown-item></span>
            </el-tooltip>
            <el-tooltip :disabled="!projectStatus" effect="dark" content="项目已结束，无法操作" placement="top-start">
              <span><el-dropdown-item :disabled="projectStatus || isApprove" command="del">移除</el-dropdown-item></span>
            </el-tooltip>
            <el-dropdown-item command="delay" :disabled="isApprove">延期</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
        <el-tooltip :disabled="!exportDisabled" effect="dark" placement="top" popper-class='export-popper-class'>
          <div slot="content">当前是系统使用高峰，暂不支持数据导出功能，有需要请登录<el-link type="primary" href='tbi.oa.com' :underline="false" target="_blank">tbi.oa.com</el-link>提单申请即可。</div>
          <span style="margin-right: 10px">
            <el-button @click="handleExport" size='small' :disabled="exportDisabled || isApprove">导出</el-button>
          </span>
        </el-tooltip>
        <el-button @click="outRecordDialog=true" size='small' :disabled="isApprove">退出记录</el-button>
      </div>
      <el-form class="form-box" ref="form" :model="form" inline>
        <el-form-item label="姓名">
          <el-input v-model="form.staff_name" placeholder="请输入学员姓名" clearable></el-input>
        </el-form-item>
        <el-form-item label="培训状态">
          <el-select v-model="form.status" placeholder="请选择培训状态" clearable>
            <el-option
              v-for="item in trainOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="加入方式">
          <el-select v-model="form.join_type" placeholder="请选择加入方式" clearable>
            <el-option
              v-for="item in addOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="加入时间">
          <el-date-picker
            v-model="addTime"
            size="small"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00','23:59:59']"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="学习结束时间" v-if="projectManageInfo.course_period_type === 2">
          <el-date-picker
            v-model="addTime"
            size="small"
            type="datetimerange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00','23:59:59']"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="所属BG"  class="member-dep-selector" >
          <sdc-unit-selector
          ref="deptSelectorRef" 
          v-model="form.dept_name"
          :props="deptProps" 
          :includeUnitSortIDs=[6]
          @change="changeDept"
          placeholder="请选择所属BG"
          />
        </el-form-item>
        <el-form-item>
          <el-button size="small" @click="handleReset">重置</el-button>
          <el-button size="small" type="primary" @click="onSearch(1)">查询</el-button>
        </el-form-item>
      </el-form>
      <div class="main-table-box">
        <el-table
          :data="tableData.records"
          style="width: 100%"
          header-row-class-name="table-header-style"
          row-class-name="table-row-style"
          @selection-change="handleSelectionChange"
        >
        <el-table-column
          type="selection"
          width="55">
        </el-table-column>
          <el-table-column prop="staff_name" label="员工姓名" show-overflow-tooltip></el-table-column>
          <el-table-column prop="dept_name" label="所属组织" show-overflow-tooltip></el-table-column>
          <el-table-column label="应学进度" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.finished_count + '/' + scope.row.task_count }}</span>
            </template>
          </el-table-column>
          <el-table-column label="培训状态" width="100">
            <template slot-scope="scope">
              <span>{{ status_info[scope.row.status]  }}</span>
              <el-tooltip v-if="scope.row.delayed" effect="dark" :content="delayedTime(scope.row.course_end_time)" placement="top-start">
                <span class="status-delay">延期</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column label="加入方式" width="100">
            <template slot-scope="scope">
              <span>{{ join_type_info[scope.row.join_type]  }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="reg_time" label="加入时间" width="200"></el-table-column>
          <el-table-column prop="reg_time" label="学习结束时间" width="200" v-if="projectManageInfo.course_period_type === 2">
            <template slot="header">
              <span>学习结束时间</span>
              <el-tooltip effect="dark" content="学习结束时间=加入时间+学习周期（延期学习例外）" placement="top">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="remind_count" label="催办次数" width="80"></el-table-column>
          <el-table-column label="操作" width="250" fixed="right">
            <template slot-scope="scope">
              <div class="operat-btn-box">
                <el-link class="link-btn" @click="handleDetail(scope.row)" type="primary" :underline="false">查看详情</el-link>
                <el-tooltip class="link-btn" v-if="scope.row.status !== 1 && scope.row.show_remind_btn" :disabled="!projectStatus" effect="dark" content="项目已结束，无法操作" placement="top-start">
                  <span><el-link :disabled="projectStatus || isApprove"  @click="handleUrge(scope.row)" type="primary" :underline="false">催办</el-link></span>
                </el-tooltip>
                <el-link class="link-btn" :disabled="isApprove" v-if="scope.row.status === 3" @click="handleDelay(scope.row)" type="primary" :underline="false">延期学习</el-link>
                <el-tooltip class="link-btn" :disabled="!projectStatus" effect="dark" content="项目已结束，无法操作" placement="top-start">
                  <span><el-link :disabled="projectStatus || isApprove" @click="handleDelete(scope.row)" type="danger" :underline="false">移除</el-link></span>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="tableData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="current"
          :page-sizes="[5, 10, 20, 30, 50, 100]"
          :page-size="size"
          layout="total,  prev, pager, next, sizes, jumper"
          :total="tableData.total"
          >
          </el-pagination>
      </div>

    </div>
    <!-- 自动加入 -->
    <autoAdd v-if="autoAddDialog" :visible.sync="autoAddDialog"></autoAdd>
    <!-- 退出记录 -->
    <outRecord v-if="outRecordDialog" :visible.sync="outRecordDialog"></outRecord>
    <!-- 延期学习 -->
    <delayStudy 
    v-if="delayStudyDialog" 
    :visible.sync="delayStudyDialog"
    ref="delayStudyRef"
    @onSearch="onSearch"
    />
    <!-- 批量催办 -->
    <batchUrge
    v-if="batchUrgeDialog"
    :visible.sync="batchUrgeDialog"
    ref="batchUrgeRef"
    @onSearch="onSearch"
    :isTipsInfo="isTipsInfo"
    @close="() => batchUrgeDialog = false"
    />
    <!-- 查看详情 -->
    <detail v-if="isShowDetailsPopup" :isShowDetailsPopup.sync="isShowDetailsPopup" :details-info="currentDetails"></detail>
    <!-- 添加学员人数大于300提示弹窗 -->
    <el-dialog
      :visible.sync="addStudentConfirm"
      custom-class="none-border-dialog batchUrge-dialog"
      :close-on-click-modal="false"
      width="430px"
      class="add-student"
      >
      <div slot="title">添加学员确认</div>
      <div class="confirm">
        <div class="tips1">本次操作将新增<span style="color: red">{{ currentAddInfo.count || 0 }}</span>位学员。由于新增学员数量较大，请进行二次确认操作，如需推送加入项目的学习提醒，请联系超级管理员graywu</div>
        <div class="tips2">请在下面的输入框中输入本次操作<span style="color: red">新增的学员数量</span></div>
        <div class="student-count">
          <el-input-number v-model="studentCount" ref="stu" @input.native="studentCountChange" @change="studentCountChange($event, 'change')"></el-input-number>
          <div style="color:red;margin-left: 20px" v-if="isShowStudentTips">请输入正确的数量</div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="addStudentConfirm = false" size="small">取 消</el-button>
        <el-button type="primary" @click="addConfirm" :disabled="isShowStudentTips" size="small">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :visible.sync="pushConfirm"
      custom-class="none-border-dialog batchUrge-dialog"
      width="430px"
      class="add-student"
      >
      <div slot="title">批量催办提醒</div>
      <div class="confirm">
        <div class="tips1">本次操作已选中<span style="color: red">{{ currentAddInfo.count || 0 }}</span>位学员，数量较大无法批量催办，请减少单次批量催办的人数，如需大批量催办或配置自动催办，请联系超级管理员graywu</div>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="pushConfirm = false" size="small">取 消</el-button>
        <el-button type="primary" @click="pushConfirm = false" size="small">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import pager from '@/mixins/pager'
import { studyManageList, batchDelete, handAddStudent } from '@/config/mooc.api.conf.js'
import { isSysBusy } from '@/config/api.conf'
import { AudienceSelector } from '@tencent/sdc-audience'
import autoAdd from './child/autoAdd.vue'
import outRecord from './child/outRecord.vue'
import delayStudy from './child/delayStudy.vue'
import batchUrge from './child/batchUrge.vue'
import detail from '.././report/component/student-details-popup.vue'
import axios from 'axios'
import env from 'config/env.conf.js'
import { mapState } from 'vuex'
const status_info = {
  null: '未开始',
  0: '进行中',
  1: '已完成',
  2: '已注销',
  3: '已逾期'
}
const join_type_info = {
  1: '手动添加',
  2: '自动加入',
  3: '自主报名'
}
export default {
  mixins: [pager],
  components: {
    AudienceSelector,
    autoAdd,
    outRecord,
    delayStudy,
    batchUrge,
    detail
  },
  data() {
    return {
      audienceEnv: process.env.NODE_ENV,
      form: {
        staff_name: '',
        status: '',
        join_type: '',
        dept_name: ''
      },
      addTime: [],
      target_list: '',
      autoAddDialog: false,
      outRecordDialog: false,
      delayStudyDialog: false,
      batchUrgeDialog: false,
      isShowDetailsPopup: false,
      isTipsInfo: {},
      currentDetails: null,
      staffId: [],
      trainOptions: [
        { label: '全部', value: '' },
        { label: '未开始', value: 1 },
        { label: '进行中', value: 2 },
        { label: '已逾期', value: 3 },
        { label: '已完成', value: 4 }
      ],
      addOptions: [
        { label: '手动添加', value: 1 },
        { label: '自动加入', value: 2 },
        { label: '自主报名', value: 3 }
      ],
      tableData: {
        records: [],
        total: 0
      },
      multipleList: [],
      detailInfo: {},
      status_info,
      join_type_info,
      deptProps: {
        UnitFullName: 'dept_name',
        unitID: 'dept_id'
      },
      exportDisabled: false,
      addStudentConfirm: false,
      isShowStudentTips: false,
      handleStudentInfo: '', // 添加学员结果
      currentAddInfo: '', // 本次新增学员信息
      studentCount: 0,
      pushConfirm: false
    }
  },
  computed: {
    ...mapState(['projectManageInfo', 'userLimitInfo']),
    projectStatus() {
      return this.projectManageInfo.course_status === 2
    },
    isApprove() {
      // 审批-审批中
      return this.$route.query.approve === '1' || this.projectManageInfo.course_status === 6
    },
    delayedTime() {
      return (val) => {
        return `结束时间：${val}` 
      }
    },
    autoJoinStatus() {
      if (!this.userLimitInfo.supper_admin) {
        return {
          tipsContent: '此功能仅支持超级管理员使用，如需开启，请联系graywu',
          isDisabled: true
        }
      }

      if (this.projectManageInfo.course_status === 2) {
        return {
          tipsContent: '项目已结束，无法操作',
          isDisabled: true
        }
      }
      
      return {
        tipsContent: '',
        isDisabled: false
      }
    }
  },
  watch: {
    addStudentConfirm(val) {
      if (!val) {
        this.studentCount = 0
      }
    }
  },
  mounted() {
    this.onSearch()
    this.getSysBusyStatus()
  },
  methods: {
    studentCountChange(val, type) {
      if (type) {
        this.studentCount = val
      } else {
        this.studentCount = this.$refs['stu'].displayValue
      }
      // this.currentAddInfo.count
      if ((this.studentCount * 1) !== this.currentAddInfo.count) {
        this.isShowStudentTips = true
      } else {
        this.isShowStudentTips = false
      }
    },
    onSearch(page_no = 1) {
      const { mooc_course_id } = this.$route.query
      const { staff_name, status, join_type, dept_name } = this.form
      const join_start_time = this.addTime?.length ? this.addTime[0] : ''
      const join_end_time = this.addTime?.length ? this.addTime[1] : ''
      const params = {
        mooc_course_id,
        staff_name,
        status,
        join_type,
        join_start_time,
        join_end_time,
        page_no,
        page_size: this.size,
        dept_name
      }
      studyManageList(params).then((res) => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    getSysBusyStatus() {
      isSysBusy().then(res => {
        if (res === '1') {
          this.exportDisabled = true
        } else {
          this.exportDisabled = false
        }
      })
    },
    // 导出
    handleExport() {
      const dataLength = this.multipleList?.length ? this.multipleList.length : this.tableData.total
      this.$messageBox.confirm(`您将导出${dataLength}条数据`, '确定导出学员列表？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        const { mooc_course_id } = this.$route.query
        const { staff_name, status, join_type, dept_name } = this.form
        const join_start_time = this.addTime?.length ? this.addTime[0] : ''
        const join_end_time = this.addTime?.length ? this.addTime[1] : ''
        let staff_ids = ''
        if (this.multipleList.length) { // 勾选学员
          this.multipleList.forEach((e) => {
            staff_ids += `${e.staff_id};` 
          })
        }
        staff_ids = staff_ids.slice(0, -1)
        const params = {
          mooc_course_id,
          staff_name,
          status,
          join_type,
          join_start_time,
          join_end_time,
          staff_ids,
          dept_name
        }
        let url = `${env[process.env.NODE_ENV].trainingPath}api/mooc/manage/student/export-student-list?page_no=${this.current}&page_size=${this.size}`
        for (let pre in params) {
          url += `&${pre}=${params[pre]}`
        }
        axios({
          url,
          method: 'get',
          responseType: 'blob'
        }).then((response) => {
          if (response.status === 200 && response.data) {
            const url = window.URL.createObjectURL(new Blob([response.data]))
            const link = document.createElement('a')
            link.href = url
            link.setAttribute('download', '学员列表.xlsx')
            document.body.appendChild(link)
            link.click()
            this.$message.success('导出成功')          
          } else {
            this.$message.error('导出失败')
          }
        })
      })
    },
    // 批量操作
    handleBatchOperate(val) {
      if (!this.multipleList?.length) {
        this.$message.warning('请至少选择一条数据进行操作')
        return
      }
      // 数量大于300 且不是超级管理员
      if (this.multipleList?.length > 300 && !this.userLimitInfo.supper_admin) {
        this.pushConfirm = true
        return
      }
      if (val === 'urge') { // 批量催办
        this.batchUrgeDialog = true
        this.isTipsInfo = {
          isTips: 'batch'
        }
        this.$nextTick(() => {
          this.$refs.batchUrgeRef.initData(this.multipleList)
        })
      } else if (val === 'del') {
        const list = this.multipleList.map((e) => e.staff_id)
        this.$messageBox.confirm(`已选中${list.length}位学员，移除后学员无法继续学习，确定移除吗？`, '批量移除学员', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          const { mooc_course_id } = this.$route.query
          batchDelete({ mooc_course_id, staff_ids: list }).then((res) => {
            this.$message.success('移除成功')
            this.onSearch()
          })
        })
      } else {
        this.delayStudyDialog = true
        this.$nextTick(() => {
          this.$refs.delayStudyRef.initData(this.multipleList)
        })
      }
    },
    // 查看详情
    handleDetail(row) {
      this.currentDetails = row
      this.isShowDetailsPopup = true
    },
    // 单行延期
    handleDelay(row) {
      this.delayStudyDialog = true
      this.$nextTick(() => {
        this.$refs.delayStudyRef.initData([row])
      })
    },
    // 单行催办
    handleUrge(row) {
      this.isTipsInfo = {
        isTips: 'single'
      }
      this.batchUrgeDialog = true
      this.$nextTick(() => {
        this.$refs.batchUrgeRef.initData([row])
      })
    },
    // 单行移除
    handleDelete(row) {
      this.$messageBox.confirm(`移除后学员${row.staff_name}无法继续学习，确定移除吗？`, '移除学员', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        const { mooc_course_id } = this.$route.query
        batchDelete({ mooc_course_id, staff_ids: [row.staff_id] }).then((res) => {
          this.$message.success('移除成功')
          this.onSearch()
        })
      })
    },
    // 添加目标学员
    handleStudentTarget(val) {
      const { mooc_course_id } = this.$route.query
      this.currentAddInfo = val
      if (val.count > 300) {
        this.addStudentConfirm = true
        return
      }
      this.handAddStudent({ mooc_course_id, val })
    },
    // 超过300学员数量的确认框
    addConfirm() {
      this.addStudentConfirm = false
      this.handAddStudent({ mooc_course_id: this.$route.query.mooc_course_id, val: this.currentAddInfo, isShowbatchUrgeDialog: false })
    },
    handAddStudent(options) {
      this.target_list = ''
      this.isTipsInfo = {
        isTips: 'target'
      }
      const { mooc_course_id, val, isShowbatchUrgeDialog = true } = options
      handAddStudent({
        mooc_course_id,
        target: val.rule
      }).then((res) => {
        isShowbatchUrgeDialog ? this.batchUrgeDialog = true : this.batchUrgeDialog = false
        this.$nextTick(() => {
          this.$refs.batchUrgeRef.initData(res)
        })
        this.handleStudentInfo = res
        this.$message.success('手动添加学员成功')
        this.onSearch(1)
        this.$store.dispatch('getProjectInfoData', mooc_course_id)
      })
    },
    handleSelectionChange(val) {
      this.multipleList = val
    },
    // 所属BG
    changeDept(val) {
      this.form.dept_name = val?.UnitFullName || ''
    },
    handleReset() {
      this.$refs.deptSelectorRef.clearSelected()
      this.form = {
        staff_name: '',
        status: '',
        join_type: '',
        dept_name: ''
      }
      this.size = 10
      this.current = 1
      this.addTime = []
      this.onSearch(1)
    }
  }
}
</script>
<style lang='less'>
.export-popper-class {
  max-width: unset;
}
.table-top-btn {
  .disabledStyle-add-btn {
    margin-right: 10px;
  }
  .sdc-selector {
    height: 32px;
    margin-right: 10px;
    .el-button {
      height: 32px;
      width: 98px;
    }
    .el-button:focus,
    .el-button:hover {
      background: rgb(51, 117, 225);
      border-color: rgb(51, 117, 225);
      color: #FFF
    }
    .el-button--default {
      color: #FFF;
      background-color: #0052D9;
      border-color: #0052D9
    }
    .el-button--default.is-active {
      background: rgb(0, 74, 195);
      border-color: rgb(0, 74, 195);
      color: #FFF
    }
    .sdc-svg-icon {
      display: none;
    }
  }
  .modal-body-audience {
    .el-input {
      .el-input__inner {
        padding-left: 30px;
        padding-right: 30px;
      }
    }
  }
}
.member-dep-selector {
  .sdc-selector {
    width: 250px;
  }
}
</style>
<style lang="less" scoped>
.add-student {
  .tips1 {
    font-size: 14px;
    line-height: 22px;
  }
  .tips2 {
    color: black;
    margin: 25px 0 0 0;
  }
  .student-count {
    margin: 20px 0 0 0;
    display: flex;
    align-items: center;
    :deep(.el-input) {
      height: initial;
    }
    :deep(.el-input-number) {
      .el-input__inner {
        line-height: 40px;
        height: auto;
        padding: initial;
      }
    }
  }
}
.study-manage-page {
  background-color: #fff;
  height: 100%;
  .header-top {
    // height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(243, 243, 243, 1);
    padding: 16px 20px;
    .title {
      font-size: 16px;
      height: 24px;
      line-height: 24px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.9)
    }
  }
  .content {
    padding: 20px 20px 60px;
    .table-top-btn {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      .dropDown-btn {
        margin-left: 10px;
        margin-right: 10px;
      }
    }
    .form-box {
      background: #f9f9f9ff;
      padding: 16px 16px 0;
      margin-bottom: 16px;
      .el-form-item {
        margin-bottom: 16px;
        margin-right: 54px;
      }
      .el-button {
        width: 60px;
      }
    }
    .main-table-box {
      .el-icon-warning-outline {
        margin-left: 5px;
      }
      .status-delay {
        background: #EBEFFC;
        color: #0052D9;
        border-radius: 2px;
        display: inline-block;
        width: 40px;
        height: 20px;
        margin-left: 10px;
        text-align: center;
        line-height: 20px;
      }
      .operat-btn-box {
        .link-btn {
          margin-right: 10px;
        }
      }
    }
  }
}
:deep(.el-link.el-link--danger) {
  .el-link--inner {
    color: #E34D59;
  }
}
// :deep(.selector-modal .modal-buttons ) {
//   .el-button:nth-child(2) {
//     display: none;
//   }
// }
:deep(.sdc-selector .num) {
  display: none;
}
</style>
