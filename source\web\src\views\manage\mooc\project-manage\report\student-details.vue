<template>
  <div class="student-details">
    <el-row>
      <div class="summary">
        <div class="summary-item" v-for="(item, index) in summaryList" :key="index">
          <span class="value">{{ item.value }}</span>
          <span class="label">{{ item.label }}</span>
        </div>
      </div>
    </el-row>
    <el-form ref="form" :model="searchForm" inline>
      <el-row>
        <el-form-item label="姓名">
          <el-input v-model="searchForm.name" size="small" placeholder="请输入学员姓名" suffix-icon="el-icon-search" @change="onSearch()"></el-input>
        </el-form-item>
        <el-form-item label="">
          <div style="display: flex;">
            <el-tooltip :disabled="!exportDisabled" effect="dark" content="当前是系统使用高峰，暂不支持数据导出功能，有需要请联系graywu" placement="top">
              <span style="margin-right: 10px">
                <el-button @click="exportEvent" size='small' :disabled="exportDisabled || isApprove">导出学员列表</el-button>
              </span>
            </el-tooltip>
            <el-tooltip :disabled="!exportDisabled" effect="dark" content="当前是系统使用高峰，暂不支持数据导出功能，有需要请联系graywu" placement="top">
              <span style="margin-right: 10px">
                <el-button @click="exportEvent('detail')" size='small' :disabled="exportDisabled || isApprove">导出培训明细 </el-button>
              </span>
            </el-tooltip>
            <el-button size="small" class="filter-btn" @click="handleAdvancedFiltering">
              <div class="filter-box">
                <img src="@/assets/mooc-img/dressing-by-screening.png" alt="" srcset="" class="icon" />
                <span>高级筛选</span>
              </div>
            </el-button>
          </div>
        </el-form-item>
      </el-row>

      <el-row class="advanced-filter" v-show="advancedFiltering">
        <el-form-item label="培训状态">
          <el-select class="self-inner" v-model="searchForm.status" placeholder="请选择培训状态" size="small">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="加入方式">
          <el-select class="self-inner" v-model="searchForm.joinType" placeholder="请选择加入方式" size="small">
            <el-option
              v-for="item in joinOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="加入时间" class="input-style">
          <el-date-picker
            class="self-inner"
            v-model="searchForm.joinTime"
            size="small"
            type="daterange"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="完成时间" class="input-style">
          <el-date-picker
            class="self-inner"
            v-model="searchForm.finishedTime"
            size="small"
            type="daterange"
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="~"
            start-placeholder="开始日期"
            end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="所属BG"  class="detail-dep-selector" >
          <sdc-unit-selector
          ref="deptSelectorRef" 
          v-model="searchForm.dept_name"
          :props="deptProps" 
          :includeUnitSortIDs=[6]
          @change="changeDept"
          placeholder="请选择所属BG"
          />
        </el-form-item>
        <el-form-item label="学习时长" class="detail-study-input">
          <el-input v-model="searchForm.start_study_time" placeholder="请输入最小值"></el-input>
          <span class="line">-</span>
          <el-input v-model="searchForm.end_study_time" placeholder="请输入最大值"></el-input>
        </el-form-item>
        <el-form-item label=" " label-width="0">
          <el-button type="primary" size="small" @click="onSearch()">搜索</el-button>
          <el-button size="small" @click="resetEvent">重置</el-button>
        </el-form-item>
      </el-row>
    </el-form>
    <el-table :data="tableData.records" header-row-class-name="table-header-style">
      <el-table-column prop="staff_name" label="学员姓名"></el-table-column>
      <el-table-column prop="dept_name" label="所属组织" min-width="260">
        <template slot-scope="scope">
          {{ scope.row.dept_name || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="status" label="培训状态">
        <template slot-scope="scope">
          {{ cultivateStatus[scope.row.status] }}
        </template>
      </el-table-column>
      <el-table-column prop="name" label="应学进度">
        <template slot-scope="scope">
          {{ scope.row.required_count }} / {{ scope.row.required_total }}
        </template>
      </el-table-column>
      <el-table-column label="选学进度">
        <template slot-scope="scope">
          {{ scope.row.optional_count }} / {{ scope.row.optional_total }}
        </template>
      </el-table-column>
      <el-table-column label="总进度">
        <template slot-scope="scope">
          {{ scope.row.finished_count }} / {{ scope.row.task_count }}
        </template>
      </el-table-column>
      <el-table-column prop="certificate_count" label="证书奖励">
        <template slot-scope="scope">
          {{ scope.row.certificate_count || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="total_study_time" label="总学习时长（分钟）" min-width="160"></el-table-column>
      <el-table-column prop="study_start_time" label="开始培训时间" min-width="160">
        <template slot-scope="scope">
          {{ scope.row.study_start_time || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="finished_time" label="完成培训时间" min-width="160">
        <template slot-scope="scope">
          {{ scope.row.finished_time || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="last_study_time" label="最近学习时间" min-width="160">
        <template slot-scope="scope">
          {{ scope.row.last_study_time || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="join_type" label="加入方式">
        <template slot-scope="scope">
          {{ ['手动添加', '自动加入', '自主报名'][scope.row.join_type - 1] }}
        </template>
      </el-table-column>
      <el-table-column prop="reg_time" label="加入时间" min-width="160"></el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="lookDetails(scope.row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="current"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="size"
      layout="total,  prev, pager, next, sizes, jumper"
      :total="tableData.total">
    </el-pagination>

    <!-- 详情弹窗 -->
    <student-details-popup 
      v-if="isShowDetailsPopup"
      :isShowDetailsPopup.sync="isShowDetailsPopup"
      :details-info="currentDetails">
    </student-details-popup>
  </div>
</template>

<script>
import StudentDetailsPopup from './component/student-details-popup.vue'
import pagination from '@/mixins/pager'
import { getStudentDetailListAPI, getStudentDetailListExportAPI, getStudentStatisticsAPI, studentDetailExportAPI } from '@/config/mooc.api.conf.js'
import { isSysBusy } from '@/config/api.conf'
import { mapState } from 'vuex'
const cultivateStatus = {
  null: '未开始',
  0: '进行中',
  1: '已完成',
  2: '已注销',
  3: '已逾期'
}
export default {
  components: {
    StudentDetailsPopup
  },
  mixins: [pagination],
  data () {
    return {
      summaryList: [
        { label: '学员总数', value: 0 },
        { label: '已完成', value: 0 },
        { label: '进行中', value: 0 },
        { label: '未开始', value: 0 },
        { label: '已逾期', value: 0 }
      ],
      searchForm: {
        name: '',
        status: '',
        joinType: '',
        joinTime: [],
        finishedTime: [],
        dept_name: '',
        start_study_time: '',
        end_study_time: ''
      },
      statusOptions: [
        { label: '全部', value: '' },
        { label: '未开始', value: 1 },
        { label: '进行中', value: 2 },
        { label: '逾期', value: 3 },
        { label: '已完成', value: 4 }
      ],
      joinOptions: [
        { label: '全部', value: '' },
        { label: '手动添加', value: 1 },
        { label: '自动加入', value: 2 },
        { label: '自主报名', value: 3 }
      ],
      cultivateStatus,
      tableData: {
        records: [],
        total: 0
      },
      // 详情弹窗
      isShowDetailsPopup: false,
      // 高级筛选
      advancedFiltering: false,
      deptProps: {
        UnitFullName: 'dept_name',
        unitID: 'dept_id'
      },
      exportDisabled: false
    }
  },
  computed: {
    ...mapState(['projectManageInfo']),
    isApprove() {
      // 审批-审批中
      return this.$route.query.approve === '1' || this.projectManageInfo.course_status === 6
    }
  },
  created () {
    this.getStudentStatistics()
    this.onSearch()
    this.getSysBusyStatus()
  },
  mounted() {
    this.$nextTick(() => {
      this.getTableHeight()
    })
  },
  methods: {
    onSearch (page_no = 1) {
      this.current = page_no
      const { start_study_time, end_study_time, dept_name } = this.searchForm
      getStudentDetailListAPI({
        mooc_course_id: this.$route.query.mooc_course_id,
        staff_name: this.searchForm.name,
        status: this.searchForm.status,
        join_type: this.searchForm.joinType,
        join_start_time: this.searchForm.joinTime?.length ? this.searchForm.joinTime[0] : '',
        join_end_time: this.searchForm.joinTime?.length ? this.searchForm.joinTime[1] : '',
        finished_start_time: this.searchForm.finishedTime?.length ? this.searchForm.finishedTime[0] : '',
        finished_end_time: this.searchForm.finishedTime?.length ? this.searchForm.finishedTime[1] : '',
        page_no,
        page_size: this.size,
        dept_name,
        start_study_time,
        end_study_time
      }).then(res => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    // 重置
    resetEvent () {
      this.$refs.deptSelectorRef.clearSelected()
      this.searchForm.name = ''
      this.searchForm.status = ''
      this.searchForm.joinType = ''
      this.searchForm.joinTime = []
      this.searchForm.finishedTime = []
      this.searchForm.dept_name = ''
      this.searchForm.start_study_time = ''
      this.searchForm.end_study_time = ''
      this.onSearch(1)
    },
    getSysBusyStatus() {
      isSysBusy().then(res => {
        if (res === '1') {
          this.exportDisabled = true
        } else {
          this.exportDisabled = false
        }
      })
    },
    // 导出
    exportEvent (type) {
      const { start_study_time, end_study_time, dept_name } = this.searchForm
      const commonAPI = type === 'detail' ? studentDetailExportAPI : getStudentDetailListExportAPI
      commonAPI({
        mooc_course_id: this.$route.query.mooc_course_id,
        staff_name: this.searchForm.name,
        status: this.searchForm.status,
        join_type: this.searchForm.joinType,
        join_start_time: this.searchForm.joinTime.length ? this.searchForm.joinTime[0] : '',
        join_end_time: this.searchForm.joinTime.length ? this.searchForm.joinTime[1] : '',
        finished_start_time: this.searchForm.finishedTime.length ? this.searchForm.finishedTime[0] : '',
        finished_end_time: this.searchForm.finishedTime.length ? this.searchForm.finishedTime[1] : '',
        start_study_time,
        end_study_time,
        dept_name
      })
    },
    // 查看详情
    lookDetails (info) {
      this.isShowDetailsPopup = true
      this.currentDetails = info
    },
    // 学员详情统计
    getStudentStatistics() {
      const { mooc_course_id } = this.$route.query
      getStudentStatisticsAPI({ mooc_course_id }).then(res => {
        this.summaryList[0].value = res.student_count
        this.summaryList[1].value = res.finished_count
        this.summaryList[2].value = res.process_count
        this.summaryList[3].value = res.not_started_count
        this.summaryList[4].value = res.overdue_count
      })
    },
    changeDept(val) {
      this.searchForm.dept_name = val.UnitFullName || ''
    },
    // 高级筛选
    handleAdvancedFiltering() {
      if (this.advancedFiltering) {
        this.$refs.deptSelectorRef.clearSelected()
        this.searchForm.status = ''
        this.searchForm.joinType = ''
        this.searchForm.joinTime = []
        this.searchForm.finishedTime = []
        this.advancedFiltering = false
        this.searchForm.start_study_time = ''
        this.searchForm.end_study_time = ''
        this.searchForm.dept_name = ''
      } else {
        this.advancedFiltering = true
      }
      this.$nextTick(() => {
        this.getTableHeight()
      })
    },
    getTableHeight () {
      let wrapHeight = document.getElementsByClassName('el-tabs__content')[0].offsetHeight
      const summaryHeight = document.getElementsByClassName('summary')[0].offsetHeight
      const searchHeight = document.getElementsByClassName('el-form')[0].offsetHeight
      const paginationHeight = document.getElementsByClassName('el-pagination')[0]?.offsetHeight
      this.tableHeight = wrapHeight - 40 - summaryHeight - searchHeight - paginationHeight - 20 - 16 - 10
    }
  }
}
</script>

<style lang="less" scoped>
.summary {
  height: 140px;
  display: flex;
  border-bottom: 1px solid #f3f3f3ff;
  margin-bottom: 19px;
  .summary-item {
    width: 205px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    ::after {
      content: '';
      width: 1px;
      height: 48px;
      background: #eeeeeeff;
      position: absolute;
      top: calc(100% / 2 - 24px);
      right: 0;
    }
    &:nth-child(2) .value {
      color: #00A870;
    }
    &:nth-child(3) .value {
      color: #ED7B2F;
    }
    &:nth-child(4) .value {
      color: #E34D59;
    }
    &:nth-child(5) .value {
      color: #00000042;
    }
    
    .value {
      font-size: 28px;
      font-weight: 600;
    }
    .label {
      color: #00000099;
      margin-top: 7px;
      font-size: 14px;
    }
  }
}
.student-details {
  height: 100%;
  background: #fff;
  :deep(.el-form) {
    padding-bottom: 16px;
    .el-form-item {
      margin-right: 40px;
      .self-inner {
        width: 280px;
      }
    }
  }
  .detail-study-input{
    .line {
      margin-right: 10px;
      margin-left: 10px;
      color: #999;
    }
    :deep(.el-input) {
      width: 140px;
      .el-input__inner {
        width: 140px;
      }
    }
  }
  :deep(.el-table) {
   .el-table__header-wrapper table, 
    .el-table__body-wrapper table {
      width: max-content !important;
    }
  }
}

.filter-btn {
  padding: 0 15px;
  height: 32px;
  border-color: #3464e0ff;
  .filter-box {
    display: flex;
    align-items: center;
    color: #0052d9ff;
    font-size: 14px;
    .icon {
      width: 16px;
      height: 16px;
      margin-right: 3px;
    }
  }
}
.advanced-filter {
  padding: 16px 16px 0 16px;
  background: #f9f9f9ff;
}
:deep(.el-table__fixed-right) {
  height: auto !important;
  bottom: 0;
  right: 0;
}
:deep(.el-table) {
  // min-width: 1800px;
}
:deep(.detail-dep-selector) {
 .sdc-selector {
    width:280px;
  }
}
</style>
