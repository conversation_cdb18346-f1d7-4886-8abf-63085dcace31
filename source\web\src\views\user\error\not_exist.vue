<template>
  <common-error>图文不存在！</common-error>
</template>
<script>
import commonError from './common-error.vue'

export default {
  name: 'error',
  components: {
    commonError
  },
  data() {
    return {
      headClearTimer: null 
    }
  },
  mounted() {
    this.headClearTimer = setInterval(() => {
      this.handleHeaderDom() 
    }, 50)
  },
  methods: {
    handleHeaderDom() {
      window.$qlCommonHeader && window.$qlCommonHeader.destroy()
      let chArr = document.body.getElementsByClassName('common-header')
      if (chArr?.length > 0) {
        for (let i = 0; i < chArr.length; i++) {
          if (chArr[i] !== null) chArr[i].parentNode.removeChild(chArr[i])
        }
        clearInterval(this.headClearTimer)
      }      
    }
  },
  beforeDestroy() {
    clearInterval(this.headClearTimer)
  }
}
</script>

<style lang="less" scoped>
</style>
