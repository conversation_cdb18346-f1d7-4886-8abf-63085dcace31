<template>
  <div class="loading">
    <!-- 加载中 -->
    <div class="loading-text" v-show="loading">
      <i class="el-icon-loading"></i>
      正在加载中
    </div>
    <div class="finish" v-show="!loading">
      <!-- 加载完成有数据 -->
      <div class="loading-text" v-show="total > 0">
        {{ isHide ? '部分评论不提供展示～' : '没有更多内容了～' }}
      </div>
      <!-- 无数据 -->
      <div class="empty" v-show="total === 0">
        <span class="empty-img"></span>
        <div class="empty-text">暂无数据</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'loading',
  props: {
    total: {
      // 数据的总条数
      type: Number,
      default: 0
    },
    loading: {
      // 是否正在加载
      type: Boolean,
      default: false
    },
    isHide: {
      // 是否隐藏内容
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="less" scoped>
.loading {
  padding-top: 32px;
  line-height: 20px;
  color: #999;
  text-align: center;
  .empty-text {
    margin-top: 20px;
  }
  .empty-img {
    display: inline-block;
    width: 360px;
    height: 258px;
    background: url(~@/assets/mooc-img/comment/empty.png) no-repeat center/cover;
  }
}
</style>
