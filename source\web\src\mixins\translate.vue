
<script>
export default {
  methods: {
    getLangJS() {
      let lang = localStorage.getItem('sdc-sys-def-lang')
      // 加载中英文js
      let langDOM = document.getElementById('multilingualres')
      if (langDOM) {
        let parent = langDOM.parentNode
        parent.removeChild(langDOM)
      }
      let langScript = document.createElement('script')
      langScript.type = 'text/javascript'
      langScript.value = 'translate'
      langScript.id = 'multilingualres'
      if (process.env.NODE_ENV === 'production') {
        langScript.src = lang === 'zh-cn' ? `//cdn.multilingualres.hr.tencent.com/MOOC/All_zh-cn.js` : `//cdn.multilingualres.hr.tencent.com/MOOC/All_en-us.js`
      } else {
        langScript.src = lang === 'zh-cn' ? `//cdntestmultilingualres.yunassess.com/MOOC/All_zh-cn.js` : `//cdntestmultilingualres.yunassess.com/MOOC/All_en-us.js`
      }
      // const num = new Date().getTime()
      // if (process.env.NODE_ENV === 'production') {
      //   langScript.src = lang === 'zh-cn' ? `//cdn.multilingualres.hr.tencent.com/MOOC/All_zh-cn.js?ts=${num}` : `//cdn.multilingualres.hr.tencent.com/MOOC/All_en-us.js?ts=${num}`
      // } else {
      //   langScript.src = lang === 'zh-cn' ? `//cdntestmultilingualres.yunassess.com/MOOC/All_zh-cn.js?ts=${num}` : `//cdntestmultilingualres.yunassess.com/MOOC/All_en-us.js?ts=${num}`
      // }
      document.getElementsByTagName('head')[0].appendChild(langScript)
      langScript.onload = langScript.onreadystatechange = (res) => {
        this.$i18n.locale = lang
        this.$i18n.setLocaleMessage(lang, JSON.parse(JSON.stringify(window.mooc_All)))
      }
    }
  }
}
</script>
