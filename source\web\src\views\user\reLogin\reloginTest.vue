<template>
  <div>
    测试页
    <iframe :src="iframeSrc" frameborder="0" style="width: 300px;height: 600px;margin-right: 100px;"></iframe>
    <iframe :src="iframeSrc" frameborder="0" style="width: 300px;height: 600px;" v-if="iframeShow"></iframe>
  </div>
</template>

<script>
// import '../../../../public/reLogin/reLogin.js'
export default {
  mounted () {
    this.loadReloginJs()
    // this.handleLogin()
    setTimeout(() => {
      console.log('执行了显示iframe---测试用')
      this.iframeShow = true
    }, 30 * 1000)
  },
  data() {
    return {
      iframeShow: false
    }
  },
  computed: {
    iframeSrc() {
      return process.env.NODE_ENV === 'production' ? '//portal.learn.woa.com/training/reLogin/reLogin.html' : '//test-portal-learn.woa.com/training/reLogin/reLogin.html'
    }
  },
  methods: {
    handleLogin () {
      window.$trainingTaihuRelogin.handlerReLoginInit({
        reloginPageUrl: 'portal.learn.woa.com/training/reLogin/reLogin.html',
        authUrl: '//test-portal-learn.woa.com/training/api/health/check',
        checkloginTime: 15
      })
    },
    loadReloginJs() {
      let url = process.env.NODE_ENV === 'production' ? `https://qlportal.ihr.tencent-cloud.com/knowledgeservice/reLogin/reLogin.js?time=${Math.random()}` : `https://cdnyewutest.yunassess.com/knowledgeservice/reLogin/reLogin.js?time=${Math.random()}`
      let reloginjs = document.createElement('script')
      reloginjs.src = url
      document.body.appendChild(reloginjs)
    }
  }
}
</script>

<style>

</style>
