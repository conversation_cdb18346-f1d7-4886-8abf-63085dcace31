<template>
  <div class="preview-lists">
    <div class="title-text">
      <span>{{ title }}</span>
      <slot></slot>
    </div>
    <div class="list-body">
      <div v-if="scene == 'course'" class="course-box">
        <div class="course-origin" @click="gokd()">
          <span>课程来源：</span
          ><el-link :underline="false" type="primary">{{
            list[0] && list[0].name
          }}</el-link>
        </div>
        <div class="course-item-box">
          <div
            v-for="(item, index) in list"
            :key="index"
            class="course-item"
            :dt-areaid="dtListBody(item, 'area')"
            :dt-eid="dtListBody(item , 'eid')"
            :dt-remark="dtListBody(item , 'remark')"
            :class="[
              $route.query.graphic_id == item.item_id ? 'item-active' : ''
            ]"
            @click="go(item)"
          >
            <p>
              <span v-if="$route.query.graphic_id != item.item_id">{{
                index | handleIndex
              }}</span>
              <i v-else class="current-play"></i>
            </p>
            <p>
              <span
                :class="['tags', getModuleClass(item.module_id)]"
                v-if="moocLang == 'zh-cn'"
                >{{ item.module_name }}</span
              >
              <img
                :src="getEnImg(item.content_module_id)"
                alt=""
                v-if="moocLang == 'en-us'"
                class="en-tags"
              />{{ item.content_name }}
            </p>
            <p>
              {{
                item.module_id == 7 ||
                item.module_id == 8 ||
                item.module_id == 99
                  ? item.module_id == 99
                    ? (item.word_num == null && item.duration == null) ||
                      (item.word_num == 0 && item.duration == 0)
                      ? `${item.duration || 0}分钟`
                      : item.word_num && item.word_num > 0
                      ? `${item.word_num}字`
                      : `${item.duration || 0}分钟`
                    : `${item.word_num || 0}字`
                  : item.module_id == 16
                  ? `-`
                  : `${item.duration || 0}分钟`
              }}
            </p>
          </div>
        </div>
      </div>
      <div v-else class="common-box">
        <div
          v-for="(item, index) in list"
          :key="index"
          class="common-item"
          @click="go(item, 1)"
          :dt-areaid="dtListBody(item, 'area')"
          :dt-eid="dtListBody(item , 'eid')"
          :dt-remark="dtListBody(item , 'remark')"
        >
          <div class="common-item-right">
            <p class="title" v-if="scene == 'extand'">
              <span>{{ index + 1 }}</span>
              <span
                v-if="moocLang == 'zh-cn'"
                :class="['tags', getModuleClass(item.content_module_id)]"
                >{{ item.content_module_name }}</span
              >
              <img
                :src="getEnImg(item.content_module_id)"
                alt=""
                v-if="moocLang == 'en-us'"
                class="en-tags"
              />
              {{ item.content_name }}
            </p>
            <p class="title" v-else>
              <span>{{ index + 1 }}</span>
              <span
                :class="['tags', getModuleClass(item.module_id)]"
                v-if="moocLang == 'zh-cn'"
                >{{ getModuleName(item.module_id) }}</span
              >
              <img
                :src="getEnImg(item.module_id)"
                alt=""
                v-if="moocLang == 'en-us'"
                class="en-tags"
              />
              {{ item.title }}
            </p>
            <p class="info">
              <span
                ><i class="icon-play"></i>
                {{ item.view_count | conuntFilter }}</span
              >
              <span v-if="scene == 'extand'"
                ><i class="icon-fav"></i>
                {{ item.avg_score | conuntFilter }}</span
              >
              <!-- <span v-else
                ><i class="icon-fav"></i
                >{{ item.origin_data.avg_score | conuntFilter }}</span
              > -->
              <span v-if="scene == 'extand'"
                ><i class="icon-time"></i
                >{{
                  item.content_module_id == 99
                    ? (item.created_at && item.created_at.split(' ')[0]) || '--'
                    : (item.content_created_time &&
                        item.content_created_time.split(' ')[0]) ||
                      '--'
                }}</span
              >
              <span v-else
                ><i class="icon-time"></i
                >{{
                  (item.created_at && item.created_at.split(' ')[0]) || '--'
                }}</span
              >
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { qlearningModuleTypes } from '../../utils/constant'
export default {
  props: {
    title: {
      type: String,
      default: '课单内容列表'
    },
    scene: {
      type: String
    },
    list: {
      type: Array,
      default: () => []
    },
    paramsData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      portUrl: location.hostname.endsWith('.woa.com')
        ? process.env.VUE_APP_PORTAL_HOST_WOA
        : process.env.VUE_APP_PORTAL_HOST
    }
  },
  computed: {
    ...mapState(['moocLang']),
    // 埋点
    dtListBody () {
      return (item, type) => {
        if (type === 'area') {
          return `area_${this.title}_${item.module_name}_${item.item_id}`
        } else if (type === 'eid') {
          return `element_${this.title}_${item.module_name}_${item.item_id}`
        } else if (type === 'remark') {
          return JSON.stringify({ 
            page: this.paramsData.course_name,
            page_type: '网课详情页',
            container_type: this.title,
            container: this.title,
            click_type: 'data',
            content_type: item.module_name,
            content_id: item.item_id,
            content_name: item.title,
            terminal: 'PC'
          })
        }
      }
    }
  },
  methods: {
    go(item, scene) {
      if (this.$route.query.scene) return false
      this.skip(item.href)
    },
    gokd() {
      const href = `${this.portUrl}/courselist/course-detail?id=${this.$route.query.area_id}`
      this.skip(href)
    },
    skip(href) {
      window.open(href, '_blank')
    },
    getModuleClass(module_id) {
      // if (this.scene !== 'extand') {
      //     let cardType = recommendModuleTypes.find((item) => module_id === item.module_id)
      //     if (cardType) {
      //         return cardType.moduleClassName
      //     }
      // } else {
      let cardType = qlearningModuleTypes.find(
        (item) => module_id === item.module_id
      )
      if (cardType) {
        return cardType.moduleClassName
      }
      // }
    },
    getEnImg(module_id) {
      let cardType = qlearningModuleTypes.find(
        (item) => module_id === item.module_id
      )
      if (cardType) {
        return require(`@/assets/img/list-tags/${cardType.enImg}`)
      }
    },
    getModuleName(module_id) {
      let cardType = qlearningModuleTypes.find(
        (item) => module_id === item.module_id
      )
      if (cardType) {
        return cardType.module_name
      }
    }
  },
  filters: {
    handleIndex: function (index) {
      const curIndex = index < 9 ? `0${index + 1}` : index + 1
      return curIndex
    },
    conuntFilter: function (val) {
      let str = ''
      str =
        val > 0 ? (val >= 10000 ? `${(val / 10000).toFixed(1)}万` : val) : '0'
      return str
    }
  }
}
</script>

<style lang="less" scoped>
.preview-lists {
  width: 272px;
  border-radius: 4px;
  background-color: #fff;

  .title-text {
    height: 68px;
    line-height: 68px;
    padding-left: 16px;
    color: rgba(51, 51, 51, 1);
    font-size: 20px;
    font-weight: bold;
    border-bottom: solid 1px #eeeeee;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .list-body {
    padding: 12px 0 20px 0;

    .course-box {
      .course-origin {
        padding: 0 16px;
        cursor: pointer;
        margin-bottom: 16px;
        color: rgba(0, 0, 0, 0.6);

        :deep(.el-link:hover) {
          color: #0052d9;
        }
      }

      .course-item-box {
        padding-left: 16px;
        max-height: 312px;
        overflow-y: auto;
      }

      .course-item {
        display: flex;
        align-items: center;
        font-size: 12px;
        margin-bottom: 12px;
        cursor: pointer;

        > div {
          display: flex;
          align-items: center;
        }

        p {
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          display: inline-block;
          color: rgba(0, 0, 0, 0.6);
        }

        p:first-child {
          width: 18px;
          display: flex;
          align-items: center;
        }

        p:nth-child(2) {
          width: 148px;
          margin-right: 8px;

          .tags {
            margin: 0 6px 0 6px;
          }
        }

        p:last-child {
          width: 63px;
          text-align: right;
        }
      }

      .item-active {
        .current-play {
          display: inline-block;
          width: 12px;
          height: 12px;
          background: url('../../assets/img/current-play.png') no-repeat center /
            cover;
        }

        p {
          color: #0052d9 !important;
        }
      }
    }

    .common-box {
      padding: 1px 16px;
      overflow-y: auto;
      max-height: 305px;

      .common-item {
        display: flex;
        align-items: baseline;
        margin-bottom: 20px;
        cursor: pointer;

        .common-item-right {
          .title {
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
            margin-bottom: 8px;
            word-break: break-all;
            white-space: nowrap;
            width: 246px;
            .en-tags {
              width: 51px;
              height: 18px;
              object-fit: contain;
              margin: 0 12px 0 10px;
            }
          }

          .info {
            display: flex;
            color: #666666;

            span:first-child {
              margin-left: 18px;
            }

            i {
              display: inline-block;
              width: 14px;
              height: 14px;
              padding-left: 12px;
              margin-right: 5px;
            }

            .icon-play {
              background: url('../../assets/img/play.png') no-repeat center
                center / cover;
            }

            .icon-fav {
              background: url('../../assets/img/fav.png') no-repeat center
                center / cover;
            }

            .icon-time {
              background: url('../../assets/img/time.png') no-repeat center
                center / cover;
            }

            span {
              + span {
                margin-left: 10px;
              }

              display: flex;
              align-items: center;
            }
          }
        }
      }

      .common-item:last-child {
        margin-bottom: unset;
      }
    }
  }
}
</style>
