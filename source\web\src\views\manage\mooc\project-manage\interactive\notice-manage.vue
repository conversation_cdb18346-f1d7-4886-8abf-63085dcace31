<template>
  <div class="notice-manage">
    <el-button :disabled="isApprove" class="btn-create" type="primary" size="small" @click="showCreateEdit('create', null)">新建公告</el-button>
    <el-table :data="tableData.records" header-row-class-name="table-header-style">
      <el-table-column prop="title" label="公告标题">
        <template slot-scope="scope">
          <span class="title" @click="showCreateEdit('look', scope.row)">{{ scope.row.title }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="resource_type_name" label="发布状态">
        <template slot-scope="scope">
          {{ ['未发布', '已发布', '已取消' ][scope.row.status] }}
        </template>
      </el-table-column>
      <el-table-column prop="prepare_publish_time" label="发布时间"></el-table-column>
      <el-table-column prop="creator_name" label="发布人"></el-table-column>
      <el-table-column prop="view_count" label="查看人数"></el-table-column>
      <el-table-column label="操作" width="260">
        <template slot-scope="scope">
          <el-button v-if="scope.row.status!=='1'" type="text" @click="showCreateEdit('edit', scope.row)">编辑</el-button>
          <el-button v-if="scope.row.status==='1'" type="text" @click="operateEvent('cancel', scope.row)">取消发布</el-button>
          <el-button v-if="scope.row.status==='2'" type="text" @click="operateEvent('reload', scope.row)">重新发布</el-button>
          <el-button class="btn-del" type="text" @click="operateEvent('del', scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="current"
      :page-sizes="[10, 20, 30, 50, 100]"
      :page-size="size"
      layout="total,  prev, pager, next, sizes, jumper"
      :total="tableData.total">
    </el-pagination>

    <!-- 公告新建编辑弹窗 -->
    <notice-create-edit
      v-if="isShowCreateEditPopup"
      :isShowCreateEditPopup.sync="isShowCreateEditPopup"
      :popupType="popupType"
      :notice-details="currentNotice"
      @updateNoticeList="onSearch">
    </notice-create-edit>
  </div>
</template>

<script>
import NoticeCreateEdit from './component/notice-create-edit.vue'
import pagination from '@/mixins/pager'
import { getNoticeListAPI, changeNoticeStatusAPI, deleteNoticeAPI } from '@/config/mooc.api.conf.js'
import { mapState } from 'vuex'
export default {
  components: {
    NoticeCreateEdit
  },
  mixins: [pagination],
  data () {
    return {
      tableData: {
        records: [],
        total: 0
      },
      // 详情弹窗
      isShowCreateEditPopup: false,
      currentNotice: null,
      popupType: 'create'
    }
  },
  computed: {
    ...mapState(['projectManageInfo']),
    isApprove() {
      // 审批-审批中
      return this.$route.query.approve === '1' || this.projectManageInfo.course_status === 6
    }
  },
  created () {
    this.onSearch()
  },
  mounted() {
    this.$nextTick(() => {
      this.getTableHeight()
    })
  },
  methods: {
    onSearch (page_no = 1) {
      getNoticeListAPI({
        act_id: this.$route.query.mooc_course_id,
        page_no,
        page_size: this.size
      }).then(res => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    // 新建or编辑弹窗
    showCreateEdit (type, info) {
      this.isShowCreateEditPopup = true
      this.popupType = type
      this.currentNotice = info
    },
    // 取消发布、重新发布、删除
    operateEvent (type, info) {
      const categoryInfo = {
        cancel: {
          title: '取消发布公告',
          content: '取消发布后学员将无法查看此公告内容，确定取消吗？',
          confirmText: '确认取消'
        },
        reload: {
          title: '重新发布公告',
          content: '确定重新发布此公告吗？',
          confirmText: '确定'
        },
        del: {
          title: '删除公告',
          content: '公告删除后学员将无法查看此公告内容，且无法恢复数据，确定删除吗？',
          confirmText: '删除公告'
        }
      }
      this.$messageBox.confirm(categoryInfo[type].content, categoryInfo[type].title, {
        cancelButtonText: '取消',
        confirmButtonText: categoryInfo[type].confirmText
      }).then(() => {
        if (type === 'del') {
          this.deleteNotice(info)
        } else {
          this.changeNoticeStatus(info, type === 'cancel' ? 2 : 1)
        }
      })
    },
    // 修改公告状态
    changeNoticeStatus (info, statusVal) {
      changeNoticeStatusAPI({
        notice_id: info.notice_id,
        status: statusVal
      }).then(() => {
        this.$message.success('操作成功!')
        this.onSearch()
      })
    },
    // 删除公告
    deleteNotice (info) {
      deleteNoticeAPI(info.notice_id).then(() => {
        this.$message.success('删除成功！')
        this.onSearch()
      })
    },
    getTableHeight () {
      let wrapHeight = document.getElementsByClassName('el-tabs__content')[0].offsetHeight
      const btnHeight = document.getElementsByClassName('btn-create')[0].offsetHeight
      const paginationHeight = document.getElementsByClassName('el-pagination')[0]?.offsetHeight
      this.tableHeight = wrapHeight - 40 - btnHeight - 10 - paginationHeight - 20 - 10
    }
  }
}
</script>

<style lang="less" scoped>
.notice-manage {
  .btn-create {
    margin-left: auto;
    margin-bottom: 10px;
    display: block;
  }
  .title {
    color: #0052d9;
    cursor: pointer;
  }
  .btn-del {
    color: #e34d59;
  }
}
</style>
