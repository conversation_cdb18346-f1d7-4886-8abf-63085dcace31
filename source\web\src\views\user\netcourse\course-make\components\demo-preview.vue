<template>
  <div class="demo-preview">
    <span class="demo-video">
      <Video
        class="video-box"
        v-if="demoData.src || demoData.content_id"
        :source_src.sync="demoData.src"
        :content_id.sync="demoData.content_id"
        :autoPlay="autoPlay"
        ref="previewVideo"
      />
    </span>
    <span class="demo-msg">{{ demoData.msg }}</span>
    <div class="tips" v-show="demoData.process_duration > 0">
      预计demo生成时长：{{ transforTime(demoData.process_duration) }}
    </div>
  </div>
</template>

<script>
import { Video } from '@/components/index'
import { transforTime } from 'utils/tools'

export default {
  name: 'demoPreview',
  components: {
    Video
  },
  props: {
    demoData: {
      type: Object,
      default: () => {
        return {
          process_duration: 0,
          request_id: '',
          src: '',
          msg: '',
          content_id: ''
        }
      }
    },
    autoPlay: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      transforTime
    }
  },
  methods: {
    pause() {
      this.$refs.previewVideo && this.$refs.previewVideo.pause()
    }
  }
}
</script>

<style lang="less" scoped>
.demo-preview {
  .demo-video,
  .video-box {
    width: 324px;
    height: 182px;
    border-radius: 3px;
  }

  .demo-video {
    display: inline-block;
    background-color: #f0f0f0;
  }

  .demo-msg {
    position: relative;
    bottom: 4px;
    margin-left: 20px;
    color: #d63535;
    font-size: 14px;
    line-height: 22px;
  }
  .tips {
    margin-top: 5px;
    color: #d63535;
  }
}
</style>
