<template>
  <div v-if="(courseData.support_type && [2, 3].includes(courseData.support_type)) || !courseData.support_type" class="net-page" :dt-areaid="dtOutCoursePage('area')" :dt-remark="dtOutCoursePage('remark')">
    <div class="contain-main">
      <div class="left">
        <div class="content-top">
          <div class="name">
            <span class="tag word">{{filterResourceName}}</span>
            <!-- <img class="geek-time" src="@/assets/mooc-img/comment/geek-time.png" alt="极客时间"> -->
            <img class="geek-time" :src="typeImg" alt="">
            <span>{{ courseData.course_title }}</span>
          </div>
          <div class="info info1">
            <div class="info-left">
              <!-- <span class="time">{{ courseData.created_at ||  $langue('NetCourse_CreateTime', { defaultText: '创建时间' })+ ': --' }}</span> -->
              <span class="time">{{ courseData.created_at }}</span>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content">
                  <!-- <span class="create">{{ teacher_name }}</span> -->
                  <span>{{ courseData.author }}</span><span v-if="courseData.author_intro">({{ courseData.author_intro }})</span>
                </div>
                <p class="create">
                  <!-- <span>{{ teacher_name }}</span> -->
                  <span>{{ courseData.author }}</span><span v-if="courseData.author_intro">({{ courseData.author_intro | nameFilter }})</span>
                </p>
              </el-tooltip>
            </div>
            <div class="info-right">
              <span><i class="icon-view"></i>({{ zanAndcollect.view_count || 0 }})</span>
              <span v-if="isIndependent_course" @click="handleLikeOrFav(1)" :class="[zanAndcollect.isZan ? 'icon-zan-active' : '']"><i
                  class="icon-zan"></i>({{ zanAndcollect.praise_count || 0 }})</span>
              <span v-if="isIndependent_course"><i class="icon-comment"></i>({{ zanAndcollect.comment_count || 0 }})</span>
              <span v-if="isIndependent_course" @click="handleLikeOrFav(2)" :class="[zanAndcollect.isCollect ? 'icon-collect-active' : '']"><i
                  class="icon-collect"></i> ({{ zanAndcollect.fav_count || 0 }})</span>
              <div class="jf-tip" v-if="isShowJfTip"><i class="jf-icon"></i>{{ $langue('Mooc_Common_Alert_CommonPoint', { point: 1, defaultText: '通用积分+1' }) }}</div>
            </div>
          </div>
          <div class="info info2" v-if="courseData.labels && courseData.labels.length">
            <div class="info-left">
              <div class="info-label">
                <span class="label">{{$langue('Article_Lable', { defaultText: '标签' })}}：</span>
                <div class="tag-list-box">
                  <div class="tag-list" v-for="(item, index) in courseData.labels" :key="index">
                    <el-tooltip effect="dark" :content="item.category_full_name" placement="top-start">
                      <span class="tag-value" @click="searchGo(item)">{{ item.label_name }}</span>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-if="isVideoType || courseData.course_type === 'Audio'" class="video-main">
             <!-- 极客时间播放器-->
             <!-- <iframe class="geek-task-iframe video-box" id="geekTaskIframe" :src="courseData.recourse_iframe_url + '&pm=parent'" frameborder="0" allowfullscreen></iframe> -->
             <iframe class="geek-task-iframe video-box" :class="{'video-fullscreen': isFullScreen}" id="geekTaskIframe" :src="courseData.recourse_iframe_url + '&pm=parent'" frameborder="0" allowfullscreen></iframe>
            <!-- <Video
              class="video-box" 
              :content_id.sync="courseData.content_id" 
              @getCurCaption="getCurCaption" 
              @handleRecord="handleRecord" 
              @getCurrentTime="getCurrentTime"
              :source_src="courseData.file_url"
              :playTime="playTime"
              ref="vidioDOM" 
              :fullscreenToggle="!enableInteractive"
              :progressControl="progressControl"
            ></Video>
            <div class="current-time-tips" v-if="isCurrentTimeShow">
              <i class="el-icon-close" @click="isCurrentTimeShow = false"></i>
              <span>{{ $langue('NetCourse_PlayAt', {seconds: playTime, defaultText: `上次播放至${playTime}秒` }) }}</span>
              <span class="tips-btn" @click="toCurrentTime">{{ $langue('NetCourse_ClickGo', { defaultText: '点击跳转' }) }}</span>
            </div> -->
             <!-- 视频播放互动弹窗 -->
            <!-- <InteractiveDialog v-if="enableInteractive" ref="interactiveDialog" class="interactive-dialog" :course_id="course_id" :record_id="learnRecordId" @changePlayStatus="changePlayStatus"/> -->
          </div>
          <div
          v-else
            class="video-box">
            <el-image lazy fit="fill"
              :src="courseData.photo_url ? courseData.photo_url : require('@/assets/img/default_bg_img.png')"
              class="item-image">
              <div slot="error" class="image-slot">
                <i class="default-icon-picture"></i>
              </div>
            </el-image>
          </div>
        </div>
        <div class="content-bottom">
          <el-tabs v-model="tabActiveName">
            <el-tab-pane 
            :label="$langue(tabItem.label, { defaultText: tabItem.text })" 
            :name="tabItem.name" 
            v-for="(tabItem) in tabList"
            :key="tabKey(tabItem.name)"
            >
          </el-tab-pane>
          </el-tabs>
          <div v-show="tabActiveName === 'desc'" class="desc-box">
            <sdc-mce-preview :urlConfig="editorConfig.urlConfig" :content="courseData.course_intro ?? '暂无简介'"></sdc-mce-preview>
          </div>
          <div v-show="tabActiveName === 'comment'" class="comment-box">
            <div v-if="isIndependent_course && loadComment" id="commentBox">
              <sdc-comment :params="commentParams" @setCommentCount="setCommentCount" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <notSupported v-else :courseInfo="courseData"/>
</template>

<script>
import { getGeekCourseDetail, checkPraised, addPraise, deletePraise, checkFavorited, addFavorited, deleteFavorite, geekStudyRecord, getSummaryData } from 'config/mooc.api.conf.js'
import { mapState } from 'vuex'
import MoocJs from 'sdc-moocjs-integrator'
import translate from 'mixins/translate.vue'
import { detailLogo } from '@/utils/outsourcedCourseMap.js'
import { pageExposure } from '@/utils/tools.js'
import notSupported from './notSupported.vue'

export default {
  mixins: [translate],
  components: { notSupported },
  data() {
    return {
      act_type: '102', // 课程类型 102-极客时间
      isFullScreen: false, // 播放器是否全屏
      editorConfig: {
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/{contentId}`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview`
        }
      },
      zanAndcollect: { // 点赞数据
        isZan: false,
        isCollect: false,
        praise_count: 0, // 点赞数
        fav_count: 0, // 收藏数
        view_count: 0, // 浏览量
        comment_count: 0 // 评论量
      },
      commentParams: {},
      loadComment: false,
      courseData: { // 课程详情
        labels: []
      }, 
      countTimer: null, // 15s上报的timeId
      isShowJfTip: false, // 是否显示通用积分
      tabActiveName: 'desc',
      captionCurTime: null,
      studyRecordQuery: { // video学习记录
        act_id: this.$route.query.course_id,
        area_id: this.$route.query.area_id || '',
        from: this.$route.query.from || '',
        learn_record_id: 0,
        is_finish: 0,
        is_review: 0,
        total_study_time: 0
        // my_study_progress: ''
      },
      // playTime: 0,
      viewRecordTime: null,
      tabList: [
        { label: 'Mooc_TaskDetail_Audio_Description', name: 'desc', text: '简介' }
      ],
      // 上报id
      graphic_access_record_id: '',
      studyRecordErrNum: 0,
      clear3sTimeId: null
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          const hostUrl = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_PORTAL_HOST_WOA : process.env.VUE_APP_PORTAL_HOST
          this.commentParams = {
            userName: val.staff_name,
            actId: this.course_id,
            appId: 'A9BiosXihR0h46ThNsAX',
            scrollTarget: '.graphic-user-page',
            urlConfig: {
              getComments: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/${this.mooc_course_id}/comment-page`,
              addComment: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/add-comment`,
              deleteComment: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/delete-comment/`,
              like: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/praised-comment`, 
              sticky: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/sticky-comment`,
              show: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/show-comment`
            }
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState(['userInfo', 'moocLang']),
    // 独立课程
    isIndependent_course() {
      return this.courseData.independent_course === 1
    },
    // mooc_id
    mooc_course_id() {
      return this.$route.query.mooc_course_id || '-1'
    },
    // 课程id
    course_id() {
      return this.$route.query.course_id || ''
    },
    // 创建者
    // teacher_name() {
    //   let { inner_teacher_names, out_teacher_names } = this.courseData
    //   let name = ''
    //   if (inner_teacher_names?.length) {
    //     inner_teacher_names.forEach((e) => {
    //       if (!e.teacher_name) return
    //       name += `${e.teacher_name}， `
    //     })
    //   }
    //   if (out_teacher_names?.length) {
    //     out_teacher_names.forEach((e) => {
    //       if (!e.teacher_name) return
    //       name += `${e.teacher_name}， `
    //     })
    //   }
    //   name = name.slice(0, -2)
    //   return name
    // },
    // 类型
    filterResourceName() {
      let { course_type } = this.courseData
      let name = ''
      if (this.isVideoType) {
        name = this.$langue('Mooc_Common_ResourceType_Video', { defaultText: '视频' })
      } else if (course_type === 'Audio') {
        name = this.$langue('Mooc_Common_ResourceType_Audio', { defaultText: '音频' })
      } else if (course_type === 'Article') {
        name = this.$langue('Mooc_Common_ResourceType_Article', { defaultText: '文章' })
      } else if (course_type === 'Doc') {
        name = this.$langue('Mooc_Common_ResourceType_Doc', { defaultText: '文档' })
      } else if (course_type === 'Scorm') {
        name = 'Scorm'
      } else if (course_type === 'Flash') {
        name = this.$langue('Mooc_Common_ResourceType_Zip', { defaultText: '压缩包' })
      }
      return name
    },
    isVideoType() {
      return ['video', 'Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(this.courseData.course_type)
    },
    tabKey() {
      return (name) => {
        return name + new Date().getTime()
      }
    },
    clearTime() { // 视频时长的2.5倍后停止上报 单位是秒 计时器需要*1000
      return (this.courseData.video_time || 0) * 2.5
    },
    typeImg() {
      return detailLogo[this.courseData.recourse_from] || detailLogo['geekBang']
    },
    // 页面曝光
    dtOutCoursePage() {
      return (type) => {
        const { course_title } = this.courseData
        if (type === 'area') {
          return `area_${this.course_id}`
        } else if (type === 'remark') {
          return JSON.stringify({ 
            page: course_title,
            page_type: '外部课程详情页',
            container: course_title,
            act_type: '102',
            content_type: '视频',
            terminal: 'PC'
          })
        } else {
          return ``
        }
      }
    }
  },
  mounted() {
    window.addEventListener('beforeunload', () => {
      // 离开当前页面学习记录归档
      if ((this.isVideoType || this.courseData.course_type === 'Audio') && this.studyRecordQuery.total_study_time) { 
        // let param = {
        //   ...this.studyRecordQuery,
        //   is_archive: true
        // }
        const recordParam = {
          from: this.$route.query.from || '',
          area_id: this.$route.query.area_id || '',
          course_id: this.course_id,
          sharer_id: this.$route.query.share_staff_id || '',
          sharer_name: this.$route.query.share_staff_name || '',
          from_type: this.courseData.recourse_from || 'geekBang',
          record_id: this.graphic_access_record_id
        }

        let blob = new Blob([JSON.stringify(recordParam)], { type: 'application/json; charset=UTF-8' })
        navigator.sendBeacon('/training/api/outsourcedCourse/user/info/study/record', blob) // 上报接口
      }
    })

    // let flag = /[^0-9]/g.test(this.$route.query.course_id)
    // if (flag) {
    //   sessionStorage.setItem('401Msg', this.$langue('Mooc_Common_Alert_DataNotExisit', { defaultText: '数据不存在' }))
    //   this.$router.replace({
    //     name: '401'
    //   })
    //   return
    // }

    // 获取课程详情
    this.getCourseInfo().then(res => {
      this.clear3sTimeId = setTimeout(() => {
        window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
      }, 3000)

      this.loadComment = true

      let _this = this
      // 监听播放等事件
      window.addEventListener('message', event => {
        console.log('外部课程video页面------event.data: ', event.data)
        let { action, vendor, params } = event.data
        let fullscreen = params?.fullscreen || false
        // vendor === 'geekbang' && console.log('极客时间--课程: ', event)
        let vendorArray = Object.keys(detailLogo) || []
        if ([...vendorArray, 'geekbang'].includes(vendor)) {
          _this.handleRecord({ evt: action }) // 播放 暂停 完成
          switch (action) {
            case 'video:mounted': // video组件加载完成
              window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
              break
            case 'video:fullscreen': // 全屏
              _this.isFullScreen = fullscreen
              break
            case 'login:error': // iframe执行登录失败
              window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
              break
            case 'video:error': // 视频加载失败
              window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
              MoocJs.onErrorInfo()
              break
            default:
              break
          }
        }
      })
    }).catch((err) => {
      if (err.code === 403 || err.code === 500) {
        sessionStorage.setItem('401Msg', err.message)
        this.$router.replace({
          name: '401'
        })
        MoocJs.sendErrorInfo(err.message)
      }
    })
    
    this.getZanAndCollectStatus()
    this.getSummaryInfo()

    let iframeDom = document.querySelector('#geekTaskIframe')
    // 控制播放器暂停和播放
    MoocJs.setPause(() => {
      // 这里需要和iframe通讯暂停
      iframeDom && iframeDom.contentWindow.postMessage('video:pause', '*')
    })
    MoocJs.setPlay(() => {
      // 这里需要和iframe通讯播放
      iframeDom && iframeDom.contentWindow.postMessage('video:play', '*')
    })
    MoocJs.messageListener((res) => {
      if (res.events === 'tencent-mooc-lang') {
        this.$store.commit('setMoocLang', res.params)
        this.getLangJS()
      }
    })
  },
  methods: {
    // iframe里面的播放器的事件
    handleRecord(param) {
      if (param.evt === 'video:play') {
        // 重新学习，重置数据
        if (this.studyRecordQuery.is_finish === 1) {
          this.studyRecordQuery.is_finish = 0
          // this.studyRecordQuery.is_review = 1 // 重播
          this.studyRecordQuery.total_study_time = 0
          this.graphic_access_record_id = ''
        }
        if (!this.countTimer) {
          this.creatViewTimer(param)
          this.createClearTime(this.clearTime)
        }
        MoocJs.play()
      }

      if (param.evt === 'video:pause' || param.evt === 'video:ended') {
        if (param.evt === 'video:ended') { // 学习完
          this.studyRecordQuery.is_finish = 1
        }
        
        // 延迟1秒调用方法，（视频播放完成会先触发pause，再触发ended），解决同时触发pause、ended情况
        if (this.viewRecordTime) clearTimeout(this.viewRecordTime)
        let _this = this
        this.viewRecordTime = setTimeout(() => {
          if (this.countTimer) {
            _this.viewRecord()
          }
        }, 1000)
        clearInterval(this.countTimer)
        this.countTimer = null

        if (param.evt === 'video:pause') {
          MoocJs.pause()
        } else if (param.evt === 'video:ended') {
          MoocJs.complete()
        }
      }
    },
    // 视频播放器实时播放时间
    // getCurrentTime(curTime) {
    //   this.captionCurTime = Number(curTime.toFixed(2))
    //   this.studyRecordQuery.my_study_progress = Math.floor(this.captionCurTime) // 视频当前播放时长
    // },
    createClearTime(clearTime) { // 2.52倍后停止上报
      let _this = this
      this.clearTimeId = setTimeout(() => {
        clearInterval(_this.countTimer)
        _this.countTimer = null
        clearTimeout(_this.clearTimeId)
        _this.clearTimeId = null
      }, clearTime * 1000)
    },
    creatViewTimer() {
      let _this = this
      let durtation = 0
      this.countTimer = setInterval(function () {
        durtation++
        _this.studyRecordQuery.total_study_time++
        if (durtation % 15 === 0) {
          _this.viewRecord() // 浏览器时长需每15秒记录一次
        }
      }, 1000)
    },
    // 学习上报
    viewRecord() {
      if (!this.studyRecordQuery.total_study_time) return
      const recordParam = {
        from: this.$route.query.from || '',
        area_id: this.$route.query.area_id || '',
        course_id: this.course_id,
        sharer_id: this.$route.query.share_staff_id || '',
        sharer_name: this.$route.query.share_staff_name || '',
        from_type: this.courseData.recourse_from || 'geekBang',
        record_id: this.graphic_access_record_id
      }
      // 上报接口
      geekStudyRecord(recordParam).then((data) => {
        // if (data) {
        //   if (recordParam.is_finish) {
        //     this.learnRecordId = 0
        //   } else {
        //     this.learnRecordId = data
        //   }
        // }
        if (data) {
          this.graphic_access_record_id = data
        }
      })
    },
    // 课程详情
    getCourseInfo() {
      window.parent && window.parent.postMessage({ page: 'iframe', loading: true, geekSourceLoading: true }, '*')
      const reFun = getGeekCourseDetail(this.course_id, { loading: false }).then(data => {
        // console.log('课程详情：data: ', data)
        document.title = `${data.course_title}_Q-Learning`
        this.courseData = data
        this.courseData.labels = data.labels || []

        if (data.independent_course === 1) {
          this.tabList.push({ label: 'Mooc_ProjectDetail_Notice_Comments', name: 'comment', text: '评论' })
        }

        if (data.course_type === 'CaseStudy') {
          this.$messageBox(`${this.$langue('NetCourse_NotSupportRecourse', { defaultText: '暂不支持该类型资源查看！' })}！`, this.$langue('Mooc_TaskDetail_ThirdParty_Alert', { defaultText: '提示' }), {
            distinguishCancelAndClose: true,
            confirmButtonText: this.$langue('NetCourse_Ok', { defaultText: '好的' }),
            cancelButtonText: this.$langue('Mooc_Common_Alert_Cancel', { defaultText: '取消' })
          })
        }
        this.studyRecordErrNum = 0

        // 详情页曝光上报
        pageExposure({
          page_type: '外部课程视频详情页',
          content_type: '网络课',
          act_type: '2',
          content_name: data.course_title,
          content_id: this.course_id
        })
      }).catch(() => {
        this.studyRecordErrNum++
        if (this.studyRecordErrNum >= 3) {
          clearInterval(this.countTimer)
          this.graphic_access_record_id = ''
        }
      })
      return reFun
    },
    // 获取统计数据
    getSummaryInfo() {
      const params = { act_type: this.act_type, course_id: this.course_id }
      getSummaryData(params).then(res => {
        this.zanAndcollect = {
          ...this.zanAndcollect, ...res
        }
      })
    },
    // 点赞、收藏状态
    getZanAndCollectStatus() {
      const params = { act_type: this.act_type, course_id: this.course_id }
      checkPraised(params).then((res) => {
        this.zanAndcollect.isZan = res
      })
      checkFavorited(params).then(res => {
        this.zanAndcollect.isCollect = res
      })
    },
    // 点赞、收藏前置
    handleLikeOrFav(scene) {
      const params = { act_type: this.act_type, course_id: this.course_id }
      if (scene === 1) {
        // 点赞/取消点赞
        checkPraised(params).then(res => {
          const PAndFCommonAPI = res ? deletePraise : addPraise
          const tip = res ? this.$langue('Mooc_Common_Alert_CancelPraiseSucessed', { defaultText: '取消点赞成功' }) : this.$langue('Mooc_Common_Alert_PraiseSucessed', { defaultText: '点赞成功' })
          this.zanAndcollect.praise_count = res ? (this.zanAndcollect.praise_count === null || this.zanAndcollect.praise_count === 0 ? 0 : this.zanAndcollect.praise_count - 1) : this.zanAndcollect.praise_count + 1
          this.handlerCommonInt(PAndFCommonAPI, params, tip)
        })
      } else {
        // 收藏/取消收藏
        checkFavorited(params).then(res => {
          const PAndFCommonAPI = res ? deleteFavorite : addFavorited
          const tip = res ? this.$langue('Mooc_Common_Alert_CancelCollectSucessed', { defaultText: '取消收藏成功' }) : this.$langue('Mooc_Common_Alert_CollectSucessed', { defaultText: '收藏成功' })
          this.zanAndcollect.fav_count = res ? (this.zanAndcollect.fav_count === null || this.zanAndcollect.fav_count === 0 ? 0 : this.zanAndcollect.fav_count - 1) : this.zanAndcollect.fav_count + 1
          this.handlerCommonInt(PAndFCommonAPI, params, tip)
        })
      }
    },
    // 点赞、收藏
    handlerCommonInt(PAndFCommonAPI, params, tip) {
      PAndFCommonAPI(params).then(data => {
        if (PAndFCommonAPI === addPraise || PAndFCommonAPI === deletePraise) this.zanAndcollect.isZan = PAndFCommonAPI === addPraise ? Boolean(true) : Boolean(false)
        if (PAndFCommonAPI === addFavorited || PAndFCommonAPI === deleteFavorite) this.zanAndcollect.isCollect = PAndFCommonAPI === addFavorited ? Boolean(true) : Boolean(false)
        if (PAndFCommonAPI === addPraise || PAndFCommonAPI === addFavorited) {
          if (data.credit && data.credit !== '0') {
            this.isShowJfTip = true
            setTimeout(() => {
              this.isShowJfTip = false
            }, 6000)
            this.$message.success(`${tip}，${this.$langue('Mooc_Common_Alert_CommonPoint', { point: +data.credit, defaultText: `通用积分+${+data.credit}` })}`)
          } else this.$message.success(tip)
        } else this.$message.success(tip)
      })
    },
    // 点击标签跳转
    searchGo(item) {
      let href = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_V8_HOST_WOA : process.env.VUE_APP_V8_HOST
      href = `${href}/mat/user/search?keywords=${item.label_name}&from_page=ql新首页&type=label`
      window.open(href)
    },
    timeToSec(timeStr) {
      let time = timeStr.split(',')[0]
      let s = ''
      const hour = time.split(':')[0]
      const min = time.split(':')[1]
      const sec = time.split(':')[2]
      let misecond = timeStr.split(',')[1].substr(0, 2)
      misecond = Number(parseInt(misecond) / 100)
      s = Number(hour * 3600) + Number(min * 60) + Number(sec)
      return (s * 100 + misecond * 100) / 100
    },
    setCommentCount() { }
    
    // 跳转时间
    // toCurrentTime() {
    //   this.isCurrentTimeShow = false
    //   this.$refs.vidioDOM.vedioPlayer.currentTime(this.playTime)
    // },
    // changePlayStatus(status) {
    //   if (status === 'play') {
    //     this.$refs.vidioDOM.vedioPlayer.play()
    //   } else if (status === 'pause') {
    //     this.$refs.vidioDOM.vedioPlayer.pause()
    //   }
    // },
  },
  filters: {
    nameFilter(value) {
      let limitNum = 40
      if (value.length > limitNum) {
        return value.substr(0, limitNum / 2) + '...' + value.substr(value.length - limitNum / 2)
      }
      return value
    }
  },
  beforeDestroy() {
    clearInterval(this.countTimer)
    this.countTimer = null
    clearTimeout(this.clearTimeId)
    this.clearTimeId = null
    clearTimeout(this.clear3sTimeId)
    this.clear3sTimeId = null
    MoocJs.removeEvent()
  }
}
</script>
<style lang="less">
</style>
<style lang="less" scoped>
.net-page {
  .contain-main {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    padding-top: 10px;

    .left {
      background-color: #fff;
      padding: 24px;
      margin-bottom: 20px;

      .content-top {
        .name {
          display: flex;
          align-items: center;
          color: #000000;
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 22px;
          word-break: break-word;

          span:last-child {
            flex: 1;
          }
        }

        .tag {
          padding: 0 6px;
          border-radius: 2px;
          margin-right: 10px;
        }

        .word {
          color: #0052D9;
          border: 1px solid #0052D9;
        }

        .geek-time {
          height: 20px;
          margin-right: 10px;
        }

        .info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 16px;

          .info-left,
          .info-right {
            display: flex;
          }

          .info-left {

            .create,
            .time {
              text-overflow: ellipsis;
              overflow: hidden;
              word-break: break-all;
              white-space: nowrap;
              color: rgba(0, 0, 0, 0.6);
            }

            .time {
              width: 140px;
            }

            .create {
              margin-left: 16px;
            }

            .info-classify,
            .info-label {
              display: flex;

              .label {
                flex-shrink: 0;
                color: rgba(0, 0, 0, 0.4) !important;
              }

              p {
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                white-space: nowrap;

                span {
                  cursor: pointer;
                  color: #3464E0;

                  i {
                    font-style: normal;
                  }
                }
              }
            }

            .info-label {
              line-height: 20px;

              .tag-list-box {
                display: flex;
                flex-wrap: wrap;
              }

              .tag-value {
                background-color: rgba(235, 239, 252, 1);
                height: 20px;
                font-size: 12px;
                color: rgba(0, 82, 217, 1);
                padding: 4px;
                border-radius: 2px;
                display: inline-block;
                margin-right: 12px;
                line-height: 10px;
                cursor: pointer
              }
            }

            .fh {
              color: rgba(0, 0, 0, 0.4);
            }
          }

          .info-right {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.4);
            flex-shrink: 0;

            span {
              display: flex;
              align-items: center;
              justify-content: center;
              width: fit-content;
              margin-right: 18px;
            }

            span:nth-child(2),
            span:nth-child(4),
            .editor {
              cursor: pointer;
            }

            .goback {
              cursor: pointer;
              color: #3464E0;
            }

            i {
              display: inline-block;
              width: 14px;
              height: 14px;
            }

            .icon-view {
              background: url("~@/assets/img/watch.png") no-repeat center /cover;
            }

            .icon-zan {
              background: url("~@/assets/img/zan1.png") no-repeat center /cover;
            }

            .icon-zan-active {
              color: #3464e0;

              i {
                background: url("~@/assets/img/zan1-active.png") no-repeat center /cover;
              }
            }

            .icon-comment {
              background: url("~@/assets/img/comment.png") no-repeat center /cover;
            }

            .icon-collect {
              background: url("~@/assets/img/fav2.png") no-repeat center /cover;
            }

            .icon-collect-active {
              color: #3464e0;

              i {
                background: url("~@/assets/img/fav2-active.png") no-repeat center /cover;
              }
            }

            .icon-edit {
              background: url("~@/assets/img/edit.png") no-repeat center / cover;
            }

            .icon-add {
              background: url("~@/assets/img/add.png") no-repeat center / cover;
            }

            .icon-share {
              background: url("~@/assets/img/share.png") no-repeat center /cover;
            }

            .icon-mobile {
              background: url("~@/assets/img/mobile.png") no-repeat center /cover;
            }

            .jf-icon {
              background: url("~@/assets/img/integral-icon.png") no-repeat center / cover;
              display: block;
              width: 20px;
              height: 20px;
              margin-right: 4px;
            }

            .jf-tip {
              color: #ff7548;
              position: absolute;
              right: 20px;
              top: -22px;
              display: flex;
              align-items: center;
            }
          }

          .right-icons {
            color: rgba(0, 0, 0, 0.6);

            span {
              margin-right: 16px;
              cursor: pointer;
            }
          }
        }

        .info1{
          height: 22px;
          line-height: 22px;
          .info-right {
            position: relative;
          }
        } 

        .info2 {
          border-bottom: solid 1px #eeeeee;
          padding-bottom: 8px;
          margin-bottom: 20px;
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
        }

        .video-box {
          border-radius: 4px;
          background-color: #F8F8F8;
          border: solid 1px #ECECEC;

          :deep(.el-image) {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .default-icon-picture {
              display: inline-block;
              width: 400px;
              height: 300px;
              background: url('~@/assets/img/default_bg_img.png') no-repeat;
            }
          }
        }
        .video-main {
          width: 100%;
          .geek-task-iframe {
            // width: 100%;
            // height: 100%;
          }
          .video-fullscreen {
            width: 100vw;
            height: 100vh;
            position: absolute;
            top: 0;
            left: 0;
            z-index: 100;
          }
        }
      }

      .content-bottom {
        margin-top: 36px;
        .en-caption-box {
          .caption-item {
            span:first-child {
              margin-right: 55px !important;
            }
          }
        }
        .caption-box {
          position: relative;
          border: 1px solid rgba(238, 238, 238, 1);
          border-radius: 3px;

          .caption-title {
            height: 36px;
            line-height: 36px;
            padding-left: 19px;
            color: rgba(0, 0, 0, 0.8);
            background-color: #F5F7F9;

            span:first-child {
              margin-right: 40px;
            }
          }

          .caption-content {
            height: 380px;
            padding: 16px 0 0 20px;
            overflow-y: auto;

            .caption-item {
              display: flex;
              align-items: baseline;
              margin-bottom: 16px;

              span {
                // width: 40px;
                margin-right: 26px;
                display: inline-block;
                color: rgba(0, 0, 0, 0.6);
              }

              p {
                color: rgba(0, 0, 0, 0.4);
                line-height: 22px;
                letter-spacing: 0.28px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
              }
            }

            .caption-item-active {

              span,
              p {
                color: #0052D9;
              }
            }
          }

          .caption-content-shadow {
            width: 100%;
            height: 100px;
            position: absolute;
            bottom: 0;
            opacity: 0.5299999713897705;
            background: linear-gradient(180deg, rgba(217, 217, 217, 0) 0%, rgba(216, 216, 216, 0.4) 100%);
          }
        }
      }

      :deep(.el-tabs) {
        margin-bottom: 20px;

        .el-tabs__header {
          border-bottom: solid 1px #eeeeee;
          margin: 0px;
        }

        .el-tabs__item {
          color: rgba(0, 0, 0, 0.4);
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 10px
        }

        .is-active {
          color: #0052D9 !important;
          font-weight: 700;
        }
      }
    }

    .right {
      width: 272px;
      height: 100%;
      margin-left: 20px;

      .mbt-20 {
        margin-bottom: 20px;
      }
    }
  }
}
.change-lang-main {
  width: 64px;
  height: 64px;
  border-radius: 6px;
  background-color: #fff;
  text-align: center;
  border: 0.5px solid  #DCDCDC;
  box-shadow: 0 8px 10px -5px #00000014, 0 16px 24px 2px #0000000a, 0 6px 30px 5px #0000000d;
  cursor: pointer;
  margin-left: 20px;
  img {
    width: 24px;
    height: 24px;
    margin-top: 12px;
  }
  .change-lang-title {
    color: #00000099;
    line-height: 20px;
    font-size: 12px;
    margin-top: 2px;
  }
}
@media screen and (max-width: 1660px) {
  .contain-main .left {
    width: 866px !important;

    .info {
      .info-left {

        .info-classify p,
        .info-label p {
          max-width: 196px !important;
        }

        .create {
          max-width: 342px;
        }
      }
    }

    .video-box,
    .scorm-box {
      width: 818px;
      height: 460px;
    }

    // .tag-list-box {
    //   width: 630px;
    // }
  }

  .create {
    max-width: 420px;
  }
}

@media screen and (min-width: 1661px) {
  .contain-main .left {
    width: 1148px !important;

    .info {
      .info-left {

        .info-classify p,
        .info-label p {
          max-width: 360px !important;
        }

        .create {
          max-width: 624px;
        }
      }
    }

    .video-box,
    .scorm-box {
      width: 1100px;
      height: 619px;
    }

    // .tag-list-box {
    //   width: 850px;
    // }
  }

  .create {
    max-width: 690px;
  }
}
</style>
