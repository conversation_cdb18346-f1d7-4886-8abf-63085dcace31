<template>
  <div :class="[ form.catalogue ? 'draft-page' : 'article-page' ]">
    <!-- 新增 -->
    <div class="editor-header">
      <input class="title" v-model="form.file_show_name" maxlength="50" placeholder="请输入标题" />
      <el-input 
        class="desc" 
        v-model="form.file_desc" 
        type="textarea" 
        rows="2" 
        maxlength="200" 
        resize="none"
        placeholder="请输入简介/导语（可选填）"
      >
      </el-input>
    </div>
    <div class="editor-body">
      <sdc-mce-editor
        ref="article" 
        :env="editorEnv" 
        :content="form.content" 
        :catalogue.sync="editorConfig.catalogue" 
        :urlConfig="editorConfig.urlConfig" 
        @getWordCount="getWordCount"
        @catalogueVisible.self="catalogueVisible"
        :insertItems="insertItems"
      />
    </div>
    <div class="bottom-opera-box">
      <div class="current-words-box">
        <span>当前字数：{{ form.word_num }}</span>
        <span class="red-time">预计所需阅读时间{{ forecastReadTime }}分钟</span>
        <span>
          每5分钟自动保存草稿，上次保存时间：{{ form.updated_at || '--' }}
        </span>
      </div>
      <div class="bottom-opera-item">
        <div class="form-item">
          <span class="label author-label">素材来源：</span>
          <el-radio-group v-model="form.content_type">
            <el-radio label="UGC">组织/项目</el-radio>
            <el-radio label="PGC">个人分享</el-radio>
          </el-radio-group>
        </div>
        <div class="form-item author-form">
          <span class="label">授权使用：</span>
          <sdc-staff-selector 
            multiple
            ref="adminsSelectorRef" 
            v-model="form.admins"
            size="small" 
            :props="adminProps" 
            placeholder="请选择可使用此素材的人员"
            @change="changeStaff"
          />
        </div>
        <convention-confirm v-model="conventionChecked" />
        <div class="form-item last-btn-form">
          <el-button @click="onCancel">取消</el-button>
          <el-button @click="approveSafe(false)" type="primary" :disabled="!conventionChecked || (editData && editData.info_sec_status === 0)">确定</el-button>
        </div>
      </div>
    </div>
    <!-- 信息审核再次编辑异步变化弹窗 -->
    <informationSafetyDialog :isShow.sync="informationSafetyShow" @safeConfirm="onSubmit(false)"/>
  </div>
</template>
  
<script>
import { saveMaterial, articlelDraftData, metaerialDetail } from '@/config/mooc.api.conf.js'
import { operatesignature, getContentInfo } from 'config/api.conf'
import { getDate } from '@/utils/tools.js'
import { mapState } from 'vuex'
import conventionConfirm from '@/views/components/convention-confirm.vue'
import informationSafetyDialog from '@/components/information-safety-dialog'
export default {
  components: {
    conventionConfirm,
    informationSafetyDialog
  },
  data() {
    return {
      editorEnv: process.env.NODE_ENV,
      insertItems: [
        'link',
        'table',
        'image',
        'profile',
        'embed',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorConfig: {
        catalogue: false, // false-显示 true-隐藏
        options: { // 非必传字段
          selector: '#graphic__editor'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      },
      radio: 3,
      adminProps: {
        staffID: 'staff_id',
        staffName: 'staff_name'
      },
      formData: {},
      editData: {},
      forecastReadTime: 0,
      wordsNumber: 0,
      saveDraftTimer: null,
      form: {
        admins: [],
        file_desc: '',
        content_type: 'UGC',
        content: '',
        word_num: 0,
        file_show_name: '',
        updated_at: '',
        catalogue: false
      },
      conventionChecked: false,
      informationSafetyShow: false,
      saveMaterialSetting: {
        act_type: 21,
        columns: [
          {
            column_code: 'file_show_name',
            column_name: '素材名称',
            column_type: 'text',
            call_type: ['sync'],
            manual_review: false
          },
          {
            column_code: 'file_desc',
            column_name: '素材简介',
            column_type: 'text',
            call_type: ['sync'],
            manual_review: false
          },
          {
            column_code: 'content',
            column_name: '正文',
            column_type: 'richText',
            call_type: ['async'],
            manual_review: false
          }
        ]
      }
    }
  },
  computed: {
    ...mapState(['userLimitInfo']),
    file_id() {
      return this.$route.query.file_id
    }
  },
  mounted() {
    this.createSaveDraftTimer()
    // 编辑
    this.queryData()
  },
  destroyed() {
    this.saveDraftTimer = null
    clearInterval(this.saveDraftTimer)
  },
  methods: {
    // 信息安全审核
    approveSafe(val) {
      const { file_id } = this.$route.query
      if (file_id && this.$refs['article'].getContent() !== this.form.content) {
        this.informationSafetyShow = true
        return
      }
      this.onSubmit(val)
    },
    queryData() {
      const { file_id } = this.$route.query
      let commonApi = file_id ? metaerialDetail : articlelDraftData
      commonApi(file_id).then((res) => {
        this.editData = res
        const { 
          admins,
          file_show_name,
          content,
          file_desc,
          content_type,
          updated_at,
          catalogue,
          word_num
        } = res
        // 文章获取目录内容
        if (res?.content_id) {
          this.getContentCenter(res.content_id)
        }
        this.processValue = 100
        // 表单数据
        this.form = {
          admins,
          content_type,
          file_show_name,
          content,
          file_desc,
          updated_at,
          catalogue: !!catalogue,
          word_num: word_num || 0
        }
        this.editorConfig.catalogue = !catalogue
        // 授权人员
        if (this.form.admins?.length) {
          this.$refs.adminsSelectorRef.setSelected(this.form.admins)
        }
      })
    },
    onSubmit(submitType = false) {
      const { file_id } = this.$route.query
      if (this.form?.admins?.length) {
        this.form.admins = this.form.admins.map((e) => {
          return {
            share_type: 1,
            staff_id: e.staff_id,
            staff_name: e.staff_name,
            file_id: file_id ? this.editData.file_id : null
          }
        })
      }
      const params = {
        file_type: 'Article',
        ...this.form,
        content: this.$refs['article'].getContent(),
        draft: !!submitType,
        file_id: this.editData?.file_id || null
      }
      delete params.updated_at // 更新时间不传

      window.$informationReview && window.$informationReview.contentReview(params, this.saveMaterialSetting).then(res => {
        const { success, status_code, column_name, data } = res
        if (!success) return
        if (status_code === 2) {
          this.saveFrom(params, submitType)
        } else if ([-1, 1].includes(status_code)) {
          let msg = '内容已提交后台审核，请耐心等待，留意企微“小腾老师”机器人消息提醒。<br/>如有疑问，可联系graywu。'
          if (status_code === 1) {
            msg = `填写${column_name || '***'}信息中包含敏感内容，无法保存，请仔细检查，修改后再提交。<br/>如有疑问，可联系graywu。`
          }
          window.$informationReview.createMessageBox({
            title: '提示',
            content: msg,
            confirmText: status_code === 1 ? '返回修改' : '确认',
            onConfirm: () => {
              if (status_code === -1) {
                this.saveFrom(data, submitType)
              }
            }
          })
        }
      }).catch(err => {
        console.log(err, 'rrr')
      })
    },
    saveFrom(params, submitType) {
      saveMaterial(params).then((res) => {
        const { file_id } = this.$route.query
        const msg = file_id ? '创建成功' : '保存成功'
        this.$message.success(msg)
        this.form.updated_at = getDate() // 获取保存时间
        window.opener.workReConnect()
        if (submitType !== 'draft') {
          this.onCancel()
        }
      })
    },
    // 内容中心鉴权并获取文件内容
    getContentCenter(contentId) {
      operatesignature({
        app_id: 'QLearningService',
        content_id: contentId,
        corp_name: 'tencent',
        operate: 'visit'
      }).then(signature => {
        if (signature) {
          getContentInfo(contentId, {
            signature: signature,
            app_id: 'QLearningService'
          }).then(res => {
            this.form.content = res?.file_info?.content || ''
          })
        }
      })
    },
    onCancel() {
      setTimeout(() => {
        window.close()
      }, 1000)
    },
    // 授权人员
    changeStaff(data) {
      this.form.admins = data
    },
    getWordCount(val) {
      this.form.word_num = val
      // 以一分钟看600个字计算
      this.forecastReadTime = val > 0 ? (val % 600 === 0 ? val / 600 : (Math.ceil(val * 10 / 600) / 10).toFixed(1)) : 0
    },
    // 每五分钟自动保存一次
    createSaveDraftTimer() {
      let _this = this
      this.saveDraftTimer = setInterval(() => {
        _this.onSubmit('draft') // 草稿保存
      }, 300000) // 300000
    },
    // goGraphicPreview(graphic_id) {
    //   const { href } = this.$router.resolve({
    //     name: 'preview',
    //     query: {
    //       graphic_id: graphic_id,
    //       scene: 1
    //     }
    //   })
    //   window.open(href)
    // },
    catalogueVisible(val) {
      console.log('wenzhanga', val)
      this.form.catalogue = val
    }
  }
}
</script>
<style lang="less">
.main-content-manage {
  margin: unset;
  background-color: unset;
}
</style>
<style lang="less" scoped>
.editor-header {
  // height: 12%;
  padding: 8px 20px;
  background-color: #fff;
  position: relative;

  input {
    width: 100%;
    display: block;
    color: rgba(153, 153, 153, 1);
    border: none;
  }

  input::-webkit-input-placeholder {
    color: rgba(153, 153, 153, 1);
  }

  :-moz-placeholder {
    color: rgba(153, 153, 153, 1);
  }

  ::-moz-placeholder {
    color: rgba(153, 153, 153, 1);
  }

  input:-ms-input-placeholder {
    color: rgba(153, 153, 153, 1);
  }

  input::-ms-input-placeholder {
    color: rgba(153, 153, 153, 1);
  }

  .title {
    padding-bottom: 16px;
    font-size: 28px;
    font-weight: bold;
    border-bottom: solid 1px rgba(238, 238, 238, 1);
    color: #000000e6;
  }

  .desc {
    margin-top: 16px;
    font-size: 16px;
    font-weight: 400;

    :deep(.el-textarea__inner) {
      color: #999999;
      font-size: 16px;
      line-height: 20px;
      padding: unset;
      border: none;
    }
  }
}
.current-words-box {
  color: #00000066;
  font-size: 14px;
  line-height: 22px;
  padding: 24px;
  border-top: 1px solid #eeeeeeff;
  background-color: #fff;
  .red-time {
    margin-left: 14px;
    margin-right: 24px;
  }
}
.draft-page { // 关闭样式
  height: 100vh;
  position: relative;
  min-width: 1210px;
  .editor-body {
    :deep(.editor__area) {
      // 隐藏富文本图片添加链接功能
      .menu_item[title = '添加链接']{
        display: none;
      }
    }
  }
}
.article-page { // 开启样式
  height: 100vh;
  position: relative;
  min-width: 1210px;
  .editor-header{
    position: relative;
    left: 210px;
    width: calc(100% - 210px);
  }
  .editor-body {
    height: 88%;
    :deep(.editor__area) {
      height: 100%;
      // 隐藏富文本图片添加链接功能
      .menu_item[title = '添加链接']{
        display: none;
      }
    }
  }
  :deep(.editor__area) {
    .tox-sidebar-wrap {
      margin-left: -5px;

      .tox-sidebar {
        position: sticky !important;
        overflow-x: unset !important;
        padding-right: 100px;
        margin-top: -158px !important;
        width: 0 !important;
        padding-right: unset !important;

        .tox-sidebar__slider {
          
          position: sticky !important;
          top: 10px !important;
          overflow-x: unset !important
        }
      }
    }
  }
  .current-words-box { 
    margin-left: 210px
  }
}
.bottom-opera-box {
  position: fixed;
  bottom: 0px;
  right: 0px;
  background: #fff;
  width: 100%;
  padding-right: 20px;
  padding-bottom: 20px;
  .bottom-opera-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-left: 24px;
    .form-item {
      display: flex;
      align-items: center;
      .label {
        color: #00000099;
      }
      .el-button {
        width: 104px;
      }
      .el-button + .el-button {
        margin-left: 20px;
      }
      .author-label::before {
        content: '*';
        color: red;
        margin-right: 2px;
      }
    }
    .author-form {
      margin-left: 52px;
      margin-right: 24px;
    }
  }
}

:deep(.tox-editor-header) {
  position: sticky !important;
  top: 0px;
  transform: unset;
  background: #fff;
  text-align: left;
}

:deep(.el-input .el-input__inner) {
  padding: 0 15px;
}
</style>
