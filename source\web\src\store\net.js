const state = {
  articleFullScreen: true, // 全屏
  showPicture: true, // 自定义画中画
  volume: 1, // 声音
  playbackRate: 1, // 播放速率
  playStatus: false
}
const mutations = {
  setArticleFull(state, data) {
    state.articleFullScreen = data
  },
  setPictureShow(state, data) {
    state.showPicture = data
  },
  setVideoVolume(state, data) {
    state.volume = data
  },
  setVidePlayRate(state, data) {
    state.playbackRate = data
  },
  setVideoPlay(state, data) {
    state.playStatus = data
  }
}
const actions = {

}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
