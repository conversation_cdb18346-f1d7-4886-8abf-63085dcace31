<template>
  <el-dialog title="" custom-class="none-border-dialog statement-dialog" :visible="visible" width="700px"
    :close-on-click-modal="false" :show-close="false">
    <div class="dialog-body">
      <span class="title">信息安全声明</span>
      <div class="statement-content">
        <p>1、QL平台的所有原创内容（包含但不限于：文档、图片、评论、音视频文件等信息）均属于
          <span class="tips">公司资产，仅用于内部学习交流。</span>鼓励原创分享，制作上传、转载的内容
          <span class="tips">请确保不违反任何法律法规规定，AI制作内容符合法律法规规定和公司内部要求，所有内容均不得侵犯任何第三方合法权益</span>，
          否则上传人应负责处理因此引起的纠纷并承担所有责任。
        </p>
        <p>2、请确保上传的照片不违反法律法规规定，不侵犯任何第三人的合法权益；人像照片属于敏感个人信息，如上传即同意基于特定的功能使用该人像照片，
          <span class="tips">请勿未经授权，违规使用、上传他人的人像照片，否则上传人需承担所有责任。</span>
        </p>
        <p>3、请务必遵守 <el-link type="danger"
            href="http://policy.woa.com/document/preview?documentId=986b51827d064d43991ca68a7450e425"
            target="_blank">《商业秘密保护与管理规范》</el-link>
          保护公司商业秘密及信息安全。未经授权批量拉取内容、私自外传内容（包含但不限于：复制、下载、拍照、截图、上传页面等操作）
          属于违规行为。无论当事人是否在职，公司将按照
          <el-link type="danger"
            href="http://policy.woa.com/document/preview?documentId=B93922B4-E7A4-4DA7-B6D0-485BE66EAEB9"
            target="_blank">《防止办公网内高危行为管理办法》</el-link> 和
          <el-link type="danger" href="http://policy.woa.com/document/preview?documentId=27fe8954d72242908e57b3d5b330a9c8"
            target="_blank">《腾讯阳光行为准则》</el-link>
          对违规行为进行处理，并对情节恶劣者保留追究法律责任的权利。
        </p>
        <p>4、QL平台内容的<span class="tips">权限默认是全员开放</span>的，即公司员工可访问对其开放的信息和内容。如有需要请及时对分享的内容观看权限进行设置。</p>
      </div>
    </div>
    <el-checkbox v-model="checked">我已阅读并同意以上规则，同意遵守公司相关管理规范及制度</el-checkbox>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" type="primary" @click="closeDialog">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      checked: false
    }
  },
  methods: {
    closeDialog() {
      if (!this.checked) {
        this.$message.warning('请阅读并同意以上规则')
        return
      }
      // 设置为有权限-信息安全声明
      localStorage.setItem('tencent_qlearnging_netcource_ai_statement', true)
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.statement-dialog {
  .tips {
    color: red;
  }

  .title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 16px;
    display: inline-block;
  }

  .statement-content {
    line-height: 20px;
    margin-bottom: 16px;
  }

  .el-checkbox__label {
    color: #333;
  }
}
</style>
