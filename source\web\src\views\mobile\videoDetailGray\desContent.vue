<template>
  <div class="des-main-container">
    <div class="des-main">
      <div class="title-box des-main-margin">
        <span class="title-left">
          <span class="tag">{{ titleTagName(courseData.course_type) }}</span>
          <span class="title">{{ courseData.course_name }}</span>
        </span>
      </div>
      <div class="mid-tag-box des-main-margin">
        <!-- <span class="score">{{ courseData.avg_score ? `${courseData.avg_score}分` : '暂无评分'}}</span> -->
        <!-- 标签展示 -->
        <template v-if="courseData.net_course_id">
          <sdc-label-show-mob
            v-if="isPreview"
            ref="labelShow"
            :labelNodeEnv="labelNodeEnv"
            :actType="2"
            :courseId="course_id"
            :isPreview="true"
            :showAll="true"
            :previewLbael="previewLbael"
            :userInfo="userInfo"
            >
          </sdc-label-show-mob>
          <sdc-label-show-mob
          v-else
          ref="labelShow"
          :labelNodeEnv="labelNodeEnv"
          :actType="2"
          :courseId="course_id"
          :isH5="false"
          :isMock="false"
          :showAll="true"
          :userInfo="userInfo"
          @toSearchPage="toSearchPage"
          >
        </sdc-label-show-mob>
        </template>
      </div>
      <active-jump class="mgb-16" :dtArg="dtArg"></active-jump>
      <!-- 简介 -->
      <div :class="['des-box', 'des-main-margin', {'overflow-desc': descOverflow}]" >
        <div class="des-content-detail overflow-l3">{{ customDes }}</div>
        <div class="more-bg" v-if="descOverflow">
          <span class="more-btn"
          @click="desDetailShow=true"
          :dt-remark="dtMore('remark', '更多', '简介')"
          :dt-areaid="dtMore('areaid', '更多', '简介')"
          :dt-eid="dtMore('eid', '更多', '简介')"
          >更多</span>
        </div>
      </div>
      <!-- 章节 -->
      <div class="chapter-box des-main-margin" v-if="chapterSummaryList.length">
        <div class="chapter-title">
          <span class="title-num">章节·共{{chapterSummaryList.length || 0}}节</span>
          <span
            class="title-btn"
            :dt-remark="dtMore('remark', '查看全部', '章节')"
            :dt-areaid="dtMore('areaid', '查看全部', '章节')"
            :dt-eid="dtMore('eid', '查看全部', '章节')"
            @click="isShowChapter = true">
            查看全部
            <span class="right-icon"></span>
          </span>
        </div>
        <swiper :options="swiperOption">
          <swiper-slide
          v-for="(e, index) in chapterSummaryList"
          :key="index"
          class="item-chapter-swiper"
          :data-i="index"
          :dt-areaid="dtChapter('areaid', e)"
          :dt-remark="dtChapter('remark', e)"
          >
            <div
            :class="['chapter-item-title', 'overflow-l2',
            {'active-item-title': captionCurTime >= e.startTime && captionCurTime < e.endTime}]"
            :data-i="index"
            >
              <span class="play-icon" v-if="captionCurTime >= e.startTime && captionCurTime < e.endTime"></span>
              <span :data-i="index" class="title overflow-l2">{{ e.chapter_title }}</span>
            </div>
          </swiper-slide>
        </swiper>
      </div>
      <!-- 课单 -->
      <courseCard class="des-main-margin" v-if="isShowCourse" :courseData="courseData"></courseCard>
      <!-- 专区 -->
      <courseCard class="des-main-margin" v-if="isSpecialArea" fromType="isSpecial" :courseData="courseData"></courseCard>
    </div>
    <!-- 推广--猜你喜欢 -->
    <div class="like-popularize-box" v-if="mergeList.length && ($route.query.from !== 'mooc')">
      <detailCard :courseData="courseData" v-for="(item, index) in mergeList" :key="index" :isMinute="true" :cardData="item" @handleToPath="toPath" entry="advertLike"></detailCard>
      <div
      class="jump-btn"
      @click="toPage"
      :dt-remark="dtMore('remark', '去首页探索更多好课', '猜你喜欢')"
      :dt-areaid="dtMore('areaid', '去首页探索更多好课', '猜你喜欢')"
      :dt-eid="dtMore('eid', '去首页探索更多好课', '猜你喜欢')"
      >去首页探索更多好课</div>
    </div>
    <!-- 章节 -->
    <chapterSummary
    :isShowChapter.sync="isShowChapter"
    :chapterSummaryList="chapterSummaryList"
    :captionCurTime="captionCurTime"
    @toChapterPosition="toChapterPosition"
    :courseData="courseData"
    >
    </chapterSummary>
    <!-- 简介详情内容 -->
    <van-popup class="desc-popup-container"  get-container="body" v-model="desDetailShow" :overlay="false" position="bottom">
      <div class="body">
        <div class="top-title">
          <span class="placeholder"></span>
          <span class="title overflow-l1">详情内容</span>
          <span class="close-icon" @click="desDetailShow=false"></span>
        </div>
        <div class="content">
          <div class="title-box">
            <span class="tag">{{ titleTagName(courseData.course_type) }}</span>
            <span class="title">{{ courseData.course_name }}</span>
          </div>
          <div class="top-view">
            <div class="item-view">
              <span class="icon view-icon"></span>
              <span>{{ courseData.view_count || 0 }}</span>
            </div>
            <div class="item-time">
              <span class="time-icon icon"></span>
              <span>{{ courseData.created_at || '-' }}</span>
            </div>
          </div>
          <div class="des-tag-box clearfix">
            <!-- <span class="score">{{ courseData.avg_score ? `${courseData.avg_score}分` : '暂无评分'}}</span> -->
            <!-- 标签展示 -->
            <template v-if="courseData.net_course_id">
              <sdc-label-show-mob
                v-if="isPreview"
                ref="labelShow"
                :labelNodeEnv="labelNodeEnv"
                :actType="2"
                :courseId="course_id"
                :isPreview="true"
                :previewLbael="previewLbael"
                :showAll="true"
                >
              </sdc-label-show-mob>
              <sdc-label-show-mob
              v-else
              ref="labelShow"
              :labelNodeEnv="labelNodeEnv"
              :actType="2"
              :courseId="course_id"
              :isH5="false"
              :isMock="false"
              :showAll="true"
              @toSearchPage="toSearchPage"
              >
            </sdc-label-show-mob>
            </template>
          </div>
          <!-- 简介 -->
          <div class="des-box">
            <span class="title">简介</span>
            <div class="des-content" v-html="courseData.course_desc"></div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import {
  isSysBusy,
  guessLikeAPI,
  popularizeApi,
  getPopularizeVisiList,
  setPopularizeVisiList
} from 'config/api.conf'
import courseCard from './child/courseCard.vue'
import chapterSummary from './child/chapterSummary.vue'
import detailCard from './child/detailCard.vue'
import { mapState } from 'vuex'
import ActiveJump from '@/views/components/activeJump.vue'
export default {
  components: {
    courseCard,
    chapterSummary,
    detailCard,
    ActiveJump
  },
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    },
    chapterSummaryList: {
      type: Array,
      default: () => ([])
    },
    isPreview: { // 是否是预览
      type: Boolean,
      default: false
    },
    studyRecordQuery: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      labelNodeEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test',
      showAddCourse: false,
      activePrise: false,
      activefav: false,
      guessLikeList: [],
      advertisingList: [],
      advertisingRecord: {},
      isShowChapter: false,
      desDetailShow: false,
      descOverflow: false,
      moreS: 0,
      customDes: ''
    }
  },
  computed: {
    ...mapState(['userInfo']),
    previewLbael() {
      return this.courseData.course_labels || []
    },
    teacher_name() {
      let { inner_teacher_names, out_teacher_names, creator_name } =
        this.courseData
      let name = ''
      if (inner_teacher_names?.length) {
        if (inner_teacher_names.length > 1) {
          name =
            inner_teacher_names[0].teacher_name + this.$langue('NetCourse_Teachers', { count: inner_teacher_names.length - 1, defaultText: `等${inner_teacher_names.length - 1}人` })
        } else {
          name = inner_teacher_names[0].teacher_name
        }
      }
      if (out_teacher_names?.length) {
        if (out_teacher_names.length > 1) {
          name =
            out_teacher_names[0].teacher_name + this.$langue('NetCourse_Teachers', { count: inner_teacher_names.length - 1, defaultText: `等${inner_teacher_names.length - 1}人` })
        } else {
          name = out_teacher_names[0].teacher_name
        }
      }
      // name = name.slice(0, -2)
      return name || creator_name
    },
    course_id() {
      return this.$route.query.course_id || ''
    },
    titleTagName() {
      return (val) => {
        if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(val)) {
          return this.$langue('Mooc_Common_ResourceType_Video', { defaultText: '视频' })
        } else if (val === 'Audio') {
          return this.$langue('Mooc_Common_ResourceType_Audio', { defaultText: '音频' })
        } else if (val === 'Article') {
          return this.$langue('Mooc_Common_ResourceType_Article', { defaultText: '文章' })
        } else if (val === 'Doc') {
          return this.$langue('Mooc_Common_ResourceType_Doc', { defaultText: '文档' })
        } else if (val === 'Scorm') {
          return 'scorm'
        } else if (val === 'Flash') {
          return this.$langue('Mooc_Common_ResourceType_Zip', { defaultText: '压缩包' })
        }
      }
    },
    mergeList() {
      if (!this.advertisingList.length) return this.guessLikeList
      if (!this.guessLikeList.length) return this.advertisingList
      const maxLength = this.advertisingList.length + this.guessLikeList.length
      const result = []
      let advIndex = 0
      let guessIndex = 0
      for (let i = 0; i < maxLength; i++) {
        if (i % 2 === 0) { // 偶数
          if (advIndex < this.advertisingList.length) {
            result.push(this.advertisingList[advIndex++])
          } else if (guessIndex < this.guessLikeList.length) {
            result.push(this.guessLikeList[guessIndex++])
          }
        } else { // 奇数
          if (guessIndex < this.guessLikeList.length) {
            result.push(this.guessLikeList[guessIndex++])
          } else if (advIndex < this.advertisingList.length) {
            result.push(this.advertisingList[advIndex++])
          }
        }
      }
      return result.slice(0, 10)
    },
    isShowCourse() {
      return this.$route.query.area_id && this.$route.query.from === 'CourseList'
    },
    isSpecialArea() {
      // 490是首页不显示
      return this.$route.query.from === 'SpecialArea' && this.$route.query.area_id && this.$route.query.area_id !== '490'
    },
    // 当前播放时间
    captionCurTime() {
      return this.studyRecordQuery.my_study_progress
    },
    swiperOption() {
      return {
        loop: false,
        autoplay: false,
        direction: 'horizontal',
        slidesPerView: 'auto',
        freeMode: false,
        lazy: {
          loadPrevNext: true
        },
        navigation: { // 设置点击箭头
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        on: {
          click: (e) => {
            const index = e.target.getAttribute('data-i') * 1
            const project = this.chapterSummaryList[index]
            this.toChapterPosition(project)
          }
        }
      }
    },
    dtMore() {
      return (type, val, container) => {
        const data = {
          page: this.courseData.course_name,
          page_type: '网课详情页面-移动新版',
          container: `详情-${container}`,
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: val,
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'H5'
        }
        if (type === 'remark') {
          return JSON.stringify(data)
        } else if (type === 'eid') {
          return `element_${this.course_id}_${val}`
        } else {
          return `area_${this.course_id}_${val}`
        }
      }
    },
    dtArg() {
      return {
        page: this.courseData.course_name,
        page_type: '网课详情页面-移动新版',
        container: '课程介绍',
        content_name: '订阅抽奖入口',
        course_id: this.course_id,
        terminal: 'H5'
      }
    }
  },
  watch: {
    courseData: {
      immediate: true,
      deep: true,
      handler() {
        if (this.courseData?.created_at) {
          const lStr = this.courseData.course_desc.replace(/<\/?[^>]+>/ig, '')
          this.courseData.created_at = this.courseData.created_at.slice(0, 16)
          this.$nextTick(() => {
            const bl = document.getElementsByClassName('des-box')[0]
            const fNum = Math.floor(bl.offsetWidth / 12) // 一行的字数
            if (bl) {
              this.descOverflow = lStr.length > fNum * 3 // 超过三行
              const idx = fNum * 3 - 7
              this.customDes = this.descOverflow ? lStr.slice(0, idx) + '...' : lStr.slice(0, idx) // 截取3行显示的字数
            }
          })
        }
      }
    }
  },
  async created() {
    // let storage = this.getStorage()
    // if (storage && typeof storage === 'object') {
    //   this.advertisingRecord = storage
    // }
    await this.getClickCourseList()
    this.getSysBusyStatus()
  },
  methods: {
    toPath(row) {
      // 推广数据（活动）的点击后还是要显示的 banner_type == 1 活动
      if (Number(row.banner_type) === 1) return
      // 推广数据记录
      let { id, end_time } = row
      if (id === undefined || end_time === undefined) return
      this.advertisingRecord[id] = end_time
      // this.setStorage(this.advertisingRecord)
      this.setClickCourseList()
    },
    toChapterPosition(item) {
      window.BeaconReport('at_click', {
        eid: this.dtChapter('eid', item),
        remark: this.dtChapter('remark', item)
      })
      this.$emit('toChapterPosition', item.chapter_time_point)
    },
    toPage() {
      const url = `//sdc.qq.com/s/Shd3Jk?scheme_type=homepage`
      window.location.href = url
    },
    // 获取当前环境是不是小程序
    getMiniProgramEnv() {
      // 通过判断navigator.userAgent中包含miniProgram字样
      let userAgent = navigator.userAgent
      return /miniProgram/i.test(userAgent)
    },
    toSearchPage({ url, item }) {
      let href = `${url}&fromNet=fromNetLabel`
      if (this.getMiniProgramEnv()) {
        let { type } = this.$route.query
        let isMookNet = type === 'taskContent' || false
        // 小程序内直接跳转
        if (!isMookNet) {
          // 如果是网络课跳转 不经过taskContent
          window.wx.miniProgram.navigateTo({
            url: href || ''
          })
          return
        }
        // 如果是mooc嵌套的网络课跳转 经过taskContent
        window.parent.postMessage({
          event: 'toSearch',
          href,
          item
        }, '*')
      } else {
        let keywords = item.label_name || ''
        window.location.href = `https://sdc.qq.com/s/yJyZMs?fromNet=fromNetLabel&keyword=${keywords}`
      }
    },
    getRouterQuery() {
      let { mooc_course_id, task_id } = this.$route.query
      return {
        mooc_course_id: mooc_course_id || '',
        task_id: task_id || ''
      }
    },
    // 推广数据
    getPopularizeList() {
      const params = {
        act_type: '2',
        count: '10'
      }
      popularizeApi(params).then((res) => {
        let keyList = Object.keys(this.advertisingRecord)
        let list = []
        const moduleObj = {
          null: '',
          1: '网络课',
          2: '面授课',
          3: '直播',
          4: '活动',
          5: '码客',
          6: '行家',
          7: '案例',
          8: '文章',
          10: '培养项目',
          17: 'iwiki',
          20: 'K吧文章'
        }
        res.forEach((v) => {
          if (v.banner_type === 2) {
            v.module_name = v.recommend_module_id ? moduleObj[v.recommend_module_id * 1] : ''
          } else { // 活动
            v.module_name = '外链'
          }
          v.module_id = v.recommend_module_id * 1
          v.content_name = v.banner_name
          v.photo_url = v.image_url || ''
          v.content_url = v.link_url
          v.item_id = v.recommend_item_id || v.id
          v.isAdvert = true
          if (!v.duration) v.duration = 0
          if (!v.labels) v.labels = []
          // 判断当前推广是否显示以及是否过期
          if (keyList.includes(String(v.id))) {
            let endTime = this.advertisingRecord[v.id]
            let time = endTime.replace(/-/g, '/')
            if (new Date(time) < new Date()) {
              delete this.advertisingRecord[v.id]
              list.push(v)
            }
          } else {
            list.push(v)
          }
        })
        this.setStorage(this.advertisingRecord)
        this.advertisingList = list
      })
    },
    handerClick(e) {
      if (e.link_url) {
        this.advertisingRecord[e.id] = e.end_time
        this.setStorage(this.advertisingRecord)
        window.open(e.link_url)
      } else {
        this.$message.wranning('跳转链接为空')
      }
    },
    getStorage() {
      return JSON.parse(localStorage.getItem('advertising_record'))
    },
    setStorage(val) {
      let data = JSON.stringify(val)
      localStorage.setItem('advertising_record', data)
    },
    // 猜你喜欢
    getGuessLike() {
      const params = {
        app_id: 'A9BiosXihR0h46ThNsAX',
        size: 10,
        current: 1,
        t: Date.now()
      }
      guessLikeAPI(params).then((res) => {
        this.guessLikeList = (res && res.records) || []
      })
    },
    getSysBusyStatus() {
      isSysBusy().then(res => {
        if (res !== '1') {
          this.getPopularizeList() // 推广数据
          this.getGuessLike()
        }
        // else {
        //   this.getGuessLike()
        // }
      })
    },
    // 获取已经点击的推广课程列表
    async getClickCourseList() {
      let res = await getPopularizeVisiList()
      this.advertisingRecord = res || {}
    },
    // 设置已经点击的推广课程列表
    setClickCourseList() {
      let data = {}
      if (!Object.values(this.advertisingRecord).length) {
        data = { '-1': '-1' } // 当数据都过期时覆盖接口数据为{}，但是后端不让传空{}
      } else {
        data = this.advertisingRecord
      }
      setPopularizeVisiList(data).then(res => {
        console.log('res: ', res)
      })
    },
    dtChapter(type, row) {
      const data = {
        page: this.courseData.course_name,
        page_type: '网课详情页面-移动新版',
        container: '详情-章节',
        click_type: 'data',
        content_type: '',
        content_id: row.chapter_time_point,
        content_name: row.chapter_title,
        act_type: '2',
        container_id: '',
        page_id: '',
        terminal: 'H5'
      }
      if (type === 'remark') {
        return JSON.stringify(data)
      } else if (type === 'eid') {
        return `element_${this.course_id}_${row.chapter_title}`
      } else {
        return `area_${this.course_id}_${row.chapter_title}`
      }
    }
  }
}
</script>
<style lang="less" scoped>
.des-main-container {
  height: 100%;
  overflow-y: auto;
  background-color: #F3F5F7;
  .mgb-16 {
    margin-bottom: 16px;
  }
}
.des-main-margin {
  margin-left: 16px;
  margin-right: 16px;
}
.des-main {
  background-color: #fff;
  padding: 16px 0px 8px;
  font-size: 12px;
  //清除浮动
  .clearfix:after {
    content: '';
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  .title-box {
    display: flex;
    align-items: flex-start;
    .title-left {
      .tag {
        display: inline-block;
        text-align: center;
        font-size: 12px;
        border-radius: 2px;
        border: 1px solid #0052d9ff;
        background: #ecf2feff;
        color: #0052d9;
        margin-right: 10px;
        height: 18px;
        line-height: 16px;
        padding: 0 6px;
      }
      .title {
        font-size: 14px;
        font-weight: bold;
        line-height: 22px;
        word-break: break-all;
         color: #000000e6;
      }
    }
  }
  .mid-tag-box {
    display: flex;
    align-items: center;
    margin-top: 8px;
    margin-bottom: 8px;
    :deep(.label-show) {
      line-height: 20px;
      width: calc(100% - 60px);
      .empty {
        margin-bottom: unset;
      }
      .label-show-all {
        word-break: break-all;
        word-wrap: break-word;
        overflow: hidden;
        height: 20px;
        .tag-item {
          padding: 0 8px;
          border-radius: 4px;
          line-height: 20px;
          height: 20px;
        }
      }
    }
    .score {
      display: inline-block;
      color: #00000099;
      font-family: "PingFang SC";
      font-size: 10px;
      font-weight: 500;
      line-height: 20px;
      padding: 0 8px;
      border-radius: 4px;
      background: #F7F8FA;
      flex-shrink: 0;
      margin-right: 8px;
    }
  }
  .des-box {
    position: relative;
    .des-content-detail {
      line-height: 20px;
      color: #00000066;
      word-break: break-all;
    }
    .more-bg {
      position: absolute;
      bottom: 0px;
      right: 0px;
      line-height: 20px;
      background-color: #fff;
      display: flex;
      align-items: flex-end;
      .ellipsis {
        color: #00000066;
        display: inline-block;
        margin-right: 24px;
      }
      .more-btn {
        color: #0052d9;
        font-family: "PingFang SC";
        font-size: 12px;
      }
    }
  }
  .overflow-desc {
    height: 60px;
    overflow: hidden;
  }
  .chapter-box {
    .chapter-title {
      margin-top: 16px;
      margin-bottom: 8px;
      line-height: 16px;
      font-size: 12px;
      display: flex;
      justify-content:space-between;
      .title-num {
        color: #000000e6;
        font-weight: bold;
      }
      .title-btn {
        color: #00000066;
        display: flex;
        align-items: center;
        .right-icon {
          background: url('~@/assets/img/mobile/right-arrow.png') no-repeat;
          background-size: 16px;
          width: 16px;
          height: 16px;
          display: inline-block
        }
      }
    }
    .item-chapter-swiper {
      border-radius: 4px;
      background: #F5F7FA;
      padding: 6px;
      width: 109px;
      height: 45px;
      .chapter-item-title {
        display: flex;
        align-items: flex-start;
        .title {
          line-height: 16px;
          color: #000000e6;
          font-size: 11px;
        }
      }
      .active-item-title {
        .title {
          color: #0052D9;
        }
        .play-icon {
          background: url('~@/assets/img/icon-live.png') no-repeat;
          background-size: 12px;
          width: 12px;
          height: 12px;
          display: inline-block;
          margin-right: 4px;
          float: left;
          flex-shrink: 0;
        }
      }
    }
    .item-chapter-swiper + .item-chapter-swiper {
      margin-left: 8px;
    }
  }
}
.like-popularize-box {
  background-color: #fff;
  padding: 8px 16px;
  margin-top: 8px;
  padding-bottom: 114px;
  .jump-btn {
    border-radius: 8px;
    background-color: #ECF2FE;
    color: #0052D9;
    height: 40px;
    line-height: 40px;
    width: 156px;
    margin: 0 auto;
    text-align: center;
    margin-top: 18px;
    margin-bottom: 20px;
  }
}
.desc-popup-container {
  height: calc(100% - 210px);
  .body {
    height: 100%;
    overflow: hidden;
    .top-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 16px;
      border-bottom: 1px solid #EDEDED;
      .title {
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        color: #000000e6;
      }
      .close-icon {
        display: inline-block;
        width: 24px;
        height: 24px;
        background: url('~@/assets/img/close.png') no-repeat center / cover;
      }
    }
    .content {
      padding: 16px;
      height: calc(100% - 45px);
      overflow: auto;
      padding-bottom: 24px;
      .title-box {
        .tag {
          display: inline-block;
          text-align: center;
          font-size: 12px;
          border-radius: 2px;
          border: 1px solid #0052d9ff;
          background: #ecf2feff;
          color: #0052d9;
          margin-right: 10px;
          height: 18px;
          line-height: 16px;
          padding: 0 6px;
        }
        .title {
          font-size: 14px;
          font-weight: bold;
          line-height: 22px;
          word-break: break-all;
          color: #000000e6;
        }
      }
      .top-view {
        color: #a3a3a3;
        font-size: 11px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        display: flex;
        align-items: center;
        margin-top: 4px;
        .icon {
          background-size: 16px;
          width: 16px;
          height: 16px;
          display: inline-block;
          margin-right: 2px;
        }
        .item-view {
          display: flex;
          align-items: center;
          margin-right: 16px;
          .view-icon {
            background: url('~@/assets/img/mobile/watch.png') no-repeat center/cover;
          }
        }
        .item-time {
          display: flex;
          align-items: center;
          .time-icon {
            background: url('~@/assets/img/mobile/time.png') no-repeat center/cover;
          }
        }
      }
      .des-tag-box {
        margin-top: 8px;
        .score {
          display: inline-block;
          color: #00000099;
          font-family: "PingFang SC";
          font-size: 10px;
          font-weight: 500;
          line-height: 20px;
          padding: 0 8px;
          border-radius: 4px;
          background: #F7F8FA;
          margin-right: 8px;
          margin-bottom: 8px;
          float: left;
        }
        :deep(.label-show) {
          line-height: 20px;
         .label-show-all {
          display: unset;
            .tag-item {
              float: left;
              padding: 0 8px;
              border-radius: 4px;
              line-height: 20px;
              height: 20px;
            }
          }
          .empty {
            margin-bottom: unset;
          }
        }
      }
      .des-box {
        margin-top: 8px;
        .title {
          color: #000000e6;
          font-family: "PingFang SC";
          font-size: 14px;
          font-weight: bold;
          line-height: 22px;
        }
        :deep(.des-content) {
          color: #00000099;
          font-size: 12px;
          line-height: 20px;
          margin-top: 12px;
          word-break: break-all;
          img {
            height: 100%;
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
