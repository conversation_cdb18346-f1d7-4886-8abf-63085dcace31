// eslint-disable
window.memberSelector = (function() {
  let memberIframe = null
  let _closeCallback = null
  let _successCallback = null
  let currentClickId = null
  let _btnList = []
  let _targetIds = ''
  const url = {
    test: 'https://test-portal-learn.woa.com/training/user/memberManagement',
    production: 'https://portal.learn.woa.com/training/user/memberManagement'
  }

  /**
   * 初始化frame
   *
   * @param domId 触发显示iframe的DOM元素的ID，默认为undefined
   * @param successCallback 成功的回调函数
   * @param memberParams 参数，默认为空对象
   */
  function initMemberIframe(options) {
    const { env = 'production' } = options
    
    if (!memberIframe) {
      memberIframe = document.createElement('iframe')
      memberIframe.src = url[env]
      memberIframe.style.position = 'fixed'
      memberIframe.style.top = '50%'
      memberIframe.style.left = '50%'
      memberIframe.style.transform = 'translate(-50%, -50%)'
      memberIframe.style.width = '100%'
      memberIframe.style.height = '100%'
      memberIframe.style.border = 'none'
      memberIframe.style.zIndex = '-1'
      memberIframe.style.visibility = 'hidden'
      memberIframe.id = 'memberIframe'
      document.body.appendChild(memberIframe)
  
      window.addEventListener('message', getMessage)
  
      memberIframe.onload = () => {
        memberIframe.style.display = 'none'
        memberIframe.style.visibility = 'visible'
        memberIframe.style.zIndex = '100'
      }
    }
  }
  
  function showMemberIframe({ targetIds = '', successCallback = null, closeCallback = null } = {}) {
    if (targetIds) {
      _targetIds = targetIds
    }
  
    _successCallback = successCallback
    _closeCallback = closeCallback
  
    memberIframe.style.display = 'block'
  
    _sendMessage({
      events: 'dialogVisible',
      params: { show: true, targetIds: _targetIds }
    })
  }
  
  function hideMemberIframe() {
    if (!memberIframe) return
    memberIframe.style.display = 'none'
  }
  
  function getMessage(e) {
    const { data } = e
  
    if (data.vendor !== 'memberPage') return
  
    if (data.events === 'addMemberSuccess') {
      let obj = null
      if (_btnList && _btnList.length > 0) {
        _btnList.find(item => item.domId === currentClickId).targetIds = data.params.id
        obj = {
          res: data.params,
          currentClickId,
          btnList: _btnList
        }
      } else {
        obj = {
          res: data.params,
          targetIds: data.params.id
        }
      }
      _successCallback && _successCallback(obj)
    }
  
    if (data.events === 'dialogClose' && !data.params.show) {
      hideMemberIframe()
      _closeCallback && _closeCallback(currentClickId) // 调用外部注册的关闭回调
    }
  }
  
  function _sendMessage(info) {
    memberIframe.contentWindow.postMessage({
      vendor: 'memberPage',
      ...info
    }, '*')
  }
  
  function unload() {
    window.removeEventListener('message', getMessage)
    if (memberIframe) {
      document.body.removeChild(memberIframe)
      memberIframe = null
    }
  }

  return {
    initMemberIframe,
    showMemberIframe,
    hideMemberIframe,
    unload,
  }
})()