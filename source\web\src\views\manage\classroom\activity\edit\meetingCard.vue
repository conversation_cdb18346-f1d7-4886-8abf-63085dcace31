<template>
  <div class="meeting-card">
    <div class="meeting-info">
      <div class="status-name">{{ meetingStatusName }}</div>
      <div class="flex-row mt-12 mb-12" v-if="isRelated">
        <div class="row-item flex-box">
          <span class="label">会议号：</span>
          <span class="meeting-code">{{ $activityInfo.meeting_info?.meeting_code || '-' }}</span>
        </div>
        <div class="row-item flex-box">
          <span class="label">主持人：</span>
          <span>{{ $activityInfo.meeting_info?.meeting_creator || '-' }}</span>
        </div>
      </div>
      <div class="flex-row" v-if="isRelated">
        <div class="row-item flex-box">
          <span class="label">会议地址：</span>
          <el-link class="a-link mr-12" :href="$activityInfo.meeting_info?.meeting_url" type="primary" target="_blank">{{ $activityInfo.meeting_info?.meeting_url || '-'}}</el-link>
          <el-button class="copy-btn" type="text" size="small" v-if="$activityInfo.meeting_info?.meeting_url" @click="handleCopyLink($activityInfo.meeting_info?.meeting_url)">点击复制</el-button>
        </div>
      </div>
    </div>

    <div class="video-recording flex-row-start mt-16">
      <div class="label pt-6">视频录制</div>
      <div class="content">
        <el-form ref="form" label-width="92px">
          <el-form-item label="访问权限" v-if="false">
            <div class="flex-row line-h-normal">
              <el-radio-group v-model="accessPermissionsValue" class="mr-12" @change="handleAccessPermissionsChange">
                <el-radio v-for="item in accessPermissions" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
              </el-radio-group>
              <AudienceSelector v-if="accessPermissionsValue === 3" audience :showTab="['unit', 'group', 'import']" multiple
              v-model="target_ids" ref="selector" appCode="qlearning" :env="audienceEnv" importNumber='1000'
              :isShowCount="false" :createStudentID="true" :disabled="false" @changeRule="handleTarget" />
            </div>
          </el-form-item>
          <el-form-item label="录制链接">
            <div class="flex-row">
              <el-link class="a-link mr-12" :href="meetingData.record_view_url" type="primary" target="_blank">{{ meetingData.record_view_url }}</el-link>
              <el-button type="text" size="small" :disabled="!showMeetingViewUrl" @click="handleCopyLink(meetingData.record_view_url)">点击复制</el-button>
            </div>
          </el-form-item>
          <div class="flex-row mt-8 pl-22">
            <el-button class="primary-btn" type="primary" size="small" :disabled="!showMeetingViewUrl" @click="handlePushStudents">推送学员</el-button>
          </div>
        </el-form>

        <div class="iframe-box" v-if="false">
          <iframe v-show="meetingData.record_view_url" :src="meetingData.record_view_url" width="100%" height="800px"  scrolling="no" id="iframe_show" allowfullscreen="true"></iframe>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { AudienceSelector } from '@tencent/sdc-audience'
import { mapState } from 'vuex'
import { copyToClipboard } from '@/utils/tools.js'
import { getMeetingRecordApi, saveMeetingRecordAuthApi, pushMeetingRecordApi } from '@/config/classroom.api.conf.js'

const allTarget = 2015587

export default {
  name: 'meeting-card',
  components: {
    AudienceSelector
  },
  data() {
    return {
      audienceEnv: process.env.NODE_ENV,
      accessPermissions: [
        { label: '全体公司员工', value: 1, target: '' },
        { label: '仅已报名学员', value: 2, target: 0 },
        { label: '自定义可见范围', value: 3, target: null }
      ],
      accessPermissionsValue: 1,
      target_ids: allTarget,
      meetingData: {
        record_view_url: ''
      }
    }
  },
  watch: {
  },
  computed: {
    ...mapState({
      $activityInfo: state => state.activity.activityInfo
    }),
    activityId () {
      return this.$route.query.activity_id
    },
    isRelated() {
      const { teaching_type } = this.$activityInfo
      return teaching_type && teaching_type.indexOf('2') > -1
    },
    meetingStatusName() {
      const { meeting_create_type } = this.$activityInfo
      let msgl = ['', '已自动创建会议，会议信息如下', '已关联，会议信息如下']
      return this.isRelated ? msgl[meeting_create_type] : '暂未关联腾讯会议'
    },
    showMeetingViewUrl() {
      return this.meetingData.record_view_url ?? true
    }
  },
  beforeCreate() {
    // if (window.location.host.indexOf('tencent.com') !== -1) {
    //   document.domain = 'tencent.com'
    // } else if (location.host.indexOf('.woa.com') > -1) {
    //   document.domain = 'woa.com'
    // } else {
    //   document.domain = 'oa.com'
    // }
  },
  created() {
    this.getMeetingRecord()
  },
  mounted() {
  },
  beforeDestroy() { },
  methods: {
    getMeetingRecord() {
      getMeetingRecordApi({ activity_id: this.activityId }).then(res => {
        this.meetingData = res
        this.accessPermissionsValue = !res.record_target_ids ? 1 : res.record_target_ids === '0' ? 2 : 3
      })
    },
    handleAccessPermissionsChange(e) {
      if ([1, 2].includes(e)) {
        this.target_ids = allTarget
        const target = this.accessPermissions.find(item => item.value === e).target
        this.saveMeetingRecordAuth(target)
      } else if (e === 3) {
        this.target_ids = ''
      }
    },
    handleTarget(e) {
      this.saveMeetingRecordAuth(this.target_ids)
    },
    saveMeetingRecordAuth(target_ids) {
      saveMeetingRecordAuthApi({ activity_id: this.activityId, target_ids }).then(res => {
        this.$message.success('保存成功')
      })
    },
    handleCopyLink(url) {
      copyToClipboard(url, '分享链接已复制到剪切板，有权限的用户获得链接后可查看录制')
    },
    handlePushStudents() {
      pushMeetingRecordApi(this.activityId).then(res => {
        this.$message.success('推送成功')
      })
    }
  }
}
</script>

<style lang="less" scoped>
  @import '~assets/css/activity-common.less';
  .meeting-card {
    width: 100%;
    height: 100%;
    padding: 32px 28px;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    .meeting-info {
      // width: 800px;
      padding: 12px;
      gap: 12px;
      border-radius: 4px;
      background-color: #F9F9F9;
      .status-name {
        color: #00000099;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
      }
      .row-item {
        margin-right: 32px;
        color: #000000e6;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        .label {
          color: #00000099;
        }
        .meeting-code {
          color: #0052d9;
        }
      }
    }

    .video-recording {
      flex: 1;
      overflow: hidden;
      .label {
        color: #000000cc;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
      }
      .content {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        .primary-btn {
          padding: 8px 16px;
          justify-content: center;
          align-items: center;
          gap: 10px;
          border-radius: 4px;
          color: #0052D9;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 16px;
          background: #ECF2FE;
          border-color: #ECF2FE;
          &:hover {
            opacity: 0.8;
          }
        }
        .iframe-box {
          flex: 1;
          margin: 24px 0 0 22px;
          overflow-y: auto;
        }
      }
    }
    .a-link {
      line-height: 1;
      color: #0052d9;
      text-decoration: revert-layer;
      font-weight: 400;
    }
    :deep(.el-link.el-link--primary) {
      &:hover::after {
        border-color: transparent;
      }
    }
    .copy-btn {
      padding: 0;
    }
    .flex-row {
      display: flex;
      align-items: center;
    }
    .flex-row-start {
      display: flex;
      align-items: flex-start;
    }
    .line-h-normal {
      height: 32px;
    }
    .el-form-item {
      margin-bottom: 0px;
    }
  }
</style>
