<template>
  <div class="train-project-list">
    <div class="top-title">
      <span>外部课程列表</span>
      <div class="link">
        <span class="help-icon"></span>
        <a class="source-detail" target="_blank" href="">内容新增指引</a>
      </div>
    </div>
    <div class="search-content">
      <el-form ref="form" :model="searchForm" inline>
        <div class="search-body">
          <el-form-item label="名称">
            <el-input v-model="searchForm.course_title" size="small" placeholder="请输入课程名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="内容来源">
            <el-select v-model="searchForm.recourse_from" size="small" placeholder="请选择内容来源" style="width:280px" clearable>
              <el-option
                v-for="item in recourseOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="内容类型">
            <el-select v-model="searchForm.recourse_content_type" size="small" placeholder="请选择内容类型" style="width:280px" clearable>
              <el-option
                v-for="item in recourseContentOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="更新时间">
            <el-date-picker
              style="width:380px"
              size="small"
              v-model="projectTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00','23:59:59']"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="内容标签">
            <LabelSelectComponent
            v-model="label_ids"
            placeholder="请选择内容标签"
            class="project-tag-box"
            :maxNum="0"
            @getSelectedLabelList="getSelectedLabelList"
            :disableCreate="true"
            >
            </LabelSelectComponent>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch(1)" size='small'>搜索</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleReset" size='small'>重置</el-button>
          </el-form-item>
          <div style="width: 100%; padding-bottom: 16px;">
            <el-button type="primary" @click="updateDialogShow = true" size='small'>新增&更新课程</el-button>
            <el-button type="primary" @click="inventorySettingsDialogShow = true" size='small'>库存设置</el-button>
            <span class="export-label">学习记录范围</span>
            <el-date-picker
              class="export-time-rag"
              size="small"
              placeholder="开始日期"
              v-model="learnHistoryConfig.start_time"
              type="date"
              :picker-options="startPickerOptions"
              value-format="yyyy-MM-dd">
            </el-date-picker>~
            <el-date-picker
              class="export-time-rag"
              size="small"
              placeholder="结束日期"
              v-model="learnHistoryConfig.end_time"
              type="date"
              :picker-options="endPickerOptions"
              value-format="yyyy-MM-dd">
            </el-date-picker>
            <el-button type="primary" @click="exportLearnHistory" size='small'>导出学习记录</el-button>
          </div>
        </div>
      </el-form>
      <el-table
        :data="tableData.records"
        style="width: 100%"
        header-row-class-name="table-header-style"
        row-class-name="table-row-style"
        class="table-content"
      > 
        <el-table-column prop="outsourced_course_id" label="id" width="120"></el-table-column>
        <el-table-column prop="course_title" label="名称" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="boutique-tag" v-if="scope.row.excellent_status === 1">精</span>
            <span class="table-course-title" @click="toManagePage(scope.row, 'name')">{{ scope.row.course_title }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="recourse_from" label="内容来源" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ handleRecourse(scope.row.recourse_from) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="course_acquisition_type" label="内容类型" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ handleCourseAcquisitionType(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updated_at" label="更新时间" width="180"></el-table-column>
        <el-table-column prop="creator_name" label="创建人" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="label_names" label="内容标签" show-overflow-tooltip></el-table-column>
        <el-table-column prop="target_list" label="目标学员" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <div class="operat-btn-box">
              <el-link v-if="scope.row.recourse_parent_mooc_id" @click="toManagePage(scope.row)" type="primary" :underline="false">管理</el-link>
              <el-link v-if="scope.row.course_status !== 0" @click="handleShare(scope.row)" type="primary" :underline="false">分享</el-link>
              <el-link @click="handleDelete(scope.row, scope.$index)" type="danger" :underline="false">删除</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
      v-if="tableData.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="current"
      :page-sizes="[5, 10, 20, 30, 50, 100]"
      :page-size="size"
      layout="total,  prev, pager, next, sizes, jumper"
      :total="tableData.total"
      >
      </el-pagination>
    </div>
    <!-- 分享项目 -->
    <shareDialog :isShow.sync="isShareDialog" ref="shareDialogRef"></shareDialog>
    <!-- 新增&更新课程 -->
    <el-dialog
      v-if="updateDialogShow"
      title="新增&更新课程"
      width="460px"
      :close-on-click-modal="false"
      :before-close="cancelUpdateDialog"
      custom-class="none-border-dialog copy-dialog"
      :show-close="false"
      :visible.sync="updateDialogShow">
      <el-form :model="updateForm" :rules="rules" ref="updateForm" label-width="80px" label-position="top">
        <el-form-item label="课程来源" prop="courseFrom">
          <el-select v-model="updateForm.courseFrom" size="small" placeholder="请选择课程来源" style="width:316px" clearable>
            <el-option
              v-for="item in recourseOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="课程ID" prop="courseIds">
          <el-input v-model="updateForm.courseIds" size="small" max-width="30" placeholder="多个ID请用 ; 隔开" clearable></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="cancelUpdateDialog">取 消</el-button>
        <el-button size='small' @click="submitInfoSync" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 更新库存 -->
    <el-dialog
      title="库存设置"
      width="460px"
      :close-on-click-modal="false"
      :before-close="cancelInventorySettingsDialog"
      custom-class="none-border-dialog copy-dialog"
      :show-close="false"
      :visible.sync="inventorySettingsDialogShow">
      <el-form :model="inventoryForm" :rules="inventoryRules" ref="inventoryForm" label-width="80px" label-position="top">
        <el-form-item label="课程来源" prop="dickey">
          <el-select v-model="inventoryForm.dickey" size="small" @change="inventorySourceChange" placeholder="请选择课程来源" style="width:316px" clearable>
            <el-option
              v-for="item in inventoryOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <div class="label-div">{{ `当前库存  ${inventoryNum}` }}</div>
        <el-form-item label="库存数量" prop="dic_val">
          <el-input v-model.number="inventoryForm.dic_val" maxLength="9" size="small" max-width="30" placeholder="请输入库存数量" clearable></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="cancelInventorySettingsDialog">取 消</el-button>
        <el-button size='small' @click="submitInventory" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import pager from '@/mixins/pager'
import { 
  getOutsourcedCoursePageApi,
  delOutsourcedApi,
  infoSyncApi,
  addGeekStockApi,
  getStockApi,
  getTenantListApi,
  getTypeConditionApi
} from '@/config/mooc.api.conf.js'
import shareDialog from '@/views/components/shareDialog.vue'
import { mapState } from 'vuex'
import LabelSelectComponent from '@/views/components/LabelSelectComponent'
import axios from 'axios'
import env from 'config/env.conf.js'
export default {
  mixins: [pager],
  components: {
    shareDialog,
    LabelSelectComponent
  },
  data() {
    return {
      searchForm: {
        course_title: '',
        recourse_from: '',
        recourse_content_type: ''
      },
      label_ids: [],
      projectTime: [],
      recourseOptions: [
        // { label: '极客时间', value: 'geekBang' },
        // { label: 'DDI', value: 'DDI' }
      ],
      inventoryOptions: [
        { label: '极客时间', value: 'geekStock' }
      ],
      inventoryNum: 0,
      recourseContentOptions: [
        { label: '极客时间 - 音频图文专栏', value: 1, name: '音频图文专栏', typeVal: 1 },
        { label: '极客时间 - 视频课专栏', value: 2, name: '视频课专栏', typeVal: 2 },
        { label: '极客时间 - 每日一课视频', value: 3, name: '每日一课视频', typeVal: 7 },
        { label: '极客时间 - 大厂案例视频', value: 4, name: '大厂案例视频', typeVal: 28 }
      ],
      courseAcquisitionTypeOptions: [
        { label: '公司统一采购', value: 1 },
        { label: '个人按需需要购买', value: 2 }
      ],
      isShareDialog: false,
      tableData: {
        records: [],
        total: 0
      },
      updateDialogShow: false, // 新增&更新课程
      updateForm: {
        courseFrom: '',
        courseIds: ''
      },
      rules: {
        courseFrom: [{ required: true, message: '请选择课程来源', trigger: 'blur' }],
        courseIds: [{ required: true, message: '请输入课程ID', trigger: 'blur' }]
      },
      inventorySettingsDialogShow: false, // 库存设置
      inventoryForm: {
        dickey: '',
        dic_val: null
      },
      inventoryRules: {
        dickey: [{ required: true, message: '请选择课程来源', trigger: 'change' }],
        dic_val: [
          { pattern: /^[+]{0,1}[0-9](\d*)$/, message: '请输入正整数', trigger: 'change' },
          { required: true, type: 'number', message: '请输入库存数', trigger: 'blur' }]
      },
      learnHistoryConfig: { // 导出学习记录的配置参数
        start_time: '',
        end_time: ''
      }
    }
  },
  computed: {
    ...mapState(['userLimitInfo']),
    startPickerOptions() {
      return {
        disabledDate: this.startDisabledDate
      }
    },
    endPickerOptions() {
      return {
        disabledDate: this.endDisabledDate
      }
    }
  },
  mounted() {
    // this.getTypeCondition()
    this.onSearch(1)
    this.getTenantList()
  },
  methods: {
    // 导出时间范围限制，限制一个月
    startDisabledDate(item) {
      let end_time = new Date(this.learnHistoryConfig.end_time).getTime()
      if (this.learnHistoryConfig.end_time) {
        let oneTime = 1000 * 60 * 60 * 24
        return item * 1 < end_time * 1 - oneTime * 31 || (item * 1 > end_time * 1)
      } else {
        return false
      }
    },
    endDisabledDate(item) {
      let start_time = new Date(this.learnHistoryConfig.start_time).getTime()
      if (this.learnHistoryConfig.start_time) {
        let oneTime = 1000 * 60 * 60 * 24
        return item * 1 > start_time * 1 + oneTime * 30 || (item * 1 + oneTime < start_time * 1)
      } else {
        return false
      }
    },
    // 导出学习记录
    exportLearnHistory() {
      if (!this.searchForm.recourse_from) {
        this.$message.error('请选择内容来源!')
        return
      }
      let { start_time, end_time } = this.learnHistoryConfig
      let url = `${env[process.env.NODE_ENV].trainingPath}api/outsourcedCourse/manage/info/export-study-record?recourse_from=${this.searchForm.recourse_from}&start_time=${start_time} 00:00:00&end_time=${end_time} 23:59:59`
      axios({
        url,
        method: 'get',
        responseType: 'blob'
      }).then((res) => {
        let _this = this
        if (res.data.type === 'application/json') { 
          const reader = new FileReader() // 创建一个FileReader实例
          reader.readAsText(res.data, 'utf-8') // 读取文件
          reader.onload = function () {
            // 文件读取成功进行信息处理
            const data = JSON.parse(reader.result) // 获取到后端返回的json信息
            if (data.code === 403) {
              _this.$router.replace({
                name: '401'
              })
            } else if (data.code !== 200) {
              _this.$message.success('导出失败') 
            }
          }
        } else {
          // 否则走成功的案例
          const url = window.URL.createObjectURL(new Blob([res.data]))
          const link = document.createElement('a')
          link.href = url
          link.setAttribute('download', '外部课程学习明细.xlsx')
          document.body.appendChild(link)
          link.click()
          _this.$message.success('导出成功')  
        }
      })
    },
    // 获取内容类型下拉
    getTypeCondition() {
      let params = {
        course_title: '',
        recourse_from: '',
        start_time: '',
        end_time: '',
        label_ids: ''
      }
      getTypeConditionApi(params).then((res) => {
      })
    },
    // 培养项目查询
    onSearch(current = 1) {
      if (this.current !== current) this.current = current
      const start_time = this.projectTime?.length ? this.projectTime[0] : ''
      const end_time = this.projectTime?.length ? this.projectTime[1] : ''
      let labels = ''
      this.label_ids.forEach((e) => {
        labels += `${e.label_id};`
      })
      labels = labels.slice(0, -1)
      const params = {
        ...this.searchForm,
        current,
        size: this.size,
        start_time,
        end_time,
        label_ids: labels
      }
      getOutsourcedCoursePageApi(params).then((res) => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    // 外部课程类型列表
    getTenantList() {
      getTenantListApi().then(res => {
        if (res) {
          let obj = JSON.parse(res)
          let typeList = []
          for (const key in obj) {
            typeList.push({ label: obj[key], value: key })
          }
          this.recourseOptions = typeList
        }
      })
    },
    getSelectedLabelList(val) {
      this.label_ids = val
    },
    // 分享
    handleShare(row) {
      this.isShareDialog = true
      let params = {}
      // iframeCourse - iframeCourse、文章-graphic、视频-video
      if (['iframeCourse', 'graphic', 'video'].includes(row.recourse_type)) {
        let url = ''
        switch (row.recourse_type) {
          case 'iframeCourse':
            url = `https://sdc.qq.com/s/MGoSJh?scheme_type=outsourced&resource_type=iframe&course_id=${row.outsourced_course_id}`
            break
          case 'graphic':
            url = `https://sdc.qq.com/s/9bd9km?scheme_type=outsourced&resource_type=graphic&course_id=${row.outsourced_course_id}`
            break
          case 'video':
            url = `https://sdc.qq.com/s/dShLEm?scheme_type=outsourced&resource_type=video&course_id=${row.outsourced_course_id}`
            break
          default:
            break
        }
        params = {
          url,
          taskTitle: row.course_title,
          scene: `${row.outsourced_course_id}_zh-CN_${row.recourse_type === 'iframeCourse' ? 'iframe' : row.recourse_type}`,
          page: 'pages/webview/outsourced/iframe/index'
        }
      } else {
        let url = process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/Zkma1k' : 'http://s.test.yunassess.com/s/UxEYPE'
        params = {
          mooc_course_id: row.outsourced_course_id,
          url: `${url}?scheme_type=mooc&mooc_course_id=${row.outsourced_course_id}`,
          taskTitle: row.course_title,
          scene: row.outsourced_course_id,
          page: 'pages/mooc/projectDetails/index'
        }
      }
      this.$nextTick(() => {
        this.$refs.shareDialogRef.initCode(params)
      })
    },
    // 删除
    handleDelete({ outsourced_course_id }, index) {
      this.$messageBox.confirm('课程删除后学员将无法查看课程，且课程相关数据将全部清除，确定删除吗？', '删除课程', {
        confirmButtonText: '删除课程',
        cancelButtonText: '取消'
      }).then(() => {
        delOutsourcedApi(outsourced_course_id).then((res) => {
          this.tableData.records.splice(index, 1)
          this.$message.success('删除成功')
        })
      })
    },
    // 管理
    toManagePage({ outsourced_course_id, course_status, recourse_type }, value) {
      // video          https://sdc.qq.com/s/SUa3ed
      // graphic        https://sdc.qq.com/s/GLZMsg
      // series         mooc
      // iframeCourse - iframeCourse
      if (value === 'name' && recourse_type === 'iframeCourse') {
        const { href } = this.$router.resolve({
          name: 'iframePlay',
          query: { course_id: outsourced_course_id }
        })
        window.open(href)
        return
      }
      // 文章
      if (value === 'name' && recourse_type === 'graphic') {
        const { href } = this.$router.resolve({
          name: 'graphicPlay',
          query: { course_id: outsourced_course_id }
        })
        window.open(href)
        return
      }
      // 视频
      if (value === 'name' && recourse_type === 'video') {
        const { href } = this.$router.resolve({
          name: 'videoPlay',
          query: { course_id: outsourced_course_id }
        })
        window.open(href)
        return
      }
      // mooc或者系列课
      if (value === 'name' && course_status !== 0) { // 名称跳转-除了未发布的都跳转到详情
        const { href } = this.$router.resolve({
          name: 'projectDetail',
          query: { mooc_course_id: outsourced_course_id }
        })
        window.open(href)
        return
      }
      const { href } = this.$router.resolve({
        name: 'basic-setting',
        query: { mooc_course_id: outsourced_course_id, from: 'externalCourse' }
      })
      window.open(href)
    },
    // 重置
    handleReset() {
      this.searchForm = {
        course_title: '',
        recourse_from: '',
        recourse_content_type: ''
      }
      this.label_ids = []
      this.projectTime = []
      this.onSearch()
    },
    handleCourseAcquisitionType(row) {
      let type = row.course_acquisition_type
      let classify = row.recourse_course_classify * 1
      let index = this.courseAcquisitionTypeOptions.findIndex(v => v.value === type * 1)
      let classifyIndex = this.recourseContentOptions.findIndex(v => v.typeVal === classify)
      if (!row.recourse_course_classify) { // 没有recourse_course_classify就取course_acquisition_type匹配
        return `${index > -1 ? this.courseAcquisitionTypeOptions[index].label : type}`
      }
      return `${index > -1 ? this.courseAcquisitionTypeOptions[index].label : type}-${classifyIndex > -1 ? this.recourseContentOptions[classifyIndex].name : classify}`
    },
    handleRecourse(value) {
      let index = this.recourseOptions.findIndex(v => v.value === value)
      if (index !== -1) return this.recourseOptions[index].label
      return value
    },
    // 关闭 新增&更新课程
    cancelUpdateDialog() {
      this.updateDialogShow = false
      this.$refs.updateForm.resetFields()
    },
    // 新增&更新课程 提交
    submitInfoSync() {
      this.$refs.updateForm.validate((valid) => {
        if (valid) {
          const params = {
            ...this.updateForm
          }
          infoSyncApi(params).then(res => {
            this.$message.success('操作成功！')
            this.onSearch(1)
            this.cancelUpdateDialog()
          })
        } else {
          return false
        }
      })
    },
    // 关闭 库存设置
    cancelInventorySettingsDialog() {
      this.inventorySettingsDialogShow = false
      this.$refs.inventoryForm.resetFields()
    },
    // 提交 库存设置
    submitInventory() {
      this.$refs.inventoryForm.validate((valid) => {
        if (valid) {
          const params = {
            ...this.inventoryForm
          }
          addGeekStockApi(params).then(res => {
            this.$message.success('操作成功！')
            this.cancelInventorySettingsDialog()
          })
        } else {
          return false
        }
      })
    },
    inventorySourceChange(e) {
      if (e === 'geekStock') this.getStockFn()
    },
    // 获取geek库存
    getStockFn() {
      getStockApi().then(res => {
        this.inventoryNum = res
      })
    }
  }
}
</script>
<style lang="less" scoped>
.train-project-list {
  padding: 4px 0;
  height: 100%;
  background: #fff;
  border-radius: 4px;
  opacity: 1;
  box-shadow: 0 0 8px 0 #eeeeeeff;
  .project-tag-box {
    width: 280px;
  }
  .boutique-tag {
    background: #FF7548;
    color: #fff;
    font-size: 12px;
    height: 16px;
    line-height: 16px;
    padding: 0 2px;
    display: inline-block;
    margin-right: 10px;
    border-radius:  2px;
  }
  .top-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f3f3f3ff;
    padding: 16px 20px;
    span {
      color: #000000e6;
      font-size: 24px;
      font-weight: 600;
    }
    .link {
      font-size: 14px;
      line-height: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      .help-icon {
        background: url('~@/assets/mooc-img/help_circle.png') no-repeat center/cover;
        width: 16px;
        height: 16px;
        display: inline-block;
        margin-right: 4px;
      }
      .source-detail {
        color: #0052d9;
        text-decoration-line: underline;
        cursor: pointer;
      }
    }
  }
  .search-content{
    padding: 18px 20px;
    .top-btn {
      margin-bottom: 8px;
    }
    .search-body{
      background: #f9f9f9ff;
      padding: 16px 16px 0px 16px;
      // display: flex;
      .export-label {
        margin-left: 10px;
        font-size: 14px;
        color: #000000cc;
      }
      .export-time-rag {
        width: 190px;
        margin: 0 10px;
      }
    }
    .table-content{
      margin-top: 15px;
      border-radius: 4px;
      opacity: 1;
      border-top: 1px solid #eeeeeeff;
      border-left: 1px solid #eeeeeeff;
      border-right: 1px solid #eeeeeeff;
    }
  }
  .table-course-title {
    color: #0052D9;
    cursor: pointer;
  }
  .operat-btn-box {
    .el-link + .el-link {
      margin-left: 10px;
    }
  }
  .boutique-box {
    margin-left: 30px;
    margin-right: 30px;
  }
}
:deep(.el-form-item) {
  margin-bottom: 16px;
}
:deep(.el-form-item__content){
  width: 80%;
}
:deep(.el-message-box__message){
  color: #00000099;
}
.copy-dialog {
  .custom-el-input-count {
    color: #ACACAC;
    background: #FFF;
    position: absolute;
    font-size: 12px;
    bottom: 6px;
    right: 6px;
    line-height: 20px;
  }
  .input-style {
    position: relative;
    :deep(.el-form-item__content) {
      width: 390px;
    }
    :deep(.el-input) {
      .el-input__inner {
        padding-right: 70px;
      }
      .el-input__suffix {
        position: absolute;
        right: 43px;
      }
    }
  }
  .course-name-input {
    width: 390px;
  }
  .warning-tips {
    color: #FF7548;
    display: inline-block;
    line-height: 20px;
    margin-top: 5px;
    i {
      margin-right: 3px;
    }
  }
}
.label-div {
  line-height: 42px;
  color: #000000cc;
}
</style>
