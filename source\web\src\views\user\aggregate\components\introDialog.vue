<template>
  <el-dialog class="give-course-dialog" :visible.sync="dialogVisible" width="856px" @close="onClose">
    <div slot="title">
      简介
    </div>
    <div class="desc-text">
      <div :class="['desc-content']">
        <sdc-mce-preview class="text-preview" ref="preview" :urlConfig="editorConfig.urlConfig" :content="courseDesc">
        </sdc-mce-preview>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button size="small" type="primary" @click="onClose">关闭</el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  props: {
    intro: {
      type: Object,
      default: () => {}
    },
    isShow: {
      type: Boolean
    }
  },
  data() {
    return {
      editorConfig: {
        urlConfig: {
          // operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          // uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          // contentinfo: `/content-center/api/v1/content/{contentId}`, // 获取内容id
          // preview: `/content-center/api/v1/content/imgage/{contentId}/preview`

          operatesignature: `${location.protocol}${
            location.hostname.endsWith('.woa.com')
              ? process.env.VUE_APP_EDTUP_HOST_WOA
              : process.env.VUE_APP_EDTUP_HOST
          }/api/sso/know-service/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `${location.protocol}${
            location.hostname.endsWith('.woa.com')
              ? process.env.VUE_APP_EDTUP_HOST_WOA
              : process.env.VUE_APP_EDTUP_HOST
          }/api/sso/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `${location.protocol}${
            location.hostname.endsWith('.woa.com')
              ? process.env.VUE_APP_EDTUP_HOST_WOA
              : process.env.VUE_APP_EDTUP_HOST
          }/api/sso/content-center/api/v1/content/{contentId}`, // 获取内容id
          preview: `${location.protocol}${
            location.hostname.endsWith('.woa.com')
              ? process.env.VUE_APP_EDTUP_HOST_WOA
              : process.env.VUE_APP_EDTUP_HOST
          }/api/sso/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        }
      }
    }
  },
  mounted() {},
  watch: {},
  computed: {
    dialogVisible: {
      set(val) {
        this.$emit('update:isShow', val)
      },
      get() {
        return this.isShow
      }
    },
    courseDesc() {
      return this.intro.description || '<span style="color:#999">暂无内容<span>'
    }
  },
  methods: {
    onClose() {
      this.$emit('update:isShow', false)
    }
  }
}
</script>
<style lang="less">
</style>
<style lang="less" scoped>
/deep/.el-dialog {
  border-radius: 8px;
}
/deep/.el-dialog__body {
  padding: 16px 32px ;
}
/deep/.el-dialog__header {
  padding: 24px 32px 16px;
  border-bottom: unset;
}
/deep/.el-dialog__headerbtn {
  top: 26px;
}
/deep/.el-dialog__footer {
  padding: 0 32px 32px;
}

</style>
