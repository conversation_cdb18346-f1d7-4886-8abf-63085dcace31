<template>
  <div class="recommend-dialog">
    <el-dialog
      :title="dialogTitle"
      :visible.sync="visible"
      width="1200px"
      :close-on-click-modal="false"
      :before-close="closeDialog"
    >
      <div class="recommend-body">
        <el-form ref="form" :model="form" :rules="rules" label-width="100px">

          <el-form-item label="栏目名：" prop="source_code">
            <el-select v-model="form.source_code" placeholder="请输入栏目名" style="width: 300px" @change="$forceUpdate()">
              <el-option
                v-for="(item, index) in options"
                :key="index"
                :label="item.name"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="推广日期：" prop="rec_time">
            <el-date-picker
              style="width: 300px"
              type="date"
              v-model="form.rec_time"
              placeholder="请选择推广日期"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
              class="rec-time"
            >
            </el-date-picker>
            <span style="margin-left: 20px;">请在推广日期前一天完成配置</span>
          </el-form-item>

          <el-form-item label="上传头图：" prop="cover_image">
            <cut-img-upload
              ref="upload"
              accept=".jpg,.jpeg,.png,.bmp"
              size="5"
              @handleSuccess="handleSuccessImage"
              :dialogImageUrl="form.cover_image" 
              :autoImgUrl="form.img_content_id"
              @handleClearImg="handleClearImg"
              :fixedNumber="[4, 1]"
            >
              <template v-slot:text>
                <p>建议图片尺寸：960px * 240px</p>
              </template>
            </cut-img-upload>
          </el-form-item>

          </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="closeDialog">取 消</el-button>
        <el-button size="small" type="primary" @click="handleSave">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { CutImgUpload } from '@/components/index'
import { addNetCoursePerson, updateNetCoursePerson, getRecommendName } from '@/config/mooc.api.conf'
export default {
  components: { CutImgUpload },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogTitle() {
      return this.moduleName === 'add' ? '新建' : '编辑'
    }
  },
  mounted () {
    this.projectParams = this.$route.query.project || ''
    this.getRecommendName()
  },
  data () {
    const validImg = (rule, value, callback) => {
      if (!value && !this.form.img_content_id) {
        return callback(new Error('请选择运营图'))
      } else {
        callback()
      }
    }
    return {
      projectParams: '',
      form: {
        rec_name: '',
        rec_time: '',
        cover_image: '',
        img_content_id: '',
        // 编辑属性
        id: '',
        rec_info: '',
        source_code: ''
      },
      rules: {
        source_code: [
          { required: true, message: '请输入栏目名', trigger: ['blur'] }
        ],
        rec_time: [
          { required: true, message: '请选择推广时间', trigger: ['change'] }
        ],
        cover_image: [
          { required: true, validator: validImg, trigger: 'blur' }
        ]
      },
      options: [],
      pickerOptions: {
        disabledDate(time) {
          const today = new Date()
          today.setHours(0, 0, 0, 0) // 设置为今天的0点，确保日期比较准确
          
          return time.getTime() < today.getTime() || 
            (time.getFullYear() === today.getFullYear() && time.getMonth() === today.getMonth() && time.getDate() === today.getDate())
        }
      },
      moduleName: 'add'
    }
  },
  methods: {
    initData(data) {
      const { moduleName = 'add', id = '', image_url = '', order_no = '', img_content_id = '', rec_time = '', rec_name = '', recommend_item_id = '', recommend_module_id = '', source_code = '' } = data
      this.moduleName = moduleName
      if (data.moduleName === 'edit') {
        this.form.rec_info = {
          name: rec_name,
          value: source_code
        }
        this.form.id = id
        this.form.order_no = order_no
        this.form.img_content_id = img_content_id
        this.form.rec_name = rec_name
        this.form.recommend_item_id = recommend_item_id
        this.form.recommend_module_id = recommend_module_id
        this.form.cover_image = image_url
        this.form.rec_time = rec_time
        this.form.busi_type = data.busi_type.split(',')
        this.form.source_code = source_code
      }
    },
    getRecommendName() {
      getRecommendName({ project: this.projectParams }).then(res => {
        this.options = res
      })
    },
    handleSuccessImage (val, file, id) {
      this.form.cover_image = val
      this.form.img_content_id = id
    },
    handleClearImg() {
      this.form.cover_image = ''
      this.form.img_content_id = ''
    },
    closeDialog() {
      this.$refs.form.clearValidate()
      this.$refs.upload.clearImg()
      this.form = {
        rec_name: '',
        rec_time: '',
        cover_image: '',
        img_content_id: '',
        // 编辑属性
        id: '',
        rec_info: '',
        source_code: ''
      }
      this.$emit('update:visible', false)
    },
    handleSave() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const { rec_time, cover_image, img_content_id, id = '', source_code = '' } = this.form
          let data = {
            rec_time,
            busi_type: 1,
            img_content_id,
            image_url: cover_image,
            status: 1,
            order_no: 1,
            rec_name: this.options.find(item => item.value === source_code).name,
            source_code: source_code,
            id
          }
          this.whiteInfo(data)
        } else {
          return false
        }
      })
    },
    whiteInfo(data) {
      let api = this.moduleName === 'add' ? addNetCoursePerson : updateNetCoursePerson
      if (this.moduleName === 'add') {
        delete data.id
      }
      api(data).then(res => {
        this.$message.success(this.moduleName === 'add' ? '添加成功' : '修改成功')
        this.$emit('onSearch')
        this.closeDialog()
      })
    }
  }
}
</script>

<style lang="less" scoped>
.recommend-dialog {
  .rec-time {
    // :deep(.el-input__prefix) {
    //   right: -257px;
    // }
    // :deep(.el-input__inner) {
    //   padding-left: 15px;
    // }
  }
}
.recommend-body {
  :deep(.el-form-item__label) {
    line-height: 40px;
  }
}
</style>
