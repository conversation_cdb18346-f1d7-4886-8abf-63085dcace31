<template>
  <div class="task-content">
    <iframe
      v-if="iframeSrc"
      class="task-iframe"
      id="taskIframe"
      :src="iframeSrc"
      frameborder="0"
      allowfullscreen
    ></iframe>
    <div v-else class="task-error">
      <img class="error-img" src="@/assets/mooc-img/page-inaccessible.png" alt="无法访问">
    </div>
  </div>
</template>

<script>
import { SDKUtils } from 'sdc-moocjs'
import translate from '@/mixins/translate.vue'
import { mapState } from 'vuex'
import {
  targetAB
} from 'config/api.conf'
export default {
  name: 'taskContent',
  mixins: [translate],
  components: {},
  props: {
    currentTask: Object,
    btnOptions: Object
  },
  data() {
    return {
      geekSourceLoading: false, // 极客资源是否开始加载~  true:开始加载极客内容  false:未开始加载或者跳转到登录页面
      iframeDOM: '',
      iframeSrc: '',
      loading: false,
      isNormalScrom: false,
      isFromQuestionTask: false
    }
  },
  watch: {
    'currentTask.task_id'(val) {
      if (val) {
        const { resource_type } = this.currentTask
        if (location.host.indexOf('.woa.com') > -1 && ['Scorm', 'Zip', 'Flash'].includes(resource_type)) {
          document.domain = 'woa.com'
        } else if (location.host.indexOf('.oa.com') > -1 && ['Scorm', 'Zip', 'Flash'].includes(resource_type)) {
          document.domain = 'oa.com'
        }
        this.loading = true
        this.getResourcePaths()
      }
    }
  },
  computed: {
    ...mapState(['userInfo']),
    isGeek() {
      return this.currentTask?.act_type === '102' || false
    },
    ...mapState(['userInfo'])
  },
  mounted() {
    // 监听loading变化
    window.addEventListener('message', ({ data: { page, loading, geekSourceLoading, methodsType, from, type = '', event = '' } }) => {
      // 来自问卷手动调用完成的方法
      console.log(page, loading, geekSourceLoading, methodsType, from, type, event, 'eventeventeventeventeventeventeventeventeventeventeventeventeventeventeventeventevent')
      if (type === 'questionnaire' && event === 'completeStatusUpdata') {
        console.log('问卷手动调用了完成的方法')
        this.$parent.handleSaveLearnRecord(true, {})
        this.isFromQuestionTask = true
      }
      page === 'iframe' && this.iframeLoadingChange(loading, geekSourceLoading)
    })
    SDKUtils.registerMessageListener()
    SDKUtils.onload(() => {
      if (this.currentTask.finished_condition?.type === '2') {
        // 当任务完成条件为至少学习多少分钟时，通知v8系统隐藏视频完成状态
        let iframeDOM = this.getIframeDom()
        SDKUtils.hideFinishStatus(iframeDOM)
      }
    })
    SDKUtils.onPlay(() => {
      this.$sdc.loading.hide()
      this.loading = false
      this.$parent.startTime()
    })
    SDKUtils.onPause(() => {
      this.$parent.pauseTime()
    })
    SDKUtils.onComplete(res => {
      let isInit = res.data.params === 'init'

      this.$parent.completeLearn(isInit)
    })
    SDKUtils.onErrorInfo(res => {
      this.$parent.handleErrorInfo(res.data.params)
    })
    SDKUtils.onStratAnswer(() => {
      this.$parent.jumpAnswerPage(true)
    })
    SDKUtils.onEndAnswer(res => {
      this.$parent.endAnswer(res.data.params)
    })
    SDKUtils.onAnswerDetail(() => {
      this.$parent.jumpAnswerPage(false)
    })
    SDKUtils.onDetailBackHome(() => {
      this.$parent.showNavBar = true
    })
    SDKUtils.messageListener((res) => {
      if (res.events === 'updateStatus') { 
        this.$parent.onUpdateStatus(res.params)
      }
      if (res.events === 'scormInit') {
        this.isNormalScrom = true
        this.$parent.startTime()
      }
      if (res.events === 'interactiveDialog') {
        this.$parent.setInteractiveStatus(res.params)
      }

      if (res.events === 'completeStatusUpdata') {
        this.$parent.onUpdateStatus()
        SDKUtils.postMessage(this.getIframeDom(), 'btnOptionsChange', this.btnOptions)
      }
      if (res.events === 'questionPageClick') {
        this.$emit('receiveMessage', res)
      }
      // 更新已完成状态
      if (res.events === 'completeStatusUpdata') {
        this.$parent.handleSaveLearnRecord(true, {})
        this.isFromQuestionTask = true
      }
    })

    // this.handleVisibilityEvent = this.handleVisibilityEvent.bind(this) 
    // document.addEventListener('visibilitychange', this.handleVisibilityEvent)
  },
  beforeDestroy() {
    SDKUtils.removeMessageListener()
    // document.removeEventListener('visibilitychange', this.handleVisibilityEvent)
  },
  methods: {
    // 控制iframe的loading显示
    iframeLoadingChange(loading = true, geekSourceLoading = false) {
      loading ? this.$sdc.loading() : this.$sdc.loading.hide()
      geekSourceLoading && (this.geekSourceLoading = true)
      if (!loading) this.$emit('loading')
    },
    getResourcePaths() {
      let { act_type = '', act_id = '', mooc_course_id = '', task_id = '', resource_type, finished_condition, resource_url } = this.currentTask
      const previewType = this.$route.query?.previewType || ''
      const from = this.$route.query?.from || ''
      const area_id = this.$route.query?.area_id || ''
      if (act_type === '102') { // 极客
        // if (process.env.NODE_ENV === 'development') {
        //   // this.iframeSrc = `http://test.woa.com:8088/training/outsourcedCourse/video/play?course_id=${act_id}&mooc_course_id=${mooc_course_id}&comeFrom=outCourse&from=${from}&area_id=${area_id}`
        //   // this.iframeSrc = `http://test.woa.com:8088/training/outsourcedCourse/graphic/play?course_id=${act_id}&mooc_course_id=${mooc_course_id}&comeFrom=outCourse&from=${from}&area_id=${area_id}`
        //  // this.iframeSrc = `http://test.woa.com:8088/training/outsourcedCourse/iframe/play?course_id=${act_id}&mooc_course_id=${mooc_course_id}&task_id=${task_id}&comeFrom=outCourse&from=${from}&area_id=${area_id}`
        // } else {
        this.iframeSrc = resource_url + `&mooc_course_id=${mooc_course_id}&task_id=${task_id}&comeFrom=outCourse&from=${from}&area_id=${area_id}`
        // }
      } else if (act_type === '2') {
        if (['Video', 'Audio'].includes(resource_type)) {
          // 音视频关闭防挂机功能
          this.$parent.timer.antiHangUp = false
        }
        // v8系统网络课
        // this.iframeSrc = process.env.VUE_APP_V8_HOST_WOA + `/user/net?act_id=${act_id}&jump_from=mooc&project=${mooc_course_id}&source=ql&from=mooc&area_id=${task_id}&previewType=${previewType}`

        // this.iframeSrc = process.env.VUE_APP_V8_HOST_WOA + `/training/netcourse/play?course_id=${act_id}&from=mooc&task_id=${task_id}&mooc_course_id=${mooc_course_id}&previewType=${previewType}`

        // 处理跳转到灰度页面，iframe接收不到双语的问题 2024-12-3 （网络课有新旧两个页面，原逻辑是iframe直接跳到旧网课，然后请求接口再跳到新网页，导致iframe接收不到双语）
        targetAB().then((res) => {
          if (res) {
            // 通过 `vm` 访问组件实例
            this.iframeSrc = `/training/netcourse/grayPlay?course_id=${act_id}&from=mooc&task_id=${task_id}&area_id=${mooc_course_id}&previewType=${previewType}`
          } else {
            // 有分享人的时候调用接口记录分享
            this.iframeSrc = `/training/netcourse/play?course_id=${act_id}&from=mooc&task_id=${task_id}&area_id=${mooc_course_id}&previewType=${previewType}`
          }
        })
      } else if (act_type === '20') {
        // 考试系统
        this.iframeSrc = process.env.VUE_APP_EXAM + `/exam/user/exam?exam_id=${act_id}&from_system=mooc&from_act_id=${task_id}&mooc_course_id=${mooc_course_id}&previewType=${previewType}`
        // this.iframeSrc = `//test.woa.com:8080/exam/user/exam?exam_id=${act_id}&from_system=mooc&&from_act_id=${task_id}&mooc_course_id=${mooc_course_id}&previewType=${previewType}`
      } else if (act_type === '10') {
        // 文档
        this.iframeSrc = process.env.VUE_APP_V8_HOST_WOA + `/user/profile?wordId=${act_id}&from=mooc&previewType=${previewType}`
      } else if (act_type === '18') {
        // 新图文、文章
        this.iframeSrc = process.env.VUE_APP_V8_HOST_WOA + `/training/graphic/user/preview?graphic_id=${act_id}&from=mooc&previewType=${previewType}&task_id=${task_id}&mooc_course_id=${mooc_course_id}`
        // this.iframeSrc = `/training/graphic/user/preview?graphic_id=${act_id}&from=mooc&task_id=${task_id}&mooc_course_id=${mooc_course_id}`
      } else if (act_type === '21') {
        // 课程素材
        this.iframeSrc = process.env.VUE_APP_V8_HOST_WOA + `/training/material/play?material_id=${act_id}&from=mooc&previewType=${previewType}`
      } else if (act_type === '22') {
        // 作业客户端
        this.iframeSrc = process.env.VUE_APP_V8_HOST_WOA + `/training/work/detail?homework_id=${act_id}&task_id=${task_id}&act_id=${mooc_course_id}&from=mooc&previewType=${previewType}`
        // this.iframeSrc = `/training/work/detail?homework_id=${act_id}&task_id=${task_id}&act_id=${mooc_course_id}&from=mooc&previewType=${previewType}`
      } else if (act_type === '23') {
        // 第三方任务
        this.iframeSrc = process.env.VUE_APP_V8_HOST_WOA + `/training/third-party?task_id=${task_id}&mooc_course_id=${mooc_course_id}&from=mooc&previewType=${previewType}`
        // this.iframeSrc = `/training/third-party?task_id=${task_id}&mooc_course_id=${mooc_course_id}&from=mooc&previewType=${previewType}`
      } else if (act_type === '32') {
        // 腾讯问卷
        // let fromIndex = resource_url.indexOf('from=mooc')
        // let footerIndex = resource_url.indexOf('footer')
        // this.iframeSrc = `${resource_url}${fromIndex < 0 ? '&from=mooc' : ''}${footerIndex < 0 ? '&footer=0' : ''}`
        this.iframeSrc = `${resource_url}?user_id=${this.userInfo.staff_id}&from=mooc&course_id=${mooc_course_id}&task_id=${task_id}`
      } else if (act_type === '99') {
        // 外链
        this.iframeSrc = this.currentTask.resource_url
      } else {
        // 未找到资源
        this.$sdc.loading.hide()
        this.loading = false
      }
      let scormTimer = null
      this.$nextTick(() => {
        let iframeDOM = this.getIframeDom()
        if (!iframeDOM) return

        iframeDOM.onload = () => {
          // 优化极客时间的iframe的加载loading
          if (!(this.isGeek && this.geekSourceLoading)) {
            this.$sdc.loading.hide()
          }
          // this.$sdc.loading.hide()
          this.loading = false
          if (['99'].includes(act_type)) {
            this.$parent.startTime()
          }
          // 压缩包类型、scorm、Series类型完成条件是xx分钟完成，加载完成开启定时器
          // scorm类型是根据课程完成条件，则等scorm的init事件触发了再开启定时器
          if (['Zip', 'Doc', 'Flash', 'Series'].includes(resource_type) || (resource_type === 'Scorm' && finished_condition?.type * 1 === 2)) {
            this.$parent.startTime()
          }

          if (resource_type === 'Scorm' && finished_condition?.type * 1 === 1) {
            scormTimer = setTimeout(() => {
              if (!this.isNormalScrom) {
                this.$message.warning('这不是一个标准的Scrom课件，请注意完成条件')
              }
            }, 5000)
          }
          if (act_type === '23') {
            let iframeDOM = this.getIframeDom()
            SDKUtils.postMessage(iframeDOM, 'taskInfo', this.currentTask)
          }
          // 视频音频，考试，素材，作业, 第三方
          if (['2', '20', '21', '22', '23'].includes(act_type)) {
            this.changeMoocLang()
          }
          // 任务状态正常，未完成学习
          if (!this.currentTask.is_finished && !this.currentTask.task_learn_status) {
            // 应学任务，并且不是文章类型才提示任务完成条件
            if (this.currentTask.required && this.currentTask.resource_type !== 'Article') {
              if (this.currentTask.conditionType * 1 === 2 && this.currentTask.finished_condition?.condition) {
                this.$message({
                  dangerouslyUseHTMLString: true,
                  showClose: true,
                  message: `<p>${this.$langue('Mooc_Common_Alert_TaskFinishWarning', { defaultText: '任务完成条件提醒' })}</p>
                  <p style="color: #ED7B2F">
                    ${this.$langue('Mooc_TaskDetail_Navigation_MinStudyTime', { minute: this.currentTask.finished_condition?.condition || 0, defaultText: `至少学习${this.currentTask.finished_condition?.condition}分钟` })}
                  </p>`,
                  customClass: 'finished-condition-tips'
                })
              }
            }
          } 
        }
      })

      let iframeTimer = null
      if (act_type === '99') {
        // 20s后外链还未onload，则提示资源加载失败
        iframeTimer = setTimeout(() => {
          if (this.loading) {
            this.$sdc.loading.hide() 
            this.loading = false
            this.iframeSrc = ''
          }
        }, 20000)
      }

      this.$once('hook:beforeDestroy', () => {
        clearTimeout(scormTimer)
        clearTimeout(iframeTimer)
        scormTimer = null
        iframeTimer = null
      })
    },
    getIframeDom() {
      return document.getElementById('taskIframe')
    },
    // 通知第三方资源暂停播放
    setVideoPause() {
      let iframeDOM = this.getIframeDom()
      SDKUtils.setPause(iframeDOM)
    },
    // 通知第三方资源暂停播放
    setVideoPlay() {
      let iframeDOM = this.getIframeDom()
      SDKUtils.setPlay(iframeDOM)
    },
    // 通知iframe多语变化
    changeMoocLang() {
      let iframeDOM = this.getIframeDom()
      let lang = localStorage.getItem('sdc-sys-def-lang')
      SDKUtils.postMessage(iframeDOM, 'tencent-mooc-lang', lang)
      this.getLangJS()
    }
    // 切出当前页面时暂停第三方资源播放
    // handleVisibilityEvent() {
    //   switch (document.visibilityState) {
    //     case 'hidden':
    //       this.setVideoPause()
    //       break
    //     }
    // }
  }
}
</script>
<style lang="less" scoped>
.task-content {
  position: relative;
  height: 100%;

  .task-iframe {
    width: 100%;
    height: 100%;
  }
}
.task-error{
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  .error-img {
    width: 240px;
    height: 148px;
  }
  .tips {
    font-size: 18px;
    font-weight: 600;
    margin: 24px 0;
  }
}
</style>
<style lang="less">
.el-message.finished-condition-tips{
  top: 50%!important;
  padding: 24px;
  border-radius: 12px;
  .el-message__icon{
    display: none;
  }
  .el-message__closeBtn {
    top: 15px;
    right: 10px;
  }
  .el-message__content{
    line-height: 22px;
    text-align: center;
  }
}
</style>
