// 外部课程使用了geekBang的页面，video和graphic

// 内容详情页面logo---pc、移动是同一个logo
const detailLogo = {
  geekBang: require('@/assets/mooc-img/comment/geek-time.png'),
  imooc: require('@/assets/mooc-img/comment/mukewang.png'),
  sanjieke: require('@/assets/mooc-img/comment/sanjieke-detail.png'),
  harvard: require('@/assets/mooc-img/comment/harvard.png'),
  hundun: require('@/assets/mooc-img/comment/hundun.png')
}

// 封面logo-pc
const pcCoverLogo = {
  geekBang: require('@/assets/mooc-img/geek_time.png'),
  imooc: require('@/assets/mooc-img/mukewang_mobile.png'),
  sanjieke: require('@/assets/mooc-img/sanjieke-pc-logo.png'),
  hundun: require('@/assets/mooc-img/comment/hundun.png')
}

export { detailLogo, pcCoverLogo }
