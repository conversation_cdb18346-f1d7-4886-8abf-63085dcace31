<template>
  <div class="registration-approval-popup activity-common">
    <!-- Mobile -->
    <div class="mobile-template" v-if="isMobile">
      <van-dialog className="van-dialog-customer" overlayClass="overlay-customer" v-model="visible" :showConfirmButton="false">
        <div class="van-dialog-header">报名审批</div>
        <div class="van-dialog-content">
          <div class="tips-box">
            <p>当前活动报名需上级审批</p>
            <p v-if="outOfTown">- 开班地点为【{{courseData.city}}】，此次报名属于异地报名</p>
            <p v-if="longDuration">- 活动时长较长，此次活动为跨天活动</p>
          </div>
          <div class="need-appovel" v-if="isNeedAppovel">
            <p class="title">是否确认报名，并提交上级审批</p>
            <div class="parent-name">
              <P><i style="color: red;margin-right:3px">* </i> 直接上级</P>
              <sdc-staff-selector ref="mobileSelectorStaff" v-model="staffId" size="mini" selectClass="selectClass" modalClass="modalClass" showFullTag placeholder="请选择直接上级" disabled @change="handlerChangeStaff"></sdc-staff-selector>
            </div>
          </div>
        </div>
        <div class="btn-colume mt-36">
          <div class="btn-box cancel-button" @click="handlerCancel()">取消</div>
          <div class="btn-box confirm-button" @click="handlerConfirm">确认</div>
        </div>
      </van-dialog>
    </div>

    <!-- PC -->
    <div class="pc-template" v-else>
      <el-dialog title="" :visible.sync="visible" width="564px" :close-on-click-modal="false" :before-close="handlerCancel">
        <div slot="title" class="dialog-header">报名审批</div>
        <div class="dialog-content">
          <div class="tips-box">
            <p>当前活动报名需上级审批</p>
            <p v-if="outOfTown">- 开班地点为【{{courseData.city}}】，此次报名属于异地报名</p>
            <p v-if="longDuration">- 活动时长较长，此次活动为跨天活动</p>
          </div>
          <div class="need-appovel" v-if="isNeedAppovel">
            <p class="title">是否确认报名，并提交上级审批</p>
            <div class="parent-name"><i style="color: red;margin-right:3px">* </i> 直接上级
              <sdc-staff-selector ref="pcSelectorStaff" v-model="staffId" size="medium" selectClass="selectClass" modalClass="modalClass" showFullTag placeholder="请选择直接上级" disabled @change="handlerChangeStaff"></sdc-staff-selector>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button class="cancel-btn" @click="handlerCancel()" size="small">取消</el-button>
          <el-button class="confirm-btn" type="primary" @click="handlerConfirm" size="small">确认</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
export default {
  name: 'chooseParticipationMethodPopup',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    courseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      staffId: '',
      form: {
        parent_staff_id: '',
        parent_name: ''
      }
    }
  },
  computed: {
    isMobile () {
      var ua = navigator.userAgent.toLowerCase()
      return /iphone|ipad|ipod|android|blackberry|mini|windows\sce|phone|mobile/.test(ua) || /micromessenger/.test(ua)
    },
    isNeedAppovel() {
      return this.courseData.need_appovel
    },
    // 异地
    outOfTown () {
      const { work_city, city } = this.courseData
      if (work_city && city) {
        return work_city.indexOf(city) === -1 
      }
      return false
    },
    // 跨天
    longDuration () {
      const { start_time, end_time } = this.courseData
      if (start_time && end_time) {
        const startTime = new Date(start_time)
        const endTime = new Date(end_time)

        const startYear = startTime.getFullYear()
        const startMonth = startTime.getMonth()
        const startDay = startTime.getDate()

        const endYear = endTime.getFullYear()
        const endMonth = endTime.getMonth()
        const endDay = endTime.getDate()

        return startYear !== endYear || startMonth !== endMonth || startDay !== endDay
      }
      return false
    }
  },
  watch: {
    visible: {
      handler(val) {
        console.log('visible', val)
        if (val) {
          const userInfo = JSON.parse(sessionStorage.getItem('login_user_dep'))
          if (userInfo && userInfo.parent_staff_id) {
            const { parent_staff_id, parent_name } = userInfo
            this.staffId = userInfo.parent_staff_id
            setTimeout(() => {
              const name = this.isMobile ? 'mobileSelectorStaff' : 'pcSelectorStaff'
              const parentStaff = {
                StaffID: parent_staff_id,
                StaffName: parent_name
              }
              this.form.parent_staff_id = parent_staff_id
              this.form.parent_name = parent_name
              this.$refs[name].setSelected(parentStaff)
            }, 400)
          }
        }
      },
      immediate: true
    }
  },
  created() { },
  mounted() { },
  beforeDestroy() { },
  methods: {
    handlerChangeStaff(val) {
      const { StaffID, StaffName } = val
      this.form.parent_staff_id = StaffID
      this.form.parent_name = StaffName
    },
    handlerConfirm() {
      if (this.isNeedAppovel && !this.form.parent_staff_id) {
        this.$message.error('请选择直接上级')
        return
      }
      this.$emit('confirm', this.form)
      this.$emit('update:visible', false)
    },
    handlerCancel() {
      this.$emit('update:visible', false)
      this.$emit('cancel')
    }
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/activity-common.less';
  .registration-approval-popup {
    .mobile-template {
      :deep(.overlay-customer) {
        background: #00000066;
      }
      :deep(.van-dialog-customer) {
        padding: 32px 24px 24px;
        width: 332px;
        border-radius: 12px;
        background: #FFF;
      }

      .van-dialog-header {
        color: #000000e6;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px;
        margin-bottom: 20px;
      }

      .van-dialog-content {
        .tips-box {
          color: #000000e6;
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: 24px;
          margin-bottom: 16px;
        }
        
        .need-appovel {
          color: #000000e6;
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 500;
          line-height: 24px;
          .parent-name {
            color: #000000e6;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            &>p {
              line-height: 1;
              padding: 12px 0 8px;
            }
          }
        }
        :deep(.sdc-selector ) {
          .selector-container--normal {
            border-radius: 4px;
            border-right: 1px solid #dcdcdc;
          }
          .suffix-open .el-button {
            display: none;
          }
        }
      }

      .btn-colume {
        margin-top: 36px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .btn-box {
          width: 136px;
          line-height: 24px;
          padding: 8px 16px;
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 6px;
        }
        .cancel-button {
          background: #F2F3FF;
          color: #0052d9;
        }
        .confirm-button {
          color: #ffffff;
          background: #0052D9;
        }
      }

    }

    .pc-template {
      .dialog-content {
        :deep(.el-dialog__body) {
          padding: 28px 32px 16px;
        }
        .tips-box {
          color: #000000e6;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          margin-bottom: 16px;
        }
        .need-appovel {
          width: 100%;
          .title {
            color: #000000e6;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: 22px;
            margin-bottom: 12px;
          }
          .parent-name {
            color: #000000e6;
            display: flex;
            align-items: center;
            padding-bottom: 4px;
            /deep/.sdc-staff-selector {
              width: 424px;
              margin-left: 10px;
            }
          }
        }
      }
    }
  }
</style>
