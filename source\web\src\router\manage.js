const manage = [
  {
    path: '/manage/distributePage',
    name: 'distributePage',
    component: () => import('views/manage/distributePage/list.vue'),
    meta: {
      title: '分发页管理'
    }
  },
  {
    path: '/manage/distributePageAdd',
    name: 'distributePageAdd',
    component: () => import('views/manage/distributePage/Add.vue'),
    meta: {
      title: '创建分发页'
    }
  },
  {
    path: '/manage/polymer-list',
    name: 'polymerList',
    component: () => import('@/views/webManage/aggregate/polymerList.vue'),
    meta: {
      title: '聚合报名列表'
    }
  },
  {
    path: '/manage/createAggregate',
    name: 'createAggregate',
    component: () => import('@/views/webManage/aggregate/createAggregate.vue'),
    meta: {
      title: '聚合报名创建'
    }
  },
  {
    path: '/manage/question',
    name: 'questionAggregate',
    component: () => import('@/views/webManage/aggregate/question.vue'),
    meta: {
      title: '问卷'
    }
  }
]

export default manage
