<template>
  <div class="ai-approve-container">
    <p class="ai-title">AI快捷做课审核</p>
    <div class="tab-box">
      <ul class="ul-tabs-chapter">
          <li 
          v-for="item in tabList" 
          :key="item.value"
          :class="[{'active': activeName === item.value}, 'li-tab-item']"
          @click.stop="changeTab(item.value)"
          >
            {{ item.label }}
          </li>
      </ul>
      <!-- ai做、课 -->
      <div class="course-info-box" v-show="activeName === 'basic'">
        <div class="content-main">
          <p class="info-title"><span class="pseudo-class-title">课程信息</span><span class="tips">检查确认在线课程信息</span></p>
          <course-info 
          ref="courseInfo" 
          :courseInfo="courseInfo" 
          :directlyUpload="false"
          videoStatus="add"
          :approveStatus="true"
          />
        </div>
        <div class="ppt-box">
          <p class="info-title"><span class="pseudo-class-title">PPT和文本解析</span></p>
          <ppt-upload :virtualInfo="virtualInfo" :approveStatus="true"/>
        </div>
      </div>
      <!-- 分段章节 -->
      <div class="chapters-content" v-show="hasPermission && (activeName === 'chapters')">
        <span class="pseudo-class-title">分段章节</span>
        <!-- 章节配置 -->
        <chapters-config 
          :approveStatus="true" 
          :activeName="activeName" 
          :estDur="courseInfo.est_dur" 
          :videoInfo="courseInfo.content_info || {}" 
          :chaptersList="chaptersList"
          :courseInfo.sync="courseInfo"
          @refreshChapterList="refreshChapterList" 
          @exportAiData="exportAiData"
          ref="chaptersConfig"
        >
        </chapters-config>
      </div>
      <!-- ai文章 -->
      <aiArticle
      class="ai-article-content-l"
      v-if="activeName === 'aiArticle'" 
      :courseData.sync="courseInfo"
      @getCourse="getCourse"
      :approveStatus="true"
      >
      </aiArticle>
    </div>
    <!-- 底部按钮区域 -->
    <div class="buttom" v-if="activeName === 'basic'">
      <div class="inner">
        <el-button :loading="approveLoading" @click="handleRefuseShow()" :disabled="courseInfo.status !== '6'">拒绝</el-button>
        <el-button :loading="approveLoading" type="primary" @click="handleApprove(1)" :disabled="courseInfo.status !== '6'">审核通过</el-button>
      </div>
    </div>
    <!-- 审核拒绝 -->
    <refuseDialog :refuseShow.sync="refuseShow" @refuseConfirm="handleApprove"></refuseDialog>
  </div>
</template>
<script>
import courseInfo from '../../user/netcourse/course-make/components/course-info.vue'
import pptUpload from '../../user/netcourse/course-make/components/PPT-upload.vue'
import refuseDialog from '../../user/netcourse/course-make/components/refuseDialog.vue'
import chaptersConfig from '../../user/netcourse/course-make/components/chapters-config.vue'
import aiArticle from '../../user/netcourse/course-make/components/aiArticle.vue'
import {
  approveAiDetail,
  approveStatus,
  checkTarget,
  getNetCourseChapters
} from 'config/api.conf'
// import moment from 'moment'
export default {
  components: {
    courseInfo,
    pptUpload,
    refuseDialog,
    chaptersConfig,
    aiArticle
  },
  data() {
    return {
      activeName: 'basic',
      hasPermission: false, // 是否有权限访问章节配置
      tabList: [
        { label: '基础配置', value: 'basic' },
        { label: '分段章节', value: 'chapters' },
        { label: 'AI文章', value: 'aiArticle' }
      ],
      chaptersList: [], // 分段章节配置数据
      courseInfo: {
        content_info: {
          file_name: '',
          content_id: '',
          file_size: ''
        },
        status: ''
      },
      virtualInfo: {},
      approveLoading: false,
      refuseShow: false
    }
  },
  mounted() {
    this.getCourse()
    const id = this.$route.query.net_course_id
    checkTarget().then((res) => {
      this.hasPermission = res || false
      if (id && this.hasPermission) {
        this.getChaptersInfo(id)
      }
    })
  },
  methods: {
    changeTab(tab) {
      // 分段章节导入并修改
      this.activeName = tab
      if (this.activeName === 'chapters') {
        // this.$refs.demoPreview && this.$refs.demoPreview.pause()
      // 查询分段章节配置
      } else {
        this.$refs.chaptersConfig && this.$refs.chaptersConfig.pause()
      }
    },
    // 刷新章节配置
    refreshChapterList() {
      let id = this.$route.query.net_course_id
      if (id) {
        this.getChaptersInfo(id)
      }
    },
    // 获取章节设置
    getChaptersInfo(id) {
      const params = { 
        course_id: id 
      }
      getNetCourseChapters(params).then(res => {
        // console.log(res, '获取章节配置返回的res-------')
        this.chaptersList = res || []
      })
    },
    exportAiData(data) {
      const { chaptersRecord } = data
      this.chaptersList = chaptersRecord
    },
    // 获取课程详情
    getCourse() {
      const { net_course_id } = this.$route.query
      approveAiDetail(net_course_id, 2).then((res) => {
        this.virtualInfo = res.virtual_info || {}
        this.courseInfo = res
        if (this.courseInfo.status !== '6') {
          this.$message.warning('该课程已审批')
        }
      })
    },
    handleApprove(approve_result, review_failed_reason) {
      this.approveLoading = true
      const { net_course_id } = this.$route.query
      let params = {
        act_type: 2,
        course_id: net_course_id,
        approve_result,
        is_mobile: 1
      }
      if (approve_result === 2) {
        params.review_failed_reason = review_failed_reason
      }
      console.log(params)
      approveStatus(params).then((res) => {
        this.approveLoading = false
        const msg = approve_result === 2 ? '审核拒绝' : '审核通过'
        this.courseInfo.status = approve_result === 2 ? '8' : '7'
        this.$message.success(msg)
        window.opener.workReConnect()
        setTimeout(() => {
          window.close()
        }, 200)
      })
    },
    handleRefuseShow() {
      this.refuseShow = true
    }
  }
}
</script>
<style lang="less">
@import '~@/assets/css/graphic-common.less';
@import '~@/assets/css/ai-common.less';
@import '~@/assets/css/center.less';
.ai-approve-container {
  padding: 20px 0;
  padding-bottom: 70px;
  background-color:#fff;
  margin-bottom: 70px;
  .ai-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 32px;
    padding: 0 20px;
  }
  .tab-box {
    .ul-tabs-chapter {
      height: 32px;
      line-height: 32px;
      border: 0;
      display: flex;
      margin-bottom: 16px;
      background: #F6F7F9;
      .li-tab-item {
        padding: 0 20px;
        width: 100px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        margin: 0;
        border: 0;
        font-family: "PingFang SC";
        font-size: 14px;
        color: #00000099;
        box-sizing: border-box;
        cursor: pointer;
      }
      .li-tab-item.active {
        color: #0052d9;
        text-align: center;
        font-weight: 600;
        background-color: #fff;
      }
    }
  }
  .info-title {
    .tips {
      color: rgba(0,0,0,0.4);
      display: inline-block;
      margin-left: 12px;
    }
    .pseudo-class-title {
      position: relative;
      padding-left: 16px;
      font-size: 16px;
      font-weight: bold;
      color: rgba(0,0,0,0.8);
      font-family: "PingFang SC";
      margin-bottom: 20px;
      display: inline-block;
    }
    .pseudo-class-title::before {
      position: absolute; 
      top: 50%; 
      transform: translatey(-50%); 
      left: 0; 
      content: ''; 
      width: 4px;
      height: 18px;
      background-color: #0052D9; 
    } 
  }
  .course-info-box {
    padding: 0 20px;
    margin-bottom: 32px;
  }
  .chapters-content {
    min-height: 696px;
    padding: 32px 28px 28px;
    background-color: #fff;
    height: 100%;
    .chapters-upload {
      width: 366px;
      background: #FBFBFB;
    }
  }
  .ai-article-content-l {
    background-color:#F6F7F9;
  }
  .buttom {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70px;
    line-height: 70px;
    background-color: #fff;
    text-align: center;
    z-index: 99;

    .inner {
      display: inline-block;
      padding-left: 188px;
      // text-align: left;

      @media screen and (max-width: 1660px) {
        width: 1158px;
      }

      @media screen and (min-width: 1661px) {
        width: 1440px;
      }

      .el-button {
        margin: 0 20px 0 0;
        width: 104px;
      }

      .tips {
        margin-left: 20px;
        color: #999;
      }
    }
  }
  .refuse-project-dialog {
    .tips {
      margin-bottom: 8px;
      display: inline-block;
      color: #e34d59ff;
    }
  }
}
</style>
