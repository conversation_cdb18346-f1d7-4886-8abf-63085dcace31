<template>
  <div class="activity-sign">
    <van-dialog className="van-dialog-customer" overlayClass="overlay-customer" v-model="visible" :showConfirmButton="false">
      <div class="sign-in-success" v-if="signInStatus">
        <div class="logo">
          <img :src="signInSuccess.logo" alt="" />
        </div>
        <div class="title">{{ signInSuccess.title }}</div>
        <div class="date-row"><span>签到时间：</span>{{ signInSuccess.signInTime }}</div>
        <div class="activity-name ellipsis-2"><span>活动：</span>{{ signInSuccess.activityName }}</div>
      </div>
      <div class="sign-in-fail" v-else>
        <div class="logo">
          <img :src="signInFail.logo" alt="" />
        </div>
        <div class="title"><span v-html="signInFail.title"></span><br>[{{ signInFail.classAdviser }}]</div>
        <div class="activity-name ellipsis-2"><span>活动：</span>{{ signInFail.activityName }}</div>
      </div>
      <div class="confirm-button" @click="handlerconfirm">好的</div>
    </van-dialog>
  </div>
</template>

<script>
import { activityStudentSignApi } from '@/config/classroom.api.conf.js'

export default {
  name: 'activitySign',
  components: {},
  props: {},
  data() {
    return {
      visible: false,
      signInSuccess: { 
        logo: require('@/assets/classroomImg/signIn-success.png'), 
        title: '签到成功', 
        activityName: '活动标题示例',
        signInTime: '2022-01-01 12:00:00'
      },
      signInFail: { 
        logo: require('@/assets/classroomImg/signIn-fail.png'), 
        title: '签到失败说明', 
        activityName: '活动标题示例',
        classAdviser: 'vincentyqwu'
      },
      signInStatus: true
    }
  },
  watch: {},
  computed: {
    activityId () {
      return this.$route.query.activity_id
    },
    signOrder () {
      return this.$route.query.sign_order
    }
  },
  created() { },
  mounted() {
    if (this.activityId && this.signOrder) {
      this.handlerSignIn()
    }
  },
  beforeDestroy() { },
  methods: {
    // 学员签到
    async handlerSignIn() {
      activityStudentSignApi({ class_id: this.activityId, act_id: this.activityId, sign_order: this.signOrder }).then(res => {
        const { success, activity_name, sign_date, msg, staff_name } = res
        if (success) {
          this.signInStatus = true
          this.signInSuccess.activityName = activity_name
          this.signInSuccess.signInTime = sign_date
          this.visible = true
        } else {
          this.signInStatus = false
          this.signInFail.title = msg
          this.signInFail.activityName = activity_name
          this.signInFail.classAdviser = staff_name
          this.visible = true
        }
      })
    },
    handlerconfirm() {
      this.visible = false
    }
  }
}
</script>

<style lang="less" scoped>
.activity-sign {
  :deep(.overlay-customer) {
    background: #00000066;
  }
  :deep(.van-dialog-customer) {
    padding: 24px;
    width: 311px;
    border-radius: 12px;
    background: #FFF;
  }

  .logo {
    width: 100%;
    height: 80px;
    margin-bottom: 8px;
    display: flex;
    justify-content: center;
    & > img {
      width: 80px;
      height: 80px;
    }
  }

  .title {
    color: #000000e6;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px;
    margin-bottom: 12px;
  }

  .activity-name {
    color: #000000e6;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    &>span {
      color: #00000099;
    }
  }

  .sign-in-success {
    .date-row {
      color: #000000e6;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
      line-height: 24px;
      margin-bottom: 8px;
      &>span {
        color: #00000099;
      }
    }
  }

  .confirm-button {
    width: 263px;
    line-height: 24px;
    padding: 8px 16px;
    margin-top: 24px;
    color: #ffffff;
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    flex: 1 0 0;
    border-radius: 6px;
    background: #0052D9;
  }
  
  .ellipsis-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
