<template>
  <div class="mooc-mobile-page">
    <router-view :key="pathKey"></router-view>
  </div>
</template>
<script>
export default {
  computed: {
    pathKey() {
      return this.$route.query?.course_id || this.$route.query?.task_id || ''
    }
  }
}
</script>
<style lang="less" scoped>
.mooc-mobile-page {
  height: 100%;
}
</style>
<style lang="less">
.interaction-toast,.vertical-dialog, .en-vertical-dialog {
  transform: translateX(-50%) translateY(-50%) rotate(90deg);
  transition: unset;
}
</style>
