<template>
  <div class="data-count">
    <div class="header-top">
      <span class="title">库存规则配置</span>
    </div>
    <div class="search-content">
      <el-table
        :data="tableData.records"
        style="width: 600px"
        header-row-class-name="table-header-style"
        row-class-name="table-row-style"
        class="table-content"
      > 
        <el-table-column prop="title" label="标题名称" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="table-course-title" @click="toManagePage(scope.row, 'name')">{{ scope.row.title }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <div class="operat-btn-box">
              <el-link @click="toManagePage(scope.row)" type="primary" :underline="false">编辑</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script>
import { 
  getStockDescApi
} from '@/config/mooc.api.conf.js'
export default {
  components: {
  },
  data () {
    return {
      tableData: {
        records: []
      }
    }
  },
  mounted() {
    this.getStockDesc()
  },
  methods: {
    getStockDesc() {
      getStockDescApi().then(res => {
        if (res) {
          this.tableData.records = JSON.parse(res)
        }
      })
    },
    toManagePage({ graphic_url, graphic_id }, value) {
      if (value === 'name') { // 展示页
        window.open(graphic_url)
      } else { // 编辑页
        let prefix = process.env.NODE_ENV === 'production' ? '//portal.learn.woa.com' : '//test-portal-learn.woa.com'
        window.open(prefix + '/training/graphic/user/create?graphic_id=' + graphic_id)
      }
    }
  }
}
</script>

<style lang="less" scoped>
  .data-count {
    height: 100%;
    background: #fff;
    .header-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid rgba(243, 243, 243, 1);
      padding: 16px 20px;
      .title {
        font-size: 16px;
        height: 32px;
        line-height: 32px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9)
      }
    }
    .search-content {
      padding: 18px 20px;
      .table-course-title {
        color: #0052D9;
        cursor: pointer;
      }
    }
  }
</style>
