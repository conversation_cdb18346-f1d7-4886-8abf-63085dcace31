<template>
  <el-dialog
    :visible.sync="isShowCreateEditPopup"
    custom-class="create-edit-popup"
    :title="popupType==='look'? '查看公告' : (!noticeDetails ? '新建公告' : '编辑公告')"
    width="785px"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :before-close="closeDialog">
    <el-form :model="form" ref="form" label-width="80px">
      <el-form-item label="公告名称" class="is-required">
        <div class="input-count-style" v-if="popupType!=='look'">
          <el-input v-if="popupType!=='look'" v-model.trim="form.title" placeholder="请输入公告名称" size="small" ></el-input>
          <span class="custom-el-input-count">{{handleValidor(form.title, 30)}}/30</span>
        </div>
        <div v-else><span>{{ form.title }}</span></div>
      </el-form-item>
      <el-form-item label="公告内容" class="editor-con is-required">
        <sdc-mce-editor
          v-if="popupType!=='look'"
          ref="editor"
          :env="editorEnv"
          :content="form.content"
          :catalogue.sync="editorConfig.catalogue"
          :urlConfig="editorConfig.urlConfig"
          :options="editorConfig.options"
          :insertItems="insertItems"
          >
        </sdc-mce-editor>
        <p v-else class="rich-text" v-html="form.content"></p>
      </el-form-item>
      <el-form-item label="发布时间">
        <template v-if="popupType!=='look'">
          <el-radio-group v-model="form.publishType" @change="publishTypeChange">
            <el-radio :label="1">立即发布</el-radio>
            <el-radio :label="0">定时发布</el-radio>
          </el-radio-group>
          <el-date-picker
            v-if="form.publishType === 0"
            class="date-time-picker"
            v-model="form.publishTime"
            type="datetime"
            size="small"
            placeholder="请选择发布时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="pickerOptions">
          </el-date-picker>
        </template>
        <template v-else>
          <label style="margin-right:10px;">{{ form.publishType === 1 ? '立即发布' : '定时发布' }}</label>
          <span v-if="form.publishType === 0">{{ form.publishTime }}</span>
        </template>
      </el-form-item>
    </el-form>
    <div slot="footer" class="footer-con">
      <convention-confirm v-model="isChooseConvention" style="margin-left: 20px;" v-if="popupType !== 'look'" />
      <el-button @click="closeDialog" style="margin-left: auto;">取 消</el-button>
      <el-button v-if="popupType!=='look'" type="primary" @click="saveEvent" :disabled="!isChooseConvention">保 存</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { createEditNoticeAPI } from '@/config/mooc.api.conf.js'
import conventionConfirm from '@/views/components/convention-confirm.vue'
export default {
  components: {
    conventionConfirm
  },
  props: {
    isShowCreateEditPopup: {
      type: Boolean,
      default: false
    },
    popupType: {
      type: String,
      default: ''
    },
    noticeDetails: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      form: {
        title: '',
        content: '',
        publishType: 1,
        publishTime: ''
      },
      insertItems: [
        'link',
        'table',
        'image',
        'profile',
        'embed',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorConfig: {
        catalogue: false,
        options: { // 非必传字段
          // selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector: 'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
            formatselect fontsizeselect lineheight |
            bold italic underline strikethrough |
            forecolor backcolor |
            dent align bullist numlist |
            insert codesample |
            fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      },
      editorEnv: process.env.NODE_ENV,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < (new Date() - 86400000)
        }
      },
      isChooseConvention: false
    }
  },
  mounted () {
    if (this.noticeDetails) {
      this.form.title = this.noticeDetails.title
      this.form.content = this.noticeDetails.content
      if (this.noticeDetails.status === '0') {
        this.form.publishType = this.noticeDetails.prepare_publish_time ? 0 : 1
        this.form.publishTime = this.noticeDetails.prepare_publish_time
      }
      this.$nextTick(() => {
        const imgArr = Array.from(document.querySelectorAll('img')).filter(
          (v) => v.getAttribute('data-content')
        )
        imgArr.map((el) => {
          const contentId = el.getAttribute('data-content')
          const srcLink = this.editorConfig.urlConfig.preview.replace(
            '{contentId}',
            contentId
          )
          el.setAttribute('src', srcLink)
        })
      })
    }
  },
  methods: {
    // 保存
    saveEvent () {
      let content = this.$refs['editor'].getContent()
      this.form.content = content
      if (content.indexOf('data-content=') === -1) {
        content = this.handleLabel(content)
        if (!content) {
          this.$message.warning('请填写公告内容')
          return
        }
      }
      if (!this.form.title) {
        this.$message.warning('请填写公告名称')
        return
      }
      if (this.form.publishType === 0 && !this.form.publishTime) {
        this.$message.warning('请选择定时发布时间~')
        return
      }
      if (this.form.publishType === 0 && (new Date(this.form.publishTime) <= new Date())) {
        this.$message.warning('定时发布时间要大于等当前时间~')
        return
      }
      createEditNoticeAPI({
        act_id: this.$route.query.mooc_course_id,
        act_type: 11,
        notice_id: this.noticeDetails ? this.noticeDetails.notice_id : '',
        title: this.form.title,
        content: this.form.content,
        status: this.form.publishType,
        prepare_publish_time: this.form.publishTime
      }).then(() => {
        this.closeDialog()
        this.$emit('updateNoticeList')
      })
    },
    // 过滤html标签
    handleLabel(str) {
      var msg = str.replace(/<[^>]+>/g, '')
      msg = msg.replace(/[|]*\n/, '')
      msg = msg.replace(/&npsp;/ig, '')
      return msg
    },
    // 关闭弹窗
    closeDialog () {
      this.$emit('update:isShowCreateEditPopup', false)
    },
    handleValidor(value, num) {
      if (!value) return 0
      const china = value.match(/[\u4e00-\u9fa5]/g)
      const zhCount = china && china.join('').length
      const enCount = Math.ceil((value.length - zhCount) / 2)
      const total = zhCount + enCount
      if (total > num) {
        this.form.title = value.slice(0, -1)
      }
      return zhCount + enCount 
    },
    publishTypeChange(val) {
      if (val === 1) {
        this.form.publishTime = ''
      }
    }
  }
}
</script>

<style lang="less" scoped>
:deep(.create-edit-popup) {
  transform: none;
  left: 0;
  right: 0;
  top: 15vh;
  margin: 0 auto;
  .el-dialog__body {
    padding: 20px 30px;
  }
  .editor-con {
    .tox.tox-tinymce {
      border: 1px solid #dcdfe6;
      .tox-sidebar-wrap .tox-edit-area {
        min-height: 200px;
      }
    }
  }
  .date-time-picker {
    margin-left: 15px;
    .el-input__inner {
      padding-left: 30px;
    }
  }
  .input-count-style {
    position: relative;
    .el-input .el-input__inner {
      padding-right: 44px;
    }
    .custom-el-input-count {
      height: 32px;
      padding: 0 8px;
      color: #ACACAC;
      size: 12px;
      position: absolute;
      right: 0;
    }
  }
  .rich-text img {
    max-width: 100%;
  }
  .footer-con {
    display: flex;
    align-items: center;

  }
}
</style>
