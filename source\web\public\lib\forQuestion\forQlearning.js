(function() {
  const createStyleSheet = (id, href) => {
    const linkElement = document.createElement('link')
    linkElement.id = id
    linkElement.rel = 'stylesheet'
    linkElement.type = 'text/css'
    linkElement.href = href

    const existingLink = document.getElementById(id);
    if (existingLink) {
      existingLink.remove()
    }

    document.head.appendChild(linkElement)
  }
  try {
    const query = new URLSearchParams(window.location.search)
    const mode = query.get('mode') || 'normal'

    switch(mode) {
      case 'qlearningCheckData':
        createStyleSheet('qlearning-check-data-style', 'https://cdnyewutest.yunassess.com/knowledgeservice/lib/forQuestion/forQlearning.css')
        break;
      default:
        break;
    }
  } catch (error) {}
}())