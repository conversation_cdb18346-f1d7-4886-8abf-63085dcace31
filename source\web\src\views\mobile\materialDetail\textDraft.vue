<template>
  <div class="text-draft-main">
    <div class="time-text-title">
      <span>时间点</span>
      <span>文本内容</span>
    </div>
    <div class="caption-box">
      <div 
      v-for="(item, index) in captionData" 
      :key="index" 
      :id="item.IntStartTime"
      :class="[{ 'row-content-active': captionCurTime >= item.IntStartTime && captionCurTime < item.IntEndTime }, 'row-content']"
      @click="toCaption($event, item, index)"
      >
        <div class="caption-item" :class="[{'visible-item': visibleIndex === index && showLine}]">
          <div class="caption-time">
            <span class="play-time" :class="[{'visible-active': visibleIndex === index && showLine}]">{{ item.startTime?.split(',')[0] }}</span>
          </div>
          <p class="play-title" :class="[{'visible-active': visibleIndex === index && showLine}]">{{ item.caption }}</p>
        </div>
      </div>
    </div>
    <div v-if="!autoScroll" class="come-back-top" @click="backTo">
      <span>回到当前位置</span>
      <span class="back-icon"></span>
    </div>
  </div>
</template>
<script>
import { debounce } from '@/utils/tools.js'
export default {
  props: {
    captionData: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      captionCurTime: null,
      draftScrollTop: 0,
      autoScroll: true,
      tiggerPlay: false,
      showLine: true,
      visibleHeightStart: 0,
      visibleHeightEnd: 0,
      visibleHeight: 0,
      visibleIndex: -1
    }
  },
  mounted() {
    window.addEventListener('touchstart', this.touchstart)
    window.addEventListener('touchmove', this.handleTouchMove, true)
    window.addEventListener('scroll', this.handleScroll, true)
  },
  destroyed() {
    window.removeEventListener('touchmove', this.handleTouchMove, true)
    window.removeEventListener('touchstart', this.touchstart)
    window.removeEventListener('scroll', this.handleScroll, true)
  },
  methods: {
    touchstart() {
      // 播放了滚动才显示
      if (this.tiggerPlay) {
        this.autoScroll = false
        if (!this.showLine) this.showLine = true
      }
    },
    // 手动滚动
    handleTouchMove: debounce(function () {
      // if (this.autoScroll) this.autoScroll = false
      if (!this.showLine) this.showLine = true
    }, 100),
    // 触发自动滚动
    scrollTopContent(curTime) {
      this.tiggerPlay = true
      let curDom = document.getElementsByClassName('row-content-active')[0]?.previousElementSibling
      // 当前字幕的高度
      this.captionCurTime = Number(curTime.toFixed(2))
      if (curDom) {
        this.draftScrollTop = curDom.offsetTop - 30
      }
      // 手动滚动就停止自动滚动
      if (this.showLine) return
      // 自动滚动的高度
      if (curDom && curDom.offsetTop) {
        if (this.showLine) this.showLine = false
        const captionBox = this.$el.querySelector('.caption-box')
        captionBox.scrollTop = curDom.offsetTop - 30
      }
    },
    // 回到对应的位置
    backTo() {
      this.autoScroll = true
      this.showLine = false
      const captionBox = this.$el.querySelector('.caption-box')
      captionBox.scrollTop = this.draftScrollTop
    },
    // 跳转至此
    toCaption(e, data, index) {
      e.stopPropagation()
      this.$emit('toCaption', data)
      const captionBox = this.$el.querySelector('.caption-box')
      let curDom = document.getElementsByClassName('row-content')[index]
      this.captionCurTime = data.IntStartTime
      if (index === 0) {
        captionBox.scrollTop = 0
      } else {
        if (curDom) {
          captionBox.scrollTop = curDom.offsetTop - 30
        } 
      }
      this.showLine = false
      this.autoScroll = true
    },
    handleScroll(e) {
      // 可视区域
      if (!this.visibleHeightStart && !this.visibleHeightEnd) {
        this.$nextTick(() => {
          let boxEl = document.getElementsByClassName('caption-box')[0]
          let ElData = boxEl.getBoundingClientRect()
          this.visibleHeightStart = ElData.height / 2 - 46 - 16
          this.visibleHeightEnd = ElData.height / 2 - 16
        })
      }
      const scrollTop = document.getElementsByClassName('caption-box')[0].scrollTop
      let rowEl = document.getElementsByClassName('row-content')
      let len = rowEl.length
      for (let i = 0; i < len; i++) {
        let rowHeight = rowEl[i].offsetTop + rowEl[i].offsetHeight
        if (rowHeight > scrollTop + this.visibleHeightStart && rowEl[i].offsetTop < scrollTop + this.visibleHeightEnd) {
          this.visibleIndex = i
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.text-draft-main {
  background-color: #fff;
  height: calc(100% - 32px);
  .time-text-title {
    height: 36px;
    line-height: 36px;
    background: #f8f8f8ff;
    color: #000000cc;
    font-size: 12px;
    padding-left: 16px;
    margin: 16px;
    span:last-child {
      margin-left: 24px;
    }
  }
  .caption-box {
    height: calc(100% - 36px);
    overflow: auto;
    position: relative;
    padding-bottom: 60%;
    .row-content + .row-content {
      margin-top: 8px;
    }
    .row-content {
      padding: 8px 32px 8px 0px;

      .play-time {
        color: #00000099;
        font-size: 12px;
        line-height: 20px;
        margin-right: 16px;
        margin-left: 34px;
      }
      .play-title {
        color: #00000066;
        font-size: 14px;
        line-height: 22px;
        letter-spacing: 0.28px;
        flex: 1;
        word-break: break-all;
      }
      .caption-item {
        display: flex;
        .caption-time {
          width: 100px;
        }
      }
    }
    .row-content-active {
      .caption-time {
        width: 120px !important;
      }
      .play-time, .play-title {
        color: #0052D9;
        font-weight: bold;
        font-size: 16px;
      }
    }
    .row-content:hover {
      background: #f9fbffff;
    }
    .center-line {
      width: 100%;
      position: fixed;
      top: 0;
      left: 0;
      text-align: right;
    }
    .visible-item {
      .caption-time {
        width: 120px !important;
      }
      .play-time, .play-title {
        color: #000000;
        font-weight: bold;
        font-size: 16px;
      }
    }
  }
}
.come-back-top {
  position: fixed;
  bottom: 96px;
  right: 16px;
  width: 114px;
  height: 32px;
  border-radius: 70px;
  opacity: 1;
  background: #ffffffff;
  box-shadow: 0 0 12px 0 #ddddddff;
  text-align: center;
  line-height: 32px;
  color: #0052D9;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 12px 0 #ddddddff;
  .back-icon {
    background: url('~@/assets/img/mobile/back.png') no-repeat center/cover;
    width: 16px;
    height: 16px;
    display: inline-block;
  }
}
</style>
