<template>
  <div class="activity-rules-popup">
    <van-popup v-model="popupShow" round :overlay-style="{ background: 'rgba(0, 0, 0, 0.4)'}">
      <div class="popup-card">
        <div class="popup-head">
          <div class="title">活动规则</div>
        </div>
        <div class="popup-body">
          <div class="content" v-html="explainData.explain"></div>
          <van-button class="btn" @click="onClose">确定</van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { 
  Popup
} from 'vant'

export default {
  props: {
    value: {
      type: Boolean,
      default: true,
      require: true
    },
    explainData: {
      type: Object,
      default: null
    }
  },
  components: {
    [Popup.name]: Popup
  },
  watch: {
    value(newVal) {
      this.popupShow = newVal
    } 
  },
  data() {
    return {
      popupShow: this.value
    }
  },
  methods: {
    // 关闭弹窗
    onClose() {
      this.popupShow = false
      this.$emit('input', this.popupShow)
    }
  }
}
</script>

<style lang='less' scoped>
  .popup-card {
    width: 82vw;
    height: 62vh;
    letter-spacing: 0.5px;
    color: rgba(16, 16, 16, 1);
    display: flex;
    flex-direction: column;
    .popup-head {
      padding: 24px 24px 8px;
      position: relative;
      flex-shrink: 0;
      .title {
        align-self: stretch;
        color: #000000e6;
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        line-height: 26px;
      }
    }
    .popup-body {
      flex: 1;
      padding: 0px 24px 24px 24px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      .content {
        flex: 1;
        overflow-y: auto;
      }
      .btn {
        flex-shrink: 0;
        margin-top: 24px;
        padding: 8px 16px;
        justify-content: center;
        align-items: center;
        gap: 4px;
        border-radius: 6px;
        background: #0052D9;
        color: #ffffff;
        font-size: 16px;
        font-weight: 600;
      }
    }
  }
</style>
