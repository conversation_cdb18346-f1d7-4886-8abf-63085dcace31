<template>
  <div class="extend-container">
    <detailCard v-for="(item, index) in extandList" :isMinute="true" :key="index"  :cardData="item" :courseData="courseData" :courseType="courseType" entry="extend"></detailCard>
  </div>
</template>
<script>
import detailCard from './child/detailCard.vue'
export default {
  components: {
    detailCard
  },
  props: {
    extandList: {
      type: Array,
      default: () => ([])
    },
    courseData: {
      type: Object,
      default: () => ({})
    },
    // 默认网络课
    courseType: {
      type: String,
      default: 'net'
    }
  },
  data() {
    return {
    }
  },
  methods: {
  }
}
</script>
<style lang="less" scoped>
.extend-container {
  height: 100%;
  overflow-y: auto;
  padding: 20px 16px;
  background: #fff;
  padding-bottom: 114px;
}
</style>
