<template>
  <div class="project-detail-common-box">
    <div class="train-tips">
      <div class="train-tips-left">
        <!-- <div class="course-tag">
          <span class="excellent-tag" v-if="showOutcourseIcon">极客时间</span>
        </div> -->
        <el-image
          lazy fit="fill"
          :src="imgUrl ? imgUrl : require('@/assets/mooc-img/default_bg_img.png')"
          class="project-img"
        >
         <div class="error-cover" slot="error">
            <img :src="require('@/assets/mooc-img/default_bg_img.png')" alt="" />
          </div>
        </el-image>
        <div :class="[moocLang === 'en-us' ? 'en-update-status' : 'update-status', 'common-update-status']" v-show="projectManageInfo.serial_type === 2 && !showOutcourseIcon"></div>
        <img class="geek-time" v-if="showOutcourseIcon" :src="typeImg" alt="">
      </div>
      <div class="train-tips-right">
        <div class="item-title">
          <div class="left-title">{{ projectManageInfo.course_title || projectManageInfo.course_title_en }}</div>
          <span :class="['right-tag',{'del-tag': projectManageInfo.info_sec_status === 0}, {'no-pass-tag': projectManageInfo.info_sec_status === 2}]" v-if="[0, 2].includes(projectManageInfo.info_sec_status) && projectManageInfo.approve_status !== 6">{{ safeInfo[projectManageInfo.info_sec_status] }}</span>
          <span class="right-tag" v-else>{{ projectManageInfo.course_status_name || '进行中' }}</span>
        </div>
        <div class="tag-box">
          <span class="label">项目标签：</span>
          <div class="tag-list-box" v-if="projectManageInfo.course_labels?.length">
            <div  class="tag-list" v-for="(item, index) in projectManageInfo.course_labels" :key="index">
              <el-tooltip effect="dark" :content="item.category_full_name" placement="top-start">
                <span class="tag-value">{{ item.label_name}}</span>
              </el-tooltip>
            </div>
          </div>
        </div>
        <div class="item-state">
          <span>{{ projectTypeTime }}</span>
        </div>
        <div class="item-opreate">
          <div class="left">
            <span><i class="el-icon-s-custom icon-target"></i> {{projectManageInfo.dept_name || '--'}}</span>
            <span><i class="el-icon-s-custom icon-task"></i> {{projectManageInfo.task_count}}项任务</span>
            <span v-if="projectManageInfo.show_join_count"><i class="el-icon-s-custom icon-count"></i> {{projectManageInfo.user_count}}人参与</span>
          </div>
          <!-- approve_status-1-进行中， 6-待审核 -->
          <!-- info_sec_status，0-课程状态为待审核，null,1-审核通过，2-直接显示审核不通过） -->
          <div class="right approve-status" v-if="!isApprove && projectManageInfo.approve_status !== 6">
            <el-tooltip effect="dark" v-if="projectManageInfo.approve_status === 8 && projectManageInfo.fail_reason" :content="projectManageInfo.fail_reason" placement="top-start">
              <span class="no-pass"><i class="el-icon-warning-outline"></i>审批未通过</span>
            </el-tooltip>
            <!-- approve_status---null-表示没有审核过， 8-表示审核不通过，7-表示审核通过，6-表示待审核 -->
            <el-button v-if="projectManageInfo.approve_status === null && [null, 1].includes(projectManageInfo.info_sec_status)" type="text" class="default-text" @click="handleApplyPublish">申请发布项目</el-button>
            <!-- 0-待发布，3-已下架 -->
            <el-button v-if="projectManageInfo.approve_status === 7 && [0, 3].includes(projectManageInfo.course_status)" type="text" class="default-text" @click="handlePublish">发布项目</el-button>
            <el-button v-if="projectManageInfo.approve_status === 8" type="text" @click="handleApplyPublish">重新申请发布项目</el-button>
            <el-button v-if="projectManageInfo.approve_status === 7" type="text" @click="handleShare">分享项目</el-button>
            <!-- <el-button type="text" class="default-text" @click="handleShare('preview')">预览项目</el-button> -->
            <el-button v-if="projectManageInfo.approve_status === 7 && [null, 1].includes(projectManageInfo.info_sec_status) && projectManageInfo.course_status_name !== '已结束'" type="text" class="default-text" @click="handleStop">结束项目</el-button>
            <el-button type="text" class="default-text" @click="handleDel">删除项目</el-button>
          </div>
          <div v-if="projectManageInfo.approve_status === 6 && !isApprove" class="right approve-status"><i class="el-icon-warning-outline"></i>审批中</div>
        </div>
      </div>
    </div>
    <ShareDialog ref="shareDialog" :isShow.sync="sharedialogShow" :title="shareDialogTitle" />
  </div>
</template>
<script>
import { endProject, deleteProject, activateProject, applyApprove } from 'config/mooc.api.conf'
import { mapState } from 'vuex'
import ShareDialog from '@/views/components/shareDialog.vue'
import { pcCoverLogo } from '@/utils/outsourcedCourseMap.js'
// import env from 'config/env.conf.js'
// const projectStatusInfo = {
//   0: '未开始',
//   1: '进行中',
//   2: '已结束'
// }
const safeInfo = {
  '0': '待审核',
  '2': '审核不通过'
}
export default {
  components: {
    ShareDialog
  },
  data() {
    return {
      sharedialogShow: false,
      imgUrl: '',
      shareDialogTitle: '',
      safeInfo
    }
  },
  computed: {
    ...mapState(['projectManageInfo', 'moocLang']),
    mooc_course_id() {
      const { mooc_course_id } = this.projectManageInfo
      return mooc_course_id
    },
    projectTypeTime() {
      const { start_time, end_time, course_period_type, period_day } = this.projectManageInfo
      return course_period_type === 1 ? `项目学习起止时间：${start_time} ~ ${end_time}` : 
        course_period_type === 2 ? `限定学习周期：${period_day || 0}天` :
          course_period_type === 3 ? '不限定项目学习时间' : '-'
    },
    showOutcourseIcon() {
      let coverLogoArray = Object.keys(pcCoverLogo) || []
      return coverLogoArray.includes(this.projectManageInfo.resource_from)
    },
    typeImg() {
      return pcCoverLogo[this.projectManageInfo.resource_from] || pcCoverLogo['geekBang']
    },
    isApprove() { // 审核预览
      return this.$route.query.approve === '1'
    }
  },
  watch: {
    projectManageInfo: {
      deep: true,
      handler(data) {
        const { cover_image } = data
        this.imgUrl = cover_image
      }
    }
  },
  methods: {
    // 申请发布项目
    handleApplyPublish() {
      applyApprove(this.mooc_course_id).then((res) => {
        this.$message.success('已发布审核')
        this.$emit('updateProject')
      })
    },
    handleShare(val) {
      this.sharedialogShow = true
      const url = process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/Zkma1k' : 'http://s.test.yunassess.com/s/UxEYPE'
      if (val === 'preview') {
        this.$nextTick(() => {
          this.shareDialogTitle = '预览项目'
          const href = `${url}?mooc_course_id=${this.mooc_course_id}&previewType=preview`
          this.$refs.shareDialog.initCode({ 
            url: href,
            taskTitle: this.projectManageInfo.course_title,
            scene: `${this.mooc_course_id}_view`,
            page: 'pages/mooc/projectDetails/index',
            previewType: 'preview',
            customText: `【${this.projectManageInfo.course_title}】${href}`
          })
        })
      } else {
        this.shareDialogTitle = ''
        this.$nextTick(() => {
          const href = `${url}?scheme_type=mooc&mooc_course_id=${this.mooc_course_id}`
          this.$refs.shareDialog.initCode({ 
            url: href,
            taskTitle: this.projectManageInfo.course_title,
            scene: this.mooc_course_id,
            page: 'pages/mooc/projectDetails/index',
            customText: `【${this.projectManageInfo.course_title}】${href}`
          })
        })
      }
    },
    handleStop() {
      this.$messageBox.confirm('项目结束后，学员将无法继续学习，且不能重新发布项目，确定结束吗？', '结束项目', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        endProject({
          mooc_course_id: this.mooc_course_id,
          course_status: 2
        }).then((res) => {
          this.$message.success('结束成功')
          // 刷新数据
          this.$emit('updateProject')
        })
      })
    },
    handleDel() {
      this.$messageBox
        .prompt(
          '删除操作无法撤销，请谨慎处理！',
          {
            closeOnClickModal: false,
            customClass: 'confirm-input-message-box',
            iconClass: 'el-icon-warning-outline',
            cancelButtonClass: 'el-button--default',
            confirmButtonClass: 'el-button--danger',
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            inputPlaceholder: '请输入“确认删除”确认此次删除操作',
            inputErrorMessage: '请输入“确认删除”',
            inputValidator: function (val) {
              return !!val && val.trim() === '确认删除'
            }
          }
        ).then(({ value }) => {
          if (value && value.trim() === '确认删除') {
            deleteProject(this.mooc_course_id).then((res) => {
              this.$message.success('删除成功')
              this.$router.push({ name: 'project-list' })
            })
          }
        })
    },
    // 发布
    handlePublish() {
      const { mooc_course_id, end_time, task_count, start_time, course_period_type } = this.projectManageInfo
      const publishEndTime = new Date(end_time).getTime()
      const currentTime = new Date(start_time).getTime()
      if (task_count) { // 配置了任务
        if (Number(course_period_type) === 3) { // 不限定项目时间
          activateProject(mooc_course_id).then((res) => {
            this.$emit('updateProject')
            this.$message.success('发布成功')
          })
        } else if (publishEndTime < currentTime) { // 发布时间小于结束时间
          this.$messageBox.confirm('当前项目设置的结束时间小于当前时间，请调整项目起止时间后再发布项目', '发布项目', {
            confirmButtonText: '知道啦',
            showCancelButton: false
          })
        } else {
          this.$messageBox.confirm('项目发布后，学员可加入项目进行学习，确认发布吗？', '发布项目', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(() => {
            activateProject(mooc_course_id).then((res) => {
              this.$message.success('发布成功')
              // 刷新数据
              this.$emit('updateProject')
            })
          })
        }
      } else { // 未配置任务
        this.$messageBox.confirm('当前项目未配置任务，无法发布，请先完成任务组织再发布项目', '发布项目', {
          confirmButtonText: '知道啦',
          showCancelButton: false
        })
      }
    }
  }
}
</script>
<style lang='less' scoped>
.project-detail-common-box {
  .train-tips {
    display: flex;
    background: #fff;
    padding: 16px;
    &-left {
      width: 221px;
      height: 148px;
      position: relative;
      .geek-time {
        // width: 60px;
        height: 24px;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
      }
      .project-img{
        width: 100%;
        height: 100%;
        border-radius: 4px;
      }
      .error-cover img {
        width: 221px;
        height: 148px;
      }
      .common-update-status {
        position: absolute;
        top: 0;
        z-index: 3;
      }
      .update-status {
        background: url('~@/assets/mooc-img/updateing.png') no-repeat center/cover;
        width: 58px;
        height: 20px;
      }
      .en-update-status {
        background: url('~@/assets/mooc-img/updateingEn.png') no-repeat center/cover;
        width: 71px;
        height: 20px;
      }
    }
    &-right {
      flex: 1;
      margin-left: 20px;
      .item-title {
        margin-bottom: 10px;
        font-size: 16px;
        line-height: 24px;
        font-weight: bold;
        color: #333;
        display:flex;
        justify-content: space-between;
        .left-title {
          word-break: break-all;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
        }
        .right-tag {
          height: 20px;
          line-height: 20px;
          border-radius: 2px;
          opacity: 1;
          background: #defff1;
          color: #00ce94;
          font-size: 12px;
          padding: 0 8px;
          min-width: 52px;
          text-align: center;
        }
        .del-tag {
          background-color:#EBEFFC;
          color: #0052D9
        }
        .no-pass-tag {
          background-color: #FDECEE;
          color: #E34D59
        }
      }
      .item-state,.item-opreate{
       display: flex;
      }
      // .item-classif{
      //   // p:last-child{
      //   //   margin-left: 58px;
      //   // }
      //   p > span {
      //     margin-right: 10px;
      //   }
      // }
      .tag-box {
        display: flex;
        align-items: center;
        .label {
          width: 90px;
        }
        .tag-list-box {
          display: flex;
          flex-wrap: wrap;
          line-height: 22px;
        }

        .tag-value {
          background-color: rgba(235, 239, 252, 1);
          height: 20px;
          line-height:20px;
          font-size: 12px;
          color: rgba(0, 82, 217, 1);
          padding: 0 4px;
          border-radius: 2px;
          display: inline-block;
          margin-right: 12px;
        }
      }
      .item-state{
        margin-top: 12px;
        line-height: 20px;
        .time-box {
          display: flex;
          align-items: center;
        }
        .time {
          background: url("~@/assets/mooc-img/time.png") no-repeat center / cover;
          display: inline-block;
          width: 13px;
          height: 13px;
          margin-right: 4px;
        }
        .el-tag {
          height: 20px;
          line-height: 20px;
          margin-right: 20px;
        }
      }
      .item-opreate{
        display: flex;
        align-items: flex-end;
        justify-content: space-between;
        margin-top: 36px;
        line-height: 22px;
        .left{
          span:nth-child(2) {
            margin: 0 36px 0 36px;
          }                  
        }
        .right {
          .el-button + .el-button {
            margin-left: 24px;
          }
          .default-text {
            color: #00000099 !important;
          }
        }
        .approve-status {
          color: red;
          text-align: right;
          i {
            margin-right: 4px;
          }
          .no-pass {
            margin-right: 24px;
          }
        }
      }
    }
    .course-tag {
      position: absolute;
      right: 8px;
      top: 8px;
      z-index: 2;

      .excellent-tag {
        display: inline-block;
        padding: 0 11px;
        font-size: 12px;
        height: 18px;
        line-height: 18px;
        border-radius: 10px;
        color: #fff;
        background: #F2995F;

      }
    }
  }
}
</style>
