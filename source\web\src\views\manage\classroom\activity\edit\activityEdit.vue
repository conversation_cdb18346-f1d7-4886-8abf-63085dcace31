<template>
  <div class="activity-edit" :class="{ 'embedded-page': isEmbedded }">
    <div class="activity-box">
      <el-form :model="form" :rules="rules" ref="form" label-width="124px">
        <el-form-item label="活动名称" prop="activity_name">
          <el-input
            v-model="form.activity_name"
            placeholder="请输入活动名称"
            class="course-name-input height-32"
            @input="handleInputActivityName"
            maxlength="101"
          ></el-input>
          <div class="normal-tips" style="margin-top: 8px;display: flex;align-items: center;">
            若需
            <span class="color-red">「课程测试」</span>体验效果，请先
            <span class="color-red">
              <a href="https://iwiki.woa.com/p/4012202789" target="_blank">查看填写说明</a>
            </span>，以免耽误进度哦！
          </div>
        </el-form-item>

        <teachingTypeSettings
          ref="teachingTypeSettingsRef"
          :formData="form"
          :activityData="activityInfo"
          :disabled="disabledTeachingType"
          @validate="handleValidate"
        ></teachingTypeSettings>

        <el-form-item label="申请直播">
          <div class="flex-row">
            <el-radio-group v-model="form.apply_live" :disabled="true">
              <el-radio
                v-for="item in applyForLive"
                :label="item.value"
                :key="item.value"
                :disabled="applyLiveDisabled"
              >{{ item.label }}</el-radio>
            </el-radio-group>

            <div class="tips ml-12">
              <i class="el-icon-warning color-orange ml-12 mr-6"></i>
              <span class="color-gray">仅在创建活动时可修改此字段，如需申请直播，请访问：https://live.learn.woa.com</span>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="活动封面" prop="photo_url">
          <cut-img-upload
            ref="upload"
            @handleSuccess="handleSuccessImage"
            :dialogImageUrl="form.photo_url"
            :autoImgUrl="form.photo_url"
            @handleClearImg="handleClearImg"
            @handleImgEdit="handleImgEdit"
            :cover_imgage_storage_type="form.photo_id ? 'zhihui' : 'contentcenter'"
          >
            <template v-slot:text>
              <p>建议图片尺寸：360*240px或3:2宽高比</p>
            </template>
            <template v-slot:createImg>
              <p
                class="text-orange"
                style="color: #E37318;cursor: pointer;display: flex; align-items: center"
                @click="handleAutoImg"
              >
                <img
                  class="icon"
                  style="width:16px; height: 16px; margin-right: 4px"
                  src="~@/assets/img/tips.png"
                  alt
                />快速生成封面图
              </p>
            </template>
          </cut-img-upload>
        </el-form-item>

        <el-form-item label="内容标签" prop="labels">
          <sdc-addlabel
            v-model="form.labels"
            class="project-tag-box"
            :labelNodeEnv="labelNodeEnv"
            @getSelectedLabelList="getSelectedLabelList"
          />
        </el-form-item>

        <el-form-item label="活动详情" prop="description" class="project-detail-tincy">
          <sdc-mce-editor
            ref="editor"
            selector="activity_info1"
            :env="editorEnv"
            :content="form.description"
            :urlParams="editorConfig.urlParams"
            :catalogue.sync="editorConfig.catalogue"
            :urlConfig="editorConfig.urlConfig"
            :options="editorConfig.options"
            :insertItems="insertItems"
            :key="3"
          />
          <!-- <OteamRichText ref="oteamRichTextRef" /> -->
        </el-form-item>

        <!-- <el-form-item label="富文本预览测试">
          <OteamRichText :textValue="testValue" ref="oteamRichTextRef2" mode="preview" />
        </el-form-item> -->

        <el-form-item label="活动时间" prop="start_time">
          <div class="flex-row">
            <el-date-picker
              class="w-time"
              v-model="form.start_time"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择活动开始时间"
              @change="handleActivityTimeChange($event, 'start')"
              size="small">
            </el-date-picker>
            <div class="pl-16 pr-16">至</div>
            <el-date-picker
              class="w-time"
              v-model="form.end_time"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="请选择活动结束时间"
              @change="handleActivityTimeChange($event, 'end')"
              size="small">
            </el-date-picker>
          </div>
        </el-form-item>

        <el-form-item label="报名截止时间">
          <el-date-picker
            class="w-big height-32 line-h-32"
            v-model="form.regist_last_date"
            type="datetime"
            :disabled="form.start_time === ''"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="pickerOptions"
            placeholder="如果不填写，则默认为活动前1小时"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="注销截止时间">
          <el-date-picker
            class="w-big height-32 line-h-32"
            v-model="form.cancel_last_date"
            type="datetime"
            :disabled="form.start_time === ''"
            value-format="yyyy-MM-dd HH:mm:ss"
            :picker-options="pickerOptions"
            placeholder="如果不填写，则默认为活动前1小时"
          ></el-date-picker>
        </el-form-item>

        <el-form-item label="报名模式">
          <div class="flex-row h-normal">
            <el-radio-group
              v-model="form.can_registed"
              class="mr-12"
              @change="handleCanRegistedChange"
            >
              <el-radio
                v-for="item in canRegistedTypes"
                :label="item.value"
                :key="item.value"
              >{{ item.label }}</el-radio>
            </el-radio-group>
            <AudienceSelector
              v-if="form.can_registed === 0"
              audience
              :showTab="['unit', 'group', 'import']"
              multiple
              v-model="form.target_ids"
              ref="selector"
              appCode="qlearning"
              :env="labelNodeEnv"
              importNumber="1000"
              :isShowCount="false"
              :createStudentID="true"
              :disabled="false"
            />
          </div>
        </el-form-item>

        <el-form-item label="内部分享人员" prop="inner_teacher_info" style="width: 904px;">
          <sdc-staff-selector
            multiple
            ref="adminsInnerRef"
            v-model="inner_teacher_info"
            size="small"
            placeholder="请选择内部分享人员"
            @change="changeCourseInner"
          />
          <orange-tips
            title="若创建活动时活动分享人未确认，请务必在活动结束前将分享人补充完整，否则将影响分享人的个人积分"
            style="margin-top: 9px;"
          ></orange-tips>
        </el-form-item>

        <el-form-item label="外部分享人员" prop="outer_teacher">
          <el-input
            v-model="form.outer_teacher"
            placeholder="请输入外部分享人员"
            class="course-name-input height-32"
          ></el-input>
        </el-form-item>

        <el-form-item label="所需签到次数">
          <el-input
            class="w-normal height-32"
            v-model="form.sign_count"
            @input="positiveIntegerUpdateFilteredValue('sign_count')"
            placeholder="请输入签到次数"
            minlength="1"
            maxlength="10"
          ></el-input>
        </el-form-item>

        <el-form-item label="活动负责人" prop="head_teacher_info" style="width: 904px;">
          <sdc-staff-selector
            multiple
            ref="adminsActivityHeadrRef"
            v-model="head_teacher_info"
            size="small"
            placeholder="请选择活动负责人"
            @change="changeCourseAuth"
          />
        </el-form-item>

        <el-form-item label="更多设置">
          <div class="more-setting" @click="showMoreSettingFun">点击查看更多配置项</div>
        </el-form-item>
      </el-form>
    </div>
    <floatingButtonBar
      :config="btnConfig"
      @handleClick="handleBtnBarClick"
      style="margin: 12px 0 0 0;"
    ></floatingButtonBar>
    <more-setting-dialog
      :visible.sync="moreSettingShow"
      :formData="form"
      @confirm="moreSettingConfirm"
      @cancel="moreSettingCancel"
    />
    <!-- 一键生成项目封面 -->
    <sdc-img-cover
      ref="sdcImgCoverRef"
      :visible.sync="autoImgCoverShow"
      :imgInfo="imgInfo"
      @handleImgCoverOk="handleImgCoverOk"
    ></sdc-img-cover>

    <editTipsConfim :visible.sync="editTipsConfimShow" @getEditReason="getEditReason"></editTipsConfim>
  </div>
</template>

<script>
import { checkActivityExist } from 'config/classroom.api.conf'
import { debounce } from '@/utils/tools.js'
import {
  getActivityDetail,
  saveActivityBaseInfo
} from '@/config/classroom.api.conf.js'
import { CutImgUpload } from '@/components/index'
import { AudienceSelector } from '@tencent/sdc-audience'
import orangeTips from '../components/orangeTips.vue'
import MoreSettingDialog from '../components/moreSettingDialog.vue'
import editTipsConfim from './components/editTipsConfim.vue'
import floatingButtonBar from '../components/floatingButtonBar.vue'
import teachingTypeSettings from '../components/teachingTypeSettings.vue'
import { mapState } from 'vuex'
// import OteamRichText from '@/components/o-team-rich-text.vue'
const integerREG = /^[1-9]\d*$/
const allTarget = 2015587
export default {
  components: {
    // SelectDevicePopup,
    CutImgUpload,
    AudienceSelector,
    orangeTips,
    MoreSettingDialog,
    editTipsConfim,
    floatingButtonBar,
    teachingTypeSettings
    // OteamRichText
  },
  computed: {
    ...mapState({
      userInfo: state => state.userInfo,
      activityInfo: state =>
        state.activity.activityInfo
          ? JSON.parse(JSON.stringify(state.activity.activityInfo))
          : null
    }),
    labelNodeEnv() {
      return process.env.NODE_ENV === 'production' ? 'production' : 'test'
    },
    activityId() {
      return this.$route.query.activity_id
    },
    curStaffId() {
      return this.userInfo.staff_id
    },
    // 判断是不是活动负责人
    isActivityHead() {
      return this.activityInfo.head_teacher_id.includes(this.curStaffId)
    },
    // 活动形式是否可以选择，当前时间超过活动开始时间并且是编辑状态
    canChangeTeachingType() {
      const now = new Date()
      const startTime = new Date(this.form.start_time)
      // 修复逻辑：如果当前时间小于活动开始时间或者不是编辑状态，则可以修改
      return !(now > startTime && this.isEdit)
    },
    // 添加 isEdit 计算属性
    isEdit() {
      return !!this.activityId
    },
    // 禁用活动形式
    disabledTeachingType() {
      if (!this.activityInfo) return false
      const { status, start_time, end_time } = this.activityInfo
      if (status === 1) {
        const now = Date.now()
        const start = new Date(start_time).getTime()
        const end = new Date(end_time).getTime()
        if (now > start || now > end) {
          return true
        }
      } else if ([102, 103].includes(status)) {
        return true
      }
      return false
    },
    applyLiveDisabled() {
      if (!this.activityInfo) return false
      const { status } = this.activityInfo
      if (this.activity_id && status !== 4) {
        return true
      }
      return false
    }
  },
  watch: {
    activityInfo: {
      handler(newVal) {
        if (newVal) {
          this.initData(newVal)
        }
      },
      immediate: true,
      deep: true
    }
  },
  destroyed() {
    this.originalFormData = null
  },
  data() {
    const validInteger = (rule, value, callback) => {
      if (!value || !integerREG.test(value)) {
        return callback(new Error('请输入1-10的整数'))
      } else {
        callback()
      }
    }
    return {
      editTipsConfimShow: false,
      originalFormData: null, // 添加字段保存初始值
      form: {
        activity_name: '',
        teaching_type: '1', // 活动形式
        description: '', // 活动详情
        city: '', // 活动形式1： 活动地点 - 城市
        location: '', // 活动形式1： 活动地点 - 详细地址
        meeting_create_type: 1, // 活动形式2： 会议创建方式
        host_type: 1, // 活动形式2： 会议主持人
        meeting_rooms: [], // 活动形式2：关联会议室Rooms设备
        refuse_join_type: 1, // 活动形式2：拒绝参与人员
        start_time: '', // 活动时间 - 开始时间
        end_time: '', // 活动时间 - 结束时间
        is_limit_student_count: 0, // 人数限制
        apply_live: 0, // 申请直播
        meeting_info: {
          meeting_code: '', // 会议编号
          meeting_creator: '' // 会议创建人
        },
        course_statement: {
          operation_title: '',
          creation_source: 0,
          human_cost: 0,
          // 活动管理组织的拆分
          dept_id: '',
          dept_full_name: '',
          is_required: 0,
          activity_level: 0, // 认证等级
          join_recommend: 0, // 是否加入推荐池
          operation_level: 3, // 运营分级
          operation_project_name: '', // 分级项目具体名称
          expert_score: '' // 内容专家评分
        },
        target_ids: allTarget, // 报名模式 - 自定义开放范围
        photo_url: '', // 活动封面
        photo_id: '', // 活动封面ID
        labels: [], // 内容标签
        regist_last_date: '', // 报名截止时间
        cancel_last_date: '', // 注销截止时间
        can_registed: 0, // 报名模式
        sign_count: 1, // 所需签到次数
        reason_text: '' // 编辑原因
      },
      editorConfig: {
        catalogue: false,
        options: {
          // 非必传字段
          selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
              formatselect fontsizeselect lineheight |
              bold italic underline strikethrough |
              forecolor backcolor |
              dent align bullist numlist |
              insert codesample |
              fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          // contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          contentinfo: `/content-center/api/v1/content/save_contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        urlParams: {
          is_public: true
        },
        is_open_catalogue: false
      },
      insertItems: [
        'link',
        'table',
        'image',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorEnv: process.env.NODE_ENV,
      offline: true, // 活动形式 - 线下授课
      online: false, // 活动形式 - 腾讯会议
      rules: {
        activity_name: [
          { required: true, trigger: 'blur', validator: this.validActivityName }
        ],
        teaching_type: [
          { required: true, message: '请选择活动形式', trigger: 'change' }
        ],
        photo_url: [
          { required: true, message: '请上传活动封面', trigger: 'blur' }
        ],
        labels: [
          {
            required: true,
            message: '请选择内容标签',
            trigger: 'change',
            validator: this.validLabels
          }
        ],
        sign_count: [
          {
            required: true,
            trigger: ['blur', 'change'],
            validator: validInteger
          }
        ],
        head_teacher_info: [
          {
            required: true,
            message: '请选择活动负责人',
            trigger: 'blur',
            validator: this.validHeadTeacher
          }
        ],
        location: [
          { required: true, message: '请选择活动地点', trigger: 'blur' }
        ],
        start_time: [
          { required: true, message: '请选择活动时间', trigger: 'change' }
        ]
      },
      // 关联会议室设备选择
      selectDevicePopupShow: false,
      autoImgCoverShow: false,
      imgInfo: {},
      cityOptions: [],
      activityTime: [], // 活动时间
      teachingTypes: [
        { label: '线下授课', value: '1' },
        { label: '腾讯会议', value: '2' }
      ],
      meetingTypes: [
        { label: '系统自动创建', value: 1 },
        { label: '关联已有会议', value: 2 }
      ],
      hostTypes: [
        { label: '活动负责人', value: 1 },
        { label: '活动分享人', value: 2 }
      ],
      refuseJoinTypes: [
        { label: '完全开放', value: 0 },
        { label: '非内部员工', value: 1 },
        { label: '非目标学员', value: 2 },
        { label: '未报名学员', value: 3 }
      ],
      canRegistedTypes: [
        { label: '仅对集团正式员工开放', value: 1 },
        { label: '自定义开放范围', value: 0 }
      ],
      numberLimitTypes: [
        { label: '无限制人数', value: 0 },
        { label: '限制报名人数', value: 1 }
      ],
      pickerOptions: {},
      inner_teacher_info: [], // 内部分享人员
      outer_teacher: '', // 外部分享人员
      head_teacher_info: [], // 活动负责人
      moreSettingShow: false, // 更多设置弹窗
      btnConfig: [
        {
          type: 'cancel',
          show: true,
          disable: false,
          text: '取消',
          btnType: 'default'
        },
        {
          type: 'preview',
          show: true,
          disable: false,
          text: '预览',
          btnType: 'default'
        },
        {
          type: 'submit',
          show: true,
          disable: false,
          text: '保存',
          btnType: 'primary'
        }
      ],
      applyForLive: [
        { label: '不申请', value: 0 },
        { label: '我要申请直播', value: 1 }
      ],
      isSubmitting: false,
      // 是否内嵌页面
      isEmbedded: false
      // testValue: JSON.stringify({
      //   version: 1,
      //   type: 'doc',
      //   content: [
      //     {
      //       type: 'paragraph',
      //       content: [
      //         {
      //           type: 'text',
      //           text: '123213'
      //         }
      //       ]
      //     },
      //     {
      //       type: 'paragraph',
      //       content: [
      //         {
      //           type: 'inlineExtension',
      //           attrs: {
      //             extensionType: 'com.tencent.iwiki.editor.image',
      //             extensionKey: 'image:stable',
      //             parameters: {
      //               state: 'stable',
      //               name: 'mobile-test.jpg',
      //               size: 140618,
      //               border: false,
      //               link: '',
      //               width: 800,
      //               specifyWidth: 800,
      //               height: 639,
      //               naturalWidth: 1001,
      //               naturalHeight: 800,
      //               record: 'middle',
      //               url:
      //                 'https://contentcenter-1257413033.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/image/2025/4/29d0a17b-1a50-4700-9f61-ca5825a3755e.jpg'
      //             },
      //             localId: '5a1e7fce-d0f5-4679-b3bc-53ca257a042d'
      //           }
      //         }
      //       ]
      //     }
      //   ]
      // })
    }
  },
  created() {
    this.isEmbedded = this.$route.path === '/manage/activity/activityEdit'
  },
  mounted() {
    let that = this
    this.$nextTick(() => {
      this.pickerOptions = {
        disabledDate(time) {
          return time.getTime() > new Date(that.form.start_time).getTime()
        }
      }
    })
  },
  methods: {
    handleCanRegistedChange(val) {
      if (val * 1 === 1) {
        this.form.target_ids = allTarget
      } else {
        this.form.target_ids = ''
      }
    },
    positiveIntegerUpdateFilteredValue(type) {
      let val = Number(this.form[type])
      let isEdit = false

      if (isNaN(val) || val < 1) {
        isEdit = true
        val = 1
      }
      if (val !== parseInt(val)) {
        isEdit = true
        val = parseInt(val)
      }

      if (isEdit) this.form[type] = val
    },
    validActivityName(rule, value, callback) {
      if (!this.form.activity_name) {
        return callback(new Error('请输入活动名称'))
      }
      if (this.form.activity_name.length > 100) {
        return callback(new Error('活动名称不能超过100个字符'))
      }
      this.checkActivityExist(value).then(res => {
        if (!res) {
          callback()
        }
        if (res) {
          callback(new Error('活动名称重复'))
        }
      })
    },
    validHeadTeacher(rule, value, callback) {
      if (!this.form.head_teacher_id || this.form.head_teacher_id.length < 1) {
        return callback(new Error('请选择活动负责人'))
      }
      callback()
    },
    checkActivityExist(name) {
      return new Promise(resolve => {
        let params = {
          activity_name: name
        }
        if (this.isEdit) {
          params.activity_id = this.activityInfo.activity_id
        }
        checkActivityExist(params).then(res => {
          resolve(res)
        })
      })
    },
    handleInputActivityName: debounce(function(value) {
      if (value.length > 100) {
        this.$refs.form.validateField('activity_name')
      } else {
        this.$refs.form.clearValidate('activity_name')
      }
    }, 500),
    initData(res) {
      // 创建一个全新的深拷贝，避免引用原始数据
      const formData = JSON.parse(JSON.stringify(res))
      
      // 先重置 form 对象
      Object.keys(this.form).forEach(key => {
        this.$delete(this.form, key)
      })

      // 使用 Object.keys 遍历并赋值以保持响应式
      Object.keys(formData).forEach(key => {
        if (key === 'meeting_info') {
          this.$set(this.form, 'meeting_info', formData.meeting_info || {
            meeting_code: '',
            meeting_creator: ''
          })
        } else {
          this.$set(this.form, key, formData[key])
        }
      })

      // 使用 Promise 确保数据更新完成
      this.$nextTick().then(() => {
        this.originalFormData = JSON.parse(JSON.stringify(this.form))
      })

      const { start_time, end_time, can_registed, target_ids } = formData

      this.form.is_limit_student_count = formData.is_limit_student_count ? 1 : 0

      if (start_time && end_time) {
        this.form.start_time = start_time
        this.form.end_time = end_time
      }

      this.form.can_registed = can_registed ? 1 : 0
      this.form.target_ids = target_ids
      if (can_registed * 1 === 1) {
        this.form.target_ids = allTarget
      }

      if (formData.apply_live) {
        this.form.apply_live = 1
      } else {
        this.form.apply_live = 0
      }

      // 使用 $nextTick 确保 DOM 已更新
      this.$nextTick(() => {
        // 处理活动负责人
        let list = this.parseTeachers(
          this.form.head_teacher_id,
          this.form.head_teacher_name
        )
        if (list && list.length > 0 && this.$refs.adminsActivityHeadrRef) {
          this.$refs.adminsActivityHeadrRef.setSelected(list)
          this.head_teacher_info = list
          this.$refs.form && this.$refs.form.validateField('head_teacher_info')
        }
        // 处理内部分享人员
        let list2 = this.parseTeachers(
          this.form.inner_teacher_ids,
          this.form.inner_teacher
        )
        if (list2 && list2.length && this.$refs.adminsInnerRef) {
          this.$refs.adminsInnerRef.setSelected(list2)
        }
      })
    },
    handleDeviceChange(val) {
      this.form.meeting_rooms = val
    },
    handleSelectDevice() {
      this.selectDevicePopupShow = true
    },
    handleSuccessImage(res) {
      this.form.photo_url = res
      this.form.photo_id = ''
      this.$refs.form.validateField('photo_url')
    },
    // 清空图片
    handleClearImg() {
      this.form.photo_url = ''
    },
    handleImgEdit() {
      this.$refs.sdcImgCoverRef.outEdit({
        id: this.cover_image_id,
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        type: 'outEdit'
      })
    },
    // 一键封面回传
    handleImgCoverOk(row) {
      this.form.photo_url = row.url
      this.form.photo_id = row.id
      this.$refs.form.validateField('photo_url')
    },
    handleAutoImg() {
      this.autoImgCoverShow = true
      this.imgInfo = {
        title: this.form.activity_name,
        env: process.env.NODE_ENV === 'production' ? 'production' : 'test',
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        showEdit: true
      }
    },
    getSelectedLabelList(val) {
      this.form.labels = val.map(item => {
        return {
          ...item,
          label_type_association: 1
        }
      })
      this.$refs.form.validateField('labels')
    },
    // 活动时间 开始时间不能早于当前时间
    handleActivityTimeChange(val, type) {
      if (type === 'start') {
        if (val !== null && new Date().getTime() > new Date(val).getTime()) {
          this.$message.error('活动开始时间不能早于当前时间')
          this.$nextTick(() => {
            this.form.start_time = ''
          })
        }
      } else if (type === 'end') {
        if (val !== null && new Date(val).getTime() <= new Date(this.form.start_time).getTime()) {
          this.$message.error('活动结束时间不能早于活动开始时间')
          this.$nextTick(() => {
            this.form.end_time = ''
          })
        }
      }
    },
    // 内部分享人员
    changeCourseInner(val) {
      this.form.inner_teacher_ids = val.map(item => item.StaffID).join(';')
      this.form.inner_teacher = val.map(item => item.StaffName).join(';')
    },
    // 活动负责人
    changeCourseAuth(val) {
      this.form.head_teacher_id = val.map(item => item.StaffID).join(';')
      this.form.head_teacher_name = val.map(item => item.StaffName).join(';')
      this.head_teacher_info = val
      // 手动触发校验
      this.$refs.form.validateField('head_teacher_info')
    },
    showMoreSettingFun() {
      this.moreSettingShow = true
    },
    moreSettingConfirm(updatedForm) {
      this.moreSettingShow = false
      // 更新表单数据
      this.form = { ...updatedForm }
    },
    moreSettingCancel() {
      this.moreSettingShow = false
    },
    validLabels(rule, value, callback) {
      if (!this.form.labels.length) {
        return callback(new Error('请选择内容标签'))
      }
      callback()
    },
    validSignCount(rule, value, callback) {
      const val = Number(value)
      if (isNaN(val) || val < 1 || val !== parseInt(val)) {
        return callback(new Error('请输入正整数'))
      }
      callback()
    },
    getEditReason(val) {
      this.form.reason_text = val
      this.sub()
    },
    // 是否显示编辑提示
    isShowEditTips() {
      // 获取当前用户ID并转为数字
      let curId = Number(this.userInfo.staff_id || 0)

      // 将分号分隔的字符串转为数字数组
      const headTeacherIds = (this.form.head_teacher_id || '')
        .split(';')
        .filter(id => id) // 过滤空字符串
        .map(id => Number(id))

      return (
        this.activityId &&
        !headTeacherIds.includes(curId) &&
        Number(this.form.creator_id) !== curId
      )
    },
    getActivityInfo() {
      if (!this.activityId) return
      getActivityDetail({ activity_id: this.activityId }).then(res => {
        this.form = {
          ...res,
          meeting_info: {
            meeting_code: '',
            meeting_creator: ''
          }
        }

        const { start_time, end_time, can_registed, target_ids } = res

        this.form.is_limit_student_count = res.is_limit_student_count ? 1 : 0

        if (start_time && end_time) {
          this.form.start_time = start_time
          this.form.end_time = end_time
          this.activityTime = [start_time, end_time]
        }

        this.form.can_registed = can_registed ? 1 : 0
        this.form.target_ids = target_ids
        if (can_registed * 1 === 1) {
          this.form.target_ids = allTarget
        }

        // 处理活动负责人
        let list = this.parseTeachers(
          this.form.head_teacher_id,
          this.form.head_teacher_name
        )
        if (list?.length > 0) {
          this.$refs.adminsActivityHeadrRef &&
            this.$refs.adminsActivityHeadrRef.setSelected(list)
          this.head_teacher_info = list
          this.$refs.form.validateField('head_teacher_info')
        }
        // 处理内部分享人员
        let list2 = this.parseTeachers(
          this.form.inner_teacher_ids,
          this.form.inner_teacher
        )
        if (list2?.length) {
          this.$refs.adminsInnerRef &&
            this.$refs.adminsInnerRef.setSelected(list2)
        }
      })
    },
    parseTeachers(id, name) {
      if (!id || !name) return
      const idList = String(id)
        .split(';')
        .filter(item => item)
      const nameList = name.split(';').filter(item => item)

      const result = []

      for (let i = 0; i < idList.length; i++) {
        const id = idList[i]
        const name = nameList[i]

        const StaffID = parseInt(id.split('(')[0])
        const StaffName = name

        result.push({
          StaffID,
          StaffName
        })
      }

      return result
    },
    handleBtnBarClick(e) {
      switch (e.type) {
        case 'cancel':
          this.$router.back()
          break
        case 'preview':
          this.preview()
          break
        case 'submit':
          this.submitInfo()
          break
        default:
          break
      }
    },
    // 现在还要判断是否有activity_id,没有的话说明还没有存草稿，不能预览
    preview() {
      if (!this.activityId) {
        this.$message.warning('请先保存草稿')
        return
      }

      if (!this.isActivityHead) {
        this.$message.warning('您不是活动负责人，无法预览')
        return
      }

      const baseUrl = window.location.origin
      window.open(
        `${baseUrl}/training/activity/detail?activity_id=${this.activityId}&preview=true`
      )
    },
    handleValidate(field) {
      // 只在表单提交时进行完整验证
      if (this.isSubmitting) {
        this.$nextTick(() => {
          this.$refs.form.validateField(field)
        })
      }
    },
    submitInfo() {
      this.isSubmitting = true
      // 在表单校验前合并teachingTypeSettings组件的数据
      this.form.description = this.$refs['editor'].getContent()
      // this.getContent2()
      this.form = {
        ...this.form,
        ...this.$refs.teachingTypeSettingsRef?.form
      }
      // 使用 $nextTick 确保在 DOM 更新后执行验证
      this.$nextTick(() => {
        this.$refs.form.validate(valid => {
          if (valid) {
            // 处理报名截止时间和注销截止时间的默认值
            if (!this.form.regist_last_date && this.form.start_time) {
              const startTime = new Date(this.form.start_time)
              startTime.setHours(startTime.getHours() - 1)
              this.form.regist_last_date = this.dateToStr(startTime)
            }
            if (!this.form.cancel_last_date && this.form.start_time) {
              const startTime = new Date(this.form.start_time)
              startTime.setHours(startTime.getHours() - 1)
              this.form.cancel_last_date = this.dateToStr(startTime)
            }
            // 如果是编辑状态且开始时间早于当前时间，则提示并恢复到原来的时间
            // const startTime = new Date(this.form.start_time)
            const endTime = new Date(this.form.end_time)
            const currentTime = new Date()
            if (this.isEdit && endTime < currentTime) {
              this.$message.warning('活动结束时间不能早于当前时间')
              // 恢复到原来的时间
              // this.activityTime = [
              //   this.activityInfo.start_time,
              //   this.activityInfo.end_time
              // ]
              // this.form.start_time = this.activityInfo.start_time
              // this.form.end_time = this.activityInfo.end_time
              return
            }
            // 校验活动形式
            if (!this.validateActivityData(this.form)) {
              return
            }
            // 参与形式 去除未选中的
            const { teaching_type, city, meeting_info } = this.form
            if (teaching_type.indexOf('1') === -1 && city) {
              this.form.city = ''
              this.form.location = ''
            }
            if (teaching_type.indexOf('2') === -1 && meeting_info?.meeting_code) {
              this.form.meeting_create_type = 0
              this.form.meeting_info = {}
            }

            if (
              this.dateSub(this.form.regist_last_date, this.form.start_time) > 0
            ) {
              this.$message.error('报名截止时间不能晚于活动开始时间')
              return
            }
            if (
              this.dateSub(
                this.form.regist_last_date,
                this.form.cancel_last_date
              ) > 0
            ) {
              this.$message.error('报名截止时间不能晚于注销截止时间')
              return
            }
            if (
              this.dateSub(this.form.cancel_last_date, this.form.start_time) > 0
            ) {
              this.$message.error('注销截止时间不能晚于活动开始时间')
              return
            }
            if (this.isShowEditTips()) {
              this.editTipsConfimShow = true
              return
            }
            this.beforeSubmit()
          }
          this.isSubmitting = false
        })
      })
    },
    beforeSubmit() {
      const { apply_live } = this.form
      const { auto_send_calendar } = this.activityInfo || {}
      // 检查日历相关字段是否有变化
      if (auto_send_calendar && this.hasCalendarFieldsChanged()) {
        const h = this.$createElement
        this.$msgbox({
          title: '提示',
          closeOnClickModal: false,
          customClass: 'activity-edit-msgbox',
          message: h('p', null, [
            h(
              'span',
              null,
              '此次保存将更新已发送日程的邀约，是否确认保存? 如不希望触发日程更新提醒，请点击页面顶部"日程和企微群"，并关闭"通知功能-自动发送日程邀约"功能后再进行保存'
            )
          ]),
          showCancelButton: true,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
          .then(action => {
            if (action === 'confirm') {
              this.handleLiveConfirmation(apply_live)
            }
          })
          .catch(() => {})
        return
      }

      this.handleLiveConfirmation(apply_live)
    },
    // 处理直播确认逻辑
    handleLiveConfirmation(apply_live) {
      if (apply_live) {
        const h = this.$createElement
        this.$msgbox({
          title: '提示',
          closeOnClickModal: false,
          customClass: 'activity-edit-msgbox',
          message: h('p', null, [
            h(
              'span',
              null,
              '当前活动创建时曾申请直播，编辑活动信息后，如需修改直播申请相关信息，可访问 '
            ),
            h(
              'a',
              {
                attrs: { href: 'https://live.learn.woa.com' },
                style: { color: '#409EFF' }
              },
              'https://live.learn.woa.com'
            ),
            h('span', null, '。如有直播相关疑问，可企微咨询"小T(连线HR)"')
          ]),
          showCancelButton: true,
          confirmButtonText: '确定'
        }).then(action => {
          if (action === 'confirm') {
            this.sub()
          }
        })
      } else {
        this.sub()
      }
    },
    // 检查日历相关字段是否有变化
    hasCalendarFieldsChanged() {
      if (!this.originalFormData) {
        return false
      }

      // 创建深拷贝用于比较
      const currentForm = JSON.parse(JSON.stringify(this.form))

      // 检查活动名称变化
      if (currentForm.activity_name !== this.originalFormData.activity_name) {
        return true
      }

      // 检查活动时间变化
      if (
        currentForm.start_time !== this.originalFormData.start_time ||
        currentForm.end_time !== this.originalFormData.end_time
      ) {
        return true
      }

      // 检查线下授课地点变化
      const oldHasOffline = (
        this.originalFormData.teaching_type || ''
      ).includes('1')
      const newHasOffline = (currentForm.teaching_type || '').includes('1')
      if (oldHasOffline && newHasOffline) {
        if (
          currentForm.city !== this.originalFormData.city ||
          currentForm.location !== this.originalFormData.location
        ) {
          return true
        }
      }

      // 检查教学类型变化（包括取消腾讯会议的情况）
      const oldHasOnline = (this.originalFormData.teaching_type || '').includes(
        '2'
      )
      const newHasOnline = (currentForm.teaching_type || '').includes('2')
      if (oldHasOnline !== newHasOnline) {
        return true
      }

      // 检查会议信息变化
      const currentMeetingInfo = currentForm.meeting_info || {}
      const originalMeetingInfo = this.originalFormData.meeting_info || {}

      // 检查会议号变化
      if (
        currentMeetingInfo.meeting_code !== originalMeetingInfo.meeting_code
      ) {
        return true
      }

      // 检查创建人变化
      if (
        currentMeetingInfo.meeting_creator !==
        originalMeetingInfo.meeting_creator
      ) {
        return true
      }

      // 检查会议链接变化
      if (currentMeetingInfo.meeting_url !== originalMeetingInfo.meeting_url) {
        return true
      }

      return false
    },
    sub() {
      this.$refs.teachingTypeSettingsRef && this.$refs.teachingTypeSettingsRef.clearMeetingInfo()
      saveActivityBaseInfo(this.form).then(res => {
        // 保存成功后立即更新初始值
        this.$nextTick().then(() => {
          this.originalFormData = JSON.parse(JSON.stringify(this.form))
        })
        this.$emit('updateData')
        // 更新Vuex中的activityInfo
        // this.$store.commit('activity/SET_ACTIVITY_INFO', this.form)
        this.$message.success('保存成功')
      })
    },
    validateActivityData(data) {
      const {
        teaching_type,
        city,
        location,
        meeting_create_type,
        meeting_info
      } = data

      if (!teaching_type) {
        this.$message.warning('请选择活动形式')
        return false
      }
      const teachingArr = teaching_type.split(';')
      if (
        teachingArr.includes('1') &&
        !this.validateOfflineFields(city, location)
      ) {
        return false
      }
      if (
        teachingArr.includes('2') &&
        !this.validateMeetingFields(meeting_create_type, meeting_info)
      ) {
        return false
      }
      return true
    },
    validateOfflineFields(city, location) {
      if (!city) {
        this.$message.warning('活动形式-线下授课所在城市不能为空')
        return false
      }
      if (!location) {
        this.$message.warning('活动形式-线下授课所在地点不能为空')
        return false
      }
      return true
    },
    validateMeetingFields(meeting_create_type, meeting_info) {
      const { meeting_code, meeting_creator, meeting_url } = meeting_info || {}
      if (meeting_create_type === 2) {
        if (!meeting_code || !meeting_creator) {
          this.$message.warning('活动形式-关联已有会议，会议号和创建人不能为空')
          return false
        }
        if (!meeting_url) {
          this.$message.warning('活动形式-关联已有会议，请对会议信息做校验')
          return false
        }
      }
      return true
    },
    dateSubOneHour(date) {
      let oDate = new Date(this.formateDate(date))
      const newTime = oDate.getTime()
      let newDate = new Date(newTime)
      return this.dateToStr(newDate)
    },
    // 时间比较
    dateSub(date1, date2) {
      const oDate1 = new Date(this.formateDate(date1))
      const oDate2 = new Date(this.formateDate(date2))
      return oDate1.getTime() - oDate2.getTime()
    },
    formateDate(dateStr) {
      return dateStr.replace(/-/g, '/')
    },
    dateToStr(d) {
      let yyyy = d.getFullYear().toString()
      let mm = this.paddingZero(d.getMonth() + 1)
      let dd = this.paddingZero(d.getDate())
      let hh = this.paddingZero(d.getHours())
      let min = this.paddingZero(d.getMinutes())
      let ss = this.paddingZero(d.getSeconds())
      return yyyy + '-' + mm + '-' + dd + ' ' + hh + ':' + min + ':' + ss
    },
    paddingZero(v) {
      const val = parseInt(v)
      if (val >= 0 && val < 10) {
        return '0' + val
      }
      return v
    },
    getContent2() {
      this.$refs.oteamRichTextRef.getContent().then(res => {
        console.log(res, 'res')
      })
    }
  }
}
</script>
<style lang="less">
</style>
<style lang="less" scoped>
.project-detail-tincy {
  .editor__area {
    background: #fff;
  }
  :deep(.activity_info1) {
    .tox.tox-tinymce {
      box-sizing: border-box;
      border: 1px solid #ccc !important;
      height: 451px;
      width: 785px;
      margin: initial;
      .tox-sidebar-wrap .tox-edit-area {
        min-height: 374px !important;
        box-sizing: border-box;
      }
    }
  }
  :deep(.activity_info1.fullscreen .tox.tox-tinymce) {
    width: unset;
  }
}
.activity-edit {
  height: 100%;
  overflow: auto;
  display: flex;
  flex-direction: column;

  :deep(.el-checkbox), :deep(.el-checkbox-button__inner), :deep(.el-radio) {
    font-weight: 400;
  }

  .activity-box {
    background: #fff;
    padding: 32px 0 22px 0;
    margin-bottom: 72px;
  }
  .normal-tips {
    color: #999999;
    font-family: 'PingFang SC';
    font-size: 12px;
    line-height: initial;
    .color-red,
    a {
      color: #e34d59;
    }
    a {
      text-decoration: underline;
    }
  }
  .normal-tips2 {
    color: #666666;
    font-family: 'PingFang SC';
    font-size: 12px;
    margin-top: -6px;
    display: block;
    line-height: initial;
  }
  .floating-button-bar {
    position: fixed;
    bottom: 0;
    z-index: 100;
    box-shadow: 1px 1px 2px 2px #f6f7f9;
  }
}

@media screen and (max-width: 1660px) {
  .embedded-page .floating-button-bar {
    width: 988px;
    left: calc(50% - 1180px / 2 + 180px + 10px);
  }
}
@media screen and (min-width: 1661px) {
  .embedded-page .floating-button-bar {
    width: 1228px;
    left: calc(50% - 1420px / 2 + 180px + 10px);
  }
}

.height-32 {
  height: 32px;
  :deep(.el-input__inner) {
    height: 32px;
    line-height: 32px;
  }
  :deep(.el-input__icon) {
    line-height: 26px;
  }
  :deep(.el-input--suffix) {
    line-height: 32px;
  }
  :deep(.el-select__selection) {
    line-height: 32px;
  }
  :deep(.el-select__caret) {
    line-height: 32px;
  }
}

.w-medium {
  width: 314px;
  flex-shrink: 0;
}
.w-normal {
  width: 200px;
  flex-shrink: 0;
}
.ml-12 {
  margin-left: 12px;
}
.mr-12 {
  margin-right: 12px;
}
.mr-16 {
  margin-right: 16px;
}
.mr-20 {
  margin-right: 20px;
}
.ml-102 {
  margin-left: 102px;
}
.mr-24 {
  margin-right: 24px;
}
.mt-12 {
  margin-top: 12px;
}
.mr-6 {
  margin-right: 6px;
}
.w-mini {
  width: 120px;
  flex-shrink: 0;
}
.w-big {
  width: 526px;
  flex-shrink: 0;
}
.flex-row {
  display: flex;
  align-items: center;
}
.common-tips {
  line-height: 16px;
  font-size: 12px;
  color: #999;
}
.project-tag-box {
  margin-top: -6px;
}
.normal-tips {
  color: #666666;
  font-size: 12px;
  line-height: 12px;
  margin: 4px 0 0 0;
}
.h-normal {
  height: 32px;
}
.line-h-32 {
  line-height: 32px;
}
.color-orange {
  color: #e37318;
}
.color-gray {
  color: #00000099;
}
.more-setting {
  color: #0052d9;
  font-family: 'PingFang SC';
  font-size: 14px;
  cursor: pointer;
}
.course-name-input {
  width: 785px;
}
.w-time {
  width: 240px;
  flex-shrink: 0;
}
.pl-16 {
  padding-left: 16px;
}
.pr-16 {
  padding-right: 16px;
}
</style>
