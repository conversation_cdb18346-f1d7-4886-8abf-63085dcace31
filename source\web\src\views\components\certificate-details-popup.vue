<template>
  <el-dialog
    :visible.sync="isShowPopup"
    custom-class="certificate-details-popup dialog-center"
    title="证书详情"
    width="850px"
    top="0px"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :before-close="closeDialog"
  >
    <div class="container">
      <p class="name">
        <el-tag class="state" :type="['', 'success', 'warning', 'info'][certificateData.certificateStatus || 3]" size="mini" effect="dark">{{ ['', '生效中', '过期', '回收'][certificateData.certificateStatus || 3] }}</el-tag>
        证书名称：{{ certificateData.certificateName }}
      </p>
      <p class="item">项目：{{ courseTitle }}</p>
      <p class="id">证书来源：</p>
      <p class="time">
        <span>颁发时间： {{ certificateData.createdAt }}</span>
        <span>失效时间： {{ certificateData.certificateValidType===0?'永久有效':certificateData.expirationTime }}</span>
        <span>ID： {{ certificateData.customBatchNo }}</span>
      </p>
      <div class="divider"></div>
      <img :src="certificateData.certificateImageUrl" alt=""/>
      <div class="num-collection">
        <p>
          <span>数字藏品名称：{{ certificateData.album_name }}</span>
          <el-button type="primary" size="mini" @click="digitalCollection">
            <span>数字藏品链接</span>
          </el-button>
        </p>
        <p>数字藏品描述：{{ certificateData.album_desc }}</p>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import pagination from '@/mixins/pager'
import { getStudentCertificateInfo } from '@/config/mooc.api.conf.js'
export default {
  mixins: [pagination],
  props: {
    isShowPopup: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      certificateData: {
        certificateName: '-',
        certificateValidType: 0,
        expirationTime: '',
        customBatchNo: '-',
        certificateStatus: 3,
        certificateImageUrl: '',
        album_name: '',
        album_desc: ''
      },
      courseTitle: ''
    }
  },
  methods: {
    // 获取学员证书列表
    onSearch(e) {
      this.courseTitle = e.courseTitle
      getStudentCertificateInfo(e.id).then((res) => {
        this.certificateData = res
      })
    },
    // 打开数字藏品
    digitalCollection() {
      const { certificateName, album_name, nftUrl } = this.certificateData
      const params = {
        certificateName,
        album_name,
        nftUrl
      }
      this.$emit('openDigitalCollection', params)
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:isShowPopup', false)
    }
  }
}
</script>

<style lang="less" scoped>
:deep(.certificate-details-popup) {
  max-height: 100%;
  overflow-y: auto;
  .el-dialog__body {
    padding: 24px 32px;
  }
  .container {
    width: 100%;
    & > p {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    }
    & > img {
      max-width: 100%;
      margin: 0 auto;
      display: block;
    }
    .name {
      color: #333;
      font-weight: 700;
      .state {
        width: auto;
        font-weight: 400;
        margin-right: 5px;
        height: 20px;
        padding: 0 5px;
        line-height: 19px;
      }
    }
    .time > span {
      margin-right: 20px;
    }
    .divider {
      height: 1px;
      width: 100%;
      margin: 10px 0;
      background-color: #dcdcdc;
      position: relative;
    }

    .num-collection {
      & > p {
        margin-bottom: 10px;
        &:first-child {
          display: flex;
          align-items: center;
          justify-content: space-between;
        }
      }
    }
  }
}
</style>
