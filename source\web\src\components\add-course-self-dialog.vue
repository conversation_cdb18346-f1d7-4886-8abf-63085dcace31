<template>
    <el-Dialog
      :visible.sync="visible"
      width="800px"
      top="50px"
      custom-class="add-course-self-dialog dialog-center"
      append-to-body
      :show-close="false"
      :close-on-click-modal="false"
      :before-close="cancel"
      v-loading="is_loaded"
    >
    <div class="add-list">
        <div class="title">
        <span>添加到课单</span>
        <i class="el-icon-close f-right" @click="cancel"></i>
        </div>
        <p class="add-course-tip">暂只显示已有课单，如需新增请在<span @click="toCreate">课单广场</span>新增</p>
        <!-- 课单列表 -->
        <el-table
        ref="courseTable"
        :data="tableData.records"
        header-row-class-name="add-header-style"
        row-class-name="add-row-style"
        :row-key="setRowKeys"
        @selection-change="selectionChange"
        height="404px"
        >
        <el-table-column
            type="selection"
            width="56"
            align="center"
            :selectable="selectDisabled"
            :reserve-selection="true"
        >
        </el-table-column>
        <el-table-column
            prop="name"
            label="课单名称"
            show-overflow-tooltip
            align="left"
        >
            <template slot-scope="{ row }">
            <span class="official-tag" v-if="row.cl_type === 1">官方</span>
            <span class="excellent-tag" v-if="row.excellent_status === 1"
                >精品</span
            >
            <span class="private-tag" v-if="row.is_open === 0"
                ><i class="el-icon-lock"></i> 私密</span
            >
            <span :class="{ 'enabled-cell-style': !selectDisabled(row) }">{{
                row.name
            }}</span>
            </template>
        </el-table-column>
        <el-table-column
            prop="created_at"
            label="创建时间"
            width="200"
            show-overflow-tooltip
            align="left"
        >
            <template slot-scope="{ row }">
            <span :class="{ 'enabled-cell-style': !selectDisabled(row) }">{{
                row.created_at
            }}</span>
            </template>
        </el-table-column>
        </el-table>
        <el-pagination
        background
        :small="true"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="current"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="size"
        layout="total,  prev, pager, next, sizes, jumper"
        :total="tableData.total"
        >
        </el-pagination>
        <div class="footer">
        <el-button
            size="medium"
            type="primary"
            class="f-right confirm-btn"
            :disabled="selection.length === 0"
            @click="onConfirm"
        >
            确定
        </el-button>
        <el-button size="medium" class="f-right" @click="cancel">取消</el-button>
        </div>
    </div>
    </el-Dialog>
</template>
<script>
import pager from 'mixins/pager'
import { getUserCourseList } from 'config/api.conf'

export default {
  components: {
  },
  mixins: [pager],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    cl_ids: {
      type: Array
      // default: () => []
    },
    itemData: {}
  },
  watch: {
    visible(val) {
      if (val) {
        this.onSearch(1)
      } 
    }
  },  
  data() {
    return {
      is_loaded: true,
      courseIdInfo: {}
    }
  },
  created() {

  },
  methods: {
    // 获取用户课单列表
    onSearch(page_no) {
      this.current = page_no
      const { module_id, item_id } = this.itemData
      let params = {
        page_no,
        page_size: this.size,
        module_id: module_id || '',
        item_id: item_id || ''
      }
      getUserCourseList(params).then((res) => {
        this.tableData = res
      })
      this.is_loaded = false
    },
    // 确定按钮
    onConfirm() {
      let ids = this.selection.map((item) => {
        return item.cl_id
      })
      if (this.cl_ids && this.cl_ids.length > 0) {
        ids = ids.concat(this.cl_ids)
      }
      this.$emit('addedHandle', ids)
      setTimeout(() => {
        this.cancel()
      }, 100)
    },
    setRowKeys(item) {
      return item.cl_id
    },
    selectionChange(val) {
      this.selection = val
    },
    // 校验是否可选，如果某个课单中已有该课程，则无法被选中进行添加的操作
    selectDisabled(row) {
      if (this.cl_ids && this.cl_ids.length > 0) {
        let retFlag = true
        this.cl_ids.map(e => {
          if (row.cl_id === parseInt(e)) {
            retFlag = false
          }
        })
        return retFlag          
      } else {
        return !row.have_content
      }
    },
    // 去创建课单
    toCreate() {
      let url = window.location.host.endsWith('.woa.com') ? process.env.VUE_APP_PORTAL_HOST_WOA : process.env.VUE_APP_PORTAL_HOST
      url = `${url}/courselist/home`
      window.open(url)
    },
    // 取消按钮
    cancel() {
      this.$refs.courseTable.clearSelection()
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.add-course-self-dialog{
    .add-list {
      :deep(.el-dialog__body){
          padding: 0;
      }
      height: 650px;
      padding: 0 32px 24px;
    .title {
        line-height: 72px;
        font-weight: 700;
        font-size: 16px;
        i {
        margin-top: 24px;
        cursor: pointer;
        }
    }
    .add-course-tip{
      margin-bottom: 24px;
      span{
        color: #0052d9;
        cursor: pointer;
      }
    }
    .footer {
        margin-top: 22px;
        .el-button {
        margin-top: 0;
        }
        .confirm-btn {
        margin-left: 8px;
        }
    }
    :deep(.el-table) {
        &::before {
        display: none;
        }
        .el-table__header-wrapper {
        border-top: 1px solid #e7e7e7;
        border-left: 1px solid #e7e7e7;
        border-right: 1px solid #e7e7e7;

        .add-header-style {
            height: 40px;
            th {
            padding: 9px 0;
            line-height: 22px;
            background: #fff;
            font-weight: 400;
            color: #999;
            }
        }
        }

        .el-table__body-wrapper {
        border-left: 1px dashed #e7e7e7;
        border-right: 1px dashed #e7e7e7;
        border-bottom: 1px dashed #e7e7e7;

        .add-row-style {
            height: 40px;
            td {
            padding: 8px 0;
            line-height: 22px;
            color: #333;
            }
            .enabled-cell-style {
            color: rgba(0, 0, 0, 0.26);
            }
        }
        }
    }

    .official-tag,
    .excellent-tag,
    .private-tag {
        display: inline-block;
        height: 20px;
        line-height: 20px;
        min-width: 48px;
        padding: 0 6px;
        box-sizing: border-box;
        color: #fff;
        border-radius: 10px;
        font-size: 12px;
        margin-right: 8px;
        text-align: center;
        position: relative;
        bottom: 1px;
    }
    .official-tag {
        background-color: #0052d9;
    }
    .excellent-tag {
        background-color: rgba(217, 0, 27);
    }
    .private-tag {
        background-color: rgba(112, 182, 3, 1);
    }
    }
}    
</style>
