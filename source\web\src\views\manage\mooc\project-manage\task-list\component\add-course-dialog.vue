<template>
  <div class="list-dialog">
    <el-dialog
      width="900px"
      :visible="visible"
      title="添加线上课程"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <div class="course-body">
        <div class="course-body-title">
          <div class="check-label-warning">
            <i class="el-icon-warning-outline"></i>
            <span>若无线上音频/Scorm/压缩包课程创建权限，请联系联系graywu</span>
          </div>
          <el-dropdown @command="handleCommand" trigger="click">
            <el-button type="primary" size="small">创建课程<i class="el-icon-arrow-down el-icon--right"></i></el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="video">视频课程</el-dropdown-item>
              <el-dropdown-item command="v8">音频课程</el-dropdown-item>
              <el-dropdown-item command="v8">scorm</el-dropdown-item>
              <el-dropdown-item command="v8">压缩包</el-dropdown-item>
              <el-dropdown-item command="Article">文章</el-dropdown-item>
              <el-dropdown-item command="Doc">文档</el-dropdown-item>
              <el-dropdown-item command="Other" v-if="extendOptions.showAddOutLink">外链内容</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </div>
        <el-form ref="form" :model="form" inline>
          <el-form-item>
            <el-cascader
              placeholder="请选择课程分类"
              v-model="form.classifyId" 
              :options="classifiyOptions" 
              :props="classifyProps"
              label="classify_name" 
              clearable
              @change="handleClassify"
              >
            </el-cascader>
          </el-form-item>
          <el-form-item>
            <el-select
              v-model="form.module_value"
              placeholder="请选择课程类型"
              clearable
            >
              <el-option
                v-for="item in contentModuleTypes"
                :key="item.module_value"
                :label="item.module_name"
                :value="item.module_value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input
              clearable
              v-model="form.course_name"
              placeholder="请输入课程名称"
              suffix-icon="el-icon-search"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="onSearch(1)">搜索</el-button>
            <el-button size="small" plain @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table
          ref="mainTable"
          :data="tableData.records"
          max-height="551px"
          @selection-change="selectionChange"
          @sort-change="sortChange"
        >
          <!-- 单选 -->
          <el-table-column width="55" v-if="entryType === 'change'" :key="1"> 
            <template slot-scope="scope">
              <el-radio @change="handleRowChange(scope.row)" v-model="tableRadio" :label="scope.row.item_id">&nbsp;</el-radio>
            </template>
          </el-table-column>
          <!-- 多选 -->
          <el-table-column width="55" type="selection" v-else :key="2" :selectable="selectable"></el-table-column>
          <el-table-column prop="item_id" label="课程id" width="80"></el-table-column>
          <el-table-column prop="content_name" label="课程名称" show-overflow-tooltip>
            <template v-slot="prop">
              <a class="content-url" target="_blank" :href="prop.row.content_url">{{ prop.row.content_name }}</a> 
            </template>
          </el-table-column>
          <el-table-column prop="classify_names" label="课程分类" show-overflow-tooltip min-width="30%"></el-table-column>
          <el-table-column prop="" label="类型" width="100">
            <template slot-scope="scope">
              <span>{{ filterResourceName(scope.row) }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="" label="创建人"></el-table-column> -->
          <el-table-column prop="created_at" label="创建时间" width="180" sortable="custom" >
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="tableData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="current"
          :page-sizes="[5, 10, 20, 30, 50, 100]"
          :page-size="size"
          layout="total,  prev, pager, next, sizes, jumper"
          :total="tableData.total"
          >
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="cancel">取 消</el-button>
        <el-button size='small' @click="submit" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 添加外链 -->
    <AddOutLinkShow ref="addOutLinkDialogRef" 
      v-if="addOutLinkShowDialogShow" 
      outLinkType="add"
      @updateTreeList="getOutLinkInfo" 
      :visible.sync="addOutLinkShowDialogShow"
    />
    <addErrorDialog :visible.sync="addErrorDialogShow" :taskNameList="taskNameList"></addErrorDialog>
  </div>
</template>
<script>
import pager from '@/mixins/pager'
import { getSearchsite, getClassify } from 'config/api.conf'
import addErrorDialog from './add-error-dialog.vue'
import { mapState } from 'vuex'
import { actTypes } from '@/utils/moduleMap.js'
import AddOutLinkShow from '@/views/manage/mooc/project-manage/task-list/component/add-outlink-dialog.vue'
export default {
  mixins: [pager],
  components: {
    addErrorDialog,
    AddOutLinkShow
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentNode: {
      type: Object
    },
    treeNode: {
      type: Array,
      default: () => ([])
    },
    entryType: { // 更换课程--添加课程区分
      type: String
    },
    extendOptions: {
      type: Object,
      default: () => ({
        showAddOutLink: false,
        banSameCourse: false,
        sameCourseList: []
      })
    }
  },
  data() {
    return {
      tableRadio: '',
      form: {
        classifyId: '',
        classify: [],
        course_name: '',
        module_value: ''
      },
      classifiyOptions: [],
      classifyProps: {
        multiple: false,
        label: 'classify_name', 
        value: 'classify_id',
        children: 'child',
        checkStrictly: true, 
        emitPath: false
      },
      contentModuleTypes: [
        { module_name: '视频', module_id: 1, course_type: ['Video', 'Video-2d', 'Video-3d', 'Video-ppt'], module_value: 0 },
        { module_name: '音频', module_id: 1, course_type: ['Audio'], module_value: 1 },
        { module_name: '文章', module_id: 8, course_type: ['Article'], module_value: 2 },
        { module_name: '文档', module_id: 16, course_type: ['Doc'], module_value: 3 },
        { module_name: 'scorm', module_id: 1, course_type: ['Scorm'], module_value: 4 },
        { module_name: '压缩包', module_id: 1, course_type: ['Flash'], module_value: 5 }
      ],
      tableData: {
        records: [],
        total: 0
      },
      selectionList: [],
      addErrorDialogShow: false,
      taskNameList: [],
      size: 5,
      sortOrder: '',
      sort_by: '',
      addOutLinkShowDialogShow: false
    }
  },
  computed: {
    ...mapState(['userInfo']),
    filterResourceName() {
      return ({ course_type, module_id }) => {
        let name = ''
        if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(course_type)) {
          name = '视频'
        } else if (course_type === 'Audio') {
          name = '音频'
        } else if (course_type === 'Article' || module_id === 8) {
          name = '文章'
        } else if (course_type === 'Doc' || module_id === 16) {
          name = '文档'
        } else if (course_type === 'Scorm') {
          name = 'Scorm'
        } else if (course_type === 'Series') {
          name = '系列课'
        } else if (course_type === 'Flash') {
          name = '压缩包'
        }
        return name
      }
    }
  },
  created() {
    this.getClassifyData()
    this.onSearch(1)
  },
  methods: {
    // 检查是否存在相同课程,需要对比item_id和act_type，并且是两个数据进行对比tableData.records和this.extendOptions.sameCourseList对比,如果存在相同课程，则选中该课程并且禁用勾选
    // checkSameCourse() {
    //   if (this.extendOptions.banSameCourse) {
    //     this.tableData.records.forEach((v) => {
    //       if (this.extendOptions.sameCourseList.some(item => item.item_id === v.item_id && item.act_type === v.act_type)) {
    //         this.$refs.mainTable.toggleRowSelection(v, true)
    //         v.disabled = true
    //       }
    //     })
    //   }
    // },
    selectable(row) {
      if (!this.extendOptions.banSameCourse) return true
      if (!this.extendOptions.sameCourseList.length) return true
      const isSame = this.extendOptions.sameCourseList.some(item => {
        return item.item_id === row.item_id && item.act_type === row.act_type
      })
      return !isSame
    },
    onSearch(page_no = 1) {
      const { classify, course_name, module_value, classifyId } = this.form
      const course_type_info = this.contentModuleTypes.find((v) => v.module_value === module_value)
      const data = {
        classify: classifyId && classify ? classify : [],
        keywords: course_name,
        moduleId: course_type_info?.module_id,
        courseType: course_type_info?.course_type,
        pageNum: page_no,
        pageSize: this.size,
        sort_order: this.sortOrder,
        sort_by: this.sort_by
      }
      // 文章图文不传course_type
      if ([8, 16].includes(data.moduleId)) delete data.courseType
      getSearchsite(data).then(res => {
        this.tableData.records = res.records.map((v) => {
          const time = v.updated_at.split('.')[0]
          // 以防首字母小写处理
          const course_type = v.course_type ? v.course_type.slice(0, 1).toUpperCase() + v.course_type.slice(1).toLowerCase() : ''
          return {
            ...v,
            updated_at: time,
            course_type
          }
        })
        this.tableData.total = res.total
        this.$nextTick(() => {
          // this.checkSameCourse()
        })
      })
    },
    getClassifyData() {
      getClassify({ act_type: 2 }).then((res) => {
        this.classifiyOptions = res
        // 分类再次操作卡死处理
        setTimeout(() => {
          const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]')
          if ($el.length > 0) {
            Array.from($el).map(item => item.removeAttribute('aria-owns'))
          }
        }, 200)
      })
    },
    // 勾选分类
    handleClassify(val) {
      const formatClassify = (list) => {
        list.forEach((e) => {
          if (e.classify_id === val) {
            this.form.classify = [
              {
                field: `classify_level_${e.level}`,
                id: val
              }
            ]
          } else {
            if (e.child?.length) {
              formatClassify(e.child)
            }
          }
        })
      }
      formatClassify(this.classifiyOptions)
    },
    handleCommand(val) {
      let url = ''
      if (val === 'video') { // 视频
        url = process.env.NODE_ENV === 'production' ? '//portal.learn.woa.com/training/creator-center/course-share' : '//test-portal-learn.woa.com/training/creator-center/course-share'
      } else if (val === 'v8') { // 音频--scorm--压缩包
        url = process.env.NODE_ENV === 'production' ? 'https://learn.woa.com/manage/net/eidt' : 'http://test.v8.learn.oa.com/manage/net/eidt'
      } else if (val === 'Article') { // 文章
        url = process.env.NODE_ENV === 'production' ? '//portal.learn.woa.com/training/graphic/user/create' : '//test-portal-learn.woa.com/training/graphic/user/create'
      } else if (val === 'Doc') {
        url = '//learn.woa.com/user/profile/add'
      } else if (val === 'Other') {
        this.addOutLinkShowDialogShow = true
        return
      }
      window.open(url)
    },
    // 单选
    handleRowChange(val) {
      this.selectionList = [val]
    },
    // 多选
    selectionChange(val) {
      this.selectionList = val
    },
    sortChange({ column, prop, order }) {
      this.sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : ''
      this.sort_by = this.sortOrder ? 'created_at' : ''
      this.onSearch()
    },
    getOutLinkInfo(data) {
      // return {
      //   ...v,
      //   id: this.currentNode?.id || '',
      //   required: true,
      //   act_id: v.item_id,
      //   resource_type,
      //   resource_type_name,
      //   task_name: v.content_name,
      //   act_name: v.content_name,
      //   radio: 1,
      //   isWordNum: '',
      //   word_number: v.word_num,
      //   act_type: actMap?.act_type || '',
      //   resource_content_type: v.content_type || '',
      //   resource_url: v.content_url
      // }
      console.log(data, 'outlink')
      let obj = {}
      if (data.length > 0) {
        obj = {
          id: '',
          act_type: data[0].act_type,
          module_id: 99,
          act_id: '',
          content_url: data[0].resource_url,
          content_name: data[0].task_name,
          cover_img_url: '',
          module_name: data[0].resource_type_name
        }
      }
      this.$emit('update:visible', false)
      this.$emit('handleShowSetDialog', [obj])
    },
    submit() {
      if (this.selectionList.length === 0) {
        this.$message.error('请选择一个课程')
        return
      }
      // this.taskNameList = []
      // let newTaskList = []
      // if (this.currentNode?.id) { // 当前阶段
      //   newTaskList = (this.currentNode?.sub_tasks || []).filter((e) => e.task_type === 'task')
      // } else { // 当前任务树
      //   newTaskList = JSON.parse(JSON.stringify(this.treeNode)) 
      // }
      // newTaskList.forEach((v) => {
      //   this.selectionList.forEach((a) => {
      //     if (v.task_name === a.content_name) {
      //       this.taskNameList.push({
      //         name: v.task_name
      //       })
      //     }
      //   })
      // })
      // if (this.taskNameList?.length) {
      //   this.addErrorDialogShow = true
      //   return
      // }
      const urlMap = {
        Video: `${process.env.VUE_APP_V8_HOST_WOA}/training/mobile/outsourcedCourse/video/play?course_id=`,
        Graphic: `${process.env.VUE_APP_V8_HOST_WOA}/training/mobile/outsourcedCourse/graphic/play?course_id=`,
        Series: `${process.env.VUE_APP_V8_HOST_WOA}/training/mobile/outsourcedCourse/iframe/play?course_id=`
      }
      this.selectionList = this.selectionList.map((v) => {
        let resource_type = ''
        let resource_type_name = ''
        let isNum = true
        if (v.module_id === 1) {
          if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(v.course_type)) {
            resource_type = 'Video'
            resource_type_name = '视频'
          } else if (v.course_type === 'Flash') {
            resource_type = 'Zip'
            resource_type_name = '压缩包'
          } else if (v.course_type === 'Audio') {
            resource_type = 'Audio'
            resource_type_name = '音频'
          } else if (v.course_type === 'Scorm') {
            resource_type = 'Scorm'
            resource_type_name = 'Scorm'
          } else if (v.course_type === 'Series') {
            resource_type = 'Series'
            resource_type_name = '系列课'
          }
          isNum = /^\d+$/.test(v.item_id)
        } else {
          if (v.module_id === 8) { // 文章
            resource_type = 'Article'
            resource_type_name = '文章'
          } else if (v.module_id === 16) { // 文档
            resource_type = 'Doc'
            resource_type_name = '文档'
          }
        }
        let actMap = actTypes.find((el) => { return el.module_id === v.module_id })
        // 如果module_id为1时，item_id非纯数字
        if (!isNum) {
          actMap.act_type = 102
        }
        if (actMap?.act_type === 102) {
          if (['Video', 'Graphic', 'Series'].includes(v.course_type)) {
            v.resource_url_mobile = urlMap[v.course_type] + v.item_id
          } else {
            v.resource_url_mobile = v.content_url
          }
        }
        return {
          ...v,
          id: this.currentNode?.id || '',
          required: true,
          act_id: v.item_id,
          resource_type,
          resource_type_name,
          task_name: v.content_name,
          act_name: v.content_name,
          radio: 1,
          isWordNum: '',
          word_number: v.word_num,
          act_type: actMap?.act_type || '',
          resource_content_type: v.content_type || '',
          resource_url: v.content_url
        }
      })
      this.$emit('update:visible', false)
      this.$emit('handleShowSetDialog', this.selectionList)
      if (this.entryType === 'change') {
        this.$emit('changeSingleData', ...this.selectionList)
      }
    },
    handleReset() {
      this.form = {
        classifyId: '',
        classify: [],
        course_name: '',
        module_value: ''
      }
      this.current = 1
      this.size = 5
      this.sortOrder = ''
      this.sort_by = ''
      this.$refs.mainTable.clearSort() 
      this.onSearch()
    },
    cancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.list-dialog {
  .content-url {
    color:#0052d9;
    cursor: pointer;
  }
  .course-body-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .check-label-warning {
    color: #ff7548;
    background-color: rgba(253, 246, 236, 1);
    padding: 10px 16px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    i {
      margin-right: 10px;
      font-size: 20px;
    }
  }
}

</style>
