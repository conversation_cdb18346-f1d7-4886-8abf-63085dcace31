window.mooc_All = {
  'Mooc_ProjectDetail_TrainingProgress_Progress': 'Training Progress',
  'Mooc_ProjectDetail_TrainingProgress_NoRequireRecord': "You haven't started your required task(s) yet. Please start as soon as possible!",
  'Mooc_ProjectDetail_TrainingProgress_BeginStudy': 'Start',
  'Mooc_ProjectDetail_TrainingProgress_RequiredProgress': 'Required Tasks',
  'Mooc_ProjectDetail_TrainingProgress_NonRequiredProgress': 'Optional Tasks',
  'Mooc_ProjectDetail_TrainingProgress_Certification': 'Certificates',
  'Mooc_ProjectDetail_TrainingProgress_LastStudy': 'Previously learned',
  'Mooc_ProjectDetail_TrainingProgress_ContinueStudy': 'Continue',
  'Mooc_ProjectDetail_TrainingProgress_ProjectFinished': 'Learning completed',
  'Mooc_ProjectDetail_TrainingProgress_NotStarted': 'Not started',
  'Mooc_ProjectDetail_TrainingProgress_InProgress': 'In progress',
  'Mooc_ProjectDetail_TrainingProgress_Finished': 'Completed',
  'Mooc_ProjectDetail_TrainingProgress_Delayed': 'Expired',
  'Mooc_ProjectDetail_TrainingProgress_FinishedTime': 'Completed on: ',
  'Mooc_ProjectDetail_TrainingProgress_DelayCanNotStudy': 'Expired required tasks are not available. If you need to complete an expired learning task, please contact the learning program admin for help.',
  'Mooc_ProjectDetail_TrainingProgress_DelayReRegist': 'Expired required tasks are not available. If you need to complete an expired learning task, please exit the learning program and then sign up for the program again.',
  'Mooc_ProjectDetail_TrainingProgress_LessStartTime': 'This program is not yet open for learning.',
  'Mooc_ProjectDetail_TrainingProgress_CannotStudyProjEnd': 'This program has ended and is no longer available.',
  'Mooc_ProjectDetail_TrainingProgress_FinishedAllRequired': 'After you finish all the required tasks, this program will be marked as completed.',
  'Mooc_ProjectDetail_Notice_ProjNotices': 'Program Bulletins',
  'Mooc_ProjectDetail_Notice_Readed': 'Mark as read',
  'Mooc_ProjectDetail_Notice_PublishTime': 'Released on',
  'Mooc_ProjectDetail_Notice_IKnow': 'Got It',
  'Mooc_ProjectDetail_Notice_AllReaded': 'All bulletins marked as read',
  'Mooc_ProjectDetail_Notice_NonRead': 'No unread bulletins',
  'Mooc_ProjectDetail_Notice_NoData': 'No content',
  'Mooc_ProjectDetail_Notice_ProjList': 'Program List',
  'Mooc_ProjectDetail_Notice_Introduce': 'Intro',
  'Mooc_ProjectDetail_Notice_Task': 'Tasks',
  'Mooc_ProjectDetail_Notice_Notices': 'Bulletins',
  'Mooc_ProjectDetail_Notice_Resources': 'References',
  'Mooc_ProjectDetail_Notice_Comments': 'Comments',
  'Mooc_ProjectDetail_Notice_NoticeInfo': 'Bulletin Details',
  'Mooc_ProjectDetail_BasicInfo_ViewByMobile': 'View on Mobile Device',
  'Mooc_ProjectDetail_BasicInfo_ProjManagement': 'Settings',
  'Mooc_ProjectDetail_BasicInfo_NotLimitTime': '<span style="color: #000000e6;">No limits on the program learning time</span>',
  'Mooc_ProjectDetail_BasicInfo_LimitStudyCycle': 'Program period',
  'Mooc_ProjectDetail_BasicInfo_LimitStudyTime': 'Program period: {days} day(s)',
  'Mooc_ProjectDetail_BasicInfo_days': 'day(s)',
  'Mooc_ProjectDetail_BasicInfo_ProjStudyTime': 'Program period',
  'Mooc_ProjectDetail_BasicInfo_Tasks': ' task(s)',
  'Mooc_ProjectDetail_BasicInfo_Students': ' participant(s)',
  'Mooc_ProjectDetail_BasicInfo_LowerScorer': 'No rating',
  'Mooc_ProjectDetail_BasicInfo_Score': 'Rate Now',
  'Mooc_ProjectDetail_BasicInfo_Scored': 'Rated',
  'Mooc_ProjectDetail_BasicInfo_Prais': 'Like',
  'Mooc_ProjectDetail_BasicInfo_Praised': 'Liked',
  'Mooc_ProjectDetail_BasicInfo_collect': 'Favorite',
  'Mooc_ProjectDetail_BasicInfo_collected': 'Favorited',
  'Mooc_ProjectDetail_BasicInfo_Comments': 'Comments',
  'Mooc_ProjectDetail_BasicInfo_ExitProject': 'Exit Program',
  'Mooc_ProjectDetail_BasicInfo_NotInProject': "You haven't signed up for this program.",
  'Mooc_ProjectDetail_BasicInfo_CannotStudyNotInProj': "Unable to start. You haven't signed up for this program yet.",
  'Mooc_ProjectDetail_BasicInfo_ClickRegistProj': 'Sign up now',
  'Mooc_ProjectDetail_BasicInfo_RegistProj': 'Sign Up',
  'Mooc_ProjectDetail_BasicInfo_SureRegist': 'After signing up for this program, you will be listed as a participant. Please look out for the program start date. Are you sure you want to sign up now?',
  'Mooc_ProjectDetail_BasicInfo_RegistProjTask': 'Sign Up',
  'Mooc_ProjectDetail_BasicInfo_SureRegistTask': 'If you need to study, please sign up for this program first. Please look out for the program period. Are you sure you want to sign up now?',
  'Mooc_ProjectDetail_BasicInfo_RegistSucessedTASK': 'Your registration is successful. Please start your study',
  'Mooc_ProjectDetail_BasicInfo_RegistSucessed': 'Signup successful',
  'Mooc_ProjectDetail_BasicInfo_RegistSucessedAuto': 'You have automatically signed up for the training program. Please pay attention to the study time of the program',
  'Mooc_ProjectDetail_BasicInfo_GetCertificate': 'Finish program to obtain certificate',
  'Mooc_ProjectDetail_StudyByWechat': 'Use Weixin/WeCom to scan the QR code to view the program.',
  'Mooc_ProjectDetail_Copy': 'Copy',
  'Mooc_ProjectDetail_Exit': 'Exit Program',
  'Mooc_ProjectDetail_ExitReason': 'Please explain why you exited the program.',
  'Mooc_ProjectDetail_EnterExitReason': 'Please explain why you want to exit the program.',
  'Mooc_ProjectDetail_ExitReasonRequired': 'Enter the reason for exiting the program (This field is required).',
  'Mooc_ProjectDetail_Cancel': "Don't Exit",
  'Mooc_ProjectDetail_SureExit': 'Exit Program',
  'Mooc_ProjectDetail_Score_Score': 'Program Rating',
  'Mooc_ProjectDetail_Score_Good': 'Excellent',
  'Mooc_ProjectDetail_Score_Better': 'Good',
  'Mooc_ProjectDetail_Score_Normal': 'Average',
  'Mooc_ProjectDetail_Score_Bad': 'Poor',
  'Mooc_ProjectDetail_Score_VeryBad': 'Very poor',
  'Mooc_ProjectDetail_Score_MyCore': 'My rating: {point}',
  'Mooc_ProjectDetail_Score_Point': '',
  'Mooc_ProjectDetail_Score_EditSure': 'Submit',
  'Mooc_ProjectDetail_Score_Submit': 'Submit',
  'Mooc_ProjectDetail_Desc_Introduce': 'Program Intro',
  'Mooc_ProjectDetail_Desc_Label': 'Tags',
  'Mooc_ProjectDetail_Desc_Admins': 'Admin',
  'Mooc_ProjectDetail_Desc_Desc': 'About the program',
  'Mooc_ProjectDetail_Desc_Detail': 'Details',
  'Mooc_ProjectDetail_Desc_NoLable': 'No data',
  'Mooc_ProjectDetail_Desc_NoDesc': 'No data',
  'Mooc_ProjectDetail_Desc_NoDetail': 'No data',
  'Mooc_ProjectDetail_Desc_NoAdmins': 'No data',
  'Mooc_ProjectDetail_TaskList_TaskDisable': 'This task has become invalid and cannot be accessed.',
  'Mooc_ProjectDetail_TaskList_TaskLocked': 'Task locked',
  'Mooc_ProjectDetail_TaskList_RetractAll': 'Collapse',
  'Mooc_ProjectDetail_TaskList_ExpandAll': 'Expand',
  'Mooc_ProjectDetail_TaskList_OnlyRequire': 'Show only required tasks',
  'Mooc_ProjectDetail_TaskList_OnlyNotFinished': 'Show only unfinished tasks',
  'Mooc_ProjectDetail_TaskList_Minute': ' min',
  'Mooc_ProjectDetail_TaskList_Words': ' characters',
  'Mooc_ProjectDetail_TaskList_NotLimitedStudyTime': 'Opening time: Unlimited',
  'Mooc_ProjectDetail_TaskList_NotLimitedPracticeTime': 'Practice time: Unlimited',
  'Mooc_ProjectDetail_TaskList_PracticeTime': 'Practice time',
  'Mooc_ProjectDetail_TaskList_NotLimitedExamTime': 'Exam time: Unlimited',
  'Mooc_ProjectDetail_TaskList_NotLimitedSubmitTime': 'Submission time: Unlimited',
  'Mooc_ProjectDetail_TaskList_EvaluateTime': 'Peer review time',
  'Mooc_ProjectDetail_TaskList_UpStandard': 'Passed',
  'Mooc_ProjectDetail_TaskList_LowStandard': 'Failed',
  'Mooc_ProjectDetail_TaskList_Pass': 'Passed',
  'Mooc_ProjectDetail_TaskList_NotPass': 'Failed',
  'Mooc_ProjectDetail_TaskList_Backed': 'Rejected',
  'Mooc_ProjectDetail_TaskList_ToBeReviewed': 'Pending review',
  'Mooc_ProjectDetail_TaskList_Submited': 'Submitted',
  'Mooc_ProjectDetail_TaskList_UnlockByPreTask': 'This task will be unlocked after you finish the previous required task.',
  'Mooc_ProjectDetail_TaskList_LockedPreTaskUnfinish': 'Complete the previous required task to unlock this one.',
  'Mooc_ProjectDetail_TaskList_TaskNotStart': 'Task not started',
  'Mooc_ProjectDetail_TaskList_TaskInProgress': 'Task in progress',
  'Mooc_ProjectDetail_TaskList_TaskFinished': 'Task completed',
  'Mooc_ProjectDetail_TaskList_RequiredTask': 'Required',
  'Mooc_ProjectDetail_TaskList_ElectiveTask': 'Optional',
  'Mooc_ProjectDetail_Documents_LearnResources': 'References',
  'Mooc_ProjectDetail_Documents_TotalFiles': 'Total {count} file(s)',
  'Mooc_ProjectDetail_Documents_View': 'View',
  'Mooc_ProjectDetail_Documents_DownLoad': 'Download',
  'Mooc_ProjectDetail_Documents_CreateTime': 'Uploaded on',
  'Mooc_ProjectDetail_Documents_FileSize': 'Size',
  'Mooc_ProjectDetail_Documents_AttInfor': 'Attachment Details',
  'Mooc_ProjectDetail_Documents_ReturnProject': 'Back',
  'Mooc_ProjectDetail_Comments_CannotCommentNotInProj': "Unable to comment as you haven't signed up for this program.",
  'Mooc_ProjectDetail_Comments_ProjectComment': 'Comments',
  'Mooc_ProjectDetail_Comments_AllComments': 'All comments',
  'Mooc_ProjectDetail_Comments_Hot': 'Most Liked',
  'Mooc_ProjectDetail_Comments_New': 'Most Recent',
  'Mooc_ProjectDetail_Comments_Comment': 'Post',
  'Mooc_ProjectDetail_Comments_Replay': 'Reply',
  'Mooc_ProjectDetail_Comments_Delete': 'Delete',
  'Mooc_ProjectDetail_Comments_RightNow': 'Just now',
  'Mooc_ProjectDetail_Comments_My': 'Me',
  'Mooc_ProjectDetail_Comments_Admin': 'Admin',
  'Mooc_ProjectDetail_Comments_TotalReplys': 'Total {count} replie(s)',
  'Mooc_ProjectDetail_Comments_ClickView': 'View',
  'Mooc_ProjectDetail_Comments_EnterReply': 'Reply',
  'Mooc_ProjectDetail_Comments_Top': 'Pin',
  'Mooc_ProjectDetail_Comments_CancelTop': 'Unpin',
  'Mooc_ProjectDetail_Comments_Hide': 'Hide',
  'Mooc_ProjectDetail_Comments_ReplyCount': ' replie(s)',
  'Mooc_ProjectDetail_Comments_HideSomeComment': 'Some comments are hidden.',
  'Mooc_ProjectDetail_Comments_CommentSuccessed': 'Comments_DelCommentWarm',
  'Mooc_ProjectDetail_Comments_ReplySucessed': "Reply posted. You've earned {point} general credit(s).",
  'Mooc_ProjectDetail_Comments_DelCommentWarm': 'Delete Comment',
  'Mooc_ProjectDetail_Comments_SureDeleteComment': 'Once deleted, the comment and related replies cannot be restored. Are you sure you want to delete this comment?',
  'Mooc_TaskDetail_Navigation_AllTypeTask': 'All Tasks',
  'Mooc_TaskDetail_Navigation_ReturnProjectHome': 'Homepage',
  'Mooc_TaskDetail_Navigation_ReturnProjectHomeNew': 'Homepage',
  'Mooc_TaskDetail_Navigation_TaskFinishCondition': 'Completion criteria:',
  'Mooc_TaskDetail_Navigation_MinStudyTime': 'Study for ≥{minute} min(s)',
  'Mooc_TaskDetail_Navigation_FinishByCourse': 'Determined by the course completion criteria',
  'Mooc_TaskDetail_Navigation_ExamPass': 'Pass the exam',
  'Mooc_TaskDetail_Navigation_PracticePass': 'Complete the exercise',
  'Mooc_TaskDetail_Navigation_LearnedTime': 'Studied for:  {minute} min {second} s',
  'Mooc_TaskDetail_Navigation_ViewTaskDesc': 'View Task Intro',
  'Mooc_TaskDetail_Navigation_TaskDesc': 'Task Intro',
  'Mooc_TaskDetail_Navigation_ExternalLinks': 'External link',
  'Mooc_TaskDetail_Navigation_TaskList': 'Task List',
  'Mooc_TaskDetail_Navigation_PreTask': 'Previous',
  'Mooc_TaskDetail_Navigation_NextTask': 'Next',
  'Mooc_TaskDetail_ThirdParty_ClickToLearn': 'Learn Now',
  'Mooc_TaskDetail_ThirdParty_RefreshTaskStatus': 'Refresh',
  'Mooc_TaskDetail_ThirdParty_ConditionDesc': 'About Completion Criteria',
  'Mooc_TaskDetail_ThirdParty_TaskStatus': 'Task Status',
  'Mooc_TaskDetail_ThirdParty_ClickToRefreshTaskStatus': "If you've met the task completion criteria, click the \"Refresh\" button above to refresh the task status.",
  'Mooc_TaskDetail_ThirdParty_RefreshStatusSucessed': 'Task status refreshed',
  'Mooc_TaskDetail_ThirdParty_RefreshHighFrequency': "You're refreshing too often. Please try again after 1 minute.",
  'Mooc_TaskDetail_ThirdParty_TaskDetailDesc': 'Task Details',
  'Mooc_TaskDetail_ThirdParty_TaskRemark': 'Other Info',
  'Mooc_TaskDetail_ThirdParty_ThirdPartyNotSupporMobile': 'This task is provided by another platform and cannot be accessed via mobile devices. Please finish it on your personal computer.',
  'Mooc_TaskDetail_ThirdParty_Alert': 'Note',
  'Mooc_TaskDetail_ThirdParty_Close': 'Close',
  'Mooc_TaskDetail_ThirdParty_CopyLink': 'Copy Link',
  'Mooc_TaskDetail_Audio_Description': 'Intro',
  'Mooc_TaskDetail_Audio_Chapter': 'Chapter', // 此处要查表
  'Mooc_TaskDetail_Audio_ContentDoc': 'Transcript',
  'Mooc_TaskDetail_HomeWork_HomeworkDesc': 'Assignment Info',
  'Mooc_TaskDetail_HomeWork_MyHomework': 'My Assignments',
  'Mooc_TaskDetail_HomeWork_EvaluateEachOther': 'Peer Review',
  'Mooc_TaskDetail_HomeWork_HomeworkSubmitTime': 'Submission time',
  'Mooc_TaskDetail_HomeWork_EvaluateTime': 'Peer review time',
  'Mooc_TaskDetail_HomeWork_SupportResourceType': 'Supported formats',
  'Mooc_TaskDetail_HomeWork_EvaluateCount': 'Assignments to be reviewed',
  'Mooc_TaskDetail_HomeWork_Evaluate': 'Review Now',
  'Mooc_TaskDetail_HomeWork_TotalPoint': 'Full score',
  'Mooc_TaskDetail_HomeWork_EvaluateType': 'Scoring method',
  'Mooc_TaskDetail_HomeWork_TeacherReview': 'Instructor review',
  'Mooc_TaskDetail_HomeWork_StudentEvaluate': 'Peer review',
  'Mooc_TaskDetail_HomeWork_ReviewScore': 'Scores',
  'Mooc_TaskDetail_HomeWork_TotalScore': 'Final score',
  'Mooc_TaskDetail_HomeWork_TeacherScore': 'Score given by instructor',
  'Mooc_TaskDetail_HomeWork_StudentScore': 'Score given by peer reviewer',
  'Mooc_TaskDetail_HomeWork_ReviewPass': 'Assignment passed',
  'Mooc_TaskDetail_HomeWork_FinishedEvaluate': 'Complete the peer review',
  'Mooc_TaskDetail_HomeWork_ReferenceFiles': 'References',
  'Mooc_TaskDetail_HomeWork_HomeWorkRequirements': 'Requirements',
  'Mooc_TaskDetail_HomeWork_AtLeastOneEvalute': 'Please review at least 1 assignment from another participant.',
  'Mooc_TaskDetail_HomeWork_100WeightScore': 'If a score is not provided, the final score will be calculated based on the other score with 100% weight.',
  'Mooc_TaskDetail_HomeWork_ToBeReviewed': 'Pending review',
  'Mooc_TaskDetail_HomeWork_Submited': 'Submitted',
  'Mooc_TaskDetail_HomeWork_UploadAttachments': 'Upload Attachments',
  'Mooc_TaskDetail_HomeWork_UploadImage': 'Images',
  'Mooc_TaskDetail_HomeWork_UploadVideo': 'Videos',
  'Mooc_TaskDetail_HomeWork_UploadDoc': 'Files',
  'Mooc_TaskDetail_HomeWork_UploadAudio': 'Audio',
  'Mooc_TaskDetail_HomeWork_UploadZip': 'Compressed Files',
  'Mooc_TaskDetail_HomeWork_CurrentWords': 'Word count: ',
  'Mooc_TaskDetail_HomeWork_AutoSave': 'The system automatically saves your progress every 3 minutes.',
  'Mooc_TaskDetail_HomeWork_SaveDraft': 'Save as Draft',
  'Mooc_TaskDetail_HomeWork_SubmitHomeWork': 'Submit',
  'Mooc_TaskDetail_HomeWork_SureSubmitHomework': 'Are you sure you want to submit your assignment? After submission, you can also withdraw the assignment to make further changes.',
  'Mooc_TaskDetail_HomeWork_CannotRevoke': 'However, no changes are allowed if the deadline has passed or after a score has been given to your assignment.',
  'Mooc_TaskDetail_HomeWork_SubmitSuccessed': 'Submitted',
  'Mooc_TaskDetail_HomeWork_HWSubmitTime': 'Assignment submission time',
  'Mooc_TaskDetail_HomeWork_Revoke': 'Withdraw and Edit',
  'Mooc_TaskDetail_HomeWork_SureRevoke': 'Are you sure you want to withdraw the assignment to make further changes?',
  'Mooc_TaskDetail_HomeWork_ReSubmitQuickly': 'After withdrawing, the assignment status will be changed to "Not submitted". Please make the changes and resubmit your assignment as soon as possible.',
  'Mooc_TaskDetail_HomeWork_ScoreDetail': 'Score Details',
  'Mooc_TaskDetail_HomeWork_PassScore': 'Passing score',
  'Mooc_TaskDetail_HomeWork_NoTeacherReview': 'No data',
  'Mooc_TaskDetail_HomeWork_NoEvaluate': 'No data',
  'Mooc_TaskDetail_HomeWork_EvaluateDetail': 'Peer Review Details',
  'Mooc_TaskDetail_HomeWork_HoneworkList': 'Assignment List',
  'Mooc_TaskDetail_HomeWork_InputStudentName': "Enter participant's name",
  'Mooc_TaskDetail_HomeWork_MyEvalution': 'My Reviews',
  'Mooc_TaskDetail_HomeWork_HoneworkContent': 'Assignment Info',
  'Mooc_TaskDetail_HomeWork_Submitter': 'Submitted by',
  'Mooc_TaskDetail_HomeWork_SubmitTime': 'Submitted on',
  'Mooc_TaskDetail_HomeWork_NoComment': 'No data',
  'Mooc_TaskDetail_HomeWork_InputEvalute': 'Write a Review',
  'Mooc_TaskDetail_HomeWork_EditEvalute': 'Edit',
  'Mooc_TaskDetail_HomeWork_Anonymous': 'Anonymous',
  'Mooc_TaskDetail_HomeWork_HomeworkComment': 'Comments',
  'Mooc_TaskDetail_HomeWork_ScoreTips': 'Full score is {total}; passing score is {pass}. Please enter an integer no higher than the full score.',
  'Mooc_TaskDetail_HomeWork_InputScore': 'Please enter a score.',
  'Mooc_TaskDetail_HomeWork_InputContent': 'Enter your comments.',
  'Mooc_TaskDetail_HomeWork_InputLessTotalScore': 'Please enter a score no higher than the full score.',
  'Mooc_Home_Search_LabelType': 'Filter by',
  'Mooc_Home_Search_Sort': 'Sort by',
  'Mooc_Home_Search_New': 'Most recent',
  'Mooc_Home_Search_Hot': 'Most popular',
  'Mooc_Home_Search_HighScore': 'Top rated',
  'Mooc_Home_Search_HighPraise': 'Most liked',
  'Mooc_Home_ListItems_JoinStudy': ' signed up',
  'Mooc_Home_ListItems_TotalTasks': 'Total of {count} task(s)',
  'Mooc_Home_ListItems_Joined': 'Signed up',
  'Mooc_Home_ListItems_Excellent': 'Selected',
  'Mooc_Home_ListItems_jiket': '极客时间',
  'Mooc_Home_ListItems_Updating': 'Updating',
  'Mooc_Home_MyProject_MyProjects': 'My Learning Programs',
  'Mooc_Home_MyProject_JoinProjects': 'Signed up',
  'Mooc_Home_MyProject_FinishedProject': 'Completed',
  'Mooc_Home_MyProject_LearnRecently': 'Recently learned',
  'Mooc_Home_MyProject_ProjectDetal': 'Details',
  'Mooc_Home_MyProject_LastProjStatus': '',
  'Mooc_Home_MyProject_IJoinedProjects': 'Programs I Signed Up',
  'Mooc_Home_MyProject_ManageProjects': 'Programs I Manage',
  'Mooc_Home_MyProject_LearnStatus': 'Status',
  'Mooc_Home_MyProject_JoinType': 'Type',
  'Mooc_Home_MyProject_NotLimitedTime': 'No time limits',
  'Mooc_Home_MyProject_LearnCycle': 'Program period: {days} day(s)',
  'Mooc_Home_MyProject_To': ' to ',
  'Mooc_Common_JoinType_RegistBySelf': 'Self-signup',
  'Mooc_Common_JoinType_AddByAdmin': 'Designated by admin',
  'Mooc_Common_JoinType_All': 'All',
  'Mooc_Common_TaskType_NotPublished': 'Unpublished',
  'Mooc_Common_TaskType_Ended': 'Ended',
  'Mooc_Common_Alert_NoContent': 'No more content',
  'Mooc_Common_Alert_Loading': 'Loading...',
  'Mooc_Common_Alert_NoData': 'No content',
  'Mooc_Common_Alert_Cancel': 'Cancel',
  'Mooc_Common_Authority_NoArthority': "You don't have access to this course. Please contact the program admin to request access.",
  'Mooc_Common_Authority_ReturnTrainingProjectHome': 'Back to Program Homepage',
  'Mooc_Common_Authority_ConnectAdmin': 'Program admins:',
  'Mooc_Common_Authority_NotStudyByTime': 'This learning task is unavailable as the program has not started.',
  'Mooc_Common_Authority_StartTime': 'Start time',
  'Mooc_Common_Authority_NotStudyByProjectEnd': 'This learning task is unavailable as the program has ended.',
  'Mooc_Common_Authority_NotStudyByDelay': 'This learning task is unavailable as the program has expired.',
  'Mooc_Common_Authority_ExtendLearnTime': 'Please contact the admin to extend the program duration if needed.',
  'Mooc_Common_Authority_NotStudyByAdminLocked': 'This learning task has been locked by the admin and cannot be accessed.',
  'Mooc_Common_Authority_NotStudyByTaskLocked': 'This learning task is still locked and cannot be accessed.',
  'Mooc_Common_Authority_UnlockTime': 'Unlock time: XXXX',
  'Mooc_Common_Authority_TaskAlert': 'Note',
  'Mooc_Common_Authority_NotSupportPC': 'This task is not supported on PC. Please open it on your mobile device instead.',
  'Mooc_Common_Authority_ViewByWechat': 'Scan the code via Weixin or WeCom to view the task.',
  'Mooc_Common_Authority_LockByFinishPreTask': 'This task will be unlocked after you finish the previous required task.',
  'Mooc_Common_Authority_TaskDIsabled': 'This task has become invalid and cannot be accessed. Please contact the admin for support.',
  'Mooc_Common_Authority_CopyPCLink': 'Copy link',
  'Mooc_Common_Authority_NoDataConnectAdmin': 'The requested data does not exist. Please contact the program admin for support.',
  'Mooc_Common_PageInfo_DataCount': 'Total of {count} item(s)',
  'Mooc_Common_PageInfo_DataPerPage': '{count} items/page',
  'Mooc_Common_PageInfo_DataPageIndex': 'Go to Page {count}',
  'Mooc_Common_Alert_PraiseSucessed': 'Liked',
  'Mooc_Common_Alert_CopySucessed': 'Copied',
  'Mooc_Common_Alert_CollectSucessed': 'Added to favorites',
  'Mooc_Common_Alert_ReplaySucessed': 'Reply posted',
  'Mooc_Common_Alert_CommentSucessed': 'Comment posted',
  'Mooc_Common_Alert_CommonPoint': "You've earned {point} general credit(s).",
  'Mooc_Common_Alert_DeleteSucessed': 'Deleted',
  'Mooc_Common_Alert_CancelPraiseSucessed': 'Like removed',
  'Mooc_Common_Alert_CancelCollectSucessed': 'Removed from favorites',
  'Mooc_Common_Alert_JoinProjectFirst': 'To access this learning task, you need to sign up for the program first.',
  'Mooc_Common_Alert_FinishTaskRemind': "You've Completed the Task",
  'Mooc_Common_Alert_EnableSyncCourse': "Progress sync is turned on for the program. You've already finished the course via other channels.",
  'Mooc_Common_Alert_MultipleTasksRemind': 'You Have Another Learning Task in Progress',
  'Mooc_Common_Alert_NotSupportMultipleTasks': 'Taking multiple tasks at once is not supported. The current task has been paused automatically as you have another ongoing task under the same program. Click "Continue" to resume the current task.',
  'Mooc_Common_Alert_ReturnProjectDetail': 'Back to Program Page',
  'Mooc_Common_Alert_ContinueStudy': 'Continue',
  'Mooc_Common_Alert_AntiHangUp': 'You Have Been Inactive for a While',
  'Mooc_Common_Alert_TriggerAntiHangUp': 'As you have not taken any action on the page for a while, the current task has been automatically paused. Click "Continue" to resume the current task.',
  'Mooc_Common_Alert_NetworkError': 'Network Error',
  'Mooc_Common_Alert_NetworkErrorWarm': 'There is a network error and the learning record cannot be uploaded. Please reconnect to the network and refresh the page. If you continue the task, your latest progress may not be recorded.',
  'Mooc_Common_Alert_RefreshPage': 'Refresh',
  'Mooc_Common_Alert_ServiceError': 'Service Unavailable',
  'Mooc_Common_Alert_ServiceErrorWarm': 'The service is currently unavailable and the learning record cannot be uploaded. Please refresh the page. If you continue the task, your latest progress may not be recorded.',
  'Mooc_Common_Alert_TaskFinishWarning': 'Task Completion Criteria not Met',
  'Mooc_Common_ResourceType_Article': 'Article',
  'Mooc_Common_ResourceType_Zip': 'Compressed file',
  'Mooc_Common_ResourceType_Practice': 'Exercise',
  'Mooc_Common_ResourceType_Exam': 'Exam',
  'Mooc_Common_ResourceType_Audio': 'Audio',
  'Mooc_Common_ResourceType_Video': 'Video',
  'Mooc_Common_ResourceType_HomeWork': 'Assignment',
  'Mooc_Common_ResourceType_Doc': 'File',
  'Mooc_Common_ResourceType_ExternalLinks': 'Link',
  'Mooc_Common_ResourceType_ThirdParty': 'Task on Other Platforms',
  'Mooc_Common_Alert_ProjectNotExist': 'This program does not exist.',
  'Mooc_Common_Alert_ProjectEnded': 'This program has ended.',
  'Mooc_Common_Alert_JoinedProject': 'You have already signed up for this program.',
  'Mooc_Common_Alert_NotSupportRegistSelf': 'This program does not support self-signup.',
  'Mooc_Common_Alert_NotProjectMember': 'You are not the target audience of the current program.',
  'Mooc_Common_Alert_moocCourseIdNull': 'moocCourseId cannot be empty.',
  'Mooc_Common_Alert_CourseNotExist': 'This course does not exist.',
  'Mooc_Common_Alert_ProjectNotExisit': 'Program ID cannot be empty.',
  'Mooc_Common_Alert_DataNotExisit': 'Data does not exist.',
  'Mooc_Common_Alert_HaveNoArthority': "You don't have permission to access this page.",
  'Api_Mooc_Alert_ProjectNotExist': 'This program does not exist.',
  'Api_Mooc_Alert_ProjectEnded': 'This program has ended.',
  'Api_Mooc_Alert_JoinedProject': 'You have already signed up for this program.',
  'Api_Mooc_Alert_NotSupportRegistSelf': 'This program does not support self-signup.',
  'Api_Mooc_Alert_NotProjectMember': 'You are not the target audience of the current program.',
  'Api_Mooc_Alert_moocCourseIdNull': 'moocCourseId cannot be empty.',
  'Api_Mooc_Alert_CourseNotExist': 'This course does not exist.',
  'Api_Mooc_Alert_ProjectNotExisit': 'Program ID cannot be empty.',
  'Api_Mooc_Alert_DataNotExisit': 'Data does not exist.',
  'Api_Mooc_Alert_HaveNoArthority': "You don't have permission to access this page.",
  'Api_Mooc_Error_ServiceError': 'Service unavailable. Please contact the admin.',
  'Api_Mooc_Error_DataDeleted': 'Data has been deleted or does not exist.',
  'Api_Mooc_Error_TaskNotExisit': 'This task does not exist.',
  'Api_Mooc_Error_ThirdPartyDataNull': 'No data found.',
  'Api_Mooc_Error_idNotNull': 'ID cannot be empty.',
  'Api_Mooc_Error_CancelFaild': 'Failed to cancel.',
  'Api_Mooc_Error_PraiseFaild': 'Failed to like.',
  'Api_Mooc_Error_CollectFaild': 'Failed to add to favorites.',
  'Api_Mooc_Error_CommentNotExisit': 'This comment does not exist.',
  'Api_Mooc_Error_ParamError': 'Incorrect parameter.',
  'Api_Mooc_Error_NoticeInsertError': '',
  'Api_Mooc_Error_ResourceNotExisit': 'This resource does not exist.',
  'Api_Mooc_Homework_HomeworkNotExisit': 'This assignment does not exist.',
  'Api_Mooc_Homework_HomeWorkRecourdNotExisit': 'This assignment record does not exist.',
  'Api_Mooc_Homework_HomeworkNotOpen': 'Assignment submissions are not open yet.',
  'Api_Mooc_Homework_HomeWorkDeadline': 'The deadline for assignment submissions has passed.',
  'Api_Mooc_Homework_DownloadError': 'Failed to download the assignment. Please try again.',
  'Api_Mooc_Homework_WriteFileError': 'Failed to download the assignment. Please try again.',
  'Api_Mooc_Homework_NotSubmitTime': 'Unable to withdraw and edit the assignment as submissions are not open currently.',
  'Api_Mooc_Homework_HaveReviewScore': 'Your assignment has been reviewed and graded. If you need to change the content of the assignment, please contact the program admin.',
  'Api_Mooc_Homework_HomeworkNotSubmit': 'This assignment has not been submitted or has been returned.',
  'Api_Mooc_Homework_HomeworkParamError': 'Incorrect assignment parameter.',
  'Api_Mooc_Homework_HomeworkDisableEvalute': 'Assignments are not open for peer review yet.',
  'Api_Mooc_Homework_NotEvaluteTime': 'Peer review has not started.',
  'Api_Mooc_Homework_EvaluteEnd': 'The peer review time has ended.',
  'Api_Mooc_Homework_DisableTeacherReview': 'Assignments are not open for instructor review yet.',
  'Api_Mooc_Homework_ScoreError': 'Invalid score.',
  'Api_Mooc_Homework_CannotReviewSelf': "You can't grade your own assignment.",
  'Api_Mooc_Project_ProjectNotStart': 'This program has not started and is unavailable.',
  'Api_Mooc_Project_ProjectDelayed': 'This program has expired.',
  'Api_Mooc_Project_CannotStudyProjEnd': 'This program has ended and is no longer available.',
  'Api_Mooc_Project_TaskUnLocked': 'The task has not been unlocked yet. Unlock time',
  'Api_Mooc_Project_NoNotices': 'No content',
  'Api_Mooc_Project_CertificateGenerating': 'Certificate generating... Please check later.',
  'Mooc_ProjectDetail_BasicInfo_ProjectEnded': 'This program has ended and is no longer accessible.',
  'Mooc_ProjectDetail_BasicInfo_ProjectUnPublished': 'This program is not yet released and cannot be accessed.',
  'Mooc_ProjectDetail_BasicInfo_ProjectNoArthority': "You don't have permission to access this program.",
  'Mooc_Common_Sure': 'OK',
  'Mooc_Common_Sure_join': 'OK',
  'Mooc_Common_Join_Cancel': 'Cancel',
  'Mooc_Common_ClickPublishComment': 'Add a comment',
  'Mooc_Common_EnterCommentContent': 'Enter a comment',
  'Mooc_Common_CanntDo': 'Operation failed.',
  'Mooc_Common_Done': 'Operation completed.',
  'Mooc_Common_DisableDownload': 'This file cannot be downloaded.',
  'Mooc_Common_DeleteReplayTips': 'Delete Reply',
  'Mooc_Common_DeleteReplayTips1': 'Once deleted, the reply cannot be restored. Are you sure you want to delete the selected reply?',
  'Mooc_Common_Display': 'Show',
  'Mooc_ProjectDetail_TaskList_ExamCheat': 'Cheated on exam',
  'Mooc_Common_NoData': 'No data',
  'Mooc_Common_Seconds': '{0} s',
  'Mooc_ProjectDetail_Documents_TotalAttachements': 'Total {count} attachment(s)',
  'Mooc_ProjectDetail_Congratulations': 'Congrats! You have completed this program.',
  'Mooc_ProjectDetail_GetOneCertificate': "You're granted the following certificate:",
  'Mooc_ProjectDetail_ViewCertificate': 'View Certificate',
  'Mooc_ProjectDetail_TaskList_ViewTime1': '{hour} h {minute} min {second} s',
  'Mooc_ProjectDetail_TaskList_ViewTime2': '{minute} min {second} s',
  'Mooc_ProjectDetail_TaskList_CourseError': 'An unknown error occurred.',
  'Mooc_ProjectDetail_TaskList_ConnectAdminChang': 'Please contact the program admin for help.',
  'Mooc_Home_Search_SearchProject': 'Program search',
  'Mooc_Home_Search_Search': 'Search',
  'Mooc_Home_Search_SearchProjectCount': '{count} searches',
  'Mooc_ProjectDetail_TaskList_NotLimit': 'No limit',
  'Mooc_TaskDetail_HomeWork_View': 'View Assignment',
  'Mooc_TaskDetail_HomeWork_Image': 'Image',
  'Mooc_TaskDetail_HomeWork_Text': 'Text',
  'Mooc_TaskDetail_HomeWork_WriteHomework': 'Do Assignment',
  'Mooc_TaskDetail_HomeWork_ReviewOtherCount': 'Please review at least {count} assignment(s) from another participant.',
  'Mooc_TaskDetail_HomeWork_FinishCondition': 'Task completion criteria',
  'Mooc_TaskDetail_HomeWork_ReSubmitHomework': 'Please resubmit your assignment before doing the peer review.',
  'Mooc_TaskDetail_HomeWork_SubmitHomeworkFirst': 'Please submit your assignment first and then do the peer review.',
  'Mooc_TaskDetail_HomeWork_SubmitThenViewOther': "You can view other participants' assignments after submitting yours.",
  'Mooc_TaskDetail_HomeWork_CanntSaveWhenExit': "The content you're editing will not be saved if you switch to another assignment.",
  'Mooc_TaskDetail_HomeWork_SureToChang': 'Switch to another assignment?',
  'Mooc_TaskDetail_HomeWork_EditHomework': 'Edit Assignment',
  'Mooc_TaskDetail_HomeWork_VideoExt': 'Supported video formats: wmv, mp4, flv, avi, rmvb, mpg, mkv, mov; size: < 2 GB',
  'Mooc_TaskDetail_HomeWork_AudioExt': 'Supported audio formats: w4v, m4a, wma, wav, mp3, amr; size: < 500 MB',
  'Mooc_TaskDetail_HomeWork_DocExt': 'Supported file formats: doc, docx, ppt, pptx, xls, xlsx, pdf; size: < 100 MB',
  'Mooc_TaskDetail_HomeWork_ImgExt': 'Supported image formats: jpg, jpeg, gif, png, bmp, ico, svg; size: < 10 MB',
  'Mooc_TaskDetail_HomeWork_ZipExt': 'Supported compressed file formats: zip, rar; size: < 1 GB',
  'Mooc_TaskDetail_HomeWork_UploadSuccessed': 'Uploaded',
  'Mooc_TaskDetail_HomeWork_FileExtEroor': 'Incorrect file format',
  'Mooc_TaskDetail_HomeWork_FileSize': 'Size',
  'Mooc_TaskDetail_HomeWork_UpdateTime': 'Uploaded on',
  'Mooc_TaskDetail_HomeWork_ReUpload': 'Upload Again',
  'Mooc_TaskDetail_HomeWork_ReadTime': 'Estimated reading time: {minute} min',
  'Mooc_TaskDetail_HomeWork_LastSaveTime': 'Last saved on:',
  'Mooc_TaskDetail_HomeWork_CanntSubmitNoContent': 'Unable to submit as the assignment content is empty.',
  'Mooc_TaskDetail_HomeWork_EnterComment': 'Write Review',
  'Mooc_TaskDetail_HomeWork_PreHomework': 'Previous',
  'Mooc_TaskDetail_HomeWork_NextHomework': 'Next',
  'Article_Original': 'Original',
  'Article_Transport': 'Reposted',
  'Article_Activity': 'Event',
  'Article_Edit': 'Edit',
  'Article_Category': 'Type',
  'Article_Lable': 'Tags',
  'Article_AddCourseList': 'Add to course list',
  'Article_Share': 'Share',
  'Article_OriginalLink': 'Original link',
  'Article_RelevantContent': 'Related content',
  'NetCourse_CreateTime': 'Created on',
  'NetCourse_ClickGo': 'Resume',
  'NetCourse_PlayAt': 'Restart the playback where you left off at {seconds} s?',
  'NetCourse_Timestamp': 'Timestamp',
  'NetCourse_TextScript': 'Script',
  'NetCourse_CourseOffLine': 'This course has been removed.',
  'NetCourse_CourseProcessing': 'This course is being processed. Please try again later.',
  'NetCourse_PlayerNotSupport': 'This course cannot be played in the new-version player. Please switch to the old-version player.',
  'NetCourse_NotSupportRecourse': 'Unable to view this type of resource.',
  'NetCourse_Fullscreen': 'Full Screen',
  'NetCourse_NoDesc': 'No data',
  'NetCourse_NoTextContent': 'No data',
  'Mooc_Home_ProjectStatus': 'Program Status',
  'Mooc_Home_More': 'More',
  'Mooc_Home_Buttom': 'No more content',
  'Mooc_Home_Other': 'Other',
  'Mooc_Home_MyProject': 'My Programs',
  'Mooc_Home_LoadMore': 'Load More',
  'Mooc_Home_NoMore': 'No more content',
  'Mooc_Home_Score': 'Rating',
  'Mooc_Home_Retrun': 'Back',
  'Mooc_Home_Top': 'Back to Top',
  'Mooc_Home_CannotCommentNotInProj': "Unable to comment as you haven't signed up for this program.",
  'Mooc_Home_ScoreSuccessed': 'Rating posted',
  'Mooc_Home_SureRegist': 'Sign Up',
  'Mooc_TaskDetail_RegistSuceessed': 'Signup successful',
  'Mooc_TaskDetail_EnterExitReason': 'Please explain why you want to exit the program.',
  'Mooc_TaskDetail_ExitReasonSucessed': "You've exited the program.",
  'Mooc_TaskDetail_UpdateWechatVersion': 'Unable to use this function as your Weixin version is not up to date. Please update your Weixin and try again.',
  'Api_Mooc_Alert_OpenWithPC': 'You are learning this task on your PC. Please close the task on your other devices.',
  'Api_Mooc_Alert_OpenWithMobile': 'You are learning this task on your mobile device. Please close the task on your other devices.',
  'Api_Mooc_Alert_OpenMutiTasks': 'You are learning multiple tasks at the same time. Please close the tasks in other windows.',
  'Mooc_Common_Alert_EnableSyncExam': "Progress sync is turned on for the program. You've already completed the exam of the current task via other channels.",
  'NetCourse_NoCourse': 'No related courses',
  'NetCourse_ListenVideo': 'Play Audio',
  'NetCourse_SupportHorizontalScreen': 'This is an interactive course that can be played only in landscape mode.',
  'NetCourse_HorizontalPhone': 'Please rotate your screen to landscape mode.',
  'NetCourse_Ok': 'OK',
  'NetCourse_Note': 'Notes',
  'NetCourse_PostNotes': 'Post Notes',
  'NetCourse_Experience': 'Notes',
  'NetCourse_Comment': 'Comments',
  'NetCourse_TextContent': 'Transcript',
  'NetCourse_Expand': 'Expand',
  'NetCourse_Retract': 'Collapse',
  'NetCourse_Teachers': 'and others ({count} in total)',
  'NetCourse_Creator': 'Created by',
  'NetCourse_Contents': 'xx course(s)',
  'NetCourse_ViewCourseList': 'View Course List',
  'NetCourse_Recommended': 'Recommended',
  'NetCourse_Extended': 'Related Courses',
  'Chapter_Summary': 'Chapter Summary', // 此处要查表
  'NetCourse_Hot': 'Most Viewed',
  'NetCourse_Newest': 'Most Recent',
  'NetCourse_NoNotoice': 'No content',
  'NetCourse_NoChapter': 'No content',
  'NetCourse_ReturnCurr': 'Back to Current Line',
  'Mooc_TaskDetail_NoData': 'No content',
  'Mooc_TaskDetail_SaveSuccessed': 'Saved',
  'Mooc_TaskDetail_DownloadTips': 'Note',
  'Mooc_TaskDetail_MiniProgNotSupportDownload': 'This file type is not supported for download in the mini program. Please copy the link and download it on a PC.',
  'Mooc_TaskDetail_Downloading': 'Downloading...',
  'Mooc_TaskDetail_ArthorTips': 'Permission Request',
  'Mooc_TaskDetail_OpenPicture': 'Please turn on Photos permission to download the file.',
  'Mooc_TaskDetail_SureDelAttr': 'Are you sure you want to delete this attachment?',
  'Mooc_TaskDetail_ProgressDetail': 'Training Progress',
  'Mooc_TaskDetail_JoinCount': '{count} participant(s)',
  'Mooc_TaskDetail_LimitStudyCircle': 'Program period',
  'Mooc_TaskDetail_StudyProgress': 'Training Progress',
  'Mooc_TaskDetail_ProjUnStart': 'Program not yet available',
  'Mooc_TaskDetail_StudentUnStart': 'Not started',
  'Mooc_TaskDetail_StudentDoing': 'In progress',
  'Mooc_TaskDetail_StudentFiinshed': 'Completed',
  'Mooc_TaskDetail_StudentDelayed': 'Overdue',
  'Mooc_TaskDetail_StudentOver': 'Program ended',
  'Mooc_TaskDetail_ExamTime': 'Exam time',
  'Mooc_TaskDetail_FirstRegist': 'Please sign up for the program first.',
  'Mooc_TaskDetail_TaskUnLockedNow': 'This task is still locked.',
  'Mooc_TaskDetail_UnlockTime': 'Unlock time',
  'Mooc_TaskDetail_TaskOverTime': 'This task has expired and cannot be accessed.',
  'Mooc_TaskDetail_Official': '官',
  'Mooc_TaskDetail_AddOneRecord': 'Please select at least one course list.',
  'Mooc_TaskDetail_AddSucessed': 'Added',
  'Mooc_TaskDetail_Content': 'Content',
  'Mooc_TaskDetail_ExitProjSucessed': "You've exited the program.",
  'Article_Notes': 'Notes',
  'Article_CourseList': 'Course List',
  'Article_Praise': 'Reward',
  'Article_CourseFrom': 'Source',
  'Article_ShareLink': 'Share Link',
  'Article_ScanQRCode': 'Please use Weixin or WeCom to scan the QR code or copy the link below.',
  'Article_Mobile': 'View on mobile',
  'Article_Previewing': 'Preview Mode',
  'Article_Praised': "The operation failed as you've already rewarded the author.",
  'Article_CantPraiseSelf': "You can't reward your own article.",
  'Article_AuthurLeft': 'The author has left the company and cannot be rewarded.',
  'Article_NoTips': "Don't show again",
  'Mooc_TaskDetail_HomeWork_ViewRequirements': 'View Assignment Requirements',
  'Mooc_TaskDetail_HomeWork_NoComent': 'No content',
  'Mooc_TaskDetail_HomeWork_ReturnList': 'Back to List',
  'Mooc_TaskDetail_HomeWork_Return': 'Reject',
  'Mooc_TaskDetail_HomeWork_Edit': 'Edit',
  'Mooc_TaskDetail_HomeWork_Preview': 'Review',
  'Mooc_TaskDetail_HomeWork_BackHomeworkTips1': 'After the assignment is rejected, the participant will need to resubmit the assignment. The score and review given by the instructor will also be cleared. This action cannot be undone. Please provide the reason for the rejection.',
  'Mooc_TaskDetail_HomeWork_BackHomeworkTips2': 'After the assignment is rejected, the participant will need to resubmit the assignment. This action cannot be undone. Please provide the reason for the rejection.',
  'Mooc_TaskDetail_HomeWork_Score': 'Rating',
  'Mooc_TaskDetail_HomeWork_EnterPoints': 'Please enter a score for the assignment.',
  'Mooc_TaskDetail_HomeWork_EnterPointsTips': 'Full score: {total}; passing score: {pass}. Enter an integer.',
  'Mooc_TaskDetail_HomeWork_SureSubmit': 'Confirm & Submit',
  'Mooc_TaskDetail_HomeWork_StudentBacked': 'The assignment has been rejected and returned.',
  'Mooc_TaskDetail_HomeWork_StudentBackTips': 'Unable to reject the assignment as the deadline for submission of assignments has passed.',
  'Mooc_TaskDetail_HomeWork_BackHomework': 'Reject',
  'Mooc_TaskDetail_HomeWork_EnterBackHomeworkReason': 'Please provide the reason for the rejection.',
  'Mooc_TaskDetail_HomeWork_EditPoint': 'Edit Score',
  'Mooc_TaskDetail_HomeWork_ReviewHomework': 'Assignment review',
  'Mooc_TaskDetail_HomeWork_KeepConfidential': 'The peer review is anonymous. Please keep the content confidential.',
  'Mooc_TaskDetail_HomeWork_EditCommentTips1': 'Unable to edit the review as peer review is not yet open.',
  'Mooc_TaskDetail_HomeWork_EditCommentTips2': 'Unable to write a review as the peer review is not yet open.',
  'Mooc_TaskDetail_HomeWork_EnterComment2': 'Please provide your review.',
  'Mooc_TaskDetail_HomeWork_ScoreHigherTotal': 'The score you entered cannot be higher than the full score.',
  'Mooc_TaskDetail_HomeWork_HWDetal': 'Assignment Details',
  'Mooc_TaskDetail_HomeWork_BackHomeworkTips3': 'Your assignment was returned to you. Please make the changes and resubmit it as soon as possible.',
  'Mooc_TaskDetail_HomeWork_BackHomeworkReason': 'Reason for rejection',
  'Mooc_TaskDetail_HomeWork_EvaluationTime': 'Peer review time',
  'Mooc_TaskDetail_HomeWork_Requirements': 'Requirements',
  'Mooc_TaskDetail_HomeWork_BeforeEvaluationTime': 'The peer review is not yet open.',
  'Mooc_TaskDetail_HomeWork_HWTaskDetail': 'Assignment Details',
  'Mooc_TaskDetail_ThirdParty_GoStudy': 'Learn Now',
  'Mooc_TaskDetail_ThirdParty_ThirdPartyNotSupportMpbile': 'This task is provided by another platform and cannot be accessed via your personal computer. Please finish it on your mobile device.',
  'Mooc_TaskDetail_AlreadyLearned': 'Already learned: ',
  'Mooc_TaskDetail_BackClass': 'Back to Class Details',
  'Mooc_TaskDetail_ContactAdmin': 'Please contact the admin for support.',
  'Mooc_TaskDetail_ProgramLocked': 'This program is still locked and is unavailable.',
  'Mooc_TaskDetail_NotSupportedPC': 'Not supported on PC',
  'Mooc_TaskDetail_NotSupportedMobile': 'Not supported on mobile device',
  'Mooc_ProjectDetail_ProgramNotPublished': 'Program not published',
  'Mooc_ProjectDetail_TrainingPrograms': 'Training Programs',
  'Mooc_ProjectDetail_Training': 'Training',
  'Mooc_ProjectDetail_Programs': 'Programs',
  'Mooc_TaskDetail_UnsupportedFile': 'Unsupported file or loading error',
  'Mooc_TaskDetail_ContentNotSupportedPC': 'This content cannot be opened on a mobile device.',
  'Mooc_TaskDetail_ContentNotExist': 'This content does not exist or has been deleted.',
  'Mooc_TaskDetail_PageNotExist': 'Page not exist.',
  'Mooc_TaskDetail_PermissionDenied': 'Permission denied.',
  'Mooc_TaskDetail_CoursePermissionDenied': "You don't have access to this course. Please contact the program admin for help.",
  'Mooc_TaskDetail_CopyLinkToPC': 'Copy the link and open it on your PC.',
  'Mooc_TaskDetail_ContentNotSupportedPC1': 'This content cannot be opened on a mobile device.',
  'Mooc_TaskDetail_BackHomepage': 'Back to Homepage',
  'Mooc_TaskDetail_BackProjectHomepage': 'Back to Program Homepage',
  'Mooc_TaskDetail_Confirm': 'Confirm',
  'NetCourse_CreateList': 'Create List',
  'NetCourse_Official': 'Official',
  'NetCourse_Private': 'Private'
}

export default window.mooc_All
