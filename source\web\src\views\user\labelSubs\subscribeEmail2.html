<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Q-learning订阅课程</title>
    <style>
        div,p,table,tr,td,th,section,main,ul,li,ol,span,img,aside,nav,a,button,input,h1,h2,h3,h4,h5,h6{
            padding: 0;
            margin: 0;
        }
        ul,li{
            list-style: none;
        }
        .flex{
            display: flex;
        }
        .flex-1{
            flex: 1;
        }
        .align-center{
            align-items: center;
        }
        .justify-center{
            justify-content: center;
        }
        .justify-between{
            justify-content: space-between;
        }
        a{
            text-decoration: none;
        }
    </style>
    <style>
        body {
            font-family: "PingFang SC";
            padding:0;
            margin:0;
            background-image: linear-gradient(180deg, #ebf2ff 0%, #e4edff 21%, #f7faff 100%);
            background-repeat: no-repeat
        }
        table.layout {
            width: 1200px;
            margin: 0 auto;
            border-spacing: 0;
        }
        .email_page {
            width: 1200px;
            margin: 52px auto;
            position: relative;
            overflow: hidden;
        }
        .email_page .headImg{
            position: absolute;
            width: 100%;
            z-index: 0;
        }
        .email_page .email_content {
            position: relative;
            margin-top: 112px;
            padding: 24px 28px;
            background: linear-gradient(180deg, #FFF 0%, #E9F2FF 0%, #F7FAFF 0.01%, #FFF 100%);
            border-radius: 16px;
            z-index: 2;
        }
        .subs_tips {
            padding: 20px 28px;
            background: #EEF5FF;
            border-radius: 16px;
        }
        .subs_tips_text p{
            color: #000000e6;
            font-family: "PingFang SC";
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 28px;
        }
        .subs_tips_text p:nth-child(2) {
            margin-top: 10px;
        }
        .subs_tips_text p a {
            color: #0052d9;
        }
        .subs_tips_btn a {
            display: inline-block;
            height: 44px;
            padding: 0 20px;
            margin-left: 16px;
            border-radius: 8px;
            color: #0052d9;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 44px;
            background: #D9E6FF;
            border: none;
            cursor: pointer;
        }
        .futureCourseData {
            overflow: hidden;
            margin-top: 23px;
        }
        .futureCourseData h4{
            color: #000000;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px;
        }
        .more{
            padding: 4px 16px;
            background: #ECF2FE;
            border-radius: 4px;
            color: #0052d9;
            text-align: center;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 24px;
            cursor: pointer;
        }
        .cardList {
            margin-top: 16px;
            display: flex;
            flex-wrap: wrap;
        }
        .cardList li {
            width: 252px;
            /* height: 317px; */
            margin-right: 42px;
            border-radius: 8px;
            border: 1px solid #F6F6F6;
        }
        .cardList li:nth-child(4n + 0) {
            margin-right: 0;
        }
        .cardList li .courseImg {
            width: 252px;
            height: 167px;
            position: relative;
        }
        .cardList li .courseImg .defaultImg {
            max-width: 100%;
            max-height: 100%;
        }
        .cardList li .courseImg .notStart {
            position: absolute;
            bottom: 10px;
            right: 10px;
            display: inline-block;
            padding: 1px 6px;
            color: #ffffff;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            text-align: center;
            border-radius: 4px;
            background: #00000099;
        }
        .cardList li .courseImg .living {
            width: 58px;
            height: 20px;
            position: absolute;
            right: 10px;
            bottom: 10px;
        }
        .cardList li .courseImg .join {
            position: absolute;
            left: 10px;
            bottom: 10px;
            display: inline-block;
            padding: 1px 6px;
            color: #ffffff;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            text-align: center;
            border-radius: 4px;
            background: #00000099;
        }
        .cardList li .courseImg .updating{
            width: 64px;
            height: 26px;
            position: absolute;
            left: 0;
            top: 0;
        }
        .cardList li .courseImg .excellent{
            position: absolute;
            right: 10px;
            top: 10px;
            display: inline-block;
            border-radius: 4px;
            padding: 1px 6px;
            color: #ffffff;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            background: #FF3000;
        }
        .cardList li .courseImg .official{
            position: absolute;
            right: 10px;
            top: 10px;
            display: inline-block;
            border-radius: 4px;
            padding: 1px 6px;
            color: #ffffff;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            background: #0052D9;
        }
        .schedule{
            position: absolute;
            top: 10px;
            left: 0;
            width: 100%;
            text-align: right;
        }
        .cardList li .courseImg .schedule .excellent{
            margin-left: 8px;
            position: relative;
            top: 0;
        }
        .cardList li .courseImg .schedule .official{
            position: relative;
            top: 0;
        }
        .hangjiaInfo{
            position: absolute;
            width: 100%;
            height: 100%;
            padding: 12px;
            padding-top: 71px;
            display: flex;
            align-items: center;
            box-sizing: border-box;
        }
        .hangjiaInfo .group{
            position: absolute;
            right: 0;
            top: 0;
            color: #bd6600;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            border-radius: 0 4px;
            padding: 2px 6px;
            background: #FFEABB;
        }
        .hangjiaInfo .avatar{
            margin-right: 20px;
        }
        .hangjiaInfo .avatar .good_hj{
            width: 85px;
            height: 68px;
            background: url('https://xue.m.tencent.com/mail/labelsub/good_hj.png');
            background-size: 100% 100%;
            text-align: center;
            position: relative;
            padding-top: 5px;
            padding-left: 1px;
        }
        .hangjiaInfo .avatar .good_hj_text{
            position: absolute;
            bottom: 0px;
            height: 20px;
            width: 60px;
            right: 11px;
        }
        .hangjiaInfo .avatar .good_hj_img{
            width: 52px;
            height: 52px;
            border-radius: 50%;
        }
        .hangjiaInfo .rightInfo{
            flex: 1;
        }
        .hangjiaInfo .rightInfo .expertName{
            color: #333333;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
        }
        .hangjiaInfo .rightInfo .tag{
            color: #777777;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            margin-top: 2px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            word-break: break-all;
        }
        .hangjiaInfo .rightInfo .consult{
            margin-top: 4px;
            color: #a3a3a3;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            align-items: center;
            
        }
        .hangjiaInfo .rightInfo .consult .split{
            margin: 0 7px;
        }
        .hangjiaInfo .rightInfo .consult .active{
            color: #0052d9;
            margin: 0 1px;
        }
        .courseInfo {
            padding: 18px 5px 0 12px;
        }
        .ascription{
            padding: 0 12px 18px;
        }
        .courseTitle {
            height: 48px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            word-break: break-all;
        }
        .courseTitle .tag {
            display: inline-block;
            padding: 0 4px;
            border-radius: 2px;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            color: #777777;
            background: #F5F5F7
        }
        .courseTitle .text {
            color: #333333;
            text-align: justify;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 24px;
        }
        .courseViews{
            display: flex;
            margin-top: 12px;
        }
        .courseViews img{
            width: 16px;
            height: 16px;
            margin-right: 2px;
        }
        .courseViews span{
            margin-right: 8px;
            color: #a3a3a3;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
        }
        .courseViews span:last-child{
            margin-right: 0;
        }
        .courseViews span.real_price {
            color: #FF7200;
        }
        .courseViews span.origin_price {
            color: #A3A3A3;
            text-decoration: line-through;
        }
        .courseTime {
            display: flex;
            margin-top: 12px;
            color: #a3a3a3;
            text-align: justify;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
        }
        .courseTime img {
            width: 16px;
            height: 16px;
            margin-right: 2px;
        }
        .courseLocation {
            margin-top: 4px;
        }
        .courseLocation  img{
            width: 16px;
            height: 16px;
            margin-right: 2px;
        }
        .courseLocation span {
            color: #a3a3a3;
            text-align: justify;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .courseRelatedLabelList {
            height: 22px;
            overflow: hidden;
            margin-top: 12px;
        }
        .courseRelatedLabelList span {
            display: inline-block;
            margin-right: 8px;
            padding: 2px 6px;
            border-radius: 4px;
            color: #777777;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
            background: #F5F7FA;
            cursor: pointer;
        }
        .courseRelatedLabelList span.active{
            color: #0052d9;
            background: #F4F9FE;
        }
        .courseRelatedSpecail{
            margin-top: 12px;
            color: #00000066;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
        }
        .courseRelatedSpecail span{
            color: #000000e6;
            cursor: pointer;
        }
        .courseRelatedSpecail span:hover{
            color: #0052D9;
        }
        .tableList {
            margin-top: 16px;
        }
        .tableList li{
            margin-bottom: 12px;
        }
        .tableList li .title{
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;
            color: #000000e6;
            text-overflow: ellipsis;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            cursor: pointer;
        }
        .tableList li .title .tag{
            display: inline-block;
            padding: 0 4px;
            border-radius: 2px;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            color: #777777;
            background: #F5F5F7
        }
        .tableList li .rightInfo img{
            width: 16px;
            height: 16px;
            margin-right: 2px;
        }
        .tableList li .rightInfo .time{
            margin-left: 16px;
            color: #a3a3a3;
            text-align: justify;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
        }
        .tableList li .rightInfo .location{
            margin-left: 16px;
            color: #a3a3a3;
            text-align: justify;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
        }
        .tableList li .rightInfo .relatedSpecial {
            margin-left: 16px;
            color: #00000066;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
        }
        .tableList li .rightInfo .relatedSpecial span{
            color: #000000e6;
            cursor: pointer;
        }
        .tableList li .rightInfo .relatedLabel {
            margin-left: 16px;
            padding: 2px 6px;
            background: #F4F9FE;
            cursor: pointer;
        }
        .tableList li .rightInfo .relatedLabel span {
            display: inline-block;
            color: #0052d9;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
            border-radius: 4px;
            margin-left: 2px;
        }
        .previousCourseData{
            overflow: hidden;
            margin-top: 20px;
        }
        .xt{
            margin-top: 20px;
            padding: 20px 28px;
            border-radius: 16px;
            background: #EEF5FF;
            color: #000000e6;
            font-family: "PingFang SC";
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 28px;
        }
    </style>
</head>
<body>
    <table class="layout">
        <tbody>
            <tr>
                <td>
                    <main class="email_page">
                        <img src="https://xue.m.tencent.com/mail/labelsub/head.png" class="headImg" alt="">
                        <div class="email_content">
                            <section class="subs_tips flex justify-between">
                                <div class="subs_tips_text">
                                    <p>Hi，yiyan(严燚)</p>
                                    <p>您订阅的<a href="https://portal.learn.woa.com/training/label-subs?jump_from=mail4&project=qlxtts&source=bqdy">标签及专区</a>最近一周新增了以下内容，开始学习吧！</p>
                                </div>
                                <div class="subs_tips_btn">
                                    <a href="https://portal.learn.woa.com/training/label-subs?stime=1&jump_from=mail4&project=qlxtts&source=bqdy">查看更多内容</a>
                                    <a href="https://portal.learn.woa.com/training/label-subs?dialog=1&jump_from=mail4&project=qlxtts&source=bqdy">管理我的订阅</a>
                                    <a href="https://portal.learn.woa.com/training/label-subs?dialog=2&jump_from=mail4&project=qlxtts&source=bqdy">修改订阅提醒</a>
                                </div>
                            </section>
                            <section class="futureCourseData">
                                <div class="flex align-center justify-between">
                                    <h4>即将开始</h4>
                                    <a class="more" href="https://portal.learn.woa.com/training/label-subs?jump_from=mail4&project=qlxtts&source=bqdy">查看更多</a>
                                </div>
                                <ul class="futureCardList cardList">
                                    <li>
                                        <div class="courseImg flex align-center justify-center">
                                            <img src="" class="defaultImg" alt="">
                                            <img src="https://xue.m.tencent.com/mail/labelsub/living.png" class="living" alt="">
                                            <span class="notStart">未开始</span>
                                        </div>
                                        <div class="courseInfo">
                                            <div class="courseTitle">
                                                <span class="tag">直播</span>
                                                <span class="text">基于云计算技术阶段学习学习指导课程学习指南</span>
                                            </div>
                                            <div class="courseTime flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>2023-02-21 12:00~2023-02-21 14:00</span>
                                            </div>
                                        </div>
                                        <div class="ascription">
                                            <div class="courseRelatedLabelList">
                                                <span class="active">云计算</span>
                                                <span>学习指南</span>
                                                <span>学习指南111</span>
                                            </div>
                                            <div class="courseRelatedSpecail">
                                                来自专区<span>「这是专区的名称」</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="courseImg flex align-center justify-center">
                                            <img src="" class="defaultImg" alt="">
                                            <span class="notStart">60分钟</span>
                                        </div>
                                        <div class="courseInfo">
                                            <div class="courseTitle">
                                                <span class="tag">活动</span>
                                                <span class="text">基于云计算技术阶段学习学习指导课程学习指南</span>
                                            </div>
                                            <div class="courseTime flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>2023-02-21 12:00~2023-02-21 14:00</span>
                                            </div>
                                            <div class="courseLocation flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/local2x.png" alt="">
                                                <span>腾讯滨海大厦S2412</span>
                                            </div>
                                        </div>
                                        <div class="ascription">
                                            <div class="courseRelatedLabelList">
                                                <span class="active">云计算</span>
                                                <span>学习指南</span>
                                                <span>学习指南111</span>
                                                <span>学习指南111</span>
                                                <span>学习指南111</span>
                                            </div>
                                            <div class="courseRelatedSpecail">
                                                来自专区<span>「这是专区的名称」</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="courseImg flex align-center justify-center">
                                            <img src="" class="defaultImg" alt="">
                                            <span class="notStart">60分钟</span>
                                        </div>
                                        <div class="courseInfo">
                                            <div class="courseTitle">
                                                <span class="tag">面授课</span>
                                                <span class="text">基于云计算技术阶段学习学习指导课程学习指南</span>
                                            </div>
                                            <div class="courseViews flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                <span>1.3W</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/flower2x.png" alt="">
                                                <span>4.8分</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>2012-11-12 12:00</span>
                                            </div>
                                        </div>
                                        <div class="ascription">
                                            <div class="courseLocation flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/local2x.png" alt="">
                                                <span>腾讯滨海大厦S2412</span>
                                            </div>
                                            <div class="courseRelatedLabelList">
                                                <span class="active">云计算</span>
                                                <span>学习指南</span>
                                                <span>学习指南111</span>
                                            </div>
                                            <div class="courseRelatedSpecail">
                                                来自专区<span>「这是专区的名称」</span>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                                <ul class="futureTableList tableList">
                                    <li class="flex justify-between align-center">
                                        <div class="title flex align-center flex-1">
                                            <span class="tag">直播</span>
                                            <span>内容标题内容标题内容标题内容标题内容标题内容标题内容标内容标内容标内容标内容标题</span>
                                        </div>
                                        <div class="rightInfo flex align-center">
                                            <div class="relatedLabel flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/tag.png" alt="">
                                                <span>云计算</span>
                                            </div>
                                            <div class="time flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>2023-02-21 12:00~2023-02-21 14:00</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li class="flex justify-between align-center">
                                        <div class="title flex align-center flex-1">
                                            <span class="tag">面授课</span>
                                            <span>内容标题内容标题内容标题内容标题内容标题内容标题内容标内容标内容标内容标内容标题</span>
                                        </div>
                                        <div class="rightInfo flex align-center">
                                            <div class="relatedLabel flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/tag.png" alt="">
                                                <span>云计算</span>
                                            </div>
                                            <div class="relatedSpecial flex align-center">
                                                来自专区<span>「这是专区B」</span>
                                            </div>
                                            <div class="location flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/local2x.png" alt="">
                                                <span>腾讯滨海大厦S2412</span>
                                            </div>
                                            <div class="time flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>2023-02-21 12:00~2023-02-21 14:00</span>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </section>
                            <section class="previousCourseData">
                                <div class="flex align-center justify-between">
                                    <h4>立即学习</h4>
                                    <a class="more" href="https://portal.learn.woa.com/training/label-subs?jump_from=mail4&project=qlxtts&source=bqdy">查看更多</a>
                                </div>
                                <ul class="previousCardList cardList">
                                    <li>
                                        <div class="courseImg flex align-center justify-center">
                                            <img src="" class="defaultImg" alt="">
                                            <span class="notStart">60分钟</span>
                                        </div>
                                        <div class="courseInfo">
                                            <div class="courseTitle">
                                                <span class="tag">网格课</span>
                                                <span class="text">基于云计算技术阶段学习学习指导课程学习指南</span>
                                            </div>
                                            <div class="courseViews flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                <span>2546</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/flower2x.png" alt="">
                                                <span>4.8分</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>2023-02-21</span>
                                            </div>
                                        </div>
                                        <div class="ascription">
                                            <div class="courseRelatedLabelList">
                                                <span class="active">云计算</span>
                                                <span>学习指南</span>
                                                <span>学习指南111</span>
                                            </div>
                                            <div class="courseRelatedSpecail">
                                                来自专区<span>「这是专区的名称」</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="courseImg flex align-center justify-center">
                                            <img src="" class="defaultImg" alt="">
                                            <img src="https://xue.m.tencent.com/mail/labelsub/updating.png" class="updating">
                                            <span class="notStart">15项任务</span>
                                            <span class="join">已参与</span>
                                            <span class="excellent">精品</span>
                                        </div>
                                        <div class="courseInfo">
                                            <div class="courseTitle">
                                                <span class="tag">培养项目</span>
                                                <span class="text">基于云计算技术阶段学习学习指导课程学习指南</span>
                                            </div>
                                            <div class="courseViews flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/flower2x.png" alt="">
                                                <span>4.8分</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/people2x.png" alt="">
                                                <span>39495人参与</span>
                                            </div>
                                        </div>
                                        <div class="ascription">
                                            <div class="courseRelatedLabelList">
                                                <span class="active">云计算</span>
                                                <span>学习指南</span>
                                                <span>学习指南111</span>
                                            </div>
                                            <div class="courseRelatedSpecail">
                                                来自专区<span>「这是专区的名称」</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="courseImg flex align-center justify-center">
                                            <img src="" class="defaultImg" alt="">
                                        </div>
                                        <div class="courseInfo">
                                            <div class="courseTitle">
                                                <span class="tag">外链课程</span>
                                                <span class="text">基于云计算技术阶段学习学习指导课程学习指南</span>
                                            </div>
                                            <div class="courseViews flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>2012-11-12</span>
                                            </div>
                                        </div>
                                        <div class="ascription">
                                            <div class="courseRelatedLabelList">
                                                <span class="active">云计算</span>
                                                <span>学习指南</span>
                                                <span>学习指南111</span>
                                            </div>
                                            <div class="courseRelatedSpecail">
                                                来自专区<span>「这是专区的名称」</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="courseImg flex align-center justify-center">
                                            <img src="http://km.woa.com/asset/raw/url?raw=files/posts/header_imgs/418861.jpeg" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/default-photo-16.png'" class="defaultImg" alt="">
                                        </div>
                                        <div class="courseInfo">
                                            <div class="courseTitle">
                                                <span class="tag">文档</span>
                                                <span class="text">基于云计算技术阶段学习学习指导课程学习指南</span>
                                            </div>
                                            <div class="courseViews flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                <span>1.3W</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/like2x.png" alt="">
                                                <span>3949</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>2012-11-12</span>
                                            </div>
                                        </div>
                                        <div class="ascription">
                                            <div class="courseRelatedLabelList">
                                                <span class="active">云计算</span>
                                                <span>学习指南</span>
                                                <span>学习指南111</span>
                                            </div>
                                            <div class="courseRelatedSpecail">
                                                来自专区<span>「这是专区的名称」</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="courseImg flex align-center justify-center">
                                            <img src="" class="defaultImg" alt="">
                                            <span class="notStart">6000字</span>
                                            <span class="excellent">精品</span>
                                        </div>
                                        <div class="courseInfo">
                                            <div class="courseTitle">
                                                <span class="tag">文章</span>
                                                <span class="text">基于云计算技术阶段学习学习指导课程学习指南</span>
                                            </div>
                                            <div class="courseViews flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                <span>1.3W</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/like2x.png" alt="">
                                                <span>3949</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>2012-11-12</span>
                                            </div>
                                        </div>
                                        <div class="ascription">
                                            <div class="courseRelatedLabelList">
                                                <span class="active">云计算</span>
                                                <span>学习指南</span>
                                                <span>学习指南111</span>
                                            </div>
                                            <div class="courseRelatedSpecail">
                                                来自专区<span>「这是专区的名称」</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="courseImg flex align-center justify-center">
                                            <img src="" class="defaultImg" alt="">
                                            <span class="notStart">6000字</span>
                                            <span class="excellent">精品</span>
                                        </div>
                                        <div class="courseInfo">
                                            <div class="courseTitle">
                                                <span class="tag">案例</span>
                                                <span class="text">基于云计算技术阶段学习学习指导课程学习指南</span>
                                            </div>
                                            <div class="courseViews flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                <span>1.3W</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/like2x.png" alt="">
                                                <span>3949</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>2012-11-12</span>
                                            </div>
                                        </div>
                                        <div class="ascription">
                                            <div class="courseRelatedLabelList">
                                                <span class="active">云计算</span>
                                                <span>学习指南</span>
                                                <span>学习指南111</span>
                                            </div>
                                            <div class="courseRelatedSpecail">
                                                来自专区<span>「这是专区的名称」</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="courseImg flex align-center justify-center">
                                            <img src="" class="defaultImg" alt="">
                                            <span class="notStart">20门课</span>
                                            <div class="schedule">
                                                <span class="official">官方</span>
                                                <span class="excellent">精品</span>
                                            </div>
                                        </div>
                                        <div class="courseInfo">
                                            <div class="courseTitle">
                                                <span class="tag">课单</span>
                                                <span class="text">基于云计算技术阶段学习学习指导课程学习指南</span>
                                            </div>
                                            <div class="courseViews flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                <span>1.3W</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/like2x.png" alt="">
                                                <span>9999</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/star2x.png" alt="">
                                                <span>9999</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/comments2x.png" alt="">
                                                <span>9999</span>
                                            </div>
                                        </div>
                                        <div class="ascription">
                                            <div class="courseRelatedLabelList">
                                                <span class="active">云计算</span>
                                                <span>学习指南</span>
                                                <span>学习指南111</span>
                                            </div>
                                            <div class="courseRelatedSpecail">
                                                来自专区<span>「这是专区的名称」</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="courseImg flex align-center justify-center">
                                            <img src="" class="defaultImg" alt="">
                                        </div>
                                        <div class="courseInfo">
                                            <div class="courseTitle">
                                                <span class="tag">K吧文章</span>
                                                <span class="text">基于云计算技术阶段学习学习指导课程学习指南</span>
                                            </div>
                                            <div class="courseViews flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                <span>1.3W</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/comments2x.png" alt="">
                                                <span>9999</span>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>2023-02-21</span>
                                            </div>
                                        </div>
                                        <div class="ascription">
                                            <div class="courseRelatedLabelList">
                                                <span class="active">云计算</span>
                                                <span>学习指南</span>
                                                <span>学习指南111</span>
                                            </div>
                                            <div class="courseRelatedSpecail">
                                                来自专区<span>「这是专区的名称」</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li>
                                        <div class="courseImg flex align-center justify-center">
                                            <img src="" class="defaultImg" alt="">
                                            <div class="hangjiaInfo">
                                                <div class="avatar">
                                                    <div class="good_hj" v-if="info.origin_data && info.origin_data.is_excellent_hangjia">
                                                        <img class="good_hj_img" src="https://xue.m.tencent.com/mail/labelsub/xt.png">
                                                        <img class="good_hj_text" src="https://xue.m.tencent.com/mail/labelsub/good_hj_text.png" alt="">
                                                    </div>
                                                    <!-- <img class="good_hj_img" src="https://xue.m.tencent.com/mail/labelsub/xt.png"> -->
                                                </div>
                                                <div class="rightInfo">
                                                    <p class="expertName">Simily(颜诗雨)</p>
                                                    <p class="tag" title="设计专家/资深设计师/全栈设计师/设计师...">设计专家/资深设计师/全栈设计师/设计师...</p>
                                                    <div class="consult flex">
                                                        <span class="active">12</span>单咨询
                                                        <span class="split">|</span>
                                                        <span v-if="info.origin_data.count && info.origin_data.count !== '0'"><span class="active">95.69</span>分</span>
                                                        <!-- <span v-else>暂无评分</span> -->
                                                    </div>
                                                </div>
                                                <span class="group" v-if="info.origin_data.least_group_sharing_count">3人预约即可成团</span>
                                            </div>
                                        </div>
                                        <div class="courseInfo">
                                            <div class="courseTitle">
                                                <span class="tag">行家</span>
                                                <span class="text">基于云计算技术阶段学习学习指导课程学习指南</span>
                                            </div>
                                            <div class="courseViews flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/jf.png" alt="">
                                                <span class="real_price">400积分/次</span>
                                                <span class="origin_price">524积分/次</span>
                                            </div>
                                        </div>
                                        <div class="ascription">
                                            <div class="courseRelatedLabelList">
                                                <span class="active">云计算</span>
                                                <span>学习指南</span>
                                                <span>学习指南111</span>
                                            </div>
                                            <div class="courseRelatedSpecail">
                                                来自专区<span>「这是专区的名称」</span>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                                <ul class="previousTableList tableList">
                                    <li class="flex justify-between align-center">
                                        <div class="title flex align-center flex-1">
                                            <span class="tag">直播</span>
                                            <span>内容标题内容标题内容标题内容标题内容标题内容标题内容标内容标内容标内容标内容标题</span>
                                        </div>
                                        <div class="rightInfo flex align-center">
                                            <div class="relatedLabel flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/tag.png" alt="">
                                                <span>云计算</span>
                                            </div>
                                        </div>
                                    </li>
                                    <li class="flex justify-between align-center">
                                        <div class="title flex align-center flex-1">
                                            <span class="tag">面授课</span>
                                            <span>内容标题内容标题内容标题内容标题内容标题内容标题内容标内容标内容标内容标内容标题</span>
                                        </div>
                                        <div class="rightInfo flex align-center">
                                            <div class="relatedLabel flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/tag.png" alt="">
                                                <span>云计算</span>
                                            </div>
                                            <div class="relatedSpecial flex align-center">
                                                来自专区<span>「这是专区B」</span>
                                            </div>
                                        </div>
                                    </li>
                                </ul>
                            </section>
                        </div>
                    </main>
                </td>
            </tr>
        </tbody>
    </table>
</body>
</html>