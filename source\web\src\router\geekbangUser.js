const manage = [
  {
    path: '/outsourcedCourse',
    name: 'outsourcedCourse',
    component: () => import('views/user/index'),
    meta: {
      title: 'ai虚拟人做课工具'
    },
    children: [
      // 极客时间
      {
        path: 'user/activePage',
        name: 'activePage',
        component: () => import('views/user/outsourced-course/activePage.vue'),
        meta: {
          title: '活动首页'
        }
      },
      // 三节课
      {
        path: 'user/activePage/sanjieke',
        name: 'activePageSanJieKe',
        component: () => import('views/user/outsourced-course/activePageSanJieKe.vue'),
        meta: {
          title: '活动首页'
        }
      },
      // 外部课程iframe页
      {
        path: 'iframe/play',
        name: 'iframePlay',
        component: () => import('views/user/outsourced-course/iframePlay.vue'),
        meta: {
          title: '外部课'
        }        
      },
      // 外部课程iframe无上报记录页
      {
        path: 'iframeNoLearnRecords/play',
        name: 'iframePlay',
        component: () => import('views/user/outsourced-course/iframeNoLearnRecordsPlay.vue'),
        meta: {
          title: '外部课'
        }        
      },
      {
        path: 'outside/play',
        name: 'outside',
        component: () => import('views/user/outsourced-course/outside.vue'),
        meta: {
          title: '外部课'
        }        
      },
      // 学霸卡活动页
      {
        path: 'user/activePage/xueba',
        name: 'xueBaPassCard',
        component: () => import('views/user/outsourced-course/xueBaCardActive.vue'),
        meta: {
          title: '活动首页'
        }
      },
      // 哈商活动页
      {
        path: 'user/activePage/harvard',
        name: 'accountCard',
        component: () => import('views/user/outsourced-course/accountCardActive.vue'),
        meta: {
          title: '哈佛精品文库好文免费看'
        }        
      }    
    ]
  }
]

export default manage
