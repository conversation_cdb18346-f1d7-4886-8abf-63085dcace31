.flex{
    display: flex;
}
.flex-wrap{
    flex-wrap: wrap;
}
.flex-1{
    flex: 1;
}
.flex-10{
    flex: 10;
}
.align-center{
    align-items: center;
}
.align-start{
    align-items: flex-start;
}
.justify-between{
    justify-content: space-between;
}
.justify-end{
    justify-content: flex-end;
}
.relative{
    position: relative;
}
.center{
    text-align: center;
}
.linkKey{
    color: #0052D9;
    cursor: pointer;
}
.pointer{
    cursor: pointer;
}


.labelCardItem {
    min-height: 288px;
    width: 100%;
    // flex: 1;
    cursor: pointer;
    .courseImage{
        height: 160px;
        position: relative;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        img{
            width: 100%;
            max-height: 100%;
        }
        .duration{
            position: absolute;
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            background: #00000099;
            font-size: 12px;
            font-weight: 500;
            line-height: 18px;
            color: #fff;
            right: 10px;
            bottom: 10px;
        }
        .partake{
            position: absolute;
            display: inline-flex;
            padding: 2px 6px;
            border-radius: 4px;
            background: #00000099;
            font-size: 12px;
            font-weight: 500;
            line-height: 18px;
            color: #fff;
            left: 10px;
            bottom: 10px;
        }
        .update_status{
            position: absolute;
            left: 0;
            top: 0;
            width: 58px;
            height: 20px;
        }
        .update_status_outcourse{
            position: absolute;
            left: 0;
            top: 0;
            width: auto;
            height: 20px;
        }
        .boutique1,.boutique2{
            display: inline-block;
            padding: 1px 6px;
            border-radius: 4px;
            background: #FF3000;
            font-size: 12px;
            font-weight: 500;
            line-height: 18px;
            color: #fff;
            position: absolute;
            top: 10px;
            right: 10px;
        }
        .boutique1{
          left: inherit;
          right: 10px;
        }
        .kdident{
          position: absolute;
          top: 10px;
          right: 10px;
          width: 100%;
          height: 22px;
          text-align: right;
          .official{
            display: inline-block;
            text-align: center;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 400;
            color: #fff;
            padding: 1px 6px;
            background: #0052D9;
            line-height: 18px;
          }
          .boutique2{
            position: relative;
            margin-left: 8px;
            top: 0;
            right: 0;
          }
        }
        .liveIcon{
            position: absolute;
            right: 10px;
            bottom: 10px;
            background:none;
            padding: 0;
            img{
                width: 58px;
                height: 22px;
            }
        }
        // .consult{
        //     position: absolute;
        //     bottom: 0;
        //     width: 100%;
        //     height: 35px;
        //     padding: 2px 12px;
        //     background: #00000066;
        //     align-items: center;
        //     color: #fff;
        //     font-size: 12px;
        // }
    }
    .courseInfo{
        padding: 18px 2px 18px 12px;
        .cardTitle{
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            word-break: break-all;
            height: 48px;
            font-family: "PingFang SC";
            padding-right: 10px;
            .moduleName{
                display: inline-block;
                padding: 0 4px;
                margin-right: 8px;
                border-radius: 2px;
                background: #F5F5F7;
                font-family: "PingFang SC";
                font-size: 12px;
                font-style: normal;
                font-weight: 500;
                line-height: 18px;
                color: #777;
            }
            .moduleTitle{
                color: #333333;
                font-family: "PingFang SC";
                font-size: 16px;
                font-style: normal;
                font-weight: 500;
                line-height: 24px;
            }
        }
        .courseView{
            color: #00000042;
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            margin: 12px 0;
            img {
                width: 16px;
                height: 16px;
                margin-right: 2px;
            }
            span{
                color: #a3a3a3;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;
                margin-right: 12px;
            }
            img+img{
                margin-top: 0 !important;
            }
            div.hjDesc{
              p{
                color: #00000099;
              }
            }
            .timeRang{
                margin-right: 0 !important;
            }
        }
        .courseView.kdInfo{
          color: #999999;
        }
        .local{
            margin-top: 4px;
            align-items: flex-start;
            img{
                margin-top: 2px;
            }
        }
        .liveTime{
            white-space: nowrap;
        }
        .relevantLabel{
            color: #00000042;
            font-size: 12px;
            font-weight: 400;
            line-height: 16px;
            a{
                color: #00000042;
            }
            a:hover{
                color: #0052D9;
            }
        }
    }
}