<template>
  <div class="activity-rules-popup">
    <van-popup v-model="popupShow" round position="bottom" :overlay-style="{ background: 'rgba(0, 0, 0, 0.4)'}">
      <div class="popup-card">
        <div class="popup-head">
          <div class="title">获取抽奖次数</div>
          <img class="close" @click="onClose" src="../../../../assets/img/mobile/lottery/close.png" alt="" srcset="">
        </div>
        <div class="popup-body">
          <div class="illustrate">
            <div class="title">抽奖规则</div>
            <div class="text">
              <p>1，活动期间通过腾讯学堂小程序参与学习网络课（不含直播）体验，可获取1次抽奖机会。</p>
              <p>2，获取的抽奖次数当日有效，当日23:59:59后失效。请尽快使用。</p>
            </div>
          </div>
          <!-- 当前活动只能通过学习获取一次抽奖机会，不需要实时更新能获取机会次数 -->
          <div class="method-card" v-if="false">
            <div class="li">
              <div class="label">1、学习课程：今日还可获得{{ pageData.count_from_share }}次抽奖机会</div>
              <!-- <div class="value">活动期间通过腾讯学堂小程序参与学习网络课（不含直播）体验，可获取1次抽奖机会。</div> -->
            </div>
            <div class="li">
              <div class="label">2、分享笔记：今日还可获得{{ pageData.count_from_study }}次抽奖机会</div>
              <!-- <div class="value">获取的抽奖次数当日有效，当日23:59:59后失效。请尽快使用。</div> -->
            </div>
          </div>
          <div class="btn-column">
            <van-button class="btn" :disabled="finish" @click="toStudy">更多活动内容，点此了解！</van-button>
            <div class="tip" v-if="finish">
              <img class="icon" src="../../../../assets/img/mobile/lottery/warning-blue.png" alt="" srcset="">
              <span>今日通过学习课程可获取的抽奖次数已达上限</span>
            </div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { 
  Popup,
  List,
  Loading
} from 'vant'
import { getLotteryCountRemainAPI } from '@/config/lottery.api.conf.js'

export default {
  props: {
    value: {
      type: Boolean,
      default: true,
      require: true
    }
  },
  components: {
    [Popup.name]: Popup,
    [List.name]: List,
    [Loading.name]: Loading
  },
  computed: {
    finish() {
      let totalNum = this.pageData.count_from_share + this.pageData.count_from_study
      return totalNum <= 0
    }
  },
  data() {
    return {
      popupShow: this.value,
      pageData: {
        count_from_share: 1, // 当前活动只能通过学习获取一次抽奖机会，固定为一次
        count_from_study: 0
      }
    }
  },
  mounted() {
    // 当前活动只能通过学习获取一次抽奖机会，不需要实时更新能获取机会次数
    // this.getPageData()
  },
  methods: {
    getPageData() {
      let userInfo = JSON.parse(sessionStorage.getItem('login_user'))
      getLotteryCountRemainAPI(userInfo.staff_id).then(res => {
        this.pageData = res
        console.log(res)
      })
    },
    // 关闭弹窗
    onClose() {
      this.popupShow = false
      this.$emit('input', this.popupShow)
    },
    // 去学习
    toStudy() {
      console.log('去学习！')
      window.location.href = 'https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=36262'
    }
  },
  beforeDestroy() {
  }
}
</script>

<style lang='less' scoped>
  .popup-card {
    width: 100vw;
    min-height: 70vh;
    max-height: 80vh;
    letter-spacing: 0.5px;
    color: rgba(16, 16, 16, 1);
    display: flex;
    flex-direction: column;
    letter-spacing: 0.5px;
    .popup-head {
      padding: 16px;
      position: relative;
      flex-shrink: 0;
      .title {
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px;
        font-family:  "PingFang SC";
        text-align: center;
      }
      .close {
        width: 24px;
        height: 24px;
        position: absolute;
        top: 17px;
        right: 16px;
      }
    }
    .popup-body {
      flex: 1;
      padding: 8px 0 20px;
      overflow-y: auto;
      overflow-x: hidden;
      color: #000000e6;
      position: relative;
      .illustrate {
        padding: 0 20px 24px;
        font-style: normal;
        .title {
          font-size: 14px;
          font-weight: 600;
          line-height: 22px;
          margin-bottom: 8px;
        }
        .text {
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
        }
      }
      .method-card {
        padding: 16px;
        border-radius: 8px;
        background: #F5F5F5;
        margin: 0 16px;
        .li {
          margin-bottom: 16px;
          font-family: "PingFang SC";
          .label {
            align-self: stretch;
            font-size: 14px;
            font-weight: 600;
            line-height: 22px;
          }
          .value {
            color: #00000099;
            font-size: 14px;
            line-height: 22px;
          }
        }
        .li:last-child {
          margin-bottom: 0;
        }
      }
      .btn-column {
        min-height: 76px;
        margin-top: 90px;
        position: absolute;
        left: 0;
        bottom: 34px;
        .btn {
          width: 335px;
          height: 48px;
          background-color: #0052D9;
          padding: 12px 20px;
          border-radius: 56px;
          color: #ffffff;
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
          margin: 0 5vw;
        }
        .tip {
          display: flex;
          justify-content: center;
          align-items: center;
          color: #0052d9;
          font-size: 12px;
          line-height: 16px;
          margin-top: 12px;
          .icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
          }
        }
      }
    }
  }
</style>
