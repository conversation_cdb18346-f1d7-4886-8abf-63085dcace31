<template>
  <div class="questionPage">
    <iframe :src="questionSrc" class="questionIframe" frameborder="0" id="questionIframe"></iframe>
    <bottomFiexd @cancel="onCancel" @save="onSubmit" submitText="保存并关联问卷"></bottomFiexd>
  </div>
</template>

<script>
import bottomFiexd from '@/views/components/botttomFixed.vue'
export default {
  components: {
    bottomFiexd
  },
  data () {
    return {
      questionSrc: 'https://wj-learn.woa.com/edit/v2.html?org=60000000002&scene=1',
      // questionSrc: 'https://test-portal-learn.woa.com/user/testOnly',
      questionType: 'add', // add or edit
      sid: '',
      questionData: ''
    }
  },
  mounted () {
    this.questionType = this.$route.query.type
    if (this.questionType === 'edit') {
      this.sid = this.$route.query.sid
      this.questionSrc = `https://wj-learn.woa.com/edit/v2.html?org=60000000002&scene=1&sid=${this.sid}`
    }
    window.addEventListener('message', (e) => {
      const { data } = e
      if (data.events === 'getData' && data.vendor === 'questionnaire') {
        this.saveInfo(data.data)
      }
      if (data.events === 'error' && data.vendor === 'questionnaire') {
        this.$message.error('问卷数据获取失败，请确认当前是否在问卷页内！')
      }
    })
  },
  computed: {
    questionIframeDom () {
      return document.getElementById('questionIframe')
    }
  },
  methods: {
    onCancel() {
      setTimeout(() => {
        window.close()
      }, 200)
    },
    saveInfo(info) {
      console.log(info, '保存时的info')
      let cutmonId = this.$route.query.cutmonId || ''
      const { title = '', id = '', respondent_url = '' } = info

      let questionData = {
        name: title, // 问卷的名称 到时候由问卷返回
        url: respondent_url, // 到时候由问卷返回
        url_mobile: respondent_url, // 到时候由问卷返回
        act_id: id // 到时候由问卷返回(可能)
      }
      let moocTaskInfo = {
        act_type: '32',
        questionType: this.questionType,
        required: false,
        tx_quest_id: id,
        id: cutmonId, // 前端自己生成的任务id
        ...questionData
      }
      if (this.questionType === 'edit') {
        setTimeout(() => {
          window.opener.questionAggregate(moocTaskInfo)
          this.onCancel()
        }, 100)
        return
      }
      setTimeout(() => {
        window.opener.questionAggregate(moocTaskInfo)
        this.onCancel()
      }, 100)
    },
    onSubmit() {
      this.questionIframeDom.contentWindow.postMessage({
        type: 'questionnaire',
        events: 'getData'
      }, '*')
    }
  }
}
</script>

<style lang="less" scoped>
.questionPage {
  height: 100%;
  width: 100%;
  .questionIframe {
    height: calc(100% - 70px);
    width: 100%;
  }
}
</style>
