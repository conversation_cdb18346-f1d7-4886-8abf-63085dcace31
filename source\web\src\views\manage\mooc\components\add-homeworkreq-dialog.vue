<template>
  <div class="hwrequire-list-dialog">
    <el-dialog
      width="864px"
      :visible="visible"
      class="require-dia"
      title="选择定制作业要求"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <div class="req-list-body">
        <el-table
          ref="requireTable"
          :data="tableData.records"
          width="800px"
          highlight-current-row
          max-height="355px"
          class="req-list-table"
        >
          <!-- 单选 @sort-change="sortChange" -->
          <el-table-column class="select-radio-col" width="50"> 
            <template slot-scope="scope">
              <el-radio class="select-radio" @change="handleRowChange(scope.row)" v-model="tableRadio" :label="scope.row.id">&nbsp;</el-radio>
            </template>
          </el-table-column>
          <el-table-column prop="jobRequirementName" label="定制作业要求名称" width="180" show-overflow-tooltip>
            <template v-slot="prop">
              <!-- <a class="jobRequirement-name" target="_blank" :href="prop.row.content_url">{{ prop.row.jobRequirementName }}</a>  -->
              <span class="jobRequirement-name">{{ prop.row.jobRequirementName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="roleName" label="负责角色名称" show-overflow-tooltip>
            <template v-slot="prop">
              <span>{{ prop.row.roleName }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="roleType" label="负责角色类型" show-overflow-tooltip>
            <template v-slot="prop">
              <span>{{ roleTypeName(prop.row.roleType)}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="associatedJobNumber" label="关联作业任务数量" width="140">
            <template v-slot="prop">
              <span class="associated-job-number">
                <span>{{ prop.row.associatedJobNumber || 0 }}</span>
                <el-tooltip v-if="prop.row.associatedJobNumber > 0" :disabled="!prop.row.jobNames" class="item" effect="dark"  placement="bottom">
                  <div slot="content">
                    <p class="tooltip-item" v-for="(jobname, ind) in prop.row.jobNames" :key="ind">
                      <span>关联作业{{ ind + 1 }}：</span>
                      <span>{{ jobname }}</span>
                    </p>
                  </div>
                  <img class="list-ask-img" src="@/assets/img/work/ask-icon.png" />
                </el-tooltip>
              </span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="" label="创建人"></el-table-column> -->
          <el-table-column prop="filledTime" label="填写时间" width="190">
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="tableData.total"
          class="require-pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="current"
          :page-sizes="[5, 10, 20, 30, 50, 100]"
          :page-size="size"
          layout="total,  prev, pager, next, sizes, jumper"
          :total="tableData.total"
          >
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="btn req-cancel-btn" size="small" @click="cancel">取 消</el-button>
        <el-button class="btn req-confirm-btn" size='small' @click="submit" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import pager from '@/mixins/pager'
import { queryHomeworkRequirements } from '@/config/mooc.api.conf.js'
export default {
  mixins: [pager],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentNode: {
      type: Object
    },
    // entryType: { // 更换课程--添加课程区分
    //   type: String
    // },
    semesterId: {
      type: [String, Number]
    }
  },
  data() {
    return {
      tableRadio: '',
      tableData: {
        records: [],
        total: 0
      },
      size: 5,
      roleTypeList: [
        { label: '全部', value: '' },
        { label: '班级', value: 'Class' },
        { label: '小组', value: 'Group' },
        { label: '个人', value: 'Personal' }
      ], // 角色类型
      selectionRequireItem: null
    }
  },
  computed: {},
  created() {
    this.onSearch(1)
  },
  methods: {
    // 作业要求已选择的反选
    setRadioId(id) {
      this.tableRadio = (id && id * 1) || ''
    },
    // 角色类型
    roleTypeName(roleType) {
      if (!roleType) return '-'
      let roleItem = this.roleTypeList.find(role => role.value === roleType)
      return (roleItem && roleItem.label) || '-'
    },
    // 查询定制作业要求可选列表
    onSearch(page_no = 1) {
      let params = {
        current: page_no,
        size: this.size,
        semesterId: this.semesterId
      }
      queryHomeworkRequirements(params).then(res => {
        // console.log(res, '查询定制作业要求可选列表返回的res--------')
        if (res) {
          res.records = (res.records && res.records.map(item => {
            const filledTime = (item.startTime && item.endTime) ? item.startTime + ' ~ ' + item.endTime : '-'
            return {
              ...item,
              filledTime: filledTime
            }
          })) || []
          this.tableData = res
          this.tableData.total = res.total
        }
      })
    },
    // 单选
    handleRowChange(val) {
      this.selectionRequireItem = val
    },
    submit() {
      if (!this.selectionRequireItem) {
        this.$message.error('请选择一个定制作业要求')
        return
      }
      this.$emit('update:visible', false)
      this.$emit('changeSingleData', this.selectionRequireItem)
    },
    handleReset() {
      this.current = 1
      this.size = 5
      this.onSearch(this.current)
    },
    cancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="less" scoped>
.hwrequire-list-dialog {
  /deep/ .require-dia.el-dialog__wrapper {
    .el-dialog__header {
      padding: 24px 32px 16px;
      border-bottom: 0;
      color: #000000e6;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
    }
    .el-dialog__headerbtn {
      top: 24px;
      right: 32px;
    }
    .el-dialog__body {
      padding: 0 32px 24px;
    }
    .el-dialog__footer {
      padding: 0 32px 24px;
    }
  }
  .req-list-body {
    /deep/ .el-table.req-list-table {
      border: 1px solid #EEE;
      border-bottom: 0;
      .el-table__header {
        height: 50px;
        border-radius: 4px;
        background: #F4F8FF;
        tr, th, .cell {
          background: #F4F8FF;
          color: #00000099;
          font-family: "PingFang SC";
          font-size: 14px;
          font-weight: 500;
        }
      }
      .cell {
        .associated-job-number {
          display: flex;
          align-items: center;
        }
        &.el-tooltip {
          height: 23px;
          line-height: 23px;
        }
      }
      .el-radio.select-radio {
        .el-radio__input, .el-radio__inner {
          width: 16px;
          height: 16px;
        }
      }
    }
    /deep/ .require-pagination.el-pagination {
      margin-top: 16px;
      text-align: right;
    }
  }
  .list-ask-img {
    width: 16px;
    height: 16px;
    margin-left: 8px;
  }
  .jobRequirement-name {
    display: inline-block;
    max-width: 180px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    color:#0052d9;
    cursor: pointer;
  }
  .dialog-footer{
    .btn {
      height: 32px;
      line-height: 32px;
      border-radius: 3px;
      padding: 0 16px;
      text-align: center;
      font-size: 14px;
      border: 0;
    }
    .req-confirm-btn {
      background: #0052D9;
      margin-left: 12px;
      color: #ffffffe6;
    }
    .req-cancel-btn {
      background: #F3F5F7;
      color: #000000e6;
    }
  }
  /deep/ .require-pagination.el-pagination {
    .el-pager {
      .number {
        line-height: 22px;
      }
    }
  }
}
</style>
