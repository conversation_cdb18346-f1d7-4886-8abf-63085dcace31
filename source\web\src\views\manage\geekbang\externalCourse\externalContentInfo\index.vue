<template>
  <div class="data-count">
    <div class="header-top">
      <span class="title">外部内容信息</span>
    </div>

    <!-- 预览 -->
    <viewContent v-if="!isShowEdit"></viewContent>
  </div>
</template>

<script>
import viewContent from './viewContent.vue'

export default {
  components: {
    viewContent
  },
  data () {
    return {
    }
  },
  mounted() {
  },
  methods: {
    
  }
}
</script>

<style lang="less" scoped>
  .data-count {
    height: 100%;
    background: #fff;
    .header-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid rgba(243, 243, 243, 1);
      padding: 16px 20px;
      .title {
        font-size: 16px;
        height: 32px;
        line-height: 32px;
        font-weight: bold;
        color: rgba(0, 0, 0, 0.9)
      }
    }
  }
</style>
