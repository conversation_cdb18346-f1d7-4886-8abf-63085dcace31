<template>
    <div class="notSupported">
        <p class="tips">当前内容暂不支持移动端访问学习<br/>请复制链接，至PC端访问</p>
        <div class="title">
            标题：{{course_title || 'tes标题内容霜环刀法标题内容霜环刀法标题内容霜环刀法标题内容霜环刀法标题内容霜环刀法'}}
        </div>
        <div class="copyLink">
            <el-input placeholder="" readonly v-model="src">
                <template slot="append">
                    <span @click="doCopy">复制</span>
                </template>
            </el-input>
        </div>
    </div>
</template>
  
<script>
export default {
  props: {
    courseInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      src: 'sfdsf'
    }
  },
  created() {
    // this.src = ``
  },
  computed: {
    course_title() {
      return this.$route.query.title
    }
  },
  methods: {
    doCopy() {
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      input.value = this.src
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      this.$message.success(
        this.$langue('Mooc_Common_Alert_CopySucessed', {
          defaultText: '复制成功'
        })
      )
      // 复制后移除输入框
      input.remove()
    }
  }
}
</script>
<style lang="less" scoped>
.notSupported{
    // width: 1158px;
    height: calc(100%);
    background: #fff;
    overflow: hidden;
    font-family: "PingFang SC";
    .tips{
        width: 210px;
        margin: 88px auto 0;
        color: #00000099;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
    }
    .title{
        margin: 24px 24px 20px;
        color: #00000099;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
    }
    .copyLink{
        margin: 0 24px;
        /deep/.el-input__inner{
            font-family: "PingFang SC";
            color: #00000066;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            background: #fff;
            border-color: #dcdcdc;
            &:focus{
                border-color: #dcdcdc;
            }
        }
        /deep/.el-input-group__append {
            font-family: "PingFang SC";
            background: #fff;
            border-color: #dcdcdc;
            color: #000000e6;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            cursor: pointer;
        }
    }
}
</style>
