<template>
    <div class="labelCardItem">
        <div class="courseImage">
            <defaultImage :info="info"/>
            <span class="duration">{{info.origin_data.est_dur||0}}分钟</span>
        </div>
        <div  class="courseInfo">
            <moduleTitle :info="info"/>
            <div class="courseView flex ">
                <div class="flex align-center">
                    <img :src="require('@/assets/img/label/eyes2x.png')">
                    <span>{{info.view_count || 0}}</span>
                </div>
                <!-- <div class="flex align-center">
                    <template v-if="info.origin_data.avg_score && info.origin_data.avg_score!=='0' && info.origin_data.avg_score!=='0.0'">
                        <img :src="require('@/assets/img/label/flower2x.png')">
                        <span class="score">{{(Math.round(info.origin_data.avg_score*10)/10).toFixed(1) }}</span>
                    </template>
                    <span v-else>暂无评分</span>
                </div> -->
                <div class="flex align-center" v-if="info.origin_data?.sort_time">
                    <img :src="require('@/assets/img/label/time2x.png')">
                    <span>{{info.origin_data?.sort_time?.split(' ')[0]}}</span>
                </div>
            </div>
            <relatedContent :info="info" :curModuleId="curModuleId" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :subsType="subsType" :contentType="contentType"></relatedContent>
        </div>
    </div>
</template>
<script>
import defaultImage from './child/defaultImage.vue'
import moduleTitle from './child/moduleTitle.vue'
import relatedContent from './child/relatedContent.vue'
export default {
  name: 'netCourseItem',
  props: ['info', 'curModuleId', 'curLabelId', 'sortSubsIds', 'subsType', 'contentType'],
  data() {
    return {}
  },
  components: {
    defaultImage,
    moduleTitle,
    relatedContent
  },
  computed: {}
}
</script>
<style lang="less" scoped>
@import '../style/item.less';
</style>
