<!-- 表格分页数据混入 -->
<script>
export default {
  name: 'pager',
  data() {
    return {
      tableData: {
        records: [],
        total: 0
      }, // 主表格数据
      current: 1, // 当前页
      size: 10, // 单页数量
      selection: [] // 已勾选的数据
    }
  },
  methods: {
    // 勾选改变
    selectionChange(selection) {
      this.selection = selection
    },
    // 单页数量改变
    handleSizeChange(size) {
      this.size = size
      this.onSearch(this.current)
    },
    // 页数改变
    handleCurrentChange(current) {
      this.current = current
      this.onSearch(this.current)
    }
  }
}
</script>
