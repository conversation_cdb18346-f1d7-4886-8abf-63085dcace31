<template>
  <div class="face-page">
    <div :class="['contain-main']">
      <div class="left">
        <div class="left-top">
          <div class="left-top-flex">
            <div class="left-top-flex-left">
              <div class="top-header">
                <div class="top-header-l">
                  <el-image lazy fit="fill" :src="courseData.photo_url ? courseData.photo_url : require('@/assets/img/activity-default.png')" class="item-image">
                    <div slot="error" class="image-slot">
                      <i class="default-icon-picture"></i>
                    </div>
                  </el-image>
                  <span class="top-header-l-tips">课程</span>
                </div>
                <div class="top-header-r">
                  <el-tooltip class="item" effect="dark" :content="courseData.course_name" placement="top">
                    <div class="face-title">{{courseData.course_name}}</div>
                  </el-tooltip>
                  <div class="desc-tag-box">
                    <div class="tag-left">
                      <div class="tag-content">
                        <div class="tag-list-box">
                          <!-- 标签显示 -->
                          <template v-if="courseData.course_id">
                            <sdc-label-show ref="labelShow" :labelNodeEnv="labelNodeEnv" :actType="1" :courseId="course_id" :showBurialPoint="true" :courseInfo="courseInfo('')">
                            </sdc-label-show>
                          </template>
                        </div>
                      </div>
                    </div>
                    <!-- 用户打标签 -->
                    <!-- <div class="tag-right">
                    <i class="icon-addlabel"></i>
                    <template v-if="!specialUsers">
                      <sdc-labeling v-model="courseData.labels" class="project-tag-box" :recommend="{
                        title: courseData.course_name,
                        desc: courseData.course_desc
                      }" :course_name="courseData.course_name" :course_id="courseData.course_id" :course_type="2" :labelNodeEnv="labelNodeEnv" @input="input" :courseInfo="courseInfo('btn')" />
                    </template>
                  </div> -->
                  </div>
                  <div class="top-info">
                    <span class="top-iconInfo">
                      <i class="icon-view"></i>
                      <span>{{ courseData.view_count || 0 }} {{ $langue('NetCourse_Views', { defaultText: '次观看' }) }}</span>
                      <i class="icon-scores"></i>
                      <span> {{courseData.avg_score || 0}}分</span>
                      <span class="time">{{ courseData.created_at || $langue('NetCourse_CreateTime', { defaultText: '创建时间' }) + ':--' }}</span>
                    </span>
                  </div>
                </div>
              </div>
              <!-- 点赞收藏分享 -->
              <div class="user-operat-box">
                <div class="operat-left">
                  <el-tooltip class="item" effect="dark" content="取消开班提醒" placement="top" :disabled="courseData.is_subscribed === 0">
                    <div :class="['item-operat', 'shore-operat', {'active-isCollect-operat': courseData.is_subscribed === 1 }]" @click="handlerSetSubscribe" :dt-areaid="dtCommon('areaid',  courseData.is_subscribed === 1 ? '取消开班提醒' : '开班提醒')" :dt-eid="dtCommon('eid', courseData.is_subscribed === 1 ? '取消开班提醒' : '开班提醒')" :dt-remark="dtCommon('remark', courseData.is_subscribed === 1 ? '取消开班提醒' : '开班提醒')">
                      <i class="el-icon-bell"></i>
                      <span>{{courseData.is_subscribed === 1 ? '已订阅开班提醒' : '开班提醒' }}</span>
                    </div>
                  </el-tooltip>
                  <div :class="['item-operat', {'add-operat':isAddCourse}]" @click="addCourseDialogShow = true" :dt-areaid="dtCommon('areaid', '添加到课单')" :dt-eid="dtCommon('eid', '添加到课单')" :dt-remark="dtCommon('remark', '添加到课单')">
                    <i class="icon-add"></i>
                    <span>{{ $langue('Article_AddCourseList', { defaultText: '添加到课单' }) }}</span>
                  </div>
                  <div :class="['item-operat', {'active-isCollect-operat': zanAndcollect.isCollect}]" @click="handleLikeOrFav(2)" :dt-areaid="dtCommon('areaid',  zanAndcollect.isCollect ? '取消收藏' : '已收藏')" :dt-eid="dtCommon('eid', zanAndcollect.isCollect ? '取消收藏' : '已收藏')" :dt-remark="dtCommon('remark', zanAndcollect.isCollect ? '取消收藏' : '已收藏')">
                    <i class="icon-collect"></i>
                    {{ zanAndcollect.isCollect ? $langue('Mooc_ProjectDetail_BasicInfo_collected', { defaultText: '已收藏'}) : $langue('Mooc_ProjectDetail_BasicInfo_collect', { defaultText: '收藏'}) }}
                  </div>
                  <div class="item-operat white-note-btn" @click="createNote" :dt-areaid="dtCommon('areaid', '写笔记')" :dt-eid="dtCommon('eid', '写笔记')" :dt-remark="dtCommon('remark', '写笔记')">
                    <i class="white-icon"></i>
                    <span>{{ $langue('NetCourse_Notes', { defaultText: '写笔记' }) }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="left-top-flex-right">
              <div class="operat-right">
                <img class="operat-shore-code" :src="qrUrl" alt="">
                <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 扫码即可分享至微信<br /> 或<span class="shore-operat-link" @click="handleShow()" :dt-areaid="dtCommon('areaid', '分享')" :dt-eid="dtCommon('eid', '分享')" :dt-remark="dtCommon('remark', '分享')">点击复制链接进行分享</span></p>
              </div>
            </div>
          </div>
        </div>
        <div class="tabs-content">
          <el-tabs v-model="tabActiveName">
            <el-tab-pane :label="$langue(tabItem.label, { defaultText: tabItem.text })" :name="tabItem.name" v-for="(tabItem, index) in tabList" :key="index">
              <span slot="label" :dt-areaid="dtCommon('areaid', tabItem.text)" :dt-eid="dtCommon('eid', tabItem.text)" :dt-remark="dtCommon('remark', tabItem.text)">
                {{ tabItem.name === 'class' ? tabItem.text :
                   $langue(tabItem.label, { defaultText: tabItem.text })
                }}
              </span>
            </el-tab-pane>
          </el-tabs>
          <div class="face-city" v-if="tabActiveName === 'class'">
            <div class="face-city-cur"><i class="icon-Local"></i> {{currentCity}} <i style="margin-left:4px;" class="el-icon-arrow-down" :class="{'down-edg':cityDown}"></i></div>
            <el-select popper-class="popper-city" class="face-city-select" @change="handlerChangeCity" v-model="currentCity" placeholder="请选择" @visible-change="handlerVisible">
              <el-option label="全部城市" value="全部城市">
                <span style="float: left" :dt-areaid="dtCommon('areaid', '全部城市')" :dt-eid="dtCommon('eid', '全部城市')" :dt-remark="dtCommon('remark', '全部城市')">全部城市</span>
              </el-option>
              <el-option v-for="item in cityArr" :key="item.city" :label="item.city" :value="item.city">
                <span style="float: left" :dt-areaid="dtCommon('areaid', item.city)" :dt-eid="dtCommon('eid', item.city)" :dt-remark="dtCommon('remark', item.city)">{{ item.city }}</span>
              </el-option>
            </el-select>
          </div>
          <!-- 开班列表 -->
          <classList v-show="tabActiveName === 'class'" :courseData="courseData" :currentCity="currentCity" :actClassListArr="actClassListArr" :registered="registered" />
          <netDesc v-if="tabActiveName === 'desc'" dtPageType="面授课详情页-新版" :courseData="courseData" @handleCourseDetail="getCourseDetail" :specialUsers="specialUsers" :isPreview="false" courseType="face" />
          <!-- 笔记 -->
          <netNotes v-if="tabActiveName === 'notes'" :act_type="1" dtPageType="面授课详情页-新版" :courseData="courseData"></netNotes>
          <!-- 讨论 -->
          <netComment v-if="tabActiveName === 'comment'" dtPageType="面授课详情页-新版" :courseData="courseData" />
        </div>
        <!-- 包含此内容 -->
        <div class="includes-content">
          <incudesContent :module_id="2" :courseData="courseData" dtPageType="面授课详情页-新版" />
        </div>
      </div>
      <div class="right">
        <!-- 右侧 -->
        <rightArea v-if="!isFormMooc && isShowRight " ref="rightSideRef" :courseData="courseData" @noRightCard="noRightCard"></rightArea>
        <!-- 右侧图标模块 -->
        <rightSideIcon :modelTabValue="modelTabValue"></rightSideIcon>
      </div>
    </div>
    <AddCourseDialog :visible.sync="addCourseDialogShow" :itemData.sync="addCourseDialogData" @addedHandle="isAddCourse=true" />
    <ShareDialog ref="shareDialog" :isShow.sync="sharedialogShow" />
  </div>
</template>

<script>
import { AddCourseDialog } from '@/components/index'
import ShareDialog from '@/views/components/shareDialog'
import netComment from '../netcourse/grayPlay/components/netcomment'
import netNotes from '../netcourse/grayPlay/components/netNotes'
import netDesc from '../netcourse/grayPlay/components/netDesc'
import incudesContent from '../netcourse/grayPlay/components/incudesContent'
import classList from './components/classList.vue'
import rightArea from './components/rightArea'
import rightSideIcon from '../netcourse/grayPlay/components/rightSideIcon'
import MoocJs from 'sdc-moocjs-integrator'
import {
  getFaceDetails,
  faceCheckFavorited,
  actClassList,
  faceDeleteFavorite,
  faceAddFavorite,
  setSubscribe
} from 'config/api.conf'
import { getlabelSpecialUsers, getMobileQrcode } from 'config/mooc.api.conf.js'
import { mapState } from 'vuex'
export default {
  name: 'face',
  components: {
    AddCourseDialog,
    ShareDialog,
    netComment,
    netNotes,
    netDesc,
    classList,
    incudesContent,
    rightArea,
    rightSideIcon
  },
  data() {
    return {
      labelNodeEnv:
        process.env.NODE_ENV === 'production' ? 'production' : 'test',
      courseData: {},
      classDateSelect: [],
      actClassListArr: [],
      cityArr: [],
      currentCity: '全部城市',
      isShowRight: true,
      cityDown: false,
      tabActiveName: 'class',
      specialUsers: false,
      registered: false,
      zanAndcollect: {
        isZan: false,
        isCollect: false
      },
      isAddCourse: false,
      addCourseDialogShow: false,
      sharedialogShow: false,
      addCourseDialogData: {
        module_id: 2,
        module_name: '面授课'
      },
      modelTabValue: 'video_model',
      qrUrl: '',
      tabList: [
        {
          label: 'Mooc_ProjectDetail_Notice_Introduce',
          name: 'class',
          text: '开班列表'
        },
        {
          label: 'Mooc_ProjectDetail_Notice_Introduce',
          name: 'desc',
          text: '介绍'
        },
        { label: 'NetCourse_Note', name: 'notes', text: '笔记' },
        {
          label: 'Mooc_ProjectDetail_Notice_Comments',
          name: 'comment',
          text: '讨论'
        }
      ]
    }
  },
  mounted() {
    this.$store.dispatch('getIsBusy')
    this.getCourseDetail()
    this.codeTopHead()
  },
  computed: {
    ...mapState({
      showPicture: (state) => state.net.showPicture,
      volume: (state) => state.net.volume,
      playbackRate: (state) => state.net.playbackRate,
      playStatus: (state) => state.net.playStatus,
      userInfo: (state) => state.userInfo
    }),
    course_id() {
      return this.$route.query.course_id
        ? parseInt(this.$route.query.course_id)
        : ''
    },
    isFormMooc() {
      return this.$route.query.from === 'mooc'
    },
    // label-show组件所需要的埋点数据
    courseInfo() {
      return (type) => {
        let { course_id } = this.$route.query
        const click_type = type === 'btn' ? 'button' : 'data'
        const container = type === 'btn' ? `介绍-打标签` : `介绍-标签`
        return {
          mooc_course_id: course_id,
          page: this.courseData.course_name, // 任务名称
          page_type: '面授课详情-新版',
          container, // 板块的名称
          click_type,
          terminal: 'PC'
        }
      }
    },
    dtCommon() {
      return (type, val) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页-新版',
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: val,
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${val}`
        } else {
          return `area_${this.course_id}_${val}`
        }
      }
    }
  },
  methods: {
    initData() {
      this.loadComment = true
      // 任务已完成时，开启任务同步弹窗
      if (this.courseData.is_finish * 1 === 1 && this.isFormMooc) {
        MoocJs.complete('init')
      }
      this.getZanAndCollectStatus()
      this.actClassList()
    },
    // 二维码
    codeTopHead() {
      const params = {
        scene: `${this.course_id}`,
        page: 'pages/face/index',
        env_version: process.env.NODE_ENV === 'production' ? 'release' : 'trial'
      }
      getMobileQrcode(params).then((res) => {
        this.qrUrl = `data:image/png;base64,${res}`
      })
    },
    // 开班提醒 1 订阅，0退订
    handlerSetSubscribe() {
      const { is_subscribed } = this.courseData
      let params = {
        course_id: this.course_id,
        is_subscribed: Number(is_subscribed) === 1 ? 0 : 1
      }
      setSubscribe(params).then((res) => {
        this.$message.success(
          Number(is_subscribed) === 1 ? '已退订开班提醒' : '成功订阅开班提醒'
        )
        this.getCourseDetail()
      })
    },
    handleLikeOrFav() {
      const params = { course_id: this.course_id }
      faceCheckFavorited(params).then((res) => {
        const PAndFCommonAPI = res ? faceDeleteFavorite : faceAddFavorite
        let tipsCancel = this.$langue(
          'Mooc_Common_Alert_CancelCollectSucessed',
          { defaultText: '取消收藏成功' }
        )
        let tipsSucess = this.$langue('Mooc_Common_Alert_CollectSucessed', {
          defaultText: '收藏成功'
        })
        const tip = res ? tipsCancel : tipsSucess
        this.courseData.fav_count = res
          ? this.courseData.fav_count === null ||
            this.courseData.fav_count === 0
            ? 0
            : this.courseData.fav_count - 1
          : this.courseData.fav_count + 1
        this.handlerCommonInt(PAndFCommonAPI, params, tip)
      })
    },
    handlerCommonInt(PAndFCommonAPI, params, tip) {
      PAndFCommonAPI(params).then((data) => {
        this.zanAndcollect.isCollect =
          PAndFCommonAPI === faceAddFavorite ? Boolean(true) : Boolean(false)
        this.$message.success(tip)
      })
    },
    // 开班列表
    async actClassList(city) {
      let params = {
        course_id: this.course_id
      }
      // start_time: '',
      // city: ''
      let data = await actClassList(params)
      data.forEach((item) => {
        if (item.teaching_type !== 1) {
          item.city = '在线授课'
        }
      })
      let cityArr = JSON.parse(JSON.stringify(data))
      this.cityArr = this.removDuplication(cityArr, 'city')
      console.log(this.courseData.sub_class_count, 'sub_class_count')
      // this.registered = data.some((item) =>
      //   [3, 5, 6, 7].includes(item.regist_type.status)
      // )
      const { sub_class_count } = this.courseData
      console.log(sub_class_count)
      if (sub_class_count && Number(sub_class_count) > 1) {
        let numReg = data.filter((item) =>
          [3, 5, 6, 7].includes(item.regist_type.status)
        )
        console.log(numReg, 'numRegnumRegnumReg')
        this.registered = numReg.length >= Number(sub_class_count)
      } else {
        this.registered = data.some((item) =>
          [3, 5, 6, 7].includes(item.regist_type.status)
        )
      }
      if (!data.length) {
        this.tabActiveName = 'desc'
      }
      this.actClassListArr = data
      this.actClassListArr.forEach((item) => {
        item.formatDate = this.formatDateTime(item.start_time)
        item.start_end_time = this.formatDateTimeStartEnd(
          item.start_time,
          item.end_time
        )
      })
      // 筛选城市
      if (city) {
        this.actClassListArr = this.actClassListArr.filter(
          (item) => item.city === city
        )
      }
      // 处理时间赛选
      // this.classDateSelect = this.removDuplication(this.actClassListArr, 'city')
      console.log('开班列表', data)
    },
    formatDateTime(date) {
      const dateTime = new Date(date)
      const formattedDate = this.$moment(dateTime).format('YYYY/MM/DD')
      const getDay = dateTime.getDay()
      const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'] // 获取星期信息
      return `${formattedDate}（${weekday[getDay]}）` // 组合日期和星期信息并返回
    },
    // 去重
    removDuplication(arr, type) {
      let resArr = arr.reduce((result, obj) => {
        if (
          !result.some(
            (item) => JSON.stringify(item[type]) === JSON.stringify(obj[type])
          )
        ) {
          obj[type] === '在线授课' ? result.unshift(obj) : result.push(obj)
        }
        return result
      }, [])
      return resArr
    },
    formatDateTimeStartEnd(start, end) {
      const dateTimeStart = new Date(start)
      const dateTimeEnd = new Date(end)
      const formattedDateStart =
        this.$moment(dateTimeStart).format('YYYY/MM/DD')
      const formattedDateEnd = this.$moment(dateTimeEnd).format('YYYY/MM/DD')
      const ltStart = this.$moment(dateTimeStart).format('HH:mm')
      const ltEnd = this.$moment(dateTimeEnd).format('HH:mm')
      const weekArr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekdayStart = weekArr[dateTimeStart.getDay()] // 获取星期信息
      const weekdayEnd = weekArr[dateTimeEnd.getDay()] // 获取星期信息
      const startDateJoin = `${formattedDateStart}(${weekdayStart}) ${ltStart}`
      const endDateJoin = `${formattedDateEnd}(${weekdayEnd}) ${ltEnd}`
      return `${startDateJoin} - ${endDateJoin}` // 组合日期和星期信息并返回
    },
    getZanAndCollectStatus() {
      const params = { course_id: this.course_id }
      faceCheckFavorited(params).then((res) => {
        this.zanAndcollect.isCollect = res
      })
    },
    async getCourseDetail() {
      try {
        const { share_staff_id, share_staff_name } = this.$route.query
        let params = {
          course_id: this.course_id,
          share_staff_id: share_staff_id || '',
          share_staff_name: share_staff_name || ''
        }
        const data = await getFaceDetails(params)
        console.log(data, 'res')
        if (data.status === 3) {
          sessionStorage.setItem(
            '401Msg',
            `${this.$langue('NetCourse_CourseOffLine', {
              defaultText: '该课程已下架'
            })}！`
          )
          this.$router.replace({
            name: '401'
          })
          return
        }
        document.title = `${data.course_name}_Q-Learning`
        this.courseData = data
        this.courseData.sub_class_count = data.ext_info.sub_class_count
        this.courseData.photo_url = data.ext_info.photo_url
        this.courseData.course_desc = data.ext_info.course_desc
        const { course_name, photo_url, course_desc, course_id, content_id } =
          this.courseData
        const net_url = location.hostname.endsWith('.woa.com')
          ? `${process.env.VUE_APP_PORTAL_HOST_WOA}/training/face?course_id=${course_id}`
          : `${process.env.VUE_APP_PORTAL_HOST}/training/face?course_id=${course_id}`
        this.addCourseDialogData = {
          ...this.addCourseDialogData,
          content_name: course_name,
          cover_img_url: photo_url,
          description: course_desc,
          href: net_url,
          item_id: course_id,
          origin: location.origin
        }
        this.courseData.content_id = content_id
        this.initData()
      } catch (err) {
        if (err.code === 403 || err.code === 500) {
          sessionStorage.setItem('401Msg', err.message)
          this.$router.replace({
            name: '401'
          })
          if (this.isFormMooc) {
            MoocJs.sendErrorInfo(err.message)
          }
        }
      }
    },
    // 获取用户是不是特殊人员
    getlabelSpecialInfo() {
      getlabelSpecialUsers().then((res) => {
        this.specialUsers = res
      })
    },
    input(val) {
      this.courseData.labels = val
      setTimeout(() => {
        this.getCourseDetail()
        this.$refs.labelShow && this.$refs.labelShow.getLabelList()
      }, 500)
    },
    // 创建笔记
    createNote() {
      const { href } = this.$router.resolve({
        name: 'create',
        query: {
          from: 'ql',
          id: this.$route.query.course_id,
          name: this.courseData.course_name,
          type: 3,
          module_id: 2
        }
      })
      window.open(href)
    },
    handleShow() {
      this.sharedialogShow = true
      this.$nextTick(() => {
        let url = 'https://sdc.qq.com/s/Eom5bg?scheme_type=faceClassDetail&course_id=' + this.course_id
        this.$refs.shareDialog.initCode({
          url,
          scene: `${this.course_id}`,
          customText: `【${this.courseData.course_name}】\n\n${url}`,
          page: 'pages/face/index'
        })
      })
    },
    noRightCard(val) {
      this.isShowRight = val
    },
    handlerVisible(val) {
      this.cityDown = val
    },
    handlerChangeCity(val) {
      // 如果不是全部城市，传某个城市的名字
      this.actClassList(val.includes('全部') ? '' : val)
    }
  }
}
</script>

<style lang="less" scoped>
.face-page {
  :deep(.sdc-editor-preview) {
    width: 100%;

    .content-wrapper {
      width: 100%;
    }

    .file-count {
      padding: 0 0 8px 24px;
    }

    .editor-file-list {
      margin: 0 24px 36px 24px;
    }

    .desc,
    .editor-content {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      word-break: break-word;

      ol,
      ul {
        padding: revert;
      }
    }

    .desc {
      margin: 0 24px 0 24px;
    }

    // .editor-content {
    //   padding: 20px 24px 32px 24px;
    // }
  }

  .contain-main {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    padding-top: 10px;

    .left {
      margin-bottom: 20px;
      .left-top {
        background-color: #fff;
        border-radius: 8px;
        padding: 24px;
        position: relative;
        display: flex;
        .top-header {
          display: flex;
          flex: 1;
          &-l {
            margin-right: 20px;
            position: relative;
            /deep/.el-image {
              width: 135px;
              height: 90px;
              img {
                border-radius: 3px;
              }
            }
            .top-header-l-tips {
              position: absolute;
              left: 4px;
              top: 4px;
              font-size: 12px;
              border-radius: 2px;
              display: inline-block;
              width: 32px;
              height: 18px;
              line-height: 18px;
              text-align: center;
              color: #fff;
              background-color: #0052d9;
            }
          }
          &-r {
            overflow: hidden;
            width: 100%;
            .face-title {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              color: #000000;
              font-size: 18px;
              line-height: 26px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .desc-tag-box {
              display: flex;
              justify-content: space-between;
              margin-bottom: 10px;

              .tag-left,
              .tag-right {
                display: flex;
                align-items: center;
              }

              .tag-left {
                .tag-content {
                  display: flex;
                  line-height: 20px;

                  .label {
                    color: rgba(0, 0, 0, 0.4);
                    width: 50px;
                  }

                  .label-height25 {
                    height: 25px;
                    line-height: 25px;
                  }

                  .tag-list-box {
                    flex: 1;
                    display: flex;
                    flex-wrap: wrap;
                  }
                }
              }

              .tag-right {
                font-size: 12px;
                color: rgba(0, 0, 0, 0.4);
                flex-shrink: 0;
                border-radius: 24px;
                background: #f5f5f7;
                height: 24px;
                line-height: 24px;
                cursor: pointer;
                position: relative;
                width: 88px;
                .project-add-label {
                  position: relative;
                  :deep(.cascader-component) {
                    .el-button {
                      span {
                        width: 88px;
                        font-size: 12px;
                        margin-left: 10px;
                        display: inline-block;
                        color: rgba(0, 0, 0, 0.4) !important;
                      }
                    }
                  }
                }
                .project-tag-box {
                  position: relative;
                  :deep(.cascader-component) {
                    .marker-tag {
                      margin-left: 36px;
                    }
                  }
                }
                i {
                  display: inline-block;
                  width: 16px;
                  height: 16px;
                  position: absolute;
                  left: 16px;
                }

                .icon-addlabel {
                  background: url('~@/assets/img/addlabel.png') no-repeat center /
                    cover;
                }
              }

              .tag-right:hover {
                background: #f2f8ff;
                color: #0052d9;
                :deep(.cascader-component) {
                  .el-button {
                    span {
                      color: #0052d9 !important;
                    }
                  }
                }
                .icon-addlabel {
                  background: url('~@/assets/img/addlabel2.png') no-repeat
                    center / cover;
                }
              }
            }

            .top-info {
              display: flex;
              align-items: center;
              color: #777777;
              // padding-bottom: 16px;
              margin-bottom: 10px;
              // border-bottom: 1px solid #EEEEEE;
              i {
                display: inline-block;
                margin-right: 4px;
                width: 16px;
                height: 16px;
              }
              .top-iconInfo {
                margin-right: 40px;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                height: 20px;
                .icon-view {
                  background: url('~@/assets/img/view.png') no-repeat
                    center/cover;
                }
                .icon-scores {
                  background: url('~@/assets/img/icon-scores.png') no-repeat
                    center/cover;
                  margin-left: 16px;
                }
                .time {
                  margin-left: 16px;
                }
              }
            }
          }
        }
        .operat-right {
          width: 154px;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          justify-content: space-between;
           color: #777777;
          .operat-shore-code {
            width: 100px;
            height: 100px;
          }
          .shore-operat-link {
              text-decoration: underline;
              color: #0052d9;
              cursor: pointer;
            }
        }
        .user-operat-box {
          margin-top: 10px;
          // border-top: 1px solid #EEEEEE;
          // padding-top: 20px;
          color: #777777;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .operat-left {
            display: flex;
          }

          .item-operat {
            background-color: #f5f5f7;
            padding: 0 16px;
            margin-right: 16px;
            border-radius: 24px;
            height: 32px;
            line-height: 32px;
            display: flex;
            align-items: center;
            cursor: pointer;
            position: relative;
            i {
              display: inline-block;
              width: 16px;
              height: 16px;
              line-height: 16px;
              margin-right: 4px;
            }
            .mid-line {
              margin-left: 12px;
              margin-right: 12px;
              color: #d9d9d9;
            }
          }
          .active-isCollect-operat {
            background: #f2f8ff;
            color: #0052d9;
            .icon-collect {
              background: url('~@/assets/img/active-fav.png') no-repeat center /
                cover;
            }
          }
          .white-note-btn {
            flex-shrink: 0;
            margin-right: 0;
            i {
              width: 20px;
              height: 20px;
              background: url('~@/assets/img/white-icon.png') no-repeat center /
                cover;
            }
          }
          .shore-operat:hover {
            background: #f2f8ff;
            color: #0052d9;
            .icon-share {
              background: url('~@/assets/img/qrcode-hover.png') no-repeat center /
                cover;
            }
          }
          .add-operat:hover {
            background: #f2f8ff;
            color: #0052d9;
            .icon-add {
              background: url('~@/assets/img/active-add.png') no-repeat center /
                cover;
            }
          }
          .icon-zan-active {
            background: #f2f8ff;
            color: #0052d9;
            i {
              background: url('~@/assets/img/zan1-active.png') no-repeat center /
                cover;
            }
          }
          .white-note-btn:hover {
            background: #f2f8ff;
            color: #0052d9;
            i {
              background: url('~@/assets/img/white-icon.png') no-repeat center /
                cover;
            }
          }
          .active-appreciate-operat {
            background: #f2f8ff;
            color: #0052d9;
            i {
              background: url('~@/assets/img/do-admire-active.png') no-repeat
                center / cover;
            }
          }
          .icon-comment {
            background: url('~@/assets/img/comment.png') no-repeat center /
              cover;
          }
          .icon-zan {
            background: url('~@/assets/img/zan1.png') no-repeat center / cover;
          }
          .icon-share {
            background: url('~@/assets/img/qrcode.png') no-repeat center / cover;
          }
          .icon-collect {
            background: url('~@/assets/img/fav2.png') no-repeat center / cover;
          }
          .icon-add {
            background: url('~@/assets/img/add.png') no-repeat center / cover;
          }
          .more-btn {
            background: url('~@/assets/img/more-line.png') no-repeat center /
              cover;
          }
          .icon-appreciate {
            background: url('~@/assets/img/icon_re.png') no-repeat center /
              cover;
          }
          .jf-tip {
            color: #ff7548;
            position: absolute;
            right: 20px;
            top: -22px;
            display: flex;
            align-items: center;
          }
          .jf-icon {
            background: url('~@/assets/img/integral-icon.png') no-repeat center /
              cover;
            display: block;
            width: 20px;
            height: 20px;
            margin-right: 4px;
          }
        }
        .left-top-flex {
          width: 100%;
          display: flex;
          justify-content: space-between;
          .left-top-flex-left {
            flex: 1;
          }
          .left-top-flex-right {
            width: 154px;
          }
        }
      }

      .tabs-content {
        margin-top: 20px;
        position: relative;
        :deep(.el-tabs) {
          background-color: #fff;
          padding: 16px 24px 0 24px;
          border-radius: 8px 8px 0 0;

          .el-tabs__header {
            border-bottom: solid 1px #eeeeee;
            margin: 0px;
          }

          .el-tabs__item {
            color: rgba(0, 0, 0, 0.4);
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 10px;
          }

          .is-active {
            color: #0052d9 !important;
            font-weight: 700;
          }
          .el-tabs__nav-wrap::after {
            height: 1px;
            background-color: #eeeeee;
          }
        }
        .face-city:hover {
          background: #fafafa;
        }
        .face-city {
          position: absolute;
          right: 24px;
          top: 10px;
          height: 28px;
          .face-city-cur {
            position: absolute;
            text-align: center;
            height: 28px;
            line-height: 28px;
            width: 108px;
            display: flex;
            align-items: center;
            justify-content: center;
            .icon-Local {
              background: url('~@/assets/img/Local.png') no-repeat center /
                cover;
              display: block;
              width: 16px;
              height: 16px;
              margin-right: 4px;
            }
            .down-edg {
              transform: rotate(180deg);
              transition: all 0.3s;
            }
          }
          .face-city-select {
            opacity: 0;
            /deep/ .el-input__inner {
              width: 108px;
              text-align: right;
              border: none;
            }
          }
          /deep/.popper-city {
            width: auto;
          }
        }
      }
    }
  }
}
@media screen and (max-width: 1660px) {
  .contain-main .left {
    width: 760px;
    .tag-list-box {
      width: 400px;
    }
    .video-box,
    .scorm-box {
      width: 711px;
      height: 401px;
    }
  }
  .article-fullen-main .left {
    width: 1180px;
  }
  .face-title {
    max-width: 380px;
  }

}

@media screen and (min-width: 1661px) {
  .contain-main .left {
    width: 1000px;
    .tag-list-box {
      width: 600px;
    }
    .video-box,
    .scorm-box {
      width: 951px;
      height: 536px;
    }
    .chapter-preview-box {
      width: 952px;
    }
  }
  .article-fullen-main .left {
    width: 1420px;
  }
  .face-title {
    max-width: 620px;
  }
}
</style>
