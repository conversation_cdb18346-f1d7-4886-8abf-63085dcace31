<template>
  <div class="net-page">
    <link
      rel="stylesheet"
      :href="linkHref"
    />
    <div class="contain-main">
      <div class="left">
        <div v-if="materialData.file_type =='Article'" id="graphic-preview" style="position: relative;">
          <sdc-mce-preview
            ref="editor"
            :urlConfig="editorConfig.urlConfig"
            :catalogue.sync="editorConfig.catalogue"
            :content="graphic_text"
            :desc="materialData.file_desc ?? ''"
            :fileInRow="fileInRow"
          >
            <slot>
              <div class="content-title" v-if="!isFormMooc">
                <div class="name">
                  <span class="tag word">{{ $langue('Mooc_Common_ResourceType_Article', { defaultText: '文章' }) }}</span>
                  <span>{{ materialData.file_show_name }}</span>
                </div>
                <div class="info">
                  <span class="time">{{ materialData.created_at }}</span>
                  <el-tooltip
                    effect="dark"
                    placement="bottom"
                  >
                    <div slot="content">
                      <span class="creator_name">{{
                        materialData.creator_name
                      }}</span>
                    </div>
                    <p class="creator_name">
                      <span>{{ materialData.creator_name }}</span>
                    </p>
                  </el-tooltip>
                </div>
              </div>
            </slot>
          </sdc-mce-preview>
          <Watermark
            ref="watermark"
            v-if="watermark.textContent"
            :targetId="watermark.targetId"
            :text="watermark.textContent"
            :canvasUserOptions="watermark.canvasUserOpt"
            :wmUserOptions="watermark.wmUserOpt"
            :isManualInit="false"
          />
        </div>
        <div v-else>
          <!-- v-if="!isFormMooc" -->
          <div class="content-title">
           <div style="display: flex; align-items: center;margin-bottom: 10px;">
            <!-- 文章模式tab切换 只有音视频模式 -->
            <div class="model-tab" v-if="show_graphic && isVideoOrAudioType">
              <span
                :class="['item-tab', {'active-model-tab': modelTabValue === v.value}]"
                v-for="v in modelTab"
                :key="v.label"
                @click="changeModelTab(v)"
                :dt-eid="dtCommon('eid', v.label)"
                :dt-remark="dtCommon('remark', v.label)"
                :dt-areaid="dtCommon('areaid', v.label)"
                >
                {{ $langue(v.tlabel, { defaultText: v.label }) }}
              </span>
            </div>
            <div class="name" v-if="!isFormMooc">
              <span class="tag word">{{ filterResourceName }}</span>
              <span>{{materialData.file_show_name}}</span>
            </div>
           </div>
            <div class="info" v-if="!isFormMooc">
              <span class="time">{{materialData.created_at || '创建时间: --'}}</span>
              <el-tooltip
                effect="dark"
                placement="bottom"
              >
                <div slot="content">
                  <span class="creator_name">{{materialData.creator_name}}</span>
                </div>
                <p class="creator_name">
                  <span>{{materialData.creator_name}}</span>
                </p>
              </el-tooltip>
              <span class="info-right" @click="toggle" v-if="materialData.file_type === 'Scorm' || materialData.file_type === 'Flash'">
                <i class="fullscreen" title="全屏"></i>
                <span>全屏</span>
              </span>
            </div>
          </div>

         <div v-if="isVideoOrAudioType">
            <!-- 音/视频模式 -->
            <template v-if="modelTabValue === 'video_model'">
              <div class="video-main">
                  <Video
                    class="video-box"
                    :content_id.sync="materialData.content_id"
                    @getCurCaption="getCurCaption"
                    @getCurrentTime="getCurrentTime"
                    @handleRecord="handleRecord"
                    :source_src="materialData.file_url"
                    :autoPlay="autoPlay"
                    ref="vidioDOM"
                  />
                  <!-- 点击跳转 -->
                  <div class="current-time-tips" v-if="isCurrentTimeShow && playTime > 0">
                      <i class="el-icon-close" @click="isCurrentTimeShow = false"></i>
                      <span>
                        {{ moocLang === 'zh-cn' ? `上次播放至 ${transforNcTime(playTime)}` :
                            `Restart the playback where you left off at ${transforNcTime(playTime)}`
                        }}
                    </span>
                    <span
                      class="tips-btn"
                      @click="toCurrentTime"
                      :dt-eid="dtCommon('eid', '点击跳转')"
                      :dt-remark="dtCommon('remark', '点击跳转')"
                      :dt-areaid="dtCommon('areaid', '点击跳转')" >{{ $langue('NetCourse_ClickGo', { defaultText: '点击跳转' })}}</span>
                  </div>
              </div>
              <!-- 章节 -->
              <materialChapter
                v-if="chapterSummaryList.length && show_chapter"
                :chapterSummaryList="chapterSummaryList"
                :showChapterTips="Boolean(chapter_from)"
                :studyRecordQuery="studyRecordQuery"
                :materialData="materialData"
                @toChaptersPosition="toChaptersPosition"
              />
            </template>
            <!-- 文章模式 -->
            <articleModel
              v-if="modelTabValue === 'article_model'"
              ref="materialDraftRef"
              :courseData="materialData"
              :captionList="captionData"
              :captionCurTime="captionCurTime"
              :chapterSummaryList.sync="chapterSummaryList"
              :forMatTab="forMatTab"
              @toChaptersPosition="toChaptersPosition"
              />
         </div>
          <Scorm
            v-else-if="['Flash', 'Scorm', 'Doc'].includes(materialData.file_type)"
            class="scorm-box"
            :courseData="materialData"
            @handleScormRecord="handleScormRecord"
            :scormType = "materialData.file_type"
            ref="scormDOM"
          />
          <div
            v-else
            class="video-box"
          >
            <el-image
              lazy
              fit="fill"
              :src="materialData.photo_url ? materialData.photo_url : require('@/assets/img/default_bg_img.png')"
              class="item-image"
            >
              <div
                slot="error"
                class="image-slot"
              >
                <i class="default-icon-picture"></i>
              </div>
            </el-image>
          </div>
          <!-- 简介 内容文稿 -->
          <div class="content-bottom">
            <el-tabs v-model="tabActiveName">
              <el-tab-pane
                :label="$langue(tabItem.label, { defaultText: tabItem.text })"
                :name="tabItem.name"
                v-for="(tabItem) in tabList"
                :key="tabKey(tabItem.name)"
              >
                <span slot="label">
                  {{ tabItem.name === 'caption' ? tabItem.text :
                    $langue(tabItem.label, { defaultText: tabItem.text })
                  }}
                </span>
              </el-tab-pane>
            </el-tabs>
            <!-- 简介 -->
            <div
              v-show="tabActiveName === 'desc'"
              class="desc-box"
            >
              <sdc-mce-preview
                v-if="materialData.file_desc"
                :urlConfig="editorConfig.urlConfig"
                :content="materialData.file_desc??''"
              ></sdc-mce-preview>
              <span
                v-else
                class="not-text"
              >暂无简介</span>
            </div>
            <!-- <div v-if="tabActiveName === 'caption'">
              <div
                v-if="captionData.length"
                class="caption-box"
              >
                <div class="caption-title"><span>时间点</span><span>文本内容</span></div>
                <div class="caption-content">
                  <div
                    v-for="(item,index) in captionData"
                    :key="index"
                    :class="[ captionCurTime >= item.IntStartTime && captionCurTime < item.IntEndTime ? 'caption-item-active':'','caption-item']"
                    :id="item.IntStartTime"
                  >
                    <span>{{item.startTime?.split(',')[0]}}</span>
                    <p>{{item.caption}}</p>
                  </div>
                </div>
              </div>
              <span v-else class="not-text">
                暂无文稿
              </span>
            </div> -->

            <!-- 字幕纪要 -->
            <articleModel
              v-if="tabActiveName === 'caption' && show_caption"
              class="subtitle-summary-main"
              :courseData="materialData"
              :captionList="captionData"
              :captionCurTime="captionCurTime"
              :chapterSummaryList.sync="chapterSummaryList"
              cType="subtitle_summary"
              :forMatTab="forMatTab"
              @toChaptersPosition="toChaptersPosition"
              ref="articleModelRef"
            />
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { Video, Scorm } from '@/components/index'
import { getMateriaDetailUser, getMateriaDetail, getChapterListApi } from 'config/mooc.api.conf'
import { operatesignature, getContentInfo, netViewRecord } from 'config/api.conf'
import MoocJs from 'sdc-moocjs-integrator'
import { pageExposure, transforNcTime } from '@/utils/tools.js'
import Watermark from '@/components/watermark.vue'
import translate from 'mixins/translate.vue'
import { getQueryVariable } from 'utils/tools'
import playImg from '@/assets/img/audioPlay.png'
import materialChapter from './components/materialChapter.vue'
import articleModel from './components/articleModel'
import { mapState } from 'vuex'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]
export default {
  mixins: [translate],
  components: {
    Video,
    Scorm,
    Watermark,
    materialChapter,
    articleModel
  },
  props: {
    isMg: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      transforNcTime,
      linkHref: '',
      editorConfig: {
        catalogue: false,
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/{contentId}`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview`
        }
      },
      materialData: {
        content_type: '',
        comment_count: 0,
        fav_count: 0
      },
      viewTimer: null,
      countTimer: null,
      tabActiveName: 'desc',
      captionData: [],
      captionCurTime: null,
      fileInRow: 4,
      graphic_text: '',
      watermark: {
        targetId: 'graphic-preview', // 水印目标元素id
        textContent: '',
        canvasUserOpt: {
          width: 200,
          height: 160,
          fillStyle: 'rgba(153, 153, 153, 0.1)',
          font: '20px Microsoft Yahei',
          rotateDegree: 39,
          translate: {
            x: 0,
            y: 0
          }
        },
        wmUserOpt: {
          'z-index': 99999
        }
      },
      // tabList: [
      //   { label: 'Mooc_TaskDetail_Audio_Description', name: 'desc', text: '简介' }
      // ],
      autoPlay: false,
      enableInteractive: false,
      modelTabValue: 'video_model',
      playTime: 0,
      isCurrentTimeShow: false,
      chapter_from: 0, // 0-人工配置章节 1-智能生成
      chapterSummaryList: [], // 章节
      studyRecordQuery: { // video学习记录
        act_id: getQueryVariable('course_id'),
        area_id: getQueryVariable('area_id'),
        from: getQueryVariable('jump_from') || getQueryVariable('from') || '',
        learn_record_id: 0,
        is_finish: 0,
        is_review: 0,
        total_study_time: 0,
        my_study_progress: 0,
        course_duration: 0
        // 下面是 scorm 课件时才需要
        // data_model: '',
        // scorm_item_id: 1,
      }
    }
  },
  computed: {
    ...mapState({
      moocLang: (state) => state.moocLang
    }),
    material_id() {
      return this.$route.query.material_id
    },
    isFormMooc() {
      return this.$route.query.from === 'mooc' || this.$route.query.from === 'spoc'
    },
    // tab
    modelTab() {
      const arr = [
        { label: '视频模式', value: 'video_model', tlabel: 'NetCourse_VideoMode' },
        { label: '文章模式', value: 'article_model', tlabel: 'NetCourse_ArticleMode' }
      ]
      if (this.materialData.file_type === 'Audio') {
        // 音频模式tab文字修改
        return [
          { label: '音频模式', value: 'video_model', tlabel: 'NetCourse_AudioMode' },
          { label: '文章模式', value: 'article_model', tlabel: 'NetCourse_ArticleMode' }
        ]
      }
      return arr
    },
    // 是否为音视频模式
    isVideoOrAudioType() {
      return ['Video', 'Video-2d', 'Video-3d', 'Video-ppt', 'Audio'].includes(this.materialData.file_type)
    },
    // 是否显示字幕
    show_caption() {
      return this.materialData?.show_caption || 0
    },
    // 是否显示章节
    show_chapter() {
      return this.materialData?.show_chapter || 0
    },
    // 是否显示文章
    show_graphic() {
      return this.materialData?.show_graphic || 0
    },
    filterResourceName() {
      let { file_type } = this.materialData
      let name = ''
      if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(file_type)) {
        name = this.$langue('Mooc_Common_ResourceType_Video', { defaultText: '视频' })
      } else if (file_type === 'Audio') {
        name = this.$langue('Mooc_Common_ResourceType_Audio', { defaultText: '音频' })
      } else if (file_type === 'Article') {
        name = this.$langue('Mooc_Common_ResourceType_Article', { defaultText: '文章' })
      } else if (file_type === 'Doc') {
        name = this.$langue('Mooc_Common_ResourceType_Doc', { defaultText: '文档' })
      } else if (file_type === 'Scorm') {
        name = 'Scorm'
      } else if (file_type === 'Flash') {
        name = this.$langue('Mooc_Common_ResourceType_Zip', { defaultText: '压缩包' })
      }
      return name
    },
    tabKey() {
      return (name) => {
        return name + new Date().getTime()
      }
    },
    showManuscript() {
      // 只有当show_manuscript有值时 并且show_manuscript 为 false时才隐藏内容文稿，其他情况为显示
      return !(this.materialData.course_statement && !this.materialData.course_statement?.show_manuscript)
    },
    isShowChapterSummary() {
      return this.chapterSummaryList.some(e => e.chapter_content)
    },
    forMatTab() {
      let text = ''
      let subtitle = ''
      let summary = ''
      if (this.materialData?.show_comments !== false) { // 字幕，纪要都显示
        if ((this.materialData.captions?.length || this.captionData?.length) && this.showManuscript) { // 字幕显示
          subtitle = this.$langue('NetCourse_Subtitle', { defaultText: '字幕' })
        }
        if (this.chapterSummaryList?.length && this.isShowChapterSummary) { // 纪要显示
          summary = this.$langue('NetCourse_Summary', { defaultText: '纪要' })
        }
        // 数据拼接
        if (summary && subtitle) {
          text = `${subtitle} · ${summary}`
        } else if (summary && !subtitle) {
          text = summary
        } else if (!summary && subtitle) {
          text = subtitle
        }
      }
      return {
        text,
        subtitle,
        summary
      }
    },
    tabList() {
      const tabArr = [
        { label: 'Mooc_TaskDetail_Audio_Description', name: 'desc', text: '简介' }
      ]
      // 显示文稿纪要
      const { subtitle, summary, text } = this.forMatTab
      if ((subtitle || summary) && this.modelTabValue === 'video_model') {
        tabArr.splice(1, 0, { name: 'caption', text })
      }
      return tabArr
    },
    dtCommon() {
      return (type, val) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.materialData?.file_name,
            page_type: '素材详情页-新版',
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: val,
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.material_id}_${val}`
        } else {
          return `area_${this.material_id}_${val}`
        }
      }
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          this.watermark.textContent = val.staff_name
        }
        let root = document.documentElement
        if (val.staff_name && !document.getElementById('graphic-common-head') && !['mooc', 'spoc'].includes(this.$route.query.from)) {
          root.style.setProperty('--app-height', 'calc(100% - 62px)')
          this.headerIsLoad = true
          this.linkHref = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeaderStyle.css` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeaderStyle.css`
          const commonHeaderJsUrl = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeader.js` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeader.js`
          this.loadHeadJS(commonHeaderJsUrl, function() {
            if (window.$qlCommonHeader) {
              window.$qlCommonHeader.create({
                staff_name: val.staff_name,
                dev: !(process.env.NODE_ENV === 'production')
              })

              setTimeout(() => {
                let el = document.getElementsByClassName('common-header-occupy')
                if (el.length > 0) {
                  console.log('El', el[0].offsetHeight)
                  document.getElementById('app').style.height = `calc(100% - ${el[0].offsetHeight}px)`
                }
              }, 500)
            }
          })
        } else {
          root.style.setProperty('--app-height', '100%')
        }
      },
      immediate: true
    }
  },
  async mounted() {
    // 详情
    await this.getCourseDetail().then((res) => {}).catch((err) => {
      if (err.code === 500) {
        sessionStorage.setItem('401Msg', err?.message || '该课程已下架！')
        this.$router.replace({
          name: '401'
        })
        if (this.isFormMooc) {
          MoocJs.sendErrorInfo(err.message)
        }
      }
    })
    // 获取章节
    await this.getChapterList()
    if (this.isFormMooc) {
      MoocJs.setPause(() => {
        this.$refs.vidioDOM.vedioPlayer.pause()
      })
      MoocJs.setPlay(() => {
        this.$refs.vidioDOM.vedioPlayer.play()
      })
    }
    if (document.body.clientWidth > 1440) {
      this.fileInRow = 4
    } else {
      this.fileInRow = 3
    }
    const _this = this
    window.addEventListener('resize', function () {
      return (() => {
        if (document.body.clientWidth > 1440) {
          _this.fileInRow = 4
        } else {
          _this.fileInRow = 3
        }
      })()
    })

    // 如果是v8跳转过来就定位v8的位置
    const { targetTime } = this.$route.query
    if (targetTime > -1) {
      const playTime = targetTime * 1
      this.toChaptersPosition(playTime)
    }

    MoocJs.messageListener((res) => {
      if (res.events === 'tencent-mooc-lang') {
        console.log('素材双语回传过来res.params', res.params)
        localStorage.setItem('sdc-sys-def-lang', res.params)
        this.$store.commit('setMoocLang', res.params)
        this.getLangJS()
      }
    })
  },
  methods: {
    // 模式选择
    changeModelTab(v) {
      // 记录切换前的播放状态
      // sessionStorage.setItem('changeTabPlayStatus', this.playStatus)
      this.modelTabValue = v.value
      // 视频模式
      if (this.modelTabValue === 'video_model') {
        // 初始化文章全屏
        // this.$store.commit('net/setArticleFull', true)
        // 切换内容的时候不让自动播放
        this.autoPlay = false
        return
      }
      // 文章模式
      if (this.tabActiveName === 'caption') {
        this.tabActiveName = 'desc'
      }
    },
    // 进入全屏方法
    enterFullscreen(docElm) {
      // W3C
      if (docElm.requestFullscreen) {
        docElm.requestFullscreen()
      } else if (docElm.mozRequestFullScreen) {
        // 火狐
        docElm.mozRequestFullScreen()
      } else if (docElm.webkitRequestFullScreen) {
        // 谷歌
        docElm.webkitRequestFullScreen()
      } else if (docElm.msRequestFullscreen) {
        // IE
        docElm.msRequestFullscreen()
      }
    },
    // 退出全屏方法
    exitFullscreen() {
      if (document.exitFullscreen) {
        document.exitFullscreen()
      } else if (document.mozCancelFullScreen) {
        document.mozCancelFullScreen()
      } else if (document.webkitCancelFullScreen) {
        document.webkitCancelFullScreen()
      } else if (document.msExitFullscreen) {
        document.msExitFullscreen()
      }
    },
    // 判断当前页面是否全屏

    getFullScreenStatus() {
      return document.isFullScreen || document.mozIsFullScreen || document.webkitIsFullScreen
    },

    // 全屏切换
    toggle() {
      let dom = this.$refs.scormDOM.$el

      if (!this.getFullScreenStatus()) {
        // 全屏
        this.enterFullscreen(dom)
      } else {
        // 取消全屏
        this.exitFullscreen()
      }
    },
    // 获取登陆用户信息
    loadHeadJS(url, callback) {
      var script = document.createElement('script')
      var fn = callback || function () { }
      script.type = 'text/javascript'
      script.id = 'graphic-common-head'
      // IE
      if (script.readyState) {
        script.onreadystatechange = function () {
          if (script.readyState === 'loaded' || script.readyState === 'complete') {
            script.onreadystatechange = null
            fn()
          }
        }
      } else {
        // 其他浏览器
        script.onload = function () {
          fn()
        }
      }
      script.src = url
      document.getElementsByTagName('head')[0].appendChild(script)
    },
    getCurCaption(data) {
      if (!this.captionData.length > 0) this.captionData = data
    },
    // handleRecord(param) {
    //   if (param.evt === 'play' && this.isFormMooc) {
    //     MoocJs.play()
    //   }
    //   if (param.evt === 'pause' && this.isFormMooc) {
    //     MoocJs.pause()
    //   }
    //   if (param.evt === 'ended' && this.isFormMooc) {
    //     MoocJs.complete()
    //   }
    // },
    handleRecord(param) {
      this.smallVideoStatus = param.evt
      if (param.evt === 'loadedmetadata') {
        // 视频加载完成后拿到播放时长
        let duration = 0
        if (param.duration) { duration = Math.floor(param.duration) }
        // 防止更换视频，过滤多余的章节时间节点
        if (duration) {
          this.chapterSummaryList = this.chapterSummaryList.filter(its => its.chapter_time_point <= duration)
        }
      }
      let status = ''
      if (param.evt === 'play') {
        this.studyRecordQuery.course_duration = Math.floor(param.duration)
        // 重新学习，重置数据
        if (this.studyRecordQuery.is_finish === 1) {
          this.studyRecordQuery.is_finish = 0
          this.studyRecordQuery.is_review = 1 // 重播
          this.studyRecordQuery.total_study_time = 0
          this.learnRecordId = 0
        }
        if (!this.countTimer) {
          this.creatViewTimer(param)
        }
        if (this.isFormMooc || this.isFormSpoc) {
          MoocJs.play()
        }
      }

      if (param.evt === 'pause' || param.evt === 'ended') {
        if (param.evt === 'ended') { // 学习完
          this.studyRecordQuery.is_finish = 1
          status = 'ended'
        }
        // 延迟1秒调用方法，（视频播放完成会先触发pause，再触发ended），解决同时触发pause、ended情况
        if (this.viewRecordTime) clearTimeout(this.viewRecordTime)
        this.viewRecordTime = setTimeout(() => {
          this.viewRecord('', '', status)
        }, 1000)
        clearInterval(this.countTimer)
        this.countTimer = null

        if (param.evt === 'pause' && (this.isFormMooc || this.isFormSpoc)) {
          MoocJs.pause()
        } else if (param.evt === 'ended' && (this.isFormMooc || this.isFormSpoc)) {
          MoocJs.complete()
        }
      }
    },
    creatViewTimer() {
      let _this = this
      let durtation = 0
      let { course_duration } = this.studyRecordQuery
      this.countTimer = setInterval(function () {
        let { my_study_progress, is_finish } = _this.studyRecordQuery
        durtation++
        _this.studyRecordQuery.total_study_time++
        if (durtation % 15 === 0) {
          _this.viewRecord() // 浏览器时长需每15秒记录一次
        }

        // 比视频时长提前10秒触发完成上报
        if (course_duration >= 60 && my_study_progress + 10 >= course_duration && !is_finish) {
          _this.studyRecordQuery.is_finish = 1
          _this.viewRecord()
          clearInterval(_this.countTimer)
          _this.countTimer = null
          if (_this.isFormMooc || _this.isFormSpoc) {
            MoocJs.complete()
          }
        }
      }, 1000)
    },
    viewRecord(videoParam, recordType, status) {
      const { previewType } = this.$route.query
      // 预览不做上报
      if (previewType === 'preview') {
        return
      }
      let recordParam = {}
      if (recordType && recordType === 'scorm') {
        recordParam = videoParam
        recordParam.act_id = this.course_id
        recordParam.learn_record_id = this.learnRecordId
        this.scromRecordQuery = recordParam
      } else {
        if (!this.studyRecordQuery.total_study_time) return
        this.studyRecordQuery.learn_record_id = this.learnRecordId
        recordParam = this.studyRecordQuery
      }
      netViewRecord(recordParam).then((data) => {
        if (data) {
          if (status) {
            this.learnRecordId = 0
          } else {
            this.learnRecordId = data
          }
        }
      })
    },
    getCurrentTime(curTime) {
      const captionBox = this.$el.querySelector('.caption-content')
      this.captionCurTime = Number(curTime.toFixed(2))
      this.studyRecordQuery.my_study_progress = Math.floor(this.captionCurTime) // 视频当前播放时长
      let curDom = document.getElementsByClassName('caption-item-active')[0]?.previousElementSibling
      if (curDom) {
        captionBox.scrollTop = curDom.offsetTop - 18
      }
      this.$refs.interactiveDialog && this.$refs.interactiveDialog.getVideoTime(curTime)
      // 处理章节定位文章内容滚动到对应播放时间过慢问题
      const refName = this.modelTabValue === 'video_model' ? 'articleModelRef' : 'materialDraftRef'
      this.$refs[refName] && this.$refs[refName].startAutoScroll() // 自动滚动内容
    },
    handleScormRecord(scormdata) {
      console.log(scormdata, 'scormdata')
    },
    getCourseDetail() {
      const api = this.isMg ? getMateriaDetail : getMateriaDetailUser
      const reFun = api(this.material_id).then((data) => {
        if (data.status !== 1) {
          // const val = data.status === 3 ? '该课程已下架！' : '课件正在处理中，请稍后再试！'
          // sessionStorage.setItem('401Msg', val)
          // this.$router.replace({
          //   name: '401'
          // })
          // return
        }
        document.title = `${data.file_show_name}_Q-Learning`
        this.materialData = data
        this.materialData.file_type = data.file_type ? data.file_type.slice(0, 1).toUpperCase() + data.file_type.slice(1).toLowerCase() : ''
        if (data.captions?.length > 0) this.readCaptionFile(data.captions)
        this.editorConfig.catalogue = data.is_open_catalogue === 0 ? Boolean(true) : Boolean(false)

        if (data.file_type === 'Article') {
          this.getContentCenter(data.content_id)
        }

        // 续播
        this.playTime = data?.my_study_progress || 0
        if (this.playTime) {
          this.isCurrentTimeShow = true
        }
        this.enableInteractive = data?.enable_interactive

        // 详情页曝光上报
        pageExposure({
          page_type: '素材播放页',
          content_type: '素材',
          act_type: '21',
          content_name: data.file_show_name,
          content_id: this.material_id
        })
      })
      return reFun
    },
    // 跳转时间
    toCurrentTime() {
      this.isCurrentTimeShow = false
      this.$refs.vidioDOM.vedioPlayer.currentTime(this.playTime)
    },
    secondsToMinutes(seconds) {
      let minutes = Math.floor(seconds / 60) // 计算分钟
      let remainingSeconds = seconds % 60 // 计算剩余秒数
      remainingSeconds = remainingSeconds.toString().padStart(2, '0')
      return {
        minutes: minutes,
        seconds: remainingSeconds
      }
    },
    // 获取章节内容
    async getChapterList() {
      try {
        const data = await getChapterListApi(this.material_id)
        console.log(data, 'res-getChapterList')
        const { chapter_list, chapter_from } = data
        const chapterList = chapter_list || []
        this.chapter_from = chapter_from || 0
        this.chapterSummaryList = chapterList.map((item, i) => {
          const { minutes, seconds } = this.secondsToMinutes(item.chapter_time_point)
          const url = item.chapter_cover_content_id ? `${envName.contentcenter}content-center/api/v1/content/imgage/${item.chapter_cover_content_id}/preview` : (item.chapter_cover_url ? `${process.env.VUE_APP_EXAM}${item.chapter_cover_url}` : playImg)
          const startTime = chapterList[i].chapter_time_point
          // 如果是最后一条数据，结束时间在开始时间基础上加1来处理
          const endTime = chapterList.length === (i + 1) ? chapterList[i]?.chapter_time_point + 1 : chapterList[i + 1]?.chapter_time_point
          return {
            ...item,
            chapter_content: item.chapter_content ? item.chapter_content.replace(/(\\r\\n|\\n|\n|\r\n)+/g, '<br>') : '',
            chapter_time: minutes + ':' + seconds,
            imgUrl: url,
            startTime,
            endTime
          }
        })
        console.log(this.chapterSummaryList, 'chapterSummaryList')
      } catch (error) {
        console.log(error)
      }
    },
    // 章节时间对应视频时间的位置
    toChaptersPosition(chapter_time_point) {
      // 互动优先级更高，开启互动后不可点击章节跳转进度
      // if (this.enableInteractive) {
      //   this.$message.warning('已开启互动，不可跳转进度')
      //   return
      // }
      this.captionCurTime = chapter_time_point
      this.$nextTick(() => {
        // 处理章节定位文章内容滚动到对应播放时间过慢问题
        const refName = this.modelTabValue === 'video_model' ? 'articleModelRef' : 'materialDraftRef'
        this.$refs[refName] && this.$refs[refName].startAutoScroll() // 自动滚动内容
        if (this.modelTabValue === 'article_model') {
          this.$refs.pictureVidioDOM && this.$refs.pictureVidioDOM.vedioPlayer.currentTime(this.captionCurTime)
        } else {
          this.$refs && this.$refs.vidioDOM && this.$refs.vidioDOM.vedioPlayer && this.$refs.vidioDOM.vedioPlayer.currentTime(this.captionCurTime)
        }
      })
    },
    // 内容中心鉴权并获取文件内容
    getContentCenter(contentId) {
      operatesignature({
        app_id: 'QLearningService',
        content_id: contentId,
        corp_name: 'tencent',
        operate: 'visit'
      }).then(signature => {
        if (signature) {
          getContentInfo(contentId, {
            signature: signature,
            app_id: 'QLearningService'
          }).then(res => {
            this.graphic_text = res.file_info.content

            if (this.isFormMooc) {
              MoocJs.play()
            }
            this.$nextTick(() => {
              this.$refs.watermark.refreshWatermark()
            })
          })
        }
      })
    },
    readCaptionFile(captions) {
      captions.forEach(item => {
        if (item.attachement_type === 'Caption') {
          axios({
            url: item.url,
            method: 'GET',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
            }
          }).then((response) => {
            if (response.status === 200 && response.data) {
              try {
                let data = response.data?.split(/\n\n|\r\n\r\n/)
                const captionArr = data?.map(str => {
                  let obj = {}
                  const captionItemArr = str.split(/[(\r\n)\r\n|(\r\n\r\n)\r\n\r\n]+/)
                  captionItemArr.map((e, idx) => {
                    if (idx === 1) {
                      const time = JSON.parse(JSON.stringify(e))
                      obj.startTime = e.split('-->')[0]
                      obj.endTime = e.split('-->')[1]
                      const endTimeCopy = JSON.parse(JSON.stringify(time.split('-->')[1]))
                      const startTimeCopy = JSON.parse(JSON.stringify(time.split('-->')[0]))
                      obj.IntStartTime = startTimeCopy ? this.timeToSec(startTimeCopy) : 0
                      obj.IntEndTime = endTimeCopy ? this.timeToSec(endTimeCopy) : 0
                    }
                    if (idx === 2) obj.caption = e
                  })
                  return obj
                })
                this.captionData = captionArr
                // let flag = this.tabList.map(item => item.name).includes('caption')
                // if (this.captionData && this.captionData.length && !flag) {
                //   this.tabList.push({ label: 'Mooc_TaskDetail_Audio_ContentDoc', name: 'caption', text: '内容文稿' })
                // }
              } catch (error) { }
            }
          })
        }
      })
    },
    timeToSec(timeStr) {
      let time = timeStr.split(',')[0]
      let s = ''
      const hour = time.split(':')[0]
      const min = time.split(':')[1]
      const sec = time.split(':')[2]
      let misecond = timeStr.split(',')[1].substr(0, 2)
      misecond = Number(parseInt(misecond) / 100)
      s = Number(hour * 3600) + Number(min * 60) + Number(sec)
      return (s * 100 + misecond * 100) / 100
    }
  },
  beforeDestroy() {
    MoocJs.removeEvent()
  }
}
</script>
<style lang="less">
#app {
  height: var(--app-height);
}
</style>
<style lang="less" scoped>
.net-page {
  height: 100%;
  overflow-y: auto;
  background: #F6F7F9;
  .contain-main {
    width: 100%;
    display: flex;
    justify-content: center;
    padding-top: 10px;
    .left {
      background-color: #fff;
      padding: 24px;
      margin-bottom: 20px;
      .content-title {
        .name {
          display: flex;
          align-items: center;
          color: #000000;
          font-size: 18px;
          font-weight: bold;
          // margin-bottom: 22px;
          word-break: break-word;
          span:last-child {
            flex: 1;
          }
        }
        .tag {
          padding: 0 6px;
          border-radius: 2px;
          margin-right: 10px;
        }
        .word {
          color: #0052d9;
          border: 1px solid #0052d9;
        }
        .info {
          display: flex;
          margin-top: 10px;
          margin-bottom: 16px;
          position: relative;
          .creator_name {
            margin-left: 16px;
          }
        }
      }
      .model-tab {
            background-color: #EFEEEE;
            color: #666666;
            height: 32px;
            line-height: 32px;
            display: inline-block;
            font-size: 12px;
            border-radius: 6px;
            margin-right: 12px;
            font-weight: 500;
            flex-shrink: 0;
            position: relative;
            .item-tab {
              padding: 0 16px;
              cursor: pointer;
              height: 32px;
              display: inline-block;
            }
            .active-model-tab {
              background-color: #0052D9;
              color: white;
              border-radius: 6px;
            }
            .custom_tips_popover {
              background: #3C6EE0;
              border-radius: 6px;
              color: #fff;
              padding: 16px 18px 16px 16px;
              position: absolute;
              min-width: 236px;
              top: 40px;
              right: -75px;
              z-index: 999;
              .el-icon-caret-top {
                color: #3C6EE0;
                position: absolute;
                right: 118px;
                top: -7px;
              }
              .custom-popover-content {
                line-height: 22px;
                font-size: 14px;
                .el-icon-close {
                  font-size: 16px;
                  margin-left: 18px;
                  cursor: pointer;
                }
              }
            }
       }
      .video-main{
        position: relative;
      }
      .video-box {
        border-radius: 4px;
        background-color: #f8f8f8;
        border: solid 1px #ececec;
        :deep(.el-image) {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          .default-icon-picture {
            display: inline-block;
            width: 400px;
            height: 300px;
            background: url("~@/assets/img/default_bg_img.png") no-repeat;
          }
        }
      }
      .current-time-tips {
          position: absolute;
          left: 10px;
          bottom: 50px;
          background: #0F1010;
          color: #fff;
          height: 26px;
          line-height: 26px;
          font-size: 14px;
          display: flex;
          align-items: center;
          padding: 0 5px;
          border-radius: 2px;

          .el-icon-close {
            margin-right: 5px;
            cursor: pointer;
            font-size: 16px
          }

          .tips-btn {
            cursor: pointer;
            color: #3464e0;
            margin-left: 5px;
          }
      }
      :deep(.sdc-editor-preview) {
        .desc,.editor-content {
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          word-break: break-word;
        }
      }
      .info-right{
        display: flex;
        align-items: center;
        color: #00000099;
        position: absolute;
        right: 0px;
        cursor: pointer;
        .fullscreen {
          width: 16px;
          height: 16px;
          margin-right: 4px;
          display: inline-block;
          background: url("~@/assets/img/fullscreen.png") no-repeat center center/cover;
        }
      }
      .content-bottom {
        margin-top: 36px;
        .caption-box {
          position: relative;
          border: 1px solid rgba(238, 238, 238, 1);
          border-radius: 3px;
          .caption-title {
            height: 36px;
            line-height: 36px;
            padding-left: 19px;
            color: rgba(0, 0, 0, 0.8);
            background-color: #f5f7f9;
            span:first-child {
              margin-right: 24px;
            }
          }
          .caption-content {
            height: 380px;
            padding: 16px 0 0 20px;
            overflow-y: auto;
            .caption-item {
              display: flex;
              align-items: baseline;
              margin-bottom: 16px;
              span {
                // width: 40px;
                margin-right: 26px;
                display: inline-block;
                color: rgba(0, 0, 0, 0.6);
              }
              p {
                color: rgba(0, 0, 0, 0.4);
                line-height: 22px;
                letter-spacing: 0.28px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
              }
            }
            .caption-item-active {
              span,
              p {
                color: #0052d9;
              }
            }
          }
        }
        .not-text {
          color: #00000066;
        }
      }
      :deep(.el-tabs) {
        margin-bottom: 20px;
        .el-tabs__header {
          border-bottom: solid 1px #eeeeee;
          margin: 0px;
        }
        .el-tabs__item {
          color: rgba(0, 0, 0, 0.4);
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 10px;
        }
        .is-active {
          color: #0052d9 !important;
          font-weight: 700;
        }
      }
    }
  }
}
@media screen and (max-width: 1660px) {
  .contain-main .left {
    width: 866px !important;
    .video-box,
    .scorm-box {
      width: 818px;
      height: 460px;
    }
  }
}
@media screen and (min-width: 1661px) {
  .contain-main .left {
    width: 1148px !important;
    .video-box,
    .scorm-box {
      width: 1100px;
      height: 619px;
    }
  }
}
</style>
