<template>
  <div class="transfer-page"></div>
</template>

<script>
import { getUserSkipUrl } from '@/config/api.conf.js'

export default {
  name: 'transferPage',
  data () {
    return {}
  },
  computed: {
    forward_id () {
      return this.$route.query?.forward_id || ''
    }
  },
  created () {},
  mounted () {
    this.skipTo()
  },
  methods: {
    skipTo () {
      if (!this.forward_id) return
      getUserSkipUrl(this.forward_id).then(res => {
        console.log('res--------------: ', res)
        window.location.href = res
      })
    }
  }
}
</script>

<style lang="less"></style>
