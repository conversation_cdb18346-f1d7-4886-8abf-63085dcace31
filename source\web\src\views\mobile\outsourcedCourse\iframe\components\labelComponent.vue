<template>
  <div class="label-component">
    <div :class="['label-list', expand ? 'expand' : '']">
      <div :class="['label-item', item.label_hot_enable === 1?'hot':'', (item.label_type === 1 && item.label_hot_enable !== 1) ? 'official' : '' ]" v-for="(item, index) in labelList" :key="index">
        <img class="icon" v-if="item.label_hot_enable === 1" src="../../../../../assets/img/mobile/geekBang/hot.png" />
        <img class="icon" v-if="item.label_type === 1 && item.label_hot_enable !== 1" src="../../../../../assets/img/mobile/geekBang/official.png" />
        <span class="txt">{{ item.label_name }}</span>
      </div>
    </div>
    <div class="expand-btn" v-if="shwoExpand" @click="toggleLable">
      <span class="txt">{{ expand ? '隐藏' : '展开' }}</span>
      <img :class="['icon', expand ? 'up' : 'down']" src="../../../../../assets/img/mobile/geekBang/arrow-down.png" />
    </div>
  </div>
</template>
<script>
export default {
  name: 'labelList',
  props: {
    labelList: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    labelList(newVal) {
    }
  },
  data() {
    return {
      expand: false,
      shwoExpand: false
    }
  },
  computed: {},
  created() {},
  mounted() {},
  methods: {
    toggleLable() {
      this.expand = !this.expand
    }
  }
}
</script>

<style lang="less" scoped>
  .label-component {
  .label-list {
    height: 32px;
    overflow: hidden;
    display: flex;
    flex-wrap: wrap;
    &.expand {
      height: auto;
    }
    .label-item {
      max-width: 122px;
      height: 24px;
      border-radius: 22px;
      background: #f7f8fa;
      color: rgba(0, 0, 0, 0.6);
      display: flex;
      align-items: center;
      padding: 0 12px;
      margin: 0 8px 8px 0;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
      gap: 4px;
      .icon {
        width: 12px;
        height: 12px;
        vertical-align: middle;
      }
      .txt {
        color: rgba(0, 0, 0, 0.6);
        font-size: 10px;
        vertical-align: middle;
      }
    }
    .hot {
      color: #ff5923;
      background: #fbf2e8;
      .txt {
        color: #ff5923;
      }
    }
    .official {
      color: #0052d9;
      background: #ecf2fe;
      .txt {
        color: #0052d9;
      }
    }
  }
  .expand-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    .txt {
      color: rgba(0, 0, 0, 0.6);
      font-size: 12px;
    }
    .icon {
      width: 16px;
      height: 16px;
      &.up {
        transform: rotate(180deg)
      }
    }
  }
}
</style>
