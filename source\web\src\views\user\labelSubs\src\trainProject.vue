<template>
  <el-dialog :title="$langue('Mooc_Home_MyProject_MyProjects', { defaultText: '我的培养项目' })" :visible.sync="visible" width="1264px" :before-close="handleClose"
    :close-on-click-modal="false" class="train-project-dialog">
    <div class="train-body">
      <div class="tab-box">
        <div :class="[{ 'active-tab-pane': activeTab === item.value }, 'item-tab-pane']" v-for="item in tabList"
          :key="item.value" @click="handleTab(item.value)">
          <span>{{ item.label }}</span>
        </div>
      </div>
      <div class="content">
        <div class='form-row'>
          <div class="group-form" v-if="activeTab === 'manage'">
            <span class="label">项目状态：</span>
            <el-radio-group v-model="course_status" @change="handleProjectStatus">
              <el-radio :label="''">{{ $langue('Mooc_Common_JoinType_All', { defaultText: '全部' }) }}</el-radio>
              <el-radio :label="0">{{ $langue('Mooc_Common_TaskType_NotPublished', { defaultText: '未发布' }) }}</el-radio>
              <el-radio :label="1">{{ $langue('Mooc_ProjectDetail_TrainingProgress_NotStarted', { defaultText: '未开始' }) }}</el-radio>
              <el-radio :label="3">{{ $langue('Mooc_ProjectDetail_TrainingProgress_InProgress', { defaultText: '进行中' }) }}</el-radio>
              <el-radio :label="2">{{ $langue('Mooc_Common_TaskType_Ended', { defaultText: '已结束' }) }}</el-radio>
            </el-radio-group>
          </div>
          <div class="join-type-form" v-else>
            <div class="group-form">
              <span class="label">{{$langue('Mooc_Home_MyProject_LearnStatus', { defaultText: '培训状态' })}}：</span>
              <el-radio-group v-model="course_status" @change="handleProjectStatus">
                <el-radio :label="''">{{ $langue('Mooc_Common_JoinType_All', { defaultText: '全部' }) }}</el-radio>
                <el-radio :label="1">{{ $langue('Mooc_ProjectDetail_TrainingProgress_NotStarted', { defaultText: '未开始' }) }}</el-radio>
                <el-radio :label="2">{{ $langue('Mooc_ProjectDetail_TrainingProgress_InProgress', { defaultText: '进行中' }) }}</el-radio>
                <el-radio :label="4">{{ $langue('Mooc_ProjectDetail_TrainingProgress_Finished', { defaultText: '已完成' }) }}</el-radio>
                <el-radio :label="3">{{ $langue('Mooc_ProjectDetail_TrainingProgress_Delayed', { defaultText: '已逾期' }) }}</el-radio>
              </el-radio-group>
            </div>
            <div class="group-form join-type-group">
              <span class="label">{{$langue('Mooc_Home_MyProject_JoinType', { defaultText: '参与方式' })}}：</span>
              <el-radio-group v-model="join_type" @change="handleJoinType">
                <el-radio :label="''">{{ $langue('Mooc_Common_JoinType_All', { defaultText: '全部' }) }}</el-radio>
                <el-radio :label="1">{{ $langue('Mooc_Common_JoinType_RegistBySelf', { defaultText: '自主报名' }) }}</el-radio>
                <el-radio :label="2">{{ $langue('Mooc_Common_JoinType_AddByAdmin', { defaultText: '管理员指定' }) }}</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
        <div class="course-list clearfix" v-if="courseListData && courseListData.length > 0">
          <div v-for="(item, index) in courseListData" :key="index"
            :class="['course-item', { 'course-item-last': index % 4 === 3 }]" @click="toListDetail(item.mooc_course_id)">
            <div class="update-status" v-if="item.serial_type === 2"></div>
            <div class="course-tag">
              <span class="excellent-tag" v-if="item.excellent_status === 1">{{ $langue('Mooc_Home_ListItems_Excellent', { defaultText: '精品' }) }}</span>
            </div>
            <div class="part-status" v-if="item.course_status_name"><span>{{ $langue('Mooc_Home_ListItems_Joined', { defaultText: '已参与' }) }}</span></div>
            <div class="course-total"><span>{{ $langue('Mooc_Home_ListItems_TotalTasks', {count: item.task_count || 0, defaultText: `共${item.task_count}项任务` }) }}</span></div>
            <el-image lazy fit="fill" :src="item.url ? item.url : require('@/assets/mooc-img/default_bg_img.png')"
              class="course-cover">
              <div class="image-slot" slot="placeholder">
                <i class="el-icon-loading"></i>
              </div>
              <div class="error-cover" slot="error">
                <img :src="require('@/assets/mooc-img/default_bg_img.png')" alt="" />
              </div>
            </el-image>
            <el-tooltip effect="light" :content="item.course_title" placement="top-start" :disabled="!item.isOverflow">
              <div class="curse-title overflow-l2" @mouseover="titleOver($event, index)">
                {{ item.course_title }}
              </div>
            </el-tooltip>
            <div class="course-data" v-if="activeTab === 'manage'">
              <!-- <span v-if="item.scorer_count < 20" class="study-num">{{ $langue('Mooc_ProjectDetail_BasicInfo_LowerScorer', { defaultText: '评分人数不足' }) }}</span> -->
              <!-- <span v-else class="rate-box">
                <el-rate class="rate-num" v-model="item.startScore" disabled :colors="{ 5: '#D35A21' }">
                </el-rate>
                <span>{{ item.score }}</span>
              </span> -->
              <span class="study-num" v-show="item.show_join_count !== false">{{ item.user_count }}{{ $langue('Mooc_Home_ListItems_JoinStudy', { defaultText: '人参与学习' }) }}</span>
            </div>
            <div class="course-time">{{ item.course_period_type === 3 ? $langue('Mooc_Home_MyProject_NotLimitedTime', { defaultText: '不限定时间' }) : `${item.start_time} ${$langue('Mooc_Home_MyProject_To', { defaultText: ' 至 ' })} ${item.end_time}`
            }}</div>
            <div class="course-status" v-if="activeTab === 'partake'">
              <div
                :class="[{ 'no-start-tag': item.status === 1 }, { 'finsh-tag': item.status === 4 }, { 'no-result': item.status === 3 }, 'tag']">
                {{ $langue(statusInfo[item.status], { defaultText: statusInfoZHCN[item.status] }) }}</div>
              <span class="main-course">{{$langue('Mooc_ProjectDetail_TaskList_RequiredTask', { defaultText: '应学' })}}：{{ `${item.required_task_finish_count || 0}/${item.required_task_count ||
                0}` }}</span>
              <span class="choice-course">{{$langue('Mooc_ProjectDetail_TaskList_ElectiveTask', { defaultText: '选学' })}}：{{ `${item.non_required_task_finish_count ||
                0}/${item.non_required_task_count
                || 0}` }}</span>
            </div>
          </div>
        </div>
        <div class="bottom-text" v-else>
          <img class="empty-img" :src="empty" alt="" />
          <div class="empty-text">{{ $langue('Mooc_Common_NoData', { defaultText: '暂无数据' }) }}</div>
        </div>
      </div>
      <el-pagination v-if="total" @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="current" :page-sizes="[8, 16, 30, 50, 100]" :page-size="size"
        layout="total,  prev, pager, next, sizes, jumper" :total="total">
      </el-pagination>
    </div>
  </el-dialog>
</template>
<script>
import { getMyTrainProject } from '@/config/mooc.api.conf.js'
import pager from '@/mixins/pager'
import env from 'config/env.conf.js'
export default {
  mixins: [pager],
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeTab: 'partake',
      course_status: '',
      join_type: '',
      courseListData: [],
      // cover: require('@/assets/mooc-img/banner.png'),
      empty: require('@/assets/img/empty.png'),
      rateValue: 0,
      total: 0,
      size: 8
    }
  },
  computed: {
    tabList() {
      return [
        { label: this.$langue('Mooc_Home_MyProject_IJoinedProjects', { defaultText: '我参与的项目' }), value: 'partake' },
        { label: this.$langue('Mooc_Home_MyProject_ManageProjects', { defaultText: '我管理的项目' }), value: 'manage' }
      ]
    },
    statusInfo() {
      return {
        1: 'Mooc_ProjectDetail_TrainingProgress_NotStarted', // 未开始
        2: 'Mooc_ProjectDetail_TrainingProgress_InProgress', // 进行中
        3: 'Mooc_ProjectDetail_TrainingProgress_Delayed', // 已逾期
        4: 'Mooc_ProjectDetail_TrainingProgress_Finished' // 已完成
      }
    },
    statusInfoZHCN() {
      return {
        1: '未开始',
        2: '进行中',
        3: '已逾期',
        4: '已完成'
      }
    }
  },
  mounted() {
    this.onSearch()
  },
  methods: {
    onSearch(page_no = 1) {
      this.courseListData = []
      this.total = 0
      const params = {
        page_no,
        page_size: this.size,
        type: this.activeTab,
        join_type: this.join_type,
        course_status: this.course_status
      }
      getMyTrainProject(params)
        .then((res) => {
          this.courseListData = this.formatList(res.records)
          this.total = res && res.total
        })
    },
    // 项目状态--培训状态
    handleProjectStatus(val) {
      this.course_status = val
      this.onSearch()
    },
    // 参与方式
    handleJoinType(val) {
      this.join_type = val
      this.onSearch()
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    handleTab(val) {
      this.size = 8
      this.activeTab = val
      this.course_status = ''
      this.join_type = ''
      this.onSearch()
    },
    toListDetail(mooc_course_id) {
      const { href } = this.$router.resolve({
        name: 'projectDetail',
        query: {
          mooc_course_id
        }
      })
      window.open(href, '_blank')
    },
    formatList(list) {
      const envName = env[process.env.NODE_ENV]
      return (list || []).map((v) => {
        let url = ''
        if (v.cover_image_storage_type === 'zhihui' || v.cover_image_storage_type === 'other') {
          url = v.cover_image
        } else {
          url = `${envName.contentcenter}content-center/api/v1/content/imgage/${v.cover_image_id}/preview`
        }
        let startScore = ''
        if (v.score) {
          v.score = Number((v.score).toFixed(1))
          const value = Number(v.score.toString().split('.')[0])
          // 数据失真处理
          const compareNum = Number((v.score - value).toFixed(1))
          if (compareNum < 0.3) {
            startScore = Math.floor(v.score)
          } else if ((compareNum < 0.8) && (compareNum >= 0.3)) {
            startScore = Math.floor(v.score) + 0.5
          } else {
            startScore = Math.round(v.score)
          }
        }
        return {
          ...v,
          url,
          score: v.score || '',
          startScore
        }
      })
    },
    // 判断标题是否超出两行
    titleOver(e, index) {
      const target = e.target
      this.$set(
        this.courseListData[index],
        'isOverflow',
        target.scrollHeight > target.clientHeight
      )
    }
  }
}
</script>
<style lang="less" scoped>
.train-project-dialog {
  :deep(.el-dialog__body) {
    padding: 24px 32px;

    .form-row {
      display: flex;
      height: 22px;
      line-height: 22px;

      .join-type-form {
        display: flex;
      }

      .group-form {
        .label {
          display: inline-block;
          margin-right: 12px;
          color: #000000ff;
        }
      }

      .join-type-group {
        margin-left: 56px;
        float: right;
      }
    }
  }

  .tab-box {
    display: flex;
    margin-bottom: 28px;

    .item-tab-pane:first-of-type {
      border-right: unset;
      border-radius: 4px 0px 0 0;
    }

    .item-tab-pane:last-of-type {
      border-left: unset;
      border-radius: 0 4px 0 0;
    }

    .item-tab-pane {
      padding: 0 30px;
      height: 32px;
      opacity: 1;
      line-height: 32px;
      text-align: center;
      border: 1px solid #dcdcdcff;
      background: #ffffffff;
      color: rgba(0, 0, 0, 0.6);
      cursor: pointer;
    }

    .active-tab-pane {
      border: 1px solid #0052d9ff;

      background: #0052d9ff;
      color: #FFFFFF;
    }
  }

  .course-list {
    width: 1220px;
    margin: 20px auto 0;
    height: 665px;
    overflow: auto;

    .course-item {
      width: 285px;
      position: relative;
      float: left;
      margin: 0 20px 28px 0;
      cursor: pointer;

      .update-status {
        background: url('~@/assets/mooc-img/updateing.png') no-repeat center/contain;
        width: 58px;
        height: 20px;
        position: absolute;
        top: 0;
        z-index: 3;
      }

      .course-tag {
        position: absolute;
        right: 8px;
        top: 8px;
        z-index: 2;

        .official-tag,
        .excellent-tag {
          display: inline-block;
          padding: 0 11px;
          font-size: 12px;
          height: 18px;
          line-height: 18px;
          border-radius: 10px;
          color: #fff;
        }

        .official-tag {
          background-color: #1374ff;
        }

        .excellent-tag {
          margin-left: 15px;
          background: #b20000;
        }
      }

      .part-status {
        position: absolute;
        left: 12px;
        top: 150px;
        z-index: 2;
        font-size: 12px;
        color: #fff;
        line-height: 28px;
        text-align: center;
        padding: 0 8px;
        height: 28px;
        border-radius: 40px;
        opacity: 1;
        background: #00000066;
      }

      .course-total {
        position: absolute;
        right: 12px;
        top: 150px;
        z-index: 2;
        font-size: 12px;
        color: #fff;
        line-height: 28px;
        text-align: center;
        padding: 0 8px;
        height: 28px;
        border-radius: 40px;
        opacity: 1;
        background: #00000066;
      }

      .course-cover {
        width: 285px;
        height: 190px;
        line-height: 190px;
        text-align: center;
        color: #666;
        border-radius: 4px;

        .error-cover img {
          width: 285px;
          height: 190px;
        }
      }

      .curse-title {
        height: 44px;
        line-height: 22px;
        font-family: @PingFangSC;
        font-weight: bold;
        color: #000000e6;
        margin-top: 10px;
        margin-bottom: 8px;
      }

      .course-time {
        font-size: 12px;
        color: #00000099;
        height: 20px;
        line-height: 20px;
        margin-bottom: 4px;
      }

      .course-status {
        display: flex;
        align-items: center;
        height: 20px;
        line-height: 20px;
        font-size: 12px;

        .tag {
          border-radius: 2px;
          padding: 0px 8px;
          background: #ebeffcff;
          color: #0052d9ff;
        }

        .no-start-tag {
          background: #fdf6ecff;
          color: #ff7548ff;
        }

        .finsh-tag {
          background: #ccf2e2ff;
          color: #00b368ff;
        }

        .no-result {
          background: #fdeceeff;
          color: #e34d59ff;
        }

        .choice-course,
        .main-course {
          color: #00000099;

        }

        .main-course {
          margin: 0 16px;
        }
      }

      .course-data {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 20px;
        line-height: 20px;
        margin-bottom: 4px;

        .rate-box {
          display: flex;
          align-items: center;
          color: #D35A21;
          font-size: 12px
        }

        :deep(.el-rate) {
          height: 16px;

          .el-rate__icon {
            font-size: 16px;
          }

          .el-rate__text {
            font-size: 12px;
          }
        }

        .study-num {
          color: #00000099;
          font-size: 12px;
        }
      }
    }

    .course-item-last {
      margin-right: 0;
    }
  }

  .bottom-text {
    padding-top: 20px;
    color: #999;
    text-align: center;

    .empty-img {
      margin-bottom: 20px;
      width: 178px;
      height: 130px;
    }
  }
}
</style>
