<template>
  <div :class="['task-content', { 'vertical-style': isVertical }]">
    <!-- 外链课程的简介浮层 -->
    <div
      class="task-desc"
      v-if="
        projectInfo.task_desc && projectInfo.act_type === '99' && !isVertical
      "
      style="line-height: 20px"
    >
      <span class="expand" @click="onDescExpand" v-if="descOverflow">
        <span class="ellipsis" v-show="!descExpand">... </span>
        <span class="text">
          {{
            descExpand
              ? $langue('NetCourse_Retract', { defaultText: '收起' })
              : $langue('NetCourse_Expand', { defaultText: '展开' })
          }}</span
        >
      </span>
      <div :class="['desc', { 'overflow-l2': !descExpand }]">
        {{ projectInfo.task_desc || '' }}
      </div>
    </div>
    <!-- 嵌入的h5页面 -->
    <iframe
      v-if="src"
      class="task-iframe"
      id="taskIframe"
      :src="src"
      frameborder="0"
    ></iframe>

    <!-- 培养项目按钮 -->
    <div
      v-show="showProjectBtn && !isVertical"
      class="project-btn"
      @click="onProjectClick"
      @touchmove.stop.prevent="onTouchmove"
      :style="{ top: projectBtnTop }"
    >
      <img class="img" src="@/assets/img/task.png" alt="" />
      <span class="text span" v-if="from === 'mooc'">
        <div>
          {{ $langue('Mooc_ProjectDetail_Training', { defaultText: '培养' }) }}
        </div>
        <div>
          {{ $langue('Mooc_ProjectDetail_Programs', { defaultText: '项目' }) }}
        </div>
      </span>
      <span class="text span" v-else-if="from === 'spoc'">
        <div>查看</div>
        <div>课表</div>
      </span>
    </div>
    <!-- 培养项目按钮--横屏的时候展示 -->
    <div
      class="task-icon"
      :class="{'task-icon-pad-fullscreen': isPadFullscreenGray}"
      v-show="showProjectBtn && isVertical"
      @click="onProjectClick"
    >
      <img class="icon" src="@/assets/img/task.png" alt="" />
    </div>
    <!-- 预览提示 -->
    <customTips 
      v-if="!isVertical && isPreview" 
      title="项目预览中，不会实际变更任务状态"
      IconName="el-icon-warning"
      backgroundColor="#FDF6EC"
      color="#FF7548"
      line-height="40px"
      >
    </customTips>
    <!-- 底部完成进度 -->
    <div
      :class="[
        'condition-popup',
        moocLang === 'en-us'
          ? 'en-safe-area-inset-bottom'
          : 'safe-area-inset-bottom'
      ]"
      v-if="projectInfo.act_type !== '20' && !isVertical && !src.includes('/management/mobile/face-course')"
    >
      <template v-if="!taskFinishedState.is_finished">
        <div>
          <span class="condition-label span">{{
            $langue('Mooc_TaskDetail_Navigation_TaskFinishCondition', {
              defaultText: '任务完成条件：'
            })
          }}</span>
          <span class="condition-detail span">{{
            projectInfo.conditionText
          }}</span>
        </div>
        <div class="study-time" v-if="projectInfo.conditionType * 1 === 2">
          <span class="study-label span">{{
            $langue('Mooc_TaskDetail_AlreadyLearned', {
              defaultText: '已学习：'
            })
          }}</span>
          <span class="study-detail span">{{ learningTime }}</span>
        </div>
      </template>
      <span class="compelete-conditon-box" v-else>
        <img
          class="img"
          src="@/assets/mooc-img/done-fill.png"
          v-show="taskFinishedState.is_finished"
          alt=""
        />
        <span
          class="complete-label span"
          v-show="taskFinishedState.is_finished"
          >{{
            $langue('Mooc_ProjectDetail_TaskList_TaskFinished', {
              defaultText: '任务已完成'
            })
          }}</span
        >
        <span
          class="complete-time-label span"
          v-show="taskFinishedState.is_finished"
        >
          {{
            $langue('Mooc_ProjectDetail_TrainingProgress_FinishedTime', {
              defaultText: '完成时间：'
            })
          }}
        </span>
        <span class="complete-time span" v-show="taskFinishedState.is_finished">
          {{ taskFinishedState.finished_time || '-' }}
        </span>
      </span>
    </div>

    <!-- 培养项目弹出层 -->
    <van-popup
      v-model="showProject"
      class="project-popup"
      :class="{'project-popup-pad-fullscreen': isPadFullscreenGray}"
      safe-area-inset-bottom
      position="bottom"
      z-index="1000"
    >
      <div class="title">
        <span>{{ courseProcess.mooc_course_name }}</span>
        <img
          class="icon"
          src="@/assets/mooc-img/close.png"
          @click="closeProject"
          alt=""
        />
      </div>
      <div class="task-box">
        <div class="task-detail">
          <span
            :class="projectInfo.required ? 'required type' : 'unrequired type'"
          >
            {{
              projectInfo.required
                ? $langue('Mooc_ProjectDetail_TaskList_RequiredTask', {
                    defaultText: '应学'
                  })
                : $langue('Mooc_ProjectDetail_TaskList_ElectiveTask', {
                    defaultText: '选学'
                  })
            }}
          </span>
          {{ projectInfo.task_name || '' }}
        </div>
        <div class="condition">
          <span class="label">{{
            $langue('Mooc_TaskDetail_Navigation_TaskFinishCondition', {
              defaultText: '任务完成条件：'
            })
          }}</span>
          <span class="text">{{ projectInfo.conditionText }}</span>
        </div>
      </div>
      <div class="schedule">
        <div class="schedule-detail">
          <span class="required-schedule"
            >{{
              $langue('Mooc_ProjectDetail_TrainingProgress_RequiredProgress', {
                defaultText: '应学进度'
              })
            }}：{{ courseProcess.requiredProcess }}</span
          >
          <span class="unrequired-schedule"
            >{{
              $langue(
                'Mooc_ProjectDetail_TrainingProgress_NonRequiredProgress',
                { defaultText: '选学进度' }
              )
            }}：{{ courseProcess.noRequireProcess }}</span
          >
          <span :class="['icon', projectStatusIcon]"></span>
        </div>
        <div class="handle-box">
          <div class="handle-item" v-if="prevTaskBtnShow">
            <div class="handle-btn" @click="changeTask(-1)">
              <img class="icon" src="@/assets/mooc-img/arrow-pre.png" />
            </div>
            <div class="handle-text">
              {{
                $langue('Mooc_TaskDetail_Navigation_PreTask', {
                  defaultText: '上个任务'
                })
              }}
            </div>
          </div>
          <div class="handle-item">
            <div class="handle-btn" @click="showTaskList">
              <img class="icon" src="@/assets/mooc-img/list.png" />
            </div>
            <div class="handle-text">
              {{
                $langue('Mooc_TaskDetail_Navigation_TaskList', {
                  defaultText: '任务列表'
                })
              }}
            </div>
          </div>
          <div class="handle-item back-item" @click="toProjectDetail">
            <div class="handle-btn">
              <img class="icon" src="@/assets/mooc-img/rollback.png" />
            </div>
            <div class="handle-text">
              {{
                from === 'mooc'
                  ? $langue('Mooc_TaskDetail_Navigation_ReturnProjectHome', {
                      defaultText: '返回项目首页'
                    })
                  : from === 'spoc'
                  ? $langue('Mooc_TaskDetail_BackClass', {
                      defaultText: '返回班级详情'
                    })
                  : ''
              }}
            </div>
          </div>
          <div
            class="handle-item next-item"
            v-if="nextTaskBtnShow"
          >
            <div class="handle-btn" @click="changeTask(1)">
              <img class="icon" src="@/assets/mooc-img/arrow-next.png" />
            </div>
            <div class="handle-text">
              {{
                $langue('Mooc_TaskDetail_Navigation_NextTask', {
                  defaultText: '下个任务'
                })
              }}
            </div>
          </div>
        </div>
      </div>
    </van-popup>
    <!-- 任务列表弹出层 -->
    <van-popup
      v-model="showTask"
      class="task-popup"
      safe-area-inset-bottom
      position="bottom"
      z-index="1001"
    >
      <div class="title">
        <span>{{ courseProcess.mooc_course_name }}</span>
        <img
          class="icon"
          src="@/assets/mooc-img/close.png"
          @click="closeTaskList"
          alt=""
        />
      </div>
      <task-list
        :mooc_course_id="mooc_course_id"
        :task_id="task_id"
        :unlocked_by_step="courseProcess.unlocked_by_step"
        :dateNum="dateNum"
        :isVertical="isVertical"
        :moocPreview="$route.query.moocPreview * 1"
      />
    </van-popup>

    <van-dialog
      v-model="finishProject.visible"
      :show-cancel-button="finishProject.enable_certificate"
      :confirm-button-text="
        finishProject.enable_certificate
          ? $langue('Mooc_ProjectDetail_ViewCertificate', {
              defaultText: '查看证书'
            })
          : $langue('Mooc_ProjectDetail_Notice_IKnow', {
              defaultText: '知道了'
            })
      "
      confirm-button-color="#0052D9"
      :cancel-button-text="
        $langue('Mooc_ProjectDetail_Notice_IKnow', { defaultText: '知道了' })
      "
      @confirm="viewCertificate"
      :class="['finish-dialog', { 'vertical-dialog': isVertical }]"
    >
      <img class="img" src="@/assets/mooc-img/done-blue.png" alt="" />
      <p>
        {{
          $langue('Mooc_ProjectDetail_Congratulations', {
            defaultText: '恭喜您完成培养项目！'
          })
        }}
      </p>
      <div v-if="finishProject.enable_certificate" class="certificate-info">
        <div class="certificate-tips">
          {{
            $langue('Mooc_ProjectDetail_GetOneCertificate', {
              defaultText: '获得一张证书奖励'
            })
          }}
        </div>
        <div>{{ finishProject.certificate_name }}</div>
      </div>
    </van-dialog>
    <!-- 双语按钮 -->
    <!-- 双语暂时注释 -->
    <!-- <div
      id="drag-taskDetail-lang"
      :class="['drag-taskDetail-lang', { 'lang-icon-vertical': isVertical }]"
      @click="changeLang"
      @touchmove.stop.prevent="onTouchmoveLang"
      :style="{ top: langBtnTop }"
      v-show="from !== 'spoc'"
    >
      <span
        :class="[moocLang === 'en-us' ? 'el-icon-zh' : 'el-icon-en', 'icon']"
      ></span>
      <span class="text">{{ moocLang === 'en-us' ? '中文' : 'Eng' }}</span>
    </div> -->
    <!-- 任务完成条件提醒 -->
    <van-popup
      :overlay="false"
      v-model="finishedConditionTipsShow"
      class="finished-condition-tips"
    >
      <img src="@/assets/img/close-circle.png" alt="" @click="finishedConditionTipsShow=false">
      <div class="tips">
        {{
          $t('Mooc_Common_Alert_TaskFinishWarning', {
            defaultText: '任务完成条件提醒'
          })
        }}
      </div>
      <div class="min-study-time">
        {{
          $t('Mooc_TaskDetail_Navigation_MinStudyTime', {
            minute: this.taskData.finished_condition?.condition || 0,
            defaultText: `至少学习${this.taskData.finished_condition?.condition}分钟`
          })
        }}
      </div>
    </van-popup>
    <!-- 任务完成提示 -->
    <div
      v-if="finishedToast && moocLang !== 'en-us'"
      :class="['finished-Toast', { 'horizontal-toast': isVertical }]"
    >
      <img src="@/assets/img/close-circle.png" alt="" @click="closeTaskTips">
      <div class="done-tips">任务已完成</div>
      <div
        v-if="nextTaskBtnShow"
        class="next-task"
        >点击前往<span @click="changeTask(1)">下一任务</span></div
      >
    </div>

    <div
      v-if="finishedToast && moocLang === 'en-us'"
      :class="['finished-Toast', { 'horizontal-toast': isVertical }]"
    >
      <img src="@/assets/img/close-circle.png" alt="" @click="closeTaskTips">
      <div class="done-tips">Task completed</div>
      <div
        v-if="nextTaskBtnShow"
        class="next-task"
        >Go to <span @click="changeTask(1)">next one</span></div
      >
    </div>

     <!-- 兑换弹窗组件 -->
     <redemptionPopup v-model="showRedemptionPopup" :cardList="cardList" :courseType="courseDetailInfo.recourse_from" :coursePurchaseInfo="coursePurchaseInfo" :currentPopInfo="currentPopInfo" :isSubscription="isSubscription" @userOperator="userOperator" @linkTo="linkTo"/>
  </div>
</template>

<script>
import redemptionPopup from '../outsourcedCourse/graphic/components/redemptionPopup.vue'
import redemptionPopups from './mixins/redemptionPopup.js'
import taskList from './taskList'
import {
  getTaskDetail,
  getTaskDetailSpoc,
  getCourseProcess,
  getCourseProcessSpoc,
  saveLearnRecord,
  saveLearnRecordSpoc,
  getTaskList,
  getTaskListSpoc,
  getGeekCourseDetail,
  getCoursePurchaseInfo,
  getSubscription,
  getAcctinfosActivity,
  purchaseSourceFromConfig
} from '@/config/mooc.api.conf.js'
import customTips from '@/components/tips.vue'
import { Dialog, Toast } from 'vant'

// 移动端适配
import 'amfe-flexible/index.js'
import { SDKTimer, SDKUtils } from 'sdc-moocjs'
import { toMoocDetailMob } from '@/utils/tools.js'
import translateLang from '@/views/mobile/mixins/translateLang.js'
import { mapState } from 'vuex'

export default {
  name: 'taskContent',
  mixins: [translateLang, redemptionPopups],
  components: {
    taskList,
    customTips,
    redemptionPopup,
    [Dialog.Component.name]: Dialog.Component
    // [Toast.Component.name]: Toast.Component
  },
  provide() {
    return {
      detailInfo: this
    }
  },
  data() {
    return {
      cardList: [],
      mooc_course_id: '',
      task_id: 0, // 当前的任务id
      from: '',
      class_id: '',
      src: '',
      showProjectBtn: true,
      projectBtnTop: 'calc(47% + 86px + 86px)',
      langBtnTop: '47%',
      descExpand: true, // 展开或收起简介
      descOverflow: false, // 简介是否超过两行
      taskData: {
        is_finished: false
      },
      courseProcess: {},
      showProject: false,
      showTask: false,
      timer: null,
      recordId: 0,
      taskList: [],
      dateNum: 0,
      taskIndex: 0, // 当前任务在纯任务列表中的索引
      prevTaskBtnShow: false, // 上个任务按钮是否显示
      nextTaskBtnShow: false, // 下个任务按钮是否显示
      seconds: 0, // 当前任务本次学习时长
      totalStudyTime: 0, // 当前任务本次学习时间+已学习时长
      taskFinishedState: {
        is_finished: false,
        finished_time: ''
      },
      finishProject: {
        visible: false,
        enable_certificate: false, // false表示未开启证书，true表示开启证书
        certificate_id: '', // 证书id
        certificate_name: '' // 证书名称
      },
      isVertical: false,
      alreadyShowCertificate: false,
      onload: false,
      errNum: 0,
      interactiveDialog: false, // 视频互动弹窗
      finishedConditionTipsShow: false,
      isMoved: false,
      taskStudyIndex: 0, // testMsg
      finishedToast: false,
      courseDetailInfo: {}, // 极客课程详情
      coursePurchaseInfo: {}, // 用户课程购买信息 (极客)
      subscriptionInfo: {}, // 订阅补货通知状态
      task_learn_status: null,
      geekSourceLoading: false, // 极客资源是否开始加载~  true:开始加载极客内容  false:未开始加载或者跳转到登录页面
      isFromMobileQuestion: false,
      sourceFromConfig: [],
      isPadFullscreenGray: false
    }
  },
  computed: {
    ...mapState(['moocLang', 'userInfo']),
    learningTime() {
      let time = (this.timer &&
        this.timer.formatSeconds(this.totalStudyTime)) || [0, 0, 0] // 传入用户输入的数据
      return this.$langue('Mooc_ProjectDetail_TaskList_ViewTime2', {
        minute: time[1],
        second: time[2],
        defaultText: `${time[1]}分${time[2]}秒`
      })
    },
    // 是否是预览
    isPreview() {
      return this.$route.query.moocPreview * 1 === 1
    },
    isGeekBang() {
      return this.$route.query.isGeekBang * 1 === 1
    },
    projectInfo() {
      const info = {}
      if (this.taskData.act_type === '20') {
        if (this.taskData.resource_type === 'Exam') {
          info.conditionText = this.$langue(
            'Mooc_TaskDetail_Navigation_ExamPass',
            { defaultText: '通过考试' }
          )
        } else if (this.taskData.resource_type === 'Practice') {
          info.conditionText = this.$langue(
            'Mooc_TaskDetail_Navigation_PracticePass',
            { defaultText: '完成练习' }
          )
        }
      } else if (this.taskData.act_type === '32') {
        info.conditionText = this.$langue(
          'Mooc_TaskDetail_Navigation_FinishByCourse333',
          { defaultText: '完成问卷填写并提交' }
        )
      } else if (this.taskData.conditionType * 1 === 1) {
        info.conditionText = this.$langue(
          'Mooc_TaskDetail_Navigation_FinishByCourse',
          { defaultText: '由课程完成条件决定' }
        )
      } else if (this.taskData.conditionType * 1 === 2) {
        info.conditionText = this.$langue(
          'Mooc_TaskDetail_Navigation_MinStudyTime',
          {
            minute: this.taskData.conditionTime,
            defaultText: `至少学习${this.taskData.conditionTime}分钟`
          }
        )
      } else {
        info.conditionText = '-'
      }
      return {
        ...this.taskData,
        ...info
      }
    },
    projectStatusIcon() {
      const status = this.courseProcess.learn_status
      let iconName = ''
      switch (status) {
        case 1:
          iconName = this.moocLang === 'en-us' ? 'en-icon-wkx' : 'icon-wks'
          break
        case 2:
          iconName = this.moocLang === 'en-us' ? 'en-icon-jxz' : 'icon-jxz'
          break
        case 3:
          iconName = this.moocLang === 'en-us' ? 'en-icon-ywc' : 'icon-ywc'
          break
        default:
          break
      }
      return iconName
    },
    // 是否外部课程系列
    isRecourseFrom() {
      return this.sourceFromConfig.includes(this.courseDetailInfo.recourse_from)
    },
    preview() { // 是否是极客时间试学模式
      return this.isRecourseFrom && this.courseDetailInfo.can_preview && this.courseDetailInfo.course_acquisition_type === 2 && this.task_learn_status === -2
    },
    previewRecords() { // 极客时间已试学 课程列表
      return (this.coursePurchaseInfo.preview_records || []).map(item => item.outsourced_course_id)
    }
  },
  watch: {
    src(val) {
      // 初始化双语
      let lang = ''
      if (this.$route.query?.lang) {
        lang = ['en-US', 'en-us'].includes(this.$route.query?.lang)
          ? 'en-us'
          : 'zh-cn'
      } else {
        lang = localStorage.getItem('sdc-sys-def-lang') || 'zh-cn'
      }
      this.$store.commit('setMoocLang', lang)
      this.getMobileLangJS()
      let iframeDOM = this.getIframeDom()
      this.$nextTick(() => {
        if (!iframeDOM) return
        iframeDOM.onload = () => {
          SDKUtils.postMessage(iframeDOM, 'tencent-mooc-lang', lang)
        }
      })
    },
    // 初始化高度
    isVertical(val) {
      this.langBtnTop = val ? '16px' : '55%'
      if (val) {
        console.log('高度监听横屏问题', val)
        window.wx.miniProgram.postMessage({
          data: {
            type: 'vertical',
            value: true
          }
        })
      }
    }
  },
  created() {
    const { mooc_course_id, task_id, from, class_id } = this.$route.query
    this.mooc_course_id = mooc_course_id
    this.task_id = task_id * 1
    this.from = from || 'mooc'
    this.class_id = class_id
    this.initData()
  },
  mounted() {
    window.addEventListener('message', this.handleMessage, false)
  },
  beforeDestroy() {
    this.timer.removeEvent()
    this.timer = null
    window.removeEventListener('message', this.handleMessage)
  },
  methods: {
    // 初始化
    async initData() {
      await this.purchaseSourceFromConfig()
      this.initTimer()
      this.onEvent()
    },
    closeTaskTips () {
      this.finishedToast = false
    },
    // 学霸卡活动特殊课程
    async purchaseSourceFromConfig() {
      const res = await purchaseSourceFromConfig()
      this.getTask()
      this.getProcess()
      this.sourceFromConfig = res || []
    },
    // 获取卡券信息
    async getAcctinfosActivity() {
      const result = await getAcctinfosActivity()
      console.log(result, '获取卡券信息')
      this.cardList = result.accts
      if (this.cardList.length) {
        this.cardList.forEach((item) => {
          // 不是本课程的专用卡不可使用
          item.isUse = (item.acct_type_code === this.courseDetailInfo.recourse_from || item.acct_type_code === 'xuebaCommon')
        })
      }
    },
    // 监听子页面传来的互动信息
    // handleMessage({ data }) {
    handleMessage({ data }) {
      console.log('子页面传的参数', data)
      // 标签显示组件label-show-mob 点击标签跳转小程序的搜索页面
      if (data.event === 'toSearch') {
        console.log('postmessage接收触发toSearch小程序内直接跳转')
        // 小程序内直接跳转
        window.wx.miniProgram.navigateTo({
          url: data.url || ''
        })
        return
      } else if (data.events === 'previous') {
        // 切换上一个任务
        this.changeTask(-1)
        return
      } else if (data.events === 'next') {
        // 切换下一个任务
        this.changeTask(1)
        return
      } else if (data.events === 'completeStatusUpdata') {
        // 更新任务状态，并刷新页面
        this.isFromMobileQuestion = true
        this.handleSaveLearnRecord(true, {}, false)
        return
      } else if (data.events === 'toProjectDetail') {
        this.toProjectDetail()
        return
      }
      // 监听loading变化
      if (data.page === 'iframe') {
        this.iframeLoadingChange(data.loading, data.geekSourceLoading)
      } else if (data?.data === 'vertical') {
        this.isVertical = true
      } else if (data.type === 'changeLang') {
        // console.log('语言变化', data.lang)
        this.$store.commit('setMoocLang', data.lang)
        this.getMobileLangJS()
        window.wx.miniProgram.postMessage({
          data: data.lang,
          type: 'lang'
        })
      } else if (data?.data === 'navigateBack') {
        window.wx.miniProgram.navigateBack()
      } else if (data.from === 'material') {
        this.isVertical = data.isVertical
      } else if (data.event === 'toSpecial') {
        console.log('专区跳转', data)
        window.wx.miniProgram.navigateTo({
          url: data.url || ''
        })
      } else if (data.event === 'toCourse') {
        console.log('专区跳转', data)
        window.wx.miniProgram.navigateTo({
          url: data.url || ''
        })
      } else if (data.event === 'isPadFullscreenGray') {
        this.isPadFullscreenGray = data.isPadFullscreenGray
      }
    },
    // 控制iframe的loading显示
    iframeLoadingChange(loading = true, geekSourceLoading = false) {
      loading ? Toast.loading({ duration: 0, forbidClick: true, message: '加载中...' }) : Toast.clear()
      geekSourceLoading && (this.geekSourceLoading = true)
    },
    // 获取任务详情
    getDetail(flag) {
      let handleApi = ''
      let params = ''
      if (this.from === 'spoc') {
        handleApi = getTaskDetailSpoc
        params = {
          semesterId: this.mooc_course_id,
          classId: this.class_id,
          taskId: this.task_id
        }
      } else if (this.from === 'mooc') {
        handleApi = getTaskDetail
        params = {
          mooc_course_id: this.mooc_course_id,
          task_id: this.task_id
        }
        if (!this.isGeekBang && this.isPreview) {
          params.preview = 1 
        }
      }
      return handleApi(params).then(async (res) => {
        this.task_learn_status = res.task_learn_status
        // task_learn_status 任务状态（空/null任务正常 -2未加入项目 -1暂无访问权限 0项目未开始 1项目已结束  2项目已逾期 3被管理员锁定 4任务未解锁 5不支持PC端，6不支持移动端 7未发布）
        // task_status 2 任务失效
        let error = [-2, -1, 0, 1, 2, 3, 4, 7]
        // spoc配置课程(面授,活动)
        const spocConfigCourse = ['1', '98']
        // 如果为spoc面授则修改错误条件
        if (this.from === 'spoc' && spocConfigCourse.includes(res.act_type)) {
          error = [-2, -1, 0, 1, 2, 3, 7]
        }
        if (res.act_type === '102' && this.sourceFromConfig.includes(res.mooc_resource_from)) {
          let detailInfo = await getGeekCourseDetail(res.act_id)
          this.courseDetailInfo = detailInfo
          this.getOtherInfo()
          this.getAcctinfosActivity()
        }
        if (this.preview) {
          console.log('res-----: ', res)
        } else {
          if (error.includes(res.task_learn_status) || res.task_status === 2) {
            let query = {
              mooc_course_id: this.mooc_course_id,
              type: res.task_status === 2 ? '-98' : 
                res.task_learn_status === -2 ? '-2' : res.task_learn_status === -1 ? '-1' : res.task_learn_status,
              admin: JSON.stringify(res.mooc_course_admins_list),
              from: this.from
            }
            if (this.from === 'spoc') {
              query.class_id = this.class_id
            }
            this.$router.replace({
              name: 'taskAbnormal',
              query
            })
          } else if (
            res.task_learn_status !== 3 &&
            res.required &&
            this.courseProcess.unlocked_by_step === true &&
            res.prev_task_finished === false
          ) {
            // 如果没有被管理员锁定，并且开启了按顺序解锁，并且是必须任务，并且上一个任务没有完成，提示‘完成上一个应学任务后解锁’
            let query = {
              mooc_course_id: this.mooc_course_id,
              type: '-97',
              from: this.from
            }
            if (this.from === 'spoc') {
              query.class_id = this.class_id
            }
            this.$router.replace({
              name: 'taskAbnormal',
              query
            })
          }
        }
        
        // 文章
        if (res.act_type * 1 === 18) {
          let miniUrl = `/pages/networkCourse/article/index?mooc_course_id=${this.mooc_course_id}&task_id=${this.task_id}&from=${this.from}`
          if (this.from === 'spoc') {
            miniUrl = miniUrl + `&class_id=${this.class_id}`
          }
          window.wx.miniProgram.reLaunch({
            url: miniUrl
          })
          return
        } else if (res.act_type * 1 === 22) { // 作业
          let miniUrl = `/pages/mooc/work/index?homework_id=${res.act_id}&task_id=${this.task_id}&act_id=${this.mooc_course_id}&from=${this.from}`
          if (this.from === 'spoc') {
            miniUrl = miniUrl + `&class_id=${this.class_id}`
          }
          window.wx.miniProgram.reLaunch({
            url: miniUrl
          })
          return
        } else if (res.act_type * 1 === 23) {
          // 第三方
          let miniUrl = `/pages/mooc/thirdPartyTasks/index?mooc_course_id=${this.mooc_course_id}&task_id=${this.task_id}&from=${this.from}`
          if (this.from === 'spoc') {
            miniUrl = miniUrl + `&class_id=${this.class_id}`
          }
          window.wx.miniProgram.reLaunch({
            url: miniUrl
          })
          return
        }
  
        // 根据任务类型是否是小程序页面
        // let url = this.toWechatMiniPage(res)
        // console.log('文章查询问题', res, url)
        // if (url) {
        //   this.testUrl = url
        //   console.log('11111111111111111111')
        //   window.wx.miniProgram.navigateTo({ 
        //     url,
        //     events: {
      
        //     },
        //     fail: function(res) {
        //       console.log('失败', res)
        //       this.errUrl = res
        //     },
        //     success: function(res) {
        //       console.log('跳转', res)
        //       // 通过eventChannel向被打开页面传送数据
        //       this.testMo = res
        //     }
        //   })
        //   return
        // }
        // console.log('22222222222222222', url)
        // this.testValUrl = url

        // if (location.host.indexOf('.woa.com') > -1 && res.act_type === '99') {
        if (location.host.indexOf('.woa.com') > -1 && ['99', '102'].includes(res.act_type)) {
          document.domain = 'woa.com'
        }
        console.log(res, '移动端res')
        this.src = this.getSrc(
          res.act_type * 1,
          res.act_id,
          res.resource_url_mobile,
          res.resource_type,
          res.resource_from,
          res
        )
        
        if (!this.src || res.task_learn_status === 6) {
          const link = `${process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/TZELHU' : 'http://s.test.yunassess.com/s/urrd9E'}?mooc_course_id=${this.mooc_course_id}&task_id=${this.task_id}&from=${this.from}&class_id=${this.class_id}`
          this.$router.replace({
            name: 'mobileError',
            query: {
              type: 2,
              href: encodeURIComponent(link),
              class_id: this.class_id,
              from: this.from,
              mooc_course_id: this.mooc_course_id
            }
          })
          return
        }

        // 音视频关闭防挂机功能
        if (res.act_type * 1 === 2) {
          this.timer.antiHangUp = false
        }
        // 处理任务完成条件
        res.finished_condition =
          typeof res.finished_condition === 'string'
            ? JSON.parse(res.finished_condition)
            : res.finished_condition
        // 单位：分钟
        res.conditionTime = res.finished_condition?.condition || 0
        res.conditionType = res.finished_condition?.type || ''
        // conditionType 1 由课程完成条件决定 2 至少学习xx分钟
        // if (res.act_type === '20') {
        //   if (res.resource_type === 'Exam') {
        //     res.conditionText = this.$langue('Mooc_TaskDetail_Navigation_ExamPass', { defaultText: '通过考试' })
        //   } else if (res.resource_type === 'Practice') {
        //     res.conditionText = this.$langue('Mooc_TaskDetail_Navigation_PracticePass', { defaultText: '完成练习' })
        //   }
        // } else if (res.conditionType * 1 === 1) {
        //   res.conditionText = this.$langue('Mooc_TaskDetail_Navigation_FinishByCourse', { defaultText: '由课程完成条件决定' })
        // } else if (res.conditionType * 1 === 2) {
        //   res.conditionText = this.$langue('Mooc_TaskDetail_Navigation_MinStudyTime', { minute: res.conditionTime, defaultText: `至少学习${res.conditionTime}分钟` })
        // } else {
        //   res.conditionText = '-'
        // }
        // is_finished, null 未开始，false进行中，true已完成
        this.taskFinishedState = {
          is_finished: res.is_finished,
          finished_time: res.finished_time
        }
        // 表示任务由未完成到已完成状态，才弹项目已完成的提示
        this.alreadyShowCertificate = res.is_finished 
        this.taskData = res
        this.taskData.finished_condition =
          typeof res.finished_condition === 'string'
            ? JSON.parse(res.finished_condition)
            : res.finished_condition
        document.title = res.task_name || ''
        this.totalStudyTime = this.taskData.total_study_time
        // 任务状态正常，未完成学习
        if (!this.taskData.is_finished && !this.taskData.task_learn_status) {
          // 应学任务，并且不是文章类型才提示任务完成条件
          if (this.taskData.required && this.taskData.resource_type !== 'Article') {
            if (
              this.taskData.conditionType * 1 === 2 &&
              this.taskData.finished_condition?.condition
            ) {
              // Toast.loading({
              //   message: `<p>${this.$langue('Mooc_Common_Alert_TaskFinishWarning', { defaultText: '任务完成条件提醒' })}</p><p style="color: #ED7B2F">${this.$langue('Mooc_TaskDetail_Navigation_MinStudyTime', { minute: this.taskData.finished_condition?.condition || 0, defaultText: `至少学习${this.taskData.finished_condition?.condition}分钟` })}</p>`,
              //   forbidClick: true,
              //   type: 'html'
              // })
              this.finishedConditionTipsShow = true
              let timmer = setTimeout(() => {
                this.finishedConditionTipsShow = false
              }, 3000)
              this.$once('hook:beforeDestroy', () => {
                clearTimeout(timmer)
                timmer = null
              })
            }
          }
        }
        // 计算简介行数，是否显示展开收起按钮
        this.$nextTick(() => {
          const el = document.querySelector('.task-desc .desc')
          if (el) {
            this.descOverflow = Math.floor(el.clientHeight / 20) > 2
            this.descExpand = !this.descOverflow
          }
          // 外链课程在iframe加载完成后开始计时
          const iframeDOM = document.getElementById('taskIframe')
          if (!iframeDOM) return
          if (flag) { // 解决任务完成时 loading没有关闭的问题
            Toast.clear()
          }
          iframeDOM.onload = () => {
            // 优化极客时间的iframe的加载loading
            if (!(this.taskData.act_type === '102' && this.geekSourceLoading)) {
              Toast.clear()
            }
            console.log('iframe加载完成')
            if (['99'].includes(this.taskData.act_type) || this.taskData.resource_from === 'gangqian') {
              this.timer.start()
            }
          }
        })
      }).catch(() => {
        Toast.clear()
      })
    },
    // 试读时获取其他信息
    getOtherInfo() {
      let coursePurchaseInfoApi = getCoursePurchaseInfo(this.mooc_course_id)
      let subscriptionApi = getSubscription({ recourse_from: this.courseDetailInfo.recourse_from })
      Promise.all([ coursePurchaseInfoApi, subscriptionApi ]).then(res => {
        this.coursePurchaseInfo = res[0]
        console.log('this.coursePurchaseInfo: ', this.coursePurchaseInfo)
        this.subscriptionInfo = res[1] || {}
      }).catch(() => {
        Toast.clear()
      })
    },
    // toWechatMiniPage(res) {
    //   let url = ''
    //   if (res.act_type === '18') {
    //     url = `/pages/networkCourse/article/index?mooc_course_id=${this.mooc_course_id}&task_id=${this.task_id}&from=${this.from}`
    //   }

    //   // 素材
    //   if (res.act_type * 1 === 21) {
    //     if (res.resource_type === 'Article') {
    //       url = `/pages/networkCourse/article/index?mooc_course_id=${this.mooc_course_id}&task_id=${this.task_id}&from=${this.from}`
    //     }
    //     if (res.resource_type === 'Audio') {
    //       url = `/pages/networkCourse/audio/index?mooc_course_id=${this.mooc_course_id}&task_id=${this.task_id}&from=${this.from}`
    //     }
    //   }

    //   // 作业
    //   if (res.act_type * 1 === 22) {
    //     url = `/pages/mooc/work/index?homework_id=${res.act_id}&task_id=${this.task_id}&act_id=${this.mooc_course_id}&from=${this.from}`
    //   }

    //   // 第三方
    //   if (res.act_type * 1 === 23) {
    //     url = `/pages/mooc/thirdPartyTasks/index?mooc_course_id=${this.mooc_course_id}&task_id=${this.task_id}&from=${this.from}`
    //   }

    //   if (url && this.from === 'spoc' && this.class_id) {
    //     url += `&class_id=${this.class_id}`
    //   }
    //   return url
    // },
    // 获取进度
    getProcess(flag = false) {
      // Toast.loading({ duration: 0, forbidClick: true, message: '加载中...' })
      let handleApi = ''
      let params = ''
      if (this.from === 'spoc') {
        handleApi = getCourseProcessSpoc
        params = {
          semesterId: this.mooc_course_id,
          classId: this.class_id
        }
      } else if (this.from === 'mooc') {
        handleApi = getCourseProcess
        params = { mooc_course_id: this.mooc_course_id }
        if (!this.isGeekBang && this.isPreview) {
          params.preview = 1
        }
      }
      handleApi(params)
        .then((res) => {
          // learn_status: 0 项目未开始时间，1 学员未开始状态 2 学员进行中状态 3 学员已完成状态 4 学员已逾期状态 5 项目已结束
          // switch (res.learn_status) {
          //   case 1:
          //     res.statusIcon = this.moocLang === 'en-us' ? 'en-icon-wkx' : 'icon-wks'
          //     break
          //   case 2:
          //     res.statusIcon = this.moocLang === 'en-us' ? 'en-icon-jxz' : 'icon-jxz'
          //     break
          //   case 3:
          //     res.statusIcon = this.moocLang === 'en-us' ? 'en-icon-ywc' : 'icon-ywc'
          //     break
          //   default:
          //     break
          // }
          this.courseProcess = {
            mooc_course_name: res.mooc_course_name || '',
            task_sum: res.task_sum || 0,
            requiredProcess: `${res.required_task_finish_count || 0}/${
              res.required_task_count || 0
            }`,
            noRequireProcess: `${res.non_required_task_finish_count || 0}/${
              res.non_required_task_count || 0
            }`,
            learn_status: res.learn_status,
            // statusIcon: res.statusIcon,
            unlocked_by_step: res.unlocked_by_step,
            enable_study_record_sync: res.enable_study_record_sync // true表示开启记录同步
          }
          this.getDetail(flag)
        }).catch(() => {
          // Toast.clear()
        })
    },
    // 点击培养项目按钮
    onProjectClick() {
      // this.getTask().then(() => {
      //   this.showProject = true
      // })
      this.showProject = !this.showProject
    },
    // 关闭项目弹窗
    closeProject() {
      this.showProject = false
    },
    // 打开任务列表
    showTaskList() {
      this.showTask = true
    },
    // 关闭任务列表
    closeTaskList() {
      this.showTask = false
    },
    // 过滤获取纯任务列表
    getTask() {
      let handleApi = ''
      let params = ''
      if (this.from === 'spoc') {
        handleApi = getTaskListSpoc
        params = {
          semesterId: this.mooc_course_id,
          classId: this.class_id
        }
      } else if (this.from === 'mooc') {
        handleApi = getTaskList
        params = {
          mooc_course_id: this.mooc_course_id
        }
        if (!this.isGeekBang && this.isPreview) {
          params.preview = 1
        }
      }
      handleApi(params).then((res) => {
        // 拿到数据递归遍历处理
        this.taskList = []
        const deepMap = (arr) => {
          arr.forEach((item) => {
            //   // 如果项目开启了顺序解锁模式，点击应学任务需判定之前是否有未完成的应学任务，如果存在则锁定任务，选修任务均不做锁定，不参与解锁的判定逻辑
            let lock
            if (
              this.courseProcess.unlocked_by_step &&
              item.task_type === 'task' &&
              item.required &&
              item.lock_status === 1
            ) {
              if (lock) {
                item.unlocked_by_step = true
              } else if (!item.is_finished) {
                // 如果上一个任务已解锁并且已完成, 则下一个解锁，反之
                lock = true
              }
            }

            if (item.task_type === 'task') {
              this.taskList.push(item)
            }
            if (item.sub_tasks) {
              deepMap(item.sub_tasks)
            }
          })
        }
        deepMap(res || [])
        // 获取当前任务的在纯任务列表的索引
        this.taskIndex = this.taskList.findIndex(
          (i) => i.task_id === this.task_id
        )
        if (
          this.taskIndex !== 0 &&
          (this.taskList[this.taskIndex - 1] ||
            JSON.stringify(this.taskList[this.taskIndex - 1]) !== '{}')
        ) {
          this.prevTaskBtnShow = true
        } else {
          this.prevTaskBtnShow = false
        }
        if (
          this.taskIndex !== this.taskList.length - 1 &&
          (this.taskList[this.taskIndex + 1] ||
            JSON.stringify(this.taskList[this.taskIndex + 1]) !== '{}')
        ) {
          this.nextTaskBtnShow = true
        } else {
          this.nextTaskBtnShow = false
        }
        this.$nextTick(() => {
          let iframeDOM = document.getElementById('taskIframe')
          if (iframeDOM) {
            SDKUtils.postMessage(iframeDOM, 'switchTaskBtnShow', { prevTaskBtnShow: this.prevTaskBtnShow, nextTaskBtnShow: this.nextTaskBtnShow })
          }
        })
      })
    },
    getSrc(act_type, act_id, resource_url_mobile, resource_type, resource_from, taskInfo) {
      const { task_id = '', mooc_course_id = '', resource_url } = taskInfo
      const link = {
        // 面授课
        1: `${process.env.VUE_APP_V8_HOST_WOA}/training/management/mobile/face-course?actId=${act_id}&taskId=${this.task_id}&classId=${this.class_id}&semesterId=${this.mooc_course_id}&from=${this.from}`,
        // 网课
        2:
          `/training/mobile/netcourse/play?course_id=${act_id}&from=${this.from}&taskId=${this.task_id}&mooc_course_id=${this.mooc_course_id}` +
          (window.__wxjs_environment === 'miniprogram' ? '&mini=true' : '') +
          (Number(this.$route.query.history) > 1 ? '&history=true' : '') + (this.isPreview ? '&moocPreview=1' : '') + '&type=taskContent',
        // 2: `${process.env.VUE_APP_V8_HOST_WOA}/training/mobile/netcourse/play?course_id=${act_id}&from=${this.from}&debugger=true`, // TODO:调试
        // 考试系统
        // 20: `${process.env.VUE_APP_EXAM}/exam-mob/exam?exam_id=${act_id}&from=mooc&lang=${this.moocLang}`, // 正确的
        20: `${process.env.VUE_APP_EXAM}/exam-mob/exam?exam_id=${act_id}&from=${this.from}&lang=${this.moocLang}&from_act_id=${this.task_id}&mooc_course_id=${this.mooc_course_id}&from_system=${this.from}&from_type=1&redirect_text=1&redirect_url=1`,
        // 20: `//test.woa.com:8080/exam-mob/exam?exam_id=${act_id}&from=mooc&lang=${this.moocLang}`, // TODO:调试
        // 素材
        21: `/training/mobile/material/play?material_id=${act_id}&from=${this.from}`,
        // 21: `${process.env.VUE_APP_V8_HOST_WOA}/training/mobile/material/play?material_id=${act_id}&from=mooc`
        // 作业
        // 22: `https://sdc.qq.com/s/mxkdgu?scheme_type=workDetail&homework_id=${act_id}&task_id=${task_id}&act_id=${this.mooc_course_id}&from=mooc`,
        // 腾讯问卷
        // 32: `https://oa.m.tencent.com/an:qlearning/mobile/survey?survey_id=${act_id}&from=${this.from}`,
        32: `${resource_url}?user_id=${this.userInfo.staff_id}&from=${this.from}&course_id=${mooc_course_id}&task_id=${task_id}`,
        // 活动
        98: `${process.env.VUE_APP_V8_HOST_WOA}/training/management/mobile/face-course?actId=${act_id}&taskId=${this.task_id}&classId=${this.class_id}&semesterId=${this.mooc_course_id}&from=${this.from}`
      }
      // 针对岗前任务 线上任务类型[18, 10, 2, 99]
      const preworkTaskTypes = [18, 10, 2, 99]
      if (resource_from === 'gangqian' && preworkTaskTypes.includes(act_type)) {
        return `${process.env.VUE_APP_V8_HOST_WOA}/prejob/mobile/user/course-detail?course_id=${act_id}&from=${this.from}`
      } else if ([1, 2, 20, 21, 32, 98].includes(act_type)) {
        if (resource_type === 'Scorm' || resource_type === 'Zip') {
          return ''
        } else {
          return link[act_type]
        }
      } else if ([99, 102].includes(act_type) && resource_url_mobile) {
        // 99-外链课程 102-极客时间
        if (act_type === 102) {
          Toast.loading({ duration: 0, forbidClick: true, message: '加载中...' })
          const from = this.$route.query?.from || ''
          const area_id = this.$route.query?.area_id || ''
          return this.preview ? resource_url_mobile + `&preview=true&from=${from}&area_id=${area_id}` : resource_url_mobile + `&from=${from}&area_id=${area_id}`
          // return `http://test.woa.com:8088/training/mobile/outsourcedCourse/iframe/play?course_id=${act_id}&debugger=true` // 临时调试
          // if (process.env.NODE_ENV === 'development') { // TODO:调试
          //   // let src = `http://test.woa.com:8088/training/mobile/outsourcedCourse/graphic/play?course_id=${act_id}`
          //   let src = `http://test.woa.com:8088/training/mobile/outsourcedCourse/video/play?course_id=${act_id}`
          //   return this.preview ? src + `&preview=true&from=${from}&area_id=${area_id}` : src + `&from=${from}&area_id=${area_id}`
          // } else {
          //   return this.preview ? resource_url_mobile + `&preview=true&from=${from}&area_id=${area_id}` : resource_url_mobile + `&from=${from}&area_id=${area_id}`
          // }
        }
        return resource_url_mobile
      } else {
        return ''
      }
    },
    // 切换任务
    async changeTask(num) {
      console.log(this.$route.query, '切换任务时候的路由参数（cyh)')
      const data = this.taskList[this.taskIndex + num]
      if (!data || JSON.stringify(data) === '{}') return
      // 判断是不是极客时间试读 试读名额判断
      if (data && this.preview && this.previewRecords.length >= 4 && !this.previewRecords.includes(data.act_id)) {
        // 名额不足弹窗
        this.openPop(4)
        return
      }
      toMoocDetailMob(data || {}, this.mooc_course_id, this.from, this.class_id, this.isVertical, this.moocLang, false, this.$route.query.moocPreview)
    },
    // 按钮跟随手指移动
    onTouchmove(event) {
      this.projectBtnTop = event.changedTouches[0].pageY - 20 + 'px'
    },
    onTouchmoveLang(event) {
      if (!this.isVertical) {
        this.langBtnTop = event.changedTouches[0].pageY - 20 + 'px'
      }
    },
    // 展开/收起外链课程的描述
    onDescExpand() {
      this.descExpand = !this.descExpand
    },
    initTimer() {
      let that = this
      const events = {
        // 学习时长回调函数
        getDurationSeconds: (seconds, durtation) => {
          // 本次学习时长
          that.seconds = seconds
          that.totalStudyTime =
            that.seconds + (that.taskData.total_study_time || 0)

          let finished_condition = that.taskData.finished_condition

          let leastLearnMinute = finished_condition?.condition || 0
          // 已完成任务不会再更新完成时间
          // 先判断当前任务是否已经完成，再判断完成条件是至少学习多少分钟，然后判断当前学习时间大于完成条件
          if (
            !that.taskFinishedState.is_finished &&
            finished_condition?.type * 1 === 2 &&
            that.totalStudyTime >= leastLearnMinute * 60
          ) {
            that.taskFinishedState = {
              is_finished: true,
              finished_time: that.$moment().format('YYYY-MM-DD HH:mm:ss')
            }
            that.handleSaveLearnRecord(true, {}, true)
          }

          // durtation 本轮定时器持续时间，定时器有暂停重新开启，则会清空（用于定时上报学习记录）
          if (durtation % 15 === 0) {
            // is_auto_report为true表示定时上报
            that.handleSaveLearnRecord(false, { is_auto_report: true })
          }
        },
        // 防挂机回调函数
        hangUpTimerCalback(e) {
          Dialog.alert({
            className:
              (this.isVertical ? 'vertical-dialog ' : '') + 'task-dialog',
            title: this.$langue('Mooc_Common_Alert_AntiHangUp', {
              defaultText: '防挂机学习提醒'
            }),
            confirmButtonText: this.$langue('Mooc_Common_Alert_ContinueStudy', {
              defaultText: '继续学习'
            }),
            confirmButtonColor: '#0052D9',
            cancelButtonText: this.$langue(
              'Mooc_ProjectDetail_Documents_ReturnProject',
              { defaultText: '返回项目' }
            ),
            showCancelButton: true,
            message: this.$langue('Mooc_Common_Alert_TriggerAntiHangUp', {
              defaultText:
                '您已触发防挂机验证，已自动停止当前任务的学习。如需继续学习，请点击“继续学习”按钮重新进入任务页面。'
            })
          })
            .then(() => {
              that.timer.start()
            })
            .catch(() => {
              // 返回项目详情
              that.toProjectDetail()
            })
        }
      }
      this.timer = new SDKTimer({
        // limtHangUpTime: 120, // 防挂机2分钟
        events: events
      })
    },
    onEvent() {
      SDKUtils.registerMessageListener()
      SDKUtils.onPlay(() => {
        this.timer.start()
      })
      SDKUtils.onPause(() => {
        this.timer.pause()
      })
      SDKUtils.onComplete((res) => {
        let isInit = res.data.params === 'init'
        this.completeLearn(isInit)
      })
      SDKUtils.onStratAnswer(() => {
        this.showProjectBtn = false
        this.timer.start()
      })
      SDKUtils.onErrorInfo((res) => {
        let query = {
          mooc_course_id: this.mooc_course_id,
          type: 'errorInfo',
          info: res.data.params,
          admin: JSON.stringify(this.taskData.mooc_course_admins_list),
          from: this.from
        }
        if (this.from === 'spoc') {
          query.class_id = this.class_id
        }
        this.$router.replace({
          name: 'taskAbnormal',
          query
        })
      })
      SDKUtils.onEndAnswer((res) => {
        let { params } = res.data
        let firstDone
        this.timer.pause()
        console.log(params, 'params')
        if (!params.init && params.is_finished) {
          firstDone = true
        }
        if (params.is_finished) {
          if (params.init && this.courseProcess.enable_study_record_sync && !this.taskFinishedState.is_finished) {
            firstDone = true
            const msg = ['Exam', 'Practice'].includes(
              this.taskData.resource_type
            )
              ? this.$langue('Mooc_Common_Alert_EnableSyncExam', {
                defaultText:
                    '培养项目已开启进度同步功能，您已通过其他途径完成当前任务关联的考试。'
              })
              : this.$langue('Mooc_Common_Alert_EnableSyncCourse', {
                defaultText:
                    '培养项目已开启进度同步功能，您已通过其他途径完成当前任务关联的课程。'
              })
            Dialog.alert({
              className:
                (this.isVertical ? 'vertical-dialog ' : '') + 'task-dialog',
              title: this.$langue('Mooc_Common_Alert_FinishTaskRemind', {
                defaultText: '任务完成提醒'
              }),
              confirmButtonText: this.$langue(
                'Mooc_ProjectDetail_Notice_IKnow',
                { defaultText: '知道了' }
              ),
              confirmButtonColor: '#0052D9',
              message: msg
            })
          }
          params.score = params.highScore // 考试通过后同步最高分，解决考试通过，分数未达到及格分显示分数问题
          this.taskFinishedState = {
            is_finished: true,
            finished_time: this.$moment().format('YYYY-MM-DD HH:mm:ss')
          }
        }
        // 先判断是否待批阅，再判断是否已通过
        const exam_status = params.waitView ? 2 : params.is_finished ? 1 : 0
        let data = {
          elapsed_seconds: params.elapsed_seconds || 0,
          is_cheat: params.is_cheat || false,
          is_finished: params.is_finished || false,
          score: params.score || 0,
          exam_status // 0-未通过, 1-通过,  2-待批阅
        }
        this.handleSaveLearnRecord(data.is_finished, data, firstDone)
        this.showProjectBtn = true
      })
      SDKUtils.onAnswerDetail(() => {
        this.showProjectBtn = false
      })
      SDKUtils.onDetailBackHome(() => {
        console.log('onDetailBackHome')
      })
      SDKUtils.messageListener((res) => {
        if (res.events === 'interactiveDialog') {
          this.interactiveDialog = res.params
        }
      })
    },
    // 通知第三方资源暂停播放
    setVideoPause() {
      let iframeDOM = document.getElementById('taskIframe')
      SDKUtils.setPause(iframeDOM)
    },
    // 通知第三方资源暂停播放
    setVideoPlay() {
      console.log('~~~~~~~~~~~~~~~iframe/play触发上报事件通知父组件更新显示时间~~~~~~~~~~~~~~~~~')
      let iframeDOM = document.getElementById('taskIframe')
      SDKUtils.setPlay(iframeDOM)
    },
    completeLearn(isInit) {
      if (
        !this.taskFinishedState.is_finished &&
        this.taskData.finished_condition?.type * 1 === 1
      ) {
        if (isInit && this.courseProcess.enable_study_record_sync) {
          Dialog.alert({
            className:
              (this.isVertical ? 'vertical-dialog ' : '') + 'task-dialog',
            title: this.$langue('Mooc_Common_Alert_FinishTaskRemind', {
              defaultText: '任务完成提醒'
            }),
            confirmButtonText: this.$langue('Mooc_ProjectDetail_Notice_IKnow', {
              defaultText: '知道了'
            }),
            confirmButtonColor: '#0052D9',
            message: this.$langue('Mooc_Common_Alert_EnableSyncCourse', {
              defaultText:
                '培养项目已开启进度同步功能，您已通过其他途径完成当前任务关联的课程。'
            })
          })
        }
        this.taskFinishedState = {
          is_finished: true,
          finished_time: this.$moment().format('YYYY-MM-DD HH:mm:ss')
        }
        this.handleSaveLearnRecord(true, {}, true)
      } else {
        this.handleSaveLearnRecord(false)
      }
    },
    handleSaveLearnRecord(flag, data, firstDone = false) {
      // 预览不做上报
      if (this.isPreview) return
      // flag: true表示上报以后需要更新列表数据
      let params = {}
      let handleApi = ''
      if (this.from === 'spoc') {
        handleApi = saveLearnRecordSpoc
        params = {
          id: this.recordId,
          semesterId: this.mooc_course_id,
          taskId: this.task_id,
          classId: this.class_id,
          isFinished: flag,
          actId: this.taskData.act_id,
          actType: this.taskData.act_type,
          elapsedSeconds: this.seconds,
          isAutoReport: data?.is_auto_report || false,
          source: 'SPOC'
        }
        // 考试系统数据
        if (data?.hasOwnProperty('score')) {
          params.isCheat = data.is_cheat
          params.score = data.score
          params.isFinished = data.is_finished
          params.elapsedSeconds = data.elapsed_seconds
        }
      } else if (this.from === 'mooc') {
        handleApi = saveLearnRecord
        params = {
          id: this.recordId,
          mooc_course_id: this.mooc_course_id,
          task_id: this.task_id,
          is_finished: flag,
          act_id: this.taskData.act_id,
          act_type: this.taskData.act_type,
          elapsed_seconds: this.seconds,
          is_auto_report: false,
          source: 'MOOC',
          ...data
        }
      }
      handleApi(params, this.class_id)
        .then((res) => {
          this.recordId = res.record_id || 0
          // 状态完成
          if (flag) {
            this.getProcess(flag)
            this.dateNum = new Date().getTime()
            if (this.isVertical) {
              // Toast({
              //   duration: 4000, // 持续展示 toast
              //   forbidClick: false,
              //   message: this.$langue(
              //     'Mooc_ProjectDetail_TaskList_TaskFinished',
              //     { defaultText: '任务已完成' }
              //   ),
              //   className: 'finished-Toast'
              // })
            } else {
              this.finishedToast = true
              let finishedToastTimer = setTimeout(() => {
                this.finishedToast = false
              }, 600 * 1000)
              this.$once('hook:beforeDestroy', () => {
                clearTimeout(finishedToastTimer)
              })
            }
          }
          if (
            res.all_finished &&
            !this.alreadyShowCertificate &&
            this.taskData.required
          ) {
            // 表示完成培养项目（最后一个应学任务完成）
            this.finishProject = {
              visible: true,
              certificate_name: res.certificate_name,
              certificate_id: res.certificate_id,
              enable_certificate: res.enable_grant_certificate
            }
            this.alreadyShowCertificate = true
          } else {
            if (flag && firstDone) {
              // 应学任务，并且不是文章类型才提示任务完成条件
              if (this.taskData.required && this.taskData.resource_type !== 'Article') {
                this.finishedToast = true
              }
              let finishedToastTimer = setTimeout(() => {
                this.finishedToast = false
              }, 600 * 1000)
              this.$once('hook:beforeDestroy', () => {
                clearTimeout(finishedToastTimer)
              })
            }
          }
          this.errNum = 0
          if (this.isFromMobileQuestion) {
            this.getTask()
            this.isFromMobileQuestion = false
          }
        })
        .catch((err) => {
          // if (err.message.indexOf('Network Error') > -1) {
          //   this.errNum++
          //   if (this.errNum >= 4) {
          //     this.timer.pause()
          //     this.setVideoPause()
          //     Dialog.alert({
          //       className: (this.isVertical ? 'vertical-dialog ' : '') + 'task-dialog',
          //       title: '服务异常提醒',
          //       confirmButtonText: this.$t('Mooc_Common_Alert_RefreshPage'),
          //       confirmButtonColor: '#0052D9',
          //       cancelButtonText: this.$t('Mooc_ProjectDetail_TrainingProgress_ContinueStudy'),
          //       showCancelButton: true,
          //       message: '当前服务异常，无法正常上报学习记录，请稍后刷新页面重试。如要继续学习，可能会丢失学习进度。'
          //     })
          //       .then(() => {
          //         location.reload()
          //       })
          //       .catch(() => {
          //         if (!this.interactiveDialog) {
          //           this.timer.start()
          //           this.setVideoPlay()
          //         }
          //         this.errNum = 0
          //       })
          //   }
          // }
          if (
            err.message.indexOf('timeout') > -1 ||
            err.message.indexOf('Network Error') > -1
          ) {
            this.errNum++
            if (this.errNum >= 6) {
              this.timer.pause()
              this.setVideoPause()
              Dialog.alert({
                className:
                  (this.isVertical ? 'vertical-dialog ' : '') + 'task-dialog',
                title: this.$langue('Mooc_Common_Alert_NetworkError', {
                  defaultText: '网络异常提醒'
                }),
                confirmButtonText: this.$t('Mooc_Common_Alert_RefreshPage'),
                confirmButtonColor: '#0052D9',
                cancelButtonText: this.$t(
                  'Mooc_ProjectDetail_TrainingProgress_ContinueStudy'
                ),
                showCancelButton: true,
                message: this.$langue('Mooc_Common_Alert_NetworkErrorWarm', {
                  defaultText:
                    '检测到网络异常，无法上报学习记录，请检查设备的网络连接状况，并在网络恢复正常稳定后刷新页面。如要继续学习，可能会丢失学习进度。'
                })
              })
                .then(() => {
                  location.reload()
                })
                .catch(() => {
                  if (!this.interactiveDialog) {
                    this.timer.start()
                    this.setVideoPlay()
                  }
                  this.errNum = 0
                })
            }
          }
          if (err.code === -14001 || err.code === -14002) {
            this.timer.pause()
            this.setVideoPause()
            Dialog.alert({
              className:
                (this.isVertical ? 'vertical-dialog ' : '') + 'task-dialog',
              title: this.$langue('Mooc_Common_Alert_MultipleTasksRemind', {
                defaultText: '多任务同时学习提醒'
              }),
              confirmButtonText: this.$langue(
                'Mooc_ProjectDetail_TrainingProgress_ContinueStudy',
                { defaultText: '继续学习' }
              ),
              confirmButtonColor: '#0052D9',
              cancelButtonText: this.$langue(
                'Mooc_ProjectDetail_Documents_ReturnProject',
                { defaultText: '返回项目' }
              ),
              showCancelButton: true,
              message: this.$langue(
                'Mooc_Common_Alert_NotSupportMultipleTasks',
                {
                  defaultText:
                    '培养项目不支持多任务同时学习，系统检测到您正在学习同一项目下的其他任务，已自动停止当前任务的学习。如需继续学习，请点击“继续学习”按钮重新进入任务页面。'
                }
              )
            })
              .then(() => {
                if (!this.interactiveDialog) {
                  this.timer.start()
                  this.setVideoPlay()
                }
              })
              .catch(() => {
                // 返回项目详情
                this.toProjectDetail()
              })
          }
        })
    },
    viewCertificate() {
      if (this.finishProject.enable_certificate) {
        const host =
          process.env.NODE_ENV === 'production'
            ? 'https://portal.learn.woa.com'
            : 'https://test-learn.woa.com'
        window.open(
          `${host}/mobile/certificate/preview?certificate_id=${this.finishProject.certificate_id}`
        )
      }
    },
    toProjectDetail() {
      console.log('this.from', this.from)
      if (this.from === 'mooc') {
        let url = `/pages/mooc/projectDetails/index?mooc_course_id=${this.mooc_course_id}`
        if (this.isPreview) {
          url += '&moocPreview=1'
        }
        console.log('url', url)
        window.wx.miniProgram.reLaunch({ url })
      } else if (this.from === 'spoc') {
        // 返回班级详情 返回课表页而不是 评价反馈页
        window.wx.miniProgram.reLaunch({
          url: `/pages/webview/spoc/coursePage?classId=${this.class_id}&semesterId=${this.mooc_course_id}`
        })
      }
      // else if (this.from === 'spoc' && this.taskData && this.taskData.resource_type === 'Survey') {
      //   window.wx.miniProgram.reLaunch({
      //     url: `/pages/webview/spoc/evaluateList?classId=${this.class_id}`
      //   })
      // } else if (this.from === 'spoc') {
      //   window.wx.miniProgram.reLaunch({
      //     url: `/pages/webview/spoc/evaluateList?classId=${this.class_id}&taskLink=1`
      //   })
      // }
    },
    // 双语切换
    changeLang() {
      let lang = this.moocLang === 'en-us' ? 'zh-cn' : 'en-us'
      this.$store.commit('setMoocLang', lang)
      this.getMobileLangJS()
      // 向小程序传参
      window.wx.miniProgram.postMessage({
        data: {
          data: lang,
          type: 'lang'
        }
      })
      console.log('多语切换')
      this.$nextTick(() => {
        let iframeDOM = this.getIframeDom()
        SDKUtils.postMessage(iframeDOM, 'tencent-mooc-lang', lang)
      })
    },
    getIframeDom() {
      return document.getElementById('taskIframe')
    }
  }
}
</script>
<style>
.finished-Toast {
  transform: rotate(90deg);
  top: 40%;
  left: 40%;
}
</style>
<style lang="less">
#app {
  height: 100% !important;
}
.task-dialog {
  border-radius: 4px;
  .van-dialog__header {
    padding-top: 32px;
    font-weight: bold;
    color: #000000e6;
  }
  .van-dialog__message {
    padding-bottom: 32px;
    font-size: 16px;
    color: #00000099;
  }
  .van-button {
    height: 56px;
    font-weight: bold;
  }
}
.finish-dialog {
  text-align: center;
  border-radius: 8px;
  font-size: 14px;
  .van-dialog__content {
    padding: 32px 0;
  }
  .img {
    width: 42px;
    height: 42px;
    margin-bottom: 12px;
  }
  .certificate-info {
    margin-top: 12px;
    color: #ed7b2f;
  }
  .certificate-tips {
    margin-bottom: 12px;
    display: inline-block;
    margin-left: 24px;
    position: relative;
    &::before {
      content: '';
      width: 20px;
      height: 20px;
      background: url('~@/assets/mooc-img/warning-icon.png') no-repeat center /
        cover;
      position: absolute;
      top: -2px;
      left: -24px;
    }
  }
}
.finished-condition-tips {
  padding: 16px 32px;
  color: #ffffff;
  text-align: center;
  line-height: 22px;
  border-radius: 12px;
  .min-study-time {
    color: #ed7b2f;
    margin-top: 10px;
  }
  img {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 18px;
    height: 18px;
  }
  .tips {
    white-space: nowrap;
    margin-top: 2px;
  }
  border-radius: 4px;
  background: var(--font-gy-260, #00000099);
}
</style>

<style lang="less" scoped>
.task-content {
  height: 100%;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  .safe-area-inset-bottom,
  .en-safe-area-inset-bottom {
    padding-bottom: constant(safe-area-inset-bottom); /* 兼容 iOS < 11.2 */
    padding-bottom: env(safe-area-inset-bottom); /* 兼容 iOS >= 11.2 */
  }
  .safe-area-inset-bottom {
    justify-content: space-between;
    align-items: center;
    display: flex;
  }
  .task-iframe {
    width: 100%;
    flex: 1;
    background: #fff;
  }
  .project-btn {
    position: fixed;
    z-index: 999999;
  }
  .project-btn {
    padding-left: 8px;
    // width: 60px;
    height: 40px;
    right: 0;
    // bottom: 160px;
    border: 1px solid #dcdcdcff;
    border-radius: 20px 0 0 20px;
    background: #fff;
    display: flex;
    align-items: center;
    .img {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }
    .text {
      //   width: 20px;
      white-space: pre-wrap;
      color: #000000e6;
      font-size: 10px;
      padding-right: 8px;
    }
  }
  .task-desc {
    position: relative;
    padding: 12px;
    box-sizing: border-box;
    background: #ffffffff;
    box-shadow: 0 0 8px 0 #eeeeee99;
    font-size: 12px;
    line-height: 20px;
    .desc {
      color: #000000e6;
      word-break: break-all;
    }
    .overflow-l2 {
      text-overflow: -o-ellipsis-lastline;
      overflow: hidden; //溢出内容隐藏
      text-overflow: ellipsis; //文本溢出部分用省略号表示
      display: -webkit-box; //特别显示模式
      -webkit-line-clamp: 2; //行数
      line-clamp: 2;
      -webkit-box-orient: vertical; //盒子中内容竖直排列
    }
    .expand {
      position: absolute;
      right: 12px;
      bottom: 12px;
      background-color: #fff;
      .ellipsis {
        margin-right: 6px;
        color: #000000e6;
      }
      .text {
        color: #0052d9;
      }
    }
  }
  .condition-popup {
    bottom: 0;
    min-height: 30px;
    padding: 8px 16px;
    box-sizing: content-box;
    background: #fff;
    box-shadow: 0 -1px 0 0 #eeeeeeff;
    font-size: 12px;
    // display: flex;
    // flex-direction:column;
    .span {
      line-height: 20px;
    }
    .condition-label {
      color: #000000e6;
    }
    .condition-detail {
      color: #00000099;
    }
    .study-time {
      height: 20px;
      padding: 0 8px;
      // margin-left: 12px;
      border-radius: 2px;
      vertical-align: middle;
      background: #ebeffcff;
      display: inline-block;
      // margin-top: 8px;
      .study-label {
        color: #00000099;
      }
      .study-detail {
        margin-left: 4px;
        color: #0052d9;
      }
    }
    .img {
      margin-right: 4px;
      width: 16px;
      height: 16px;
    }
    .complete-label {
      color: #0ad0b6ff;
    }
    .complete-time-label {
      margin-left: 12px;
      color: #00000099;
      white-space: break-spaces;
    }

    .complete-time {
      color: #000000e6;
    }
  }
  .compelete-conditon-box {
    min-height: 30px;
    display: flex;
    align-items: center;
  }
  :deep(.project-popup),
  :deep(.task-popup) {
    border-radius: 8px 8px 0 0;
    padding-bottom: 16px;
    .title {
      height: 44px;
      line-height: 44px;
      border-bottom: 1px solid #eeeeeeff;
      color: #000000e6;
      font-size: 16px;
      font-weight: bold;
      text-align: center;
      padding: 0 44px 0 16px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      position: relative;
      .icon {
        position: absolute;
        width: 24px;
        height: 24px;
        cursor: pointer;
        top: 10px;
        right: 16px;
      }
    }
  }
  :deep(.project-popup.project-popup-pad-fullscreen) {
    transform: none;
  }
  :deep(.project-popup) {
    .task-box {
      padding: 16px 16px 8px;
      box-shadow: 0 1px 0 0 #eeeeeeff;
      .task-detail {
        height: 22px;
        color: #000000e6;
        font-weight: bold;
        line-height: 22px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap; // 默认不换行；
        .type {
          float: left;
          margin-top: 2px;
          margin-right: 8px;
          height: 18px;
          line-height: 18px;
          padding: 0 4px;
          border-radius: 2px;
          font-size: 12px;
          font-weight: normal;
        }
        .required {
          background: #fdf6ecff;
          color: #ff7548;
        }
        .unrequired {
          background: #ccf2e2ff;
          color: #00b368ff;
        }
      }

      .condition {
        margin-top: 8px;
        line-height: 14px;
        font-size: 12px;
        .label {
          color: #00000099;
        }
        .text {
          color: #000000e6;
        }
      }
    }
    .schedule {
      padding: 4px 9px 0 16px;
      .schedule-detail {
        height: 32px;
        line-height: 32px;
        margin-bottom: 11px;
        color: #00000099;
        font-size: 12px;
        .required-schedule {
          margin-right: 32px;
        }
        .icon {
          float: right;
          width: 32px;
          height: 32px;
          opacity: 0.5;
        }
        .icon-jxz {
          background: url('~@/assets/mooc-img/status-jxz.png') no-repeat center /
            cover;
        }
        .en-icon-jxz {
          // 英文
          background: url('~@/assets/mooc-img/en-status-jxz.png') no-repeat
            center / cover;
        }
        .icon-wks {
          background: url('~@/assets/mooc-img/status-wks.png') no-repeat center /
            cover;
        }
        .en-icon-wkx {
          // 英文
          background: url('~@/assets/mooc-img/en-status-wks.png') no-repeat
            center / cover;
        }
        .icon-ywc {
          background: url('~@/assets/mooc-img/status-ywc.png') no-repeat center /
            cover;
        }
        .en-icon-ywc {
          // 英文
          background: url('~@/assets/mooc-img/en-status-ywc.png') no-repeat
            center / cover;
        }
      }
      .handle-box {
        padding: 0 3px;
        text-align: center;
        .handle-item {
          display: inline-block;
          width: 72px;
          margin: 0 8px;
          text-align: center;
          &:first-of-type {
            margin-left: 0;
          }
          &:last-of-type {
            margin-right: 0;
          }
          .handle-btn {
            display: inline-block;
            padding-top: 10px;
            width: 40px;
            height: 40px;
            box-sizing: border-box;
            border-radius: 20px;
            background: #f3f3f3ff;
            .icon {
              width: 20px;
              height: 20px;
            }
          }
          .handle-text {
            margin-top: 8px;
            color: #00000099;
            font-size: 12px;
            line-height: 20px;
          }
        }
        .back-item {
          width: 79px;
        }
        .next-item {
          .handle-btn {
            background: #0052d9ff;
          }
        }
      }
    }
  }

  .span {
    display: inline-block;
  }
}
.vertical-style {
  .task-icon {
    width: 36px;
    height: 36px;
    border-radius: 36px;
    background: #fff;
    box-shadow: 0 0 12px 0 #99999999;
    position: fixed;
    top: 16px;
    right: 126px;
    z-index: 9999;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    .icon {
      width: 20px;
      height: 18px;
      transform: rotate(90deg);
    }
  }
  .task-icon-pad-fullscreen {
    left: 16px;
    top: 126px;
    .icon {
      transform: none;
    }
  }
  .project-popup,
  .task-popup {
    width: 100vw;
    height: 100vw;
    border-radius: 0;
    transform-origin: 0 0;
    transform: translateX(100vw) rotate(90deg);
    display: flex;
    flex-direction: column;
    padding-bottom: 0;
  }
  .project-popup {
    .schedule {
      flex: 1;
      position: relative;
      .handle-box {
        position: absolute;
        bottom: 34px;
        width: calc(100% - 25px);
      }
    }
  }
  .task-popup {
    .task-list {
      flex: 1;
    }
  }
}
.drag-taskDetail-lang {
  width: 60px;
  height: 40px;
  padding-left: 8px;
  background: #fff;
  border: 1px solid #dcdcdc;
  border-radius: 20px 0 0 20px;
  border-right-color: transparent;
  box-shadow: 0 0 24px 0 #dcdcdc99;
  position: fixed;
  right: 0;
  z-index: 999999;
  font-size: 10px;
  display: flex;
  align-items: center;
  user-select: none;
  .el-icon-en {
    background: url('~@/assets/img/english.png') no-repeat center / cover;
  }
  .el-icon-zh {
    background: url('~@/assets/img/china.png') no-repeat center / cover;
  }
  .icon {
    width: 20px;
    height: 20px;
    margin-right: 4px;
    display: inline-block;
  }
}
.lang-icon-vertical {
  top: 16px;
  right: 196px;
  z-index: 9999;
  padding: 0;
  width: 36px;
  height: 36px;
  justify-content: center;
  border-radius: 36px;
  background: #fff;
  box-shadow: 0 0 12px 0 #99999999;
  text-align: center;
  .icon {
    margin: 0;
    width: 20px;
    height: 20px;
    transform: rotate(90deg);
  }
  .text {
    display: none;
  }
}
.finished-Toast {
  text-align: center;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 14px;
  line-height: 20px;
  background-color: #010101;
  padding: 16px 24px;
  border-radius: 6px;
  width: 185px;
  > img {
    position: absolute;
    top: 7px;
    right: 7px;
    width: 20px;
    height: 20px;
  }
  .done-tips {
    line-height: 22px;
  }
  .next-task {
    margin: 10px 0 0 0;
    color: #ed7b2f;
    line-height: 22px;
    text-decoration: underline;
  }
}
.horizontal-toast {
  transform: translate(-50%, -50%) rotate(90deg);
  top: 50%;
  left: 50%;
}
</style>
