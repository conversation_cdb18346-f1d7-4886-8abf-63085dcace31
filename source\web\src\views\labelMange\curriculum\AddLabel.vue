<template>
  <div>
    <el-dialog :title="itemData !== '' ? '编辑标签' : '新建标签'" :visible.sync="visible" width="30%" :before-close="handleClose"
      :close-on-click-modal="click_modal" @open="getlist">
      <div class="tabtop">
        <div class="labelname">
          <p class="name_p">标签名称</p>
          <el-tooltip class="item" effect="dark" placement="bottom-start" content="1.单个标签字符建议最多不超过50字符(25个汉字/50个英文字母或数字)">
            <i class="el-icon-warning-outline warring"></i>
          </el-tooltip>
        </div>
        <el-input size="medium" type="text" placeholder="请输入内容" v-model="label_name"
          @input="checkNickName"
          class="input-label">
          <template slot="suffix">
            <div style="height: 36px;line-height: 36px;">{{ label_name ? handleValidor(label_name, 25) : 0 }}/25</div>
          </template>
        </el-input>
        <span class="labelnameerror" style="color: red; display: block; margin-top: 10px" v-show="nickFlag">{{ errorMsg
        }}</span>
        <div class="dalbottom">
          <p>标签分类</p>
          <div class="block">
            <el-cascader v-model="labelchange" :options="optionslist" :props="{ emitPath: false }"
              clearable></el-cascader>
          </div>
        </div>
        <div class="dalbottom">
          <p>标签类型</p>
          <el-select
            clearable
            v-model="labeltypes"
            placeholder="请选择标签类型"
            class="block"
            style="width: 100%;"
          >
            <el-option
              v-for="item in options2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>

      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="out">取 消</el-button>
        <el-button type="primary" @click="dialogVisible()" :disabled="disabledbtn">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { add_or_update_label } from 'config/api.conf'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    itemData: {},
    optionslist: []
  },
  data() {
    return {
      props: { multiple: true },
      options: [],
      click_modal: false,
      label_name: '',
      labelchange: '',
      labeltypes: '',
      options2: [
        {
          value: '1',
          label: '官方'
        },
        {
          value: '2',
          label: '用户'
        },
        {
          value: '3',
          label: '机器'
        },
        {
          value: '4',
          label: '外采'
        }
      ],
      disabledbtn: false,
      nickFlag: false,
      errorMsg: ''
    }
  },
  methods: {
    getlist() {
      this.label_name = this.itemData.label_name
      this.labelchange = this.itemData.category_id
      if (this.itemData.label_type) {
        this.labeltypes = this.options2.find(option => option.value === this.itemData.label_type.toString()).value
      }
    },
    // 检查备注名
    checkNickName() {
      let reg = /^[A-Za-z0-9-+#&\\/()（）\u4e00-\u9fa5\s]{0,1000}$/ // 中文，数字，字母，下划线
      // this.computedStrLen(this.remarkName)
      if (!reg.test(this.label_name) && this.label_name !== '') {
        this.errorMsg = '备注名称中不能包含特殊符号'
        this.nickFlag = true
        this.disabledbtn = true
      } else {
        if (this.handleValidor(this.label_name) > 25) {
          this.errorMsg = '最多输入50个字符(1个汉字2个字符)'
          this.nickFlag = true
          this.disabledbtn = true
        } else {
          this.nickFlag = false
          this.disabledbtn = false
        }
      }
    },
    handleValidor(value, num) {
      if (value) {
        const china = value.match(/[\u4e00-\u9fa5]/g)
        const zhCount = china && china.join('').length
        const enCount = Math.ceil((value.length - zhCount) / 2)
        const total = zhCount + enCount
        if (total > num) {
          // this.label_name = value.slice(0, -1)
        }
        return total || 0
      }
      return 0
    },
    // computedStrLen(str) {
    //   var len = 0
    //   for (var i = 0; i < str.length; i++) {
    //     var c = str.charCodeAt(i)
    //     // 单字节加1
    //     if ((c >= 0x0001 && c <= 0x007e) || (c >= 0xff60 && c <= 0xff9f)) {
    //       len++
    //     } else {
    //       len += 2
    //     }
    //   }
    //   return len
    // },
    handleClose() {
      this.nickFlag = false
      this.disabledbtn = false
      this.$emit('update:visible', false)
    },
    dialogVisible() {
      let params =
        this.itemData === ''
          ? {
            label_id: this.itemData.label_id || '',
            label_name: this.label_name,
            category_id: this.labelchange,
            label_type: this.labeltypes
          }
          : {
            label_id: this.itemData.label_id || '',
            label_name: this.label_name,
            category_id: this.labelchange,
            label_type: this.labeltypes
          }
      add_or_update_label(params).then((res) => {
        this.$message({
          type: 'success',
          message: this.itemData.label_id ? '修改成功' : '创建成功'
        })
        this.labeltypes = ''
        this.$parent.getlist()
        this.$emit('update:visible', false)
      })
    },
    out() {
      this.labeltypes = ''
      this.label_name = ''
      this.labelchange = ''
      this.$emit('update:visible', false)
      this.disabledbtn = false
      this.nickFlag = false
    }
  }
}
</script>
<style lang="less" scoped>
.input-label{
   :deep .el-input__inner{
    padding-right: 45px;
   }
}
.tabtop {
  .labelname {
    margin-bottom: 18px;
    display: flex;

    .name_p {
      display: inline-block;
      color: #000;
      line-height: 20px;
      font-size: 16px;
    }

    .warring {
      line-height: 20px;
      margin-left: 8px;
    }
  }

  .dalbottom {
    margin-top: 20px;

    p {
      color: #000;
      line-height: 20px;
      font-size: 16px;
      margin-bottom: 18px;
    }

    .el-cascader {
      width: 100%;
    }
  }
}
</style>
