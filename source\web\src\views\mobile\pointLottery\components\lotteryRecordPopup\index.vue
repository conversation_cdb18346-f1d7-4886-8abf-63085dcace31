<template>
  <div class="activity-rules-popup">
    <van-popup v-model="popupShow" round position="bottom" :overlay-style="{ background: 'rgba(0, 0, 0, 0.4)'}">
      <div class="popup-card">
        <div class="popup-head">
          <img class="go-back" v-if="showType === 'details'" @click="goBack" src="../../../../../assets/img/mobile/go-back.png" alt="" srcset="">
          <div class="title">抽奖记录{{ showType === 'details' ? '详情' : '' }}</div>
          <img class="close" @click="onClose" src="../../../../../assets/img/mobile/lottery/close.png" alt="" srcset="">
        </div>
        <div class="popup-body">
          <!-- 抽奖记录集 -->
          <record-list v-show="showType === 'list'" @prizeDetails="openPrizeDetails"></record-list>
          <!-- 抽奖记录详情 -->
          <lottery-details v-if="showType === 'details'" :order-id="orderId"></lottery-details>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { 
  Popup
} from 'vant'
import list from './list.vue'
import lotteryDetails from './lotteryDetails.vue'
export default {
  props: {
    value: {
      type: Boolean,
      default: true,
      require: true
    }
  },
  components: {
    [Popup.name]: Popup,
    recordList: list,
    lotteryDetails
  },
  data() {
    return {
      popupShow: this.value,
      showType: 'list',
      orderId: ''
    }
  },
  mounted() {
  },
  methods: {
    // 关闭弹窗
    onClose() {
      this.popupShow = false
      this.$emit('input', this.popupShow)
    },
    // 去学习
    openPrizeDetails(e) {
      console.log(e)
      if (e.goods_type === -1) return
      this.orderId = e.goods_order_id
      this.showType = 'details'
    },
    // 返回记录列表
    goBack() {
      this.showType = 'list'
    }
  },
  beforeDestroy() {
  }
}
</script>

<style lang='less' scoped>
  .popup-card {
    width: 100vw;
    height: 90vh;
    letter-spacing: 0.5px;
    color: rgba(16, 16, 16, 1);
    display: flex;
    flex-direction: column;
    letter-spacing: 0.5px;
    .popup-head {
      padding: 16px;
      position: relative;
      flex-shrink: 0;
      .title {
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px;
        font-family:  "PingFang SC";
        text-align: center;
      }
      .close {
        width: 24px;
        height: 24px;
        position: absolute;
        top: 17px;
        right: 16px;
      }
      .go-back {
        width: 24px;
        height: 24px;
        position: absolute;
        top: 17px;
        left: 16px;
      }
    }
    .popup-body {
      flex: 1;
      overflow-y: auto;
      color: #000000e6;
      position: relative;
    }
  }
</style>
