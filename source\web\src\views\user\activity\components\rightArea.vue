<template>
  <div class="right">
    <!-- 课单 -->
    <courseCard v-if="isShowCourse" :courseData="courseData" dtPageType="活动详情页-新版"></courseCard>
    <!-- 专区 -->
    <specialCard v-if="isSpecialArea" :courseData="courseData" dtPageType="活动详情页-新版"></specialCard>
    <!-- 推广 -->
    <!-- <advertisingCard actType="1" :isShowRecommend="courseData.is_show_recommend" :courseData="courseData"></advertisingCard> -->
    <!-- 延伸学习 课前/课后学习 -->
    <extandCard class="extand-card" v-if="extandCardData.length" :tabs="extandCardTabs" :commonList="extandCardData" :courseData="courseData" @switchTabs="switchTabs" dtPageType="活动详情页-新版"></extandCard>
    <!-- 猜你喜欢 -->
    <card v-if="guessLikeList.length" dtPageType="活动详情页-新版" :commonList="guessLikeList" :courseData="courseData" :commonTitle="$langue('NetCourse_GuessYouLike', { defaultText: '猜你喜欢' })" type="NetCourse_GuessYouLike"></card>
    <!-- 相关推荐 -->
    <card v-if="recommendList.length" dtPageType="活动详情页-新版" :commonList="recommendList" :courseData="courseData" :commonTitle="$langue('NetCourse_RelatedRecommendations', { defaultText: '相关推荐' })" type="NetCourse_RelatedRecommendations"></card>
  </div>
</template>
<script>
import card from '../../netcourse/grayPlay/components/card.vue'
import courseCard from '../../netcourse/grayPlay/components/courseCard'
import specialCard from '../../netcourse/grayPlay/components/specialCard.vue'
import extandCard from '../../netcourse/grayPlay/components/extandCard.vue'
// import advertisingCard from '@/views/user/components/advertisingCard.vue'
import {
  // getExtanContentList,
  getRecommendList,
  guessLikeAPI
} from 'config/api.conf'
import { getRelationContentsApi } from '@/config/classroom.api.conf.js'

import { mapState } from 'vuex'
export default {
  components: {
    card,
    courseCard,
    specialCard,
    extandCard
  },
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      actType: 4, // 活动
      extandTabs: [
        { label: '全部', value: 'all', listKey: '', show: false },
        { label: '课前学习', value: 'preCourse', listKey: 'preCourseRecList', show: false },
        { label: '课后学习', value: 'postCourse', listKey: 'postCourseRecList', show: false }
      ],
      extandTabsKey: 'all',
      // extandList: [],
      preCourseRecList: [],
      postCourseRecList: [],
      recommendList: [],
      guessLikeList: []
    }
  },
  computed: {
    ...mapState(['isBusy']),
    activity_id() {
      return this.$route.query.activity_id ? parseInt(this.$route.query.activity_id) : ''
    },
    isShowCourse() {
      return this.$route.query.area_id && this.$route.query.from === 'CourseList'
    },
    isSpecialArea() {
      // 490是首页不显示
      return this.$route.query.from === 'SpecialArea' && this.$route.query.area_id && this.$route.query.area_id !== '490'
    },
    extandCardTabs() {
      const preCourseNum = this.preCourseRecList.length || 0
      const postCourseNum = this.postCourseRecList.length || 0
      if (preCourseNum > 0 && postCourseNum > 0) {
        return this.extandTabs.map(tab => ({
          ...tab,
          show: true
        }))
      }
      return []
    },
    extandCardData() {
      const tabsMap = {
        all: [...this.preCourseRecList, ...this.postCourseRecList],
        preCourse: this.preCourseRecList,
        postCourse: this.postCourseRecList
      }
      return tabsMap[this.extandTabsKey] || []
    }
  },
  watch: {
    courseData: {
      handler(val) {
        if (val?.created_at) {
          this.getConRecommendList()
        }
      }
    }
  },
  mounted() {
    // this.getExtandList()
    this.getGuessLike()
    this.getRelationContentsList()
  },
  methods: {
    // 猜你喜欢
    getGuessLike() {
      const params = {
        app_id: 'A9BiosXihR0h46ThNsAX',
        size: 10,
        current: 1
      }
      guessLikeAPI(params).then((res) => {
        this.guessLikeList = (res && res.records) || []
        this.guessLikeList = this.guessLikeList.slice(0, 5)
      })
    },
    // 延伸学习 - 
    // getExtandList() {
    //   const params = {
    //     act_id: this.activity_id,
    //     act_type: 4
    //   }
    //   getExtanContentList(params).then((data) => {
    //     this.extandList = data || []
    //   })
    // },
    switchTabs(val) {
      this.extandTabsKey = val.value
    },
    // 课前/课后学习列表
    getRelationContentsList() {
      const params = {
        activity_id: this.activity_id,
        relation_usage_type: ''
      }
      getRelationContentsApi(params).then((data = []) => {
        const list = (data || []).map(v => {
          let currentCourse = false
          if (Number(v.item_id) === this.activity_id && v.act_type === this.act_type) currentCourse = true
          return {
            ...v,
            currentCourse
          }
        })
        this.preCourseRecList = (list || []).filter(item => item.relation_usage_type === 1)
        this.postCourseRecList = (list || []).filter(item => item.relation_usage_type === 2)
        if (this.postCourseRecList.length > 0 && this.preCourseRecList.length > 0) {
          this.extandTabsKey = 'all'
        } else if (this.preCourseRecList.length > 0) {
          this.extandTabsKey = 'preCourse'
        } else if (this.postCourseRecList.length > 0) {
          this.extandTabsKey = 'postCourse'
        }
      })
    },
    // 相关推荐
    async getConRecommendList() {
      if (this.isBusy !== '1') {
        // 当天创建的数据凌晨12点之前，不请求数据
        let curDateLastTime = new Date().setHours(0, 0, 0, 0)
        let createTimeS = this.courseData?.created_at ? `${this.courseData.created_at.split(' ')[0]} 23:59:59` : ''
        const createTime = new Date(createTimeS).getTime()
        if (curDateLastTime > createTime) {
          const params = {
            module_id: 4,
            item_id: this.activity_id
          }
          getRecommendList(params).then((data) => {
            this.recommendList = data || []
            if (this.recommendList.length > 5) {
              this.recommendList = this.recommendList.splice(0, 5)
            }
          })
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
  .right {
    width: 400px;
    margin-left: 20px;
    :deep(.extand-card) {
      .common-content {
        height: 300px;
      }
    }
    .common-card + .common-card {
      margin-top: 20px;
    }
  }
</style>
