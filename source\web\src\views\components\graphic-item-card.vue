<template>
  <div>
    <ul class="item-card">
      <li class="item-card-content">
        <el-image
          lazy
          fit="fill"
          :src="
            item.cover_image_id
              ? item.cover_image_id
              : require('@/assets/img/default_bg_img.png')
          "
          @click="handleCommand('go')"
          class="item-image" :class="{'pointer-default': item.graphic_status !== 1}"
        >
        <div class="image-slot" slot="error">
          <img :src="require('@/assets/img/default_bg_img.png')" alt="" />
        </div>
        </el-image>
        <div class="text">
          <p class="video-title" :class="{'pointer-default': item.graphic_status !== 1}" @click="handleCommand('go')">
            <span class="article-tag">文章</span>
            <span
              >{{ item.graphic_name
              }}<span class="jing" v-if="item.excellent_status === 1"
                >精</span
              ></span
            >
          </p>
          <div class="bottom-icon">
            <div class="bottom-left">
              <span class="icon-box">
                <span class="watch-icon icon"></span>{{ item.view_count }}</span>
              <span class="icon-box">
                <span class="like-icon icon"></span>{{ item.praise_count }}
              </span>
              <span class="icon-box">
                <span class="collect-icon icon"></span
                >{{ item.favorite_count }}</span
              >
              <span class="icon-box"> {{ item.graphic_number }}个字</span>
              <span class="icon-box">{{ timeToDate(item.creator_at) }}</span>
               <!-- 0-待审核；1-通过审核；2-审核不通过 -->
              <span class="tool-tips-box" v-if='[0, 2].includes(item.info_sec_status)'>
                <span  :class="['icon-box', item.info_sec_status === 0 ? 'deal-color' : 'no-pass-color']">{{ item.info_sec_status === 0 ? '待审核' : '审核不通过' }}</span>
                <el-tooltip popper-class="safe-tool"  :content="secContent(item.info_sec_status)" placement="top-end">
                  <i class="el-icon-warning-outline"></i>
                </el-tooltip>
              </span>
              <span v-else :class="[getStatusColor(item.graphic_status), 'icon-box']">{{ status_map[item.graphic_status] }}</span> 
              <el-tooltip v-if="item.approve_status === 8 && item.approve_remark" class="item" effect="dark" :content="item.approve_remark" placement="bottom-start">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>

            </div>
            <!-- <div class="bottom-right">
              <span class="other-icon-box" @click="handleShow('edit')">
                <span :class="[{ 'stop-edit-icon': false }, 'right-icon', 'edit-icon']"></span>
              </span>
              <span class="other-icon-box" @click="handleShow('share')">
                <span :class="[{ 'stop-share-icon': false }, 'right-icon', 'share-icon']"></span>
              </span>
              <el-dropdown @command="handleCommand($event)" placement="bottom-start">
                <span class="other-icon-box">
                  <span :class="['right-icon', 'other-icon']"></span>
                </span>
                <el-dropdown-menu class="opreate-dropdown" slot="dropdown">
                  <el-dropdown-item command="addCourse">添加到课单</el-dropdown-item>
                  <el-dropdown-item command="del">删除</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div> -->
            <div :class="[{'right-not-allow': delStatus(item)}, 'bottom-right']">
              <!-- 在用草稿审核未通过显示编辑 -->
              <el-tooltip class="item" effect="dark" content="编辑" placement="top-start">
                <span :class="[{'disabled-icon':![2].includes(item.info_sec_status) && ![1, 3, 4].includes(item.graphic_status)},'other-icon-box']" @click="handleShow('edit')"> <span class="right-icon edit-icon"></span></span>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="分享" placement="top-start">
                <span :class="[{'disabled-icon': [3, 4, 6].includes(item.graphic_status) || [2].includes(item.info_sec_status)},'other-icon-box']"  @click="handleShow('share')"> <span class="right-icon share-icon"></span></span>
              </el-tooltip>
              <el-tooltip v-if="[3].includes(item.graphic_status)" class="item" effect="dark" content="上架" placement="top-start">
                <span class="other-icon-box"  @click="handleShow('on')"> <span class="right-icon up-icon"></span></span>
              </el-tooltip>
              <el-tooltip v-else class="item" effect="dark" content="下架" placement="top-start">
                <span :class="[{'disabled-icon': [3, 4].includes(item.graphic_status) || [2].includes(item.info_sec_status)},'other-icon-box']"  @click="handleShow('up')"> <span class="right-icon down-icon"></span></span>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="添加到课单" placement="top-start">
                <span :class="[{'disabled-icon': [3, 4].includes(item.graphic_status) || [2].includes(item.info_sec_status)},'other-icon-box']"  @click="handleCommand('addCourse')"> <span class="right-icon add-icon"></span></span>
              </el-tooltip>
              <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
                <span :class="[{'disabled-icon': ![3, 4, 6].includes(item.graphic_status)} && ![2].includes(item.info_sec_status),'other-icon-box']"  @click="handleCommand('del')"> <span class="right-icon del-icon"></span></span>
              </el-tooltip>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
import { timeToDate, handleImgUrl } from 'utils/tools'
export default {
  name: 'listitemcard',
  props: {
    item: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      timeToDate: timeToDate,
      handleImgUrl: handleImgUrl,
      status_map: {
        1: '在用',
        3: '下架',
        4: '草稿',
        6: '待审核'
      }
    }
  },
  computed: {
    secContent() {
      return (val) => {
        return val === 0 ? '请留意企微“小腾老师”机器人消息提醒。如有疑问，可联系graywu。' : '提交信息包含敏感内容，请仔细仔细检查并重新修改后提交。如有疑问，可联系graywu。'
      }
    },
    delStatus() {
      return (v) => {
        if ([0].includes(v.info_sec_status)) { // 待审核禁止
          return true
        }
        if (v.info_sec_status === 2) { // 审核不通过
          return false // 不禁止
        }
        if ([6].includes(v.graphic_status)) {
          return true
        }
      }
    }
  },
  methods: {
    handleShow(evt) {
      this.$emit('handleEvent', { evt: evt, data: this.item })
    },
    handleCommand(evt) {
      if (evt === 'go' && this.item.graphic_status !== 1) return
      const emitObj = { evt: '', data: this.item }
      if (evt === 'addCourse') emitObj.evt = 'addCourse'
      else if (evt === 'del') emitObj.evt = 'del'
      else if (evt === 'go') emitObj.evt = 'go'
      this.$emit('handleEvent', emitObj)
    },
    getStatusColor(value = 0) {
      // status 全部-为空 状态 0-待发布、1-在用、3-下架（出库）、4-草稿
      switch (value) {
        case 0 :
          return 'await-color' // 待发布 --- 待审核
        case null:
          return 'await-color'
        case 1 :
          return 'use-color' // 在用 ---- 发布完成
        case 3 :
          return 'stop-color' // 下架
      }
    }
  }
}
</script>
<style lang="less">
.el-tooltip__popper.safe-tool {
  background: #fff;
  color: #D35A21;
  line-height: 16px;
  box-shadow: 0 3px 20px 2px #5d5d5d38;
  width: 352px;
  padding: 16px;
  .popper__arrow::after {
    border-top-color: #fff
  }
}
</style>
<style lang="less" scoped>
.pointer-default {
  cursor: default !important;
}
.item-card {
  border-bottom: 1px solid rgba(238, 238, 238, 1);
  padding: 10px 0;
  &-content {
    display: flex;
    .item-image, .image-slot img {
      width: 108px;
      height: 72px;
      margin-right: 12px;
      border-radius: 2px;
      cursor: pointer;
    }
    .text {
      color: rgba(51, 51, 51, 1);
      @media screen and (max-width: 1660px) {
        width: 790px;
      }
      @media screen and (min-width: 1661px) {
        width: 1075px;
      }
      .video-title {
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        white-space: nowrap;
        margin-bottom: 36px;
        cursor: pointer;
        .jing {
          height: 20px;
          color: #fff;
          background-color: #d9001b;
          display: inline-block;
          border-radius: 5px;
          width: 20px;
          text-align: center;
          margin: 0 5px 0 14px;
        }
        .article-tag {
          display: inline-block;
          font-size: 12px;
          height: 18px;
          line-height: 16px;
          border-radius: 2px;
          border: 1px solid;
          color: #ff8b6c;
          padding: 0 7px;
          margin-right: 8px;
        }
      }
      .bottom-icon {
        height: 16px;
        display: flex;
        align-items: baseline;
        .bottom-left {
          display: flex;
          flex: 1;
          .tool-tips-box {
            display: flex;
            align-items: center;
            margin-left: 16px;
          }
          .el-icon-warning-outline {
            font-size: 16px;
            color: #E34D59;
            margin-left: 4px;
          }
          .icon-box {
            display: flex;
            align-items: center;
            color: rgba(0, 0, 0, 0.4);
            font-size: 12px;
          }
          .icon-box + .icon-box {
            margin-left: 16px;
          }
          .icon {
            width: 16px;
            height: 16px;
            display: inline-block;
            margin-right: 3px;
          }
          .deal-color {
            color:rgba(0,82,217,1);
            cursor: pointer;
          }
          .no-pass-color {
            color: #E34D59;
          }
          .await-color {
            color: #FF8B6C
          }
          .use-color {
            color: rgba(0,168,112,1)
          }
          .stop-color {
            color: rgba(227,77,89,1)
          }
          .watch-icon {
            background: url('~@/assets/img/watch.png') no-repeat center/cover;
          }
          .like-icon {
            background: url('~@/assets/img/zan1.png') no-repeat center/cover;
          }
          .collect-icon {
            background: url('~@/assets/img/fav2.png') no-repeat center/cover;
          }
        }

        .right-not-allow {
          cursor: not-allowed;
          pointer-events: none;
          .other-icon-box {
            cursor: not-allowed;
            pointer-events: none;
            .edit-icon {
              background: url('~@/assets/img/edit.png') no-repeat center/cover !important;
            }
            .share-icon {
              background: url('~@/assets/img/stop-share.png') no-repeat center/cover !important;
            }
            .up-icon {
              background:  url('~@/assets/img/up-1.png') no-repeat center/cover !important;
            }
            .down-icon {
              background:  url('~@/assets/img/down-1.png') no-repeat center/cover !important;
            }
            .add-icon {
              background:  url('~@/assets/img/add-circle-1.png') no-repeat center/cover !important;
            }
            .del-icon {
              background:  url('~@/assets/img/del-1.png') no-repeat center/cover !important;
            }
          }
        }

        .disabled-icon {
          cursor: not-allowed;
          pointer-events: none;
          .edit-icon {
            background: url('~@/assets/img/edit.png') no-repeat center/cover !important;
          }
          .share-icon {
            background: url('~@/assets/img/stop-share.png') no-repeat center/cover !important;
          }
          .up-icon {
            background:  url('~@/assets/img/up-1.png') no-repeat center/cover !important;
          }
          .down-icon {
            background:  url('~@/assets/img/down-1.png') no-repeat center/cover !important;
          }
          .add-icon {
            background:  url('~@/assets/img/add-circle-1.png') no-repeat center/cover !important;
          }
          .del-icon {
            background:  url('~@/assets/img/del-1.png') no-repeat center/cover !important;
          }
        }

        .bottom-right {
          display: flex;
          span + span {
            margin-left: 20px;
          }
          .el-dropdown {
            margin-left: 20px;
          }
          .right-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
          }
          .other-icon-box {
            // width: 24px;
            // height: 24px;
            width: 16px;
            height: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            cursor: pointer;

            .edit-icon {
              background: url('~@/assets/img/edit-allow.png') no-repeat
                center/cover;
            }
            .share-icon {
              background: url('~@/assets/img/share.png') no-repeat center/cover;
            }
            .other-icon {
              background: url('~@/assets/img/other-icon.png') no-repeat
                center/cover;
            }
            // .stop-edit-icon {
            //   background: url('~@/assets/img/edit.png') no-repeat center/cover;
            // }
            // .stop-share-icon {
            //   background: url('~@/assets/img/stop-share.png') no-repeat
            //     center/cover;
            // }
            // .stop-other-icon {
            //   background: url('~@/assets/img/other-stop.png') no-repeat
            //     center/cover;
            // }
            .down-icon {
              background: url('~@/assets/img/down.png') no-repeat center/cover;
            }
            .up-icon {
              background: url('~@/assets/img/up.png') no-repeat center/cover;
            }
            .add-icon {
              background: url('~@/assets/img/add-circle.png') no-repeat center/cover;
            }
            .del-icon {
              background: url('~@/assets/img/del.png') no-repeat center/cover;
            }
          }

          .other-icon-box:hover {
            background: rgba(245, 247, 249, 1);
            .edit-icon {
              background: url('~@/assets/img/edit-icon-hover.png') no-repeat
                center/cover;
            }
            .share-icon {
              background: url('~@/assets/img/share-hover.png') no-repeat
                center/cover;
            }
            .other-icon {
              background: url('~@/assets/img/other-icon-hover.png') no-repeat
                center/cover;
            }
            .down-icon {
              background: url('~@/assets/img/down-active.png') no-repeat center/cover;
            }
            .up-icon {
              background: url('~@/assets/img/up-1.png') no-repeat center/cover;
            }
            .add-icon {
              background: url('~@/assets/img/add-circle-1.png') no-repeat center/cover;
            }
            .del-icon {
              background: url('~@/assets/img/del-1.png') no-repeat center/cover;
            }
          }
        }
      }
    }
  }
}
.item-card:last-of-type {
  border-bottom: 1px solid rgba(238, 238, 238, 1);
}
.item-card:hover {
  background: rgba(247, 251, 255, 0.5);
}
</style>
