<template>
  <div class="task-finish-wrap">
    <template v-if="routerParams.from === 'coregist'">
      <div class="normal-box">
        <img :src="require('@/assets/img/wjFinish.png')" alt="" class="normal-finish-icon">
        <p>聚合报名以及问卷提交成功，感谢您的参与!</p>
      </div>      
    </template>
    <template v-else-if="routerParams.from === 'normal'">
      <div class="normal-box">
        <img :src="require('@/assets/img/wjFinish.png')" alt="" class="normal-finish-icon">
        <p>问卷到此结束，感谢您的参与!</p>
      </div>
    </template>
    <template v-else-if="routerParams.from === 'activity'">
      <div class="normal-box">
        <img :src="require('@/assets/img/wjFinish.png')" alt="" class="normal-finish-icon">
        <p>活动报名以及问卷提交成功，感谢您的参与!</p>
      </div>
    </template>
    <template v-else>
      <div class="task-finish-mobile" v-if="isMobileTerminal">
        <div class="content">
          <img
            :src="require('@/assets/mooc-img/task-finish.png')"
            alt=""
            class="finish-icon"
          />
          <p class="tips1">当前任务已完成，可继续项目学习</p>
          <p class="back-project" @click="toProjectDetail('back')">回到项目任务列表</p>
          <div class="btn-box">
            <el-button plain @click="toParent('prevTask')" v-if="prevTaskBtnShow" :style="nextTaskBtnShow ? 'margin-right:14px' : 'margin-right:0' ">上一个任务</el-button>
            <el-button type="primary" @click="toParent('nextTask')" v-if="nextTaskBtnShow">下一个任务</el-button>
          </div>
        </div>
      </div>

      <div class="task-finish-pc" v-else>
        <div class="content">
          <img
            :src="require('@/assets/mooc-img/task-finish.png')"
            alt=""
            class="finish-icon"
          />
          <p class="tips1">当前任务已完成，可继续项目学习</p>
          <p class="back-project" @click="postMessageToParent('back')">回到项目任务列表</p>
          <div class="btn-box">
            <el-button plain @click="postMessageToParent('prevTask')" v-if="prevTaskBtnShow" :style="nextTaskBtnShow ? 'margin-right:14px' : 'margin-right:0' ">上一个任务</el-button>
            <el-button type="primary" @click="postMessageToParent('nextTask')" v-if="nextTaskBtnShow">下一个任务</el-button>
          </div>
        </div>
      </div>
    </template>

  </div>
</template>

<script>

import MoocJs from 'sdc-moocjs-integrator'

export default {
  data() {
    return {
      prevTaskBtnShow: false,
      nextTaskBtnShow: false,
      isUserBtn: false,
      routerParams: {
        from: ''
      }
    }
  },
  computed: {
    isMobileTerminal() {
      let isInWechatMP = (navigator.userAgent.match(/micromessenger/i) && navigator.userAgent.match(/miniprogram/i)) || window.__wxjs_environment === 'miniprogram'
      let userAgent = navigator.userAgent.toLowerCase()
      let isMobile = /ipad|iphone|midp|rv:*******|ucweb|android|windows ce|windows mobile/.test(userAgent)
      if (isInWechatMP || isMobile) {
        console.log('前端是移动端')
        return true
      } else {
        console.log('前端是PC端')
        return false
      }
    }
  },
  methods: {
    // Mobile
    toParent(type) {
      this.isUserBtn = true
      if (type === 'prevTask') {
        MoocJs.postMessage('previous')
      } else {
        MoocJs.postMessage('next')
      }
    },
    toProjectDetail() {
      this.isUserBtn = true
      MoocJs.postMessage('toProjectDetail')
    },

    // PC
    postMessageToParent(type = '') {
      let data = {
        methodsType: type,
        from: 'questionnaire'
      }
      MoocJs.postMessage('questionPageClick', data)
    }
  },
  created() {
    console.log('created - taskFinish')
    this.routerParams = this.$route.query
  },
  mounted () {
    MoocJs.messageListener((res) => {
      let { params } = res
      console.log('父页面传过来的参数', res)
      //  PC - btnOptionsChange, Mobile - switchTaskBtnShow
      if (['btnOptionsChange', 'switchTaskBtnShow'].includes(res.events)) {
        this.prevTaskBtnShow = params.prevTaskBtnShow
        this.nextTaskBtnShow = params.nextTaskBtnShow
      }
    })

    console.log('mounted - isMobileTerminal', this.isMobileTerminal)
    // 通知任务详情页更新任务状态，并刷新页面
    MoocJs.postMessage('completeStatusUpdata')
  },
  beforeDestroy() {
    console.log('问卷的任务完成页销毁前')
    if (!this.isUserBtn && this.isMobileTerminal) {
      console.log('调用了返回任务列表的方法')
      this.toProjectDetail()
    }
  }
}
</script>

<style lang="less">
.task-finish-wrap {
  width: 100%;
  height: 100%;
  .task-finish-mobile {
    width: 100%;
    height: 100%;
    background-color: #FFFFFF;
    .content {
      width: 100%;
      height: 100%;
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background: #fff;
      border-radius: 4px;
      .finish-icon {
        width: 160px;
      }
      .btn-box {
        display: flex;
        margin-top: 24px;
      }
    }
  }
  .task-finish-pc {
    padding: 16px 0 0 0;
    .content {
      margin: 0 auto;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 794px;
      width: 1000px;
      background: #fff;
      border-radius: 4px;
      .finish-icon {
        width: 160px;
      }
      .btn-box {
        display: flex;
        margin-top: 24px;
      }
    }
  }
  .normal-box {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    .normal-finish-icon {
      width: 240px;
    }

    >p {
      font-size: 24px;
      font-weight: 600;
      margin: 80px 0 0 0;
    }
    @media screen and (max-width: 480px) {
      .normal-finish-icon {
        width: 160px;
      }

      >p {
        font-size: 20px;
        font-weight: 500;
        margin: 35px 0 0 0;
      }
    }
  }
  .tips1 {
    margin-top: 24px;
    color: #000000;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }
  .back-project {
    margin-top: 24px;
    color: #0052d9;
    text-align: right;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 14px;
    cursor: pointer;
  }
}
</style>
