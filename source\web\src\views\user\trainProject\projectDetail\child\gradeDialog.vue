<template>
  <el-dialog
    class="grade-dialog dialog-center none-border-dialog"
    :title="$langue('Mooc_ProjectDetail_Score_Score', { defaultText: '项目评分' })"
    :visible.sync="visible" 
    width="360px"
    top="50px"
    :show-close="false"
    :close-on-click-modal="false"
   >
   <div class="grade-body">
      <p class="grade-status">{{ scoreInfo[score] }}</p>
      <p class="grade-score">{{$langue("Mooc_ProjectDetail_Score_MyCore", {point: score, defaultText: `我的评分：${score}分` })}}</p>
      <!-- <p class="grade-score">我的评分：{{score}}分</p> -->
      <el-rate class="rate-num" 
        v-model="score" 
        :colors="{5: '#0052D9'}"
      >
      </el-rate>
   </div>
   <div class="bottom-btn">
      <el-button type="primary" size="small" @click="submit">{{ scoreStatus > 0 ? $langue('Mooc_ProjectDetail_Score_EditSure', { defaultText: '确认修改' }) : $langue('Mooc_ProjectDetail_Score_Submit', { defaultText: '提交' })}}</el-button>
      <div class="cancel-btn" @click="cancel">{{ $langue("Mooc_Common_Alert_Cancel", { defaultText: '取消' }) }}</div>
   </div>
  </el-dialog>
</template>
<script>
import { addScore } from '@/config/mooc.api.conf.js'
import { mapState } from 'vuex'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    scoreStatus: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      score: 0
      // scoreInfo: { // 1-非常差、2-比较差、3-一般般、4-比较好、5-非常好
      //   1: '非常差',
      //   2: '比较差',
      //   3: '一般般',
      //   4: '比较好',
      //   5: '非常好'
      // }
    }
  },
  computed: {
    ...mapState(['userInfo']),
    scoreInfo() {
      return {
        1: this.$langue('Mooc_ProjectDetail_Score_VeryBad', { defaultText: '非常差' }),
        2: this.$langue('Mooc_ProjectDetail_Score_Bad', { defaultText: '比较差' }),
        3: this.$langue('Mooc_ProjectDetail_Score_Normal', { defaultText: '一般般' }),
        4: this.$langue('Mooc_ProjectDetail_Score_Better', { defaultText: '比较好' }),
        5: this.$langue('Mooc_ProjectDetail_Score_Good', { defaultText: '非常好' })
      }
    }
  },
  mounted() {
    this.score = this.scoreStatus
  },
  methods: {
    submit() {
      const { mooc_course_id } = this.$route.query
      const { staff_id } = this.userInfo
      const params = {
        act_id: mooc_course_id,
        act_type: 11,
        staff_id,
        score: this.score
      }
      addScore(params).then((res) => {
        this.cancel()
        this.$emit('getScore')
      })
    },
    cancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.grade-dialog {
  .grade-body {
    text-align: center;
    .grade-status {
      color: #000000e6;
      font-size: 16px;
      font-weight: bold;
    }
    .grade-score {
      color: #0052d9ff;
      font-size: 14px;
      margin-top: 8px;
      margin-bottom: 8px;
    }
    :deep(.el-rate) {
      .el-rate__icon {
        font-size: 20px;
      }
    }
  }
  .bottom-btn {
    text-align: center;
    margin-top: 24px;
    .el-button {
      width:  296px;
    }
    .cancel-btn {
      margin-top: 8px;
      height: 22px;
      line-height: 22px;
      color: #000000e6;
      cursor: pointer;
    }
    .cancel-btn:hover {
      color: #666666;
      background: #fff;
      border-color: transparent;
    }
  }
}
</style>
