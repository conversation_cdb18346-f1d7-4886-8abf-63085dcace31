<template>
  <div class="article-detail" :dt-areaid="dtOutCoursePage('area')" :dt-remark="dtOutCoursePage('remark')">
    <!-- 文章内容区域 -->
    <div class="article-head" v-if="!isScreen">
      <div class="title overflow-l2">
        <span class="tag" v-if="filterResourceName">{{ filterResourceName }}</span>
        <span v-else>-</span>
        {{ courseData.course_title }}
      </div>
      <div class="user-date">
        <span class="user">{{ courseData.authorInfo }}</span>
        <span class="date">{{ courseData.created_at }}</span>
      </div>
      <!-- 功能栏 -->
      <div class="count" v-if="isIndependentCourse">
        <!-- 阅读量 -->
        <div class="praise">
          <img
            class="icon"
            src="../../../../assets/img/mobile/geekBang/icon-watch.png"
          />
          <span class="num">({{ summaryData.view_count || 0 }})</span>
        </div>
        <!-- 点赞 -->
        <div
          class="praise"
          :class="[isLike ? 'active-item' : '']"
          @click="handleLike"
        >
          <img
            v-if="isLike"
            class="icon"
            src="../../../../assets/img/mobile/geekBang/praise-active.png"
          />
          <img
            v-else
            class="icon"
            src="../../../../assets/img/mobile/geekBang/praise.png"
          />
          <span class="num">({{ summaryData.praise_count || 0 }})</span>
        </div>
        <!-- 评论 -->
        <div class="praise">
          <img class="icon" src="../../../../assets/img/mobile/chart.png" />
          <span class="num">({{ summaryData.comment_count || 0 }})</span>
        </div>
        <!-- 收藏 -->
        <div
          class="praise"
          :class="[isCollect ? 'active-item' : '']"
          @click="handleFavorited"
        >
          <img
            v-if="isCollect"
            class="icon"
            src="../../../../assets/img/mobile/geekBang/cellect-active.png"
          />
          <img
            v-else
            class="icon"
            src="../../../../assets/img/mobile/geekBang/cellect.png"
          />
          <span class="num">({{ summaryData.fav_count || 0 }})</span>
        </div>
        <!-- 打标签 -->
        <!-- 添加到客单 -->
        <!-- <div class="praise add-to-list" @click="openCourseForm">
          <img class="icon" src="../../../../assets/img/mobile/geekBang/add.png" />
          <span class="num">添加到课单</span>
        </div> -->
        <!-- 分享 -->
        <!-- <div class="praise share" @click="shareCourse">
          <img class="icon" src="../../../../assets/img/mobile/share.png" />
          <span class="num">分享</span>
        </div> -->
      </div>
      <!-- 简介 -->
      <div class="simple-info tree-line-show">
        简介：{{ courseData.course_intro }}
      </div>
      <!-- 标签 -->
      <div class="label-list" v-if="courseData.labels.length > 0">
        <label-component
          :label-list="courseData.labels"
          max-line="1"
        ></label-component>
      </div>
    </div>

    <!-- 文章内容 - 嵌入的h5页面 -->
    <template v-if="iframeSrc">
      <iframe
        class="iframe-info"
        :class="{'fullscreen': isScreen}"
        id="DDIIframe"
        :src="iframeSrc"
        frameborder="0"
        scrolling="no"
        allowfullscreen="true"
        webkitallowfullscreen="true"
        mozallowfullscreen="true"
        style="width: 100%; height: 100%;"
      ></iframe>
    </template>
    <div class="video-box" v-else>
      <van-image
        lazy
        fit="fill"
        :src="
          courseData.photo_url
            ? courseData.photo_url
            : require('@/assets/img/default_bg_img.png')
        "
      >
      </van-image>
    </div>

    <!-- 评论 -->
    <div class="comment-wrap" v-if="!isScreen">
      <sdc-comment-mob
        v-if="isIndependentCourse && commentParams"
        :params="commentParams"
      ></sdc-comment-mob>
    </div>

    <!-- 添加到客单弹窗 -->
    <!-- <addCourse :show.sync="showCourseFormPopup" :courseData="courseData"></addCourse> -->
  </div>
</template>
<script>
import labelComponent from './components/labelComponent'
// import addCourse from './components/addCourse.vue'
import {
  getGeekCourseDetail,
  geekStudyRecord,
  getSummaryData,
  checkPraised,
  addPraise,
  deletePraise,
  checkFavorited,
  addFavorited,
  deleteFavorite
} from 'config/mooc.api.conf.js'
import { Toast } from 'vant'
import MoocJs from 'sdc-moocjs-integrator'
import { pageExposure } from '@/utils/tools.js'

export default {
  name: 'DDIIframes',
  components: { 
    labelComponent
    // addCourse
  },
  data () {
    return {
      act_type: 102,
      courseData: {
        course_title: '',
        authorInfo: '',
        creator_at: '',
        labels: []
      },
      iframeEl: null,
      iframeSrc: 'https://cloud.italent.cn/ElinkPage/Index?token=MTAwNTQ4OmRjN2VhZTYwLWI4NzMtNGZkNi1iMTA0LTExOGY0NzkxYTU3MQ**',
      iframeHeight: 0,
      viewTimer: null, // 上报定时器
      recordId: null, // 上报记录id
      isPageHidden: false, // 是否熄屏
      summaryData: {},
      commentParams: null, // 评论组件配置
      isLike: false, // 是否点赞
      isCollect: false, // 是否收藏
      showCourseFormPopup: false, // 是否显示添加客单pop
      errNum: 0, // 上报错误次数
      isScreen: true // true：不显示顶部栏和底部评论
    }
  },
  computed: {
    courseId () {
      return this.$route.query?.course_id || ''
    },
    // mooc_id
    mooc_course_id() {
      return this.$route.query.mooc_course_id || '-1'
    },
    isIndependentCourse() {
      return this.courseData.independent_course === 1
    },
    shareStaffId () {
      return this.$route.query.share_staff_id || ''
    },
    shareStaffName () {
      return this.$route.query.share_staff_name || ''
    },
    // 类型
    filterResourceName () {
      let { course_type = '' } = this.courseData
      let name = ''
      if (this.isVideoType) {
        name = this.$langue('Mooc_Common_ResourceType_Video', {
          defaultText: '视频'
        })
      } else if (course_type === 'Audio') {
        name = this.$langue('Mooc_Common_ResourceType_Audio', {
          defaultText: '音频'
        })
      } else if (
        ['Article', 'graphic'].includes(course_type.toLocaleLowerCase())
      ) {
        name = this.$langue('Mooc_Common_ResourceType_Article', {
          defaultText: '文章'
        })
      } else if (course_type === 'series') {
        // name = '专栏课程'
        name = '系列课程'
      } else if (course_type === 'Doc') {
        name = this.$langue('Mooc_Common_ResourceType_Doc', {
          defaultText: '文档'
        })
      } else if (course_type === 'Scorm') {
        name = 'Scorm'
      } else if (course_type === 'Flash') {
        name = this.$langue('Mooc_Common_ResourceType_Zip', {
          defaultText: '压缩包'
        })
      }
      return name
    },
    isVideoType () {
      return ['video', 'Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(
        this.courseData.course_type
      )
    },
    // 页面曝光
    dtOutCoursePage() {
      return (type) => {
        const { course_title } = this.courseData
        if (type === 'area') {
          return `area_${this.courseId}`
        } else if (type === 'remark') {
          return JSON.stringify({ 
            page: course_title,
            page_type: '外部课程详情页',
            container: course_title,
            act_type: '102',
            content_type: this.filterResourceName,
            terminal: 'H5'
          })
        } else {
          return ``
        }
      }
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler (val) {
        if (val.staff_name) {
          const hostUrl =
            process.env.NODE_ENV === 'development'
              ? process.env.VUE_APP_PORTAL_HOST_WOA
              : window.origin
          this.commentParams = {
            userName: val.staff_name,
            actId: this.courseId,
            appId: 'A9BiosXihR0h46ThNsAX',
            orderType: this.act_type,
            urlConfig: {
              getComments: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/${this.mooc_course_id}/comment-page`,
              addComment: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/add-comment`,
              deleteComment: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/delete-comment/`,
              like: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/praised-comment`,
              sticky: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/sticky-comment`,
              show: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/show-comment`
            }
          }
        }
      },
      immediate: true
    }
  },
  created () {
    // 添加message事件监听
    window.addEventListener('visibilitychange', this.visibilitychange, false)
    window.addEventListener('beforeunload', this.beforeunloadEvent, false)
    // this.getGeekCourseInfo()
  },
  mounted () {},
  beforeDestroy () {
    window.removeEventListener('visibilitychange', this.visibilitychange)
    this.clearViewTimer()
  },
  methods: {
    // 获取课程详情
    getGeekCourseInfo () {
      Toast.loading({
        duration: 0, // 持续展示 toast
        forbidClick: true,
        message: '加载中...'
      })
      getGeekCourseDetail(this.courseId)
        .then(res => {
          Toast.clear()
          document.title = res.course_title
          this.courseData = res
          // ddi测评没有移动端
          if (this.courseData.recourse_from === 'DDIexam') {
            let link =
              process.env.NODE_ENV === 'production'
                ? 'https://sdc.qq.com/s/b5GaSG'
                : 'http://s.test.yunassess.com/s/hoo9Gg'
            this.$router.replace({
              name: 'mobileError',
              query: {
                type: 2,
                href: encodeURIComponent(`${link}?course_id=${this.course_id}`)
              }
            })
            return
          }
          this.iframeSrc = res.recourse_iframe_url || ''
          // 极客时间 文章加载完成后开始计时
          this.creatViewTimer()
          // 通知mooc开启学习上报
          MoocJs.play()
          // window.addEventListener('message', this.communication)
          
          if (!this.isScreen) {
            let intro = this.computedStr(res.author_intro)
            res.authorInfo = intro ? `${res.author}(${intro})` : res.author
            this.courseData.labels = res.labels || []
            if (this.isIndependentCourse) {
              this.getSummaryInfo()
            }
          }

          // 详情页曝光上报
          pageExposure({
            page_type: '移动端外部课程iframe详情页',
            content_type: '网络课',
            act_type: '2',
            content_name: res.course_title,
            content_id: this.courseId,
            terminal: 'H5'
          })
        })
        .catch(err => {
          Toast.clear()
          if (err.code) {
            let type = 0
            if (err.code === 403) {
              type = 5
            } else if (err.code === 500) {
              type = 3
            }
            this.$router.replace({
              name: 'mobileError',
              query: {
                type
              }
            })
          }
        })
    },
    communication (event) {
      let { action, vendor, params, height } = event.data
      if (vendor === 'geekbang' && action === 'article:mounted') {
        Toast.clear()
        // 浏览页开启时长记录
        // this.creatViewTimer()
      }
      if (vendor === 'geekbang' && action === 'article:loaded') {
        // 如果有高度 设置iframe高度
        if (params.height) {
          this.iframeHeight = params.height
          this.iframeAutoFit(params.height + 0 + 'px')
        }
      }
      if (vendor === 'geekbang' && this.iframeHeight > 0) {
        let hVal = params.height ? params.height : height
        if (hVal > this.iframeHeight) {
          this.iframeHeight = hVal
          this.iframeAutoFit(hVal + 0 + 'px')
        }
      }
    },
    // iframe高度自适应
    iframeAutoFit (height) {
      this.$nextTick(() => {
        let iframeObj = document.querySelector('#DDIIframe')
        if (iframeObj) {
          iframeObj.style.height = height
        }
      })
    },
    // 初始化监听器
    creatViewTimer () {
      let _this = this
      let durtation = 0
      let totalTime = (this.courseData.total_time || 15) * 2.5
      this.viewTimer = setInterval(() => {
        ++durtation
        // if (durtation % 15 === 0) {
        //   _this.handleViewGraphicRecord() // 浏览器时长需每15秒记录一次
        // }
        if (durtation >= totalTime) {
          _this.clearViewTimer()
        }
      }, 1000)
    },
    // 访问记录上报
    handleViewGraphicRecord () {
      const recordParam = {
        area_id: this.$route.query.area_id || '',
        from: this.$route.query.from || '',
        course_id: this.courseId,
        sharer_id: this.shareStaffId || '',
        sharer_name: this.shareStaffName || '',
        from_type: this.courseData.recourse_from || '',
        record_id: this.recordId || ''
      }
      console.log('iframePlay上报参数recordParam: ', recordParam)
      geekStudyRecord(recordParam).then(data => {
        // 只统计连续上报的错误次数
        this.errNum = 0
        if (data) {
          this.recordId = data
        }
      }).catch(() => {
        // 统计连续上报错误次数
        this.errNum++
        if (this.errNum >= 3) {
          this.clearViewTimer()
        }
      })
    },
    // 清除15s计时器
    clearViewTimer () {
      if (this.viewTimer !== null) {
        clearInterval(this.viewTimer)
        this.recordId = ''
      }
    },
    // 监听熄屏/显示
    visibilitychange () {
      if (document.hidden) {
        this.clearViewTimer()
        // this.handleViewGraphicRecord()
        this.isPageHidden = true
      } else {
        this.isPageHidden = false
        if (this.courseData.recourse_iframe_url) {
          this.creatViewTimer()
        }
      }
    },
    // 销毁前处理
    beforeunloadEvent () {
      // this.handleViewGraphicRecord()
      this.clearViewTimer()
    },
    // 获取点赞，收藏……等统计数据
    getSummaryInfo () {
      getSummaryData({
        act_type: this.act_type,
        course_id: this.courseId
      })
        .then(res => {
          this.summaryData = res || {}
          this.getPraisedStatus()
          this.getFavoritedStatus()
        })
        .catch(() => {
          Toast.clear()
        })
    },
    // 获取点赞状态
    getPraisedStatus () {
      checkPraised({
        act_type: this.act_type,
        course_id: this.courseId
      }).then(res => {
        this.isLike = res
        Toast.clear()
      }).catch(() => {
        Toast.clear()
      })
    },
    // 点赞/取消点赞
    handleLike () {
      let praiseCommonAPI = null
      let tip = null
      if (this.isLike) {
        praiseCommonAPI = deletePraise
        this.summaryData.praise_count--
        tip = this.$langue('Mooc_Common_Alert_CancelPraiseSucessed', {
          defaultText: '取消点赞成功'
        })
      } else {
        praiseCommonAPI = addPraise
        this.summaryData.praise_count++
        tip = this.$langue('Mooc_Common_Alert_PraiseSucessed', {
          defaultText: '点赞成功'
        })
      }
      praiseCommonAPI({
        act_type: this.act_type,
        course_id: this.courseId
      }).then(res => {
        if (res.data) {
          Toast(`${tip}`)
          this.isLike = !this.isLike
        }
        this.getPraisedStatus()
      })
    },
    // 获取收藏状态
    getFavoritedStatus () {
      checkFavorited({
        act_type: this.act_type,
        course_id: this.courseId
      }).then(res => {
        this.isCollect = res
        Toast.clear()
      }).catch(() => {
        Toast.clear()
      })
    },
    // 收藏/取消收藏
    handleFavorited () {
      let praiseCommonAPI = null
      let tip = null
      if (this.isCollect) {
        praiseCommonAPI = deleteFavorite
        this.summaryData.fav_count--
        tip = this.$langue('Mooc_Common_Alert_CancelCollectSucessed', {
          defaultText: '取消收藏成功'
        })
      } else {
        praiseCommonAPI = addFavorited
        this.summaryData.fav_count++
        tip = this.$langue('Mooc_Common_Alert_CollectSucessed', {
          defaultText: '收藏成功'
        })
      }
      praiseCommonAPI({
        act_type: this.act_type,
        course_id: this.courseId
      }).then(res => {
        if (res.data) {
          Toast(`${tip}`)
          this.isCollect = !this.isCollect
        }
        this.getFavoritedStatus()
      })
    },
    computedStr (str) {
      if (!str) return str
      let len = 0
      let index = 0
      if (str.length <= 6) return str
      for (let i = 0; i < str.length; i++) {
        let c = str.charCodeAt(i)
        // 单字节加1
        if ((c >= 0x0001 && c <= 0x007e) || (c >= 0xff60 && c <= 0xff9f)) {
          len++
        } else {
          len += 2
        }
        if (len >= 12) {
          index = i
          break
        }
      }
      if (str.length > index + 2) {
        return (
          str.substring(0, index + 1) + '...' + str.substring(str.length - 2)
        )
      } else {
        return str
      }
    },
    // ---------------------------------------------------------------------
    // 添加到客单
    openCourseForm () {
      this.showCourseFormPopup = true
    },
    // 分享
    shareCourse () {
      window.wx.miniProgram.postMessage({
        data: {
          type: 'share',
          config: {
            title: this.courseData.course_title,
            imgUrl: this.courseData.course_cover,
            path: `/pages/webview/DDI/index?course_id=${this.courseId}`
          }
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.article-detail {
  height: 100%;
  background: #fff;
  .article-head {
    padding: 12px 16px 8px;
    background-color: #fff;
    .title {
      line-height: 24px;
      color: #000000ff;
      font-size: 16px;
      font-weight: 600;
      .tag {
        margin-right: 10px;
        display: inline-block;
        // width: 42px;
        padding: 0 6px;
        height: 20px;
        line-height: 20px;
        border-radius: 2px;
        border: 1px solid #ff8b6cff;
        background: #ff8b6c33;
        color: #ff8b6cff;
        font-size: 14px;
        font-weight: 600;
        text-align: center;
      }
      .label-icon {
        height: 20px;
        margin-right: 8px;
        position: relative;
        top: -2px;
        z-index: 0;
      }
    }
    .user-date {
      margin-top: 6px;
      min-height: 20px;
      line-height: 20px;
      color: #00000099;
      font-size: 12px;
      display: flex;
      align-items: baseline;
      justify-content: space-between;
      .date {
        text-align: right;
        flex-shrink: 0;
        margin-left: 36rpx;
      }
    }
    .count {
      height: 16px;
      line-height: 16px;
      font-size: 12px;
      margin-top: 16px;
      .admire,
      .praise,
      .cellect {
        float: left;
        color: #00000066;
      }
      .admire,
      .praise {
        margin-right: 24px;
      }
      .active-item {
        color: #0052d9;
      }
      .admire,
      .praise,
      .add-to-list {
        .icon {
          display: inline-block;
          width: 16px;
          height: 16px;
          float: left;
          margin-right: 2px;
        }
      }
      .add-to-list {
        color: #0052d9ff;
      }
      .share {
        color: #666666;
      }
    }
    .simple-info {
      padding: 8px 8px 8px 12px;
      margin-top: 16px;
      max-height: 78px;
      border-radius: 3px;
      background: #f6f6f6;
      color: #00000099;
      font-size: 14px;
      line-height: 22px;
    }
    .tree-line-show {
      display: -webkit-box; /* 必须 */
      overflow: hidden; /* 必须 */
      text-overflow: ellipsis; /* 必须 */
      -webkit-line-clamp: 3; /* 控制显示的行数 */
      -webkit-box-orient: vertical; /* 必须 */
      -webkit-box-direction: normal; /* 必须 */
      -webkit-box-pack: end; /* 可选，根据需求调整省略号位置 */
    }
    .label-list {
      margin-top: 8px;
    }
  }
  .iframe-info {
    margin: 8px auto 0;
    padding: 0 15px;
    width: 100%;
    height: 0;
    position: relative;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
    height: 667px;
    // height: 814px;
    // height: 1334px;
  }
  .fullscreen {
    margin: 0;
    padding: 0;
    height: calc(100vh);
  }
  .comment-wrap {
    background-color: #fff;
  }
  // 文字超出两行省略号
  .overflow-l2 {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden; //溢出内容隐藏
    text-overflow: ellipsis; //文本溢出部分用省略号表示
    display: -webkit-box; //特别显示模式
    -webkit-line-clamp: 2; //行数
    line-clamp: 2;
    -webkit-box-orient: vertical; //盒子中内容竖直排列
  }
  .trial-study-pop {
    margin: 17px 16px 0;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 8px;
    background: #f8fbff;
    display: flex;
    flex-direction: column;
    align-items: center;
    .warm {
      color: #0052d9;
      font-size: 20px;
      font-weight: 600;
      line-height: 28px;
      margin-bottom: 8px;
    }
    .title {
      width: 279px;
      color: #000000e6;
      font-size: 16px;
      line-height: 24px;
      margin-bottom: 16px;
      text-align: center;
    }
    .btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 304px;
      height: 48px;
      color: #ffffff;
      font-size: 16px;
      font-weight: 600;
      border-radius: 24px;
      background: linear-gradient(90deg, #488dff 0%, #1766ff 100%);
    }
    .coupon-content {
      margin-top: 16px;
      font-size: 14px;
      color: #0052d9;
    }
    .points {
      color: #00000066;
      line-height: 22px;
      text-decoration-line: line-through;
      margin-right: 12px;
    }
    .use-num {
      line-height: 22px;
      margin-right: 20px;
    }
    .extra-num {
      line-height: 22px;
      span {
        font-weight: 500;
      }
    }
    .my-coupon {
      margin-top: 8px;
      color: #0052d9;
      font-size: 14px;
      line-height: 22px;
    }
    .link {
      cursor: pointer;
      margin-top: 16px;
      .icon {
        width: 14px;
        height: 14px;
        margin-right: 5px;
      }
      span {
        color: #0052d9;
        font-size: 14px;
        line-height: 22px;
        text-decoration-line: underline;
      }
    }
  }
}
</style>
