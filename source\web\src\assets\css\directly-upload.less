.directly-upload-container {
  background: #F6F7F9;
  padding-bottom: 110px;
  height: 100%;
  ::v-deep .tab-box {
    margin-top: 20px;
    .ul-tabs-chapter {
      height: 32px;
      line-height: 32px;
      border: 0;
      display: flex;
      .li-tab-item {
        padding: 0 20px;
        width: 100px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        margin: 0;
        border: 0;
        font-family: "PingFang SC";
        font-size: 14px;
        color: #00000099;
        box-sizing: border-box;
        cursor: pointer;
      }
      .li-tab-item.active {
        background-color: #ffffff;
        color: #0052d9;
        text-align: center;
        font-weight: 600;
      }
    }
  }
  .pseudo-class-title {
    position: relative;
    padding-left: 16px;
    font-size: 16px;
    font-weight: bold;
    color: rgba(0,0,0,0.8);
    font-family: "PingFang SC";
    margin-bottom: 20px;
    display: inline-block;
    .tips {
      color: rgba(0,0,0,0.4);
      display: inline-block;
      margin-left: 12px;
    }
  }
  .pseudo-class-title::before {
    position: absolute; 
    top: 50%; 
    transform: translatey(-50%); 
    left: 0; 
    content: ''; 
    width: 4px;
    height: 18px;
    background-color: #0052D9; 
  } 
  .header {
    padding: 24px 28px;
    border-bottom: 1px solid rgba(238,238,238,1);
    color: rgba(0,0,0,0.8);
    font-size: 16px;
    font-weight: 700;
    background-color: #fff;
  }
  .main-content {
    min-height: 696px;
    height: 100%;
    padding: 32px 28px 28px;
    background-color: #fff;
    // .vedio-upload {
    //   margin-top: 20px;
    // }

    .subttile-box {
      margin-bottom: 32px;
      .subttile-set-main {
        display: flex;
        align-items: center;
        margin-bottom: 12px;

        .upload-btn {
          width: 242px;
          height: 32px;
          line-height: 32px;
          border-radius: 3px;
          color: rgba(0, 0, 0, 0.9);
          border: 1px solid rgba(220, 220, 220, 1);
          margin-left: 16px;
    
          .el-icon-upload2 {
            margin-right: 10px;
          }
        }
        .subttile-tips {
          color: rgba(0, 0, 0, 0.4);
          font-size: 12px;
          margin-left: 16px;
        } 
      }
    }
  }
  .chapters-content {
    min-height: 696px;
    padding: 32px 28px 28px;
    background-color: #fff;
    height: 100%;
    .chapters-upload {
      width: 366px;
      background: #FBFBFB;
    }
  }
  .buttom {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70px;
    line-height: 70px;
    background-color: #fff;
    text-align: center;
    z-index: 99;

    .inner {
      display: inline-block;
      padding-left: 188px;
      text-align: left;

      @media screen and (max-width: 1660px) {
        width: 1158px;
      }

      @media screen and (min-width: 1661px) {
        width: 1440px;
      }

      .el-button {
        margin: 0 20px 0 0;
        width: 104px;
      }

      .tips {
        margin-left: 20px;
        color: #999;
      }
    }
    .directly-inner {

      .el-button {
        margin: 0 20px 0 0;
        width: 104px;
      }
    }
  }
}
// .chapters-config-container.directly-upload-container {
//   padding-bottom: 0;
// }