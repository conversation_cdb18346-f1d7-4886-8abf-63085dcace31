import { getRouter } from 'sdc-vue'
import VueRouter from 'vue-router'
import aiUser from './aiUser'
import aiManage from './aiManage'
import moocUser from './moocUser'
import moocManage from './moocManage'
import geekbangManage from './geekbangManage'
import geekbangUser from './geekbangUser'
import recommendedEmailManage from './recommendedEmailManage'
import other from './other'
import manage from './manage'
import transfer from './transfer'
import common from './common'
import classroom from './classroom'
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

const routes = [
  ...manage,
  ...aiUser,
  ...aiManage,
  ...moocUser,
  ...moocManage,
  ...geekbangManage,
  ...geekbangUser,
  ...transfer,
  ...recommendedEmailManage,
  ...other,
  ...common,
  ...classroom,
  // 用来测试 sdc-moocjs-integrator 组件使用方法
  // {
  //   path: '/moocjs',
  //   name: 'moocjs',
  //   component: () => import('views/user/moocjs/index.vue'),
  //   meta: {
  //     title: 'moocjs'
  //   }
  // },
  // {
  //   path: '/moocChild',
  //   name: 'moocChild',
  //   component: () => import('views/user/moocjs/child.vue'),
  //   meta: {
  //     title: 'moocChild'
  //   }
  // },
  // 暂无权限
  {
    path: '/401',
    name: '401',
    component: () => import('views/user/error/401.vue'),
    meta: {
      title: '401'
    }
  },
  // 404
  {
    path: '*',
    name: '404',
    component: () => import('views/user/error/404.vue'),
    meta: {
      title: '404'
    }
  },
  // 视频正在处理中
  {
    path: '/video-error',
    name: 'videoError',
    component: () => import('views/user/error/common-msg.vue'),
    meta: {
      title: '视频正在处理中',
      msg: '视频正在处理中，请稍候...'
    }
  },
  {
    path: '/mobile-err',
    name: 'mobile-err',
    component: () => import('views/user/error/mobile-err.vue'),
    meta: {
      title: '企微/微信内置浏览器无法使用'
    }
  },
  {
    path: '/not_exist',
    name: 'not_exist',
    component: () => import('views/user/error/not_exist.vue'),
    meta: {
      title: 'not_exist'
    }
  },
  {
    path: '/no-permission',
    name: 'no-permission',
    component: () => import('views/user/error/no-permission.vue'),
    meta: {
      title: '无权限页面'
    }
  },
  {
    path: '/detail-no-author',
    name: 'detail-no-author',
    component: () => import('views/user/error/detail-no-author.vue'),
    meta: {
      title: '暂无详情权限'
    }
  },
  {
    path: '/taskFinish',
    name: 'taskFinish',
    component: () => import('views/user/trainProject/projectDetail/child/taskFinish.vue'),
    meta: {
      title: '完成课程页'
    }
  },
  {
    path: '/user/taskFinish',
    name: 'taskFinish',
    component: () => import('views/user/trainProject/taskDetail/taskFinish.vue'),
    meta: {
      title: '完成课程页'
    }
  },
  // 问卷任务完成后的中转页
  {
    path: '/user/wjRedirect',
    name: 'wjRedirect',
    component: () => import('@/views/user/wjRedirect/index.vue')
  },
  // 导师测试页面
  {
    path: '/user/mentorTest',
    name: 'mentorTest',
    component: () => import('@/views/test/index.vue')
  },
  {
    path: '/outsourcedCourse/saml/sso',
    name: 'saml',
    component: () => import('views/user/outsourced-course/sso.vue'),
    meta: {
      title: ''
    }        
  },
  {
    path: '/outsourcedCourse/saml/ssoInput',
    name: 'ssoInput',
    component: () => import('views/user/outsourced-course/ssoInput.vue'),
    meta: {
      title: ''
    }        
  }
]

console.log('路由', routes)
export default getRouter(routes, {
  base: ['test', 'production'].includes(process.env.NODE_ENV) ? '/training' : '',
  mode: 'history'
})
