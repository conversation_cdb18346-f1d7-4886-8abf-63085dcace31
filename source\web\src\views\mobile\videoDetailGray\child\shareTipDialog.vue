<template>
  <div class="share-tip-dialog">
    <!-- 横屏 -->
    <van-dialog v-if="!portraitScreen" className="van-dialog-customer-1" overlayClass="overlay-customer" v-model="value" :showConfirmButton="false">
        <img class="warm-pic" src="@/assets/img/mobile/warm-pic-1.png" alt="分享提示" />
        <span class="confirm-button" @click="$emit('input', false)">我知道了</span>
    </van-dialog>
    <!-- 竖屏 -->
    <van-dialog v-else className="van-dialog-customer-2" overlayClass="overlay-customer" v-model="value" :showConfirmButton="false">
        <img class="warm-pic" src="@/assets/img/mobile/warm-pic-2.png" alt="分享提示" />
        <span class="confirm-button" @click="$emit('input', false)">我知道了</span>
    </van-dialog>
  </div>
</template>

<script>
export default {
  props: {
    // 是否显示提示框
    value: {
      type: Boolean,
      default: false
    },
    // 是否是竖屏
    portraitScreen: {
      type: Boolean,
      default: false        
    }
  },
  data() {
    return {

    }
  },
  mounted() {

  },
  methods: {

  }
}
</script>

<style lang="less" scoped>
.share-tip-dialog {
  /deep/.van-dialog__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    height: 100%;
  }
  .van-dialog-customer-1 {
    top: 8px;
    right: 20px;
    left: auto;
    width: 293px;
    height: 224px;
    background: rgba(255, 255, 255, 0);
    transform: rotate(90deg) translate(100%, 0%);
    transform-origin: right top;
    text-align: center;
    .warm-pic {
      width: 100%;
      height: 168px;
      margin-bottom: 20px;
    }
  }
  .van-dialog-customer-2 {
    top: 8px;
    right: 20px;
    left: auto;
    width: 277px;
    height: 240px;
    background: rgba(255, 255, 255, 0);
    transform: none;
    text-align: center;
    .warm-pic {
      width: 100%;
      height: 184px;
      margin-bottom: 20px;
    }
  }
  .confirm-button {
    width: 88px;
    height: 36px;
    line-height: 36px;
    border-radius: 32px;
    font-size: 14px;
    font-weight: 500;
    color: #ffffff;
    border: 1px solid #ffffffcc;
    background: #474747;
  }
  .overlay-customer {
    background: #00000099;
  }
}
</style>
