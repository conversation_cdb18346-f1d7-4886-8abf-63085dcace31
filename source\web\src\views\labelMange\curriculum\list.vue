<template>
  <div class="first-stage">
    <el-tabs v-model="activeName">
      <el-tab-pane
        v-for="item in tabList"
        :key="item.id"
        :label="item.label"
        :name="item.name"
        :disabled="item.disabled"
      >
        <component
          :is="item.name"
          :BGusers="BGusers"
          v-if="activeName === item.name"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import labelManagement from './labelManagement.vue'
import classificationManagement from './classificationManagement.vue'
export default {
  name: 'first-stage',
  data() {
    return {
      activeName: 'labelManagement',
      tabList: [
        { label: '标签管理', name: 'labelManagement', id: 1, disabled: false },
        {
          label: '分类管理',
          name: 'classificationManagement',
          id: 2,
          disabled: false
        }
      ],
      dialogVisible: true,
      BGusers: 0
    }
  },
  created() {},
  methods: {},
  components: {
    labelManagement,
    classificationManagement
  }
}
</script>

<style lang="less" scoped>
.first-stage {
  ::v-deep .el-tabs {
    .el-tabs__header {
      padding: 20px;
      font-size: 18px;
      line-height: 30px;
    }
    #tab-labelManagement {
      margin-bottom: 18px;
    }
    background-color: #fff;
  }
}
</style>
