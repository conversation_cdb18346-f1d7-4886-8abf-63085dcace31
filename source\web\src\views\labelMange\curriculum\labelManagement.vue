<template>
  <div class="label_management">
    <div class="labe_header">
      <el-button type="primary" size="medium" @click="Addlabel()"
        >新建标签</el-button
      >
      <el-button class="btnexc" size="medium" @click="exportdata"
        >导出明细</el-button
      >
    </div>
    <div class="labe_change">
      <div class="change_top">
        <div class="top">
          <span class="change_top_p">标签名称:</span>
          <el-input
            placeholder="请输入标签名称"
            v-model="labelname"
            clearable
            size="small"
          >
          </el-input>
        </div>
        <div>
          <!-- <span class="top_p">标签分类:</span> -->
          <div class="block">
            <span class="demonstration">标签分类</span>
            <el-cascader
              placeholder="请选择标签分类"
              size="small"
              :options="options"
              v-model="classification"
              :props="{ checkStrictly: true, emitPath: false }"
              clearable
            ></el-cascader>
          </div>
        </div>
        <div>
          <span class="top_p">标签类型:</span>
          <el-select
            clearable
            v-model="labeltypes"
            placeholder="请选择标签类型"
            size="small"
          >
            <el-option
              v-for="item in options2"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="change_bottom">
        <div class="bot_left">
          <span class="bot_p">创建时间:</span>
          <el-date-picker
            value-format="yyyy-MM-dd HH:mm:ss"
            :default-time="['00:00:00', '23:59:59']"
            range-separator="-"
            size="small"
            v-model="timnestart"
            type="datetimerange"
            start-placeholder="请选择开始时间"
            end-placeholder="请选择结束时间"
            class="time-range mr-16"
          >
          </el-date-picker>
        </div>
        <div class="bot_right">
          <el-button size="medium" @click="clean">
            <i class="el-icon-refresh"></i>重置</el-button
          >
          <el-button type="primary" size="medium" @click="search"
            >搜索</el-button
          >
        </div>
      </div>
    </div>
    <div class="label_list">
      <el-table
        @selection-change="selectionChange"
        :data="tableData"
        header-row-class-name="table-header-style"
        row-class-name="table-row-style"
        :header-cell-style="{
          background: '#f5f5f5'
        }"
      >
        <el-table-column type="selection" width="40"> </el-table-column>
        <el-table-column prop="label_type" label="标签类型">
          <template slot-scope="{ row }">
            <span>
              <el-tag :type="tagArr(row.label_type)">
                {{ statusText(row.label_type) }}
              </el-tag>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="label_id" label="标签id"></el-table-column>
        <el-table-column prop="label_name" label="标签名称"></el-table-column>
        <el-table-column prop="category_full_name" label="标签分类"></el-table-column>
        <el-table-column prop="label_course_count" label="标签课程数">
          <template slot="header">
            <label class="coursestatus">标签课程数</label>
            <el-tooltip class="item" effect="dark" placement="top">
              <div slot="content">标签课程数实时更新</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </template>
          <template slot-scope="{ row }">
            <span class="jumurllabel" @click="jum(row)">{{row.label_course_count || 0}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="creator_name" label="创建者"></el-table-column>
        <el-table-column prop="created_at" label="创建时间"></el-table-column>
        <el-table-column prop="subscribe_count" label="订阅人数"></el-table-column>
        <el-table-column prop="address" label="操作">
          <template slot-scope="scope">
            <el-button type="text" @click="setUp(scope.row)">
              {{ scope.row.label_type === 1 ? '取消官方' : '设为官方' }}
            </el-button>
            <el-button type="text" @click="sethot(scope.row)">
              {{ scope.row.label_hot_enable === 1 ? '取消热门' : '设为热门' }}
            </el-button>
            <el-button type="text" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button class="checkout" type="text" @click="deletes(scope.row)">
              删除
            </el-button>
            <span v-if="scope.row.enableFinishCourse"
              >&nbsp;&nbsp;&nbsp;&nbsp;-</span
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="table-pagination">
        <el-pagination
          :hide-on-single-page="tableParams.list.length === 0"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="tableParams.page_no"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="tableParams.page_size"
          layout="total,  prev, pager, next, sizes, jumper"
          :total="tableParams.total"
        >
        </el-pagination>
      </div>
    </div>
    <Addlabel
      :visible.sync="showlableadd"
      ref="Addlabel"
      :itemData="addleables"
      :optionslist="options"
    ></Addlabel>
    <Refinement
      :visible.sync="showrefinement"
      ref="Refinement"
      :itemData="addrefinement"
      :optionslist="options"
    ></Refinement>
    <Sethot
      :visible.sync="showsethot"
      ref="Refinement"
      :itemData="changehot"
      :selectionlist="selectionlist"
    ></Sethot>
    <labelCourse :visible.sync="showLabelCourse" :info="labelInfo"></labelCourse>
    <deleteLabel :visible.sync="showdelLabel" :itemData="delLabelInfo"></deleteLabel>
  </div>
</template>
<script>
import pager from 'mixins/pager'
import Addlabel from './AddLabel.vue'
import Refinement from './Refinement.vue'
import Sethot from './sethot.vue'
import labelCourse from './labelCourse.vue'
import deleteLabel from './deleteLabel.vue'
import {
  find_label_page,
  export_label_data,
  // del_label,
  category_tree
} from 'config/api.conf'
export default {
  mixins: [pager],
  components: {
    Addlabel,
    Refinement,
    Sethot,
    labelCourse,
    deleteLabel
  },
  data() {
    return {
      labelname: '',
      options: [],
      options2: [
        {
          value: '1',
          label: '官方'
        },
        {
          value: '2',
          label: '用户'
        },
        {
          value: '3',
          label: '机器'
        },
        {
          value: '4',
          label: '外采'
        }
      ],
      tableData: [],
      tableList: [
        { label: '标签类型', prop: 'label_type' },
        { label: '标签id', prop: 'label_id' },
        { label: '标签名称', prop: 'label_name' },
        { label: '标签分类', prop: 'category_full_name' },
        { label: '标签课程数', prop: 'label_course_count' },
        { label: '创建者', prop: 'creator_name' },
        { label: '创建时间', prop: 'created_at' },
        { label: '订阅人数', prop: 'subscribe_count' }
      ],
      classification: '',
      labeltypes: '',
      timnestart: '',
      tableParams: {
        page_no: 1,
        page_size: 10,
        total: 0,
        list: [
          { label: '标签类型', prop: 'nomineeStaffName' },
          { label: '标签名称', prop: 'name' },
          { label: '标签分类', prop: 'address' },
          { label: '标签课程数', prop: 'name1' },
          { label: '创建者', prop: 'name2' },
          { label: '创建时间', prop: 'name3' },
          { label: '订阅人数', prop: 'name4' }
        ]
      },
      showlableadd: false,
      showrefinement: false,
      showsethot: false,
      addleables: {},
      addrefinement: {},
      changehot: {},
      optionsHotlist: {},
      selectionlist: [],
      showLabelCourse: false,
      labelInfo: {},
      showdelLabel: false,
      delLabelInfo: {}
    }
  },
  created() {
    this.getlist()
    this.getcatgoryTree()
  },
  mounted() {},
  methods: {
    jum(row) {
      if ((row.label_course_count * 1) > 0) {
        this.labelInfo = row
        this.showLabelCourse = true
      }
      /* let pas = window.location.hostname.endsWith('.woa.com')
      let url = ''
      if (pas) {
        url =
          process.env.NODE_ENV === 'production'
            ? 'https://learn.woa.com' +
              '/mat/user/search?keywords=' +
              row.label_name +
              '&from_page=ql新首页&module_id=1'
            : 'https://test-learn.woa.com' +
              '/mat/user/search?keywords=' +
              row.label_name +
              '&from_page=ql新首页&module_id=1'
      } else {
        url =
          process.env.NODE_ENV === 'production'
            ? 'https://learn.woa.com' +
              '/mat/user/search?keywords=' +
              row.label_name +
              '&from_page=ql新首页&module_id=1'
            : 'http://test.v8.learn.oa.com' +
              '/mat/user/search?keywords=' +
              row.label_name +
              '&from_page=ql新首页&module_id=1'
      }
      window.open(url) */
    },
    getlist() {
      let params = {
        label_name: this.labelname,
        category_id: this.classification,
        label_type: this.labeltypes,
        start_time: this.timnestart.length !== 0 ? this.timnestart[0] : '',
        end_time: this.timnestart.length !== 0 ? this.timnestart[1] : '',
        page_no: this.tableParams.page_no,
        page_size: this.tableParams.page_size
      }
      find_label_page(params).then((res) => {
        this.tableData = res.records || []
        this.tableParams.total = res.total
      })
    },
    getcatgoryTree() {
      function processCategory(item) {
        item.label = item.category_name
        item.value = item.category_id
        if (item.sub_categories && item.sub_categories.length > 0) {
          item.children = item.sub_categories.map((es) => {
            return processCategory(es)
          })
        }
        return item
      }

      category_tree().then((res) => {
        this.options = res.map((item) => {
          return processCategory(item)
        })
      })
    },
    setUp(res) {
      this.addrefinement = res
      this.showrefinement = true
    },
    sethot(res) {
      this.changehot = res
      this.showsethot = true
    },
    handleEdit(res) {
      this.addleables = res
      this.showlableadd = true
    },
    deletes(data) {
      this.delLabelInfo = data
      this.showdelLabel = true
      // this.$messageBox
      //   .confirm('确认删除该条数据吗?', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消',
      //     type: 'warning'
      //   })
      //   .then(() => {
      //     del_label(data.label_id).then((res) => {
      //       this.$message({
      //         type: 'success',
      //         message: '已删除'
      //       })
      //       this.getlist()
      //     })
      //   })
      //   .catch(() => {
      //     this.$message({
      //       type: 'info',
      //       message: '已取消删除'
      //     })
      //   })
    },
    tagArr(status) {
      const tagArr = ['primary', 'info', 'info', 'info']
      return tagArr[status - 1]
    },
    statusText(status) {
      const textArr = ['官方', '用户', '机器', '外采']
      return textArr[status - 1]
    },
    handleSizeChange(size) {
      this.tableParams.page_size = size
      this.getlist()
    },
    handleCurrentChange(current) {
      this.tableParams.page_no = current
      this.getlist()
    },
    clean() {
      this.labeltypes = ''
      this.classification = ''
      this.labelname = ''
      this.value = ''
      this.value2 = ''
      this.timnestart = ''
      this.tableParams.page_no = 1
      this.getlist()
    },
    search() {
      this.tableParams.page_no = 1
      this.getlist()
    },
    Addlabel() {
      this.addleables = ''
      this.showlableadd = true
    },
    exportdata() {
      let params = {
        label_name: this.labelname,
        category_id: this.classification,
        label_type: this.labeltypes,
        start_time: this.timnestart.length !== 0 ? this.timnestart[0] : '',
        end_time: this.timnestart.length !== 0 ? this.timnestart[1] : ''
      }
      export_label_data(params).then((res) => {
        // this.$message({
        //   type: 'success',
        //   message: '导出成功'
        // })
      })
    },
    // 列表多选框
    selectionChange(row) {
      this.selectionlist = []
      row.map((item) => {
        this.selectionlist.push(item.label_id)
      })
      // })
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  background-color: #fff !important;
}
/deep/.el-range__close-icon {
  width: 25px;
}
/deep/.el-table {
  th > .cell {
    padding-left: 14px;
  }
}
/deep/.el-table .el-table__body td {
  color: #000 !important;
}
/deep/.time-range {
  width: 375px;
}
.el-button {
  border: none;
}
.label_hot {
  margin-right: 8px;
  margin-bottom: 5px;
  width: 23px;
  height: 23px;
}
.label_management {
  .labe_header {
    .btnexc {
      border: 1px solid #dcdcdc;
    }
    margin-left: 20px;
  }
  .demonstration {
    margin-right: 10px;
  }
  .labe_change {
    .change_top {
      .top {
        display: flex;
      }
      .change_top_p {
        line-height: 30px;
        width: 90px;
      }
      .top_p {
        padding-right: 10px;
      }
      display: flex;
      justify-content: space-between;
      margin: 10px 20px 0 20px;
      padding: 15px;
    }
    .change_bottom {
      display: flex;
      justify-content: space-between;
      margin: 0 32px 15px 35px;
      .bot_p {
        padding-right: 10px;
      }
      .bot_left {
        width: 426px;
        white-space: nowrap;
        overflow: hidden;
        border-right: 1px solid #dcdcdc;
      }
    }
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
    margin: 15px 19px;
  }
  .label_list {
    .el-table {
      border-radius: 5px;
      border: 1px solid #ebeef5;
    }
    margin: 15px;
    .fire {
      margin-right: 5px;
    }
  }
  .coursestatus {
    margin-right: 5px;
  }
}
.jumurllabel {
  color: #3464e0;
  cursor: pointer;
}
.checkout {
  color: #e34d59 !important;
}
</style>
