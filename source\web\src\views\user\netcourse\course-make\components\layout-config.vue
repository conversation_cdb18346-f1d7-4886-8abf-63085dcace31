<template>
  <div class="layout-config">
    <div class="layout">
      <img class="ppt-img" v-show="pptImg" :src="pptImg" alt="" />
      <img
        :class="`portrait portrait-bgc ${form.layout} ${form.size}`"
        :style="{ height: portraitHeight[picture_type || 'img'][form.size] }"
        v-show="!portrait_img && form.layout && form.size"
        :src="defaultImg"
      />
      <el-image
        v-show="portrait_img && picture_type === 'img' && form.layout && form.size"
        :class="`portrait ${form.layout} ${form.size}`"
        :style="{ height: portraitHeight[picture_type || 'img'][form.size] }"
        :src="portrait_img"
        fit="fill"
      >
      </el-image>
      <video
        v-show="portrait_img && picture_type === 'video' && form.layout && form.size"
        :class="`portrait ${form.layout} ${form.size}`"
        :style="{ height: portraitHeight[picture_type || 'img'][form.size] }"
        :src="portrait_img"
        muted
      ></video>
    </div>
    <el-form ref="form" :model="form" label-width="100px">
      <el-form-item label="人像位置布局：">
        <el-radio-group v-model="form.layout" @input="onLayoutChange">
          <el-radio label="left-top">左上角</el-radio>
          <el-radio label="left-bottom">左下角</el-radio>
          <el-radio label="right-top">右上角</el-radio>
          <el-radio label="right-bottom">右下角</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="人像画面尺寸：">
        <el-radio-group v-model="form.size" @input="onSizeChange">
          <el-radio label="small">较小</el-radio>
          <el-radio label="medium">适中</el-radio>
          <el-radio label="big">较大</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]

export default {
  name: 'layout-config',
  props: {
    pptImg: {
      type: String,
      default: ''
    },
    portraitData: {
      type: Object,
      default: () => {}
    },
    layoutInfo: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    portraitData: {
      handler(val) {
        this.picture_type = val.picture_type || ''
        this.portrait_img = val.picture_id
          ? `${envName.contentcenter}content-center/api/v1/content/imgage/${val.picture_id}/preview`
          : ''
      },
      deep: true
    },
    layoutInfo(val) {
      this.picture_type = val.picture_type
      this.form.layout = val.layout || ''
      this.form.size = val.size || ''
      this.portrait_img = `${envName.contentcenter}content-center/api/v1/content/imgage/${val.picture_id}/preview`
    }
  },
  data() {
    return {
      form: {
        size: '',
        layout: ''
      },
      defaultImg: require('@/assets/img/portrait.png'),
      picture_type: 'img',
      portrait_img: '',
      portraitHeight: {
        img: {
          small: '102px',
          medium: '153px',
          big: '192px'
        },
        video: {
          small: 'auto',
          medium: 'auto',
          big: 'auto'
        }
      }
    }
  },
  methods: {
    // 布局改变
    onLayoutChange(val) {
      this.$emit('onLayoutChange', val)
    },
    // 大小改变
    onSizeChange(val) {
      this.$emit('onSizeChange', val)
    }
  }
}
</script>

<style lang="less" scoped>
.layout-config {
  .layout {
    width: 726px;
    height: 409px;
    background-color: #f0f0f0;
    text-align: center;
    position: relative;
    .portrait-bgc {
      display: inline-block;
      background-color: #eee;
    }
    .ppt-img {
      width: 614px;
      height: 100%;
    }
    .portrait {
      margin-top: unset !important;
      position: absolute;
    }
    .right-bottom {
      right: 0;
      bottom: 0;
    }
    .left-bottom {
      left: 0;
      bottom: 0;
    }
    .left-top {
      left: 0;
      top: 0;
    }
    .right-top {
      right: 0;
      top: 0;
    }
    .small {
      width: 68px;
    }
    .medium {
      width: 102px;
    }
    .big {
      width: 128px;
    }
  }
  :deep(.el-form) {
    margin-top: 20px;
    .el-form-item {
      margin-bottom: 12px;
      &:last-of-type {
        margin-bottom: 0;
      }
      .el-form-item__label {
        padding-right: 0;
        color: #666;
      }
      .el-radio {
        margin-right: 16px;
        font-weight: 400;
        color: #333;
        .el-radio__label {
          padding-left: 8px;
        }
      }
    }
  }
}
</style>
