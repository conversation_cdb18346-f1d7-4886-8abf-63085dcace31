
<template>
  <div class="article-detail">
    <!-- 文章内容区域 -->
    <div class="main" v-if="!isFormMooc">
      <div class="title overflow-l2">
        <span class="tag">文章</span>{{ courseData.file_show_name }}
      </div>
      <div class="user-date">
        <span class="user">{{ courseData.creator_name }}</span>
        <span class="date">{{ courseData.created_at }}</span>
      </div>
      <!-- 附件 -->
      <div
        class="file-list"
        v-if="ancillaryList.length > 0"
      >
        <div class="header">
          <span class="total">附件：共{{ancillaryList.length}}个</span>
          <div
            class="view-all"
            @click="handleShowAll"
          >
            <span class="text">查看全部</span>
            <image
              class="icon"
              :class="showAll && 'show-all'"
              src="../../../image/networkCourse/arrow-down.png"
            ></image>
          </div>
        </div>
        <div
          v-for="item in ancillaryList"
          :key="item.id"
        >
          <div
            class="file-item"
            v-if="showAll ? true : index === 0"
            @click="lookArticle(item)"
          >
            <div class="name">{{item.name}}</div>
            <div class="type">{{item.type}}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 文章内容 -->
    <div
      class="article-card"
      v-html="graphic_text"
      @click="toLink"
    ></div>
    <!-- 听文章悬浮按钮 -->
    <!-- <movable-area class="movable-area" wx:if="{{audioUrl && !audioPlay}}">
      <movable-view class="change-audio" y="{{ floatingBoxY }}" direction="vertical" bindtap="createAudio">
        <image class="icon" src="../../../image/networkCourse/audio.png"></image>
        <span>听文章</span>
      </movable-view>
    </movable-area> -->
  </div>
</template>
<script>
// import { getImg } from '@common/api/special.js'
import { operatesignature, getMobileContentInfo } from 'config/api.conf'
import MoocJs from 'sdc-moocjs-integrator'
export default {
  props: {
    courseData: {
      type: Object
    },
    // 是否加密视频地址
    isPrivate: {
      type: Boolean,
      default: false
    },
    // 视频是否添加水印
    isWatermark: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      floatingBoxY: 0,
      graphic_id: '', // 文章id
      area_id: '', // 课单id
      share_staff_id: '', // 分享者id
      share_staff_name: '', // 分享者name
      graphic_access_record_id: '', // 访问记录id
      coverImage: '',
      viewTimer: null,
      isLike: false, // 是否点赞
      isCollect: false, // 是否收藏
      labels: [],
      articleInfo: {
        labels: [],
        authors_list: [],
        graphic_text: '',
        audio_content_id: ''
      },
      showAll: false,
      ancillaryList: [], // 附件列表
      showCourseFormPopup: false,
      courseList: [], // 课单列表
      similarItemList: [], // 相关推荐
      commentParam: {
        actId: '',
        disabled: false,
        moduleType: 'graphic'
      },
      audioUrl: '', // 音频链接
      audioPlay: false,
      isAvailable: false,
      extendedLearningShow: true,
      graphic_text: '',
      urlConfig: {
        operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
        uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
        contentinfo: `/content-center/api/v1/content/{contentId}`, // 获取内容id
        preview: `/content-center/api/v1/content/imgage/{contentId}/preview`
      },
      showIframe: false
    }
  },
  computed: {
    isFormMooc() {
      return this.$route.query.from === 'mooc' || this.$route.query.from === 'spoc'
    }
  },
  watch: {
    courseData: {
      handler(val) {
        if (val.content_id) {
          this.getContentCenter(val.content_id).then(res => {
            this.graphic_text = res.file_info.content
            if (this.isFormMooc) {
              MoocJs.play()
            }
            this.$nextTick(() => {
              this.handleContent(res.file_info.content)
            })
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取文章封面图
    getImgUrl(url) {
      // getImg(url, { imageMogr2Format: true, ci_param: 'imageView2/3/w/200/h/0/q/85' }).then((res) => {
      //   const base64Str = wx.arrayBufferToBase64(res)
      //   this.coverImage = 'data:image/png;base64,' + base64Str
      // })
    },
    // 内容中心鉴权并获取文件内容
    getContentCenter(contentId) {
      return operatesignature({
        app_id: 'QLearningService',
        content_id: contentId,
        corp_name: 'tencent',
        operate: 'visit'
      }).then(signature => {
        if (signature) {
          return getMobileContentInfo(contentId, {
            signature: signature,
            app_id: 'QLearningService'
          })
        }
      })
    },
    async handleContent() {
      // 处理图片
      this.handleImg()
      // 处理视频
      this.handleVideo()
      // 处理文件
      this.handleFiles()
    },
    handleImg() {
      const imgArr = Array.from(document.querySelectorAll('img')).filter(
        (v) => v.getAttribute('data-content')
      )
      imgArr.map((element) => {
        const contentId = element.getAttribute('data-content')
        const srcLink = this.urlConfig.preview.replace('{contentId}', contentId)
        element.setAttribute('src', srcLink)
        element.setAttribute('style', 'width: 100%')
      })
    },
    handleVideo() {
      const videoArr = Array.from(document.querySelectorAll('video'))
      videoArr.map((element) => {
        const contentId = element.getAttribute('data-content')
        if (this.isPrivate) {
          if (contentId) {
            element.style.display = 'none'
            const createEl = document.createElement('div')
            createEl.setAttribute(
              'id',
              `video-${contentId || Math.random().toString().slice(-6)}`
            )
            createEl.setAttribute('playsinline', '')
            createEl.setAttribute('webkit-playsinline', '')
            createEl.setAttribute('x5-playsinline', '')
            createEl.style.position = 'relative'
            element.parentNode.append(createEl)
            this.$nextTick(async () => {
              /* eslint-disable*/
              await new contentCenter.filePreview({
                el: `#video-${contentId}`,
                operateAuthUrl: `${this.urlConfig.operatesignature}?app_id=${this.appId}&corp_name=tencent`,
                contentId,
                appId: this.appId,
                width: '100%'
              })
              if (this.isWatermark) {
                new contentCenter.Watermark({
                  targetId: `#video-${contentId}>div`,
                  text: this.userName
                })
              }
            })
          } else {
            const id = `TCPlayer-${Math.random().toString().slice(-6)}`
            const src = element.getAttribute('src')
            const arr = src.split('.')
            const type = arr[arr.length - 1]
            element.setAttribute('id', id)
            TCPlayer(id, {
              autoplay: true,
              plugins: {
                ContextMenu: {
                  levelSwitch: {
                    open: true
                  }
                }
              },
              sources: [{ src, type: `video/${type}` }]
            })
            this.$nextTick(() => {
              if (this.isWatermark) {
                new contentCenter.Watermark({
                  targetId: `#${id}`,
                  text: this.userName
                })
              }
            })
          }
        } else {
          element.setAttribute('controlsList', 'nodownload')
          if (contentId) {
            const srcLink = this.urlConfig.preview.replace(
              '{contentId}',
              contentId
            )
            element.setAttribute('src', `${srcLink}#t=0.1`)
          }
        }
      })
    },
    handleFiles(){
      const fileArr = Array.from(
        document.querySelectorAll('[data-type="application"]')
      )
      const height = this.fileHeight

      fileArr.map((el) => {
        el.innerText = ''
        const fileName = el.getAttribute('title')
        const contentId = el.getAttribute('data-content')
        this.getContentCenter(contentId).then(res =>{
          if (res.is_successed) {
            const fileLink = res.file_info && res.file_info.doc_url
            if (fileLink) {
              if (this.showIframe) {
                el.innerHTML = `<iframe src="${fileLink}" frameborder="0" style="height:${height}" allowfullscreen="true"></iframe>`
              } else {
                el.className += ' application-error-style'
                el.innerHTML = `当前浏览器暂不支持文档预览组件，请点击按钮继续浏览文档 <a href="${fileLink}" target="_black">点击跳转</a>（${fileName}）`
              }
            } else {
              el.className += ' application-error-style'
              el.innerText = `文档链接获取失败，请刷新重试！（${fileName}）`
            }
          } else {
            el.className += ' application-error-style'
            el.innerText = `${res.status}！（${fileName}）`
          }
        })
      })
    },
    // 判断是否展示文件
    isShowIframe() {
      if (location.host.includes('woa.com')) {
        this.showIframe = true
      }
      let explorer = window.navigator.userAgent.toLowerCase()
      console.log(explorer);
      // firefox、qq、谷歌版本大于76
      if (
        explorer.indexOf('firefox') >= 0 ||
        explorer.indexOf('qqbrowser') >= 0 || explorer.indexOf('mozilla') >= 0
      ) {
        this.showIframe = true
      } else if (explorer.indexOf('chrome') >= 0) {
        let ver = parseInt(explorer.match(/chrome\/([\d.]+)/)[1])
        if (ver < 76) {
          this.showIframe = true
        } else if (!location.host.includes('woa.com')) {
          window.location.href = window.location.href.replace('.oa', '.woa')
        }
      } else {
        this.showIframe = false
      }
    },

    // 创建背景音频
    createAudio() {
      //   const bgAudio = wx.getBackgroundAudioManager()
      //   bgAudio.title = this.articleInfo.graphic_name
      //   bgAudio.singer = this.articleInfo.authorsList
      //   bgAudio.coverImgUrl = this.coverImage
      //   console.log('bgAudio：', bgAudio)
      //   console.log('播放音频地址：', this.audioUrl)
      //   // 设置了 src 之后会自动播放
      //   bgAudio.src = this.audioUrl
      //   // bgAudio.src = '//video-learn.woa.com/video/2022/24926/z7krtn5xi8.mp3?token=MjgyMTQ1fGRhYmVubGl8MTk4Nzh8MTsyfDk4Nw%3D%3D&timestamp=1684826540&sign=V9Rw3xt%2BWaFN3ViLXAhDiKfYwps%3D&index=m0'
      //   bgAudio.playbackRate = 1
      //   bgAudio.startTime = 0
      //   console.log(bgAudio)
      //   bgAudio.onCanplay(() => {
      //     this.audioPlay = true
      //   })
      //   bgAudio.onStop(() => {
      //     this.audioPlay = false
      //   })
      //   bgAudio.onEnded(() => {
      //     this.audioPlay = false
      //   })
      //   bgAudio.onError((res) => {
      //     console.log('error:', res)
      //     this.audioPlay = false
      //     wx.showToast({
      //       title: '音频加载出错~',
      //       icon: 'none',
      //       duration: 2000
      //     })
      //   })
    },
    // 获取附件数据
    getAncillaryData(e) {
      let list = e.detail.data
      list.map(v => {
        v.name = v.title.substring(0, v.title.lastIndexOf('.'))
        v.type = v.title.substring(v.title.lastIndexOf('.'))
      })
      this.ancillaryList = list
    },
    // 开启/关闭 附件
    handleShowAll() {
      this.showAll = !this.showAll
    },
    // 查看附件
    lookArticle(e) {
      // getDownloadUrlApi({
      //   content_id: e.contentId
      // }).then(res => {
      //   const downUrl = res
      //   wx.showLoading({
      //     title: '正在下载'
      //   })
      //   wx.downloadFile({
      //     url: downUrl,
      //     success: (res) => {
      //       const filePath = res.tempFilePath
      //       wx.hideLoading()
      //       wx.openDocument({
      //         filePath: filePath,
      //         showMenu: true,
      //         fail: (err) => {
      //           wx.showToast({
      //             title: err.errMsg,
      //             icon: 'none',
      //             duration: 2000
      //           })
      //         }
      //       })
      //     },
      //     fail: (err) => {
      //       wx.showToast({
      //         title: err.errMsg,
      //         icon: 'none',
      //         duration: 2000
      //       })
      //     }
      //   })
      // })
    },
    toLink(e) {
      console.log(e);
      if (e.target.tagName === 'IMG') {
        const href = e.target.getAttribute('data-href')
        const target = e.target.getAttribute('data-target')
        if (!href) return false
        if (target === '_blank') {
          window.open(href)
        } else {
          window.location.href = href
        }
      }
    },
  },
  created() {
    this.isShowIframe()
  },
}
</script>
<style lang='less' scoped>
.article-detail {
  .main {
    padding: 12px 16px 0;
    background-color: #fff;
    .title {
      line-height: 24px;
      color: #000000ff;
      font-size: 16px;
      font-weight: 600;
      .tag {
        margin-right: 12px;
        display: inline-block;
        width: 42px;
        height: 20px;
        line-height: 20px;
        border-radius: 2px;
        border: 1px solid #ff8b6cff;
        background: #ff8b6c33;
        color: #ff8b6cff;
        font-size: 14px;
        font-weight: 600;
        text-align: center;
      }
    }
    .user-date {
      margin-top: 8px;
      height: 20px;
      line-height: 20px;
      color: #00000099;
      font-size: 12px;
      .user {
        float: left;
      }
      .date {
        float: right;
      }
    }
    .count {
      height: 16px;
      line-height: 16px;
      font-size: 12px;
      margin-bottom: 20px;
      .view,
      .praise,
      .cellect {
        float: left;
        color: #00000066;
      }
      .view,
      .praise {
        margin-right: 16px;
      }
      .active-item {
        color: #0052d9;
      }

      .add-to-list {
        float: right;
        color: #0052d9ff;
      }
      .view,
      .praise,
      .cellect,
      .add-to-list {
        .icon {
          float: left;
          margin-right: 2px;
        }
      }
    }
    .file-list {
      margin-top: 16px;
      padding: 8px 12px;
      border-radius: 3px;
      background: #f7f7f7ff;
      font-size: 14px;
      .header {
        height: 20px;
        color: #00000099;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .view-all {
          display: flex;
          justify-content: space-between;
          align-items: center;
          .icon {
            margin-left: 2px;
            transform: rotate(-90deg);
            transition: transform 0.3s ease;
          }
          .show-all {
            transform: rotate(0deg);
          }
        }
      }
      .file-item {
        margin-top: 8px;
        color: #3464e0ff;
        display: flex;

        .name {
          max-width: 280px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          float: left;
        }
        .type {
          flex-shrink: 0;
          float: left;
        }
      }
    }
  }

  .movable-area {
    width: 5px;
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 999;
    .change-audio {
      width: 68px;
      height: 40px;
      padding-left: 10px;
      box-sizing: border-box;
      border-radius: 20px 0 0 20px;
      box-shadow: 0 0 12px 0 #dcdcdc99;
      color: #000000e6;
      font-size: 10px;
      background-color: #fff;
      display: flex;
      align-items: center;
      position: fixed;
      left: calc(100vw - 68px);
      .icon {
        margin-right: 4px;
      }
    }
  }
  .icon {
    display: inline-block;
    width: 16px;
    height: 16px;
  }
  // 文字超出两行省略号
  .overflow-l2 {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden; //溢出内容隐藏
    text-overflow: ellipsis; //文本溢出部分用省略号表示
    display: -webkit-box; //特别显示模式
    -webkit-line-clamp: 2; //行数
    line-clamp: 2;
    -webkit-box-orient: vertical; //盒子中内容竖直排列
  }
  .article-card {
    padding: 20px 20px 28px;
    background-color: #ffffff;
    width: 100%;
    color: #000000cc;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    word-break: break-word;
    letter-spacing: 1px;
    img{
      width: 100%;
    }
  }
}
</style>
