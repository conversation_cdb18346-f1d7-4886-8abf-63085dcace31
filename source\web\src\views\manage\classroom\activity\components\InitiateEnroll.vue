<template>
  <div class="initiate-enroll">
    <div class="tip-row font">活动已发布！可以通过以下方式分发</div>
    <div class="activity-title font">活动名：{{ shareData.title }}</div>
    <div class="link-input">
      <el-input class="w-600" v-model="linkUrl" placeholder="" readonly></el-input>
    </div>
    <div class="toolbar">
      <el-button class="btn-box" type="primary" size="small" @click="handleCopyLink()">复制链接</el-button>
      <el-button class="btn-box default-btn" size="small" @click="handleOpenLink()">打开</el-button>
      <el-button class="btn-box default-btn" size="small" v-if="false">
        <span class="icon-box">
          <img class="btn-icon" src="@/assets/classroomImg/poster.png" alt="" />
        </span>
        二维码海报
      </el-button>
    </div>
    <div class="model-box">
      <div class="qrcode-card card">
        <div class="qr-title">二维码</div>
        <div class="qrcode">
          <vue-qr
            v-if="qrCodeUrl"
            ref="qrCode" 
            :text="qrCodeUrl" 
            :size="84"
            :margin="0" 
          >
          </vue-qr>
        </div>
        <div class="tip-box">企微/微信扫码，即可转发至群聊</div>
      </div>
      <div class="share-card card">
        <div class="card-title">自定义微信分享内容</div>
        <div class="content">
          <div class="avatar">
            <img src="@/assets/img/avatar.png" alt="" srcset="" />
          </div>
          <div class="share-info">
            <div class="header">
              <img class="icon" src="@/assets/img/applet-logo.png" alt="" srcset="" />
              <span>腾讯学堂</span>
            </div>
            <div class="title">{{ shareData.title }}</div>
            <img class="cover-img" :src="shareData.photo_url" alt="">
            <div class="card-footer">
              <img class="icon" src="@/assets/img/applet-icon.png" alt="" srcset="" />
              <span>小程序</span>
            </div>
          </div>
        </div>
        <div class="footer">
          <el-button class="btn-box default-btn" size="small" @click="editShareContent()" icon="el-icon-edit">修改</el-button>
        </div>
      </div>
    </div>

    <el-dialog :visible.sync="showEditSharePopup" title="编辑分享" custom-class="activity-info-more-setting-dialog" width="580px" append-to-body :close-on-click-modal="false">
      <el-form :model="shareForm" :rules="editShareRules" ref="editShare" label-width="100px">
        <div class="setting-container">
          <el-form-item label="分享标题" prop="title" class="w-inherit">
            <el-input v-model="shareForm.title" placeholder="请填写分享内容标题" maxlength="100" class="course-name-input"></el-input>
          </el-form-item>
          <!-- <el-form-item label="分享简介" prop="text" class="w-inherit">
            <el-input type="textarea" v-model="shareForm.text" placeholder="请填写分享内容简介" :autosize='{ minRows: 3, maxRows: 6}' class="course-name-input"></el-input>
          </el-form-item> -->
          <el-form-item label="分享封面" prop="photo_url">
            <cut-img-upload 
              ref="upload" 
              @handleSuccess="handleSuccessImage"
              :dialogImageUrl="shareForm.photo_url" 
              :autoImgUrl="shareForm.photo_url" 
              @handleClearImg="handleClearImg"
              @handleImgEdit="handleImgEdit"
              :cover_imgage_storage_type="shareForm.photo_id ? 'zhihui' : 'contentcenter'"
              >
                <template v-slot:text>
                  <p>建议图片尺寸：500*400px或5:4宽高比</p>
                </template>
                <template v-slot:createImg>
                  <p class="text-orange" style="color: #E37318;cursor: pointer;display: flex; align-items: center" @click="handleAutoImg"><img class="icon" style="width:16px; height: 16px; margin-right: 4px" src="~@/assets/img/tips.png" alt="">快速生成封面图</p>
                </template>
            </cut-img-upload>
          </el-form-item>
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="shareSettingCancel">取 消</el-button>
        <el-button type="primary" @click="shareSettingConfirm">确 定</el-button>
      </span>
    </el-dialog>

    <!-- 一键生成项目封面 -->
    <sdc-img-cover 
      ref="sdcImgCoverRef" 
      :visible.sync="autoImgCoverShow" 
      :imgInfo="imgInfo"
      @handleImgCoverOk="handleImgCoverOk"
    >
    </sdc-img-cover>
  </div>
</template>

<script>
import { getShareSetting, saveShareSetting } from '@/config/classroom.api.conf.js'
import { CutImgUpload } from '@/components/index'
import { copyToClipboard } from '@/utils/tools.js'

export default {
  name: 'initiateEnroll',
  components: {
    CutImgUpload
  },
  props: {
    actId: {
      type: [String, Number]
    },
    baseInfo: {
      type: Object
    }
  },
  data() {
    return {
      linkUrl: '',
      qrCodeUrl: '',
      shareData: {},
      shareForm: {
        title: '',
        // text: '',
        photo_url: ''
      },
      editShareRules: {
        title: [
          { required: true, message: '请输入分享标题', trigger: 'blur' },
          { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
        ],
        // text: [{ required: true, message: '请输入分享简介', trigger: 'blur' }],
        photo_url: [{ required: true, message: '请上传分享封面图片', trigger: 'blur' }]
      },
      showEditSharePopup: false,
      autoImgCoverShow: false,
      imgInfo: {},
      cover_image_id: '', // 智慧封面图片id
      shareSettingData: {
        id: '',
        actType: '4',
        actId: '',
        shareType: 1,
        setting: ''
      }
    }
  },
  watch: {
    actId: {
      handler(val) {
        if (val) {
          // this.linkUrl = `https://ihr.tencent.com/f1Da3z/${val}`
          this.linkUrl = `https://sdc.qq.com/s/JZH8Eg?scheme_type=activityDetail&activity_id=${val}&jump_from=mp_qrcode&project=0&source=ql`
          this.qrCodeUrl = `https://sdc.qq.com/s/JZH8Eg?scheme_type=activityDetail&activity_id=${val}&project=0&source=ql`
          this.getShareSettingFn()
        }
      },
      immediate: true
    }
  },
  computed: {},
  created() {
  },
  mounted() {},
  beforeDestroy() {},
  methods: {
    getShareSettingFn() {
      getShareSetting({
        act_type: '4',
        act_id: this.actId,
        share_type: 1
      }).then(res => {
        this.shareSettingData = res
        let data = JSON.parse(res.setting)
        this.shareData = data
        this.shareForm = data
      })
    },
    handleClick() {
      this.$message.success('操作成功')
    },
    editShareContent() {
      this.showEditSharePopup = true
    },
    shareSettingCancel () {
      this.$refs.editShare.resetFields()
      this.showEditSharePopup = false
    },
    shareSettingConfirm () {
      this.$refs.editShare.validate(valid => {
        if (valid) {
          this.shareSettingData.act_id = this.actId
          this.shareSettingData.setting = JSON.stringify(this.shareForm)
          saveShareSetting(this.shareSettingData).then(res => {
            this.showEditSharePopup = false
            this.$message.success('保存成功')
          })
        }
      })
    },
    handleImgEdit() {
      this.$refs.sdcImgCoverRef.outEdit({
        id: this.cover_image_id,
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        type: 'outEdit'
      })
    },
    handleSuccessImage(res) {
      this.shareForm.photo_url = res
      this.shareForm.photo_id = ''
    },
    handleClearImg() {
      this.shareForm.photo_url = ''
    },
    // 一键封面回传
    handleImgCoverOk(row) {
      this.form.photo_url = row.url
      this.form.photo_id = row.id
    },
    handleAutoImg() {
      this.autoImgCoverShow = true
      this.imgInfo = {
        title: this.shareForm.title,
        env: process.env.NODE_ENV === 'production' ? 'production' : 'test',
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        showEdit: true
      }
    },
    handleCopyLink() {
      copyToClipboard(this.linkUrl)
    },
    handleOpenLink() {
      window.open(this.linkUrl)
    }
  }
}
</script>

<style lang="less" scoped>
.initiate-enroll {
  padding: 28px;
  background-color: #fff;
  .font {
    color: #000000cc;
    font-family: "PingFang SC";
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }
  .tip-row {
    font-size: 18px;
    margin-bottom: 26px;
  }
  .activity-title {
    font-size: 16px;
    margin-bottom: 20px;
  }
  .link-input {
    margin-bottom: 24px;
  }
  .toolbar {
    margin-bottom: 16px;
    .btn-box {
      min-width: 80px;
      font-size: 14px;
    }
    .default-btn {
      color: #000000e6;
    }
    .icon-box {
      width: 20px;
      height: 10px;
      margin-right: 6px;
      display: inline-block;
    }
    .btn-icon {
      width: 24px;
      height: 24px;
      position: relative;
      left: -4px;
      top: -7px;
      float: left;
    }
  }
  .model-box {
    display: flex;
    .card {
      padding: 20px;
      border-radius: 4px;
      background: #F9F9F9;
    }
    .qrcode-card {
      width: 250px;
      flex-shrink: 0;
      margin-right: 20px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      // .btn-box {
      //   width: 80px;
      //   margin: 0 auto 12px;
      // }
      .qr-title {
        width: 80px;
        height: 32px;
        line-height: 30px;
        text-align: center;
        color: #000000e6;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        border-radius: 3px;
        border: 1px solid #DCDCDC;
        background: #FFF;
        margin: 0 auto 12px;
        user-select: none;
      }
      .qrcode {
        width: 100px;
        height: 100px;
        padding: 8px;
        margin: 0 auto;
        margin-bottom: 12px;
        border-radius: 4px;
        background: #fff;
        & > img {
          background: #EFEFEF;
        }
      }
      .tip-box {
        color: #0052d9;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
    }
    .share-card {
      flex: 1;
      padding: 22px 20px 20px 38px;
      .card-title {
        color: #000000e6;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
        margin-bottom: 12px;
      }
      .content {
        display: flex;
        margin-bottom: 18px;
        .avatar {
          width: 28px;
          height: 28px;
          border-radius: 4px;
          background-color: #EFEFEF;
          margin-right: 6px;
          overflow: hidden;
          & > img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .share-info {
          min-width: 274px;
          padding: 12px 12px 6px 12px;
          background-color: #ffffff;
          border-radius: 4px;
          .header,
          .card-footer {
            font-size: 12px;
            color: #c9c9c9;
            .icon {
              width: 16px;
              width: 16px;
              margin-right: 8px;
            }
          }
          .content {
            color: #00000073;
            font-family: "PingFang SC";
          }
          .title {
            margin: 6px 0;
            color: #000000e6;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 22px;
          }
          .cover-img {
            width: 250px;
            min-height: 200px;
            overflow: hidden;
            margin: 2px 0 12px;
            object-fit: cover;
            background-color: #cdcdcd;
          }
          .card-footer {
            line-height: 24px;
            border-top: 1px solid #dfdfdf4f;
            .icon {
              width: 12px;
              width: 12px;
            }
          }
        }
      }
      .footer {
        display: flex;
        justify-content: flex-end;
      }
    }
  }
  .default-btn {
    color: #000000e6;
  }

  .w-600 {
    width: 600px;
  }
  .w-inherit {
    width: inherit;
  }
  :deep(.w-600) .el-input__inner {
    color: #00000042;
  }
}
</style>
