/* eslint-disable */
window.staffSelector = (function(){
  function initStaffSelector({ containerId = '', staffList = '', changeCallback } = {}) {
    let _staffContainerBox = null // 容器
    let enterTextarea = null // 输入框
    let outIn = null // 装人员名字的盒子和input的盒子
    let outInTagsBox = null // 装人员名字的盒子
    let afterSearchStaff = null // 搜索之后的人员候选框
    let _staffList = [] // 添加的列表
    let searchTipsNode = null // 提示文本盒子
    let serachResult = [] // 搜索结果
    let clearAll = null // 清空按钮
  
    if (!containerId) throw new Error('containerId is required!')
  
    _staffContainerBox = document.getElementById(containerId)
    _staffContainerBox.style = 'display: flex; align-items: center;position: relative;border: 1px solid #dcdcdc;cursor: pointer; width: 100%;border-radius: 4px;min-height: 40px;padding: 2px 0 2px 0;'
  
    _staffContainerBox.onmouseover = function() {
      _staffContainerBox.style.borderColor = '#acacac'
      if (_staffList.length !== 0) {
        clearAll.style.display = 'block'
      }
    }
  
    _staffContainerBox.onmouseout = function() {
      _staffContainerBox.style.borderColor = '#dcdcdc'
      clearAll.style.display = 'none'
    }
  
    _staffContainerBox.addEventListener('click', function(e) {
      if (serachResult.length !== 0) {
        afterSearchStaff.style.display = 'block'
      }
      _staffContainerBox.style.borderColor = '#3464e0'
    })
  
    // 清空全部的按钮
    clearAll = document.createElement('img')
    clearAll.src = 'https://xue.m.tencent.com/ql/common/video-bc-close.png'
    clearAll.style = 'position: absolute;right: 10px;top: 50%; transform: translateY(-50%);cursor: pointer;width: 12px;display: none;'
    clearAll.onclick = function() {
      _staffList = []
      outInTagsBox.innerHTML = ''
      sendStaffData()
    }
    _staffContainerBox.appendChild(clearAll)
  
  
    // 输入框
    enterTextarea = document.createElement('input') 
    enterTextarea.type = 'text'
    enterTextarea.style = 'outline: none;border: none;background: transparent;height: 30px;flex: 1;margin: 0 0 0 10px;width: 0.0478468%;font-size: 14px;'
  
    // 初始化搜索之后的人员候选框的样式
    afterSearchStaff = document.createElement('div')
  
    // 装选择之后人员名单的盒子
    outIn = document.createElement('div')
    outIn.style = 'display: flex;aligh-items: center;flex-wrap: wrap;max-width: 90%;width:100%;flex-shrink: 0;margin: 0 10px 0 0;'
  
    // outIn底下还要装两个节点，一个是人员的，一个是input框
    outInTagsBox = document.createElement('span')
    outInTagsBox.style = 'display: contents;'
    outIn.appendChild(outInTagsBox)
    outIn.appendChild(enterTextarea)
  
    // 搜索下拉框的错误提示
    searchTipsNode = document.createElement('div')
    searchTipsNode.style = 'text-align: center; color: #999;line-height: 45px;'
  
    _staffContainerBox.appendChild(outIn)
    document.body.appendChild(afterSearchStaff) // 直接添加body中
  
    const debouncedShowSuggestions = _debounce(_serachStaff, 1000)
  
    // 之前有数据，直接开始回显
    if (staffList && staffList.length > 0) {
      staffList.forEach(item => {
        handleChooseStaff(item)
      })
    }
  
    enterTextarea.addEventListener('input', function(e) {
      // 清除之前的人员候选框中的内容
      afterSearchStaff.innerHTML = ''
      afterSearchStaff.style.display = 'none'
      debouncedShowSuggestions(e.target.value)
    })
  
    enterTextarea.addEventListener('click', function(e) {
      if (serachResult && serachResult.length > 0) { 
        afterSearchStaff.style.display = 'block'
      }
      e.stopPropagation()
    })
  
    document.addEventListener('click', function(e) {
      // 检查点击事件的目标是否不是下拉框或其子元素
      if (!afterSearchStaff.contains(e.target)) {
        // 隐藏下拉框
        afterSearchStaff.style.display = 'none';
        enterTextarea.value = ''
      }
    }, false);
  
    function _debounce(func, wait) {
      let timeout
  
      return function(...args) {
        const context = this
  
        if (timeout) clearTimeout(timeout)
        timeout = setTimeout(function() {  
          func.apply(context, args)
        }, wait)
      }
    }
      // 处理搜索到结果之后
    function handleResult(res) {
      serachResult = res
      if (res.length === 0) {
        searchTipsNode.innerText = '无匹配数据'
        afterSearchStaff.appendChild(searchTipsNode)
        return
      }
  
      searchTipsNode.innerText = ''
  
      for (let i = 0; i < res.length; i++) {
        let divElement = document.createElement('div');
        divElement.style.height = '34px';
        divElement.style.padding = '0 20px';
        divElement.style.cursor = 'pointer';
        divElement.style.display = 'flex';
        divElement.style.alignItems = 'center';
        divElement.style.position = 'relative';
        divElement.textContent = res[i].Name;
        divElement.dataset.staffInfo = JSON.stringify(res[i]);
  
        // 如果有已添加的，置为蓝色加粗
        if (_staffList.some(item => item.ID === res[i].ID)) { 
          divElement.style.color = '#3464e0';
          divElement.style.fontWeight = 'bold';
  
          let choosed = document.createElement('img');
          choosed.src = 'https://xue.m.tencent.com/ql/common/active-add.png';
          choosed.style = 'position: absolute;right: 10px;top: 50%;transform: translateY(-50%);width: 16px;'
          divElement.appendChild(choosed);
        }
  
        divElement.onmouseover = function() {
          this.style.background = '#F5F7F9';
          this.style.color = '#3464e0';
        }
  
        divElement.onmouseout = function() {
          this.style.background = 'white';
          this.style.color = 'inherit';
  
          if (_staffList.some(item => item.ID === res[i].ID)) { 
            this.style.color = '#3464e0';
          }
        }
        afterSearchStaff.appendChild(divElement);
      }
      
  
      afterSearchStaff.addEventListener('click', function(e) {
        handleChooseStaff(JSON.parse(e.target.dataset.staffInfo))
      })
    }
  
    function _serachStaff(staffName) {
      let { width, height, top, left } = _staffContainerBox.getBoundingClientRect()
      let afterSearchStaffTop = top + height + 10
      let afterSearchStaffLeft = left
  
      searchTipsNode.innerText = '搜索中...'
      afterSearchStaff.appendChild(searchTipsNode)
      // 搜索之后的人员候选框的样式重新设置
      afterSearchStaff.style = `position: fixed;top: ${afterSearchStaffTop}px; left: ${afterSearchStaffLeft}px; background: white;z-index: 999;padding: 5px 0;display: block;border-radius: 4px;box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);border: 1px solid #E4E7ED;min-height: 45px;min-width: 258px;width: ${width}px;display: block;`
  
      const callbackName = 'jsonpCallback_' + Math.random().toString(36).substr(2, 9);
      window.staffSelector[callbackName] = function(res) {
        handleResult(res)
        // 清理全局回调函数，避免内存泄漏
        delete window.staffSelector[callbackName]
      };
  
      let requestScript = document.createElement('script');
      requestScript.src = 'https://hrc.woa.com/v2.0/pages/chooser/data/staff.aspx?q=' + encodeURIComponent(staffName) + '&limit=20&callback=window.staffSelector.'+ callbackName;
      document.body.appendChild(requestScript);
    }
  
    // 选择人员之后，将选择的员工信息展示到输入框
    function handleChooseStaff(info) {
      let { ID, Name } = info
  
      // 清除搜索框中的内容
      enterTextarea.value = ''
      // 隐藏人员候选框
      afterSearchStaff.style = 'display: none';
  
      // 如果已经添加了，不再添加
      if (_staffList.some(item => item.ID === ID)) {
        serachResult = []
        return;
      }
      // 记录选择的人员id
      _staffList.push(info)
      
  
      // 将选择到的人插入到容器
      let staffShowNode = document.createElement('span');
      
      staffShowNode.textContent = Name;
      staffShowNode.style = 'display: flex;align-items: center;background-color: #f4f4f5;border-color: #e9e9eb;color: #909399;padding: 0 10px;height: 24px;margin: 2px 0 2px 6px;flex-shrink: 0;font-size: 12px;'
  
      // 生成删除按钮
      let deleteBtn = document.createElement('i');
      deleteBtn.style = 'cursor: pointer;margin-left: 8px; display: block; width: 12px; height: 12px;font-style: normal;line-height: 12px;text-align: center;background: url(https://xue.m.tencent.com/ql/common/video-bc-close.png);background-size: 12px;'
      deleteBtn.onclick = function() {
        _staffList.splice(_staffList.findIndex(item => item.ID === ID), 1)
        this.parentElement.remove()
        sendStaffData()
      }
      staffShowNode.appendChild(deleteBtn)
  
      // 插入到容器中
      outInTagsBox.appendChild(staffShowNode)
  
      // 选完之后，清空之前搜索到的数据列表
      serachResult = []
  
      sendStaffData()
    }
  
    // 返回添加的员工信息
    function sendStaffData () {
      changeCallback && changeCallback(_staffList)
    }
  }

  return {
    initStaffSelector
  }
})()

