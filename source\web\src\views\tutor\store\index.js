import { getStore } from 'sdc-vue'
export default getStore({
  state: {
    userRole: {
      super_admin: false,
      bg_admin: false,
      dept_admin: false,
      bg_ids: '',
      dept_ids: ''
    },
    // 当前登陆用户
    userInfo: {
      staff_id: '',
      staff_name: ''
    }
  },
  mutations: {
    setUserRole(state, data) {
      state.userRole = data
    },
    setUserInfo(state, data) {
      console.log('用户信息', data)
      state.userInfo = data
    }
  },
  actions: {

  }
})
