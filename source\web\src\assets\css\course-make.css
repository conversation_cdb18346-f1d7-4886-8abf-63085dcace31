.course-make-style {
  display: flex;
  background-color: #f6f7f9;
}
.course-make-style .tab-box {
  flex: 1;
}
.course-make-style .tab-box .header {
  padding: 24px 28px;
  border-bottom: 1px solid #eeeeee;
  color: rgba(0, 0, 0, 0.8);
  font-size: 16px;
  font-weight: 700;
  background-color: #fff;
}
.course-make-style .tab-box .ul-tabs-chapter {
  height: 32px;
  line-height: 32px;
  border: 0;
  display: flex;
  margin-top: 20px;
}
.course-make-style .tab-box .ul-tabs-chapter .li-tab-item {
  padding: 0 20px;
  width: 100px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  margin: 0;
  border: 0;
  font-family: "PingFang SC";
  font-size: 14px;
  color: #00000099;
  box-sizing: border-box;
  cursor: pointer;
}
.course-make-style .tab-box .ul-tabs-chapter .li-tab-item.active {
  background-color: #ffffff;
  color: #0052d9;
  text-align: center;
  font-weight: 600;
}
.course-make-style .tab-box .content-main {
  display: flex;
}
.course-make-style .step-left {
  flex: 1;
  background-color: #fff;
  margin-bottom: 90px;
}
.course-make-style .step-left .content .tips {
  display: flex;
  padding: 28px 28px 22px;
  line-height: 20px;
}
.course-make-style .step-left .content .tips .label {
  color: #0052d9;
}
.course-make-style .step-left .content .tips .text {
  flex: 1;
  color: #999;
}
.course-make-style .step-left .content :deep(.el-steps) {
  padding: 31px 28px 24px 16px;
}
.course-make-style .step-left .content :deep(.el-steps) .el-step:last-of-type .el-step__line {
  display: unset;
}
.course-make-style .step-left .content :deep(.el-steps) .el-step .el-step__head {
  top: 6px;
}
.course-make-style .step-left .content :deep(.el-steps) .el-step .el-step__head .el-step__line {
  background-color: #e4f2ff;
}
.course-make-style .step-left .content :deep(.el-steps) .el-step .el-step__head .el-step__icon {
  bottom: 6px;
  width: 24px;
  height: 24px;
  background-color: #3464e0;
  border: unset;
  font-size: 16px;
  color: #fff;
  font-weight: bold;
  transform: scale(0.5, 0.5);
}
.course-make-style .step-left .content :deep(.el-steps) .el-step .el-step__main {
  padding-left: 8px;
}
.course-make-style .step-left .content :deep(.el-steps) .el-step .el-step__main .el-step__title {
  padding-bottom: 0;
  font-size: 16px;
  font-weight: bold;
  line-height: 24px;
  color: #333;
}
.course-make-style .step-left .content :deep(.el-steps) .el-step .el-step__main .el-step__title > span {
  display: inline-block;
  line-height: 24px;
}
.course-make-style .step-left .content :deep(.el-steps) .el-step .el-step__main .el-step__title .result-tips,
.course-make-style .step-left .content :deep(.el-steps) .el-step .el-step__main .el-step__title .cofirm-text {
  margin-left: 16px;
  font-weight: 400;
  font-size: 14px;
  color: #999;
}
.course-make-style .step-left .content :deep(.el-steps) .el-step .el-step__main .el-step__title .sumbmit-btn,
.course-make-style .step-left .content :deep(.el-steps) .el-step .el-step__main .el-step__title .demo-generat {
  margin-left: 20px;
}
.course-make-style .step-left .content :deep(.el-steps) .el-step .el-step__main .el-step__title .demo-generat {
  padding-right: 28px;
  color: #0052d9;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  background: url(~@/assets/img/loading.gif) no-repeat right / 16px 16px;
}
.course-make-style .step-left .content :deep(.el-steps) .el-step .el-step__main .el-step__description {
  padding: 20px 0 33px;
}
.course-make-style .step-left .content :deep(.el-steps) .el-step .el-step__main .el-step__description.is-finish {
  color: #333;
}
.course-make-style .chapters-content {
  min-height: 696px;
  padding: 32px 28px 28px;
  background-color: #fff;
  height: 100%;
}
.course-make-style .chapters-content .chapters-upload {
  width: 366px;
  background: #FBFBFB;
}
.course-make-style .buttom {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 70px;
  line-height: 70px;
  background-color: #fff;
  text-align: center;
  z-index: 99;
}
.course-make-style .buttom .inner {
  display: inline-block;
  padding-left: 188px;
  text-align: left;
}
@media screen and (max-width: 1660px) {
  .course-make-style .buttom .inner {
    width: 1158px;
  }
}
@media screen and (min-width: 1661px) {
  .course-make-style .buttom .inner {
    width: 1440px;
  }
}
.course-make-style .buttom .inner .el-button {
  margin: 0 20px 0 0;
  width: 104px;
}
.course-make-style .buttom .inner .tips {
  margin-left: 20px;
  color: #999;
}
