<template>
  <div class="curse-extend">
    <div>
      <!-- 课单 -->
      <div class="line-8" v-if="isShowCourse || isSpecialArea"></div>
      <courseCard class="curse-extend_course" courseType="face" v-if="isShowCourse" :courseData="courseData"></courseCard>
      <!-- 专区 -->
      <courseCard class="curse-extend_course" courseType="face" v-if="isSpecialArea" fromType="isSpecial" :courseData="courseData"></courseCard>
    </div>
    <!-- 推广--猜你喜欢 -->
    <div class="line-8" v-if="mergeList.length"></div>
    <div class="like-popularize-box" v-if="mergeList.length">
      <detailCard :courseData="courseData" courseType="activity" v-for="(item, index) in mergeList" :key="index" :isMinute="true" :cardData="item" @handleToPath="toPath" entry="advertLike"></detailCard>
      <div class="jump-btn" @click="toPage" :dt-remark="dtMore('remark', '去首页探索更多好课', '猜你喜欢')" :dt-areaid="dtMore('areaid', '去首页探索更多好课', '猜你喜欢')" :dt-eid="dtMore('eid', '去首页探索更多好课', '猜你喜欢')">去首页探索更多好课</div>
    </div>
  </div>
</template>

<script>
import detailCard from '@/views/mobile/videoDetailGray/child/detailCard.vue'
import courseCard from '@/views/mobile/videoDetailGray/child/courseCard.vue'
import { isSysBusy, guessLikeAPI, popularizeApi } from 'config/api.conf'
export default {
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    }
  },
  components: {
    detailCard,
    courseCard
  },
  data() {
    return {
      guessLikeList: [],
      advertisingList: [],
      advertisingRecord: {}
    }
  },
  created() {
    let storage = this.getStorage()
    if (storage && typeof storage === 'object') {
      this.advertisingRecord = storage
    }
    this.getSysBusyStatus()
  },
  computed: {
    course_id() {
      return this.courseData.course_id || this.$route.query.course_id || this.$route.query.activity_id
    },
    isShowCourse() {
      return (
        this.$route.query.area_id && this.$route.query.from === 'CourseList'
      )
    },
    isSpecialArea() {
      // 490是首页不显示
      return (
        this.$route.query.from === 'SpecialArea' &&
        this.$route.query.area_id &&
        this.$route.query.area_id !== '490'
      )
    },
    mergeList() {
      if (!this.advertisingList.length) return this.guessLikeList
      if (!this.guessLikeList.length) return this.advertisingList
      const maxLength = this.advertisingList.length + this.guessLikeList.length
      const result = []
      let advIndex = 0
      let guessIndex = 0
      for (let i = 0; i < maxLength; i++) {
        if (i % 2 === 0) {
          // 偶数
          if (advIndex < this.advertisingList.length) {
            result.push(this.advertisingList[advIndex++])
          } else if (guessIndex < this.guessLikeList.length) {
            result.push(this.guessLikeList[guessIndex++])
          }
        } else {
          // 奇数
          if (guessIndex < this.guessLikeList.length) {
            result.push(this.guessLikeList[guessIndex++])
          } else if (advIndex < this.advertisingList.length) {
            result.push(this.advertisingList[advIndex++])
          }
        }
      }
      console.log(result.slice(0, 5), 'resultresultresult')
      return result.slice(0, 10)
    },
    dtMore() {
      return (type, val, container) => {
        const data = {
          page: this.courseData.course_name,
          page_type: '活动详情-移动新版',
          container: `详情-${container}`,
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: val,
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'H5'
        }
        if (type === 'remark') {
          return JSON.stringify(data)
        } else if (type === 'eid') {
          return `element_${this.course_id}_${val}`
        } else {
          return `area_${this.course_id}_${val}`
        }
      }
    }
  },
  methods: {
    toPath(row) {
      // 推广数据记录
      let { id, end_time } = row
      this.advertisingRecord[id] = end_time
      this.setStorage(this.advertisingRecord)
    },
    toPage() {
      const url = `//sdc.qq.com/s/Shd3Jk?scheme_type=homepage`
      window.location.href = url
    },
    // 推广数据
    getPopularizeList() {
      const params = {
        act_type: '4',
        count: '10'
      }
      popularizeApi(params).then((res) => {
        let keyList = Object.keys(this.advertisingRecord)
        let list = []
        const moduleObj = {
          null: '',
          1: '网络课',
          2: '面授课',
          3: '直播',
          4: '活动',
          5: '码客',
          6: '行家',
          7: '案例',
          8: '文章',
          10: '培养项目',
          17: 'iwiki',
          20: 'K吧文章'
        }
        res.forEach((v) => {
          if (v.banner_type === 2) {
            v.module_name = v.recommend_module_id
              ? moduleObj[v.recommend_module_id * 1]
              : ''
          } else {
            // 活动
            v.module_name = '外链'
          }
          v.module_id = v.recommend_module_id * 1
          v.content_name = v.banner_name
          v.photo_url = v.image_url || ''
          v.content_url = v.link_url
          v.item_id = v.recommend_item_id || v.id
          v.isAdvert = true
          if (!v.duration) v.duration = 0
          if (!v.labels) v.labels = []
          // 判断当前推广是否显示以及是否过期
          if (keyList.includes(String(v.id))) {
            let endTime = this.advertisingRecord[v.id]
            let time = endTime.replace(/-/g, '/')
            if (new Date(time) < new Date()) {
              delete this.advertisingRecord[v.id]
              list.push(v)
            }
          } else {
            list.push(v)
          }
        })
        this.setStorage(this.advertisingRecord)
        this.advertisingList = list
      })
    },
    // 是否系统高峰期
    getSysBusyStatus() {
      isSysBusy().then((res) => {
        if (res !== '1') {
          this.getPopularizeList() // 推广数据
          this.getGuessLike()
        } else {
          this.getGuessLike()
        }
      })
    },
    // 猜你喜欢
    getGuessLike() {
      const params = {
        app_id: 'A9BiosXihR0h46ThNsAX',
        size: 10,
        current: 1,
        t: Date.now()
      }
      guessLikeAPI(params).then((res) => {
        this.guessLikeList = (res && res.records) || []
      })
    },
    handerClick(e) {
      if (e.link_url) {
        this.advertisingRecord[e.id] = e.end_time
        this.setStorage(this.advertisingRecord)
        window.open(e.link_url)
      } else {
        this.$message.wranning('跳转链接为空')
      }
    },
    getStorage() {
      return JSON.parse(localStorage.getItem('advertising_record'))
    },
    setStorage(val) {
      let data = JSON.stringify(val)
      localStorage.setItem('advertising_record', data)
    }
  }
}
</script>

<style lang="less" scoped>
.curse-extend {
    .curse-extend_course {
        padding: 8px 16px;
        background-color: #fff;
    }
}
  .line-8 {
    height: 8px;
    width: 100%;
    background-color: #F3F5F7;
  }
.like-popularize-box {
  background-color: #fff;
  padding: 8px 16px;
  padding-bottom: 114px;
  .jump-btn {
    border-radius: 8px;
    background-color: #ecf2fe;
    color: #0052d9;
    height: 40px;
    line-height: 40px;
    width: 156px;
    margin: 0 auto;
    text-align: center;
    margin-top: 18px;
    margin-bottom: 20px;
  }
}
</style>
