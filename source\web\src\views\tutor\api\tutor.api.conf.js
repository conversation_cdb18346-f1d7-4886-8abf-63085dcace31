import http from '../utils/http.js'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]
// 获取登陆用户信息
export const getLoginUser = () => http.get(envName.commonPath + 'training-portal-common/api/v1/portal/user/common/loginuser')
// 导师管理
// 导师列表
export const getTuTorList = (params) => http.get(`${envName.trainingPath}api/tutor/manage/tutorBase/get-tutor-base-list`, { params, loading: true })
// 导师校验数据
export const getTuTorCheck = (id) => http.get(`${envName.trainingPath}api/tutor/user/${id}/riskCheck`)
// 导师免认证录入
export const getAuthentication = (params) => http.post(`${envName.trainingPath}api/tutor/manage/tutorBase/saveWoCert`, { params })
// 导师新增录入
export const tutorAdd = (params) => http.post(`${envName.trainingPath}api/tutor/manage/tutorBase/save`, { params })
// 禁用导师
export const disabledTutor = (params) => http.post(`${envName.trainingPath}api/tutor/manage/tutorBase/disable`, { params })
// 重新认证
export const restId = (params) => http.post(`${envName.trainingPath}api/tutor/manage/tutorBase/reCert`, { params })
// 认证风险
export const getUserRisk = (id) => http.get(`${envName.trainingPath}api/tutor/manage/tutorBase/${id}/riskCheck`)
// 导出
export const tutorExportAPI = (params) => http.get(`${envName.trainingPath}api/tutor/manage/tutorBase/export-tutor-base-list`, { params, loading: true, responseType: 'blob' })
// 导师状态变更最新状态
export const getLatestStatus = (params) => http.get(`${envName.trainingPath}api/tutor/manage/tutorStatusChangeRecord/status_change_record_latest`, { params, loading: true })

// 规则配置
// 用户权限
export const getUserRole = () => http.get(`${envName.trainingPath}api/tutor/manage/tutorBase/getUserRole`)
// 门槛列表
export const thresholdList = () => http.get(`${envName.trainingPath}api/tutor/manage/rule/threshold/thresholdList`, { loading: true })
// 认证要求
export const certificationsList = () => http.get(`${envName.trainingPath}api/tutor/manage/rule/certifications`)
// 认证要求-查询单门课程信息
export const requireSingleSearch = (params) => http.get(`${envName.trainingPath}api/tutor/manage/rule/certification/findCourse`, { params })
// 认证要求保存
export const saveRequire = (params) => http.post(`${envName.trainingPath}api/tutor/manage/rule/certification/save`, { params })
// 门槛资格-保存
export const saveRule = (params) => http.post(`${envName.trainingPath}api/tutor/manage/rule/threshold/saveThreshold`, { params })
// 证书详情
export const getcertificate = () => http.get(`${envName.trainingPath}api/tutor/manage/rule/certificateImage`)
// 证书保存
export const saveCertificate = (params) => http.post(`${envName.trainingPath}api/tutor/manage/rule/certificateImage/save`, { params })
// 管理员-保存
export const saveAdmin = (params) => http.post(`${envName.trainingPath}api/tutor/manage/rule/admin/saveAdmins`, { params })
// 管理员-列表
export const adListAPI = () => http.get(`${envName.trainingPath}api/tutor/manage/rule/admin/admin_list`, { loading: true })
// 获取员工状态信息
export const getTutorStatus = () => http.get(`${envName.trainingPath}api/tutor/user/status`, { loading: true })
// 认证要求检查
export const certificationRiskCheck = () => http.get(`${envName.trainingPath}api/tutor/user/certificationRiskCheck`, { loading: true })
// 查询当前用户部门和BG管理员信息
export const currAdmins = () => http.get(`${envName.trainingPath}api/tutor/user/currAdmins`, { loading: true })
// 辅导记录
// 获取已入职员工的导师辅导记录列表
export const getTutorCounselPageOnBoard = (params) => http.get(`${envName.trainingPath}api/tutor/tutorCounselingRecord/getTutorCounselPageOnBoard`, { loading: true, params })
// 导出已入职员工的导师辅导记录列表
export const exportTutorCounselListOnBoard = (params) => http.get(`${envName.trainingPath}api/tutor/tutorCounselingRecord/exportTutorCounselListOnBoard`, { loading: true, params, responseType: 'blob' })
// 获取未入职员工的导师辅导记录列表
export const getTutorCounselPageUnBoard = (params) => http.get(`${envName.trainingPath}api/tutor/tutorCounselingRecord/getTutorCounselPageUnBoard`, { loading: true, params })
// 导出未入职员工的导师辅导记录列表
export const exportTutorCounselListUnBoard = (params) => http.get(`${envName.trainingPath}api/tutor/tutorCounselingRecord/exportTutorCounselListUnBoard`, { loading: true, params, responseType: 'blob' })
// 获取证书列表
export const getCertificateList = (params) => http.get(envName.trainingPath + `api/mooc/manage/certificate/get-certificate-list`, { params, loading: false })
