<template>
  <div class="add-banner-dialog">
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="visible"
       width="800px" 
      :close-on-click-modal="false"
      :before-close="closeDialog"
      >
        <div class="add-banner-body">
          <el-form ref="form" :model="form" :rules="rules" label-width="136px">
            <el-form-item label="活动名称：" prop="banner_name">
              <el-input
                class="width-inherit pub-p-r48"
                type="text"
                placeholder="请输入活动名称"
                v-model="form.banner_name"
                show-word-limit
              >
              </el-input>
            </el-form-item>
            <el-form-item label="活动描述：" prop="description">
              <el-input
                class="width-inherit pub-p-r48"
                type="textarea"
                :rows="2"
                maxLength="200"
                placeholder="请输入活动描述"
                v-model="form.description"
                show-word-limit
              >
              </el-input>
            </el-form-item>
            <el-form-item label="排序" prop="order_no" >
              <el-input v-model.number="form.order_no" class="w-256 mr-12" maxLength="3" max-width="30" placeholder="请输入排序" ></el-input>
              <span class="grey-text">仅支持输入1-10的数字</span>
            </el-form-item>
            <el-form-item label="活动链接：" prop="link_url">
              <el-input
                class="width-inherit"
                type="text"
                placeholder="请输入http://或者https://开头的地址"
                v-model="form.link_url"
              >
              </el-input>
              <!-- <el-button type="text" style="margin-left: 10px;" @click="toLink">替换灯塔链接</el-button> -->
            </el-form-item>
            <el-form-item label="button文案：" prop="banner_text">
              <el-input
                class="width-240 pub-p-r48"
                type="text"
                placeholder="请输入按钮文案内容"
                v-model="form.banner_text"
                show-word-limit
              >
              </el-input>
              <br>
              <span class="grey-text">Button文案限制在8个字以内</span>
            </el-form-item>
            <el-form-item label="活动生效时间：" prop="activity_time">
              <el-date-picker
                class="width-360"
                v-model="form.activity_time"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期时间"
                end-placeholder="结束日期时间">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="上传运营图：" prop="cover_image">
              <cut-img-upload 
              ref="upload"
              accept=".jpg,.jpeg,.png,.bmp"
              size="5"
              :fixedNumber="[4, 1]"
              @handleSuccess="handleSuccessImage"
              :dialogImageUrl="form.cover_image" 
              :autoImgUrl="form.cover_image_id" 
              @handleClearImg="handleClearImg"
              >
                <template v-slot:text>
                  <p>建议图片尺寸：1144px * 286px  (4 : 1)</p>
                </template>
              </cut-img-upload>
            </el-form-item>
            <el-form-item label="选择展示的邮件：" prop="busi_type">
              <el-checkbox-group v-model="form.busi_type">
                <el-checkbox v-for="(item, index) in emailType" :label="item.value" :key="index">{{ item.label }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="活动管理员：" prop="admins">
              <sdc-staff-selector multiple ref="manageAdminsSelectorRef" v-model="form.admins" :props="adminProps" placeholder="请选择管理员" @change="changeAdminsStaff" />
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="handleSave">提交</el-button>
        </div>
      </el-dialog>
  </div>
</template>
<script>
import { getImgBgColor } from '@/utils/tools.js'
import { CutImgUpload } from '@/components/index'
import { addBannerApi, updateBannerApi } from '@/config/mooc.api.conf'
import env from 'config/env.conf.js'
export default {
  components: {
    CutImgUpload
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogTitle() {
      return this.moduleName === 'add' ? '新建' : '编辑'
    }
  },
  data() {
    const orderNoValid = (rule, value, callback) => {
      if (!value && value !== 0) {
        callback(new Error('请输入排序数'))
      } else {
        // if (value > 10 || value < 1) {
        //   callback(new Error('仅支持输入1-10的数字'))
        // }
        callback()
      }
    }
    const validImg = (rule, value, callback) => {
      if (!value && !this.form.cover_image_id) {
        return callback(new Error('请选择运营图'))
      } else {
        callback()
      }
    }
    // 跳转链接校验
    const validateUrl = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入'))
      } else {
        // 需要填写协议
        if (value.indexOf('http://') === 0 || value.indexOf('https://') === 0) {
          callback()
        } else {
          callback(new Error('格式错误，请输入http或https开头的链接地址'))
        }
      }
    }
    return {
      moduleName: 'add',
      form: {
        act_type: '',
        id: '',
        order_no: null,
        banner_type: 1,
        banner_name: '',
        description: '',
        link_url: '',
        img_content_id: '',
        status: 1,
        cover_image: '',
        cover_image_id: '',
        banner_text: '',
        activity_time: [],
        busi_type: ['new', 'hot', 'label', 'online', 'live'],
        admins: []
      },
      rules: {
        banner_name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' },
          { min: 0, max: 200, message: '请输入小于200个字符', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入活动描述', trigger: 'blur' }
        ],
        order_no: [
          { pattern: /^[+]{0,1}[0-9](\d*)$/, message: '请输入正整数', trigger: 'change' },
          { required: true, type: 'number', validator: orderNoValid, trigger: ['change', 'blur'] }
        ],
        link_url: [
          { required: true, message: '请输入活动链接', trigger: 'blur' },
          { validator: validateUrl, trigger: ['change', 'blur'] }
        ],
        banner_text: [
          { required: true, message: '请输入按钮文案内容', trigger: 'blur' },
          { min: 0, max: 8, message: '按钮文案限制在8个字以内', trigger: 'blur' }
        ],
        activity_time: [
          { required: true, message: '请选择活动时间', trigger: ['change', 'blur'] }
        ],
        cover_image: [
          { required: true, validator: validImg, trigger: 'blur' }
        ],
        busi_type: [{ required: true, message: '请选择展示的邮件', trigger: 'change' }],
        admins: [{ required: true, message: '请选择活动管理员', trigger: 'change' }]
      },
      imgInfo: {},
      fileList: [],
      // emailType: ['新课榜', '热学榜', '标签订阅', 'AI智推', '标签订阅+AI智推'],
      emailType: [
        { label: '新课榜', value: 'new' },
        { label: '热学榜', value: 'hot' },
        { label: '标签订阅', value: 'label' },
        { label: '课找人', value: 'online' },
        { label: '直播预告', value: 'live' }
        // { label: 'AI智推', value: 'ai' },
        // { label: '标签订阅+AI智推', value: 'label&ai' }
      ],
      adminProps: {
        staffID: 'staff_id',
        staffName: 'staff_name'
      }
    }
  },
  mounted() {
  },
  methods: {
    initData(data) {
      console.log('initData:', data)
      this.moduleName = data.moduleName
      if (data.moduleName === 'edit') {
        const envName = env[process.env.NODE_ENV]
        this.form.id = data.id
        this.form.banner_type = data.banner_type
        this.form.banner_name = data.banner_name
        this.form.description = data.description
        this.form.order_no = data.order_no * 1
        this.form.link_url = data.link_url
        this.form.img_content_id = data.img_content_id
        if (data.image_url) {
          this.form.cover_image = data.image_url
        } else if (this.form.img_content_id) {
          this.form.cover_image = `${envName.contentcenter}content-center/api/v1/content/imgage/${this.form.img_content_id}/preview`
        } else {
          this.form.cover_image = ''
        }
        this.form.status = data.status

        this.form.banner_text = data.banner_text
        this.form.activity_time = [data.start_time, data.end_time]
        this.form.busi_type = data.busi_type.split(',')
        this.form.admins = data.admins.split(',').map(v => {
          let user = v.split('_')
          return { staff_name: user[0], staff_id: user[1] } 
        })
        this.$nextTick(() => {
          this.$refs.manageAdminsSelectorRef.setSelected(this.form.admins)
        })
      }
    },
    changeCourseAuth(e) {
      this.targetArr = e
    },
    // 管理员
    changeAdminsStaff(data) {
      this.form.admins = data
    },
    // toLink() {
    //   this.form.link_url = 'http://academymail.oa.com/#/lighthouse/index'
    //   window.open(this.form.link_url)
    // },
    // 保存
    handleSave() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const { banner_name, description, banner_type, link_url, order_no, status, id, bg_color, img_content_id, cover_image, banner_text, activity_time, busi_type, admins } = this.form
          let adminIds = admins.map(v => `${v.staff_name}_${v.staff_id}`)
          const params = {
            act_type: this.$route.query.act_type,
            banner_name,
            description,
            banner_text,
            banner_type,
            order_no,
            link_url,
            start_time: activity_time[0],
            end_time: activity_time[1],
            busi_type: busi_type.join(),
            admins: adminIds.join(),
            status,
            img_content_id: img_content_id,
            image_url: cover_image,
            bg_color
          }
          if (this.moduleName === 'add') {
            this.addBanner(params)
          } else {
            this.editBanner({ ...params, id: Number(id) })
          }
        } else {
          return false
        }
      })
    },
    addBanner(params) {
      addBannerApi(params).then(res => {
        this.$message.success(this.dialogTitle + '成功')
        this.closeDialog()
        this.$emit('onSearch')
      })
    },
    editBanner(params) {
      updateBannerApi(params).then(res => {
        this.$message.success(this.dialogTitle + '成功')
        this.closeDialog()
        this.$emit('onSearch')
      })
    },
    handleClearImg(val) {
      this.form.cover_image_id = ''
      this.form.cover_image = ''
    },
    handleSuccessImage(val, file, id) {
      this.form.cover_image = val
      // 清空一键生成封面
      this.form.cover_image_id = ''
      this.form.img_content_id = id
      if (this.form.cover_image) {
        this.$refs.form.clearValidate('cover_image')
      }
      let that = this
      getImgBgColor(URL.createObjectURL(file), function (e) {
        that.form.bg_color = e
      })
    },
    closeDialog() {
      this.$refs.form.clearValidate()
      this.$refs.upload.clearImg()
      this.form = {
        act_type: this.$route.query.act_type,
        id: '',
        banner_type: 1,
        banner_name: '',
        description: '',
        order_no: null,
        link_url: '',
        img_content_id: '',
        status: 1,
        cover_image: '',
        cover_image_id: '',
        banner_text: '',
        activity_time: [],
        busi_type: ['new', 'hot', 'label', 'online', 'live'],
        admins: []
      }
      this.$refs.manageAdminsSelectorRef.setSelected([])
      this.$emit('update:visible', false)
      this.$emit('close')
    }
  }
}
</script>
<style lang="less" scoped>
.add-banner-dialog {
  :deep(.el-dialog) {
    .el-dialog__header {
      border-bottom: 0;
    }
    .el-dialog__body {
      padding: 0;
      .el-dialog__footer {
        padding: 8px 0 24px;
      }
    }
  }
  .add-banner-body {
    padding: 10px 32px 0;
    .banner-upload {
      display: flex;
      .success-box {
        position: relative;
        height: 102px;
        border: 1px dashed #dcdcdc;
        .banner {
          width: 350px;
          height: 100px;
          line-height: 100px;
          text-align: center;
        }
        .delete-icon {
          display: none;
          position: absolute;
          top: 30px;
          left: 115px;
          color: #fff;
          font-size: 16px;
          padding: 12px;
          border-radius: 20px;
          background-color: rgba(0, 0, 0, 0.3);
          cursor: pointer;
        }
      }
      .success-box:hover {
        .delete-icon {
            display: block;
        }
      }
      .upload-btn {
        width: 350px;
        height: 100px;
        border: 1px dashed #dcdcdc;
        border-radius: 6px;
        color: #666;
        text-align: center;

        .upload-icon {
          font-size: 40px;
          margin-top: 10px;
        }
        .text {
          line-height: 20px;
        }
      }
      .desc {
        flex: 1;
        margin-left: 20px;
        width: 280px;
        line-height: 18px;
        color: #999;
        p {
            margin-top: 15px;
        }
      }
    }
  }
  .width-240 {
    width: 240px;
  }
  .width-420 {
    width: 420px;
  }
  .width-490 {
    width: 490px;
  }
  .grey-text {
    color: #00000066;
    font-size: 12px;
    line-height: 20px;
  }
}
.pub-p-r48 {
  :deep(.el-input__inner) {
    padding-right: 48px;
  }
}
</style>
