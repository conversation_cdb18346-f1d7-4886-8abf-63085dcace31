<template>
  <div id="app">
    <router-view></router-view>
    <sdc-permission-control appKey="learn-credit-service" :message="'抱歉,你没有权限'"  :checkPermPath="permissionPathList"  :componentEnv="sdcPermEnv"></sdc-permission-control>
  </div>
</template>

<script>
import { refresh, resize } from 'sdc-vue'
import { getLoginUser, getUserDep } from 'config/api.conf'
import { getUserLimits } from 'config/mooc.api.conf'
import translate from 'mixins/translate.vue'
import { loadJS } from '@/utils/tools.js'
// import lang from './../locales/lang'
import axios from 'axios'
export default {
  name: 'app',
  mixins: [refresh, resize, translate],
  data() {
    return {
      sdcPermEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test',
      aiUrlList: [
        {
          production: {
            woa: '//digger.woa.com/script/digger-2J442J0R2VVPJ20B.js',
            oa: '//digger.oa.com/script/digger-2J442J0R2VVPJ20B.js'
          },
          development: {
            woa: '//digger.woa.com/script/digger-A972CBA23494320F.js',
            oa: '//digger.oa.com/script/digger-A972CBA23494320F.js'
          },
          test: {
            woa: '//digger.woa.com/script/digger-A972CBA23494320F.js',
            oa: '//digger.oa.com/script/digger-A972CBA23494320F.js'
          }
        }
      ],
      moocUrlList: [
        {
          production: {
            woa: '//digger.woa.com/script/digger-B261E63661CD06F7.js',
            oa: '//digger.oa.com/script/digger-B261E63661CD06F7.js'
          },
          development: {
            woa: '//test-digger.woa.com/script/digger-96E90544C4929836.js',
            oa: '//test-digger.oa.com/script/digger-96E90544C4929836.js'
          },
          test: {
            woa: '//test-digger.woa.com/script/digger-96E90544C4929836.js',
            oa: '//test-digger.oa.com/script/digger-96E90544C4929836.js'
          }
        }
      ],
      checkPermPath: [
        'training/mooc/manage/project-list',
        'training/mooc/manage/banner-list',
        'training/mooc/manage/task-list',
        'training/mooc/manage/members',
        'training/mooc/manage/basic-setting',
        'training/mooc/manage/regist-setting',
        'training/mooc/manage/interactive',
        'training/mooc/manage/report',
        'training/mooc/manage/advanced-setting',
        'training/manage/material',
        'training/manage/material-play',
        'training/manage/material-upload',
        'training/manage/networkManage',
        'training/label/manage/label-list',
        'training/graphic/manage/graphic-list',
        'training/outsourcedCourse/manage/externalCourse',
        '/training/recommend/manage/banners',
        '/training/recommend/manage/course',
        'training/manage/yearRankSet'
      ]
    }
  },

  beforeCreate() {
    // 加载cdn语言包
    // this.getLangJS()
    // console.log('语言包', mooc_All_En, mooc_All_Zh)
  },
  created() {
    const url = process.env.NODE_ENV === 'production' ? `${this.commDomain}/portal-qlearning-adapter/` : `${this.commDomain}/portal-qlearning-adapter/`
    const id = process.env.NODE_ENV === 'production' ? '2029519' : '3018316'
    const commonUrl = `${url}api/adapter/common/checkTarget?target=${id}`
    axios.get(commonUrl, { withCredentials: true }).then((res) => {
      const isTarget = res.data.data
      console.log('>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>', isTarget)
      this.$store.commit('setIsTarget', isTarget)
    })
    this.loadVerify()
    this.getUser()
    this.getUserLimits()
    this.loadScript()
    this.getLangJS()
  },
  destroyed() {
    localStorage.clear()
  },
  methods: {
    // 用户角色权限
    getUserLimits() {
      getUserLimits().then((res) => {
        this.$store.commit('saveUserLimits', res)
      })
    },
    // 获取登陆用户信息
    getUser() {
      getLoginUser().then((res) => {
        const userInfo = {
          staff_id: res.staff_id,
          staff_name: res.staff_name
        }
        this.getUserDepData(res.staff_id)
        this.$store.commit('setUserInfo', userInfo)
        this.autoInstance(userInfo)
        sessionStorage.setItem('login_user', JSON.stringify(userInfo))
      })
    },
    // 获取用户所属部门
    getUserDepData(staff_id) {
      getUserDep({ staff_id }).then((res) => {
        this.$store.commit('setUserDepInfo', res)
        sessionStorage.setItem('login_user_dep', JSON.stringify(res))
      })
    },
    loadScript() {
      let srcUrlList = [
        {
          production: {
            woa: '//portal.learn.woa.com/content-center/lib/contentCenter.js',
            oa: '//ntsapps.oa.com/content-center/lib/contentCenter.js'
          },
          development: {
            woa: '//test-contentcenter.woa.com/content-center/lib/contentCenter.js',
            oa: '//dev.ntsapps.oa.com/content-center/lib/contentCenter.js'
          },
          test: {
            woa: '//test-contentcenter.woa.com/content-center/lib/contentCenter.js',
            oa: '//dev.ntsapps.oa.com/content-center/lib/contentCenter.js'
          }
        }
      ]
      const url = window.location.href.includes('mooc') ? this.moocUrlList : this.aiUrlList
      srcUrlList = srcUrlList.concat(url)
      srcUrlList.forEach((e) => {
        let scriptCon = document.createElement('script')
        scriptCon.type = 'text/javascript'
        scriptCon.src = location.hostname.endsWith('.woa.com')
          ? e[process.env.NODE_ENV]['woa']
          : e[process.env.NODE_ENV]['oa']
        document.getElementsByTagName('head')[0].appendChild(scriptCon)
      })
    },
    loadVerify() {
      loadJS(`//${location.host}/training/verify/index.js?ts=${Math.random()}`, () => {
        window.$informationReview.create({
          dev: process.env.NODE_ENV !== 'production'
        })
      })
    },
    reAuthItem(arr) {
      let data = []
      function getArr(list, prePath = '') {
        list.forEach((item) => {
          let firstPath = prePath + item.path
          if (item.meta && item.meta.isAuth) {
            data.push(firstPath)
          } else {
            if (item.children) {
              getArr(item.children, firstPath)
            }
          }
        })
      }
      getArr(arr)
      return data
    }
  },
  computed: {
    permissionPathList() {
      let routeList = []
      let routes = this.$router.options.routes
      routeList = this.reAuthItem(routes)
      // const pathList = routeList.map(routeItem => {
      //   return '/training/management/#' + routeItem
      // })
      routeList.push(...this.checkPermPath)
      console.log(routeList, routes, 'pathList-------')
      return routeList
    },
    commDomain() {
      return process.env.NODE_ENV === 'production' ? '//portal.learn.woa.com' : '//test-portal-learn.woa.com'
    }
  },
  beforeDestroy() {
    sessionStorage.removeItem('login_user')
  }
}
</script>

<style lang="less">
@import '~assets/css/app.less';
@import '~assets/css/el-style.less';
@import '~assets/css/common.less';
#app {
  font-family: @PingFangSC;
  font-weight: 400;
}
</style>
