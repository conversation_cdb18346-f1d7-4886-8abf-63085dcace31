<template>
  <div class="inter-setting">
    <el-form :model="form" ref="form">
      <el-form-item label="课后问卷" prop="wenjuan">
        <div class="question-item">
          <el-checkbox v-model="form.available">授课后学员需填写反馈问卷</el-checkbox>
          <el-button
            :class="{ 'guanlian': form.available, 'guanlian-disabled': !form.available }"
            :style="{ margin: '0 0 0 12px' }"
            size="small"
            plain
            @click="handleRelatedQuestionnaire()"
            :disabled="questionTabData.length > 0 || !form.available"
            v-if="questionTabData.length === 0"
          >创建并关联问卷</el-button>
          <div class="question-view-box" v-if="questionTabData.length > 0">
            <span class="question-label">已关联问卷:</span>
            <span class="question-name">{{ questionTabData[0]?.survey_name }}</span>
            <el-button type="text" @click="editQuestion(questionTabData[0])">查看</el-button>
            <el-button type="text" @click="handleSetRemind">设置催办&启动方式</el-button>
            <el-button
              type="text"
              @click="handleDeleteQuestion(questionTabData[0])"
              :disabled="isOldQuestion(questionTabData[0])"
              style="margin-left: 5px;"
            >移除</el-button>
          </div>
        </div>
      </el-form-item>

      <div class="extand-box">
        <div class="extand-title">
          <span class="title">课前学习</span>
          <p class="tip">此处关联的内容将在详情页中显示</p>
          <span class="addExtandItem" @click="addNewCourse('front')">+ 新增内容</span>
        </div>

        <div class="extand-table">
          <el-table :data="frontRelationContents" max-height="216px" style="width: 100%">
            <el-table-column
              prop="relation_content_name"
              label="内容标题"
              width="706"
              show-overflow-tooltip
            >
              <template slot-scope="scope" v-if="scope">
                <span
                  :class="['tags', getModuleClass(scope.row).className]"
                >{{ getModuleClass(scope.row).typeName }}</span>
                <span>{{ scope.row.relation_content_name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="right">
              <template slot-scope="scope">
                <div class="icon-btns">
                  <i class="icon-up" @click="handleUp(scope.row, scope.$index, 'front')"></i>
                  <i class="icon-delete" @click="handleDelete(scope.row, scope.$index, 'front')"></i>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <div class="extand-box">
        <div class="extand-title">
          <span class="title">课后学习</span>
          <p class="tip">此处关联的内容将在详情页中显示</p>
          <span class="addExtandItem" @click="addNewCourse('after')">+ 新增内容</span>
        </div>

        <div class="extand-table">
          <el-table :data="backRelationContents" max-height="216px" style="width: 100%">
            <el-table-column label="内容标题" width="706" show-overflow-tooltip>
              <template slot-scope="scope" v-if="scope">
                <span
                  :class="['tags', getModuleClass(scope.row).className]"
                >{{ getModuleClass(scope.row).typeName }}</span>
                <span>{{ scope.row.relation_content_name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="right">
              <template slot-scope="scope">
                <div class="icon-btns">
                  <i class="icon-up" @click="handleUp(scope.row, scope.$index, 'back')"></i>
                  <i class="icon-delete" @click="handleDelete(scope.row, scope.$index, 'back')"></i>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <!-- <p class="tips1">暂未开启课堂互动功能，点击按钮即可启用</p>
      <div>
        <el-button type="primary" class="course-hudong">启用课堂互动</el-button>
      </div>
      <p class="tips2"><span>课堂互动功能</span>，将为你提供以下能力</p>

      <div class="intro-meeting">
        <div class="intro-meeting-item">
          <p class="title">腾讯会议+小程序集成：学员在线上线下均可参与互动式</p>
          <p class="desc">支持腾讯会议内置应用+小程序访问,腾讯会议中，学员无需切换窗口，即可在会议客户端内参与课堂互动。线下培训时，扫码即可小程序参与互动</p>
          <div class="intro-meeting-item-img">
            <img :src="require('@/assets/img/meeting1.png')" alt="">
            <img :src="require('@/assets/img/meeting2.png')" alt="">
          </div>
        </div>
        <div class="second-box">
          <div class="intro-meeting-item">
            <p class="title">学员分组和积分功能 ：个人积分、小组积分灵活管理</p>
            <p class="desc">可提前配置学员名单，并对学员进行分组操作。培训过程中，可为学员或小组通过人工或自动方式发放积分、用于评比和激励。</p>
            <div class="intro-meeting-item-img">
              <img :src="require('@/assets/img/meeting3.png')" alt="">
            </div>
          </div>

          <div class="intro-meeting-item">
            <p class="title">丰富的互动能力：学员可提问、讲师可发起抢答、投票</p>
            <p class="desc">学员可以在线提交疑问，供讲师在答疑环节统一解答。讲师/班主任可提前配置抢答/投票互动，与线上下学员进行互动</p>
            <div class="intro-meeting-item-img">
              <img :src="require('@/assets/img/meeting3.png')" alt="">
            </div>
          </div>
        </div>
      </div>-->
    </el-form>
    <!-- 问卷确认 -->
    <AddQuestionnairConfirmDialog
      :visible.sync="addQuestionnairConfirmDialogShow"
      :questionType="questionType"
      @continueCreateQuestionnaire="continueCreateQuestionnaire"
      v-if="addQuestionnairConfirmDialogShow"
    />

    <!-- 添加课程 -->
    <addActivityCourseDialog
      v-if="addLineCourseShow"
      ref="addLineCourse"
      :visible.sync="addLineCourseShow"
      :extendOptions="extendOptions"
      :addNewCourseType="addNewCourseType"
      @handleShowSetDialog="handleShowSetDialog"
    />

    <!-- 问卷管理 -->
    <questionManageDialog
      v-if="isShowQuestionManage"
      :visible.sync="isShowQuestionManage"
      :sid="selfQuestionData.length > 0 ? selfQuestionData[0].survey.wj_id : ''"
      :type="type"
      @getQuestionData="getQuestionData"
      :questionType="isEmpty ? 'add' : 'edit'"
      :otherOption="otherOption"
      :activityName="baseInfo.activity_name"
    ></questionManageDialog>

    <remind
      v-if="isShowRemind"
      :visible.sync="isShowRemind"
      @getRemindInfo="getRemindInfo"
      :remindInfo="remind_setting"
      :isShowStartMode="true"
      :startMode="questionTabData[0].is_auto_start"
      :time="{
        start: baseInfo.start_time,
        end: baseInfo.end_time
      }"
    ></remind>
  </div>
</template>

<script>
import AddQuestionnairConfirmDialog from '@/views/manage/mooc/project-manage/task-list/component/add-questionnairConfirmDialog.vue'
import { qlearningModuleTypes } from 'utils/constant'
import addActivityCourseDialog from './addActivityCourseDialog.vue'
import { createQuestionApi } from '@/config/classroom.api.conf.js'
import questionManageDialog from '@/views/components/questionManageDialog.vue'
import remind from '@/views/manage/classroom/activity/edit/components/remind.vue'
import { mapState } from 'vuex'
import { actTypes } from 'utils/map'
export default {
  props: {
    baseInfo: {
      type: Object,
      default: () => {}
    }
  },
  watch: {
    baseInfo: {
      handler(newVal) {
        if (newVal) {
          this.setBaseInfo()
        }
      },
      immediate: true
    },
    questionTabData: {
      handler(newVal) {
        this.isEmpty = newVal.map(item => item.category === 1).length === 0
      },
      immediate: true
    }
  },
  data() {
    return {
      form: {
        available: false
      },
      addQuestionnairConfirmDialogShow: false,
      questionType: 'showList',
      frontRelationContents: [], // 课前关联内容
      backRelationContents: [], // 课后关联内容
      questionTabData: [],
      curOperateNode: {},
      addLineCourseShow: false,
      addNewCourseType: '',
      extendOptions: {
        showAddOutLink: true,
        banSameCourse: true,
        sameCourseList: []
      },
      questionAddress: {
        development: '//test-learn.woa.com',
        test: '//test-learn.woa.com',
        production: '//learn.woa.com'
      },
      isShowQuestionManage: false,
      isEmpty: true,
      selfQuestionData: [],
      type: 'after',
      otherOption: {
        mode: 'normal',
        customAdd: false,
        customUrl: ''
      },
      remind_setting: {},
      isShowRemind: false
    }
  },
  computed: {
    oldQuestionAddress() {
      return this.questionAddress[process.env.NODE_ENV]
    },
    ...mapState({
      activityInfo: state => state.activity.activityInfo || {}
    })
  },
  components: {
    AddQuestionnairConfirmDialog,
    addActivityCourseDialog,
    questionManageDialog,
    remind
  },
  created() {},
  methods: {
    getRemindInfo(data) {
      const { remindData = {}, startMode = 1 } = data
      let remindInfo = this.handleRemindInfo(remindData)
      this.remind_setting = remindInfo
      this.questionTabData[0].is_auto_start = startMode
    },
    handleSetRemind() {
      this.isShowRemind = true
    },
    isOldQuestion(row) {
      const { wj_id = '', wj_url = '' } = row
      return !wj_id && !wj_url
    },
    handleDeleteQuestion(val) {
      if (this.isOldQuestion(val)) {
        return
      }
      this.questionTabData = this.questionTabData.filter(
        item => item.survey_id !== val.survey_id
      )
    },
    getModuleClass(data) {
      const { relation_module_id = '', relation_act_type = '' } = data
      const name =
        actTypes.find(item => item.act_type === relation_act_type)
          ?.act_type_name || ''
      if (relation_module_id === 99) {
        return {
          className: 'tag-link',
          typeName: name
        }
      }
      if (!relation_module_id) {
        return {
          className: 'tag-net',
          typeName: '综合'
        }
      }
      let cardType = qlearningModuleTypes.find(
        item => relation_module_id === item.module_id
      )
      if (cardType) {
        return {
          className: cardType.moduleClassName,
          typeName: name
        }
      } else {
        return {
          className: 'tag-net',
          typeName: name
        }
      }
    },
    addNewCourse(type) {
      this.addNewCourseType = type
      this.addLineCourseShow = true
    },
    handleShowSetDialog(data) {
      let newData = []
      newData = data.map(item => {
        return {
          relation_content_name: item.content_name || '',
          relation_content_url: item.resource_url || '',
          relation_act_type: item.act_type || '',
          relation_module_id: item.module_id || '',
          relation_act_id: item.act_id || '',
          relation_cover_img_url: item.photo_url || '',
          relation_view_count: item.view_count_total || '',
          relation_avg_score: item.avg_score || '',
          relation_created_time: item.created_at || '',
          relation_content_module_name: item.module_name || '',
          finished_condition_desc: item.finished_condition_desc || '',
          relation_content_url_mobile: item.content_mobile_url || '',
          relation_content_desc: item.relation_content_desc || '',
          relation_content_resource_type:
            item.relation_content_resource_type || '',
          wechat_mini_appid: item.wechat_mini_appid || ''
        }
      })
      if (this.addNewCourseType === 'front') {
        this.frontRelationContents.push(...newData)
      } else {
        this.backRelationContents.push(...newData)
      }
      this.extendOptions.sameCourseList = [
        ...this.frontRelationContents,
        ...this.backRelationContents
      ].map(v => {
        return {
          item_id: String(v.relation_act_id),
          act_type: String(v.relation_act_type)
        }
      })
    },
    handleRelatedQuestionnaire() {
      if (!this.activityInfo.activity_name) {
        this.$message.error('活动名称不能为空')
        return
      }
      this.addQuestionnairConfirmDialogShow = true
    },
    continueCreateQuestionnaire() {
      this.addQuestionnairConfirmDialogShow = false
      this.createQuestion()
    },
    createQuestion() {
      createQuestionApi({
        survey_name: `${this.activityInfo.activity_name || ''}-课后问卷`,
        survey_desc:
          '感谢您参与本次活动，我们非常重视每位学员的宝贵意见，期待您的反馈！'
      })
        .then(res => {
          if (!res) {
            this.$message.error('问卷创建失败, 请稍后再试')
            return
          }
          this.otherOption.customUrl = res += '&mode=qlearningCheckData'
          this.isShowQuestionManage = true
        })
        .catch(() => {
          this.$message.error('问卷创建失败, 请稍后再试')
        })
    },
    editQuestion(val) {
      if (this.isOldQuestion(val)) {
        let id = this.$route.query.activity_id || ''
        window.open(
          `${this.oldQuestionAddress}/manage/activity/survey?activity_id=${id}`
        )
        return
      }
      this.selfQuestionData = [{ survey: val }]
      this.isShowQuestionManage = true
    },
    getQuestionData(data) {
      if (this.questionTabData && this.questionTabData.length) {
        data.survey_id = this.questionTabData[0].survey_id
      }
      const {
        act_id = '',
        act_name = '',
        resource_url = '',
        startMode = 1,
        remindSetting = {}
      } = data
      let obj = {
        wj_id: act_id,
        wj_url: resource_url,
        survey_name: act_name,
        survey_id: '', // 新增为空，编辑沿用之前拿到的（给数据库更新用作标识）
        // status: 1, // 1 勾选了，3 未勾选
        category: 1,
        is_auto_start: startMode
      }
      if (remindSetting) {
        this.remind_setting = this.handleRemindInfo(remindSetting)
      }

      this.questionTabData = [obj]
    },
    // 置顶
    handleUp(row, index, type) {
      if (type === 'front') {
        this.frontRelationContents.unshift(
          this.frontRelationContents.splice(index, 1)[0]
        )
      } else {
        this.backRelationContents.unshift(
          this.backRelationContents.splice(index, 1)[0]
        )
      }
    },
    handleRemindInfo(data) {
      let {
        message_type,
        time_1,
        time_2,
        reminder_fixed_week_time,
        reminder_gap_time,
        reminder_fixed_send_times,
        date,
        addWeekList,
        enabled_remaind,
        reminder_task_id
      } = data
      let reminder_send_times = reminder_gap_time ? time_1 : time_2
      let reminder_begin_time, reminder_end_time
      if (message_type && message_type.length > 0) {
        message_type = message_type.join(';')
      }
      if (addWeekList && addWeekList.length > 0) {
        reminder_fixed_send_times = addWeekList
          .map(item => {
            return item.value
          })
          .join(';')
      }
      if (date && date.length > 0) {
        reminder_begin_time = date[0]
        reminder_end_time = date[1]
      }
      return {
        reminder_begin_time, // 起止催办时间 - 开始时间
        reminder_end_time, // 起止催办时间 - 结束时间
        reminder_send_times, // 间隔天数的具体时间
        reminder_fixed_send_times, // 固定时间催办 格式 xx:xx:xx;aa:aa:aa
        reminder_fixed_week_time, // 每周 - 周几催办 ？
        reminder_gap_time, // 每周 - 周几的具体时间
        message_type: message_type, // 催办渠道
        enabled_remaind: enabled_remaind,
        reminder_task_id
      }
    },
    // 删除
    handleDelete(row, index, type) {
      if (type === 'front') {
        this.frontRelationContents.splice(index, 1)
      } else {
        this.backRelationContents.splice(index, 1)
      }
    },
    setBaseInfo() {
      this.$nextTick(() => {
        if (
          Array.isArray(this.baseInfo.surveys) &&
          this.baseInfo.surveys.length
        ) {
          const filteredSurveys =
            this.baseInfo.surveys?.filter(v => v.category === 1) || []
          this.form.available = filteredSurveys.some(item => item.status === 1)
          this.questionTabData = filteredSurveys
        } else {
          this.form.available = false
        }
        if (this.baseInfo.remind_setting) {
          this.remind_setting = JSON.parse(this.baseInfo.remind_setting)
        }
        this.frontRelationContents = this.baseInfo.front_relation_contents
          ? this.baseInfo.front_relation_contents.sort(
            (a, b) => a.order_no - b.order_no
          )
          : []
        this.backRelationContents = this.baseInfo.back_relation_contents
          ? this.baseInfo.back_relation_contents.sort(
            (a, b) => a.order_no - b.order_no
          )
          : []
      })
    },
    submit({ isDraft = false } = {}) {
      // this.questionTabData.forEach(item => {
      //   item.status = this.form.available ? 1 : 3
      // })
      return new Promise(resolve => {
        resolve({
          isPass: true,
          data: {
            ...this.form,
            front_relation_contents: this.frontRelationContents.map(
              (item, index) => {
                return {
                  ...item,
                  order_no: index
                }
              }
            ),
            back_relation_contents: this.backRelationContents.map(
              (item, index) => {
                return {
                  ...item,
                  order_no: index
                }
              }
            ),
            surveys: this.questionTabData,
            remind_setting: JSON.stringify(this.remind_setting)
          }
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.inter-setting {
  padding: 33px 28px 40px 48px;
  background-color: #fff;

  .question-item {
    display: flex;
    align-items: center;
    height: 39px;
  }
  .guanlian {
    background: #fff;
    color: #0052d9;
    border-color: #0052d9;
  }

  .guanlian-disabled {
    background: #fff;
    color: #999;
  }

  .question-view-box {
    display: flex;
    align-items: center;
    margin-left: 15px;
    .question-label {
      margin-right: 10px;
      color: #999;
    }
    .question-name {
      margin-right: 12px;
      max-width: 150px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #000;
      font-weight: 700;
    }
  }

  .extand-box {
    margin-top: 23px;
    .extand-title {
      display: flex;
      align-items: center;
      padding-right: 16px;
      margin-bottom: 8px;

      .title {
      }

      .tip {
        color: #00000099;
        font-size: 14px;
        margin-left: 12px;
      }

      .addExtandItem {
        color: #0052d9;
        font-size: 14px;
        cursor: pointer;
        margin: 0 0 0 auto;
      }
    }
  }
  .extand-table {
    padding: 0 0 0 58px;
    margin: 16px 0 0 0;
    :deep(.el-table) {
      border-left: 1px solid #EBEEF5;
      border-right: 1px solid #EBEEF5;
      th {
        background: rgba(245, 245, 245, 1);
        font-weight: 500;
        border-bottom: solid 1px #eeeeee;
      }
      .el-table__header {
        background: #efefef;
        height: 52px;
        .cell {
          padding: 0 16px;
        }
      }

      th,
      td {
        height: 36px;
        padding: unset;
        color: #000000e6;
        border-right: 1px solid #EBEEF5;
      }

      td {
        color: #000000e6;
        border-bottom: 1px solid #EBEEF5;
      }
    }
    .operat-btn-box {
      i {
        & + i {
          margin-left: 20px;
        }
        font-size: 16px;
        width: 16px;
        height: 16px;
        display: inline-block;
        font-size: 16px;
        cursor: pointer;
        margin: 0 8px;
      }
      .view-icon {
        color: #989898;
        &:hover {
          color: #0052d9;
        }
      }

      .edit-icon {
        background: url('~@/assets/img/edit-allow.png') no-repeat center/cover;
        &:hover {
          background: url('~@/assets/img/edit-icon-hover.png') no-repeat
            center/cover;
        }
      }

      .icon-delete {
        background: url('~@/assets/img/icon-delete.png') no-repeat center /
          cover;
        &:hover {
          background: url('~@/assets/img/del-active.png') no-repeat center /
            cover;
        }
      }
      .ban {
        cursor: not-allowed;
        pointer-events: none;
        &.edit-icon {
          background: url('~@/assets/img/edit.png') no-repeat center/cover !important;
        }
        &.icon-delete {
          background: url('~@/assets/img/del-1.png') no-repeat center/cover !important;
        }
      }
    }
    .tags {
      margin: 0 12px 0 10px;
      display: inline-block;
      height: 18px;
      padding: 0 7px;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      border: 1px solid;
      border-radius: 4px;
      &.tag-net {
        color: #0b8bff;
      }
      &.tag-music {
        color: #04aef6;
      }
      &.tag-live {
        color: #fe5d34;
      }
      &.tag-article {
        color: #42c55b;
      }
      &.tag-note {
        color: #40c19d;
      }
      &.tag-exam {
        color: #ff6600;
      }
      &.tag-hangjia {
        color: #ff8900;
      }
      &.tag-face {
        color: #ffa31a;
      }
      &.tag-activity {
        color: #ffbc03;
      }
      &.tag-marker {
        color: #00a99d;
      }
      &.tag-word {
        color: #0052d9;
      }
      &.tag-link {
        color: #a65ad4;
      }
    }
    :deep(.el-table--scrollable-x .el-table__body-wrapper) {
      overflow-x: hidden;
    }
  }

  .tips1 {
    margin: 36px 0 0 0;
    font-size: 14px;
    line-height: 22px;
    color: #666666;
  }

  .course-hudong {
    margin: 24px 0 0 0;
    font-weight: 600;
  }

  .tips2 {
    margin: 33.5px 0 0 0;
    font-size: 14px;
    line-height: 22px;
    color: #000000cc;
    span {
      color: #0052d9;
    }
  }

  .intro-meeting {
    margin: 8px 0 0 0;
    .intro-meeting-item {
      padding: 20px 17px 20px 16px;
      background: linear-gradient(90deg, #fff -1.04%, #fafdfff5 98.51%);
      border-radius: 4px;
      border: 1px solid #eee;
      .title {
        font-size: 14px;
        color: #333333;
        line-height: 20px;
        font-weight: 600;
      }
      .desc {
        margin: 8px 0 0 0;
        font-size: 12px;
        color: #666666;
        line-height: 20px;
      }
      .intro-meeting-item-img {
        margin: 16px 0 0 0;
        display: flex;
        justify-content: space-between;
        gap: 0 16px;
        img {
          width: 412px;
        }
      }
    }

    .second-box {
      margin: 16px 0 0 0;
      display: flex;
      justify-content: space-between;
      gap: 0 16px;
    }
  }
}
</style>
