<template>
  <div class="account-page">
    <div class="banner-card" :style="bannerImgTop('top')">
      <div class="banner-content" :style="bannerImgTop('center')">
        <div class="title"><el-button class="common" type="primary" plain @click.stop="toRulesDetail">活动规则</el-button> </div>
        <div class="desc">
          参与活动，即可获得《哈佛精品文库》500+篇文章的阅读权限！
        </div>
      </div>
    </div>
    <div class="account-content">
      <div class="account-authority" :style="shareAuth ? 'border-radius:8px 8px 0 0 ' : '' ">
        <div class="authority-status">
          <div class="study-a"> <img :src="require('@/assets/outsourcedCourse/learn.png')" alt=""><span class="study-text"> 学习权限：</span> <span v-if="cardInfo.balance"><i class="el-icon-success"></i> 已开启</span> <span v-else><i class="el-icon-error"></i> 未开启</span> </div>
          <div class="study-a"> <img :src="require('@/assets/outsourcedCourse/share-a.png')" alt=""><span class="study-text">分享权限：</span> <span v-if="shareAuth"><i class="el-icon-success"></i> 已开启</span> <span v-else><i class="el-icon-error"></i> 未开启</span> </div>
        </div>
        <div class="demand-button" v-if="!cardInfo.balance" @click="applyperm"> {{record ? '已提交需求' : '暂未开启学习权限，可点此提交需求'}}</div>
      </div>
      <div class="invite-main" id="invite-main_id" v-if="shareAuth">
        <div class="invite-main-border"></div>
        <div class="invite-main_title">限时分享：邀请同事参与学习，为其免费开通价值<span style="color:red">¥1200</span>的《哈佛精品文库》阅读权限</div>
        <!-- <div class="give-explain-content">
          除了自己学习，您还可以通过点击下方“邀请同事”按钮，邀请您的同事一起参与学习，对方接受邀请后，将获得与您同等的阅读和分享权限。数量有限，先到先得！
        </div> -->
        <div class="tab-content">
          <div class="tab-content-tips">
            当前已成功邀请 <span class="color_b">{{total}}</span> 名同事
          </div>
          <div class="tab-content-list">
            <el-tooltip content="暂无剩余名额" :disabled="isQuantity" placement="top" effect="light">
              <span>
                <div class="tab-content-list_left" :style="!isQuantity ? 'opacity: 0.2;' : '' " @click="openGive">
                  <img :src="require('@/assets/outsourcedCourse/invite-ha.png')" alt="">
                  <span>邀请同事</span>
                </div>
              </span>
            </el-tooltip>
            <div class="tab-content-list_right" v-if="presentRecordList.length">
              <swiper class="mySwiper" :style="presentRecordList.length > 3 ? 'margin: 0 36px;' : '' " :options="swiperOption" :space-between="50">
                <div class="swiper-button-prev" slot="button-prev" :style="presentRecordList.length > 3 ? '' : 'display:none;' ">
                </div>
                <!-- @click="next" -->
                <div class="swiper-button-next" slot="button-next" :style="presentRecordList.length > 3 ? '' : 'display:none;' "></div>
                <swiper-slide v-for="(item, index) in presentRecordList" :key="index">
                  <div class="tab-content_item">
                    <div class="tab-content_item-left"><img :src="userImgUrl(item.staff_name)" alt=""></div>
                    <div class="tab-content_item-right">
                      <div class="name">邀请对象：{{item.staff_name}}</div>
                      <div class="number1">邀请时间：{{createTime(item.created_at)}}</div>
                    </div>
                  </div>
                </swiper-slide>
                <swiper-slide class="swiper-slide_more" v-if="total > presentRecordList.length">
                  <div class="tab-content_more" @click="handlerLoadingMore">
                    查看更多
                  </div>
                </swiper-slide>
              </swiper>
            </div>
          </div>
        </div>
      </div>
      <!-- 精选好文 -->
      <div class="course-list" v-if="(courseList || []).length">
        <div class="title-col">
          <span class="title">精选好文</span>
          <!-- <el-tabs v-model="activeName" @tab-click="handleClick">
            <el-tab-pane label="全部好文" name="0"></el-tab-pane>
            <el-tab-pane :label="item.value" :name="item.key" v-for="(item, index) in lableTabs" :key="index"></el-tab-pane>
          </el-tabs> -->
          <span class="link" @click="toActiveDetail" :dt-areaid="dtMoerCourses('area', '1')" :dt-eid="dtMoerCourses('eid', '1')" :dt-remark="dtMoerCourses('remark', '1')">查看更多</span>
        </div>
        <div class="list">
          <div class="course-item" v-for="(item, index) in (courseList || [])" :key="item.course_id" @click="toCourseDetail(item.course_url)" :dt-areaid="dtListBody(item, 'area', index)" :dt-eid="dtListBody(item , 'eid', index)" :dt-remark="dtListBody(item , 'remark', index)">
            <el-image class="cover" :src="getCourseCoverUrl(item.course_pic_id)">
              <div class="image-slot" slot="placeholder">
                <i class="el-icon-loading"></i>
              </div>
              <div class="error-cover" slot="error">
                <img class="image-box" :src="require('@/assets/outsourcedCourse/movie.png')" alt="" />
              </div>
            </el-image>
            <div class="time" v-if="item.course_length">{{item.course_length}}分钟</div>
            <div class="title">
              <span>{{item.course_from_name}}</span>{{ item.course_name }}
            </div>
          </div>
        </div>
        <div class="link" @click="toActiveDetail" :dt-areaid="dtMoerCourses('area', '2')" :dt-eid="dtMoerCourses('eid', '2')" :dt-remark="dtMoerCourses('remark', '2')">
          <span>点击查看更多</span>
        </div>
      </div>
    </div>
    <activityShare :isShow.sync="giveDialog" width="504px" top="20%"></activityShare>
    <pickUpPC :visible.sync="pickUpVisible" :isQuantity="isQuantity" @handlerReceive="handlerReceive"></pickUpPC>
  </div>
</template>

<script>
import env from 'config/env.conf.js'
import {
  getAcctinfos,
  getUserActiveInfo,
  getHomePageInfo,
  getPresentRecord,
  checkShareAuth,
  activityPresent,
  getRequestRecord,
  messageSubscribe,
  getSubscribeStatus
} from '@/config/mooc.api.conf.js'
import moment from 'moment'
import activityShare from './components/activityShare.vue'
import pickUpPC from './components/pickUpPC.vue'
const HTTPS_REG = /^https:\/\//
export default {
  components: {
    activityShare,
    pickUpPC
  },
  data() {
    return {
      pickUpVisible: false,
      record: false,
      activeName: 0,
      total: 0,
      current: 1,
      courseList: [],
      recommendMap: {},
      lableTabs: [],
      consumePoint: 0,
      giveDialog: false,
      shareAuth: false,
      cardInfo: {},
      presentRecordList: [],
      xueBaCardConfig: {},
      userInfo: {},
      swiperOption: {
        loop: false, // 是否循环轮播
        speed: 1000, // 切换速度
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        centeredSlides: false,
        spaceBetween: 12,
        // 设置slider容器能够同时显示的slides数量
        slidesPerView: 3,
        // 左右切换
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      },
      isFirst: false
    }
  },
  async created() {
    await this.getUserActiveInfo()
  },
  computed: {
    // 有权限无权限
    isPermission() {
      return this.cardInfo.balance
    },
    cardName() {
      return this.xueBaCardConfig.card_name || ''
    },
    activityId() {
      return this.$route.query.activityId || 1
    },
    // 有无分享整体名额
    isQuantity() {
      const { quantity = 0 } = this.xueBaCardConfig
      return quantity > 0
    },
    // 头部背景图
    bannerImgTop() {
      return (type) => {
        const { banner_img_top } = this.xueBaCardConfig
        if (!banner_img_top) return
        return type === 'top'
          ? `background-image:url(${banner_img_top.banner_bg})`
          : `background-image:url(${banner_img_top.banner_center})`
      }
    },
    userImgUrl() {
      return (val) => {
        if (!val) return ''
        let staffname = val.split(',')[0].split('(')[0]
        return `https://learn.woa.com/rhrc/photo/150/${staffname}.png`
      }
    },
    createTime() {
      return (date) => {
        return moment(date).format('YYYY-MM-DD')
      }
    },
    dtMoerCourses() {
      return (type, index) => {
        let { audience_id, audience_name } = this.xueBaCardConfig
        if (type === 'area') {
          return `area_${audience_id}_more_${index}`
        } else if (type === 'eid') {
          return `element_${audience_id}_more_${index}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${this.cardName}活动首页`,
            page_type: `${this.cardName}活动首页`,
            container: audience_name,
            container_id: '',
            click_type: 'button',
            content_name: '查看更多可兑换好课' + index,
            terminal: 'PC'
          })
        }
      }
    },
    // 埋点
    dtListBody() {
      return (item, type, index) => {
        let dt_id = `${item.course_from}_${item.course_id}`
        if (type === 'area') {
          return `area_${dt_id}`
        } else if (type === 'eid') {
          return `element_${dt_id}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${this.cardName}活动首页`,
            page_type: `${this.cardName}活动首页`,
            container:
              index <= 7
                ? `${this.xueBaCardConfig.audience_name}_8`
                : `${this.xueBaCardConfig.audience_name}_16`,
            container_id: '',
            click_type: 'data',
            content_type: '培养项目',
            act_type: '11',
            content_id: item.course_id,
            content_name: item.course_name,
            terminal: 'PC'
          })
        }
      }
    }
  },
  methods: {
    // 获取订阅转态
    async getSubscribeStatus() {
      // "first_" + acctTypeCode + "_"+ activityId
      let res = await getSubscribeStatus(`first_${this.xueBaCardConfig.acct_type_code}_${this.xueBaCardConfig.activity_id}`)
      console.log(res, '订阅状态')
      this.isFirst = !res
    },
    messageSubscribe() {
      messageSubscribe({}, `first_${this.xueBaCardConfig.acct_type_code}_${this.xueBaCardConfig.activity_id}`).then(
        (res) => {}
      )
    },
    handlerReceive() {
      // 没有订阅说明是第一次进入需要自动订阅
      // if (this.isFirst && this.cardInfo.balance) {
      //   this.messageSubscribe()
      //   this.$message.success('成功解锁权限')
      //   this.pickUpVisible = false
      //   setTimeout(() => {
      //     this.getSubscribeStatus()
      //   }, 1000)
      //   return
      // }
      this.activityPresent()
    },
    // 提交需求
    applyperm() {
      if (this.record) return
      let params = {
        recourse_from: this.xueBaCardConfig.acct_type_code,
        course_id: ''
      }
      getRequestRecord(params).then((res) => {
        this.$message.success('提交成功')
        this.record = true
      })
    },
    // 获取他人赠送的卡券
    activityPresent() {
      const userInfo = JSON.parse(sessionStorage.getItem('login_user'))
      let params = {
        from: this.$route.query.staff_id || 0,
        from_name: this.$route.query.staff_name || '腾讯学堂',
        acct_type_code: this.xueBaCardConfig.acct_type_code,
        to_batch: [userInfo.staff_id],
        object_id: this.xueBaCardConfig.activity_id,
        object_name: this.xueBaCardConfig.activity_name,
        object_type: 'XuebaActivity',
        object_type_name: '活动',
        trans_amt: '1',
        notify_type: 0 // 手动赠送1 ，用户进入页面自动领的 0
      }
      activityPresent(params).then((res) => {
        if (res.success_count) {
          this.$message.success('成功解锁权限')
          this.pickUpVisible = false
          this.isFirst = false
          this.messageSubscribe()
        } else {
          this.$message.error('活动太火爆了，请稍后再试！')
        }
        console.log(res, '赠送接口')
        this.getAcctinfos()
        this.getHomePageInfoFn()
        this.checkShareAuth()
      })
    },
    // 积分授予-查询是否可以领取geek
    async getHomePageInfoFn() {
      try {
        this.userInfo = JSON.parse(sessionStorage.getItem('login_user'))
        let res = await getHomePageInfo({
          staff_id: this.userInfo.staff_id,
          acct_type_code: this.xueBaCardConfig.acct_type_code,
          activity_id: this.activityId
        })
        this.cardInfo = res
        // const { staff_id, staff_name } = this.$route.query
        //  当前用户无权限打开领取弹窗
        if (!this.cardInfo.balance) {
          this.pickUpVisible = true
        } else if (this.cardInfo.balance && this.isFirst) { // isFirst: true 首次进入需要调用弹窗
          this.openPermissionTips()
        }
      } catch (error) {
        if (error.code === 0 && error.data) {
          this.cardInfo = error.data
        }
      }
    },
    // 权限弹窗
    openPermissionTips() {
      let message = this.cardInfo.balance
        ? `恭喜您已经成功订阅，获得哈佛精品文库的阅读权限！`
        : `很遗憾您暂时没有办法参与本次活动，您可以通过身边已有全权限的小伙伴的邀请获得权限，敬请留意哦。<br /> 如有疑问请联系：minnaluan或v_xxyhe`
      this.$confirm(message, '温馨提示', {
        confirmButtonText: '确定',
        showCancelButton: false,
        closeOnClickModal: false,
        dangerouslyUseHTMLString: true,
        center: true,
        customClass: 'permission-tips',
        type: this.cardInfo.balance ? 'success' : ''
      }).then(() => {
        this.isFirst = false
        this.messageSubscribe()
        // localStorage.setItem('permission_tips', true)
      })
    },
    // 获取学霸卡基础信息
    async getUserActiveInfo() {
      try {
        let res = await getUserActiveInfo({ activity_id: this.activityId })
        console.log(res, '获取学霸卡基础信息')
        this.xueBaCardConfig = res
        this.recommendMap = res.recommend_map
        this.recommendMap['0'] = res.course_list
        console.log('recommend_map', this.recommendMap)
        this.courseList = this.recommendMap['0']
        this.lableTabs = Object.entries(res.audience_map).map(
          ([key, value]) => ({ key, value })
        )
        let SubscribeRes = await getSubscribeStatus(`first_${this.xueBaCardConfig.acct_type_code}_${this.xueBaCardConfig.activity_id}`)
        console.log(SubscribeRes, 'SubscribeResSubscribeRes')
        this.isFirst = !SubscribeRes
        this.getAcctinfos()
        this.checkShareAuth()
        this.getHomePageInfoFn()
        this.getPresentRecord(1)
        console.log(this.lableTabs, 'courseListTabcourseListTabcourseListTab')
      } catch (error) {
        console.log('获取基础信息: ', error)
      }
    },
    // 分享权限
    async checkShareAuth() {
      const result = await checkShareAuth({
        type: this.xueBaCardConfig.acct_type_code
      })
      this.shareAuth = result
    },
    // 有余额有卡券属于内容学习权限开启
    async getAcctinfos() {
      let params = {
        acct_type_codes: this.xueBaCardConfig.acct_type_code + 'Trans'
      }
      const result = await getAcctinfos(params)
      console.log(result, '查看有没有余额有余额解锁赠送劝学卡')
      //   查看有没有余额有余额解锁赠送劝学卡
      this.consumePoint = Number(result.consume_point)
    },
    // 赠送的
    async getPresentRecord(current) {
      let params = {
        activityId: this.xueBaCardConfig.activity_id,
        current: current,
        size: 10
      }
      const res = await getPresentRecord(params)
      console.log(res, '赠送')
      let records = res.records || []
      this.total = res.total
      if (current === 1) {
        this.presentRecordList = records
      } else {
        this.presentRecordList = this.presentRecordList.concat(records)
      }
    },
    handlerLoadingMore() {
      this.current = this.current + 1
      this.getPresentRecord(this.current)
    },
    openGive() {
      if (!this.isQuantity) return
      this.giveDialog = true
    },
    handlerGiveXuebaka() {
      this.getPresentRecord(1)
      this.getAcctinfos()
    },
    // 获取内容中心图片
    getCourseCoverUrl(data) {
      if (HTTPS_REG.test(data)) {
        return data
      }
      if (data) {
        const envName = env[process.env.NODE_ENV]
        return `${envName.contentcenter}content-center/api/v1/content/imgage/${data}/preview`
      }
      return 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
    },
    handleClick() {
      this.courseList = this.recommendMap[this.activeName]
    },
    // 活动规则
    toRulesDetail() {
      window.open(this.xueBaCardConfig.activity_detail_link)
    },
    // 跳转到课程详情
    toCourseDetail(href) {
      window.open(href)
    },
    // 外部好课专区
    toActiveDetail() {
      window.open(this.xueBaCardConfig.course_more_link)
    }
  }
}
</script>
<style lang="less">
.permission-tips {
  .el-message-box__header {
    padding-top: 0;
  }
  .el-message-box__content {
    text-align: left;
  }
}
</style>

<style lang="less" scoped>
.account-page {
  .color_b {
    color: #0052d9;
  }
  .banner-card {
    height: 200px;
    background: url('~@/assets/outsourcedCourse/after_bg_sanjieke.png')
      no-repeat center / cover;
    .banner-content {
      width: 1100px;
      min-width: 1100px;
      height: 100%;
      margin: 0 auto;
      position: relative;
      cursor: pointer;
      background-size: cover;
      background-repeat: no-repeat;
      .title {
        position: absolute;
        top: 50px;
        left: 436px;
      }
      .desc {
        padding-top: 96px;
        color: #ffffff;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
    }
    .common {
      height: 32px;
      padding: 5px 20px;
      border-radius: 64px;
      color: #0052d9;
      font-size: 14px;
      font-weight: 600;
      background: #e8f4ff;
      border-color: #e8f4ff;
      &.is-plain:hover {
        background: #fff;
        border-color: #fff;
        color: #2f74e1;
      }
      & + .el-button {
        margin-left: 12px;
      }
    }
  }
  .account-content {
    width: 1100px;
    min-width: 1100px;
    margin: -50px auto 0;
    position: relative;
    z-index: 2;
    filter: drop-shadow(0 0 8px #eeeeee99);
  }
  .account-authority {
    padding: 12px 28px 20px;
    border-radius: 8px;
    background-color: #fff;
    position: relative;
    .my-auth {
      color: #000000e6;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
    }
    .demand-button {
      color: #ffffff;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      height: 32px;
      width: 256px;
      line-height: 32px;
      text-align: center;
      border-radius: 3px;
      background-color: #0052d9;
      position: absolute;
      right: 28px;
      top: 23px;
      cursor: pointer;
    }
    .authority-status {
      display: flex;
      margin-top: 8px;
      .study-text {
        color: #********;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
      }
      .el-icon-success {
        color: #00a870;
        font-size: 16px;
        padding-right: 5px;
      }
      .el-icon-error {
        color: #e34d59;
        font-size: 16px;
        padding-right: 5px;
      }
      .study-a {
        line-height: 24px;
        margin-right: 40px;
        display: flex;
        align-items: center;
        img {
          width: 24px;
          height: 24px;
          margin-right: 8px;
        }
        span {
          display: flex;
          align-items: center;
        }
      }
    }
  }
  .invite-main {
    padding: 20px 28px;
    background-color: #fff;
    border-radius: 0 0 8px 8px;
    padding-top: 0;
    .invite-main-border {
      width: 100%;
      height: 1px;
      background-color: #eee;
    }
    &_title {
      padding-top: 12px;
      color: #000000e6;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
    }
    .give-explain-content {
      color: #00000066;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 22px;
      padding: 6px 12px;
      background: #f9f9f9;
      margin: 10px 0 20px 0;
    }
  }
  .tab-content {
    margin-top: 16px;
    &-tips {
      border-radius: 4px;
      color: #000;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    &-list {
      //   height: 84px;
      width: 100%;
      display: flex;
      margin-top: 16px;
      &_left {
        width: 152px;
        display: flex;
        height: 84px;
        padding: 6px 24px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;
        align-self: stretch;
        border-radius: 8px;
        border: 0.5px solid #eee;
        background: #eff4ff;
        margin-right: 12px;
        cursor: pointer;
        img {
          width: 16px;
          height: 16px;
        }
        span {
          color: #0052d9;
          font-family: 'PingFang SC';
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px;
        }
      }
      &_right {
        flex: 1;
        overflow: hidden;
        position: relative;
        .mySwiper {
          position: unset;
        }
        .swiper-slide {
          //   margin-right: 12px;
          display: flex;
          height: 84px;
          //   padding: 8px 12px;
          flex-direction: column;
          align-items: flex-start;
          border-radius: 8px;
          border: 0.5px solid #eee;
          background: linear-gradient(
            0deg,
            #fdffff 36.9%,
            #fbfeff 77.92%,
            #f9fffe 100%
          );
        }
        .swiper-slide_more {
          width: 28px !important;
          display: flex;
          align-items: center; /* 垂直居中 */
          justify-content: center; /* 水平居中 */
          border-radius: 3px;
          cursor: pointer;
          .tab-content_more {
            width: 100%;
            height: 100%;
            text-align: center; /* 文字居中 */
            writing-mode: vertical-rl; /* 文字竖排 */
            line-height: 28px;
          }
        }
        // .swiper-slide:last-child {
        //   margin-right: 0;
        // }
        .swiper-button-prev,
        .swiper-button-next {
          height: 100%;
          width: 28px;
          border: 1px solid #eeeeee;
          background: #ffffff;
          border-radius: 4px;
          top: 26%;
          cursor: pointer;
        }
        .swiper-button-prev {
          border-radius: 4px 0 0 4px;
          left: 0;
        }
        .swiper-button-next {
          border-radius: 0 4px 4px 0;
          right: 0;
        }
        .swiper-button-prev:after,
        .swiper-button-next:after {
          font-size: 14px;
          color: #666666;
          font-weight: 600;
        }
        .tab-content_item {
          display: flex;
          align-items: center;
          width: 100%;
          height: 100%;
          padding-left: 12px;
          &-left {
            margin-right: 6px;
            img {
              width: 40px;
              height: 40px;
              border-radius: 50%;
            }
          }
          &-right {
            line-height: 20px;
            font-size: 12px;
            .name {
              align-self: stretch;
              color: #333333ff;
              font-family: 'PingFang SC';
              font-size: 14px;
              font-style: normal;
              font-weight: 500;
              line-height: 22px;
              display: -webkit-box;
              overflow: hidden;
              text-overflow: ellipsis;
              word-break: break-all;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 1;
            }
            .number1 {
              align-self: stretch;
              color: #666666ff;
              font-family: 'PingFang SC';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
            }
            .number2 {
              color: #999999ff;
              font-family: 'PingFang SC';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
            }
          }
        }
      }
    }
    &_null {
      &-right {
        img {
          position: relative;
          left: 50%;
          transform: translateX(-50%);
          width: 128px;
          height: 128px;
          margin: 12px 0 8px;
        }
      }
      &-footer {
        color: #666666ff;
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
      .tab-content_tips {
        margin: 24px 0 16px;
        line-height: 32px;
        background-color: #f9f9f9;
        font-size: 12px;
        padding: 0 12px;
        color: #777777;
      }
      &-left {
        img {
          position: relative;
          left: 50%;
          transform: translateX(-50%);
          width: 128px;
          height: 128px;
          margin: 8px 0;
        }
      }
    }
  }
  .course-list {
    padding: 21px 28px;
    background: #fff;
    margin-top: 20px;
    border-radius: 8px;
    box-shadow: 0 0 8px 0 #eeeeee99;
    .title-col {
      display: flex;
      // justify-content: space-between;
      align-items: center;
      position: relative;
      /deep/.el-tabs__nav-next,
      /deep/.el-tabs__nav-prev {
        line-height: 22px;
      }
      /deep/.el-tabs__item {
        padding: 0 10px;
      }
      /deep/.el-tabs__header {
        margin-bottom: -8px;
      }
      /deep/.el-tabs__nav-wrap::after {
        display: none;
      }
      /deep/.el-tabs__active-bar {
        background-color: transparent !important;
        background-image: linear-gradient(
          90deg,
          transparent 0,
          transparent 27%,
          #0052d9 0,
          #0052d9 73%,
          transparent 0,
          transparent
        );
      }

      .title {
        color: #000000e6;
        font-size: 18px;
        font-weight: 600;
        line-height: 22px;
        margin-right: 20px;
      }
      .link {
        color: #0052d9;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        cursor: pointer;
        position: absolute;
        right: 0;
      }
    }
    .list {
      margin: 26px -28px 0 0;
      display: flex;
      flex-wrap: wrap;
      .course-item {
        width: 240px;
        margin: 0 28px 24px 0;
        cursor: pointer;
        .cover {
          width: 100%;
          height: 160px;
          margin-bottom: 12px;
          border-radius: 6px;
        }
        .title {
          color: #333;
          font-weight: 500;
          font-size: 14px;
          line-height: 22px;
          display: -webkit-box;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          & > span {
            height: 18px;
            font-weight: 500;
            line-height: 18px;
            font-size: 12px;
            padding: 0 6px;
            margin-right: 6px;
            justify-content: center;
            align-items: center;
            gap: 10px;
            border-radius: 2px;
            background: #f5f5f7;
            color: #777777;
          }
        }
      }
    }
    & > .link {
      text-align: center;

      span {
        cursor: pointer;
        color: #0052d9;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
      }
    }
  }
}
</style>
