<template>
  <div class="project-manage-page-box">
    <link rel="stylesheet" :href="linkHref">
    <div class="container-box">
      <div class="container-header">
        <ProjectDetailCommon class="project-detail-common" @updateProject="getProjectInfoData" />
      </div>
      <div class="container-body">
        <div class="aside">
          <ul class="menu">
            <li v-for="(item,index) in manageMenuList" :key="index" :class="[item.name==$route.name?'menu-item-active':'','menu-item']" @click="handleSkip(item.name)">{{item.text}}</li>
          </ul>
        </div>
        <div class="main">
          <router-view :key="$route.path" />      
        </div>
      </div>      
    </div>
    <!-- 底部按钮区域 -->
    <bottomFiexd
      v-if="isApprove" 
      @cancel="handleRefuseShow" 
      @save="handleApprove(1)"
      cancelText="拒绝"
      submitText="审核通过"
    >
    </bottomFiexd>
    <!-- 审核拒绝 -->
    <refuseDialog :refuseShow.sync="refuseShow" @refuseConfirm="handleApprove"></refuseDialog>
  </div>
</template>

<script>
import { mapMutations } from 'vuex'
import ProjectDetailCommon from './mooc/components/project-detail-common'
import bottomFiexd from '@/views/components/botttomFixed.vue'
import refuseDialog from '@/views/user/netcourse/course-make/components/refuseDialog.vue'
import { approveStatus } from 'config/api.conf'
export default {
  components: {
    ProjectDetailCommon,
    bottomFiexd,
    refuseDialog
  },
  data() {
    return {
      linkHref: '',
      courseData: {
        photo_url: '',
        project_status_name: '进行中'
      },
      manageMenuList: [
        { name: 'basic-setting', text: '基础设置' },
        { name: 'task-list', text: '任务组织' },
        { name: 'members', text: '学员管理' },
        { name: 'regist-setting', text: '报名管理' },
        { name: 'interactive', text: '互动管理' },
        { name: 'report', text: '数据统计' },
        { name: 'advanced-setting', text: '高级设置' }  
      ],
      refuseShow: false
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name && !document.getElementById('graphic-common-head')) {
          this.headerIsLoad = true
          this.linkHref = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeaderStyle.css` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeaderStyle.css`  
          const commonHeaderJsUrl = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeader.js` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeader.js`
          this.loadHeadJS(commonHeaderJsUrl, function() {
            if (window.$qlCommonHeader) {
              window.$qlCommonHeader.create({
                staff_name: val.staff_name,
                dev: !(process.env.NODE_ENV === 'production')
              })
              setTimeout(() => {
                let el = document.getElementsByClassName('common-header-occupy')
                if (el.length > 0) {
                  console.log('El', el[0].offsetHeight)
                  document.getElementById('app').style.height = `calc(100% - ${el[0].offsetHeight}px)`
                }
              }, 500)
            }
          })
        }
      },
      immediate: true
    }
  },
  computed: {
    isApprove() {
      return this.$route.query.approve === '1'
    }
  },
  created() {
    this.getProjectInfoData()

    if (this.$route.query.from && this.$route.query.from === 'externalCourse') {
      this.manageMenuList.push({ name: 'external-info', text: '外部内容信息' })
    }
  },
  methods: {
    ...mapMutations(['saveProjectManageInfo']),
    // 拒绝
    handleRefuseShow() {
      this.refuseShow = true
    },
    // 管理后台审核
    handleApprove(approve_result, review_failed_reason) {
      // this.approveLoading = true
      const { mooc_course_id } = this.$route.query
      const params = {
        act_type: 11,
        course_id: mooc_course_id,
        approve_result,
        is_mobile: 1
      }
      if (approve_result === 2) {
        params.review_failed_reason = review_failed_reason
      }
      approveStatus(params).then((res) => {
        this.refuseShow = false
        const msg = approve_result === 2 ? '审核拒绝' : '审核通过'
        // this.courseInfo.status = approve_result === 2 ? '8' : '7'
        this.$message.success(msg)
        try {
          window.opener.workReConnect()
        } catch (error) {
          console.error('error: ', error)
        }
        setTimeout(() => {
          window.close()
        }, 200)
      })
    },
    // 获取登陆用户信息
    loadHeadJS (url, callback) {
      var script = document.createElement('script')
      var fn = callback || function() {}
      script.type = 'text/javascript'
      script.id = 'graphic-common-head'
      // IE
      if (script.readyState) {
        script.onreadystatechange = function() {
          if (script.readyState === 'loaded' || script.readyState === 'complete') {
            script.onreadystatechange = null
            fn()
          }
        }
      } else {
        // 其他浏览器
        script.onload = function() {
          fn()
        }
      }
      script.src = url
      document.getElementsByTagName('head')[0].appendChild(script)
    },
    async getProjectInfoData() {
      const { mooc_course_id } = this.$route.query
      this.$store.dispatch('getProjectInfoData', mooc_course_id)
    },
    handleSkip(name) {
      // const { mooc_course_id } = this.$route.query
      // let query = {
      //   mooc_course_id
      // }
      // if (this.$route.query.from) {
      //   query.from = this.$route.query.from
      // }
      this.$router.push({ name, query: this.$route.query })
    }
  },
  mounted() {
    
  }
}
</script>

<style lang="less">
@import '~assets/css/el-style.less';
@import '~assets/css/center.less';
@import '~assets/css/common.less';
#app {
  // height: calc(100% - 50px);
  height: calc(100% - 62px);
}
.project-manage-page-box {
  @media screen and (max-width: 1660px) {
    width: 1158px;
  }
  @media screen and (min-width: 1661px) {
    width: 1440px;
  }
  margin: 0 auto;
  padding: 0 24px 24px 24px;
  display: flex;
  justify-content: center;
  height: 100%;
  overflow-y: auto;
  .container-box{
    width: 100%;
  }
  .container-header{
    margin: 20px 0 16px 0;
    border-radius: 4px;
  }
  .container-body{
    height: calc(100% - 230px);
    display: flex;
    .aside{
      min-width: 120px;
      height: 480px;
      margin-right: 14px;
      border-radius: 4px;
      background: #fff;
      .menu{
        text-align: center;
        padding: 20px 12px 20px 12px;
        .menu-item{
          color: rgba(0, 0, 0, 0.6);
          height: 36px;
          line-height: 36px;
          cursor: pointer;
        }
        .menu-item-active{
          border-radius: 4px;
          color: #0052D9!important;
          background-color: #ecf2fe;
        }
      }     
    }
    .main{
      border-radius: 4px;
      flex: 1;
      overflow: auto;
      // margin-bottom: 20px;
      background-color: #fff;
      .project-detail-common{
        margin-bottom: 14px;
      }
    }
  }  
}
</style>
