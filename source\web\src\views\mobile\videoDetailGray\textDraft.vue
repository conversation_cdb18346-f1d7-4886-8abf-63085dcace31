<template>
  <div class="text-draft-main">
    <div class="top-search-box">
      <div class="tools-lf">
        <span class="tools-icon"></span>
        <van-search
        class="search-custom" 
        v-model="searchValue"
        :clearable="true"
        placeholder="请输入搜索关键词"
        @clear="handleClear"
        @input="onSearch"
        >
          <template v-slot:left-icon>
            <span class="left-icon"></span>
          </template>
        </van-search>
      </div>

      <div 
      class="tools-lr" 
      @click="handleFeedBack" 
      :dt-eid="dtBtn('eid', '反馈')"
      :dt-remark="dtBtn('remark', '反馈')"
      :dt-areaid="dtBtn('areaid', '反馈')"
      >
        <span class="tips-icon icon"></span>
        <span class="tips-title">反馈</span>
      </div>
    </div>
    <div class="opera-btn-box" v-if="searchValue">
      <span class="search-num">{{ searchNum }}</span>
      <span 
      class="serarch-prev btn" 
      @click="addIndex" :dt-eid="dtBtn('eid', '上一个')" :dt-remark="dtBtn('remark', '上一个')" :dt-areaid="dtBtn('areaid', '上一个')">上一个</span>
      <span class="search-next btn" @click="reduceIndex" :dt-eid="dtBtn('eid', '下一个')" :dt-remark="dtBtn('remark', '下一个')" :dt-areaid="dtBtn('areaid', '下一个')">下一个</span>
    </div>
    <div id="caption-box" class='caption-box'>
      <div 
      v-for="(item, index) in captionArr" 
      :key="index" 
      :class="[
        'row-content',
        // 随时间播放自动滚动
        { 'row-content-active': captionCurTime >= item.IntStartTime && captionCurTime < item.IntEndTime || noCurrentTime(item, index) },
        // 这个主要是为了写样式
        { 'scroll-content': visibleIndex === index && showLine },
        // 文本查询滚动
        { 'scoll-text-active': choiceTextIndex(item) }
      ]"
      @click="toCaption($event, item)"
      :dt-eid="dtToCaption('eid', '跳转至此', item)"
      :dt-remark="dtToCaption('remark', '跳转至此', item)"
      :dt-areaid="dtToCaption('areaid', '跳转至此', item)"
      >
        <div class="caption-item">
          <div class="caption-time">
            <span class="play-time">{{ item.startTime?.split(',')[0] }}</span>
            <div class="isPlay-box" v-if="visibleIndex === index && showLine">
              <span class="isPlay-icon"></span>
              <span>跳转至此</span>
            </div>
          </div>
          <p class="play-title" v-html="item.caption"></p>
        </div>
      </div>
    </div>
    <div v-if="showBack" class="come-back-top" @click="backTo"  :dt-eid="dtBtn('eid', '回到当前位置')" :dt-remark="dtBtn('remark', '回到当前位置')" :dt-areaid="dtBtn('areaid', '回到当前位置')">
      <span>{{ $langue('NetCourse_ReturnCurr', { defaultText: '回到当前位置' }) }}</span>
      <span class="back-icon"></span>
    </div>
    <!-- 章节弹窗 -->
    <van-popup 
    class="summary-title-container"
    id="popup-summary"
    v-model="isShowChapter" 
    :overlay="false"
    position="bottom"
    :lazy-render="false"
    get-container="body"
    :style="{ height: popupHeight }"
    >
      <div class="body">
        <div class="switch-btn" @click="popupClose"><span class="btn"></span></div>
        <div class="list-box" v-if="summaryTitleList.length">
          <div 
          :class="['item-ul', {'item-active-ul': captionCurTime >= item.startTime && captionCurTime < item.endTime}]" 
          v-for="(item, index) in summaryTitleList" 
          :key="index"
          @click="toCaption($event, item, 'popup')"
          :dt-eid="dtToCaption('eid', '章节标题弹窗', item)"
          :dt-remark="dtToCaption('remark', '章节标题弹窗', item)"
          :dt-areaid="dtToCaption('areaid', '章节标题弹窗', item)"
          >
            <span class="title">{{ item.chapter_title }}</span>
          </div>
          <div class="no-more">没有更多内容了~</div>
        </div>
        <div v-else class="empty">
          <img :src="require('@/assets/img/mobile/empty-note.png')" />
          <div>{{ $langue('Mooc_Common_NoData', { defaultText: '暂无数据' }) }}</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import { debounce } from '@/utils/tools.js'
import { mapState } from 'vuex'
export default {
  props: {
    captionData: {
      type: Array,
      default: () => ([])
    },
    chapterSummaryList: {
      type: Array,
      default: () => ([])
    },
    captionCurTime: [Number, String],
    courseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      showBack: false,
      showLine: false,
      visibleHeightStart: 0,
      visibleHeightEnd: 0,
      visibleHeight: 0,
      visibleIndex: -1,
      captionEl: null,
      popupEl: null,
      searchValue: '',
      isClickToCaption: false,
      captionArr: [],
      choiceWordList: [],
      currentIndex: 0,
      settimeout: null,
      scrollCount: 0,
      isShowChapter: false,
      popupHeight: '316px'
    }
  },
  watch: {
    // 视频处理
    captionData: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (!this.captionArr?.length) {
          this.captionArr = newVal
          const list = JSON.stringify(this.captionArr)
          sessionStorage.setItem('mobileCaptionArr', list)
        }
      }
    }
  },
  computed: {
    ...mapState(['moocLang']),
    course_id() {
      return this.$route.query.course_id ? parseInt(this.$route.query.course_id) : ''
    },
    // 当视频播放时间和文稿时间对不上的时候，用文稿的开始时间进行比较
    noCurrentTime() {
      let colorFlag = false
      return (row, index) => {
        // 播放时长大于字幕最后一条数据的时间
        if (this.captionData.length - 1 === index && this.captionCurTime > this.captionData[this.captionData.length - 1].IntEndTime) {
          colorFlag = true
        } else {
          if (!row?.IntStartTime) {
            colorFlag = false
          } else {
            if ((row.IntStartTime < this.captionCurTime) && this.captionData[index + 1] && (this.captionCurTime < this.captionData[index + 1].IntStartTime)) {
              colorFlag = true
            } else {
              colorFlag = false
            }
          }
        }
        return colorFlag
      }
    },
    searchNum() {
      return this.choiceWordList.length ? `${this.currentIndex + 1}/${this.choiceWordList.length}` : '无结果'
    },
    choiceTextIndex() {
      return (row) => {
        const findIndex = this.choiceWordList.findIndex(r => row.uuId === Number(r.id))
        return findIndex === this.currentIndex
      }
    },
    summaryTitleList() {
      return this.chapterSummaryList.map((e) => {
        return {
          ...e,
          chapter_time: e.chapter_time,
          chapter_title: e.chapter_title,
          startTime: e.startTime,
          endTime: e.endTime
        }
      })
    },
    dtBtn() {
      return (type, val, row) => {
        const contentName = val === '跳转至此' ? row.caption : ''
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页-新版', 
            container: `字幕检索-${val}`,
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: contentName,
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${val}`
        } else {
          return `area_${this.course_id}_${val}`
        }
      }
    },
    dtToCaption() {
      return (type, val, row) => {
        const contentName = val === '跳转至此' ? row.caption : row.chapter_title
        const contentId = val === '跳转至此' ? row.IntStartTime : row.chapter_time_point
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页-新版', 
            container: `字幕检索-${val}`,
            click_type: 'data',
            content_type: '',
            content_id: contentId,
            content_name: contentName,
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${contentId}`
        } else {
          return `area_${this.course_id}_${contentId}`
        }
      }
    }
  },
  mounted() {
    this.captionEl = document.getElementById('caption-box')
    this.captionEl.addEventListener('scroll', this.scrollEvent, true)
    this.popupEl = document.getElementById('popup-summary')
    this.popupEl.addEventListener('scroll', this.handleBodyScroll, true)
  },
  destroyed() {
    this.captionEl.removeEventListener('scroll', this.scrollEvent, true)
    this.popupEl.removeEventListener('scroll', this.handleBodyScroll, true)
    this.settimeout = null
    clearTimeout(this.settimeout)
  },
  methods: {
    handleBodyScroll() {
      if (this.isShowChapter) {
        this.popupHeight = 'calc(100% - 220px)'
      }
    },
    popupClose() {
      this.popupHeight = '316px'
      this.isShowChapter = false
    },
    // 反馈
    handleFeedBack() {
      window.open('https://km.tencent.com/openkm/url/lpciih', '_blank')
    },
    // 文本查询
    onSearch: debounce(function (e) {
      if (!e) {
        this.handleClear()
        return
      }
      window.BeaconReport('at_click', {
        eid: this.dtCommon('eid', this.searchValue, '内容检索'),
        remark: this.dtCommon('remark', this.searchValue, '内容检索')
      })
      const sessionCaptionArr = JSON.parse(sessionStorage.getItem('mobileCaptionArr'))
      const list = sessionCaptionArr?.length ? sessionCaptionArr : this.captionArr
      list.forEach((e) => {
        if (e.caption && e.caption.indexOf(this.searchValue) > -1) {
          const id = this.generateUUID()
          const caption = this.hightLight(this.searchValue, e.caption, id)
          e.caption = caption
          e.uuId = id
        }
      })
      this.captionArr = list
      this.getChoiceScrollWord('enter')
    }),
    getChoiceScrollWord(val) {
      this.scrollCount = 0
      this.choiceWordList = []
      this.$nextTick(() => {
        const a = Array.from(document.getElementsByClassName('hightLightWord'))
        a.forEach((e, i) => {
          e.style.background = '#F7F0A7'
          if (i === this.currentIndex) {
            e.style.background = '#EECD3C'
          }
          this.choiceWordList.push({
            id: e.getAttribute('id')
          })
        })
      })
      // 滚动到选中的数据
      this.isScroll(val)
    },
    generateUUID () {
      const d = new Date().getTime()
      const r = d + Math.random() * 1000
      return r
    },
    // 高亮
    hightLight (keyWord, suggtion, id) {
      // 使用 regexp 构造函数，因为这里要匹配的是一个变量
      const reg = new RegExp(keyWord, 'ig')
      const newSrt = suggtion.replace(reg, function (keyWord) {
        return `<span class='hightLightWord' id=${id} style="background: #F7F0A5;">${keyWord}</span>`
      })
      return newSrt
    },
    handleClear() {
      this.searchValue = ''
      this.currentIndex = 0
      this.choiceWordList = []
      this.captionArr = JSON.parse(sessionStorage.getItem('mobileCaptionArr')) || []
    },
    // 上一个
    addIndex() {
      if (this.currentIndex === 0) {
        this.currentIndex = this.choiceWordList.length - 1
        this.getChoiceScrollWord()
        this.isScroll('enter')
        return
      }
      this.currentIndex--
      this.getChoiceScrollWord()
      this.isScroll('enter')
    },
    // 下一个
    reduceIndex(val) {
      if (this.currentIndex === (this.choiceWordList.length - 1)) {
        this.currentIndex = 0
        this.getChoiceScrollWord()
        this.isScroll('enter')
        return
      }
      this.currentIndex++
      this.getChoiceScrollWord()
      this.isScroll('enter')
    }, 
    // 触发自动滚动
    scrollTopContent() {
      // 在查询状态的时候，不需要自动滚动
      if (this.searchValue) {
        return
      }
      this.isScroll()
    },
    isScroll(val) {
      if (this.scrollCount > 2) return // 手动滚动
      // 非手动滚动那就继续自动滚动
      this.scrollCount = 0
      this.showLine = false
      let rowName = 'row-content-active'
      if (val === 'enter') { // 上一个，下一个滚动
        rowName = 'scoll-text-active'
      }
      this.settimeout = setTimeout(() => {
        let curDom = document.getElementsByClassName(rowName)[0]?.previousElementSibling
        const captionBox = this.$el.querySelector('.caption-box')
        // 自动滚动的高度
        if (curDom) {
          captionBox.scrollTop = curDom.offsetTop + curDom.clientHeight - 20
        } else if (this.currentIndex === 0 && !curDom) { // 当翻页到最后一条数据的时候滚动到最顶端
          captionBox.scrollTop = 0
        }
      })
    },
    // 回到对应的位置
    backTo() {
      this.scrollCount = 0
      this.showLine = false
      this.showBack = false
      this.isScroll()
    },
    // 跳转至此
    toCaption(e, data, val) {
      e.stopPropagation()
      if (val === 'popup') {
        this.$emit('toChapterPosition', data.chapter_time_point)
        return
      }
      this.$emit('toChapterPosition', data.IntStartTime)
      this.showLine = false
      this.isClickToCaption = true
    },
    // 手动滚动
    scrollEvent() {
      // 点击时候, 不让出现showLine
      if (this.isClickToCaption) {
        this.isClickToCaption = false
        return
      }
      this.handleScroll()
    },
    handleScroll() {
      this.scrollCount++ // 只有手动滚动次数才会大于2
      if (this.scrollCount <= 2) { // 自动滚动
        return
      }
      // 手动滚动-显示定位线，显示回到当前位置
      if (!this.showBack && this.scrollCount > 2) {
        this.showBack = true
      }
      if (this.scrollCount > 2) {
        this.showLine = true
      }
      // 可视区域
      if (!this.visibleHeightStart && !this.visibleHeightEnd) {
        this.$nextTick(() => {
          let boxEl = document.getElementsByClassName('caption-box')[0]
          let ElData = boxEl.getBoundingClientRect()
          this.visibleHeightStart = ElData.height / 2 - 46 - 16
          this.visibleHeightEnd = ElData.height / 2 - 16
        })
      }
      const scrollTop = document.getElementsByClassName('caption-box')[0].scrollTop
      let rowEl = document.getElementsByClassName('row-content')
      let len = rowEl.length
      for (let i = 0; i < len; i++) {
        let rowHeight = rowEl[i].offsetTop + rowEl[i].offsetHeight
        if (rowHeight > scrollTop + this.visibleHeightStart && rowEl[i].offsetTop < scrollTop + this.visibleHeightEnd) {
          this.visibleIndex = i
        }
      }
    },
    dtCommon(type, label, val) {
      if (type === 'remark') {
        return JSON.stringify({
          page: this.courseData.course_name,
          page_type: '网课详情页-新版', 
          container: `字幕检索_${val}`,
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: label,
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'PC'
        })
      } else if (type === 'eid') {
        return `element_${this.course_id}_${label}`
      } else {
        return `area_${this.course_id}_${label}`
      }
    }
  }
}
</script>
<style lang="less" scoped>
.text-draft-main {
  background-color: #fff;
  padding-top: 16px;
  height: 100%;
  .top-search-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px 8px;
    .tools-lf {
      display: flex;
      align-items: center;
      .tools-icon {
        display: inline-block;
        width: 24px;
        height: 24px;
        // background: url('~@/assets/img/mobile/chapter-icon.png') no-repeat center / cover;
        margin-right: 16px;
      }
      .search-custom {
        width: 227px;
        padding: unset;
        .left-icon {
          display: inline-block;
          width: 24px;
          height: 24px;
          background: url('~@/assets/img/mobile/search.png') no-repeat center / cover;
        }
        :deep(.van-search__content) {
          background-color: #F3F3F3;
          border-radius: 6px;
        }
        :deep(.van-cell) {
          display: flex;
          align-items: center;
          .van-field__left-icon{
            height: 24px;
          }
          .van-icon-clear {
            font-size: 24px;
          }
        }
      }
    }
    .tools-lr {
      display: flex;
      align-items: center;
      color: #777777;
      margin-right: 8px;
      .tips-icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-right: 4px;
        background: url('~@/assets/img/mobile/tips.png') no-repeat center / cover;
      }
    }
  }
  .opera-btn-box {
    background: #F6F6F6;
    padding: 12px 16px;
    display: flex;
    .search-num {
      border-radius: 4px;
      background: #FFF;
      height: 28px;
      line-height: 20px;
      padding: 4px 19px;
      color: #000000e6;
      font-size: 12px;
      display: inline-block;
    }
    .btn {
      border-radius: 2px;
      border: 1px solid #DCDCDC;
      background: #FFF;
      height: 28px;
      color: #000000e6;
      font-family: "PingFang SC";
      font-size: 12px;
      display: inline-block;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 69px;
    }
    .serarch-prev {
      margin-left: 12px;
      margin-right: 12px;
      
    }
  }
  .caption-box {
    height: calc(100% - 40px);
    overflow: auto;
    position: relative;
    padding-top: 8px;
    padding-bottom: 70%;
    .row-content {
      padding: 8px 16px 8px 0px;

      .play-time {
        color: #00000099;
        font-size: 12px;
        line-height: 20px;
        margin-left: 34px;
        display:inline-block;
      }
      .isPlay-box {
        color: #0052d9;
        font-family: "PingFang SC";
        font-size: 10px;
        line-height: 16px;
        display: flex;
        align-items: center;
        margin-top: 5px;
        margin-left: 8px;
        .isPlay-icon {
          display: inline-block;
          width: 12px;
          height: 12px;
          margin-right: 4px;
          background: url('~@/assets/img/mobile/play-to.png') no-repeat center / cover;
        }
      }
      .play-title {
        color: #00000066;
        font-size: 14px;
        line-height: 22px;
        letter-spacing: 0.28px;
        flex: 1;
        word-break: break-all;
        margin-left: 20px;
      }
      .caption-item {
        display: flex;
        // .caption-time {
        //   width: 100px;
        // }
      }
    }
    .row-content-active {
      .play-time, .play-title {
        color: #0052D9;
        line-height: 22px;
      }
    }
    .scroll-content {
      background: #F9FBFF;
    }
  }
}
.come-back-top {
  position: fixed;
  bottom: 96px;
  right: 16px;
  // width: 114px;
  height: 32px;
  padding: 0 8px 0 12px;
  border-radius: 70px;
  opacity: 1;
  background: #ffffffff;
  box-shadow: 0 0 12px 0 #ddddddff;
  text-align: center;
  line-height: 32px;
  color: #0052D9;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 0 12px 0 #ddddddff;
  .back-icon {
    background: url('~@/assets/img/mobile/back.png') no-repeat center/cover;
    width: 16px;
    height: 16px;
    display: inline-block;
  }
}
.summary-title-container {
  z-index: 999;
  border-radius: 12px 12px 0 0 ;
  .body {
    background-color: #F6F7F9;
    padding: 0px 16px;
    height: 100%;
    overflow: hidden;
    .switch-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 8px;
      padding-bottom: 8px;
      .btn {
        width: 30px;
        height: 4px;
        border-radius: 12px;
        background: #D9D9D9;
      }
    }
    .list-box {
      background:#fff;
      padding: 0px 16px;
      height: 100%;
      overflow: auto;
      padding-bottom: 48px;
      .item-ul {
        padding-top: 12px;
        padding-bottom: 12px;
        border-bottom: 1px solid #EDEDED;
        .title {
          color: #333333;
          font-size: 14px;
          line-height: 22px;
          word-break: break-all;
        }
      }
      .item-active-ul {
        .title {
          color: #0052D9
        }
      }
      .no-more {
        color: #00000066;
        text-align: center;
        margin-top: 16px;
        font-size: 14px
      }
    }
    .empty {
      text-align: center;
      color: #000000e6;
      font-size: 14px;
      margin-top: 10px;
      img {
        width: 160px;
        height: 160px;
      }
    }
  }
}
</style>
