<template>
  <div>
    <div class="ai-article-content">
      <div class="ai-top-content">
        <span class="ai-link">AI文章功能请参考： <el-link type="primary" href="https://iwiki.woa.com/p/4012285812" target="_blank">https://iwiki.woa.com/p/4012285812</el-link></span>
        <div v-if="useSwitch" class="tips-content-box">
          <span>AI文章识别：</span>
          <span class="tips" :class="{'tips-disabled': approveStatus}">
            <i class="el-icon-warning" style="color: #0052D9"></i>
            已智能识别内容并生成文章，
            <el-link type="primary" :disabled="approveStatus" @click="checkExport" class='export-click'>点此查看、导入或停用</el-link>
          </span>
        </div>
        <div v-else class="tips-content-box">
          <span>AI文章识别：</span>
          <span class="tips" :class="{'tips-disabled': approveStatus}">
            <i class="el-icon-error" style="color: #D54941"></i>
            文章智能生成功能已关闭，
            <el-link type="primary" :disabled="approveStatus" @click="checkExport" class='export-click'>点此查看、导入或停用</el-link>
          </span>
        </div>
      </div>
      <div class="article-content">
        <div class="content-top" v-if="showArticleEdit">
          <span>人工转写文章</span>
          <div>
            <el-button :disabled="!$route.query.net_course_id || approveStatus" type="primary" size="small" @click="showArticleEdit=false">编辑</el-button>
            <el-button v-if='pageArticleList.length' :disabled="approveStatus" type="danger" plain size="small" @click="handleDelte">删除</el-button>
          </div>
        </div>
        <div class="content-main" v-if="showArticleEdit">
          <div
            :class="['article-list-box']"
            v-for="(item, index) in pageArticleList" 
            :key="index"
          >
            <div class="tp-title">
              <span class="time"><span>{{ transforNcTime(item.time) }}</span></span>
              <span class="title" v-html="item.title"></span>
            </div>
            <div class="article-card-content">
              <p class="item-other-title" v-for="(e, i) in item.titleList" :key="delTitle(e, i)">
                <span v-html="delTitle(e, i)"></span>
              </p>
              <p class="item-other-title" v-for="(c, j) in item.content" :key="j"><span v-html="delTitle(c, j)"></span></p>
              <img class="item-img" v-for="v in item.imglist" :key="v" :src="v" />
            </div>
          </div>
        </div>
        <sdc-mce-editor
          v-else
          ref="editor" 
          selector="ai_article" 
          :env="editorEnv" 
          :content="pageArticleData"
          :catalogue.sync="editorConfig.catalogue" 
          :urlConfig="editorConfig.urlConfig" 
          :options="editorConfig.options"
          :insertItems="insertItems"
        />
      </div>
      <div class="empty" v-if='pageArticleList.length === 0 && showArticleEdit'>
        <span class="empty-img"></span>
        <div class="empty-text">暂无数据</div>
      </div>
      <div class="buttom-btn" v-if="!showArticleEdit">
        <div class="inner">
          <el-button class="cancel-btn" @click="onCancel">取消</el-button>
          <el-button class="confirm-btn" v-if="showAiGraphic" type="primary" @click="onPreview">预览</el-button>
          <el-button class="confirm-btn" type="primary" @click="onSubmit">保存</el-button>
        </div>
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      title="AI文章识别"
      :visible.sync="aiArticleVisible"
      width="960px"
      :before-close="handleClose"
      custom-class="ai-article-dialog none-border-dialog" 
    >
      <div class="ai-article-body">
        <div class="ai-switch-item">
          <span class="label">功能开关</span>
          <el-switch
            v-model="useSwitch"
            class="ai-switch"
            active-color="#0052D9"
            :width="26"
            @change="handleUseSwitch"
            >
          </el-switch>
          <span class="label-tips">若启用AI文章识别，且未维护人工转写文章数据，则将对用户显示AI识别结果</span>
        </div>
        <div class="config-item">
          <span class="label">人工配置</span>
          <el-button :disabled="disabledExport" @click="exportData()" type="primary" size="small">导入并修改</el-button>
          <span class="label-tips">点击即可导入AI文章识别数据，并可进行修改</span>
        </div>
        <div class="article-body-main">
          <div
            :class="['article-list-box']"
            v-for="(item, index) in dialogArticleList" 
            :key="index"
          >
            <div class="tp-title">
              <span class="time"><span>{{ transforNcTime(item.time) }}</span></span>
              <span class="title" v-html="item.title"></span>
            </div>
            <div class="article-card-content">
              <p class="item-other-title" v-for="(e, i) in item.titleList" :key="delTitle(e, i)"><span v-html="delTitle(e, i)"></span></p>
              <p class="item-other-title" v-for="(c, j) in item.content" :key="j"><span v-html="delTitle(c, j)"></span></p>
              <img class="item-img" v-for="v in item.imglist" :key="v" :src="v" />
            </div>
          </div>
          <div class="empty" v-if='dialogArticleList.length === 0'>
            <span class="empty-img"></span>
            <div class="empty-text">{{!useSwitch ? '未启用AI文章识别' : '暂无数据'}}</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  aiArticleApi,
  saveAiArticleApi,
  getAiGraphic,
  deleteAIArticle
} from 'config/api.conf'
import { transforNcTime } from 'utils/tools'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV] 
export default {
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    },
    approveStatus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      transforNcTime,
      dialogArticleList: [],
      showArticleEdit: true,
      pageArticleList: [],
      pageArticleData: null,
      dialogArticleData: null,
      aiArticleVisible: false,
      useSwitch: false,
      insertItems: [
        'link',
        'table',
        'image',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorEnv: process.env.NODE_ENV,
      editorConfig: {
        catalogue: false,
        options: { // 非必传字段
          selector: '#course_desc',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
              formatselect fontsizeselect lineheight |
              bold italic underline strikethrough |
              forecolor backcolor |
              dent align bullist numlist |
              insert codesample |
              fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      }
    }
  },
  computed: {
    delTitle() {
      return (row, i) => {
        return (row && row[`t${i}`]) || ''
      }
    },
    showAiGraphic() {
      return !!(this.courseData.artificial_graphic_id || this.courseData.ai_graphic_id)
    },
    disabledExport() {
      return !(this.useSwitch && this.dialogArticleList?.length)
    }
  },
  watch: {
    courseData: {
      deep: true,
      immediate: true,
      handler(newVal) {
        this.useSwitch = !!(newVal.enable_ai_graphic === 1)
        if (newVal.artificial_graphic_id) {
          this.getCustomArticle()
        } else {
          this.pageArticleList = []
          this.pageArticleData = null
        }
      }
    }
  },
  mounted() {
  },
  methods: {
    // 删除
    handleDelte() {
      this.$messageBox.confirm('确定删除吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(async () => {
        await deleteAIArticle(this.$route.query.net_course_id)
        this.$emit('getCourse')
        this.pageArticleList = []
        this.pageArticleData = null
      })
    },
    // 查看导入
    checkExport() {
      this.aiArticleVisible = true
      if (this.useSwitch) { // 开启状态有数据
        this.getDialogArticle()
      }
    },
    // 数据导入
    exportData() {
      this.$nextTick(() => {
        this.showArticleEdit = false
      })
      this.pageArticleData = JSON.parse(JSON.stringify(this.delStr(this.dialogArticleData)))
      this.pageArticleList = JSON.parse(JSON.stringify(this.dialogArticleList))
      this.$message.success('数据导入成功')
      this.handleClose()
    },
    onCancel() {
      this.$router.push({ name: 'courseList' })
    },
    onPreview() {
      const id = this.courseData.artificial_graphic_id || this.courseData.ai_graphic_id
      const { href } = this.$router.resolve({ name: 'preview', query: { graphic_id: id, scene: 1 } })
      window.open(href)
    },
    handleClose() {
      this.aiArticleVisible = false
    },
    // ai文章開啓
    handleUseSwitch() {
      const params = {
        course_id: this.$route.query.net_course_id,
        enableAiGraphic: this.useSwitch ? 1 : 0 // 1-开启，0-关闭
      }
      aiArticleApi(params).then((res) => {
        const msg = this.useSwitch ? '已开启AI文章识别' : '已关闭AI文章识别'
        this.$message.success(msg)
        this.$emit('getCourse')
        if (this.useSwitch) { // 开启状态有数据
          this.getDialogArticle()
        } else {
          this.dialogArticleData = null
          this.dialogArticleList = []
        }
      })
    },
    // 获取用户的文章数据
    getCustomArticle(val) {
      const id = val || this.courseData.artificial_graphic_id
      getAiGraphic({ graphic_id: id }).then((res) => {
        this.pageArticleData = res ? this.delStr(res) : ''
        this.pageArticleList = this.forMatList(res)
      })
    },
    // 获取智能文章数据
    getDialogArticle(val) {
      if (!this.courseData.ai_graphic_id) return
      getAiGraphic({ graphic_id: this.courseData.ai_graphic_id }).then((res) => {
        this.dialogArticleData = res
        this.dialogArticleList = this.forMatList(res)
      })
    },
    forMatList(res) {
      res = res ? res.replace(/\n/g, '<br/>') : ''
      let container = document.createElement('div')
      container.innerHTML = res
      let articleList = []
      let currentTime = null
      let currentImgList = []
      let currentTitleList = []
      let currentContent = []
      function createEntry(time, imgList, titleList, content) {
        const otherTitleList = titleList?.length ? titleList.slice(1) : []
        const formattitle = titleList?.length ? titleList[0] : ''
        return {
          time: parseInt(time),
          imglist: imgList,
          title: formattitle,
          titleList: otherTitleList.map((v, i) => { 
            return {
              [`t${i}`]: v 
            }
          }),
          content: content.map((e, j) => {
            return {
              [`t${j}`]: e
            }
          })
        }
      }
      Array.from(container.children).forEach(e => {
        if (e.classList.contains('chaptersTime')) {
          if (currentTime !== null) { // 当前时间
            articleList.push(createEntry(currentTime, currentImgList, currentTitleList, currentContent))
            currentImgList = []
            currentTitleList = []
            currentContent = []
          }
          currentTime = e.getAttribute('id')
        } else if (e.tagName === 'H1' || e.tagName === 'H2' || e.tagName === 'H3') { // 标题
          currentTitleList.push(e.innerHTML)
        } else if (e.tagName === 'P' && e.querySelector('img')) { // 图片
          let img = e.querySelector('img')
          currentImgList.push(
            `${envName.contentcenter}content-center/api/v1/content/imgage/${img.getAttribute('data-content')}/preview`
          )
          // 规避图片中也有文本
          currentContent.push(e.innerHTML)
        } else { // 内容
          currentContent.push(e.innerHTML)
        }
      })
      if (currentTime !== null) {
        articleList.push(createEntry(currentTime, currentImgList, currentTitleList, currentContent))
      }
      return articleList
    },
    // 保存文章
    onSubmit() {
      let str = this.$refs['editor'].getContent()
      const regTime = /<p>章节开始时间-(\d{2}):(\d{2}):(\d{2})<\/p>/g
      if (!regTime.test(str)) {
        this.$message.warning('请输入正确的时间格式（章节开始时间-00:00:00）')
        return
      }
      const regTitle = /<(h[1-3])>章节标题-(.*?)<\/\1>/g
      if (!regTitle.test(str)) {
        this.$message.warning('请输入正确的标题格式（章节标题-标题内容）')
        return
      }
      str = str.replace(/<p>章节开始时间-(\d{2}):(\d{2}):(\d{2})<\/p>/g, (match, h, m, s) => {
        const seconds = parseInt(h) * 3600 + parseInt(m) * 60 + parseInt(s)
        return `<p class="chaptersTime" id="${seconds}"></p>`
      }).replace(/<(h[1-3])>章节标题-(.*?)<\/\1>/g, '<$1>$2</$1>')
      saveAiArticleApi({ graphic_content: str }, this.$route.query.net_course_id).then((res) => {
        this.showArticleEdit = true
        this.$message.success('保存成功')
        this.$emit('getCourse')
      })
    },
    delStr(res) {
      return res = res.replace(/<p class="chaptersTime" id="(\d+)"><\/p>/g, (match, id) => {
        const hours = String(Math.floor(id / 3600)).padStart(2, '0')
        const minutes = String(Math.floor((id % 3600) / 60)).padStart(2, '0')
        const seconds = String(id % 60).padStart(2, '0')
        return `<p>章节开始时间-${hours}:${minutes}:${seconds}</p>`
      }).replace(/<(h[1-3])(?:\s+id="[^"]*")?>(.*?)<\/\1>/g, '<$1>章节标题-$2</$1>')
    }
  }
}
</script>
<style lang="less" scoped>
  .ai-article-content {
    .ai-top-content {
      background: #fff;
      padding: 16px 20px;
      display: flex;
      align-items: center;
      line-height: 22px;
      .tips-content-box {
        display: flex;
        i {
          margin-right: 4px;
        }
        .tips-disabled {
          color: #9ab2f0;
          cursor: not-allowed;
        }
      }
      .ai-link {
        margin-right: 20px;
        display: flex;
        align-items: center;
      }
      .tips {
        color: #0052D9;
        display: flex;
        align-items: center;
        // .export-click{
        //   text-decoration: underline;
        //   color: #0052D9;
        //   cursor: pointer;
        // }
      }
    }
    .article-content {
      background:#fff;
      padding: 20px;
      margin-top: 12px;
      .content-top {
        display: flex;
        align-items: center;
        justify-content: space-between;
      }
      .content-main {
        margin-top: 12px;
      }
    }
  }
  .article-list-box {
    cursor: pointer;
    margin-bottom: 12px;
    .tp-title {
      margin-bottom: 4px;
      line-height: 22px;
      display: flex;
      .time {
        border-radius: 4px;
        background: #EEE;
        padding: 0 8px;
        height: 24px;
        color: #333333;
        margin-right: 16px;
        display: flex;
        align-items: center;
        span {
          height: 22px;
          line-height: 22px;
          display: inline-block;
        }
      }
      .title {
        font-weight: bold;
        color: #333333;
      }
    }
    .article-card-content {
      .item-other-title {
        line-height: 22px;
        letter-spacing: 0.28px;
        margin-top: 4px;
      }
      .item-img {
        margin-top: 4px;
        width: 315px;
        height: 177px;
        border-radius: 4px;
      }
    }
  }
  .buttom-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 74px;
    line-height: 74px;
    background-color: #fff;
    text-align: center;
    z-index: 99;
    .inner {
      display: inline-block;
      width: 1440px;
      padding-left: 188px;
      text-align: left;
    }
    .confirm-btn, .cancel-btn {
      width: 104px;
      height: 40px;
      border-radius: 4px;
    }
    .el-button + .el-button {
      margin-left: 20px;
    }
    .confirm-btn {
      background: #0052D9;
    }
  }
  .ai-article-dialog {
    .article-body-main {
      height: 500px;
      overflow: auto;
      margin-top: 20px;
    }
    .label-tips {
      color: #00000066;
      font-size: 14px;
      line-height: 22px;
      margin-left: 12px;
    }
      
    .ai-article-body {
      :deep(.ai-switch){
        .el-switch__core {
          height: 16px;
        }
        .el-switch__core:after {
          width: 12px;
          height: 12px;
        }
      }
      :deep(.is-checked) {
        .el-switch__core::after {
          margin-left: -13px;
        }
      }
      .label {
        color: #000000;
        margin-right: 12px;
      }
      .ai-switch-item {
        margin-bottom: 21px;
      }
      .chapters-content {
        height: 550px;
        overflow-y: auto;
        padding-right: 16px;
        .chapters-list-box {
          display: flex;
          align-items: baseline;
          color: #00000099;
          margin-top: 24px;
          .form-title {
            margin-right: 12px;
            height: 22px;
            line-height: 22px;
            font-weight: bold;
          }
  
          .chapter-dialog-form {
            flex: 1;
            .dialog-item {
              margin-bottom: 24px;
              display: flex;
            }
            .dialog-item:last-of-type {
              margin-bottom: unset;
            }
            .placehodler {
              margin-right: 13px;
              display: inline-block;
            }
            .form-time-item {
              display: flex;
              align-items: center;
              .minute-input-style, 
              .second-input-style {
                width: 56px;
                margin-left: 8px;
                margin-right: 8px;
              }
              :deep(.is-disabled) {
                .el-input__inner {
                  padding-right: 5px;
                }
              }
            }
            .form-coverImg-item {
              display: flex;
              .cover-label {
                margin-right: 12px;
              }
            }
            .form-title-item {
              display: flex;
              .red-start {
                color: red;
                margin-right: 7px;
              }
            }
          }
        }
      }
    }
  }
  .empty {
    text-align: center;
    background:#fff;
    padding-bottom: 20px;

    .empty-text {
      margin-top: 17px;
      color: #999999;
      font-size: 14px;
      margin-bottom: 24px;
    }

    .empty-img {
      display: inline-block;
      width: 160px;
      height: 160px;
      background: url(~@/assets/img/empty.png) no-repeat center/contain;
    }
  }
</style>
