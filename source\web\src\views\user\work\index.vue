<template>
  <div class="work-page">
    <div class="contain-main">
      <div class="contain-wrapper" :class="{ 'padding-b': (activeTab === 'myWork' && unopenedState) || activeTab === 'workEvaluation' }">
        <p class="work-title">
          {{ workInfo.title }}
          <span :class="['work-status', workStatus.key]">{{ workStatus.text }}</span>
        </p>
        <div class="back-box" v-if="workStatus.key === 'reject'">
          <div class="back">
            <p class="back-t"><i class="el-icon-warning-outline"></i> 作业已被退回，完成作业内容修改后请尽快提交。</p>
            <p><span class="y-color">退回原因：</span>
              <span class="result">
                {{backReason}}
              </span>
            </p>
          </div>
        </div>
        <div class="contain-content">
          <div class="tab-nav">
            <span 
            class="tab-pane" 
            :class="{ 'line-height': activeTab === item.value }" 
            @click="tabClick(item)"
            v-for="item in tabList" 
            :key="item.value"
            >
              {{ $langue(item.label, { defaultText: item.defaultText }) }}
            </span>
          </div>
          <!-- 作业说明 -->
          <workExplanation v-if="activeTab === 'workExplanation'" :workInfo="workInfo" @changeTab="changeTab">
          </workExplanation>
          <!-- 我的作业 -->
          <myWork v-if="activeTab === 'myWork'" @getExplainInfo="getExplainInfo" @myWorkRecord="myWorkRecord"
            :active="activeTab" :workInfo="workInfo" ref="myWorkRef"></myWork>
          <!-- 作业互评 -->
          <workEvaluation v-if="activeTab === 'workEvaluation'" @getExplainInfo="getExplainInfo"
            :workInfo="workInfo" :anonymous="workInfo.enabled_student_mark_anonymous"></workEvaluation>
          <!-- 作业列表 -->
          <!-- 开启共享查看且勾选其他学员，同时如果评分方式勾选了学员互评则不显示 -->
          <workList v-if="activeTab === 'workList'"></workList>
        </div>
      </div>
      <!-- 评分详情 -->
      <template v-if="workInfo.enable_mark && !unopenedState">
        <div class="contain-details" v-show="activeTab === 'myWork'">
          <scoreDetails :detailData="workInfo" ref="scoreDetailsRef"></scoreDetails>
        </div>

      </template>
    </div>
  </div>
</template>

<script>
import workExplanation from './workExplanation.vue'
import scoreDetails from '@/views/components/scoreDetails'
import myWork from './myWork.vue'
import workEvaluation from './workEvaluation.vue'
import workList from './workList.vue'
import { homework_detail, getHomeworkRecord } from 'config/api.conf'
import moment from 'moment'
import MoocJs from 'sdc-moocjs-integrator'
import translate from 'mixins/translate.vue'
export default {
  mixins: [translate],
  components: {
    workExplanation,
    myWork,
    workEvaluation,
    workList,
    scoreDetails
    
  },
  data() {
    return {
      workDisabled: false,
      saveDraftTimer: null,
      tabList: [
        { label: 'Mooc_TaskDetail_HomeWork_HomeworkDesc', value: 'workExplanation', defaultText: '作业说明' },
        { label: 'Mooc_TaskDetail_HomeWork_MyHomework', value: 'myWork', defaultText: '我的作业' }
      ],
      workInfo: {},
      activeTab: 'workExplanation',
      query: {
        act_id: '',
        homework_id: '',
        task_id: ''
      },
      scoreDetail: {},
      backReason: ''
    }
  },
  mounted() {
    MoocJs.messageListener((res) => {
      if (res.events === 'tencent-mooc-lang') {
        localStorage.setItem('sdc-sys-def-lang', res.params)
        this.getLangJS()
      } 
    })
  },
  computed: {
    isPreview() {
      return this.$route.query.previewType === 'preview'
    },
    // 老师批阅 1  学员互评 2
    markTypeTwo() {
      return (type) => {
        const markType = this.workInfo?.mark_type
        if (markType?.length === 1) {
          return markType.includes(type)
        }
        return markType?.split(';').includes(type)
      }
    },
    workStatus() {
      let status = {}
      let type = this.workInfo.status
      if (type === null || type === 0) {
        status = {
          text: '未提交',
          key: 'unsubmit'
        }
      } else if (type === 1) {
        status = {
          text: this.workInfo.mark_type.includes('1') ? this.$langue('Mooc_TaskDetail_HomeWork_ToBeReviewed', { defaultText: '待批阅' }) : this.$langue('Mooc_ProjectDetail_TaskList_Submited', { defaultText: '已提交' }),
          key: 'reviewed'
        }
      } else if (type === 2) {
        status = {
          text: this.$langue('Mooc_ProjectDetail_TaskList_Backed', { defaultText: '已退回' }),
          key: 'reject'
        }
      } else if (type === 3) {
        status = {
          text: this.$langue('Mooc_ProjectDetail_TaskList_UpStandard', { defaultText: '合格' }),
          key: 'pass'
        }
      } else if (type === 4) {
        status = {
          text: this.$langue('Mooc_ProjectDetail_TaskList_LowStandard', { defaultText: '不合格' }),
          key: 'unpass'
        }
      }
      return status
    },
    // 未提交状态、包含作业被退回
    unopenedState() {
      return [null, 0, 2].includes(this.workInfo.status)
    }
  },
  created() {
    this.getExplainInfo('init')
  },
  methods: {
    // 获取作业说明
    getExplainInfo(flag, submitResult) {
      const { homework_id, task_id, act_id } = this.$route.query
      let params = {
        act_id,
        homework_id,
        task_id
      }
      homework_detail(params).then((res) => {
        this.workInfo = res
        if (res.start_time) this.workInfo.start_time = moment(res.start_time).format('YYYY-MM-DD HH:mm')
        if (res.end_time) this.workInfo.end_time = moment(res.end_time).format('YYYY-MM-DD HH:mm')
        if (res.student_mark_start_time) this.workInfo.student_mark_start_time = moment(res.student_mark_start_time).format('YYYY-MM-DD HH:mm')
        if (res.student_mark_end_time) this.workInfo.student_mark_end_time = moment(res.student_mark_end_time).format('YYYY-MM-DD HH:mm')
        // 区分是页面初始加载，还是操作中途需要更新加载
        if (flag === 'init') {
          if (this.markTypeTwo('2') && !this.isPreview) {
            // 开启了互评学员
            this.tabList.push({ label: 'Mooc_TaskDetail_HomeWork_EvaluateEachOther', value: 'workEvaluation', defaultText: '作业互评' })
          } else if (!this.markTypeTwo('2') && this.workInfo.enabled_share && this.workInfo.shared_type.includes('1') && !this.isPreview) {
            // shared_type 1 其他学员 2 学员直接上级 3 学员所属组织BP
            // 开启学员共享查看
            this.tabList.push({ label: 'Mooc_TaskDetail_HomeWork_HoneworkList', value: 'workList', defaultText: '作业列表' })
          }
          if (this.workInfo.status === 2) {
            getHomeworkRecord({ act_id, homework_id }).then(res => {
              this.backReason = res.teacher_mark_record?.content || '-'
            })
          }
        } else {
          if (this.$route.query.from === 'mooc') {
            // 监听
            MoocJs.postMessage('updateStatus', submitResult)
          }
        }
      })
    },
    // 我的作业获取评分详情
    myWorkRecord(data) {
      if (!this.unopenedState && this.workInfo.enable_mark) { // 提交作业获取评分详情
        this.$nextTick(() => {
          this.$refs['scoreDetailsRef'].initUserScore({ record_id: data.record_id, homework_id: data.homework_id })
        })
      }
      if (this.workInfo.status === 2) {
        this.backReason = data.teacher_mark_record?.content || ''
      }
    },
    tabClick(item) {
      if (this.activeTab === item.value) return
      if (this.unopenedState && item.value === 'workEvaluation') {
        this.$message.warning('请先提交作业，然后参与互评')
        return
      }
      if (this.unopenedState && item.value === 'workList') {
        this.$message.warning('提交作业后可查看其他学员作业')
        return
      }
      if (this.activeTab === 'myWork' && [null, 0].includes(this.workInfo.status)) { // 未提交作业切换
        this.$confirm(
          '切换后当前编辑的内容不会保存，请谨慎操作', '确定切换作业？', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }
        ).then(() => {
          this.activeTab = item.value
        })
      } else {
        this.activeTab = item.value
      }
    },
    changeTab(val) {
      this.activeTab = val
    }
  }
}
</script>

<style lang="less" scoped>
.work-page {
  height: 100%;
  .padding-b {
    margin-bottom: 70px;
  }
  .back-box{
    padding: 0 24px;
    margin-top: 16px;
  }
  .back {
    background-color: #FEF3E6;
    padding: 12px;
    border-radius: 3px;
    color: #FF7548;
    .back-t {
      padding-bottom: 10px;
    }
  }
  .contain-main {
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 10px 0 20px;
  }

  .contain-wrapper {
    width: 1000px;
    background-color: #fff;
    .work-title {
      padding: 24px;
      border-bottom: 1px solid #eeeeeeff;
      font-size: 20px;
      font-weight: 600;
      color: #000000ff;

      .work-status {
        font-size: 12px;
        font-weight: 400;
        height: 20px;
        line-height: 20px;
        padding: 0 6px;
        border-radius: 2px;
        display: inline-block;
        background: #f3f3f3;
        color: #00000042;
      }

      .unsubmit {
        color: #00000042;
        background: #f3f3f3ff;
      }

      .unpass {
        color: #e34d59;
        background: #fdecee;
      }

      .reject {
        color: #ed7b2f;
        background: #fef3e6;
      }

      .pass {
        background: #ccf2e2;
        color: #00b368;
      }

      .reviewed {
        background: #d4e3fc;
        color: #0052d9;
      }
    }

    .contain-content {
      position: relative;
      padding: 16px 24px;

      .tab-nav {
        padding: 0 4px;
        height: 36px;
        line-height: 36px;
        display: inline-block;
        background: #f8f8f8ff;
        border-radius: 4px;
        margin-bottom: 20px;

        .tab-pane {
          font-size: 14px;
          padding: 0 12px;
          height: 28px;
          line-height: 28px;
          display: inline-block;
          border-radius: 4px;
          cursor: pointer;
        }

        .line-height {
          color: #0052d9;
          background: #fff;
        }
      }
    }
  }

  .contain-details {
    width: 320px;
  }

  .my-work-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70px;
    line-height: 70px;
    background-color: #fff;
    z-index: 99;

    .footer-content {
      text-align: center;
      color: #00000066;

      span {
        padding-left: 24px;
      }
    }

    .footer-content-work {
      width: 1100px;
      margin: auto;
      text-align: right;
    }

    .inner {
      @media screen and (max-width: 1660px) {
        width: 1158px;
      }

      @media screen and (min-width: 1661px) {
        width: 1440px;
      }

      text-align: right;
      margin: 0 auto;
    }

    .el-button {
      margin: 0 20px 0 0;
      width: 104px;
    }
  }
}
</style>
