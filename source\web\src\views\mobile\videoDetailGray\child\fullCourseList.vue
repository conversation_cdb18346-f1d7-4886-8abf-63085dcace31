<template>
  <div :class="isPad ? 'course-card-container-pad': 'course-card-container'" v-if="commonInfo.list.length || commonInfo.recomendList.length">
    <swiper ref="swiperCard" :options="swiperOption">
      <template v-if="fromType">
        <swiper-slide class="item-course-swiper" v-for="(e, index) in commonInfo.list" :key="index" :data-id="e.item_id" data-type="title">
          <div :class="['chapter-item-title', { 'active-item-swiper': e.item_id == course_id }]" :data-id="e.item_id" data-type="title">
            <div class="img-box" :data-id="e.item_id" data-type="title">
              <van-image class="item-img" lazy fit="fill" :src="e.cover_img_url || formatModuleMap(e.module_id)" :data-id="e.item_id" data-type="title">
                <template v-slot:error>
                  <van-image class="item-img" lazy fit="fill" :src="formatModuleMap(e.module_id)" :data-id="e.item_id" data-type="title"></van-image>
                </template>
              </van-image>
              <span class="module-icon">{{ e.module_name }}</span>
              <div class="play-time" v-if="showModuleTips(e)">{{ showModuleTips(e) }}</div>
            </div>
            <div class="course-name-box overflow-l2" :data-id="e.item_id" data-type="title">
              <span class="title">{{ e.content_name }}</span>
            </div>
          </div>
        </swiper-slide>
      </template>
      <swiper-slide id="more-1" class="item-swiper-more mgb-8 mgl-8" v-if="fromType && commonInfo.list.length">
        <img class="more-item" @click="toPage(fromType === 'isSpecial' ? 'area' : 'courseList')" :src="require(`@/assets/img/mobile/${fromType === 'isSpecial' ? 'to-area' : 'course-list'}.png`)" alt="" />{{fromType === 'isSpecial' ? '查看专区' : '查看课单'}}
      </swiper-slide>
      <template v-if="fromType">
        <swiper-slide class="item-course-swiper" :class="{'item-course-swiper-5': index === 4 && !commonInfo.list.length}" v-for="(e, index) in commonInfo.recomendList" :key="index+'key'" :data-id="e.item_id" data-type="recomend">
          <div :class="['chapter-item-title', { 'active-item-swiper': e.item_id == course_id }]" :data-id="e.item_id" data-type="recomend">
            <div class="img-box" :data-id="e.item_id" data-type="recomend">
              <van-image class="item-img" lazy fit="fill" :src="e.cover_img_url || formatModuleMap(e.module_id)" :data-id="e.item_id" data-type="recomend">
                <template v-slot:error>
                  <van-image class="item-img" lazy fit="fill" :src="formatModuleMap(e.module_id)" :data-id="e.item_id" data-type="recomend"></van-image>
                </template>
              </van-image>
              <span class="module-icon">{{ e.module_name }}</span>
              <div class="play-time" v-if="showModuleTips(e)">{{ showModuleTips(e) }}</div>
            </div>
            <div class="course-name-box overflow-l2" :data-id="e.item_id" data-type="recomend">
              <span class="title">{{ e.content_name }}</span>
            </div>
          </div>
        </swiper-slide>
      </template>
      <swiper-slide id="more-2" class="item-swiper-more mgl-8" v-if="commonInfo.recomendList.length">
        <img class="more-item" @click="toPage('home')" src="@/assets/img/mobile/to-home.png" alt="" />
      </swiper-slide>
    </swiper>
    <div class="course-title-box">
      <span class="title-num" :class="{'title-active': tabActive === 'title'}" @click="changedTab('title')" v-if="commonInfo.list.length">{{ title }}</span>
      <span class="title-more" :class="{'title-active': tabActive === 'recomend', 'mgl-0': commonInfo.list && !commonInfo.list.length}" @click="changedTab('recomend')" v-if="commonInfo.recomendList.length">更多推荐</span>
    </div>
  </div>
</template>
<script>
import {
  getCourseDetailAPI,
  getCourseViewCount,
  specialAreaAPI,
  specialDetail,
  getRecommendList,
  isSysBusy
} from 'config/api.conf'
import { transformUnitW, formatModuleMap, transforNcTime } from 'utils/tools'
// import env from 'config/env.conf.js'
// const envName = env[process.env.NODE_ENV]
export default {
  props: {
    fromType: {
      type: String,
      default: ''
    },
    isPad: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tabActive: 'title',
      commonInfo: {
        name: '',
        list: [], // 专区、课单
        recomendList: [] // 更多推荐
      },
      transformUnitW,
      formatModuleMap,
      transforNcTime
    }
  },
  computed: {
    course_id() {
      return this.$route.query.course_id ? parseInt(this.$route.query.course_id) : ''
    },
    title() {
      const val = this.fromType === 'isSpecial' ? '专区' : '课单'
      return `${val}·${this.commonInfo.name}`
    },
    showModuleTips() {
      return ({ module_id, duration, word_num, task_count }) => {
        let tips = ''
        duration = duration * 60
        if ([1, 2, 4].includes(module_id)) { // 网络课, 面授课，活动
          tips = this.fromType === 'isSpecial' ? transforNcTime(duration) : transforNcTime(duration)
        } else if ([7, 8].includes(module_id)) { // 案例，文章
          tips = `${word_num || 0}` + this.$langue('Mooc_ProjectDetail_TaskList_Words', { defaultText: '字' })
        } else if ([10].includes(module_id)) { // 培养项目
          tips = `${task_count || 0}` + this.$langue('Mooc_ProjectDetail_BasicInfo_Tasks', { defaultText: '项任务' })
        } else if ([15].includes(module_id)) { // 课单
          tips = `·${task_count || 0}` + this.$langue('NetCourse_Contents', { defaultText: '个内容' })
        }
        return tips
      }
    },
    swiperOption() {
      return {
        loop: false,
        autoplay: false,
        direction: this.isPad ? 'horizontal' : 'vertical',
        slidesPerView: 'auto',
        freeMode: false,
        lazy: {
          loadPrevNext: true
        },
        navigation: { // 设置点击箭头
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        on: {
          tap: (e) => {
            const aId = e.target.parentElement.getAttribute('data-id')
            const type = e.target.parentElement.getAttribute('data-type')
            if (aId) {
              let list = type === 'title' ? this.commonInfo.list : this.commonInfo.recomendList
              const project = list.find((e) => e.item_id === aId)
              this.toLink(project, type)
            }
          },
          touchEnd: () => {
            this.transformTab()
          }
        }
      }
    }
  },
  watch: {
    fromType: {
      immediate: true,
      handler(val) {
        if (val === 'isCourse') {
          this.getCourseList()
        } else if (val === 'isSpecial') {
          this.getSpcialList()
        } else {
          this.tabActive = 'recomend'
          this.getRecommendListFn()
        }
      }
    },
    'commonInfo.list': {
      immediate: true,
      handler(newVal) {
        const fieldMap = {
          content_url: ['href']
        }
        newVal.forEach((item) => {
          for (let targetKey in fieldMap) {
            if (!item.hasOwnProperty(targetKey)) {
              for (let sourceKey of fieldMap[targetKey]) {
                item[targetKey] = item[sourceKey]
              }
            }
          }
          if (this.fromType === 'isSpecial') {
            item.task_count = item.origin_data.task_count
            // 专区图片处理
            // try {
            //   if (item.content) {
            //     item.cover_img_url = JSON.parse(item.content)?.thumbnail_url || item.photo_url || ''
            //   }
            // } catch (error) {
            //   item.cover_img_url = item.photo_url || ''
            // }
          }
        })
      }
    }
  },
  methods: {
    // 滚动时和上面的tab栏联动
    transformTab() {
      if (!this.$refs.swiperCard.$swiper) return
      let scrollInstance = Math.abs(this.$refs.swiperCard.$swiper.translate || 0)
      let dom = document.querySelector('#more-1')
      if (!dom) return
      let leftInstance = this.isPad ? dom.offsetLeft : dom.offsetTop + 44
      if (scrollInstance >= leftInstance) {
        this.tabActive = 'recomend'
      } else {
        this.tabActive = 'title'
      }
    },
    // 切换tab栏
    changedTab(type) {
      this.tabActive = type
      if (type === 'title') {
        // 专区、课单
        this.$refs.swiperCard && this.$refs.swiperCard.$swiper.slideTo(0, 600, false)
      } else {
        // 更多推荐
        this.$refs.swiperCard && this.$refs.swiperCard.$swiper.slideTo(this.commonInfo.list.length + 1, 600, false)
      }
    },
    // 专区
    async getSpcialList() {
      const { area_id } = this.$route.query
      const param = {
        app_id: 'A9BiosXihR0h46ThNsAX',
        terminal_type: 1,
        condition: {
          orderby: [{ column: 'view_count_total', type: 'desc' }]
        },
        current: 1,
        size: 10
      }
      const detailApi = await specialDetail(area_id, { app_id: 'A9BiosXihR0h46ThNsAX' })
      const listApi = await specialAreaAPI(area_id, param)
      const params1 = {
        module_id: 1,
        item_id: this.$route.query.course_id || '',
        limit: 10
      }
      let promiseArray = [detailApi, listApi]
      let isBusy = await isSysBusy()
      if (isBusy !== '1') {
        const recomendApi = await getRecommendList(params1)
        promiseArray.push(recomendApi)
      }
      Promise.all(promiseArray).then((res) => {
        const name = res[0].name
        const page_id = res[0].page_id
        const list = res[1].records && res[1].records.length ? res[1].records.slice(0, 3) : []
        let recomendList = res[2] && res[2].length ? res[2].slice(0, 10) : []
        recomendList = this.resolveRecommendData(recomendList)
        this.commonInfo = {
          name,
          list,
          recomendList,
          page_id
        }
      })
    },
    // 课单
    async getCourseList() {
      const { area_id, from } = this.$route.query
      const param = {
        from,
        share_staff_name: '',
        share_staff_id: ''
      }
      const listApi = await getCourseDetailAPI(area_id, param)
      const params1 = {
        module_id: 1,
        item_id: this.$route.query.course_id || '',
        limit: 10
      }
      let promiseArray = [listApi]
      let isBusy = await isSysBusy()
      if (isBusy !== '1') {
        const recomendApi = await getRecommendList(params1)
        promiseArray.push(recomendApi)
      }
      Promise.all(promiseArray).then((res) => {
        const name = res[0].name
        const page_id = res[0].cl_id
        const list = res[0].content_basic_list && res[0].content_basic_list.length ? res[0].content_basic_list.slice(0, 3) : []
        let recomendList = res[1] && res[1].length ? res[1].slice(0, 10) : []
        recomendList = this.resolveRecommendData(recomendList)
        this.commonInfo = {
          name,
          list,
          recomendList,
          page_id
        }
      })
    },
    // 获取推荐数据
    getRecommendListFn() {
      // 先判断服务器忙不忙 不忙时再调用推荐接口
      isSysBusy().then(res => {
        if (res !== '1') {
          const params = {
            module_id: 1,
            item_id: this.$route.query.course_id || '',
            limit: 10
          }
          getRecommendList(params).then(res => {
            let recomendList = res && res.length ? res.slice(0, 10) : []
            this.commonInfo.recomendList = this.resolveRecommendData(recomendList)
          }).catch(err => {
            console.log('err: ', err)
          })
        }
      })
    },
    // 单个详情跳转
    async toLink({ module_id, content_id, content_url }, type) {
      let url = this.fromType === 'isSpecial' ? `${content_url}&from=SpecialArea&area_id=${this.commonInfo.page_id}` : content_url
      if (this.fromType === 'isCourse' && type !== 'recomend') { // 课单调用
        await getCourseViewCount(this.$route.query.area_id)
      }
      // 码客、行家、图文、外链暂不支持跳转
      const moduleIds = [5, 6, 9, 99]
      if (moduleIds.includes(module_id)) {
        this.$router.push({
          name: 'mobileError',
          query: {
            type: 2,
            href: encodeURIComponent(url)
          }
        })
      } else if (module_id === 1) { // 网络课
        window.location.href = url
      } else if (module_id === 2) { // 面授课
        window.location.href = `https://sdc.qq.com/s/Eom5bg?scheme_type=faceClassDetail&course_id=${content_id}`
      } else if (module_id === 8) {
        if (window.__wxjs_environment === 'miniprogram') {
          // 小程序内直接跳转
          window.wx.miniProgram.navigateTo({
            url: `/pages/networkCourse/article/index?graphic_id=${content_id}`
          })
        } else {
          // 打开二合一地址唤起小程序
          window.location.href = url
        }
      } else if (url) {
        window.location.href = url
      }
    },
    // 页面跳转
    toPage(symble) {
      let url = ''
      if (symble === 'area') {
        // 专区
        // if (window.top === window.self) { // 当前页面没有被iframe嵌套
        //   window.wx.miniProgram.navigateTo({ url: `/pages/special/index?page_id=${this.commonInfo.page_id}` })
        // } else {
        //   window.parent.postMessage({ event: 'toSpecial', url: `/pages/special/index?page_id=${this.commonInfo.page_id}` }, '*')
        // }
        url = `//sdc.qq.com/s/Shd3Jk?scheme_type=homepage&page_id=${this.commonInfo.page_id}`
      } else if (symble === 'courseList') {
        // 课单
        // const mobCourseListHref = `${envName.courseWoaHost}mobile/courselist/course-detail?id=${this.commonInfo.page_id}`
        // const url = '/pages/specialView/index?href=' + encodeURIComponent(mobCourseListHref)
        // if (window.top === window.self) {
        //   window.wx.miniProgram.navigateTo({ url })
        // } else {
        //   window.parent.postMessage({ event: 'toCourse', url }, '*')
        // }
        url = `//sdc.qq.com/s/TG3H8U?scheme_type=courselist&id=${this.commonInfo.page_id}`
      } else {
        // 首页
        // if (window.top === window.self) {
        //   window.wx.miniProgram.navigateTo({ url: `/pages/main/index?tab=home` })
        // } else {
        //   window.parent.postMessage({ event: 'toSearch', url: '/pages/main/index?tab=home' }, '*')
        // }
        url = `//sdc.qq.com/s/Shd3Jk?scheme_type=homepage`
      }
      window.location.href = url
    },
    // 接口拿到"更多推荐"数据后，处理格式和"课单、专区"一样
    resolveRecommendData(list) {
      const fieldMap = {
        cover_img_url: ['thumbnail_url'],
        content_name: ['title'],
        content_url: ['href'],
        module_name: ['content_module_name'],
        module_id: ['content_module_id'],
        view_count: ['view_count_uv', 'play_total_count']
      }
      list.forEach((item) => {
        for (let targetKey in fieldMap) {
          if (!item.hasOwnProperty(targetKey)) {
            for (let sourceKey of fieldMap[targetKey]) {
              item[targetKey] = item[sourceKey]
            }
          }
        }
        item.avg_score = item.origin_data.avg_score
        item.duration = item.origin_data.est_dur || 0
      })
      return list
    }
  }
}
</script>
<style lang="less" scoped>
.course-card-container {
  position: absolute;
  left: 60px;
  top: 0;
  width: 168px;
  height: 100vh;
  background-color: #00000066;
  z-index: 1000;
  padding: 0 50px 0 0;
  /deep/.swiper-container {
    padding-top: 20px;
    height: 100vh;
    overflow: hidden;
    .swiper-wrapper {
      height: auto;
    }
  }
  .item-course-swiper-5 {
    margin-left: 0 !important;
  }
  .item-course-swiper {
    width: 109px;
    height: 118px;
    overflow: hidden;
    transform: rotate(90deg) translate(100%, 0%);
    transform-origin: right top;
    &:first-child, &:nth-child(5) {
      transform: rotate(90deg) translate(100%, -8px);
    }
    .chapter-item-title {
      width: 100%;
      height: 100%;
    }
    .img-box {
      position: relative;
      margin-bottom: 6px;
      width: 109px;
      height: 72px;
      .module-icon {
        position: absolute;
        top: 4px;
        left: 4px;
        height: 16px;
        padding: 0px 4px;
        border-radius: 4px;
        // color: #777777;
        color: #ffffff;
        font-size: 10px;
        font-weight: 500;
        line-height: 16px;
        // background: #F5F5F7;
        background: #00000066;
      }
      .play-time {
        position: absolute;
        right: 3.6px;
        bottom: 4px;
        border-radius: 5px;
        padding: 0 6px;
        text-align: center;
        color: #fff;
        font-size: 10px;
        font-weight: 600;
        height: 20px;
        line-height: 20px;
        background: #00000099;
      }
    }
    :deep(.item-img) {
      width: 109px;
      height: 72px;
      text-align: center;
      line-height: 91px;
      img, .van-image__loading, .van-image__error {
        width: 109px;
        height: 72px;
        border-radius: 4px;
      }
    }
    .overflow-l2 {
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      color: #ffffff;
    }
    .course-name-box {
      height: 40px;
      .title {
        color: #ffffff;
        line-height: 20px;
        font-size: 11px;
        font-weight: 500;
      }
    }
  }
  .active-item-swiper {
    .title {
      color: #0052D9;
    }
  }
  .item-course-swiper + .item-course-swiper {
    margin-left: 8.4px;
  }

  .mgb-8 {
    margin-bottom: 8px;
  }
  .item-swiper-more {
    width: 118px;
    height: 44px;
    overflow: hidden;
    .more-item {
      width: 44px;
      height: 118px;
      transform: rotate(90deg) translate(0, -100%);
      transform-origin: left top;
    }
  }

  .course-title-box {
    position: absolute;
    top: 0;
    right: 0;
    width: 100vh;
    height: 22px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    line-height: 22px;
    color: #ffffff99;
    font-size: 12px;
    font-weight: 500;
    transform: rotate(90deg) translate(100%, 16px);
    transform-origin: right top;
    z-index: 1000;
    .title-num {
      max-width: 224px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap
    }
    .title-more {
      margin-left: 20px;
    }
    .title-active {
      color: #ffffff;
    }
    .mgl-0 {
      margin-left: 0;
    }
  }
}
.course-card-container-pad {
  position: absolute;
  left: 0;
  bottom: 60px;
  width: 100vw;
  height: 168px;
  background-color: #00000066;
  z-index: 1000;
  padding-top: 50px;
  /deep/.swiper-container {
    padding-left: 20px;
    width: 100vw;
    overflow: hidden;
    .swiper-wrapper {
      width: auto;
    }
  }
  .item-course-swiper-5 {
    margin-left: 0 !important;
  }
  .item-course-swiper {
    width: 109px;
    height: 118px;
    overflow: hidden;
    &:first-child, &:nth-child(5) {
      // transform: translate(100%, -8px);
      margin-left: 8px;
    }
    .chapter-item-title {
      width: 100%;
      height: 100%;
    }
    .img-box {
      position: relative;
      margin-bottom: 6px;
      width: 109px;
      height: 72px;
      .module-icon {
        position: absolute;
        top: 4px;
        left: 4px;
        height: 16px;
        padding: 0px 4px;
        border-radius: 4px;
        color: #ffffff;
        font-size: 10px;
        font-weight: 500;
        line-height: 16px;
        background: #00000066;
      }
      .play-time {
        position: absolute;
        right: 3.6px;
        bottom: 4px;
        border-radius: 5px;
        padding: 0 6px;
        text-align: center;
        color: #fff;
        font-size: 10px;
        font-weight: 600;
        height: 20px;
        line-height: 20px;
        background: #00000099;
      }
    }
    :deep(.item-img) {
      width: 109px;
      height: 72px;
      text-align: center;
      line-height: 91px;
      img, .van-image__loading, .van-image__error {
        width: 109px;
        height: 72px;
        border-radius: 4px;
      }
    }
    .overflow-l2 {
      overflow: hidden;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      color: #ffffff;
    }
    .course-name-box {
      height: 40px;
      .title {
        color: #ffffff;
        line-height: 20px;
        font-size: 11px;
        font-weight: 500;
      }
    }
  }
  .active-item-swiper {
    .title {
      color: #0052D9;
    }
  }
  .item-course-swiper + .item-course-swiper {
    margin-left: 8.4px;
  }

  .mgl-8 {
    margin-left: 8px;
  }
  .item-swiper-more {
    width: 44px;
    height: 118px;
    overflow: hidden;
    .more-item {
      width: 44px;
      height: 118px;
    }
  }

  .course-title-box {
    position: absolute;
    top: 16px;
    left: 0;
    width: 100vw;
    height: 22px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    line-height: 22px;
    color: #ffffff99;
    font-size: 12px;
    font-weight: 500;
    z-index: 1000;
    .title-num {
      max-width: 224px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap
    }
    .title-more {
      margin-left: 20px;
    }
    .title-active {
      color: #ffffff;
    }
    .mgl-0 {
      margin-left: 0;
    }
  }
}
</style>
