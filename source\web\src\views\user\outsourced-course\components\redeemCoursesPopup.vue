<template>
  <div class="redeem-courses-pupop">
    <el-dialog custom-class="redeem-courses-dialog" :visible.sync="visible" width="400px" :show-close="false" @close="handleClose">
      <div class="redeem-courses-main">
        <img class="left-top-pic" src="@/assets/mooc-img/left-top-pic.png" alt="">
        <div class="body">
          <div class="title">{{ info.title }}</div>
          <div class="msg" v-html="info.msg"></div>
          <div class="btn">
            <el-button size='medium' v-if="info.btnText && (status === 1 || status === 2)" :class="{ 'disable': isSubscription }" :disabled="isSubscription" @click="handleConfirm">{{ info.btnText }}</el-button>
          </div>
          <div class="data">
            <p>
              <span class="spot">900积分</span>
              <span class="text">需使用 <b style="color: #0052d9;">{{ purchaseData.course_val || 1 }}</b> 张学霸卡</span>
            </p>
            <!-- <p>
              <span class="text">我的学霸卡：<b style="color: #ed7b2f;">{{ purchaseData.user_account_num || 0 }}</b> 张</span>
            </p> -->
            <div class="pupop-select" v-if="status === 0 || status === 3">
              <el-select @visible-change="visibleChangeSelect" @change="handelrChangeValue" popper-class="popper_select_pupop" class="el-select_pupop" style="width:100%" v-model="currentValue" placeholder="请选择">
                <el-option v-for="item in cardList" :key="item.acct_type_code" :label="item.acct_type_code_name" :value="item.acct_type_code">
                  <div class="popper-content">
                    <div style="color: #000000cc;">学霸卡-{{item.acct_type_code_name}} <span :class="item.isUse ? '' : 'is-use' ">{{item.isUse ? '' : '不可用'}}</span> </div>
                    <div class="num">拥有 <span class="num-b">{{item.consume_point}}</span> 张<span v-if="item.consume_point > 0">，最近过期时间 <span class="num-b">{{timeToDate(item.expire_time)}}</span></span> </div>
                  </div>
                </el-option>
              </el-select>
              <div class="pupop-select-cur">
                <div class="cur-left">
                  兑换方式
                </div>
                <div class="cur-center">
                  <div class="cur-center_title">学霸卡-{{currentItemCard.acct_type_code_name}}<span :class="currentItemCard.isUse ? '' : 'is-use' ">{{currentItemCard.isUse ? '' : '不可用'}}</span></div>
                  <div class="cur-center_num">拥有 <span class="cur-center_num_b">{{currentItemCard.consume_point}}</span> 张<span v-if="currentItemCard.consume_point > 0">，最近过期时间 <span class="cur-center_num_b">{{timeToDate(currentItemCard.expire_time)}}</span></span></div>
                </div>
                <div class="cur-right">
                  <i class="el-icon-arrow-down cur-right_down" :class="isReverse ? 'is-reverse' : ''"></i>
                </div>
              </div>
            </div>
          </div>
          <div class="btn">
            <el-button size='medium' v-if="info.btnText && status === 0 || status === 3" :class="{ 'disable': isExchange }" :disabled="isExchange" @click="handleConfirm">{{ info.btnText }}</el-button>
          </div>
          <div class="link">
            <span class="help-icon"></span>
            <a class="source-detail" target="_blank" :href="sourceDetailLink">想获得更多学霸卡？点此获取攻略</a>
          </div>
        </div>
      </div>
      <img class="close-img" src="@/assets/mooc-img/close-grey.png" alt="" @click="handleClose" />
    </el-dialog>
  </div>
</template>

<script>
// import env from 'config/env.conf.js'
// const envName = env[process.env.NODE_ENV]
import {
  getSubscription,
  insertSubscription,
  moocEnroll,
  getAcctinfosActivity
} from '@/config/mooc.api.conf.js'

export default {
  components: {},
  props: {
    visible: {
      type: Boolean
    },
    courseType: {
      type: String
    },
    purchaseData: {
      type: Object,
      default: () => {
        return {
          course_val: 1,
          course_stock_total: 0,
          user_account_num: 0
        }
      }
    },
    status: {
      type: Number
    },
    consume_appid: {
      type: String,
      default: ''
    }
  },
  computed: {
    info() {
      if (![0, 1, 2, 3].includes(this.status)) {
        return { title: '***', msg: '***', btnText: '' }
      }
      return this.msgData[this.status]
    },
    sourceDetailLink() {
      return this.$parent.outsourceLink.rule_page_link
      // let link =
      //   'https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=37119&jump_from=hdym&project=hdyy&source=jksj'
      // this.courseType === 'geekBang'
      //   ? (link =
      //       'https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=37119&jump_from=hdym&project=hdyy&source=jksj')
      //   : (link =
      //       'https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=38680&from_act_id=38680&share_staff_id=73758&share_staff_name=circlechai')
      // return link
    },
    timeToDate() {
      return (val) => {
        let str = '--'
        if (val) {
          let timeArr = val.split(' ')[0].split('-')
          str = `${timeArr[0]}-${timeArr[1]}-${timeArr[2]}`
        }
        return str
      }
    },
    isExchange() {
      return (
        this.currentItemCard.consume_point === 0 || this.currentItemCard.isUse === false
      )
    }
  },
  data() {
    return {
      // status: 0,
      currentValue: '',
      currentItemCard: {},
      cardList: [],
      msgData: [
        {
          title: '兑换课程',
          msg: '当前课程可任选4个任务试学，你已试学任务数量：0个，如需学习课程所有任务内容，请兑换课程后学习',
          btnText: '确认兑换'
        },
        {
          title: '兑换名额不足',
          msg: '当前课程的可兑换名额为 <span style="color: #E34D59;">0</span>，暂不支持兑换<br/>运营团队将会定期补充兑换库存，敬请关注',
          btnText: '订阅补货通知'
        },
        {
          title: '学霸卡不足',
          msg: '抱歉，你的学霸卡数量不足，无法兑换此课程',
          btnText: '如何获取更多学霸卡？点击查看指引'
        },
        {
          title: '提示：试学额度已用完',
          msg: '当前课程可任选4个任务试学，你已试学任务数量：0个，如需学习课程所有任务内容，请兑换课程后学习',
          btnText: '兑换课程'
        }
      ],
      isSubscription: false,
      isReverse: false
    }
  },
  watch: {
    purchaseData: {
      handler(newV) {
        if (newV) {
          console.log(newV, 'newVnewV')
          const { allow_preview_num, previewed_num } = newV
          let msg = `当前课程可任选 <span style="color: #ED7B2F;">${allow_preview_num}</span> 个任务试学，你已试学任务数量：<span style="color: #ED7B2F;">${previewed_num}</span> 个，<br>如需学习课程所有任务内容，请兑换课程后学习`
          this.msgData[0].msg = msg
          this.msgData[3].msg = msg
        }
      },
      immediate: true
    },
    courseType: {
      handler(newVal) {
        if (newVal) {
          this.getAcctinfosActivity()
        }
      }
    },
    status: {
      handler(newV) {
        if (newV === 1) this.getSubscriptionInfo()
      },
      immediate: true
    }
  },
  created() {},
  mounted() {
    this.getAcctinfosActivity()
  },
  methods: {
    visibleChangeSelect(val) {
      console.log(val)
      this.isReverse = val
    },
    handleConfirm() {
      console.log('this.status: ', this.status)
      switch (this.status) {
        case 0:
          this.handleEnroll()
          break
        case 1:
          if (!this.isSubscription) this.addInsertSubscription()
          break
        case 2:
          window.open(this.sourceDetailLink)
          break
        case 3:
          this.$emit('redeemCourses')
          break
        default:
          break
      }
    },
    // 订阅查询
    getSubscriptionInfo() {
      getSubscription({
        recourse_from: this.purchaseData.course_from_type
      }).then((res) => {
        console.log('订阅查询', res)
        if (res) {
          this.isSubscription = true
          this.msgData[1].btnText = '已订阅补货通知'
        } else {
          this.isSubscription = false
        }
      })
    },
    // 添加订阅
    addInsertSubscription() {
      insertSubscription({
        recourse_from: this.purchaseData.course_from_type
      }).then((res) => {
        console.log('添加订阅：', res)
        this.isSubscription = true
        this.msgData[1].btnText = '已订阅补货通知'
        this.$message.success('订阅成功')
      })
    },
    // 获取卡券信息
    async getAcctinfosActivity() {
      const result = await getAcctinfosActivity()
      console.log(result, '获取卡券信息')
      this.cardList = result.accts
      if (this.cardList.length) {
        this.cardList.forEach((item) => {
          // 自动选中专用卡
          // if (item.acct_type_code === this.consume_appid) {
          //   this.currentValue = item.acct_type_code
          //   this.currentItemCard = item
          // } else if (item.acct_type_code === this.courseType) {
          //   this.currentValue = item.acct_type_code
          //   this.currentItemCard = item
          // }
          // 不是本课程的专用卡不可使用
          if (this.consume_appid) {
            item.isUse = item.acct_type_code === this.consume_appid
          } else {
            item.isUse = item.acct_type_code === this.courseType || item.acct_type_code === 'xuebaCommon'
          }
        })
        this.cardList.sort((a, b) => {
          let flagA = this.consume_appid ? a.acct_type_code === this.consume_appid : a.acct_type_code === this.courseType
          let flagB = this.consume_appid ? b.acct_type_code === this.consume_appid : b.acct_type_code === this.courseType
          let commonA = a.acct_type_code === 'xuebaCommon'
          let commonB = b.acct_type_code === 'xuebaCommon'
          if (flagA) {
            return -1
          }
          if (flagB) {
            return 1
          }
          if (commonA) {
            return -1
          }
          if (commonB) {
            return 1
          }
          if (!a.isUse) {
            return 1
          }
          return 0
        })
        this.cardList.sort((a, b) => {
          let aIscount = a.consume_point > 0
          let bIsCount = b.consume_point > 0
          if (aIscount && !bIsCount) {
            return -1
          }
          if (bIsCount && !aIscount) {
            return 1
          }
          return 0
        })
        this.currentValue = this.cardList[0].acct_type_code
        this.currentItemCard = this.cardList[0]
        console.log(this.courseType, 'aaaaaaaaaaaaaaaa')
        console.log(this.cardList, 'aaaaaaaaaaaaaaaa')
      }
    },
    handelrChangeValue(val) {
      console.log(val, '改变的zhi')
      this.cardList.forEach((item) => {
        if (item.acct_type_code === val) {
          this.currentItemCard = item
        }
      })
    },
    // 报名
    handleEnroll() {
      const loading = this.$loading({
        lock: true,
        text: '课程兑换中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
        customClass: 'loading-mask-geek'
      })
      let acct_type_code = this.consume_appid ? this.consume_appid : (this.currentValue === 'xuebaCommon' ? 'xuebaCommon' : this.courseType)
      const params = {
        mooc_course_id: this.purchaseData.course_id,
        acct_type_code: acct_type_code,
        join_type: '3'
      }
      moocEnroll(params, false)
        .then((res) => {
          loading.close()
          this.$message.success('兑换课程成功')
          this.$emit('handleRegistered')
          this.handleClose()
        })
        .catch((res) => {
          loading.close()
          if (res.code === 'ECONNABORTED') {
            this.$message.error('活动太火爆了，请稍后再试！')
          } else if (res.message) {
            this.$message.error(res.message)
          }
        })
    },
    handleClose() {
      this.$emit('handleClose')
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less">
.redeem-courses-dialog {
  background: transparent;
  box-shadow: none;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
.loading-mask-geek {
  .el-icon-loading {
    font-size: 40px;
    color: #fff;
  }
  .el-loading-text {
    color: #e9e9e9;
  }
}
.popper_select_pupop {
  border-radius: 6px;
  background: #fff;
  box-shadow: 0 6px 30px 5px #0000000d, 0 16px 24px 2px #0000000a,
    0 8px 10px -5px #00000014;
  border: none;
  font-size: 12px;
  .popper__arrow {
    display: none;
  }
  .popper-content {
    font-size: 12px;
  }
  .el-select-dropdown__item {
    height: 52px;
    line-height: 20px;
    margin: 5px 12px;
    padding: 6px 8px;
    border-radius: 4px;
    border: 1px solid #eee;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    // background-color: #fff;
    // border-radius: 4px;
    // border: 1px solid var(---Brand7-Hover, #266FE8);
  }
  .el-select-dropdown__item.selected {
    color: unset;
    border-radius: 4px;
    border: 1px solid var(---Brand7-Hover, #266fe8);
    font-weight: 400;
  }
  .num {
    color: #00000066;
  }
  .num-b {
    color: #0052d9;
  }
}
</style>
<style lang="less" scoped>
.is-use {
  border-radius: 2px;
  background: #fdf6ec;
  padding: 3px 4px;
  color: #ff7548;
  font-family: 'PingFang SC';
  font-size: 12px;
  line-height: 12px;
  margin-left: 8px;
}
.redeem-courses-pupop {
  text-align: center;
  .redeem-courses-main {
    position: relative;
    overflow: hidden;
    border-radius: 24px;
    background: linear-gradient(180deg, #e3f2ff 0%, #e3f2ff 0%, #eaf5ff 35.99%);
    .left-top-pic {
      position: absolute;
      width: 89px;
      left: 4px;
      top: 5px;
      z-index: 1;
    }
    .body::before,
    .body::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #e9f4ff;
    }
    .body::before {
      left: 0;
      transform: translate(-50%, -50%);
    }
    .body::after {
      right: 0;
      transform: translate(50%, -50%);
    }
    .body {
      position: relative;
      border-radius: 26px;
      margin: 34px 24px 24px;
      padding: 20px 15.5px;
      background: #fff;

      .title {
        color: #0052d9;
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        margin-bottom: 16px;
      }
      .msg {
        color: #000000e6;
        text-align: center;
        font-size: 14px;
        line-height: 22px;
        margin-bottom: 16px;
      }
      .btn {
        display: flex;
        justify-content: center;
        .el-button {
          width: 264px;
          height: 36px;
          border-radius: 24px;
          background: linear-gradient(90deg, #488dff 0%, #1766ff 100%);
          color: #ffffff;
          font-size: 14px;
          margin-bottom: 12px;
        }
        .disable {
          background: linear-gradient(90deg, #e0e0e0 0%, #a5a5a7 100%);
        }
      }
      .data {
        margin-bottom: 12px;
        background: #f9fbfc;
        border-radius: 4px;
        padding: 8px;
        p {
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          span {
            line-height: 20px;
            color: #00000099;
            b {
              font-weight: 500;
            }
          }
          .text {
            margin-left: 12px;
          }
        }
        p:last-child {
          margin-top: 4px;
          justify-content: center;
        }
        .spot {
          color: #00000066;
          text-decoration-line: line-through;
        }
        .pupop-select {
          position: relative;
          margin-top: 8px;
          /deep/.el-select_pupop {
            position: relative;
            height: 52px;
            z-index: 200;
            opacity: 0;
            .el-input .el-input__inner {
              height: 52px;
            }
            .el-input--suffix {
              height: 52px;
            }
          }
          .pupop-select-cur {
            position: absolute;
            width: 100%;
            height: 52px;
            border: 1px solid #eee;
            border-radius: 4px;
            top: 0;
            z-index: 100;
            display: flex;
            align-items: center;
            padding: 6px 8px;
            font-size: 12px;
            line-height: 20px;
            font-family: 'PingFang SC';
            .cur-left {
              width: 24px;
              font-size: 12px;
              line-height: 20px;
              color: #00000066;
            }
            .cur-right {
              width: 16px;
              &_down {
                color: #0052d9;
                font-size: 16px;
                font-weight: 600;
                transition: transform 0.3s;
                transform: rotate(0deg);
                cursor: pointer;
              }
              .is-reverse {
                transform: rotate(180deg);
              }
            }
            .cur-center {
              flex: 1;
              text-align: left;
              padding: 0 6px;
              &_title {
                color: #000000cc;
              }
              &_num {
                color: #00000066;
                &_b {
                  color: #0052d9;
                }
              }
            }
          }
        }
      }
      .link {
        line-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        .help-icon {
          background: url('~@/assets/mooc-img/help_circle.png') no-repeat
            center/cover;
          width: 14px;
          height: 14px;
          display: inline-block;
          margin-right: 5px;
        }
        .source-detail {
          font-size: 12px;
          color: #0052d9;
          cursor: pointer;
        }
      }
    }
  }
  .close-img {
    width: 28px;
    margin-top: 16px;
    cursor: pointer;
  }
}
</style>
