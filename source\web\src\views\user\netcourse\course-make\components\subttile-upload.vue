<template>
  <div :class="[{'directly-upload-active': !directlyUpload}, 'subttile-upload-container']">
    <div class="on-upload" v-show="percent > 0">
      <div class="progress">
        <div class="inner">
          <span class="status">{{
            percent === 100 ? '上传完成' : '上传中'
          }}</span>
          <span class="percent" v-show="percent < 100">{{ percent }}%</span>
          <i class="el-icon-check icon" v-show="percent === 100"></i>
        </div>
      </div>
      <el-progress
        :percentage="percent"
        :stroke-width="4"
        color="#0052d9"
        :show-text="false"
      ></el-progress>
    </div>
    <div class="has-upload" v-show="percent === 0 && setInfo.caption_name">
      <div class="ppt-name">
        <span>{{ setInfo.caption_name }}</span>
        <i class="el-icon-check icon"></i>
      </div>
      <div class="ppt-size">
        <span>文件大小: </span>
        <span>{{ fileSize }}</span>
      </div>
      <div class="upload-date">
        <span>上传日期: </span>
        <span>{{ setInfo.date }}</span>
      </div>
      <div class="btn-box">
        <el-button type="text" @click="againUpload">重新上传</el-button>
        <el-button type="text" @click="deleteFile">删除</el-button>
      </div>
    </div> 
  </div>
</template>
<script>
import { getDate } from '@/utils/tools'
export default {
  props: {
    directlyUpload: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      setInfo: {
        caption_id: '',
        caption_name: '',
        caption_size: '',
        date: '',
        fileList: []
      },
      percent: 0
    }
  },
  computed: {
    fileSize() {
      return Math.ceil((this.setInfo.caption_size / 1048576) * 10) / 10 + 'M'
    }
  },
  methods: {
    initData(val) {
      this.setInfo = {
        caption_id: val.caption_id,
        caption_name: val.caption_name,
        caption_size: val.caption_size,
        date: getDate(),
        fileList: []
      }
    },
    onUpload(options) {
      let that = this
      const fileName = options.file.name
      const arr = fileName.split('.')
      const fileType = arr[arr.length - 1]
      if (!['srt', 'vtt'].includes(fileType)) {
        this.$message.error('请选择.srt或.vtt文件')
        return
      }
      const authUrl = location.hostname.endsWith('.woa.com')
        ? process.env.VUE_APP_PORTAL_HOST_WOA
        : process.env.VUE_APP_PORTAL_HOST
      /* eslint-disable*/
      new contentCenter.uploadFile({
        file: options.file,
        type: 3, // 0表示上传图片，1视频 2音频 3文档
        appId: 'QLearningService',
        operateAuthUrl: `${authUrl}/training-portal-common/api/v1/portal/user/common/uploadOperateSignature`,
        onSuccess(res) {
          that.setInfo = {
            caption_id: res[0] && res[0].content_id,
            caption_name: fileName,
            caption_size: options.file.size,
            date: getDate(),
          }
          that.$emit('confirmOnUpload', that.setInfo)
        },
        onError(err) {
          that.$message.error(err)
        },
        onProgress(info) {
          that.percent = parseInt(info.percent * 10000) / 100
          if (that.percent === 100) {
            setTimeout(() => {
              that.percent = 0
            }, 500)
          }
        }
      })
    },
    // 重新上传
    againUpload() {
      this.$emit('againUpload')
    },
    deleteFile() {
      this.setInfo = {
        caption_name: '',
        caption_size: '',
        caption_id: '',
        date: '',
        fileList: [],
      },
      this.$emit('confirmOnUpload', {}) 
    }
  }
}
</script>
<style lang="less" scoped>
.subttile-upload-container {
  font-size: 14px;
  margin-left: 110px;
  margin-bottom: 20px;
  :deep(.upload-box) {
    .el-upload {
      width: 100%;
      height: 100%;
      .el-upload-dragger {
        width: 100%;
        height: 100%;
        line-height: 144px;
        border: unset;
      }
      .split-line {
        margin: 0 8px;
        color: #333;
      }
      .upload-text {
        color: #666;
      }
    }
  }
  .upload-box,
  .on-upload,
  .has-upload {
    height: 144px;
    box-sizing: border-box;
    border-radius: 3px;
    border: 1px dashed #dcdcdc;
  }
  .on-upload {
    text-align: center;
    .progress {
      margin-top: 45px;
      line-height: 22px;
      .inner {
        display: inline-block;
        width: 246px;
        text-align: left;
        .status {
          margin-right: 8px;
          color: #666;
        }
        .icon {
          border-radius: 50%;
          border: unset;
          background-color: #00a870;
          color: #fff;
        }
      }

      .percent {
        color: #0052d9;
      }
    }
    :deep(.el-progress) {
      display: inline-block;
      width: 246px;
      .el-progress-bar {
        .el-progress-bar__outer {
          background-color: #f2f2f2;
        }
      }
    }
  }
  .has-upload {
    padding: 16px;
    .ppt-name {
      margin-bottom: 8px;
      color: #333;
      line-height: 22px;
      .icon {
        font-size: 12px;
        padding: 1px;
        margin-left: 17px;
        border-radius: 50%;
        border: unset;
        background-color: #00a870;
        color: #fff;
      }
    }
    .ppt-size,
    .upload-date {
      line-height: 20px;
      font-size: 12px;
      color: #999;
    }
    .btn-box {
      margin-top: 20px;
      display: flex;
      .el-button {
        margin-right: 6px;
      }
    }
  }
}
.directly-upload-active {
  margin-left: unset;
}
</style>
