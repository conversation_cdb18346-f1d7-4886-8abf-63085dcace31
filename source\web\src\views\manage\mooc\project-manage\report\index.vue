<template>
  <div class="data-count">
    <el-tabs v-model="currentTab">
      <el-tab-pane label="项目概览" name="projectOverview">
        <project-details v-if="currentTab === 'projectOverview'"></project-details>
      </el-tab-pane>
      <el-tab-pane label="任务详情" name="taskDetails">
        <task-details v-if="currentTab === 'taskDetails'"></task-details>
      </el-tab-pane>
      <el-tab-pane label="学员详情" name="studentDetails">
        <student-details v-if="currentTab === 'studentDetails'"></student-details>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import ProjectDetails from './project-details.vue'
import TaskDetails from './task-details.vue'
import studentDetails from './student-details.vue'

export default {
  components: {
    ProjectDetails,
    TaskDetails,
    studentDetails
  },
  data () {
    return {
      currentTab: 'projectOverview'
    }
  }
}
</script>

<style lang="less" scoped>
  .data-count {
    height: 100%;
    background: #fff;
  }
  :deep(.el-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;
    .el-tabs__header {
      padding: 20px 20px 0;
      margin: 0;
      border-bottom: 1px solid #f3f3f3ff;
    }
    .el-tabs__content {
      flex: 1;
      padding: 20px;
      overflow: auto;
    }
  }
</style>
