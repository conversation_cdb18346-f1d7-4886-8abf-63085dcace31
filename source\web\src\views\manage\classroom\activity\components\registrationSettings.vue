<template>
  <div class="registration-settings">
    <el-form :model="form" :rules="rules" ref="form" label-width="164px">

      <!-- 活动形式 -->
      <teachingTypeSettings ref="teachingTypeSettingsRef" :formData="form" :activityData="activityData"></teachingTypeSettings>

      <el-form-item label="申请直播">
        <div class="flex-row">
          <el-radio-group v-model="form.apply_live">
            <el-radio v-for="item in applyForLive" :label="item.value" :key="item.value" :disabled="applyLiveDisabled">{{ item.label }}</el-radio>
          </el-radio-group>

          <div class="tips ml-12">
            <i class="el-icon-warning color-orange ml-12 mr-6"></i>
            <span class="color-gray">如选择申请直播，将在创建活动时，同步创建直播申请单</span>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="活动时间" prop="start_time">
        <div class="flex-row">
          <el-date-picker
            class="w-time"
            v-model="form.start_time"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择活动开始时间"
            @change="handleActivityTimeChange($event, 'start')"
            size="small">
          </el-date-picker>
          <div class="pl-16 pr-16">至</div>
          <el-date-picker
            class="w-time"
            v-model="form.end_time"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择活动结束时间"
            @change="handleActivityTimeChange($event, 'end')"
            size="small">
          </el-date-picker>
        </div>
      </el-form-item>
      <el-form-item label="报名截止时间">
        <el-date-picker
          class="w-big"
          v-model="form.regist_last_date"
          type="datetime"
          :disabled="!form.start_time && !form.end_time"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="pickerOptions"
          placeholder="如果不填写，则默认为活动前1小时">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="注销截止时间">
        <el-date-picker
          class="w-big"
          v-model="form.cancel_last_date"
          type="datetime"
          :disabled="!form.start_time && !form.end_time"
          value-format="yyyy-MM-dd HH:mm:ss"
          :picker-options="pickerOptions"
          placeholder="如果不填写，则默认为活动前1小时">
        </el-date-picker>
      </el-form-item>

      <!-- 报名模式 -->
      <el-form-item label="报名模式">
        <div class="flex-row h-normal">
          <el-radio-group v-model="form.can_registed" class="mr-12" @change="handleCanRegistedChange">
            <el-radio v-for="item in canRegistedTypes" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
          <AudienceSelector v-if="form.can_registed === 0" audience :showTab="['unit', 'group', 'import']" multiple
          v-model="form.target_ids" ref="selector" appCode="qlearning" :env="audienceEnv" importNumber='1000'
          :isShowCount="false" :createStudentID="true" :disabled="false" />
        </div>
      </el-form-item>

      <el-form-item label="所需签到次数">
        <el-input class="w-normal" v-model="form.sign_count" @input="positiveIntegerUpdateFilteredValue('sign_count')" placeholder="请输入签到次数" minlength="1" maxlength="10"></el-input>
      </el-form-item>

      <el-form-item label="通知功能">
        <el-checkbox v-model="form.auto_send_calendar" :true-label="1" :false-label="0">自动发送日程邀约</el-checkbox>
        <el-checkbox v-model="form.auto_join_group" :true-label="1" :false-label="0">创建并自动邀请进入企业微信群</el-checkbox>
      </el-form-item>
      <el-form-item label="将至活动提醒">
        <el-checkbox-group v-model="notifyChannels" @change="notifyChannelsChange">
          <el-checkbox v-for="item in tipsType" :label="item.value" :key="item.value">{{ item.label }}</el-checkbox>
        </el-checkbox-group> 
      </el-form-item>

      <el-form-item label="报名问卷"> 
        <div class="flex-row h-normal">
          <el-checkbox v-model="form.available">报名前需填写问卷</el-checkbox>
          <el-button v-if="!questionTabData.length" class="ml-12" :class="{ 'guanlian': form.available, 'guanlian-disabled': !form.available }" plain size="small" @click="handleRelatedQuestionnaire()" :disabled="!form.available || questionTabData.length > 0">创建并关联问卷</el-button>
          <div class="question flex-row ml-12" v-if="questionTabData.length">
            <span class="label ml-12">已关联问卷：</span>
            <span class="name">{{ questionTabData[0].survey_name }}</span>
            <el-button type="text" size="small" class="ml-12" @click="editQuestion(questionTabData[0])">查看</el-button>
            <el-button type="text" size="small" class="ml-12" :disabled="isOldQuestion(questionTabData[0])" @click="handleDelete(0)">删除</el-button>
          </div>
        </div>
      </el-form-item>
    </el-form>

    <!-- 问卷确认 -->
    <AddQuestionnairConfirmDialog
      :visible.sync="addQuestionnairConfirmDialogShow"
      :questionType="questionType"
      @continueCreateQuestionnaire="continueCreateQuestionnaire"
      v-if="addQuestionnairConfirmDialogShow"
    />

    <!-- 问卷管理 -->
    <questionManageDialog
      v-if="isShowQuestionManage"
      :visible.sync="isShowQuestionManage"
      :sid="selfQuestionData.length > 0 ? selfQuestionData[0].survey.wj_id : ''"
      :type="type"
      @getQuestionData="getQuestionData"
      :questionType="isEmpty ? 'add' : 'edit'"
      :otherOption="otherOption"
      :activityName="activityData.activity_name"
    ></questionManageDialog>

  </div>
</template>

<script>
import AddQuestionnairConfirmDialog from '@/views/manage/mooc/project-manage/task-list/component/add-questionnairConfirmDialog.vue'
import teachingTypeSettings from './teachingTypeSettings.vue'
import { AudienceSelector } from '@tencent/sdc-audience'
import questionManageDialog from '@/views/components/questionManageDialog.vue'
import { createQuestionApi } from '@/config/classroom.api.conf.js'
import { mapState } from 'vuex'
const integerREG = /^[1-9]\d*$/
const allTarget = 2015587
export default {
  name: 'registrationSettings',
  components: {
    AddQuestionnairConfirmDialog,
    AudienceSelector,
    teachingTypeSettings,
    questionManageDialog
  },
  props: {
    activityData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    const validInteger = (rule, value, callback) => {
      if (!value || !integerREG.test(value)) {
        return callback(new Error('请输入1-10的整数'))
      } else {
        callback()
      }
    }

    return {
      audienceEnv: process.env.NODE_ENV,
      teachingTypes: [{ label: '线下授课', value: '1' }, { label: '腾讯会议', value: '2' }],
      cityOptions: [],
      // meetingTypes: [{ label: '系统自动创建', value: 1 }, { label: '关联已有会议', value: 2 }],
      applyForLive: [{ label: '不申请', value: 0 }, { label: '我要申请直播', value: 1 }],
      canRegistedTypes: [{ label: '仅对集团正式员工开放', value: 1 }, { label: '自定义开放范围', value: 0 }],
      tipsType: [{ label: '邮件提醒', value: 'mail' }, { label: 'Tips提醒', value: 'tips' }, { label: '短信提醒', value: 'sms' }],
      form: {
        teaching_type: '1', // 活动形式
        city: '', // 活动形式1： 活动地点 - 城市
        location: '', // 活动形式1： 活动地点 - 详细地址
        meeting_create_type: 1, // 活动形式2： 会议创建方式
        // host_type: 1, // 活动形式2： 会议主持人
        // meeting_rooms: [], // 活动形式2：关联会议室Rooms设备
        refuse_join_type: 1, // 活动形式2：参会人员
        enable_meeting_record: true, // 活动形式2：会议录制
        apply_live: 0, // 申请直播
        auto_send_calendar: 1, // 自动发送日程邀约
        auto_join_group: 0, // 创建并自动邀请进入企业微信群
        notify_channels: 'mail;tips;sms', // 将至活动提醒
        start_time: '', // 活动时间 - 开始时间
        end_time: '', // 活动时间 - 结束时间
        regist_last_date: '', // 报名截止时间
        cancel_last_date: '', // 注销截止时间
        can_registed: 1, // 报名模式
        target_ids: allTarget, // 报名模式 - 自定义开放范围
        sign_count: 1, // 签到次数
        is_limit_student_count: 0, // 人数限制
        max_student_count: 20, // 人数限制 - 报名人数
        allow_waiting_list: 0, // 是否允许等待队列
        available: false, // 是否报名前需填写问卷
        surveys: [], // 课前问卷
        meeting_info: {
          meeting_code: '', // 会议编号
          meeting_creator: '' // 会议创建人
        }
      },
      notifyChannels: ['mail', 'tips', 'sms'], // 活动提醒渠道
      meetingId: 1, // 1:系统自动创建 2:关联已有会议
      rules: {
        teaching_type: [{ required: true, message: '请选择活动形式', trigger: 'blur' }],
        location: [{ required: true, message: '请选择活动地点', trigger: 'blur' }],
        start_time: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
        sign_count: [{ validator: validInteger, trigger: ['blur', 'change'] }],
        allow_waiting_list: [{ required: true, message: '请选择是否开启候补名单', trigger: 'change' }]
      },
      pickerOptions: {},
      cityList: [],
      cityProps: {
        value: 'id',
        label: 'name',
        children: 'children'
      },
      // 报名问卷
      addQuestionnairConfirmDialogShow: false,
      questionType: 'showList',
      questionTabData: [],
      curOperateNode: {},
      isEdit: false,

      questionAddress: {
        development: '//test-learn.woa.com',
        test: '//test-learn.woa.com',
        production: '//learn.woa.com'
      },
      isShowQuestionManage: false,
      isEmpty: true,
      selfQuestionData: [],
      type: 'font',
      otherOption: {
        mode: 'normal',
        customAdd: false,
        customUrl: ''
      }
    }
  },
  watch: {
    activityData: {
      handler(val) {
        if (val) {
          this.isEdit = !!val.activity_id
          if (this.isEdit) {
            this.handleDataRender()
          }
        }
      },
      immediate: true
    },
    questionTabData: {
      handler(newVal) {
        this.isEmpty = newVal.map(item => item.category * 1 === 0).length === 0
      },
      immediate: true
    }
  },
  computed: {
    ...mapState({
      activityInfo: state => state.activity.activityInfo || {}
    }),
    activity_id() {
      return this.$route.query.activity_id || ''
    },
    oldQuestionAddress() {
      return this.questionAddress[process.env.NODE_ENV]
    },
    applyLiveDisabled() {
      const { status } = this.activityInfo
      if (this.activity_id && status !== 4) {
        return true
      }
      return false
    }
  },
  created() {},
  mounted() {
    let that = this
    this.$nextTick(() => {
      this.pickerOptions = {
        disabledDate(time) {
          return time.getTime() > new Date(that.form.start_time).getTime()
        }
      }
    })
  },
  beforeDestroy() { },
  methods: {
    handleQuestionData(data = {}) {
      if (this.questionTabData && this.questionTabData.length) {
        data.survey_id = this.questionTabData[0].survey_id
      }
      this.questionTabData = [data]
    },
    handleCanRegistedChange(val) {
      if (val * 1 === 1) {
        this.form.target_ids = allTarget
      } else {
        this.form.target_ids = ''
      }
    },
    handleDataRender() {
      const { 
        start_time,
        end_time,
        allow_waiting_list,
        city,
        location,
        teaching_type,
        // host_type,
        // meeting_rooms,
        refuse_join_type,
        regist_last_date,
        cancel_last_date,
        can_registed,
        target_ids,
        sign_count,
        is_limit_student_count,
        max_student_count,
        surveys,
        meeting_info,
        apply_live,
        auto_send_calendar,
        auto_join_group,
        notify_channels
      } = this.activityData
      this.form.teaching_type = teaching_type

      this.form.city = city
      this.form.location = location
      // this.form.host_type = host_type * 1
      this.form.refuse_join_type = refuse_join_type
      // this.form.meeting_rooms = meeting_rooms || []
      if (meeting_info) this.form.meeting_info = meeting_info
      this.form.apply_live = apply_live ? 1 : 0
      this.form.auto_send_calendar = auto_send_calendar !== null ? auto_send_calendar : ''
      this.form.auto_join_group = auto_join_group !== null ? auto_join_group : ''
      this.form.notify_channels = notify_channels || ''
      this.notifyChannels = notify_channels ? notify_channels.split(';') : []

      if (start_time && end_time) {
        this.form.start_time = start_time
        this.form.end_time = end_time
      }
      if (regist_last_date) this.form.regist_last_date = regist_last_date
      if (cancel_last_date) this.form.cancel_last_date = cancel_last_date
      
      this.form.sign_count = sign_count
      this.form.is_limit_student_count = is_limit_student_count
      this.form.max_student_count = max_student_count
      this.form.can_registed = can_registed ? 1 : 0
      this.form.target_ids = target_ids
      if (can_registed * 1 === 1) {
        this.form.target_ids = allTarget
      }
      this.form.allow_waiting_list = allow_waiting_list ? 1 : 0
      
      const filteredSurveys = surveys?.filter(v => v.category * 1 === 0) || []
      if (filteredSurveys.length) {
        let questionList = filteredSurveys[0]
        this.form.surveys.push(questionList)
        this.questionTabData.push(questionList)
        this.form.available = questionList.status === 1
      } else {
        this.form.available = false
      }
      console.log(this.form, 'form')
    },
    // 活动时间
    handleActivityTimeChange(val, type) {
      console.log(val, type, 'val, type')
      if (type === 'start') {
        if (val !== null && new Date().getTime() > new Date(val).getTime()) {
          this.$message.error('活动开始时间不能早于当前时间')
          this.$nextTick(() => {
            this.form.start_time = ''
          })
        }
      } else if (type === 'end') {
        if (val !== null && new Date(val).getTime() <= new Date(this.form.start_time).getTime()) {
          this.$message.error('活动结束时间不能早于活动开始时间')
          this.$nextTick(() => {
            this.form.end_time = ''
          })
        }
      }
    },
    notifyChannelsChange(val) {
      this.form.notify_channels = val.join(';')
    },
    // 正整数校验
    positiveIntegerUpdateFilteredValue(type) {
      let val = Number(this.form[type])
      let isEdit = false

      if (isNaN(val) || val < 1) {
        isEdit = true
        val = 1
      }
      if (val !== parseInt(val)) {
        isEdit = true
        val = parseInt(val)
      }

      if (isEdit) this.form[type] = val
    },
    // 关联问卷
    handleRelatedQuestionnaire() {
      if (!this.activityInfo.activity_name) {
        this.$message.error('活动名称不能为空')
        return
      }
      this.addQuestionnairConfirmDialogShow = true
    },
    continueCreateQuestionnaire() {
      this.addQuestionnairConfirmDialogShow = false
      this.createQuestion()
    },
    createQuestion() {
      createQuestionApi({
        survey_name: `${this.activityInfo.activity_name}-课前问卷`,
        survey_desc: '感谢您参与本次活动，我们非常重视每位学员的宝贵意见，期待您的反馈！'
      }).then(res => {
        if (!res) {
          this.$message.error('问卷创建失败, 请稍后再试')
          return
        }
        this.otherOption.customUrl = res += '&mode=qlearningCheckData'
        this.isShowQuestionManage = true
      }).catch(() => {
        this.$message.error('问卷创建失败, 请稍后再试')
      })
    },
    submit ({ isDraft = false } = {}) {
      this.form = {
        ...this.form,
        ...this.$refs.teachingTypeSettingsRef?.form
      }

      let info = {
        isPass: false,
        data: {}
      }

      return new Promise(resolve => {
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (!this.form.regist_last_date) {
              this.form.regist_last_date = this.dateSubOneHour(this.form.start_time, 60 * 60 * 1000)
            }
            if (!this.form.cancel_last_date) {
              this.form.cancel_last_date = this.dateSubOneHour(this.form.start_time, 60 * 60 * 1000)
            }
            if (this.dateSub(this.form.regist_last_date, this.form.start_time) > 0) {
              this.$message.error('报名截止时间不能晚于活动开始时间')
              info.isPass = false
              return
            }
            if (this.dateSub(this.form.regist_last_date, this.form.cancel_last_date) > 0) {
              this.$message.error('报名截止时间不能晚于注销截止时间')
              info.isPass = false
              return
            }
            if (this.dateSub(this.form.cancel_last_date, this.form.start_time) > 0) {
              this.$message.error('注销截止时间不能晚于活动开始时间')
              info.isPass = false
              return
            }

            info.isPass = true
          } else {
            info.isPass = false
          }
          if (this.questionTabData.length) {
            this.questionTabData.forEach(item => {
              item.status = this.form.available ? 1 : 3
            })
          }
          info.data = JSON.parse(JSON.stringify(this.form))
          delete info.data.available
          info.data.surveys = this.questionTabData
          resolve(info)
        })
      })
    },
    isOldQuestion (data) {
      const { wj_id = '', wj_url = '' } = data
      return !wj_id && !wj_url
    },
    editQuestion(data) {
      if (this.isOldQuestion(data)) {
        let id = this.$route.query.activity_id || ''
        window.open(`${this.oldQuestionAddress}/manage/activity/survey?activity_id=${id}`)
        return
      }
      this.selfQuestionData = [{ survey: data }]
      this.isShowQuestionManage = true
    },
    handleDelete(index) {
      this.questionTabData.splice(index, 1)
    },
    // 时间比较
    dateSub(date1, date2) {
      const oDate1 = new Date(this.formateDate(date1))
      const oDate2 = new Date(this.formateDate(date2))
      return oDate1.getTime() - oDate2.getTime()
    },
    dateSubOneHour(date, deduct = 0) {
      let oDate = new Date(this.formateDate(date))
      const newTime = oDate.getTime() - deduct
      let newDate = new Date(newTime)
      return this.dateToStr(newDate)
    },
    formateDate(dateStr) {
      return dateStr.replace(/-/g, '/')
    },
    dateToStr(d) { 
      let yyyy = d.getFullYear().toString()
      let mm = this.paddingZero(d.getMonth() + 1)
      let dd = this.paddingZero(d.getDate())
      let hh = this.paddingZero(d.getHours())
      let min = this.paddingZero(d.getMinutes())
      let ss = this.paddingZero(d.getSeconds())
      return yyyy + '-' + mm + '-' + dd + ' ' + hh + ':' + min + ':' + ss
    },
    paddingZero(v) {
      const val = parseInt(v)
      if (val >= 0 && val < 10) {
        return '0' + val
      }
      return v
    },
    getQuestionData(data) {
      if (this.questionTabData && this.questionTabData.length) {
        data.survey_id = this.questionTabData[0].survey_id
      }
      const { act_id = '', act_name = '', resource_url = '' } = data
      let obj = {
        wj_id: act_id,
        wj_url: resource_url,
        survey_name: act_name,
        survey_id: '', // 新增为空，编辑沿用之前拿到的（给数据库更新用作标识）
        status: 1, // 1 勾选了，3 未勾选
        category: 0
      }
      this.questionTabData = [obj]
    }
  }
}
</script>

<style lang="less" scoped>
  @import '~assets/css/activity-common.less';

  input[aria-hidden=true] {
    display: none !important;
  }
  .registration-settings {
    padding: 32px 12px;
    background-color: #fff;

    .question {
      font-size: 14px;
      .label {
        color: #666;
      }
      .name {
        color: #000;
        font-weight: 700;
        display: block;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }

    .guanlian {
      background: #fff;
      color: #0052D9;
      border-color: #0052D9;
    }

    .guanlian-disabled {
      background: #fff;
      color: #999;
    }

    :deep(.el-select .el-input .el-select__caret) {
      line-height: 32px;
    }
    :deep(.el-input--suffix .el-input__icon) {
      line-height: 32px;
    }
    :deep(.w-time.el-input) {
      width: 240px;
    }
  }

  .flex-row {
    display: flex;
    align-items: center;
  }
  
  .w-inherit {
    width: 650px;
    flex-shrink: 0;
  }
  .w-big {
    width: 526px;
    flex-shrink: 0;
  }
  .w-normal {
    width: 200px;
    flex-shrink: 0;
  }
  .w-time {
    widows: 240px;
  }
  .w-medium {
    width: 314px;
    flex-shrink: 0;
  }
  .w-mini {
    width: 120px;
    flex-shrink: 0;
  }

  .h-normal {
    height: 32px;
  }

  .color-gray {
    color: #00000099;
  }
  .color-orange {
    color: #E37318;
  }
  .color-blue {
    color: #0052d9;
  }
  .color-block {
    color: #000000e6;
  }

  :deep(.sdc-selector) .selector-container .container-inner .tags--small {
    max-width: 94% !important;
  }
</style>
