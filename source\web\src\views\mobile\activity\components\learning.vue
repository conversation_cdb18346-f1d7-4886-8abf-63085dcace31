<template>
  <div class="learning-container">
    <detailCard v-for="(item, index) in learningList" :isMinute="true" :key="index"  :cardData="item" :courseData="courseData" :courseType="courseType" entry="extend"></detailCard>
  </div>
</template>

<script>
import detailCard from '@/views/mobile/videoDetailGray/child/detailCard.vue'
export default {
  components: {
    detailCard
  },
  props: {
    learningList: {
      type: Array,
      default: () => ([])
    },
    courseData: {
      type: Object,
      default: () => ({})
    },
    // 默认网络课
    courseType: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="less" scoped>
.learning-container {
  height: 100%;
  overflow-y: auto;
  padding: 20px 16px;
  background: #fff;
  padding-bottom: 114px;
}
</style>
