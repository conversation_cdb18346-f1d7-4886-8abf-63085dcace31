// moudleid枚举
// 参考资料
// https://iwiki.woa.com/p/4007272147
const moduleTypes = [
  {
    module_name: '网络课',
    module_id: 1
  },
  {
    module_name: '面授课',
    module_id: 2
  },
  {
    module_name: '直播',
    module_id: 3
  },
  {
    module_name: '活动',
    module_id: 4
  },
  {
    module_name: '码客',
    module_id: 5
  },
  {
    module_name: '行家',
    module_id: 6
  },
  {
    module_name: '案例',
    module_id: 7
  },
  {
    module_name: '文章',
    module_id: 8
  },
  // {
  //   module_name: '图文',
  //   module_id: 9
  // },
  {
    module_name: '培养项目',
    module_id: 10
  },
  {
    module_name: '考试',
    module_id: 11
  },
  {
    module_name: '课单',
    module_id: 15
  },
  {
    module_name: '文档',
    module_id: 16
  },
  {
    module_name: 'iwiki',
    module_id: 17
  },
  {
    module_name: 'K吧文章',
    module_id: 20
  },
  {
    module_name: '外链',
    module_id: 99
  }
]

// actType枚举
const actTypes = [
  {
    act_type_name: '面授课',
    act_type: 1,
    module_id: 2,
    langKey: 'Mooc_CourseType_Face'
  },
  {
    act_type_name: '网络课',
    act_type: 2,
    module_id: 1,
    resource_type: 1,
    langKey: 'NetCourse_NetCourse'
  },
  {
    act_type_name: '文档',
    act_type: 10,
    module_id: 16,
    resource_type: 5,
    langKey: 'Mooc_Common_ResourceType_Doc'
  },
  {
    act_type_name: '课单',
    act_type: 15,
    module_id: 15,
    langKey: 'Mooc_CourseType_CourseList'
  },
  {
    act_type_name: '案例',
    act_type: 16,
    module_id: 7,
    langKey: 'Mooc_CourseType_Anli'
  },
  {
    act_type_name: '文章',
    act_type: 18,
    module_id: 8,
    langKey: 'Mooc_CourseType_Article'
  },
  {
    act_type_name: '行家',
    act_type: 19,
    module_id: 6,
    langKey: 'Mooc_CourseType_hangjia'
  },
  {
    act_type_name: '考试',
    act_type: 20,
    module_id: 11,
    resource_type: 6,
    langKey: 'Mooc_Common_ResourceType_Exam'
  },
  {
    act_type_name: '培养项目',
    act_type: 11,
    module_id: 10,
    langKey: 'Mooc_CourseType_Mooc'
  },
  {
    act_type_name: '专区',
    act_type: 13,
    module_id: 12,
    langKey: 'NetCourse_Portal'
  },
  {
    act_type_name: '直播',
    act_type: 5,
    module_id: 3,
    langKey: 'NetCourse_Live'
  },
  {
    act_type_name: '外链',
    act_type: 99,
    module_id: 99,
    resource_type: 9,
    langKey: 'Mooc_Common_ResourceType_ExternalLinks'
  },
  {
    act_type_name: '活动',
    act_type: 4,
    module_id: 4,
    langKey: 'Mooc_CourseType_Activity'
  },
  {
    act_type_name: '图文',
    act_type: 6,
    module_id: 9
  },
  {
    act_type_name: '笔记',
    act_type: 17,
    module_id: 8,
    langKey: 'NetCourse_Note'
  },
  {
    act_type_name: '榜单',
    act_type: 202,
    module_id: -999
  },
  {
    act_type_name: '未知',
    act_type: -1000,
    module_id: -1000
  },
  {
    act_type_name: 'K吧文章',
    act_type: 26,
    langKey: '',
    module_id: 20
  }
]

const resourceTypes = [
  {
    resource_type: 1,
    resource_type_name: '视频'
  },
  {
    resource_type: 2,
    resource_type_name: '音频'
  },
  {
    resource_type: 3,
    resource_type_name: '图片'
  },
  {
    resource_type: 4,
    resource_type_name: '压缩文档'
  }, 
  {
    resource_type: 5,
    resource_type_name: '文档'
  },
  {
    resource_type: 6,
    resource_type_name: '考试'
  },  
  {
    resource_type: 7,
    resource_type_name: '练习'
  },
  {
    resource_type: 8,
    resource_type_name: '文章'
  },
  {
    resource_type: 9,
    resource_type_name: '外链'
  }
]
// 根据module_id获取act_type
function getActTypeByModuleId(moduleId) {
  const activityType = actTypes.find(item => item.module_id === moduleId)
  return activityType ? activityType.act_type : ''
}
export { moduleTypes, actTypes, resourceTypes, getActTypeByModuleId }
