<template>
  <el-dialog 
  width="350px" 
  :visible="visible" 
  title="提示"
  top="0px"
  class="dialog-center tasks-operate-dialog none-border-dialog" 
  :close-on-click-modal="false"
  :show-close="false"
  >
    <div class="err-body">
      <div class="tips"><i class="el-icon-warning"></i>任务列表已被更新，请点击确定刷新页面</div>
      <div class="submit-name">最后更新人：{{ lastSubmitInfo.task_last_submitor_name }}</div>
      <div>最后更新时间：{{ lastSubmitInfo.task_last_submit_time }}</div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="submit" type="primary" size="small">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean
    },
    lastSubmitInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {

    }
  },
  methods: {
    submit() {
      this.$emit('handleRefresh', 'refresh')
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.err-body {
  .tips {
    .el-icon-warning {
      color: rgb(255, 117, 72);
      margin-right: 5px;
    }
    color: #333;
  }
  .submit-name  {
    margin-bottom: 10px;
    margin-top: 10px;
  }
}
</style>
