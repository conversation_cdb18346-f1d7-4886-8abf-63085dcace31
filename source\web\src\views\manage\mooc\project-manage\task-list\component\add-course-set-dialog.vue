<template>
  <div class="add-course-set-dialog">
    <el-dialog width="1000px" :visible="visible" :title="`${taskTypeInfo.title}-任务完成条件设置`" :close-on-click-modal="false" :before-close="cancel">
      <div class="course-body">
        <div class="check-label-warning">
          <i class="el-icon-warning-outline"></i>
          <span>{{ taskTypeInfo.tips }}</span>
        </div>
        <el-table 
        :data="tableData" 
        header-row-class-name="table-header-style"
        row-class-name="table-row-style"
        >
          <el-table-column prop="task_name" :label="`${taskTypeInfo.name}名称`" show-overflow-tooltip></el-table-column>
          <el-table-column prop="resource_type_name" label="类型" show-overflow-tooltip width="160"></el-table-column>
          <el-table-column prop="resource_type" label="时长/字数" width="100">
            <template slot-scope="scope">
              <span>
                {{ ['Doc', 'Article'].includes(scope.row.resource_type) ?  (scope.row.word_num ? `${scope.row.word_num}字` : '-' ): 
                   ['Video', 'Audio', 'Scorm', 'Zip', 'Series'].includes(scope.row.resource_type) ?  (scope.row.duration ? `${scope.row.duration}分钟` : '-') :  '-'
                }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="完成条件" width="450">
            <template slot-scope="scope">
              <div class="finsh-oper-box" v-if="['Video', 'Audio', 'Scorm'].includes(scope.row.resource_type) && taskTypeInfo.type !== 'material'">
                <el-radio-group v-model="scope.row.radio" @change="changeDurtionType($event, scope.$index, scope.row)">
                  <el-radio :label="1">由课程完成条件决定</el-radio>
                  <el-radio :label="2">至少学习</el-radio>
                </el-radio-group>
                <el-input-number 
                :disabled="scope.row.radio === 1" 
                v-model="scope.row.isWordNum"
                controls-position="right" 
                :min="0"
                >
                </el-input-number>
                <span class="time-unit">分钟</span>
              </div>
              <div class="finsh-oper-box" v-else-if="scope.row.resource_type==='Scorm' && taskTypeInfo.type === 'material'">
                <el-radio-group v-model="scope.row.radio" @change="changeDurtionType($event, scope.$index, scope.row)">
                  <el-radio :label="1">由素材完成条件决定</el-radio>
                  <el-radio :label="2">至少学习</el-radio>
                </el-radio-group>
                <el-input-number 
                :disabled="scope.row.radio === 1" 
                v-model="scope.row.isWordNum"
                controls-position="right" 
                :min="0"
                >
                </el-input-number>
                <span class="time-unit">分钟</span>
              </div>

              <!-- 文章 文档 压缩包 -->
              <div v-else>
                <span style="margin-right: 10px">至少学习</span>
                <el-input-number v-model="scope.row.isWordNum" controls-position="right" :min="0"></el-input-number>
                <span class="time-unit">分钟</span>
              </div>
              <div v-if="scope.row.duration && scope.row.isWordNum > scope.row.duration" class="video-durtion-tips">{{`${scope.row.resource_type_name}时长${scope.row.duration}分钟，学习时长必须不大于视频时长`}}</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="cancel">取 消</el-button>
        <el-button size='small' @click="submit" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  components: {

  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    tableData: {
      type: Array
    },
    taskTypeInfo: {
      type: Object
    }
  },
  data() {
    return {
    }
  },
  computed: {
  },
  created() {
  },
  methods: {
    submit() {
      const flag = this.tableData.some((e) => e.duration && e.duration < e.isWordNum)
      if (flag) {
        return
      }
      const list = this.tableData.map((e) => {
        e.radio = ['Article', 'Doc', 'Zip', 'Series'].includes(e.resource_type) ? 2 : e.radio
        e.isWordNum = e.radio === 1 ? null : e.isWordNum
        return {
          ...e,
          finished_condition: {
            type: e.radio.toString(),
            condition: e.isWordNum
          }
        }
      })
      this.$emit('updateTreeList', list)
      this.$emit('update:visible', false)
      this.$message.success('添加课程成功')
    },
    cancel() {
      this.$emit('update:visible', false)
    },
    changeDurtionType(value, index, row) {
      if (value === 1) {
        this.$set(this.tableData, index, { ...row, isWordNum: '' })
      }
    }
  }
}
</script>
<style lang="less" scoped>
.add-course-set-dialog {
  .time-unit {
    color: #00000066;
    margin-left: 12px;
  }
  .finsh-oper-box {
    display: flex;
    align-items: center;
    height: 32px;
    .el-radio-group {
      display: flex;
      height: 32px;
      align-items: center;
      margin-right: 8px;
      .el-radio {
        height: 20px;
        line-height: 20px;
      }
    }
  }
  :deep(.is-controls-right){
    width: 130px;
    height: 32px !important;
    line-height: 32px;
    .el-input-number__decrease,.el-input-number__increase{
      line-height: 16px !important;
    }
  }
  :deep(.el-date-editor .el-input__inner){
    padding-left: 30px;
  }
  .course-body-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .check-label-warning {
    color: #ff7548;
    background-color: rgba(253, 246, 236, 1);
    padding: 10px 16px;
    margin-bottom: 10px;
    display: inline-block;
    display: flex;
    align-items: center;

    i {
      margin-right: 10px;
      font-size: 20px;
    }
  }
  .video-durtion-tips {
    color: red;
    font-size: 10px;
  }
}
</style>
