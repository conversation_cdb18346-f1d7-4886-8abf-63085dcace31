<template>
  <div class="page-box">
    <!-- <div class="top-title">
      <span>活动列表</span>
    </div> -->
    <div class="content-top-wrap">
      <div class="content-top">
        <el-form ref="form" :model="searchData" inline label-width="80px">
          <el-form-item label="活动名称">
            <el-input
              placeholder="请输入活动名称"
              class="w-280 mr-16"
              prefix-icon="el-icon-search"
              v-model="searchData.banner_name"
              size="small"
            />
          </el-form-item>
          <el-form-item label="创建人">
            <el-input
              placeholder="请输入创建人"
              class="w-280 mr-16"
              prefix-icon="el-icon-search"
              v-model="searchData.creator_name"
              size="small"
            />
          </el-form-item>
          <el-form-item label="创建日期">
            <el-date-picker
              v-model="createTime"
              type="daterange"
              value-format="yyyy-MM-dd"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              size="small"
              >
            </el-date-picker>
          </el-form-item>
        </el-form>
        <el-form inline class="j-end">
          <el-form-item>
            <el-button @click="handleReset" size="small">
              <i class="el-icon-refresh"></i><span>重置</span>
            </el-button>
            <el-button type="primary" @click="onSearch(1)" size="small">搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="content-main">
      <div class="table-top">
        <div class="left">
          <el-button type="primary" @click="handleAdd()" size="small">
            <span>添加</span>
          </el-button>
          <el-button :disabled="multipleChoice.length < 1" @click="handleBatchDelete" size="small">
            <span>批量删除</span>
          </el-button>
          <div class="select-num">已选择<span> {{ multipleChoice.length || 0 }} </span>条内容</div>
        </div>
      </div>
      <el-table
        ref="table"
        :data="tableParams.list"
        header-row-class-name="table-header-style"
        row-class-name="table-row-style"
        @sort-change="tableSortChange"
        @selection-change="handleSelectionChange"
      >
      <el-table-column
        type="selection"
        width="55">
      </el-table-column>
        <el-table-column
          v-for="item of tableCols"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :width="item.width ? item.width : 'auto'"
          :sortable="item.sortable ? item.sortable : false"
          style="overflow: hidden"
          show-overflow-tooltip
          align="left"
        >
          <template slot-scope="{ row }">
            <span
              v-if="item.prop === 'banner_name'"
              @click="handleLink(row)"
              class="name"
            >
              {{ row.banner_name }}
            </span>
            <span v-else>{{ row[item.prop] ? row[item.prop] : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="160">
          <template slot-scope="{ row }">
            <el-button type="text" @click="handleConfigEdit(row)">编辑</el-button>
            <el-button type="text" @click="handleDelete(row)" class="color-red" >删除</el-button>
            <el-button type="text" @click="handleSort(row)">排序</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="table-pagination">
        <el-pagination
          :hide-on-single-page="tableParams.list.length === 0"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="tableParams.page_no"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="tableParams.page_size"
          layout="total,  prev, pager, next, sizes, jumper"
          :total="tableParams.total"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 添加/编辑轮播图 -->
    <editEmailConfig ref="editEmailConfigDialogRef" :visible.sync="editEmailConfigDialogVisible" @onSearch="onSearch"></editEmailConfig>
  </div>
</template>
<script>
import editEmailConfig from './editEmailConfig.vue'
import { getBannerListApi, deleteBannerApi, updateOrderNoApi } from '@/config/mooc.api.conf.js'

import pager from 'mixins/pager'
export default {
  mixins: [pager],
  components: {
    editEmailConfig
  },
  data () {
    return {
      tableCols: [
        { label: '序号', prop: 'order_no', sortable: 'custom' },
        { label: '活动名称', prop: 'banner_name' },
        { label: '创建人', prop: 'creator_name' },
        { label: '活动开始时间', prop: 'start_time' },
        { label: '活动结束时间', prop: 'end_time' },
        { label: '创建时间', prop: 'created_at', sortable: 'custom' },
        { label: '修改时间', prop: 'updated_at', sortable: 'custom' }
      ],
      searchData: {
        creator_name: '',
        banner_name: '',
        start_time: '',
        end_time: '',
        order_by: '' // updated_at - 更新时间，created_at  - 创建时间
      },
      createTime: [],
      tableParams: {
        page_no: 1,
        page_size: 10,
        total: 0,
        list: []
      },
      multipleChoice: [],
      editEmailConfigDialogVisible: false
    }
  },
  watch: {
  },
  mounted () {
    this.getList()
  },
  methods: {
    onSearch() {
      this.tableParams.page_no = 1
      this.getList()
    },
    // 重置
    handleReset() {
      this.searchData = {
        creator_name: '',
        banner_name: '',
        start_time: '',
        end_time: '',
        order_by: ''
      }
      this.createTime = []
      this.multipleChoice = []
      this.$refs.table.clearSort()
      this.onSearch()
    },
    async getList () {
      const { page_no, page_size } = this.tableParams
      if (!this.createTime || !this.createTime.length) {
        this.createTime = []
      }
      let params = {
        act_type: this.$route.query.act_type,
        // banner_type: '1', // 邮件
        banner_name: this.searchData.banner_name,
        creator_name: this.searchData.creator_name,
        create_start_time: this.createTime.length ? this.createTime[0] + ' 00:00:00' : '',
        create_end_time: this.createTime.length ? this.createTime[1] + ' 23:59:59' : '',
        order_by: this.searchData.order_by,
        current: page_no,
        size: page_size
      }
      await getBannerListApi(params).then(res => {
        this.tableParams.list = res.records || []
        this.tableParams.total = res.total
      })
    },
    // 排序
    tableSortChange({ prop, order }) {
      let sort = prop
      if (order === 'ascending') {
        sort += ';asc'
      } else if (order === 'descending') {
        sort += ';desc'
      } else {
        sort = ''
      }
      this.searchData.order_by = sort
      this.getList()
    },
    // 批量多选
    handleSelectionChange(val) {
      this.multipleChoice = val
    },
    // 批量删除
    handleBatchDelete() {
      this.handleDelete(this.multipleChoice, true)
    },
    handleDelete (item, batch = false) {
      let ids = ''
      let names = ''
      const h = this.$createElement
      let html = [
        h('span', { style: 'color: #00000099;' }, '请确认是否删除： ')
      ]
      if (batch) {
        item.forEach((v, i) => {
          ids += v.id + ','
          if (i < 6) {
            names += `${v.banner_name}、`
          }
        })
        names = names.substring(0, names.length - 1)
        if (item.length > 6) names += '...'
        ids = ids.substring(0, ids.length - 1)

        html.push(h('span', { style: 'color: #000000e6;' }, names))
        html.push(h('span', { style: 'color: #000000e6; display: inline; margin-left: 4px;' }, '共'))
        html.push(h('i', { style: 'color: #0052d9; font-weight: 500; font: icon; display: inline; margin: 0 4px;' }, item.length))
        html.push(h('span', { style: 'color: #000000e6;' }, '项活动？'))
      } else {
        ids = item.id
        names = item.banner_name
        html.push(h('span', { style: 'color: #000000e6;' }, names))
      }
      let that = this

      this.$msgbox({
        title: '提示',
        closeOnClickModal: false,
        message: h('p', null, html),
        showCancelButton: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '执行中...'
            deleteBannerApi(ids).then((res) => {
              instance.confirmButtonLoading = false
              that.$message.success('删除成功')
              that.getList()
              done()
            })
          } else {
            done()
          }
        }
      })
    },
    handleSort(row) {
      this.$prompt('请输入排序', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputValidator: function(e) {
          if (!e) {
            return '排序不能为空'
          } else if (e * 1 < 1) {
            return '仅支持输入1-10的数字'
          } else {
            let reg = /^[+]{0,1}[0-9](\d*)$/
            if (!reg.test(e)) {
              return '请输入正整数'
            }
            // if (e > 10 || e < 1) {
            //   return '仅支持输入1-10的数字'
            // }
            return true
          }
        },
        inputValue: row.order_no
      }).then(({ value }) => {
        const params = {
          id: row.id,
          order_no: value
        }
        updateOrderNoApi(params).then(res => {
          this.$message.success('修改成功')
          this.getList()
        })
      })
    },
    handleLink (row) {
      try {
        window.open(row.link_url)
      } catch (error) {
        this.$message.error('跳转地址错误')
      }
    },
    handleAdd() {
      this.editEmailConfigDialogVisible = true
      this.$refs.editEmailConfigDialogRef.initData({ moduleName: 'add' })
    },
    handleConfigEdit (row) {
      this.editEmailConfigDialogVisible = true
      this.$refs.editEmailConfigDialogRef.initData({ moduleName: 'edit', ...row })
    },
    handleSizeChange (size) {
      this.tableParams.page_size = size
      this.getList()
    },
    handleCurrentChange (current) {
      this.tableParams.page_no = current
      this.getList()
    }
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/graphic-manage.less';
.page-box {
  .content-top-wrap {
    margin-bottom: 10px;
    padding: 0 20px;
  }
  .content-top {
    border-radius: 4px;
    background: #FAFAFA;
    padding: 20px 20px 0 20px;
    display: flex;
    flex-direction: column;
    .j-end {
      display: flex;
      justify-content: flex-end;
      & /deep/ .el-button {
        min-width: 80px;
      }
    }
    .el-form /deep/ .el-form-item {
      margin-bottom: 16px;
    }
    .el-form--inline /deep/ .el-form-item__label {
      color: #00000099;
    }
  }
  .content-main {
    padding: 0 20px;
    .table-top {
      padding: 10px 10px 10px 0;
    }
    & /deep/ .el-table {
      border: 1px solid #eee;
      .table-header-style th {
        padding: 6px 0;
        color: #00000099;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 14px;
      }
    }
    /deep/ .el-button.el-button--text {
      padding: 0;
    }
    /deep/ .el-button--text:hover, /deep/ .el-button--text:focus {
      color: #0052D9 !important;
    }
  }
}
.table-top {
  display: flex;
  justify-content: space-between;
  .left {
    display: flex;
    text-align: center;
    .select-num {
      margin-left: 16px;
      color: #666666;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      span {
        color: #0052d9;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
      }
    }
  }
}
.name:hover {
  color: #0052d9;
  cursor: pointer;
}
.el-button--text:hover {
  background: unset !important;
}
.el-date-editor /deep/ .el-range-separator {
  min-width: 26px;
}
.color-red {
  color: #e34d59 !important;
}
</style>
