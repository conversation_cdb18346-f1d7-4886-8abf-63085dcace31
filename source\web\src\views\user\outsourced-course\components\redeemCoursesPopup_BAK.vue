<template>
  <div class="redeem-courses-pupop">
    <el-dialog custom-class="redeem-courses-dialog" :visible.sync="visible" width="392px" :show-close="false" @close="handleClose">
      <div class="redeem-courses-main">
        <div class="head">
          <img class="bg" src="@/assets/mooc-img/header-bg.png" />
          <img class="close" @click="handleClose" src="@/assets/mooc-img/close-grey.png" />
        </div>
        <div class="body">
          <div class="title">{{ info.title }}</div>
          <div class="msg" v-html="info.msg"></div>
          <div class="btn">
            <el-button size='medium' v-if="info.btnText" :class="{ 'disable': isSubscription }" :disabled="isSubscription" @click="handleConfirm">{{ info.btnText }}</el-button>
          </div>
          <div class="data">
            <p>
              <span class="spot">900积分</span>
              <span class="text">需使用 <b class="important">{{ purchaseData.course_val || 1 }}</b> 张学霸卡</span>
              <span class="text">剩余名额：<b class="important">{{ purchaseData.course_stock_total || 0 }}</b></span>
            </p>
            <p>
              <span class="text">我的学霸卡：<b class="important">{{ purchaseData.user_account_num || 0 }}</b>张</span>
            </p>
          </div>
          <div class="link">
            <span class="help-icon"></span>
            <a class="source-detail" target="_blank" :href="sourceDetailLink">想获得更多学霸卡？点此获取攻略</a>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// import env from 'config/env.conf.js'
// const envName = env[process.env.NODE_ENV]
import { 
  getSubscription,
  insertSubscription,
  moocEnroll
} from '@/config/mooc.api.conf.js'

export default {
  components: {
  },
  props: {
    visible: {
      type: Boolean
    },
    purchaseData: {
      type: Object,
      default: () => {
        return {
          course_val: 1,
          course_stock_total: 0,
          user_account_num: 0
        }
      }
    },
    status: {
      type: Number
    }
  },
  computed: {
    info() {
      if (![0, 1, 2, 3].includes(this.status)) {
        return { title: '***', msg: '***', btnText: '' }
      }
      return this.msgData[this.status]
    },
    sourceDetailLink() {
      let link = 'https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=37119&jump_from=hdym&project=hdyy&source=jksj'
      this.courseType === 'geekBang' ? link = 'https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=37119&jump_from=hdym&project=hdyy&source=jksj' : link = 'https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=38680&from_act_id=38680&share_staff_id=73758&share_staff_name=circlechai'
      return link
    }
  },
  data() {
    return {
      msgData: [
        { title: '兑换课程', msg: '当前课程可任选4个章节试学，你已试学章节数量：0个，如需学习课程所有章节内容，请兑换课程后学习', btnText: '确认兑换' },
        { title: '兑换名额不足', msg: '当前课程的可兑换名额为 <span style="color: #E34D59;">0</span>，暂不支持兑换<br/>运营团队将会定期补充兑换库存，敬请关注', btnText: '订阅补货通知' },
        { title: '学霸卡不足', msg: '抱歉，你的学霸卡数量不足，无法兑换此课程', btnText: '如何赢取更多学霸卡？点击查看指引' },
        { title: '提示：试学额度已用完', msg: '当前课程可任选4个章节试学，你已试学章节数量：0个，如需学习课程所有章节内容，请兑换课程后学习', btnText: '兑换课程' }
      ],
      isSubscription: false
    }
  },
  watch: {
    purchaseData: {
      handler(newV) {
        if (newV) {
          const { allow_preview_num, previewed_num } = newV
          let msg = `当前课程可任选 <span style="color: #ED7B2F;">${allow_preview_num}</span> 个章节试学，你已试学章节数量：<span style="color: #ED7B2F;">${previewed_num}</span> 个，<br>如需学习课程所有章节内容，请兑换课程后学习`
          this.msgData[0].msg = msg
          this.msgData[3].msg = msg
        }
      },
      immediate: true
    },
    status: {
      handler(newV) {
        if (newV === 1) this.getSubscriptionInfo()
      },
      immediate: true
    }
  },
  created() {
  },
  methods: {
    handleConfirm() {
      switch (this.status) {
        case 0:
          this.handleEnroll()
          break
        case 1:
          if (!this.isSubscription) this.addInsertSubscription()
          break
        case 2:
          window.open(this.sourceDetailLink)
          break
        case 3:
          this.$emit('redeemCourses')
          break
        default:
          break
      }
    },
    // 订阅查询
    getSubscriptionInfo() {
      getSubscription({ recourse_from: this.purchaseData.course_from_type }).then(res => {
        console.log('订阅查询', res)
        if (res) {
          this.isSubscription = true
          this.msgData[1].btnText = '已订阅补货通知'
        } else {
          this.isSubscription = false
        }
      })
    },
    // 添加订阅
    addInsertSubscription() {
      insertSubscription({ recourse_from: this.purchaseData.course_from_type }).then(res => {
        console.log('添加订阅：', res)
        this.isSubscription = true
        this.msgData[1].btnText = '已订阅补货通知'
        this.$message.success('订阅成功')
      })
    },
    // 报名
    handleEnroll() {
      const loading = this.$loading({
        lock: true,
        text: '课程兑换中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
        customClass: 'loading-mask-geek'
      })
      const params = {
        mooc_course_id: this.purchaseData.course_id,
        join_type: '3'
      }
      moocEnroll(params, false).then((res) => {
        loading.close()
        this.$message.success('兑换课程成功')
        this.$emit('handleRegistered')
        this.handleClose()
      }).catch(res => {
        loading.close()
        if (res.code === 'ECONNABORTED') {
          this.$message.error('活动太火爆了，请稍后再试！')
        } else if (res.message) {
          this.$message.error(res.message)
        }
      })
    },
    handleClose() {
      this.$emit('handleClose')
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less">
.redeem-courses-dialog {
  background: transparent;
  box-shadow: none;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
.loading-mask-geek {
  .el-icon-loading {
    font-size: 40px;
    color: #fff;
  }
  .el-loading-text {
    color: #e9e9e9;
  }
}
</style>
<style lang="less" scoped>
.redeem-courses-pupop {
  .redeem-courses-main {
    .head {
      width: 100%;
      height: 112px;
      position: relative;
      .bg {
        width: 100%;
        height: 100%;
      }
      .close {
        width: 28px;
        height: 28px;
        position: absolute;
        top: 40px;
        right: 20px;
        cursor: pointer;
      }
    }
    .body {
      padding: 16px 32px 28px;
      background: #fff;
      border-radius: 0 0 12px 12px;
      .title {
        color: #0052d9;
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        margin-bottom: 8px;
      }
      .msg {
        color: #000000e6;
        text-align: center;
        font-size: 16px;
        line-height: 24px;
        margin-bottom: 16px;
      }
      .btn {
        display: flex;
        justify-content: center;
        .el-button {
          width: 304px;
          height: 48px;
          border-radius: 24px;
          background: linear-gradient(90deg, #488DFF 0%, #1766FF 100%);
          color: #ffffff;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 16px;
        }
        .disable {
          background: linear-gradient(90deg, #e0e0e0 0%, #a5a5a7 100%);
        }
      }
      .data {
        margin-bottom: 16px;
        p {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 14px;
          font-size: 14px;
          span {
            line-height: 22px;
            color: #0052d9;
            b {
              font-weight: 500;
            }
          }
        }
        p:last-child {
          margin-top: 8px;
          justify-content: center;
        }
        .spot {
          color: #00000066;
          text-decoration-line: line-through;
        }
      }
      .link {
        margin-top: 8px;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        justify-content: center;
        .help-icon {
          background: url('~@/assets/mooc-img/help_circle.png') no-repeat center/cover;
          width: 16px;
          height: 16px;
          display: inline-block;
          margin-right: 4px;
        }
        .source-detail {
          color: #0052d9;
          text-decoration-line: underline;
          cursor: pointer;
        }
      }
    }
  }
  .important {
    color: #ED7B2F !important;
  }
}
</style>
