<template>
  <div class="study-means-container">
    <div class="top-box" v-if="total">
      <span>{{ $langue('Mooc_ProjectDetail_Documents_TotalFiles', { count: total, defaultText: `共 ${total} 个文件` }) }}</span>
      <!-- <el-button type="text" @click="downAll">下载全部<i class="el-icon-download"></i></el-button> -->
    </div>
    <div class="down-list clearfix">
      <div class="left-content" v-for="item in resourceList" :key="item.id">
        <img :src="resourceImg(item.resource_type, item.resource_name)" alt="">
        <div class="right-box">
          <div class="right-top">
            <span class="study-title">{{ item.resource_name }}</span>
            <p>
              <span :class="[item.resource_type === 'Zip' ? 'disable-view' : '', 'btn-span']" @click="preview(item)">{{ $langue('Mooc_ProjectDetail_Documents_View', { defaultText: '查看' }) }}</span>
              <span :class="[!item.allow_download ? 'disable-view' : '', 'btn-span down-btn']" @click="download(item)">{{ $langue('Mooc_ProjectDetail_Documents_DownLoad', { defaultText: '下载' }) }}</span>
            </p>
          </div>
          <div class="right-botttom">
            <span class="size">{{ item.file_size }}</span>
            <span class="time">{{ item.created_at }}</span>
          </div>
        </div>
      </div>
      <div class="bottom-text" v-if="resourceList.length == 0">
        <img class="empty-img" :src="empty" alt="" />
        <div class="empty-text">{{ $langue('Mooc_ProjectDetail_Notice_NoData', { defaultText: '暂无内容' }) }}</div>
      </div>
    </div>
    <!-- 分页 -->
    <el-pagination v-if="total" class="down-pagination" @current-change="handleCurrentChange" :current-page.sync="curPage"
      layout="total, prev, pager, next" :total="total" :page-size="pageSize">
    </el-pagination>
    <!-- 弹窗 -->
    <mediaDialog ref="mediaDialog"></mediaDialog>
  </div>
</template>
<script>
import {
  getStudyDataList,
  sourceDownload,
  urlForDownloadApi,
  // sourceAllDownload,
  batchDownload
} from '@/config/mooc.api.conf.js'
import mediaDialog from '@/views/components/media-dialog'
export default {
  components: {
    mediaDialog
  },
  data() {
    return {
      resourceList: [],
      resourceAllList: [],
      total: 0,
      empty: require('@/assets/img/empty.png'),
      curPage: 1,
      pageSize: 8
    }
  },
  computed: {
    resourceImg() {
      let imgName = ''
      return (type, name) => {
        switch (type) {
          case 'Video':
            imgName = 'icon-video'
            break
          case 'Audio':
            imgName = 'icon-mp3'
            break
          case 'Image':
            imgName = 'icon-jpg'
            break
          case 'Zip':
            imgName = 'icon-zip'
            break
          case 'Doc':
            const name_subtr = name.lastIndexOf('.') && name.substr(name.lastIndexOf('.') + 1)
            switch (name_subtr) {
              case 'doc': case 'docx':
                imgName = 'icon-word'
                break
              case 'ppt': case 'pptx':
                imgName = 'icon-ppt'
                break
              case 'pdf':
                imgName = 'icon-pdf'
                break
              case 'xls': case 'xlsx':
                imgName = `icon-excel`
            }
            break
        }
        console.log(type, name, imgName)
        return require(`@/assets/mooc-img/${imgName}.png`)
      }
    }
  },
  mounted() {
    this.getData()
  },
  methods: {
    getData() {
      const { mooc_course_id } = this.$route.query
      getStudyDataList({ mooc_course_id, pages: 1, page_size: 10 }).then((res) => {
        this.resourceAllList = res.records.map((v) => {
          return {
            ...v,
            file_size: Math.ceil((v.file_size / 1048576) * 10) / 10 + 'M' // 保留1位小数
          }
        })
        this.resourceList = this.resourceAllList.slice(0, this.pageSize)
        this.total = res.total
      })
    },
    // 下载全部
    downAll() {
      const list = this.resourceAllList.map((item) => {
        return {
          content_id: item.content_id,
          file_name: item.resource_name
        }
      })
      batchDownload({ compress_name: '打包文件', contents: list }).then((res) => {
        console.log(res, 'res')
      })
      // sourceAllDownload(mooc_course_id).then(res => {
      //   console.log(res)
      // })
    },
    // 预览
    preview(item) {
      if (item.resource_type === 'Zip') return
      this.$nextTick(() => {
        this.$refs.mediaDialog.handleViewFile(item)
      })
    },
    download(item) {
      this.urlForDownload(item)
    },
    // 获取下载文件源地址
    urlForDownload(obj) {
      if (!obj.allow_download) return
      urlForDownloadApi(obj.content_id).then(res => {
        sourceDownload({ content_id: obj.content_id, act_id: obj.mooc_course_id })
        this.getBlob(res).then(bolb => {
          this.saveAs(bolb, obj.resource_name)
        })
      })
    },
    // 获取 blob 格式文件
    getBlob(url) {
      return new Promise(resolve => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', url, true)
        xhr.responseType = 'blob'
        xhr.onload = () => {
          if (xhr.status === 200) {
            resolve(xhr.response)
          }
        }
        xhr.send()
      })
    },
    // 文件下载
    saveAs(blob, filename) {
      let link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      link.click()
    },
    handleCurrentChange(val) {
      this.curPage = val
      this.showPage(val)
    },
    showPage(page) {
      const startIndex = (page - 1) * this.pageSize // 计算起点
      const endIndex = startIndex + this.pageSize // 计算终点
      this.resourceList = this.resourceAllList.slice(startIndex, endIndex) // 截取需要展示的数据
    }
  }
}
</script>
<style lang="less" scoped>
.study-means-container {
  font-size: 14px;

  .top-box {
    padding: 20px 36px 0px;
    color: #000000e6;

    // height: 22px;
    // line-height: 22px;
    .el-button {
      margin-left: 20px;
    }
  }

  .down-list {
    // display: flex;
    // align-items: center;
    // justify-content: space-between;

    padding: 16px 20px;
    width: 886px;

    // .time {
    //   text-align: right;
    // }
    // .left-box-text {
    //   width: 200px;
    //   height: 200px;
    //   background-color: red;
    //   float: left;
    //   margin-right: 20px;
    // }
    .btn-span {
      color: #0052D9;
      cursor: pointer;
    }

    .btn-span:hover {
      color: #5d83e6;
    }

    .down-btn {
      margin-left: 12px;
    }

    .disable-view {
      color: #00000042;
    }

    .disable-view:hover {
      color: #00000042;
      cursor: not-allowed;
    }

    .left-content {
      width: 415px;
      position: relative;
      float: left;
      display: flex;
      align-items: center;
      // margin-right: 16px;
      padding: 16px 20px;
      margin-bottom: 16px;

      img {
        width: 48px;
        height: 48px;
      }

      .right-box {
        padding-left: 8px;
        flex: 1;
        width: 88%;

        .right-top,
        .right-botttom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 22px;
          line-height: 22px;

          .study-title {
            display: inline-block;
            width: 75%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }

        .right-botttom {
          height: 20px;
          line-height: 20px;

          .size,
          .time {
            font-size: 12px;
            color: #00000066;
            ;
          }
        }
      }
    }

    .last-left-content {
      margin-right: unset;
    }
  }

  .down-pagination {
    padding: 0 24px;
  }

  .bottom-text {
    padding-top: 20px;
    color: #999;
    text-align: center;

    .empty-img {
      margin-bottom: 20px;
      width: 178px;
      height: 130px;
    }
  }
}</style>
