<template>
  <div class="msgSetting-page">
    <p class="page-title">
      <span class="span-title">消息模板配置</span>
    </p>
    <!-- <div class="warning-box pub-flex" style="margin-left:20px">
      <img class="warning-icon" :src="warningIcon" alt="">
      <span>当前邮件均以xxx的个人邮箱发出，如需更改请联系graywu</span>
    </div> -->
    <div class="top-template pub-flex">
      <div class="card-box pub-m-r-16">
        <div class="card-box-title">加入项目提醒</div>
        <div class="row-box pub-row-between" v-for="item in addItemReminder" :key="item.id">
          <span class="row-left">{{ item.label }}</span>
          <div class="row-right pub-flex">
            <span :class="{'disabled-check': isApprove}" @click="handleCheckTemplate(item)">{{ item.type === 'hrAssistant' ? '查看模板' : '配置模板'}}</span>
            <el-tooltip :disabled="disabledMessage(item).able" effect="dark" :content="disabledMessage(item).content" placement="top">
              <span>
                <el-link class="publish-test" type="primary" :underline="false" :disabled="!disabledMessage(item).able || isApprove" @click="handleSendTest(item)">发送测试
                </el-link>
              </span>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="card-box pub-m-r-16">
        <div class="card-box-title">学习催办</div>
        <div class="row-box pub-row-between" v-for="item in messageTypeList" :key="item.id">
          <span class="row-left">{{ item.label }}</span>
          <div class="row-right pub-flex">
            <span :class="{'disabled-check': isApprove}" @click="handleCheckTemplate(item)">{{
          item.type === 'hrAssistant' ? '查看模板' : '配置模板'
        }}</span>
            <el-tooltip v-if="item.type !== 'myoa'" :disabled="disabledMessage(item).able" effect="dark"
              :content="disabledMessage(item).content" placement="top">
              <span><el-link class="publish-test" type="primary" :underline="false"
                  :disabled="!disabledMessage(item).able || isApprove" @click="handleSendTest(item)">发送测试</el-link></span>
            </el-tooltip>
            <span v-else style="width: 56px; cursor: unset"></span>
          </div>
        </div>
      </div>
      <div class="card-box">
        <div class="card-box-title">催办同步提醒邮件</div>
        <div class="row-box pub-row-between" v-for="item in syncTipsMail" :key="item.id">
          <span class="row-left">{{ item.label }}</span>
          <div class="row-right pub-flex">
            <span :class="{'disabled-check': isApprove}" @click="handleSyncTemplate(item)">配置模板</span>
            <el-tooltip :disabled="disabledMessage(item).able" effect="dark" :content="disabledMessage(item).content" placement="top">
              <span><el-link class="publish-test" type="primary" :underline="false" :disabled="!disabledMessage(item).able || isApprove"
                  @click="handleSendTest(item)">发送测试</el-link></span>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>

    <p class="page-title">
      <span class="span-title">自动催办设置</span>
    </p>
    <div class="automatic-eminder-setting">
      <div class="warning-box pub-flex">
        <img class="warning-icon" :src="warningIcon" alt="" />
        <span>该项目处于“进行中”状态时，将根据催办设置进行自动催办。自动催办功能仅支持超级管理员操作，如有需要请联系graywu。</span>
      </div>

      <el-form :model="form" ref="form" :rules="rules">
        <el-form-item label="自动催办 :">
          <div class="pub-flex pub-col-center">
            <el-switch v-model="form.enabled_remaind"
              :disabled="!userLimitInfo.supper_admin">
            </el-switch>
            <div class="automatic-reminder-switch">
              开启后，将在设定的时间针对<span class="color-red">“未开始”</span>和<span class="color-red">“进行中”</span>状态的学员自动发送催办消息
            </div>
          </div>
        </el-form-item>

        <template v-if="form.enabled_remaind">
          <el-form-item label="催办渠道 :" prop="message_type">
            <el-checkbox-group v-model="form.message_type" :disabled="!userLimitInfo.supper_admin">
              <el-checkbox :label="item.type" :disabled="!disabledMessage(item, 'remind').able"
                v-for="item in messageTypeList" :key="item.id" @change="handleRemaind($event, item.type)">
                <el-tooltip effect="dark" :disabled="disabledMessage(item, 'remind').able" :content="disabledMessage(item, 'remind').content"
                  placement="top">
                  <span>{{ item.label }}</span>
                </el-tooltip>
              </el-checkbox>
            </el-checkbox-group>
            <span class="urge-tips">企微Tips和机器人2选1，最多可选3个催办渠道</span>
          </el-form-item>

          <el-form-item label="催办范围 :" prop="join_type">
            <el-checkbox-group v-model="form.join_type" :disabled="!userLimitInfo.supper_admin">
              <el-checkbox v-for="item in scopeList" :label="item.val" :key="item.val">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="催办时间 :" required>
            <div class="reminder-time">
              <div class="cycle-time">
                <el-checkbox v-model="checked1" @change="checkBoxChange($event, 1)"
                  :disabled="!userLimitInfo.supper_admin">按周期时间催办</el-checkbox>
                <div class="cycle-time-content" v-if="checked1">
                  <el-form-item label="起止催办时间 :">
                    <el-date-picker v-model="form.date" size="small" type="daterange" format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" range-separator="至"
                      start-placeholder="开始日期" end-placeholder="结束日期" :disabled="!userLimitInfo.supper_admin">
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item label="催办频率 :">
                    <div class="pub-m-l-76">
                      <div class="row-box pub-m-b-16">
                        <el-radio v-model="frequency" label="1" @change="frequencyChange($event, 1)"
                          :disabled="!userLimitInfo.supper_admin">每隔</el-radio>
                        <el-input-number v-model="form.reminder_gap_time" class="width-88" size="small"
                          controls-position="right" :min="frequency === '1' ? 1 : 0" :max="9999"
                          :disabled="!userLimitInfo.supper_admin"></el-input-number>
                        <span class="tips pub-m-l-12 pub-m-r-12">天</span>
                        <el-time-picker v-model="form.time_1" class="width-120" size="small" :picker-options="{
          selectableRange: '9:00:00 - 23:59:59'
        }" value-format="HH:mm:ss" placeholder="任意时间点" :disabled="!userLimitInfo.supper_admin">
                        </el-time-picker>
                        <span class="tips pub-m-l-12">进行催办</span>
                      </div>
                      <div class="row-box">
                        <el-radio v-model="frequency" label="2" @change="frequencyChange($event, 2)"
                          :disabled="!userLimitInfo.supper_admin">每周</el-radio>
                        <el-select v-model="form.reminder_fixed_week_time" class="width-114 pub-m-r-12" size="small"
                          placeholder="请选择" :disabled="!userLimitInfo.supper_admin">
                          <el-option v-for="item in weekList" :key="item.val" :label="item.label" :value="item.val">
                          </el-option>
                        </el-select>
                        <el-time-picker v-model="form.time_2" size="small" class="width-120" :picker-options="{
          selectableRange: '9:00:00 - 23:59:59'
        }" value-format="HH:mm:ss" placeholder="任意时间点" :disabled="!userLimitInfo.supper_admin">
                        </el-time-picker>
                        <span class="tips pub-m-l-12">进行催办</span>
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>
              <div class="fixed-time">
                <el-checkbox v-model="checked2" @change="checkBoxChange($event, 2)"
                  :disabled="!userLimitInfo.supper_admin">按固定时间催办</el-checkbox>
                <div class="fixed-time-content" v-if="checked2">
                  <el-row class="pub-flex pub-m-b-12" v-for="(item, index) in addWeekList" :key="item.id">
                    <el-date-picker v-model="item.value" size="small" format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="选择日期时间"
                      :disabled="!userLimitInfo.supper_admin">
                    </el-date-picker>
                    <div class="btn-box add-btn pub-row-center pub-col-center"
                      v-if="index === addWeekList.length - 1 && userLimitInfo.supper_admin" @click="addWeekItem"></div>
                    <div class="btn-box remove-btn pub-row-center pub-col-center"
                      v-if="addWeekList.length > 1 && userLimitInfo.supper_admin" @click="removeWeekItem(item.id)">
                    </div>
                  </el-row>
                </div>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="同步提醒 ：" class="records-sync-box">
            <el-switch v-model="form.enabled_reminder_leader"
              :disabled="!userLimitInfo.supper_admin"></el-switch>
            <span class="records-sync-tips">开启后，将在设定的催办时间点给直接上级/组织BP发送未完成学员的汇总信息邮件提醒</span>
            <div class="tips-person-box" v-if="form.enabled_reminder_leader">
              <span class="tips-label">提醒对象</span>
              <el-checkbox-group v-model="reminder_leader_type" @change="handleLeader"
                :disabled="!userLimitInfo.supper_admin">
                <el-checkbox label="leader" :disabled="!projectManageInfo.enable_copy_leader">
                  <el-tooltip effect="dark" :disabled="projectManageInfo.enable_copy_leader"
                    content="请先联系超级管理员启用催办抄送直接上级功能" placement="top">
                    <span>
                      学员直接上级
                    </span>
                  </el-tooltip>
                </el-checkbox>
                <el-checkbox label="deptBp" :disabled="!projectManageInfo.enable_copy_bp">
                  <el-tooltip effect="dark" :disabled="projectManageInfo.enable_copy_bp" content="请先联系超级管理员启用催办抄送HRBP功能"
                    placement="top">
                    <span>学员所属组织BP</span>
                  </el-tooltip>
                </el-checkbox>
              </el-checkbox-group>
              <el-checkbox v-if="reminder_leader_type.includes('leader')" class="l4-check"
                :disabled="!userLimitInfo.supper_admin"
                v-model="form.shield_l4">提醒邮件屏蔽管理职级为L4及以上的直接上级</el-checkbox>
            </div>
          </el-form-item>
        </template>
      </el-form>
      <bottomFiexd class="msg-bottom-fixed" cancelText="" @save="handleSave" submitText="保存催办设置"
        :disabledBtn="!userLimitInfo.supper_admin || isApprove"></bottomFiexd>
    </div>
    <!-- 消息模板 -->
    <commonInfoTemplate :visible.sync="commonInfoTemplateDialog" ref="commonInfoTemplateRef"
      :templateType="templateType" :previewType="previewType"></commonInfoTemplate>
    <!-- 发送测试 -->
    <sendTestMsg :visible.sync="sendTestDialog" ref="sendTestRef"></sendTestMsg>
  </div>
</template>
<script>
import {
  infoTemplate,
  getStudentExpedite,
  studentExpedite,
  superiorUrgeTemplate
} from '@/config/mooc.api.conf'
import commonInfoTemplate from '@/views/manage/mooc/components/commonInfoTemplate.vue'
import sendTestMsg from './component/send-test-msg.vue'
import bottomFiexd from '@/views/components/botttomFixed.vue'
import { mapState } from 'vuex'
export default {
  components: {
    commonInfoTemplate,
    sendTestMsg,
    bottomFiexd
  },
  data() {
    return {
      // 消息模板配置
      addItemReminder: [
        { id: '1', label: '邮件', moduleName: 'notify', type: 'mail' },
        { id: '2', label: '企微Tips', moduleName: 'notify', type: 'tips' },
        { id: '3', label: '企微机器人', moduleName: 'notify', type: 'bot', content: '请先联系超级管理员启用企微机器人提醒功能' },
        {
          id: '4',
          label: '微信HR助手',
          moduleName: 'notify',
          type: 'hrAssistant',
          previewType: 'remindWxAssistant',
          content: '请先联系超级管理员启用HR助手提醒功能'
        }
      ],
      syncTipsMail: [
        { id: '1', label: '直接上级', moduleName: 'remind', type: 'leader', content: '请先联系超级管理员启用直接上级催办功能' },
        {
          id: '2',
          label: '组织BP',
          moduleName: 'remind',
          type: 'deptBp',
          content: '请先联系超级管理员启用催办抄送HRBP功能'
        }
      ],
      messageTypeList: [
        { id: '1', label: '邮件', type: 'mail', moduleName: 'remind' },
        {
          id: '2',
          label: '企微Tips',
          type: 'tips',
          moduleName: 'remind',
          content: '企微Tips和机器人只能2选1'
        },
        {
          id: '3',
          label: '企微机器人',
          type: 'bot',
          moduleName: 'remind',
          content: '企微Tips和机器人只能2选1'
        },
        {
          id: '4',
          label: 'MyOA',
          type: 'myoa',
          previewType: 'fasterMyOa',
          moduleName: 'remind',
          content: '请先联系超级管理员启用MyOA催办功能'
        },
        {
          id: '5',
          label: '微信HR助手',
          type: 'hrAssistant',
          previewType: 'fasterWxAssistant',
          moduleName: 'remind',
          content: '请先联系超级管理员启用HR助手催办功能'
        }
      ],
      templateType: '',
      previewType: '',
      commonInfoTemplateDialog: false,
      sendTestDialog: false,
      // 自动催办设置
      warningIcon: require('@/assets/mooc-img/warning-icon.png'),
      weekList: [
        { label: '周一', val: '1' },
        { label: '周二', val: '2' },
        { label: '周三', val: '3' },
        { label: '周四', val: '4' },
        { label: '周五', val: '5' },
        { label: '周六', val: '6' },
        { label: '周日', val: '7' }
      ],
      form: {
        enabled_remaind: false, // 自动催办
        message_type: [], // 渠道
        join_type: [], // 范围
        reminder_begin_time: '', // 周期催办开始时间
        reminder_end_time: '', // 周期催办结束时间
        reminder_gap_time: '', // 催办间隔时间，单位：天。1-表示每天催办，2-表示隔一天催办一次
        reminder_fixed_week_time: '', // 催办固定星期数，1-表示每周一进行催半，7-表示每周天进行催办；
        reminder_send_times: '', // 每隔几天催办一次，每周几催办的具体时间
        reminder_fixed_send_times: [], // 固定催办时间，需要指定日期。格式：“yyyy-mm-dd hh:mm:ss”
        date: [],
        time_1: null,
        time_2: null,
        enabled_reminder_leader: false,
        shield_l4: true
      },
      reminder_leader_type: [],
      rules: {
        message_type: [
          { required: true, message: '请选择催办渠道', trigger: 'blur' }
        ],
        join_type: [
          { required: true, message: '请选择催办范围', trigger: 'blur' }
        ]
      },
      frequency: '0',
      scopeList: [
        { label: '手动添加的学员', val: '1' },
        { label: '自动加入的学员', val: '2' },
        { label: '自主报名的学员', val: '3' }
      ],
      checked1: false, // 周期时间
      checked2: false, // 固定时间
      addWeekList: [
        {
          id: 0,
          value: ''
        }
      ],
      operations: 1
    }
  },
  computed: {
    ...mapState(['projectManageInfo', 'userLimitInfo']),
    // disabledMessage() {
    //   return (item, val) => {
    //     if (
    //       (item.type === 'myoa' && !this.projectManageInfo.enable_myoa) ||
    //       (item.type === 'deptBp' &&
    //         !this.projectManageInfo.enable_hr_assistant)
    //     ) {
    //       return false
    //       // 企微Tips和机器人只能2选1
    //     } else if (
    //       item.type === 'tips' &&
    //       this.form.message_type.includes('bot') &&
    //       val === 'remaind'
    //     ) {
    //       return false
    //     } else if (
    //       item.type === 'bot' &&
    //       this.form.message_type.includes('tips') &&
    //       val === 'remaind'
    //     ) {
    //       return false
    //     }
    //     return true
    //   }
    // }
    disabledMessage() {
      return (item, val) => {
        // 微信HR助手，myOA,组织BP,直接上级
        if (
          (item.type === 'hrAssistant' && !this.projectManageInfo.enable_hr_assistant) ||
          (item.type === 'myoa' && !this.projectManageInfo.enable_myoa) || 
          (item.type === 'deptBp' && !this.projectManageInfo.enable_copy_bp) || 
          (item.type === 'leader' && !this.projectManageInfo.enable_copy_leader)
        ) {
          return {
            able: false,
            content: item.content || ''
          }
        }
        if ((item.type === 'bot' && !this.projectManageInfo.enable_bot && val !== 'remind')) {
          return {
            able: false,
            content: '请先联系超级管理员启用企微机器人提醒功能'
          }
        }
        if (val === 'remind') {
          if (item.type === 'bot' && !this.projectManageInfo.enable_bot) {
            return {
              able: false,
              content: '请先联系超级管理员启用企微机器人催办功能'
            }
          } else if (this.projectManageInfo.enable_bot) {
            if (item.type === 'tips' && this.form.message_type.includes('bot')) {
              return {
                able: false,
                content: item.content || ''
              }
            } else if (item.type === 'bot' && this.form.message_type.includes('tips')) {
              return {
                able: false,
                content: '企微Tips和机器人只能2选1'
              }
            }
          }
        }
        return {
          able: true,
          content: ''
        }
      }
    },
    isApprove() {
      // 审批-审批中
      return this.$route.query.approve === '1' || this.projectManageInfo.course_status === 6
    }
  },
  mounted() {
    this.getStudentExpediteFn()
  },
  methods: {
    // 同步催办
    handleSyncTemplate(row) {
      const { mooc_course_id } = this.$route.query
      const params = {
        mooc_course_id,
        template_type: row.type,
        module_name: row.moduleName
      }
      superiorUrgeTemplate(params).then((res) => {
        this.commonInfoTemplateDialog = true
        this.$nextTick(() => {
          this.templateType = ''
          this.$refs.commonInfoTemplateRef.initData(res)
        })
      })
    },
    // 消息模板配置
    // 获取消息模板数据
    handleCheckTemplate(e) {
      this.getInfoTemplate(e).then((res) => {
        this.commonInfoTemplateDialog = true
        this.$nextTick(() => {
          this.previewType = e.previewType
          this.templateType = e.moduleName === 'notify' ? 'atuoAdd' : ''
          this.$refs.commonInfoTemplateRef.initData(res)
        })
      })
    },
    // 发送测试
    handleSendTest(item) {
      this.sendTestDialog = true
      this.$nextTick(() => {
        this.$refs.sendTestRef.initData(item)
      })
    },
    // 获取消息模板数据
    async getInfoTemplate(e) {
      const { mooc_course_id } = this.$route.query
      // notify--通知  remind--催办
      const params = {
        mooc_course_id,
        template_type: e.type,
        module_name: e.moduleName
      }
      let res = await infoTemplate(params)
      return res
    },
    // 自动催办设置
    // 获取自动催办设置数据
    getStudentExpediteFn() {
      const { mooc_course_id } = this.$route.query
      getStudentExpedite({ mooc_course_id }).then((res) => {
        if (res) {
          const {
            enabled_remaind,
            message_type,
            join_type,
            reminder_begin_time,
            reminder_end_time,
            reminder_gap_time,
            reminder_fixed_week_time,
            reminder_fixed_send_times,
            reminder_send_times,
            reminder_leader_type,
            enabled_reminder_leader,
            shield_l4
          } = res
          this.form = {
            enabled_remaind,
            reminder_begin_time,
            message_type: message_type.split(';'),
            join_type: join_type.split(';'),
            reminder_end_time,
            reminder_gap_time,
            reminder_fixed_week_time,
            reminder_fixed_send_times,
            reminder_send_times,
            date:
              res.reminder_begin_time && res.reminder_end_time
                ? [res.reminder_begin_time, res.reminder_end_time]
                : [],
            time_1: '',
            time_2: '',
            enabled_reminder_leader,
            shield_l4
          }
          this.reminder_leader_type = reminder_leader_type.split(';')
          if (!!reminder_gap_time || !!reminder_fixed_week_time) {
            this.checked1 = true
            if (reminder_gap_time) {
              this.frequency = '1'
              this.form.time_1 = reminder_send_times
            } else if (res.reminder_fixed_week_time) {
              this.frequency = '2'
              this.form.time_2 = reminder_send_times
            }
          }
          if (reminder_fixed_send_times && reminder_fixed_send_times.length) {
            this.checked2 = true
            this.addWeekList = []
            reminder_fixed_send_times.split(';').forEach((v, i) => {
              this.addWeekList.push({
                id: i,
                value: v
              })
              this.operations =
                this.addWeekList[this.addWeekList.length - 1].id + 1
            })
            this.form.reminder_fixed_send_times =
              reminder_fixed_send_times.split(';')
          }
        }
      })
    },
    // 添加周期项
    addWeekItem() {
      ++this.operations
      this.addWeekList.push({
        id: this.operations,
        value: ''
      })
    },
    // 删除周期项
    removeWeekItem(id) {
      if (this.addWeekList.length <= 1) return
      let index = this.addWeekList.findIndex((e) => e.id === id)
      this.addWeekList.splice(index, 1)
    },
    // 保存
    handleSave() {
      // 开关关闭时，清空数据
      if (!this.form.enabled_remaind) {
        this.form = {
          enabled_remaind: false, // 自动催办
          message_type: [], // 渠道
          join_type: [], // 范围
          reminder_begin_time: '', // 周期催办开始时间
          reminder_end_time: '', // 周期催办结束时间
          reminder_gap_time: '', // 催办间隔时间，单位：天。1-表示每天催办，2-表示隔一天催办一次
          reminder_fixed_week_time: '', // 催办固定星期数，1-表示每周一进行催半，7-表示每周天进行催办；
          reminder_send_times: '', // 每隔几天催办一次，每周几催办的具体时间
          reminder_fixed_send_times: [], // 固定催办时间，需要指定日期。格式：“yyyy-mm-dd hh:mm:ss”
          date: [],
          time_1: null,
          time_2: null,
          enabled_reminder_leader: false,
          shield_l4: true
        }
        this.reminder_leader_type = []
        this.checked1 = false
        this.checked2 = false
        this.addWeekList = [{ id: 0, value: '' }]
      }
      this.form.date = this.form.date || []
      const {
        enabled_remaind,
        message_type,
        join_type,
        date,
        reminder_gap_time,
        time_1,
        time_2,
        reminder_fixed_week_time,
        enabled_reminder_leader
      } = this.form
      if (enabled_remaind) {
        if (message_type.length <= 0) {
          this.$message.warning('请选择催办渠道')
          return
        }
        if (message_type?.length > 3) {
          this.$message.warning('最多可选3个催办渠道')
          return
        }
        if (join_type.length <= 0) {
          this.$message.warning('请选择催办范围')
          return
        }
        // 选择按周期时间催办
        if (!this.checked1 && !this.checked2) {
          this.$message.warning('请选择催办时间')
          return
        }
        if (this.checked1) {
          let reminderTime = date || []
          if (reminderTime.length < 1) {
            this.$message.warning('请选择起止催办时间')
            return
          }
          this.form.reminder_begin_time = date[0]
          this.form.reminder_end_time = date[1]
          if (this.frequency === '1') {
            if (!reminder_gap_time) {
              this.$message.warning('请选择催办频率每隔催办天数')
              return
            }
            if (!time_1) {
              this.$message.warning('请选择催办频率每隔催办时间')
              return
            }
            this.form.reminder_send_times = time_1
          } else if (this.frequency === '2') {
            if (!reminder_fixed_week_time) {
              this.$message.warning('请选择催办频率每周催办日期')
              return
            }
            if (!time_2) {
              this.$message.warning('请选择催办频率每周催办时间')
              return
            }
            this.form.reminder_send_times = time_2
          }
        }
        if (this.checked2) {
          let listEvery = this.addWeekList.every((e) => {
            if (e.value) {
              return true
            } else {
              return false
            }
          })
          if (!listEvery) {
            this.$message.warning('按固定时间催办时间不能为空')
            return
          }
          this.form.reminder_fixed_send_times = this.addWeekList.map(
            (e) => e.value
          )
        }
        this.reminder_leader_type = this.reminder_leader_type.filter((e) => e)
        if (enabled_reminder_leader && !this.reminder_leader_type?.length) {
          this.$message.warning('请选择提醒对象')
          return
        }
        // 同步提醒
        if (!enabled_reminder_leader) {
          this.reminder_leader_type = []
          this.form.shield_l4 = true
        }
      }
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const { mooc_course_id } = this.$route.query
          const param = {
            mooc_course_id,
            enabled_remaind,
            message_type: message_type.join(';'),
            join_type: join_type.join(';'),
            reminder_begin_time: this.form.reminder_begin_time,
            reminder_end_time: this.form.reminder_end_time,
            reminder_gap_time: this.frequency === '2' ? '' : reminder_gap_time,
            reminder_fixed_week_time,
            reminder_send_times: this.form.reminder_send_times,
            reminder_fixed_send_times: this.form.reminder_fixed_send_times
              .length
              ? this.form.reminder_fixed_send_times.join(';')
              : '',
            reminder_leader_type: this.reminder_leader_type?.length
              ? this.reminder_leader_type.join(';')
              : '',
            enabled_reminder_leader,
            shield_l4: this.form.shield_l4
          }
          studentExpedite(param).then((res) => {
            this.$message.success('保存成功')
            this.$nextTick(() => {
              this.getStudentExpediteFn()
            })
          })
        } else {
          return false
        }
      })
    },
    // 催办勾选
    handleRemaind(val, type) {
      this.form.message_type = this.form.message_type.filter((e) => e)
      if (this.form.message_type?.length > 3) {
        this.$message.warning('最多可选3个催办渠道')
        return
      }
      if (type === 'myoa') {
        if (val) {
          this.$confirm(
            '当选择多个催办时间时，为避免生成多条MyOA待办给学员造成困扰，针对符合催办条件的学员仅会生成一条MyOA待办。',
            '确定选中MyOA渠道催办吗？',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消'
            }
          )
            .then(() => { })
            .catch(() => {
              this.form.message_type.pop()
            })
        }
      }
    },
    handleLeader(val) {
      if (val.includes('leader')) {
        this.form.shield_l4 = false
      } else {
        this.form.shield_l4 = true
      }
    },
    // 催办时间
    checkBoxChange(e, t) {
      if (e && t === 1) {
        this.frequency = '1'
        this.form.time_1 = '09:00:00'
      } else if (!e && t === 1) {
        this.form.reminder_begin_time = ''
        this.form.reminder_end_time = ''
        this.form.reminder_gap_time = ''
        this.form.reminder_fixed_week_time = ''
        this.form.reminder_send_times = ''
        this.frequency = '0'
        this.form.time_1 = ''
        this.form.time_2 = ''
      } else if (!e && t === 2) {
        this.addWeekList = [{ id: 0, value: '' }]
        this.form.reminder_fixed_send_times = []
      }
    },
    // 催办频率
    frequencyChange(e, t) {
      if (e && t === 1) {
        this.form.reminder_gap_time = 1
        this.form.reminder_fixed_week_time = ''
        if (!this.form.time_1) {
          this.form.time_1 = '09:00:00'
        }
        this.form.time_2 = null
      } else if (e && t === 2) {
        this.form.reminder_gap_time = null
        this.form.reminder_fixed_week_time = '1'
        this.form.time_1 = null
        if (!this.form.time_2) {
          this.form.time_2 = '09:00:00'
        }
      }
    },
    getDate(time = '', style = '/') {
      const now = time ? new Date(time) : new Date()
      const year = now.getFullYear()
      const month = now.getMonth() + 1
      const day = now.getDate()
      console.log(day)
      return year + style + this.dateStyle(month) + style + this.dateStyle(day)
    },
    dateStyle(v) {
      return Number(v) < 10 ? `0${v}` : v
    }
  }
}
</script>
<style lang="less" scoped>
.project-manage-page-box .container-body {
  height: calc(100% - 270px);
}

:deep(.msg-bottom-fixed) {
  .el-button {
    width: unset;
  }

  .inner {
    text-align: center;
    width: unset;
  }
}

.warning-box {
  width: 855px;
  border-radius: 3px;
  background: #fcf6ed;
  padding: 10px 16px;
  color: #ff7548;
  font-size: 14px;
  font-family: 'PingFang SC';
  line-height: 20px;
  margin-bottom: 20px;

  .warning-icon {
    width: 20px;
    height: 20px;
    margin-right: 4px;
  }
}

.urge-tips {
  color: #e34d59;
  margin-left: 85px;
  // color: rgba(0, 0, 0, 0.4);
}

.msgSetting-page {
  .page-title {
    padding-bottom: 16px;

    .span-title {
      color: #000000;
      font-size: 14px;
      font-weight: 600;
      font-family: 'PingFang SC';
      position: relative;
      margin-left: 24px;

      &::before {
        content: '';
        width: 7px;
        height: 7px;
        background: #85a7ff;
        position: absolute;
        top: 6px;
        left: -21px;
      }

      &::after {
        content: '';
        width: 7px;
        height: 7px;
        background: #3464e0;
        position: absolute;
        top: 9px;
        left: -18px;
      }
    }
  }

  .top-template {
    margin: 0 0 36px 24px;

    .card-box {
      padding: 16px;
      width: 384px;
      line-height: 22px;
      border-radius: 4px;
      border: 1px solid #eeeeee;
      background: linear-gradient(135.4deg,
          #f0f6ff 7.000000000000001%,
          #f0f6ff 79%,
          #f2f2f2 100%);
      font-size: 14px;
      font-family: 'PingFang SC';

      .card-box-title {
        color: rgba(0, 0, 0, 0.6);
        margin-bottom: 12px;
      }

      .row-box {
        margin-bottom: 8px;

        &:nth-last-child(1) {
          margin: 0;
        }

        .row-left {
          color: rgbaa(0, 0, 0, 0.9, 0.9);
        }

        .row-right {
          color: #0052d9;
          .disabled-check {
            pointer-events: none;
            cursor: not-allowed;
            color: #9ab2f0;
          }

          &>span {
            display: inline-block;
            margin-left: 20px;
            cursor: pointer;
          }
        }
      }
    }
  }

  .automatic-eminder-setting {
    padding: 0 0 50px 24px;

    .automatic-reminder-switch {
      font-size: 14px;
      font-family: 'PingFang SC';
      color: rgba(0, 0, 0, 0.4);
      margin-left: 8px;

      .color-red {
        color: #e34d59;
        font-size: 14px;
        font-family: 'PingFang SC';
      }
    }
  }

  .reminder-time {
    margin-left: 86px;

    .cycle-time {
      margin-bottom: 16px;

      .cycle-time-content {
        width: 672px;
        height: 160px;
        padding: 16px 24px 16px 16px;
        background: #fbfbfb;
        margin-top: 12px;

        .tips {
          color: rgba(0, 0, 0, 0.4);
        }
      }
    }

    .fixed-time {
      .fixed-time-content {
        margin: 10px 0 0 26px;

        .btn-box {
          width: 32px;
          height: 32px;
          border-radius: 3px;
          opacity: 1;
          border: 1px dashed #dcdcdc;
          background: rgba(255, 255, 255, 1);
          font-weight: 700;
          font-size: 16px;
          cursor: pointer;
          margin-left: 8px;
        }

        .add-btn {
          position: relative;

          &::before {
            content: '';
            width: 9px;
            height: 1.3px;
            background: #000000e6;
            position: absolute;
            top: 16px;
            left: 11px;
          }

          &::after {
            content: '';
            width: 9px;
            height: 1.3px;
            background: #000000e6;
            position: absolute;
            top: 16px;
            left: 11px;
            transform: rotate(90deg);
          }
        }

        .remove-btn {
          position: relative;
          font-size: 1px;

          &::after {
            content: '';
            width: 9px;
            height: 1.3px;
            background: #000000e6;
            position: absolute;
            top: 16px;
            left: 11px;
          }
        }
      }
    }
  }

  .records-sync-box {
    .records-sync-tips {
      margin-left: 12px;
      color: rgba(0, 0, 0, 0.4);
    }

    .tips-person-box {
      display: flex;
      margin-left: 80px;
      background: #fbfbfb;
      width: 872px;
      padding: 16px 24px 16px 16px;
      margin-top: 12px;

      .tips-label {
        color: rgba(0, 0, 0, 0.6);
        margin-right: 12px;
      }

      .l4-check {
        margin-left: 30px;
      }
    }
  }
  :deep(.el-radio) {
    .el-radio__input.is-disabled .el-radio__inner, .el-radio__input.is-disabled.is-checked .el-radio__inner {
      border-color: #ced9f8;
    }
    .el-radio__input.is-disabled.is-checked .el-radio__inner::after {
      background-color: #ced9f8;
    }
  }
  :deep(.el-form) {
    .el-form-item__label {
      color: rgba(0, 0, 0, 0.6);
    }
  }

  :deep(.el-checkbox) {

    .el-checkbox__input.is-checked .el-checkbox__inner,
    .el-checkbox__input.is-indeterminate .el-checkbox__inner,
    .el-checkbox__inner {
      border-radius: 4px;
    }

    .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
      background-color: #ced9f8;
      border-color: #ced9f8;
    }
  }

  :deep(.el-range-editor--small.el-input__inner) {
    width: 280px;
  }

  :deep(.el-date-editor) {
    .el-range-separator {
      padding: 0;
    }
  }

  :deep(.el-radio) {
    margin-right: 12px;
    color: rgba(0, 0, 0, 0.9);

    .el-radio__input.is-checked+.el-radio__label {
      color: rgba(0, 0, 0, 0.9);
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 16px;
  }

  .pub-flex {
    display: flex;
  }

  .pub-row-center {
    display: flex;
    justify-content: center;
  }

  .pub-row-between {
    display: flex;
    justify-content: space-between;
  }

  .pub-col-center {
    align-items: center;
  }

  .pub-m-l-8 {
    margin-left: 8px;
  }

  .pub-m-l-12 {
    margin-left: 12px;
  }

  .pub-m-l-76 {
    margin-left: 76px;
  }

  .pub-m-r-16 {
    margin-right: 16px;
  }

  .pub-m-r-12 {
    margin-right: 12px;
  }

  .pub-m-b-12 {
    margin-bottom: 12px;
  }

  .pub-m-b-16 {
    margin-bottom: 16px;
  }

  .width-88 {
    width: 88px;
  }

  .width-114 {
    width: 114px;
  }

  .width-120 {
    width: 114px;
  }
}

.publish-test {
  vertical-align: initial;
}
</style>
