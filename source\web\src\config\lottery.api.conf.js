import http from 'utils/http'
import env from 'config/env.conf.js'

const envName = env[process.env.NODE_ENV]

// 积分抽奖
// 获取奖品信息
export const getLuckDrawListAPI = (activityId) => http.get(envName.lotteryHost + `api/v1/user/lottery/luck_draw_list/${activityId}`, { loading: true })
// 获取中奖列表
export const getLotteryRecordsAPI = (activityId) => http.get(envName.lotteryHost + `api/v1/user/lottery/lottery_records?activity_id=${activityId}`, { loading: true })
// 获取抽奖次数
export const getNumberOfLuckyDrawsAPI = (staffid) => http.get(envName.lotteryHost + `api/v1/user/lottery/lottery-acct`, {
  loading: true
})
// 获取抽奖结果
export const getLuckDrawResultsAPI = (params) => http.post(envName.lotteryHost + 'api/v1/user/lottery/luck_draw', { params, loading: true })
// 抽奖活动信息
export const getLotteryActivityInfoAPI = (activityId) => http.get(envName.lotteryHost + `api/v1/user/lottery/lottery-activity?activity_id=${activityId}`, { loading: true })
// 抽奖记录
export const getLuckDrawHistoryAPI = (params) => http.get(envName.lotteryHost + `api/v1/user/lottery/luck_draw_history?activity_id=${params.activity_id}&current=${params.current}&size=${params.size}${params.lotteryNo ? '&lotteryNo=' + params.lotteryNo : ''}`, { loading: true })
// 抽奖记录详情
export const getLuckDrawHistoryInfoAPI = (orderId) => http.get(envName.lotteryHost + `api/v1/user/order_info/${orderId}`, { loading: true })
// 剩余能获取抽奖次数查询
export const getLotteryCountRemainAPI = (staffId) => http.get(envName.lotteryHost + `api/v1/user/lottery/lottery_count_remain?staff_id=${staffId}`, { loading: true })
// 抽奖次数明细
export const getPersonalLotteryDetailAPI = (params) => http.get(envName.lotteryHost + `api/v1/user/lottery/personal_lottery_detail?page=${params.current}&size=${params.size}`, { loading: true })
