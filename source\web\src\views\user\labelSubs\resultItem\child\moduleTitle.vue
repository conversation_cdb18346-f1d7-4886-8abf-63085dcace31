<template>
    <div class="cardTitle" 
      :dt-eid=dtEidCard(info)
      :dt-remark="dtRgihtModuleRemark(info)" 
    >
      <span class="moduleName">{{ name || info.module_name}}</span>
      <span class="moduleTitle" :title="info.title">{{ info.title }}</span>
    </div>
</template>
<script>
export default {
  props: ['info', 'curModuleId', 'curLabelId', 'name'],
  data() {
    return {}
  },
  computed: {
    dtEidCard() {
      return (val) => {
        return `element_${val.item_id}_${val.module_id}`
      }
    },
    dtRgihtModuleRemark() {
      return (val, container) => {
        return JSON.stringify({ 
          page: '标签订阅内容', 
          page_type: '标签订阅内容',
          container: '已上架内容',
          click_type: 'data',
          content_name: val.title,
          content_id: val.item_id,
          content_type: val.module_name,
          terminal: 'PC'
        })
      }
    }
  },
  methods: {}
}
</script>
<style lang="less" scoped>
    .cardTitle{
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        word-break: break-all;
        height: 48px;
        font-family: "PingFang SC";
        padding-right: 10px;
        .moduleName{
            display: inline-block;
            padding: 0 4px;
            margin-right: 8px;
            border-radius: 2px;
            background: #F5F5F7;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            color: #777;
            overflow: hidden;
            margin-bottom: -3px;
        }
        .moduleTitle{
            color: #333333;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 24px;
        }
    }
</style>
