<template>
  <div class="meeting-review-page">
    <div class="page-title">回看：{{ meetingData.subject || '-' }}</div>

    <div class="iframe-box">
      <iframe id="addCourseiframe" style="height: 100%; width: 100%" :src="meetingData.record_view_url" frameborder="0" ></iframe>
      <iframe id="iframe_show" :src="meetingData.record_view_url" width="100%" height="100%" scrolling="no" allowfullscreen="true"></iframe>
    </div>
  </div>
</template>

<script>
import { getMeetingRecordUserApi } from '@/config/classroom.api.conf.js'
export default {
  name: 'meetingReview',
  components: {},
  data() {
    return {
      meetingData: {
        subject: '',
        record_view_url: ''
      }
    }
  },
  watch: {},
  computed: {
    activityId() {
      return this.$route.query.activity_id
    }
  },
  created() {
    this.getMeetingRecordInfo()
  },
  mounted() { },
  beforeDestroy() { },
  methods: {
    getMeetingRecordInfo() {
      getMeetingRecordUserApi({ activity_id: this.activityId }).then(res => {
        this.meetingData = res
      })
    }
  }
}
</script>

<style lang="less" scoped>
.meeting-review-page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f3f5f7;
  .page-title {
    width: 100%;
    height: 48px;
    margin-bottom: 12px;
    padding: 10px 20px;
    font-size: 18px;
    line-height: 28px;
    font-weight: 500;
    color: #333333;
    background-color: #ffffff;
  }
  .iframe-box {
    flex: 1;
    overflow-y: auto;
    background-color: #ffffff;
  }
}
</style>
