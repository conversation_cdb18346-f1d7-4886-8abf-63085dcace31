<template>
  <div class="tutor-page" v-if="!isLoading">
    <div class="background-mask"></div>
    <div class="position-content">
      <img class="logo" src="@/assets/img/tutor/certification-logo.png" alt="">
      <div class="title">新员工导师认证要求</div>
      <!-- 导师状态为风险但是认证要求全部满足 -->
      <div class="status status-green" v-if="status === 3 && !hasNotPass">
        <img class="status-icon" :src="statusMap[1].img" alt="">
        <div class="status-text">{{ statusMap[1].text }}</div>
      </div>
      <div class="status status-green" :class="statusClass" v-else-if="statusMap[status]">
        <img class="status-icon" :src="statusMap[status].img" alt="">
        <div class="status-text">{{ statusMap[status].text }}</div>
      </div>
      <!-- 满足且导师状态非禁用或免认证 -->
      <template v-if="status === 1">
        <div class="sub-title">满足认证要求需完成以下内容，点击可查看内容详情</div>
        <div class="result-main">
          <div class="result-item item-green" v-for="(item, index) in certRiskDetail" :key="index">
            <img class="result-icon" src="@/assets/img/tutor/item-green.png" alt="">
            <span class="result-text" @click="toDetail(item.course_url)">{{ item.requirements_content }}</span>
          </div>
        </div>
        <div class="connection">如有疑问，请联系BG管理员{{ bgStr }}</div>
      </template>
      <!-- 不满足且导师状态为风险 -->
      <template v-else-if="status === 3">
        <div class="sub-title">满足认证要求需完成以下内容，点击可跳转查看内容详情</div>
        <div class="result-main">
          <div class="result-item" :class="item.meet_requirements ? 'item-green' : 'item-red'" v-for="(item, index) in certRiskDetail" :key="index">
            <img class="result-icon" :src="resolveImgUrl(item)" alt="">
            <span class="result-text" @click="toDetail(item.course_url)">{{ item.requirements_content }}</span>
          </div>
        </div>
        <div v-if="hasNotPass" class="link-main">如您已完成指定内容，请<span class="refresh" @click="refresh">刷新页面</span>获取最新状态（数据同步有一定延迟，请耐心等待~）</div>
        <div class="connection">如有疑问，请联系BG管理员{{ bgStr }}</div>
      </template>
      <!-- 不满足且导师状态为认证中 -->
      <template v-else-if="status === 0">
        <div class="sub-title">满足认证要求需完成以下内容，点击可跳转查看内容详情</div>
        <div class="result-main">
          <div class="result-item" :class="item.meet_requirements ? 'item-green' : 'item-red'" v-for="(item, index) in certRiskDetail" :key="index">
            <img class="result-icon" :src="resolveImgUrl(item)" alt="">
            <span class="result-text" @click="toDetail(item.course_url)">{{ item.requirements_content }}</span>
          </div>
        </div>
        <div class="warm">如果考试页面显示“已通过”但本页面显示未通过，则为历史考试通过记录，重新认证时要求再次通过考试</div>
        <div v-if="hasNotPass" class="link-main">如您已完成指定内容，请<span class="refresh" @click="refresh">刷新页面</span>获取最新状态（数据同步有一定延迟，请耐心等待~）</div>
        <div class="connection">如有疑问，请联系BG管理员{{ bgStr }}</div>
      </template>
      <!-- 导师状态为禁用 -->
      <template v-else-if="status === -1">
        <div class="result-people">
          <div class="people-item">部门管理员：{{ departSrt }}</div>
          <div class="people-item">BG管理员：{{ bgStr }}</div>
        </div>
        <div class="link-main">如已发起重新认证，请<span class="refresh" @click="refresh">刷新页面</span>获取最新状态（数据同步有一定延迟，请耐心等待~）</div>
      </template>
      <!-- 导师状态为免认证 -->
      <template v-else-if="status === 2">
        <div class="sub-title" v-if="notExamList.length">如您想了解辅导须知内容，可点击下方链接</div>
        <div class="result-main">
          <div class="result-item item-blue" v-for="(item, index) in notExamList" :key="index">
            <span class="result-text" @click="toDetail(item.course_url)">{{ item.requirements_content }}</span>
          </div>
        </div>
        <div class="connection">如有疑问，请联系BG管理员{{ bgStr }}</div>
      </template>
      <!-- 无权限 -->
      <template v-else-if="status === 4">
        <div class="no-main">
          <img class="no-power-img" src="@/assets/img/tutor/no-power.png" alt="">
          <div class="no-warm">当前您暂无页面访问权限</div>
          <div class="connection">如有疑问，请联系部门管理员{{ departSrt }}</div>
        </div>
      </template>
      <!-- 不在导师池的集团本部正式员工及顾问 -->
      <!-- <template v-else-if="status === 5"> -->
      <template v-else>
        <div class="no-main">
          <img class="no-demand-img" src="@/assets/img/tutor/no-demand.png" alt="">
          <div class="no-warm">当前您暂无导师认证要求</div>
          <div class="connection">如有疑问，请联系部门管理员{{ departSrt }}</div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { getTutorStatus, certificationRiskCheck, currAdmins } from '../api/tutor.api.conf'
export default {
  mixins: [],
  components: {},
  data() {
    return {
      status: -1, // 4: 无权限 5：不在导师池
      statusMap: {
        '1': { img: require('@/assets/img/tutor/status-green.png'), text: '当前您已满足新员工导师认证要求' },
        '2': { img: require('@/assets/img/tutor/status-green.png'), text: '当前您已满足担任新员工导师资格要求' },
        '-1': { img: require('@/assets/img/tutor/status-red.png'), text: '您当前暂无法担任新员工导师，详情请联系部门HBBP沟通。' },
        '0': { img: require('@/assets/img/tutor/status-yellow.png'), text: '当前处于重新认证的流程中，不满足认证要求' },
        '3': { img: require('@/assets/img/tutor/status-yellow.png'), text: '当前您不满足新员工导师认证要求' }
      },
      certRiskDetail: [], // 认证结果
      personInfo: {
        departList: [],
        bgList: []
      },
      isLoading: false
      // currentStaffId: ''
    }
  },
  computed: {
    statusClass() {
      let className = ''
      if ([1, '1', 2, '2'].includes(this.status)) {
        className = 'status-green'
      } else if ([-1, '-1'].includes(this.status)) {
        className = 'status-red'
      } else {
        className = 'status-yellow'
      }
      return className
    },
    // staffId() {
    //   if (process.env.NODE_ENV === 'production') {
    //     return this.currentStaffId
    //   } else {
    //     return this.$route.query?.staffId || this.currentStaffId || ''
    //   }
    // },
    departSrt() {
      return this.personInfo.departList.map(item => item.staff_full_name).join('、') || '-'
    },
    bgStr() {
      return this.personInfo.bgList.map(item => item.staff_full_name).join('、') || '-'
    },
    // 非考试的认证
    notExamList() {
      return this.certRiskDetail.filter(item => {
        let act_type = item.requirement_key.split('-')[0] || ''
        return act_type !== '20'
      })
    },
    // 有未完成的要求
    hasNotPass() {
      return this.certRiskDetail.some(item => !item.meet_requirements)
    }
  },
  watch: {
    // '$store.state.userInfo': {
    //   handler(newValue) {
    //     if (newValue?.staff_id) {
    //       this.currentStaffId = newValue.staff_id || ''
    //     }
    //   },
    //   immediate: true,
    //   deep: true
    // },
    // staffId: {
    //   handler(newvalue) {
    //     if (newvalue) {
    //       this.certificationRiskCheck()
    //       this.getCurrAdmins()
    //     }
    //   },
    //   immediate: true,
    //   deep: true
    // }
  },
  mounted() {
    this.isLoading = true
    this.certificationRiskCheck()
    this.getCurrAdmins()
  },
  methods: {
    resolveImgUrl(item) {
      return require(`@/assets/img/tutor/${item.meet_requirements ? 'item-green' : 'item-red'}.png`)
    },
    refresh() {
      this.certificationRiskCheck()
    },
    // 获取员工状态信息
    getTutorStatusInfo() {
      getTutorStatus().then((data) => {
        this.status = data
      }).catch((error) => {
        if (error.code === 403) {
          this.status = 4 // 没权限
        } else if ([false, null].includes(error.data)) {
          this.status = 5 // 不在导师池
        } else {
          let message = error.message || '网络异常，请稍后重试！'
          this.$message.error(message)
        }
      }).finally(() => {
        this.isLoading = false
      })
    },
    // 认证要求检查
    certificationRiskCheck() {
      certificationRiskCheck().then((data) => {
        // console.log('certificationRiskCheck----data~~~~~: ', data)
        this.certRiskDetail = data.cert_risk_detail || []
      }).catch((error) => {
        console.log('error: ', error)
      }).finally(() => {
        this.getTutorStatusInfo()
      })
    },
    // 获取部门和BG管理员
    getCurrAdmins() {
      currAdmins().then(res => {
        // console.log('getCurrAdmins------res: ', res)
        if (res) {
          this.personInfo.departList = res.dept_admins || []
          this.personInfo.bgList = res.bg_admins || []
        }
      }).catch(error => {
        console.log('error: ', error)
      })
    },
    toDetail(href) {
      if (href) {
        window.open(href)
      }
    }
  },
  filters: {
  },
  beforeDestroy() {
  }
}
</script>

<style lang="less" scoped>
.tutor-page {
  position: relative;
  height: 100%;
  background: #F5F5F7;
  .background-mask {
    width: 100%;
    height: 460px;
    background: linear-gradient(180deg, #DCE8F8 0%, #F5F5F7 100%);
  }
  .position-content {
    position: absolute;
    top: 40px;
    bottom: 78px;
    left: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 36px 0 100px;
    overflow-y: auto;
    width: 1200px;
    border-radius: 12px;
    background: #FFF;
    transform: translateX(-50%);
    .logo {
      width: 424.5px;
      height: 74px;
    }
    .title {
      margin-top: 52px;
      color: #000000;
      text-align: center;
      font-size: 28px;
      font-weight: 600;
      line-height: 36px;
    }
    .status {
      margin-top: 24px;
      display: flex;
      padding: 0 24px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      height: 54px;
      border-radius: 6px;
      .status-icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }
      .status-text {
        color: #000000e6;
        font-size: 14px;
        line-height: 22px;
      }
    }
    .status-green {
      background: #C6F3D7;
    }
    .status-yellow {
      background: #FFD9C2;
    }
    .status-red {
      background: #FFD8D2;
    }
    .sub-title {
      margin-top: 16px;
      color: #000000e6;
      font-size: 14px;
      line-height: 22px;
    }
    .result-main {
      margin: 16px 0 4px;
      display: flex;
      flex-direction: column;
      align-items: center;
      .result-item {
        display: flex;
        padding: 2px 8px;
        align-items: center;
        margin-bottom: 12px;
        height: 26px;
        border-radius: 3px;
        .result-icon {
          width: 14px;
          height: 14px;
          margin-right: 4px;
        }
        .result-text {
          cursor: pointer;
          text-decoration: underline;
          text-decoration-skip-ink: auto;
          text-underline-offset: 0.2em;
        }
      }
      .item-green {
        color: #2ba471;
        background: #E3F9E9;
      }
      .item-red {
        color: #d54941;
        background: #FFF0ED;
      }
      .item-blue {
        color: #0052d9;
      }
    }
    .result-people {
      margin: 16px 0 8px;
      color: #00000099;
      text-align: center;
      font-size: 14px;
      line-height: 22px;
    }
    .no-main {
      text-align: center;
      .no-power-img {
        margin: 8px 0 16px;
        width: 160px;
        height: 160px;
      }
      .no-demand-img {
        margin: 48px 0 16px;
        width: 239px;
        height: 148px;
      }
      .no-warm {
        color: #000000e6;
        font-size: 18px;
        font-weight: 600;
        line-height: 25px;
        margin-bottom: 16px;
      }
    }
    .warm {
      margin-bottom: 16px;
      color: #d54941;
      font-size: 14px;
      line-height: 22px;
    }
    .link-main {
      margin-bottom: 8px;
      color: #00000099;
      font-size: 14px;
      line-height: 22px;
      .refresh {
        cursor: pointer;
        color: #0052d9;
      }
    }
    .connection {
      color: #00000099;
      text-align: center;
      font-size: 14px;
      line-height: 22px;
    }
  }
}
@media screen and (max-width: 1660px) {
}

@media screen and (min-width: 1661px) {
}
</style>
