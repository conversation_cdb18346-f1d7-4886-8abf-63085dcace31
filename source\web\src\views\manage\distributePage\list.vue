f<template>
  <div class="distributePage">
    <!-- <Breadcrumb></Breadcrumb> -->
    <div class="content-top">
      <el-input
        placeholder="请输入页面名称"
        class="input-search"
        prefix-icon="el-icon-search"
        v-model="distributePage.searchName"
      />
      <el-select
        v-model="distributePage.status"
        @change="getList"
        class="wt-146">
        <el-option
          v-for="item in qtStatusList"
          :key="item.id"
          :label="item.title"
          :value="item.id"
        ></el-option>
      </el-select>
    </div>
    <div class="content-main">
      <div class="table-top">
        <button class="el-button el-button--primary" @click="createDistribute">
          <i class="el-icon-circle-plus-outline"></i><span>创建分发页</span>
        </button>
      </div>
      <el-table
        ref="multipleTable"
        :data="tableData"
        tooltip-effect="dark"
        style="width: 100%"
      >
        <el-table-column
          prop="forward_name"
          label="页面名称"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          label="页面状态"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span>{{scope.row.status === 1 ? '在用' : '停用'}}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="url_count"
          label="已关联分发项"
          :show-overflow-tooltip="true"
        >
          <!-- <template slot-scope="scope">
            <span>{{scope.row.rele_exam_ids}}</span>
          </template> -->
        </el-table-column>
        <el-table-column
          prop="creator_name"
          label="创建人"
          :show-overflow-tooltip="true"
        ></el-table-column>
        <el-table-column
          prop="created_at"
          label="创建时间"
          :show-overflow-tooltip="true"
        >
          <template slot-scope="scope">
            <span>{{scope.row.created_at.split('.')[0] || '-'}}</span>
          </template>
        </el-table-column>
        <el-table-column width="300" label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="handleLink(scope.row)">页面链接</el-button>
            <el-button type="text" @click="handleEdit(scope.row)" style="marginRight: 10px">编辑</el-button>
            <el-dropdown @command="handleCommand(scope.row, scope.$index, $event)">
              <el-button type="text">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="over">{{scope.row.status === 1 ? '停用' : '启用'}}</el-dropdown-item>
                <el-dropdown-item style="color: red;" command="delete">删除</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <div class="table-pager" v-show="tableData.length != 0">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
          :page-sizes="[10, 20, 50, 100]"
          :current-page="distributePage.pageNum"
          :page-size="distributePage.pageSize"
          :total="distributePage.total"
        >
        </el-pagination>
      </div>
    </div>
    <shareLink ref="shareLink" :visible.sync="shareDialog"></shareLink>
  </div>
</template>

<script>
import { getForwardPageApi, getForwardStatusApi, delForwardApi } from '@/config/api.conf.js'
// import { command } from 'mixins'
import shareLink from './shareLink'

export default {
  name: 'distributePage',
  data() {
    return {
      shareDialog: false,
      tableData: [],
      distributePage: {
        searchName: '',
        status: '',
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      qtStatusList: [
        { title: '全部', id: '' },
        { title: '在用', id: 1 },
        { title: '停用', id: 2 }
      ]
    }
  },
  watch: {
    'distributePage.searchName': function () {
      clearTimeout(this.timer)
      this.timer = setTimeout(() => {
        this.getList()
      }, 500)
    }
  },
  components: {
    shareLink
  },
  mounted() {
    this.getList()
  },
  methods: {
    getList() {
      const { searchName, status, pageNum, pageSize } = this.distributePage
      let params = {
        forward_name: searchName,
        status: status,
        current: pageNum,
        size: pageSize
      }
      getForwardPageApi(params).then(res => {
        this.distributePage.total = res.total
        this.tableData = res.records
      })
    },
    createDistribute() {
      this.$router.push({
        name: 'distributePageAdd'
      })
    },
    handleEdit(item) {
      this.$router.push({
        name: 'distributePageAdd',
        query: {
          id: item.forward_id
        }
      })
    },
    handleLink(item) {
      this.shareDialog = true
      this.$refs.shareLink.getData({
        exam_name: item.forward_name,
        exam_id: item.forward_id
      })
    },
    handleCommand(item, index, e) {
      if (e === 'over') {
        this.handleOver(item)
      } else if (e === 'delete') {
        this.handleDelete(item, index)
      }
    },
    handleOver(item) {
      // 1-启用 2-停用
      let status = item.status === 1 ? 2 : 1
      getForwardStatusApi({
        forward_id: item.forward_id,
        status
      }).then(res => {
        this.$message.success('操作成功！')
        this.getList()
      })
    },
    handleDelete(item, index) {
      this.$messageBox.confirm('确定删除吗？', '删除分发页', {
        confirmButtonText: '删除',
        cancelButtonText: '取消'
      }).then(() => {
        delForwardApi(item.forward_id).then((res) => {
          this.tableData.splice(index, 1)
          this.$message.success('删除成功')
        })
      })
    },
    handleSizeChange(val) {
      this.distributePage.pageSize = val
      this.getList()
    },
    handlePageChange(val) {
      this.distributePage.pageNum = val
      this.getList()
    }
  }
}
</script>

<style lang="less" scoped>
.wt-146{
  width: 146px;
  margin-right: 10px;
}
.content-top {
  width: 100%;
  padding: 20px;
  background-color: #fff;
  margin-bottom: 10px;
  display: flex;
  .input-search {
    width: 280px;
    margin-right: 10px;
  }
}
.content-main {
  .table-top {
    padding: 10px;
    padding-left: 20px;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
    display: flex;
  }
  .exam_end {
    color:red;
  }
  .table-pager {
    display: flex;
    justify-content: flex-end;
    padding-top: 10px;
    /deep/.el-pagination {
      padding-right: 0px;
    }
  }
}
</style>
