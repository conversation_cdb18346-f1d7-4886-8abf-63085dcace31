<template>
  <div class="prize-details">
    <!-- 抽奖产品信息 -->
    <prize-item :data="orderInfo"></prize-item>
    <!-- 实物未发放显示 -->
    <div class="btn-column" v-if="fillInDeliveryInfoShow">
      <van-button class="btn" @click="deliveryLink">
        <img class="btn-icon" src="../../../../../assets/img/mobile/lottery/edit.png" />
        <span>填写配送信息</span>
      </van-button>
    </div>
    <div class="content">
      <div class="card" v-if="orderInfo.goods_type === 0">
        <div class="card-title">卡券信息</div>
        <div class="card-content">
          <div class="li">
            <span class="label">卡号：</span>
            <span class="value van-ellipsis">{{ orderInfo.redeem_code }}</span>
            <span class="copy-btn" id="copy-tag1" @click="copy(1)" :data-clipboard-text="orderInfo.redeem_code">复制</span>
          </div>
          <div class="li">
            <span class="label">密码：</span>
            <span class="value van-ellipsis">{{ orderInfo.redeem_pwd }}</span>
            <span class="copy-btn" id="copy-tag2" @click="copy(2)" :data-clipboard-text="orderInfo.redeem_pwd">复制</span>
          </div>
          <div class="li">
            <span class="label">兑换截止时间：</span>
            <span class="value">{{ orderInfo.expire_time }}</span>
          </div>
        </div>
      </div>
      <div class="card">
        <div class="card-title">奖品说明</div>
        <div class="card-content">
          <div class="explain" v-html="orderInfo.goods_remark"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { 
  Toast
} from 'vant'
import prizeItem from './prizeItem.vue'
import Clipboard from 'clipboard'
import { getLuckDrawHistoryInfoAPI } from '@/config/lottery.api.conf.js'

export default {
  props: {
    orderId: {
      type: [String, Number],
      require: true
    }
  },
  components: {
    prizeItem
  },
  computed: {
    fillInDeliveryInfoShow() {
      return this.orderInfo.goods_type === 1 && this.orderInfo.order_status === 1 && this.orderInfo.redeem_questionnaire_url
    }
  },
  data() {
    return {
      // goods_type 商品类型  0-卡券 1-实物 2-课程 3-抽奖
      orderInfo: {}
    }
  },
  mounted() {
    this.getOrderInfo()
  },
  methods: {
    getOrderInfo() {
      getLuckDrawHistoryInfoAPI(this.orderId).then(res => {
        this.orderInfo = res
      })
    },
    // 复制
    copy (type) {
      var clipboard = new Clipboard('#copy-tag' + type)
      clipboard.on('success', e => {
        Toast('已复制到剪贴板')
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        // 不支持复制
        Toast('该浏览器不支持自动复制')
        // 释放内存
        clipboard.destroy()
      })
    },
    deliveryLink() {
      if (this.orderInfo.redeem_questionnaire_url) {
        window.location.href = this.orderInfo.redeem_questionnaire_url
      }
    }
  },
  beforeDestroy() {
    console.log('销毁')
  }
}
</script>

<style lang='less' scoped>
  .prize-details {
    height: 100%;
    overflow-y: auto;
    padding: 20px 16px;
    color: #000000e6;
    .btn-column .btn {
      width: 100%;
      display: flex;
      padding: 8px 16px;
      justify-content: center;
      align-items: center;
      gap: 8px;
      align-self: stretch;
      border-radius: 4px;
      background: #F2F3FF;
      color: #0052d9;
      font-size: 14px;
      font-weight: 500;
      .btn-icon {
        width: 16px;
        height: 16px;
        display: inline;
        margin-right: 8px;
      }
    }
    .content {
      margin-top: 24px;
      .card {
        margin-bottom: 20px;
        .card-title {
          padding: 0 8px 8px;
          font-size: 14px;
          font-weight: 600;
          line-height: 22px;
        }
        .card-content{
          padding: 16px;
          gap: 12px;
          border-radius: 8px;
          background: #F9F9F9;
          .li {
            display: flex;
            margin-bottom: 12px;
            font-size: 12px;
            .label {
              width: 90px;
              margin-right: 10px;
              flex-shrink: 0;
              color: #00000066;
              line-height: 20px;
            }
            .value {
              flex: 1;
              line-height: 20px;
            }
            .copy-btn {
              margin-left: 16px;
              height: 20px;
              padding: 2px 6px;
              gap: 6px;
              border-radius: 3px;
              border: 1px solid #0052D9;
              background: #ECF2FE;
              color: #0052d9;
              text-align: center;
              font-size: 10px;
              flex-shrink: 0;
            }
          }
          .li:last-child {
            margin-bottom: 0;
          }
          .explain {
            font-size: 14px;
            line-height: 22px;
            color: #00000099;
          }
        }
      }
    }
  }
</style>
