<template>
  <div></div>
</template>

<script>
import { getSurveyInfoApi } from '@/config/classroom.api.conf'
export default {
  created() {
    this.getSurveyInfo()
  },
  methods: {
    async getSurveyInfo() {
      try {
        if (!this.$route.query.survey_id) {
          return
        }
        if (this.$route.query.act_type * 1 === 4) {
          let res = await getSurveyInfoApi({ survey_id: this.$route.query.survey_id })
          window.location.href = res
        }
      } catch (error) {
        console.log(error)
      }
    }
  }
}
</script>

<style>

</style>
