<template>
  <div class="active-page">
    <div class="img-content" @click="goTo">
      <van-image class="top-img" :src="topImgUrl">
        <template v-slot:error>
          <img class="top-img" src="../../../../assets/img/mobile/geekBang/active-top.png" />
        </template>
      </van-image>
    </div>
    <div class="content-main">
      <div class="my-card-info">
        <div class="top">
          <span class="title">我的学霸卡：已领取
            <span class="num" @click="openDetailPop"> {{xuebaTotal}}</span> 张学霸卡,
            <span class="num color-6600" @click="openDetailPop">{{ cardInfo.balance || 0 }}</span> 张可兑课,
            <span class="num" @click="openDetailPop">{{usedNum}}</span> 张已使用,
            <span class="num" @click.stop="openDetailPop">{{overdueNum}}</span> 张已过期
          </span>

          <!-- <span class="title">我的学霸卡:&nbsp;
            <span class="num">{{ cardInfo.balance || 0 }} </span>张
          </span> -->
          <!-- <span class="date" v-if="cardInfo.balance">最近过期时间 <span>{{timeToDate(cardInfo.recently_expire_date)}}</span> </span> -->
        </div>
        <div class="btns">
          <div class="btn" :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', '已领卡去兑课')" @click="openDetailPop">已领卡去兑课</div>
          <div class="btn" :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', '已兑课程')" @click="toMooc">已兑课程</div>
          <div class="btn" :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', '活动规则')" @click="toRulesDetail">活动规则</div>
        </div>
      </div>
      <div class="general-banner" v-if="xueBaCardConfig.acct_type_code === 'xuebaCommon'">
        <swiper v-if="xueBaCardConfig.banner_img_mid.length  > 1" :options="swiperOption" ref="mySwiper">
          <swiper-slide class="swiper-slide swiper-container-3d" v-for="(banner,index) in xueBaCardConfig.banner_img_mid" :key="index" style="position:relative;overflow:hidden;width:100%;height:100px;">
            <a :href="banner.href" target="_blank" style="inline-block">
              <img :src="banner.img_url_mobile" style="width:100%;height:100px" />
            </a>
          </swiper-slide>
          <!-- 分页器 -->
          <div class="swiper-pagination" slot="pagination"></div>
        </swiper>
        <div v-else>
          <a :href="xueBaCardConfig.banner_img_mid[0].href" target="_blank" style="inline-block">
            <img :src="xueBaCardConfig.banner_img_mid[0].img_url_mobile" style="width:100%;height:100px" />
          </a>
        </div>
        <!-- <img :src="generalBanner('img')" alt=""> -->
      </div>
      <div class="card-info-common init-card-info-new">
        <!-- <div class="subscription">
          <div class="subscription-btn" @click="messageSubscribe">
            <img v-if="isSub" src="../../../../assets/img/mobile/geekBang/subscription-no.png" alt="">
            <img v-else src="../../../../assets/img/mobile/geekBang/subscription.png" alt=""> {{isSub ? '退订领卡提醒' : '订阅领卡提醒'}}
          </div> <span class="subscription-tips">订阅后，将在每月发卡时收到企微提醒</span>
        </div> -->

        <!-- 官方发放 -->
        <div class="official-mobile-img">
          <img src="../../../../assets/img/mobile/geekBang/official-mobile.png" alt="">
          <span class="span-text">官方发放</span>
        </div>
        <div class="card-stack-content">
          <div class="card-content" :class="getClass1()" :style="bgiStyle1">
            <div class="type" :class="{'init-disable-class1': isDisableStyle1}" :style="isDisableStyle1 ? '' : card_font_style('a')">「{{cardName}}」学霸卡</div>
            <span class="effect" :class="{'init-disable-class2': isDisableStyle1}" :style="isDisableStyle1 ? '' : card_font_style('b')">可用于兑换「{{cardName}}」课程</span>
            <div class="num-info">领取后7个自然日内有效</div>
            <div class="collect-btn-box">
              <div class="collect-btn" :style="buttonColor(1)" v-if="initCardCanGet" @click="throttleFn(1, true)" :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', '官方发放-点击领取')">点击领取</div>
              <div class="collect-btn stand-btn-disabled" :style="buttonColor(2, showText(0, 1))" v-else-if="officialList.length && [0, 'consume', 'manage_deduct', 'deduct_expire', undefined].includes(officialList[0].verb_id)" @click="throttleFn(1, false)" :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', `官方发放-${showText(0, 1)}`)">{{showText(0, 1)}}</div>
              <!-- 点击领取、已领取、抢光了 :style="showText(0) === '点击领取' ? buttonColor(1) : buttonColor(2)" -->
              <div class="collect-btn stand-btn-gray" v-else @click="throttleFn(1, false)" :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', `官方发放-${showText(0, 1)}`)">{{ showText(0, 1) }} </div>
            </div>
          </div>
        </div>
        <div class="card-info-common-tips">领取官方下发的学霸卡，即可解锁分享资格</div>
        <!-- 分享有礼 -->
        <div class="give-box-mobile" id="invite-main_id" v-if="xueBaCardConfig.acct_type_code !== 'xuebaCommon'">
          <div class="official-mobile-img">
            <img src="../../../../assets/img/mobile/geekBang/official-mobile.png" alt="">
            <span class="span-text">分享有礼</span>
             <span class="right-position">还可获得<span style="color:#0052d9"> {{Math.max(3 - presentRewardList.length, 0)}} </span>张奖励</span>
          </div>
          <!-- <div class="give-explain">
            <p style="color:#333333;line-height: 22px;">分享给同事，可额外获得学霸卡奖励</p>
          </div> -->
          <!-- <give-tab @openGive="openGive" :consumePoint="consumePoint" :numberOfRewards="numberOfRewards" :Config="xueBaCardConfig" :listObj="{presentRecordList: presentRecordList, presentPassiveRecordList: giveCardDataList }"></give-tab> -->
          <give-tab :disabled="isGetOfficial" :xueBaCardConfig="xueBaCardConfig"></give-tab>
          <div class="shear-card-content" v-if="presentRewardList.length">
            <div :class="['card-content', 'shear-card-item', `shear-card-position-${item}`, getClassGive(item, presentRewardList)]" :style="bgiStyleGive(item, presentRewardList)" v-for="item in 3" :key="item">
              <div v-if="presentRewardList.length >= item">
                <div class="type"  :style="card_font_style('a')" >「{{cardName}}」学霸卡</div>
                <span style="font-size:10px" class="effect" :style="card_font_style('b')">可用于兑换{{cardName}}课程</span>
                <div class="num-info">领取后7个自然日内有效</div>
                <div class="collect-btn-box">
                  <!-- 可使用已使用已过期 -->
                  <div :style="buttonColor(2, showTextGive(item - 1, 'reward'))" class="collect-btn stand-btn-disabled" type="primary" plain>
                    {{ showTextGive(item - 1, 'reward') }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="give-right no-data">
            <img :src="require('@/assets/outsourcedCourse/no-data-give.png')" alt="">
            <div class="data-text">暂无奖励</div>
          </div>
          <div class="card-info-common-tips">
            1、成功分享学霸卡给同事，可额外获赠1张学霸卡。通过分享行为最多可额外获赠3张；
            2、 被分享人最多收到3张学霸卡。若已收满3张，则无法分享成功。
          </div>
          <div class="see-record-text " @click="openSeeRecord">
            <span>查看送出记录<i class="el-icon-arrow-right"></i></span>
          </div>
        </div>
      </div>

      <div class="given-by-others-content card-info-common init-card-info-new ">
        <!-- 学习奖励 -->
        <div class="card-info-common init-card-info-new ">
          <div class="given-by-others">
            <div class="official-mobile-img" :style="packup ? 'margin-bottom: 0' : '' ">
              <img :src="require('@/assets/outsourcedCourse/reward2.png')" alt="">
              <span class="span-text">学习奖励</span>
                <span class="right-position" style="margin-right: 28px;">还可解锁 <span style="color:#0052D9; font-weight: 500;">{{ unlockNum }}</span> 张</span>
              <span class="right-position back-up" @click="handlePack"><i :class="['el-icon-arrow-up', {'packup-arrow': packup}]"></i></span>
            </div>
          </div>
          <div v-show="!packup">
            <div class="stand-card-info-new-box">
              <div class="card-content" :class="getClass(index)" :style="bgiStyle(index)" v-for="(item, index) in 4" :key="item">
                <div class="type study-card-title" :class="{'stand-disable-class1': isDisableStyle(index)}" :style="isDisableStyle(index) ? '' : card_font_style('a')">「{{cardName}}」学霸卡</div>
                <span class="effect study-card-effect" :class="{'stand-disable-class2': isDisableStyle(index)}" :style="isDisableStyle(index) ? '' : card_font_style('b')">可用于兑换{{cardName}}课程</span>
                <div class="num-info">领取后7个自然日内有效</div>
                <div class="collect-btn-box">
                  <div class="collect-btn" :style="buttonColor(1)" v-if="(index) >= getNums && standCardCanGet" @click="throttleFn(2, true)" :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', `学习奖励-点击领取`)">
                    点击领取
                  </div>
                  <div class="collect-btn stand-btn-disabled"
                  :style="buttonColor(2, showText(index, 0))"
                  v-else-if="index < getNums && rewardList[index] && [0, 'consume', 'manage_deduct','deduct_expire',  undefined].includes(rewardList[index].verb_id)"
                  @click="throttleFn(2, false)" :dt-areaid="dtBotton('area')"
                  :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', `学习奖励-${showText(index, 0)}`)">
                    {{showText(index, 0)}}
                  </div>
                  <!-- 点击领取、已领取、抢光了 -->
                  <div class="collect-btn stand-btn-gray" v-else @click="throttleFn(2, false)" :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', `官方发放-${showText(index, 0)}`)">
                    {{ showText(index, 0) }}
                  </div>
                </div>
              </div>
            </div>
            <div class="card-info-common-tips" style="margin-top:12px;">
              1、通过课程学习最多可解锁<span style="color:#ff6600;font-weight:600">4张</span>学霸卡。2、学习课程中任意5个任务，每个任务学习时长不低于3分钟，即可解锁一张学霸卡。
            </div>
          </div>
            <!-- 他人赠送 -->
            <template v-if="Number(activityId) !== 1">
              <div class="given-by-others" style="margin-top: 20px;"  id="learning-reward-success" >
                <div class="official-mobile-img">
                  <img src="../../../../assets/img/mobile/geekBang/give-mobile.png" alt="">
                  <span class="span-text">他人赠送</span>
                  <span class="right-position" :class="packup ? 'right28' : '' ">还可获赠 <span style="color:#0052D9; font-weight: 500;">{{ 3 - giveCardDataList.length }}</span> 张</span>
                </div>
              </div>
          <div v-show="!packup">
              <div class="give-right card-stack-content" v-if="giveCardDataList.length">
                <div class="card-content give-card-content" :class="getClassGive(index + 1, giveCardDataList)" :style="bgiStyleGiveNew"  v-for="(item, index) in giveCardDataList" :key="index">
                  <div >
                    <div class="type"  :style="card_font_style('a')">「{{cardName}}」学霸卡</div>
                    <span class="effect" :style="card_font_style('b')">可用于兑换「{{cardName}}」课程</span>
                    <div class="num-info" >领取后7个自然日内有效</div>
                    <div class="collect-btn-box collect-btn-give">
                      <span >{{receiveRemark(item)}}赠送</span>
                      <!-- 可使用已使用已过期 -->
                      <div :style="buttonColor(2, showTextGive(index))" class="collect-btn stand-btn-disabled" type="primary" plain>
                        {{ showTextGive(index) }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="give-right no-data">
                <img :src="require('@/assets/outsourcedCourse/no-data-give.png')" alt="">
                <div class="data-text">暂无他人赠送</div>
              </div>
              <div class="card-info-common-tips">
                通过他人分享最多领取3张学霸卡，同一人分享的学霸卡仅可领取一次。
              </div>
          </div>
            </template>
          </div>
        </div>
      <div class="good-course-list" v-if="(courseList || []).length">
        <div class="top">
          <span class="title">立即兑换课程</span>
          <span class="link" @click="toActiveDetail" :dt-areaid="dtMoerCourses('area', '1')" :dt-eid="dtMoerCourses('eid', '1')" :dt-remark="dtMoerCourses('remark', '1')"> >>> 查看更多可兑换好课 </span>
        </div>
        <div class="list-content">
          <div class="item" v-for="(item, index) in courseList || []" :key="item.course_id" :dt-areaid="dtListBody(item, 'area', index)" :dt-eid="dtListBody(item , 'eid', index)" :dt-remark="dtListBody(item , 'remark', index)" @click="toCourseDetail(item.course_url)">
            <div class="cover">
              <!-- 走内容中心 -->
              <van-image class="cover-img" :src="getCourseCoverUrl(item.course_pic_id)">
                <template v-slot:error>
                  <img class="cover-img" src="../../../../assets/img/mobile/geekBang/err-cover-img.png" />
                </template>
              </van-image>
              <div class="time" v-if="item.course_length">
                {{ item.course_length }}分钟
              </div>
            </div>
            <div class="text-main">
              <div class="two-line">
                <span class="course-type">{{item.course_from_name}}</span>
                <span class="title">{{ item.course_name }}</span>
              </div>
              <div class="desc two-line">{{ processString(item.course_desc || '') }}</div>
              <!-- <div class="desc two-line" v-html="item.course_desc"></div> -->
            </div>
          </div>
        </div>
        <div class="link">
          <span @click="toActiveDetail" :dt-areaid="dtMoerCourses('area', '2')" :dt-eid="dtMoerCourses('eid', '2')" :dt-remark="dtMoerCourses('remark', '2')"> &gt;&gt;&gt; 查看更多可兑换好课 &lt;&lt;&lt; </span>
        </div>
      </div>
    </div>
    <!-- 领券详情弹窗 -->
    <van-popup class="card-detail-pop" v-model="showCardDetail" position="bottom" round closeable safe-area-inset-bottom>
      <div class="title">学霸卡「{{cardName}}」{{xueBaCardConfig.acct_type_code === 'xuebaCommon' ? '' : '专用卡' }}领用详情</div>
      <div style="overflow-y: auto;">
        <div class="table">
          <el-table :data="cardDetailList" :header-cell-style="headerCellStyle" style="width: 100%">
            <el-table-column label="序号" type="index" width="35" style="font-size:10px;">
            </el-table-column>
            <el-table-column label="领取时间" width="70">
              <template slot-scope="scope">
                <span class="table-ceel_content">{{scope.row.receive_time}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="" label="获得方式" width="45">
              <template slot-scope="scope">
                <span class="table-ceel_content">{{scope.row.receive_verb_name}}</span>
              </template>
            </el-table-column>
            <el-table-column label="卡券状态" width="55">
              <template slot-scope="scope">
                <span class="table-ceel_content" :class="getCardStyle(scope.row.verb_id)">
                  {{
                    getCardStatus(scope.row.verb_id)
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="说明">
              <template slot-scope="scope">
                <span class="table-ceel_content"> {{resolveDescData(scope.row)}}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="75">
              <template slot-scope="scope">
                <div class="operat-btn-box" v-if="!scope.row.verb_id">
                  <span @click="toManagePage()" type="primary" :underline="false">去兑换课程</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <div class="foot-page">
        <Pagination v-model="currentPage" :page-count="pageCount" mode="simple" @change="pageChange">
          <template #prev-text>
            <span class="prev-btn">
              <van-icon name="arrow-left" style="margin-right: 4px" /> 上一页
            </span>
          </template>
          <template #next-text>
            <span class="next-btn">
              下一页<van-icon name="arrow" style="margin-left: 4px" />
            </span>
          </template>
          <template #pageDesc>
            <span class="page-desc">
              <span class="current">{{ currentPage }}</span> / {{ pageCount }}
            </span>
          </template>
        </Pagination>
      </div>
    </van-popup>
    <!-- 已兑换课程 -->
    <exchangedMobile :isExchanged.sync="isExchanged" ></exchangedMobile>
    <!-- 送出记录 -->
    <xueba-send-record :isShow.sync="recordShow" :xueBaCardConfig="xueBaCardConfig"></xueba-send-record>
    <!-- 送出劝学卡 -->
    <!-- <giveCardMobile @handlerGiveXuebaka="handlerGiveXuebaka" :xueBaCardConfig="xueBaCardConfig" :isShowGive.sync="isShowGive" :giveNumber="presentRecordList.length" :numberOfRewards="numberOfRewards" :consumePoint="consumePoint"></giveCardMobile> -->
    <div class="share-model" v-show="isShowHint">
      <div class="model-content">
        <img src="../../../../assets/img/mobile/geekBang/share-right.png" alt="">
        <div class="model-footer">
          <span class="model-btn" @click="coloseShaerHint">知道了</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 移动端适配
import 'amfe-flexible/index.js'
import { mapState } from 'vuex'
import env from 'config/env.conf.js'
import translateLang from '@/views/mobile/mixins/translateLang.js'
import moment from 'moment'
import { Toast, Pagination } from 'vant'
import {
  getHomePageInfo,
  claimPoints,
  queryGeekRecord,
  getUserActiveInfo,
  getPresentRecord,
  messageSubscribe,
  messageUnsubscribe,
  getAcctinfos,
  getSubscribeStatus,
  activityPresent
  // getActiveOrders
} from '@/config/mooc.api.conf.js'
import { throttle } from '@/utils/tools.js'
// import giveCardMobile from './giveCardMobile.vue'
import exchangedMobile from './exchangedMobile.vue'
import GiveTab from './giveTab.vue'
import XuebaSendRecord from './xuebaSendRecord.vue'
const HTTPS_REG = /^https:\/\//
// 映射对象
const STATUS_MAP = {
  0: '待兑换',
  consume: '已兑换',
  deduct_expire: '已失效',
  manage_deduct: '已兑换'
  // manage_deduct: '已使用'
}
const STATUS_STYLE_MAP = {
  0: 'status-waite-use',
  consume: 'status-oready-used',
  deduct_expire: 'status-no-effict',
  manage_deduct: 'status-oready-used'
}
export default {
  name: 'activePage',
  mixins: [translateLang],
  components: {
    Pagination,
    // giveCardMobile,
    exchangedMobile,
    GiveTab,
    XuebaSendRecord
  },
  data() {
    return {
      rewardList: [],
      officialList: [],
      giveCardDataList: [],
      numberOfRewards: 0,
      usedNum: 0,
      overdueNum: 0,
      xuebaTotal: 0,
      consumePoint: 0,
      isShowGive: false,
      packup: true,
      isShowHint: false,
      isGetOfficial: false,
      isSub: false,
      recordShow: false,
      isExchanged: false,
      presentRewardList: [], // 报酬
      presentRecordList: [],
      configInfo: {}, // 配置信息
      courseList: [],
      xueBaCardConfig: {},
      cardInfo: {}, // 是否可领券、库存信息
      showCardDetail: false, // 领券弹窗是否显示
      currentPage: 1,
      pageSize: 5,
      pageCount: 0,
      cardDetailList: [], // 卡券详情列表
      cardDataList: [],
      swiperOption: {
        loop: true, // 是否循环轮播
        speed: 1000, // 切换速度
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        // 自动轮播
        autoplay: {
          delay: 5000,
          disableOnInteraction: false
        },
        // 设置slider容器能够同时显示的slides数量
        slidesPerView: 1,
        // 分页器
        pagination: {
          el: '.swiper-pagination',
          clickable: true // 允许点击小圆点跳转
        }
      }
    }
  },
  computed: {
    ...mapState(['userInfo']),
    //  可领取的数量
    limitNum() {
      let limitn = this.cardInfo.limit || 5
      return limitn - 1
    },
    cardName() {
      return this.xueBaCardConfig.card_name || ''
    },
    activityId() {
      return this.$route.query.activityId || 1
    },
    topImgUrl() {
      // 顶部图片
      const { banner_img_top } = this.xueBaCardConfig
      if (!banner_img_top) return
      return banner_img_top.banner_mobile
    },
    timeToDate() {
      return (val) => {
        let str = '--'
        if (val) {
          let timeArr = val.split(' ')[0].split('-')
          str = `${timeArr[0]}-${timeArr[1]}-${timeArr[2]}`
        }
        return str
      }
    },
    // 字体颜色
    card_font_style() {
      return (type) => {
        const { card_img_pc } = this.xueBaCardConfig
        if (!card_img_pc) return ''
        return `color:${
          type === 'a'
            ? card_img_pc.card_font_style_a
            : card_img_pc.card_font_style_b
        }`
      }
    },
    // 初始卡券 判断是否置灰 “学霸卡”和“可用于兑换外部付费好课”文字置灰
    isDisableStyle1() {
      if (this.initCardCanGet) {
        return false
      } else if (this.officialList.length && [0, 'consume', 'manage_deduct', undefined].includes(this.officialList[0].verb_id)
      ) {
        return false
      } else {
        return true
      }
    },
    // 通兑卡banner
    generalBanner() {
      return (type) => {
        const { banner_img_mid } = this.xueBaCardConfig
        if (!banner_img_mid) return
        return type === 'img'
          ? banner_img_mid[0].img_url_mobile
          : banner_img_mid[0].href
      }
    },
    unlockNum() {
      return 4 - this.getNums
    },
    initCardCanGet() {
      // 能否领取初始卡券
      return this.cardInfo.can_get_geek_num > 0
    },
    standCardCanGet() {
      // 能否领取达标卡券
      return this.cardInfo.can_get_reward_num > 0 && this.cardInfo.can_get_geek
    },
    // 已经领取的数量
    getNums() {
      return this.rewardList.length
    },
    // 领取顺序
    cardsOrder() {
      return JSON.parse(JSON.stringify(this.cardDataList)).reverse()
    },
    buttonColor() {
      return (type, text) => {
        const { card_img_pc } = this.xueBaCardConfig
        if (!card_img_pc) return ''
        if (text && text === '待使用') {
          return card_img_pc.button_style_a
        }
        if (type === 1) {
          return card_img_pc.button_style_a
        } else if (type === 2) {
          return card_img_pc.button_style_b
        }
      }
    },
    // 达标卡 背景图
    bgiStyle() {
      return (index) => {
        const { card_img_mobile } = this.xueBaCardConfig
        if (!card_img_mobile) return
        if (index >= this.getNums && this.standCardCanGet) {
          //  return `background-image: url(${require('@/assets/img/mobile/geekBang/card-stand-new1.png')});`
          return `background-image: url(${card_img_mobile.card_img_url_c});`
        } else if (index < this.getNums &&
          this.rewardList[index] &&
          [0, 'consume', 'manage_deduct', 'deduct_expire', undefined].includes(
            this.rewardList[index].verb_id
          )
        ) {
          //   return `background-image: url(${require('@/assets/img/mobile/geekBang/card-stand-new1.png')});`
          return `background-image: url(${card_img_mobile.card_img_url_c});`
        } else {
          //   return `background-image: url(${require('@/assets/img/mobile/geekBang/card-stand-no.png')});`
          return `background-image: url(${card_img_mobile.card_img_url_d});`
        }
      }
    },
    // 初始卡券 背景图
    bgiStyle1() {
      const { card_img_mobile } = this.xueBaCardConfig
      if (!card_img_mobile) return
      if (this.initCardCanGet) {
        // return `background-image: url(${require('@/assets/img/mobile/geekBang/stand-card-bg.png')});`
        return `background-image: url(${card_img_mobile.card_img_url_a});`
      } else if (this.officialList.length &&
        [0, 'consume', 'manage_deduct', 'deduct_expire', undefined].includes(
          this.officialList[0].verb_id
        )
      ) {
        // return `background-image: url(${require('@/assets/img/mobile/geekBang/stand-card-bg.png')});`
        return `background-image: url(${card_img_mobile.card_img_url_a});`
      } else {
        // return `background-image: url(${require('@/assets/img/mobile/geekBang/card-init-no-effict.png')});`
        return `background-image: url(${card_img_mobile.card_img_url_b});`
      }
    },
    bgiStyleGive() {
      return (num, list) => {
        let giveNum = list.length
        const { card_img_mobile } = this.xueBaCardConfig
        if (!card_img_mobile) return
        if (giveNum >= num) {
          return `background-color: #fff;background-image: url(${card_img_mobile.card_img_url_c});`
        } else {
          return `background-color: #fff;background-image: url(${card_img_mobile.card_img_url_d});`
        }
      }
    },
    bgiStyleGiveNew() {
      const { card_img_mobile } = this.xueBaCardConfig
      if (!card_img_mobile) return
      return `background-image: url(${card_img_mobile.card_img_url_a});`
    },
    headerCellStyle() {
      return {
        background: '#F5F5F5',
        color: '#00000099',
        fontSize: '12px',
        fontWeight: '500'
      }
    },
    // 按钮显示的文字
    showText() {
      return (index, type) => {
        let list = type ? this.officialList : this.rewardList
        let statusMap = {
          0: '待使用',
          consume: '已使用',
          manage_deduct: '已使用',
          deduct_expire: '已过期'
          // manage_deduct: '已使用',
        }
        if (list[index]) {
          let res = list[index].verb_id
          return statusMap[res || 0]
        } else if (!this.cardInfo.quantity) {
          return '抢光了'
        } else {
          return '点击领取'
        }
      }
    },
    receiveRemark() {
      return (item) => {
        let { receive_remark = '', receive_time } = item
        let str = receive_remark.split('赠送')[0]
        return str + this.$moment(receive_time).format('MM月DD日')
      }
    },
    // 他人赠送的
    showTextGive() {
      return (index, type) => {
        let statusMap = {
          0: '待使用',
          consume: '已使用',
          manage_deduct: '已使用',
          deduct_expire: '已过期'
          // manage_deduct: '已使用',
        }
        let list = type === 'reward' ? this.presentRewardList : this.giveCardDataList
        if (list[index]) {
          let res = list[index].verb_id
          return statusMap[res || 0]
        }
      }
    },
    dtBotton() {
      return (type, name) => {
        const { audience_id, audience_name, activity_name } = this.xueBaCardConfig
        if (type === 'area') {
          return `area_${audience_id}_receive`
        } else if (type === 'eid') {
          return `element_${audience_id}_receive`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${activity_name}活动落地页`,
            page_type: `学霸卡活动落地页`,
            container: audience_name,
            container_id: '',
            click_type: 'button',
            content_name: name,
            terminal: 'H5'
          })
        }
      }
    },
    dtMoerCourses() {
      return (type, index) => {
        const { audience_id, audience_name, activity_name } = this.xueBaCardConfig
        if (type === 'area') {
          return `area_${audience_id}_more_${index}`
        } else if (type === 'eid') {
          return `element_${audience_id}_more_${index}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${activity_name}活动落地页`,
            page_type: `学霸卡活动落地页`,
            container: audience_name,
            container_id: '',
            click_type: 'button',
            content_name: '查看更多可兑换好课' + index,
            terminal: 'H5'
          })
        }
      }
    },
    // 埋点
    dtListBody() {
      return (item, type, index) => {
        const { activity_name } = this.xueBaCardConfig
        let dt_id = `${item.course_from}_${item.course_id}`
        if (type === 'area') {
          return `area_${dt_id}`
        } else if (type === 'eid') {
          return `element_${dt_id}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${activity_name}活动落地页`,
            page_type: `学霸卡活动落地页`,
            container:
              index <= 7
                ? `${this.xueBaCardConfig.audience_name}_8`
                : `${this.xueBaCardConfig.audience_name}_16`,
            container_id: '',
            click_type: 'data',
            content_type: '培养项目',
            act_type: '11',
            content_id: item.course_id,
            content_name: item.course_name,
            terminal: 'H5'
          })
        }
      }
    }
  },
  watch: {
    'userInfo.staff_id': {
      async handler(newValue) {
        if (newValue) {
          await this.getUserActiveInfo()
          await this.getHomePageInfoFn()
          await this.getCardAgeAndUsedDetail()
          // 获取数据判断是不是已经领取官方发放
          await this.getCardAgeAndUsedDetail(100)
          await this.getCardDataList()
          await this.presentReward() // 报酬
          await this.getSubscribeStatus()
          // 赠送的
          await this.getPresentRecord()
          // 获赠的卡片
          await this.getGiveCard()
          await this.getAcctinfos()
          // await this.getActiveOrders()
          // 如果是分享链接直接调用赠送劝学卡
          console.log(
            this.$route.query,
            'this.$route.querythis.$route.querythis.$route.query'
          )
          if (this.$route.query.staff_id && this.$route.query.staff_name) {
            this.activityPresent()
          }
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    // 邀请劝学
    this.$nextTick(() => {
      if (!this.$route.query.position) return
      setTimeout(() => {
        const element = document.getElementById('invite-main_id')
        if (element) {
          console.log(element)
          element.scrollIntoView({ behavior: 'smooth' })
        }
      }, 2000)
    })
  },
  methods: {
    handlePack() {
      this.packup = !this.packup
    },
    openSeeRecord() {
      this.recordShow = true
    },
    // 获取他人赠送的卡券
    activityPresent() {
      let params = {
        from: this.$route.query.staff_id,
        from_name: this.$route.query.staff_name,
        acct_type_code: this.xueBaCardConfig.acct_type_code,
        to_batch: [this.userInfo.staff_id],
        object_id: this.xueBaCardConfig.activity_id,
        object_name: this.xueBaCardConfig.activity_name,
        object_type: 'XuebaActivity',
        object_type_name: '活动',
        trans_amt: '1',
        notify_type: 0 // 手动赠送1 ，用户进入页面自动领的 0
      }
      activityPresent(params).then((res) => {
        if (res.success_count) {
          let num = 3 - this.giveCardDataList.length - 1
          Toast({
            message: `学霸卡领取成功。您通过他人赠送还可以再领取${num}学霸卡`,
            icon: 'success',
            duration: 3000
          })
          setTimeout(() => {
            const element = document.getElementById('learning-reward-success')
            if (element) {
              console.log(element)
              element.scrollIntoView({ behavior: 'smooth' })
            }
          }, 800)
        }
        if (res.fail_count) {
          let isRepeat = res.fail_names.includes('您已领取过')
          if (isRepeat) {
            // let str = res.fail_names.split('[')
            // let newStr = str[1].replace(/]/g, '')
            Toast({
              message: '您已领取过该同事赠送的学霸卡',
              icon: 'fail',
              duration: 3000
            })
          } else if (this.giveCardDataList.length === 3) {
            // 学霸卡领取失败。您收到他人分享的学霸卡已达上限（最多3张）。
            Toast({
              message: '学霸卡领取失败。您收到他人赠送的学霸卡已达上限（最多3张）。',
              icon: 'fail',
              duration: 5000
            })
          }
          // else {
          //   Toast({
          //     message: res.fail_names,
          //     icon: 'fail',
          //     duration: 3000
          //   })
          // }
        }
        console.log(res, '赠送接口')
        this.getAcctinfos()
        this.getHomePageInfoFn()
        this.getGiveCard()
      })
    },
    // 获取已经使用卡券数量
    // getActiveOrders() {
    //   let params = {
    //     current: 1,
    //     size: 5,
    //     sourceFrom: this.xueBaCardConfig.acct_type_code
    //   }
    //   getActiveOrders(params).then((res) => {
    //     console.log('usedNum', res)
    //     this.usedNum = res.total
    //   })
    // },
    // 领取和使用详情 - 去兑换课程
    toManagePage() {
      let encodeUrl = encodeURIComponent('https://sdc.qq.com/s/sYkoua?scheme_type=homepage&page_id=503')
      let url = '/pages/webview/index?href=' + encodeUrl
      window.wx.miniProgram.navigateTo({ url })
    },
    async getSubscribeStatus() {
      let res = await getSubscribeStatus(this.xueBaCardConfig.acct_type_code)
      this.isSub = res
    },
    messageSubscribe() {
      if (!this.isSub) {
        messageSubscribe({}, this.xueBaCardConfig.acct_type_code).then(
          (res) => {
            this.getSubscribeStatus()
            Toast({
              message: '订阅成功',
              icon: 'success'
            })
          }
        )
      } else {
        messageUnsubscribe({}, this.xueBaCardConfig.acct_type_code).then(
          (res) => {
            this.getSubscribeStatus()
            Toast({
              message: '退订成功',
              icon: 'success'
            })
          }
        )
      }
    },
    async getAcctinfos() {
      let params = {
        acct_type_codes: this.xueBaCardConfig.acct_type_code + 'Trans'
      }
      const result = await getAcctinfos(params)
      //   查看有没有余额有余额解锁赠送劝学卡
      this.consumePoint = Number(result.consume_point)
    },
    openBanner() {
      window.open(this.generalBanner('href'))
    },
    openGive() {
      if (this.consumePoint === 0) return
      this.isShowGive = true
    },
    // 达标卡 右上角图片
    getClass(index) {
      if (index >= this.getNums && this.standCardCanGet) {
        return `` // 待领取
      } else if (
        index < this.getNums &&
        this.rewardList[index] &&
        [0, 'consume', 'manage_deduct', undefined].includes(
          this.rewardList[index].verb_id
        )
      ) {
        if ([0, undefined].includes(this.rewardList[index].verb_id)) {
          return `card-content-1` // 待兑换
        } else {
          return `card-content-2` // 已兑换
        }
      } else {
        if (
          this.rewardList[index] &&
          this.rewardList[index].verb_id === 'deduct_expire'
        ) {
          return `card-content-4` // 已失效
        } else {
          return `card-content-3` // 未解锁
        }
      }
    },
    // 初始卡 右上角图片
    getClass1() {
      if (this.initCardCanGet) {
        return `` // 待领取
      } else if (this.officialList.length && [0, 'consume', 'manage_deduct', undefined].includes(this.officialList[0].verb_id)
      ) {
        if ([0, undefined].includes(this.officialList[0].verb_id)) {
          return `card-content-1` // 待兑换
        } else {
          return `card-content-2` // 已兑换
        }
      } else {
        return `card-content-4` // 已失效
      }
    },
    //  他人赠送的, 获得奖励 右上角图片
    getClassGive(num, list) {
      let index = num - 1
      let giveNum = list.length
      if (
        giveNum >= num &&
        list[index] &&
        [0, 'consume', 'manage_deduct', undefined].includes(
          list[index].verb_id
        )
      ) {
        if ([0, undefined].includes(list[index].verb_id)) {
          return `card-content-1` // 待兑换
        } else {
          return `card-content-2` // 已兑换
        }
      } else {
        if (list[index] && list[index].verb_id === 'deduct_expire') {
          return `card-content-4` // 已失效
        } else {
          return `card-content-3` // 未解锁
        }
      }
    },
    // 达标卡 判断是否置灰 “学霸卡”和“可用于兑换外部付费好课”文字置灰
    isDisableStyle(index) {
      if (index >= this.getNums && this.standCardCanGet) {
        return false
      } else if (
        index < this.getNums &&
        this.rewardList[index] &&
        [0, 'consume', 'manage_deduct', undefined].includes(
          this.rewardList[index].verb_id
        )
      ) {
        return false
      } else {
        return true
      }
    },
    /**
     * 字符串去除标签类型的字符等
     * @param {string} str
     * @returns {string}
     */
    processString(str) {
      if (!str || typeof str !== 'string') {
        return ''
      }
      // 去除HTML标签
      str = str.replace(/<[^>]*>/g, '')
      // 去除特殊字符
      str = str.replace(/&nbsp;/g, ' ')
      str = str.replace(/&amp;/g, '&')
      str = str.replace(/&lt;/g, '<')
      str = str.replace(/&gt;/g, '>')
      // 可以继续添加其他特殊字符的替换规则
      return str
    },
    // 课程封面 内容中心逻辑
    getCourseCoverUrl(course_pic_id) {
      if (HTTPS_REG.test(course_pic_id)) {
        return course_pic_id
      }
      const envName = env[process.env.NODE_ENV]
      return `${envName.contentcenter}content-center/api/v1/content/imgage/${course_pic_id}/preview`
    },
    initData() {
      // 更新卡券数据
      this.getHomePageInfoFn()
      this.getCardAgeAndUsedDetail()
      this.getCardDataList()
      // 后端异步需要延后调用
      setTimeout(() => {
        this.getAcctinfos()
      }, 1000)
      this.isGetOfficial = true
    },
    // 获取学霸卡基础信息
    async getUserActiveInfo() {
      try {
        let res = await getUserActiveInfo({ activity_id: this.activityId })
        console.log(res, '获取学霸卡基础信息')
        this.xueBaCardConfig = res
        this.courseList = res.course_list || []
        // 活动过期提示
        const end_time = res.end_time
        console.log(end_time, 'end_time--')
        if (end_time && moment(end_time).isBefore(moment(new Date()))) {
          Toast(`${res.activity_name}活动已结束，敬请关注后续活动`)
        }
      } catch (error) {
        console.log('获取基础信息: ', error)
      }
    },
    // 赠送的
    async getPresentRecord() {
      let params = {
        activityId: this.xueBaCardConfig.activity_id,
        current: 1,
        size: 10
      }
      const res = await getPresentRecord(params)
      console.log(res, '赠送')
      this.presentRecordList = res.records || []
      //   获得奖励的有多少张
      let presentFilterList = this.presentRecordList.filter(
        (item) => item.reward_amt
      )
      this.numberOfRewards = presentFilterList.length
    },
    // 赠送劝学卡后刷新赠送列表
    handlerGiveXuebaka() {
      this.getHomePageInfoFn()
      this.getPresentRecord()
      this.getCardAgeAndUsedDetail()
      this.getCardDataList()
      this.getAcctinfos()
    },
    // 积分授予-查询是否可以领取geek
    async getHomePageInfoFn() {
      try {
        let res = await getHomePageInfo({
          staff_id: this.userInfo.staff_id,
          acct_type_code: this.xueBaCardConfig.acct_type_code,
          activity_id: this.activityId
        })
        this.cardInfo = res
      } catch (error) {
        console.error('积分授予-查询是否可以领取geek-error: ', error)
      }
    },
    // 发请求领取积分卡
    async claimPointsFn(grant_type) {
      try {
        let payload = {
          staff_id: this.userInfo.staff_id,
          staff_name: this.userInfo.staff_name,
          acct_type_code: this.xueBaCardConfig.acct_type_code,
          grant_amt: '1',
          grant_type: grant_type,
          busi_id: this.xueBaCardConfig.activity_id
        }
        await claimPoints(payload)
        // 领取成功
        Toast({
          message: '领取成功',
          icon: 'passed'
        })
        // 初始化数据
        this.initData()
      } catch (error) {
        // 领取失败
        Toast({
          message: '领取失败',
          icon: 'close'
        })
        console.error('发请求领取积分卡---error: ', error)
      }
    },
    // 获取学霸卡领取和使用详情
    async getCardAgeAndUsedDetail(size) {
      try {
        let careteSize = size
        // 发请求获取分页数据
        let payload = {
          staff_id: this.userInfo.staff_id,
          acct_type_code: this.xueBaCardConfig.acct_type_code,
          current: this.currentPage,
          activity_id: this.xueBaCardConfig.activity_id,
          size: careteSize || this.pageSize
        }
        let res = await queryGeekRecord(payload)
        if (careteSize) {
          let list = res.records || []
          this.isGetOfficial = list.some((item) => item.receive_verb_id === 'receive')
          this.usedNum = list.filter(item => (item.verb_id === 'consume' || item.verb_id === 'manage_deduct')).length
          this.overdueNum = list.filter(item => item.verb_id === 'deduct_expire').length
          return
        }
        this.cardDetailList = res.records || []
        this.xuebaTotal = res.total
        this.pageCount = res.pages
      } catch (error) {}
    },
    // 赠送获得的奖励卡券报酬
    presentReward() {
      queryGeekRecord({
        staff_id: this.userInfo.staff_id,
        acct_type_code: this.xueBaCardConfig.acct_type_code,
        current: 1,
        size: 10,
        receive_verb_id: 'present_reward',
        activity_id: this.activityId
      }).then((res) => {
        this.presentRewardList = res.records
        console.log(res, '赠送获得的奖励卡券报酬')
      })
    },
    // 获取五张自主领取卡券
    async getCardDataList() {
      try {
        // 发请求获取分页数据
        let payload = {
          staff_id: this.userInfo.staff_id,
          acct_type_code: this.xueBaCardConfig.acct_type_code,
          current: this.currentPage,
          activity_id: this.xueBaCardConfig.activity_id,
          receive_verb_id: 'receive',
          size: this.pageSize
        }
        let res = await queryGeekRecord(payload)
        this.cardDataList = res.records || []
        console.log('获取五张自主领取卡券', this.cardDataList)
        this.rewardList = res.records.filter(item => item.busi_code === 'reward')
        console.log('rewardListrewardList', this.rewardList)
        this.officialList = res.records.filter(item => item.busi_code === this.xueBaCardConfig.acct_type_code)
      } catch (error) {}
    },
    // 三张他人赠送的
    getGiveCard() {
      queryGeekRecord({
        staff_id: this.userInfo.staff_id,
        acct_type_code: this.xueBaCardConfig.acct_type_code,
        current: 1,
        size: 10,
        receive_verb_id: 'present_passive',
        activity_id: this.activityId
      }).then((res) => {
        this.giveCardDataList = res.records
        console.log(res, '他人赠送···············')
      })
    },
    // 点击领取卡券 节流方法
    throttleFn: throttle(function (code, status) {
      this.getCoupon(code, status)
    }, 500),
    // 点击领取卡券
    getCoupon(type, status) {
      switch (type) {
        case 1:
          // 初始卡
          if (status) {
            this.claimPointsFn(this.xueBaCardConfig.acct_type_code)
          } else {
            // hasGetGeek-是否已经领取，canGetGeekNum-可领取的兑换券的数量，formalStaff-是否正式员工，totalBalance-已领取数量，limit-最大限制数量
            const {
              has_get_geek: hasGetGeek,
              can_get_geek_num: canGetGeekNum,
              formal_staff: formalStaff,
              total_balance: totalBalance,
              limit
            } = this.cardInfo
            if (!formalStaff) {
              Toast('非集团本部正式员工不可领取')
            } else if (totalBalance >= limit) {
              Toast('已达到领取数量上限')
            } else if (!hasGetGeek && canGetGeekNum <= 0) {
              Toast(
                '本轮学霸卡已发放完毕，暂时无法领取，敬请期待后续轮次的发放活动'
              )
            } else if (hasGetGeek) {
              Toast(
                '你本轮次已领取过学霸卡，每人限领1次，敬请期待后续轮次的发放活动'
              )
            } else {
            }
          }
          break
        case 2:
          // 达标卡
          if (status) {
            this.claimPointsFn('reward')
          } else {
            // hasGetGeekReward-是否已经领取，canGetRewardNum-可领取的兑换券的数量，canGetGeek-是否可以领取，formalStaff-是否正式员工，totalBalance-已领取数量，limit-最大限制数量
            const {
              has_get_geek_reward: hasGetGeekReward,
              can_get_reward_num: canGetRewardNum,
              can_get_geek: canGetGeek,
              formal_staff: formalStaff,
              total_balance: totalBalance,
              limit
            } = this.cardInfo
            let canBeClaimed = canGetRewardNum > 0 && canGetGeek
            if (!formalStaff) {
              Toast('非集团本部正式员工不可领取')
            } else if (totalBalance >= limit) {
              Toast('已达到领取数量上限')
            } else if (!canBeClaimed) {
              Toast('暂无领取资格，请查看活动规则并完成学习条件后再点击领取')
            } else if (!hasGetGeekReward && canGetRewardNum <= 0) {
              Toast(
                '本轮学霸卡已发放完毕，暂时无法领取，敬请期待后续轮次的发放活动'
              )
            } else if (hasGetGeekReward) {
              Toast(
                '你本轮次已领取过学霸卡，每人限领1次，敬请期待后续轮次的发放活动'
              )
            } else {
            }
          }
          break
        default:
          break
      }
    },
    // 处理列表的"说明"字段
    resolveDescData(item) {
      if (item.verb_id === 'consume' || item.verb_id === 'manage_deduct') {
        return `使用时间：${this.resolveTime(item.deduct_time)}`
      } else if (item.verb_id === 'deduct_expire') {
        return `失效时间：${this.resolveTime(item.expire_time)}`
      } else {
        return `领取7天后，卡券失效，请及时使用`
      }
    },
    // 打开领用详情弹窗
    openDetailPop() {
      this.showCardDetail = true
    },
    // 券领取详情分页改变
    pageChange(page) {
      this.currentPage = page
      this.getCardAgeAndUsedDetail()
    },
    // 获取卡券状态
    getCardStatus(code = 0) {
      let statusText = STATUS_MAP[code] || STATUS_MAP[0]
      return statusText
    },
    // 获取券状态样式
    getCardStyle(code = 0) {
      let statusStyle = STATUS_STYLE_MAP[code] || STATUS_STYLE_MAP[0]
      return statusStyle
    },
    // 点击顶部图片跳转
    goTo() {
      let url = this.configInfo.banner?.banner_pic_go_url || ''
      if (url) {
        window.open(url)
      }
    },
    // 查看更多可兑换好课 外部好课专区
    toActiveDetail() {
      let encodeUrl = encodeURIComponent(this.xueBaCardConfig.course_more_link)
      let url = '/pages/webview/index?href=' + encodeUrl
      window.wx.miniProgram.navigateTo({ url })
    },
    // 活动规则
    toRulesDetail() {
      let encodeUrl = encodeURIComponent(
        this.xueBaCardConfig.activity_detail_link
      )
      let url = '/pages/webview/index?href=' + encodeUrl
      window.wx.miniProgram.navigateTo({ url })
    },
    // 已兑课程
    toMooc() {
      this.isExchanged = true
      // window.wx.miniProgram.navigateTo({
      //   url: `/pages/mooc/myProject/index`
      // })
    },
    // 跳转到课程详情
    toCourseDetail(href) {
      let encodeUrl = encodeURIComponent(href)
      let url = '/pages/webview/index?href=' + encodeUrl
      window.wx.miniProgram.navigateTo({ url })
      //   window.wx.miniProgram.reLaunch({
      //     url: href
      //   })
    },
    // 去兑换课程
    // toExchangeCourse () {
    //   window.wx.miniProgram.reLaunch({
    //     url: `/pages/mooc/projectList/index`
    //   })
    // }
    // 处理时间数据
    resolveTime(time) {
      if (!time) return ''
      let srt = time.split(':').slice(0, -1).join(':')
      return srt.replace(/-/g, '/')
    },
    coloseShaerHint() {
      this.isShowHint = false
    }
  }
}
</script>

<style lang="less" scoped>
* {
  box-sizing: border-box;
}
.active-page {
  overflow-y: auto;
  height: 100%;
  background: #f4faff;
  padding-bottom: 21px;
  .top-img {
    width: 100%;
  }
  .give-explain {
    background-color: #fff;
    border-radius: 16px;
    margin-bottom: 12px;
    &-title {
      color: #000000e6;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      margin-bottom: 12px;
    }
    &-content {
      color: #00000066;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      // font-weight: 500;
      line-height: 22px;
    }
  }
  .content-main {
    position: relative;
    padding: 0 16px;
    margin: -46px auto 0;
    & > div {
      padding: 20px;
      border-radius: 16px;
      background-color: #fff;
      box-shadow: 0 0 8px 0 #b5d0e30a;
      &:nth-child(n + 1) {
        margin-top: 12px;
      }
    }
    .general-banner {
      height: 100px;
      width: 100%;
      padding: 0;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .give-box-mobile {
      padding-top: 16px;
      margin-top: 16px;
      border-top: 1px solid #eee;
      background: #fff;
      .shear-card-content {
        position: relative;
        height: 127px;
        margin: 16px 0;
        .shear-card-item {
          position: absolute;
          width: 162px;
          height: 127px;
        }
        .shear-card-position-1 {
          left: 0;
          z-index: 100;
          box-shadow: 4px 2px 5px -5px rgba(0, 0, 0, 0.4);
        }
        .shear-card-position-2 {
          left: 50%;
          transform:translateX(-50%);
          z-index: 99;
          box-shadow: 4px 2px 5px -5px rgba(0, 0, 0, 0.4);
        }
        .shear-card-position-3 {
          right: 0;
          z-index: 98;
          box-shadow: 4px 2px 5px -5px rgba(0, 0, 0, 0.4);
        }
      }
    }
    .color-cupton-num {
      color: #ed7b2f !important;
    }
    .my-card-info {
      .top {
        display: flex;
        align-items: baseline;
        font-size: 14px;
        line-height: 24px;
      }
      .title {
        position: relative;
        color: #000000e6;
        font-weight: 500;
        // height: 24px;
        line-height: 24px;
        font-weight: 600;
        // background: url('../../../../assets/img/mobile/geekBang/bd.png') no-repeat 0 18px;
      }
      .num {
        // color: #0052d9;
        color: #0052d9;
        // font-size: 18px;
      }
      .color-6600 {
        color: #ff6600;
      }
      .date {
        padding-left: 16px;
        font-size: 12px;
        color: #00000066;
        span {
          color: #0052d9;
        }
      }
      .detail-link {
        margin-left: 16px;
        color: #0052d9;
        text-decoration-line: underline;
        cursor: pointer;
      }
      .btns {
        margin-top: 16px;
        display: flex;
        justify-content: space-between;
      }
      .btn {
        font-size: 14px;
        color: #0052d9;
        font-weight: 500;
        width: 93px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: 6px;
        cursor: pointer;
        background: var(---Brand1-Light, #ecf2fe);
      }
    }
    .official-mobile-img {
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      position: relative;
      img {
        width: 28px;
        height: 28px;
      }
      .span-text {
        color: #000000e6;
        font-family: 'PingFang SC';
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        padding-left: 8px;
      }
      .right-position {
        position: absolute;
        right: 0;
        color: #00000099;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
      .right28 {
        right: 28px;
      }
      .back-up {
        width: 18px;
        height: 18px;
        background-color: #639effff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        /deep/.el-icon-arrow-up {
          color: #fff;
          font-size: 12px;
        }
        .packup-arrow {
          transform: rotateX(180deg);
        }
      }
    }
    .card-info-common {
      .subscription {
        display: flex;
        align-items: flex-end;
        margin-bottom: 16px;
        .subscription-btn {
          display: flex;
          height: 32px;
          padding: 0 8px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          border-radius: 6px;
          background: #0052d9;
          color: #fff;
          font-size: 14px;
          img {
            width: 16px;
            height: 16px;
          }
        }
        &-tips {
          color: #00000066;
          font-family: 'PingFang SC';
          font-size: 10px;
          font-style: normal;
          font-weight: 500;
          line-height: 16px;
          padding-left: 8px;
        }
      }

      .card-content {
        height: 127px;
        padding: 12px;
        background-size: 100% 100%;
        color: #805524;
      }
      .type {
        font-size: 14px;
        font-weight: 600;
        line-height: 22px;
        font-family: 'Source Han Sans CN';
        // color: #573a18;
        color: #805524;
        display: flex;
        align-items: center;
        img {
          margin-left: 4px;
        }
      }
      .study-card-title {
        font-size: 12px;
      }
      .effect {
        margin-top: 2px;
        padding-bottom: 6px;
        font-size: 12px;
        line-height: 16px;
        border-bottom: 1px dashed #90714b;
        // border-bottom-color: #90714b;
      }
      .study-card-effect {
        font-size: 10px;
      }
      .num-info {
        margin-top: 10px;
        color: #ff6600;
        font-size: 10px;
        line-height: 16px;
      }
      .left {
        display: flex;
        flex-direction: column;
        font-size: 12px;
        line-height: 16px;
        max-width: 158px;
        span:last-child {
          margin-top: 4px;
        }
      }
      .collect-btn-box {
        margin-top: 13px;
        display: flex;
        justify-content: flex-end;
      }
      .collect-btn-give {
        justify-content: space-between;
        align-items: center;
        span {
          font-size: 12px;
          color: #333;
        }
      }
      .collect-btn {
        width: 72px;
        height: 22px;
        line-height: 22px;
        color: #ffffff;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        border-radius: 64px;
        cursor: pointer;
      }
      .desc {
        margin-top: 12px;
        color: #00000099;
        font-size: 14px;
        line-height: 22px;
      }
    }
    .init-card-info-new {
      .card-stack-content {
        position: relative;
        z-index: 3;
        margin-bottom: 16px;
      }
      .stack1 {
        width: calc(100% - 18px);
        height: 127px;
        background-color: #d5d5d5;
        position: absolute;
        z-index: 2;
        border-radius: 8px;
        bottom: -6px;
        left: 9px;
      }
      .stack2 {
        width: calc(100% - 36px);
        height: 125px;
        background-color: #e6e6e6;
        position: absolute;
        z-index: 1;
        bottom: -12px;
        left: 18px;
        border-radius: 8px;
      }
      .card-content {
        position: relative;
        z-index: 22;
        background-image: url('../../../../assets/img/mobile/geekBang/stand-card-bg.png');
        &::after {
          // 待领取
          position: absolute;
          content: '';
          right: 0;
          top: 0;
          width: 40px;
          height: 40px;
          background: url('../../../../assets/img/mobile/geekBang/wait-get.png')
            no-repeat 0 0;
          background-size: 100%;
          z-index: 1;
        }
        .init-disable-class1 {
          color: #00000099;
        }
        .init-disable-class2 {
          color: #00000066;
        }
      }
      .type {
        .geek-font {
          display: inline-block;
          padding: 0 5px;
          height: 16px;
          border: 1px solid #ab8143;
          color: #ab8143;
          line-height: 15px;
          border-radius: 8px;
          font-size: 8px;
          text-align: center;
          margin-left: 4px;
        }
      }
      .effect {
      }
      .collect-btn {
        background: linear-gradient(276deg, #8b5300 14.56%, #ae864a 95.9%);
      }
      .stand-btn-disabled {
        background: linear-gradient(276deg, #eac896 14.56%, #eedcb9 95.9%);
      }
      .stand-btn-gray {
        background: linear-gradient(276deg, #d4d4d4 14.56%, #d8d8d8 95.9%);
      }
      .card-content-1::after {
        // 待兑换
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-1.png');
      }
      .card-content-2::after {
        // 已兑换
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-2.png');
      }
      .card-content-3::after {
        // 未解锁
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-3.png');
      }
      .card-content-4::after {
        // 已失效
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-4.png');
      }
    }
    .given-by-others-content {
    }
    .given-by-others {
      background-color: #fff;
      -webkit-box-shadow: 0 0 8px 0 #b5d0e30a;
      box-shadow: 0 0 8px 0 #b5d0e30a;
    }
    .give-right {
    }
    .give-card-content {
      margin-bottom: 12px;
    }
    .no-data {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: #00000066;
      margin-bottom: 12px;
    }
    .card-info-common-tips {
      position: relative;
      margin-top: 4px;
      padding: 8px 12px;
      border-radius: 4px;
      background: #f8f8f8;
      align-self: stretch;
      color: #00000099;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .see-record-text {
      text-align: center;
      color: #0052D9;
      line-height: 22px;
      margin-top: 16px;
    }
    .stand-card-info-new-box {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;

      .card-content {
        position: relative;
        width: 146px;
        height: 127px;
        border-radius: 10px;
        margin-bottom: 16px;
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-new1.png');
        // &::after {
        //   // 待领取
        //   position: absolute;
        //   content: '';
        //   right: 0;
        //   top: 0;
        //   width: 40px;
        //   height: 40px;
        //   background: url('../../../../assets/img/mobile/geekBang/wait-get.png')
        //     no-repeat 0 0;
        //   background-size: 100%;
        //   z-index: 1;
        // }
        .stand-disable-class1 {
          color: #00000099;
        }
        .stand-disable-class2 {
          color: #00000066;
        }
      }
      .card-content-1::after {
        // 待兑换
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-1.png');
      }
      .card-content-2::after {
        // 已兑换
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-2.png');
      }
      .card-content-3::after {
        // 未解锁
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-3.png');
      }
      .card-content-4::after {
        // 已失效
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-4.png');
      }
    }
    .text-box {
      margin-top: 16px;
      color: #00000099;
      font-size: 14px;
      line-height: 22px;
      .text-title {
        color: #00000099;
        font-weight: 500;
      }
      .warm {
        color: #ed7b2f;
      }
      .text-attention {
        color: #ed7b2f;
        font-weight: 500;
      }
    }

    .good-course-list {
      padding: 20px;
      border-radius: 16px;
      background: #fff;
      box-shadow: 0 0 8px 0 #b5d0e30a;
      .top {
        .title {
          color: #000000e6;
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
        }
        .link {
          margin-left: 20px;
          color: #0052d9;
          font-size: 14px;
          line-height: 22px;
          text-decoration-line: underline;
        }
      }
      .list-content {
        margin-top: 12px;
        .item {
          display: flex;
          // align-items: center;
          border-top: 0.5px solid #eee;
          padding: 12px 0;
          .cover {
            position: relative;
            margin-right: 12px;
            border-radius: 3px;
          }
          /deep/.van-image__error,
          /deep/ .van-image__img,
          /deep/.van-image__loading {
            border-radius: 3px;
          }
          .cover-img {
            width: 129px;
            height: 86px;
            border-radius: 3px;
            img {
              border-radius: 3px;
            }
          }
          .time {
            position: absolute;
            right: 8px;
            bottom: 8px;
            padding: 2px 12px;
            height: 20px;
            line-height: 20px;
            color: #ffffff;
            font-size: 12px;
            border-radius: 12px;
            background: #00000066;
          }
          .text-main {
            .course-type {
              margin-right: 5px;
              padding: 3px 4px;
              color: #0052d9;
              font-size: 12px;
              line-height: 12px;
              border-radius: 2px;
              background: #ebeffc;
            }
            .title {
              color: #000000e6;
              font-size: 14px;
              line-height: 22px;
            }
            .desc {
              margin-top: 2px;
              height: 40px;
              color: #00000099;
              font-size: 12px;
              line-height: 20px;
            }
          }
        }
      }
      & > .link {
        text-align: center;
        span {
          cursor: pointer;
          color: #0052d9;
          font-size: 14px;
          line-height: 22px;
          text-decoration-line: underline;
        }
      }
    }
  }
  .one-line {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .two-line {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /*显示两行*/
  }
}
.card-detail-pop {
  //   max-height: 396px;
  padding: 16px 0;
  .title {
    color: #000000e6;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    line-height: 26px;
  }
  .table {
    margin-top: 16px;
    .table-title {
      display: flex;
      height: 38px;
      line-height: 38px;
      color: #00000099;
      font-size: 12px;
      font-weight: 500;
      background-color: #f5f5f5;
      & > div {
        padding-left: 16px;
      }
    }
    /deep/.el-table__row {
      font-size: 10px;
    }
    .table-ceel_content {
      color: #000000e6;
      font-family: 'PingFang SC';
      font-size: 10px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
    }
    .operat-btn-box {
      font-size: 10px;
      span {
        color: #0052D9;
      }
    }
    .item-content {
      max-height: 300px;
      overflow-y: auto;
    }
    .item {
      display: flex;
      color: #000000e6;
      height: 60px;
      line-height: 60px;
      box-shadow: 0 -1px 0 0 #eee inset;
      & > div {
        font-size: 10px;
        padding-left: 14px;
        &:nth-child(n + 3) {
          padding-left: 16px;
        }
      }
    }
    // .no {
    //   width: 56px;
    // }
    .time {
      width: 99px;
    }
    .status {
      width: 70px;
    }
    .status-text {
      line-height: 16px;
      text-align: center;
      border-radius: 4px;
      padding: 1px 8px;
      font-size: 10px;
    }
    .status-no-effict {
      color: #ed7b2f;
      border: 1px solid var(---Warning5-Normal, #ed7b2f);
      background: var(---Warning1-Light, #fef3e6);
    }
    .status-waite-use {
      color: #0052d9;
      border: 1px solid var(---Brand8-Normal, #0052d9);
      background: var(---Brand1-Light, #ecf2fe);
    }
    .status-oready-used {
      color: #00a870;
      border: 1px solid var(---Success5-Normal, #00a870);
      background: var(---Success1-Light, #e8f8f2);
    }
    .desc {
      padding: 0 16px;
      flex: 1;
      text-align: left !important;
      // width: 235px;
    }
    // .course-link {
    //   white-space: nowrap;
    //   cursor: pointer;
    //   color: #0052d9;
    //   padding-right: 16px;
    // }
  }
  .foot-page {
    margin-top: 20px;
    padding: 0 16px;
    .prev-btn {
      color: #00000099;
      font-size: 12px;
      line-height: 20px;
    }
    .next-btn {
      color: #0052d9;
      font-size: 12px;
      line-height: 20px;
    }
    .page-desc {
      color: #000000e6;
      font-size: 12px;
      line-height: 20px;
      padding: 6px 16px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      .current {
        color: #0052d9;
      }
    }
    /deep/.van-pagination__prev {
      height: 32px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      background: var(---White, #fff);
    }
    /deep/.van-pagination__page-desc {
      height: 32px;
    }
    /deep/.van-pagination__next {
      height: 32px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      background: #f3f7ff;
    }
  }
  /deep/.van-popup__close-icon--top-right {
    color: #000000;
    top: 23px;
    right: 22px;
  }
  /deep/.van-popup__close-icon {
    font-size: 12px;
    font-weight: 700;
  }
}
.share-model {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5); /* 半透明遮罩层 */
  z-index: 3000; /* 确保遮罩层在其他内容之上 */
  .model-content {
    width: 227px;
    position: absolute;
    right: 5.3%;
    img {
      width: 100%;
      height: 100%;
    }
    .model-footer {
      padding-top: 20px;
      display: flex;
      justify-content: center;
      .model-btn {
        width: 86px;
        height: 34px;
        border-radius: 32px;
        border: 1px solid #ffffffcc;
        background: #474747;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        color: #fff;
        line-height: 34px;
        text-align: center;
      }
    }
  }
}
</style>
