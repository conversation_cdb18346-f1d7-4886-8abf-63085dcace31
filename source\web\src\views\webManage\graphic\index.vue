<template>
  <div class="graphic-approve-container">
    <p class="title">图文审核</p>
    <Graphic :approveStatus="true"></Graphic>
  </div>
</template>
<script>
import Graphic from '../../user/graphic/create.vue'
export default {
  components: {
    Graphic
  },
  data() {
    return {
      
    }
  }
}
</script>
<style lang="less" scoped>
.graphic-approve-container {
  background-color: #f6f7f9;
  .title {
    font-size: 18px;
    font-weight: bold;
    padding: 10px 16px;
    margin: 0 auto 0;
    background-color: #fff;
  }
  @media screen and (max-width: 1660px) {
    .title {
      width: 1148px;
    }
  }
  @media screen and (min-width: 1661px) {
    .title {
      width: 1430px;
    }
  }
}
</style>
