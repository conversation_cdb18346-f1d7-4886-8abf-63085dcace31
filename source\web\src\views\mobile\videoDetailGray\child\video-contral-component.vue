<template>
  <div class="video-contral-compotent" :class="{'video-contral-compotent-pad': isPad}">
    <!-- 控制栏-小屏幕 -->
    <div class="small-screen" v-if="!isFullscreen">
      <!-- 字幕 -->
      <template v-if="isRenderCaption">
        <div v-show="isShowCaption" :class="['subtitles-content', 'subtitles-small', 'line-hidden-2', { 'subtitles-hidden-small': hiddentralBox }]">{{ subtitlesText }}</div>
      </template>
      <!-- 控制栏 -->
      <div class="customer-contral-small" :class="{'hidden-customer-contral': hiddentralBox}" @click="contralBoxClick">
        <div class="top">
          <van-slider v-if="!courseData.limit_progress_bar" class="video-progress-customer" v-model="videoProgress" :min="0" :max="videoTotalTime" @change="processChange" @drag-start="dragStart" @drag-end="dragEnd(videoProgress)" @click.native="contralBoxClick">
            <template #button>
              <div class="custom-button"><div class="custom-button-inner"></div></div>
            </template>
          </van-slider>
        </div>
        <div class="bottom">
          <div class="left">
            <img v-if="isPlaying" class="video-icon" @click="playOrPause('pause')" src="@/assets/img/mobile/pause-video.png" alt="">
            <img v-else-if="isVideoFinish" class="video-icon" @click="playOrPause('play')" src="@/assets/img/mobile/replay-video.png" alt="">
            <img v-else class="video-icon" @click="playOrPause('play')" src="@/assets/img/mobile/play-video.png" alt="">
            <span class="time"> {{timeToString(videoProgress)}}/{{timeToString(videoTotalTime)}} </span>
          </div>
          <div class="right">
            <template v-if="isRenderCaption">
              <img v-if="showCustomerCaption" class="subtitles" src="@/assets/img/mobile/caption_open.png" alt="字幕" @click="showCustomerCaption = !showCustomerCaption" />
              <img v-else class="subtitles" src="@/assets/img/mobile/caption_close.png" alt="字幕" @click="showCustomerCaption = !showCustomerCaption" />
            </template>
            <span class="speed" @click="changeSpeed">{{ speedArray[speedIndex] }}x</span>
            <img @click="openFullMode" class="fullscreen-icon" src="@/assets/img/mobile/fullscreen-video.png" alt="">
          </div>
        </div>
      </div>
    </div>

    <!-- 大屏幕-横屏 -->
    <div class="fullscreen-big" v-if="isFullscreen && (isInteractive || !videoIsVertical)">
      <!-- 锁icon -->
      <template v-if="isPlaying && !hiddenLockIcon">
        <img v-if="showLock && !hiddenLockIcon" class="lock-icon" src="@/assets/img/mobile/lock-close.png" alt="锁屏" @click="clickLockIcon" />
        <img v-if="!showLock && !hiddenLockIcon" class="lock-icon" src="@/assets/img/mobile/lock-open.png" alt="开锁" @click="clickLockIcon" />
      </template>
      <!-- 锁-遮罩层 -->
      <van-overlay class-name="overlay-customer" :duration="0" :show="showLock" @click="clickOverlay" />
      <!-- 顶部区域 名称、后台和分享 -->
      <div class="top-content" :class="{'hidden-customer-contral': hiddentralBox}" @click="contralBoxClick">
        <div class="left">
          <img v-if="!isInteractive" @click="closeFullMode" class="back-icon" src="@/assets/img/mobile/full-back.png" alt="">
          <span class="course-name">{{ courseData.course_name }}</span>
        </div>
        <div class="right">
          <template v-if="!isInteractive && !isRequest && false">
            后台播放
            <div class="switch-content" :class="{'switch-content-close': !backPlay}">
              <van-switch v-model="backPlay" @change="backPlayChange" size="22" active-color="#00000066" inactive-color="#00000066" />
            </div>
          </template>
          <img @click="share" class="share-img" src="@/assets/img/mobile/full-share.png" alt="" v-if="$route.query.from !== 'mooc'">
        </div>
      </div>
      <!-- 字幕 -->
      <template v-if="isRenderCaption">
        <div v-show="isShowCaption" :class="['subtitles-content', 'subtitles-fullscreen-b', 'line-hidden-2', {'subtitles-hidden-fullscreen-b' : hiddentralBox}]">{{ subtitlesText }}</div>
      </template>
      <!-- 章节列表 -->
      <van-popup class="chapter-list-pop" v-model="showChapter" position="right" overlay-class="overlay-class">
        <div class="chapter-list">
          <div class="chapter-item " :class="{'chapter-item-active': chapterIndex === index}" v-for="(item, index) in chapterSummaryList" :key="index" @click="chosechapter(item, index)">
            <img v-if="chapterIndex === index" class="chapter-play-icon" src="@/assets/img/mobile/chapter-play-icon.png" alt="" /><span class="chapter-item-text">{{item.chapter_title}}</span>
          </div>
        </div>
      </van-popup>
      <!-- 控制栏 -->
      <div class="customer-contral-big-b" :class="{'hidden-customer-contral': hiddentralBox, 'customer-contral-big-end': isVideoFinish}" @click="contralBoxClick">
        <div class="left">
          <img v-if="isPlaying" class="video-icon" @click="playOrPause('pause')" src="@/assets/img/mobile/pause-video.png" alt="">
          <img v-else-if="isVideoFinish" class="video-icon" @click="playOrPause('play')" src="@/assets/img/mobile/replay-video.png" alt="">
          <img v-else class="video-icon" @click="playOrPause('play')" src="@/assets/img/mobile/play-video.png" alt="">
          <!-- 进度条占位 -->
          <div v-if="!courseData.limit_progress_bar" class="video-progress-customer"></div>
          <span class="time"> {{timeToString(videoProgress)}}/{{timeToString(videoTotalTime)}} </span>
        </div>
        <div class="right">
          <template v-if="isRenderCaption">
            <img v-if="showCustomerCaption" class="subtitles" src="@/assets/img/mobile/caption_open.png" alt="字幕" @click="showCustomerCaption = !showCustomerCaption" />
            <img v-else class="subtitles" src="@/assets/img/mobile/caption_close.png" alt="字幕" @click="showCustomerCaption = !showCustomerCaption" />
          </template>
            <span v-if="!isInteractive" class="text-middle" @click="changeSpeed">{{ speedArray[speedIndex] }}x</span>
            <span @click="clickChapter" class="text-middle" v-if="chapterSummaryList.length">章节</span>
            <img v-if="!isInteractive" class="fullscreen-icon text-middle" @click="closeFullMode" src="@/assets/img/mobile/nofullscreen-video.png" alt="">
        </div>
      </div>
      <!-- 进度条 -->
      <van-slider v-if="!courseData.limit_progress_bar" class="video-progress" :class="{'hidden-customer-contral': hiddentralBox}"
        v-model="videoProgress" :min="0" :max="videoTotalTime" :vertical="!isPad" @change="processChange" @drag-start="dragStart" @drag-end="dragEnd(videoProgress)" @click.native="contralBoxClick"
      >
        <template #button>
          <div class="custom-button"><div class="custom-button-inner"></div></div>
        </template>
      </van-slider>
      <!-- 结束后展示的课单、专区、更多推荐 -->
      <template v-if="isVideoFinish">
        <!-- 课单 -->
        <full-course-list :fromType="fromType" :isPad="isPad"></full-course-list>
      </template>
    </div>

    <!-- 大屏幕-竖屏 -->
    <div class="fullscreen-small" v-if="isFullscreen && !isInteractive && videoIsVertical">
      <!-- 顶部后台播放和分享 -->
      <div class="top-content" :class="{'hidden-customer-contral': hiddentralBox}" @click="contralBoxClick">
        <template v-if="!isInteractive && !isRequest && false">
          后台播放
          <div class="switch-content" :class="{'switch-content-close': !backPlay}">
            <van-switch v-model="backPlay" @change="backPlayChange" size="22" active-color="#ffffff33" inactive-color="#ffffff33" />
          </div>
        </template>
        <img @click="share" class="share-img" src="@/assets/img/mobile/full-share.png" alt="" v-if="$route.query.from !== 'mooc'">
      </div>
      <!-- 字幕 -->
      <template v-if="isRenderCaption">
        <div v-show="isShowCaption" :class="['subtitles-content', 'subtitles-fullscreen-s', 'line-hidden-2']">{{ subtitlesText }}</div>
      </template>
      <!-- 控制栏 -->
      <div class="customer-contral-big-s" :class="{'hidden-customer-contral': hiddentralBox}" @click="contralBoxClick">
        <div class="top">
          <van-slider v-if="!courseData.limit_progress_bar" class="video-progress-customer" v-model="videoProgress" :min="0" :max="videoTotalTime" @change="processChange"
            @drag-start="dragStart" @drag-end="dragEnd(videoProgress)" @click.native="contralBoxClick">
            <template #button>
              <div class="custom-button"><div class="custom-button-inner"></div></div>
            </template>
          </van-slider>
          <span class="time" :class="{'mgl-0': courseData.limit_progress_bar}"> {{timeToString(videoProgress)}}/{{timeToString(videoTotalTime)}} </span>
        </div>
        <div class="bottom">
          <template>
            <img v-if="isPlaying" class="video-icon" @click="playOrPause('pause')" src="@/assets/img/mobile/pause-video.png" alt="">
            <img v-else-if="isVideoFinish" class="video-icon" @click="playOrPause('play')" src="@/assets/img/mobile/replay-video.png" alt="">
            <img v-else class="video-icon" @click="playOrPause('play')" src="@/assets/img/mobile/play-video.png" alt="">
          </template>
          <div class="right">
            <template v-if="isRenderCaption">
              <img v-if="showCustomerCaption" class="subtitles" src="@/assets/img/mobile/caption_open.png" alt="字幕" @click="showCustomerCaption = !showCustomerCaption" />
              <img v-else class="subtitles" src="@/assets/img/mobile/caption_close.png" alt="字幕" @click="showCustomerCaption = !showCustomerCaption" />
            </template>
            <span v-if="!isInteractive" class="text-middle" @click="changeSpeed">{{ speedArray[speedIndex] }}x</span>
            <img v-if="!isInteractive" class="fullscreen-icon text-middle" @click="closeFullMode" src="@/assets/img/mobile/nofullscreen-video.png" alt="">
          </div>
        </div>
      </div>
    </div>

    <!-- 分享提示框 -->
    <shareTipDialog v-model="showShareDialog" :portraitScreen="isPad ? true : videoIsVertical"></shareTipDialog>
  </div>
</template>

<script>
import Vue from 'vue'
import fullCourseList from './fullCourseList.vue'
import shareTipDialog from './shareTipDialog.vue'
import { Overlay } from 'vant'
Vue.use(Overlay)

const trailingPunctuationRegex = /[.。,，!！?？]$/
export default {
  components: {
    fullCourseList,
    shareTipDialog
  },
  data() {
    return {
      backPlay: true, // 是否打开背景播放
      showShareDialog: false, // 是否显示分享提示框
      showCustomerCaption: true, // 是否显示字幕(手动实现的字幕)
      subtitlesText: '', // 当前时间所显示的字幕
      hiddentralBox: false, // 播放时点击屏幕显示或者隐藏视频操作栏
      hiddenTimeId: null, // 播放时点击隐藏视频操作栏的计时器
      isPlaying: false, // 是否播放状态
      isVideoFinish: false, // 本轮视频播放是否结束
      videoProgress: 0, // 进度条
      speedArray: [0.5, 1, 1.25, 1.5, 2], // 倍速容器
      speedIndex: 1, // 当前播放速度
      showChapter: false, // 是否显示章节的列表
      chapterIndex: 0, // 当前播放的章节下标
      showLock: false, // 是否锁住屏幕
      hiddenLockIcon: false, // 是否隐藏锁的图标
      hiddenLockTimeId: null, // 播放时点击隐藏"锁屏按钮"的计时器
      fontSize: 0 // 根标签的字体大小 pad时缩小一半
    }
  },
  props: {
    // 视频总时长
    videoTotalTime: {
      type: Number,
      default: 0
    },
    // 是否全屏
    isFullscreen: {
      type: Boolean,
      default: false
    },
    // 字幕
    captionData: {
      type: Array,
      default: () => []
    },
    // 课程信息
    courseData: {
      type: Object,
      default: () => ({
        limit_progress_bar: false
      })
    },
    // 是否是纵向视频比例 高>宽
    videoIsVertical: {
      type: Boolean,
      default: false
    },
    // 是否开启互动
    isInteractive: {
      type: Boolean,
      default: false
    },
    // 是否是必修课程
    isRequest: {
      type: Boolean,
      default: false
    },
    // 章节列表
    chapterSummaryList: {
      type: Array,
      default: () => []
    },
    isPad: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    // 是否渲染字幕vif
    isRenderCaption() {
      return !!(this.captionData && this.captionData.length)
    },
    // 是否显示字幕vshow
    isShowCaption() {
      return !!(this.showCustomerCaption && this.subtitlesText)
    },
    isShowCourse() {
      return this.$route.query.area_id && this.$route.query.from === 'CourseList'
    },
    isSpecialArea() {
      // 490是首页不显示
      return this.$route.query.from === 'SpecialArea' && this.$route.query.area_id && this.$route.query.area_id !== '490'
    },
    // 从哪里跳转过来
    fromType() {
      if (this.isShowCourse) {
        return 'isCourse'
      } else if (this.isSpecialArea) {
        return 'isSpecial'
      } else {
        return 'normal'
      }
    },
    // 课程id
    course_id() {
      return this.$route.query.course_id || ''
    },
    // 是否是ios设备
    isiOS() {
      const u = navigator.userAgent
      return !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)
    }
    // 是否是小程序环境
    // isMiniprogram() {
    //   return window.__wxjs_environment === 'miniprogram'
    // }
  },
  watch: {
    chapterSummaryList: {
      handler(newValve) {
        this.calcProcessWidth(this.isFullscreen)
      },
      deep: true
    },
    chapterIndex() {
      this.$nextTick(() => {
        this.calcCurrentChapterCenter()
      })
    }
  },
  mounted() {
    window.addEventListener('resize', this.calcFontSize)
    this.calcFontSize()
  },
  methods: {
    // pad计算html的font-size
    calcFontSize() {
      this.$nextTick(() => {
        if (!this.isPad) return
        try {
          let htmlDom = document.querySelector('html')
          if (htmlDom) {
            let str = htmlDom?.style?.fontSize || ''
            let fontSize = +str.split('px')[0]
            this.fontSize = fontSize
            htmlDom.style.fontSize = `${fontSize / 2}px`
          }
        } catch (error) {
          console.log('error: ', error)
        }
      })
    },
    // 点击"锁屏"遮罩层
    clickOverlay() {
      this.hiddenLockIcon = !this.hiddenLockIcon
      if (this.hiddenLockIcon) {
        clearTimeout(this.hiddenLockTimeId)
      } else {
        this.hiddenLockTime()
      }
    },
    // 点击"锁屏按钮"
    clickLockIcon() {
      this.showLock = !this.showLock
      // if (!this.showLock) { // 解锁
      // } else {
      // }
      this.hiddentralBox = false
      this.hiddenOperaTime()
      this.hiddenLockTime()
    },
    // 点击章节
    clickChapter() {
      this.showChapter = !this.showChapter
      if (this.showChapter) {
        this.chapterPosition(this.videoProgress)
      }
    },
    // 章节选中定位
    chapterPosition(time) {
      if (this.showChapter) {
        let filterItem = [...this.chapterSummaryList].reverse().find(item => {
          return time >= item.chapter_time_point
        })
        let index = this.chapterSummaryList.findIndex(item => filterItem.id === item.id)
        this.chapterIndex = index || 0
      }
    },
    // 计算章节的位置 居中显示
    calcCurrentChapterCenter() {
      try {
        let domArray = document.querySelectorAll('.chapter-item')
        let scrollDom = document.querySelector('.chapter-list-pop')
        let itemDom = domArray[this.chapterIndex]
        let instance = this.isPad ? window.innerHeight / 2 - 22 : 168
        scrollDom.scrollTo({ top: itemDom.offsetTop - instance, behavior: 'smooth' })
      } catch (error) {
        console.log('error: ', error)
      }
    },
    // 选择章节
    chosechapter(item, index) {
      if (this.isInteractive) return
      this.chapterIndex = index
      this.$parent.$refs.videoRef.vedioPlayer.currentTime(item.chapter_time_point)
    },
    // 返回
    goBack() {
      if (window.top === window.self) { // 当前页面没有被iframe嵌套
        window.wx.miniProgram.navigateBack()
      } else {
        window.parent.postMessage({ data: 'navigateBack' }, '*')
      }
    },
    // 后后台播放按钮改变
    backPlayChange(val) {
      this.$parent.backPlay = val
    },
    // 点击分享按钮
    share() {
      // 判断设备 安卓调用小程序分享 ios弹出引导提示
      this.showShareDialog = !this.showShareDialog
    },
    // 跳转至对应的播放时间
    toCurrentTime(time) {
      this.videoProgress = time
    },
    // 监听播放时长
    getCurrentTime(time) {
      // 章节定位
      this.chapterPosition(time)
      // 字幕
      if (this.showCustomerCaption && this.captionData && this.captionData.length) {
        this.changeSubtitlesText(time)
      }
      // 进度条同步
      this.videoProgress = +time.toFixed(2)
      // 同步进度条长度
      this.calcProcessWidth(this.isFullscreen)
    },
    // 显示字幕时视频中的字幕切换
    changeSubtitlesText(time) {
      time = time + 0.2 // 容错时间
      // this.$nextTick(() => {
      for (let i = 0; i < this.captionData.length; i++) {
        if (time >= this.captionData[i].IntStartTime && time - 0.2 <= this.captionData[i].IntEndTime) {
          this.subtitlesText = this.removeTrailingPunctuation(this.captionData[i].caption) || ''
          break
        } else {
          this.subtitlesText = ''
        }
      }
      // })
    },
    // 如果字符串最后一位是标点符号，那么删除最后一位
    removeTrailingPunctuation(str) {
      if (trailingPunctuationRegex.test(str)) {
        return str.replace(trailingPunctuationRegex, '')
      }
      return str
    },
    // 播放
    onPlay() {
      this.isPlaying = true
      this.isVideoFinish = false
      this.hiddenOperaTime()
      this.hiddenLockTime()
    },
    // 暂停
    onPause() {
      this.isPlaying = false
      this.isVideoFinish = false
      this.hiddentralBox = false
      clearTimeout(this.hiddenTimeId)
      this.hiddenLockIcon = false
      clearTimeout(this.hiddenLockTimeId)
    },
    // 播放结束
    onEnded() {
      this.isPlaying = false
      this.isVideoFinish = true
      clearTimeout(this.hiddenTimeId)
      this.hiddentralBox = false
      clearTimeout(this.hiddenLockTimeId)
      this.hiddenLockIcon = false
      this.showLock = false
    },
    // 全屏切换
    fullscreenchange(data) {
      this.calcProcessWidth(data) // 将弹性布局的进度条长度 设置给定位上去的滚动条
      this.hiddentralBox = false
      this.hiddenLockIcon = false
      if (this.isPlaying) {
        this.hiddenOperaTime()
        if (data) {
          this.hiddenLockTime()
        }
      }
    },
    // 隐藏控制栏倒计时
    hiddenOperaTime() {
      if (this.hiddenTimeId) {
        clearInterval(this.hiddenTimeId)
      }
      this.hiddenTimeId = setTimeout(() => {
        this.hiddentralBox = true
      }, 2400)
    },
    // 隐藏"锁屏按钮"倒计时
    hiddenLockTime() {
      this.$nextTick(() => {
        if (!this.isFullscreen) return
        if (this.hiddenLockTimeId) {
          clearInterval(this.hiddenLockTimeId)
        }
        this.hiddenLockTimeId = setTimeout(() => {
          this.hiddenLockIcon = true
        }, 2400)
      })
    },
    // 点击显示控制栏
    contralBoxClick() {
      this.hiddentralBox = false
      this.hiddenLockIcon = false
      if (this.isPlaying) {
        this.hiddenOperaTime()
        this.hiddenLockTime()
      }
    },
    // 进度条拖动并且已经结束
    processChange(value) {
      this.$parent.$refs.videoRef.vedioPlayer.currentTime(value)
      if (this.isPlaying) {
        this.hiddenOperaTime()
      }
    },
    // 进度条开始拖动
    dragStart() {
      this.$parent.$refs.videoRef.vedioPlayer.pause()
      this.isPlaying = false
    },
    // 进度条结束拖动
    dragEnd(value) {
      this.$parent.$refs.videoRef.vedioPlayer.currentTime(value)
      this.$parent.$refs.videoRef.vedioPlayer.play()
      this.isPlaying = true
    },
    // 播放或者暂停操作
    playOrPause(type) {
      if (type === 'play') {
        this.$parent.$refs.videoRef.vedioPlayer.play()
      } else {
        this.$parent.$refs.videoRef.vedioPlayer.pause()
      }
    },
    // 秒转换时分秒
    timeToString(val) {
      val = parseInt(val)
      if (!val) {
        return '0:00'
      }
      // 小于60秒
      if (val < 60) {
        if (val < 10) {
          val = `0${val}`
        }
        return `0:${val}`
      }

      let newSecondTime = 0 // 秒
      let minuteTime = 0 // 分
      let hourTime = 0 // 小时
      let result = ''

      //  如果秒数大于60，将秒数转换成整数
      // 获取分钟，除以60取整数，得到整数分钟
      minuteTime = parseInt(String(val / 60))
      // 获取秒数，秒数取佘，得到整数秒数
      newSecondTime = parseInt(String(val % 60))
      // 如果分钟大于60，将分钟转换成小时
      if (minuteTime >= 60) {
        // 获取小时，获取分钟除以60，得到整数小时
        hourTime = parseInt(String(minuteTime / 60))
        // 获取小时后取佘的分，获取分钟除以60取佘的分
        minuteTime = parseInt(String(minuteTime % 60))
      }

      if (newSecondTime < 10) {
        newSecondTime = `0${newSecondTime}`
      }
      // 存在分钟
      if (minuteTime > 0) {
        if (hourTime > 0 && minuteTime < 10) {
          minuteTime = `0${minuteTime}`
        }
        result = `${minuteTime}:${newSecondTime}`
      }
      // 存在小时
      if (hourTime > 0) {
        result = `${hourTime}:${minuteTime}:${newSecondTime}`
      }
      return result
    },
    // 速度切换
    changeSpeed() {
      if (this.speedIndex === 4) {
        this.speedIndex = 0
      } else {
        this.speedIndex++
      }
      const speed = this.speedArray[this.speedIndex]
      this.$parent.$refs.videoRef.vedioPlayer.playbackRate(speed)
      this.calcProcessWidth(this.isFullscreen)
    },
    // 点击全屏按钮
    openFullMode() {
      setTimeout(() => {
        this.$parent.$refs.videoRef.vedioPlayer.requestFullscreen()
      }, 150)
    },
    // 点击退出全屏按钮
    closeFullMode() {
      setTimeout(() => {
        this.$parent.$refs.videoRef.vedioPlayer.exitFullscreen()
      }, 150)
    },
    // 长按视频0.8s后以1.5倍速播放 点击显示/隐藏视频控制栏
    initVideoDrag(domId) {
      let that = this
      let timer = null
      const btnEl = document.getElementById(domId)
      if (btnEl) {
        btnEl.addEventListener('touchstart', function (e) {
          e.preventDefault()
          if (that.isPlaying) {
            that.hiddentralBox = !that.hiddentralBox
            if (that.hiddentralBox) {
              clearTimeout(that.hiddenTimeId)
            } else {
              that.hiddenOperaTime()
            }
            if (that.isFullscreen) {
              that.hiddenLockIcon = !that.hiddenLockIcon
              if (that.hiddenLockIcon) {
                clearTimeout(that.hiddenLockTimeId)
              } else {
                that.hiddenLockTime()
              }
            }

            // 互动的课程不能快进
            if (that.courseData.enable_interactive) return
            timer = setTimeout(() => {
              that.$parent.$refs.videoRef.vedioPlayer.playbackRate(1.5)
            }, 800)
          }
        })
        btnEl.addEventListener('touchmove', function (e) {
          //
        })
        btnEl.addEventListener('touchend', function (e) {
          if (timer) {
            clearTimeout(timer)
            timer = null
          }
          // 互动的课程不能快进
          if (that.courseData.enable_interactive) return
          const speed = that.speedArray[that.speedIndex]
          that.$parent.$refs.videoRef.vedioPlayer.playbackRate(speed)
        })
      }
    },
    // 全屏(横屏)时实时计算进度条的长度
    calcProcessWidth(data) {
      if (!data || this.videoIsVertical || this.courseData.limit_progress_bar) return // 排除非全屏和限制进度条的情况
      this.$nextTick(() => {
        let flexProgreessDom = document.querySelector('.customer-contral-big-b .left .video-progress-customer')
        try {
          let width = window.getComputedStyle(flexProgreessDom).width
          let videoProgressDom = document.querySelector('.video-progress')
          if (this.isPad) {
            videoProgressDom.style.width = width
          } else {
            videoProgressDom.style.height = width
          }
        } catch (error) {
          console.log('error: ', error)
          let width = flexProgreessDom ? flexProgreessDom.offsetWidth : 364
          let videoProgressDom = document.querySelector('.video-progress')
          if (this.isPad) {
            videoProgressDom.style.width = width + 'px'
          } else {
            videoProgressDom.style.height = width + 'px'
          }
        }
      })
    }
  },
  beforeDestroy() {
    clearTimeout(this.hiddenTimeId)
    clearTimeout(this.hiddenLockTimeId)
    window.removeEventListener('resize', this.calcFontSize)
    if (this.isPad && this.fontSize) {
      let htmlDom = document.querySelector('html')
      if (htmlDom) {
        htmlDom.style.fontSize = `${this.fontSize}px`
      }
    }
  }
}
</script>

<style lang="less" scoped>
  .video-contral-compotent {
    .hidden-customer-contral { // 自己写的控制栏的显示或者隐藏
      opacity: 0 !important;
    }
    // 字幕-公用
    .subtitles-content {
      position: absolute;
      padding: 2px 8px;
      overflow: hidden;
      width: 196px;
      max-height: 44px;
      line-height: 20px;
      font-size: 12px;
      color: #fff;
      text-align: center;
      border-radius: 3px;
      background: #00000066;
      z-index: 999;
    }
    .line-hidden-2 {
      overflow: hidden;
      // text-overflow: ellipsis;
      // display: -webkit-box;
      // -webkit-line-clamp: 2;
      // -webkit-box-orient: vertical;
    }

    // 小屏
    .small-screen {
      .subtitles-small {
        left: 50%;
        bottom: 64px;
        transform: translateX(-50%);
        z-index: 0;
      }
      .subtitles-hidden-small {
        bottom: 20px;
      }
      .customer-contral-small {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 64px;
        font-size: 12px;
        color: #ffffff;
        opacity: 1;
        animation: all .5s linear;
        background: linear-gradient(180deg, #00000000 0%, #0000008c 100%);
        z-index: 1000;

        .top {
          height: 4px;
          margin: 14px 16px 12px;
          .video-progress-customer {
            height: 4px;
            /deep/.van-slider__button-wrapper {
              .custom-button {
                border-radius: 50%;
                padding: 5px;
                .custom-button-inner {
                  border-radius: 50%;
                  width: 8px;
                  height: 8px;
                  background-color: #fff;
                }
              }
            }
          }
        }

        .bottom {
          display: flex;
          justify-content: space-between;
          margin: 0 16px;
          height: 24px;
          font-size: 12px;
          .left {
            display: flex;
            align-items: center;
            .video-icon {
              width: 24px;
            }
            .time {
              margin-left: 16px;
              font-weight: 600;
            }
          }
          .right {
            height: 24px;
            display: flex;
            align-items: center;
            .subtitles {
              width: 32px;
              height: 20px;
            }
            .speed {
              margin-left: 16px;
              height: 100%;
              line-height: 24px;
            }
            .fullscreen-icon {
              margin-left: 16px;
              width: 24px;
              height: 24px;
            }
          }
        }
      }
    }

    // 全屏-横屏
    .fullscreen-big {
      .lock-icon {
        position: absolute;
        bottom: 20px;
        left: 50%;
        width: 16px;
        height: 16px;
        z-index: 1003;
        transform: rotate(90deg) translateX(-50%);
      }

      /deep/.overlay-customer {
        opacity: 0;
        z-index: 1002;
      }

      // 字幕
      .subtitles-fullscreen-b {
        left: 64px;
        top: 50vh;
        transform: rotate(90deg) translateX(-118px);
        transform-origin: left bottom;
      }
      .subtitles-hidden-fullscreen-b {
        left: 20px;
      }
      // 顶部名称、后台和分享
      .top-content {
        position: absolute;
        top: 0;
        left: 0;
        display: flex;
        justify-content: space-between;
        padding: 18px 20px 0;
        width: 100vh;
        height: 51px;
        z-index: 1000;
        transform: rotate(90deg) translateY(-100vw);
        transform-origin: left top;
        .left {
          height: 100%;
          display: flex;
          align-items: center;
          .back-icon {
            width: 24px;
            height: 24px;
            margin-right: 16px;
          }
          .course-name {
            max-width: 400px;
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .right {
          height: 100%;
          display: flex;
          align-items: center;
          font-size: 12px;
          color: #ffffffe6;
          .switch-content {
            height: 24px;
            margin: 0 20px 0 8px;
            /deep/.van-switch {
              position: relative;
              &::before {
                position: absolute;
                left: 7.8px;
                top: 50%;
                content: '开';
                font-size: 12px;
                transform: translateY(-50%);
                opacity: 1;
              }
              &::after {
                position: absolute;
                right: 7.8px;
                top: 50%;
                content: '关';
                font-size: 12px;
                transform: translateY(-50%);
                opacity: 0;
              }
              .van-switch__node {
                top: 2px;
                width: 18px;
                height: 18px;
              }
            }
          }
          .switch-content-close {
            /deep/.van-switch {
              position: relative;
              &::before {
                opacity: 0;
              }
              &::after {
                opacity: 1;
              }
              .van-switch__node {
                left: 3px;
              }
            }
          }
          .share-img {
            width: 16px;
            height: 16px;
          }
        }
      }

      // 章节弹窗
      // /deep/.overlay-class {
      //   opacity: 0;
      // }
      .chapter-list-pop {
        width: 352px;
        height: 100vw;
        padding: 12px;
        background-color: #000000b3;
        overflow: auto;
        &::-webkit-scrollbar {
          display: none;//隐藏滚动条
        }
        .chapter-list {
          .chapter-item {
            display: flex;
            align-items: center;
            height: 44px;
            margin-top: 8px;
            padding: 11px 10px;
            color: #ffffff;
            font-size: 14px;
            font-weight: 500;
            border: 0.5px solid #ffffff4d;
            &:first-child {
              margin-top: 0;
            }
            .chapter-play-icon {
              width: 16px;
              height: 16px;
              margin-right: 10px;
            }
          }
          .chapter-item-active {
            color: #6fa5ff;
            border: 1px solid #6FA5FF;
          }
        }
      }
      .van-popup--right {
        top: calc(100% - 100vw);
        transform: rotate(90deg) translateY(100vw);
        transform-origin: right bottom;
      }
      .customer-contral-big-b {
        position: absolute;
        top: 0%;
        left: 0%;
        display: flex;
        justify-content: space-between;
        padding: 20px;
        width: 100vh;
        height: 64px;
        color: #ffffff;
        font-size: 12px;
        opacity: 1;
        animation: all .5s linear;
        transform: rotate(90deg) translateX(-64px);
        transform-origin: left bottom;
        background: linear-gradient(180deg, #00000000 0%, #0000008c 100%);
        z-index: 1000;
        .left {
          flex: 1;
          display: flex;
          align-items: center;
          height: 100%;
          .video-icon {
            width: 24px;
          }
          .video-progress-customer {
            flex: 1;
            margin-left: 12px;
            height: 4px;
          }
          .time {
            margin-left: 12px;
            font-size: 14px;
            font-weight: 600;
          }
        }
        .right {
          display: flex;
          align-items: center;
          height: 100%;
          font-size: 12px;
          margin-left: 24px;
          .subtitles {
            width: 32px;
            height: 20px;
          }
          .text-middle {
            margin-left: 20px;
            line-height: 24px;
          }
          .mgl-0 {
            margin-left: 0;
          }
          .fullscreen-icon {
            width: 24px;
          }
        }
      }
      .customer-contral-big-end {
        background-color: #00000066;
      }
      .video-progress {
        opacity: 1;
        animation: all .5s linear;
        position: absolute;
        top: 56px;
        left: 30px;
        width: 4px;
        height: 364px;
        z-index: 1001;
        /deep/.van-slider__button-wrapper {
          .custom-button {
            border-radius: 50%;
            padding: 8px;
            .custom-button-inner {
              border-radius: 50%;
              width: 12px;
              height: 12px;
              background-color: #fff;
            }
          }
        }
      }
    }

    // 全屏-竖屏
    .fullscreen-small {
      .top-content {
        position: absolute;
        right: 20px;
        top: 20px;
        display: flex;
        align-items: center;
        height: 32px;
        font-size: 12px;
        color: #ffffffe6;
        z-index: 1000;
        .switch-content {
          height: 24px;
          margin: 0 20px 0 8px;
          /deep/.van-switch {
            position: relative;
            &::before {
              position: absolute;
              left: 7.8px;
              top: 50%;
              content: '开';
              font-size: 12px;
              transform: translateY(-50%);
              opacity: 1;
            }
            &::after {
              position: absolute;
              right: 7.8px;
              top: 50%;
              content: '关';
              font-size: 12px;
              transform: translateY(-50%);
              opacity: 0;
            }
            .van-switch__node {
              top: 2px;
              width: 18px;
              height: 18px;
            }
          }
        }
        .switch-content-close {
          &::before {
            opacity: 0;
          }
          &::after {
            opacity: 1;
          }
          /deep/.van-switch {
            .van-switch__node {
              left: 3px;
            }
          }
        }
        .share-img {
          width: 16px;
          height: 16px;
        }
      }
      .subtitles-fullscreen-s {
        left: 50%;
        top: 50%;
        transform: translate(-50%, 45px);
        z-index: 1000;
      }
      .customer-contral-big-s {
        position: absolute;
        left: 0;
        bottom: 0;
        padding: 14px 16px 9px;
        width: 100%;
        height: 103px;
        color: #ffffff;
        font-size: 12px;
        opacity: 1;
        background: linear-gradient(180deg, #00000000 50%, #0000008c 100%);;
        z-index: 1000;
        .top {
          display: flex;
          align-items: center;
          margin-top: 26px;
          height: 20px;
          .video-progress-customer {
            height: 4px;
            /deep/.van-slider__button-wrapper {
              .custom-button {
                border-radius: 50%;
                padding: 5px;
                .custom-button-inner {
                  border-radius: 50%;
                  width: 8px;
                  height: 8px;
                  background-color: #fff;
                }
              }
            }
          }
          .time {
            margin-left: 12px;
            font-weight: 600;
          }
          .mgl-0 {
            margin-left: 0;
          }
        }
        .bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 10px;
          height: 24px;
          .video-icon {
            width: 24px;
            height: 24px;
          }
          .right {
            display: flex;
            align-items: center;
            .subtitles {
              height: 20px;
            }
            .text-middle {
              margin-left: 20px;
            }
            .fullscreen-icon {
              width: 24px;
            }
          }
        }
      }
    }

  }
  .video-contral-compotent.video-contral-compotent-pad {
     // 小屏
     .small-screen {
      .customer-contral-small {
        .video-progress-customer {
          /deep/.van-slider__button-wrapper {
            .custom-button {
              .custom-button-inner {
                width: 16px;
                height: 16px;
              }
            }
          }
        }
      }
    }

    // 大屏
    .fullscreen-big {
      .lock-icon {
        top: 50%;
        right: 20px;
        bottom: auto;
        left: auto;
        transform: none;
      }
      // 字幕
      .subtitles-fullscreen-b {
        left: 50%;
        bottom: 64px;
        top: auto;
        transform: translateX(-50%);
        z-index: 1000;
      }
      .subtitles-hidden-fullscreen-b {
        bottom: 20px;
      }
      .top-content {
          width: 100vw;
          height: 51px;
          transform: none;
          .left {
            max-width: 60%;
          }
      }
      .chapter-list-pop {
        height: 100vh;
        max-width: 65%;
        .chapter-item {
          .chapter-item-text {
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
      }
      .van-popup--right {
        top: 0;
        right: 0;
        transform: none;
      }
      .customer-contral-big-b {
        top: auto;
        left: 0;
        bottom: 0;
        transform: none;
        width: 100vw;
      }
      .video-progress {
        left: 56px;
        bottom: 30px;
        top: auto;
        width: 364px;
        height: 4px;
      }
    }
  }
</style>
