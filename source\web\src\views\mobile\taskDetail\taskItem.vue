<template>
  <div class="task-style" @click="toDetail">
    <div class="task-title">
      <span :class="['icon', taskData.typeIcon]"></span>
      <div class="text-customer overflow-l1">
        <span :class="['text', 'overflow-l1', { active: taskData.activeTask }]">{{
        taskData.task_name || ''
      }}</span>
        <div class="study" v-if="preview && previewRecords.includes(taskData.act_id)">
          <!-- 已试学 -->
          <img src="../../../assets/img/mobile/geekBang/item-prefix-icon.png" />
          <span>已试学</span>
        </div>
      </div>
      <span :class="['status', taskData.statusIcon]" v-show="!moocPreview"></span>
    </div>
    <div class="task-bottom">
      <div class="detail">
        <span
          :class="taskData.required ? 'required type' : 'unrequired type'"
          >{{ taskData.required ? this.$langue('Mooc_ProjectDetail_TaskList_RequiredTask', { defaultText: '应学' }) : this.$langue('Mooc_ProjectDetail_TaskList_ElectiveTask', { defaultText: '选学' }) }}</span
        >
        <span :class="taskData.task_status === 2 ? 'expire' : 'duration'">{{
          taskData.durationStr
        }}</span>
      </div>
      <div class="exam" v-if="taskData.resource_type === 'Exam'">
        <span class="exam-time">{{ $langue('Mooc_TaskDetail_ExamTime', { defaultText: '考试时间' }) }}</span>
        <span
          class="exam-time text end-time"
          v-if="taskData.exam_time_type === 1"
          >{{ $langue('Mooc_ProjectDetail_TaskList_NotLimit', { defaultText: '不限制' }) }}</span
        >
        <span class="exam-time" v-if="taskData.exam_time_type !== 1">{{
          taskData.exam_start_time || '-'
        }}</span>
        <span class="exam-time text" v-if="taskData.exam_time_type !== 1"
          >{{ $langue('Mooc_Home_MyProject_To', { defaultText: ' 至 ' }) }}</span
        >
        <span class="exam-time end-time" v-if="taskData.exam_time_type !== 1">{{
          taskData.exam_end_time || '-'
        }}</span>
        <!-- is_finished null 未开始，false进行中，true已完成 -->
        <span :class="examStatusColor">{{ examStatus }}</span>
        <span
          class="exam-score"
          v-show="taskData.is_cheat || taskData.score !== null"
          >{{
            taskData.is_cheat
              ? this.$langue('Mooc_ProjectDetail_TaskList_ExamCheat', { defaultText: '考试作弊' })
              : taskData.score !== null
              ? taskData.score + this.$langue('Mooc_ProjectDetail_Score_Point', { defaultText: '分' })
              : ''
          }}</span
        >
      </div>
    </div>
  </div>
</template>

<script>
import { toMoocDetailMob } from '@/utils/tools.js'
import { mapState } from 'vuex'
export default {
  props: {
    // 课程id
    mooc_course_id: {
      type: String,
      value: ''
    },
    // 当前任务数据
    taskData: {
      type: Object,
      value: {}
    },
    // 是否横屏
    isVertical: {
      type: Boolean,
      value: false
    },
    moocPreview: {
      type: Number,
      value: 0
    }
  },
  inject: ['detailInfo'],
  data() {
    return {
      icon: require('@/assets/mooc-img/group.png')
    }
  },
  computed: {
    ...mapState(['moocLang']),
    examStatus() {
      if (this.taskData.exam_status === 2) return this.$langue('Mooc_TaskDetail_HomeWork_ToBeReviewed', { defaultText: '待批阅' })
      if (this.taskData.is_finished === false) {
        return this.$langue('Mooc_ProjectDetail_TaskList_NotPass', { defaultText: '未通过' })
      } else if (this.taskData.is_finished === true) {
        return this.$langue('Mooc_ProjectDetail_TaskList_Pass', { defaultText: '通过' })
      }
      return ''
    },
    examStatusColor() {
      if (this.taskData.is_finished === false) {
        return 'exam-unpass'
      } else if (this.taskData.is_finished === true) {
        return 'exam-pass'
      }
      return 'exam-default'
    },
    preview() { // 是否是极客试学
      return this.detailInfo.preview
    },
    previewRecords() { // 极客试学 已试学列表
      return this.detailInfo.previewRecords
    }
  },
  methods: {
    toDetail() {
      // 判断是不是极客试学 试学名额
      if (this.preview && this.detailInfo.coursePurchaseInfo?.previewed_num >= this.detailInfo.coursePurchaseInfo?.allow_preview_num && !this.previewRecords.includes(this.taskData.act_id)) {
        this.detailInfo.openPop(4)
        return
      }
      let { from = 'mooc', class_id } = this.$route.query
      toMoocDetailMob(this.taskData, this.mooc_course_id, from, class_id, this.isVertical, this.moocLang, false, this.moocPreview)
    }
  }
}
</script>

<style lang='less' scoped>
.third-task {
  .task-title {
    padding-left: 28px;
  }
  .task-bottom {
    padding-left: 48px;
  }
}

.task-style {
  padding: 0 16px 0 16px;
  .task-title {
    width: 100%;
    display: flex;
    padding: 4px 0 0 0;
    height: 20px;
    line-height: 20px;

    .text-customer {
      display: flex;
      flex: 1;
      display:flex;
      align-items: center;
      .study {
          width: 46px;
          margin: 0 12px;
          img {
            margin-right: 4px;
            width: 12px;
            vertical-align: middle
          }
          span {
            color: #2ba471;
            font-size: 12px;
            font-weight: 500;
            transform: scale(0.83);
          }
        }
    }

    .icon,
    .status {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-top: 2px;
    }
    .text {
      // flex: 1;
      height: 20px;
      margin-left: 4px;
      color: #000000e6;
      font-size: 12px;
    }
    .active {
      color: #0052d9;
    }
  }
  .task-bottom {
    padding-left: 20px;
    border-bottom: 1px solid #ebedf0;
    .detail {
      padding: 4px 0;
      .type {
        display: inline-block;
        margin-right: 8px;
        height: 18px;
        line-height: 18px;
        padding: 0 4px;
        border-radius: 2px;
        font-size: 12px;
      }
      .required {
        background: #fdf6ecff;
        color: #ff7548ff;
      }
      .unrequired {
        background: #ccf2e2ff;
        color: #00b368ff;
      }
      .duration,
      .expire {
        font-size: 10px;
      }
      .duration {
        color: #00000099;
      }
      .expire {
        color: #00000042;
      }
    }
    .exam {
      display: flex;
      padding-bottom: 4px;
      font-size: 10px;
      .exam-time {
        color: #00000099;
      }
      .end-time {
        flex: 1;
      }
      .text {
        padding: 0 4px;
      }
      .exam-pass {
        color: #00a870ff;
      }
      .exam-unpass {
        color: #ed7b2f;
      }
      .exam-default {
        color: #00000099;
      }
      .exam-score {
        margin-left: 4px;
        padding-left: 4px;
        color: #00000099;
        border-left: 1px solid #00000099;
      }
    }
  }
  .video-icon {
    background: url('~@/assets/mooc-img/video.png') no-repeat center / cover;
  }

  .audio-icon {
    background: url('~@/assets/mooc-img/audio.png') no-repeat center / cover;
  }

  .exam-icon {
    background: url('~@/assets/mooc-img/exam-task-round.png') no-repeat center /
      cover;
  }

  .practice-icon {
    background: url('~@/assets/mooc-img/practise.png') no-repeat center / cover;
  }

  .scorm-icon {
    background: url('~@/assets/mooc-img/scorm.png') no-repeat center / cover;
  }
  .other-icon {
    background: url('~@/assets/mooc-img/outlink.png') no-repeat center / cover;
  }

  .zip-icon {
    background: url('~@/assets/mooc-img/zip.png') no-repeat center / cover;
  }
  .doc-icon {
    background: url('~@/assets/mooc-img/file.png') no-repeat center / cover;
  }
  .series-icon {
    background: url('~@/assets/mooc-img/series.png') no-repeat center / cover;
  }
  .article-icon {
    background: url('~@/assets/mooc-img/article.png') no-repeat center / cover;
  }
  .homework-icon {
    background: url('~@/assets/mooc-img/work-icon.png') no-repeat center / cover;
  }
  .thirdparty-icon {
    background: url('~@/assets/mooc-img/third-party-icon.png') no-repeat center /
      cover;
  }

  .wait-icon {
    background: url('~@/assets/mooc-img/wait.png') no-repeat center / cover;
  }

  .loading-icon {
    background: url('~@/assets/mooc-img/loading.png') no-repeat center / cover;
  }

  .done-icon {
    background: url('~@/assets/mooc-img/done.png') no-repeat center / cover;
  }

  .lock-icon {
    background: url('~@/assets/mooc-img/lock-on.png') no-repeat center / cover;
  }
  .facecourse-icon {
    background: url('~@/assets/mooc-img/face-course.png') no-repeat center / cover;
  }
  .activity-icon {
    background: url('~@/assets/mooc-img/activity.png') no-repeat center / cover;
  }
  .overflow-l1 {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }
}
</style>
