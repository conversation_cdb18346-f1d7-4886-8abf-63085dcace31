<template>
  <div>
    <el-dialog
      :visible.sync="isShowDetailsPopup"
      custom-class="task-details-popup"
      title="任务详情"
      width="960px"
      :lock-scroll="false"
      :close-on-click-modal="false"
      :before-close="closeDialog">
      <div class="title">
        <span>任务名称：</span>{{ detailsInfo.task_name }}
        <el-button class="btn-look" type="text" v-if="['Exam', 'Practice'].includes(detailsInfo.resource_type)" @click="toExamDetail">查看考试详情</el-button>
      </div>
      <div class="info">
        <p><span>类型：</span>{{ detailsInfo.resource_type_name }}</p>
        <p><span>性质：</span>{{ detailsInfo.required ? '应学' : '选学' }}</p>
        <!-- <p><span>解锁时间：</span>{{ detailsInfo.unlock_time || '--' }}</p> -->
        <p><span class="completed dot">已完成：</span>{{ statisticsInfo.finished_students_count }}</p>
        <p><span class="afoot dot">进行中：</span>{{ statisticsInfo.learning_students_count }}</p>
        <p><span class="not-started dot">未开始：</span>{{ statisticsInfo.not_started_students_count }}</p>
      </div>
      <el-form ref="form" :model="form" inline>
        <el-form-item label="名称">
          <el-input v-model="form.staffName" placeholder="请输入学员名称" size="small"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="form.finishStatus" placeholder="请选择任务状态" size="small">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="onSearch(1)">搜索</el-button>
          <el-button size="small" @click="reset">重置</el-button>
          <el-button size="small" @click="exportData" :disabled="isApprove">导出</el-button>
        </el-form-item>
      </el-form>
      <div v-if="detailsInfo.resource_type === 'ThirdParty'" class="third-party-box">
        <CustomTips
          class="third-party-tips"
          title="每日0点和12点系统自动调用结果查询接口刷新“未开始”和“进行中”学员的任务状态，同时支持手动刷新任务状态" 
          IconName="el-icon-warning" 
          backgroundColor="#fdf6ec" 
          color="#FF7548"
          lineHeight="40px"
          >
        </CustomTips>
        <el-button class="refresh-status" type="primary" @click="refreshStatus" size="small">刷新任务状态</el-button>      
      </div>
      <el-table :data="tableData.records" header-row-class-name="table-header-style">
        <el-table-column prop="staff_name" label="学员名称" min-width="140"></el-table-column>
        <el-table-column prop="is_finished" label="任务状态" min-width="100">
          <template slot-scope="scope">
            {{ scope.row.is_finished ? '已完成' : scope.row.is_finished === null ? '未开始' : '进行中' }}
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="开始任务时间" min-width="160">
          <template slot-scope="scope">
            {{ scope.row.start_time || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="end_time" label="完成任务时间" min-width="160">
          <template slot-scope="scope">
            {{ scope.row.finished_time || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="elapsed_seconds" label="累计学习时长" min-width="110">
          <template slot-scope="scope">
            {{ handleTime(scope.row.elapsed_seconds) }}
          </template>
        </el-table-column>
        <template v-if="['Exam', 'Practice'].includes(detailsInfo.resource_type)">
          <el-table-column prop="is_finished" label="通过状态"  min-width="130">
            <template slot-scope="scope">
              <span>{{ handleExamStatus(scope.row)}}</span>
              <el-tooltip class="item" effect="dark" content="该考生存在作弊行为，考试不通过！" placement="top-start">
                <img class="info-circle" v-if="scope.row.is_cheat" src="@/assets/mooc-img/info-circle.png" alt="" srcset="">
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="score" label="最高成绩"  min-width="100"></el-table-column>
        </template>
        <template v-if="detailsInfo.resource_type === 'ThirdParty'">
          <el-table-column prop="operate" label="操作"  min-width="100">
            <template slot-scope="scope">
              <el-button size="small" type="text" @click="viewMore(scope.row)">查看更多</el-button>
            </template>
          </el-table-column>
        </template>
      </el-table>
      <el-pagination
        v-if="tableData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="current"
        :page-sizes="[10, 20, 30, 50, 100]"
        :page-size="size"
        layout="total,  prev, pager, next, sizes, jumper"
        :total="tableData.total">
      </el-pagination>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      title="补充信息"
      :visible.sync="explenishVisible"
      width="358px"
      class="explenish-dialog"
      :before-close="closeExplenishDialog">
      <div v-for="(item, index) in thirdPartyExtensions" :key="index" class="explenish-item">
        <span class="field-name">{{item.name}}：</span>
        <span class="value">{{item.value}}</span>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import pagination from '@/mixins/pager'
import { getTaskStudyRecordsAPI, getTaskStudyRecordsExportAPI, thirdPartyExport, refreshTaskManage, getThirdPartyExtensions } from '@/config/mooc.api.conf.js'
import { transforTime } from '@/utils/tools.js'
import CustomTips from '@/components/tips.vue'
import { mapState } from 'vuex'
export default {
  mixins: [pagination],
  components: {
    CustomTips
  },
  props: {
    isShowDetailsPopup: {
      type: Boolean,
      default: false
    },
    detailsInfo: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      form: {
        staffName: '',
        finishStatus: ''
      },
      statusOptions: [
        { label: '全部', value: '' },
        { label: '已完成', value: 4 },
        { label: '进行中', value: 2 },
        { label: '未开始', value: 1 }
      ],
      tableData: {
        records: [],
        total: 0
      },
      statisticsInfo: {},
      explenishVisible: false,
      thirdPartyExtensions: []
    }
  },
  computed: {
    ...mapState(['projectManageInfo']),
    isApprove() {
      // 审批-审批中
      return this.$route.query.approve === '1' || this.projectManageInfo.course_status === 6
    }
  },
  created () {
    this.onSearch(1)
  },
  methods: {
    onSearch (page_no = 1) {
      this.current = page_no
      getTaskStudyRecordsAPI({
        course_id: this.$route.query.mooc_course_id,
        task_id: this.detailsInfo.task_id,
        staff_name: this.form.staffName,
        learn_status: this.form.finishStatus,
        page_no,
        page_size: this.size
      }).then(res => {
        this.tableData.records = res.records
        this.tableData.total = res.total
        this.statisticsInfo = {
          finished_students_count: res.finished_students_count,
          learning_students_count: res.learning_students_count,
          not_started_students_count: res.not_started_students_count
        }
      })
    },
    // 重置
    reset () {
      this.form.staffName = ''
      this.form.finishStatus = ''
      this.current = 1
      this.onSearch(1)
    },
    // 导出
    exportData () {
      let exportURL = this.detailsInfo.resource_type === 'ThirdParty' ? thirdPartyExport : getTaskStudyRecordsExportAPI
      exportURL({
        course_id: this.detailsInfo.mooc_course_id,
        task_id: this.detailsInfo.task_id,
        staff_name: this.form.staffName,
        is_finished: this.form.finishStatus
      })
    },
    // 关闭弹窗
    closeDialog () {
      this.$emit('update:isShowDetailsPopup', false)
    },
    handleTime (e) {
      return transforTime(e)
    },
    handleExamStatus (e) {
      if (e.exam_status === 2) {
        return '待批阅'
      }
      if (e.is_cheat) {
        return '未通过'
      } else {
        if (e.is_finished === null) return '-'
        if (e.is_finished) return '已通过'
        if (!e.is_finished) return '未通过'
      }
    },
    // 考试/练习 跳转详情页
    toExamDetail() {
      const { act_id, resource_type } = this.detailsInfo
      let url = null
      if (resource_type === 'Exam') {
        url = process.env.NODE_ENV === 'production' ? `https://exam.woa.com/exam/manage/examDetailNew/${act_id}/1` : `http://dev.ntsapps.oa.com/exam/manage/examDetailNew/${act_id}/1`
      } else if (resource_type === 'Practice') {
        url = process.env.NODE_ENV === 'production' ? `https://exam.woa.com/exam/manage/practiceDetailNew/${act_id}/1` : `http://dev.ntsapps.oa.com/exam/manage/practiceDetailNew/${act_id}/1`
      }
      window.open(url, '_blank')
    },
    refreshStatus() {
      let str = 
          '<p style="font-size:14px">确定刷新所有“未开始”和“进行中”学员的任务状态吗？</p>' +
          '<p style="font-size:14px; color: #FF7548"><i class="el-icon-warning-outline" style="margin-right: 4px; font-size: 16px; position: relative; top: 1px" ></i>请勿在短时间内多次进行刷新操作<p/>'
      this.$messageBox
        .confirm(str, '刷新任务状态', {
          dangerouslyUseHTMLString: true,
          closeOnClickModal: false,
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        })
        .then(() => {
          const { mooc_course_id, task_id } = this.detailsInfo
          let params = {
            mooc_course_id,
            task_id,
            staff_id: this.$store.state.userInfo.staff_id
          }
          refreshTaskManage(params).then(() => {
            this.onSearch(1)
            this.$message.success('刷新任务状态成功')
          })
        })
    },
    viewMore(row) {
      this.explenishVisible = true
      const { mooc_course_id, task_id } = this.detailsInfo
      getThirdPartyExtensions({ mooc_course_id, task_id, staff_id: this.$store.state.userInfo.staff_id }).then(res => {
        this.thirdPartyExtensions = res.extensions
      })
    },
    closeExplenishDialog() {
      this.explenishVisible = false
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.task-details-popup {
  .el-dialog__body {
    padding: 20px 30px;
  }
  .title {
    color: #333;
    display: flex;
    align-items: center;
    & > span {
      color: #666;
    }
    .btn-look {
      margin-left: 80px;
    }
  }
  .info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 20px 0 25px;
    & > p {
      color: #333;
      & > span {
        color: #666;
      }
    }
    .dot {
      padding-left: 10px;
      position: relative;
      &::before {
        content: '';
        width: 6px;
        height: 6px;
        border-radius: 50%;
        position: absolute;
        top: 7px;
        left: 0;
      }
    }
    .completed::before {
      background: #48C79C;
    }
    .afoot::before {
      background: #266FE8;
    }
    .not-started::before {
      background: #FF9649;
    }
  }
  .info-circle {
    width: 14px;
    height: 14px;
    cursor: pointer;
  }
  .third-party-box{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
}
/deep/.explenish-dialog{
  display: flex;
  .el-dialog {
    margin: auto !important;
  }
  .el-dialog__header{
    border: none;
  }
  .el-dialog__body{
    padding-top: 0;
  }
  .explenish-item{
   margin-bottom: 8px;
   display: flex;
  }
  .field-name{
    flex-shrink: 0;
    color: #00000099;
  }
  .value{
    margin-left: 8px;
  }
}
</style>
