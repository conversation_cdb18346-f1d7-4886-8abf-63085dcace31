<template>
  <div class="work-explanation" v-if="workInfo.title">
    <div class="basic-info">
      <div class="info-item">
        <span class="label">{{ $langue('Mooc_TaskDetail_HomeWork_HomeworkSubmitTime', { defaultText: '作业提交时间' }) }}：</span>
        <span class="value">
          {{ !workInfo.start_time && !workInfo.end_time ? '不限制' : `${workInfo.start_time || '不限制'} 至 ${workInfo.end_time || '不限制'}` }}
        </span>
      </div>
      <div class="info-item">
        <span class="label">{{ $langue('Mooc_TaskDetail_HomeWork_SupportResourceType', { defaultText: '支持提交类型' }) }}：</span>
        <span class="value" 
          v-for="(item, index) in workTypes" 
          :key="index"
          >
          {{item}}{{index === workTypes.length - 1 ? '' : '、'}}
        </span>
        <span class="info-item-btn" @click="todoWork('myWork')">{{ !unopenedState ? '查看作业' : '写作业' }}</span>
      </div>
      <div class="info-item" v-if="markTypeTwo('2')">
        <span class="label">{{ $langue('Mooc_TaskDetail_HomeWork_EvaluateTime', { defaultText: '作业互评时间' }) }}：</span>
        {{ !workInfo.student_mark_start_time && !workInfo.student_mark_end_time ? '不限制' : `${workInfo.student_mark_start_time || '不限制'} 至 ${workInfo.student_mark_end_time || '不限制'}` }}
      </div>
      <div class="info-item" v-if="markTypeTwo('2')">
        <span class="label">互评份数：</span>
        <span class="value">{{workInfo.student_review_count || 0}}/{{workInfo.student_mark_count || 0}}</span>
        <el-tooltip class="item" effect="dark" :content="`请至少批阅${workInfo.student_mark_count}份其他学员的作业`" placement="top">
          <img class="info-circle" style="width: 16px; height: 16px;" src="@/assets/mooc-img/info-circle.png" alt="">
        </el-tooltip>
        <el-button type="text" @click="todoWork('workEvaluation')">{{ $langue('Mooc_TaskDetail_HomeWork_Evaluate', { defaultText: '去互评' }) }}</el-button>
      </div>
      <div class="info-item" v-if="enableMark">
        <span class="label">作业总分：</span>
        <span class="value">{{workInfo.total_score}}</span>
      </div>
      <div class="info-item" v-if="workInfo.finished_condition.type !== '3'">
        <span class="label">合格总分：</span>
        <span class="value">{{workInfo.pass_score}}</span>
      </div>
      <div class="info-item" v-if="enableMark">
        <span class="label">{{$langue('Mooc_TaskDetail_HomeWork_EvaluateType', { defaultText: '评分方式' })}}：</span>
        <span class="value">
          <span v-if="markTypeTwo('1')">{{$langue('Mooc_TaskDetail_HomeWork_TeacherReview', { defaultText: '老师批阅' })}}{{ workInfo?.mark_type?.length > 1 ? `（${workInfo.teacher_mark_weight}%）；` : ''}}</span>
          <span v-if="markTypeTwo('2')">{{$langue('Mooc_TaskDetail_HomeWork_StudentEvaluate', { defaultText: '学员互评' })}}{{ workInfo?.mark_type?.length > 1 ? `（${workInfo.student_mark_weight}%）` : ''}}</span>
        </span>
      </div>
    </div>
    <div class="explanation-item" v-if="enableMark && !unopenedState">
      <div class="item-label">
        <span class="line"></span>
        <span class="label-text">{{ $langue('Mooc_TaskDetail_HomeWork_ReviewScore', { defaultText: '批阅分数' }) }}</span>
        <el-tooltip class="item" effect="dark" :content="$langue('Mooc_TaskDetail_HomeWork_100WeightScore', { defaultText: '某个分数为空时，先按照另一个分数100%权重计算总成绩' })" placement="top">
          <img class="info-circle" style="width: 16px; height: 16px;" src="@/assets/mooc-img/info-circle.png" alt="">
        </el-tooltip>
      </div>
      <div class="item-value">
        <div class="definite">
          <p class="definite-item" v-if="markTypeTwo('2') && markTypeTwo('1')">
            <span class="value-label">{{ $langue('Mooc_TaskDetail_HomeWork_TotalScore', { defaultText: '总成绩' }) }}：</span>
            <span v-if="workInfo.score" class="num-r num-r1">{{workInfo.score}}</span>
            <span v-else>-</span>
          </p>
          <p class="definite-item" v-for="item in workInfo.mark_list" :key="item.mark_type">
            <span class="value-label">{{$langue(item.mark_type === '1' ? 'Mooc_TaskDetail_HomeWork_TeacherScore' : 'Mooc_TaskDetail_HomeWork_StudentScore' , { defaultText: item.mark_type === '1' ? '老师分数' : '互评分数'})}}：</span>
            <span v-if="item.score" class="num-r num-r2">{{item.score}}</span>
            <span v-else>-</span>
          </p>
          <!-- <p class="definite-item" v-if="markTypeTwo('2')">
            <span class="value-label">{{$langue('Mooc_TaskDetail_HomeWork_StudentScore', { defaultText: '互评分数' })}}：</span>
            <span v-if="workInfo.student_score" class="num-r num-r3">{{workInfo.student_score}}</span>
            <span v-else>-</span>
          </p> -->
        </div>
      </div>
    </div>
    <div class="explanation-item">
      <div class="item-label">
        <span class="line"></span>
        <span class="label-text">完成条件</span>
      </div>
      <div class="item-value">
        <div class="completion">
          <span v-for="(item, index) in conditionList" :key="index" class="radio-item">
            <img v-if="item.value === true" class="img" style="width:16px;height:16px" :src="require('@/assets/mooc-img/fulfil.png')" alt="">
            <img v-else-if="item.value === false" class="img" style="width:16px;height:16px" :src="require('@/assets/mooc-img/mistake.png')" alt="">
            <span v-else class="completion-wait"></span>
            <span class="radio-text">{{ item.text }}</span>
          </span>
        </div>
      </div>
    </div>
    <div class="explanation-item">
      <div class="item-label">
        <span class="line"></span>
        <span class="label-text">{{ $langue('Mooc_TaskDetail_ThirdParty_TaskStatus', { defaultText: '任务状态' }) }}</span>
      </div>
      <div class="item-value">
        <span class="task-status-icon" :class="taskProcess.className">{{ taskProcess.text }}</span>
        <span v-if="taskProcess.className === 'done-icon'">{{ $langue('Mooc_ProjectDetail_TrainingProgress_FinishedTime', { defaultText: '完成时间：' }) }}{{ workInfo.finished_time || '-' }}</span>
      </div>
    </div>
    <div class="reference-data">
      <div class="item-label">
        <span class="line"></span>
        <span class="label-text">{{ $langue('Mooc_TaskDetail_HomeWork_ReferenceFiles', { defaultText: '参考资料' }) }}</span>
      </div>
      <div class="down-list clearfix">
        <div class="left-content" v-for="item in workInfo.homework_attachments" :key="item.id">
          <img :src="resourceImg(item.file_type,item.file_name)" alt="">
          <div class="right-box">
            <div class="right-top">
              <span class="study-title">{{ item.file_name }}</span>
              <p>
                <span :class="[item.file_type === 'Zip' ? 'disable-view' : '','btn-span']" @click="handlePreview(item)">{{ $langue('Mooc_ProjectDetail_Documents_View', { defaultText: '查看' }) }}</span>
                <span :class="['btn-span down-btn']"  @click="urlForDownload(item)">{{ $langue('Mooc_ProjectDetail_Documents_DownLoad', { defaultText: '下载' }) }}</span>
              </p>
            </div>
            <div class="right-botttom">
              <span class="size">{{ item.file_size | bytesToSize }}</span>
              <span class="time">{{ item.created_at }}</span>
            </div>
          </div>
        </div>
        <div class="empty" v-if="workInfo.homework_attachments.length == 0">
          <img class="empty-img" src="@/assets/img/empty.png" alt="" />
          <div class="empty-text">{{ $langue('Mooc_ProjectDetail_Notice_NoData', { defaultText: '暂无内容' }) }}</div>
        </div>
      </div>
    </div>
    <div class="explanation-item">
      <div class="item-label">
        <span class="line"></span>
        <span class="label-text">{{ $langue('Mooc_TaskDetail_HomeWork_HomeWorkRequirements', { defaultText: '作业要求' }) }}</span>
      </div>
      <div class="item-value work-require" v-if="workInfo.desc">
        <sdc-mce-preview ref="preview" :urlConfig="editorConfig.urlConfig" :catalogue.sync="editorConfig.catalogue" :content="workInfo.desc">
        </sdc-mce-preview>
      </div>
      <div class="empty" v-else>
        <img class="empty-img" src="@/assets/img/empty.png" alt="" />
        <div class="empty-text">{{ $langue('Mooc_Common_Alert_NoData', { defaultText: '暂无内容' }) }}</div>
      </div>
    </div>
    <!-- 参考资料预览弹窗 -->
    <workPreviewDialog ref="workPreviewDialogRef" />
  </div>
</template>
<script>
import workPreviewDialog from '@/views/components/work-preview-dialog'
import { operatesignature } from 'config/api.conf'
import { workSourceInfo } from 'config/mooc.api.conf'
export default {
  name: '',
  components: {
    workPreviewDialog
  },
  props: ['workInfo', 'record_id'],
  data() {
    return {
      editorConfig: {
        catalogue: false,
        options: {
          // 非必传字段
          selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
                    formatselect fontsizeselect lineheight |
                    bold italic underline strikethrough |
                    forecolor backcolor |
                    dent align bullist numlist |
                    insert codesample |
                    fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      }
    }
  },
  computed: {
    conditionList() {
      let conditionList = [
        { text: this.$langue('Mooc_TaskDetail_HomeWork_SubmitHomeWork', { defaultText: '提交作业' }), value: 0, type: '3' },
        { text: this.$langue('Mooc_TaskDetail_HomeWork_ReviewPass', { defaultText: '批阅合格' }), value: 0, type: '4' },
        { text: this.$langue('Mooc_TaskDetail_HomeWork_FinishedEvaluate', { defaultText: '完成互评' }), value: 0, type: '5' }
      ]
      let newArr = []
      if (this.workInfo.finished_condition) {
        let { type = '', submit_homework_finished, teacher_mark_finished, student_mark_finished } = this.workInfo.finished_condition
        let types = (type && type.split(';')) || []
        conditionList.forEach(item => {
          if (types.includes(item.type)) {
            let value = 0
            switch (item.type) {
              case '3':
                value = submit_homework_finished
                break
              case '4':
                value = teacher_mark_finished
                break
              case '5':
                value = student_mark_finished
                break
            }
            newArr.push({
              ...item,
              value
            })
          }
        })
      }
      return newArr
    },
    // 学员批阅 2  老师批阅 1
    markTypeTwo() {
      return (type) => {
        const markType = this.workInfo?.mark_type?.split(';')
        return markType.includes(type)
      }
    },
    taskProcess() {
      let className = ''
      let text = ''
      if (this.workInfo?.task_status) {
        className = 'done-icon' // 已完成
        text = this.$langue('Mooc_ProjectDetail_TaskList_TaskFinished', { defaultText: '任务已完成' })
      } else {
        // loading-icon 进行中 / wait-icon 未开始
        className = this.workInfo.task_status === false ? 'loading-icon' : 'wait-icon'
        text = this.workInfo.task_status === false ? this.$langue('Mooc_ProjectDetail_TaskList_TaskInProgress', { defaultText: '任务进行中' }) : this.$langue('Mooc_ProjectDetail_TaskList_TaskNotStart', { defaultText: '任务未开始' })
      }
      return { className, text }
    },
    resourceImg() {
      let imgName = ''
      return (type, name) => {
        switch (type) {
          case 'Video':
            imgName = 'icon-video'
            break
          case 'Audio':
            imgName = 'icon-mp3'
            break
          case 'Image':
            imgName = 'icon-jpg'
            break
          case 'Zip':
            imgName = 'icon-zip'
            break
          case 'Doc' :
            const name_subtr = name.lastIndexOf('.') && name.substr(name.lastIndexOf('.') + 1) 
            switch (name_subtr) {
              case 'doc' : case 'docx' :
                imgName = 'icon-word'
                break
              case 'ppt' : case 'pptx':
                imgName = 'icon-ppt'
                break
              case 'pdf' :
                imgName = 'icon-pdf'
                break
              case 'xls' : case 'xlsx' :
                imgName = `icon-excel`
                break
            }
            break
        }
        return imgName ? require(`@/assets/mooc-img/${imgName}.png`) : require('@/assets/img/default_bg_img.png')
      }
    },
    workTypes() {
      // （ Text：文本  Image：图片 Audio：音频  Video：视频 Doc：文档 Zip：压缩包）
      let workTypes = this.workInfo?.work_types?.split(';') || []
      let type = {
        Image: '图片',
        Text: '文本',
        Audio: '音频',
        Video: '视频',
        Doc: '文档',
        Zip: '压缩包'
      }
      return workTypes.map((item) => {
        return type[item]
      })
    },
    // 作业批阅
    enableMark() {
      return this.workInfo.enable_mark
    },
    // 未提交状态
    unopenedState() {
      return [null, 0, 2].includes(this.workInfo.status)
    }
  },
  filters: {
    bytesToSize (b) {
      if (b === 0) return '0B'
      let k = 1024
      let size = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      let i = Math.floor(Math.log(b) / Math.log(k))
      return (b / Math.pow(k, i)).toPrecision(3) + size[i]
    }
  },
  mounted() {},
  methods: {
    todoWork(val) {
      if (this.unopenedState && val === 'workEvaluation') {
        if (this.workInfo.status === 2) {
          this.$message.warning('请重新提交作业，然后参与互评')
        } else {
          this.$message.warning('请先提交作业，然后参与互评')
        }
        return
      }
      this.$emit('changeTab', val)
    },
    // 预览
    handlePreview(item) {
      const { act_id } = this.$route.query
      let curItem = {
        content_id: item.content_id,
        resource_type: item.file_type,
        resource_name: item.file_name,
        file_size: Math.ceil((item.file_size / 1048576) * 10) / 10 + 'M', // 保留1位小数
        mooc_course_id: act_id,
        allow_download: true
      }
      if (item.file_type === 'Zip') return
      this.$nextTick(() => {
        this.$refs.workPreviewDialogRef.handleViewFile(curItem)
      })
    },
    // 获取下载文件源地址
    urlForDownload(obj) {
      const signatureParams = {
        content_id: obj.content_id,
        operate: 'download'
      }
      operatesignature(signatureParams).then((signature) => {
        workSourceInfo(obj.content_id, {
          app_id: 'QLearningService',
          signature: signature
        }).then((res) => {
          this.getBlob(res).then((bolb) => {
            this.saveAs(bolb, obj.file_name)
          })
        })
      })
    },
    // 获取 blob 格式文件
    getBlob(url) {
      return new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', url, true)
        xhr.responseType = 'blob'
        xhr.onload = () => {
          if (xhr.status === 200) {
            resolve(xhr.response)
          }
        }
        xhr.send()
      })
    },
    // 文件下载
    saveAs(blob, filename) {
      let link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      link.click()
    }
  }
}
</script>
<style lang='less' scoped>
.work-explanation {
  margin-bottom: 18px;
}
.info-circle {
  position: relative;
  top: -1px;
  margin-left: 8px;
}
.basic-info {
  padding: 16px;
  background: #fcfcfcff;
  display: flex;
  flex-wrap: wrap;

  .info-item {
    width: 50%;
    line-height: 24px;
    margin-bottom: 12px;
    .el-button--text {
      padding: 0 20px;
    }
    .label {
      color: #00000099;
      margin-right: 6px;
      width: 100px;
      display: inline-block;
      text-align: right;
    }
    .info-item-btn {
      color: #0052d9;
      cursor: pointer;
      margin-left: 10px;
    }
  }
}
.explanation-item {
  color: #00000099;
  margin-top: 32px;
  .work-require {
    word-break: break-all;
  }
  .item-value {
    padding-left: 16px;
    margin-top: 16px;
    color: #000000e6;
    .completion {
      display: flex;
      .completion-wait {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        border: 1px solid #dcdcdc;
        margin-right: 8px;
      }
    }
    .radio-item {
      margin-right: 32px;
      display: flex;
      align-items: center;
      .img {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
    }
  }

  .task-status-icon {
    position: relative;
    padding-left: 24px;
    margin-right: 20px;
    white-space: break-spaces;
    &::before {
      content: '';
      width: 18px;
      height: 18px;
      display: inline-block;
      position: absolute;
      top: 1px;
      left: 0;
    }
  }

  .wait-icon {
    color: #00000099;
    &::before {
      background: url('~@/assets/mooc-img/wait.png') no-repeat center / cover;
    }
  }

  .loading-icon {
    color: #0052d9;
    &::before {
      background: url('~@/assets/mooc-img/loading.png') no-repeat center / cover;
    }
  }

  .done-icon {
    color: #00a870ff;
    &::before {
      background: url('~@/assets/mooc-img/done.png') no-repeat center / cover;
    }
  }
  .definite {
    display: flex;
    .definite-item {
      display: flex;
      align-items: center;
    }
    p {
      padding-right: 32px;
      color: #00000099;
      .value-item {
        font-size: 12px;
      }
    }
    .num-r {
      display: inline-block;
      width: 40px;
      height: 20px;
      line-height: 20px;
      border-radius: 2px;
      text-align: center;
      font-size: 12px;
    }
    .num-r1 {
      background-color: #ccf2e2ff;
      color: #00b368ff;
    }
    .num-r2 {
      background-color: #fef3e6ff;
      color: #ed7b2fff;
    }
    .num-r3 {
      color: #0052d9ff;
      background-color: #d4e3fcff;
    }
  }
}
.explanation-item,.reference-data {
  .item-label {
    display: flex;
    align-items: center;
    .line {
      display: inline-block;
      width: 4px;
      height: 16px;
      background: #0052d9ff;
      margin-right: 12px;
    }
  }
  .empty {
    padding-top: 20px;
    color: #999;
    text-align: center;
    .empty-img {
      margin-bottom: 20px;
      width: 178px;
      height: 130px;
    }
  }
}
.reference-data {
  margin-top: 32px;
  .down-list {
    .btn-span {
      color: #0052d9;
      cursor: pointer;
    }
    .btn-span:hover {
      color: #5d83e6;
    }
    .down-btn {
      margin-left: 12px;
    }
    .disable-view {
      color: #00000042;
    }
    .disable-view:hover {
      color: #00000042;
      cursor: not-allowed;
    }
    .left-content {
      width: 415px;
      position: relative;
      float: left;
      display: flex;
      align-items: center;
      padding: 16px 20px;
      margin-bottom: 16px;
      img {
        width: 48px;
        height: 48px;
      }
      .right-box {
        padding-left: 8px;
        flex: 1;
        width: 88%;
        .right-top,
        .right-botttom {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 22px;
          line-height: 22px;
          .study-title {
            display: inline-block;
            width: 75%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .right-botttom {
          height: 20px;
          line-height: 20px;
          .size,
          .time {
            font-size: 12px;
            color: #00000066;
          }
        }
      }
    }
    .last-left-content {
      margin-right: unset;
    }
  }
  .clearfix:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
}
</style>
