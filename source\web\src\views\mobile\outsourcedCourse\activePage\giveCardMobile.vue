<template>
  <!-- 送出学霸卡弹窗 -->
  <van-popup class="card-detail-pop" @open="showToast" v-model="showCardDetailGive" position="bottom" round closeable safe-area-inset-bottom>
    <div class="title">{{giveType === 'account' ? '内容分享' : '送出学霸卡'}} </div>
    <div>
      <div class="share-content">
        <div class="share-content-title" v-if="giveType === 'account'">链接分享：访问链接，即可加入学习哈佛管理导师相关内容（已邀请 <span style="color:#0052d9ff">{{giveNumber}}</span> 人，共可邀请10人）</div>
        <div class="share-content-title" v-else>链接分享：被分享者访问链接，即可领取学霸卡</div>
        <div class="share-box">
          <div class="share-rq-code">
            <img id="codeImg" :src="qrUrl" alt="img" srcset="">
          </div>
          <div class="share-url-box">
            <span class="copy-btn" style="line-height: 24px;" 
            @click="handleCopyImg"
            :dt-areaid="dtButton('area')"
            :dt-eid="dtButton('eid')"
            :dt-remark="dtButton('remark', '复制二维码图片')"
            >复制二维码图片</span>
            <div class="url-text">
              <el-input size="small" style="padding-right: 16px;" v-model="urlText" type="text" disabled></el-input>
              <span class="copy-btn" @click="doCopy"
              :dt-areaid="dtButton('area')"
              :dt-eid="dtButton('eid')"
              :dt-remark="dtButton('remark', '复制专属分享链接')"
              >复制专属分享链接</span>
            </div>
          </div>
        </div>
      </div>
      <div class="give-header" v-if="giveType === 'xueba'">
        <!-- <div class="give-header_tips">
          已送出 <span class="b_0052D9">{{giveNumber}}</span> 张学霸卡（共可送出 10 张），获得奖励 <span class="b_0052D9">{{numberOfRewards}}</span> 张「{{xueBaCardConfig.card_name}}」专用卡
        </div> -->
        <div class="give-header-title">
          {{giveType === 'account' ? '定向分享：选中分享对象，系统将向其推送邀约信息' : '定向分享：选中分享对象，向其赠送学霸卡'}}
        </div>
        <div class="give-header_search">
          <div> <input class="header_search-input" v-model="serachData.staffName" type="text" placeholder="请输入员工英文名"> </div>
          <div> <input class="header_search-input" v-model="serachData.orgName" type="text" placeholder="请输入组织架构关键词"> </div>
          <div class="give-header_search_btn">
            <span class="give-header_search_btn_r" @click="handelrReset">重置</span>
            <span class="give-header_search_btn_s" @click="onSubmit"
            :dt-areaid="dtButton('area')"
            :dt-eid="dtButton('eid')"
            :dt-remark="dtButton('remark', '搜索')"
            >搜索</span>
          </div>
        </div>
      </div>
      <div class="select-tips-content" v-if="selectList.length">
        <div class="select-tips-item" v-for="(item, index) in selectList" :key="index">
            {{item.staff_name}} <i class="el-icon-close" @click="handelrCloseName(item)"></i>
        </div>
        <div class="select-tips-item" @click="handelrCloseName('all')">
          全部清除<i class="el-icon-close"></i>
        </div>
      </div>
      <div class="table" :style="selectList.length ? 'margin-top: 0' : ''" v-if="giveType === 'xueba'">
        <el-table ref="multipleTable" :data="tableData" @selection-change="handleSelectionChange" @select="handleSelect" :header-cell-style="headerCellStyle" style="width: 100%">
          <el-table-column type="selection" width="45" :selectable="checkSelectable">
          </el-table-column>
          <el-table-column label="员工姓名" width="80">
            <template slot-scope="scope">
              <span class="table-ceel_content">{{scope.row.staff_name}}</span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="所属组织" >
            <template slot-scope="scope">
              <span class="table-ceel_content">{{scope.row.org_name}}</span>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="80">
            <template slot-scope="scope">
              <span class="table-ceel_content">
                <span :style="canGetReward(scope.row).style">{{ canGetReward(scope.row).name }}</span>
              </span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <template v-if="giveType === 'xueba'">
      <div class="foot-page" >
        <Pagination v-model="currentPage" :page-count="pageCount" mode="simple" @change="pageChange">
          <template #prev-text>
            <span class="prev-btn">
              <van-icon name="arrow-left" style="margin-right: 4px" /> 上一页
            </span>
          </template>
          <template #next-text>
            <span class="next-btn">
              下一页<van-icon name="arrow" style="margin-left: 4px" />
            </span>
          </template>
          <template #pageDesc>
            <span class="page-desc">
              <span class="current">{{ currentPage }}</span> / {{ pageCount }}
            </span>
          </template>
        </Pagination>
      </div>
      <div :class="['give-btn', {'give-btn-dis': isDisabled}]" @click="openGivePop">
        {{giveType === 'account' ? '邀请学习' : '送出学霸卡' }}
      </div>
    </template>
    <van-popup class="give-popup-tips" v-model="show" :style="{ padding: '64px' }">
      <div class="tips-title">提示</div>
      <div class="tips-content" v-if="surplusGiven">
      {{ giveType === 'account' ? `超出可分享数量，当前剩余可分享 ${consumePoint} 次` : `超出可送出学霸卡数量，当前剩余可送出${consumePoint}张学霸卡` }}
      </div>
      <div class="tips-content" v-else>
        <span v-if="giveType === 'account'">是否确认邀请{{names}}，{{selectList.length}}人分享学习</span>
        <span v-else>是否确认送出 {{selectList.length || 0}} 张学霸卡，送出后将获得劝学奖励 {{obtainUnmber}} 张「{{card_name}}」专用卡？</span>
      </div>
      <div class="tips-footer" v-if="surplusGiven">
        <div class="tips-footer_big" @click="colseTips">确认</div>
      </div>
      <div class="tips-footer" v-else>
        <div class="tips-footer_btn tips-footer_colse" @click="colseTips">取消</div>
        <div class="tips-footer_btn tips-footer_confirm" @click="handlerConfirm">确认</div>
      </div>
    </van-popup>
  </van-popup>
</template>

<script>
import {
  getActiveStaffs,
  activityPresent,
  getMobileQrcode
} from '@/config/mooc.api.conf.js'
import { Toast, Pagination } from 'vant'
import { mapState } from 'vuex'
const defaultSearch = {
  staffName: '',
  orgName: ''
}
export default {
  props: {
    giveNumber: {
      type: Number,
      default: 0
    },
    isShowGive: {
      type: Boolean,
      default: false
    },
    numberOfRewards: {
      type: Number,
      default: 0
    },
    xueBaCardConfig: {
      type: Object,
      default: () => {}
    },
    consumePoint: {
      type: Number,
      default: 0
    },
    giveType: {
      type: String,
      default: 'xueba'
    }
  },
  components: { Pagination },
  data() {
    return {
      show: false,
      serachData: Object.assign({}, defaultSearch),
      currentPage: 1,
      pageSize: 5,
      pageCount: 0,
      tableData: [],
      qrUrl: '',
      selectList: []
    }
  },
  created() {},
  mounted() {
    setTimeout(() => {
      // this.getActiveStaffs('first')
      this.getMobileCode()
    }, 1000)
  },
  computed: {
    showCardDetailGive: {
      set(val) {
        this.$emit('update:isShowGive', val)
      },
      get() {
        return this.isShowGive
      }
    },
    ...mapState(['userInfo']),
    names() {
      let names = this.selectList.map(item => item.staff_name).join(';')
      return names
    },
    isDisabled() {
      return !this.selectList.length
    },
    card_name() {
      return this.xueBaCardConfig.card_name
    },
    activityId() {
      return this.xueBaCardConfig.activity_id || this.$route.query.activityId
    },
    urlText() {
      let url = this.giveType === 'account' ? 'https://sdc.qq.com/s/PTUrrm?scheme_type=harvard' : 'https://sdc.qq.com/s/hxbgLe?scheme_type=xueba'
      return `${url}&activityId=${this.activityId}&staff_name=${this.userInfo.staff_name}&staff_id=${this.userInfo.staff_id}`
    },
    headerCellStyle() {
      return {
        background: '#F5F5F5',
        color: '#********',
        fontSize: '12px',
        fontWeight: '500'
      }
    },
    // 剩余赠送
    surplusGiven() {
      // return 10 - (this.selectGiveList.length + this.giveNumber)
      return this.selectList.length > this.consumePoint
    },
    // 送出后获得
    obtainUnmber() {
      const giveNumbers =
        this.selectList.filter(
          (item) => item.can_get_reward && item.can_present
        ) || []
      let RewardsNum = 0
      if (giveNumbers.length <= (3 - this.numberOfRewards)) {
        RewardsNum = giveNumbers.length
      } else {
        RewardsNum = 3 - this.numberOfRewards
      }
      return RewardsNum
    },
    canGetReward() {
      return (val) => {
        // 是否可以领取奖励can_get_reward, 是否可以赠送can_present
        const { can_get_reward, can_present } = val
        if (this.giveType === 'account') {
          if (can_present) {
            return { name: '可接受邀请', style: 'color: #00A870' }
          } else {
            return { name: '已有权限', style: 'color: #E34D59' }
          }
        } else {
          if (can_get_reward && can_present) {
            return {
              name: `送出后将获得 1 张「${this.xueBaCardConfig.card_name}」专用卡`,
              style: 'color: #00A870'
            }
          } else if (!can_present) {
            return { name: '受赠额度已满，不可赠送', style: 'color: #E34D59' }
          } else if (!can_get_reward) {
            return { name: '送出后无奖励', style: 'color: #E34D59' }
          }
        }
      }
    },
    // 埋点
    dtButton() {
      return (type, name) => {
        const { activity_id, audience_id, audience_name, card_name } = this.xueBaCardConfig
        if (type === 'area') {
          return `area_${audience_id}_${activity_id}`
        } else if (type === 'eid') {
          return `element_${audience_id}_${activity_id}`
        } else if (type === 'remark') {
          return JSON.stringify({ 
            page: `${card_name}活动首页`,
            page_type: `${card_name}活动首页`,
            container: audience_name,
            container_id: '',
            click_type: 'button',
            content_name: name,
            terminal: 'H5'
          })
        }
      }
    }
  },
  methods: {
    handelrCloseName(val) {
      if (val === 'all') {
        this.selectList = []
        this.$refs.multipleTable.clearSelection()
        return
      }
      this.selectList = this.selectList.filter(item => item.staff_id !== val.staff_id)
      this.$refs.multipleTable.toggleRowSelection(val, false)
    },
    handleSelect(selection, row) {
      console.log(selection, row, 'selectionselectionselection')
      // 如果selection是空数组说明当前行是取消勾选,总数据中需要对比row进行移除
      if (selection.length === 0) {
        this.selectList = this.selectList.filter(item => item.staff_id !== row.staff_id)
      } else {
        // 判断row.staff_id是否在selection里面如果不在说明是取消进行移除
        const isSelect = selection.some(item => item.staff_id === row.staff_id)
        if (!isSelect) {
          this.selectList = this.selectList.filter(item => item.staff_id !== row.staff_id)
        } else {
          // isSelect ：true 新增勾选
          this.selectList.push(row)
        }
      }
    },
    openGivePop() {
      if (this.selectList.length === 0) return
      this.show = true
    },
    getMobileCode() {
      const params = {
        scene: `${this.activityId}_${this.userInfo.staff_id}_${this.userInfo.staff_name}_${this.xueBaCardConfig.acct_type_code}`,
        page: 'pages/webview/active/index',
        env_version: process.env.NODE_ENV === 'production' ? 'release' : 'trial'
      }
      getMobileQrcode(params).then((res) => {
        this.qrUrl = res ? `data:image/png;base64,${res}` : ''
      })
    },
    /* eslint-disable */
    handleCopyImg() {
      location.origin.includes(`https://`) ||
        this.$message.error(`图片复制功能需要在https://协议下使用`)
      let base64Data = this.qrUrl.split(';base64,')
      let type = base64Data[0].split('data:')[1]
      base64Data = base64Data[1]
      // 将base64转为Blob类型
      let bytes = atob(base64Data),
        ab = new ArrayBuffer(bytes.length),
        ua = new Uint8Array(ab)
      ;[...Array(bytes.length)].forEach((v, i) => (ua[i] = bytes.charCodeAt(i)))
      let blob = new Blob([ab], { type })
      // “navigator.clipboard.write”该方法的确只能在本地localhost 、127.0.0.1 或者 https 协议下使用，否则navigator没有clipboard方法。
      navigator.clipboard.write([new ClipboardItem({ [type]: blob })])
      Toast({
        message: '复制成功',
        icon: 'passed'
      })
    },
    doCopy() {
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      input.value = this.urlText
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      Toast({
        message: '复制成功',
        icon: 'passed'
      })
      // 复制后移除输入框
      input.remove()
    },
    // 处理时间数据
    resolveTime(time) {
      console.log(time, 'resolveTimeresolveTime')
      if (!time) return ''
      let srt = time.split(':').slice(0, -1).join(':')
      return srt.replace(/-/g, '/')
    },
    showToast() {
      this.serachData = Object.assign({}, defaultSearch)
      this.currentPage = 1
      this.getActiveStaffs()
    },
    async getActiveStaffs(first) {
      // const dept_full_name_last =
      //   this.$store.state.userDepInfo?.dept_full_name?.split('/')
      // console.log(dept_full_name_last, 'dept_full_name_last')
      // if (!this.serachData.orgName && first) {
      //   this.serachData.orgName =
      //     dept_full_name_last[dept_full_name_last.length - 1]
      // }
      const { staffName, orgName } = this.serachData
      let params = {
        current: this.currentPage,
        size: this.pageSize,
        activityId: this.activityId,
        staffName: staffName,
        orgName: orgName
      }
      const res = await getActiveStaffs(params)
      this.tableData = res?.records || []
      this.pageCount = res?.pages || 0
      this.$nextTick(() => {
        this.tableData.forEach(item => {
          if (this.selectList.some(s => s.staff_id === item.staff_id)) {
            console.log('111')
            this.$refs.multipleTable.toggleRowSelection(item, true)
          }
        })
      })
      console.log(res, 'dadasdasda')
    },
    handelrReset() {
      this.serachData = Object.assign({}, defaultSearch)
      this.currentPage = 1
      this.getActiveStaffs()
    },
    onSubmit() {
      const { staffName, orgName } = this.serachData
      if (!staffName && !orgName) return Toast('请输入内容')
      this.currentPage = 1
      this.getActiveStaffs()
    },
    handleSelectionChange(val) {
      console.log(val, '选中项')
    },
    // 券领取详情分页改变
    pageChange(page) {
      this.currentPage = page
      this.getActiveStaffs()
    },
    colseTips() {
      this.show = false
    },
    handlerConfirm() {
      let params = {
        from: this.userInfo.staff_id,
        from_name: this.userInfo.staff_name,
        acct_type_code: this.xueBaCardConfig.acct_type_code,
        to_batch: this.selectList.map((item) => item.staff_id),
        object_id: this.xueBaCardConfig.activity_id,
        object_name: this.xueBaCardConfig.activity_name,
        object_type: 'XuebaActivity',
        object_type_name: '活动',
        trans_amt: '1',
        notify_type: 1 // 手动赠送1 ，用户进入页面自动领的 0
      }
      activityPresent(params).then((res) => {
        let message = `成功送出学霸卡 ${res.success_count} 张，获得劝学奖励 ${res.reward_count} 张「${this.xueBaCardConfig.card_name}」专用卡；`
        let accountMsg = `邀请成功 ${res.success_count} 人,邀请信息将通过到企微Tips和机器人消息推送至受邀人；`
        if (Number(res.fail_count) > 0) {
          message = `${message}\n送出失败${res.fail_count} 张，失败人员：${res.fail_names || '-'}`
          accountMsg = `${accountMsg}\n邀请失败${res.fail_count}人，失败人员：${res.fail_names || '-'}`
        }
        Toast({
          type: res.success_count ? 'success' : 'fail',
          message: this.giveType === 'account' ? accountMsg : message,
          duration: 5000,
          closeOnClick: true
        })
        this.colseTips()
        this.$emit('update:isShowGive', false)
        this.$emit('handlerGiveXuebaka')
        this.handelrCloseName('all') // 清空全部选中
      })
    },
    checkSelectable(row) {
      return row.can_present
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.el-table th.gutter {
  background-color: #f5f5f5;
}
/* 定义checkbox的选中颜色 */
/deep/.el-checkbox__input.is-checked .el-checkbox__inner,
/deep/.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #0052d9;
  border-color: #0052d9;
}

/* 定义checkbox未选中时的边框颜色 */
// /deep/.el-checkbox__input .el-checkbox__inner {
//   border-color: #0052D9;
// }

/* 定义checkbox的对勾颜色 */
.el-checkbox__input.is-checked .el-checkbox__inner::after {
  color: #fff;
}
.b_0052D9 {
  color: #0052d9;
}
.card-detail-pop {
  //   max-height: 396px;
  padding: 16px 0;
  .title {
    color: #000000e6;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    line-height: 26px;
    padding-bottom: 16px;
    border-bottom: 0.5px solid #eee;
  }
  .share-content {
    padding: 12px 16px;
    &-title {
      color: #000000e6;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 22px;
      margin-bottom: 12px;
    }
    .share-box {
      display: flex;
      border-bottom: 1px solid #e7e7e7;
      padding-bottom: 12px;
      .share-rq-code {
        width: 64px;
        height: 64px;
        background: #f5f5f5;
        border-radius: 6px;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
          width: 52px;
          height: 52px;
          border-radius: 6px;
        }
      }
      .share-url-box {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin-left: 16px;
        flex: 1;
        .url-text {
          display: flex;
        }
      }
      .copy-btn {
        color: #0052d9;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px;
        cursor: pointer;
        white-space: nowrap;
      }
    }
  }
  .give-header {
    padding: 0 16px;
    &-title {
      color: #000000e6;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 22px;
      padding-bottom: 12px;
    }
    &_tips {
      border-radius: 4px;
      background: #f9f9f9;
      padding: 6px 12px;
      color: #666666;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      margin-bottom: 16px;
    }
    .give-header_search {
      .header_search-input {
        font-size: 12px;
        width: 100%;
        height: 32px;
        line-height: 32px;
        padding: 4px 12px;
        border-radius: 4px;
        border: 0.5px solid #eee;
        background: #f5f5f5;
        margin-bottom: 10px;
      }
      &_btn {
        display: flex;
        justify-content: space-between;
        span {
          width: 44%;
          height: 32px;
          line-height: 32px;
          text-align: center;
          border-radius: 16px;
        }
        &_r {
          color: #********;
          background-color: #f6f6f6;
        }
        &_s {
          color: #ffffff;
          background-color: #0052d9;
        }
      }
    }
  }
  .table {
    margin-top: 16px;
    .table-ceel_content {
      color: #000000e6;
      font-family: 'PingFang SC';
      font-size: 10px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
    }
    /deep/.el-table td,
    /deep/.el-table th {
      padding: 9px 0;
    }
    /deep/.el-table .cell {
      line-height: 16px;
    }
    /* 去掉hover效果 */
    /deep/.el-table .el-table__body tr.el-table__row:hover {
      background-color: #fff;
    }
    /deep/.el-table--enable-row-hover .el-table__body tr:hover > td {
      background-color: #fff;
    }
    /deep/.el-checkbox__inner {
      width: 16px;
      height: 16px;
      border-radius: 3px;
    }
    .table-title {
      width: 100%;
      //   display: flex;
      height: 38px;
      line-height: 38px;
      color: #********;
      font-size: 12px;
      font-weight: 500;
      background-color: #f5f5f5;
      & > div {
        display: inline-block;
        padding-left: 16px;
      }
    }
    .item-content {
      max-height: 300px;
      overflow: auto;
    }
    .item {
      display: flex;
      color: #000000e6;
      height: 60px;
      line-height: 60px;
      box-shadow: 0 -1px 0 0 #eee inset;
      & > div {
        font-size: 10px;
        padding-left: 14px;
        &:nth-child(n + 3) {
          padding-left: 16px;
        }
      }
    }
    .no {
      display: flex;
      align-items: center;
      width: 56px;
    }
    .time {
      width: 99px;
    }
    .status {
      width: 192px;
    }
    .status-text {
      line-height: 16px;
      text-align: center;
      border-radius: 4px;
      padding: 1px 8px;
      font-size: 10px;
    }
    .status-no-effict {
      color: #ed7b2f;
      border: 1px solid var(---Warning5-Normal, #ed7b2f);
      background: var(---Warning1-Light, #fef3e6);
    }
    .status-waite-use {
      color: #0052d9;
      border: 1px solid var(---Brand8-Normal, #0052d9);
      background: var(---Brand1-Light, #ecf2fe);
    }
    .status-oready-used {
      color: #00a870;
      border: 1px solid var(---Success5-Normal, #00a870);
      background: var(---Success1-Light, #e8f8f2);
    }
    .desc {
      padding: 0 16px;
      width: 116px;
      text-align: left !important;
      // width: 235px;
    }
    // .course-link {
    //   white-space: nowrap;
    //   cursor: pointer;
    //   color: #0052d9;
    //   padding-right: 16px;
    // }
  }
  .foot-page {
    margin-top: 20px;
    padding: 0 16px;
    .prev-btn {
      color: #********;
      font-size: 12px;
      line-height: 20px;
    }
    .next-btn {
      color: #0052d9;
      font-size: 12px;
      line-height: 20px;
    }
    .page-desc {
      color: #000000e6;
      font-size: 12px;
      line-height: 20px;
      padding: 6px 16px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      .current {
        color: #0052d9;
      }
    }
    /deep/.van-pagination__prev {
      height: 32px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      background: var(---White, #fff);
    }
    /deep/.van-pagination__page-desc {
      height: 32px;
    }
    /deep/.van-pagination__next {
      height: 32px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      background: #f3f7ff;
    }
  }
  /deep/.van-popup__close-icon--top-right {
    color: #000000;
    top: 23px;
    right: 22px;
  }
  /deep/.van-popup__close-icon {
    font-size: 12px;
    font-weight: 700;
  }
}
.foot-page {
  margin-top: 20px;
  padding: 0 16px;
  .prev-btn {
    color: #********;
    font-size: 12px;
    line-height: 20px;
  }
  .next-btn {
    color: #0052d9;
    font-size: 12px;
    line-height: 20px;
  }
  .page-desc {
    color: #000000e6;
    font-size: 12px;
    line-height: 20px;
    padding: 6px 16px;
    border-radius: 6px;
    border: 0.5px solid var(---Gray3-, #e7e7e7);
    .current {
      color: #0052d9;
    }
  }
  /deep/.van-pagination__prev {
    height: 32px;
    border-radius: 6px;
    border: 0.5px solid var(---Gray3-, #e7e7e7);
    background: var(---White, #fff);
  }
  /deep/.van-pagination__page-desc {
    height: 32px;
  }
  /deep/.van-pagination__next {
    height: 32px;
    border-radius: 6px;
    border: 0.5px solid var(---Gray3-, #e7e7e7);
    background: #f3f7ff;
  }
}
.give-btn {
  margin: 16px 16px 4%;
  background-color: #0052d9;
  color: #fff;
  font-size: 14px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 6px;
}
.give-btn-dis {
  background-color: #bbd3fb;
}
.give-popup-tips {
  border-radius: 8px;
  width: 310px;
  padding: 30px 24px 24px !important;
  .tips-title {
    color: #000000e6;
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 18px;
    font-style: normal;
    font-weight: 600;
    line-height: 26px;
  }
  .tips-content {
    color: #********;
    text-align: center;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin: 8px 0 24px;
  }
  .tips-footer {
    display: flex;
    justify-content: space-between;
    &_btn {
      width: 126px;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      border-radius: 6px;
    }
    &_colse {
      color: #0052d9;
      background-color: #f2f3ff;
    }
    &_confirm {
      color: #ffffff;
      background-color: #0052d9;
    }
    &_big {
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      border-radius: 6px;
      color: #ffffff;
      background-color: #0052d9;
    }
  }
}
.select-tips-content {
  display: flex;
  flex-wrap: wrap;
  margin: 4px 0 12px 0;
  padding: 0 16px;
  .select-tips-item {
    background-color: #f2f2f2;
    border-color: #eee;
    color: #666;
    cursor: pointer;
    font-size: 12px;
    height: 22px;
    line-height: 22px;
    padding: 0 8px;
    border-radius: 4px;
    margin: 8px 8px 0 0;
  }
}
</style>
