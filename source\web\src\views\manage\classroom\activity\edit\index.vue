<template>
  <div class="edit-contain">
    <div class="edit-title">
      <div class="left-title">
        <span>活动管理</span>
        <span class="title-tips">快捷功能：</span>
      </div>
      <div class="fast-btn">
        <span @click="exportActivityStudent()">导出全部学员</span>
        <span @click="exportActivityStudentSignInList()">下载签到表</span>
        <span @click="signInQrCodePopupVisible = true">签到二维码</span>
        <span @click="addStudentPopupVisible = true">添加学员</span>
        <span @click="handleShare()">转发分享</span>
      </div>
    </div>
    <div class="info-area">
      <div class="tab-area">
        <span v-for="(item, index) in tabList" :key="index" @click="handleTabClick(item.label)" :class="{ active: currentTab === item.label }">{{ item.name }}</span>
      </div>
      <div class="info-contain">
        <activity-edit v-if="currentTab === 'activityEdit'" @updateData="getActivityInfo()"></activity-edit>
        <student-manage ref="studentManageRef" v-if="currentTab === 'studentManage'"></student-manage>
        <question-info v-if="currentTab === 'questionnaireManage'"></question-info>
        <interact-manage v-if="currentTab === 'interactManage'"></interact-manage>
        <schedule-manage v-if="currentTab === 'scheduleAndWechatGroup'"></schedule-manage>
        <sign-up-card v-if="currentTab === 'signUp'"></sign-up-card>
        <meeting-card v-if="currentTab === 'tencentMeeting'"></meeting-card>
      </div>
    </div>

    <!-- 签到二维码 -->
    <sign-in-qr-code-popup :visible.sync="signInQrCodePopupVisible"></sign-in-qr-code-popup>
    <!-- 添加学员 -->
    <add-student-popup v-if="activityInfo.activity_id" :visible.sync="addStudentPopupVisible" @success="updateStudentList()"></add-student-popup>
    <!-- 二维码 -->
    <QrCodeDialog v-if="copyShow.show" :visible.sync="copyShow.show" :url="copyShow.url" :copyTitle="copyShow.title" isAppletQrCode :appletPath="copyShow.appletPath" :scene="copyShow.scene" />
  </div>
</template>

<script>
import activityEdit from './activityEdit.vue'
import studentManage from './studentManage.vue'
import questionInfo from './questionInfo.vue'
import interactManage from './interactManage.vue'
import scheduleManage from './scheduleManage.vue'
import signUpCard from './signUpCard.vue'
import meetingCard from './meetingCard.vue'
import signInQrCodePopup from './components/signInQrCodePopup.vue'
import addStudentPopup from './components/addStudentPopup.vue'
import QrCodeDialog from '@/views/components/qrCodeDialog'
import env from 'config/env.conf.js'

const envName = env[process.env.NODE_ENV]
export default {
  components: {
    activityEdit,
    studentManage,
    questionInfo,
    interactManage,
    scheduleManage,
    signUpCard,
    meetingCard,
    signInQrCodePopup,
    addStudentPopup,
    QrCodeDialog
  },
  data() {
    return {
      currentTab: 'activityEdit',
      tabList: [
        { name: '基础信息', label: 'activityEdit' },
        { name: '学员管理', label: 'studentManage' },
        { name: '问卷管理', label: 'questionnaireManage' },
        { name: '互动管理', label: 'interactManage' },
        { name: '日程和企微群', label: 'scheduleAndWechatGroup' },
        { name: '发起报名(运营工具)', label: 'signUp' },
        { name: '腾讯会议', label: 'tencentMeeting' }
      ],
      signInQrCodePopupVisible: false,
      addStudentPopupVisible: false,
      copyShow: {
        show: false,
        title: '',
        url: '',
        scene: '',
        appletPath: ''
      }
    }
  },
  computed: {
    activityInfo() {
      return this.$store.getters['activity/activityInfo']
    },
    activityId () {
      return this.$route.query.activity_id
    }
  },
  created() {
    this.$route.query.tab && (this.currentTab = this.$route.query.tab)
    this.getActivityInfo()
  },
  methods: {
    handleTabClick(val) {
      this.currentTab = val
    },
    getActivityInfo() {
      this.$store.dispatch('activity/getActivityInfo', {
        activity_id: this.$route.query.activity_id
      })
    },
    // 导出全部学员
    exportActivityStudent() {
      const params = {
        class_id: this.activityId,
        staff_names: [],
        dept_full_name: '', // 组织架构
        join_type: [1, 2], // 参与方式
        reg_status: [0, 6, 2, 3, 11, 1], // 报名状态
        status: [-1, 4, 5, 18, 19] // 签到状态
      }
      let url = `${envName.trainingPath}api/activity/manage/members/export-members`
      
      this.downloadFileWithPost(url, params)
    },
    downloadFileWithPost(url, params) {
      fetch(url, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json' // 明确指定 JSON 格式
        },
        body: JSON.stringify(params)
      }).then(response => response.blob()).then(blob => {
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${this.activityInfo.activity_name}-学员列表.xls`
        a.click()
        window.URL.revokeObjectURL(url)
      })
    },
    // 下载签到表
    exportActivityStudentSignInList() {
      const url = `${envName.trainingPath}api/activity/manage/members/export-sign-list?activity_id=${this.activityId}&act_type=4`
      window.location.href = url
    },
    updateStudentList() {
      this.$refs.studentManageRef && this.$refs.studentManageRef.onSearch(1)
    },
    handleShare() {
      this.copyShow.title = this.activityInfo.activity_name
      this.copyShow.url = `https://sdc.qq.com/s/JZH8Eg?scheme_type=activityDetail&activity_id=${this.activityInfo.activity_id}`
      this.copyShow.scene = this.activityInfo.activity_id
      this.copyShow.appletPath = 'pages/activity/index'
      this.copyShow.show = true
    }
  }
}
</script>

<style lang="less" scoped>
:deep(.qcode-box .dialog-center) {
  position: absolute !important;
}
.edit-contain {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: initial;
  .edit-title {
    height: 64px;
    background: #fff;
    display: flex;
    align-items: center;
    flex-shrink: 0;
    padding: 0 20px;
    .left-title {
      display: flex;
      align-items: center;
      margin-right: 8px;
      span {
        &:nth-child(1) {
          font-size: 16px;
          font-weight: 600;
          color: #000000cc;
          margin: 0 24px 0 0;
        }
        &:nth-child(2) {
          color: #000000e6;
          font-size: 14px;
        }
      }
    }
    .fast-btn {
      display: flex;
      gap: 10px 0;
      align-items: center;
      >span {
        padding: 8px 16px;
        background: #ECF2FE;
        border-radius: 4px;
        margin-right: 12px;
        color: #0052d9;
        font-size: 14px;
        cursor: pointer;
        // 悬停，点击设置透明度
        &:hover, &:active {
          opacity: 0.8;
        }
      }
    }
  }
  .info-area {
    flex: 1;
    margin: 20px 0 0 0;
    display: flex;
    flex-direction: column;
    overflow: auto;
    .tab-area {
      height: 32px;
      background: initial;
      display: flex;
      align-items: center;
      flex-shrink: 0;
      span {
        padding: 8px 22px;
        font-size: 14px;
        color: #00000099;
        cursor: pointer;
        &.active {
          background: #fff;
          color: #0052D9;
          font-weight: 500;
          border-radius: 4px 4px 0 0;
        }
      }
    }
    .info-contain {
      overflow: hidden;
      /* padding: 32px 0 0 0; */
      flex: 1;
      display: flex;
      flex-direction: column
      /* background: #fff; */
    }
  }
}
</style>
