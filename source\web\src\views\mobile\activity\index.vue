b<template>
  <div class="face-page"  id="face-scroll" :class="paddingBottom ? 'padding-b100' : '' ">
    <div class="top-info">
      <img :src="courseData.photo_url ? courseData.photo_url : require('@/assets/img/activity-default.png')" alt="">
      <span class="top-header-l-tips">活动</span>
      <div class="right-info">
        <div class="face-title">{{courseData.course_name}} </div>
        <div class="face-base">
          <span class="top-iconInfo">
            <i class="icon-view"></i>
            <span>{{ courseData.view_count || 0 }} </span>
            <i class="icon-scores"></i>
            <span> {{courseData.avg_score || 0}}分</span>
            <span class="time">{{ courseData.created_at ||  ':--' }}</span>
          </span>
        </div>
      </div>
    </div>
    <div class="line-8"></div>
    <!-- apply -->
    <div class="item-class-info" :dt-eid="dtTableList('eid', courseData)" :dt-areaid="dtTableList('area', courseData)" :dt-remark="dtTableList('remark', courseData)">
      <div class="item-class-info_left">
        <div class="info-lable"><i class="icon icon-time"></i> {{courseData.start_end_time}}</div>
        <div class="info-lable info-lable-hidden"><i class="icon icon-name"></i> <span class="hidden-text"> {{courseData.head_teacher_name}}</span></div>
        <div class="info-lable" v-if="!is_preview"><i class="icon icon-city"></i> <span v-html="teachingType(courseData)"></span></div>
        <div class="info-lable" v-else><i class="icon icon-city"></i> <span v-if="courseData.city !== '0'" v-html="teachingType(courseData)"></span></div>
        <div class="info-lable" v-if="courseData.is_limit_student_count !== 0"><i class="icon surplus-icon"></i> {{surplusText}}</div>
        
        <div class="btn-box item-class-info_right">
          <!-- 可报名 -->
          <div v-if="statusBtn === 1" :class="['status-button', 'status1', {'registered-btn': registered }]" @click="handlerBeforeApply(1, '报名')" :dt-eid="dtTable('eid', '报名', courseData)" :dt-areaid="dtTable('area', '报名', courseData)" :dt-remark="dtTable('remark', '报名', courseData)">
            <span class="button-text">报名</span>
            <span class="button-info" v-if="courseData.is_limit_student_count !== 0">剩余名额 {{surplusQuota}}</span>
          </div>
          <!-- 已报满-等待列表 -->
          <div v-else-if="statusBtn === 2 && courseData.allow_waiting_list" :class="['status-button', 'status1', {'registered-btn': registered }]" @click="handlerBeforeApply(1, '加入等待列表')">
            <span class="button-text">加入等待列表</span>
            <span class="button-info">排队人数 {{courseData.waiting_count}}</span>
          </div>
          <!-- 线下已满，无等待列表，线上报名 -->
          <div v-else-if="statusBtn === 2 && courseData.teaching_type.indexOf('2')" :class="['status-button', 'status1', {'registered-btn': registered }]" @click="handlerBeforeApply(1, '加入等待列表')">
            <span class="button-text">报名</span>
            <span class="button-info">线下名额0，线上名额不限</span>
          </div>
          <!-- 报名名额已报满 -->
          <div v-else-if="statusBtn === 2 && !courseData.allow_waiting_list && courseData.teaching_type.indexOf('2') === -1" class="status-button status2" >
            <span class="button-text">开班提醒</span>
            <span class="button-info">已无剩余名额</span>
          </div>
          <!-- 等待列表中 -->
          <div v-else-if="statusBtn === 3" :class="['status-button', 'status2', {'registered-btn': registered }]" @click="handlerCancelApply(2, '退出等待队列')">
            <span class="button-text">已报名候补中</span>
            <span class="button-info">点击可取消报名</span>
          </div>
          <!-- 可注销 -->
          <div v-else-if="statusBtn === 5" :class="['status-button', 'status2', {'registered-btn': registered }]" @click="handlerCancelApply(2, '取消报名')">
            <span class="button-text" >已报名{{ attendStatusName }}</span>
            <span class="button-info">点击可取消报名</span>
          </div>
          <!-- 上级审核 -->
          <div class="status-button status2" v-else-if="statusBtn === 7" @click="handlerCancelApply(2, '取消报名')">
            <span class="button-text">已报名，待审批</span>
            <span class="button-info">点击可取消报名</span>
          </div>
          <!-- 已报名-不可注销 -->
          <div class="status-button status3" v-else-if="statusBtn === 6 && courseData.reg_status === 0">
            <span class="button-text">已报名{{ attendStatusName }}</span>
            <span class="button-info">已超过注销截止时间</span>
          </div>
          <!-- 已报名-不可注销-等待队列 -->
          <div class="status-button status3" v-else-if="statusBtn === 6 && courseData.reg_status === 2">
            <span class="button-text">已报名候补中</span>
            <span class="button-info">已超过注销截止时间</span>
          </div>
          <!-- 已报名-不可注销-上级审批 -->
          <div class="status-button status3" v-else-if="statusBtn === 6 && courseData.reg_status === 3">
            <span class="button-text">已报名，待审批</span>
            <span class="button-info">已超过注销截止时间</span>
          </div>
          <!-- 已截止报名 -->
          <div class="status-button status3" v-else-if="statusBtn === 4">
            <span class="button-text">开班提醒</span>
            <span class="button-text">已截止报名</span>
          </div>
          <!-- 未报名霸课 -->
          <div class="status-button status3" v-else-if="statusBtn === 8">
            <span class="button-text">已报名{{ attendStatusName }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="line-8"></div>
    <!-- tab栏 -->
    <div class="tabs-card">
      <div class="tabs" ref="tabs">
        <div :class="['item-tab', {'active-item-tab': activeKey === item.key}]" v-for="item in tabList" :key="item.key" @click="changeTabs(item.key)">
          <span>{{ item.text }}</span>
          <span v-show="activeKey === item.key" class="active-line"></span>
        </div>
      </div>
    </div>
    <!-- 介绍 -->
    <activityIntroduce  v-show="activeKey === 'des'" :courseData="courseData" courseType="activity"></activityIntroduce>
    <!-- 笔记 -->
    <notes v-show="activeKey === 'note'" :courseData="courseData" module_id="4" courseType="activity"></notes>
    <!-- 延伸学习 -->
    <extendLearning v-show="activeKey === 'extand'" :extandList="extandList" :courseData="courseData" courseType="activity"></extendLearning>
    <!-- 专区推广内容 -->
    <courseExtend v-show="activeKey === 'list' || activeKey === 'des'" :courseData="courseData" courseType="activity"></courseExtend>
    <!-- 课前学习 -->
    <learning v-show="activeKey === 'beforeLearn'" :learningList="courseBeforeData" :courseData="courseData" courseType="activity"></learning>
    <!-- 课后学习 -->
    <learning v-show="activeKey === 'afterLearn'" :learningList="courseAfterData" :courseData="courseData" courseType="activity"></learning>

    <!-- 底部操作按钮 -->
    <bottomNav @handleBtChoice="handleBtChoice" ref="bottomNavRef" :courseData="courseData" scrollID="window" courseType="activity"></bottomNav>
    <!-- 评论弹窗 -->
    <commentPopup :commentShow.sync="commentShow" :courseData.sync="courseData" :isShowZan="false" courseType="activity"></commentPopup>
    <!-- 添加课单 -->
    <addCousePopup :show.sync="addCourseShow" :courseData.sync="courseData" courseType="activity"></addCousePopup>
    <!-- 分享提示框 -->
    <shareTipDialog v-model="showShareDialog" :portraitScreen="true"></shareTipDialog>
    <!-- 报名选择参加方式 -->
    <chooseParticipationMethodPopup :visible.sync="chooseParticipationMethodVisible" :courseData="courseData" :regStatus="statusBtn" @confirm="handlerParticipationApply"></chooseParticipationMethodPopup>
    <!-- 报名审批 -->
    <registrationApprovalPopup v-if="registrationApprovalVisible" :visible.sync="registrationApprovalVisible" :courseData="courseData" @confirm="handlerSelectApprover"></registrationApprovalPopup>
    <!-- 活动签到 -->
    <activitySignIn></activitySignIn>
    <!-- 学员问卷 -->
    <studentQuestionnairePopup v-if="studentQuestionnaireVisible" :visible.sync="studentQuestionnaireVisible" :courseData="courseData" :joinType="joinType" @onClose="getCourseDetail"></studentQuestionnairePopup>
  </div>
</template>

<script>
import {
  getActivityInfo,
  getExtanContentList,
  setMemberStatus
} from 'config/api.conf'
import { getRelationContentsApi } from '@/config/classroom.api.conf.js'
import bottomNav from '@/views/mobile/videoDetailGray/child/bottomNav.vue'
import notes from '@/views/mobile/videoDetailGray/notes.vue'
import extendLearning from '@/views/mobile/videoDetailGray/extendLearning.vue'
import courseExtend from './components/courseExtend.vue'
import shareTipDialog from '@/views/mobile/videoDetailGray/child/shareTipDialog.vue'
import addCousePopup from '@/views/mobile/videoDetailGray/child/addCousePopup.vue'
import commentPopup from '@/views/mobile/videoDetailGray/child/commentPopup.vue'
import activityIntroduce from './components/activityIntroduce.vue'
import activitySignIn from './components/activitySignIn.vue'
import chooseParticipationMethodPopup from '@/views/components/chooseParticipationMethodPopup.vue'
import registrationApprovalPopup from '@/views/components/registrationApprovalPopup.vue'
import studentQuestionnairePopup from '@/views/components/studentQuestionnairePopup.vue'
import MoocJs from 'sdc-moocjs-integrator'
import { Toast, Dialog } from 'vant'
import learning from './components/learning.vue'

export default {
  components: {
    bottomNav,
    notes,
    extendLearning,
    courseExtend,
    shareTipDialog,
    addCousePopup,
    commentPopup,
    activityIntroduce,
    activitySignIn,
    chooseParticipationMethodPopup,
    registrationApprovalPopup,
    studentQuestionnairePopup,
    learning
  },
  data() {
    return {
      commentShow: false,
      paddingBottom: true,
      showShareDialog: false,
      addCourseShow: false,
      activeKey: 'des',
      statusBtn: 0, // 按钮状态 1-可报名 2-已报满-等待列表 3-已加入等待列表 4-截至报名 5-已报名-可注销 6-已报名-不可注销
      courseData: {},
      extandList: [],
      addCourseDialogData: {},
      tabList: [
        { key: 'des', text: '活动介绍' },
        { key: 'note', text: '笔记' },
        { key: 'beforeLearn', text: '课前学习' },
        { key: 'afterLearn', text: '课后学习' }
      ],
      registered: false,
      chooseParticipationMethodVisible: false,
      registrationApprovalVisible: false,
      studentQuestionnaireVisible: false,
      joinType: 1,
      joinTypeName: '',
      parentStaff: {
        parent_staff_id: '',
        parent_name: ''
      },
      // fontAfterData: ''
      courseBeforeData: [],
      courseAfterData: [],
      tabsY: 0,
      windowScrollY: 0
    }
  },
  watch: {},
  computed: {
    // 是否是预览
    is_preview() {
      try {
        return JSON.parse(this.$route.query.is_preview)
      } catch (error) {
        return false
      }
    },
    activity_id() {
      return this.$route.query.activity_id || ''
    },
    isFormMooc() {
      return this.$route.query.from === 'mooc'
    },
    teachingType() {
      return (val) => {
        if (val && val.teaching_type) {
          const { teaching_type, location, city } = val
          // 1 线下面试 2 在线授课 3 网络研讨会',
          const typeObj = {
            1: '线下地址：' + `${city}-${location}`,
            2: '线上参加：报名成功后自动推送活动链接',
            3: '线上参加：报名成功后自动推送活动链接'
          }
          const types = teaching_type.split(';').map(Number)
          return types.map(type => typeObj[type]).join('<br/>')
        }
        return ''
      }
    },
    surplusText() {
      const { waiting_count } = this.courseData
      // courseData.waiting_count
      if (this.statusBtn === 1 && this.surplusQuota !== 0) {
        return `剩余名额：${this.surplusQuota}`
      } else if (
        this.statusBtn === 2 ||
        this.statusBtn === 3 ||
        (this.statusBtn === 1 && this.surplusQuota === 0)
      ) {
        return `排队人数：${waiting_count}`
      } else if (this.statusBtn === 4) {
        return '已截止报名'
      } else if (this.statusBtn === 5) {
        return '点击可取消报名'
      } else if (this.statusBtn === 6) {
        return '已超过注销截止时间'
      } else {
        return '待上级审批'
      }
    },
    // 剩余名额
    surplusQuota() {
      const { max_student_count, regist_count } = this.courseData
      if (!max_student_count) return 0
      return Number(max_student_count) - Number(regist_count)
    },
    attendStatusName() {
      const { attend_status } = this.courseData
      switch (attend_status) {
        case 4:
          return '，全勤'
        case 5:
          return '，缺勤'
        case 18:
          return '，部分缺勤'
        case 19:
          return '，临时取消'
        default:
          return ''
      }
    },
    dtTableList() {
      return (type, item) => {
        if (type === 'eid') {
          return `element_${this.activity_id}_${item.activity_id}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.activity_name,
            page_type: '活动详情页-新版',
            container: '',
            click_type: 'data',
            content_type: '活动',
            content_id: '',
            content_name: item.class_name,
            terminal: 'PC'
          })
        } else {
          return `area_${this.activity_id}_${item.activity_id}`
        }
      }
    },
    dtTable() {
      return (type, name, item) => {
        if (type === 'eid') {
          return `element_${item ? item.activity_id : ''}_${name}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.activity_name,
            page_type: '活动页情页-新版',
            container: '',
            click_type: 'button',
            content_type: '报名',
            content_id: '',
            content_name: '',
            button_name: name,
            terminal: 'PC'
          })
        } else {
          return `area_${item ? item.activity_id : ''}_${name}`
        }
      }
    }
  },
  mounted() {
    window.addEventListener('message', (e) => {
      // 来自问卷手动调用完成的方法
      const { events, type } = e.data
      if (type === 'Mooc-task' && events === 'completeStatusUpdata') {
        this.handlerApply(1, '', this.joinType)
      }
    })
    this.getCourseDetail()
    this.getExtandList()
    this.getLearningList()
    window.addEventListener('scroll', this.scrollEvent, true)

    const tabsElement = this.$refs.tabs
    const tabsRect = tabsElement.getBoundingClientRect()
    this.tabsY = tabsRect.y || 0
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.scrollEvent)
  },
  methods: {
    async getLearningList() {
      const res = await getRelationContentsApi({ activity_id: this.activity_id, relation_usage_type: '' })
      this.courseBeforeData = res.filter(item => item.relation_usage_type === 1)
      this.courseAfterData = res.filter(item => item.relation_usage_type === 2)
    },
    scrollEvent(e) {
      // 获取当前滚动位置
      let scrollTop = window.scrollTop || document.documentElement.scrollTop
      // 滚动高度 250是视频的高度
      if (scrollTop > 200) {
        console.log('scrollTop', scrollTop)
        // 向下滚动，修改多余高度
        this.paddingBottom = false
      } else {
        // 向上滚动
        this.paddingBottom = true
      }
    },
    // 报名之前的处理
    handlerBeforeApply(type, message) {
      this.joinType = ''
      this.parentStaff = {
        parent_staff_id: '',
        parent_name: ''
      }
      const { teaching_type, before_class_survey, need_appovel } = this.courseData
      let teachingType = teaching_type.split(';')
      this.joinTypeName = message
      if (teachingType.length > 1) {
        this.chooseParticipationMethodVisible = true
        return
      }
      this.joinType = teaching_type
      // 需要审核，且参与方式为线下
      if (need_appovel && teachingType[0] === '1') {
        this.registrationApprovalVisible = true
        return
      }
      if (before_class_survey?.wj_url) {
        this.studentQuestionnaireVisible = true
      } else {
        this.handlerApply(type, message, teaching_type)
      }
    },
    // 报名 - 选择参与方式
    handlerParticipationApply(e) {
      this.joinType = e.join_type
      const { need_appovel, before_class_survey } = this.courseData
      // 需要审核，且参与方式为线下
      if (need_appovel && this.joinType === 1) {
        this.registrationApprovalVisible = true
        return
      }
      if (before_class_survey?.wj_url) {
        this.studentQuestionnaireVisible = true
      } else {
        this.handlerApply(1, this.joinTypeName, this.joinType)
      }
    },
    // 报名审核 - 选择审核人员
    handlerSelectApprover(e) {
      this.parentStaff = {
        parent_staff_id: e.parent_staff_id,
        parent_name: e.parent_name.split('(')[0]
      }
      const { before_class_survey } = this.courseData
      if (before_class_survey?.wj_url) {
        this.studentQuestionnaireVisible = true
      } else {
        this.handlerApply(1, '', this.joinType)
      }
    },
    // type 1报名, 2取消报名
    handlerApply(type, message, joinType) {
      let params = {
        activity_id: this.activity_id,
        action_code: type,
        ...((type === 1 && joinType) && { join_type: Number(joinType) }),
        ...((type === 1 && this.parentStaff.parent_staff_id) && { ...this.parentStaff })
      }
      setMemberStatus(params).then((res) => {
        console.log(res, '报名接口')
        if (res) {
          let msg = message ? '成功' + message : '提交成功'
          Toast.success(msg)
        } else {
          Toast(message + '失败')
        }
        this.getCourseDetail()
      })
    },
    // 取消报名
    handlerCancelApply(type, message) {
      Dialog.confirm({
        title: '注销报名',
        message: '是否确认注销报名？',
        confirmButtonColor: '#0052d9'
      }).then(() => {
        this.handlerApply(type, message)
      })
    },
    async getCourseDetail() {
      try {
        const { share_staff_id, share_staff_name } = this.$route.query
        let params = {
          activity_id: this.activity_id,
          share_staff_id: share_staff_id || '',
          share_staff_name: share_staff_name || ''
        }
        const data = await getActivityInfo(params)
        document.title = `${data.activity_name}_Q-Learning`
        this.courseData = data
        const {
          activity_name,
          photo_url,
          course_desc,
          activity_id,
          content_id,
          start_time,
          end_time,
          my_status
        } = this.courseData
        this.statusBtn = my_status // 设置按钮状态
        this.courseData.course_id = data.activity_id
        this.courseData.course_name = data.activity_name
        this.courseData.course_desc = data.description
        this.courseData.start_end_time = this.formatDateTimeStartEnd(
          start_time,
          end_time
        )

        const net_url = location.hostname.endsWith('.woa.com')
          ? `${process.env.VUE_APP_PORTAL_HOST_WOA}/training/activity/detail?activity_id=${activity_id}`
          : `${process.env.VUE_APP_PORTAL_HOST}/training/activity/detail?activity_id=${activity_id}`
        this.addCourseDialogData = {
          ...this.addCourseDialogData,
          content_name: activity_name,
          cover_img_url: photo_url,
          description: course_desc,
          href: net_url,
          item_id: activity_id,
          origin: location.origin
        }
        this.courseData.content_id = content_id
      } catch (err) {
        if (err.code) {
          if (this.isFormMooc && (err.code === 403 || err.code === 500)) {
            MoocJs.sendErrorInfo(err.message)
            return
          }
          let type = 0
          if (err.code === 403) {
            type = 3
            if (err.message.includes('权限')) {
              type = 5
            }
          } else if (err.code === 500) {
            type = 5
          }
          this.$router.replace({
            name: 'mobileError',
            query: {
              type
            }
          })
        }
      }
    },
    getExtandList() {
      const params = {
        act_id: this.activity_id,
        act_type: 1
      }
      this.loading = true
      getExtanContentList(params)
        .then((data) => {
          this.loading = false
          this.extandList = (data || []).map((item) => {
            return {
              ...item,
              module_name: item.content_module_name, // 类型名称
              module_id: item.content_module_id, // 类型id
              content_name: item.content_name, // 内容名称
              content_url: item.href,
              item_id: item.content_item_id,
              description: item.course_desc, // 简介
              play_total_count: item.view_count, // 查看次数
              word_num: item.word_num, // 图文/笔记 - 字数
              praise_count: '', // 图文/笔记/案例/码客 - 点赞数
              avg_score: item.avg_score, // 得分
              created_time: item.content_created_time, // 时间
              task_count: item.sub_count,
              labels: (item.labels || []).map((v) => v.label_name),
              photo_url: item.photo_url || '',
              origin_data: {
                expert_name: '', // 行家-人员名称
                meet_num: 0, // 咨询量
                avg_score: '', // 评分
                start_time: '', // 活动开始时间
                end_time: '' // 活动结束时间
              }
            }
          })
          if (this.extandList.length) {
            this.tabList.splice(2, 0, { key: 'extand', text: '延伸学习' })
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    formatDateTimeStartEnd(start, end) {
      const dateTimeStart = new Date(start)
      const dateTimeEnd = new Date(end)
      const formattedDateStart =
        this.$moment(dateTimeStart).format('YYYY/MM/DD')
      const formattedDateEnd = this.$moment(dateTimeEnd).format('YYYY/MM/DD')
      const ltStart = this.$moment(dateTimeStart).format('HH:mm')
      const ltEnd = this.$moment(dateTimeEnd).format('HH:mm')
      const weekArr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekdayStart = weekArr[dateTimeStart.getDay()] // 获取星期信息
      const weekdayEnd = weekArr[dateTimeEnd.getDay()] // 获取星期信息
      const startDateJoin = `${formattedDateStart}(${weekdayStart}) ${ltStart}`
      const endDateJoin = `${formattedDateEnd}(${weekdayEnd}) ${ltEnd}`
      return `${startDateJoin} - ${endDateJoin}` // 组合日期和星期信息并返回
    },
    formatDateTime(date) {
      const dateTime = new Date(date)
      const formattedDate = this.$moment(dateTime).format('YYYY/MM/DD')
      const getDay = dateTime.getDay()
      const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'] // 获取星期信息
      return `${formattedDate}（${weekday[getDay]}）` // 组合日期和星期信息并返回
    },
    // 去重
    removDuplication(arr, type) {
      let resArr = arr.reduce((result, obj) => {
        if (
          !result.some(
            (item) => JSON.stringify(item[type]) === JSON.stringify(obj[type])
          )
        ) {
          obj[type] === '在线授课' ? result.unshift(obj) : result.push(obj)
        }
        return result
      }, [])
      return resArr
    },
    changeTabs(val) {
      // 保存切换前的滚动位置
      const oldScroll = window.scrollY
      this.activeKey = val
      
      // 等待DOM更新完成后执行滚动逻辑
      this.$nextTick(() => {
        // 获取当前内容区域的高度
        const contentHeight = document.body.scrollHeight
        
        // 如果之前的滚动位置超过了tabs栏位置，重置到顶部
        if (oldScroll > this.tabsY) {
          window.scrollTo({
            top: 0,
            behavior: 'auto'
          })
        } else {
          // 恢复到之前保存的位置，但要确保不超过当前内容的最大高度
          const scrollPosition = Math.min(oldScroll, contentHeight - window.innerHeight)
          window.scrollTo({
            top: scrollPosition,
            behavior: 'auto'
          })
        }
        
        // 保存当前滚动位置用于下次切换
        this.windowScrollY = oldScroll
        this.scrollToTabs()
        
        // 初始化底部导航状态
        if (this.$refs.bottomNavRef) {
          this.$refs.bottomNavRef.initShow()
        }
      })
    },
    scrollToTabs() {
      if (this.windowScrollY > this.tabsY) {
        this.$nextTick(() => {
          const tabsElement = this.$refs.tabs
          if (tabsElement) {
            tabsElement.scrollIntoView({ behavior: 'auto' })
          }
        })
      }
    },
    // 底部按钮显示
    handleBtChoice(val) {
      console.log('底部按钮', val)
      if (val.icon === 'home') {
        const url = `//sdc.qq.com/s/Shd3Jk?scheme_type=homepage`
        window.location.href = url
      } else if (val.icon === 'comment') {
        this.commentShow = true
      } else if (val.icon === 'share') {
        this.showShareDialog = true
      } else if (val.icon === 'add') {
        this.addCourseShow = true
      }
    }
  }
}
</script>

<style lang="less" scoped>
.padding-b100 {
  padding-bottom: 100px !important;
}
.face-page {
  background-color: #fff;
  min-height: 100vh;
  .line-8 {
    height: 8px;
    width: 100%;
    background-color: #f3f5f7;
  }
  .top-info {
    // position: fixed;
    display: flex;
    background-color: #fff;
    padding: 12px;
    margin-bottom: 10px;
    position: relative;
    img {
      width: 92px;
      height: 62px;
      border-radius: 2px;
      margin-right: 10px;
    }
    .top-header-l-tips {
      position: absolute;
      left: 16px;
      top: 16px;
      font-size: 12px;
      border-radius: 2px;
      display: inline-block;
      width: 32px;
      height: 18px;
      line-height: 18px;
      text-align: center;
      color: #fff;
      background-color: #0052d9;
    }
    .right-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .face-title {
        font-size: 14px;
        font-weight: 600;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        white-space: normal;
      }
    }
    .face-base {
      display: flex;
      align-items: center;
      color: #777777;
      // margin-bottom: 10px;
      font-size: 10px;
      i {
        display: inline-block;
        margin-right: 4px;
        width: 16px;
        height: 16px;
      }
      .top-iconInfo {
        margin-right: 40px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        .icon-view {
          background: url('~@/assets/img/view.png') no-repeat center/cover;
        }
        .icon-scores {
          background: url('~@/assets/img/icon-scores.png') no-repeat
            center/cover;
          margin-left: 16px;
        }
        .time {
          margin-left: 16px;
        }
      }
    }
  }
  .tabs-card {
    position: sticky;
    z-index: 1000;
    top: 0;
    width: 100%;
    box-shadow: 0 4px 4px 0 #eeeeee40;
    // display: flex;
    // justify-content: space-between;
    background-color: #fff;
    .tabs {
      display: flex;
      align-items: center;
      padding-left: 20px;
      .item-tab {
        color: #00000099;
        font-size: 12px;
        line-height: 40px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
      }
      .item-tab + .item-tab {
        margin-left: 26px;
      }
      .active-item-tab {
        font-size: 14px;
        font-weight: bold;
        color: #0052d9;
      }
      .active-line {
        position: absolute;
        bottom: 0px;
        display: inline-block;
        width: 28px;
        height: 2px;
        background-color: #0052d9;
      }
    }
    .back-play {
      margin-right: 12px;
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #333333;
      .switch-content {
        height: 20px;
        margin-left: 4px;
        color: #ffffffe6;
      }
    }
    .face-list-content {
      padding: 10px;
      background-color: #fff;
      box-shadow: 0 4px 4px 0 #eeeeee40;
      border-top: 1px solid #eee;
      .city {
        display: flex;
        align-items: center;
        .icon-Local {
          background: url('~@/assets/img/mobile/city-icon.png') no-repeat center /
            cover;
          display: block;
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
      }
      /* 隐藏滚动条样式 */
      .date-select::-webkit-scrollbar {
        display: none; /* 对于Webkit浏览器 */
      }
      .date-select {
        display: flex;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch; /* 在移动设备上优化滚动 */
        .date-item {
          flex-shrink: 0;
          font-size: 12px;
          background: #f4f9fe;
          color: #777;
          padding: 0px 12px;
          height: 28px;
          line-height: 28px;
          margin-right: 14px;
          border-radius: 4px;
          cursor: pointer;
        }

        .date-item:hover {
          background: #f4f9fe;
          color: #0052d9;
        }
        .date-item-current {
          background: #f4f9fe;
          color: #0052d9;
        }
        .moer-date-btn {
          // position: absolute;
          // right: 0;
        }
      }
    }
  }
  .item-class-info {
    position: relative;
    margin: 12px;
    padding: 12px;
    border-radius: 8px;
    background: linear-gradient(90deg, #f9f9f9 0%, #f1f5fd 97.29%);
    font-size: 11px;
    &_left {
      .info-lable {
        display: flex;
        align-items: flex-start;
        line-height: 20px;
        margin-bottom: 8px;
        .hidden-text {
          flex: 1; /* 或者具体的flex值，确保子元素能够收缩 */
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
        }
        .icon {
          display: block;
          width: 20px;
          height: 20px;
          margin-right: 12px;
          flex-shrink: 0;
        }
        .icon-time {
          background: url('~@/assets/img/mobile/time-class.png') no-repeat
            center / cover;
        }
        .icon-name {
          background: url('~@/assets/img/teacher-class.png') no-repeat center /
            cover;
        }
        .icon-city {
          background: url('~@/assets/img/local-class.png') no-repeat center /
            cover;
        }
        .surplus-icon {
          background: url('~@/assets/img/mobile/surplus-icon.png') no-repeat
            center / cover;
        }
      }
      // .info-lable-hidden {
      //   width: 100%;
      //   overflow: hidden;
      //   text-overflow: ellipsis;
      //   white-space: nowrap;
      // }
      .info-lable:last-child {
        margin-bottom: 0;
      }

      .btn-box {
        flex-shrink: 0;
        position: relative;
        display: flex;
        flex-direction: column;
        margin-top: 12px;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        .status-button {
          height: 56px;
          padding: 7px 12px;
          .button-text {
            line-height: 22px;
          }
        }
        .status1 {
          .button-info {
            color: #ffffffe6;
            font-size: 11px;
            font-style: normal;
            font-weight: 500;
            line-height: 20px;
          }
        }
        .status3 {
          color: #777777;
          background-color: #F0F0F0;
          border-color: #F0F0F0;
          .button-text {
            color: #333333;
          }
          .button-info {
            color: #777777;
          }
        }
      }
    }
    &_right {
      position: absolute;
      right: 12px;
      bottom: 8px;
      .status-button {
        min-width: 80px;
        padding: 0 12px;
        height: 32px;
        border-radius: 4px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: center;
        cursor: pointer;
        .button-text {
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          line-height: 32px;
        }
        .button-info {
          font-family: 'PingFang SC';
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 16px;
        }
      }
      .status1 {
        background-color: #0052d9;
        color: #fff;
      }
      .status2 {
        color: #333333;
        background-color: #fff;
        border-color: #fff;
        .button-info {
          color: #777777;
        }
      }
      .status3 {
        color: #bcbec2;
        background-color: #c8c9cc;
        border-color: #c8c9cc;
        cursor: not-allowed;
      }
      .status4 {
        color: #fff;
        background-color: #f56c6c;
      }
      .registered-btn {
        color: #fff;
        background-color: #c8c9cc;
        border-color: #c8c9cc;
        cursor: not-allowed;
      }
    }
  }
}
</style>
