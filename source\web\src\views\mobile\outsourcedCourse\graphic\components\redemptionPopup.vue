<template>
  <van-popup class="redemption-popup" v-model="show" round z-index="1003" :close-on-click-overlay="false">
    <div class="content">
      <img class="left-top-pic" src="../../../../../assets/img/mobile/geekBang/left-top-pic.png" alt="">
      <div class="content-main">
        <div class="warm">{{ currentPopInfo.warm }}</div>
        <div class="title" v-html="currentPopInfo.title"></div>
        <van-button v-if="currentPopInfo.code === 2" class="btn" @click="userOperator(currentPopInfo.code)" :disabled="currentPopInfo.code === 2 && isSubscription">{{ currentPopInfo.btn }}</van-button>
        <van-button v-if="currentPopInfo.code === 3" class="btn btn-long" @click="userOperator(currentPopInfo.code)" :disabled="currentPopInfo.code === 2 && isSubscription">{{ currentPopInfo.btn }}</van-button>
        <div class="coupon-content">
          <div class="points-content">
            <span class="points">900积分</span>
            <span class="use-num">需使用 <span style="font-weight:500;color:#0052d9;line-height:20px;">{{ coursePurchaseInfo.course_val }}</span> 张学霸卡</span>
          </div>
          <!-- <div class="my-coupon">我的学霸卡：<span class="color-cupton-num">{{coursePurchaseInfo.user_account_num}}</span> 张</div> -->
          <div class="pupop-select">
            <div class="pupop-select-cur" @click="handlerSelectDown">
              <div class="cur-left">兑换方式</div>
              <div class="cur-center">
                <div class="cur-center_title">学霸卡-{{currentItemCard.acct_type_code_name}}<span :class="currentItemCard.isUse ? '' : 'is-use' ">{{currentItemCard.isUse ? '' : '不可用'}}</span></div>
                <div class="cur-center_num">拥有 <span class="cur-center_num_b"> {{currentItemCard.consume_point}} </span> 张<span v-if="currentItemCard.consume_point > 0">，最近过期时间 <span class="cur-center_num_b">{{timeToDate(currentItemCard.expire_time)}}</span></span></div>
              </div>
              <div class="cur-right">
                <i class="el-icon-arrow-down cur-right_down" :class="isShowDown ? 'is-reverse' : ''"></i>
              </div>
            </div>
            <div class="drop-down-box" v-show="isShowDown">
              <div class="drop-down-item" :class="currentItemCard.acct_type_code === item.acct_type_code ? 'select-item' : '' " @click="pitchUpItem(item)" v-for="(item, index) in cardList" :key="index">
                <div style="color: #000000cc;">学霸卡-{{item.acct_type_code_name}}<span :class="item.isUse ? '' : 'is-use' ">{{item.isUse ? '' : '不可用'}}</span> </div>
                <div class="num">拥有 <span class="num-b">{{item.consume_point}}</span> 张<span v-if="item.consume_point > 0">，最近过期时间 <span class="num-b">{{timeToDate(item.expire_time)}}</span></span> </div>
              </div>
            </div>
          </div>
        </div>
        <van-button v-if="isShowUse" class="btn" @click="userOperator(currentPopInfo.code)" :disabled="isDisabledUse">{{ currentPopInfo.btn }}</van-button>
        <div class="link" @click="linkTo">
          <img class="icon" src="../../../../../assets/img/mobile/geekBang/help_circle.png" alt="" />
          <span>想获得更多学霸卡？点此获取攻略</span>
        </div>
      </div>
    </div>
    <img class="close-icon" @click="$emit('input', false)" src="../../../../../assets/img/mobile/geekBang/right.png" alt="" />
  </van-popup>
</template>

<script>
import { getOutsourceLinkConfig } from '@/config/mooc.api.conf.js'
export default {
  name: 'redemptionPopup',
  props: {
    value: {
      type: Boolean,
      default: false,
      require: true
    },
    currentPopInfo: {
      type: Object,
      default: () => {},
      require: true
    },
    coursePurchaseInfo: {
      type: Object,
      default: () => {},
      require: true
    },
    isSubscription: {
      type: Boolean,
      default: false,
      require: true
    },
    cardList: {
      type: Array,
      default: () => []
    },
    courseType: {
      type: String
    }
  },
  watch: {
    courseType: {
      handler(newValue) {
        if (newValue) {
          console.log(newValue)
          // activity_page_link 活动落地页 rule_page_link 活动规则 ；special_page_link 外部好课专区
          getOutsourceLinkConfig({ resourceConfig: newValue }).then((res) => {
            this.linkConfig = res
          })
        }
      }
    },
    value(newValue) {
      console.log(newValue, 'newValuenewValuenewValue')
      this.show = newValue
    },
    cardList: {
      handler(val) {
        console.log(val, 'cardListwatch')
        // 自动选中专用卡
        if (val.length) {
          val.forEach((item) => {
            if (item.acct_type_code === this.courseType) {
              this.currentItemCard = item
            }
            // 没有找到专用卡，则默认选中通用卡
            if (!this.currentItemCard.acct_type_code && item.acct_type_code === 'xuebaCommon') {
              this.currentItemCard = item
            }
          })
        }
      }
    }
  },
  data() {
    return {
      linkConfig: {},
      show: false,
      isShowDown: false,
      currentValue: '',
      currentItemCard: {}
    }
  },
  computed: {
    timeToDate() {
      return (val) => {
        let str = '--'
        if (val) {
          let timeArr = val.split(' ')[0].split('-')
          str = `${timeArr[0]}-${timeArr[1]}-${timeArr[2]}`
        }
        return str
      }
    },
    // 显示兑换按钮
    isShowUse() {
      return (
        this.currentPopInfo.code === 0 ||
        this.currentPopInfo.code === 1 ||
        this.currentPopInfo.code === 4
      )
    },
    isDisabledUse() {
      return (
        this.currentItemCard.consume_point === 0 ||
        this.currentItemCard.isUse === false
      )
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 用户操作
    userOperator(code) {
      // 跳转链接直接跳转
      if (code === 3) {
        this.linkTo()
      } else {
        this.$emit('userOperator', code)
      }
    },
    // 链接跳转
    linkTo() {
      window.open(this.linkConfig.rule_page_link)
      // this.$emit('linkTo')
    },
    handlerSelectDown() {
      this.isShowDown = !this.isShowDown
    },
    // 选中每一项触发
    pitchUpItem(val) {
      this.currentItemCard = val
      this.isShowDown = false
    }
  }
}
</script>

<style lang="less" scoped>
.is-use {
  border-radius: 2px;
  background: #fdf6ec;
  padding: 3px 4px;
  color: #ff7548;
  font-family: 'PingFang SC';
  font-size: 10px;
  line-height: 12px;
  margin-left: 8px;
}
.redemption-popup {
  width: 312px;
  background-color: rgba(255, 255, 255, 0);
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: unset;
  top: 25%;
  right: 0;
  left: 0;
  bottom: 0;
  margin: auto;
  .content {
    position: relative;
    padding: 34px 12px 12px;
    width: 100%;
    border-radius: 24px;
    background: linear-gradient(180deg, #e3f2ff 0%, #e3f2ff 0%, #eaf5ff 35.99%);
    .left-top-pic {
      position: absolute;
      left: 4px;
      top: 8px;
      width: 89px;
      z-index: 1;
    }
    .content-main {
      position: relative;
      padding: 20px 7px;
      display: flex;
      flex-direction: column;
      align-items: center;
      border-radius: 16px;
      background-color: #fff;
      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #e9f4ff;
      }
      &::before {
        left: 0;
        transform: translate(-50%, -50%);
      }
      &::after {
        right: 0;
        transform: translate(50%, -50%);
      }
    }
    .color-cupton-num {
      color: #ed7b2f;
      font-weight: 500;
      line-height: 20px;
    }
    .warm {
      color: #0052d9;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
    }
    .title {
      margin-top: 16px;
      color: #000000e6;
      font-size: 12px;
      line-height: 20px;
      text-align: center;
    }
    .coupon-content {
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: 12px;
      padding: 8px;
      border-radius: 4px;
      background: #f9fbfc;
      color: #00000099;
      font-size: 12px;
      .points-content {
        .points {
          color: #00000066;
          line-height: 20px;
          text-decoration-line: line-through;
        }
        .use-num {
          margin-left: 12px;
          line-height: 20px;
        }
      }
      .extra-num {
        margin-top: 6px;
        line-height: 22px;
        span {
          font-weight: 500;
          color: #ed7b2f;
        }
      }
      .my-coupon {
        margin-top: 4px;
        line-height: 20px;
      }
      .pupop-select {
        position: relative;
        width: 100%;
        margin-top: 8px;
        .pupop-select-cur {
          width: 100%;
          height: 44px;
          border: 1px solid #eee;
          border-radius: 4px;
          top: 0;
          z-index: 100;
          display: flex;
          align-items: center;
          padding: 6px 8px;
          font-size: 10px;
          line-height: 16px;
          font-family: 'PingFang SC';
          .cur-left {
            width: 24px;
            font-size: 10px;
            line-height: 16px;
            color: #00000066;
          }
          .cur-right {
            width: 12px;
            &_down {
              color: #0052d9;
              font-size: 16px;
              font-weight: 600;
              transition: transform 0.3s;
              transform: rotate(0deg);
              cursor: pointer;
            }
            .is-reverse {
              transform: rotate(180deg);
            }
          }
          .cur-center {
            flex: 1;
            text-align: left;
            padding: 0 6px;
            &_title {
              color: #000000cc;
            }
            &_num {
              color: #00000066;
              &_b {
                color: #0052d9;
              }
            }
          }
        }
        .drop-down-box {
          position: absolute;
          z-index: 999;
          top: 50px;
          width: 257px;
          max-height: 110px;
          overflow: auto;
          border-radius: 4px;
          background: #fff;
          box-shadow: 0 6px 30px 5px #7daac31f;
          .drop-down-item {
            height: 44px;
            border-radius: 4px;
            border: 1px solid #eee;
            background: #fff;
            padding: 6px 4px 6px 8px;
            line-height: 16px;
            font-size: 10px;
            margin: 6px;
          }
          .select-item {
            border: 1px solid #266fe8;
          }
          .num {
            color: #00000066;
          }
          .num-b {
            color: #0052d9;
          }
        }
      }
    }
    .link {
      margin-top: 12px;
      cursor: pointer;
      .icon {
        width: 14px;
        margin-right: 5px;
      }
      span {
        font-size: 12px;
        line-height: 20px;
        color: #0052d9;
        // text-decoration-line: underline;
      }
    }
    .btn {
      font-size: 12px;
      margin: 0 auto;
      color: #ffffff;
      margin-top: 16px;
      display: flex;
      width: 232px;
      height: 36px;
      padding: 9px 20px;
      justify-content: center;
      align-items: center;
      border-radius: 24px;
      background: linear-gradient(90deg, #488dff 0%, #1766ff 100%);
    }
    .btn-long {
      width: auto;
    }
  }
  .close-icon {
    width: 28px;
    margin-top: 16px;
  }
}
</style>
