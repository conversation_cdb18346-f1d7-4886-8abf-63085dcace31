<template>
  <div class="result-popup">
    <van-popup v-model="popupShow" round :overlay-style="{ background: 'rgba(0, 0, 0, 0.4)'}">
      <div class="popup-card">
        <div class="popup-head">
          <div class="title">{{ prizeResult.luck_draw_type === 3 ? '很遗憾' : '恭喜中奖' }}</div>
          <img class="close" @click="onClose" src="../../../../assets/img/mobile/lottery/close.png" alt="" srcset="">
        </div>
        <div class="popup-body">
          <div class="goods-img">
            <img class="goods-img" v-if="prizeResult.luck_draw_type === 3" src="../../../../assets/img/mobile/lottery/not-winning-the-lottery.png" alt="" srcset="">
            <van-image width="124" v-else height="124" :src="prizeResult.img_url"></van-image>
          </div>
          <div class="goods-name overflow-l2">{{ prizeResult.goods_name }}</div>
          <div class="lottery-time">抽奖时间：{{ prizeResult.redeem_time }}</div>
          <template v-if="prizeResult.luck_draw_type !== 3">
            <!-- 实物 -->
            <div class="tips e_commerce" v-if="prizeResult.luck_draw_type === 1">请填写配送信息，奖品将在活动结束后由工作人员统一发放<br/>如有疑问，请联系v_lequ</div>
            <!-- 积分 -->
            <div class="tips integral" v-if="prizeResult.luck_draw_type === 2">积分类奖品活动结束后将由工作人员统一发放<br/>如有疑问，请联系v_lequ</div>
            <!-- 卡券 -->
            <div class="category-block" v-if="prizeResult.luck_draw_type === 0">
              <p>
                <span class="label">卡号：</span>
                <span class="key">{{ prizeResult.redeem_code }}</span>
                <van-popover
                  v-model="showPopover1"
                  trigger="click"
                  placement="top-end"
                  :offset="[16, 8]"
                >
                  <div class="popover-text">已复制到剪贴板</div>
                  <template #reference>
                    <span class="btn" id="copy-tag" @click="copy" :data-clipboard-text="prizeResult.redeem_code">复制</span>
                  </template>
                </van-popover>
              </p>
              <p>
                <span class="label">密码：</span>
                <span class="key">{{ prizeResult.redeem_pwd }}</span>
                <van-popover
                  v-model="showPopover2"
                  trigger="click"
                  placement="top-end"
                  :offset="[17, 8]"
                >
                  <div class="popover-text">已复制到剪贴板</div>
                  <template #reference>
                    <span class="btn" id="copy-tag" @click="copy" :data-clipboard-text="prizeResult.redeem_pwd">复制</span>
                  </template>
                </van-popover>
              </p>
              <p>
                <span class="label">兑换截止时间：</span>
                <span class="key">{{ prizeResult.expire_time }}</span>
              </p>
            </div>
          </template>
          <div :class="['btn-column', { 'btn-column-padding' : prizeResult.luck_draw_type === 3 }]">
            <van-button class="btn light-blue" v-if="prizeResult.luck_draw_type === 1" @click="deliveryLink">
              <img class="btn-icon" src="../../../../assets/img/mobile/lottery/edit.png" alt="" srcset="">
              <span>填写配送信息</span>
            </van-button>
            <!-- <button type="button" class="btn" @click="onClose">继续抽奖</button> -->
            <div class="text-btn" @click="prizeDetail">查看奖品详情</div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  // Toast,
  Popup,
  Image as VanImage,
  Popover } from 'vant'
import Clipboard from 'clipboard'

export default {
  props: {
    value: {
      type: Boolean,
      default: true,
      require: true
    },
    prizeResult: {
      type: Object,
      default: null
    }
  },
  components: {
    [Popup.name]: Popup,
    VanImage,
    [Popover.name]: Popover
  },
  watch: {
    value(newVal) {
      this.popupShow = newVal
    }
  },
  data() {
    return {
      popupShow: this.value,
      showPopover1: true,
      showPopover2: true
    }
  },
  computed: {
  },
  methods: {
    // 复制
    copy () {
      var clipboard = new Clipboard('#copy-tag')
      clipboard.on('success', e => {
        // Toast('复制成功')
        // 释放内存
        clipboard.destroy()
      })
      clipboard.on('error', e => {
        // 不支持复制
        // Toast('该浏览器不支持自动复制')
        // 释放内存
        clipboard.destroy()
      })
    },
    // 实物配送填写链接
    deliveryLink () {
      if (this.prizeResult.redeem_questionnaire_url) {
        window.location.href = this.prizeResult.redeem_questionnaire_url
      }
    },
    // 奖品详情
    prizeDetail() {
      this.$emit('openPrizeDetail', this.prizeResult.goods_order_id)
    },
    // 关闭弹窗
    onClose() {
      this.popupShow = false
      this.$emit('input', this.popupShow)
    }
  }
}
</script>

<style lang='less' scoped>
  .popup-card {
    width: 93.6vw;
    max-height: 80vh;
    // min-height: 65vh;
    letter-spacing: 0.5px;
    .popup-head {
      padding: 16px;
      position: relative;
      flex-shrink: 0;
      .title {
        color: #000000e6;
        text-align: center;
        font-size: 18px;
        font-weight: 600;
        line-height: 26px;
      }
      .close {
        width: 24px;
        height: 24px;
        position: absolute;
        top: 17px;
        right: 16px;
      }
    }
    .popup-body {
      padding: 12px 20px 28px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      font-family: PingFangSC-regular;
      .goods-img {
        width: 124px;
        height: 124px;
        display: inline-block;
        border-radius: 6px;
        background-color: rgba(229, 229, 229, 1);
        margin: 0 auto;
        overflow: hidden;
      }
      .goods-name {
        width: 250px;
        height: 44px;
        margin: 16px auto 0;
        line-height: 22px;
        color: #000000e6;
        font-weight: 500;
        text-align: center;
      }
      .lottery-time {
        line-height: 17px;
        color: rgba(0, 0, 0, 0.4);
        font-size: 12px;
        text-align: center;
        margin-top: 8px;
      }
      .tips {
        background-color: rgba(249, 249, 249, 1);
        margin-top: 16px;
        width: 100%;
        padding: 12px 16px;
        line-height: 20px;
        color: #00000099;
        font-size: 14px;
        text-align: center;
      }
      .e_commerce {
        margin-bottom: 12px;
      }
      .integral {
        margin-bottom: 64px;
      }
      .category-block {
        width: 100%;
        border-radius: 2px;
        background: #f5f7f9;
        padding: 8px 6px;
        margin: 12px auto 0;
        & > p {
          font-size: 12px;
          display: flex;
          align-items: center;
          margin: 10px auto;
        }
        .label {
          width: auto;
          color: rgba(16, 16, 16, 1);;
          text-align: justify;
          flex-shrink: 0;
        }
        .key {
          flex: 1;
          color:  rgba(108, 108, 108, 1);;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-right: 10px;
        }
        .score {
          color: #ff8200;
        }
        .btn {
          color: #3464e0;
          margin-left: auto;
          cursor: pointer;
        }
      }
      .btn-column {
        padding-top: 28px;
        display: flex;
        flex-direction: column;
        align-items: center;
        .btn {
          width: 100%;
          height: 40px;
          line-height: 24px;
          border-radius: 36px;
          background-color: #0052D9;
          border-color: #0052D9;
          color: #FFFFFF;
          font-size: 14px;
          text-align: center;
          font-family: Roboto;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-bottom: 12px;
          letter-spacing: 0.5px;
          font-weight: 500;
        }
        .btn:last-child {
          margin-bottom: 0;
        }
        .light-blue {
          background: #F2F3FF;
          border-color: #F2F3FF;
          color: #0052d9;
        }
        .btn-icon {
          width: 16px;
          height: 16px;
          display: inline;
          margin-right: 8px;
        }
        .text-btn {
          line-height: 17px;
          color: rgba(64, 117, 255, 1);
          font-size: 12px;
          text-align: center;
          margin-top: 4px;
        }
      }
      .btn-column-padding {
        padding-top: 144px;
      }
    }
  }
  .overflow-l2 {
    overflow: hidden; //溢出内容隐藏
    text-overflow: ellipsis; //文本溢出部分用省略号表示
    display: -webkit-box; //特别显示模式
    -webkit-line-clamp: 2; //行数
    line-clamp: 2;
    -webkit-box-orient: vertical; //盒子中内容竖直排列
  }
  .popover-text {
    padding: 6px 16px;
    color: rgba(255, 255, 255, 1);
    font-size: 12px;
    line-height: 17px;
    text-align: center;
    font-family: PingFangSC-regular;
  }
</style>
<style lang='less'>
  .van-popover--light {
    .van-popover__arrow {
      color: rgba(0, 0, 0, 0.75);
    }
    .van-popover__content {
      border-radius: 4px;
      background-color: rgba(0, 0, 0, 0.75);
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
      font-family: Roboto;
    }
  }
</style>
