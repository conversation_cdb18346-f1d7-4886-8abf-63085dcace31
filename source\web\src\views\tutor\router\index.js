import Vue from 'vue'
import Router from 'vue-router'
Vue.use(Router)

const routerPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch(err => err)
}
const routes = [
  {
    path: '/401',
    name: '401',
    component: () => import('views/user/error/401.vue'),
    meta: {
      title: '401'
    }
  },
  // 404
  {
    path: '*',
    name: '404',
    component: () => import('views/user/error/404.vue'),
    meta: {
      title: '404'
    }
  },
  {
    path: '/not_exist',
    name: 'not_exist',
    component: () => import('views/user/error/not_exist.vue'),
    meta: {
      title: 'not_exist'
    }
  },
  // 导师用户端 导师认证
  {
    path: '/tutor',
    name: 'userTutor',
    component: () => import('views/tutor/index'),
    meta: {
      title: '导师用户端'
    },
    children: [
      {
        path: 'certification',
        name: 'tutor-certification',
        component: () => import('views/tutor/tutorPage/certification.vue'),
        meta: {
          title: '认证要求页面'
        }
      }
    ]
  },
  {
    path: '/tutor',
    name: 'tutor',
    component: () => import('views/tutor/tutorManage/index.vue'),
    meta: {
      title: '导师系统管理后台'
    },
    children: [
      {
        path: 'addManage',
        name: 'addManage',
        component: () => import('views/tutor/tutorManage/addManage/index.vue'),
        meta: {
          title: '导师管理'
        }
      },
      {
        path: 'record',
        name: 'record',
        component: () => import('views/tutor/tutorManage/record/index.vue'),
        meta: {
          title: '辅导记录'
        }
      },
      {
        path: 'ruleSet',
        name: 'ruleSet',
        component: () => import('views/tutor/tutorManage/ruleSet/index.vue'),
        meta: {
          title: '规则配置'
        }
      }
    ]
  }
]
const router = new Router({
  mode: 'history',
  base: '/training',
  routes
})
export default router
