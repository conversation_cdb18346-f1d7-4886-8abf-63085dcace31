const aiManage = [
  // 图文管理端
  {
    path: '/graphic',
    name: 'graphic',
    component: () => import('views/manage/index.vue'),
    meta: {
      title: '文章管理端'
    },
    children: [
      {
        path: 'manage/graphic-list',
        name: 'graphic-list',
        component: () => import('views/manage/graphic/list.vue'),
        meta: {
          title: '文章列表',
          breadcrumb: ['文章管理', '文章列表']
        }
      }
    ]
  },
  // 标签管理端
  {
    path: '/label',
    name: 'label',
    component: () => import('views/labelMange/index.vue'),
    meta: {
      title: '标签管理端'
    },
    children: [
      {
        path: 'manage/label-list',
        name: 'label-list',
        component: () => import('views/labelMange/curriculum/list.vue'),
        meta: {
          title: '课程标签管理',
          breadcrumb: ['课程标签管理', '标签管理']
        }
      }
    ]
  },
  // 虚拟人后台管理
  {
    path: '/manage',
    name: 'manage',
    component: () => import('@/views/webManage/index.vue'),
    meta: {
      title: '内容管理'
    },
    children: [
      {
        path: 'networkManage',
        name: 'networkManage',
        component: () => import('@/views/webManage/networkManage/index.vue'),
        meta: {
          title: '评审管理'
        }
      },
      {
        path: 'directyleApprove',
        name: 'directyleApprove',
        component: () => import('@/views/webManage/directyleApprove/index.vue'),
        meta: {
          title: '直接上传审核'
        }
      },
      {
        path: 'ai-approve',
        name: 'aiApprove',
        component: () => import('@/views/webManage/aiApprove/index.vue'),
        meta: {
          title: 'AI做课审核'
        }
      },
      {
        path: 'graphic-approve',
        name: 'graphicApprove',
        component: () => import('@/views/webManage/graphic/index.vue'),
        meta: {
          title: '图文审核'
        }
      },
      // 素材管理
      {
        path: 'material',
        name: 'material',
        component: () => import('@/views/webManage/materialManage/index.vue'),
        meta: {
          title: '素材管理'
        }
      },
      // 上传素材-视频
      {
        path: 'material-upload',
        name: 'materialUpload',
        component: () => import('@/views/webManage/materialManage/materialUpload.vue'),
        meta: {
          title: '素材上传'
        }
      },
      {
        path: 'material-detail',
        name: 'materialDetail',
        component: () => import('@/views/webManage/materialManage/materialDetail.vue'),
        meta: {
          title: '素材详情'
        }
      },
      // 管理端-课程素材
      {
        path: 'material-play',
        name: 'materialPlay',
        component: () => import('@/views/webManage/materialManage/materialPlay.vue'),
        meta: {
          title: '课程素材'
        }
      }
    ]
  }
]
export default aiManage
