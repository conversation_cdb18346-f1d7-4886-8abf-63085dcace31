<template>
  <el-dialog title="" custom-class="none-border-dialog copyright-dialog" :visible="visible" width="500px"
    :close-on-click-modal="false" :show-close="false">
    <div class="dialog-body">
      <span class="title">请上传人确认：</span>
      <div class="copyright-content">
        <p v-show="typeOriginal === 0">
          <span class="tips">转载的内容允许转载或已获得充分授权，不违反任何法律法规规定和公司内部要求，所有内容均不侵犯任何第三方合法权益。</span>
          否则上传人应负责处理因此引起的纠纷并承担所有责任。
        </p>
        <p v-show="typeOriginal === 1">
          <span class="tips">上传的课件、文件资料均为原创内容，内容合法且不侵犯任何第三方合法权益（包括但不限于版权、肖像权等）。</span>
          上传后的内容，知识产权归属于公司，仅用于内部学习交流。
        </p>
      </div>
    </div>
    <el-checkbox v-model="checked">我已阅读并同意以上规则，同意遵守公司相关管理规范及制度</el-checkbox>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" type="primary" @click="closeDialog">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    typeOriginal: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      checked: false,
      type: -1
    }
  },
  methods: {
    closeDialog() {
      if (!this.checked) {
        this.$message.warning('请阅读并同意以上规则')
        return
      }
      this.$emit('handleCheck', this.checked)
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.copyright-dialog {
  .tips {
    color: red;
  }

  .title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 16px;
    display: inline-block;
  }

  .copyright-content {
    line-height: 20px;
    margin-bottom: 16px;
  }

  .el-checkbox__label {
    color: #333;
  }
}
</style>
