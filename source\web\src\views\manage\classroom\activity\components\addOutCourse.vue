<template>
  <div class="outlink-dialog">
    <el-dialog
      width="960px"
      :visible="visible"
      :title="outLinkType === 'add' ? '添加外链内容': '编辑外链内容'"
      :close-on-click-modal="false"
      :before-close="cancel"
      custom-class="add-out-course"
    >
      <div class="content-body">
        <el-form ref="form" :model="form" label-width="120px" :rules="rules" :validate-on-rule-change="false">
          <el-form-item class="input-style" label="内容标题" prop="content_name">
            <el-input v-model="form.content_name" placeholder="请输入内容标题" clearable></el-input>
            <span class="custom-el-input-count">{{handleValidor(form.content_name, 50, '1')}}/50</span>
          </el-form-item>

          <!-- <el-form-item class="input-style" label="完成任务说明" prop="finished_condition_desc">
            <el-input v-model="form.finished_condition_desc" placeholder="请输入任务说明" clearable></el-input>
            <span class="custom-el-input-count">{{handleValidor(form.finished_condition_desc, 50, '1')}}/50</span>
          </el-form-item>-->

          <el-form-item class="coustom-form-url" label="跳转链接" prop="urlRules" :rules="urlRules()">
            <el-row class="flex-row">
              <el-tag size="small" class="tag">PC端</el-tag>
              <el-input
                v-model="form.resource_url"
                placeholder="请输入PC端跳转链接"
                clearable
                style="width: unset;"
              ></el-input>
              <el-link class="test-link" type="primary" :underline="false" @click="urlTest">测试跳转</el-link>
            </el-row>
            <el-row class="flex-col">
              <el-tag class="tag" style="height: 20px;line-height: 20px;margin: 0 12px 0 0;">移动端</el-tag>
              <div>
                <el-radio-group v-model="form.relation_content_resource_type" @change="handleResourceTypeChange">
                  <el-radio label="H5">H5页面</el-radio>
                  <el-radio label="WechatMini">小程序</el-radio>
                </el-radio-group>
                <div
                  style="margin: 16px 0 0 0;display: flex;flex-direction: column;"
                >
                  <el-input
                    class="wechat-mini-input"
                    placeholder="请输入小程序appid"
                    v-model="form.wechat_mini_appid"
                    clearable
                    style="width: unset;margin-bottom: 16px;"
                    v-if="form.relation_content_resource_type === 'WechatMini'"
                  >
                  </el-input>
                  <div style="display: flex;align-items: center;">
                    <el-input
                      v-model="form.relation_content_url_mobile"
                      :placeholder="`请输入${form.relation_content_resource_type === 'H5' ? 'H5页面' : '小程序'}跳转链接`"
                      clearable
                      style="width: unset;"
                    >
                    </el-input>
                    <el-link
                      class="test-link"
                      type="primary"
                      :underline="false"
                      @click="redirectTo()"
                    >查看配置要求</el-link>
                  </div>
                </div>
              </div>
            </el-row>
          </el-form-item>

          <div class="outlink-custom-tips">
            <img src="@/assets/mooc-img/warning-icon.png" alt />
            <span>请确保填写的跳转链接能在对应的终端打开，链接为空时无法跳转</span>
          </div>
          <!-- <el-form-item label="任务详细说明" class="project-detail-tincy2">
            <sdc-mce-editor
              ref="editor3"
              selector="activity_info2"
              :env="editorEnv" 
              :content="form.relation_content_desc" 
              :urlParams="editorConfig.urlParams"
              :catalogue.sync="editorConfig.catalogue" 
              :urlConfig="editorConfig.urlConfig" 
              :options="editorConfig.options"
              :insertItems="insertItems"
              :key="4"
            />
          </el-form-item>-->
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer-add">
        <convention-confirm v-model="isChooseConvention" style="margin-left: 20px;" />
        <el-button @click="cancel" size="small" style="margin-left: auto;">取 消</el-button>
        <el-button
          @click="submit"
          type="primary"
          size="small"
          :disabled="!isChooseConvention"
        >{{ currentNode && currentNode.id ? '保 存' : '添 加' }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import conventionConfirm from '@/views/components/convention-confirm.vue'
export default {
  components: {
    conventionConfirm
  },
  props: {
    outLinkType: {
      type: String,
      default: 'add'
    },
    visible: {
      type: Boolean,
      default: false
    },
    currentNode: {
      type: Object
    },
    treeNode: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      ssshow: false,
      form: {
        content_name: '',
        finished_condition_desc: '',
        resource_url: '',
        relation_content_url_mobile: '',
        relation_content_desc: '',
        resource_type: 'Other',
        resource_type_name: '外链',
        relation_content_resource_type: 'H5', // H5 | WechatMini
        wechat_mini_appid: ''
      },
      rules: {
        content_name: [
          { required: true, message: '请输入任务名称', trigger: 'blur' }
        ],
        finished_condition_desc: [
          { required: true, message: '请输入任务说明', trigger: 'blur' }
        ]
      },
      editorConfig: {
        catalogue: false,
        options: {
          // 非必传字段
          selector: '#question_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
              formatselect fontsizeselect lineheight |
              bold italic underline strikethrough |
              forecolor backcolor |
              dent align bullist numlist |
              insert codesample |
              fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          // contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          contentinfo: `/content-center/api/v1/content/save_contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        urlParams: {
          is_public: true
        },
        is_open_catalogue: false
      },
      isChooseConvention: false,
      setting: {
        act_type: '99',
        columns: [
          {
            column_code: 'content_name',
            column_name: '任务名称',
            column_type: 'text',
            call_type: ['sync'],
            manual_review: false
          },
          {
            column_code: 'relation_content_desc',
            column_name: '任务简介',
            column_type: 'richText',
            call_type: ['sync'],
            manual_review: false
          }
        ]
      },
      editorEnv: process.env.NODE_ENV,
      insertItems: [
        'link',
        'table',
        'image',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ]
    }
  },
  computed: {
    ...mapState(['projectManageInfo'])
  },
  watch: {
    visible: {
      handler(val) {
        console.log(val)
        if (val) {
          setTimeout(() => {
            this.ssshow = true
          }, 3000)
        }
      },
      immediate: true
    }
  },
  methods: {
    // 初始化数据
    initData(data) {
      this.form = {
        ...data,
        relation_content_resource_type: data.relation_content_resource_type || 'H5'
      }
      
      // 如果是编辑模式且是小程序，确保wechat_mini_appid被设置
      if (this.outLinkType === 'edit' && data.relation_content_resource_type === 'WechatMini') {
        this.form.wechat_mini_appid = data.wechat_mini_appid || ''
      }
    },
    urlTest() {
      const reg = /(http|https):\/\/([\w.]+\/?)\S*/
      if (!this.form.resource_url) return this.$message.error('请输入链接')
      if (this.form.resource_url && !reg.test(this.form.resource_url)) {
        return this.$message.error(
          '原文链接请输入http://或https://开头的链接地址'
        )
      }
      window.open(this.form.resource_url)
    },
    redirectTo() {
      window.open(
        'https://iwiki.woa.com/pages/viewpage.action?pageId=4008670334'
      )
    },
    submit() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          // 验证链接，至少需要填写一个
          if (!this.form.resource_url && !this.form.relation_content_url_mobile) {
            this.$message.error('请至少输入一个跳转链接')
            return false
          }
          
          // 验证链接格式
          const reg = /(https):\/\/([\w.]+\/?)\S*/
          if (this.form.resource_url && !reg.test(this.form.resource_url)) {
            this.$message.error('PC端链接格式错误，请输入https开头的链接地址')
            return false
          }
          
          if (this.form.relation_content_url_mobile && this.form.relation_content_resource_type === 'H5' && !reg.test(this.form.relation_content_url_mobile)) {
            this.$message.error('移动端链接格式错误，请输入https开头的链接地址')
            return false
          }
          
          // 小程序必须填写appid
          if (this.form.relation_content_resource_type === 'WechatMini' && !this.form.wechat_mini_appid) {
            this.$message.error('选择小程序时，必须填写小程序appid')
            return false
          }
          
          // 接入人工审核
          window.$informationReview &&
            window.$informationReview
              .contentReview(this.form, this.setting)
              .then(res => {
                if (res.success && res.status_code * 1 === 1) return

                // 创建提交数据对象，确保包含所有必要字段
                const submitData = {
                  ...this.form,
                  id: this.currentNode ? this.currentNode.id || '' : '',
                  module_id: 99,
                  act_type: 99
                }

                // 确保小程序相关信息被包含
                if (this.form.relation_content_resource_type === 'WechatMini') {
                  submitData.wechat_mini_appid = this.form.wechat_mini_appid
                }

                this.$emit(
                  'updateTreeList',
                  [submitData],
                  this.outLinkType
                ) // edit, add
                this.$emit('update:visible', false)
              })
              .catch(err => {
                console.log(err, 'rrr')
              })
        } else {
          return false
        }
      })
    },
    cancel() {
      this.$refs['form'].resetFields()
      this.$emit('update:visible', false)
    },
    handleValidor(value, num, type) {
      if (!value) return 0
      const china = value.match(/[\u4e00-\u9fa5]/g)
      const zhCount = china && china.join('').length
      const enCount = Math.ceil((value.length - zhCount) / 2)
      const total = zhCount + enCount
      if (total > num) {
        if (type === '1') {
          this.form.content_name = value.slice(0, -1)
        } else {
          this.form.relation_content_desc = value.slice(0, -1)
        }
      }
      return zhCount + enCount
    },
    handleResourceTypeChange() {
      // 当资源类型改变时，清空字段值
      if (this.form.relation_content_resource_type === 'H5') {
        this.form.wechat_mini_appid = ''
      }
    },
    urlRules() {
      let validator = (rule, value, callback) => {
        const { resource_url, relation_content_url_mobile, relation_content_resource_type, wechat_mini_appid } = this.form
        const reg = /(https):\/\/([\w.]+\/?)\S*/
        
        // 检查链接是否为空
        if (!relation_content_url_mobile && !resource_url) {
          callback(new Error('请至少输入一个跳转链接'))
          return
        }
        
        // 检查链接格式
        if (resource_url && !reg.test(resource_url)) {
          callback(new Error('PC端链接格式错误，请输入https开头的链接地址'))
          return
        }
        
        if (this.form.relation_content_url_mobile && this.form.relation_content_resource_type === 'H5' && !reg.test(this.form.relation_content_url_mobile)) {
          callback(new Error('移动端链接格式错误，请输入https开头的链接地址'))
          return
        }
        
        // 如果选择了小程序，检查appid
        if (relation_content_resource_type === 'WechatMini' && !wechat_mini_appid) {
          callback(new Error('请输入小程序appid'))
          return
        }
        
        // 所有验证通过
        callback()
      }
      
      return {
        validator,
        trigger: 'blur',
        required: true
      }
    }
  }
}
</script>
<style lang="less">
.project-detail-tincy2 {
  .editor__area {
    background: #fff;
  }
  .tox.tox-tinymce {
    box-sizing: border-box;
    border: 1px solid #ccc !important;
    height: 205px !important;
    width: 792px !important;
    margin: initial;
    .tox-editor-container {
      height: 203px !important;
    }
    .tox-sidebar-wrap .tox-edit-area {
      min-height: 129px !important;
      box-sizing: border-box;
    }
  }
}
</style>
<style lang="less" scoped>
.outlink-dialog {
  .content-body {
    .flex-row {
      display: flex;
      align-items: center;

      .tag {
        text-align: center;
        min-width: 60px;
        margin-right: 10px;
        height: 20px;
        line-height: 20px;
        color: #0052d9;
        background-color: #eaeffc;
        border: unset;
      }
    }

    .flex-col {
      display: flex;
      align-items: baseline;
    }

    .input-style {
      position: relative;

      :deep(.el-input) {
        width: 524px;
        .el-input__inner {
        }

        .el-input__suffix {
          position: absolute;
          right: 43px;
        }
      }
    }

    .input-area-style {
      :deep(.el-textarea) {
        width: 524px;
      }
    }

    .custom-el-input-count {
      color: #acacac;
      background: #fff;
      position: absolute;
      font-size: 12px;
      bottom: 6px;
      right: 282px;
      line-height: 20px;
    }

    .coustom-form-url {
      margin-bottom: 16px !important;
      :deep(.el-form-item__label) {
        font-weight: 500;
      }
      :deep(.el-form-item__label::before) {
        content: '*';
        color: #f81d22;
        margin-right: 4px;
      }

      .text-blue {
        margin-left: 10px;
      }

      :deep(.el-input) {
        .el-input__inner {
          width: 452px;
        }
      }

      .wechat-mini-input {
        width: 452px !important;
      }
    }

    .outlink-custom-tips {
      padding-left: 8px;
      margin-left: 119px;
      margin-bottom: 20px;
      height: 40px;
      line-height: 40px;
      width: 524px;
      color: #ff7548;
      font-size: 14px;
      background-color: #fcf6ed;
      img {
        width: 20px;
        margin-right: 4px;
      }
    }

    .sp-bt {
      justify-content: space-between;
      margin-bottom: 0px !important;
    }

    .flex-row:first-child {
      margin-bottom: 20px;
    }

    .is-duration {
      width: 80px;
      margin-left: 24px;
    }

    .condition {
      width: 130px;
      margin: 0 10px 0 10px;
    }

    :deep(.is-controls-right) {
      width: 130px;
      height: 32px !important;
      line-height: 32px;

      .el-input-number__decrease,
      .el-input-number__increase {
        line-height: 16px !important;
      }
    }

    :deep(.el-date-editor .el-input__inner) {
      padding-left: 30px;
    }

    .test-link {
      margin-left: 12px;
    }
  }
  :deep(.add-out-course) {
    .el-dialog__body {
      padding-left: 10px !important;
    }
    .el-form-item {
      margin-bottom: 24px;
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .video-durtion-tips {
    color: red;
    font-size: 14px;
  }
}
:deep(.el-dialog__footer) {
  padding-top: 0;
  .dialog-footer-add {
    display: flex;
    align-items: center;
    margin-top: 24px;
  }
}
</style>
