> 基于[SDC前端全家桶框架]((http://git.code.oa.com/SDCFront/sdc-web-app.git))搭建的企业级web端应用。

## 项目运行步骤

- 先安装 node.js

- 执行命令 npm install @tencent/tnpm -g --registry=http://r.tnpm.oa.com --verbose

- 然后在项目文件目录下执行命令 tnpm install，下载依赖包 

- 依赖包下载完成后，执行启动项目命令 npm run serve

- 项目启动后如果需要连接后台，须在 vue.config.js 文件配置本地代理     
    proxy: {
        // 本地连接后台本地
        '/api': {
            target: 'http://***********:8088', // 后台ip地址
            changOrigin: true
        }
    }

    修改 target 属性值为后台地址即可

- 项目如需打包，执行命令 npm run build:test 和 npm run build:prd，分别打包测试环境和生产环境
