<template>
  <div class="auto-dialog-main">
    <el-dialog 
    title="自动加入" 
    :visible.sync="visible"
    width="800px"
    :close-on-click-modal="false"
    :before-close="closeDialog"
    >
      <el-form :model="form" :rules="rules" ref="form" label-position="right" label-width="120px">
        <el-form-item label="自动加入项目">
          <el-switch
            v-model="form.enable_auto_join"
            active-text="已开启">
          </el-switch>
        </el-form-item>
        <template v-if="form.enable_auto_join">
          <el-form-item label="设置学员范围">
            <AudienceSelector
            audience
            :showTab="['unit', 'group']"
            multiple
            v-model="form.target" 
            ref="selector"
            appCode="qlearning"
            :env="audienceEnv" 
            importNumber='1000'
            :isShowCount="false"
            :employee="false"
            :groupBtnHide="true"
            :createStudentID="true"
            />
          </el-form-item>
          <el-form-item label="设置有效期">
            <el-date-picker
              v-model="setTime"
              size="small"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00','23:59:59']"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="设置加入时间" prop="every_day">
            <span>每天</span>
            <el-time-select
              placeholder="起始时间"
              v-model="form.every_day"
              :picker-options="{
                start: '00:00',
                step: '00:15',
                end: '24:00'
              }">
            </el-time-select>
            <span class="time-tips">将设定范围内的学员加入此项目</span>
          </el-form-item>
          <el-form-item label="加入学习提醒">
            <el-switch
              v-model="addTips"
              active-color="#0052D9"
              inactive-color="#dcdfe6"
              active-text="已开启"
              @change="handleSwitch"
              >
            </el-switch>
            <div class="info-template-box" v-show="addTips">
              <div class="item-contenet">
                <el-checkbox v-model="form.mailChceck" @change="changeTemplate($event, 'mail')">邮件</el-checkbox>
                <span @click="getInfoTemplate('mail')" class='item-check-template'>
                  查看消息模板
                </span>
              </div>
              <div class="item-contenet">
                <el-checkbox v-model="form.msgCheck" @change="changeTemplate($event, 'tips')">企微Tips</el-checkbox>
                <span @click="getInfoTemplate('tips')" class='item-check-template'>
                  查看消息模板
                </span>
              </div>
              <div class="item-contenet">
                <el-tooltip effect="dark" :disabled="projectManageInfo.enable_bot" content="请先联系超级管理员启用企微机器人通知功能" placement="top">
                  <el-checkbox :disabled="!projectManageInfo.enable_bot" v-model="form.botCheck" @change="changeTemplate($event, 'bot')">企微机器人</el-checkbox>
                </el-tooltip>
                <span @click="getInfoTemplate('bot')" class='item-check-template'>
                  查看消息模板
                </span>
              </div>
              <div class="item-contenet">
                <el-tooltip effect="dark" :disabled="projectManageInfo.enable_hr_assistant" content="请先联系超级管理员启用HR助手通知功能" placement="top">
                  <el-checkbox :disabled="!projectManageInfo.enable_hr_assistant" v-model="form.hrCheck" @change="changeTemplate($event, 'hrAssistant')">HR助手</el-checkbox>
                </el-tooltip>
                <span @click="getInfoTemplate('hrAssistant')" class='item-check-template'>
                  查看消息模板
                </span>
              </div>
            </div>
          </el-form-item>
        </template>
      </el-form>
      <CustomTips
        v-if="form.enable_auto_join"
        class="auto-tips"
        title="按照设置的加入时间每天将指定范围的增量学员同步到项目中，如果学员已存在，则不会重复添加；如果之前已同步的学员不符合加入条件，请手动移除" 
        IconName="el-icon-warning" 
        backgroundColor="#fdf6ec" 
        color="#FF7548"
        >
      </CustomTips>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" size="small">取 消</el-button>
        <el-button type="primary" @click="handleSave" size="small">确 定</el-button>
      </div>
    </el-dialog>
    <commonInfoTemplate :visible.sync="commonInfoTemplateDialog" ref="commonInfoTemplateRef" :donCloseAfterSaving="true" templateType="atuoAdd"></commonInfoTemplate>
  </div>
</template>
<script>
import { AudienceSelector } from '@tencent/sdc-audience'
import { autoAddStudent, infoTemplate, getStudentAutoJoin } from '@/config/mooc.api.conf'
import commonInfoTemplate from '@/views/manage/mooc/components/commonInfoTemplate.vue'
import CustomTips from '@/components/tips.vue'
import { mapState } from 'vuex'
export default {
  components: {
    AudienceSelector,
    commonInfoTemplate,
    CustomTips
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      audienceEnv: process.env.NODE_ENV,
      form: {
        enable_auto_join: false,
        target: '',
        every_day: '',
        mailChceck: false,
        msgCheck: false,
        botCheck: false,
        hrCheck: false
      },
      addTips: true,
      setTime: '',
      commonInfoTemplateDialog: false,
      commTemplateList: [],
      msg_value_list: [],
      rules: {
        every_day: [
          { required: false, pattern: '[0-9]', message: '请输入数字', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapState(['projectManageInfo'])
  },
  mounted() {
    this.getStudentAutoJoinFn()
  },
  methods: {
    closeDialog() {
      this.$emit('update:visible', false)
    },
    handleSave() {
      const { mooc_course_id } = this.$route.query
      const { enable_auto_join, target, every_day } = this.form
      if (!enable_auto_join) {
        this.form = {
          ...this.form,
          target: '',
          every_day: '',
          mailChceck: false,
          msgCheck: false,
          botCheck: false,
          hrCheck: false
        }
        this.setTime = ''
        this.addTips = false
      }
      const start_time = this.setTime?.length ? this.setTime[0] : ''
      const end_time = this.setTime?.length ? this.setTime[1] : ''
      let list = this.msg_value_list.filter((e) => e.template_id)
      if (this.addTips && !list?.length) {
        this.$message.warning('请选择催办渠道')
        return
      }
      
      const params = {
        mooc_course_id,
        enable_auto_join,
        target,
        start_time,
        end_time,
        every_day,
        msg_list: list,
        remind: this.addTips
      }
      
      autoAddStudent(params).then((res) => {
        this.closeDialog()
        const msg = enable_auto_join ? '自动加入添加成功' : '自动加入关闭成功'
        this.$message.success(msg)
      })
    },
    // 加入学习提醒
    handleSwitch(val) {
      if (!val) {
        this.msg_value_list = []
      }
    },
    // 获取消息模板
    getInfoTemplate(type) {
      // notify--通知  remind--催办
      this.commonInfoTemplateDialog = true
      const { mooc_course_id } = this.$route.query
      infoTemplate({ mooc_course_id, template_type: type, module_name: 'notify' }).then((res) => {
        this.$nextTick(() => {
          this.$refs.commonInfoTemplateRef.initData(res)
        })
      })
    },
    // 模板勾选
    changeTemplate(val, type) {
      if (!val) {
        this.msg_value_list = this.msg_value_list.filter((e) => e.type !== type)
        this.form = JSON.parse(JSON.stringify(this.form))
        return
      }
      const { mooc_course_id } = this.$route.query
      infoTemplate({ module_name: 'remind', mooc_course_id, template_type: type }).then((res) => {
        this.msg_value_list.push({
          template_id: res.template_id,
          mooc_course_id: mooc_course_id,
          type
        })
      })
    },
    // 获取学员自动加入规则
    getStudentAutoJoinFn() {
      const { mooc_course_id } = this.$route.query
      getStudentAutoJoin({ mooc_course_id }).then(res => {
        const { enable_auto_join, target, every_day, start_time, end_time, remind, msg_list, mooc_course_id } = res
        if (start_time) {
          this.setTime = [start_time, end_time]
        }
        this.form = {
          enable_auto_join,
          target,
          every_day
        }
        this.msg_value_list = msg_list.map((e) => {
          return {
            ...e,
            mooc_course_id
          }
        })
        this.addTips = remind
        msg_list.forEach(v => {
          switch (v.type) {
            case 'mail':
              this.form.mailChceck = true
              break
            case 'tips':
              this.form.msgCheck = true
              break
            case 'bot':
              this.form.botCheck = true
              break
            case 'hrAssistant':
              this.form.hrCheck = true
              break    
            default:
              break
          }
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
.auto-dialog-main {
  .add-time-input {
    width: 200px;
    margin-left: 10px;
    margin-right: 10px
  }
  .info-template-box {
    display: flex;
    flex-direction: column;
    width: 366px;
    margin-top: 16px;
    .item-check-template {
      cursor: pointer;
      color: #0052D9;
    }
    // .active-mail-template, .active-sms-template, .active-check-template {
    //   cursor: not-allowed;
    //   pointer-events: none;
    // }
  }
  .item-contenet {
    display: flex;
    justify-content: space-between;
    height: 22px;
    line-height: 22px;
  }
  .item-contenet + .item-contenet {
    margin-top: 12px;
  }
  .auto-tips {
    padding: 10px 16px;
    line-height: 20px;
  }
  .el-date-editor {
    margin-left: 12px;
    margin-right: 12px;
  }
  .time-tips {
    color: rgba(0, 0, 0, 0.4)
  }
}
</style>
