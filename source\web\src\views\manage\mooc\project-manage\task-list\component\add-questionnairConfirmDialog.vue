<template>
  <div>
    <el-dialog
      title="温馨提示"
      :visible.sync="visible"
      width="508px"
      :before-close="handleQuestionnairClose"
      class="add-dialog"
      >
      <div class="add-dialog-content">
        <div class="title1">腾讯学堂在线学习平台中，进行各类内容发布时，</div>
        <div class="title2">需共同遵守<span class="link">《腾讯学堂学习平台文明公约》。</span></div>
        <p>请阅读公约并勾选下方勾选项后，继续进行问卷的创建/编辑。</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <convention-confirm v-model="conventionChecked" style="margin: 0 0 16px 0;" ref="conventionConfirmRef" />
        <el-button @click="handleQuestionnairClose" style="width: 155px;height: 32px;line-height: 32px;text-align: center;padding: 0;border: 1px solid #DCDCDC;background: #FFF;font-size: 14px">取消</el-button>
        <el-button type="primary" @click="continueCreate" :disabled="!conventionChecked" style="width: 155px;font-size: 14px;padding: 0;text-align: center;height: 32px;line-height: 32px;">继续创建&编辑问卷</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import env from 'config/env.conf.js'
import conventionConfirm from 'views/components/convention-confirm.vue'
export default {
  components: {
    conventionConfirm
  },
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    questionType: {
      type: String,
      default: 'add'
    }
  },
  data() {
    return {
      conventionChecked: false
    }
  },
  methods: {
    continueCreate() {
      this.$emit('continueCreateQuestionnaire', { type: this.questionType })
      this.$emit('update:visible', false)
    },
    goConvention() {
      let envName = env[process.env.NODE_ENV]
      let url = `${envName.commonPath}common/convention.html`
      window.open(url, '_blank')
    },
    handleQuestionnairClose() {
      // this.conventionChecked = false
      this.$nextTick(() => {
        this.$emit('update:visible', false)
      })
    }
  }
}
</script>

<style lang="less" scoped>
.add-dialog {
  .add-dialog-content {
    .title1 {
      font-size: 16px;
      color: #000000e6;
      margin-bottom: 10px;
      font-weight: 600;
      line-height: 24px;
    }
    .title2 {
      font-size: 16px;
      color: #000000e6;
      font-weight: 600;
      line-height: 24px;
      .link {
        color: #0052D9;
      }
    }
    p {
      margin-top: 36px;
      font-size: 14px;
      color: #00000099;
    }
  }
  /deep/.el-dialog__header {
    padding: 24px 24px 20px 32px;
  }
  /deep/.el-dialog__body {
    padding: 24px 32px 16px 32px;
  }
  /deep/.el-dialog__footer {
    padding: 0 32px 24px 32px;
  }
}
/deep/.el-dialog__header {
  // border-bottom: none;
  .el-dialog__title {
    color: #000000e6;
    font-size: 16px;
    font-weight: 600;
    line-height: initial;
  }
  .el-dialog__headerbtn {
    font-size: 22px;
    top: 16px;
  }
}
</style>
