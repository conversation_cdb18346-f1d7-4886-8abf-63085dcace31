<template>
    <div class="notSupported">
        <div class="qrCode">
            <img :src="qrUrl" alt="">
        </div>
        <p class="tips">当前内容暂不支持PC端访问学习<br/>请扫码或复制链接，至移动端访问</p>
        <div class="title">
            标题：{{courseInfo.course_title}}
        </div>
        <div class="copyLink">
            <el-input placeholder="" readonly v-model="src">
                <template slot="append">
                    <span @click="doCopy">复制</span>
                </template>
            </el-input>
        </div>
    </div>
</template>
  
<script>
import { 
  getMobileQrcode
} from '@/config/mooc.api.conf.js'
export default {
  props: {
    courseInfo: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      src: '',
      qrUrl: ''
    }
  },
  watch: {
    courseInfo: {
      handler(val) {
        this.initCode()
        if (val.recourse_type === 'iframeCourseNoReportLearnRecords') {
          this.src = `https://sdc.qq.com/s/yHZGoo?scheme_type=outsourced&resource_type=iframeNoLearnRecords&course_id=${val.outsourced_course_id}`
        } else if (val.recourse_type === 'iframeCourse') {
          this.src = `https://sdc.qq.com/s/MGoSJh?scheme_type=outsourced&resource_type=iframe&course_id=${val.outsourced_course_id}`
        } else if (val.recourse_type === 'graphic') {
          this.src = `https://sdc.qq.com/s/9bd9km?scheme_type=outsourced&resource_type=graphic&course_id=${val.outsourced_course_id}`
        } else if (val.recourse_type === 'video') {
          this.src = `https://sdc.qq.com/s/dShLEm?scheme_type=outsourced&resource_type=video&course_id=${val.outsourced_course_id}`
        }
      },
      deep: true
    }
  },
  created() {
    // this.initCode()
  },
  computed: {
    scene() {
      let { recourse_type, outsourced_course_id } = this.courseInfo
      if (recourse_type === 'iframeCourseNoReportLearnRecords') {
        return `${outsourced_course_id}_zh-CN`
      } else if (recourse_type === 'iframeCourse') {
        return `${outsourced_course_id}_zh-CN_iframe`
      } else if (recourse_type === 'graphic') {
        return `${outsourced_course_id}_zh-CN_graphic`
      } else if (recourse_type === 'video') {
        return `${outsourced_course_id}_zh-CN_video`
      } else {
        return `${outsourced_course_id}_zh-CN`
      }
    },
    webPage() {
      let { recourse_type } = this.courseInfo
      if (recourse_type === 'iframeCourseNoReportLearnRecords') {
        return `pages/webview/outsourced/iframeNoLearnRecords/index`
      } else if (recourse_type === 'outside') {
        return `pages/webview/outsourced/outside/index`
      } else {
        return `pages/webview/outsourced/iframe/index`
      }
    }
  },
  methods: {
    initCode() {
      let scene = this.scene
      let page = this.webPage
      const params = {
        scene: scene,
        page: page,
        env_version: process.env.NODE_ENV === 'production' ? 'release' : 'trial'
      }
      getMobileQrcode(params).then((res) => {
        this.qrUrl = `data:image/png;base64,${res}`
      })
    },
    doCopy() {
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      input.value = this.src
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      this.$message.success(
        this.$langue('Mooc_Common_Alert_CopySucessed', {
          defaultText: '复制成功'
        })
      )
      // 复制后移除输入框
      input.remove()
    }
  }
}
</script>
<style lang="less" scoped>
.notSupported{
    width: 1158px;
    height: calc(100% - 40px);
    margin: 20px auto;
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    font-family: "PingFang SC";
    .qrCode{
        width: 120px;
        height: 120px;
        margin: 100px auto 0;
        img{
            width: 100%;
        }
    }
    .tips{
        width: 210px;
        margin: 16px auto 0;
        color: #00000099;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
    }
    .title{
        width: 400px;
        margin: 24px auto;
        color: #00000099;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
    }
    .copyLink{
        width: 400px;
        margin: 0 auto;
        /deep/.el-input__inner{
            font-family: "PingFang SC";
            color: #00000066;
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            background: #fff;
            border-color: #dcdcdc;
            &:focus{
                border-color: #dcdcdc;
            }
        }
        /deep/.el-input-group__append {
            font-family: "PingFang SC";
            background: #fff;
            border-color: #dcdcdc;
            color: #000000e6;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            cursor: pointer;
        }
    }
}
</style>
