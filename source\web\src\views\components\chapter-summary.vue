<template>
  <div class="preview-lists">
    <div class="title-text">
      <span>{{ title }}</span>
      <slot></slot>
    </div>
    <div class="list-body">
      <div class="common-box">
        <div
          v-for="(item, index) in list"
          :key="index"
          class="common-item"
          :dt-areaid="dtListBody(item, 'area')"
          :dt-eid="dtListBody(item , 'eid')"
          :dt-remark="dtListBody(item , 'remark')"
        >
          <div class="common-item-right">
            <p class="head" @click="toChapter(item)">
              <el-tooltip effect="dark" popper-class="click-tooltip" placement="bottom-end">
                <div slot="content">
                  <span class="click-tips">点击即可跳转到对应章节</span>
                </div>
                <span class="title">{{ item.chapter_title }}</span>
              </el-tooltip>
              <span class="chapter-time">{{ item.chapter_time }}</span>
            </p>
            <div class="chapter-desc-box" v-if="item.chapter_content && item.chapter_content !== '\n'">
              <p class="chapter-desc" v-html="item.chapter_content" :class="{'maxheight': !item.showFullText}" :ref="'descText' + index"></p>
              <span class="light-key" :class="{'no-full-text': !item.showFullText}" v-if="item.showBtn" @click="handleMore(item)">{{ item.showFullText ? '收起' : '...更多' }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { qlearningModuleTypes } from '../../utils/constant'
export default {
  props: {
    title: {
      type: String,
      default: '课单内容列表'
    },
    scene: {
      type: String
    },
    list: {
      type: Array,
      default: () => []
    },
    paramsData: {
      type: Object,
      default: () => { }
    }
  },
  data() {
    return {
      portUrl: location.hostname.endsWith('.woa.com')
        ? process.env.VUE_APP_PORTAL_HOST_WOA
        : process.env.VUE_APP_PORTAL_HOST
    }
  },
  computed: {
    ...mapState(['moocLang']),
    // 埋点
    dtListBody () {
      return (item, type) => {
        // console.log(item, 'kk')
        if (type === 'area') {
          return `area_${this.title}_${item.module_name}_${item.item_id}`
        } else if (type === 'eid') {
          return `element_${this.title}_${item.module_name}_${item.item_id}`
        } else if (type === 'remark') {
          return JSON.stringify({ 
            page: this.paramsData.course_name,
            page_type: '网课详情页',
            container_type: this.title,
            container: this.title,
            click_type: 'data',
            content_type: item.module_name,
            content_id: item.item_id,
            content_name: item.title,
            terminal: 'PC'
          })
        }
      }
    }
  },
  mounted() {
    // 暂时放在这里的，是否展示更多按钮
    this.$nextTick(() => {
      this.list.forEach((ele, index) => {
        if (ele.chapter_content && ele.chapter_content !== '\n') {
          ele.showFullText = false
          let element = this.$refs['descText' + index][0]
          if (element.scrollHeight > element.clientHeight) {
            ele.showBtn = true
          } else {
            ele.showBtn = false
          }
        // console.log(ele.showBtn, 'showBtn-----')
        }
      })
      this.$forceUpdate()
    })
  },
  methods: {
    // 展开更多
    handleMore(item) {
      item.showFullText = !item.showFullText
      // this.$set(item, 'showFullText', !item.showFullText)
      this.$forceUpdate()
      // console.log(item.showFullText, 'showFullText----', this.list)
    },
    // 点击跳转到对应的章节
    toChapter(item) {
      this.$emit('toChaptersPosition', item.chapter_time_point)
    },
    getModuleClass(module_id) {
      // if (this.scene !== 'extand') {
      //     let cardType = recommendModuleTypes.find((item) => module_id === item.module_id)
      //     if (cardType) {
      //         return cardType.moduleClassName
      //     }
      // } else {
      let cardType = qlearningModuleTypes.find(
        (item) => module_id === item.module_id
      )
      if (cardType) {
        return cardType.moduleClassName
      }
      // }
    },
    getEnImg(module_id) {
      let cardType = qlearningModuleTypes.find(
        (item) => module_id === item.module_id
      )
      if (cardType) {
        return require(`@/assets/img/list-tags/${cardType.enImg}`)
      }
    },
    getModuleName(module_id) {
      let cardType = qlearningModuleTypes.find(
        (item) => module_id === item.module_id
      )
      if (cardType) {
        return cardType.module_name
      }
    }
  },
  filters: {
    handleIndex: function (index) {
      const curIndex = index < 9 ? `0${index + 1}` : index + 1
      return curIndex
    },
    conuntFilter: function (val) {
      let str = ''
      str =
        val > 0 ? (val >= 10000 ? `${(val / 10000).toFixed(1)}万` : val) : '0'
      return str
    }
  }
}
</script>

<style lang="less" scoped>
.preview-lists {
  width: 272px;
  border-radius: 4px;
  background-color: #fff;

  .title-text {
    height: 68px;
    line-height: 68px;
    padding-left: 16px;
    color: rgba(51, 51, 51, 1);
    font-size: 20px;
    font-weight: bold;
    border-bottom: solid 1px #eeeeee;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .list-body {
    width: 100%;
    padding: 15px 0;

    .common-box {
      padding: 1px 16px;
      overflow-y: auto;
      max-height: 469px;
      width: 100%;
      .common-item {
        width: 100%;
        display: flex;
        align-items: baseline;
        margin-bottom: 12px;
        cursor: pointer;

        .common-item-right {
          width: 100%;
          position: relative;
          .head {
            width: 100%;
            margin-bottom: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-family: "PingFang SC";
          }
          .title {
            width: 200px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            color: #000000e6;
            font-size: 12px;
            font-weight: 600;
            line-height: 20px;
          }
          .chapter-time {
            color: #6c7390;
            font-size: 12px;
            font-weight: 400;
            line-height: 20px;
            max-width: 48px;
            align-self: flex-start;
          }
          .head:hover {
            .title {
              color: #0052d9;
              font-weight: 600;
            }
            .chapter-time {
              color: #000000e6;
            }
          }
          .chapter-desc-box {
            padding: 8px 12px;
            background: #F6F6F6;
          }
          .chapter-desc {
            box-sizing: border-box;
            border-radius: 4px;
            line-height: 20px;
            color: #000000cc;
            /*
            text-overflow: ellipsis;
            */
            word-wrap: break-word;
            word-break: break-all;
            font-family: "PingFang SC";
            font-size: 12px;
            font-weight: 400;
          }
          .maxheight {
            max-height: 120px;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 6;
            overflow: hidden;
          }
          .light-key {
            display: inline-block;
            position: absolute;
            right: 12px;
            bottom: 8px;
            color: #0052d9;
            background-color: #F6F6F6;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
          }
          .no-full-text:before {
            position: absolute;
            right: 100%;
            content: "";
            width: 100px;
            height: 20px;
            background-image: linear-gradient(270deg,#F6F6F6,hsla(0,0%,100%,0));
          }
        }
      }

      .common-item:last-child {
        margin-bottom: unset;
      }
    }
  }
}
</style>
<style lang="less" >
.el-tooltip__popper.is-dark.click-tooltip {
  height: 20px;
  line-height: 20px;
  padding: 0 4px;
  border-radius: 3px;
  background: var(--text-icon-font-gy-190-primary, #000000e6);
  box-shadow: 0 5px 5px -3px #0000001a, 0 8px 10px 1px #0000000f, 0 3px 14px 2px #0000000d;
  .click-tips {
    color: #ffffff;
    font-size: 12px;
    font-weight: 400;
    height: 20px;
    line-height: 20px;
  }
}
</style>
