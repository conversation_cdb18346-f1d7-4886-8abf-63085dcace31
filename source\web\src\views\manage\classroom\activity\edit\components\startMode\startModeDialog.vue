<template>
  <div class="start-mode-dialog">
    <el-dialog
      :visible.sync="visible"
      custom-class="c-dialog"
      :before-close="closeDialog"
      :close-on-click-modal="false"
      width="640px"
    >
      <template slot="title">
        <div class="title">
          <span>问卷启动配置</span>
        </div>
      </template>
      <start-set ref="startSet" :startMode="startMode" />
      <div class="button-group">
        <el-button @click="closeDialog" style="margin-left: auto;">取消</el-button>
        <el-button type="primary" @click="confirm">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import startSet from './startSet.vue'
export default {
  components: {
    startSet
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    startMode: {
      type: Number,
      default: 1 // 0 手动 1 自动
    }
  },
  watch: {
  },
  methods: {
    closeDialog() {
      this.$emit('cancel')
    },
    confirm() {
      const startMode = this.$refs.startSet.getStartMode()
      this.$emit('confirm', startMode)
    }
  }
}
</script>

<style lang="less" scoped>
.start-mode-dialog {
  .title {
    font-size: 16px;
    font-weight: 600;
  }
  :deep(.el-dialog__header) {
    height: 72px;
    padding: 0 34px 0 32px;
    display: flex;
    align-items: center;
  }
  :deep(.el-dialog__body) {
    padding: 23px 32px 24px 52px;
  }

  .button-group {
    display: flex;
    margin-top: 44px;

    .el-button {
      width: 60px;
      height: 32px;
      font-size: 14px;
      display: flex;
      align-items: center;
      justify-content: center;
      &:first-child {
        margin-right: 8px;
      }
    }
  }
}
</style>
