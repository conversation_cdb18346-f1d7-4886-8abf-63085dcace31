<template>
  <!-- 课单，猜你喜欢-推荐，延伸学习 -->
  <div
  class="item-like-popularize"
  @click="handleToPath(cardData)"
  :dt-areaid="dtCard('areaid', cardData)"
  :dt-remark="dtCard('remark', cardData)"
  :dt-eid="dtCard('eid', cardData)"
  >
    <div class="img-box">
      <div v-if="cardData.isAdvert" class="advert-name">推荐</div>
      <div v-else class="module-name">{{ courseName }}</div>
      <van-image class="item-img" lazy fit="fill" :src="cardData.photo_url || ''">
        <template v-slot:error>
          <img :src="formatModuleMap(cardData.module_id)" />
        </template>
      </van-image>
      <div v-if="!cardData.isAdvert" class="view-num"><span class="view-icon"></span>{{ transformUnitW(cardData.play_total_count) }}</div>
      <div class="play-time" v-if="showModuleTips(cardData)">{{ showModuleTips(cardData) }}</div>
    </div>
    <div class="item-left">
      <div class="lf-top">
        <span class="top-title overflow-l2">
          <span class="tag" v-if="cardData.isAdvert && cardData.banner_type !== 1">{{ cardData.module_name }}</span>
          {{ cardData.content_name }}
        </span>
      </div>
      <div v-if="cardData.isAdvert" class="lf-mid lf-mid-d" :class="cardData.banner_type === 1 ? 'overflow-l2' : 'overflow-l1'">
        <span v-if="cardData.description">{{ cardData.description }}</span>
      </div>
      <!-- <div v-else class="lf-mid lf-mid-a">
        <span v-if="cardData.avg_score">{{ `${cardData.avg_score}分` }}</span>
      </div> -->
      <div class="lf-bottom" v-if="cardData.labels && cardData.labels.length">
        <span class="bottom-tag overflow-l1" v-for="(e, i) in cardData.labels" :key="i">{{ e }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import { transformUnitW, formatModuleMap, transforNcTime, commonJump } from 'utils/tools'
import { Toast } from 'vant'
import { actTypes } from 'utils/moduleMap.js'
export default {
  props: {
    cardData: {
      type: Object,
      default: () => ({})
    },
    isMinute: {
      type: Boolean,
      default: false
    },
    entry: {
      type: String,
      default: ''
    },
    commonInfo: {
      type: Object,
      default: () => ({})
    },
    courseData: {
      type: Object,
      default: () => ({})
    },
    courseType: {
      type: String,
      default: 'net'
    }
  },
  data() {
    return {
      transformUnitW,
      formatModuleMap,
      transforNcTime,
      actTypes
    }
  },
  watch: {
    cardData: {
      immediate: true,
      handler(newVal) {
        const fieldMap = {
          photo_url: ['cover_img_url'],
          content_url: ['href'],
          play_total_count: ['view_count']
        }
        for (let targetKey in fieldMap) {
          if (!newVal.hasOwnProperty(targetKey)) {
            for (let sourceKey of fieldMap[targetKey]) {
              newVal[targetKey] = newVal[sourceKey]
            }
          }
        }
      }
    }
  },
  computed: {
    courseName() {
      if (this.cardData.module_id === 99) {
        return '外链'
      }
      return this.cardData.module_name
    },
    course_id() {
      return this.courseData.course_id || this.$route.query.course_id || this.$route.query.activity_id
    },
    pageTypeName() {
      let obj = {
        'net': '网课',
        'face': '面授课',
        'activity': '活动'
      }
      return obj[this.courseType]
    },
    showModuleTips() {
      return ({ module_id, duration = 0, word_num = 0, task_count = 0, isAdvert }) => {
        let tips = ''
        if (isAdvert) { // 推广
          tips = duration ? transforNcTime(duration * 60) : ''
        } else {
          if ([1, 2, 4].includes(module_id)) { // 网络课, 面授课，活动
            tips = this.isMinute ? transforNcTime(duration * 60) : transforNcTime(duration)
          } else if ([7, 8].includes(module_id)) { // 案例，文章
            tips = `${word_num || 0}` + this.$langue('Mooc_ProjectDetail_TaskList_Words', { defaultText: '字' })
          } else if ([10].includes(module_id)) { // 培养项目
            tips = `${task_count || 0}` + this.$langue('Mooc_ProjectDetail_BasicInfo_Tasks', { defaultText: '项任务' })
          } else if ([15].includes(module_id)) { // 课单
            tips = `${task_count || 0}` + this.$langue('NetCourse_Contents', { defaultText: '个内容' })
          }
        }
        return tips
      }
    },
    dtCard() {
      return (type, row) => {
        let container = ''
        let actRow = this.actTypes.find((e) => row.module_id === e.module_id)
        if (this.entry === 'courseType') { // 课单
          container = `课单`
        } else if (this.entry === 'advertLike') { // 推广猜你喜欢
          container = row.isAdvert ? '推广' : '猜你喜欢'
        } else if (this.entry === 'extend') {
          container = '延伸学习'
        }
        const data = {
          page: document.title,
          page_type: this.pageTypeName + '详情页面-移动新版',
          container: container,
          click_type: 'data',
          content_type: row.module_name,
          content_id: row.item_id,
          content_name: row.content_name,
          act_type: actRow.act_type,
          container_id: '',
          page_id: '',
          terminal: 'H5'
        }
        if (type === 'remark') {
          return JSON.stringify(data)
        } else if (type === 'eid') {
          return `element_${this.course_id}_${row.item_id}`
        } else {
          return `area_${this.course_id}_${row.item_id}`
        }
      }
    }
  },
  methods: {
    handleToPath(row) {
      const { content_url, module_id, item_id, act_type } = row

      // 先调用 commonJump 方法，如果返回 true 表示已处理，则直接返回
      let isHandler = commonJump(row)
      if (isHandler) return

      // 如果 commonJump 未处理，继续执行原有逻辑
      if (!content_url) {
        Toast('跳转链接为空')
        return
      }
      this.$emit('handleToPath', row)
      // 码客、行家、图文、外链暂不支持跳转
      // const moduleIds = [5, 6, 9, 99]
      const moduleIds = [5, 6, 9]
      if (moduleIds.includes(module_id) || (module_id === 99 && !this.checkWebviewHost(content_url))) {
        this.$router.push({
          name: 'mobileError',
          query: {
            type: 2,
            href: encodeURIComponent(content_url)
          }
        })
      } else if (module_id === 1) { // 网络课
        if (act_type && Number(act_type) === 102) {
          window.location.href = content_url
        } else {
          this.$router.push({
            name: 'grayPlay',
            query: {
              course_id: item_id || ''
            }
          })
        }
      } else if (module_id === 2) { // 面授课
        window.location.href = `https://sdc.qq.com/s/Eom5bg?scheme_type=faceClassDetail&course_id=${item_id}`
      } else if (module_id === 8) {
        if (window.__wxjs_environment === 'miniprogram') {
          // 小程序内直接跳转
          window.wx.miniProgram.navigateTo({
            url: `/pages/networkCourse/article/index?graphic_id=${item_id}`
          })
        } else {
          // 打开二合一地址唤起小程序
          window.location.href = content_url
        }
      } else if (content_url) {
        window.location.href = content_url
      }
    },
    // 检查域名是否在webview的白名单内
    checkWebviewHost(url) {
      const host = [
        'https://auth-mgate.woa.com',
        'https://csig.lexiangla.com',
        'https://ihr.tencent.com',
        'https://iwiki.woa.com',
        'https://km.tencent.com',
        'https://km.woa.com',
        'https://lexiangla.com',
        'https://m-learn.woa.com',
        'https://mybucket-1258938271.cos.ap-chengdu.myqcloud.com',
        'https://ntsgw.woa.com',
        'https://oa.m.tencent.com',
        'https://panshi.tencent.com',
        'https://portal.learn.woa.com',
        'https://test-portal-learn.woa.com',
        'https://test-learn.woa.com',
        'https://learn.woa.com',
        'https://video-learn.woa.com',
        'https://exam.woa.com',
        'https://sdc.qq.com',
        'https://hangjia.woa.com',
        'https://qianlong.woa.com',
        'https://ntsapps.woa.com',
        'https://cos.learn.woa.com',
        'https://policy.woa.com',
        'https://s.test.yunassess.com',
        'https://static.taishan.qq.com',
        'https://test-cos.learn.woa.com'
      ]
      return host.findIndex(i => url.startsWith(i)) > -1
    }
  }
}
</script>
<style lang="less" scoped>
.item-like-popularize {
  display: flex;

  .img-box {
    position: relative;
    width: 136px;
    height: 91px;

    .advert-name {
      position: absolute;
      top: 0px;
      left: 0px;
      border-radius: 5px 0 5px 0;
      z-index: 99;
      background-color: #FF843F;
      color: #fff;
      font-size: 12px;
      font-weight: 500;
      padding: 2px 6px;
    }

    .module-name {
      position: absolute;
      top: 4px;
      left: 4px;
      background-color: #0052D9;
      color: #fff;
      border-radius: 2px;
      z-index: 99;
      height: 16px;
      line-height: 16px;
      padding: 0 8px;
      font-size: 10px;
    }

    .view-num {
      position: absolute;
      left: 4px;
      bottom: 4px;
      border-radius: 5px;
      background: #00000099;
      color: #fff;
      height: 20px;
      line-height: 20px;
      padding: 0 4px;
      font-size: 10px;
      display: flex;
      align-items: center;

      .view-icon {
        background: url('~@/assets/img/mobile/view.png') no-repeat center/cover;
        display: inline-block;
        height: 12px;
        width: 12px;
        background-size: 12px;
        margin-right: 2px;
      }
    }

    .play-time {
      position: absolute;
      right: 4px;
      bottom: 4px;
      font-size: 10px;
      border-radius: 5px;
      background: #00000099;
      color: #fff;
      height: 20px;
      line-height: 20px;
      padding: 0 6px;
    }

    :deep(.item-img) {
      width: 136px;
      height: 91px;
      text-align: center;
      line-height: 91px;
      flex-shrink: 0;

      img,
      .van-image__loading {
        width: 136px;
        height: 91px;
        border-radius: 5px;
      }
    }
  }

  .item-left {
    margin-left: 10px;
    width: calc(100% - 145px);
    .lf-top {
      height: 40px;
      .tag {
        background-color: #F5F5F7;
        border-radius: 2px;
        line-height: 16px;
        font-size: 10px;
        padding: 0 4px;
        display: inline-block;
        color: #777777;
        margin-right: 4px;
        font-weight: bold;
      }

      .top-title {
        font-size: 12px;
        color: #333333;
        line-height: 20px;
      }
    }

    .lf-mid {
      font-size: 10px;
      line-height: 20px;
      margin-top: 4px;
    }

    .lf-mid-d {
      color: #666666;
    }

    .lf-mid-a {
      color: #FF5923;
      height: 20px;
    }

    .lf-bottom {
      margin-top: 8px;
      overflow: hidden;
      height: 16px;
      word-break: break-all;
      word-wrap: break-word;

      .bottom-tag {
        border-radius: 2px;
        background: #F5F7FA;
        font-size: 10px;
        color: #777777;
        height: 16px;
        line-height: 16px;
        padding: 0 2px;
        max-width: 52px;
        flex-shrink: 0;
      }

      .bottom-tag+.bottom-tag {
        margin-left: 4px;
      }
    }
  }
}

.item-like-popularize+.item-like-popularize {
  margin-top: 16px;
}
</style>
