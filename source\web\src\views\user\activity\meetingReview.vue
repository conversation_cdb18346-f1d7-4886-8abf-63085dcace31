<template>
  <div class="meeting-review-page">
    <div class="contain-main">
      <div class="page-title">回看：{{ meetingData.subject || '-' }}</div>

      <div class="iframe-box">
        <iframe id="addCourseiframe" style="height: 100%; width: 100%" :src="meetingData.record_view_url" frameborder="0" ></iframe>
        <iframe id="iframe_show" :src="meetingData.record_view_url" width="100%" height="100%" scrolling="no" allowfullscreen="true"></iframe>
      </div>
    </div>
  </div>
</template>

<script>
import { getMeetingRecordUserApi } from '@/config/classroom.api.conf.js'
export default {
  name: 'meetingReview',
  components: {},
  data() {
    return {
      meetingData: {
        subject: '',
        record_view_url: ''
      }
    }
  },
  watch: {},
  computed: {
    activityId() {
      return this.$route.query.activity_id
    }
  },
  created() {
    this.getMeetingRecordInfo()
  },
  mounted() { },
  beforeDestroy() { },
  methods: {
    getMeetingRecordInfo() {
      getMeetingRecordUserApi({ activity_id: this.activityId }).then(res => {
        this.meetingData = res
      })
    }
  }
}
</script>

<style lang="less" scoped>
.meeting-review-page {
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f6f7f9;
  .contain-main {
    width: 1420px;
    height: 100%;
    display: flex;
    flex-direction: column;
    .page-title {
      width: 100%;
      height: 48px;
      margin: 24px 0;
      padding: 10px 20px;
      font-size: 18px;
      line-height: 28px;
      font-weight: 500;
      color: #333333;
      background-color: #fff;
    }
    .iframe-box {
      flex: 1;
      background: #fff;
      overflow-y: auto;
    }
  }
}
  
@media screen and (max-width: 1660px) {
  .contain-main {
    width: 1180px;
  }
  
}
@media screen and (min-width: 1661px) {
  .contain-main {
    width: 1420px;
  }
}
</style>
