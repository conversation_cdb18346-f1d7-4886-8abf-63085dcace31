<template>
  <div class="question-info">
    <div class="activity-base-info">
      <div class="act-name">
        <span>活动名称：</span>
        <span>{{ activityInfo.activity_name }}</span>
      </div>

      <div class="score">
        <span>评价分：</span>
        <div class="score-item">
          <span>综合评分</span>
          <span>{{ activityInfo.total_score ? activityInfo.total_score : '--' }}</span>
          <span>分</span>
        </div>

        <div class="score-item">
          <span>内容评价</span>
          <span
            v-if="!isModifyScore"
          >{{ activityInfo.activity_score ? activityInfo.activity_score : '--' }}</span>
          <el-input v-model="sorceData.activity_score" placeholder class="score-input" v-else></el-input>
          <span>分</span>
        </div>

        <div class="score-item">
          <span>讲师评价</span>
          <span
            v-if="!isModifyScore"
          >{{ activityInfo.lecturer_score ? activityInfo.lecturer_score : '--' }}</span>
          <el-input v-model="sorceData.lecturer_score" placeholder class="score-input" v-else></el-input>
          <span>分</span>
        </div>

        <div class="score-item">
          <span>组织评价</span>
          <span
            v-if="!isModifyScore"
          >{{ activityInfo.organize_score ? activityInfo.organize_score : '--' }}</span>
          <el-input v-model="sorceData.organize_score" placeholder class="score-input" v-else></el-input>
          <span>分</span>
        </div>

        <div class="score-btn">
          <el-button
            type="text"
            icon="el-icon-refresh"
            @click="refreshScore"
            :disabled="!isScoreAutoCalculate || questionFrontData.length === 0 && questionAfterData.length === 0"
          >刷新获取问卷评分</el-button>
          <el-button
            type="text"
            @click="modifyScore"
            class="modify-btn"
            :disabled="questionFrontData.length === 0 && questionAfterData.length === 0"
          >{{ isModifyScore ? '保存' : '手动修改' }}</el-button>
        </div>
      </div>

      <div class="activity-base-tips">
        <img :src="require('@/assets/img/warn.png')" alt />
        手动修改分数后，将不再自动获取问卷收集的用户评分
      </div>
    </div>
    <div class="question-list" v-loading="isLoading">
      <question-view :questionData="questionFrontData" @showFeedback="showFeedback"></question-view>
      <question-view
        style="margin-top: 30px;"
        type="after"
        :questionData="questionAfterData"
        @showFeedback="showFeedback"
      ></question-view>
    </div>

    <!-- 发送反馈总结 -->
    <sendFeedbackSummaryPopup :visible.sync="sendFeedbackSummaryVisible" :surveyId="surveyId" :isCanEdit="isCanEdit" @success="getQuestionDataList"></sendFeedbackSummaryPopup>
  </div>
</template>

<script>
import questionView from './components/questionView.vue'
import sendFeedbackSummaryPopup from './components/sendFeedbackSummaryPopup.vue'
import {
  modifyScoreApi,
  refreshScoreApi,
  getQuestionDataList
} from '@/config/classroom.api.conf.js'
import { mapState, mapMutations } from 'vuex'
export default {
  components: {
    questionView,
    sendFeedbackSummaryPopup
  },
  computed: {
    ...mapState({
      activityInfo: state => state.activity.activityInfo
    })
  },
  watch: {
    activityInfo: {
      handler(newVal) {
        // let val = JSON.parse(JSON.stringify(newVal))
        // if (val.surveys && val.surveys.length > 0) {
        //   this.questionFrontData = val.surveys.filter(item => item.category === 0) // 课前
        //   this.questionAfterData = val.surveys.filter(item => item.category === 1) // 课后
        // }
        this.isScoreAutoCalculate = newVal.is_score_auto_calculate
      },
      deep: true,
      immediate: true
    }
  },
  data() {
    return {
      isLoading: true,
      sorceData: {
        total_score: 0, // 综合评分
        activity_score: 0, // 内容评价
        lecturer_score: 0, // 讲师评价
        organize_score: 0 // 组织评价
      },
      isScoreAutoCalculate: true, // 是否自动计算评分
      questionFrontData: [],
      questionAfterData: [],
      isModifyScore: false, // 是否手动修改评分
      sendFeedbackSummaryVisible: false, // 是否显示发送反馈总结弹窗
      surveyId: '', // 问卷id
      isCanEdit: false // 是否可以编辑
    }
  },
  mounted() {
    this.getQuestionDataList()
  },
  methods: {
    ...mapMutations({
      setActivityInfo: 'activity/SET_ACTIVITY_INFO'
    }),
    modifyScore() {
      this.isModifyScore = !this.isModifyScore
      if (!this.isModifyScore) {
        modifyScoreApi({
          activity_id: this.$route.query.activity_id,
          activity_score: this.sorceData.activity_score * 1,
          lecturer_score: this.sorceData.lecturer_score * 1,
          organize_score: this.sorceData.organize_score * 1
        }).then(res => {
          this.isScoreAutoCalculate = true
          this.setActivityInfo({
            total_score: res.total_score * 1,
            activity_score: res.activity_score * 1,
            lecturer_score: res.lecturer_score * 1,
            organize_score: res.organize_score * 1
          })
          this.$message.success('评分修改成功')
        })
      } else {
        this.sorceData = {
          activity_score: this.activityInfo.activity_score,
          lecturer_score: this.activityInfo.lecturer_score,
          organize_score: this.activityInfo.organize_score
        }
      }
    },
    refreshScore() {
      this.isModifyScore = false
      refreshScoreApi({
        activity_id: this.$route.query.activity_id
      }).then(res => {
        this.setActivityInfo({
          total_score: res.total_score,
          activity_score: res.activity_score,
          lecturer_score: res.lecturer_score,
          organize_score: res.organize_score
        })
        this.$message.success('评分刷新成功')
      })
    },
    // 获取问卷数据统计
    getQuestionDataList() {
      getQuestionDataList({
        activity_id: this.$route.query.activity_id
      }).then(res => {
        this.isLoading = false
        if (res && res.length > 0) {
          this.questionFrontData = res.filter(
            item => item.survey.category === 0
          )
          this.questionAfterData = res.filter(
            item => item.survey.category === 1
          )
          // 测试用
          // this.questionAfterData[0].survey.is_auto_start = 1 // 0 手动启动 1 自动启动
          // this.questionAfterData[0].survey.status = null // null未启动  1启动了   2已关闭
          // this.questionAfterData[0].survey.feedback_type = 1 // 1 提示“反馈总结已发送至活动负责人邮箱” 2 提示“反馈总结已发送至学员、讲师和活动负责人邮箱”
        }
      }).catch(() => {
        this.isLoading = false
      })
    },
    showFeedback(e) {
      console.log(e, 1111111111)
      const { survey_id, isCanEdit } = e
      if (!survey_id) {
        this.$message.error('问卷id异常：null')
        return
      }
      this.surveyId = survey_id
      this.isCanEdit = isCanEdit
      this.sendFeedbackSummaryVisible = true
    }
  }
}
</script>

<style lang="less" scoped>
.question-info {
  padding: 30px 28px 20px 28px;
  background: #fff;
  overflow-y: auto;
  height: 100%;
  .activity-base-info {
    padding: 0 0 20px 0;
    margin-bottom: 21px;
    border-bottom: 1px solid #eee;
    .act-name {
      margin-bottom: 17px;
      span {
        &:nth-child(1) {
          font-size: 14px;
          color: #00000099;
        }
        &:nth-child(2) {
          font-size: 14px;
          color: #000000e6;
        }
      }
    }

    .score {
      display: flex;
      align-items: center;
      gap: 0 24px;

      > span {
        color: #00000099;
        font-size: 14px;
        width: 70px;
        text-align: right;
      }
      .score-item {
        display: flex;
        align-items: center;
        &:nth-child(2) {
          margin-left: -24px;
        }
        span {
          font-size: 14px;
          line-height: 22px;
          &:nth-child(1) {
            color: #00000099;
            flex-shrink: 0;
          }
          &:nth-child(2) {
            display: block;
            padding: 0 8px;
            line-height: 20px;
            background: #f6f6f6;
            color: #0052d9;
            font-size: 12px;
            margin: 0 4px;
            border-radius: 3px;
          }
          &:nth-child(3) {
            color: #000000e6;
          }
        }
        .score-input {
          :deep(.el-input__inner) {
            width: 56px;
            height: 24px;
            margin: 0 4px;
            font-size: 12px;
          }
        }
      }

      .score-btn {
        margin: 0 0 0 auto;
        display: flex;
        align-items: center;
        gap: 0 10px;
        .modify-btn {
          width: 56px;
        }
        :deep(.el-button--text) {
          padding-top: 0;
          padding-bottom: 0;
        }
      }
    }

    .activity-base-tips {
      display: flex;
      align-items: center;
      margin-top: 17px;
      color: #00000099;
      font-size: 14px;
      img {
        width: 15px;
        height: 15px;
        margin-right: 4px;
      }
    }
  }
  .question-list {

  }
}
</style>
