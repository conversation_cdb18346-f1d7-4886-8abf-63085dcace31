<template>
  <div class="note-list-box" >
    <div class="title-text"><span>{{ title }}</span>
      <el-button type="primary" class="note-add" @click="createNote" dt-eid="element_去发表" :dt-remark="dtCreated">
        <i
          class='icon-note-add'></i>
          {{$langue('NetCourse_PostNotes', { defaultText: '去发表' })}}
      </el-button>
    </div>
    <div class="list-body">
      <div class="common-box">
        <div v-for="(item,index) in noteList" :key="index" class="common-item" @click="go(item)" :dt-areaid="dtNoteBox(item, 'area')" :dt-eid="dtNoteList(item , 'eid')" :dt-remark="dtNoteList(item , 'remark')">
            <div class="common-item-right">
                <p class="title">
                  <span>{{index+1}}</span><span class="tags tag-note" v-if="moocLang == 'zh-cn'">图文</span>
                  <img :src="require('@/assets/img/list-tags/Article.png')" v-if="moocLang == 'en-us'" />{{item.graphic_name}}
                </p>
                <p class="info">
                    <span><i class="icon-play"></i>{{ item.view_count | conuntFilter }}</span>
                    <span><i class="icon-fav"></i>{{ item.favorite_count | conuntFilter}}</span>
                    <span><i class="icon-time"></i>{{ item.creator_at&&item.creator_at.split(' ')[0] || '--'}}</span>
                </p>
            </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getRelationGraphic } from 'config/api.conf'
export default {
  props: {
    title: {
      type: String,
      default: '笔记心得'
    },
    paramsData: {
      type: Object,
      default: () => { }
    }
  },
  computed: {
    ...mapState(['moocLang']),
    // 埋点
    dtNoteBox () {
      return (item, type) => {
        const { course_name } = this.paramsData
        if (type === 'area') {
          return `area_${this.title}_graphic_${item.graphic_id}`
        } else {
          return JSON.stringify({ 
            page: course_name,
            container: this.title,
            container_type: '网课详情页',
            content_name: course_name
          })
        }
      }
    },
    dtNoteList () {
      return (item, type) => {
        if (type === 'eid') {
          return `element_${this.title}_graphic_${item.graphic_id}`
        } else {
          return JSON.stringify({ 
            page: this.paramsData.course_name,
            page_type: '网课详情页',
            container_type: '网课详情页',
            container: this.title,
            click_type: 'data',
            content_type: '文章',
            content_id: item.graphic_id,
            content_name: item.graphic_name,
            terminal: 'PC'
          })
        }
      }
    },
    dtCreated () {
      return JSON.stringify({
        page: this.paramsData.course_name,
        page_type: '网课详情页',
        container_type: this.title,
        container: this.title,
        click_type: 'button',
        content_name: '去发表',
        terminal: 'PC'
      })
    }
  },
  data() {
    return {
      portUrl: location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_PORTAL_HOST_WOA : process.env.VUE_APP_PORTAL_HOST,
      noteList: []
    }
  },
  watch: {
    paramsData: {
      handler(newV) {
        const { act_id, act_type } = newV
        if (act_id && act_type) {
          this.getNoteList()
        }
      },
      immediate: true
    }
  },
  methods: {
    getNoteList() {
      const { act_id, act_type } = this.paramsData
      const params = {
        act_type: act_type,
        act_id: act_id,
        page_no: 1,
        page_size: 5
      }
      getRelationGraphic(params).then((data) => {
        this.noteList = data.records
      })
    },
    createNote() {
      const { act_id, course_name, module_id } = this.paramsData
      const { href } = this.$router.resolve({
        name: 'create',
        query: {
          from: 'ql',
          id: act_id,
          name: course_name,
          type: 3,
          module_id: module_id
        }
      })
      window.open(href)
    },
    go(item) {
      window.open(item.url, '_blank')
    }
  },
  filters: {
    handleIndex: function (index) {
      const curIndex = index < 9 ? `0${index + 1}` : index + 1
      return curIndex
    },
    conuntFilter: function (val) {
      let str = ''
      str = val > 0 ? val >= 10000 ? `${(val / 10000).toFixed(1)}万` : val : '0'
      return str
    }
  }
}
</script>

<style lang="less" scoped>
.note-list-box {
  width: 272px;
  border-radius: 4px;
  background-color: #fff;

  .title-text {
    height: 68px;
    line-height: 68px;
    padding-left: 16px;
    color: rgba(51, 51, 51, 1);
    font-size: 20px;
    font-weight: bold;
    border-bottom: solid 1px #eeeeee;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .note-add {
      // width: 90px;
      height: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;

      :deep(span) {
        display: flex;
        align-items: center;

        .icon-note-add {
          display: inline-block;
          width: 16px;
          height: 16px;
          margin-right: 8px;
          background: url('~@/assets/img/edit-white.png');
        }
      }
    }
  }

  .list-body {
    padding: 12px 0 20px 0;

    .common-box {
      padding: 1px 16px;
      overflow-y: auto;
      max-height: 305px;

      .common-item {
        display: flex;
        align-items: baseline;
        margin-bottom: 20px;
        cursor: pointer;

        .common-item-right {
          .title {
            overflow: hidden;
            text-overflow: ellipsis;
            font-size: 14px;
            margin-bottom: 8px;
            word-break: break-all;
            white-space: nowrap;
            width: 246px;
            >img {
              width: 51px;
              margin: 0 12px 0 10px;
              object-fit: contain;
            }
          }

          .info {
            display: flex;
            color: #666666;

            span:first-child {
              margin-left: 18px;
            }

            i {
              display: inline-block;
              width: 14px;
              height: 14px;
              padding-left: 12px;
              margin-right: 5px;
            }

            .icon-play {
              background: url('../../assets/img/play.png') no-repeat center center / cover;
            }

            .icon-fav {
              background: url('../../assets/img/fav.png') no-repeat center center / cover;
            }

            .icon-time {
              background: url('../../assets/img/time.png') no-repeat center center / cover;
            }

            span {
              +span {
                margin-left: 10px;
              }

              display: flex;
              align-items: center;
            }
          }
        }

      }

      .common-item:last-child {
        margin-bottom: unset;
      }
    }
  }
}</style>
