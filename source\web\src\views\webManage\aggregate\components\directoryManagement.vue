<!-- //目录管理 -->
<template>
  <el-dialog :modal-append-to-body="false" :visible.sync="drawer" :before-close="handelrClose" width="800px" :close-on-click-modal="false">
    <template #title>
      <span class="el_title">新建目录</span>
      <el-button icon="el-icon-circle-plus-outline" size="small" type="primary" @click="addPolymerCatalogFn">新增目录</el-button>
    </template>

    <div class="face-activity">
      <el-table header-row-class-name="table-header-style" row-class-name="table-row-style" :data="tableData.records"
        style="width: 100%" height="328px" class="agg-table">
        <el-table-column prop="category_name" label="目录名称"></el-table-column>
        <el-table-column label="创建日期" width="170px">
          <template slot-scope="scope">
            <span class="table-course-title">{{ scope.row.created_at || '-'}}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人">
          <template slot-scope="scope">
            <span class="table-course-title">{{ scope.row.creator_name || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="order_no" label="排序"></el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div class="operat-btn-box">
              <el-link type="primary" :underline="false" @click="openSort(scope.row)">排序</el-link>
              <el-link type="primary" :underline="false" @click="openEdit(scope.row)">编辑</el-link>
              <el-link type="primary" :underline="false" @click="handleDeletePolymerCatalog(scope.row)">删除</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination v-show="tableData.total" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="current"
        :page-sizes="[5, 10, 20, 30, 50, 100]" :page-size="size"
        layout="total,  prev, pager, next, sizes, jumper" :total="tableData.total">
      </el-pagination>
    </div>

    <!-- 排序弹窗 -->
    <el-dialog custom-class="custom-dialog" :append-to-body="true" :title="form.title" :visible.sync="formDialog" :before-close="formClose" width="540px" :close-on-click-modal="false">
      <el-form :rules="rules" ref="form" :model="form" label-width="98px">
        <template v-if="['add', 'edit'].includes(operationType)">
          <el-form-item prop="category_name" label="目录名称">
            <el-input class="suffix-input" size="small" placeholder="请输入内容" show-word-limit v-model="form.category_name" @input="handleInput"></el-input>
            <span class="title-length">{{ titleLength }}/50</span>
          </el-form-item>
          <el-form-item label="目标学员">
            <el-radio v-model="staffStatus" label="1" @change="handlePersonnelScopeChange">全体员工</el-radio>
            <el-radio v-model="staffStatus" label="2" @change="handlePersonnelScopeChange">部分员工</el-radio>
            <AudienceSelector v-if="staffStatus === '2'" class="audience_selector" :showTab="['unitStaff', 'unit', 'group', 'import']"
              v-model="form.target_list" ref="selector" appCode="qlearning" :env="audienceEnv" />
          </el-form-item>
          <el-form-item label="是否展示标题">
            <el-radio v-model="form.show_name" :label="true">显示目录标题</el-radio>
            <el-radio v-model="form.show_name" :label="false">不显示目录标题</el-radio>
          </el-form-item>
        </template>

        <el-form-item v-show="operationType === 'sort'" label="排序">
          <el-input-number v-model="form.order_no" controls-position="right" :min="1"></el-input-number>
        </el-form-item>
      </el-form>
      <div class="drawer__footer">
        <el-button size="small" @click="formClose">取 消</el-button>
        <el-button size="small" type="primary" @click="handlePolymerCatalogChange">确 定</el-button>
      </div>
    </el-dialog>
  </el-dialog>

</template>

<script>
import pager from '@/mixins/pager'
import { AudienceSelector } from '@tencent/sdc-audience'
import {
  getPolymerCatalogList,
  addPolymerCatalog,
  editPolymerCatalog,
  delPolymerCatalog
} from '@/config/mooc.api.conf.js'
import { handlerDateFormat } from '@/utils/tools.js'
// 原始的表单数据
const formOrigin = {
  category_name: '',
  target_list: '',
  show_name: true,
  order_no: 1,
  title: '新增目录'
}

export default {
  mixins: [pager],
  props: {
    dialogFace: {
      type: Boolean,
      defatut: false
    },
    // 关联的数据
    selectTableData: {
      type: Array,
      defatut: () => []
    }
  },
  components: {
    AudienceSelector
  },
  data() {
    return {
      audienceEnv: process.env.NODE_ENV,
      titleLength: 0,
      form: formOrigin,
      formDialog: false,

      tableData: {
        total: 0,
        records: []
      },
      subDisabled: true,
      rules: {
        category_name: [
          { required: true, message: '请输入目录名称', trigger: 'blur' }
        ]
      },
      operationType: 'add', // sort, edit, add
      staffStatus: '1'
    }
  },
  computed: {
    drawer: {
      set(val) {
        this.$emit('update:dialogFace', val)
      },
      get() {
        return this.dialogFace
      }
    },
    aggStatus() {
      return (val) => {
        // regist_count 已报名人数 max_student_count 可报名人数
        const { regist_count, max_student_count, start_time } = val
        if (new Date(start_time) < new Date()) {
          return '已截止报名'
        } else if (regist_count >= max_student_count) {
          return '等待列表'
        } else {
          return '可报名'
        }
      }
    },
    polymerId() {
      return this.$route.query.polymer_id
    },
    userPublicInfo() {
      return JSON.parse(sessionStorage.getItem('login_user_dep')) || {}
    }
  },
  created() {
    this.polymerId && this.getPolymerCatalogListFn()
    if (!this.polymerId) {
      this.tableData.records.push({ 
        id: 1,
        category_id: '',
        category_name: '默认目录',
        target_list: '',
        show_name: true,
        order_no: 1,
        created_at: handlerDateFormat(new Date(), '-'),
        creator_name: this.userPublicInfo.emp_name_en
      })
      this.tableData.total = 1
    }
  },
  methods: {
    // 检查目录名称是否重复
    checkCategoryNameDuplicate(categoryName, currentId = null) {
      const check = this.tableData.records.some(item => item.category_name === categoryName && item.id !== currentId)
      if (check) {
        this.$message.error('目录名称已存在，请重新输入')
      }
      return check
    },
    // 目录列表
    getPolymerCatalogListFn() {
      const params = {
        polymer_id: this.polymerId,
        current: this.current,
        size: this.size
      }
      getPolymerCatalogList(params).then((res) => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    // 新增目录
    addPolymerCatalogFn() {
      this.operationType = 'add'
      this.formDialog = true
      this.form.title = '新增目录'
    },
    // 编辑目录
    openEdit(val) {
      this.operationType = 'edit'
      const { category_id, category_name, target_list, show_name, order_no, id, creator_name, created_at } = val
      this.staffStatus = target_list ? '2' : '1'
      this.form = {
        id,
        category_id,
        category_name,
        target_list,
        show_name,
        order_no,
        created_at,
        title: '编辑目录'
      }
      creator_name && this.$set(this.form, 'creator_name', creator_name)
      this.formDialog = true
    },
    // 调整排序
    openSort(val) {
      this.operationType = 'sort'
      const { category_id, category_name, target_list, show_name, order_no, id, creator_name } = val
      this.form = {
        id,
        category_id,
        category_name,
        target_list,
        show_name,
        order_no,
        title: '调整排序'
      }
      creator_name && this.$set(this.form, 'creator_name', creator_name)
      this.formDialog = true
    },
    handleInput(e) {
      const input = e
      const split = e.split('')
      // 计算已输入的长度
      const map = split.map((s, i) =>
        input.charCodeAt(i) >= 0 && input.charCodeAt(i) <= 128 ? 0.5 : 1
      )
      // 这里设置想要限制的长度
      const maxLength = 50
      let n = 0
      const charLength =
        map.length > 0 &&
        map.reduce((accumulator, currentValue, index) => {
          if (accumulator === maxLength || accumulator === maxLength - 1) {
            n = index
          }
          return accumulator + currentValue
        })
      this.titleLength = charLength || 0
      if (charLength > maxLength) {
        this.form.category_name = split.slice(0, n).join('')
        this.titleLength = 50
      }
    },
    handelrClose() {
      this.current = 1
      this.$emit('updateCatalogList', this.tableData.records)
      this.$emit('update:dialogFace', false)
    },
    formClose() {
      this.form = { ...formOrigin }
      this.staffStatus = '1'
      this.$refs.form.resetFields()
      this.formDialog = false
    },
    // openDetail(val) {
    //   let linkUrl = process.env.NODE_ENV === 'production' ? 'https://portal.learn.woa.com/training' : 'https://test-portal-learn.woa.com/training'
    //   // 活动
    //   if (val.act_type === 4) {
    //     linkUrl = linkUrl + `/activity/detail?activity_id=${val.class_id}`
    //   } else {
    //     linkUrl = linkUrl + `/face?course_id=${val.course_id}`
    //   }
    //   window.open(linkUrl)
    // },
    handlePolymerCatalogChange() {
      this.$refs.form.validate(valid => {
        if (valid) {
          switch (this.operationType) {
            case 'add':
              this.handleAddPolymerCatalog()
              break
            case 'edit':
              this.handleEditPolymerCatalog()
              break
            case 'sort':
              this.handleEditPolymerCatalog()
              break
            default:
              console.warn(`未知的操作类型: ${this.operationType}`)
          }          
        }
      })
    },
    // 生成唯一ID
    generateUniqueId() {
      return Date.now().toString(36) + Math.random().toString(36).substr(2)
    },
    // 新建目录
    async handleAddPolymerCatalog() {
      const { category_name } = this.form
      
      // 检查名称是否重复
      if (this.checkCategoryNameDuplicate(category_name)) return

      const lastRecord = this.tableData.records[this.tableData.records.length - 1]
      const order_no = lastRecord ? lastRecord.order_no + 1 : 1
      const { target_list, show_name } = this.form

      const params = {
        polymer_id: this.polymerId,
        category_name,
        target_list,
        show_name,
        order_no
      }

      try {
        if (!this.polymerId) {
          await this.handleAddLocalCatalog(params)
        } else {
          await addPolymerCatalog(params)
          this.$message.success('新增成功')
          this.formClose()
          await this.getPolymerCatalogListFn()
        }
      } catch (error) {
        console.error('新增目录失败:', error)
      }
    },
    // 编辑目录
    async handleEditPolymerCatalog() {
      const { target_list, category_id, category_name, show_name, order_no, id, creator_name, created_at } = this.form
      
      // 检查名称是否重复（排序操作不需要检查）
      if (this.operationType !== 'sort' && this.checkCategoryNameDuplicate(category_name, id)) return

      const params = {
        polymer_id: this.polymerId,
        category_id,
        category_name,
        target_list,
        show_name,
        order_no
      }

      try {
        if (!this.polymerId) {
          params.id = id
          params.creator_name = creator_name
          params.created_at = created_at
          if (this.operationType === 'sort') {
            await this.handleSortLocalCatalog(params)
          } else {
            await this.handleEditLocalCatalog(params)
          }
        } else {
          params.polymer_id = this.polymerId
          params.category_id = category_id
          await editPolymerCatalog(params)
          this.$message.success('编辑成功')
          this.formClose()
          await this.getPolymerCatalogListFn()
        }
      } catch (error) {
        console.error('编辑目录失败:', error)
      }
    },
    // 删除目录
    async handleDeletePolymerCatalog(row) {
      try {
        const confirmRes = await this.$confirm('确定删除该目录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        if (confirmRes === 'confirm') {
          if (!this.polymerId) {
            await this.handleDeleteLocalCatalog(row.id)
          } else {
            const params = {
              polymer_id: this.polymerId,
              category_id: row.category_id
            }
            await delPolymerCatalog(params)
            this.$message.success('删除成功')
            await this.getPolymerCatalogListFn()
          }
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除目录失败:', error)
        }
      }
    },
    // 添加本地目录数据
    handleAddLocalCatalog(params) {
      params.created_at = handlerDateFormat(new Date(), '-')
      const newParams = { ...params }
      newParams.creator_name = this.userPublicInfo.emp_name_en
      newParams.id = this.generateUniqueId()
      this.tableData.records.push(newParams)
      this.$message.success('新增成功')
      this.formClose()
      return Promise.resolve()
    },
    // 编辑本地目录数据
    handleEditLocalCatalog(params) {
      const index = this.tableData.records.findIndex(item => item.id === params.id)
      if (index === -1) {
        return Promise.reject(new Error('未找到要编辑的目录'))
      }
      const newData = { ...params, old_category_name: this.tableData.records[index].category_name }
      this.tableData.records.splice(index, 1, newData)
      this.$message.success('编辑成功')
      this.formClose()
      return Promise.resolve()
    },
    // 删除本地目录数据
    handleDeleteLocalCatalog(id) {
      const index = this.tableData.records.findIndex(item => item.id === id)
      if (index === -1) {
        return Promise.reject(new Error('未找到要删除的目录'))
      }
      this.tableData.records.splice(index, 1)
      this.$message.success('删除成功')
      return Promise.resolve()
    },
    /**
     * 处理目录本地排序
     * @param {Object} params 包含id和新序号的参数对象
     * @returns {Promise} 处理结果的Promise
     */
    handleSortLocalCatalog(params) {
      const { id, order_no } = params
      const records = this.tableData.records

      const targetIndex = records.findIndex(item => item.id === id)
      if (targetIndex === -1) {
        return Promise.reject(new Error('未找到要排序的目录'))
      }

      const currentItem = records[targetIndex]
      if (currentItem.order_no === order_no) {
        this.formClose()
        return Promise.resolve()
      }

      const isOrderNoExist = records.some(item => item.id !== id && item.order_no === order_no)
      if (isOrderNoExist) {
        this.$message.error('序号已存在，请重新输入')
        return Promise.reject(new Error('序号已存在'))
      }

      records.splice(targetIndex, 1, { ...currentItem, order_no })
      this.$message.success('编辑成功')
      this.formClose()
      return Promise.resolve()
    },
    handlePersonnelScopeChange() {
      this.form.target_list = ''
    }
  }
}
</script>

<style lang='less' scoped>
.operat-btn-box {
  .el-link+.el-link {
    margin-left: 10px;
  }
}

.el-input-number {
  width: 100%;

  /deep/ .el-input__inner {
    text-align: left;
  }
}

.title-length {
  position: absolute;
  right: 4px;
  color: #909399;
  font-size: 12px;
}

button {
  font-size: 14px;
}

.el_title {
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  margin-right: 24px;
}

.face-activity {
  padding: 0 20px;

  .agg-table {
    border-radius: 4px;
    margin-top: 15px;
    opacity: 1;
    border-top: 1px solid #eeeeeeff;
    border-left: 1px solid #eeeeeeff;
    border-right: 1px solid #eeeeeeff;
  }
}

.drawer__footer {
  display: flex;
  //   justify-content: center;
  position: sticky;
  bottom: 0;
  background-color: #fff;
  justify-content: flex-end;
}

:deep(.custom-dialog.el-dialog) {
  .el-dialog__header {
    padding: 32px 32px 0px;
    border-bottom-width: 0;
  }
  .el-dialog__body {
    padding: 24px 32px 24px 18px;
  }
  .el-dialog__headerbtn {
    top: 32px;
    right: 32px;
  }
}
:deep(.el-radio) {
  .el-radio__input.is-checked+.el-radio__label,
  .el-radio__label {
    color: #000000e6;
  }
}
:deep(.suffix-input .suffix-title) {
  margin-right: 50px;
}
:deep(.sdc-selector) {
  display: inline-block;
}
</style>
