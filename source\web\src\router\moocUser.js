const moocUser = [
  {
    path: '/mooc',
    name: 'trainProject',
    component: () => import('views/user/trainProject/index.vue'),
    meta: {
      title: '培养项目'
    },
    children: [
      {
        path: 'home',
        name: 'home',
        component: () => import('views/user/trainProject/home/<USER>'),
        meta: {
          title: '培养项目'
        }
      },
      {
        path: 'projectDetail',
        name: 'projectDetail',
        component: () => import('views/user/trainProject/projectDetail/index.vue'),
        meta: {
          title: '培养项目详情'
        }
      },
      {
        path: 'taskDetail',
        name: 'taskDetail',
        component: () => import('views/user/trainProject/taskDetail/index.vue'),
        meta: {
          title: '任务详情'
        }
      },
      {
        path: 'user/work-mark',
        name: 'work-mark',
        component: () => import('views/user/workMark/index.vue'),
        meta: {
          title: '批阅作业',
          breadcrumb: '批阅作业'
        }
      }
    ]
  },
  {
    path: '/label-subs',
    name: 'labelSubs',
    component: () => import('views/user/labelSubs/head.vue'),
    meta: {
      title: '标签订阅'
    },
    children: [
      {
        path: '/',
        name: 'labelSubsContent',
        component: () => import('@/views/user/labelSubs/index.vue'),
        meta: {
          title: '标签订阅内容'
        }
      }
    ]
  },
  // 作业
  {
    path: '/work/detail',
    name: 'workDetail',
    component: () => import('@/views/user/work/index.vue'),
    meta: {
      title: '作业'
    }
  },
  // 作业预览
  {
    path: '/work/preview',
    name: 'preview',
    component: () => import('@/views/user/work/preview.vue'),
    meta: {
      title: '作业'
    }
  },
  // 第三方任务
  {
    path: '/third-party',
    name: 'thirdParty',
    component: () => import('@/views/user/thirdParty/index.vue'),
    meta: {
      title: '第三方任务'
    }
  },
  // 课程素材
  {
    path: '/material/play',
    name: 'materialPlay',
    component: () => import('@/views/user/material/index.vue'),
    meta: {
      title: '课程素材'
    }
  }
]
export default moocUser
