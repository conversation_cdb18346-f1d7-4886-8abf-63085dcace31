<template>
  <div class="note-main">
    <swiper :options="swiperOption" ref="noteSwiperRef" class="note-content" v-if="noteList?.length">
      <swiper-slide v-for="(item, index) in noteList" :key="index" class="guess-main">
        <div class="course-item">
          <div class="course-time"><span>{{item.graphic_number}}{{$langue('Mooc_ProjectDetail_TaskList_Words', { defaultText: '字' })}}</span></div>
          <el-image lazy fit="fill" :src="item.cover_image_id" class="course-cover">
            <div class="image-slot" slot="placeholder">
              <i class="el-icon-loading"></i>
            </div>
            <div class="error-cover" slot="error">
              <img :src="formatModuleMap(8)" alt="" />
            </div>
          </el-image>
          <div class="card-conent">
            <span class="tag-title overflow-l2">
              <span class="tag"><span>文章</span></span>
                <span class="curse-title">
                  {{ item.graphic_name }}
                </span>
            </span>
            <div class="course-data">
              <span class="study-num">{{ transformUnitW(item.view_count || 0) }}{{$langue('NetCourse_PeopleWatched', { defaultText: '人观看' })}}</span>
              <!-- <span class="line">|</span>
              <span>{{ item.avg_score || 0 }}评分</span> -->
            </div>
          </div>
        </div>
      </swiper-slide>
      <div class="swiper-button-prev" slot="button-prev"  :dt-areaid="dtCommon('areaid', '上一节')" @click="handleRight('上一节')">
        <span class="icon-left"></span>
      </div>
      <div class="swiper-button-next" slot="button-next" :dt-areaid="dtCommon('areaid', '下一节')" @click="handleRight('下一节')">
        <span class="icon-right"></span>
      </div>
    </swiper>
    <Empty v-else :emptyShow="emptyShow"/>
  </div>
</template>
<script>
import { getRelationGraphic } from 'config/api.conf'
import { transformUnitW, formatModuleMap } from 'utils/tools'
import Empty from '@/views/user/components/empty.vue'

export default {
  components: {
    Empty
  },
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    },
    dtPageType: {
      type: String,
      default: '网课详情页-新版'
    },
    act_type: {
      type: Number,
      default: 2
    }
  },
  data() {
    return {
      formatModuleMap,
      noteList: [],
      page_no: 1,
      total: 0,
      transformUnitW
    }
  },
  computed: {
    emptyShow() {
      return !this.noteList.length
    },
    course_id() {
      return this.courseData.course_id || this.$route.query.course_id
    },
    swiperOption() {
      return {
        loop: false,
        // autoplay: {
        //   delay: 3000,
        //   stopOnLastSlide: false,
        //   disableOnInteraction: false
        // },
        autoplay: false,
        direction: 'horizontal',
        slidesPerView: 'auto',
        freeMode: false,
        // 显示分页
        // pagination: {
        //   el: '.swiper-pagination',
        //   clickable: true // 允许分页点击跳转
        // },
        navigation: { // 设置点击箭头
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        on: {
          click: (e) => {
            const i = this.$refs.noteSwiperRef.$swiper.clickedIndex
            if (i >= 0) {
              const v = this.noteList[i]
              this.toNote(v)
            }
          },
          slideChange: (e) => {
            if (this.noteList.length >= this.total) {
              return
            }
            this.getNoteList('add')
          }
        }
      }
    }
  },
  mounted() {
    this.getNoteList()
  },
  methods: {
    handleRight(val) {
      window.BeaconReport('at_click', {
        eid: this.dtCommon('eid', val),
        remark: this.dtCommon('remark', val)
      })
    },
    dtCommon(type, val) {
      if (type === 'remark') {
        return JSON.stringify({
          page: this.courseData.course_name,
          page_type: this.dtPageType, 
          container: '笔记',
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: val,
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'PC'
        })
      } else if (type === 'eid') {
        return `element_${this.course_id}_${val}`
      } else {
        return `area_${this.course_id}_${val}`
      }
    },
    toNote(e) {
      if (!e.url) return
      window.BeaconReport('at_click', {
        eid: this.delListAread('eid', e),
        remark: this.delListAread('remark', e)
      })
      window.open(e.url)
    },
    titleOver(e, index) {
      const target = e.target
      this.$set(
        this.noteList[index],
        'isOverflow',
        target.scrollWidth > target.clientWidth
      )
    },
    getNoteList(val) {
      if (val === 'add') {
        this.page_no++
      }
      const params = {
        act_type: this.act_type,
        act_id: this.course_id,
        page_no: this.page_no,
        page_size: 10
      }
      getRelationGraphic(params).then((data) => {
        const list = data.records || []
        this.noteList = this.noteList.concat(list)
        this.total = data.total
        list.forEach((v) => {
          window.BeaconReport('at_show_area', {
            eid: this.delListAread('area', v),
            remark: this.delListAread('remark', v)
          })
        })
      })
    },
    delListAread(type, v) {
      if (type === 'remark') {
        return JSON.stringify({
          page: this.courseData.course_name,
          page_type: this.dtPageType, 
          container: `笔记`,
          click_type: 'data',
          content_type: '',
          content_id: v.graphic_id,
          content_name: v.graphic_name,
          act_type: '',
          page_id: '',
          container_id: '',
          terminal: 'PC'
        })
      } else if (type === 'eid') {
        return `element_${this.course_id}_${v.graphic_id}`
      } else {
        return `area_${this.course_id}_${v.graphic_id}`
      }
    }
  }
}
</script>
<style lang="less" scoped>
.note-main {
  // background-color: #fff;
  .note-content {
    padding: 16px 24px 24px;
    background: #fff;
    border-radius: 0 0 8px 8px;
    position: relative;
    // display: flex;
    // flex-direction: column;
    .guess-main {
      flex-shrink: unset;
      width: unset;
      .course-item {
        position: relative;
        width: 223px;
        position: relative;
        float: left;
        cursor: pointer;
        border: 1px solid #EEEEEE;
        border-radius: 8px;
        margin-right: 20px;
        padding-bottom: 18px;
  
        .course-time {
          position: absolute;
          right: 10px;
          top: 116px;
          z-index: 2;
          font-size: 12px;
          color: #fff;
          line-height: 22px;
          text-align: center;
          padding: 0 6px;
          height: 22px;
          border-radius: 4px;
          opacity: 1;
          background: #00000066;
        }
  
        .course-cover {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 221px;
          height: 148px;
          text-align: center;
          color: #666;
          border-radius: 8px 8px 0 0;
          box-sizing: content-box;
  
          .error-cover img {
            width: 221px;
            height: 148px;
          }
        }
  
        .card-conent {
          margin-top: 18px;
          padding: 0 12px;
  
          .tag-title {
            line-height: 24px;
            margin-bottom: 12px;
  
            .tag {
              height: 18px;
              background: #F5F5F7;
              color: #777777;
              border-radius: 2px;
              padding: 0 4px;
              font-size: 12px;
            }
  
            .curse-title {
              font-family: @PingFangSC;
              font-weight: bold;
              color: #000000e6;
              word-break: break-all;
            }
          }
  
          .course-data {
            display: flex;
            align-items: center;
            height: 20px;
            line-height: 20px;
            color: #777777;
            font-size: 12px;
  
            .line {
              color: #D9D9D9;
              margin-left: 8px;
              margin-right: 8px;
            }
          }
        }
      }
  
      // .course-item-last {
      //   margin-right: 0;
      // }
    }
    ::v-deep .swiper-button-prev,
    .swiper-button-next {
      &:after {
        display: none;
      }
  
      &.swiper-button-disabled {
        display: none;
      }
      top: 153px;
      width: 32px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 50%;
      background: var(--Color, #FFF);
      box-shadow: 0 8px 10px -5px #00000014,
      0 16px 24px 2px #0000000a,
      0 6px 30px 5px #0000000d;
  
      .icon-left,
      .icon-right {
        display: inline-block;
        width: 20px;
        height: 20px;
        font-size: 20px;
      }
  
      .icon-left {
        background: url('~@/assets/mooc-img/chevron-left.png') no-repeat center / cover;
      }
  
      .icon-right {
        background: url('~@/assets/mooc-img/chevron-right.png') no-repeat center / cover;
      }
    }
    .swiper-button-prev:hover, .swiper-button-next:hover {
      background-color: #0052D9;
      box-shadow: 0 4px 18.67px 2.67px #0000000d, 0 10.67px 13.33px 1.33px #0000000f, 0 6.67px 6.67px -4px #0000001a;
      .icon-left {
        background: url('~@/assets/img/white-left.png') no-repeat center / cover;
      }

      .icon-right {
        background: url('~@/assets/img/white-right.png') no-repeat center / cover;
      }
    }
  }
}
</style>
