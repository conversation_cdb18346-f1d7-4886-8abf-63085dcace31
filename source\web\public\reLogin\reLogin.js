window.$trainingTaihuRelogin = (function() {
  let timer = null
  // 重新登录
  let checkIsloginTime = 20
  let enabledHeartbeat = true
  let requestHeader = {}
  const _devUrl = [
    'test-portal-learn.woa.com',
    'test.portal.learn.oa.com',
    'test-learn.woa.com',
    'test.woa.com',
    'local.oa.com'
  ]
  let checkRequestUrlList = [
    'portal.learn.woa.com',
    'learn.woa.com',
    'test-portal-learn.woa.com',
    'test-learn.woa.com',
    'm-learn.woa.com'
  ]
  let isProduction = !_devUrl.includes(location.hostname)
  let AUTH_URL = null
  let iframeDomSrc = isProduction ? '//portal.learn.woa.com/training/reLogin/reLogin.html' : '//test-portal-learn.woa.com/training/reLogin/reLogin.html'
  let handlerCheckLogin = false
  let checkLoginPageConfig = null
  let requestConfigUrl = '//contentcenter-1252291750.cos.ap-guangzhou.myqcloud.com/public/academy/config/relogin/relogin_config.json'
  let _isMobile = isMobile()
  
  // 公共样式
  const commonStyles = `
    .tof-modal-root {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 1000;
    }
    .tof-modal-mask {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.45);
      z-index: 1000;
    }
    .tof-modal-wrap {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      overflow: auto;
      outline: 0;
      -webkit-overflow-scrolling: touch;
      z-index: 1000;
    }
    .tof-modal-content {
      position: relative;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 3px 6px -4px rgba(0,0,0,.12), 0 6px 16px 0 rgba(0,0,0,.08), 0 9px 28px 8px rgba(0,0,0,.05);
    }
    .tof-modal-body {
      padding: 0;
      font-size: 14px;
      line-height: 1.5715;
      word-wrap: break-word;
    }
    .tof-modal-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 405px;
    }
    .tof-spin {
      font-size: 32px;
      color: #1890ff;
      display: flex;
      align-items: center;
      justify-content: center;
      animation: antRotate 1s infinite linear;
    }
    @keyframes antRotate {
      to {
        transform: rotate(360deg);
      }
    }
  `
  createCheckRequestUrl()
  
  function createCheckRequestUrl() {
    let isCheckRequestUrl = checkRequestUrlList.includes(location.hostname)
    if (isCheckRequestUrl) {
      AUTH_URL = `//${location.hostname}/training/api/health/check`
    } else {
      AUTH_URL = isProduction ? '//portal.learn.woa.com/training/api/health/check' : '//test-portal-learn.woa.com/training/api/health/check'
    }
  }

  function reLoginInit() {
    const isWoa = window.location.hostname.endsWith('woa.com')
    
    checkIsloginTime = checkIsloginTime * 1000
    if (_isMobile) {
      injectMobileStyles()
    } else {
      injectPcStyles()
    }

    if (!isWoa) {} else {}
    startHeartbeat()
    
    setupEventListeners()

    window.addEventListener('message', (e) => {
      const { data } = e
      if (data.from === 'TaihuReloginPage' && data.events === 'showLoginPage') {
        showLoginDialog()
      } else if (data.from === 'TaihuReloginPage' && data.events === 'closeLoginPage') {
        destoryIframe()
      }
    })
  }

  // 开启心跳
  function startHeartbeat() {
    if (!enabledHeartbeat) return

    timer = setInterval(() => {
      handlerRequestTaihu().catch(err => {
        console.log('心跳检测被拒绝，原因：', err)
      })
    }, checkIsloginTime)
  }

  // 检查登录状态
  function handlerRequestTaihu() {
    return new Promise((resolve, reject) => {    
      if (!checkCurrentPage()) {
        reject()
        return
      }
      fetch(`${AUTH_URL}`, { 
        method: 'GET',
        headers: {
          ...requestHeader,
          'X-Requested-With': 'XMLHttpRequest'
        },
        credentials: 'include'
      }).then(res => {
        let isLogin = false
        if (!res.ok && res.status === 401) {
          showLoginDialog()
          isLogin = false
        }
        if (res.ok && res.status === 200) {
          destoryIframe()
          isLogin = true
        }
        resolve({ isLogin })
      }).catch(err => {
        console.log(err, 'catch-relogin')
        reject(err)
      })
    })
  }

  function showLoginDialog() {
    let dialog = document.getElementById('tof-login-dialog-wrapper')
    if (dialog) return
    const dialogHtml = `
      <div class="tof-modal-root">
          <div class="tof-modal-mask"></div>
          <div class="tof-modal-wrap">
              <div class="tof-modal">
                  <div class="tof-modal-content">
                      <div class="tof-modal-body">
                          <div class="tof-modal-loading" id="tof-loading">
                              <span class="tof-spin">
                                ${getLoadingContent()}
                              </span>
                          </div>
                          <iframe style="display: none" id="loginIframe" class="tof-login-iframe" src="${iframeDomSrc}"></iframe>
                      </div>
                  </div>
              </div>
          </div>
      </div>
    `

    const dialogElement = document.createElement('div')
    const id = 'tof-login-dialog-wrapper'

    dialogElement.innerHTML = dialogHtml
    dialogElement.id = id
    document.body.appendChild(dialogElement)
    watchLoginFrameLoad()
  }

  function handlerReLoginInit(options) {
    const { authUrl = '', reloginPageUrl, checkloginTime, handlerEnabledHeartbeat = true } = options
    if (authUrl) {
      AUTH_URL = authUrl
    }
    if (reloginPageUrl) {
      iframeDomSrc = reloginPageUrl
    }
    if (checkIsloginTime) {
      checkIsloginTime = checkloginTime * 1
    }
    enabledHeartbeat = handlerEnabledHeartbeat
    handlerCheckLogin = true
    requestEnablePage().then(() => {
      reLoginInit()
    }).catch(err => {
      console.log('请求重新登录页面列表失败：', err)
    })
  }

  function getLoadingContent() {
    return `<svg viewBox="0 0 1024 1024" focusable="false" data-icon="loading" width="1em" height="1em" fill="currentColor" aria-hidden="true"><path d="M988 548c-19.9 0-36-16.1-36-36 0-59.4-11.6-117-34.6-171.3a440.45 440.45 0 00-94.3-139.9 437.71 437.71 0 00-139.9-94.3C629 83.6 571.4 72 512 72c-19.9 0-36-16.1-36-36s16.1-36 36-36c69.1 0 136.2 13.5 199.3 40.3C772.3 66 827 103 874 150c47 47 83.9 101.8 109.7 162.7 26.7 63.1 40.2 130.2 40.2 199.3.1 19.9-16 36-35.9 36z"></path></svg>`
  }

  function injectPcStyles() {
    const styleElement = document.createElement('style')

    styleElement.textContent = `
      ${commonStyles}
      .tof-modal {
        position: relative;
        top: 50%;
        transform: translateY(-50%);
        margin: 0 auto;
        width: 720px;
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 3px 6px -4px rgba(0,0,0,.12), 0 6px 16px 0 rgba(0,0,0,.08), 0 9px 28px 8px rgba(0,0,0,.05);
        padding-bottom: 0;
      }
      .tof-login-iframe {
        width: 100%;
        height: 405px;
        border: none;
      }
    `
    document.head.appendChild(styleElement)
  }

  function injectMobileStyles() {
    const styleElement = document.createElement('style')
    styleElement.textContent = `
      ${commonStyles}
      .tof-modal {
        position: relative;
        top: 50%;
        transform: translateY(-50%);
        margin: 0 auto;
        width: 93%;
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 3px 6px -4px rgba(0,0,0,.12), 0 6px 16px 0 rgba(0,0,0,.08), 0 9px 28px 8px rgba(0,0,0,.05);
        padding-bottom: 0;
      }
      .tof-login-iframe {
        width: 100%;
        height: 535px;
        border: none;
        overflow: hidden;
      }
    `
    document.head.appendChild(styleElement)
  }

  function setupEventListeners() {
    document.addEventListener('visibilitychange', function() {
      handleVisibilityChange()
    })
  }

  function handleVisibilityChange() {
    try {
      if (document.visibilityState !== 'hidden' && checkCurrentPage()) {
        handlerRequestTaihu().then(res => {
          if (res.isLogin) {
            destoryIframe()
          }
        })
      }
    } catch (error) {
    }
  }

  // 监听登录弹窗加载
  function watchLoginFrameLoad() {
    const iframe = document.getElementById('loginIframe')

    if (iframe) {
      iframe.addEventListener('load', () => {
        const loadingElement = document.getElementById('tof-loading')
        loadingElement && loadingElement.parentNode && loadingElement.parentNode.removeChild(loadingElement)

        iframe.style.display = 'block'

      })
    }
  }

  function destoryIframe() {
    try {
      const dialogRoot = document.querySelector('.tof-modal-root')
      if (dialogRoot) {
        document.body.removeChild(dialogRoot.parentNode)
      }
    } catch (error) {
      console.error('Error destroying iframe:', error)
    }
  }

  function stopHeartbeat() {
    clearInterval(timer)
  }

  function checkCurrentPage() {
    // 获取当前页面的完整 URL 和主域名
    let fullUrl = ''
    if (window.location.hostname && window.location.pathname) {
      fullUrl = window.location.hostname + window.location.pathname
    }
    const hostName = window.location.hostname || '' // 不包含协议
  
    // 如果配置表不存在或者当前 URL 或主域名为空，则默认返回 true
    if (!checkLoginPageConfig || !fullUrl || !hostName) return true
  
    // 解构配置表
    const { includes = {}, excludes = {} } = checkLoginPageConfig
  
    // 获取 includes 和 excludes 中的 hosts 和 urls 列表
    const excludeHosts = excludes.hosts || []
    const excludeUrls = excludes.urls || []
    const includeHosts = includes.hosts || []
    const includeUrls = includes.urls || []
    // 检查是否在排除的主域名列表中
    if (excludeHosts.includes(hostName)) {
      return false
    }
    // 检查是否在排除的完整 URL 列表中
    if (excludeUrls.includes(fullUrl)) {
      return false
    }
  
    // 检查是否在启用的主域名列表中
    if (includeHosts.includes(hostName)) {
      return true
    }
    // 检查是否在启用的完整 URL 列表中
    if (includeUrls.includes(fullUrl)) {
      return true
    }
  
    // 3. 如果既不在 includes 也不在 excludes 中，返回 false（默认排除）
    return false
  }

  function requestEnablePage() {
    let random = Math.random()
    return new Promise((resolve, reject) => {
      fetch(requestConfigUrl + `?time=${random}`).then(response => {
        if (!response.ok) {
          reject()
        }
        response.json().then(data => {
          checkLoginPageConfig = data
          resolve()
        })
      })
    })
  }

  function isMobile() {
    var ua = navigator.userAgent.toLowerCase()
    return /iphone|ipad|ipod|android|blackberry|mini|windows\sce|phone|mobile/.test(ua) || /micromessenger/.test(ua)
  }

  setTimeout(() => {
    if (handlerCheckLogin) return
    requestEnablePage().then(() => {
      reLoginInit()
    }).catch(err => {
      console.log('请求重新登录页面列表失败：', err)
    })
  }, 300)

  return {
    stopHeartbeat,
    handlerRequestTaihu,
    handlerReLoginInit,
    showLoginDialog
  }
})()
