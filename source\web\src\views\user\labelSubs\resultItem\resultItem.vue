<template>
    <div @click="toPage(info)">
        <activeItemVue :info="info" :curModuleId="curModuleId" :subsType="subsType" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :contentType="contentType" v-if="info.module_id === 4"/>
        <liveItem :info="info" :curModuleId="curModuleId" :subsType="subsType" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :contentType="contentType" v-if="info.module_id === 3"/>
        <faceCourseItem :info="info" :curModuleId="curModuleId" :subsType="subsType" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :contentType="contentType" v-if="info.module_id === 2"/>
        <netCourseItem :info="info" :curModuleId="curModuleId" :subsType="subsType" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :contentType="contentType" v-if="info.module_id === 1"/>
        <articleItemVue :info="info" :curModuleId="curModuleId" :subsType="subsType" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :contentType="contentType" v-if="info.module_id === 8"/>
        <trainingItemVue :info="info" :curModuleId="curModuleId" :subsType="subsType" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :contentType="contentType" v-if="info.module_id === 10"/>
        <classScheduleItemVue :info="info" :curModuleId="curModuleId" :subsType="subsType" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :contentType="contentType" v-if="info.module_id === 15"/>
        <hangjiaItemVue :info="info" :curModuleId="curModuleId" :subsType="subsType" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :contentType="contentType" v-if="info.module_id === 6"/>
        <kaceItemVue :info="info" :curModuleId="curModuleId" :subsType="subsType" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :contentType="contentType" v-if="info.module_id === 7"/>
        <docItem :info="info" :curModuleId="curModuleId" :subsType="subsType" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :contentType="contentType" v-if="info.module_id === 16"/>
        <kbItem :info="info" :curModuleId="curModuleId" :subsType="subsType" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :contentType="contentType" v-if="info.module_id === 20"/>
        <linkItem :info="info" :curModuleId="curModuleId" :subsType="subsType" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :contentType="contentType" v-if="info.module_id === 99"/>
    </div>
</template>
<script>
import activeItemVue from './activeItem.vue'
import articleItemVue from './articleItem.vue'
import classScheduleItemVue from './classScheduleItem.vue'
import docItem from './docItem.vue'
import faceCourseItem from './faceCourseItem.vue'
import hangjiaItemVue from './hangjiaItem.vue'
import kbItem from './kbItem.vue'
import kaceItemVue from './kaceItem.vue'
import liveItem from './liveItem.vue'
import netCourseItem from './netCourseItem.vue'
import trainingItemVue from './trainingItem.vue'
import linkItem from './linkItem.vue'
export default {
  name: 'resultItem',
  props: ['info', 'curModuleId', 'curLabelId', 'sortSubsIds', 'subsType', 'contentType', 'isLabelGatherPage'],
  components: {
    activeItemVue,
    articleItemVue,
    classScheduleItemVue,
    docItem,
    faceCourseItem,
    hangjiaItemVue,
    kbItem,
    kaceItemVue,
    liveItem,
    netCourseItem,
    trainingItemVue,
    linkItem
  },
  data() {
    return {
      type: null,
      page_id: ''
    }
  },
  watch: {
    'info': {
      handler(val) {
        this.info.view_count = this.formatNumber(this.info.view_count)
      },
      immediate: true
    }
  },
  methods: {
    // 内容跳转
    toPage(item) {
      let name = this.type === 2 ? `zqdy` : 'bqdy'
      let from_name = this.isLabelGatherPage ? 'LabelGather' : 'LabelSubscribe'
      let url = item.href // .indexOf('?') > -1 ? `${item.href}&from=${from_name}&source=${name}` : `${item.href}?from=${from_name}&source=${name}`
      if (item.href.indexOf('?') > -1) {
        let params = url.split('?')[1]
        let arrParams = this.getUrlParams(params)
        console.log(arrParams)
        if (!arrParams.includes('from')) {
          url += `&from=${from_name}`
        }
        if (!arrParams.includes('source')) {
          url += `&source=${name}`
        }
        if (this.type === 2 && !arrParams.includes('page_id')) {
          url += `&page_id=${this.page_id || this.curLabelId}`
        }
      } else {
        url += `?from=${from_name}&source=${name}`
        if (this.type === 2) {
          url += `&page_id=${this.page_id || this.curLabelId}`
        }
      }
      console.log(url)
      window.open(url)
    },
    getUrlParams(strUrl) {
      if (!strUrl) return []
      let urlArr = strUrl.split('&')
      let arr = []
      urlArr.forEach(item => {
        if (item.indexOf('=') !== -1) {
          let name = item.split('=')[0]
          arr.push(name)
        }
      })
      return arr
    },
    formatNumber(value) {
      value = Number(value)
      if (!value) {
        return 0
      } else if (value > 0 && value < 10000) {
        return value.toString()
      } else {
        return (value / 10000).toFixed(1) + 'w'
      }
    }
  }
}
</script>
