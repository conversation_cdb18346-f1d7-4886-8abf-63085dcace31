// moudleid枚举
const moduleTypes = [
  {
    module_name: '网络课',
    module_id: 1
  },
  {
    module_name: '面授课',
    module_id: 2
  },
  {
    module_name: '直播',
    module_id: 3
  },
  {
    module_name: '活动',
    module_id: 4
  },
  {
    module_name: '码客',
    module_id: 5
  },
  {
    module_name: '行家',
    module_id: 6
  },
  {
    module_name: '案例',
    module_id: 7
  },
  {
    module_name: '文章',
    module_id: 8
  },
  {
    module_name: '培养项目',
    module_id: 10
  },
  {
    module_name: '考试',
    module_id: 11
  },
  {
    module_name: '专区',
    module_id: 12
  },
  {
    module_name: '视频集锦',
    module_id: 13
  },
  {
    module_name: '话题',
    module_id: 14
  },
  {
    module_name: '课单',
    module_id: 15
  },
  {
    module_name: '文档',
    module_id: 16
  },
  {
    module_name: 'iwiki',
    module_id: 17
  },
  {
    module_name: 'km文章',
    module_id: 20
  },
  {
    module_name: '外链',
    module_id: 99
  },
  {
    module_name: '未知',
    module_id: -1000
  }
]

// actType枚举
const actTypes = [
  {
    act_type_name: '面授课',
    act_type: 1,
    module_id: 2
  },
  {
    act_type_name: '网络课',
    act_type: 2,
    module_id: 1,
    resource_type: 1
  },
  {
    act_type_name: '班级',
    act_type: 3
  },
  {
    act_type_name: '活动',
    act_type: 4,
    module_id: 4
  },
  {
    act_type_name: '直播',
    act_type: 5,
    module_id: 3
  },
  {
    act_type_name: '图文',
    act_type: 6,
    module_id: 9
  },
  {
    act_type_name: '系列课',
    act_type: 7
  },
  {
    act_type_name: '系列班',
    act_type: 8
  },
  {
    act_type_name: '论文',
    act_type: 9
  },
  {
    act_type_name: '文档',
    act_type: 10,
    module_id: 16,
    resource_type: 5
  },
  {
    act_type_name: '培养项目',
    act_type: 11,
    module_id: 10
  },
  {
    act_type_name: '内容中心',
    act_type: 12
  },
  {
    act_type_name: '专区',
    act_type: 13,
    module_id: 12
  },
  {
    act_type_name: '课单',
    act_type: 15,
    module_id: 15
  },
  {
    act_type_name: '案例',
    act_type: 16,
    module_id: 7
  },
  {
    act_type_name: '笔记',
    act_type: 17,
    module_id: 8
  },
  {
    act_type_name: '文章', // 新图文
    act_type: 18,
    module_id: 8
  },
  {
    act_type_name: '行家',
    act_type: 19,
    module_id: 6
  },
  {
    act_type_name: '考试',
    act_type: 20,
    module_id: 11,
    resource_type: 6
  },
  {
    act_type_name: '素材',
    act_type: 21
  },
  {
    act_type_name: '作业',
    act_type: 22
  },
  {
    act_type_name: '第三方任务',
    act_type: 23
  },
  {
    act_type_name: '压缩包',
    act_type: 24
  },
  {
    act_type_name: '帖子',
    act_type: 25
  },
  {
    act_type_name: 'km文章',
    act_type: 26,
    module_id: 20
  },
  {
    act_type_name: 'Spoc',
    act_type: 27
  },
  {
    act_type_name: 'MoocTask',
    act_type: 28
  },
  {
    act_type_name: 'SpocTask',
    act_type: 29
  },
  {
    act_type_name: 'Spoc培训及活动',
    act_type: 30
  },
  {
    act_type_name: 'push中台',
    act_type: 31
  },
  {
    act_type_name: '问卷',
    act_type: 32
  },
  {
    act_type_name: '测评',
    act_type: 33
  },
  {
    act_type_name: '模拟仓',
    act_type: 34
  },
  {
    act_type_name: '码客',
    act_type: 50,
    module_id: 5
  },
  {
    act_type_name: 'iwiki',
    act_type: 51,
    module_id: 17
  },
  {
    act_type_name: '外链',
    act_type: 99,
    module_id: 99,
    resource_type: 9
  },
  {
    act_type_name: 'knowledge',
    act_type: 101
  },
  {
    act_type_name: '外部引入课程',
    act_type: 102
  },
  {
    act_type_name: '岗前系统',
    act_type: 201
  },
  {
    act_type_name: 'qlearning',
    act_type: 999
  },
  {
    act_type_name: '未知',
    act_type: -1000,
    module_id: -1000
  }
]

const resourceTypes = [
  {
    resource_type: 1,
    resource_type_name: '视频'
  },
  {
    resource_type: 2,
    resource_type_name: '音频'
  },
  {
    resource_type: 3,
    resource_type_name: '图片'
  },
  {
    resource_type: 4,
    resource_type_name: '压缩文档'
  }, 
  {
    resource_type: 5,
    resource_type_name: '文档'
  },
  {
    resource_type: 6,
    resource_type_name: '考试'
  },  
  {
    resource_type: 7,
    resource_type_name: '练习'
  },
  {
    resource_type: 8,
    resource_type_name: '文章'
  },
  {
    resource_type: 9,
    resource_type_name: '外链'
  }
]

function getActTypeByModuleId(moduleId) {
  let obj = actTypes.find(item => item.module_id && item.module_id === moduleId)
  if (!obj) {
    return '-1000'
  } else {
    return obj.act_type
  }
}

function getActTypeNameByModuleId(moduleId) {
  let obj = actTypes.find(item => item.module_id && item.module_id === moduleId)
  if (!obj) {
    return '未知'
  } else {
    return obj.act_type_name
  }
}
export { moduleTypes, actTypes, resourceTypes, getActTypeByModuleId, getActTypeNameByModuleId }
