import {
  Popup,
  Switch,
  Collapse,
  CollapseItem,
  Tab,
  Tabs,
  Toast,
  Checkbox,
  CheckboxGroup,
  RadioGroup,
  Radio,
  CellGroup,
  Cell,
  List,
  PullRefresh,
  NavBar,
  Image,
  Button,
  Sticky,
  Icon,
  Slider,
  Search,
  Picker
} from 'vant'
import zh from 'vant/lib/locale/lang/zh-CN'
import en from 'vant/lib/locale/lang/en-US'
export default {
  components: [
    Popup,
    Switch,
    Collapse, 
    CollapseItem,
    Tab,
    Tabs,
    Toast,
    CheckboxGroup,
    Checkbox,
    RadioGroup,
    Radio,
    Cell,
    CellGroup,
    List,
    PullRefresh,
    NavBar,
    Image,
    Button, 
    Sticky,
    Icon,
    Slider,
    Search,
    Picker
  ],
  langs: {
    zh,
    en
  },
  install() {}
}
