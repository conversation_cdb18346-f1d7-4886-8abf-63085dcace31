<template>
  <div>
    <button id="openBtn" @click="openMember">打开</button>
    <button id="openBtn2">打开2</button>
    <button @click="openMember">自己调用暴露的显示方法</button>
    <div class="test1">
      <div id="chooseBox123"></div>
    </div>
    <div style="width: 550px">
      <div id="chooseBox1234"></div>
    </div>
  </div>
</template>

<script>
// let staffSelector = require('@/staffSelector.js')
export default {
  mounted () {
    // 3017123 
    // const data = [
    //   {
    //     'Code': '',
    //     'ID': 3898705,
    //     'Name': 'test1(中文名1)'
    //   },
    //   {
    //     'Code': '',
    //     'ID': 3898702,
    //     'Name': 'test2(中文名2)'
    //   }
    // ]  

    // staffSelector.initStaffSelector({ 
    //   containerId: 'chooseBox123',
    //   changeCallback: res => {
    //     console.log(res, 'jjjjj')
    //   }
    // })
    // staffSelector.initStaffSelector({
    //   containerId: 'chooseBox123',
    //   changeCallback: res => {
    //     console.log(res, 'jjjjj')
    //   }
    // })
    // window.staffSelector.initStaffSelector('chooseBox123')

    // let res = window.staffSelector.getStaffData()
    // console.log(res)
    // this.openMember()
    // memberSelector.initMemberIframe({
    //   env: 'test',
    //   targetIds: ''
    // })
    this.loadJS('https://qlportal.ihr.tencent-cloud.com/qlweb/memberSelector.js', () => {
      window.memberSelector.initMemberIframe({
        env: 'test',
        targetIds: ''
      })
    })
  },
  methods: {
    hanC (res) {
      console.log(res, '成功')
    },
    openMember() {
      window.memberSelector.showMemberIframe({
        successCallback: (res) => {
          console.log(res, '成功')
        },
        closeCallback: (domId) => {
          console.log(domId, '关闭了')
        }
      })
    },
    loadJS(url, callback) {
      var script = document.createElement('script')
      var fn = callback || function () { }
      script.type = 'text/javascript'
      // IE
      if (script.readyState) {
        script.onreadystatechange = function () {
          if (script.readyState === 'loaded' || script.readyState === 'complete') {
            script.onreadystatechange = null
            fn(url)
          }
        }
      } else {
        // 其他浏览器
        script.onload = function () {
          fn(url)
        }
      }
      script.src = url
      document.getElementsByTagName('head')[0].appendChild(script)
    }
  }
}
</script>

<style lang="less" scoped>
.test1 {
  width: 450px;
  height: 150px;
  overflow: hidden;
  position: absolute;
  top: 50%;
  left: 50%;
}
</style>
