<template>
  <div class="content">
    <div class="base-info-box">
      <div class="title-box"><img src="@/assets/mooc-img/title-icon.png" /><span class="bassinfo-class-title">基本信息</span>
      </div>
      <div class="base-info-content">
        <div class="item-box">
          <span class="label">项目名称：</span>
          <span class="value">{{ form.course_title }}</span>
        </div>
        <div class="item-box">
          <span class="label">运营标题：</span>
          <span class="value">{{ form.course_statement.operation_title || '-' }}</span>
        </div>
        <!-- <div class="item-box"><span class="label">项目分类：</span><span class="value">{{ form.course_category_name }}</span></div> -->
        <div class="item-box tag-box">
          <span class="label">项目标签：</span>
          <div class="tag-list-box" v-if="form.course_labels.length">
            <div  class="tag-list" v-for="(item, index) in form.course_labels" :key="index">
              <el-tooltip effect="dark" :content="item.category_full_name" placement="top-start">
                <span class="tag-value">{{ item.label_name}}</span>
              </el-tooltip>
            </div>
          </div>
          <span class="tag-line" v-else>-</span>
        </div>
        <div class="item-box project-time">
          <span class="label">项目时间：</span>
          <div v-if="form.course_period_type === 1" class="projectt-start-time">
            <span class="value">限定项目学习起止时间</span><span class="time-tips">所有学员的学习起止时间相同，到达项目开始时间后可进行学习，到达项目结束时间后无法继续学习。</span>
            <div class="timer-box">
              <p class="time-label">起止时间<span class="time-value">{{ form.start_time }} 至 {{ form.end_time}}</span></p>
            </div>
          </div>
          <div v-else-if="form.course_period_type === 2">
            <span class="value">限定项目学习周期</span><span class="time-tips">项目发布后长期有效，每个学员加入项目后具备独立的学习起止时间，需要在指定天数内完成任务，逾期无法继续学习。</span>
          </div>
          <div v-else>
            <span class="value">不限定项目学习时间</span><span class="time-tips">项目发布后，学员可在任意时间参与项目进行学习</span>
          </div>
        </div>
        <div class="item-box project-cover-img">
          <span class="label">项目封面：</span>
          <el-image class="cover-img" :src= "editImgUrl" alt="">
            <div class="image-slot" slot="placeholder">
              <i class="el-icon-loading"></i>
            </div>
            <div class="error-default-img" slot="error">
              <img  class="cover-img" :src="require('@/assets/mooc-img/default_bg_img.png')" alt="" />
            </div>
          </el-image>
        </div>
        <!-- <div class="item-box project-detail diy-detail"><span class="label">项目简介：</span><span class="value">{{ form.course_desc || '-' }}</span></div> -->
        <div class="item-box project-detail diy-detail" :class="{'project-detail-sanjieke': form.resource_from === 'sanjieke'}"><span class="label">项目简介：</span>
          <sdc-mce-preview
              ref="editor"
              :urlConfig="editorConfig.urlConfig"
              :catalogue.sync="editorConfig.catalogue"
              :content="form.course_desc || '-'"
            >
          </sdc-mce-preview>
        </div>
        <div class="item-box project-detail diy-detail" :class="{'project-detail-sanjieke': form.resource_from === 'sanjieke'}"><span class="label">项目详情：</span>
          <sdc-mce-preview
            v-if="tincyValue"
            ref="editor"
            :urlConfig="editorConfig.urlConfig"
            :catalogue.sync="editorConfig.catalogue"
            :content="form.course_desc_detail ? form.course_desc_detail.replace(/\n/g, '<br/>') : ''"
          >
          </sdc-mce-preview>
          <span v-else class="tag-line">-</span>
        </div>
      </div>
    </div>
    <div class="base-set-box">
      <div class="title-box"><img src="@/assets/mooc-img/title-icon.png" /><span class="bassinfo-class-title">基础设置</span>
      </div>
      <div class="base-set-content">
        <!-- <div class="item-box"><span class="label">项目级别：</span><span class="value">{{ form.course_level_name || '-' }}</span></div> -->
        <div class="item-box"><span class="label">内容的管理组织：</span><span class="value">{{ form.dept_name || '-' }}</span></div>
        <div class="item-box project-detail diy-detail"><span class="label">管理员：</span><span class="value">{{ form.course_admins_name || '-' }}</span></div>
        <div class="item-box"><span class="label">展现形式：</span><span class="value">{{ form.serial_type_name || '-' }}</span></div>
        <div class="item-box"><span class="label">连载状态：</span><span class="value">{{ form.study_type_name || '-'}}</span></div>
        <div class="item-box">
          <div class="certify-switch-box">
            <span class="label">证书颁发：</span>
            <el-switch 
            :disabled="true" 
            v-model="form.enable_grant_certificate" 
            active-color="#0052D9"
            inactive-color="#dcdfe6"
            >
            </el-switch>
            <span>当学员完成培养项目时，系统将自动为其颁发证书；如调整证书设置，不会影响已获得的学员；请联系超级管理员graywu进行设置</span>
          </div>
          <div v-show="form.certificate_name && form.enable_grant_certificate" class="certificate-box">
            <span>{{ form.certificate_name }}</span>
          </div>
        </div>
        <div class="item-box">
          <span class="label">记录同步：</span>
          <el-switch :disabled="true" v-model="form.enable_study_record_sync" active-color="#0052D9" inactive-color="#dcdfe6"></el-switch>
          <span>开启后，如果学员在QL平台通过其他途径完成过相同课程或考试，结果记录将会自动同步至此项目对应的任务中</span>
        </div>

        <div class="item-box">
          <span class="label">HR助手通知：</span>
          <el-switch :disabled="true" v-model="form.enable_hr_assistant" active-color="#0052D9" inactive-color="#dcdfe6"></el-switch>
          <span>开启后，可使用HR助手消息渠道进行加入提醒或催办，请联系超级管理员graywu进行设置</span>
        </div>
        <div class="item-box">
          <span class="label">企微机器人通知：</span>
          <el-switch :disabled="true" v-model="form.enable_bot" active-color="#0052D9" inactive-color="#dcdfe6"></el-switch>
          <span>开启后，可使用企微机器人消息渠道进行加入提醒或催办，请联系超级管理员graywu进行设置</span>
        </div>

        <div class="item-box">
          <span class="label">MyOA催办：</span>
          <el-switch :disabled="true" v-model="form.enable_myoa" active-color="#0052D9" inactive-color="#dcdfe6"></el-switch>
          <span>开启后，项目管理员可以通过MyOA渠道催办未完成的学员；请联系超级管理员graywu进行设置</span>
        </div>
        <div class="item-box">
          <span class="label">催办抄送HRBP：</span>
          <el-switch :disabled="true" v-model="form.enable_copy_bp" active-color="#0052D9" inactive-color="#dcdfe6"></el-switch>
          <span>开启后，项目管理员可以选择在设定的催办时间点给组织BP发送未完成学员的汇总信息邮件提醒；请联系超级管理员graywu进行设置</span>
        </div>

        <div class="item-box">
          <span class="label">催办抄送直接上级：</span>
          <el-switch :disabled="true" v-model="form.enable_copy_leader" active-color="#0052D9" inactive-color="#dcdfe6"></el-switch>
          <span>开启后，可以选择在设定的催办时间点给直接上级发送未完成学员的汇总信息邮件提醒，请联系超级管理员graywu进行设置</span>
        </div>

        <div class="item-box">
          <span class="label">显示项目人数：</span>
          <el-switch :disabled="true" v-model="form.show_join_count" active-color="#0052D9" inactive-color="#dcdfe6"></el-switch>
          <span>开启后，项目给用户展示的信息中会显"xx人参与学习"</span>
        </div>

        <div class="item-box" v-if="form.resource_from">
          <span class="label">是否需要购买：</span>
          <el-switch :disabled="true" v-model="form.acquisition_type_flag" active-color="#0052D9" inactive-color="#dcdfe6"></el-switch>
          <span>开启后，项目需要使用对应类型学霸卡兑换</span>
        </div>
      </div>
    </div>
    <div class="base-set-box">
      <div class="title-box"><img src="@/assets/mooc-img/title-icon.png" /><span class="bassinfo-class-title">其他内容信息</span>
      </div>
      <div class="base-set-content">
        <div class="item-box"><span class="label">父内容ID：</span><span class="value">{{ form.course_statement.parent_content_act_type ? `${showLabel(parent_content_act_type_options, form.course_statement.parent_content_act_type)}; ${form.course_statement.parent_content_id}` : '-' }}</span></div>
        <div class="item-box"><span class="label">创作来源：</span><span class="value">{{ showLabel(creation_source_Options, form.course_statement.creation_source) }}</span>
          <div class="creation_source_sub_content" v-if="[0, 1, 3].includes(form.course_statement.creation_source)">
            <template v-if="form.course_statement.creation_source === 0">
              <div class="item-box"><span class="label">创作组织：</span><span class="value">{{ showOrgName(form.course_statement.pgc_creation_org) }}</span></div>
              <!-- <div class="item-box"><span class="label">联合创建组织：</span><span class="value">{{ showOrgName(form.course_statement.pgc_joint_creation) }}</span></div> -->
            </template>
            <template v-if="form.course_statement.creation_source === 3">
              <div class="item-box"><span class="label">创作组织：</span><span class="value">{{ showOrgName(form.course_statement.pugc_creation_org) }}</span></div>
              <div class="item-box"><span class="label">联合创建组织：</span><span class="value">{{ showOrgName(form.course_statement.pugc_joint_creation) }}</span></div>
            </template>
            <template v-if="form.course_statement.creation_source === 1">
              <div class="item-box-specal"><span class="label">供应商名称：</span><span class="value">{{ form.course_statement.ogc_supplier_name || '-' }}</span></div>
              <div class="item-box"><span class="label">采购组织：</span><span class="value">{{ showOrgName(form.course_statement.ogc_purchase_org) }}</span></div>
              <div class="item-box"><span class="label">采购方式：</span><span class="value">{{ showLabel(purchase_type_Options, form.course_statement.ogc_purchase_type) }}</span></div>
              <div class="item-box"><span class="label">外部讲师：</span><span class="value">{{ form.course_statement.ogc_out_teachers || '-' }}</span></div>
              <div class="item-box"><span class="label">采购成本：</span><span class="value">{{ form.course_statement.ogc_purchase_amount || '-' }} <span v-if="form.course_statement.ogc_purchase_amount">元</span></span></div>
            </template>
          </div>
        </div>
        <div class="item-box"><span class="label">项目创建人：</span><span class="value">{{ showPeopleName(form.course_statement.project_creator) }}</span></div>
        <div class="item-box"><span class="label">人力成本：</span><span class="value">{{ form.course_statement.human_cost || '-' }} <span v-if="form.course_statement.human_cost">人天</span></span></div>
        <div class="item-box"><span class="label">内容开发人：</span><span class="value">{{ showPeopleName(form.course_statement.developer) }}</span></div>
        <div class="item-box"><span class="label">认证等级：</span><span class="value">{{ resolveLavel(form.course_level) }}</span></div>
        <div class="item-box" v-if="isSuperOrCompanyAdmin"><span class="label">运营分级：</span><span class="value">{{ showLabel(operation_type_options, form.course_statement.operation_level) }}</span>
          <div class="creation_source_sub_content" v-if="form.course_statement.operation_project_name">
            <div class="item-box"><span class="label">分级项目：</span><span class="value">{{ form.course_statement.operation_project_name || '-' }}</span></div>
          </div>
        </div>
        <div class="item-box"><span class="label">内容的专家评分：</span><span class="value">{{ form.course_statement.expert_score || '-' }}</span></div>
        <div class="item-box" v-if="isSuperOrCompanyAdmin"><span class="label">是否加入推荐池：</span><span class="value">{{ form.course_statement.join_recommend === null ? '-' : form.course_statement.join_recommend ? '是，加入推荐池' : '否，不加入推荐池' }}</span></div>
        <div class="item-box"><span class="label">是否必修：</span><span class="value">{{ form.course_statement.is_required === null ? '-' : form.course_statement.is_required ? '指定人群必修或全员必修' : '否，全员选修' }}</span></div>
        <div class="item-box" v-if="isSuperAdmin"><span class="label">是否同步给小Q同学：</span><span class="value">{{ form.ai_sync_flag  ? '同步' : '不同步'}}</span></div>
        <div class="item-box" v-if="isSuperAdmin && form.ai_sync_flag"><span class="label">数据有效时间：</span><span class="value">{{ form.ai_expire_type === 1  ? '长期' : `${form.ai_expire_end_time || ''}`}}</span></div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { getTrainInfo } from '@/config/mooc.api.conf.js'
export default {
  data() {
    return {
      parent_content_act_type_options: [
        { label: '网络课', value: 2 },
        { label: '培养项目', value: 11 },
        { label: '直播', value: 5 },
        { label: '面授课', value: 1 },
        { label: '活动', value: 4 },
        { label: '文章', value: 6 },
        { label: '课单', value: 15 },
        { label: '案例', value: 16 },
        { label: '行家', value: 19 }
      ],
      creation_source_Options: [
        { label: '培训or业务团队独立开发(PGC)', value: 0 },
        { label: '培训团队联合业务作者合作创作（PUGC）', value: 3 },
        { label: '员工自发原创（UGC）', value: 2 },
        { label: '外部引入（OGC）', value: 1 }
      ],
      courseLevelList: [
        { code: '0', name: '无', disabled: false }, 
        { code: '1', name: '公司级', disabled: false }, 
        { code: '2', name: 'BG级', disabled: false },
        { code: '3', name: '部门级', disabled: false }
      ],
      operation_type_options: [
        { label: '非体系', value: 3 },
        { label: '基础', value: 2 },
        { label: '中坚', value: 1 },
        { label: '头部', value: 0 }
      ],
      purchase_type_Options: [
        { label: '个人按需购买', value: 0 },
        { label: '公司统一采购', value: 1 },
        { label: '账号采购', value: 2 }
      ],
      form: {
        resource_from: '',
        course_title: '',
        course_category_name: '',
        course_period_type: 1, // 1-限定项目时间 2-限定项目周期 3-不限定时间
        course_desc: '',
        course_desc_detail: '',
        course_level: '1',
        dept_id: '',
        dept_name: '',
        course_admins: '',
        study_type: 1, // 1-列表样式 2-地图样式
        serial_type: 1, // 1-非连载 2-连载
        enable_grant_certificate: true,
        enable_study_record_sync: true,
        studyTime: '',
        cover_image_id: '',
        cover_image: '',
        cover_image_storage_type: '',
        course_classifies: [],
        course_labels: [],
        certificate_id: '',
        certificate_name: '',
        end_time: '', 
        start_time: '',
        course_admins_name: '',
        study_type_name: '',
        serial_type_name: '',
        course_level_name: '',
        enable_hr_assistant: false,
        enable_myoa: false,
        enable_copy_leader: false, // 是否抄送直接上级
        show_join_count: false, // 是否显示项目人数
        enable_copy_bp: false, // 是否抄送BP
        enable_bot: false,
        ai_sync_flag: '',
        ai_expire_type: '',
        ai_expire_start_time: '',
        ai_expire_end_time: '',
        acquisition_type_flag: false,
        course_statement: {
          operation_title: '', // 运营标题
          parent_content_act_type: '', // 父内容类型
          parent_content_id: '', // 父内容ID
          creation_source: null, // 创作来源
          pgc_creation_org: [], // PGC创作组织
          pgc_joint_creation: [], // PGC联合创作组织
          pugc_creation_org: [], // PUGC创作组织
          pugc_joint_creation: [], // PUGC联合创作组织
          ogc_supplier_name: '', // 供应商名称
          ogc_purchase_org: [], // 采购组织
          ogc_purchase_type: null, // 采购方式
          ogc_purchase_amount: undefined, // 采购成本
          project_creator: [], // 项目创建人
          human_cost: undefined, // 人力成本
          developer: [], // 内容开发人
          operation_level: null, // 运营分级
          operation_project_name: '', // 分级项目
          expert_score: null, // 内容专家评分
          join_recommend: false, // 是否加入推荐池
          is_required: false // 是否纳入应学
        }
      },
      editImgUrl: '',
      showTypeOptions: [
        { value: 1, label: '列表样式' },
        { value: 2, label: '地图样式' }
      ],
      serialTypeOptions: [
        { value: 1, label: '非连载' },
        { value: 2, label: '连载' }
      ],
      courseLevelOptions: [
        { value: '1', label: '公司级' },
        { value: '2', label: 'BG级' },
        { value: '3', label: '部门级' }
      ],
      editorConfig: {
        catalogue: false,
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/{contentId}`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview`
        }
      }
    }
  },
  watch: {
    form: {
      deep: true,
      handler(data) {
        const { cover_image } = data
        this.editImgUrl = cover_image
        // const { cover_image_id, cover_image_storage_type, cover_image } = data
        // const envName = env[process.env.NODE_ENV]
        // if (['zhihui', 'other', 'geekBang', 'imooc'].includes(cover_image_storage_type)) {
        //   this.editImgUrl = cover_image
        // } else {
        //   this.editImgUrl = `${envName.contentcenter}content-center/api/v1/content/imgage/${cover_image_id}/preview`
        // }
      }
    }
  },
  computed: {
    ...mapState(['userLimitInfo']),
    tincyValue() {
      return this.form?.course_desc_detail ? this.form?.course_desc_detail.replace(/(<([^>]+)>)/ig, '') || this.form?.course_desc_detail.includes('data-content') : false
    },
    // 是否是公司管理员或者超级管理员
    isSuperOrCompanyAdmin() {
      let { supper_admin, mooc_company_admin } = this.userLimitInfo
      return supper_admin || mooc_company_admin
    },
    // 是否是超管
    isSuperAdmin() {
      let { supper_admin } = this.userLimitInfo
      return supper_admin
    }
  },
  mounted() {
    this.getInfo()
  },
  methods: {
    showLabel(options, value) {
      if (value === null || value === undefined || value === '') return '-'
      return options.find(item => {
        return item.value === value
      })?.label || '-'
    },
    showPeopleName(array) {
      try {
        if (!array.length) return '-'
        let newArray = array.map(item => item.StaffName || item.StaffID)
        return newArray.join('; ')
      } catch (error) {
        return '-'
      }
    },
    showOrgName(array) {
      try {
        if (!array.length) return '-'
        let newArray = array.map(item => item.UnitFullName || item.UnitName)
        return newArray.join('; ')
      } catch (error) {
        return '-'
      }
    },
    resolveLavel(value) {
      if (!value) return '-'
      return this.courseLevelList.find(item => item.code === value)?.name || '-'
    },
    getInfo() {
      const id = this.$route.query.mooc_course_id
      if (id) {
        getTrainInfo(id).then((res) => {
          // 兼容
          if (res.course_statement) {
            const resolveArray = ['project_creator', 'developer', 'pgc_creation_org', 'pgc_joint_creation', 'pugc_creation_org', 'pugc_joint_creation', 'ogc_purchase_org']
            resolveArray.forEach(item => {
              try {
                res.course_statement[item] = JSON.parse(res.course_statement[item])
              } catch (error) {
                res.course_statement[item] = []
              }
            })
            // 特殊处理OGC的外部讲师字段 ogc_out_teachers
            if (res.course_statement.ogc_out_teachers) {
              try {
                let ogc_out_teachers_array = JSON.parse(res.course_statement['ogc_out_teachers'])
                res.course_statement['ogc_out_teachers'] = ogc_out_teachers_array.map(item => item.StaffName).join(';')
              } catch (error) {
                res.course_statement['ogc_out_teachers'] = ''
              }
            }
          }
          const {
            course_title,
            course_classifies,
            course_period_type,
            end_time, 
            start_time,
            cover_image_id,
            cover_image,
            cover_image_storage_type,
            course_desc,
            course_desc_detail,
            course_level,
            dept_name,
            dept_id,
            course_admins,
            study_type,
            serial_type,
            enable_grant_certificate,
            enable_study_record_sync,
            certificate_name,
            course_labels,
            enable_hr_assistant,
            enable_myoa,
            enable_copy_leader,
            show_join_count,
            enable_copy_bp,
            enable_bot,
            resource_from,
            course_acquisition_type,
            ai_sync_flag,
            ai_expire_type,
            ai_expire_start_time,
            ai_expire_end_time
          } = res
          let course_statement = res.course_statement
          if (!course_statement) {
            course_statement = {
              operation_title: '',
              parent_content_act_type: null,
              parent_content_id: null,
              creation_source: null,
              pgc_creation_org: [],
              pgc_joint_creation: [],
              pugc_creation_org: [],
              pugc_joint_creation: [],
              ogc_supplier_name: '',
              ogc_purchase_org: [],
              ogc_out_teachers: '',
              ogc_purchase_type: null,
              ogc_purchase_amount: undefined,
              project_creator: [],
              human_cost: undefined,
              developer: [],
              operation_level: null,
              operation_project_name: '',
              expert_score: null,
              join_recommend: null,
              is_required: null
            }
          }
          const {
            operation_title,
            parent_content_act_type,
            parent_content_id,
            creation_source,
            pgc_creation_org,
            pgc_joint_creation,
            pugc_creation_org,
            pugc_joint_creation,
            ogc_supplier_name,
            ogc_purchase_org,
            ogc_purchase_type,
            ogc_out_teachers,
            ogc_purchase_amount,
            project_creator,
            human_cost,
            developer,
            operation_level,
            operation_project_name,
            expert_score,
            join_recommend,
            is_required
          } = course_statement
          // // 分类处理
          // let course_category_name = ''
          // course_classifies.forEach((e) => {
          //   course_category_name += e.classify_full_name + '; '
          // })
          // course_category_name = course_category_name.slice(0, -2)
          // 管理人员处理
          let course_admins_name = ''
          course_admins.forEach((e) => {
            course_admins_name += e.admin_name + '; '
          })
          course_admins_name = course_admins_name.slice(0, -2)
          const serial_type_Info = this.showTypeOptions.find((e) => e.value === study_type)
          const study_type_Info = this.serialTypeOptions.find((e) => e.value === serial_type)
          const course_level_Info = this.courseLevelOptions.find((e) => e.value === course_level)
          this.form = {
            resource_from,
            course_title,
            course_classifies,
            // course_category_name,
            course_period_type,
            // studyTime,
            cover_image_id,
            cover_image,
            cover_image_storage_type,
            course_desc,
            course_desc_detail,
            course_labels,
            dept_name,
            dept_id,
            course_admins: course_admins?.length ? course_admins : '',
            enable_grant_certificate,
            enable_study_record_sync,
            end_time, 
            start_time,
            course_admins_name,
            serial_type_name: serial_type_Info?.label || '',
            study_type_name: study_type_Info?.label || '',
            certificate_name,
            course_level_name: course_level_Info?.label || '',
            enable_hr_assistant,
            enable_myoa,
            enable_copy_leader,
            show_join_count,
            enable_copy_bp,
            enable_bot,
            course_level,
            ai_sync_flag,
            ai_expire_type,
            ai_expire_start_time,
            ai_expire_end_time,
            course_acquisition_type,
            acquisition_type_flag: course_acquisition_type === 2,
            course_statement: {
              operation_title,
              parent_content_act_type,
              parent_content_id,
              creation_source,
              pgc_creation_org,
              pgc_joint_creation,
              pugc_creation_org,
              pugc_joint_creation,
              ogc_supplier_name,
              ogc_purchase_org,
              ogc_purchase_type,
              ogc_out_teachers: ogc_out_teachers || '',
              ogc_purchase_amount: ogc_purchase_amount === null ? undefined : ogc_purchase_amount,
              project_creator,
              human_cost: human_cost === null ? undefined : human_cost,
              developer,
              operation_level,
              operation_project_name,
              expert_score,
              join_recommend,
              is_required
            }
          }
        })
      }
    }
  }
}
</script>
  
<style lang="less" scoped>
.content {
  padding: 20px 20px 40px;
  .tag-line {
    line-height: 22px;
    color: #333;
  }

  .item-box {
    color: rgba(0, 0, 0, 0.4);
    font-size: 14px;

    .value {
      color: rgba(0, 0, 0, 0.9);
    }
    .label {
      width: 138px;
      text-align: right;
      display: inline-block;
    }

    .label,
    .value {
      line-height: 22px;
    }
  }
  .item-box-specal {
    margin-bottom: 20px;
    display: flex;
    color: rgba(0, 0, 0, 0.4);
    font-size: 14px;
    .value {
      color: rgba(0, 0, 0, 0.9);
    }
    .label {
      flex-shrink: 0;
      width: 126px;
      text-align: right;
      display: inline-block;
    }

    .label,
    .value {
      line-height: 22px;
    }
  }

  .item-box+.item-box {
    margin-top: 20px;
  }

  .title-box {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    img {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .bassinfo-class-title {
      color: #000000;
      font-weight: bold;
      line-height: 22px;
      display: inline-block;
    }
  }
.project-detail, .project-cover-img {
    display: flex;
    align-items: flex-start;
  }
  .diy-detail {
    .value {
      display: inline-block;
      flex: 1;
    }
  }
  .base-info-content {
    // padding-left: 24px;
    .cover-img {
      width: 200px;
      height: 133px;
      border-radius: 4px;
    }
   
    .tag-list-box {
      display: flex;
      flex-wrap: wrap;
      line-height: 22px
    }
    .tag-box {
      display: flex;

      .tag-value {
        background-color: rgba(235, 239, 252, 1);
        height: 20px;
        font-size: 12px;
        color: rgba(0, 82, 217, 1);
        padding: 4px;
        border-radius: 2px;
        display: inline-block;
        margin-right: 12px;
        line-height: 10px;
      }
    }

    .project-time {
      display: flex;
      .time-tips {
        margin-left: 16px;
      }

      .timer-box {
        background: #f9f9f9;
        border-radius: 4px;
        padding: 13px 16px;
        // margin-left: 70px;
        margin-top: 8px;
        width: 504px;
        .time-label {
          line-height: 22px;
        }

        .time-value {
          margin-left: 24px;
        }
      }
    }
  }

  .base-set-box {
    margin-top: 36px;

    .base-set-content {
      // padding-left: 24px;

      .item-box {
        .el-switch {
          margin-right: 12px;
        }
      }

      .certificate-box {
        margin-top: 12px;
        margin-left: 70px;
        background: #ecf2fe;
        border-radius: 4px;
        padding: 0px 16px;
        margin-right: 16px;
        height: 36px;
        line-height: 36px;
        display: inline-block;

        span {
          line-height: 22px;
          height: 22px;
          display: inline-block;
        }
      }
      .creation_source_sub_content {
        background: #f9f9f9;
        border-radius: 4px;
        margin: 10px 0 0 126px;
        padding: 16px;
        // width: 600px;
        // height: 100px;
        .label {
          width: 100px;
        }
      }
    }
  }
  .sdc-editor-preview {
    flex: 1
  }
}

@font-color: #2C2C2E;
@line-height: 28px;
@main-color: #273BC1;
.project-detail-sanjieke {
    :deep(.sdc-editor-preview) {
      .editor-content {
        h1 {
          font-size: 24px;
        }
        h2 {

        }
        img {
          margin: 15px 0;
        }

        h1 {
          font-weight: 600;
          font-size: 24px;
          line-height: @line-height;
          color: @font-color;
          margin-bottom: 28px;
        }

        h2 {
          font-weight: 600;
          font-size: 19px;
          line-height: @line-height;
          color: @font-color;
          border-left: 4px solid @main-color;
          padding-left: 10px!important;
          margin-bottom: 28px;
        }

        h3 {
          font-weight: 600;
          font-size: 17px;
          line-height: @line-height;
          color: @font-color;
          margin-bottom: 28px;
        }

        p {
          font-weight: 400;
          font-size: 16px;
          line-height: @line-height;
          color: @font-color;
          margin-bottom: 18px;
          text-indent: 0!important;
        }

        p:last-child {
          margin-bottom: 0;
        }

        img {
          display: block;
          margin-bottom: 28px;
        }

        blockquote {
          font-weight: 400;
          font-size: 16px;
          line-height: @line-height;
          color: #5A6A80;
          border-left: 4px solid #CBD3DE;
          padding-left: 10px!important;
          margin-left: 20px;
        }

        pre {
          white-space: pre-wrap;
          white-space: -moz-pre-wrap;
          white-space: -pre-wrap;
          white-space: -o-pre-wrap;
          word-wrap: break-word;
          padding: 10px;
          margin-bottom: 16px;
          border-radius: 3px;
          line-height: 2;
          background: #f4f2f0;
          overflow-x: auto;
        }

        code {
          background: #f4f2f0;
          padding: 2px 4px;
          color: #474334;
        }

        a {
          font-weight: 400;
          font-size: 16px;
          line-height: @line-height;
          color: @main-color;
          text-decoration: none;
        }

        a:hover {
          color: #3E5AE5;
        }

        ul, ol {
          padding-left: 24px;
          font-weight: 400;
          font-size: 14px;
          line-height: @line-height;
          color: @font-color;
          li {
            margin-bottom: 9px;
            list-style: inherit;
            p {
              font-size: 14px;
            }
          }
        }

        ul {
          list-style-type: disc;
        }
        ol {
          list-style-type: decimal;
        }

        table {
          border-collapse: collapse;
          td {
            padding: 8px 16px;
          }

          thead {
            td {
              font-weight: 500;
              font-size: 16px;
              line-height: @line-height;
              color: @font-color;
            }
          }

          tbody {
            td {
              font-weight: 400;
              font-size: 16px;
              line-height: @line-height;
              color: @font-color;
            }
            tr:nth-child(odd) {
              background: #F0F2F5;
            }
          }
        }

      }
    }
  }
</style>
