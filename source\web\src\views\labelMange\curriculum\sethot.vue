<template>
  <div>
    <el-dialog title="消息" :visible.sync="visible" width="20%" :before-close="handleClose"
      :close-on-click-modal="click_modal">
      <p class="title_p" v-if="itemData.label_hot_enable === 1 && selectionlist.length === 0">
        确定要给此标签进行取消热门操作吗?
      </p>
      <p class="title_p" v-if="itemData.label_hot_enable === 1 && selectionlist.length !== 0">
        确定要取消选中的{{ selectionlist.length }}个标签热门吗?
      </p>
      <p class="title_p" v-if="itemData.label_hot_enable !== 1 && selectionlist.length === 0">
        确定要给此标签进行设为热门操作吗?
      </p>
      <p class="title_p" v-if="itemData.label_hot_enable !== 1 && selectionlist.length !== 0">
        确定要设置选中的{{ selectionlist.length }}个标签为热门吗?
      </p>

      <span slot="footer" class="dialog-footer">
        <el-button @click="out">取 消</el-button>
        <el-button type="primary" @click="dialogVisible()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { update_hot_label } from 'config/api.conf'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    itemData: {},
    selectionlist: []
  },
  data() {
    return {
      classification: '',
      click_modal: false
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    dialogVisible() {
      let params =
        this.selectionlist.length === 0
          ? {
            label_ids: [this.itemData.label_id],
            label_hot_enable: this.itemData.label_hot_enable === 1 ? 0 : 1
          }
          : {
            label_ids: this.selectionlist,
            label_hot_enable: this.itemData.label_hot_enable === 1 ? 0 : 1
          }
      update_hot_label(params).then((res) => {
        this.$parent.getlist()
      })

      this.$emit('update:visible', false)
    },
    out() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.block {
  margin-bottom: 10px;

  span {
    margin-right: 20px;
  }
}

.title_p {
  font-size: 16px;
  margin-bottom: 10px;
  line-height: 26px;
}

.title {
  color: #ff7c51;
}
</style>
