<template>
  <div class="student-questionnaire-popup activity-common" v-if="visible">
    <!-- Mobile -->
    <div class="mobile-template" v-if="isMobile">
      <van-dialog className="van-dialog-customer" overlayClass="overlay-customer" v-model="visible" :showConfirmButton="false" :close-on-click-overlay="false">
        <div class="dialog-popup">
          <div class="dialog-header">
            课前问卷<span>请填写并提交问卷以完成报名</span>
            <button type="button" class="header-btn" @click="handlerCancel"><i class="el-icon el-icon-close"></i></button>
          </div>
          <div class="dialog-content" v-loading="loading">
            <iframe :src="questionSrc" class="questionIframe" frameborder="0" id="questionIframe" @load="onIframeLoad"></iframe>
          </div>
        </div>
      </van-dialog>
    </div>

    <!-- PC -->
    <div class="pc-template" v-else>
      <el-dialog title="" :visible.sync="visible" width="1180px" :close-on-click-modal="false" :before-close="handlerCancel">
        <div slot="title" class="dialog-header">课前问卷<span>请填写并提交问卷以完成报名</span></div>
        <div class="dialog-content" v-loading="loading">
          <iframe :src="questionSrc" class="questionIframe" frameborder="0" id="questionIframe" @load="onIframeLoad"></iframe>
        </div>
      </el-dialog>
    </div>

  </div>
</template>

<script>
export default {
  name: 'studentQuestionnairePopup',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    joinType: {
      type: [Number, String],
      default: 1
    },
    parentStaff: {
      type: Object,
      default: () => ({})
    },
    courseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      questionSrc: '',
      loading: true
    }
  },
  watch: {
    courseData: {
      handler(newVal) {
        if (newVal.before_class_survey) {
          const { activity_id } = newVal
          const loginUser = JSON.parse(sessionStorage.getItem('login_user'))
          const src = newVal.before_class_survey.wj_url
          let ext1 = `0#${this.joinType}`
          const { parent_name, parent_staff_id } = this.parentStaff
          if (parent_name && parent_staff_id) {
            ext1 = `0#${this.joinType}#${parent_name}#${parent_staff_id}`
          }
          console.log('ext1', ext1, this.loading)
          this.questionSrc = `${src}?user_id=${loginUser.staff_id}&course_id=${activity_id}&from=activity&ext1=${ext1}`
        }
      },
      immediate: true
    }
  },
  computed: {
    isMobile () {
      var ua = navigator.userAgent.toLowerCase()
      return /iphone|ipad|ipod|android|blackberry|mini|windows\sce|phone|mobile/.test(ua) || /micromessenger/.test(ua)
    },
    questionIframeDom () {
      return document.getElementById('questionIframe')
    }
  },
  created() { },
  mounted() {
    setTimeout(() => {
      if (this.loading) this.loading = false
    }, 2000)
  },
  beforeDestroy() { },
  methods: {
    onIframeLoad(e) {
      this.loading = false
    },
    handlerCancel() {
      this.$emit('update:visible', false)
      this.$emit('onClose')
    }
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/activity-common.less';
.student-questionnaire-popup {
  .mobile-template {
    :deep(.overlay-customer) {
      background: #00000066;
    }
    :deep(.van-dialog-customer) {
      width: 343px;
      border-radius: 0;
      background: transparent;
    }
    .dialog-header {
      padding: 20px 24px;
      color: #000000e6;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      position: relative;
      border-bottom: 1px solid #EFEFEF;
      border-radius: 8px 8px 0 0;
      background: #fff;
      & > span {
        color: #00000099;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px;
        margin-left: 20px;
        display: inline-block;
      }
      .header-btn {
        width: 24px;
        padding: 0;
        background: 0 0;
        border: none;
        outline: 0;
        cursor: pointer;
        font-size: 16px;
        color: #999999;
        position: absolute;
        right: 24px;
        top: 20px;
        & > i {
          font-weight: 600;
        }
      }
    }
    .dialog-content {
      width: 100%;
      height: 70vh;
      display: flex;
      border-radius: 0 0 8px 8px;
      background: #fff;
      position: relative;
      .questionIframe {
        width: 100%;
        height: 100%;
      }
    }
  }
  .pc-template {
    :deep(.el-dialog) {
      margin-top: 5vh !important;
    }
    :deep(.el-dialog .el-dialog__body) {
      padding: 0;
    }
    .dialog-header {
      & > span {
        color: #00000099;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        margin-left: 28px;
        display: inline-block;
      }
    }
    .dialog-content {
      width: 100%;
      position: relative;
      .questionIframe {
        width: 100%;
        height: 80vh;
        min-height: 576px;
        max-height: calc(100vh - 200px);
      }
    }
  }
}
</style>
