<template>
  <div class="page-wrap">
    <div class="upload-cover-image-page">
      <!-- 一键生成项目封面 -->
      <sdc-img-cover 
        v-if="autoImgCoverShow"
        ref="sdcImgCoverRef" 
        :visible.sync="autoImgCoverShow" 
        :imgInfo="imgInfo"
        @handleImgCoverOk="handleImgCoverOk"
        >
      </sdc-img-cover>
    </div>
  </div>
</template>
  
<script>
import { getLoginUser } from 'config/api.conf'

export default {
  components: {
  },
  data() {
    return {
      // form: {
      //   cover_image_id: '',
      //   cover_image: '',
      //   cover_image_storage_type: ''
      // },
      imgInfo: {
        title: '',
        env: process.env.NODE_ENV === 'production' ? 'production' : 'test',
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        showEdit: true,
        isIndependentPage: true
      },
      autoImgCoverShow: false
    }
  },
  computed: {
  },
  watch: {
  },
  mounted() {
    if (!this.$store.state.userInfo.staff_name) {
      let userInfo = JSON.parse(sessionStorage.getItem('login_user'))
      this.imgInfo.stamp = userInfo ? userInfo.staff_name : ''
    }
    console.log('stamp', this.imgInfo.stamp)
    this.$nextTick(() => {
      if (this.imgInfo.stamp) {
        this.autoImgCoverShow = true
      } else {
        this.getUser()
      }
    })
  },
  methods: {
    // 获取登陆用户信息
    getUser() {
      getLoginUser().then((res) => {
        const userInfo = {
          staff_id: res.staff_id,
          staff_name: res.staff_name
        }
        console.log('用户登录信息', res)
        this.imgInfo.stamp = res.staff_name
        this.autoImgCoverShow = true
        this.$store.commit('setUserInfo', userInfo)
        sessionStorage.setItem('login_user', JSON.stringify(userInfo))
      })
    },
    // 一键封面回传
    handleImgCoverOk(row) {
      // this.autoImgUrl = row.url
      // 清空裁剪封面
      // this.form.cover_image = row.url
      // this.form.cover_image_id = row.id
      // this.form.cover_image_storage_type = 'zhihui'
      // if (this.autoImgUrl) {
      //   this.$refs.form.clearValidate('cover_image')
      // }
    }
  }
}
</script>
<style lang="less" scoped>
  .page-wrap {
    width: 100%;
    height: 100%;
    background-color: #f5f7f9;
    .upload-cover-image-page {
      height: 100%;
      background: linear-gradient(180deg, #DCE8F8 0px, #F5F5F7 400px);
      :deep(.modal-cover-img-container) {
        z-index: 0 !important;
        .el-dialog {
          box-shadow: none;
          top: 82px;
          transform: translate(-50%, -0%);
        }
        // @media screen and (max-width: 1660px) {
        //   .el-dialog {
        //     width: 1180px !important;
        //   }
        // }
        // @media screen and (min-width: 1661px) {
        //   .el-dialog {
        //     width: 1420px !important;
        //   }
        // }
      }
    }
  }
</style>
