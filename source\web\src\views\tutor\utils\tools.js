// 通过用户英文名获取对于的头像地址
export const getAvatar = (useName) => {
  if (window.location.host.endsWith('.woa.com')) {
    return `//r.hrc.woa.com/photo/150/${useName}.png`
  } else {
    return `//r.hrc.oa.com/photo/150/${useName}.png`
  }
}
// actType枚举
export const actTypes = [
  {
    act_type_name: '面授课',
    act_type: 1,
    module_id: 2,
    langKey: 'Mooc_CourseType_Face'
  },
  {
    act_type_name: '网络课',
    act_type: 2,
    module_id: 1,
    resource_type: 1,
    langKey: 'NetCourse_NetCourse'
  },
  {
    act_type_name: '文档',
    act_type: 10,
    module_id: 16,
    resource_type: 5,
    langKey: 'Mooc_Common_ResourceType_Doc'
  },
  {
    act_type_name: '课单',
    act_type: 15,
    module_id: 15,
    langKey: 'Mooc_CourseType_CourseList'
  },
  {
    act_type_name: '案例',
    act_type: 16,
    module_id: 7,
    langKey: 'Mooc_CourseType_Anli'
  },
  {
    act_type_name: '文章',
    act_type: 18,
    module_id: 8,
    langKey: 'Mooc_CourseType_Article'
  },
  {
    act_type_name: '行家',
    act_type: 19,
    module_id: 6,
    langKey: 'Mooc_CourseType_hangjia'
  },
  {
    act_type_name: '考试',
    act_type: 20,
    module_id: 11,
    resource_type: 6,
    langKey: 'Mooc_Common_ResourceType_Exam'
  },
  {
    act_type_name: '培养项目',
    act_type: 11,
    module_id: 10,
    langKey: 'Mooc_CourseType_Mooc'
  },
  {
    act_type_name: '专区',
    act_type: 13,
    module_id: 12,
    langKey: 'NetCourse_Portal'
  },
  {
    act_type_name: '直播',
    act_type: 5,
    module_id: 3,
    langKey: 'NetCourse_Live'
  },
  {
    act_type_name: '外链',
    act_type: 99,
    module_id: 99,
    resource_type: 9,
    langKey: 'Mooc_Common_ResourceType_ExternalLinks'
  },
  {
    act_type_name: '活动',
    act_type: 4,
    module_id: 4,
    langKey: 'Mooc_CourseType_Activity'
  },
  {
    act_type_name: '图文',
    act_type: 6,
    module_id: 9
  },
  {
    act_type_name: '笔记',
    act_type: 17,
    module_id: 8,
    langKey: 'NetCourse_Note'
  },
  {
    act_type_name: '榜单',
    act_type: 202,
    module_id: -999
  },
  {
    act_type_name: '未知',
    act_type: -1000,
    module_id: -1000
  },
  {
    act_type_name: 'K吧文章',
    act_type: 26,
    langKey: '',
    module_id: 20
  }
]
