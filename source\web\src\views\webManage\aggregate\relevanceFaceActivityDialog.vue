<template>
  <el-drawer title="关联班级&活动" :visible.sync="drawer" @open="handlerOpen" @close="handelrClose" direction="rtl" ref="drawer" size="70%">
    <div class="face-activity">
      <el-form :inline="true" :model="form" class="demo-form-inline">
        <el-form-item label="">
          <el-input style="width:208px" size="small" clearable v-model="form.name" placeholder="请输入页面名称"></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-select style="width:256px" size="small" v-model="form.act_type" placeholder="请选择类型">
            <el-option label="活动" value="4"></el-option>
            <el-option label="班级" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="search">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table header-row-class-name="table-header-style" @selection-change="handleSelectionChange" row-class-name="table-row-style" :data="tableData.records" style="width: 100%" class="agg-table">
        <el-table-column type="selection" width="55" :selectable="selectableDisabled">
        </el-table-column>
        <el-table-column prop="class_name" label="标题">
        </el-table-column>
        <el-table-column label="类型" width="80">
          <template slot-scope="scope">
            <span>{{ Number(scope.row.act_type) === 4 ? '活动' : '班级' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template slot-scope="scope">
            <span>{{ aggStatus(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="开班时间">
          <template slot-scope="scope">
            <span class="table-course-title">{{ scope.row.start_time }}</span>
          </template>
        </el-table-column>
        <el-table-column label="授课形式">
          <template slot-scope="scope">
            <span class="table-course-title">{{ scope.row.teaching_type === 1 ? '线下授课' : '在线授课' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人">
          <template slot-scope="scope">
            <span class="table-course-title">{{ scope.row.creator_name || '-'}}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div class="operat-btn-box">
              <el-link type="primary" :underline="false" @click="openDetail(scope.row)">详情</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="current" :page-sizes="[5, 10, 20, 30, 50, 100]" :page-size="size" layout="total,  prev, pager, next, sizes, jumper" :total="tableData.total">
      </el-pagination>
      <div class="drawer__footer">
        <el-button size="small" @click="cancelForm">取 消</el-button>
        <el-button size="small" type="primary" @click="submitDrawer" :disabled="subDisabled || selectData.length === 0">确 定</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script>
import pager from '@/mixins/pager'
import { getActivityClass } from '@/config/mooc.api.conf.js'
export default {
  mixins: [pager],
  props: {
    dialogFace: {
      type: Boolean,
      defatut: false
    },
    // 关联的数据
    selectTableData: {
      type: Array,
      defatut: () => []
    }
  },
  data() {
    return {
      form: {
        name: '',
        act_type: '4'
      },
      tableData: {
        total: 0,
        records: []
      },
      subDisabled: true,
      selectData: []
    }
  },
  computed: {
    drawer: {
      set(val) {
        this.$emit('update:dialogFace', val)
      },
      get() {
        return this.dialogFace
      }
    },
    aggStatus() {
      return (val) => {
        // regist_count 已报名人数 max_student_count 可报名人数
        const { regist_count, max_student_count, start_time } = val
        if (new Date(start_time) < new Date()) {
          return '已截止报名'
        } else if (regist_count >= max_student_count) {
          return '等待列表'
        } else {
          return '可报名'
        }
      }
    }
  },
  methods: {
    handlerOpen() {
      this.getList(1)
    },
    onSearch(current) {
      this.getList(current)
    },
    selectableDisabled(row) {
      console.log(row, 'dis')
      return !this.selectTableData.some(
        (item) => item.class_id === row.class_id
      )
    },
    getList(current) {
      let params = {
        name: this.form.name,
        act_type: this.form.act_type,
        size: this.size,
        current: current
      }
      getActivityClass(params).then((res) => {
        console.log(res, '活动班级列表')
        this.tableData = {
          records: res.records,
          total: res.total
        }
      })
    },
    search() {
      this.getList(1)
    },
    cancelForm() {
      this.$emit('update:dialogFace', false)
    },
    handelrClose() {
      this.current = 1
      this.$emit('update:dialogFace', false)
    },
    handleSelectionChange(val) {
      console.log(val, 'handleSelectionChange')
      this.subDisabled = val.length + this.selectTableData.length > 10
      this.selectData = val
    },
    openDetail(val) {
      let linkUrl = process.env.NODE_ENV === 'production' ? 'https://portal.learn.woa.com/training' : 'https://test-portal-learn.woa.com/training'
      // 活动
      if (val.act_type === 4) {
        linkUrl = linkUrl + `/activity/detail?activity_id=${val.class_id}`
      } else {
        linkUrl = linkUrl + `/face?course_id=${val.course_id}`
      }
      window.open(linkUrl)
    },
    submitDrawer() {
      console.log(this.selectData, 'this.selectData')
      this.$emit('updateClass', this.selectData)
      this.$emit('update:dialogFace', false)
    }
  }
}
</script>

<style lang='less' scoped>
.face-activity {
  padding: 0 20px;
  .agg-table {
    border-radius: 4px;
    margin-top: 15px;
    opacity: 1;
    border-top: 1px solid #eeeeeeff;
    border-left: 1px solid #eeeeeeff;
    border-right: 1px solid #eeeeeeff;
  }
  .drawer__footer {
    display: flex;
    //   justify-content: center;
    position: sticky;
    bottom: 0;
    background-color: #fff;
    margin: 20px 70% 20px 20px;
  }
  .drawer__footer button {
    flex: 1;
  }
}
</style>
