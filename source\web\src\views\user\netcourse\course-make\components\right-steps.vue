<template>
  <div class="right-steps">
    <div class="title">步骤指引</div>
    <el-steps direction="vertical" finish-status="success">
      <el-step
        v-for="(item, index) in stepList"
        :key="item.label"
        :title="item.label"
        :status="item.process ? 'process' : statusList[index]"
        @click.native="scrollStep(index, item.id)"
      >
      </el-step>
    </el-steps>
  </div>
</template>

<script>
export default {
  name: 'rightSteps',
  props: {
    stepList: {
      type: Array,
      default: () => []
    },
    statusList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 点击进度滚动到对应进度区域
    scrollStep(index, id) {
      if (id === '') return
      this.$emit('isClickStep', true)
      this.stepList.forEach((i, j) => {
        i.process = index === j
      })
      const anchorElement = document.querySelector(`#${id}`)
      const scrollConfig = {
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      }
      anchorElement && anchorElement.scrollIntoView(scrollConfig)
      setTimeout(() => {
        this.$emit('isClickStep', false)
      }, 1000)
    }
  }
}
</script>

<style lang="less" scoped>
.right-steps {
  position: -webkit-sticky; /* Safari */
  position: sticky;
  top: 12px;
  margin-left: 12px;
  width: 152px;
  padding: 36px 0 0 20px;
  height: 422px;
  border-radius: 4px;
  background-color: #fff;
  .title {
    width: 80px;
    padding-bottom: 9px;
    line-height: 24px;
    font-weight: bold;
    font-size: 16px;
    color: #0052d9;
    border-bottom: 1px solid #eee;
  }
  :deep(.el-steps) {
    margin-top: 16px;
    position: relative;
    right: 6px;
    .el-step {
      height: 34px;
      flex-basis: unset !important;
      .el-step__head {
        top: 1px;
        .el-step__line {
          color: #f2f9ff;
          border-color: #f2f9ff;
          background-color: #f2f9ff;
        }
        .el-step__icon {
          bottom: 6px;
          width: 24px;
          height: 24px;
          background-color: #3464e0;
          border: unset;
          font-size: 16px;
          color: #fff;
          font-weight: bold;
          transform: scale(0.5, 0.5);
          .el-icon-check:before {
            font-weight: bold;
          }
        }
      }
      .el-step__head.is-success {
        .el-step__icon {
          background-color: #3464e0;
          color: #fff;
        }
      }
      .el-step__head.is-process {
        .el-step__icon {
          background-color: #3464e0;
          color: #fff;
        }
      }
      .el-step__head.is-wait {
        .el-step__icon {
          background-color: #f5f7f9;
          color: #cbcdce;
        }
      }

      .el-step__main {
        padding-left: 7px;
        .el-step__title {
          padding-bottom: 0;
          font-size: 14px;
          line-height: 14px;
          cursor: pointer;
        }
        .el-step__title.is-success {
          color: #333;
        }
        .el-step__title.is-process {
          color: #0052d9;
          font-weight: bold;
        }
        .el-step__title.is-wait {
          color: #999;
        }
        .el-step__description {
          padding-right: 0;
        }
      }
    }
  }
}
</style>
