<template>
    <div class="labelCardItem">
        <div class="courseImage">
            <defaultImage :info="info"/>
            <span class="duration">{{ info.origin_data.est_dur || 0 }}分钟</span>
        </div>
        <div  class="courseInfo">
            <moduleTitle :info="info" :name="moduleName"/>
            <div class="courseView">
                <div class="flex">
                    <div class="flex align-center">
                        <img :src="require('@/assets/img/label/eyes2x.png')">
                        <span>{{info.view_count || 0}}</span>
                    </div>
                    <!-- <div class="flex align-center" v-if="info.origin_data.avg_score && info.origin_data.avg_score !== '0' && info.origin_data.avg_score !== '0.0'">
                        <img :src="require('@/assets/img/label/flower2x.png')">
                        <span>{{(Math.round(info.origin_data.avg_score*10)/10).toFixed(1)}}分</span>
                    </div>
                    <span v-else>暂无评分</span> -->
                    <div class="flex align-center" v-if="info.origin_data?.sort_time">
                        <img :src="require('@/assets/img/label/time2x.png')">
                        <span>{{info.origin_data?.sort_time?.split(' ')[0]}}</span>
                    </div>
                </div>
                <div class="flex align-center local" v-if="info.origin_data.location">
                    <img :src="require('@/assets/img/label/local2x.png')">
                    <span>{{info.origin_data.location}}</span>
                </div>
            </div>
            <relatedContent :info="info" :curModuleId="curModuleId" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :subsType="subsType" :contentType="contentType"></relatedContent>
        </div>
    </div>
</template>
<script>
import defaultImage from './child/defaultImage.vue'
import moduleTitle from './child/moduleTitle.vue'
import relatedContent from './child/relatedContent.vue'
export default {
  name: 'faceCourseItem',
  props: ['info', 'curModuleId', 'curLabelId', 'sortSubsIds', 'subsType', 'contentType'],
  data() {
    return {
      nowTime: new Date().getTime()
    }
  },
  components: {
    defaultImage,
    moduleTitle,
    relatedContent
  },
  computed: {
    moduleName() {
      let class_list = this.info.origin_data?.class_list
      if (!class_list || class_list.length < 1) return '面授课'
      let flag = class_list.some((item) => {
        return item.regist_last_date && new Date(item.regist_last_date).getTime() > this.nowTime
      })
      if (flag) {
        return '面授课报名'
      } else {
        return '面授课'
      }
    }
  }
}
</script>
<style lang="less" scoped>
@import '../style/item.less';
</style>
