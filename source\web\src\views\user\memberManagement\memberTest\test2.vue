<template>
  <div>
    <button id="openBtn">打开333</button>
    <div style="width: 450px">
      <div id="chooseBox123"></div>
    </div>
  </div>
</template>

<script>
// let memberjs = require('@/memberSelector.js')
// let staffSelector = require('@/staffSelector.js')
export default {
  mounted () {
    window.memberjs.initMemberIframe('openBtn', (res) => {
      console.log(res, '有数据了')
    }, {})
    // window.memberjs.initMemberIframe('openBtn2', (res) => {
    //   console.log(res, '有数据了2')
    // }, {})

    // memberjs.initMemberIframe(['openBtn', 'openBtn2'], (res) => {
    //   console.log(res, '有数据了')
    // }, {})
    // memberjs.cancelCallback(this.hanC)

    // memberjs.initMemberIframe('openBtn2', (res) => {
    //   console.log(res, '有数据了2')
    // }, {})

    // const data = [
    //   {
    //     'Code': '',
    //     'ID': 3898705,
    //     'Name': 'test1(中文名1)'
    //   },
    //   {
    //     'Code': '',
    //     'ID': 3898702,
    //     'Name': 'test2(中文名2)'
    //   }
    // ]

    // staffSelector.initStaffSelector('chooseBox123')
    // window.staffSelector.initStaffSelector('chooseBox123', data)

    // let res = window.staffSelector.getStaffData()
    // console.log(res)
  },
  methods: {
    hanC (res) {
      console.log(res, '关闭之后的回调')
    }
  }
}
</script>

<style lang="less" scoped></style>
