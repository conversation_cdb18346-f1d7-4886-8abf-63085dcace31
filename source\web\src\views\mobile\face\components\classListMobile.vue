<template>
  <div class="class-list-te">
    <div :class="['class-content', { 'class-content-moer': showMoerList }]" v-if="classLists !== null && classLists.length">
      <div class="class-content-item" v-for="(item, index) in classLists" :key="index" :dt-eid="dtTableList('eid', item)" :dt-areaid="dtTableList('area', item)" :dt-remark="dtTableList('remark', item)">
        <div class="item-title"><span class="item-title_b">班级：</span> {{item.class_name}}</div>
        <div class="item-tips">
          <span v-if="formate1(item.start_time)">夜校班</span>
          <span>{{item.teaching_type === 1 ? '线下授课' : '在线授课'}}</span>
          <span v-if="item.need_appovel">需上级审批</span>
        </div>
        <div class="item-class-info">
          <div class="item-class-info_left">
            <div class="info-lable"><i class="icon icon-time"></i> {{item.start_end_time}}</div>
            <div class="info-lable info-lable-hidden"><i class="icon icon-name"></i> <span class="hidden-text"> {{item.inner_teacher_names}}</span></div>
            <div class="info-lable"><i class="icon icon-city"></i> {{teachingType(item)}} </div>
            <div class="info-lable"><i class="icon surplus-icon"></i> {{surplusText(item)}}</div>
          </div>
          <div class="item-class-info_right">
            <!-- 可报名 -->
            <el-tooltip class="item" effect="dark" content="已有报名班级" placement="top" v-if="statusBtn(item) === 1 && surplus_count(item) !== 0" :disabled="!registered">
              <div :class="['status-button', 'status1', {'registered-btn': registered }]" @click="handlerApply(item)" :dt-eid="dtTable('eid', '报名', item)" :dt-areaid="dtTable('area', '报名', item)" :dt-remark="dtTable('remark', '报名', item)">
                <span class="button-text">报名</span>
                <span class="button-info">剩余名额 {{surplus_count(item)}}</span>
              </div>
            </el-tooltip>
            <!-- 加入等待列表 -->
            <el-tooltip class="item" effect="dark" content="已有报名班级" placement="top" v-else-if="statusBtn(item) === 2 ||  (statusBtn(item) === 1 && surplus_count(item) === 0) " :disabled="!registered">
              <div :class="['status-button', 'status1', {'registered-btn': registered }]" @click="handlerApply(item)">
                <span class="button-text">加入等待列表</span>
                <span class="button-info">排队人数 {{wait_queen_count(item)}}</span>
              </div>
            </el-tooltip>
            <!-- 已加入等待列表 -->
            <el-tooltip v-else-if="statusBtn(item) === 3" class="item" effect="dark" content="点击取消报名" placement="top">
              <div class="status-button status2 " @click="handelrOpenLogoOut(item)">
                <span class="button-text">已加入等待列表</span>
                <span class="button-info">排队人数 {{wait_queen_count(item)}}</span>
              </div>
            </el-tooltip>
            <!-- 截止报名  -->
            <el-tooltip v-else-if="statusBtn(item) === 4" class="item" effect="dark" content="取消开班提醒" placement="top" :disabled="courseData.is_subscribed === 0">
              <div class="status-button status2" @click="handlerSetSubscribe">
                <span class="button-text">{{courseData.is_subscribed === 1 ? '已订阅开班提醒' : '开班提醒'}} </span>
                <span class="button-info" v-if="statusBtn(item) === 4">已截止报名</span>
              </div>
            </el-tooltip>
            <!-- 已报名取消报名 -->
            <el-tooltip v-else-if="statusBtn(item) === 5" class="item" effect="dark" content="点击取消报名" placement="top">
              <div class="status-button status2 " @click="handelrOpenLogoOut(item)">
                <span class="button-text">已报名</span>
                <span class="button-info">点击可取消报名</span>
              </div>
            </el-tooltip>
            <!-- 已报名不可取消报名 -->
            <div class="status-button status2" v-else-if="statusBtn(item) === 6">
              <span class="button-text">已报名</span>
              <span class="button-info">已超过注销截止时间</span>
            </div>
            <!-- 已报名审核中 -->
            <div class="status-button status2" v-else-if="statusBtn(item) === 7">
              <span class="button-text">已报名</span>
              <span class="button-info">待上级审批</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Empty v-if="classLists !== null && classLists.length === 0" :emptyShow="emptyShow" emptyTips="暂无排班" >
      <div style="padding-top:10px;">
        <span style="color: #0052d9" @click="handlerSetSubscribe">{{courseData.is_subscribed === 1 ? '已订阅开班提醒' : '订阅开班提醒' }}</span>
      </div>
    </Empty>
    <div :class="['show-more-brn', { 'show-more-brn-moer': showMoerList }, {'colse-moer-100': !closeMoer && showMoerList}]" v-show="classLists !== null && classLists.length >= 2">
      <span @click="clickMoer" class="show-more-brn-text">{{!showMoerList ? '展开查看更多' : '收起' }} <i class="icon" :class="!showMoerList ? 'el-icon-arrow-down' : 'el-icon-arrow-up' "></i> </span>
    </div>
    <!-- 报名弹窗 -->
    <van-dialog v-model="showApply" title="Q-Learning课程报名" @opened="handelrOpened" show-cancel-button class="logo-out-dialog face-dialog" @confirm="handelrConfirmApply" :before-close="closebefore" confirm-button-color="#0052d9">
      <div class="face-dialog-flex apply-dialog">
        <div class="right">
          <div v-if="!is_need_idp_score">培训公约：</div>
          <div v-else>
            <span>为保证学习效果，建议每季度报名IDP课程不超过<strong style="color:red">5</strong>门</span>
            <span v-show="currentClassItem.idp_courses_count">，您现在已报名<strong style='color:red'>{{currentClassItem.idp_courses_count}}</strong>门。</span>
          </div>
          <div>
            <ol>
              <li v-if="showCity">
                此开课地点为<span class="red-fw">【{{currentClassItem.city}}】</span>
                <span v-show="currentClassItem.need_appovel">，报名需直接上司的审批;</span>
              </li>
              <li v-else-if="!showCity && currentClassItem.need_appovel">报名需直接上司的审批;</li>
              <li>报名成功后请准时、全程参加培训，珍惜培训资源;</li>
              <li>若未准时、全程参加培训达4次，将影响下季度报名公司级课程的权限;</li>
              <li v-if="is_need_idp_score" class="red-fw">报名所需积分：<span>{{pointsRange}}</span> </li>
              <li v-if="currentClassItem.surplus_count === 0">本班报名已超过名额，你报名后将进入等待列表，你的前面还有{{wait_queen_countCur}}人在排队，后续等待邮件通知</li>
            </ol>
          </div>
          <!--积分相关提示  -->
          <div class="apply-type" v-if="is_need_idp_score">
            <p class="p0" v-if="!curr_class.isEnough"><span style="line-height:18px;padding-left:2px;"><i style="color:#e6a23c;font-size:14px;" class="el-icon-warning"></i> 积分不足</span></p>
            <p class="p1">当前可用积分：<strong>{{curr_class.surplus}}</strong></p>
            <p class="p1">第{{curr_class.quater}}季度专项课程报名积分为 <strong>{{curr_class.idp_point}}</strong>
              <!-- 分数足够抵扣 -->
              <span v-if="curr_class.isEnough">，本次报名将<strong class="red-fw">扣除{{pointsRange}}</strong><strong class="red-fw" v-html="curr_class.idp_point < curr_class.amt?'通用积分':'专项课程报名积分'"></strong></span>
            </p>
            <p class="p1">
              <span class="details-sapn"><a href="//portal.learn.woa.com/point/user/index" target="_blank">积分明细 ></a></span>
              <span class="details-sapn">
                <a :href="is_need_idp_score ? 'https://km.tencent.com/openkm/url/tn4yw3' : 'https://km.tencent.com/openkm/url/ksaldg' " target="_blank">积分消费说明></a>
              </span>
              <span><a href="//portal.learn.woa.com/point/user/rule" target="_blank">如何获取积分?</a></span>
            </p>
          </div>
          <div class="comfir-tips">
            请确认是否继续提交报名？
          </div>
          <!-- 上级审核 -->
          <div class="manage" v-if="currentClassItem.need_appovel">
            <p class="title">课程：{{courseData.course_name}}</p>
            <div class="managerName"><span style="width:80px;"><i style="color: red;margin-right:3px">* </i> 直接上级：</span>
              {{currentClassItem.parent_name}} <span class="edit-name" @click="handlerEditName">修改上级</span>
              <!-- <sdc-staff-selector ref="selectorStaff" v-model="staffId" size="small" selectClass="selectClass" modalClass="modalClass" showFullTag @change="handlerChangeStaff"></sdc-staff-selector> -->
              <div class="managerName_right">
                <!-- <el-select size="small" ref="selectInfo" @hook:mounted="cancelReadOnly" @visible-change="cancelReadOnly" v-model="valueSearch" filterable remote reserve-keyword placeholder="请输入关键词" :remote-method="remoteMethod" :loading="loading">
                  <el-option v-for="item in options" :key="item.staff_id" :label="item.staff_full_name" :value="item.staff_full_name">
                  </el-option>
                </el-select> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-dialog>
    <!-- 注销弹窗 -->
    <van-dialog v-model="showCancel" title="Q-Learning课程注销" show-cancel-button class="logo-out-dialog face-dialog" @confirm="handelrLogoOut" :before-close="closebefore" confirm-button-color="#0052d9">
      <div class="face-dialog-flex">
        <div class="right">
          <div class="tips"> <i class="el-icon-warning"></i>{{currentClassItem.need_appovel ? '培训资源有限，本次培训申请已通过直属领导审批，如取消将邮件知会您的直属领导，您确定要注销本次课程吗？' : '培训资源有限，您确定要注销本次课程吗？'}} </div>
          <div class="Reason-sel">请选择/输入注销原因</div>
          <div v-for="item in logoOutReason" :key="item.id" class="redio-content">
            <el-radio v-model="reasonRadio" :label="item.id">{{item.reason}}</el-radio>
          </div>
          <el-input v-show="[0, 3].includes(reasonRadio)" type="textarea" :rows="3" placeholder="请输入注销原因" v-model="logoOutReason[reasonRadio].other">
          </el-input>
          <template v-if="is_need_idp_score">
            <div class="label_form" style="color:#FF8200">
              <!-- <i class="el-icon-warning"></i>  -->
              注销成功后，报名课程消耗的积分将退还至个人积分账户
            </div>
          </template>
        </div>
      </div>
    </van-dialog>
    <van-popup v-model="showEditName" position="bottom" :overlay-style="{style: 'z-index: 9999' }">
      <div class="edit-name-dialog">
        <!-- <div class="title">
          <span @click="showEditName = false">取消</span>
          <span style="color: #0052d9" @click="confirmName">确认</span>
        </div> -->
        <!-- <form action="#"><van-search v-model="valueSearch" placeholder="请输入搜索关键词" @search="onSearch" /></form> -->
        <van-picker show-toolbar :columns="options" @confirm="confirmName" @cancel="showEditName = false">
          <div slot="columns-top">
            <form action="#"><van-search v-model="valueSearch" placeholder="请输入搜索关键词" @search="onSearch" /></form>
          </div>
        </van-picker>
      </div>
    </van-popup>
    <!-- <van-dialog v-model="showEditName" title="修改上级" show-cancel-button class="logo-out-dialog face-dialog" @confirm="handelrLogoOut" :before-close="closebefore" confirm-button-color="#0052d9">
    
    </van-dialog> -->
  </div>

</template>

<script>
import {
  getClassStatusItem,
  logoOutFace,
  getFaceCancelAcct,
  faceClassRegistApply,
  getBaseEmpInfo
} from 'config/api.conf'
import { mapState } from 'vuex'
import Empty from '@/views/user/components/empty.vue'
import { Toast } from 'vant'
export default {
  components: {
    Empty
  },
  props: ['registered', 'courseData', 'classLists'],
  data() {
    return {
      closeMoer: false,
      widthSize: 4,
      currentClassItem: {},
      dateCurrent: '全部',
      isMoreDate: true,
      showMoerList: false,
      reasonRadio: 0,
      partClassDateSelect: [],

      parentStaffInfo: {
        StaffID: '',
        StaffName: ''
      },
      staffId: '',
      logoOutReason: [
        {
          id: 0,
          reason: '与重要工作冲突(请输入此工作具体信息)',
          other: ''
        },
        {
          id: 1,
          reason: '临时有出差',
          other: ''
        },
        {
          id: 2,
          reason: '休假,不在公司',
          other: ''
        },
        {
          id: 3,
          reason: '其他',
          other: ''
        }
      ],
      showApply: false,
      showCancel: false,
      // 当前班级相关信息
      curr_class: {
        quater: 0, // 所属季度
        amt: 0, // 报名所需分数
        idp_point: 0, // 所属季度idp分数
        isEnough: true, // 分数是否足够抵扣
        surplus: 0, // 所属季度可用积分
        is_ldi_course: false, // 是否是ldi课程
        class_level: 0 // 课程级别 1=>公司级,2=>部门级,3=>bg级
      },
      // IDP账号积分数据
      grantAccountData: [],
      showEditName: false,
      valueSearch: '',
      currentNameInfo: {},
      options: [],
      loading: false
    }
  },
  watch: {},
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo
    }),
    emptyShow() {
      return !this.classLists || !this.classLists.length
    },
    teachingType() {
      return (val) => {
        const { teaching_type, location, city } = val
        // 1 线下面试 2 在线授课 3 网络研讨会',
        const typeObj = {
          1: location || city,
          2: '腾讯会议在线授课，报名成功后自动推送授课链接',
          3: '腾讯会议在线授课，报名成功后自动推送授课链接'
        }
        return typeObj[teaching_type]
      }
    },
    surplusText() {
      return (item) => {
        if (this.statusBtn(item) === 1 && this.surplus_count(item) !== 0) {
          return `剩余名额：${this.surplus_count(item)}`
        } else if (
          this.statusBtn(item) === 2 ||
          (this.statusBtn(item) === 1 && this.surplus_count(item) === 0)
        ) {
          return `排队人数：${this.wait_queen_count(item)}`
        } else if (this.statusBtn(item) === 3) {
          return `排队人数：${this.wait_queen_count(item)}`
        } else if (this.statusBtn(item) === 4) {
          return '已截止报名'
        } else if (this.statusBtn(item) === 5) {
          return '点击已报名可取消报名'
        } else if (this.statusBtn(item) === 6) {
          return '已超过注销截止时间'
        } else if (this.statusBtn(item) === 7) {
          return '待上级审批'
        }
      }
    },
    // 按钮状态 1-可报名 2-已报满-等待列表 3-已加入等待列表 4-截至报名 5-已报名-可注销 6-已报名-不可注销
    statusBtn() {
      return (val) => {
        const { status } = val.regist_type
        return status || 1
      }
    },
    // 剩余名额
    surplus_count() {
      return (val) => {
        const { surplus_count } = val.regist_type
        return surplus_count || 0
      }
    },
    //  等待人数
    wait_queen_count() {
      return (val) => {
        const { wait_queen_count } = val.regist_type
        return wait_queen_count || 0
      }
    },
    // 当前项等待人数
    wait_queen_countCur() {
      let wait_queen_count = 0
      if (this.currentClassItem.regist_type) {
        wait_queen_count = this.currentClassItem.regist_type.wait_queen_count
      }
      return wait_queen_count || 0
    },
    // 报名需要分数
    pointsRange() {
      let est_dur = 200
      if (this.currentClassItem.est_dur) {
        est_dur = this.currentClassItem.est_dur / 60 <= 3.5 ? 200 : 400
      }
      return est_dur
    },
    formate1() {
      return (val) => {
        val = new Date(val)
        let h = val.getHours()
        let ms = val.getMinutes()
        // 判断时间大于18:45
        if (h > 18 || (h === 18 && ms >= 45)) {
          return true
        } else {
          return false
        }
      }
    },
    is_need_idp_score() {
      return this.currentClassItem.is_need_idp_score
    },
    showCity() {
      const { city, work_city, teaching_type } = this.currentClassItem
      return city !== work_city && teaching_type === 1
    },
    dtTable() {
      return (type, name, item) => {
        if (type === 'eid') {
          return `element_${item ? item.class_id : ''}_${name}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '面授课详情页-新版',
            container: '',
            click_type: 'button',
            content_type: '班级',
            content_id: item ? item.class_id : '',
            content_name: item ? item.class_name : '',
            button_name: name,
            terminal: 'PC'
          })
        } else {
          return `area_${item ? item.class_id : ''}_${name}`
        }
      }
    },
    dtTableList() {
      return (type, item) => {
        if (type === 'eid') {
          return `element_${this.course_id}_${item.class_id}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '面授课详情页-新版',
            container: '',
            click_type: 'data',
            content_type: '班级',
            content_id: item.class_id,
            content_name: item.class_name,
            terminal: 'PC'
          })
        } else {
          return `area_${this.course_id}_${item.class_id}`
        }
      }
    }
  },
  mounted() {
    this.getFaceCancelAcct()
    window.addEventListener('scroll', this.scrollEvent, true)
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.scrollEvent)
  },
  methods: {
    confirmName(value) {
      console.log(value, 'value')
      if (!value || !value.staff_id) {
        Toast('请选择人员')
        return false
      }
      this.currentNameInfo = value
      this.parentStaffInfo.StaffID = this.currentNameInfo.staff_id
      this.parentStaffInfo.StaffName = this.currentNameInfo.staff_name
      this.currentClassItem.parent_name = this.currentNameInfo.staff_full_name
      this.showEditName = false
    },
    onSearch() {
      getBaseEmpInfo({ staff_name: this.valueSearch }).then((res) => {
        console.log('人员列表', res)
        this.loading = false
        if (!res || res.length === 0) {
          this.options = [{ text: '暂无数据' }]
          return false
        }
        
        this.options = res.map((item) => {
          return {
            ...item,
            text: item.staff_full_name
          }
        })
      })
      console.log(this.valueSearch, '搜索搜索搜索')
    },
    handlerEditName() {
      this.showEditName = true
    },
    remoteMethod(query) {
      if (query !== '') {
        this.loading = true
      } else {
        this.options = []
      }
    },
    handlerChangeStaff(val) {
      const { StaffID, StaffName } = val
      this.parentStaffInfo = { StaffID, StaffName }
      console.log(val, '改变人员信息')
    },
    scrollEvent(e) {
      // 获取当前滚动位置
      let scrollTop = window.scrollTop || document.documentElement.scrollTop
      // 滚动高度 250是视频的高度
      if (scrollTop > 50) {
        // 向下滚动，修改多余高度
        this.closeMoer = true
      } else {
        // 向上滚动
      }
    },
    clickMoer() {
      this.showMoerList = !this.showMoerList
    },
    // 积分接口
    async getFaceCancelAcct() {
      try {
        this.grantAccountData = await getFaceCancelAcct()
      } catch (err) {
      }
    },
    // 初始化当前班级信息
    currClassInfo(data) {
      let date_saf = data.start_time
      date_saf = date_saf.replace(/-/g, '/')
      let date = new Date(date_saf)

      let year = date.getFullYear(date)
      let m = date.getMonth(date) + 1
      let quater = Math.ceil(parseInt(m) / 3)
      let curr_quater = year + 'S' + quater
      let surplus = 0
      let amt = data.est_dur / 60 <= 3.5 ? 200 : 400

      // 可用积分 = 当前季度idp专项积分>0 ?当季idp积分+通用积分:通用积分
      const idpAccount = this.grantAccountData.filter(
        (item) => item.type === 'idp'
      )[0] // 当前季度idp专项积分
      console.log(idpAccount, 'idpAccountidpAccountidpAccountidpAccount')
      const commonAccount = this.grantAccountData.filter(
        (item) => item.type === 'common'
      )[0] // 通用积分
      console.log(commonAccount, 'commonAccountcommonAccountcommonAccount')
      surplus = commonAccount.point_total
      if (
        idpAccount?.point_season &&
        idpAccount?.point_season[curr_quater] > 0
      ) {
        surplus =
          idpAccount.point_season[curr_quater] + commonAccount.point_total
      }
      this.curr_class.quater = quater
      this.curr_class.amt = amt
      this.curr_class.idp_point = idpAccount?.point_season[curr_quater] || 0
      this.curr_class.isEnough = surplus >= amt
      this.curr_class.surplus = surplus
      this.curr_class.is_ldi_course =
        data.pdi_sub_level === 2 && data.class_level === 1
      this.curr_class.class_level = data.class_level
    },
    // 处理报名逻辑
    handlerApply(val) {
      if (this.registered) return
      this.currentClassItem = JSON.parse(JSON.stringify(val))
      console.log(this.currentClassItem, 'currentClassItemcurrentClassItem')
      this.showApply = true
      const {
        is_need_idp_score,
        idp_courses_count,
        black_list,
        need_appovel,
        parent_name,
        parent_staff_id
      } = val
      if (is_need_idp_score) {
        // 提示考勤异常累积达4次
        if (Number(black_list) === 1) {
          let nowDate = new Date().toLocaleDateString()
          this.$confirm(
            `1、为了更有效地利用培训资源，员工培训考勤异常累积达4次，下季度将限制报名公司级课程；<br />2、截至 ${nowDate},您培训考勤异常次数已达4次，本季度不能报名公司级课程，下季度恢复报名权限。<br />如有疑问请咨询v_shannwang(王阳) !`,
            `提示`,
            {
              confirmButtonText: '确定',
              customClass: 'confirm-center-face',
              showCancelButton: false,
              dangerouslyUseHTMLString: true,
              type: 'warning',
              center: true
            }
          ).then(() => {})
          return false
        } else {
          if (idp_courses_count >= 5) {
            this.$confirm(
              `为保证学习效果，建议每季度报名IDP课程不超过<strong style="color:red">5</strong>门，您现在已报名<strong style="color:red">5</strong>门，请下季度再报名其他课程！`,
              `提示`,
              {
                confirmButtonText: '确定',
                customClass: 'confirm-center-face',
                showCancelButton: false,
                dangerouslyUseHTMLString: true,
                type: 'warning',
                center: true
              }
            ).then(() => {})
            return false
          }
        }
      }
      // 如果需要审批
      if (need_appovel) {
        this.parentStaffInfo.StaffID = parent_staff_id
        this.parentStaffInfo.StaffName = parent_name
      }
      this.currClassInfo(val)
    },
    handelrConfirmApply() {
      const { need_appovel, class_id } = this.currentClassItem
      let params = {
        class_id: class_id
      }
      // 需要审核
      if (need_appovel) {
        if (!this.parentStaffInfo.StaffID) {
          Toast.fail('请输入直接上级!')
          return false
        } else if (this.staffId + '' === this.userInfo.staff_id + '') {
          Toast.fail('直接上级不能为自己!')
          return false
        }
        params.leader = {
          user_name: this.parentStaffInfo.StaffName,
          user_id: this.parentStaffInfo.StaffID
        }
      }
      faceClassRegistApply(params).then((res) => {
        console.log(res, '成功报名')
        let message = this.currentClassItem.need_appovel
          ? '提交成功,该记录正等待直接上司的审批, 请留意邮件提醒'
          : '报名成功'
        if (this.currentClassItem.regist_type.status === 2) {
          Toast.success('成功报名加入等待列表')
        } else {
          Toast.success(message)
        }
        this.$parent.actClassList()
        this.showApply = false
        // this.refurbishStatus()
      })
    },
    handelrOpenLogoOut(val) {
      this.showCancel = true
      this.currentClassItem = val
    },
    closebefore(action, done) {
      if (action === 'confirm') {
        return done(false)
      } else {
        return done()
      }
    },
    // 注销
    handelrLogoOut() {
      if (
        [0, 3].includes(this.reasonRadio) &&
        !this.logoOutReason[this.reasonRadio].other.trim()
      ) {
        Toast('请输入注销原因')
        return false
      }
      let parmas = {
        class_id: this.currentClassItem.class_id,
        reason: `${this.logoOutReason[this.reasonRadio].reason}${
          this.logoOutReason[this.reasonRadio].other
        }`
      }
      logoOutFace(parmas).then((res) => {
        this.showCancel = false
        // 需要积分打开积分弹出提示
        if (this.is_need_idp_score) {
          Toast({
            message: `注销成功\n报名课程消耗的${this.pointsRange}积分已退还个人积分账户`,
            icon: 'success',
            duration: 3000,
            size: 'large'
          })
        } else {
          Toast({
            message: '注销成功',
            icon: 'success'
          })
        }
        this.$parent.actClassList()
      })
    },
    // 刷新状态
    refurbishStatus() {
      getClassStatusItem({ class_id: this.currentClassItem.class_id }).then(
        (res) => {
          this.classLists.forEach((item) => {
            if (item.class_id === this.currentClassItem.class_id) {
              item.regist_type = res
            }
          })
          console.log('获取class的状态', res)
        }
      )
    },
    handlerSetSubscribe() {
      this.$parent.handlerSetSubscribe()
    },
    handelrOpened() {
      // this.$nextTick(() => {
      //   // 解决IOS 输入中文，无法触发el-select remote-method方法
      //   console.log(this.$refs, 'mounted')
      //   const { selectInfo } = this.$refs
      //   const input = selectInfo.$el.querySelector('.el-input__inner')
      //   // 监听事件，查询下拉框
      //   input.addEventListener('compositionend', (e) => {
      //     this.getIntervieweeList(e.target.value)
      //   })
      // })
    },
    // 解决ios手机无法调用键盘
    cancelReadOnly(onOff) {
      // if (onOff) { // 打开下拉框 显示可清空按钮
      //   this.showClose = true
      // }
      this.$nextTick(() => {
        if (!onOff) {
          // ios 手机有延迟问题
          setTimeout(() => {
            const { selectInfo } = this.$refs
            const input = selectInfo.$el.querySelector('.el-input__inner')
            input.removeAttribute('readonly')
          }, 200)
        }
      })
    }
  }
}
</script>
<style>
/* 增加Toast的z-index值 */
.van-toast {
  z-index: 9999 !important; /* 设置一个较高的值，确保它在Dialog之上 */
}
</style>
<style lang="less" scoped>
.edit-name-dialog {
  .title {
    font-size: 16px;
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    span {
      padding: 16px;
    }
  }
  .no-data {
    height: 264px;
    text-align: center;
    line-height: 264px;
    color: rgba(0, 0, 0, 0.4);
  }
}
.class-list-te {
  position: relative;
  .class-content {
    max-height: 340px;
    padding: 0 12px;
    background-color: #fff;
    overflow: hidden;
    &-item {
      padding: 12px 0 16px 0;
      border-bottom: 1px solid #eee;
      .item-title {
        color: #00000099;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        &_b {
          color: #00000066;
        }
      }
      .item-tips {
        margin: 10px 0;
        span {
          padding: 3px 6px;
          font-size: 10px;
          line-height: 18px;
          color: #777;
          margin-right: 8px;
          border-radius: 4px;
          background: #f5f7fa;
        }
      }

      .item-class-info {
        position: relative;
        padding: 12px;
        border-radius: 8px;
        background: linear-gradient(90deg, #f9f9f9 0%, #f1f5fd 97.29%);
        font-size: 11px;
        &_left {
          .info-lable {
            display: flex;
            align-items: center;
            line-height: 20px;
            margin-bottom: 8px;
            .hidden-text {
              flex: 1; /* 或者具体的flex值，确保子元素能够收缩 */
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }
            .icon {
              display: block;
              width: 20px;
              height: 20px;
              margin-right: 12px;
            }
            .icon-time {
              background: url('~@/assets/img/mobile/time-class.png') no-repeat
                center / cover;
            }
            .icon-name {
              background: url('~@/assets/img/teacher-class.png') no-repeat
                center / cover;
            }
            .icon-city {
              background: url('~@/assets/img/local-class.png') no-repeat center /
                cover;
            }
            .surplus-icon {
              background: url('~@/assets/img/mobile/surplus-icon.png') no-repeat
                center / cover;
            }
          }
          // .info-lable-hidden {
          //   width: 100%;
          //   overflow: hidden;
          //   text-overflow: ellipsis;
          //   white-space: nowrap;
          // }
          .info-lable:last-child {
            margin-bottom: 0;
          }
        }
        &_right {
          margin-top: 12px;
          .status-button {
            min-width: 80px;
            padding: 7px 12px;
            height: 56px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            cursor: pointer;
            .button-text {
              font-size: 14px;
              font-style: normal;
              font-weight: 500;
              line-height: 22px;
            }
            .button-info {
              font-family: 'PingFang SC';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 16px;
            }
          }
          .status1 {
            background-color: #0052d9;
            color: #fff;
          }
          .status2 {
            color: #333333;
            background-color: #fff;
            border-color: #fff;
            .button-info {
              color: #777777;
            }
          }
          .status3 {
            color: #bcbec2;
            background-color: #c8c9cc;
            border-color: #c8c9cc;
            cursor: not-allowed;
          }
          .status4 {
            color: #fff;
            background-color: #f56c6c;
          }
          .registered-btn {
            color: #fff;
            background-color: #c8c9cc;
            border-color: #c8c9cc;
            cursor: not-allowed;
          }
        }
      }
      .item-class-info:hover {
        background: linear-gradient(90deg, #f9f9f9 0%, #f1f5fd 97.29%);
      }
    }
    &-item:last-child {
      border-bottom: none;
    }
  }
  .class-content-moer {
    max-height: unset;
  }
  .show-more-brn {
    display: flex;
    justify-content: center;
    position: absolute;
    bottom: 0;
    width: 100%;
    height: 73px;
    background-image: linear-gradient(
      0deg,
      #fff 0,
      #fff 30%,
      rgba(255, 255, 255, 0.8) 65%,
      rgba(255, 255, 255, 0) 100%
    );
    .show-more-brn-text {
      width: 102px;
      height: 28px;
      line-height: 28px;
      font-size: 11px;
      font-weight: 500;
      border-radius: 36px;
      background-color: #fff;
      box-shadow: 0 0 8px 0 #a0a0a040;
      text-align: center;
      margin-top: 30px;
      color: #0052d9;
      .icon {
        font-weight: 500;
      }
    }
  }
  .show-more-brn-moer {
    position: relative;
    height: 50px;
    background-color: #fff;
    .show-more-brn-text {
      margin: 10px 0;
    }
  }
  .colse-moer-100 {
    height: 150px;
  }
}
.logo-out-dialog {
  /deep/.van-dialog__header {
    padding-top: 12px;
  }
  .red-fw {
    color: #fe3733;
    font-weight: 600;
  }
  .face-dialog-flex {
    display: flex;
    .right {
      padding: 12px 12px 0;
      .tips {
        font-size: 14px;
        line-height: 20px;
      }
    }
    .el-icon-warning {
      color: #e6a23c;
      font-size: 15px;
    }
    .label_form {
      margin: 10px 0;
      font-size: 14px;
    }
  }
  .apply-dialog {
    line-height: 20px;
    font-size: 12px;
    color: #00000099;
    .right {
      flex: 1;
      padding: 8px 12px 0;
    }
    ol {
      list-style: decimal !important;
      padding-left: 20px;
      li {
        list-style: decimal !important;
      }
    }
    .manage {
      width: 100%;
      border-top: 1px solid #eee;
      .title {
        font-size: 12px;
        font-weight: 500;
        color: #000;
        padding: 10px 0;
      }
    }
    .comfir-tips {
      color: #000000e6;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 500;
      line-height: 20px;
      padding: 10px 0;
    }
    .managerName {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      .managerName_right {
        flex: 1;
      }
      .edit-name {
        color: #0052d9;
        margin-left: 12px;
      }
    }
    .apply-type {
      padding: 8px 12px;
      background: #f5f7f9;
      padding-top: 8px;
      text-align: left;
      margin-top: 8px;
      color: #000000e6;
    }
    .p0 {
      color: #ff8200;
      display: block;
      cursor: pointer;
    }
    .p1 {
      display: block;
      cursor: pointer;
      .details-sapn {
        margin-right: 10px;
      }
      span {
        a {
          color: #3464e0;
        }
      }
    }
  }
  .Reason-sel {
    margin: 10px 0;
    // font-size: 16px;
    // font-weight: 500;
  }
  .redio-content {
    margin: 10px 0;
  }
}
</style>
