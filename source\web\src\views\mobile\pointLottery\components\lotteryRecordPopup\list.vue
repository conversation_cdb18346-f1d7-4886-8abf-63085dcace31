<template>
  <div class="record-list">
    <van-tabs v-model="active" color="#0052D9" title-active-color="#0052D9" line-width="16px" @change="tabChange">
      <van-tab title="中奖奖品">
        <div class="list-content list-1">
          <prize-item v-for="(item, index) in pageData.list" :key="index" :data="item" @goDetails="openPrizeDetails(item)"></prize-item>
          <van-loading v-if="loading">加载中...</van-loading>
          <div class="finished-text" v-if="finished">{{ pageData.list.length > 0 ? '没有更多了' : '暂无数据' }}</div>
        </div>
      </van-tab>
      <van-tab title="全部记录">
        <div class="list-content list-2">
          <prize-item v-for="(item, index) in pageData.list" :key="index" :data="item" @goDetails="openPrizeDetails(item)"></prize-item>
          <van-loading v-if="loading">加载中...</van-loading>
          <div class="finished-text" v-if="finished">{{ pageData.list.length > 0 ? '没有更多了' : '暂无数据' }}</div>
        </div>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script>
import { 
  Tabs,
  Loading
} from 'vant'
import prizeItem from './prizeItem.vue'
import { getLuckDrawHistoryAPI } from '@/config/lottery.api.conf.js'

export default {
  props: {
    value: {
      type: Boolean,
      default: true,
      require: true
    }
  },
  components: {
    [Tabs.name]: Tabs,
    [Loading.name]: Loading,
    prizeItem
  },
  data() {
    return {
      active: 0,
      pageData: {
        list: [],
        current: 1,
        size: 10,
        total: 0
      },
      loading: true,
      finished: false,
      initTypeAll: false
    }
  },
  created() {
    this.getList()
  },
  mounted() {
    this.$nextTick(() => {
      this.handlerScroll('.list-1')
    })
  },
  methods: {
    tabChange(e) {
      this.loading = false
      this.finished = false
      this.pageData.current = 1
      this.pageData.list = []
      this.getList()
    },
    getList() {
      getLuckDrawHistoryAPI({
        activity_id: this.$route.query.activity_id,
        lotteryNo: this.active === 0 ? 1 : null,
        current: this.pageData.current,
        size: this.pageData.size
      }).then(res => {
        this.loading = false
        this.pageData.total = res.total
        this.pageData.list = this.pageData.list.concat(res.records)
        if (this.pageData.total <= this.pageData.list.length) {
          this.finished = true
        }
        if (!this.initTypeAll && this.active === 1) {
          this.initTypeAll = true
          this.$nextTick(() => {
            this.handlerScroll('.list-2')
          })
        }
      }).catch(() => {
        this.loading = false
        this.finished = false
      })
    },
    openPrizeDetails(d) {
      this.$emit('prizeDetails', d)
    },
    handlerScroll(el) {
      console.log('初始化')
      let that = this
      let fun = that.debounce(e => {
        // 距离顶部
        let scrollTop = e.target.scrollTop
        // 可视区域
        let clientHeight = e.target.clientHeight
        // 滚动条总高度
        let scrollHeight = e.target.scrollHeight
        // 当距离顶部的值加上可视区域的值大于等于总高度，则滚动条触底
        if (Math.ceil(scrollTop + clientHeight) >= (scrollHeight - 50)) {
          console.log('滚动到底部')
          if (this.finished) return
          if (this.pageData.total > this.pageData.list.length && !this.loading) {
            this.loading = true
            ++this.pageData.current
            this.getList()
          }
        }
      }, 300)
      let box = document.querySelector(el)
      box.addEventListener('scroll', function (e) {
        fun(e)
      })
    },
    debounce(handle, delay = 500) {
      let timer = null
      return function () {
        let _self = this
        let _args = arguments
        clearTimeout(timer)
        timer = setTimeout(function() {
          handle.apply(_self, _args)
        }, delay)
      }
    }
  },
  beforeDestroy() {
  }
}
</script>

<style lang='less' scoped>
  .record-list {
    height: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    :deep(.van-tabs) {
      height: 100%;
      display: flex;
      flex-direction: column;
      .van-tabs__wrap {
        border-bottom: 1px solid #ededed;
        flex-shrink: 0;
      }
      .van-tabs__content {
        flex: 1;
        overflow: hidden;
        .van-tab__pane {
          height: 100%;
        }
      }
    }
    .list-content {
      height: 100%;
      overflow-y: auto;
      padding: 20px 16px 40px;
      .van-loading {
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .finished-text {
        color: #969799;
        font-size: 14px;
        text-align: center;
      }
    }
  }
</style>
