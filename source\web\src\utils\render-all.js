/**
 * 全量加载
 */
import { render } from 'sdc-vue'
import Element from 'element-ui'
import SDCWeb from 'sdc-webui'
// 应用配置
import App from 'views/app'
import router from '../router'
import store from '../store'
import env from '../config/env.conf'
// 插件配置
import 'plugins/svg/'
import 'plugins/styles'

export default render(App, {
  router,
  store,
  env,
  theme: 'skyblue',
  lazy: {
    preLoad: 1,
    loading: require('assets/img/loading-spinning-bubbles.svg')
  },
  init: Vue => {
    Vue.use(Element)
    Vue.use(SDCWeb)
  }
})
