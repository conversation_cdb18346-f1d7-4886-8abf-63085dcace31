<template>
  <div class="teaching-type-settings">
    <el-form-item label="活动形式" prop="teaching_type">
      <div class="offline">
        <div class="flex-row h-normal">
          <el-checkbox class="mr-20" v-model="offline" @change="handleTeachingTypeChange($event, 'offline')"
            :disabled="disabled">线下授课</el-checkbox>
          <el-radio-group v-model="form.is_limit_student_count" :disabled="disabled" @change="handleMeetingCreateTypeChange('offline')">
            <el-radio v-for="item in numberLimitTypes" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
          <template v-if="form.is_limit_student_count === 1">
            <el-input class="w-mini ml-12 mr-24" v-model="form.max_student_count"
              @blur="positiveIntegerUpdateFilteredValue('max_student_count')" placeholder="请输入人数限制" :minlength="1"
              :maxlength="1000" :disabled="disabled"></el-input>
            <el-checkbox v-model="form.allow_waiting_list" :true-label="1" :false-label="0"
              :disabled="disabled">报名已满时允许排队候补</el-checkbox>
          </template>
        </div>
        <div class="flex-row h-normal mt-12 ml-102" v-if="offline">
          <el-select v-model="form.city" placeholder="请选择活动所在城市" class="w-normal" :disabled="disabled">
            <el-option v-for="item in cityOptions" :key="item.item_id" :label="item.item_name"
              :value="item.item_name"></el-option>
          </el-select>
          <el-input class="w-medium ml-12 mr-16" v-model="form.location"
            :placeholder="form.city ? '请填写活动详情地址' : '请先选择城市'" :disabled="disabled || (form.city ? false : true)"
            size="small"></el-input>
          <el-button type="text" size="small" @click="toLink('http://meeting.woa.com')"
            :disabled="disabled">预约会议室</el-button>
        </div>
      </div>
      <div class="online mt-12">
        <div class="flex-row h-normal">
          <el-checkbox class="mr-20" v-model="online" @change="handleTeachingTypeChange($event, 'online')"
            :disabled="disabled">腾讯会议</el-checkbox>
          <el-radio-group v-model="form.meeting_create_type" :disabled="disabled" @change="handleMeetingCreateTypeChange('online')">
            <el-radio v-for="item in meetingTypes" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
          <el-button class="ml-12" type="text" size="small" @click="openAdvancedSetting"
            :disabled="disabled">进阶设置</el-button>
          <div class="tips ml-12">
            <i class="el-icon-warning color-orange ml-12 mr-6"></i>
            <span class="color-fail">关联会议需是自己创建的会议</span>
          </div>
        </div>
        <div class="flex-row h-normal mt-12 ml-102 mb-12" v-if="form.meeting_create_type === 2">
          <div class="meeting-input-box">
            <el-input class="w-normal" ref="codeMeetingInput" v-model="associationMeetingInfo.meeting_code"
              placeholder="会议号：" size="small" :disabled="disabled" @blur="handleMeetingBlur('code')"
              @focus="handleMeetingFocus('code')"></el-input>
            <div class="display-box" :class="{ 'disabled-box': disabled }"
              v-if="codeDisplayBoxVisible && associationMeetingInfo.meeting_code" @click="focusMeetingInput('code')">
              会议号：
              <span>{{ associationMeetingInfo.meeting_code }}</span>
            </div>
          </div>
          <div class="meeting-input-box ml-12">
            <el-input class="w-medium" ref="nameMeetingInput" v-model="associationMeetingInfo.meeting_creator"
              placeholder="创建人：" size="small" :disabled="disabled" @blur="handleMeetingBlur('name')"
              @focus="handleMeetingFocus('name')"></el-input>
            <div class="display-box meeting-name" :class="{ 'disabled-box': disabled }"
              v-if="nameDisplayBoxVisible && associationMeetingInfo.meeting_creator" @click="focusMeetingInput('name')">
              创建人：
              <span>{{ associationMeetingInfo.meeting_creator }}</span>
            </div>
          </div>
          <el-button type="text" class="ml-12" size="small" @click="validateMeetingCode"
            :disabled="disabled">会议信息校验</el-button>
        </div>
        <div class="meeting-info mt-4 ml-102" v-if="automaticCreationShow">
          <p>已自动创建会议，会议信息如下</p>
          <p>
            <span class="item mr-32">
              会议号：
              <span class="color-blue">{{ automaticMeetingInfo?.meeting_code || '-' }}</span>
            </span>
            <span class="item">
              主持人：
              <span class="color-block">{{ automaticMeetingInfo?.meeting_creator || '-' }}</span>
            </span>
          </p>
          <p>
            会议地址：
            <span>
              <el-link class="a-link mr-12" :href="automaticMeetingInfo?.meeting_url" type="primary" target="_blank">{{
            automaticMeetingInfo?.meeting_url }}</el-link>
              <el-button class="text-btn" type="text" size="small"
                @click="copyToClipboard(automaticMeetingInfo?.meeting_url)">点击复制</el-button>
            </span>
          </p>
        </div>
        <div class="meeting-info mt-4 ml-102" v-if="associationMeetingShow">
          <p>{{ relatedMeetings ? '关联会议信息如下' : '已关联会议，会议信息如下' }}</p>
          <p>
            <span class="item mr-32">
              会议号：
              <span class="color-blue">{{ associationMeetingInfo?.meeting_code || '-' }}</span>
            </span>
            <span class="item">
              主持人：
              <span class="color-block">{{ associationMeetingInfo?.meeting_creator || '-' }}</span>
            </span>
          </p>
          <p>
            会议时间：<span class="color-block">{{ associationMeetingInfo?.start_time + ' - ' + associationMeetingInfo?.end_time }}</span>
          </p>
          <p>
            会议地址：
            <span>
              <el-link class="a-link mr-12" :href="associationMeetingInfo?.meeting_url" type="primary" target="_blank">{{
            associationMeetingInfo?.meeting_url }}</el-link>
              <el-button class="text-btn" type="text" size="small"
                @click="copyToClipboard(associationMeetingInfo.meeting_url)">点击复制</el-button>
            </span>
          </p>
        </div>
        <div class="tips ml-90">
          <i class="el-icon-warning color-orange ml-12 mr-6"></i>
          <span class="color-fail">如需修改，请修改后及时告知已报名的学员</span>
        </div>
      </div>
    </el-form-item>

    <!-- 进阶设置 -->
    <advanced-settings-popup :visible.sync="advancedSettingsVisible" :formData="advancedSettingsForm"
      @confirm="advancedSettingConfirm"></advanced-settings-popup>
  </div>
</template>

<script>
import advancedSettingsPopup from '@/views/manage/classroom/activity/edit/components/advancedSettingsPopup.vue'
import {
  operationInfo,
  checkMeetingCodeApi
} from '@/config/classroom.api.conf.js'
import { copyToClipboard } from '@/utils/tools.js'

export default {
  name: 'teachingTypeSettings',
  components: {
    advancedSettingsPopup
  },
  props: {
    formData: {
      type: Object,
      default: () => ({})
    },
    activityData: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isEdit: false,
      isInitialized: false,
      numberLimitTypes: [
        { label: '无限制人数', value: 0 },
        { label: '限制报名人数', value: 1 }
      ],
      meetingTypes: [
        { label: '系统自动创建', value: 1 },
        { label: '关联已有会议号', value: 2 }
      ],

      cityOptions: [],
      offline: true, // 活动形式 - 线下授课
      online: false, // 活动形式 - 腾讯会议
      form: {
        teaching_type: '1', // 活动形式
        city: '', // 活动形式1： 活动地点 - 城市
        location: '', // 活动形式1： 活动地点 - 详细地址
        meeting_create_type: 0, // 活动形式2： 会议创建方式
        refuse_join_type: 1, // 活动形式2：参会人员
        enable_meeting_record: true, // 活动形式2：会议录制
        is_limit_student_count: 0, // 人数限制
        max_student_count: 20, // 人数限制 - 报名人数
        allow_waiting_list: 0, // 是否允许等待队列
        meeting_info: {
          meeting_code: '', // 会议编号
          meeting_creator: '' // 会议创建人
        }
      },
      automaticMeetingInfo: { // 自动创建会议信息
        meeting_code: '',
        meeting_creator: ''
      },
      associationMeetingInfo: { // 关联会议信息
        meeting_code: '',
        meeting_creator: ''
      },
      temporarilyStore: {
        is_limit_student_count: -1,
        meeting_create_type: 0
      },
      // 进阶设置
      advancedSettingsVisible: false,
      advancedSettingsForm: {
        refuse_join_type: 1,
        enable_meeting_record: true
      },
      // 会议
      codeDisplayBoxVisible: true,
      nameDisplayBoxVisible: true,
      relatedMeetings: false
    }
  },
  watch: {
    activityData: {
      handler(val) {
        if (!val || !Object.keys(val).length || this.isInitialized) return

        this.isEdit = true

        // 解构所需属性
        const {
          teaching_type,
          city,
          location,
          meeting_create_type,
          refuse_join_type,
          enable_meeting_record,
          is_limit_student_count,
          max_student_count,
          allow_waiting_list,
          meeting_info
        } = val

        // 处理教学类型
        if (teaching_type) {
          const teachingTypes = teaching_type.split(';')
          this.form.teaching_type = teaching_type
          this.offline = teachingTypes.includes('1')
          this.online = teachingTypes.includes('2')
        }

        // 设置基本表单数据
        Object.assign(this.form, {
          city,
          location,
          meeting_create_type,
          refuse_join_type,
          enable_meeting_record,
          is_limit_student_count: is_limit_student_count ? 1 : 0,
          max_student_count,
          allow_waiting_list: allow_waiting_list ? 1 : 0
        })

        // 处理会议信息
        if (meeting_info?.meeting_code) {
          this.form.meeting_info = meeting_info
          if (meeting_create_type === 1) {
            this.automaticMeetingInfo = meeting_info
          } else if (meeting_create_type === 2) {
            this.associationMeetingInfo = meeting_info
          }
        }

        // 标记初始化完成
        setTimeout(() => {
          this.isInitialized = true
        }, 1000)
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    // 自动创建会议信息显示
    automaticCreationShow() {
      const { meeting_create_type } = this.form
      const { meeting_code } = this.automaticMeetingInfo
      if (this.isEdit && meeting_create_type === 1 && meeting_code) {
        return true
      }
      return false
    },
    // 关联会议信息显示
    associationMeetingShow() {
      const { meeting_create_type } = this.form
      const { meeting_url } = this.associationMeetingInfo
      if ((this.isEdit && meeting_create_type === 2 && meeting_url) || (this.relatedMeetings && meeting_create_type === 2)) {
        return true
      }
      return false
    }
  },
  created() {
    this.getCityList()
  },
  mounted() { },
  beforeDestroy() { },
  methods: {
    getCityList() {
      operationInfo({ key: 'Class_City' }).then(res => {
        this.cityOptions = res || []
      })
    },
    // 活动形式
    handleTeachingTypeChange() {
      let value = ''

      // 已选择线下授课
      if (this.offline) {
        value = '1'

        if (this.form.is_limit_student_count < 0) {
          this.form.is_limit_student_count =
            this.temporarilyStore.is_limit_student_count >= 0 
              ? this.temporarilyStore.is_limit_student_count 
              : 0
        }
      } else {
        this.temporarilyStore.is_limit_student_count = this.form.is_limit_student_count
        this.form.is_limit_student_count = -1
      }

      // 已选择腾讯会议
      if (this.online) {
        value = value ? `${value};2` : '2'
        if (this.form.meeting_create_type <= 0) {
          this.form.meeting_create_type = this.temporarilyStore.meeting_create_type || 1
        }
      } else {
        this.temporarilyStore.meeting_create_type = this.form.meeting_create_type
        this.form.meeting_create_type = 0
      }
      this.form.teaching_type = value
      // 只在初始化完成后触发验证
      if (this.isInitialized) {
        this.$nextTick(() => {
          this.$emit('validate', 'teaching_type')
        })
      }
    },
    handleMeetingCreateTypeChange(name) {
      if (name === 'online') {
        const { meeting_create_type } = this.form
        let meetingData = meeting_create_type === 1 ? { ...this.automaticMeetingInfo } : { ...this.associationMeetingInfo }
        this.form.meeting_info = meetingData
      }
      if (!this[name]) {
        this[name] = true
        this.handleTeachingTypeChange()
      }
    },
    // 打开进阶设置
    openAdvancedSetting() {
      const { refuse_join_type, enable_meeting_record } = this.form
      this.advancedSettingsForm = {
        refuse_join_type,
        enable_meeting_record
      }
      this.advancedSettingsVisible = true
    },
    // 进阶设置数据
    advancedSettingConfirm(e) {
      const { refuse_join_type, enable_meeting_record } = e
      this.form.refuse_join_type = refuse_join_type
      this.form.enable_meeting_record = enable_meeting_record
    },
    validateMeetingCode() {
      const { meeting_code, meeting_creator } = this.associationMeetingInfo
      const { start_time: a_start_time, end_time: a_end_time } = this.formData

      if (!meeting_code || meeting_code.length < 8) {
        this.$message.error('请正确输入会议号')
        return
      }
      if (!meeting_creator) {
        this.$message.error('请输入会议创建人')
        return
      }
      if (!a_start_time || !a_end_time) {
        this.$message.error('请选择活动时间')
        return
      }
      const params = { meeting_code, create_user: meeting_creator }
      checkMeetingCodeApi(params).then(res => {
        this.relatedMeetings = false
        if (!res || !res.meeting_code) {
          this.$message.error('校验失败，请正确填写会议信息')
          return
        }

        // 校验活动时间是否与会议时间一致
        const { start_time, end_time, meeting_creator: meetingCreator } = res
        const userInfo = JSON.parse(sessionStorage.getItem('login_user_dep'))
        const meetingCreatorCheck = meetingCreator.split('(')[0]

        if (userInfo?.emp_name_en !== meetingCreatorCheck) {
          this.handleInvalidCreator()
          return
        } 
        
        if (start_time && end_time && (this.dateSub(start_time, a_start_time) !== 0 || this.dateSub(end_time, a_end_time) !== 0)) {
          this.handleTimeMismatch(res)
          return
        }

        this.relatedMeetings = true
        this.associationMeetingInfo = res
        this.form.meeting_info = res
        this.$message.success('校验成功')
      })
    },
    handleInvalidCreator() {
      this.$message.error('关联的腾讯会议创建人与当前用户不一致')
      this.associationMeetingInfo.meeting_code = ''
      this.associationMeetingInfo.meeting_creator = ''
    },
    handleTimeMismatch(res) {
      this.handlerSubmit(res).then(submitRes => {
        this.associationMeetingInfo = res
        this.form.meeting_info = res
        this.relatedMeetings = true
      })
    },
    handlerSubmit(res) {
      const { start_time, end_time } = res

      return new Promise(resolve => {
        this.$confirm(
          `关联的腾讯会议起止时间和当前活动的起止时间不一致（${start_time} ~ ${end_time}），是否确认提交？`,
          '提示',
          {
            confirmButtonText: '确定',
            showCancelButton: false,
            type: 'warning'
          }
        )
          .then(() => {
            resolve({ confirm: true })
          })
          .catch(() => {
            resolve({ confirm: false })
          })
      })
    },
    handleMeetingFocus(type) {
      this[`${type}DisplayBoxVisible`] = false // 聚焦时显示输入框
    },
    handleMeetingBlur(type) {
      if (type === 'code') {
        let code = this.associationMeetingInfo.meeting_code
        code = code.replace(/-/g, '').trim()
        this.associationMeetingInfo.meeting_code = code
        this.form.meeting_info.meeting_code = code
      } else if (type === 'name') {
        this.form.meeting_info.meeting_creator = this.associationMeetingInfo.meeting_creator
      }
      this[`${type}DisplayBoxVisible`] = true // 失焦时显示文本
    },
    focusMeetingInput(type) {
      if (this.disabled) return
      this[`${type}DisplayBoxVisible`] = false // 点击文本时显示输入框
      this.$refs[`${type}MeetingInput`].focus() // 聚焦输入框
    },
    // 时间比较
    dateSub(date1, date2) {
      const oDate1 = new Date(this.formateDate(date1))
      const oDate2 = new Date(this.formateDate(date2))
      return oDate1.getTime() - oDate2.getTime()
    },
    formateDate(dateStr) {
      return dateStr.replace(/-/g, '/')
    },
    // 正整数校验
    positiveIntegerUpdateFilteredValue(type) {
      let val = Number(this.form[type])
      let isEdit = false

      if (isNaN(val) || val < 1) {
        isEdit = true
        val = 1
      }
      if (val !== parseInt(val)) {
        isEdit = true
        val = parseInt(val)
      }

      if (isEdit) this.form[type] = val
    },
    toLink(src) {
      if (src) window.open(src, '_blank')
    },
    copyToClipboard(text) {
      copyToClipboard(text)
    },
    // 清理腾讯会议信息
    clearMeetingInfo() {
      const { meeting_create_type } = this.form
      if (meeting_create_type === 1) {
        this.associationMeetingInfo = {
          meeting_code: '',
          meeting_creator: ''
        }
        this.form.meeting_info = {}
      } else if (meeting_create_type === 2) {
        this.automaticMeetingInfo = {
          meeting_code: '',
          meeting_creator: ''
        }
      }
      // this.associationMeetingInfo = {
      //   meeting_code: '',
      //   meeting_creator: ''
      // }
      // this.automaticMeetingInfo = {
      //   meeting_code: '',
      //   meeting_creator: ''
      // }
      this.isInitialized = false
      this.relatedMeetings = false
    }
  }
}
</script>

<style lang="less" scoped>
  @import '~assets/css/activity-common.less';

.teaching-type-settings {
  input[aria-hidden='true'] {
    display: none !important;
  }

  :deep(.el-checkbox), :deep(.el-checkbox-button__inner), :deep(.el-radio) {
    font-weight: 400;
  }

  .meeting-input-box {
    display: inline;
    position: relative;

    .display-box {
      width: 180px;
      height: 30px;
      line-height: 30px;
      padding: 0 15px;
      color: #00000099;
      background: #fff;
      border-radius: 4px;
      cursor: text;
      position: absolute;
      top: 1px;
      left: 1px;
      z-index: 1;

      &>span {
        color: #000000e6;
      }
    }
    .meeting-name {
      width: 280px;
    }

    .disabled-box {
      background-color: #f2f2f2;
      border-color: #e4e7ed;
      color: #999;
      cursor: not-allowed;

      &>span {
        color: #999;
      }
    }
  }

  .meeting-info {
    width: 602px;
    padding: 12px;
    border-radius: 4px;
    background: #f9f9f9;
    color: #00000099;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;

    &>p {
      &:not(:last-child) {
        margin-bottom: 12px;
      }
    }
  }

  .a-link {
    line-height: 1;
    text-decoration-line: underline;
  }

  .text-btn {
    padding: 0;
  }

  .flex-row {
    display: flex;
    align-items: center;
  }

  .w-inherit {
    width: 650px;
    flex-shrink: 0;
  }

  .w-big {
    width: 526px;
    flex-shrink: 0;
  }

  .w-normal {
    width: 200px;
    flex-shrink: 0;
  }

  .w-medium {
    width: 314px;
    flex-shrink: 0;
  }

  .w-mini {
    width: 120px;
    flex-shrink: 0;
  }

  .h-normal {
    height: 32px;
  }
}
</style>
