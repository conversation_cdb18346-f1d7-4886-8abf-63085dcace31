<template>
  <div class="active-page">
    <div class="img-content" @click="goTo">
      <van-image class="top-img" :src="topImgUrl">
        <template v-slot:error>
          <img class="top-img" src="../../../../assets/img/mobile/geekBang/active-top.png" />
        </template>
      </van-image>
    </div>
    <div class="content-main">
      <div class="my-card-info">
        <div class="top">
          <span class="title">我的学霸卡:&nbsp;
            <span class="num">{{ cardInfo.balance || 0 }} </span>张
          </span>
          <!-- <span class="date" v-if="cardInfo.balance">最近过期时间 <span>{{timeToDate(cardInfo.recently_expire_date)}}</span> </span> -->
        </div>
        <div class="btns">
          <div class="btn" @click="toMooc">已兑课程</div>
          <div class="btn" @click="openDetailPop">领用详情</div>
          <div class="btn" @click="toRulesDetail">活动规则</div>
        </div>
      </div>
      <div class="general-banner" v-if="xueBaCardConfig.acct_type_code === 'xuebaCommon'">
        <swiper v-if="xueBaCardConfig.banner_img_mid.length  > 1" :options="swiperOption" ref="mySwiper">
          <swiper-slide class="swiper-slide swiper-container-3d" v-for="(banner,index) in xueBaCardConfig.banner_img_mid" :key="index" style="position:relative;overflow:hidden;width:100%;height:100px;">
            <a :href="banner.href" target="_blank" style="inline-block">
              <img :src="banner.img_url_mobile" style="width:100%;height:100px" />
            </a>
          </swiper-slide>
          <!-- 分页器 -->
          <div class="swiper-pagination" slot="pagination"></div>
        </swiper>
        <div v-else>
          <a :href="xueBaCardConfig.banner_img_mid[0].href" target="_blank" style="inline-block">
            <img :src="xueBaCardConfig.banner_img_mid[0].img_url_mobile" style="width:100%;height:100px" />
          </a>
        </div>
        <!-- <img :src="generalBanner('img')" alt=""> -->
      </div>
      <div class="card-info-common init-card-info-new">
        <div class="subscription">
          <div class="subscription-btn" @click="messageSubscribe">
            <img v-if="isSub" src="../../../../assets/img/mobile/geekBang/subscription-no.png" alt="">
            <img v-else src="../../../../assets/img/mobile/geekBang/subscription.png" alt=""> {{isSub ? '退订领卡提醒' : '订阅领卡提醒'}}
          </div> <span class="subscription-tips">订阅后，将在每月发卡时收到企微提醒</span>
        </div>
        <div class="card-content" :class="getClass1(0)" :style="bgiStyle1">
          <div class="type" :class="{'init-disable-class1': isDisableStyle1()}">学霸卡<span class="geek-font">{{cardName}}</span></div>
          <span class="effect" :class="{'init-disable-class2': isDisableStyle1()}">可用于兑换外部付费好课</span>
          <div class="num-info">领取后7个自然日内有效</div>
          <div class="collect-btn-box">
            <div class="collect-btn" v-if="initCardCanGet" @click="throttleFn(1, true)">点击领取</div>
            <div class="collect-btn stand-btn-disabled" v-else-if="getNums > 0 && cardsOrder[0] && [0, 'consume', 'manage_deduct', undefined].includes(cardsOrder[0].verb_id)" @click="throttleFn(1, false)">已领取</div>
            <!-- 点击领取、已领取、抢光了 -->
            <div class="collect-btn stand-btn-gray" v-else @click="throttleFn(1, false)">{{ showText(0) }} </div>
          </div>
        </div>
        <div class="stand-card-info-new-box">
          <div class="card-content" :class="getClass(item)" :style="bgiStyle(item)" v-for="item in 4" :key="item">
            <div class="type" :class="{'stand-disable-class1': isDisableStyle(item)}">学霸卡<span class="geek-font">{{cardName}}</span></div>
            <span class="effect" :class="{'stand-disable-class2': isDisableStyle(item)}">可用于兑换外部付费好课</span>
            <div class="num-info">领取后7个自然日内有效</div>
            <div class="collect-btn-box">
              <div class="collect-btn" v-if="getNums >= 1 && item >= getNums && standCardCanGet" @click="throttleFn(2, true)">
                点击领取
              </div>
              <div class="collect-btn stand-btn-disabled" v-else-if="getNums >= 1 && item < getNums && cardsOrder[item] && [0, 'consume', 'manage_deduct', undefined].includes(cardsOrder[item].verb_id)" @click="throttleFn(2, false)">
                已领取
              </div>
              <!-- 点击领取、已领取、抢光了 -->
              <div class="collect-btn stand-btn-gray" v-else @click="throttleFn(2, false)">
                {{ showText(item) }}
              </div>
            </div>
          </div>
        </div>
        <div class="text-box" v-html="xueBaCardConfig.activity_remark"></div>
      </div>
      <div class="give-box-mobile" id="invite-main_id" v-if="xueBaCardConfig.acct_type_code !== 'xuebaCommon'">
        <div class="give-explain">
          <div class="give-explain-title">分享有礼：送给同事学霸卡，自己也可赢取学霸卡奖励</div>
          <div class="give-explain-content">
            <p><span style="font-weight:500">领取说明：</span> 每次可领取1张卡，完成解锁条件后可额外领取1张，活动期间每人最多领取5张卡，先到先得，领完即止；</p>
            <p><span style="font-weight:500">解锁条件：</span> 每张学霸卡可兑换1门课，学习该课程中任意5个任务，每个任务学习时长不低于3分钟，即可解锁下一张学霸卡。</p>
            <p>无需使用学霸卡兑换的【{{cardName}}】课程不在本次活动范围内</p>
          </div>
        </div>
        <give-tab @openGive="openGive" :consumePoint="consumePoint" :numberOfRewards="numberOfRewards" :Config="xueBaCardConfig" :listObj="{presentRecordList: presentRecordList, presentPassiveRecordList: presentPassiveRecordList }"></give-tab>
      </div>
      <div class="good-course-list" v-if="(courseList || []).length">
        <div class="top">
          <span class="title">立即兑换课程</span>
          <span class="link" @click="toActiveDetail"> >>> 查看更多可兑换好课 </span>
        </div>
        <div class="list-content">
          <div class="item" v-for="item in courseList || []" :key="item.course_id" @click="toCourseDetail(item.course_url)">
            <div class="cover">
              <!-- 走内容中心 -->
              <van-image class="cover-img" :src="getCourseCoverUrl(item.course_pic_id)">
                <template v-slot:error>
                  <img class="cover-img" src="../../../../assets/img/mobile/geekBang/err-cover-img.png" />
                </template>
              </van-image>
              <div class="time" v-if="item.course_length">
                {{ item.course_length }}分钟
              </div>
            </div>
            <div class="text-main">
              <div class="two-line">
                <span class="course-type">{{item.course_from_name}}</span>
                <span class="title">{{ item.course_name }}</span>
              </div>
              <div class="desc two-line">{{ processString(item.course_desc || '') }}</div>
              <!-- <div class="desc two-line" v-html="item.course_desc"></div> -->
            </div>
          </div>
        </div>
        <div class="link">
          <span @click="toActiveDetail"> &gt;&gt;&gt; 查看更多可兑换好课 &lt;&lt;&lt; </span>
        </div>
      </div>
    </div>
    <!-- 领券详情弹窗 -->
    <van-popup class="card-detail-pop" v-model="showCardDetail" position="bottom" round closeable safe-area-inset-bottom>
      <div class="title">学霸卡「{{cardName}}」{{xueBaCardConfig.acct_type_code === 'xuebaCommon' ? '' : '专用卡' }}领用详情</div>
      <div style="overflow-y: auto;">
        <div class="table">
          <el-table :data="cardDetailList" :header-cell-style="headerCellStyle" style="width: 100%" max-height="300">
            <el-table-column label="序号" type="index" width="50" style="font-size:10px;">
            </el-table-column>
            <el-table-column label="领取时间" width="99">
              <template slot-scope="scope">
                <span class="table-ceel_content">{{scope.row.receive_time}}</span>
              </template>
            </el-table-column>
            <el-table-column prop="" label="获得方式" width="70">
              <template slot-scope="scope">
                <span class="table-ceel_content">{{scope.row.receive_verb_name}}</span>
              </template>
            </el-table-column>
            <el-table-column label="卡券状态" width="70">
              <template slot-scope="scope">
                <span class="table-ceel_content" :class="getCardStyle(scope.row.verb_id)">
                  {{
                    getCardStatus(scope.row.verb_id)
                  }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="说明" width="188">
              <template slot-scope="scope">
                <span class="table-ceel_content"> {{resolveDescData(scope.row)}}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="" width="90">
              <template slot-scope="scope">
                <div class="operat-btn-box" v-if="!scope.row.verb_id">
                  <el-link @click="toManagePage()" type="primary" :underline="false">去兑换课程</el-link>
                </div>
              </template>
            </el-table-column> -->
          </el-table>
        </div>
      </div>
      <div class="foot-page">
        <Pagination v-model="currentPage" :page-count="pageCount" mode="simple" @change="pageChange">
          <template #prev-text>
            <span class="prev-btn">
              <van-icon name="arrow-left" style="margin-right: 4px" /> 上一页
            </span>
          </template>
          <template #next-text>
            <span class="next-btn">
              下一页<van-icon name="arrow" style="margin-left: 4px" />
            </span>
          </template>
          <template #pageDesc>
            <span class="page-desc">
              <span class="current">{{ currentPage }}</span> / {{ pageCount }}
            </span>
          </template>
        </Pagination>
      </div>
    </van-popup>
    <!-- 已兑换课程 -->
    <exchangedMobile :isExchanged.sync="isExchanged"></exchangedMobile>
    <!-- 送出劝学卡 -->
    <giveCardMobile @handlerGiveXuebaka="handlerGiveXuebaka" :xueBaCardConfig="xueBaCardConfig" :isShowGive.sync="isShowGive" :giveNumber="presentRecordList.length" :numberOfRewards="numberOfRewards" :consumePoint="consumePoint"></giveCardMobile>
  </div>
</template>

<script>
// 移动端适配
import 'amfe-flexible/index.js'
import { mapState } from 'vuex'
import env from 'config/env.conf.js'
import translateLang from '@/views/mobile/mixins/translateLang.js'
import { Toast, Pagination } from 'vant'
import {
  getHomePageInfo,
  claimPoints,
  queryGeekRecord,
  getUserActiveInfo,
  getPresentRecord,
  getPresentPassiveRecord,
  messageSubscribe,
  messageUnsubscribe,
  getAcctinfos,
  getSubscribeStatus
} from '@/config/mooc.api.conf.js'
import { throttle } from '@/utils/tools.js'
import giveCardMobile from './giveCardMobile.vue'
import exchangedMobile from './exchangedMobile.vue'
import GiveTab from './giveTab.vue'
const HTTPS_REG = /^https:\/\//
// 映射对象
const STATUS_MAP = {
  0: '待兑换',
  consume: '已兑换',
  deduct_expire: '已失效',
  manage_deduct: '已兑换'
  // manage_deduct: '已使用'
}
const STATUS_STYLE_MAP = {
  0: 'status-waite-use',
  consume: 'status-oready-used',
  deduct_expire: 'status-no-effict',
  manage_deduct: 'status-oready-used'
}
export default {
  name: 'activePage',
  mixins: [translateLang],
  components: { Pagination, giveCardMobile, exchangedMobile, GiveTab },
  data() {
    return {
      numberOfRewards: 0,
      consumePoint: 0,
      isShowGive: false,
      isSub: false,
      isExchanged: false,
      presentRecordList: [],
      presentPassiveRecordList: [],
      configInfo: {}, // 配置信息
      courseList: [],
      xueBaCardConfig: {},
      cardInfo: {}, // 是否可领券、库存信息
      showCardDetail: false, // 领券弹窗是否显示
      currentPage: 1,
      pageSize: 5,
      pageCount: 0,
      cardDetailList: [], // 卡券详情列表
      cardDataList: [],
      swiperOption: {
        loop: true, // 是否循环轮播
        speed: 1000, // 切换速度
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        // 自动轮播
        autoplay: {
          delay: 5000,
          disableOnInteraction: false
        },
        // 设置slider容器能够同时显示的slides数量
        slidesPerView: 1,
        // 分页器
        pagination: {
          el: '.swiper-pagination',
          clickable: true // 允许点击小圆点跳转
        }
      }
    }
  },
  computed: {
    ...mapState(['userInfo']),
    cardName() {
      return this.xueBaCardConfig.card_name || ''
    },
    activityId() {
      return this.$route.query.activityId || 1
    },
    topImgUrl() {
      // 顶部图片
      const { banner_img_top } = this.xueBaCardConfig
      if (!banner_img_top) return
      return banner_img_top.banner_mobile
    },
    timeToDate() {
      return (val) => {
        let str = '--'
        if (val) {
          let timeArr = val.split(' ')[0].split('-')
          str = `${timeArr[0]}-${timeArr[1]}-${timeArr[2]}`
        }
        return str
      }
    },
    // 通兑卡banner
    generalBanner() {
      return (type) => {
        const { banner_img_mid } = this.xueBaCardConfig
        if (!banner_img_mid) return
        return type === 'img'
          ? banner_img_mid[0].img_url_mobile
          : banner_img_mid[0].href
      }
    },
    initCardCanGet() {
      // 能否领取初始卡券
      return this.cardInfo.can_get_geek_num > 0
    },
    standCardCanGet() {
      // 能否领取达标卡券
      return this.cardInfo.can_get_reward_num > 0 && this.cardInfo.can_get_geek
    },
    // 已经领取的数量
    getNums() {
      return this.cardDataList.length || 0
    },
    // 领取顺序
    cardsOrder() {
      return JSON.parse(JSON.stringify(this.cardDataList)).reverse()
    },
    // 达标卡 背景图
    bgiStyle() {
      return (item) => {
        const { card_img_mobile } = this.xueBaCardConfig
        if (!card_img_mobile) return
        if (this.getNums >= 1 && item >= this.getNums && this.standCardCanGet) {
          //  return `background-image: url(${require('@/assets/img/mobile/geekBang/card-stand-new1.png')});`
          return `background-image: url(${card_img_mobile.card_img_url_c});`
        } else if (
          this.getNums >= 1 &&
          item < this.getNums &&
          this.cardsOrder[item] &&
          [0, 'consume', 'manage_deduct', undefined].includes(
            this.cardsOrder[item].verb_id
          )
        ) {
          //   return `background-image: url(${require('@/assets/img/mobile/geekBang/card-stand-new1.png')});`
          return `background-image: url(${card_img_mobile.card_img_url_c});`
        } else {
          //   return `background-image: url(${require('@/assets/img/mobile/geekBang/card-stand-no.png')});`
          return `background-image: url(${card_img_mobile.card_img_url_d});`
        }
      }
    },
    // 初始卡券 背景图
    bgiStyle1() {
      const { card_img_mobile } = this.xueBaCardConfig
      if (!card_img_mobile) return
      if (this.initCardCanGet) {
        // return `background-image: url(${require('@/assets/img/mobile/geekBang/stand-card-bg.png')});`
        return `background-image: url(${card_img_mobile.card_img_url_a});`
      } else if (
        this.getNums > 0 &&
        this.cardsOrder[0] &&
        [0, 'consume', 'manage_deduct', undefined].includes(
          this.cardsOrder[0].verb_id
        )
      ) {
        // return `background-image: url(${require('@/assets/img/mobile/geekBang/stand-card-bg.png')});`
        return `background-image: url(${card_img_mobile.card_img_url_a});`
      } else {
        // return `background-image: url(${require('@/assets/img/mobile/geekBang/card-init-no-effict.png')});`
        return `background-image: url(${card_img_mobile.card_img_url_b});`
      }
    },
    headerCellStyle() {
      return {
        background: '#F5F5F5',
        color: '#00000099',
        fontSize: '12px',
        fontWeight: '500'
      }
    }
  },
  watch: {
    'userInfo.staff_id': {
      async handler(newValue) {
        if (newValue) {
          await this.getUserActiveInfo()
          await this.getHomePageInfoFn()
          await this.getCardAgeAndUsedDetail()
          await this.getCardDataList()
          await this.getSubscribeStatus()
          // 赠送的
          await this.getPresentRecord()
          // 获赠的卡片
          await this.getPresentPassiveRecord()
          await this.getAcctinfos()
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    // 邀请劝学
    this.$nextTick(() => {
      if (!this.$route.query.position) return
      setTimeout(() => {
        const element = document.getElementById('invite-main_id')
        if (element) {
          console.log(element)
          element.scrollIntoView({ behavior: 'smooth' })
        }
      }, 2000)
    })
  },
  methods: {
    // 领取和使用详情 - 去兑换课程
    toManagePage() {
      let herf =
        process.env.NODE_ENV === 'production'
          ? 'https://portal.learn.woa.com'
          : 'https://test-portal-learn.woa.com'
      window.open(herf + '/training/mooc/home')
    },
    async getSubscribeStatus() {
      let res = await getSubscribeStatus(this.xueBaCardConfig.acct_type_code)
      this.isSub = res
    },
    messageSubscribe() {
      if (!this.isSub) {
        messageSubscribe({}, this.xueBaCardConfig.acct_type_code).then(
          (res) => {
            this.getSubscribeStatus()
            Toast({
              message: '订阅成功',
              icon: 'success'
            })
          }
        )
      } else {
        messageUnsubscribe({}, this.xueBaCardConfig.acct_type_code).then(
          (res) => {
            this.getSubscribeStatus()
            Toast({
              message: '退订成功',
              icon: 'success'
            })
          }
        )
      }
    },
    async getAcctinfos() {
      let params = {
        acct_type_codes: this.xueBaCardConfig.acct_type_code + 'Trans'
      }
      const result = await getAcctinfos(params)
      //   查看有没有余额有余额解锁赠送劝学卡
      this.consumePoint = Number(result.consume_point)
    },
    openBanner() {
      window.open(this.generalBanner('href'))
    },
    openGive() {
      this.isShowGive = true
    },
    // 置灰的背景下按钮应该显示的文字
    showText(item) {
      if (
        this.cardsOrder[item] &&
        this.cardsOrder[item].verb_id === 'deduct_expire'
      ) {
        return '已领取'
      } else if (!this.cardInfo.quantity) {
        return '抢光了'
      } else {
        return '点击领取'
      }
    },
    // 达标卡 右上角图片
    getClass(item) {
      if (this.getNums >= 1 && item >= this.getNums && this.standCardCanGet) {
        return `` // 待领取
      } else if (
        this.getNums >= 1 &&
        item < this.getNums &&
        this.cardsOrder[item] &&
        [0, 'consume', 'manage_deduct', undefined].includes(
          this.cardsOrder[item].verb_id
        )
      ) {
        if ([0, undefined].includes(this.cardsOrder[item].verb_id)) {
          return `card-content-1` // 待兑换
        } else {
          return `card-content-2` // 已兑换
        }
      } else {
        if (
          this.cardsOrder[item] &&
          this.cardsOrder[item].verb_id === 'deduct_expire'
        ) {
          return `card-content-4` // 已失效
        } else {
          return `card-content-3` // 未解锁
        }
      }
    },
    // 初始卡 右上角图片
    getClass1() {
      if (this.initCardCanGet) {
        return `` // 待领取
      } else if (
        this.getNums > 0 &&
        this.cardsOrder[0] &&
        [0, 'consume', 'manage_deduct', undefined].includes(
          this.cardsOrder[0].verb_id
        )
      ) {
        if ([0, undefined].includes(this.cardsOrder[0].verb_id)) {
          return `card-content-1` // 待兑换
        } else {
          return `card-content-2` // 已兑换
        }
      } else {
        return `card-content-4` // 已失效
      }
    },
    // 达标卡 判断是否置灰 “学霸卡”和“可用于兑换外部付费好课”文字置灰
    isDisableStyle(item) {
      if (this.getNums >= 1 && item >= this.getNums && this.standCardCanGet) {
        return false
      } else if (
        this.getNums >= 1 &&
        item < this.getNums &&
        this.cardsOrder[item] &&
        [0, 'consume', 'manage_deduct', undefined].includes(
          this.cardsOrder[item].verb_id
        )
      ) {
        return false
      } else {
        return true
      }
    },
    // 初始卡券 判断是否置灰 “学霸卡”和“可用于兑换外部付费好课”文字置灰
    isDisableStyle1() {
      if (this.initCardCanGet) {
        return false
      } else if (
        this.getNums > 0 &&
        this.cardsOrder[0] &&
        [0, 'consume', 'manage_deduct', undefined].includes(
          this.cardsOrder[0].verb_id
        )
      ) {
        return false
      } else {
        return true
      }
    },
    /**
     * 字符串去除标签类型的字符等
     * @param {string} str
     * @returns {string}
     */
    processString(str) {
      if (!str || typeof str !== 'string') {
        return ''
      }
      // 去除HTML标签
      str = str.replace(/<[^>]*>/g, '')
      // 去除特殊字符
      str = str.replace(/&nbsp;/g, ' ')
      str = str.replace(/&amp;/g, '&')
      str = str.replace(/&lt;/g, '<')
      str = str.replace(/&gt;/g, '>')
      // 可以继续添加其他特殊字符的替换规则
      return str
    },
    // 课程封面 内容中心逻辑
    getCourseCoverUrl(course_pic_id) {
      if (HTTPS_REG.test(course_pic_id)) {
        return course_pic_id
      }
      const envName = env[process.env.NODE_ENV]
      return `${envName.contentcenter}content-center/api/v1/content/imgage/${course_pic_id}/preview`
    },
    initData() {
      // 更新卡券数据
      this.getHomePageInfoFn()
      this.getCardAgeAndUsedDetail()
      this.getAcctinfos()
    },
    // 获取学霸卡基础信息
    async getUserActiveInfo() {
      try {
        let res = await getUserActiveInfo({ activity_id: this.activityId })
        console.log(res, '获取学霸卡基础信息')
        this.xueBaCardConfig = res
        this.courseList = res.course_list || []
      } catch (error) {
        console.log('获取基础信息: ', error)
      }
    },
    // 赠送的
    async getPresentRecord() {
      let params = {
        activityId: this.xueBaCardConfig.activity_id,
        current: 1,
        size: 10
      }
      const res = await getPresentRecord(params)
      console.log(res, '赠送')
      this.presentRecordList = res.records || []
      //   获得奖励的有多少张
      let presentFilterList = this.presentRecordList.filter(
        (item) => item.reward_amt
      )
      this.numberOfRewards = presentFilterList.length
    },
    // 获赠的
    async getPresentPassiveRecord() {
      let params = {
        activityId: this.xueBaCardConfig.activity_id,
        current: 1,
        size: 10
      }
      const res = await getPresentPassiveRecord(params)
      console.log(res, '获赠')
      this.presentPassiveRecordList = res.records || []
    },
    // 赠送劝学卡后刷新赠送列表
    handlerGiveXuebaka() {
      this.getPresentRecord()
      this.getCardAgeAndUsedDetail()
      this.getCardDataList()
      this.getAcctinfos()
    },
    // 获取活动详情数据
    // async getHomePageData() {
    //   try {
    //     let res = await getHomePageConfig()
    //     let data = res ? JSON.parse(res) : {}
    //     this.configInfo = data
    //   } catch (error) {}
    // },
    // 积分授予-查询是否可以领取geek
    async getHomePageInfoFn() {
      try {
        let res = await getHomePageInfo({
          staff_id: this.userInfo.staff_id,
          acct_type_code: this.xueBaCardConfig.acct_type_code,
          activity_id: this.activityId
        })
        this.cardInfo = res
      } catch (error) {
        console.error('积分授予-查询是否可以领取geek-error: ', error)
      }
    },
    // 发请求领取积分卡
    async claimPointsFn(grant_type) {
      try {
        let payload = {
          staff_id: this.userInfo.staff_id,
          staff_name: this.userInfo.staff_name,
          acct_type_code: this.xueBaCardConfig.acct_type_code,
          grant_amt: '1',
          grant_type: grant_type,
          busi_id: this.xueBaCardConfig.activity_id
        }
        await claimPoints(payload)
        // 领取成功
        Toast({
          message: '领取成功',
          icon: 'passed'
        })
        // 初始化数据
        this.initData()
      } catch (error) {
        // 领取失败
        Toast({
          message: '领取失败',
          icon: 'close'
        })
        console.error('发请求领取积分卡---error: ', error)
      }
    },
    // 获取学霸卡领取和使用详情
    async getCardAgeAndUsedDetail() {
      try {
        // 发请求获取分页数据
        let payload = {
          staff_id: this.userInfo.staff_id,
          acct_type_code: this.xueBaCardConfig.acct_type_code,
          current: this.currentPage,
          activity_id: this.xueBaCardConfig.activity_id,
          size: this.pageSize
        }
        let res = await queryGeekRecord(payload)
        this.cardDetailList = res.records || []
        this.pageCount = res.pages
      } catch (error) {}
    },
    // 获取五张自主领取卡券
    async getCardDataList() {
      try {
        // 发请求获取分页数据
        let payload = {
          staff_id: this.userInfo.staff_id,
          acct_type_code: this.xueBaCardConfig.acct_type_code,
          current: this.currentPage,
          activity_id: this.xueBaCardConfig.activity_id,
          receive_verb_id: 'receive',
          size: this.pageSize
        }
        let res = await queryGeekRecord(payload)
        this.cardDataList = res.records || []
      } catch (error) {}
    },

    // 点击领取卡券 节流方法
    throttleFn: throttle(function (code, status) {
      this.getCoupon(code, status)
    }, 500),
    // 点击领取卡券
    getCoupon(type, status) {
      switch (type) {
        case 1:
          // 初始卡
          if (status) {
            this.claimPointsFn(this.xueBaCardConfig.acct_type_code)
          } else {
            // hasGetGeek-是否已经领取，canGetGeekNum-可领取的兑换券的数量，formalStaff-是否正式员工，totalBalance-已领取数量，limit-最大限制数量
            const {
              has_get_geek: hasGetGeek,
              can_get_geek_num: canGetGeekNum,
              formal_staff: formalStaff,
              total_balance: totalBalance,
              limit
            } = this.cardInfo
            if (!formalStaff) {
              Toast('非集团本部正式员工不可领取')
            } else if (totalBalance >= limit) {
              Toast('已达到领取数量上限')
            } else if (!hasGetGeek && canGetGeekNum <= 0) {
              Toast(
                '本轮学霸卡已发放完毕，暂时无法领取，敬请期待后续轮次的发放活动'
              )
            } else if (hasGetGeek) {
              Toast(
                '你本轮次已领取过学霸卡，每人限领1次，敬请期待后续轮次的发放活动'
              )
            } else {
            }
          }
          break
        case 2:
          // 达标卡
          if (status) {
            this.claimPointsFn('reward')
          } else {
            // hasGetGeekReward-是否已经领取，canGetRewardNum-可领取的兑换券的数量，canGetGeek-是否可以领取，formalStaff-是否正式员工，totalBalance-已领取数量，limit-最大限制数量
            const {
              has_get_geek_reward: hasGetGeekReward,
              can_get_reward_num: canGetRewardNum,
              can_get_geek: canGetGeek,
              formal_staff: formalStaff,
              total_balance: totalBalance,
              limit
            } = this.cardInfo
            let canBeClaimed = canGetRewardNum > 0 && canGetGeek
            if (!formalStaff) {
              Toast('非集团本部正式员工不可领取')
            } else if (totalBalance >= limit) {
              Toast('已达到领取数量上限')
            } else if (!canBeClaimed) {
              Toast('暂无领取资格，请查看活动规则并完成学习条件后再点击领取')
            } else if (!hasGetGeekReward && canGetRewardNum <= 0) {
              Toast(
                '本轮学霸卡已发放完毕，暂时无法领取，敬请期待后续轮次的发放活动'
              )
            } else if (hasGetGeekReward) {
              Toast(
                '你本轮次已领取过学霸卡，每人限领1次，敬请期待后续轮次的发放活动'
              )
            } else {
            }
          }
          break
        default:
          break
      }
    },
    // 处理列表的"说明"字段
    resolveDescData(item) {
      if (item.verb_id === 'consume' || item.verb_id === 'manage_deduct') {
        return `使用时间：${this.resolveTime(item.deduct_time)}`
      } else if (item.verb_id === 'deduct_expire') {
        return `失效时间：${this.resolveTime(item.expire_time)}`
      } else {
        return `领取7天后，卡券失效，请及时使用`
      }
    },
    // 打开领用详情弹窗
    openDetailPop() {
      this.showCardDetail = true
    },
    // 券领取详情分页改变
    pageChange(page) {
      this.currentPage = page
      this.getCardAgeAndUsedDetail()
    },
    // 获取卡券状态
    getCardStatus(code = 0) {
      let statusText = STATUS_MAP[code] || STATUS_MAP[0]
      return statusText
    },
    // 获取券状态样式
    getCardStyle(code = 0) {
      let statusStyle = STATUS_STYLE_MAP[code] || STATUS_STYLE_MAP[0]
      return statusStyle
    },
    // 点击顶部图片跳转
    goTo() {
      let url = this.configInfo.banner?.banner_pic_go_url || ''
      if (url) {
        window.open(url)
      }
    },
    // 查看更多可兑换好课 外部好课专区
    toActiveDetail() {
      window.open(this.xueBaCardConfig.course_more_link)
    },
    // 活动规则
    toRulesDetail() {
      window.open(this.xueBaCardConfig.activity_detail_link)
    },
    // 已兑课程
    toMooc() {
      this.isExchanged = true
      // window.wx.miniProgram.navigateTo({
      //   url: `/pages/mooc/myProject/index`
      // })
    },
    // 跳转到课程详情
    toCourseDetail(href) {
      window.open(href)
      //   window.wx.miniProgram.reLaunch({
      //     url: href
      //   })
    },
    // 去兑换课程
    // toExchangeCourse () {
    //   window.wx.miniProgram.reLaunch({
    //     url: `/pages/mooc/projectList/index`
    //   })
    // }
    // 处理时间数据
    resolveTime(time) {
      if (!time) return ''
      let srt = time.split(':').slice(0, -1).join(':')
      return srt.replace(/-/g, '/')
    }
  }
}
</script>

<style lang="less" scoped>
* {
  box-sizing: border-box;
}
.active-page {
  overflow-y: auto;
  height: 100%;
  background: #f4faff;
  padding-bottom: 21px;
  .top-img {
    width: 100%;
  }
  .content-main {
    position: relative;
    padding: 0 16px;
    margin: -65px auto 0;
    & > div {
      padding: 20px;
      border-radius: 16px;
      background-color: #fff;
      box-shadow: 0 0 8px 0 #b5d0e30a;
      &:nth-child(n + 1) {
        margin-top: 12px;
      }
    }
    .general-banner {
      height: 100px;
      width: 100%;
      padding: 0;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .give-box-mobile {
      padding: 0;
      background: transparent;
      .give-explain {
        padding: 20px;
        background-color: #fff;
        border-radius: 16px;
        margin-bottom: 12px;
        &-title {
          color: #000000e6;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px;
          margin-bottom: 12px;
        }
        &-content {
          color: #00000066;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          line-height: 22px;
        }
      }
    }
    .color-cupton-num {
      color: #ed7b2f !important;
    }
    .my-card-info {
      .top {
        display: flex;
        align-items: baseline;
        font-size: 14px;
        line-height: 24px;
      }
      .title {
        position: relative;
        color: #000000e6;
        font-weight: 500;
        height: 24px;
        line-height: 24px;
        background: url('../../../../assets/img/mobile/geekBang/bd.png')
          no-repeat 0 18px;
      }
      .num {
        // color: #0052d9;
        color: #0052d9;
        font-size: 18px;
      }
      .date {
        padding-left: 16px;
        font-size: 12px;
        color: #00000066;
        span {
          color: #0052d9;
        }
      }
      .detail-link {
        margin-left: 16px;
        color: #0052d9;
        text-decoration-line: underline;
        cursor: pointer;
      }
      .btns {
        margin-top: 16px;
        display: flex;
        justify-content: space-between;
      }
      .btn {
        font-size: 14px;
        color: #0052d9;
        font-weight: 500;
        width: 93px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: 6px;
        cursor: pointer;
        background: var(---Brand1-Light, #ecf2fe);
      }
    }
    .card-info-common {
      .subscription {
        display: flex;
        align-items: flex-end;
        margin-bottom: 16px;
        .subscription-btn {
          display: flex;
          height: 32px;
          padding: 0 8px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          border-radius: 6px;
          background: #0052d9;
          color: #fff;
          font-size: 14px;
          img {
            width: 16px;
            height: 16px;
          }
        }
        &-tips {
          color: #00000066;
          font-family: 'PingFang SC';
          font-size: 10px;
          font-style: normal;
          font-weight: 500;
          line-height: 16px;
          padding-left: 8px;
        }
      }

      .card-content {
        height: 127px;
        padding: 12px;
        background-size: 100% 100%;
      }
      .type {
        font-size: 16px;
        font-weight: 700;
        line-height: 22px;
        font-family: 'Source Han Sans CN';
        // color: #573a18;
        color: #0c3102;
        display: flex;
        align-items: center;
        img {
          margin-left: 4px;
        }
      }
      .effect {
        margin-top: 2px;
        padding-bottom: 6px;
        font-size: 10px;
        line-height: 16px;
        border-bottom: 1px dashed #90714b;
        color: #6e491f;
        // border-bottom-color: #90714b;
      }
      .num-info {
        margin-top: 10px;
        color: #ff6600;
        font-size: 10px;
        line-height: 16px;
      }
      .left {
        display: flex;
        flex-direction: column;
        font-size: 12px;
        line-height: 16px;
        max-width: 158px;
        span:last-child {
          margin-top: 4px;
        }
      }
      .collect-btn-box {
        margin-top: 13px;
        display: flex;
        justify-content: flex-end;
      }
      .collect-btn {
        width: 72px;
        height: 22px;
        line-height: 22px;
        color: #ffffff;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        border-radius: 64px;
        cursor: pointer;
      }
      .desc {
        margin-top: 12px;
        color: #00000099;
        font-size: 14px;
        line-height: 22px;
      }
    }
    .init-card-info-new {
      .card-content {
        position: relative;
        background-image: url('../../../../assets/img/mobile/geekBang/stand-card-bg.png');
        &::after {
          // 待领取
          position: absolute;
          content: '';
          right: 0;
          top: 0;
          width: 40px;
          height: 40px;
          background: url('../../../../assets/img/mobile/geekBang/wait-get.png')
            no-repeat 0 0;
          background-size: 100%;
          z-index: 1;
        }
        .init-disable-class1 {
          color: #00000099;
        }
        .init-disable-class2 {
          color: #00000066;
        }
      }
      .type {
        .geek-font {
          display: inline-block;
          padding: 0 5px;
          height: 16px;
          border: 1px solid #ab8143;
          color: #ab8143;
          line-height: 15px;
          border-radius: 8px;
          font-size: 8px;
          text-align: center;
          margin-left: 4px;
        }
      }
      .effect {
      }
      .collect-btn {
        background: linear-gradient(276deg, #8b5300 14.56%, #ae864a 95.9%);
      }
      .stand-btn-disabled {
        background: linear-gradient(276deg, #eac896 14.56%, #eedcb9 95.9%);
      }
      .stand-btn-gray {
        background: linear-gradient(276deg, #d4d4d4 14.56%, #d8d8d8 95.9%);
      }
      .card-content-1::after {
        // 待兑换
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-1.png');
      }
      .card-content-2::after {
        // 已兑换
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-2.png');
      }
      .card-content-3::after {
        // 未解锁
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-3.png');
      }
      .card-content-4::after {
        // 已失效
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-4.png');
      }
    }
    .stand-card-info-new-box {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .card-content {
        position: relative;
        margin-top: 16px;
        width: 146px;
        height: 127px;
        border-radius: 10px;
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-new1.png');
        &::after {
          // 待领取
          position: absolute;
          content: '';
          right: 0;
          top: 0;
          width: 40px;
          height: 40px;
          background: url('../../../../assets/img/mobile/geekBang/wait-get.png')
            no-repeat 0 0;
          background-size: 100%;
          z-index: 1;
        }
        .stand-disable-class1 {
          color: #00000099;
        }
        .stand-disable-class2 {
          color: #00000066;
        }
      }
      .card-content-1::after {
        // 待兑换
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-1.png');
      }
      .card-content-2::after {
        // 已兑换
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-2.png');
      }
      .card-content-3::after {
        // 未解锁
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-3.png');
      }
      .card-content-4::after {
        // 已失效
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-4.png');
      }
    }
    .text-box {
      margin-top: 16px;
      color: #00000099;
      font-size: 14px;
      line-height: 22px;
      .text-title {
        color: #00000099;
        font-weight: 500;
      }
      .warm {
        color: #ed7b2f;
      }
      .text-attention {
        color: #ed7b2f;
        font-weight: 500;
      }
    }

    .good-course-list {
      padding: 20px;
      border-radius: 16px;
      background: #fff;
      box-shadow: 0 0 8px 0 #b5d0e30a;
      .top {
        .title {
          color: #000000e6;
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
        }
        .link {
          margin-left: 20px;
          color: #0052d9;
          font-size: 14px;
          line-height: 22px;
          text-decoration-line: underline;
        }
      }
      .list-content {
        margin-top: 12px;
        .item {
          display: flex;
          align-items: center;
          border-top: 0.5px solid #eee;
          padding: 12px 0;
          .cover {
            position: relative;
            margin-right: 12px;
            border-radius: 3px;
          }
          .cover-img {
            width: 129px;
            height: 86px;
          }
          .time {
            position: absolute;
            right: 8px;
            bottom: 8px;
            padding: 2px 12px;
            height: 20px;
            line-height: 20px;
            color: #ffffff;
            font-size: 12px;
            border-radius: 12px;
            background: #00000066;
          }
          .text-main {
            .course-type {
              margin-right: 5px;
              padding: 3px 4px;
              color: #0052d9;
              font-size: 12px;
              line-height: 12px;
              border-radius: 2px;
              background: #ebeffc;
            }
            .title {
              color: #000000e6;
              font-size: 14px;
              line-height: 22px;
            }
            .desc {
              margin-top: 2px;
              height: 40px;
              color: #00000099;
              font-size: 12px;
              line-height: 20px;
            }
          }
        }
      }
      & > .link {
        text-align: center;
        span {
          cursor: pointer;
          color: #0052d9;
          font-size: 14px;
          line-height: 22px;
          text-decoration-line: underline;
        }
      }
    }
  }
  .one-line {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .two-line {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /*显示两行*/
  }
}
.card-detail-pop {
  //   max-height: 396px;
  padding: 16px 0;
  .title {
    color: #000000e6;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    line-height: 26px;
  }
  .table {
    margin-top: 16px;
    .table-title {
      display: flex;
      height: 38px;
      line-height: 38px;
      color: #00000099;
      font-size: 12px;
      font-weight: 500;
      background-color: #f5f5f5;
      & > div {
        padding-left: 16px;
      }
    }
    /deep/.el-table__row {
      font-size: 10px;
    }
    .table-ceel_content {
      color: #000000e6;
      font-family: 'PingFang SC';
      font-size: 10px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
    }
    .item-content {
      max-height: 300px;
      overflow-y: auto;
    }
    .item {
      display: flex;
      color: #000000e6;
      height: 60px;
      line-height: 60px;
      box-shadow: 0 -1px 0 0 #eee inset;
      & > div {
        font-size: 10px;
        padding-left: 14px;
        &:nth-child(n + 3) {
          padding-left: 16px;
        }
      }
    }
    // .no {
    //   width: 56px;
    // }
    .time {
      width: 99px;
    }
    .status {
      width: 70px;
    }
    .status-text {
      line-height: 16px;
      text-align: center;
      border-radius: 4px;
      padding: 1px 8px;
      font-size: 10px;
    }
    .status-no-effict {
      color: #ed7b2f;
      border: 1px solid var(---Warning5-Normal, #ed7b2f);
      background: var(---Warning1-Light, #fef3e6);
    }
    .status-waite-use {
      color: #0052d9;
      border: 1px solid var(---Brand8-Normal, #0052d9);
      background: var(---Brand1-Light, #ecf2fe);
    }
    .status-oready-used {
      color: #00a870;
      border: 1px solid var(---Success5-Normal, #00a870);
      background: var(---Success1-Light, #e8f8f2);
    }
    .desc {
      padding: 0 16px;
      flex: 1;
      text-align: left !important;
      // width: 235px;
    }
    // .course-link {
    //   white-space: nowrap;
    //   cursor: pointer;
    //   color: #0052d9;
    //   padding-right: 16px;
    // }
  }
  .foot-page {
    margin-top: 20px;
    padding: 0 16px;
    .prev-btn {
      color: #00000099;
      font-size: 12px;
      line-height: 20px;
    }
    .next-btn {
      color: #0052d9;
      font-size: 12px;
      line-height: 20px;
    }
    .page-desc {
      color: #000000e6;
      font-size: 12px;
      line-height: 20px;
      padding: 6px 16px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      .current {
        color: #0052d9;
      }
    }
    /deep/.van-pagination__prev {
      height: 32px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      background: var(---White, #fff);
    }
    /deep/.van-pagination__page-desc {
      height: 32px;
    }
    /deep/.van-pagination__next {
      height: 32px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      background: #f3f7ff;
    }
  }
  /deep/.van-popup__close-icon--top-right {
    color: #000000;
    top: 23px;
    right: 22px;
  }
  /deep/.van-popup__close-icon {
    font-size: 12px;
    font-weight: 700;
  }
}
</style>
