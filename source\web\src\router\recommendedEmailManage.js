const manage = [
  // 推荐邮件
  {
    path: '/recommend/manage/banners',
    name: 'configList',
    component: () => import('views/manage/recommend/bannersList/index.vue'),
    meta: {
      title: '推荐邮件活动列表',
      breadcrumb: ['推荐邮件', '活动列表']
    }
  },
  // 课找人
  {
    path: '/recommend/manage/course',
    name: 'courseList',
    component: () => import('views/manage/recommend/courseList/index.vue'),
    meta: {
      title: '推荐'
    } 
  },
  // 年度好课内容配置
  {
    path: '/manage/yearRankSet',
    name: 'yearRankSet',
    component: () => import('views/manage/yearRankSet/index.vue'),
    meta: {
      title: '年度好课配置'
    } 
  }
]

export default manage
