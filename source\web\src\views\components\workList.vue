<template>
  <div :class="[listType === 'manage-mutual'? 'manage-work-list' :'work-list']">
    <p class="list-text">作业列表</p>
    <div class="list-box">
      <div class="input">
        <el-input 
        @input="handleSearchName" 
        v-model="form.staff_name" 
        size="small" 
        placeholder="请输入学员名称" 
        suffix-icon="el-icon-search"
        clearable
        ></el-input>
      </div>
      <div style="width:164px;padding-top:12px;" v-if="listType !== 'user-mutual' && readAuth">
        <el-select 
        size="small" 
        v-model="form.status" 
        placeholder="请选择" 
        @change="handleChangeSelect"
        >
          <el-option 
          v-for="item in statusOptions" 
          :key="item.value" 
          :label="item.label" 
          :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
      <div class="infinite-list-wrapper" v-if="statusStudentList.length">
        <ul class="list-loading list-c" @scroll="handleScroll">
          <li 
          class="list-i" 
          :class="[activeStudent === item.staff_id ? 'disable-view' : '']" 
          v-for="(item, index) in statusStudentList" 
          :key="index" 
          @click="handleActive(item)"
          >
            <div class="li-name">
              <span>{{item.staff_name}}</span>
              <span v-if="form.status === '' && listType !== 'user-mutual'" class="status-label">{{ item.statusLabel }}</span>
            </div>
            <i v-if="item.review" class="el-icon-success success"></i>
          </li>
          <li class="load-center" v-if="workListLoading">加载中...</li>
          <li class="load-center" v-else>没有更多了</li>
        </ul>
      </div>
      <div class="empty" v-else>
        <div class="empty-text">暂无数据~</div>
      </div>
    </div>
  </div>
</template>

<script>
import { debounce } from '@/utils/tools.js'
export default {
  props: {
    // 作业详情
    detailData: {
      type: Object,
      default: () => {}
    },
    // 选中的员工
    activeStudent: {
      type: Number
    },
    studentList: {
      type: Array,
      default: () => []
    },
    statisticsNumber: {
      type: Object,
      default: () => {}
    },
    listType: {
      type: String,
      default: 'manage-mutual'
    },
    currentInfo: {
      type: Object,
      default: () => ({})
    },
    workListLoading: {
      type: Boolean,
      default: false
    },
    total: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      // 1 已提交/待批阅  2 驳回  3 合格 4 不合格)
      readOptions: [
        { value: '', label: '全部', number: 0, name: 'total_count' },
        { value: 1, label: '待批阅', number: 0, name: 'submit_count' },
        { value: 2, label: '已退回', number: 0, name: 'refund_count' },
        { value: 3, label: '合格', number: 0, name: 'eligible_count' },
        { value: 4, label: '不合格', number: 0, name: 'un_eligible_count' }
      ],
      noReadoptions: [
        { value: '', label: '全部', number: 0, name: 'total_count' },
        { value: 1, label: '已提交', number: 0, name: 'submit_count' },
        { value: 2, label: '已退回', number: 0, name: 'refund_count' }
      ],
      form: {
        staff_name: '',
        status: '',
        page_no: 1,
        page_size: 5
      }
    }
  },
  watch: {
  },
  created() {
  },
  computed: {
    statusOptions() {
      const readList = JSON.parse(JSON.stringify(this.readOptions))
      const noReadList = JSON.parse(JSON.stringify(this.noReadoptions))
      const options = this.detailData?.enable_mark ? readList : noReadList
      options.forEach((e) => {
        for (let prp in this.statisticsNumber) {
          if (e.name === prp) {
            e.number = this.statisticsNumber[prp]
            e.label = `${e.label}(${this.statisticsNumber[prp]})`
          }
        }
      })
      return options
    },
    // 查看--批阅权限
    readAuth() {
      return this.currentInfo.has_show && this.currentInfo.has_mark
    },
    statusStudentList() {
      const readList = JSON.parse(JSON.stringify(this.readOptions))
      const noReadList = JSON.parse(JSON.stringify(this.noReadoptions))
      const list = this.detailData?.enable_mark ? readList : noReadList
      this.studentList.forEach((e) => {
        list.forEach((v) => {
          if (e.status === v.value) {
            e.statusLabel = v.label
          }
        })
      })
      return this.studentList
    },
    noMore() {
      return this.statusStudentList?.length >= this.total
    }
  },
  methods: {
    handleScroll(e) {
      if (this.noMore) { // 已加载完
        this.$emit('update:workListLoading', false)
        return
      }
      const { scrollTop, clientHeight, scrollHeight } = e.target
      if (Math.ceil(scrollTop) + clientHeight >= scrollHeight) {
        this.form.page_no++
        this.$emit('getStudentList', this.form, 'load')
        this.$emit('update:workListLoading', true)
      }
    },
    // 选中学员
    handleActive(val) {
      this.$emit('handleCurrentActive', val)
    },
    handleChangeSelect(val) {
      this.form.page_no = 1
      this.$emit('getStudentList', this.form)
    },
    // 学员查询
    handleSearchName: debounce(function () {
      this.form.page_no = 1
      this.$emit('getStudentList', this.form)
    }, 500)
  }
}
</script>

<style lang="less" scoped>

.work-list, .manage-work-list {
  position: sticky;
  top: 0px;
  z-index: 101;
  background-color: #fff;
  padding-top: 24px;
  border-right: 1px solid #eeeeeeff;
  width: 164px;
  .list-text {
    font-weight: bold;
    color: #00000099;
    font-size: 14px;
  }
  .input {
    padding-top: 16px;
  }
  .load-center {
    text-align: center;
    color: #00000066;
    font-size: 12px;
    padding-top: 16px;
  }
  .empty {
    padding-top: 20px;
    color: #999;
    text-align: left;
  }
  .list-c {
    height: 575px;
    margin-top: 12px;
    overflow: auto;

    .list-i {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // height: 28px;
      line-height: 28px;
      // padding: 0 8px;
      margin: 1px 0;
      border-radius: 3px;
      padding: 0 8px;
      // padding-right: 20px;
      cursor: pointer;
      .success {
        color: #00a870;
      }
    }
    .li-name {
      display: flex;
      justify-content: space-between;
      width: 100%;
      .status-label {
        color: #00000066;
        font-size: 12px;
      }
    }
    .list-i:hover {
      background-color: #ecf2feff;
    }
  }
  .disable-view {
    background-color: #ecf2feff;
    color: #0034b5ff;
  }
}
.manage-work-list {
  border-right: unset
}
.work-list {
  width: 184px;
  padding-right: 20px;
}
</style>
