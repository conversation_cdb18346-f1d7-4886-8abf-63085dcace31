<template>
  <div :class="isH5 ? 'active-jump-content-h5' : 'active-jump-content'" v-if="isGrayTarget">
    <div class="active-jump"
      :dt-eid="dtCommon('eid')"
      :dt-remark="dtCommon('remark')"
      :dt-areaid="dtCommon('areaid')"
      @click="jumpToActive"
    >
      <img class="warm-icon" src="@/assets/img/active-warm.png" alt="">
      2.14-4.30，凡订阅了标签并学习在线课程均可参与抽奖。
    </div>
  </div>
</template>
<script>
import { getGrayTarget } from '@/config/api.conf.js'

export default {
  components: {
  },
  props: {
    dtArg: {
      type: Object,
      default: () => ({
        page: '',
        page_type: '',
        container: '',
        content_name: '',
        course_id: '',
        terminal: 'PC'
      })
    }
  },
  data() {
    return {
      isGrayTarget: false // 是否是灰度目标学员
    }
  },
  computed: {
    isH5() {
      return this.dtArg.terminal === 'H5'
    },
    dtCommon() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.dtArg?.page || '',
            page_type: this.dtArg?.page_type || '', 
            container: this.dtArg?.container || '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: this.dtArg?.content_name || '',
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: this.dtArg?.terminal || 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.dtArg.course_id || ''}_${this.dtArg?.content_name || ''}`
        } else {
          return `area_${this.dtArg.course_id || ''}_${this.dtArg?.content_name || ''}`
        }
      }
    }
  },
  mounted() {
    this.getTarget()
  },
  methods: {
    jumpToActive() {
      window.open('https://sdc.qq.com/s/YTZPTE?scheme_type=activitySub&type=lottery')
    },
    getTarget() {
      let target = process.env.NODE_ENV === 'production' ? '2029458' : '3018309'
      getGrayTarget({ target }).then(res => {
        this.isGrayTarget = !!res
      }).catch(err => {
        console.error('获取灰度名单：', err)
      })
    }
  }
}
</script>
<style lang="less" scoped>
.active-jump-content {
  display: flex;
  .active-jump {
    display: flex;
    align-items: center;
    padding: 0 12px;
    height: 36px;
    font-size: 14px;
    font-weight: 700;
    color: #ff7e29;
    cursor: pointer;
    border-radius: 8px;
    background: #FFF3E7;
    .warm-icon {
      width: 24px;
      margin-right: 12px;
    }
  }
}
.active-jump-content-h5 {
  display: flex;
  .active-jump {
    display: flex;
    align-items: center;
    padding: 0 12px;
    width: 100vw;
    height: 32px;
    font-size: 12px;
    font-weight: 700;
    color: #ff7e29;
    cursor: pointer;
    background: #FFF3E7;
    .warm-icon {
      width: 20px;
      margin-right: 12px;
    }
  }
}
</style>
