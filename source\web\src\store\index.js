import { getStore } from 'sdc-vue'
import { getProjectDetail } from 'config/mooc.api.conf'
import { isSysBusy } from 'config/api.conf.js'
import net from './net'
import activity from './activity'
export default getStore({
  state: {
    // 当前登陆用户
    userInfo: {
      staff_id: '',
      staff_name: ''
    },
    userDepInfo: {},
    moduleInfo: [], // 内容模块选项
    netClassify: [], // 网络课的分类选项
    projectManageInfo: {},
    userLimitInfo: {}, // 用户角色权限
    superAdminList: [
      {
        id: '1.2',
        path: 'banner-list',
        name: '轮播图管理',
        parentId: '1',
        level: 2,
        module: 'mooc'
      }
    ], // 超级权限菜单
    taskListFilterInfo: { // 任务列表过滤条件
      required: false,
      unFinished: false
    },
    bgColor: '',
    moocLang: localStorage.getItem('sdc-sys-def-lang') || 'zh-cn',
    showTaskPanel: true,
    isBusy: '1',
    isTarget: false
  },
  mutations: {
    setUserInfo(state, data) {
      state.userInfo = data
    },
    setUserDepInfo(state, data) {
      state.userDepInfo = data
    },
    saveProjectManageInfo(state, payload) {
      state.projectManageInfo = payload
    },
    saveUserLimits(state, payload) {
      state.userLimitInfo = payload
    },
    setTaskListFilter(state, payload) {
      state.taskListFilterInfo = payload
    },
    setBgColor(state, payload) {
      state.bgColor = payload
    },
    setMoocLang(state, payload) {
      state.moocLang = payload
      localStorage.setItem('sdc-sys-def-lang', payload)
    },
    setTaskPanel(state, payload) {
      state.showTaskPanel = payload
    },
    setIsBusy(state, payload) {
      state.isBusy = payload
    },
    setIsTarget(state, payload) {
      state.isTarget = payload
    }
  },
  actions: {
    getProjectInfoData(context, option) {
      getProjectDetail(option).then((res) => {
        context.commit('saveProjectManageInfo', res)
      })
    },
    async getIsBusy(context, option) {
      const data = await isSysBusy()
      context.commit('setIsBusy', data)
    }
  },
  // 注册其他模块，避免混淆使用
  modules: {
    net,
    activity
  }
})
