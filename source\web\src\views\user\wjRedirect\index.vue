<template>
  <div></div>
</template>

<script>
export default {
  name: 'wjRedirect',
  data () {
    return {
      routerParame: {
        userId: '',
        from: '',
        courseId: '',
        taskId: '',
        ext1: '' // spoc课程的classId
      }
    }
  },
  mounted() {
    this.hanldeJumpTaskFinish()
  },
  methods: {
    hanldeJumpTaskFinish() {
      let params = this.$route.query
      console.log(params, 'paramsparamsparamsparamsparamsparamsparamsparamsparamsparamsparams')
      const { user_id = '', from = '', course_id = '', task_id = '', ext1 = '' } = params
      if (user_id && from && course_id && task_id) {
        if (from === 'mooc') {
          this.$router.push({
            path: '/user/taskFinish',
            query: {
              user_id,
              from,
              course_id,
              task_id
            }
          })
        } else if (from === 'spoc') {
          this.$router.push({
            path: '/user/taskFinish',
            query: {
              user_id,
              from,
              course_id,
              task_id,
              ext1
            }
          })
        } else if (from === 'coregist') {
          console.log(from, 'fromfromfrom')
          this.$router.push({
            path: '/user/taskFinish',
            query: {
              user_id,
              from,
              course_id,
              task_id,
              ext1
            }
          })          
        } else {
          throw new Error('from参数错误')
        }
      } else {
        this.$router.push({
          path: '/user/taskFinish',
          query: {
            from: 'normal',
            ...params
          }
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>

</style>
