<template>
  <div class="module-category-card" @click="linkTo">
    <span
      class="card-title"
      :class="[{ 'cur-course': curIndex === this.detailsInfo.order_no }]"
    >
      <img
        class="label-play-icon"
        v-if="curIndex === this.detailsInfo.order_no"
        src="@/assets/img/mobile/play-active.png"
      />
      <span class="label">{{ this.detailsInfo.module_name }}</span
      >{{ this.detailsInfo.content_name }}
    </span>
    <div class="card-content">
      <!-- 左侧图片 如果没有则不显示 -->
      <div
        v-if="detailsInfo.photo_url"
        class="left-con"
        :class="[detailsInfo.module_id === 6 ? 'left-avatar' : '']"
      >
        <van-image
          lazy
          fit="fill"
          :src="detailsInfo.photo_url ? detailsInfo.photo_url : this.imageSrc"
        >
          <div class="image-slot" slot="placeholder">
            <i class="el-icon-loading"></i>
          </div>
        </van-image>
        <span class="time" v-if="detailsInfo.duration"
          >{{ detailsInfo.duration }}分钟</span
        >
      </div>
      <div class="right-con">
        <!-- 仅‘行家’显示expert_name，其简介显示一行 -->
        <span class="name" v-if="detailsInfo.module_id === 6">{{
          detailsInfo.origin_data.expert_name
        }}</span>
        <div
          v-if="detailsInfo.description"
          :class="detailsInfo.module_id === 6 ? 'hangjia-intro' : 'intro'"
        >
          {{ descRichText }}
        </div>
        <!-- 资源信息 -->
        <div class="statistics">
          <!-- 网络课1 -->
          <div class="item-bottom" v-if="detailsInfo.module_id === 1">
            <img class="item-icon" src="@/assets/img/mobile/play.png" />
            <span class="item-text">{{
              detailsInfo.play_total_count || 0
            }}</span>
            <!-- <img class="item-icon" src="@/assets/img/mobile/score.png" />
            <span class="item-text">{{ detailsInfo.avg_score || 0 }}分</span> -->
            <span class="item-text">{{ detailsInfo.duration || 0 }}分钟</span>
            <img class="item-icon" src="@/assets/img/mobile/date.png" />
            <span class="item-text">{{ detailsInfo.created_time }}</span>
          </div>
          <!-- 面授课2 课单15 -->
          <div
            class="item-bottom"
            v-if="detailsInfo.module_id === 2 || detailsInfo.module_id === 15"
          >
            <img class="item-icon" src="@/assets/img/mobile/play.png" />
            <span class="item-text">{{
              detailsInfo.play_total_count || 0
            }}</span>
            <!-- <img class="item-icon" src="@/assets/img/mobile/score.png" />
            <span class="item-text">{{ detailsInfo.avg_score || 0 }}分</span> -->
            <img class="item-icon" src="@/assets/img/mobile/date.png" />
            <span class="item-text">{{ detailsInfo.created_time }}</span>
          </div>
          <!-- 笔记/文章8 图文9 文档16 -->
          <div
            class="item-bottom"
            v-else-if="
              detailsInfo.module_id === 8 ||
              detailsInfo.module_id === 9 ||
              detailsInfo.module_id === 16
            "
          >
            <img class="item-icon" src="@/assets/img/mobile/watch.png" />
            <span class="item-text">{{
              detailsInfo.play_total_count || 0
            }}</span>
            <img class="item-icon" src="@/assets/img/mobile/praise.png" />
            <span class="item-text">{{ detailsInfo.praise_count || 0 }}</span>
            <img class="item-icon" src="@/assets/img/mobile/word.png" />
            <span class="item-text">{{ detailsInfo.word_num || 0 }}</span>
            <img class="item-icon" src="@/assets/img/mobile/date.png" />
            <span class="item-text">{{ detailsInfo.created_time }}</span>
          </div>
          <!-- 案例7 码客5 -->
          <div
            v-else-if="
              detailsInfo.module_id === 7 || detailsInfo.module_id === 5
            "
            class="item-bottom"
          >
            <img class="item-icon" src="@/assets/img/mobile/watch.png" />
            <span class="item-text">{{
              detailsInfo.play_total_count || 0
            }}</span>
            <img class="item-icon" src="@/assets/img/mobile/praise.png" />
            <span class="item-text">{{ detailsInfo.praise_count || 0 }}</span>
            <img class="item-icon" src="@/assets/img/mobile/date.png" />
            <span class="item-text">{{ detailsInfo.created_time }}</span>
          </div>
          <!-- 行家6 -->
          <div v-else-if="detailsInfo.module_id === 6" class="item-bottom">
            <span class="item-text"
              >咨询量：{{ detailsInfo.origin_data.meet_num || 0 }}单</span
            >
            <!-- <span class="item-text"
              >评分：{{ detailsInfo.avg_score || 0 }}分</span
            > -->
            <!-- <span class="item-text">暂无评分</span> -->
          </div>
          <!-- 活动4 直播3 -->
          <div
            v-else-if="
              detailsInfo.module_id === 4 || detailsInfo.module_id === 3
            "
            class="item-bottom"
          >
            <img class="item-icon" src="@/assets/img/mobile/watch.png" />
            <span class="item-text">{{
              detailsInfo.play_total_count || 0
            }}</span>
            <img class="item-icon" src="@/assets/img/mobile/date.png" />
            <span class="item-text"
              >{{ detailsInfo.start_time || '' }} -
              {{ detailsInfo.end_time || '' }}</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { fomatSecond } from 'utils/tools'
// const moduleMap = {
//   1: { pic: 'default-netcourse', text: '网络课' },
//   2: { pic: 'default-facecourse', text: '面授课' },
//   3: { pic: 'default-live', text: '直播' },
//   4: { pic: 'default-activity', text: '活动' },
//   5: { pic: 'default-marker', text: '码客' },
//   6: { pic: 'default-hangjia', text: '行家咨询' },
//   7: { pic: 'default-case', text: '案例' },
//   8: { pic: 'default-note', text: '笔记' },
//   9: { pic: 'default-article', text: '图文' },
//   15: { pic: 'default-courselist', text: '课单' },
//   99: { pic: 'default-linkcourse', text: '外链' }
// }
export default {
  name: 'CategoryCard',
  props: {
    detailsInfo: {
      type: Object,
      default: () => {}
    },
    curIndex: {
      type: [Number, String]
    }
  },
  data() {
    return {
      imageSrc: require('@/assets/img/mobile/default-image.png'),
      fomatSecond
    }
  },
  computed: {
    // 简介富文本
    descRichText() {
      return this.parseRichText(this.detailsInfo.description)
    }
  },
  methods: {
    // 跳转到素材详情页
    linkTo() {
      const { module_id, content_id, content_url } = this.detailsInfo
      // 码客、行家、图文、外链暂不支持跳转
      const moduleIds = [5, 6, 9, 99]
      if (moduleIds.includes(module_id)) {
        this.$router.push({
          name: 'mobileError',
          query: {
            type: 2,
            href: encodeURIComponent(content_url)
          }
        })
      } else if (module_id === 1) {
        this.$router.push({
          name: 'play',
          query: {
            course_id: content_id || '',
            jump_from: this.$route.query.jump_from || '',
            from: this.$route.query.from || '',
            area_id: this.$route.query.area_id || ''
          }
        })
      } else if (module_id === 8) {
        console.log(window.__wxjs_environment === 'miniprogram')
        if (window.__wxjs_environment === 'miniprogram') {
          let path = `/pages/networkCourse/article/index?graphic_id=${content_id}`
          if ((this.$route.query.jump_from === 'CourseList' || this.$route.query.from === 'CourseList') && this.$route.query.area_id) {
            path = `/pages/networkCourse/article/index?graphic_id=${content_id}&from=CourseList&area_id=${this.$route.query.area_id}`
          }
          // 小程序内直接跳转
          window.wx.miniProgram.navigateTo({
            url: path
          })
        } else {
          // 打开二合一地址唤起小程序
          window.location.href = content_url
        }
        // console.log(typeof content_id === 'number')
        // // 文章id返回为number类型时，跳转到小程序页面
        // if (typeof content_id === 'number') {
        //   window.location.href = content_url
        // } else {
        //   // 返回为字符串时，跳转到不支持页面
        //   this.$router.push({
        //     name: 'unsupported',
        //     query: {
        //       href: encodeURIComponent(content_url)
        //     }
        //   })
        // }
      } else if (content_url) {
        window.location.href = content_url
      }
    },
    // 富文本解析处理
    parseRichText(str) {
      if (!str) return ''
      str = str.replace(/<span class='highlight'>/g, '@#$')
      str = str.replace(/<\/span>/g, '$#@')
      str = str.replace(/[<]+/g, '') // 正则去掉所有的<标记
      str = str.replace(/@#\$/g, `<span class='highlight'>`)
      str = str.replace(/\$#@/g, `</span>`)
      return str
    }
  }
}
</script>

<style lang="less" scoped>
.module-category-card {
  width: 100%;
  background: #fff;
  padding: 16px 0;
  overflow: hidden;
  .card-title {
    width: 100%;
    color: rgba(0, 0, 0, 0.9);
    font-size: 14px;
    line-height: 22px;
    text-overflow: ellipsis;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    .label-play-icon {
      width: 12px;
      height: 12px;
      margin-right: 8px;
    }
    .label {
      height: 18px;
      background: #ebeffc;
      border-radius: 2px;
      color: #3464e0;
      font-size: 12px;
      line-height: 18px;
      padding: 0 4px;
      margin-right: 10px;
      display: inline-block;
    }
    .highlight {
      color: #3464e0;
    }
  }
  .cur-course {
    color: #0052d9;
  }
  .card-content {
    display: flex;
    .left-con {
      width: 92px;
      height: 62px;
      border-radius: 2px;
      margin-right: 8px;
      overflow: hidden;
      position: relative;
      .van-image {
        width: 100%;
        height: 100%;
      }
      &.left-avatar {
        width: 61px;
        height: 61px;
        border-radius: 50%;
      }
      .pic {
        width: 100%;
        height: 100%;
      }
      .time {
        color: #fff;
        font-size: 10px;
        position: absolute;
        right: 4px;
        bottom: 2px;
        height: 16px;
        line-height: 16px;
        border-radius: 2px;
        opacity: 1;
        background: #00000080;
        padding: 0 4px;
      }
    }
    .right-con {
      flex: 1;
      width: 0;
      .name {
        line-height: 20px;
        color: rgba(0, 0, 0, 0.6);
        font-size: 12px;
      }
      .intro {
        width: 100%;
        height: 40px;
        color: rgba(0, 0, 0, 0.6);
        font-size: 12px;
        line-height: 20px;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
      .hangjia-intro {
        width: 100%;
        height: 20px;
        color: rgba(0, 0, 0, 0.6);
        font-size: 12px;
        line-height: 20px;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
      .highlight {
        color: #3464e0;
      }
      .statistics {
        width: 100%;
        margin-top: 6px;
        display: flex;
        align-items: center;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        .item-bottom {
          display: flex;
          align-items: center;
        }
        .item-icon {
          width: 12px;
          height: 12px;
          margin-right: 4px;
          flex-shrink: 0;
        }
        .item-text {
          color: rgba(0, 0, 0, 0.26);
          font-size: 10px;
          margin-right: 12px;
        }
      }
    }
    .module-99 {
      .name {
        line-height: 20px;
        color: rgba(0, 0, 0, 0.6);
        font-size: 12px;
      }
      .link {
        width: 100%;
        color: rgba(0, 0, 0, 0.3);
        font-size: 12px;
        line-height: 20px;
        margin-top: 4px;
        word-break: break-all;
        text-overflow: ellipsis;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
  }
}
</style>
