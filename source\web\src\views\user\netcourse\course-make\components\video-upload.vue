<template>
  <div class="subttile-upload">
      <!-- 审批视频播放 -->
      <div v-if="approveStatus" class="approve-video-box">
        <approveVideo  
        :content_id.sync="fileData.content_id"
        :getCurPlayTime="false"
        :autoPlay="false"
        ref="approveVideoRef"
        />
        <div class="item-box" v-show="fileData.duration">
          <span  class="label">视频时长： </span>
          <span class="value">{{ durationTime }}</span>
        </div>
        <div class="item-box">
          <span  class="label">视频大小： </span>
          <span class="value">{{ fileSize }}</span>
        </div>
      </div>
      <div v-else>
        <el-upload
        v-show="!uploadStatus && !fileData.content_id"
        class="upload-box" 
        action="" 
        :drag="true" 
        :with-credentials="true"
        :http-request="onUpload" 
        :file-list="fileList" 
        :show-file-list="false"
        >
          <el-button type="text" id="ppt-upload-btn">点击上传</el-button>
          <span class="split-line">/</span>
          <span class="upload-text">拖拽到此区域上传文件</span>
        </el-upload>
        <!-- 上传完成 -->
        <div class="has-upload"  v-show="uploadStatus">
          <div class="upload-satus-box" v-show="!(fileData.duration)">
            <img :src='require("@/assets/img/file-video.png")' alt="">
            <div class="right-progress">
              <div class="success-text">
                <span class="status">{{ processValue === 100 ? '上传完成' : '上传中'}}</span>
                <span class="percent" v-show="processValue < 100">{{ processValue }}%</span>
                <i class="el-icon-success icon" v-show="processValue === 100"></i>
              </div>
              <el-progress 
              :percentage="processValue" 
              :stroke-width="4" 
              color="#0052d9" 
              :show-text="false"
              >
            </el-progress>
            </div>
          </div>
          <div class="item-box" v-if="!(fileData.duration)">
            <span class="label">视频标题： </span>
            <span class="value">{{ fileData.fileName }}</span>
          </div>
          <!-- 视频回显 -->
          <demo-preview v-if="fileData.duration" ref="previewVideo" :demoData="fileData" :autoPlay="false" />
          <div class="item-box" v-show="fileData.duration">
            <span  class="label">视频时长： </span>
            <span class="value">{{ durationTime }}</span>
          </div>
          <div class="item-box">
            <span  class="label">视频大小： </span>
            <span class="value">{{ fileSize }}</span>
          </div>
          <div class="btn-box">
            <el-dropdown placement="bottom-start">
              <div :class="[{ 'disabled-el-menu-btn': approveStatus }, 'el-menu-btn']" ><span>更换视频素材</span></div>
              <el-dropdown-menu slot="dropdown" class="directly-upload-dropdown">
                <el-dropdown-item>
                  <span class="ai-icon icon"></span>
                  <span class="text" @click="handleMakeAI">AI快捷做课</span>
                </el-dropdown-item>
                <el-dropdown-item>
                  <span class="local-video-icon icon"></span>
                  <span class="text" @click="triggerUpload">上传本地视频</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div class="upload-tips">
          注意：更换视频素材后，课程将暂时停用，需重新审核后才可恢复可用。文件大小请勿超过4GB。
        </div>
      </div>
  </div>
</template>
<script>
import demoPreview from '../components/demo-preview.vue'
import approveVideo from './approve-video.vue'
import { transforTime } from 'utils/tools'

export default {
  name: 'voice-select',
  components: {
    demoPreview,
    approveVideo
  },
  props: {
    videoInfo: {
      type: Object,
      default: () => ({})
    },
    courseInfo: {
      type: Object,
      default: () => ({})
    },
    approveStatus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      transforTime,
      fileList: [],
      processValue: 0,
      uploadStatus: false,
      fileData: {
        content_id: '',
        fileName: '',
        fileSize: '',
        duration: 0
      },
      timer: null
    }
  },
  watch: {
    videoInfo: {
      deep: true,
      immediate: true,
      handler(val) {
        this.changeFileData(val)
      }
    }
  },
  computed: {
    fileSize() {
      return this.fileData?.fileSize ? Math.ceil((this.fileData.fileSize / 1048576) * 10) / 10 + 'M' : ''
    },
    durationTime() {
      if (this.fileData.duration) {
        return transforTime(this.fileData.duration)
      }
      return transforTime(this.courseInfo.est_dur * 60)
    }
  },
  destroyed() {
    this.timer = null
    clearInterval(this.timer)
  },
  methods: {
    changeFileData(val) {
      if (val.content_id) {
        this.uploadStatus = true
        this.fileData = {
          fileName: val.file_name,
          fileSize: val.file_size,
          content_id: val.content_id,
          duration: val.duration
        }
        this.processValue = 100
      }
    },
    pause() {
      if (this.approveStatus) {
        this.$refs.approveVideoRef && this.$refs.approveVideoRef.pause()
      } else {
        this.$refs.previewVideo && this.$refs.previewVideo.pause()
      }
    },
    onUpload(options) {
      let that = this
      const fileName = options.file.name
      const arr = fileName.split('.')
      const fileType = arr[arr.length - 1]
      if (!['mp4', 'avi', 'wmv', 'mov'].includes(fileType)) {
        this.$message.error('请选择.mp4、.avi、.wmv、或.mov文件')
        return
      }
      
      const size = options.file.size
      let fileSzie = 0
      try {
        fileSzie = size / 1024 / 1024 / 1024
      } catch (error) {
        fileSzie = 0
      }
      if (fileSzie > 4) {
        this.$message.error('文件大小请勿超过4GB')
        return
      }
      
      that.uploadStatus = true
      const authUrl = location.hostname.endsWith('.woa.com')
        ? process.env.VUE_APP_PORTAL_HOST_WOA
        : process.env.VUE_APP_PORTAL_HOST
      /* eslint-disable*/
      new contentCenter.uploadFile({
        file: options.file,
        type: 1, // 0表示上传图片，1视频 2音频 3文档
        appId: 'QLearningService',
        operateAuthUrl: `${authUrl}/training-portal-common/api/v1/portal/user/common/uploadOperateSignature`,
        onSuccess(res) {
          const content_id = res[0] && res[0].content_id
          that.fileData = {
            content_id,
            fileName,
            fileSize: options.file.size, // 保留1位小数
          }
          that.$emit('onVideoChange', that.fileData)
          that.$emit('onVideoChangeType', 'Video')
        },
        onError(err) {
          that.$message.error(err)
          that.uploadStatus = false
        },
        onProgress(info) {
          const percent = parseInt(info.percent * 10000) / 100
          that.timer = setTimeout(() => {
            that.processValue = percent
          }, 50)
          if(that.processValue >= 100) {
            that.uploadStatus = false
            clearInterval(that.timer)
          }
        }
      })
    },
    handleMakeAI() {
      const { net_course_id } = this.$route.query
      this.$router.push({ name: 'courseQuick', query: { net_course_id } })
    },
    triggerUpload() {
      this.uploadStatus = false
      this.deletePPT()
      document.getElementById('ppt-upload-btn').click()
    },
    deletePPT() {
      this.processValue = 0
      this.fileList = []
      this.fileData = {
        content_id: '',
        fileName: '',
        fileSize: '',
        duration: 0
      }
      this.$emit('onVideoChange', {})
    }
  }
}
</script>
<style lang="less">
.directly-upload-dropdown {
  padding: 4px;
  li + li {
    margin-top: 4px;
  }
  .el-dropdown-menu__item {
    display: flex;
    align-items: center;
    color: rgba(0,0,0,0.6);
    cursor: pointer;
  }
  .el-dropdown-menu__item:focus, 
  .el-dropdown-menu__item:not(.is-disabled):hover {
    color: rgba(0,82,217,1);
    font-size: 14px;
    background: rgba(245,247,249,1);
  }
  .icon {
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-right: 8px;
  }
  .ai-icon {
    background: url('~@/assets/img/li-ai-icon.png') no-repeat center/cover;
  }
  .local-video-icon {
    background: url('~@/assets/img/li-ai-icon.png') no-repeat center/cover;
  }
}
</style>
<style lang="less" scoped>
.subttile-upload {
  font-size: 14px;
  :deep(.upload-box) {
    .el-upload {
      width: 100%;
      height: 100%;
      .el-upload-dragger {
        width: 100%;
        height: 100%;
        line-height: 230px;
        height: 230px;
        border: unset;
        background-color: unset;
      }
      .split-line {
        margin: 0 8px;
        color: #333;
      }
      .upload-text {
        color: #666;
      }
    }
  }
  .upload-box,
  .has-upload {
    box-sizing: border-box;
    border-radius: 3px;
    background: rgba(251, 251, 251, 1);
    border-radius: 3px;
  }
  .has-upload {
    padding: 20px;
    .demo-preview {
      margin-bottom: 15px;
    }
    .upload-satus-box {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
      img {
        width: 40px;
        height: 40px;
      }
      .right-progress {
        margin-left: 8px;
        .success-text {
          height: 22px;
          display: flex;
          align-items: center;
          margin-bottom: 6px;
          color:#666666;
          .icon {
            font-size: 16px;
            color: #00a870;
            margin-left: 8px;
          }
        }
        .el-progress {
          width: 246px;
        }
      }
    }
    .item-box {
      line-height: 22px;
      .label {
        color: rgba(0,0,0,0.6);
      }
      .value {
        color: rgba(0,0,0,0.8);
      }
    }
    .item-box + .item-box {
      margin-top: 8px;
    }
    .btn-box {
      margin-top: 16px;
      .el-menu-btn {
        width: 116px;
        height: 32px;
        cursor: pointer;
        border-radius: 3px;
        background: rgba(0,82,217,1);
        padding: 5px 16px;
        color: #fff;
        span  {
          height: 22px;
          line-height: 22px;
        }
      }
      .disabled-el-menu-btn {
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }
  .upload-tips {
    margin: 4px 0 32px 0;
    line-height: 20px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.4);
  }
  .analysis-ppt {
    font-size: 14px;
  }
  .approve-video-box {
    padding: 20px 20px 32px;
    .item-box {
      line-height: 22px;
      .label {
        color: rgba(0,0,0,0.6);
      }
      .value {
        color: rgba(0,0,0,0.8);
      }
    }
    .item-box + .item-box {
      margin-top: 8px;
    }
  }
}
</style>