<template>
  <!-- 上传视频素材 -->
  <div class="material-video-container">
    <convention-banner mode="2" />
    <!-- 文章 -->
    <Article v-if="uploadType === 'Article'"></Article>
    <!-- 其他类型素材 -->
    <div v-else class="videoWrap">
      <p class="title">上传素材-{{ filterResourceInfo.name }}</p>
      <div class="tabs" v-if="['视频', '音频'].includes(filterResourceInfo.name)">
        <span v-for="item in tabList" :key="item.type" :class="{'active':tabIndex === item.type}" @click="changeType(item)">{{item.name}}</span>
        <!-- <span :class="{ 'active': tabIndex === 'basis' }" @click="tabIndex='basis'">基础配置</span>
        <span :class="{ 'active': tabIndex === 'chapter' }" @click="tabIndex='chapter'">章节信息</span>
        <span :class="{ 'active': tabIndex === 'article' }" @click="tabIndex='article'">文章信息</span> -->
      </div>
      <el-form :model="form" v-if="tabIndex === 'basis'" :rules="rules" ref="form" label-width="130px" class="video-form">
        <div class="title-box">
          <img src="@/assets/mooc-img/title-icon.png" />
          <span class="bassinfo-class-title">{{ filterResourceInfo.name }}素材</span>
        </div>
        <div class="upload-box">
          <el-upload
            v-show="!uploadStatus && !fileData.content_id"
            action
            :drag="true"
            :http-request="onUpload"
            :file-list="fileList"
            :show-file-list="false"
            :auto-upload="true"
            :accept="filterResourceInfo.accepts"
            :limit="10"
            :multiple="false"
            :before-upload="beforeAvatarUpload"
          >
            <el-button type="text" id="material-upload-btn">点击上传</el-button>
            <span class="split-line">/</span>
            <span class="upload-text">拖拽到此区域上传文件</span>
          </el-upload>
          <!-- 上传完成 -->
          <div class="has-upload" v-show="uploadStatus">
            <div class="top-progress" v-if="processValue < 100">
              <div class="progress-box">
                <div class="success-text">
                  <span class="status">{{ processValue === 100 ? '上传完成' : '上传中' }}</span>
                  <span class="percent" v-show="processValue < 100">{{ processValue }}%</span>
                  <i class="el-icon-success icon" v-show="processValue === 100"></i>
                </div>
                <el-progress :percentage="processValue" :stroke-width="4" color="#0052d9" :show-text="false">
                </el-progress>
              </div>
            </div>
            <div class="submit-status" v-else>
              <div class="video-preview" v-if="fileData.content_id && uploadType === 'Video' && currentStatus === 13 && file_id">
                <Video
                  class="video-box"
                  :content_id.sync="fileData.content_id"
                  :autoPlay="false"
                  ref="previewVideo"
                  @getVideoInfo="getVideoInfo"
                />
              </div>
              <div class="file-title-box">
                <span>{{ fileData.file_name }}</span>
                <i class="el-icon-success"></i>
              </div>
              <div class="item-box">
                <span class="label">文件大小： </span>
                <span class="value">{{ changeSize(fileData.file_size) }}</span>
              </div>
              <div class="item-box">
                <span class="label">上传日期： </span>
                <span class="value">{{ fileData.time }}</span>
              </div>
              <div class="upload-btn">
                <span class="btn" @click="resetUpload">重新上传</span>
                <!-- <span @click="delUpload">删除</span> -->
              </div>
            </div>
          </div>
          <!-- 视频 -->
          <span class="tips"
            v-if="uploadType === 'Video'">支持格式：.wmv、.mp4、.flv、.avi、.rmvb、.mpg、.mkv、.mov、.mts；单个视频小于4GB</span>
          <!-- 音频 -->
          <span class="tips" v-else-if="uploadType === 'Audio'">支持格式：.w4v、.m4a、.wma、.wav、.mp3、.amr；单个音频小于2GB</span>
          <!-- 文档 -->
          <span class="tips"
            v-else-if="uploadType === 'Doc'">支持格式：.doc、.docx、.ppt、.pptx、.xls、.xlsx、.pps、.pdf；单个文档小于200MB</span>
          <!-- scorm -->
          <span class="scrom-tips" v-else-if="uploadType === 'Scorm'">请注意，SCORM包只识别zip格式 <span class="check-scorm"
              @click="handleScrom">点击查看SCROM制作标准</span></span>
          <!-- 压缩包 -->
          <span class="flash-tips" v-else-if="uploadType === 'Flash'"><i
              class="el-icon-warning-outline"></i>压缩包生成时请在根目录下打包，只支持使用zip包</span>
        </div>
        <el-form-item v-if="uploadType === 'Video'" label="外挂字幕：" class="subttile-form">
          <el-radio-group v-model="show_caption" @change="handleOpenSubttile" :disabled="false">
            <el-radio :label="0">关闭</el-radio>
            <el-radio :label="1">开启</el-radio>
          </el-radio-group>
          <el-upload
          v-show="show_caption === 1 && !subttileSetInfo?.content_id"
          action
          accept=".srt,.vtt"
          :with-credentials="true"
          :http-request="subttileUpload"
          :file-list="captationFileList"
          :show-file-list="false"
          >
            <div id="workSrt-upload-btn" class="upload-btn">
              <i class="el-icon-upload2"></i>
              <span>上传字幕文件（支持srt文件）</span>
            </div>
          </el-upload>
        </el-form-item>
        <!-- 字幕已上传 -->
        <subttile-upload
        v-show="subttileSetInfo?.content_id"
        ref="subttileUpload"
        @confirmOnUpload="confirmOnUpload"
        @againUpload="againUpload"
        />
        <el-form-item v-if="uploadType === 'Flash'" label="启动文件路径：" prop="start_file_name">
          <div class="start-file">
            <el-input style="width: 496px" v-model="form.start_file_name" placeholder="请输入启动文件路径" clearable></el-input>
            <div class="flash-tips"><i class="el-icon-warning-outline"></i>启动名路径样例：根目录/xxx/xxx.html</div>
          </div>
        </el-form-item>
        <div class="title-box basic-info">
          <img src="@/assets/mooc-img/title-icon.png" />
          <span class="bassinfo-class-title">基本信息</span>
        </div>
        <el-form-item label="素材名称：" prop="file_show_name" class="input-style">
          <el-input style="width: 496px" v-model="form.file_show_name" placeholder="请输入素材名称" clearable>
          </el-input>
          <span class="custom-el-input-count">{{ handleValidor(form.file_show_name, 50, '1') }}/50</span>
          <p class="title-tips tips">为方便后续查找，素材命名请参考命名规范：XXX（课名）XXX | XX（项目名）-20230101（上传日期）</p>
        </el-form-item>
        <el-form-item label="素材简介：" class="project-detail-tincy" prop="file_desc">
          <sdc-mce-editor ref="editor" selector="file_desc" :env="editorEnv" :content="form.file_desc"
            :catalogue.sync="editorConfig.catalogue" :urlConfig="editorConfig.urlConfig" :options="editorConfig.options"
            :insertItems="insertItems" />
        </el-form-item>
        <el-form-item label="素材来源：" prop="content_type">
          <el-radio-group v-model="form.content_type">
            <el-radio label="PGC">组织/项目</el-radio>
            <el-radio label="UGC">个人分享</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="授权使用：" prop="admins">
          <sdc-staff-selector multiple ref="adminsSelectorRef" v-model="form.admins" size="small" :props="adminProps"
            placeholder="请选择可使用此素材的人员" @change="changeStaff" />
          <p class="tips auth-tips">授权后，指定人员可以在其他场景下使用此素材</p>
        </el-form-item>
        <el-form-item label="管理员：" prop="manage_admins">
          <sdc-staff-selector multiple ref="manageAdminsSelectorRef" v-model="form.manage_admins" size="small" :props="adminProps"
            placeholder="请选择管理员" @change="changeAdminsStaff" />
        </el-form-item>
        <template v-if="['视频', '音频'].includes(filterResourceInfo.name)">
          <el-form-item label="字幕文稿：" class="subttile-form">
            <el-radio-group v-model="form.show_caption" :disabled="false">
              <el-radio :label="1">显示</el-radio>
              <el-radio :label="0">隐藏</el-radio>
            </el-radio-group>
            <p class="radioTips">该设置仅在素材详情页生效，如果网课关联了此素材，该信息是否在网课详情页显示以网络课的配置为准</p>
          </el-form-item>
          <el-form-item label="文章信息：" class="subttile-form">
            <el-radio-group v-model="form.show_graphic" :disabled="false">
              <el-radio :label="1">显示</el-radio>
              <el-radio :label="0">隐藏</el-radio>
            </el-radio-group>
            <p class="radioTips">该设置仅在素材详情页生效，如果网课关联了此素材，该信息是否在网课详情页显示以网络课的配置为准</p>
          </el-form-item>
          <el-form-item label="章节信息：" class="subttile-form">
            <el-radio-group v-model="form.show_chapter" :disabled="false">
              <el-radio :label="1">显示</el-radio>
              <el-radio :label="0">隐藏</el-radio>
            </el-radio-group>
            <p class="radioTips">该设置仅在素材详情页生效，如果网课关联了此素材，该信息是否在网课详情页显示以网络课的配置为准</p>
          </el-form-item>
        </template>
      </el-form>
      <div class="articleSect" v-if="tabIndex === 'chapter'">
        <p class="articleTips">素材/网课详情页共用此章节信息，如果人工配置了分段章节，则在素材/网课详情页优先显示人工配置的信息</p>
        <span class="pseudo-class-title">智能分段章节</span>
        <div style="width:906px">
          <!-- 章节配置 -->
          <chapters-config
            :approveStatus="false"
            fromType='material'
            :activeName="tabIndex"
            :videoInfo="videoInfo"
            :chaptersList="chaptersList"
            :courseInfo.sync="editData"
            @refreshChapterList="refreshChapterList"
            @exportAiData="exportAiData"
            @updateChapterList="updateChapterList"
            ref="chaptersConfig"
          >
          </chapters-config>
        </div>
      </div>
      <div class="articleSect" v-if="tabIndex === 'article'">
        <p class="articleTips">素材/网课详情页共用此文章信息，如果人工配置了文章转写内容，则在素材/网课详情页优先显示人工配置的信息</p>
        <span class="pseudo-class-title">智能转写文章</span>
        <transliterateArticleVue ref="articleConfig" :currentTime="0" :estDur="editData.duration" :articleInfo="editData" :videoInfo="videoInfo" @updateArticle='updateArticle' @saved='savedArticle'/>
      </div>
      <!-- 底部按钮区域 -->
      <template v-if="tabIndex === 'basis'">
        <bottomFiexd @cancel="onCancel" @save="approveSafe" :submitText="file_id ? '保存' : '确定'" :isNeedConvention="true" :disabledBtn="editData.info_sec_status === 0" conventionStyle="margin-left: 160px"></bottomFiexd>
      </template>
      <!-- 信息审核再次编辑异步变化弹窗 -->
      <informationSafetyDialog :isShow.sync="informationSafetyShow" @safeConfirm="onSubmit()"/>
    </div>
  </div>
</template>
<script>
import subttileUpload from './child/subttile.vue'
import bottomFiexd from '@/views/components/botttomFixed.vue'
import chaptersConfig from '@/views/user/netcourse/course-make/components/chapters-config.vue'
import { getDate } from '@/utils/tools.js'
import { saveMaterial, metaerialDetail } from '@/config/mooc.api.conf.js'
import { getMaterialChapters } from 'config/api.conf'
import Article from './child/article.vue'
import { mapState } from 'vuex'
import conventionBanner from '@/views/components/convention-banner.vue'
import informationSafetyDialog from '@/components/information-safety-dialog'
import { Video } from '@/components/index'
import transliterateArticleVue from './child/transliterateArticle.vue'
// 文件类型
// const upload_info = {
//   'Video': '视频',
//   'Audio': '音频',
//   'Scorm': 'scorm',
//   'Flash': '压缩包',
//   'Doc': '文档'
// }
export default {
  components: {
    subttileUpload,
    bottomFiexd,
    Article,
    conventionBanner,
    informationSafetyDialog,
    Video,
    chaptersConfig,
    transliterateArticleVue
  },
  data() {
    return {
      editorEnv: process.env.NODE_ENV,
      show_caption: 0,
      form: {
        admins: [],
        manage_admins: [],
        content_type: 'PGC',
        file_show_name: '',
        file_desc: '',
        file_id: null,
        show_caption: 1,
        show_chapter: 1,
        show_graphic: 1
      },
      currentStatus: '',
      captationFileList: [],
      subttileSetInfo: {
        generate_type: 1,
        attachement_type: 'Caption',
        content_id: '',
        file_id: null,
        file_name: '',
        size: ''
      },
      fileData: {
        file_name: '',
        file_size: '',
        time: '',
        start_file_name: ''
      },
      fileList: [],
      adminProps: {
        staffID: 'staff_id',
        staffName: 'staff_name'
      },
      processValue: 0,
      uploadStatus: false,
      editData: {},
      informationSafetyShow: false,
      rules: {
        file_show_name: [
          { required: true, message: '请输入素材名称', trigger: 'blur' }
        ],
        content_type: [
          { required: true, message: '请选择素材来源', trigger: 'change' }
        ],
        start_file_name: [
          { required: true, message: '请输入启动文件路径', trigger: 'blur' }
        ]
      },
      editorConfig: {
        catalogue: false,
        options: { // 非必传字段
          selector: '#file_desc',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
              formatselect fontsizeselect lineheight |
              bold italic underline strikethrough |
              forecolor backcolor |
              dent align bullist numlist |
              insert codesample |
              fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      },
      curFile: {},
      insertItems: [
        'link',
        'table',
        'image',
        'profile',
        'embed',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      fileTypeArr: [{
        suffix: ['wmv', 'mp4', 'flv', 'avi', 'rmvb', 'mpg', 'mkv', 'mov', 'mpeg', 'x-matroska', 'quicktime'],
        file_type: '1',
        file_type_name: '视频',
        size: 4294967296,
        size_name: '4GB',
        upload_type: 'Video'
      },
      {
        suffix: ['w4v', 'm4a', 'wma', 'wav', 'mp3', 'amr', 'mpeg'],
        file_type: '2',
        file_type_name: '音频',
        size: 2147483648,
        size_name: '2G',
        upload_type: 'Audio'
      },
      {
        suffix: ['zip'],
        file_type: '3',
        file_type_name: 'Scorm',
        size: 4294967296,
        size_name: '4GB',
        upload_type: 'Scorm'
      },
      {
        suffix: ['zip', 'rar'],
        file_type: '4',
        file_type_name: '压缩包',
        size: 4294967296,
        size_name: '4GB',
        upload_type: 'Flash'
      },
      {
        suffix: ['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf', 'msword', 'vnd.ms-excel', 'vnd.ms-powerpoint'],
        file_type: '5',
        file_type_name: '文档',
        size: 209715200,
        size_name: '200MB',
        upload_type: 'Doc'
      }],
      accepts: '.wmv,.mp4,.flv,.avi,.rmvb,.mpg,.mkv,.mov,.w4v,.m4a,.wma,.wav,.mp3,.amr,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.pdf,.zip,.rar',
      saveMaterialSetting: {
        act_type: 21,
        columns: [
          {
            column_code: 'content_id',
            column_name: '素材（视频，音频，文章，文档）',
            column_type: 'file',
            call_type: ['async'],
            manual_review: false
          },
          {
            column_code: 'file_show_name',
            column_name: '素材名称',
            column_type: 'text',
            call_type: ['sync'],
            manual_review: false
          },
          {
            column_code: 'file_desc',
            column_name: '素材简介',
            column_type: 'richText',
            call_type: ['sync'],
            manual_review: false
          },
          {
            column_code: 'content',
            column_name: '正文',
            column_type: 'richText',
            call_type: ['async'],
            manual_review: false
          }
        ]
      },
      chaptersList: [],
      tabIndex: 'basis',
      currentTime: 0,
      tabList: [
        {
          name: '基础配置',
          type: 'basis'
        },
        {
          name: '章节信息',
          type: 'chapter'
        },
        {
          name: '文章信息',
          type: 'article'
        }
      ],
      saveTabObj: {
        'basis': 0,
        'chapter': 0,
        'article': 0
      },
      baseInfoString: '',
      fileDataString: '',
      show_captionString: '',
      subttileSetInfoString: '',
      fileDescString: '',
      articleString: '',
      sectionString: '[{"minutes":0,"seconds":0,"chapter_cover_url":"","chapter_title":"","chapter_content":"","chapter_status":1}]'
    }
  },
  computed: {
    ...mapState(['userLimitInfo']),
    // 标题、视频课件、封面、课程简介、标签 任何一个字段是否发生改变
    isFilesChange() {
      return !!(this.fileData.content_id !== this.editData.content_id)
    },
    uploadType() {
      return this.$route.query.uploadType
    },
    file_id() {
      return this.$route.query.file_id
    },
    // 上传类型---视频、音频等
    filterResourceInfo() {
      const { uploadType } = this.$route.query
      let name = ''
      let accepts = ''
      if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(uploadType)) {
        name = '视频'
        accepts = '.wmv,.mp4,.flv,.avi,.rmvb,.mpg,.mkv,.mov'
      } else if (uploadType === 'Audio') {
        name = '音频'
        accepts = '.w4v,.m4a,.wma,.wav,.mp3,.amr,.mpeg'
      } else if (uploadType === 'Article') {
        name = '文章'
      } else if (uploadType === 'Doc') {
        name = '文档'
        accepts = '.doc,.docx,.ppt,.pptx,.xls,.xlsx,.pdf'
      } else if (uploadType === 'Scorm') {
        name = 'Scorm'
        accepts = '.zip,.rar'
      } else if (uploadType === 'Flash') {
        name = '压缩包'
        accepts = '.zip,.rar'
      }
      return {
        name,
        accepts
      }
    },
    changeSize() {
      return (size) => {
        return size ? Math.ceil((size / 1048576 * 10) / 10) + 'M' : ''
      }
    },
    videoInfo() {
      const {
        content_id,
        file_name,
        file_size
      } = this.fileData
      const {
        file_show_name,
        new_file_name,
        file_extension,
        file_path,
        file_url,
        file_id,
        file_type,
        duration,
        status
      } = this.form
      return {
        content_id,
        file_name,
        file_size,
        file_show_name,
        new_file_name,
        file_extension,
        file_path,
        file_url,
        file_type,
        duration,
        status,
        file_id,
        net_course_id: file_id
      }
    }
  },
  created() {
    const { file_id } = this.$route.query
    this.editData.file_id = file_id
  },
  mounted() {
    // 编辑
    const { file_id, uploadType } = this.$route.query
    if (file_id && uploadType !== 'Article') {
      this.queryData(file_id)
      if (['视频', '音频'].includes(this.filterResourceInfo.name)) {
        this.getChaptersInfo(file_id)
      }
    } else {
      this.baseInfoString = JSON.stringify(this.form)
      this.fileDataString = JSON.stringify(this.fileData)
      this.show_captionString = JSON.stringify(this.show_caption)
      this.subttileSetInfoString = JSON.stringify(this.subttileSetInfo)
      this.fileDescString = '<p><br data-mce-bogus="1"></p>'
    }
  },
  methods: {
    getVideoInfo(data) {
      if (!data.is_successed) {
        this.currentStatus = ''
      }
    },
    // 信息安全审核
    approveSafe() {
      const { file_id, uploadType } = this.$route.query
      if (file_id) {
        const type = [
          'Video',
          'Video-2d',
          'Video-3d',
          'Video-ppt',
          'Audio',
          'Doc',
          'Flash'
        ]
        if (type.includes(uploadType) && this.isFilesChange) {
          this.informationSafetyShow = true
          return
        }
      }
      this.onSubmit()
    },
    queryData(file_id) {
      metaerialDetail(file_id).then((res) => {
        this.editData = res
        const {
          content_id,
          file_name,
          file_size,
          created_at,
          admins,
          manage_admins,
          content_type,
          file_show_name,
          new_file_name,
          file_extension,
          file_path,
          file_url,
          file_type,
          duration,
          file_desc,
          file_id,
          pub_file_attachements,
          start_file_name,
          status,
          show_caption,
          show_chapter,
          show_graphic
        } = res
        // 素材上传
        this.fileData = {
          content_id,
          file_name,
          file_size,
          time: created_at
        }
        this.processValue = 100
        this.uploadStatus = true
        this.currentStatus = status
        // 表单数据
        this.form = {
          start_file_name,
          admins,
          manage_admins,
          content_type,
          file_show_name,
          file_desc,
          file_id,
          show_caption,
          show_chapter,
          show_graphic,
          new_file_name,
          file_extension,
          file_path,
          file_url,
          file_type,
          duration,
          status,
          net_course_id: file_id
        }
        this.editData.net_course_id = file_id
        // 字幕
        if (pub_file_attachements?.content_id) {
          this.subttileSetInfo = JSON.parse(JSON.stringify(pub_file_attachements))
          this.show_caption = 1
          this.$refs.subttileUpload.initData(this.subttileSetInfo)
        }
        // 授权人员
        if (this.form.admins?.length) {
          this.$refs.adminsSelectorRef.setSelected(this.form.admins)
        }
        // 管理员
        if (this.form.manage_admins?.length) {
          this.$refs.manageAdminsSelectorRef.setSelected(this.form.manage_admins)
        }
        this.baseInfoString = JSON.stringify(this.form)
        this.fileDataString = JSON.stringify(this.fileData)
        this.show_captionString = JSON.stringify(this.show_caption)
        this.subttileSetInfoString = JSON.stringify(this.subttileSetInfo)
        this.fileDescString = file_desc || '<p><br data-mce-bogus="1"></p>'
      })
    },
    // 获取章节设置
    getChaptersInfo(id) {
      const params = {
        fileId: id
      }
      getMaterialChapters(params).then(res => {
        this.chaptersList = res || []
        this.$nextTick(() => {
          this.sectionString = JSON.stringify(this.$refs['chaptersConfig'].chaptersForm.chaptersList)
        })
      })
    },
    // 字幕上传
    subttileUpload(options) {
      this.$nextTick(() => {
        this.$refs.subttileUpload.onUpload(options)
      })
    },
    // 授权人员
    changeStaff(data) {
      this.form.admins = data
    },
    // 管理员
    changeAdminsStaff(data) {
      this.form.manage_admins = data
    },
    // 重新上传
    resetUpload() {
      this.delUpload()
      document.getElementById('material-upload-btn').click()
    },
    // 删除
    delUpload() {
      this.fileData = {
        file_name: '',
        file_size: '',
        time: '',
        content_id: ''
      }
      this.uploadStatus = false
      this.processValue = 0
    },
    changeType(item) {
      let flag = true
      if (this.tabIndex === 'basis') {
        let newbaseInfoString = JSON.stringify(this.form)
        let newfileDataString = JSON.stringify(this.fileData)
        let newshow_captionString = JSON.stringify(this.show_caption)
        let newsubttileSetInfoString = JSON.stringify(this.subttileSetInfo)
        let newfileDescString = this.$refs['editor'].getContent() || '<p><br data-mce-bogus="1"></p>'
        if (newfileDescString === '<p><br></p>') newfileDescString = '<p><br data-mce-bogus="1"></p>'
        console.log(newfileDescString, 'fdd~~~~~~~~~~~~~~~~~~~~')
        console.log(this.fileDescString, 'dfsds')
        if (newbaseInfoString !== this.baseInfoString || newfileDataString !== this.fileDataString || newshow_captionString !== this.show_captionString || newsubttileSetInfoString !== this.subttileSetInfoString || newfileDescString !== this.fileDescString) {
          flag = false
        }
      } else if (this.tabIndex === 'chapter') {
        let newsectionString = JSON.stringify(this.$refs['chaptersConfig'].chaptersForm.chaptersList)
        if (this.sectionString !== newsectionString) {
          flag = false
        }
      } else if (this.tabIndex === 'article' && !this.$refs['articleConfig'].showArticleEdit) {
        let newarticleString = JSON.stringify(this.$refs['articleConfig'].$refs['editor'].getContent())
        if (this.articleString !== newarticleString) {
          flag = false
        }
      }
      if (!flag) {
        this.$confirm('当前页面数据未保存，确定切换窗口吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.tabIndex = item.type
        })
      } else {
        this.tabIndex = item.type
      }
    },
    exportAiData(data) {
      this.chaptersList = data.chaptersRecord
    },
    updateChapterList(data) {
      this.sectionString = data
    },
    refreshChapterList() {
      if (this.editData.file_id || this.$route.query.file_id) {
        this.getChaptersInfo(this.editData.file_id || this.$route.query.file_id)
      }
    },
    updateArticle(data) {
      this.articleString = JSON.stringify(data)
    },
    savedArticle(data) {
      this.editData.artificial_graphic_id = data
    },
    onSubmit() {
      if (this.show_caption === 1 && !this.subttileSetInfo?.content_id) {
        this.$message.warning('请上传字幕')
        return
      }
      const { uploadType, file_id } = this.$route.query
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.form.admins = this.form.admins.map((e) => {
            return {
              share_type: 1,
              staff_id: e.staff_id,
              staff_name: e.staff_name,
              file_id: file_id ? this.editData.file_id : null
            }
          })
          this.form.manage_admins = this.form.manage_admins.map((e) => {
            return {
              staff_id: e.staff_id,
              staff_name: e.staff_name
            }
          })
          const { content_id, file_name, file_size } = this.fileData
          const params = {
            content_id,
            file_name,
            file_size,
            file_type: uploadType,
            pub_file_attachements: this.subttileSetInfo, // 字幕
            ...this.form,
            file_desc: this.$refs['editor'].getContent()
          }

          window.$informationReview && window.$informationReview.contentReview(params, this.saveMaterialSetting).then(res => {
            const { success, status_code, column_name, data } = res
            if (!success) return
            if (status_code === 2) {
              this.saveFrom(params)
            } else if ([-1, 1].includes(status_code)) {
              let msg = '内容已提交后台审核，请耐心等待，留意企微“小腾老师”机器人消息提醒。<br/>如有疑问，可联系graywu。'
              if (status_code === 1) {
                msg = `填写${column_name || '***'}信息中包含敏感内容，无法保存，请仔细检查，修改后再提交。<br/>如有疑问，可联系graywu。`
              }
              window.$informationReview.createMessageBox({
                title: '提示',
                content: msg,
                confirmText: status_code === 1 ? '返回修改' : '确认',
                onConfirm: () => {
                  if (status_code === -1) {
                    this.saveFrom(data)
                  }
                }
              })
            }
          }).catch(err => {
            console.log(err, 'rrr')
          })
        } else {
          return false
        }
      })
    },
    saveFrom(params) {
      saveMaterial(params).then((res) => {
        const { file_id } = this.$route.query
        const msg = file_id ? '创建成功' : '保存成功'
        this.$message.success(msg)
        try {
          window.opener.workReConnect()
        } catch (error) {
          console.log(error)
        }
        if (!file_id) {
          setTimeout(() => {
            window.close()
          }, 1000)
        } else {
          this.queryData(file_id)
        }
      })
    },
    onCancel() {
      setTimeout(() => {
        window.close()
      }, 1000)
    },
    // 关闭字幕--清空字幕信息
    handleOpenSubttile(val) {
      if (val === 0) {
        this.captationFileList = []
        this.subttileSetInfo = {
          generate_type: 1,
          attachement_type: 'Caption',
          content_id: '',
          file_id: null
        }
      }
    },
    confirmOnUpload(data) {
      const { caption_id, caption_name, caption_size } = data
      this.subttileSetInfo = {
        generate_type: 1,
        content_id: caption_id,
        attachement_type: 'Caption',
        file_id: null,
        file_name: caption_name,
        size: caption_size
      }
    },
    againUpload() {
      document.getElementById('workSrt-upload-btn').click()
    },
    handleScrom() {
      const url = 'https://iwiki.woa.com/pages/viewpage.action?pageId=1743581574'
      window.open(url, '_blank')
    },
    beforeAvatarUpload(file) {
      this.curFile = {}
      let typeObj = JSON.parse(JSON.stringify(this.fileTypeArr))
      let arrL = typeObj.length
      let isSize = false
      let fileType = file.type ? file.type : file.name.substring(file.name.lastIndexOf('.') + 1)
      for (let i = 0; i < arrL; i++) {
        let suffixL = typeObj[i].suffix.length
        for (let v = 0; v < suffixL; v++) {
          let suffix = typeObj[i].suffix
          let reg = RegExp(suffix[v])
          if (reg.exec(fileType)) {
            let size = typeObj[i].size
            isSize = file.size < size
            if (isSize) {
              this.curFile = typeObj[i]
              this.curFile.size = file.size
              break
            } else {
              this.$message.error(`上传${typeObj[i].file_type_name}大小不能超过 ${typeObj[i].size_name}!`)
              return false
            }
          }
        }
      }
      return isSize
    },
    onUpload({ file }) {
      let _this = this
      /* eslint-disable*/
      _this.uploadStatus = true
      let authUrl = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_PORTAL_HOST_WOA : process.env.VUE_APP_PORTAL_HOST
      this.currentStatus = ''
      new contentCenter.uploadFile({
        file: file,
        type: _this.curFile.update_type, // 0表示上传图片，1视频 2音频 3文档
        appId: 'QLearningService',
        startFilePath: this.form?.start_file_name || '',
        needUnzip: !!['Flash','Scorm'].includes(this.uploadType), // 压缩包是否解压
        operateAuthUrl: `${authUrl}/training-portal-common/api/v1/portal/user/common/uploadOperateSignature`,
        onSuccess(res) {
          console.log('上传成功', res)
          // _this.$sdc.loading.hide()
          const content_id = res[0] && res[0].content_id
          _this.fileData = {
            content_id,
            file_name: file.name,
            file_size: _this.curFile.size, // 保留1位小数
            time: getDate()
          }
          _this.$message.success(`${_this.curFile.file_type_name}上传成功`);
        },
        onError(err) {
          _this.$message.error('上传失败');
          _this.fileList = []
          _this.uploadStatus = false
        },
        onProgress(info) {
          const percent = parseInt(info.percent * 10000) / 100
          _this.timer = setTimeout(() => {
            _this.processValue = percent
          }, 50)
          if (_this.processValue >= 100) {
            _this.uploadStatus = false
            clearInterval(_this.timer)
          }
        }
      })
      /* eslint-disable*/
    },
    handleValidor(value, num) {
      if (value) {
        const china = value.match(/[\u4e00-\u9fa5]/g)
        const zhCount = china && china.join('').length
        const enCount = Math.ceil((value.length - zhCount) / 2)
        const total = zhCount + enCount
        if (total > num) {
          this.form.file_show_name = value.slice(0, -1)
        }
        return zhCount + enCount
      }
      return 0
    }
  }
}
</script>
<style lang="less" scoped>
.material-video-container {
  padding: 16px 20px 90px 20px;
  background: #f6f7f9;
  .videoWrap{
    background: #fff;
    border-radius: 4px;
    padding-bottom: 20px;
  }

  .input-style {
    position: relative;

    :deep(.el-input) {
      .el-input__inner {
        padding-right: 70px;
      }

      .el-input__suffix {
        position: absolute;
        right: 43px;
      }
    }
  }

  .custom-el-input-count {
    color: #ACACAC;
    background: #FFF;
    position: absolute;
    font-size: 12px;
    top: 6px;
    left: 454px;
    line-height: 20px;
  }

  .title {
    color: #000000e6;
    font-size: 16px;
    font-weight: bold;
    padding: 16px 20px;
    border-bottom: 1px solid #f3f3f3ff;
  }
  .tabs{
    margin: 16px 20px;
    span{
      display: inline-block;
      padding: 10px 16px;
      font-size: 14px;
      font-weight: 500;
      border-radius: 4px;
      cursor: pointer;
    }
    span.active{
      color: #0052d9ff;
      background: #f2f3ff;
    }
  }
  .articleSect{
    margin: 12px 36px;
    .articleTips{
      margin-bottom: 20px;
      font-size: 12px;
      color: #E34D59;
    }
    .pseudo-class-title {
      position: relative;
      padding-left: 16px;
      font-size: 16px;
      font-weight: bold;
      color: rgba(0,0,0,0.8);
      font-family: "PingFang SC";
      margin-bottom: 20px;
      display: inline-block;
    }
    .pseudo-class-title::before {
      position: absolute;
      top: 50%;
      transform: translatey(-50%);
      left: 0;
      content: '';
      width: 4px;
      height: 18px;
      background-color: #0052D9;
    }
  }

  :deep(.buttom) {
    .inner {
      width: 100%
    }
  }

  :deep(.project-detail-tincy) {
    width: 1200px;

    .tox.tox-tinymce {
      border: 1px solid #ccc !important;
      height: 450px;

      .tox-sidebar-wrap .tox-edit-area {
        min-height: 450px !important;
      }
    }
  }

  :deep(.sdc-staff-selector) {
    width: 496px;
  }

  .title-box {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    margin-top: 20px;
    padding: 0 20px;

    img {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .bassinfo-class-title {
      color: #000000;
      font-weight: bold;
      line-height: 22px;
      display: inline-block;
    }
  }

  .basic-info {
    margin-top: 36px;
  }

  .tips {
    color: #00000066;
    font-size: 14px;
    font-weight: 400;
    margin-top: 8px;
    display: inline-block;
    height: 22px;
    line-height: 22px;
  }

  .flash-tips,
  .scrom-tips {
    color: #ED7B2F;
    margin-top: 8px;
    display: inline-block;
    height: 22px;
    line-height: 22px;

    i {
      margin-right: 5px;
    }
  }

  .scrom-tips {
    .check-scorm {
      color: #0052d9ff;
      margin-left: 16px;
      cursor: pointer;
    }
  }

  .title-tips {
    display: block;
    // color: #ED7B2F;
    // i {
    //   margin-right: 5px;
    // }
  }

  .radioTips{
    margin-left: 20px;
    font-size: 12px;
    color:#E34D59;
  }

  .subttile-form {
    margin-top: 24px;

    // line-height: 32px;
    // margin-left: 25px;
    :deep(.el-form-item__content) {
      display: flex;
      align-items: center;
      height: 32px;
    }

    .upload-btn {
      height: 32px;
      line-height: 32px;
      border-radius: 3px;
      border: 1px solid #dcdcdcff;
      padding: 0 16px;
      margin-left: 16px;

      .el-icon-upload2 {
        margin-right: 10px;
        font-size: 16px;
      }
    }
  }

  .start-file {
    display: flex;
    flex-direction: column;
  }

  :deep(.upload-box) {
    margin-left: 44px;
    margin-bottom: 25px;

    .el-upload {
      width: 100%;
      height: 100%;

      .el-upload-dragger {
        width: 100%;
        height: 100%;
        line-height: 144px;
        height: 144px;
        // border: unset;
        background-color: unset;
        border-radius: 3px;
      }

      .split-line {
        margin: 0 8px;
        color: #333;
      }

      .upload-text {
        color: #666;
      }
    }
  }

  .has-upload {
    box-sizing: border-box;
    border: 1px dashed #d9d9d9;
    border-radius: 3px;
    background: #ffffffff;
    padding: 16px;
    font-size: 14px;
    // line-height: 144px;
    // height: 144px;
    .submit-status {
      .video-preview {
        background-color: #f0f0f0;
        width: 324px;
        height: 182px;
        margin-bottom: 15px;
        .video-box {
          width: 324px;
          height: 182px;
          border-radius: 3px;
        }
      }
    }
    .file-title-box {
      height: 22px;
      color: #000000e6;
      line-height: 22px;
      margin-bottom: 8px;

      .el-icon-success {
        color: #00a870;
        margin-left: 8px;
      }
    }

    .item-box {
      color: #00000066;
      font-size: 12px;
      height: 20px;
      line-height: 20px;
    }

    .upload-btn {
      color: #0052d9ff;
      margin-top: 20px;

      span {
        cursor: pointer;
      }

      .btn {
        margin-right: 16px;
      }
    }
  }

  .top-progress {
    margin-left: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;

    .progress-box {

      .success-text {
        display: flex;
        align-items: center;
        margin-bottom: 6px;
        color: #666666;

        .icon {
          font-size: 16px;
          color: #00a870;
          margin-left: 8px;
        }
      }
    }

    .el-progress {
      width: 246px;
    }
  }

  .video-form {
    margin-top: 20px;
  }

  .placeholder-box {
    width: 100%;
    height: 70px;
    background: #f6f7f9;
  }
}
</style>
