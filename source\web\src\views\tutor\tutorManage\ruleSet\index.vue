<template>
  <div class="rule-set-container">
    <div class="custom-tas">
      <span 
      :class="[{'active-tab': activeTab === item.value}, 'item-tab']" 
      v-for="item in tabList" 
      :key="item.label"
      @click="handleTab(item)"
      >{{ item.label }}</span>
    </div>
    <div class="rule-main" v-if="activeTab === 1">
      <!-- 门槛资格 -->
      <div class="rule-title">
        <div class="title">门槛资格</div>
        <CustomTips
        title="BG如未配置个性化规则，则默认沿用公司基础规则，BG配置的个性化规则需高于公司基础要求" 
        IconName="el-icon-warning-outline" 
        backgroundColor="#fdf6ec" 
        color="#FF7548"
        class="rule-tips"
        >
        </CustomTips>
      </div>
      <el-row class='multips-rule-box' :gutter="24">
        <div v-for="(e, index) in ruleList" :key="index">
          <el-col :span="6">
            <div class='single-rule'>
              <div class="single-title">
                <span>{{ e.bg_id === 0 ? `${e.bg_name}基础规则` : `${e.bg_name}个性化规则` }}</span>
                <span class="icon" v-if="showRule(e)" @click="ruleEdit(e)"></span>
              </div>
              <div class="update-info">
                <span class="update-common">
                  <span class="label">更新时间：</span>
                  <span class="value">{{ e.update_name !== 'system' ? e.updated_at : '-' }}</span>
                </span>
                <span class="update-common">
                  <span class="label">更新人：</span>
                  <span class="value">{{ e.update_name !== 'system' ? e.update_name : '-' }}</span>
                </span>
              </div>
              <div class="position-info" v-if="e.enabled">
                <div class="row-value">
                  <span class="label">专业职级：</span>
                  <span class="value"><span class="blue-value">{{ e.level_requirement }}</span>级及以上</span>
                </div>
                <div class="row-value">
                  <span class="label">司龄：</span>
                  <span class="value"><span class="blue-value">{{ e.working_years_requirement }}</span>年及以上</span>
                </div>
                <div class="row-value">
                  <span class="label">绩效：</span>
                  <span class="value">近<span class="blue-value">{{ e.performance_requirement }}</span>次绩效GOOD及以上</span>
                </div>
                <div class="row-value">
                  <span class="label">BG工作年限：</span>
                  <span class="value">
                    <span class="blue-value">{{ e.bg_work_requirement }}</span>{{ e.bg_work_requirement ? '年及以上' : '-' }}
                  </span>
                </div>
              </div>
              <div class="position-info none-position-info" v-else>
                <p>未开启个性化规则配置</p>
                <p>沿用公司基础规则</p>
              </div>
            </div>
          </el-col>
        </div>
      </el-row>
      <!-- 认证要求 -->
      <div class="rule-title">
        <div class="title">认证要求</div>
        <CustomTips
        title="基于公司+BG认证要求进行校验，仅支持配置Q-Learning平台的网络课、培养项目及考试；如果提示已失效，将跳过此项完成状态的校验，请尽快调整配置" 
        IconName="el-icon-warning-outline" 
        backgroundColor="#fdf6ec" 
        color="#FF7548"
        class="rule-tips"
        >
        </CustomTips>
      </div>
      <div class="require-box">
        <div class="require-for-row" v-for="(e, index) in requireList" :key="index">
          <div class="require-row">
            <span class="title">{{ e.bg_name }}</span>
            <span v-if="showRule(e)" class="icon r-icon" @click="editRequire(e)"></span>
            <template v-if="e.rule_course_info && e.rule_course_info.length > 0">
              <div class="reuqire-wrap">
                <div :class="['require-list', { 'delete-require-list': !c.enabled }]" v-for="(c, i) in e.rule_course_info" :key="i" @click="toDetail(c)">
                  <span class="rq-module_name"> {{!c.enabled ? `【已失效】【${module(c)}】` : `【${module(c)}】`}}</span>
                  <span class="rq-title">{{ c.course_name }}</span>
                </div>
              </div>
            </template>
            <div v-else class="none-requireData">未配置</div>
          </div>
        </div>
      </div>
      <!-- 聘书发放 -->
      <template v-if="userRole.super_admin || userRole.bg_admin || userRole.dept_admin">
        <div class="rule-title">
          <div class="title">聘书发放</div>
          <CustomTips
          title="如果配置的证书已被删除，将无法正常发放聘书，请尽快调整配置；修改发放的聘书后，针对已发放的导师不会补发新证书" 
          IconName="el-icon-warning-outline" 
          backgroundColor="#fdf6ec" 
          color="#FF7548"
          class="rule-tips"
          >
          </CustomTips>
        </div>
        <div class="certificate-row">
          <div v-if="cerInfo.certification_name" class="certificate-btn">{{ cerInfo.certification_name }}</div>
          <span v-if="userRole.super_admin" class="icon cer-icon" @click="certificateDialog=true"></span>
          <span class="cer-tips" v-if="!cerInfo.enabled">该证书已被删除，请尽快更换</span>
        </div>
        <el-image
          lazy fit="fill"
          :src="cerImg"
          class="certificate-img"
        >
          <div class="error-cover" slot="error">
            <img :src="require('@/assets/img/default_bg_img.png')" alt="" />
          </div>
        </el-image>
      </template>
    </div>
    <div class="admin-main" v-if="activeTab === 2">
      <div class="company-admin">
        <div class="company-admin common-title-box">
          <span class="title-icon"></span>
          <span class="title">公司管理员</span>
        </div>
        <div class="admin-content">
          <span class="label">管理员：</span>
          <template>
            <span v-if="isAdminSave" class="custom-unit-content">{{ companyAdminContent}}</span>
            <sdc-staff-selector 
            v-else
            ref="companyAdminRef"
            @change="changeAdminsStaff" 
            :disabled="isAdminSave" 
            selectClass="custom-select-unit" 
            :props="adminProps"
            multiple
            showFullTag
            v-model="adminList" 
            >
            </sdc-staff-selector>
          </template>
          <el-button v-if="isAdminSave" @click="editCompanyAdmin" type="primary" size="small">修改</el-button>
          <template v-else>
            <el-button type="primary" size="small" @click="handleAdminSave">保存</el-button>
            <el-button size="small" @click="isAdminSave=true">取消</el-button>
          </template>
        </div>
      </div>
      <div class="bg-admin">
        <div class="common-title-box">
          <span class="title-icon"></span>
          <span class="title">BG管理员</span>
        </div>
        <div class="table-admin">
          <table>
            <tr> <!-- 表格的头部 -->
              <th>BG</th>
              <th class="admin-th">管理员</th>
              <th>操作</th>
            </tr>
            <tr v-for="(e, index) in bgList" :key="index"> <!-- 每一行数据 -->
              <td>{{ e.bg_name }}</td>
              <td class="admin-td">
                <span v-if="e.disabled">{{ bgAdminContent(e) }}</span>
                <sdc-staff-selector
                v-else
                :ref="bgSelectRef(index)"
                selectClass="td-select-unit" 
                multiple 
                v-model="e.admins" 
                showFullTag
                :props="adminProps"
                @change="changeSingleStaff($event, e)"
                >
                </sdc-staff-selector>
              </td>
              <td class="btn-td">
                <el-button @click="handleSingleEdit(e, index)" v-if="e.disabled" type="text" size="small">修改</el-button>
                <template v-else>
                  <el-button @click="handleSingleSave(e)" type="text" size="small">保存</el-button>
                  <el-button @click="handleSingleCancle(e)" type="text" size="small">取消</el-button>
                </template>
              </td>
            </tr>
          </table>
        </div>
      </div>
      <div class="department-admin">
        <div class="common-title-box">
          <span class="title-icon"></span>
          <span class="title">部门管理员</span>
        </div>
        <div class="department-content">
          部门管理员默认为部门bp，如部门没有bp，根据BP关系链取上级组织的HRBP和BP管理者作为部门管理员，直到找到为止；
          如果找到的上级组织的HRBP和BP管理者管理职级在L3-1及以上或专业职级在14级及以上，则不默认配置为
          部门管理员工，该部门管理员为空
        </div>
      </div>
    </div>
    <!-- 门槛规则 -->
    <ruleDialog v-if="ruleDialogShow" ref="ruleDialogRef" :companyRuleData="companyRuleData" :visible.sync="ruleDialogShow" @updateRule="thresholdList"></ruleDialog>
    <!-- 认证要求 -->
    <requireDialog v-if="requireDialogShow" ref="requireDialogRef" :visible.sync="requireDialogShow" :requireData="requireData" @updateCertificate="certificationsList"></requireDialog>
    <!-- 获取证书 -->
    <certificateList v-if="certificateDialog" :visible.sync="certificateDialog" :cerInfo="cerInfo" @confirmCerificateList="confirmCerificateList"></certificateList>
  </div>
</template>
<script>
import CustomTips from '@/components/tips.vue'
import { actTypes } from '../../utils/tools.js'
import { 
  thresholdList, 
  certificationsList, 
  saveAdmin, 
  adListAPI,
  getcertificate,
  saveCertificate
} from '../../api/tutor.api.conf'
import certificateList from './child/certificateList.vue'
import requireDialog from './child/requireDialog.vue'
import ruleDialog from './child/ruleDialog'
import { mapState } from 'vuex'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]
export default {
  components: {
    CustomTips,
    certificateList,
    ruleDialog,
    requireDialog
  },
  data() {
    return {
      // tabList: [
      //   { label: '资质校验规则', value: 1 },
      //   { label: '管理员配置', value: 2 }
      // ],
      activeTab: 1,
      ruleList: [],
      requireList: [],
      adminList: '',
      isAdminSave: true,
      adminProps: {
        staffID: 'staff_id',
        engName: 'staff_name',
        staffName: 'staff_full_name'
      },
      companyData: {
        admins: []
      },
      bgList: [],
      initBgList: [],
      certificateDialog: false,
      cerInfo: {},
      ruleDialogShow: false,
      requireDialogShow: false,
      requireData: {}
    }
  },
  computed: {
    ...mapState({
      userRole: state => state.userRole
    }),
    companyAdminContent() {
      return this.companyData.admins.map((e) => e.staff_full_name).join(`；`)
    },
    bgAdminContent() {
      return (v) => {
        return v.admins.map((e) => e.staff_full_name).join(`；`)
      }
    },
    bgSelectRef() {
      return (index) => {
        return `bgSelectRef${index}`
      }
    },
    module() {
      return (e) => {
        const actMap = actTypes.find((el) => el.act_type === e.act_type)
        return actMap.act_type_name
      }
    },
    cerImg() {
      return `${envName.contentcenter}content-center/api/v1/content/imgage/${this.cerInfo.certification_img}/preview`
    },
    showRule() {
      const { super_admin, bg_admin, bg_ids, dept_admin, dept_ids } = this.userRole
      return (e) => {
        let flag = false
        if (super_admin) { // 公司
          flag = true
        } else if (bg_admin && bg_ids.includes(e.bg_id)) { // bg
          flag = true
        } else if (dept_admin && dept_ids.includes(e.bg_id)) { // 部门
          flag = true
        }
        return flag
      }
    },
    tabList() {
      const tabList = [
        { label: '资质校验规则', value: 1 }
      ]
      if (this.userRole.super_admin) {
        tabList.push({
          label: '管理员配置', value: 2
        })
      }
      return tabList
    }
  },
  mounted() {
    this.thresholdList()
    this.certificationsList()
    this.getcertificate()
  },
  methods: {
    // 认证要求编辑
    editRequire(e) {
      this.requireDialogShow = true
      this.requireData = e
    },
    // 门槛编辑
    ruleEdit(e) {
      this.ruleDialogShow = true
      this.$nextTick(() => {
        this.$refs.ruleDialogRef.initData(e)
      })
    },
    getcertificate() {
      getcertificate().then((res) => {
        this.cerInfo = res
      })
    },
    toDetail(v) {
      if (!v.course_url || !v.enabled) return
      window.open(v.course_url, '_blank')
    },
    handleTab(row) {
      this.activeTab = row.value
      if (row.value === 2) {
        this.getAdminList()
      }
    },
    editCompanyAdmin() {
      this.isAdminSave = !this.isAdminSave
      this.$nextTick(() => {
        this.$refs.companyAdminRef && this.$refs.companyAdminRef.setSelected(this.companyData.admins)
      })
    },
    getAdminList() {
      adListAPI().then((res) => {
        const list = (res || []).map((e) => {
          return {
            ...e,
            disabled: true
          }
        })
        this.companyData = list.find((e) => e.bg_id === 0)
        this.bgList = list.filter((e) => e.bg_id !== 0)
        this.initBgList = JSON.parse(JSON.stringify(this.bgList))
      })
    },
    // 单挑数据获取
    changeSingleStaff(val, e) {
      this.$set(e, 'admins', val)
    },
    // 管理员获取
    changeAdminsStaff(val) {
      this.adminList = val
    },
    // 单条编辑
    handleSingleEdit(e, index) {
      this.$set(e, 'disabled', false)
      this.$nextTick(() => {
        const refName = `bgSelectRef${index}`
        this.$refs[refName][0] && this.$refs[refName][0].setSelected(e.admins)
      })
    },
    // 单条取消
    handleSingleCancle(v) {
      const res = this.initBgList.find((e) => v.bg_id === e.bg_id)
      this.$set(v, 'disabled', true)
      this.$set(v, 'admins', res.admins)
    },
    // 单条数据保存
    handleSingleSave(e) {
      const params = {
        bg_id: e.bg_id, // bg
        admins: e.admins
      }
      saveAdmin(params).then(() => {
        this.$message.success('保存成功')
        this.$set(e, 'disabled', true)
      })
    },
    // 管理员保存
    handleAdminSave() {
      const params = {
        bg_id: 0, // 公司
        admins: this.adminList
      }
      saveAdmin(params).then(() => {
        this.$message.success('保存成功')
        this.isAdminSave = true
        this.companyData.admins = this.adminList // 更新后重新赋值
      })
    },
    thresholdList() {
      thresholdList().then((res) => {
        this.ruleList = res.map((e) => {
          return {
            ...e,
            bg_name: e.bg_id === 0 ? '公司' : e.bg_name
          }
        })
        this.companyRuleData = this.ruleList.find((e) => e.bg_id === 0)
      })
    },
    certificationsList() {
      certificationsList().then((res) => {
        this.requireList = res.map((e) => {
          return {
            ...e,
            bg_name: e.bg_id === 0 ? '公司' : e.bg_name
          }
        })
      })
    },
    // 证书回传数据
    async confirmCerificateList(data) {
      await saveCertificate({ certification_id: data.certificateId })
      this.getcertificate()
    }
  }
}
</script>
<style lang='less' scoped>
.rule-set-container {
  background-color: #fff;
  border-radius: 4px;
  padding-bottom: 53px;
  .custom-tas {
    height: 48px;
    padding-left: 36px;
    line-height: 48px;
    border-bottom: 1px solid #E7E7E7;
    .item-tab {
      margin-right: 24px;
      display: inline-block;
      color: #00000099;
      font-size: 14px;
      line-height: 22px;
      cursor: pointer;
    }
    .active-tab {
      color: #0052D9;
      border-bottom: 2px solid #0052D9;
      padding-bottom: 10px;
    }
  }
  .rule-main {
    padding: 20px 24px;
    .icon {
      display: inline-block;
      width: 12px;
      height: 12px;
      background: url('~@/assets/tutor/edit.png') no-repeat center / cover;
      cursor: pointer;
    }
    .rule-tips {
      border-radius: 3px;
      display: flex;
      align-items: center;
      height: 40px;
    }
    .rule-title {
      display: flex;
      align-items: center;
      .title {
        color: #000000e6;
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        margin-right: 24px;
      }
    }
    .multips-rule-box {
      margin-top: 20px;
      .single-rule {
        height: 184px;
        border-radius: 4px;
        border: 1px solid #EEE;
        padding: 12px;
        margin-bottom: 24px;
        .single-title {
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #000000e6;
          font-size: 14px;
          font-weight: bold;
          line-height: 22px;
        }
        .update-info {
          margin-top: 8px;
          line-height: 14px;
          .update-common {
            font-size: 10px;
            margin-right: 12px;
            .label {
              color: #00000099;
            }
            .value {
              color: #000000e6;
            }
          }
        }
        .position-info {
          font-size: 12px;
          border-radius: 4px;
          background: #F9F9F9;
          padding: 8px;
          margin-top: 8px;
          height: 108px;
          .row-value {
            margin-bottom: 12px;
          }
          .row-value:last-of-type {
            margin-bottom: unset;
          }
          .label {
            color: #00000099;
          }
          .value {
            color: #000000e6;
          }
          .blue-value {
            color: #0052D9;
          }
        }
        .none-position-info {
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          line-height: 20px;
        }
      }
    }
    .require-box {
      margin-top: 12px;
      margin-bottom: 12px;
      .reuqire-wrap {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
      }

      .require-row {
        border-top: 1px solid #EEEEEE;
        line-height: 22px;
        display: flex;
        align-items: center;
        padding: 12px 0px 0px;
        .title {
          color: #000000e6;
          font-size: 14px;
          font-weight: bold;
          line-height: 22px;
          width: 36px;
          flex-shrink: 0;
          margin-bottom: 12px;
        }
        .r-icon {
          margin-left: 12px;
          margin-right: 12px;
          width: 16px;
          height: 16px;
          flex-shrink: 0;
          margin-bottom: 12px;
        }
        .require-list {
          background-color: #F2F3FF;
          border-radius: 3px;
          color: #0052D9;
          padding: 2px 8px;
          margin-right: 12px;
          text-decoration: underline;
          cursor: pointer;
          margin-bottom: 12px;
          font-size: 12px;
          .rq-title {
            line-height: 20px;
          }
        }
        .delete-require-list {
          background-color: #F3F3F3;
          color: #00000099;
          cursor: default;
        }
      }
      .require-for-row:first-of-type {
        .require-row {
          border-top: unset;
        }
      }
      .none-requireData {
        color: #00000099;
        font-size: 12px;
        line-height: 14px;
        margin-bottom: 12px;
      }
    }
    .certificate-row {
      margin-top: 20px;
      .certificate-btn {
        display: inline-block;
        background-color: #ECF2FE;
        border-radius: 4px;
        color: #000000e6;
        line-height: 36px;
        height: 36px;
        padding: 0 16px;
        margin-right: 16px;
      }
      .cer-icon {
        margin-right: 16px;
      }
      .cer-tips {
        color: #D54941;
      }
    }
    .certificate-img {
      width: 400px;
      height: 288px;
      margin-top: 16px;
      .error-cover {
        img {
          width: 400px;
          height: 288px;
        }
      }
    }
  }
  .admin-main {
    padding: 28px 32px;
    :deep(.custom-select-unit) {
      width: 496px;
      margin-right: 24px;
    }

    .common-title-box {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      .title-icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('~@/assets/mooc-img/title-icon.png') no-repeat center / cover;
        margin-right: 8px;
      }
      .title {
        color: #000000e6;
        font-size: 14px;
        font-weight: bold;
        line-height: 22px;
      }
    }
    .company-admin {
      margin-bottom: 28px;
      .admin-content {
        display: flex;
        align-items: center;
        margin-left: 24px;
        .el-button {
          width: 60px;
        }
        .el-button+.el-button {
          margin-left: 12px;
        }
        .label {
          color: #000000cc;
          font-size: 14px;
          line-height: 22px;
        }
        .custom-unit-content {
          margin-right: 24px;
          color: #000000e6;
          line-height: 22px;
        }
      }
    }
    .bg-admin {
      margin-bottom: 28px;
      .table-admin {
        padding: 0 28px;
        table {
          width: 100%;
          border-collapse: collapse;
        }
        th {
          color: #00000066;
        }
        td {
          color: #000000e6;
        }
        th, td {
          border: 1px solid #DCDCDC;
          padding: 12px 16px;
          text-align: left;
          line-height: 22px;
          width: 25%;
          height: 46px;
        }
        .admin-th, .admin-td {
          width: 50%;
        }
        .admin-td {
          padding: 7px 16px;
        }
        .btn-td {
          text-align: center;
          .el-button + .el-button {
            margin-left: 20px;
          }
        }
      }
    }
    .department-content {
      color: #000000e6;
      font-size: 14px;
      line-height: 22px;
      margin-left: 24px;
    }
  }
}
</style>
