<template>
  <div class="chapter-info">
    <div class="ai-tips-box" v-if="showChapterTips">
      <i class="el-icon-warning" style="color: #0052D9"></i>
      <span class="tips">当前显示的章节数据为智能生成，如有错漏，<a href="https://km.tencent.com/openkm/url/lpciih">可点此反馈</a></span>
    </div>
    <div class="chapter-list-box" v-if="chapterSummaryList.length > 0">
      <div class="chapter-item" v-for="(item, index) in chapterSummaryList" :key="index" @click="slidTo(item)">
        <div class="info-box-top">
          <div class="info-left">
            <van-image
              lazy 
              fit="fill"
              class="chapter-image"
              :src="item.imgUrl ? item.imgUrl : imageSrc"
            >
              <div class="image-slot" slot="placeholder">
                <i class="el-icon-loading"></i>
              </div>
            </van-image>
          </div>
          <div class="info-right">
            <p class="title">{{ item.chapter_title }}</p>
            <div class="bottom">
              <span class="chapter-time">{{ item.chapter_time }}</span>
              <span class="chapter-label" v-if="isPlaying(item.chapter_time_point, index)">
                <span class="play-icon"></span>播放中
              </span>
              <span v-else class="play-tips">
                <span class="to-play-icon"></span>
                跳转至此
              </span>
            </div>
          </div>
        </div>
        <div class="chapter-desc-box" v-if="item.chapter_content && item.chapter_content !== '\n'">
          <p class="chapter-desc" v-html="item.chapter_content" :class="{'maxheight': !item.showFullText}" :ref="'descText' + index"></p>
          <span class="light-key" :class="{'no-full-text': !item.showFullText}" v-if="item.showBtn" @click.stop="handleMore(item)">{{ item.showFullText ? '收起' : '...更多' }}</span>
        </div>
      </div>
    </div>
    <div v-else class="video-chapter-empty">
      <img src="@/assets/img/mobile/empty-note.png" alt="" />
      <div class="empty-text">{{$langue('NetCourse_NoChapter', { defaultText: '暂无相关章节' })}}～</div>
    </div>
  </div>
</template>

<script>
// import { Toast } from 'vant'
export default {
  props: {
    chapterSummaryList: {
      type: Array,
      default: () => ([])
    },
    // 当前视频的总播放时长
    duration: {
      type: Number,
      default: 0
    },
    isInteractive: {
      type: Boolean, // 是否开启了互动
      default: false
    },
    chapterData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      imageSrc: require('@/assets/img/mobile/default-image.png'),
      current_chapter_time: 0
    }
  },
  computed: {
    showChapterTips() {
      const { chapter_ai_content_list, chapter_content_list } = this.chapterData
      return !chapter_content_list?.length && chapter_ai_content_list?.length
    }
  },
  mounted() {
    // 暂时放在这里的，是否展示更多按钮
    // this.initChapterData(this.chapterSummaryList)
  },
  watch: {
    chapterSummaryList: {
      deep: true,
      immediate: true,
      handler(val) {
        if (val.length > 0) {
          this.initChapterData(val)
        }
      }
    }
  },
  methods: {
    // 初始化数据
    initChapterData(list) {
      this.$nextTick(() => {
        list.forEach((ele, ind) => {
          if (ele.chapter_content && ele.chapter_content !== '\n') {
            ele.showFullText = false
            let element = this.$refs['descText' + ind][0]
            if (element.scrollHeight > element.clientHeight) {
              ele.showBtn = true
            } else {
              ele.showBtn = false
            }
            // console.log(ele.showBtn, 'showBtn-----')
          }
        })
        this.$forceUpdate()
      })
    },
    // 从外部获取当前视频正在播放的时间点 playTime
    slidToContent(currentTime) {
      this.current_chapter_time = Math.ceil(currentTime)
    },
    // 判断为播放中的章节
    isPlaying(chapter_time, index) {
      let next_chapter_time = 0
      if (index < this.chapterSummaryList.length - 1) {
        next_chapter_time = this.chapterSummaryList.length > 0 && this.chapterSummaryList[index + 1].chapter_time_point
      } else {
        next_chapter_time = this.duration
      }
      return (this.current_chapter_time >= chapter_time * 1) && (this.current_chapter_time < next_chapter_time * 1)
    },
    // 展开更多
    handleMore(item) {
      item.showFullText = !item.showFullText
      // this.$set(item, 'showFullText', !item.showFullText)
      this.$forceUpdate()
      // console.log(item.showFullText, 'showFullText----', this.list)
    },
    // 视频播放进度跳转到某个章节节点
    slidTo(item) {
      // if (this.isInteractive) { // 互动已全屏，点不到章节
      //   Toast('已开启互动，不可跳转进度')
      //   return
      // }
      this.current_chapter_time = item.chapter_time_point
      this.$emit('changePlaytime', this.current_chapter_time)
    }
  }
}
</script>

<style lang="less" scoped>
.chapter-info {
  flex: 1;
  overflow-y: auto;
  font-family: "PingFang SC";
  .ai-tips-box {
    color: #0052D9;
    background: #ECF2FE;
    padding: 10px 8px 10px 10px;
    .tips {
      margin-left: 4px;
      font-size: 13px;
      a {
        text-decoration: underline;
        color: #0052D9;
      }
    }
  }
  .chapter-list-box {
    padding: 10px 16px 10px;
    background: var(--Color, #FFF);
  }
  .chapter-item {
    padding: 10px 0;
    border-bottom: 0.5px solid #EEE;
  }
  .info-box-top {
    display: flex;
  }
  ::v-deep .info-left {
    .chapter-image {
      width: 120px;
      height: 68px;
      margin-right: 12px;
      border-radius: 4px;
      img {
        display: inline-block;
        width: 120px;
        height: 68px;
        border-radius: 4px;
      }
    }
  }
  .info-right {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .title {
      max-height: 44px;
      word-wrap: break-word;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      color: #000000e6;
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 22px;
    }
    .bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 20px;
      margin-top: 4px;
    }
    .chapter-time {
      color: #777777;
      font-size: 11px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
    .chapter-label {
      height: 20px;
      padding: 0 8px 0 4px;
      line-height: 20px;
      border-radius: 4px;
      background: var(---Brand8-Normal, #0052D9);
      color: #ffffff;
      font-size: 12px;
      font-weight: 400;
    }
    .play-icon {
      display: inline-block;
      width: 12px;
      height: 12px;
      vertical-align: middle;
      margin-right: 4px;
      background: url('~@/assets/mooc-img/icon_play.png') no-repeat center / cover;
    }
    .play-tips {
      color: #777777;
      font-family: "PingFang SC";
      font-size: 11px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      height: 20px;
      display: flex;
      align-items: center;
    }
    .to-play-icon {
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-right: 2px;
      background: url('~@/assets/mooc-img/play-circle.png') no-repeat center / cover;
    }
  }
  .chapter-desc-box {
    position: relative;
    margin-top: 8px;
    padding: 8px 12px;
    background: #F6F6F6;
    border-radius: 4px;
  }
  .chapter-desc {
    box-sizing: border-box;
    border-radius: 4px;
    line-height: 20px;
    color: #000000cc;
    word-wrap: break-word;
    word-break: break-all;
    font-family: "PingFang SC";
    font-size: 12px;
    font-weight: 400;
    letter-spacing: 0.48px;
  }
  .maxheight {
    max-height: 120px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 6;
    overflow: hidden;
  }
  .light-key {
    display: inline-block;
    position: absolute;
    right: 12px;
    bottom: 8px;
    color: #0052d9;
    background-color: #F6F6F6;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
  }
  .no-full-text:before {
    position: absolute;
    right: 100%;
    content: "";
    width: 100px;
    height: 20px;
    background-image: linear-gradient(270deg,#F6F6F6,hsla(0,0%,100%,0));
  }
  .video-chapter-empty {
    padding: 20px 0;
    text-align: center;
    .empty-text {
      margin: 16px 0 20px;
    }
  }
}
.chapter-info::-webkit-scrollbar{
  display: none;
}
</style>
