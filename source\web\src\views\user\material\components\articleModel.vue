<template>
  <div class="articleModel-main">
    <div :class="[{ 'en-caption-box': moocLang === 'en-us' }, 'caption-box']" id="article-model">
      <div class="top-search">
        <span class="title">
          {{
            this.dropdownValue === 'article' ? $langue('NetCourse_ArticleTranscription', { defaultText: '文章转写' }) :
            this.dropdownValue === 'summary' ? $langue('Mooc_ChapterSummary', { defaultText: '章节纪要' }) :
            this.dropdownValue === 'draft' ?  $langue('NetCourse_SubtitlesScript', { defaultText: '字幕文稿' }) : ''
          }}
        </span>
        <div class="lf-btn-box">
          <span class="search-box">
            <span class='s-left'>
              <i class="el-icon-search"></i>
              <el-input class="search-input" v-model="searchContent" @input="handleInput" @keyup.enter="handleKeyDown" :placeholder="$langue('Mooc_TaskDetail_HomeWork_InputContent', { defaultText: '请输入内容' })"></el-input>
            </span>
            <span class="s-right" v-if="searchContent">
              <span class="s-num" v-if="searchNum === '无结果'">
                <span class="no-result">{{ $langue('Mooc_NoResults', { defaultText: '无结果' }) }}</span>
              </span>
              <span class="s-num" v-else>
                <i class="el-icon-arrow-left" @click="addIndex" :dt-eid="dtCommon('eid', '上一页')" :dt-remark="dtCommon('remark', '上一页')"></i>
                <span class="num">{{ searchNum }}</span>
                <i class="el-icon-arrow-right" @click="reduceIndex" :dt-eid="dtCommon('eid', '下一页')" :dt-remark="dtCommon('remark', '下一页')"></i>
              </span>
              <span class="line">|</span>
              <i class="el-icon-close" @click="inputClear"></i>
            </span>
          </span>
          <span
          v-if="cType !== 'subtitle_summary'"
          :class="['fullScreen-btn', {'enlarge-full-btn':!articleFullScreen}]"
          @click="handleArticleFull"
          :dt-eid="dtCommon('eid', articleFullScreen ? '宽屏查看' : '退出宽屏')"
          :dt-remark="dtCommon('remark', articleFullScreen ? '宽屏查看' : '退出宽屏')"
          >
            <i class="shrink-icon"></i>
            <span class="btn">{{articleFullScreen ? $langue('NetCourse_ViewInWidescreen', { defaultText: '宽屏查看' }) :  $langue('NetCourse_ExitWidescreen', { defaultText: '退出宽屏' })  }}</span>
          </span>
          <div class="dropdown-box">
            <span class="dropdown-bc">
              <el-dropdown trigger="click" placement="bottom" @command="handleDropdown">
                <span class="el-dropdown-link">
                  {{ showDropInfo?.langKey ? $langue(showDropInfo.langKey, { defaultText: showDropInfo.label }) : ''}}
                  <i class="el-icon-arrow-down el-icon--right"></i>
                </span>
                <el-dropdown-menu slot="dropdown" class="netCaption-dropdownMenu">
                  <el-dropdown-item
                  v-for="item in dropList"
                  :key="item.label"
                  :command="item.value"
                  :dt-areaid="dtCommon('areaid', item.label, '下拉框')"
                  >
                    {{$langue(item.langKey, { defaultText: item.label })}}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </span>
            <div class="custom_tips_popover" v-if="popoverVisible">
              <i class="el-icon-caret-top"></i>
              <div class="custom-popover-content">
                <span>{{ $langue('NetCourse_ModelChangeTips', { defaultText: '点击此处可在 文章、纪要、字幕模式间切换' }) }}</span>
                <i class="el-icon-close" @click="handlePopover"></i>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 文章模式 -->
      <div :class="['article-main', {'fullScreen-main': !articleFullScreen}]" v-if="dropdownValue === 'article'" @scroll="handleScroll">
        <div
        :class="[
        'article-list-box',
        {'scoll-active': choiceTextIndex(item) },
        {'scoll-article-active': captionCurTime >= item.startTime && captionCurTime < item.endTime}
        ]"
        v-for="(item, index) in articleList"
        :key="index"
        @click="toPosition(item, 'article')"
        :dt-eid="dtList('eid', item)"
        :dt-remark="dtList('remark', item)"
        :dt-areaid="dtList('areaid', item)"
        >
        <!-- 当前时间:{{ captionCurTime }}
        <div>是不是没有时间了：{{ item.startTime }} - {{ item.endTime }}</div> -->
          <div class="tp-title">
            <span class="time"><span>{{ transforNcTime(item.time) }}</span></span>
            <span class="title-right">
              <span class="title" v-html="item.title"></span>
              <div class="click-jump-tips">
                <span class="play-circle-icon"></span>
                <span>点击跳转</span>
              </div>
            </span>
          </div>
          <div class="article-card-content">
            <p class="item-other-title" v-for="(e, i) in item.titleList" :key="delTitle(e, i)"><span v-html="delTitle(e, i)"></span></p>
            <p class="item-other-title" v-for="(c, j) in item.content" :key="j"><span v-html="delTitle(c, j)"></span></p>
            <img class="item-img" v-for="v in item.imglist" :key="v" :src="v" />
          </div>
        </div>
        <div class="caption-content-shadow"></div>
        <div class="back-box" v-if="showBack" @click="toBack('articleBack')" :dt-eid="dtCommon('eid', '回到当前位置')" :dt-remark="dtCommon('remark', '回到当前位置')">
          <span>回到当前位置</span>
          <span class="back-icon"></span>
        </div>
      </div>
      <!-- 字幕模式 -->
      <div v-if="dropdownValue === 'draft'" :class="['caption-main', {'fullScreen-main': !articleFullScreen}]">
        <div class="caption-title">
          <span>{{ $langue('NetCourse_Timestamp', { defaultText: '时间点' }) }}</span>
          <span>{{ $langue('NetCourse_TextScript', { defaultText: '文本内容' }) }}</span>
        </div>
        <div class="caption-content" @scroll="handleScroll">
          <div
          v-for="(item, index) in captionArr"
          :key="index"
          :class="[
            {'caption-item-active': captionCurTime >= item?.IntStartTime && captionCurTime <= item.IntEndTime || noCurrentTime(item, index)},
            {'scoll-active': choiceTextIndex(item)},
            'caption-item'
          ]"
          :id="item?.IntStartTime"
          @click="toPosition(item, 'caption')"
          :dt-eid="dtList('eid', item)"
          :dt-remark="dtList('remark', item)"
          :dt-areaid="dtList('areaid', item)"
          >
            <span class="time">{{ item.startTime?.split(',')[0] }}</span>
            <div class="title-right">
              <div class="click-jump-tips">
                <span class="play-circle-icon"></span>
                <span>点击跳转</span>
              </div>
              <div class="title" v-html="item.caption"></div>
            </div>
          </div>
        </div>
        <div class="caption-content-shadow"></div>
        <div class="back-box" v-if="showBack" @click="toBack" :dt-eid="dtCommon('eid', '回到当前位置')" :dt-remark="dtCommon('remark', '回到当前位置')">
          <span>回到当前位置</span>
          <span class="back-icon"></span>
        </div>
      </div>
      <!-- 纪要模式 -->
      <div :class="['summary-main', {'fullScreen-main': !articleFullScreen}]" v-if="dropdownValue === 'summary'" @scroll="handleScroll">
        <div
        :id="item.startTime"
        v-for="(item, index) in chapterList"
        :key="index"
        @click="toPosition(item, 'summary')"
        :class="[
          {'summary-item-active': captionCurTime >= item.startTime && captionCurTime < item.endTime},
          {'scoll-active': choiceTextIndex(item)},
          'summary-list'
        ]"
        :dt-eid="dtList('eid', item)"
        :dt-remark="dtList('remark', item)"
        :dt-areaid="dtList('areaid', item)"
        >
          <span class="time">{{ item.chapter_time }}</span>
          <div class="summary-content">
            <div class="title-right">
              <span class="title" v-html="item.chapter_title"></span>
              <div class="click-jump-tips">
                <span class="play-circle-icon"></span>
                <span>点击跳转</span>
              </div>
            </div>
            <div class="content" v-html="item.chapter_content"></div>
          </div>
        </div>
        <div class="caption-content-shadow"></div>
        <div class="back-box" v-if="showBack" @click="toBack" :dt-eid="dtCommon('eid', '回到当前位置')" :dt-remark="dtCommon('remark', '回到当前位置')">
          <span>回到当前位置</span>
          <span class="back-icon"></span>
        </div>
      </div>
    </div>
    <Watermark
      v-if="watermark.textContent"
      ref="watermark"
      :targetId="watermark.targetId"
      :text="watermark.textContent"
      :canvasUserOptions="watermark.canvasUserOpt"
      :wmUserOptions="watermark.wmUserOpt"
      :isManualInit="false"
    />
    <!-- courseData.artificial_graphic_id 只有ai文章才显示 -->
    <div class="ai-tips-box" v-if="dropdownValue === 'article' && !courseData.artificial_graphic_id">
      <i class="el-icon-warning" style="color: #0052D9"></i>
      <span class="tips">
        {{ $langue('NetCourse_ArticleCorretTips', { defaultText: '当前文章为AI大模型智能生成，如有错漏不当，' }) }}
        <a href="https://km.tencent.com/openkm/url/lpciih" target="_blank">{{ $langue('NetCourse_FeedbackTips', { defaultText: '可点此反馈' }) }}</a>
      </span>
    </div>
  </div>
</template>
<script>
import axios from 'axios'
import { mapMutations, mapState } from 'vuex'
import { debounce, transforNcTime } from 'utils/tools'
import { getAiGraphic } from '@/config/api.conf.js'
import Watermark from '@/components/watermark.vue'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]
export default {
  components: {
    Watermark
  },
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    },
    captionList: {
      type: Array,
      default: () => ([])
    },
    captionCurTime: {
      type: [Number, String],
      default: 0
    },
    chapterSummaryList: { // 纪要
      type: Array,
      default: () => ([])
    },
    forMatTab: { // 用来判断是纪要还是字幕模式
      type: Object,
      default: () => ({})
    },
    cType: { // 视频模式下的字幕纪要
      type: String,
      default: ''
    }
  },
  data() {
    return {
      transforNcTime,
      captionArr: [],
      searchContent: '',
      choiceWordList: [],
      currentIndex: 0,
      showBack: false,
      isScrolling: false,
      autoScrollInterval: null,
      chapterList: [],
      popoverVisible: true,
      articleList: [],
      settimeout: null,
      dropdownValue: 'article',
      scrollCount: 0,
      showDropInfo: {
        langKey: 'NetCourse_ArticleMode',
        label: '文章模式',
        value: 'article'
      },
      watermark: {
        targetId: 'article-model', // 水印目标元素id
        textContent: '',
        canvasUserOpt: {
          width: 200,
          height: 160,
          fillStyle: 'rgba(153, 153, 153, 0.1)',
          font: '20px Microsoft Yahei',
          rotateDegree: 39,
          translate: {
            x: 0,
            y: 0
          }
        },
        wmUserOpt: {
          'z-index': 99999
        }
      }
    }
  },
  computed: {
    ...mapState({
      moocLang: state => state.moocLang,
      articleFullScreen: state => state.net.articleFullScreen
    }),
    ...mapMutations(['setArticleFull']),
    // 当视频播放时间和文稿时间对不上的时候，用文稿的开始时间进行比较
    noCurrentTime() {
      let colorFlag = false
      return (row, index) => {
        if (!row?.IntStartTime) {
          colorFlag = false
        } else {
          if (row?.IntStartTime < this.captionCurTime && this.captionCurTime < this.captionArr[index + 1]?.IntStartTime) {
            colorFlag = true
          } else {
            colorFlag = false
          }
        }
        return colorFlag
      }
    },
    showManuscript() {
      // 只有当show_manuscript有值时 并且show_manuscript 为 false时才隐藏内容文稿，其他情况为显示
      return !(this.courseData.course_statement && !this.courseData.course_statement.show_manuscript)
    },
    isShowChapterSummary() {
      return this.chapterList.some(e => e.chapter_content)
    },
    searchNum() {
      return this.choiceWordList.length ? `${this.currentIndex + 1}/${this.choiceWordList.length}` : '无结果'
    },
    delTitle() {
      return (row, i) => {
        return (row && row[`t${i}`]) || ''
      }
    },
    dropList() {
      const tabList = []
      if (this.cType !== 'subtitle_summary' && this.isShowArticle) {
        tabList.push({ label: '文章模式', value: 'article', langKey: 'NetCourse_ArticleMode' })
      }
      if (this.captionArr?.length && this.showManuscript) {
        tabList.push({ label: '字幕模式', value: 'draft', langKey: 'NetCourse_SubtitleMode' })
      }
      if (this.chapterList?.length && this.isShowChapterSummary) {
        tabList.push({ label: '纪要模式', value: 'summary', langKey: 'NetCourse_SummaryMode' })
      }
      return tabList
    },
    material_id() {
      return this.$route.query.material_id ? parseInt(this.$route.query.material_id) : ''
    },
    isShowArticle() {
      return this.articleList.some(e => e.title)
    },
    dtList() {
      return (type, item) => {
        const modelValue = this.cType === 'subtitle_summary' ? '字幕纪要' : '文章模式'
        const data = {
          page: this.courseData?.course_name,
          page_type: '素材详情页-新版',
          container: `${modelValue}_${this.showDropInfo?.label}`,
          click_type: 'data',
          content_type: '',
          content_id: '',
          content_name: '',
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'PC'
        }
        let fatData = {}
        if (this.dropdownValue === 'article') { // 文章
          fatData = {
            content_id: item?.startTime,
            content_name: item?.title
          }
        } else if (this.dropdownValue === 'summary') { // 纪要
          fatData = {
            content_id: item?.id,
            content_name: item?.chapter_title
          }
        } else { // 字幕
          fatData = {
            content_id: item?.IntStartTime,
            content_name: item?.caption
          }
        }
        if (type === 'remark') {
          return JSON.stringify({
            ...data,
            ...fatData
          })
        } else if (type === 'eid') {
          return `element_${this.material_id}_${fatData.content_id}`
        } else {
          return `area_${this.material_id}_${fatData.content_id}`
        }
      }
    },
    dropdownValueFilter() {
      return {
        forMatTab: this.forMatTab,
        cType: this.cType,
        isShowArticle: this.isShowArticle
      }
    }
  },
  watch: {
    courseData: {
      immediate: true,
      deep: true,
      // 数据回显
      handler(newVal) {
        if (newVal.captions?.length) {
          this.readCaptionFile(newVal.captions)
        }
        const id = newVal.artificial_graphic_id || newVal.ai_graphic_id
        if (id) {
          this.getAiGraphicData()
        }
      }
    },
    // 视频处理
    captionList: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (newVal?.length) {
          this.captionArr = newVal
          const list = JSON.stringify(this.captionArr)
          sessionStorage.setItem('captionArr', list)
        }
      }
    },
    chapterSummaryList: {
      immediate: true,
      deep: true,
      handler(newVal) {
        if (!this.chapterList?.length) {
          this.chapterList = newVal
          const summaryList = JSON.stringify(this.chapterList)
          sessionStorage.setItem('summaryList', summaryList)
        }
      }
    },
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          this.watermark.textContent = val.staff_name
        }
      },
      immediate: true
    },
    // 监听对象 才能拿到所有的最新值
    dropdownValueFilter: {
      deep: true,
      immediate: true,
      handler(val) {
        const dropInfo = [
          { label: '文章模式', value: 'article', langKey: 'NetCourse_ArticleMode' },
          { label: '字幕模式', value: 'draft', langKey: 'NetCourse_SubtitleMode' },
          { label: '纪要模式', value: 'summary', langKey: 'NetCourse_SummaryMode' }
        ]
        // 文章-article，章节纪要-summary，字幕文稿-draft
        if (this.cType !== 'subtitle_summary' && this.isShowArticle) {
          this.dropdownValue = 'article'
        } else {
          if (val.forMatTab?.summary || (val.forMatTab?.summary && val.forMatTab?.subtitle)) {
            this.dropdownValue = 'summary'
          } else {
            this.dropdownValue = 'draft'
          }
        }
        this.showDropInfo = dropInfo.find((e) => this.dropdownValue === e.value)
      }
    }
  },
  mounted() {
    window.addEventListener('keydown', this.handleKeyDown)
    // 切换模式展示
    const visible = localStorage.getItem('article_popover_visible')
    this.popoverVisible = Number(visible) !== 1
  },
  destroyed() {
    this.settimeout = null
    clearTimeout(this.settimeout)
    window.removeEventListener('keydown', this.handleKeyDown, false)
  },
  methods: {
    // 点击字幕播放时间更新
    toPosition(row, type) {
      const time = type === 'article' ? row.time : type === 'caption' ? (row?.IntStartTime || 0) : (row.chapter_time_point || 0)
      this.$emit('toChaptersPosition', time)
    },
    getAiGraphicData() {
      const id = this.courseData.artificial_graphic_id || this.courseData.ai_graphic_id
      getAiGraphic({ graphic_id: id }).then((res) => {
        res = res ? res.replace(/\n/g, '<br/>') : ''
        let container = document.createElement('div')
        container.innerHTML = res
        this.articleList = []
        let currentTime = null
        let currentImgList = []
        let currentTitleList = []
        let currentContent = []
        function createEntry(time, imgList, titleList, content) {
          const otherTitleList = titleList?.length ? titleList.slice(1) : []
          const formattitle = titleList?.length ? titleList[0] : ''
          return {
            time: parseInt(time),
            imglist: imgList,
            title: formattitle,
            titleList: otherTitleList.map((v, i) => {
              return {
                [`t${i}`]: v
              }
            }),
            content: content.map((e, j) => {
              return {
                [`t${j}`]: e
              }
            })
          }
        }
        Array.from(container.children).forEach(e => {
          if (e.classList.contains('chaptersTime')) {
            if (currentTime !== null) { // 当前时间
              this.articleList.push(createEntry(currentTime, currentImgList, currentTitleList, currentContent))
              currentImgList = []
              currentTitleList = []
              currentContent = []
            }
            currentTime = e.getAttribute('id')
          } else if (e.tagName === 'H1' || e.tagName === 'H2' || e.tagName === 'H3') { // 标题
            currentTitleList.push(e.innerHTML)
          } else if (e.tagName === 'P' && e.querySelector('img')) { // 图片
            let img = e.querySelector('img')
            currentImgList.push(
              `${envName.contentcenter}content-center/api/v1/content/imgage/${img.getAttribute('data-content')}/preview`
            )
            // 规避图片中也有文本
            currentContent.push(e.innerHTML)
          } else { // 内容
            currentContent.push(e.innerHTML)
          }
        })
        if (currentTime !== null) {
          this.articleList.push(createEntry(currentTime, currentImgList, currentTitleList, currentContent))
        }
        this.articleList = this.articleList.map((v, i) => {
          const startTime = this.articleList[i].time
          // 如果是最后一条数据，结束时间在开始时间基础上加1来处理
          const endTime = this.articleList.length === (i + 1) ? this.articleList[i].time + 1 : this.articleList[i + 1].time
          return {
            ...v,
            startTime,
            endTime
          }
        })
        sessionStorage.setItem('articleList', JSON.stringify(this.articleList))
        console.log('?????????????????????', this.articleList)
      })
    },
    handlePopover() {
      this.popoverVisible = false
      localStorage.setItem('article_popover_visible', 1)
    },
    // 文章屏幕操作
    handleArticleFull() {
      this.$store.commit('net/setArticleFull', !this.articleFullScreen)
    },
    // 回车
    handleKeyDown(e) {
      if (e.key === 'Enter') {
        this.reduceIndex()
      }
    },
    isScroll(val) {
      // 查询的时候需要滚动
      if (val === 'enter') {
        this.delscroll('search')
        return
      }
      // 文章没有自动滚动，回到当前位置需要操作滚动
      if (val === 'articleBack') {
        this.delscroll()
        return
      }
      // 手动滚动接入，自动滚动停止
      if (this.showBack) return
      // 文章模式不自动滚动, 查询需要自动滚动
      if (val !== 'search' && this.dropdownValue === 'article') {
        return
      }
      this.delscroll(val)
    },
    delscroll(val) {
      this.settimeout = setTimeout(() => {
        let domName = ''
        let scrollName = ''
        if (this.dropdownValue === 'draft') {
          domName = '.caption-content'
          scrollName = 'caption-item-active'
        } else if (this.dropdownValue === 'summary') {
          domName = '.summary-main'
          scrollName = 'summary-item-active'
        } else if (this.dropdownValue === 'article') {
          domName = '.article-main'
          scrollName = 'scoll-article-active'
        }
        // 文本查询定位
        if (val === 'search') {
          scrollName = 'scoll-active'
        }
        if (!domName) return
        const captionBox = this.$el.querySelector(domName)
        let curDom = document.getElementsByClassName(scrollName)[0]?.previousElementSibling
        if (curDom) {
          // 前面一条数据距离上方的位置+他自己的数据的高度，100有一个默认高度
          captionBox.scrollTop = curDom.offsetTop + curDom.clientHeight - 100
        } else if (this.currentIndex === 0 && !curDom) { // 当翻页到最后一条数据的时候滚动到最顶端
          captionBox.scrollTop = 0
        }
      })
    },
    // 自动滚动
    startAutoScroll() {
      // 在查询状态的时候，不需要自动滚动
      if (this.searchContent) {
        return
      }
      if (!this.autoScrollInterval) {
        this.autoScrollInterval = setInterval(() => {
          // 滚动逻辑，可以根据需要调整
          this.isScroll()
          this.scrollCount = 0
        }, 2000) // 每2秒滚动一次
      }
    },
    // 手动滚动
    handleScroll() {
      // scrollCount 手动滚动这里会相加很多次
      // 造成的原因是因为captionBox.scrollTop多滚动了点
      this.scrollCount++
      if (!this.isScrolling && this.scrollCount > 2) {
        // 用户手动滚动时停止自动滚动
        this.stopAutoScroll()
        this.showBack = true
      }
      this.isScrolling = false // 重置滚动标记
    },
    stopAutoScroll() {
      if (this.autoScrollInterval) {
        clearInterval(this.autoScrollInterval)
        this.autoScrollInterval = null
      }
    },
    // 回到当前位置
    toBack(val) {
      this.scrollCount = 0
      this.isScrolling = true
      this.showBack = false
      this.isScroll(val)
    },
    choiceTextIndex(row) {
      const findIndex = this.choiceWordList.findIndex(r => row.uuId === Number(r.id))
      return findIndex === this.currentIndex
    },
    getChoiceScrollWord() {
      this.choiceWordList = []
      this.scrollCount = 0 // 重置为0就可以自动滚动了
      this.$nextTick(() => {
        const a = Array.from(document.getElementsByClassName('hightLightWord'))
        a.forEach((e, i) => {
          e.style.background = '#F7F0A7'
          if (i === this.currentIndex) {
            e.style.background = '#EECD3C'
          }
          this.choiceWordList.push({
            id: e.getAttribute('id')
          })
        })
        // 没有查询到结果不做滚动
        if (!this.choiceWordList?.length) return
        // 滚动到选中的数据
        this.isScroll('search')
      })
    },
    // 文本查询
    handleInput: debounce(function (e) {
      if (!e) {
        this.inputClear()
        return
      }
      window.BeaconReport('at_click', {
        eid: this.dtCommon('eid', this.searchContent, '内容检索'),
        remark: this.dtCommon('remark', this.searchContent, '内容检索')
      })
      // 内容查询的时候不让自动滚动，销毁自动滚动
      this.stopAutoScroll()
      // 字幕模式
      if (this.dropdownValue === 'draft') {
        const sessionCaptionArr = JSON.parse(sessionStorage.getItem('captionArr'))
        const list = sessionCaptionArr?.length ? sessionCaptionArr : this.captionArr
        list.forEach((e) => {
          if (e.caption.indexOf(this.searchContent) > -1) {
            const id = this.generateUUID()
            const caption = this.hightLight(this.searchContent, e.caption, id)
            e.caption = caption
            e.uuId = id
          }
        })
        this.captionArr = list
        this.getChoiceScrollWord()
        return
      }
      // 纪要模式
      if (this.dropdownValue === 'summary') {
        const sessionChapterList = JSON.parse(sessionStorage.getItem('summaryList'))
        const list = sessionChapterList?.length ? sessionChapterList : this.chapterList
        list.forEach((e) => {
          if (e.chapter_title.indexOf(this.searchContent) > -1 || e.chapter_content.indexOf(this.searchContent) > -1) {
            const id = this.generateUUID()
            e.chapter_title = this.hightLight(this.searchContent, e.chapter_title, id)
            e.chapter_content = this.hightLight(this.searchContent, e.chapter_content, id)
            e.uuId = id // 定义一个唯一的id
          }
        })
        this.chapterList = list
        this.getChoiceScrollWord()
        return
      }
      // 文章
      const sessionArticleList = JSON.parse(sessionStorage.getItem('articleList'))
      const list = sessionArticleList?.length ? sessionArticleList : this.articleList
      list.forEach((e) => {
        const id = this.generateUUID()
        e.title = this.hightLight(this.searchContent, e.title, id)
        e.titleList.forEach((v, i) => {
          if (v[`t${i}`].indexOf(this.searchContent) > -1) {
            v[`t${i}`] = this.hightLight(this.searchContent, v[`t${i}`], id)
            v.uuId = id // 定义一个唯一的id
          }
        })
        e.content.forEach((e, j) => {
          if (e[`t${j}`].indexOf(this.searchContent) > -1) {
            e[`t${j}`] = this.hightLight(this.searchContent, e[`t${j}`], id)
            e.uuId = id // 定义一个唯一的id
          }
        })
        e.uuId = id
      })
      this.articleList = list
      this.getChoiceScrollWord()
    }),
    generateUUID () {
      const d = new Date().getTime()
      const r = d + Math.random() * 1000
      return r
    },
    // 高亮
    hightLight (keyWord, suggtion, id) {
      // 使用 regexp 构造函数，因为这里要匹配的是一个变量
      const reg = new RegExp(keyWord, 'ig')
      const newSrt = suggtion.replace(reg, function (keyWord) {
        return `<span class='hightLightWord' id=${id} style="background: #F7F0A7;">${keyWord}</span>`
      })
      return newSrt
    },
    // 清除
    inputClear() {
      this.searchContent = ''
      this.currentIndex = 0
      this.choiceWordList = [] // 清空选择的数据
      if (this.dropdownValue === 'draft') {
        this.captionArr = JSON.parse(sessionStorage.getItem('captionArr')) || []
      } else if (this.dropdownValue === 'summary') {
        const list = JSON.parse(sessionStorage.getItem('summaryList')) || []
        this.chapterList = list
        this.$emit('update', list)
      } else if (this.dropdownValue === 'article') {
        this.articleList = JSON.parse(sessionStorage.getItem('articleList')) || []
      }
    },
    // 上一个
    addIndex() {
      // 当已经手动滚动后(出现回到当前位置)，回车就是手动滚动
      if (this.showBack) {
        this.isScroll('enter')
      }
      if (this.currentIndex === 0) {
        this.currentIndex = this.choiceWordList.length - 1
        this.getChoiceScrollWord()
        return
      }
      this.currentIndex--
      this.getChoiceScrollWord()
    },
    // 下一个
    reduceIndex(val) {
      // 当已经手动滚动后(出现回到当前位置)，回车就是手动滚动
      if (this.showBack) {
        this.isScroll('enter')
      }
      if (this.currentIndex === (this.choiceWordList.length - 1)) {
        this.currentIndex = 0
        this.getChoiceScrollWord()
        return
      }
      this.currentIndex++
      this.getChoiceScrollWord()
    },
    handleDropdown(val) {
      this.scrollCount = 0 // 初始化滚动次数
      this.showBack = false
      this.dropdownValue = val
      this.showDropInfo = this.dropList.find((e) => this.dropdownValue === e.value)
      this.inputClear()
      window.BeaconReport('at_click', {
        eid: this.dtCommon('eid', this.showDropInfo.label, '下拉框'),
        remark: this.dtCommon('remark', this.showDropInfo.label, '下拉框')
      })
    },
    dtCommon(type, label, val) {
      const modelValue = this.cType === 'subtitle_summary' ? '字幕纪要' : '文章模式'
      if (type === 'remark') {
        return JSON.stringify({
          page: this.courseData?.file_name,
          page_type: '素材详情页-新版',
          container: `${modelValue}_${val}`,
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: label,
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'PC'
        })
      } else if (type === 'eid') {
        return `element_${this.material_id}_${label}`
      } else {
        return `area_${this.material_id}_${label}`
      }
    },
    // 文稿内容
    readCaptionFile(captions) {
      captions.forEach(item => {
        if (item.attachement_type === 'Caption') {
          axios({
            url: item.url,
            method: 'GET',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
            }
          }).then((response) => {
            if (response.status === 200 && response.data) {
              try {
                let data = response.data?.split(/\n\n|\r\n\r\n/)
                let newData = []
                data.forEach(value => {
                  let valueItem = value.split(/[(\r\n)\r\n|(\r\n\r\n)\r\n\r\n]+/)
                  if (this.isNumeric(valueItem[0]) && valueItem[1].includes('-->')) {
                    newData.push(value)
                  } else {
                    newData[newData.length - 1] += `\r\n${value}`
                  }
                })
                const captionArr = newData?.map(str => {
                  let obj = {}
                  const captionItemArr = str.split(/[(\r\n)\r\n|(\r\n\r\n)\r\n\r\n]+/)
                  captionItemArr.map((e, idx) => {
                    if (idx === 1) {
                      const time = JSON.parse(JSON.stringify(e))
                      obj.startTime = e.split('-->')[0]
                      obj.endTime = e.split('-->')[1]
                      const endTimeCopy = JSON.parse(
                        JSON.stringify(time.split('-->')[1])
                      )
                      const startTimeCopy = JSON.parse(
                        JSON.stringify(time.split('-->')[0])
                      )
                      obj.IntStartTime = startTimeCopy
                        ? this.timeToSec(startTimeCopy)
                        : 0
                      obj.IntEndTime = endTimeCopy
                        ? this.timeToSec(endTimeCopy)
                        : 0
                    }
                    if (idx === 2) obj.caption = e
                    if (idx > 2) {
                      obj.caption += e
                    }
                  })
                  return obj
                })
                this.captionArr = captionArr
                const list = JSON.stringify(this.captionArr)
                sessionStorage.setItem('captionArr', list)
              } catch (error) {
                console.log('error: ', error)
              }
            }
          })
        }
      })
    },
    isNumeric(str) {
      return /^\d+$/.test(str)
    },
    timeToSec(timeStr) {
      let time = timeStr.split(',')[0]
      let s = ''
      const hour = time.split(':')[0]
      const min = time.split(':')[1]
      const sec = time.split(':')[2]
      let misecond = timeStr.split(',')[1].substr(0, 2)
      misecond = Number(parseInt(misecond) / 100)
      s = Number(hour * 3600) + Number(min * 60) + Number(sec)
      return (s * 100 + misecond * 100) / 100
    }
  }
}
</script>
<style lang="less">
.netCaption-dropdownMenu {
  padding: 4px;
  .el-dropdown-menu__item {
    color: #333333;
    border-radius: 3px;
  }
  .el-dropdown-menu__item:hover{
    color: #006FFF;
    background-color: #F2F8FF;
  }
}
</style>
<style lang="less" scoped>
.articleModel-main {
  user-select: none;
  .en-caption-box {
    .caption-item {
      span:first-child {
        margin-right: 55px !important;
      }
    }
    @media screen and (max-width: 1660px) {
      .search-box {
        .search-input {
          width: 110px !important;
        }
      }
    }
    @media screen and (min-width: 1661px) {
      .search-box {
        .search-input {
          width: 250px !important;
        }
      }
    }
  }
  .caption-box {
    position: relative;
    border: 1px solid rgba(238, 238, 238, 1);
    border-radius: 3px;
    .top-search {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 20px 10px 15px;
      background: #f5f5f5;
      .title {
        font-weight: bold;
        color: #000000;
        font-size: 14px;
        flex-shrink: 0;
      }
      .lf-btn-box {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        width: 100%;
      }
      .search-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 20px;
        background: #FFF;
        padding-left: 16px;
        padding-right: 16px;
        .s-left {
          flex: 1;
          display: flex;
          align-items: center;
          i {
            color: #006FFF;
            font-weight: bold;
          }
        }
        .s-right {
          flex-shrink: 0;
          i {
            color: #999999;
            font-weight: bold;
            cursor: pointer;
          }
          .num {
            margin-right: 4px;
            margin-left: 4px;
          }
          .line {
            color: #EEEEEE;
            margin-right: 12px;
            margin-left: 12px;
          }
        }
        :deep(.search-input) {
          // width: 250px;
          .el-input__inner {
            border: unset;
            padding-right: 5px;
          }
        }
      }
      .dropdown-box {
        position: relative;
        flex-shrink: 0;
        margin-left: 16px;
        .custom_tips_popover {
          background: #3C6EE0;
          border-radius: 6px;
          color: #fff;
          padding: 16px;
          position: absolute;
          min-width: 350px;
          top: 40px;
          right: 24px;
          .el-icon-caret-top {
            color: #3C6EE0;
            position: absolute;
            right: 20px;
            top: -9px;
          }
          .custom-popover-content {
            line-height: 22px;
            .el-icon-close {
              margin-left: 14px;
              cursor: pointer;
            }
          }
        }
      }
      .dropdown-bc {
        border-radius: 20px;
        background: #FFF;
        padding: 0 8px 0 16px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        :deep(.el-dropdown) {
          color: #333333;
        }
      }
      .dropdown-bc:hover {
        background: #F2F8FF;
        .el-dropdown-link {
          color: #0052D9;
        }
      }
      .fullScreen-btn {
        background-color: #fff;
        height: 32px;
        border-radius: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        color: #666666;
        padding: 0 16px;
        margin-left: 16px;
        .shrink-icon {
          display: inline-block;
          width: 16px;
          height: 16px;
          margin-right: 4px;
          background: url("~@/assets/img/enlarge.png") no-repeat center/cover
        }
      }
      .fullScreen-btn:hover {
        color: #333333;
        .shrink-icon {
          background: url("~@/assets/img/enlarge-hover.png") no-repeat center/cover
        }
      }
      .enlarge-full-btn {
        .shrink-icon {
          background: url("~@/assets/img/shrink.png") no-repeat center/cover
        }
      }
      .enlarge-full-btn:hover {
        color: #333333;
        .shrink-icon {
          background: url("~@/assets/img/shrink-hover.png") no-repeat center/cover
        }
      }
    }
    .caption-main {
      .caption-title {
        height: 36px;
        line-height: 36px;
        padding-left: 19px;
        color: rgba(0, 0, 0, 0.8);
        background-color: #EFEFEF;

        span:first-child {
          margin-right: 40px;
        }
      }
      .title-right {
        .click-jump-tips {
          display: none;
          align-items: center;
          color: #0052D9;
          line-height: 20px;
          margin-bottom: 8px;
          .play-circle-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: url("~@/assets/img/play-circle.png") no-repeat center/cover;
            margin-right: 4px;
          }
        }
      }
      .caption-content {
        height: 490px;
        // padding: 16px 0 0 20px;
        overflow-y: auto;
        margin-top: 16px;
        cursor: pointer;

        .caption-item {
          display: flex;
          align-items: flex-start;
          padding: 8px 16px;
          line-height: 22px;
          // margin-bottom: 16px;

          .time,.title {
            // width: 40px;
            margin-right: 26px;
            display: inline-block;
            color: #666666;
          }

          .title {
            line-height: 22px;
            letter-spacing: 0.28px;
          }
        }
        .caption-item:hover {
          background: #ECF2FE;
          :deep(.title-right) {
            .click-jump-tips {
              display: flex;
            }
          }
        }

        .caption-item-active {

          .title,
          .time {
            color: #0052D9;
          }
        }
        :deep(.current-word) {
          .hightLightWord {
            background: #EECD3C !important;
          }
        }
      }
    }
    .summary-main {
      margin-top: 16px;
      cursor: pointer;
      height: 490px;
      overflow: auto;
      .title-right {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        .title {
          font-weight: bold;
          color: #333333;
          margin-right: 16px;
        }
        .click-jump-tips {
          display: flex;
          align-items: center;
          opacity: 0;
          color: #0052D9;
          line-height: 20px;
          .play-circle-icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: url("~@/assets/img/play-circle.png") no-repeat center/cover;
            margin-right: 4px;
          }
        }
      }
      .summary-list {
        display: flex;
        align-items: flex-start;
        padding: 8px 16px;
        cursor: pointer;
        .time {
          color: #666666;
          line-height: 22px;
        }
        .summary-content {
          margin-left: 24px;
          line-height: 22px;
          .title {
            color: #333333;
            font-weight: bold;
            margin-bottom: 4px;
          }
          .content {
            color: #666666;
            letter-spacing: 0.28px;
            word-break: break-all;
          }
        }
      }
      .summary-list:hover {
        background: #ECF2FE;
        .title-right {
          .click-jump-tips {
            opacity: 1;
          }
        }
      }
      .summary-item-active {
        .time {
          color: #0052D9;
        }
        .summary-content {
          .content,
          .title {
            color: #0052D9;
          }
        }
        :deep(.current-word) {
          .hightLightWord {
            background: #EECD3C !important;
          }
        }
      }
    }
    .article-main {
      height: 624px;
      overflow: auto;
      margin-top: 16px;
      .article-list-box {
        padding: 8px 16px;
        cursor: pointer;
        .tp-title {
          margin-bottom: 4px;
          line-height: 22px;
          display: flex;
          .time {
            border-radius: 4px;
            background: #EEE;
            padding: 0 8px;
            height: 24px;
            color: #333333;
            margin-right: 16px;
            display: flex;
            align-items: center;
            span {
              height: 22px;
              line-height: 22px;
              display: inline-block;
            }
          }
          .title-right {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            .title {
              font-weight: bold;
              color: #333333;
              margin-right: 16px;
            }
            .click-jump-tips {
              display: flex;
              align-items: center;
              opacity: 0;
              color: #0052D9;
              line-height: 20px;
              .play-circle-icon {
                display: inline-block;
                width: 16px;
                height: 16px;
                background: url("~@/assets/img/play-circle.png") no-repeat center/cover;
                margin-right: 4px;
              }
            }
          }
        }
        .article-card-content {
          .item-other-title {
            line-height: 22px;
            letter-spacing: 0.28px;
            margin-top: 4px;
          }
          .item-img {
            margin-top: 4px;
            width: 315px;
            height: 177px;
            border-radius: 4px;
          }
        }
      }
      .article-list-box:hover {
        background: #ECF2FE;
        .title-right {
          .click-jump-tips {
            opacity: 1;
          }
        }
      }
      .scoll-article-active {
        .tp-title {
          color: #0052D9;
          .time {
            background: #F0F5FD ;
            color: #0052D9;
          }
          .title-right {
            .title {
              color: #0052D9;
            }
          }
        }
        :deep(.current-word) {
          .hightLightWord {
            background: #EECD3C !important;
          }
        }
      }
    }
    .caption-main,.summary-main,.article-main {
      .back-box {
        position: absolute;
        bottom: 16px;
        right: 16px;
        height: 32px;
        padding: 0 12px;
        border-radius: 70px;
        background-color: #fff;
        color: #0052D9;
        display: flex;
        align-items: center;
        box-shadow: 0 0 12px 0 #DDD;
        cursor: pointer;
        .back-icon {
          display: inline-block;
          width: 16px;
          height: 16px;
          background: url("~@/assets/img/top.png") no-repeat center/cover;
          margin-left: 4px;
        }
      }
      .caption-content-shadow {
        width: 100%;
        height: 26px;
        position: absolute;
        bottom: 0;
        background: linear-gradient(180deg, #ffffff63 0%, #FFF 100%);
      }
    }
    .fullScreen-main {
      height: calc(100vh - 300px);
      .caption-content {
        height: calc(100vh - 335px);
      }
    }
  }
  .ai-tips-box {
    border-radius: 4px;
    background: #F0F5FD;
    color: #0052D9;
    line-height: 22px;
    padding: 7px 12px;
    margin-top: 10px;
    .tips {
      margin-left: 4px;
      a {
        text-decoration: underline;
        color: #0052D9;
        cursor: pointer;
      }
    }
  }
  @media screen and (max-width: 1660px) {
    .search-box {
      width: 280px;
      .search-input {
        width: 100%;
      }
    }
  }
  @media screen and (min-width: 1661px) {
    .search-box {
      width: 420px;
      .search-input {
        width: 100%;
      }
    }
  }
}
</style>
