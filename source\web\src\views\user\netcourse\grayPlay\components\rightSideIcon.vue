<template>
  <div class="right-side-main">
    <div v-if="modelTabValue === 'article_model'" :class="['small-video-box', 'card', { 'not-click-picture': showPicture }]" @click="handlePicture">
      <span class="video-img-icon"></span>
      <div class="video-title">{{ $langue('NetCourse_sideWindow', { defaultText: '小窗' }) }}</div>
    </div>
    <!-- 切换双语图标 -->
    <!-- 双语暂时注释 -->
    <div class="change-lang-main card" v-if="!isFormMooc" @click="handleChangelang">
      <img :src="langIcon" alt="">
      <div class="change-lang-title">{{ moocLang === 'en-us' ? '中文' : 'EN' }}</div>
    </div>
    <sdc-learn-ai-bot class="aibot card" :env="aiBotEnv"></sdc-learn-ai-bot>
    <div class="toolbar-box card">
      <el-popover placement="left" trigger="hover" popper-class="net-detail-popper">
        <div class="jump-box">
          <div>
            <img src="data:image/jpeg;base64,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" />
            <p>{{ $langue('NetCourse_FollowWeChat', { defaultText: '关注微信公众号' }) }}</p>
          </div>
          <div>
            <img src="@/assets/img/RScode.jpg" />
            <p>{{ $langue('NetCourse_EnterMiniProgram', { defaultText: '进入小程序' }) }}</p>
          </div>
        </div>
        <div class="mobile" slot="reference"></div>
      </el-popover>
      <div class="consultation" @click="goConsultation" v-html="html2" @mouseenter="goConsultationText" @mouseleave="goConsultationText2"></div>
    </div>
    <div class="arrow styleT card" @click="goArrow"></div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import translate from 'mixins/translate.vue'
export default {
  mixins: [translate],
  props: {
    modelTabValue: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      html2: ''
    }
  },
  mounted() {
    // 将子元素移动到 body
    const childElement = document.querySelector('.client-side-wrap_PC')
    document.body.appendChild(childElement)
  },
  computed: {
    ...mapState({
      moocLang: (state) => state.moocLang,
      showPicture: (state) => state.net.showPicture
    }),
    isFormMooc() {
      return this.$route.query.from === 'mooc'
    },
    // 双语图标
    langIcon() {
      return this.moocLang === 'en-us'
        ? require('@/assets/img/china.png')
        : require('@/assets/img/english.png')
    },
    aiBotEnv() {
      return process.env.NODE_ENV === 'production' ? 'production' : 'test'
    }
  },
  methods: {
    handlePicture() {
      if (this.showPicture) return
      this.$store.commit('net/setPictureShow', true)
    },
    // 切换双语
    handleChangelang() {
      const lang = this.moocLang === 'en-us' ? 'zh-cn' : 'en-us'
      this.$store.commit('setMoocLang', lang)
      this.getLangJS()
    },
    goConsultation() {
      window.location.href = 'wxwork://message?username=小T'
    },
    goConsultationText() {
      this.html2 = this.moocLang === 'en-us' ? 'feed</br>back' : '反馈</br>咨询'
    },
    goConsultationText2() {
      this.html2 = ''
    },
    goArrow() {
      // const timer = setInterval(function () {
      // let osTop = document.documentElement.scrollTop || document.body.scrollTop
      // console.log('怎么没关栋', osTop)
      // let ispeed = Math.floor(-osTop / 2)
      const graphic = document.querySelector('.graphic-user-page')
      graphic.scrollTo({
        top: 0,
        behavior: 'smooth' // 可选，平滑滚动效果
      })
      // this.isTop = true
      // if (osTop === 0) {
      //   clearInterval(timer)
      // }
      // }, 300)
    }
  }
}
</script>
<style lang="less">
.net-detail-popper {
  margin-right: 32px;
  padding: 16px;
  width: 210px;
  height: 134px;
  border-radius: 8px;
  background: #fff;
  .jump-box {
    display: flex;
    justify-content: space-between;
    > div {
      img {
        width: 80px;
        height: 80px;
      }
      p {
        margin: 4px 0 0;
        color: #777777;
        font-family: 'PingFang SC';
        font-size: 12px;
        line-height: 18px;
        text-align: center;
      }
    }
  }
}
</style>
<style lang="less" scoped>
.right-side-main {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  position: fixed;
  z-index: 10;
  right: 50%;
  bottom: 65px;
  transform: translateX(776px);
  height: 100%;

  @media screen and (max-width: 1660px) {
    transform: translateX(656px);
  }
  .card {
    margin-top: 20px;
  }
  .change-lang-main {
    width: 46px;
    height: 80px;
    border-radius: 50px;
    background: #fff;
    text-align: center;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      width: 24px;
      height: 24px;
    }
    .change-lang-title {
      color: #00000099;
      line-height: 14px;
      font-size: 12px;
      margin-top: 6px;
    }
  }
  .aibot {
    :deep(.sidebar-entry) {
      position: unset;
      left: unset;
      bottom: unset;
    }
    :deep(.client-side-wrap) {
      // height: calc(100% - 167px);
     bottom: -37px;
      right: -30px;
    }
  }
  .small-video-box {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    width: 46px;
    height: 74px;
    border-radius: 50px;
    background-color: #fff;
    font-size: 12px;
    cursor: pointer;
    .video-title {
      color: #00000099;
    }
    .video-img-icon {
      width: 24px;
      height: 24px;
      margin-bottom: 4px;
      background: url('~@/assets/img/small-video.png') no-repeat center/cover;
    }
  }
  .small-video-box:hover {
    .video-title {
      color: #333333;
    }
    .video-img-icon {
      background: url('~@/assets/img/small-active-video.png') no-repeat
        center/cover;
    }
  }
  .not-click-picture {
    cursor: not-allowed;
  }
  .not-click-picture:hover {
    .video-title {
      color: #00000099;
    }
    .video-img-icon {
      background: url('~@/assets/img/small-video.png') no-repeat center/cover;
    }
  }
  .toolbar-box {
    width: 46px;
    height: 108px;
    background: #fff;
    border-radius: 23px;
    padding: 22px 11px;
    .mobile {
      width: 24px;
      height: 24px;
      cursor: pointer;
      background: url('~@/assets/img/Phone-icon.png') no-repeat center/24px;
      &:hover {
        background: url('~@/assets/img/Phone-icon-hover.png') no-repeat
          center/24px;
      }
    }
    .consultation {
      width: 24px;
      height: 28px;
      cursor: pointer;
      color: #006fff;
      margin: 16px 0 0 0;
      font-size: 12px;
      font-weight: 500;
      line-height: 14px;
      background: url('~@/assets/img/Communication.png') no-repeat center/24px;
      &:hover {
        background: unset;
      }
    }
  }
  .styleT {
    width: 46px;
    height: 46px;
    background: #006fff;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .arrow {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #006fff url('~@/assets/img/To-top.png') no-repeat center/20px;
  }
}
</style>
