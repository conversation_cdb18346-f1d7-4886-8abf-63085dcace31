<template>
  <div class="test-page">
    <div class="main">
      <div class="title">
        我是导师测试页面
      </div>
      <div class="component">
        回显原因：<el-input style="width: 50%;" v-model="entryReason" placeholder="请输入需要传入的原因"></el-input>
        <el-button style="margin: 0 0 10px 20px" @click="setMember">回显：{{ staffTestData.StaffName }}</el-button> <el-button style="margin: 0 0 10px 20px" @click="clearMember">清除回显</el-button><br />
        <p class="test-item"> <!-- platform 系统code：1-人才透视系统 2-招聘系统 3-入职准备系统 4-入职入场系统 -->
          平台(必填)：
          <el-select v-model="platform" placeholder="请选择" @change="updateMontorCom">
            <el-option label="人才透视系统" value="1"></el-option>
            <el-option :disabled="operaTypeId === '3'" label="招聘系统(传入原因回显)" value="2"></el-option>
            <el-option :disabled="operaTypeId === '3'" label="入职准备系统" value="3"></el-option>
            <el-option :disabled="operaTypeId === '3'" label="入职入场系统" value="4"></el-option>
            <el-option :disabled="operaTypeId === '3'" label="异动系统" value="5"></el-option>
          </el-select>
          <span class="mgl-20">platform: {{ platform }}</span>
        </p>
        <!-- <p class="test-item">
          当前操作人(必选)：
          <sdc-staff-selector :range="range" v-model="operatorId" selectClass="selectClass" modalClass="modalClass" showFullTag></sdc-staff-selector>
          <span class="mgl-20" v-if="operatorId">operatorId: {{ operatorId }}</span>
        </p> -->
        <p class="test-item">
          当前操作人身份(人才透视系统必选)：
          <el-radio-group v-model="operaTypeId" @change="updateMontorCom">
            <el-radio label="">不传</el-radio>
            <el-radio label="1">组织BP</el-radio>
            <el-radio label="2">直接上级</el-radio>
            <el-radio label="3" :disabled="platform !== '1'">员工本人</el-radio>
            <el-radio label="4">招聘经理</el-radio>
            <el-radio label="5">运营人员</el-radio>
          </el-radio-group>
          <span class="mgl-20" v-if="operaTypeId">operaTypeId: {{ operaTypeId }}</span>
        </p>
        <!-- <p class="test-item">
          是否是本人修改(必填)：
          <el-radio-group v-model="isSelf">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
          <span class="mgl-20">isSelf: {{ isSelf }}</span>
        </p> -->
        <p class="test-item">
          新员工staffId：<sdc-staff-selector :range="range" v-model="studentStaffId" selectClass="selectClass" modalClass="modalClass" showFullTag></sdc-staff-selector>
          <span class="mgl-20" v-if="studentStaffId">studentStaffId: {{ studentStaffId }}</span>
        </p>
        <p class="test-item">
          新员工简历id：<el-input style="width: 50%;" v-model="student_resume_id" placeholder="请输入新员工简历id"></el-input>
          <span class="mgl-20" v-if="student_resume_id">student_resume_id: {{ student_resume_id }}</span>
        </p>
        <p class="test-item">
          工作地：<sdc-area-selector v-model="placeId" placeholder="请选择" @change="placeChange"/>
          <span class="mgl-20" v-if="placeId">placeId: {{ placeId }}</span>
        </p>
        <p class="test-item">
          <!-- 部门：<el-input class="depart-input" v-model="departInfo.deptName" placeholder="请输入部门名称"></el-input> <el-input class="depart-input" v-model="departInfo.deptId" placeholder="请输入部门id"></el-input> -->
          最小组织：<sdc-unit-selector v-model="deptId" showFullTag placeholder="请输入最小组织名称" />
          <span class="mgl-20" v-if="deptId">deptId: {{ deptId }}</span>
        </p>
        <div class="select-box">
          导师选择器：
          <sdc-mentor-selector
            v-if="showMentorCom"
            ref="mentorSelector"
            v-model="staffData"
            :entryReason="entryReason"
            :operaTypeId="operaTypeId"
            :studentStaffId="studentStaffId"
            :student_resume_id="student_resume_id"
            :workPlaceInfo="workPlaceInfo"
            :deptId="deptId"
            :platform="platform"
            @change="handleChangeStaff"
            @getReason="getReason">
          </sdc-mentor-selector>
          <!-- :isSelf="isSelf" -->
        </div>
        <div class="show">
          <p>导师staffId：{{staffData || '-'}}</p>
          <p>门槛匹配原因：{{reason || '-'}}</p>
        </div>
        <div class="box-flex" style="display: flex;">
          <span class="box-label">加密：</span>
          <el-input v-model="encodeText" clearable placeholder="请输入需要加密的文本" @clear="clearEncodeText"></el-input>
          <el-button v-if="encodeText" class="box-btn" size="mini" @click="encryption">加密</el-button>
          <el-button v-if="encodeText" class="box-btn" size="mini" @click="passValue">传输</el-button>
        </div>
        <p>加密后的文本：{{encodeResult}}<el-button v-if="encodeResult" class="box-btn" style="margin-left: 20px;" size="mini" @click="copyRight(encodeResult)">复制</el-button></p>
        <div class="box-flex" style="display: flex;">
          <span class="box-label">解密：</span>
          <el-input v-model="uncodeText" clearable placeholder="请输入需要解密的文本"  @clear="clearUncodeText"></el-input>
          <el-button v-if="uncodeText" class="box-btn" size="mini" @click="decryption">解密</el-button>
        </div>
        <p>解密后的文本：{{uncodeResult}}<el-button v-if="uncodeResult" class="box-btn" style="margin-left: 20px;" size="mini" @click="copyRight(uncodeResult)">复制</el-button></p>
      </div>
    </div>
  </div>
</template>

<script>
import { MentorSelector } from '@tencent/sdc-mentor'
import CryptoJS from 'crypto-js'
const KEY = CryptoJS.enc.Utf8.parse('8ia(IT2sKw^fFs^+')
export default {
  name: 'test',
  components: {
    sdcMentorSelector: MentorSelector
  },
  data() {
    return {
      encodeText: '', // 需要加密的文本
      encodeResult: '',
      uncodeText: '', // 加密后的文本
      uncodeResult: '',
      // --------------------------------------------------------------------------------------------------------------------------------------------------------
      showMentorCom: true, // 是否显示导师组件，用来刷新组件
      operaTypeId: '', // 操作人身份：招聘经理：4，组织bp：1，直接上级：2，员工本人：3
      platform: '1',
      entryReason: '',
      // isSelf: false,
      staffData: '',
      studentStaffId: '', // 学生staffId
      student_resume_id: '', // 学生简历id
      placeId: '', // 工作地
      deptId: '', // 学生最小组织id
      workPlaceInfo: {}, // 新人工作地信息
      staffTestData: {
        // Avatar: 'http://rhrc.woa.com/photo/100/shizhouwang.png',
        // EngName: 'shizhouwang',
        // StaffID: 186627,
        // StaffName: 'shizhouwang(王世洲)',
        // UnitFullName: 'S3职能系统－HR与管理线/人力资源平台部/业务研发中心/学发研发交付组',
        // UnitID: 35426,
        // UnitName: '学发研发交付组'
        Avatar: 'http://rhrc.woa.com/photo/100/adasrzou.png',
        StaffID: 251022,
        StaffName: 'adasrzou(邹司蕊)'
      },
      reason: '', // 门槛原因
      range: {
        // manageUnitIdList: [10101],
        // staffTypeIdList: [2, 6]
      }
    }
  },
  watch: {
  },
  computed: {
  },
  created() {
  },
  methods: {
    updateMontorCom() {
      this.showMentorCom = false
      this.$nextTick(() => {
        this.showMentorCom = true
        this.staffData = ''
        this.reason = ''
      })
    },
    passValue() {
      this.uncodeText = ''
      setTimeout(() => {
        this.uncodeText = this.encodeResult
      }, 100)
    },
    clearEncodeText() {
      this.encodeText = ''
      this.encodeResult = ''
    },
    clearUncodeText() {
      this.uncodeText = ''
      this.uncodeResult = ''
    },
    copyRight(valueText) {
      var tempInput = document.createElement('input')
      tempInput.value = valueText || '测试文本'
      tempInput.id = 'creatDom'
      document.body.appendChild(tempInput)
      tempInput.select()
      if (document.execCommand('copy')) {
        document.execCommand('copy')
        this.$message.success(this.$langue('Mooc_Common_Alert_CopySucessed', { defaultText: '复制成功' }))
        let creatDom = document.getElementById('creatDom')
        creatDom.parentNode.removeChild(creatDom)
      }
    },
    encryption() {
      // 使用 AES 加密
      const encryptedContent = CryptoJS.AES.encrypt(this.encodeText, KEY, {
        mode: CryptoJS.mode.ECB,  
        padding: CryptoJS.pad.Pkcs7
      })
      this.encodeResult = encryptedContent.ciphertext.toString()
    },
    decryption() {
      const decryptedContent = CryptoJS.AES.decrypt(CryptoJS.format.Hex.parse(this.uncodeText), KEY, {
        mode: CryptoJS.mode.ECB,  
        padding: CryptoJS.pad.Pkcs7
      })
      this.uncodeResult = CryptoJS.enc.Utf8.stringify(decryptedContent)
    },
    placeChange(value) {
      if (value && value.length) {
        let item = value[0]
        this.workPlaceInfo = {
          placeName: item.fullName,
          place_id: item.item_id
        }
        console.log('this.workPlaceInfo: ', this.workPlaceInfo)
      } else {
        this.workPlaceInfo = {}
      }
    },
    setMember() {
      if (!this.entryReason) {
        this.entryReason = '我是测试原因'
      }
      this.$nextTick(() => {
        this.$refs.mentorSelector.setSelectedInit(this.staffTestData)
      })
    },
    clearMember() {
      this.$refs.mentorSelector.clearSelected()
    },
    handleChangeStaff(res) {
      console.log('成员', res)
    },
    getReason(value) {
      console.log('value~~~: ', value)
      this.reason = value
    }
  }
}
</script>

<style lang="less">
.test-page {
  padding: 20px;
  height: 100vh;
  background-color: #fff;
  .mgl-20 {
    margin-left: 20px;
  }
  .main {
    .title {
      text-align: center;
    }
    .component {
      width: 1000px;
      margin: 20px auto;
      .select-box {
        display: flex;
        align-items: center
      }
      .sdc-selector {
        height: auto;
        width: 500px;
        margin-left: 20px;
      }
      .show {
        margin-top: 20px;
      }
      .test-item {
        margin: 20px 0;
        display: flex;
        align-items: center;
        .sdc-cascader {
          width: 300px;
        }
        .depart-input {
          margin-right: 20px;
          width: 300px;
        }
      }
    }
  }
  .box-flex {
    margin: 20px 0 20px;
    display: flex;
    align-items: center;
    .box-label {
      flex-shrink: 0;
    }
    .box-btn {
      margin-left: 20px;
    }
  }
}
</style>
