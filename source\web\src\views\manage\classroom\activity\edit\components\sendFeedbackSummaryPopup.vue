<template>
  <div class="send-feedback-summary-popup">
    <el-dialog title="" :visible.sync="visible" width="1200px" :close-on-click-modal="false" :before-close="handlerCancel">
      <div slot="title" class="dialog-header">关闭问卷并发送反馈总结<span>活动名称：{{ activityInfo.activity_name || '-' }}</span></div>
      <div class="dialog-content" >
        <div class="tool-area">
          <div class="row flex-box">
            <el-button class="send-btn" type="primary" size="small" @click="handlerBeforeSending(1)" :disabled="is_send_feedback_mail">关闭问卷，并发送总结至我的邮箱</el-button>
            <div class="tips flex-box">
              <img class="icon mr-4" src="@/assets/classroomImg/help-circle-filled.png" alt="" srcset="">
              <span>停止课后问卷收集，并将总结仅发送至我的邮箱。如需复杂的编辑功能，建议将邮件发送至自己邮箱进一步编辑</span>
            </div>
          </div>
          <div class="row flex-box">
            <el-button class="send-btn" type="primary" size="small" @click="handlerBeforeSending(2)" :disabled="is_send_feedback_mail">关闭问卷，并发送总结至学员邮箱</el-button>
            <div class="tips flex-box">
              <img class="icon mr-4" src="@/assets/classroomImg/help-circle-filled.png" alt="" srcset="">
              <span>停止课后问卷收集，并将总结发送至以下收件人和抄送人</span>
            </div>
          </div>
        </div>
        <div class="form-box">
          <div class="row-title">邮件预览如下</div>
          <el-form :model="form" :rules="rules" ref="form" label-width="80px">
            <el-form-item label="邮件标题" prop="title">
              <el-input class="w-785" v-model="form.title" placeholder="请输入内容" :disabled="!isCanEdit || is_send_feedback_mail"></el-input>
            </el-form-item>

            <el-form-item label="收件人" prop="receiver">
              <div class="tips-box">默认收件人包括所有考勤状态为“全勤”、“部分缺勤”，且“接受问卷”的学员</div>
              <el-input
                resize="none"
                type="textarea"
                v-model="form.receiver"
                placeholder="请输入学员英文名，多名学员使用中文分号分隔，单次至多添加300人"
                size="small"
                clearable
                class="remark-textarea w-785"
                :autosize='{ minRows: 6, maxRows: 8 }'
                :disabled="!isCanEdit || is_send_feedback_mail"
              />
            </el-form-item>

            <el-form-item label="抄送">
              <div class="tips-box">默认抄送活动负责人、班主任、讲师</div>
              <el-input
                resize="none"
                type="textarea"
                v-model="form.cc"
                placeholder="请输入学员英文名，多名学员使用中文分号分隔，单次至多添加300人" 
                size="small"
                clearable
                class="remark-textarea w-785"
                :autosize='{ minRows: 6, maxRows: 8 }'
                :disabled="!isCanEdit || is_send_feedback_mail"
              />
            </el-form-item>
          </el-form>

          <div class="editor-box" id="editor">
            <sdc-mce-editor
              v-if="form.content"
              ref="editor"
              selector="email_info"
              :env="editorEnv"
              :content="form.content"
              :editorCSS="editorCSS"
              :urlParams="editorConfig.urlParams"
              :catalogue.sync="editorConfig.catalogue"
              :urlConfig="editorConfig.urlConfig"
              :options="editorConfig.options"
              :insertItems="insertItems"
              :key="3"
            />
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { getQuestionFeedbackApi, sendQuestionFeedbackApi } from '@/config/classroom.api.conf.js'

export default {
  name: 'sendFeedbackSummaryPopup',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    surveyId: {
      type: [String, Number],
      default: ''
    },
    isCanEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editorEnv: process.env.NODE_ENV,
      editorConfig: {
        catalogue: false,
        options: {
          // 非必传字段
          selector: '#course_make',
          menubar: false,
          readonly: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
            formatselect fontsizeselect lineheight |
            bold italic underline strikethrough |
            forecolor backcolor |
            dent align bullist numlist |
            insert codesample |
            fullScreenButton copyall code`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          // contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          contentinfo: `/content-center/api/v1/content/save_contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        urlParams: {
          is_public: true
        },
        is_open_catalogue: false
      },
      insertItems: [
        'link',
        'table',
        'image',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      form: {
        title: '',
        receiver: '',
        cc: '',
        content: ''
      },
      rules: {
        title: [{ required: true, message: '请输入邮件标题', trigger: 'blur' }],
        receiver: [{ required: true, message: '请输入收件人', trigger: 'blur' }]
      },
      is_send_feedback_mail: false,
      collected_rate: 0,
      tinyEditor: null,
      editorCSS: ''
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.editorCSS = ''
          this.getQuestionFeedback()
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState({
      activityInfo: state => state.activity.activityInfo
    })
  },
  created() { },
  mounted() { },
  beforeDestroy() { },
  methods: {
    getQuestionFeedback() {
      getQuestionFeedbackApi(this.surveyId).then(res => {
        const tempDiv = document.createElement('div')
        tempDiv.innerHTML = res.content
        const styleTags = tempDiv.getElementsByTagName('style')
        for (let i = 0; i < styleTags.length; i++) {
          this.editorCSS += styleTags[i].innerHTML
        }

        this.is_send_feedback_mail = res.is_send_feedback_mail
        this.collected_rate = res.collected_rate || 0
        if (this.is_send_feedback_mail || !this.isCanEdit) {
          this.editorConfig.options.readonly = true
        }
        this.form = {
          title: res.title,
          receiver: res.receiver,
          cc: res.cc,
          content: res.content
        }
      })
    },
    // 发送前的处理
    handlerBeforeSending(type) {
      if (this.collected_rate < 75) {
        this.$confirm(`当前问卷反馈率低于75%，确定关闭问卷，并且发送课程总结到${type === 1 ? '我' : '学员'}的邮箱吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.sendQuestionFeedback(type)
        })
      } else {
        this.sendQuestionFeedback(type)
      }
    },
    sendQuestionFeedback(type) {
      const content = this.getEditorContent()
      const params = {
        ...this.form,
        content,
        send_type: type,
        survey_id: this.surveyId
      }
      console.log('form', params, content)
      sendQuestionFeedbackApi(params).then(res => {
        this.$message.success('发送成功')
        this.$emit('update:visible', false)
      })
    },
    getEditorContent() {
      const editorContent = this.$refs.editor.getContent()
      const tempEditor = document.createElement('div')
      tempEditor.innerHTML = editorContent

      const contentContainer = tempEditor.querySelector('.all-contain')
      if (!contentContainer) {
        this.$message.error('请填写反馈总结内容')
        return null
      }

      // 原始数据
      const baseContent = document.createElement('div')
      baseContent.innerHTML = this.form.content

      // 原始数据内容替换
      const baseContainer = baseContent.querySelector('.all-contain')
      if (baseContainer) {
        baseContainer.innerHTML = contentContainer.innerHTML
        return baseContent.innerHTML
      }

      return editorContent
    },
    handlerCancel() {
      this.$emit('update:visible', false)
      this.$emit('onClose')
    }
  }
}
</script>

<style lang="less" scoped>
  @import '~assets/css/activity-common.less';

  .send-feedback-summary-popup {
    :deep(.el-dialog) {
      margin-top: 5vh !important;
    }
    :deep(.el-dialog .el-dialog__body) {
      padding: 0;
    }
    .dialog-header {
      & > span {
        color: #00000099;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        margin-left: 28px;
        display: inline-block;
      }
    }
    .dialog-content {
      width: 100%;
      height: 80vh;
      padding: 32px;
      overflow-y: auto;
      .tool-area {
        margin-bottom: 36px;
        .row {
          & + .row {
          margin-top: 24px;
          }
        }
        .send-btn {
          background-color: #0052D9;
          margin-right: 20px;
          &:hover {
            opacity: 0.8;
          }
        }
        :deep(.is-disabled) {
          border-color: #BBD3FB;
          background-color: #BBD3FB;
          &:hover {
            opacity: 1;
          }
        }
        .disabled-btn {
          border-color: #BBD3FB;
          background-color: #BBD3FB;
        }
        .tips {
          color: #0052d9;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          .icon {
            width: 16px;
            height: 16px;
          }
        }
      }
      
      .row-title {
        color: #0052d9;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
        margin-bottom: 20px;
      }
      :deep(.el-form .el-form-item__label) {
        color: #000000e6;
      }
      .tips-box {
        color: #00000099;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 32px;
        margin-bottom: 3px;
      }
      .remark-textarea {
        max-width: 800px;
        :deep(.el-textarea__inner) {
          height: 100%;
          max-width: 800px;
        }
      }
      .editor-box {
        margin-top: 38px;
        height: 700px;
        :deep(.tox.tox-tinymce) {
          box-sizing: border-box;
          border: 1px solid #ccc !important;
          min-height: 700px !important;
          margin: initial;
          .tox-sidebar-wrap .tox-edit-area {
            min-height: 374px !important;
            box-sizing: border-box;
          }
          .tox-editor-header {
            text-align: left;        
          }
        }
      }
    }
    .w-785 {
      width: 785px;
    }
  }
</style>
