// 个性推荐和相关推荐中ModuleTypes对应值
const qlearningModuleTypes = [
  {
    module_name: '网络课',
    moduleClassName: 'tag-net',
    moduleClassName2: 'tag-music',
    module_id: 1,
    urlPre: 'http://ihr.tencent.com/5FD89y/',
    enImg: 'Online.png'
  },
  {
    module_name: '面授课',
    moduleClassName: 'tag-face',
    module_id: 2,
    urlPre: 'http://ihr.tencent.com/89fVj6/',
    enImg: 'Onsite.png'
  },
  {
    module_name: '直播',
    moduleClassName: 'tag-live',
    module_id: 3,
    urlPre: 'http://ihr.tencent.com/gCjGf4/'
  },
  {
    module_name: '活动',
    moduleClassName: 'tag-activity',
    module_id: 4,
    enImg: 'Event.png'
  },
  {
    module_name: '码客',
    moduleClassName: 'tag-marker',
    module_id: 5
  },
  {
    module_name: '行家',
    moduleClassName: 'tag-hangjia',
    module_id: 6,
    urlPre: 'http://hangjia.woa.com/blank-main/hangj_main-page/expert-detail?id=',
    enImg: 'Genius.png'
  },
  {
    module_name: '案例',
    moduleClassName: 'tag-exam',
    module_id: 7,
    enImg: 'Case.png'
  },
  {
    module_name: '文章',
    moduleClassName: 'tag-note',
    module_id: 8,
    enImg: 'Article.png'
  },
  {
    module_name: '文章', // 图文
    moduleClassName: 'tag-article',
    module_id: 9,
    enImg: 'Article.png'
  },
  {
    module_name: '文档',
    moduleClassName: 'tag-word',
    module_id: 16,
    urlPre: 'https://learn.woa.com/user/profile?wordId='
  },
  {
    module_name: '外链',
    moduleClassName: 'tag-link',
    module_id: 99
  }
]

// 延伸学习中ModuleTypes对应值
const recommendModuleTypes = [
  {
    // module_name: '面授课',
    moduleClassName: 'tag-face',
    module_id: 1
  },
  {
    // module_name: '网络课',
    moduleClassName: 'tag-net',
    moduleClassName2: 'tag-music',
    module_id: 2
  },
  {
    // module_name: '班级',
    moduleClassName: 'tag-marker',
    module_id: 3
  },
  {
    // module_name: '活动',
    moduleClassName: 'tag-activity',
    module_id: 4
  },
  {
    // module_name: '直播',
    moduleClassName: 'tag-live',
    module_id: 5
  },
  {
    // module_name: '图文',
    moduleClassName: 'tag-article',
    module_id: 6
  },
  {
    // module_name: '系列课',
    moduleClassName: 'tag-hangjia',
    module_id: 7
  },
  {
    // module_name: '系列班',
    moduleClassName: 'tag-exam',
    module_id: 8
  },
  {
    // module_name: '论文',
    moduleClassName: 'tag-note',
    module_id: 9
  },
  {
    // module_name: '文档',
    moduleClassName: 'tag-word',
    module_id: 10
  }
]

const contentModuleTypes = [
  {
    module_name: '网络课',
    moduleClassName: 'tag-net',
    moduleClassName2: 'tag-music',
    module_id: 1
  },
  {
    module_name: '面授课',
    moduleClassName: 'tag-face',
    module_id: 2
  },
  {
    module_name: '直播',
    moduleClassName: 'tag-live',
    module_id: 3
  },
  {
    module_name: '活动',
    moduleClassName: 'tag-activity',
    module_id: 4
  },
  {
    module_name: '行家',
    moduleClassName: 'tag-hangjia',
    module_id: 6
  },
  {
    module_name: '案例',
    moduleClassName: 'tag-exam',
    module_id: 7
  },
  {
    module_name: '文章',
    moduleClassName: 'tag-note',
    module_id: 8
  },
  {
    module_name: '图文',
    moduleClassName: 'tag-article',
    module_id: 9
  },
  {
    module_name: '文档',
    moduleClassName: 'tag-word',
    module_id: 16
  },
  {
    module_name: '外链',
    moduleClassName: 'tag-link',
    module_id: 99
  },
  {
    module_name: '培养项目',
    moduleClassName: 'tag-mooc',
    module_id: 10
  },
  {
    module_name: '课单',
    moduleClassName: 'tag-courselist',
    module_id: 15
  }
]

export { qlearningModuleTypes, recommendModuleTypes, contentModuleTypes }
