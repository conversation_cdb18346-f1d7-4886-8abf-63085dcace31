<template>
  <div class="active-page">
    <div class="banner-card" :style="bannerImgTop('top')">
      <div class="banner-content" @click="goTo" :style="bannerImgTop('center')">
        <div class="toolbar">
          <div class="right">
            <el-button class="common" type="primary" plain @click.stop="openRewardDetails" :dt-eid="dtBtnMain('eid','已领卡去兑课')" :dt-areaid="dtBtnMain('area','已领卡去兑课')" :dt-remark="dtBtnMain('remark','已领卡去兑课')">已领卡去兑课</el-button>
            <el-button class="common" type="primary" plain @click.stop="openDialogCourse" :dt-eid="dtBtnMain('eid','已兑课程')" :dt-areaid="dtBtnMain('area','已兑课程')" :dt-remark="dtBtnMain('remark','已兑课程')">已兑课程</el-button>
            <el-button class="common" type="primary" plain @click.stop="toRulesDetail" :dt-eid="dtBtnMain('eid','活动规则')" :dt-areaid="dtBtnMain('area','活动规则')" :dt-remark="dtBtnMain('remark','活动规则')">活动规则</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="contain-main">
      <div class="coupon-bar-new">
        <div class="title">
          <div>我的学霸卡：已领取
          <span class="color-005" @click.stop="openRewardDetails"> {{pagination.total}}</span> 张学霸卡,
          <span class="color-005 color-6600" @click.stop="openRewardDetails">{{ cardInfo.balance || 0 }}</span> 张可兑课,
          <span class="color-005" @click.stop="openRewardDetails">{{usedNum}}</span> 张已使用,
          <span class="color-005" @click.stop="openRewardDetails">{{overdueNum}}</span> 张已过期
          
          </div>
          <!-- <div>我的学霸卡：当前有 <span style="color:#0052D9;cursor:pointer;" @click.stop="openRewardDetails">{{ cardInfo.balance || 0 }}</span> 张学霸卡可用，已使用<span style="color:#0052D9;cursor:pointer;" @click.stop="openDialogCourse">{{usedNum}}</span>张</div> -->
          <div class="subscription">
            <!-- <span class="subscription-tips"></span> -->
             <el-tooltip class="item" effect="dark" content="订阅后，将在每月发卡时收到企微提醒" placement="top">
              <div class="subscription-btn" @click="messageSubscribe" :dt-eid="dtSubscript('eid')" :dt-areaid="dtSubscript('area')" :dt-remark="dtSubscript('remark')">
                <img v-if="isSub" src="@/assets/outsourcedCourse/subscription-no.png" alt="">
                <img v-else src="@/assets/outsourcedCourse/subscription.png" alt=""> {{isSub ? '退订领卡提醒' : '订阅领卡提醒'}}
              </div>
            </el-tooltip> 

          </div>
        </div>
        <div class="card-list-content">
          <div class="official">
            <div class="official-top">
              <img class="official-top-left" :src="require('@/assets/outsourcedCourse/official.png')" />
              <div class="official-top-text">官方发放</div>
            </div>
            <div class="official-right">
              <div class="card-init card-stand" :class="getClass1()" :style="bgiStyle1">
                <div class="sub-title" :class="{'init-disable-class1': isDisableStyle1 }" 
                :style="isDisableStyle1 ? '' : card_font_style('a')">「{{cardName}}」学霸卡
                </div>
                <div class="title-1" :class="{'init-disable-class2': isDisableStyle1 }" :style="isDisableStyle1 ? '' : card_font_style('b') ">可用于兑换外部付费好课</div>
                <span class="title-warm">领取后7个自然日内有效</span>
                <div class="botton-box">
                  <div :style="buttonColor(1)" v-if="cardInfo.can_get_geek_num > 0" class="button-customer" type="primary" @click="throttleFn(1, true)" :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', '官方发放-点击领取')" plain>点击领取</div>
                  <div :style="buttonColor(2, showText(0, officialList))"
                  v-else-if="officialList.length && [0, 'consume', 'manage_deduct', 'deduct_expire', undefined].includes(officialList[0].verb_id)" 
                  class="button-customer disabled" type="primary" 
                  @click="throttleFn(1, false)" plain 
                  :dt-areaid="dtBotton('area')" 
                  :dt-eid="dtBotton('eid')" 
                  :dt-remark="dtBotton('remark', `官方发放-${showText(0, officialList)}`)">{{showText(0, officialList)}}</div>
                  <!-- 点击领取、已领取、抢光了 灰色字体-->
                  <div v-else class="button-customer disabled-gray" 
                  type="primary" @click="throttleFn(1, false)" 
                  :dt-areaid="dtBotton('area')" 
                  :dt-eid="dtBotton('eid')"
                  :dt-remark="dtBotton('remark', `官方发放-${showText(0, officialList)}`)">{{ showText(0, officialList) }}</div>
                </div>
              </div>
            </div>
            <div class="official-tips">
              领取官方下发的学霸卡，即可解锁右侧学霸卡分享资格。
            </div>
          </div>
          <div class="share-polite" v-if="Number(activityId) !== 1">
            <div class="official-top">
              <div class="official-top-flex">
                <img class="official-top-left" :src="require('@/assets/outsourcedCourse/share-top.png')" />
                <div class="official-top-text">分享有礼：分享给同事，可额外获得学霸卡奖励</div>
              </div>
              <div class="official-top-right ">
                <span style="color:#00000099;padding-right:12px;">还可获得 <span style="color:#0052d9"> {{Math.max(3 - presentRewardList.length, 0)}}</span> 张奖励</span>
                <span  @click="openSeeRecord" :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', '查看送出记录')">查看送出记录 <i class="el-icon-arrow-right"></i></span>
              
              </div>
            </div>
            <div class="share-po-display">
              <div :dt-eid="dtBtnMain('eid','送出学霸卡')" :dt-areaid="dtBtnMain('area','送出学霸卡')" :dt-remark="dtBtnMain('remark','送出学霸卡')" class="give-right-btn" @click="openGiveDialog" :style="!isGetOfficial ? 'opacity: 0.4;' : '' ">
                <img class="give-pc" :src="require('@/assets/outsourcedCourse/give-pc.png')" alt="">
                <span class="give-btn">送出学霸卡</span>
              </div>
              <div class="give-right" v-if="presentRewardList.length">
                <div class="card-stand" :class="getClassGive(item, presentRewardList)"  :style="bgiStyleGive(item, presentRewardList)" v-for="item in 3" :key="item">
                  <div v-if="presentRewardList.length >= item">
                    <div class="sub-title" :style="card_font_style('a')">「{{cardName}}」学霸卡
                    </div>
                    <div class="title-1" :style="card_font_style('b')">可用于兑换外部付费好课</div>
                    <span class="title-warm">获赠后7个自然日内有效</span>
                    <div class="botton-box">
                      <!-- 可使用已使用已过期 -->
                      <div :style="buttonColor(2, showTextGive(item, 'reward'))" class="button-customer disabled" type="primary" plain>
                        {{ showTextGive(item, 'reward') }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else class="give-right no-data">
                <img :src="require('@/assets/outsourcedCourse/no-data-give.png')" alt="">
                <div class="data-text">暂无奖励</div>
              </div>
            </div>
            <div class="share-po-tips">
              1、领取官方下发的学霸卡后，即可获得学霸卡分享资格。 成功分享学霸卡给同事，可额外获赠1张学霸卡。通过分享行为最多可额外获赠3张；2、被分享人最多收到3张学霸卡。若已收满3张，则无法分享成功。
            </div>
          </div>
        </div>
      </div>

      <div :class="['coupon-bar-new', 'learning-reward',  {'learning-reward-packup-arrow': packup}]" id="learning-reward-success">
        <div class="pack-up-btn" @click="handlePack"><i :class="['el-icon-arrow-up', {'packup-arrow': packup}]" ></i> </div>
        <div class="learning-reward_left card-list-content" v-show="!packup">
          <div class="learning-reward_right">
            <div class="official-top">
              <div class="official-top-flex">
                <img class="official-top-left" :src="require('@/assets/outsourcedCourse/reward.png')" alt="">
                <div class="official-top-text">学习奖励
                  <!-- <el-tooltip class="item" effect="dark" content="兑换课程并完成学习任务，可解锁学习奖励" placement="top">
                    <i class="el-icon-warning-outline"></i>
                  </el-tooltip> -->
                </div>
                <div class="official-top-right">
                  还可解锁 <span> {{ unlockNum }} </span> 张
                </div>
              </div>
            </div>
            <div class="card-list-content card-list-content-study">
              <div :class="['card-stand ', `card-stand-position-${i}`, getClass(i)]" style="width: 194px;" :style="newbgiStyle(i)" v-for="i in 4" :key="i">
                <div class="sub-title" :class="{'stand-disable-class1': !canGetReward}">「{{cardName}}」学霸卡
                </div>
                <div class="title-1" :class="{'stand-disable-class1': !canGetReward}">可用于兑换外部付费好课</div>
                <span class="title-warm" :class="{'stand-disable-class1': !canGetReward}">领取后7个自然日内有效</span>
                <div class="botton-box" v-if="canGetReward">
                  <div :style="buttonColor(1)" :class="['button-customer']" type="primary" @click="throttleFn(2, canGetReward)" plain :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', '学习奖励-点击领取')">点击领取</div>
                </div>
                <div class="botton-box" v-else>
                  <div :style="rewardList.length !== 4 ? '' : buttonColor(2, showTextRe(i - 1, rewardList))" 
                  :class="['button-customer', { 'disabled-gray': rewardList.length !== 4 } ]" type="primary" plain 
                  @click="throttleFn(2, canGetReward)" :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" 
                  :dt-remark="dtBotton('remark', '学习奖励-点击领取')">{{showTextRe(i - 1, rewardList)}}</div>
                </div>
              </div>
            </div>
            <el-tooltip class="item" effect="dark" content="  1、通过课程学习最多可解锁4张学霸卡。2、学习课程中任意5个任务，每个任务学习时长不低于3分钟，即可解锁一张学霸卡。" placement="top">
              <div class="give-tips">
              1、通过课程学习最多可解锁<span style="color:#ff6600;font-weight:600">4张</span> 学霸卡。2、学习课程中任意5个任务，每个任务学习时长不低于3分钟，即可解锁一张学霸卡。
              </div>
            </el-tooltip>
          </div>
          <div class="give-away" v-if="Number(activityId) !== 1">
            <div class="official-top">
              <div class="official-top-flex">
                <img class="official-top-left" :src="require('@/assets/outsourcedCourse/give-top.png')" alt="">
                <div class="official-top-text">他人赠送</div>
              </div>
              <div class="official-top-right">
                还可获赠 <span> {{ Math.max(3 - giveCardDataList.length, 0) }} </span> 张
              </div>
            </div>
            <div class="give-right" v-if="giveCardDataList.length">
              <div class="card-stand" :class="getClassGive(item, giveCardDataList)" :style="bgiStyleGive(item, giveCardDataList)" v-for="item in 3" :key="item">
                <div v-if="giveCardDataList.length >= item">
                  <div class="sub-title" :style="card_font_style('a')">「{{cardName}}」学霸卡
                  </div>
                  <div class="title-1" :style="card_font_style('b')">可用于兑换外部付费好课</div>
                  <span class="title-warm">获赠后7个自然日内有效</span>
                  <div class="right-footer">
                    <el-tooltip class="item" effect="dark" :content="receiveRemark(item) + '赠送' " placement="top">
                      <div class="title-name">{{receiveRemark(item, true)}}赠送</div>
                    </el-tooltip>
                    <div class="botton-box">
                      <!-- 可使用已使用已过期 -->
                      <div :style="buttonColor(2, showTextGive(item, 'passive'))" class="button-customer disabled" type="primary" plain>
                        {{ showTextGive(item, 'passive') }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="give-right no-data">
              <img :src="require('@/assets/outsourcedCourse/no-data-give.png')" alt="">
              <div class="data-text">暂无他人赠送</div>
            </div>
            <div class="give-tips">
              通过他人分享最多领取3张学霸卡，同一人分享的学霸卡仅可领取一次。
            </div>
          </div>

        </div>
        <div class="pack-up-false" v-show="packup">
          <div class="official-top official-top__left">
            <div class="official-top-flex">
              <img class="official-top-left" :src="require('@/assets/outsourcedCourse/reward2.png')" alt="">
              <div class="official-top-text">学习奖励
              </div>
            </div>
            <div class="official-top-right right15">
              还可解锁 <span> {{ unlockNum }} </span> 张
            </div>
          </div>
          <div class="official-top official-top__right">
              <div class="official-top-flex">
                <img class="official-top-left" :src="require('@/assets/outsourcedCourse/give-top.png')" alt="">
                <div class="official-top-text">他人赠送</div>
              </div>
              <div class="official-top-right right15">
                还可获赠 <span> {{ 3 - giveCardDataList.length }} </span> 张
              </div>
          </div>
        </div>
      </div>
      <div class="general-banner" v-if="xueBaCardConfig.acct_type_code === 'xuebaCommon' && xueBaCardConfig.banner_img_mid">
        <swiper v-if="xueBaCardConfig.banner_img_mid.length  > 1" :options="swiperOption" ref="mySwiper">
          <swiper-slide class="swiper-slide swiper-container-3d" v-for="(banner,index) in xueBaCardConfig.banner_img_mid" :key="index" style="position:relative;overflow:hidden;width:100%;height:128px;">
            <a :href="banner.href" target="_blank" style="inline-block">
              <img :src="banner.img_url" style="width:100%;height:100%" />
            </a>
          </swiper-slide>
          <!-- 分页器 -->
          <div class="swiper-pagination" slot="pagination"></div>
          <!-- 左右箭头 -->
          <!-- @click="prev" -->
          <div class="swiper-button-prev" v-if="xueBaCardConfig.banner_img_mid.length  > 1" slot="button-prev"></div>
          <!-- @click="next" -->
          <div class="swiper-button-next" v-if="xueBaCardConfig.banner_img_mid.length > 1" slot="button-next"></div>
        </swiper>
        <div v-else>
          <a :href="xueBaCardConfig.banner_img_mid[0].href" target="_blank" style="inline-block">
            <img :src="xueBaCardConfig.banner_img_mid[0].img_url" style="width:100%;height:128px" />
          </a>
        </div>
      </div>
      <!-- <div class="invite-main" id="invite-main_id" v-if="xueBaCardConfig.acct_type_code !== 'xuebaCommon'">
        <div class="invite-main_title">分享奖励：分享学霸卡给同事，可额外获得学霸卡奖励</div>
        <div class="give-explain-content">
          <p><span style="font-weight:500">分享学霸卡：</span>领取官方下发的学霸卡后，即获得10次学霸卡分享资格。</p>
          <p><span style="font-weight:500">分享奖励：</span>
            1、 成功分享学霸卡给同事，分享人可额外获赠1张学霸卡，分享人通过分享行为最多可额外获赠3张；<br />
            <span style="margin-left:74px;"></span> 2、 被分享人最多获得3张他人分享的学霸卡。若被分享人收到的学霸卡已超过3张，则无法分享成功。
          </p>
        </div>
        <giveTab @openGive="openGiveDialog" :numberOfRewards="numberOfRewards" :isQuantity="isQuantity" :Config="xueBaCardConfig" :listObj="{presentRecordList: presentRecordList, presentPassiveRecordList: giveCardDataList }"></giveTab>
      </div> -->
      <div class="course-list" v-if="(courseList || []).length">
        <div class="title-col">
          <span class="title">立即兑换课程</span>
          <span class="link" @click="toActiveDetail" :dt-eid="dtBtnMain('eid','查看更多可兑换好课')" :dt-areaid="dtBtnMain('area','查看更多可兑换好课')" :dt-remark="dtBtnMain('remark','查看更多可兑换好课')">>>> 查看更多可兑换好课</span>
        </div>
        <div class="list">
          <div class="course-item" v-for="(item, index) in (courseList || [])" :key="item.course_id" :dt-areaid="dtListBody(item, 'area', index)" :dt-eid="dtListBody(item , 'eid', index)" :dt-remark="dtListBody(item , 'remark', index)" @click="toCourseDetail(item.course_url)">
            <el-image class="cover" :src="getCourseCoverUrl(item.course_pic_id)">
              <div class="image-slot" slot="placeholder">
                <i class="el-icon-loading"></i>
              </div>
              <div class="error-cover" slot="error">
                <img class="image-box" :src="require('@/assets/outsourcedCourse/movie.png')" alt="" />
              </div>
            </el-image>
            <div class="time" v-if="item.course_length">{{item.course_length}}分钟</div>
            <div class="title">
              <span>{{item.course_from_name}}</span>{{ item.course_name }}
            </div>
          </div>
        </div>
        <div class="link">
          <span @click="toActiveDetail" :dt-areaid="dtMoerCourses('area', '2')" :dt-eid="dtMoerCourses('eid', '2')" :dt-remark="dtMoerCourses('remark', '2')"> &gt;&gt;&gt; 查看更多可兑换好课 &lt;&lt;&lt; </span>
        </div>
      </div>
    </div>

    <el-dialog custom-class="coupon-dialog" :visible.sync="dialogVisible" width="711px" :show-close="false">
      <div class="usage-details">
        <div class="head">
          <span>学霸卡「{{cardName}}」{{xueBaCardConfig.acct_type_code === 'xuebaCommon' ? '' : '专用卡' }}领用详情</span>
          <img class="close" src="@/assets/mooc-img/close.png" @click="dialogVisible = false" />
        </div>
        <div class="body">
          <el-table :data="tableData" style="width: 100%; " header-row-class-name="table-header-style" row-class-name="table-row-style" class="table-content">
            <el-table-column prop="index" label="序号" width="60">
              <template slot-scope="scope">
                <span>{{ (pagination.current-1)*pagination.size + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="receive_time" label="领取时间" width="160"></el-table-column>
            <el-table-column prop="receive_time" label="获得方式" width="90">
              <template slot-scope="scope">
                <span>{{ scope.row.receive_verb_name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="verb_id" label="卡券状态" width="90">
              <template slot-scope="scope">
                <span :class="['status-label', getCardInfo(scope.row.verb_id).class]">{{ getCardInfo(scope.row.verb_id).name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="说明" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ resolveDescData(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column label="" width="100">
              <template slot-scope="scope">
                <div class="operat-btn-box" v-if="!scope.row.verb_id">
                  <el-link @click="toManagePage()" type="primary" :underline="false">去兑换课程</el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <div :class="['box btn prev', { 'disable': pagination.current <= 1 }]" @click="toPrev">
              <img class="img-16 icon-grey" src="@/assets/mooc-img/bottom-arrow.png" />
              <img class="img-16 icon-active" src="@/assets/mooc-img/<EMAIL>" />上一页
            </div>
            <div class="box num">
              <span class="current">{{ pagination.current }}</span>
              <span class="">/</span>
              <span class="total">{{ pagination.pageNum }}</span>
            </div>
            <div :class="['box btn next', { 'disable': pagination.current >= pagination.pageNum} ]" @click="toNext">
              下一页
              <img class="img-16 icon-grey" src="@/assets/mooc-img/bottom-arrow.png" />
              <img class="img-16 icon-active" src="@/assets/mooc-img/<EMAIL>" />
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
    <has-course-dialog :isShow.sync="dialogVisibleCourse" :xueBaCardConfig="xueBaCardConfig"></has-course-dialog>
    <!-- <give-dialog @handlerGiveXuebaka="handlerGiveXuebaka" :isShow.sync="giveDialog" :xueBaCardConfig="xueBaCardConfig" :giveNumber="presentRecordList.length" :numberOfRewards="numberOfRewards" :consumePoint="consumePoint"></give-dialog> -->
    <xue-ba-share :isShow.sync="giveDialog" :cardName="cardName" :courseType="xueBaCardConfig.acct_type_code" :xueBaCardConfig="xueBaCardConfig"></xue-ba-share>
    <send-record :isShow.sync="recordDialog"></send-record>
  </div>
</template>

<script>
import env from 'config/env.conf.js'
import {
  getHomePageInfo,
  claimPoints,
  queryGeekRecord,
  getUserActiveInfo,
  messageSubscribe,
  messageUnsubscribe,
  getAcctinfos,
  getSubscribeStatus,
  activityPresent
  // getActiveOrders
} from '@/config/mooc.api.conf.js'
import { throttle } from '@/utils/tools.js'
import hasCourseDialog from './components/hasCourseDialog.vue'
// import giveDialog from './components/giveDialog.vue'
// import giveTab from './components/giveTab.vue'
import XueBaShare from './components/xueBaShare.vue'
import SendRecord from './components/sendRecord.vue'
const HTTPS_REG = /^https:\/\//
export default {
  components: {
    hasCourseDialog,
    // giveDialog,
    // giveTab,
    XueBaShare,
    SendRecord
  },
  data() {
    return {
      officialList: [], // 官方领卡
      rewardList: [], // 学习奖励
      rewarCardKey: [
        
      ],
      xueBaCardConfig: {},
      courseList: [],
      presentRecordList: [],
      presentRewardList: [], // 报酬
      numberOfRewards: 0,
      consumePoint: 0,
      usedNum: 0,
      overdueNum: 0,
      dialogVisibleCourse: false,
      giveDialog: false,
      recordDialog: false,
      packup: true,
      isSub: false,
      isGetOfficial: false,
      userInfo: {},
      cardInfo: {
        can_get_geek: false,
        balance: 0,
        quantity: 0,
        can_get_geek_num: 0,
        can_get_reward_num: 0
      }, // 是否可领券、库存信息
      dialogVisible: false,
      tableData: [],
      cardDataList: [],
      giveCardDataList: [],
      // 分页
      pagination: {
        pageNum: 1,
        current: 1,
        size: 5,
        total: 0
      },
      swiperOption: {
        loop: true, // 是否循环轮播
        speed: 1000, // 切换速度
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        // 自动轮播
        autoplay: {
          delay: 5000,
          disableOnInteraction: false
        },
        // 设置slider容器能够同时显示的slides数量
        slidesPerView: 1,
        // 左右切换
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        // 分页器
        pagination: {
          el: '.swiper-pagination',
          clickable: true // 允许点击小圆点跳转
        }
      }
    }
  },
  watch: {},
  computed: {
    // 有无分享整体名额
    isQuantity() {
      const { quantity = 0 } = this.xueBaCardConfig
      return quantity > 0
    },
    //  可领取的数量
    limitNum() {
      let limitn = this.cardInfo.limit || 5
      return limitn - 1
    },
    cardName() {
      return this.xueBaCardConfig.card_name || ''
    },
    activityId() {
      return this.$route.query.activityId || 1
    },
    getCardInfo() {
      return (res) => {
        let statusMap = {
          0: '待兑换',
          consume: '已兑换',
          manage_deduct: '已兑换',
          deduct_expire: '已失效'
          // manage_deduct: '已使用',
        }
        let statusStyleMap = {
          0: 'status-waite-use',
          consume: 'status-oready-used',
          manage_deduct: 'status-oready-used',
          deduct_expire: 'status-no-effict'
        }
        return {
          name: statusMap[res || 0],
          class: statusStyleMap[res || 0]
        }
      }
    },
    // 学习奖励已经领取的数量
    getNums() {
      return this.rewardList.length || 0
    },
    // 领取顺序
    cardsOrder() {
      return JSON.parse(JSON.stringify(this.rewardList)).reverse()
    },
    // 头部背景图
    bannerImgTop() {
      return (type) => {
        const { banner_img_top } = this.xueBaCardConfig
        if (!banner_img_top) return
        return type === 'top'
          ? `background-image:url(${banner_img_top.banner_bg})`
          : `background-image:url(${banner_img_top.banner_center})`
      }
    },
    unlockNum() {
      return 4 - this.getNums
    },
    // 学习奖励是否可以领取
    canGetReward() {
      const { can_get_reward_num, can_get_geek } = this.cardInfo
      return can_get_reward_num > 0 && can_get_geek
    },
    receiveRemark() {
      return (index, all) => {
        let { receive_remark = '', receive_time } = this.giveCardDataList[index - 1]
        let str = receive_remark.split('赠送')[0]
        let result = all ? str.substring(0, 7) + '...' : str + this.$moment(receive_time).format('MM月DD日')
        return result
      }
    },
    // 达标卡 背景图
    newbgiStyle() {
      return (i) => {
        // canGetRewardNum-可领取的兑换券的数量，canGetGeek-是否可以领取
        let index = i - 1
        const { card_img_pc } = this.xueBaCardConfig
        if (!card_img_pc) return ''
        if (this.rewardList.length < 4) {
          if (index === 0) {
            return this.canGetReward ? `background-image: url(${card_img_pc.card_img_url_a});` : `background-image: url(${card_img_pc.card_img_url_b});`
          } else {
            if (this.rewardList.length === 0) {
              return `background-image: url(${card_img_pc.card_img_url_b});`
            } else if (this.rewardList.length === 1) {
              if (index > 2) {
                return `background-image: url(${card_img_pc.card_img_url_a});`
              } else {
                return `background-image: url(${card_img_pc.card_img_url_b});`
              }
            } else if (this.rewardList.length === 2) {
              if (index > 1) {
                return `background-image: url(${card_img_pc.card_img_url_a});`
              } else {
                return `background-image: url(${card_img_pc.card_img_url_b});`
              }
            } else if (this.rewardList.length === 3) {
              if (index > 0) {
                return `background-image: url(${card_img_pc.card_img_url_a});`
              } else {
                return `background-image: url(${card_img_pc.card_img_url_b});`
              }
            }
          }
        } else {
          if (this.rewardList[index] &&
            [0, 'consume', 'manage_deduct', 'deduct_expire', undefined].includes(
              this.rewardList[index].verb_id
            )
          ) {
            return `background-image: url(${card_img_pc.card_img_url_a});`
          } else {
            return `background-image: url(${card_img_pc.card_img_url_b});`
          }
        }
      }
    },
    // 达标卡 背景图
    // bgiStyle() {
    //   return (item) => {
    //     const { card_img_pc } = this.xueBaCardConfig
    //     if (!card_img_pc) return
    //     if (
    //       this.getNums >= 1 &&
    //       item >= this.getNums &&
    //       this.cardInfo.can_get_reward_num &&
    //       this.cardInfo.can_get_geek
    //     ) {
    //       // return `background-image: url(${require('@/assets/outsourcedCourse/card-stand-new.png')});`
    //       return `background-image: url(${card_img_pc.card_img_url_a});`
    //     } else if (
    //       this.getNums >= 1 &&
    //       item < this.getNums &&
    //       this.cardsOrder[item] &&
    //       [0, 'consume', 'manage_deduct', 'deduct_expire', undefined].includes(
    //         this.cardsOrder[item].verb_id
    //       )
    //     ) {
    //       console.log('bgiStyleccccccc')
    //       // return `background-image: url(${require('@/assets/outsourcedCourse/card-stand-new.png')});`
    //       return `background-image: url(${card_img_pc.card_img_url_a});`
    //     } else {
    //       console.log('bgiStyledddddd')
    //       // return `background-image: url(${require('@/assets/outsourcedCourse/card-stand-no.png')});`
    //       return `background-image: url(${card_img_pc.card_img_url_b});`
    //     }
    //   }
    // },
    // 初始卡 背景图
    bgiStyle1() {
      const { card_img_pc } = this.xueBaCardConfig
      if (!card_img_pc) return
      if (this.cardInfo.can_get_geek_num > 0) {
        // return `background-image: url(${require('@/assets/outsourcedCourse/card-init-new.png')});`
        return `background-image: url(${card_img_pc.card_img_url_a});`
      } else if (this.officialList.length && [0, 'consume', 'manage_deduct', 'deduct_expire', undefined].includes(this.officialList[0].verb_id)
      ) {
        // return `background-image: url(${require('@/assets/outsourcedCourse/card-init-new.png')});`
        return `background-image: url(${card_img_pc.card_img_url_a});`
      } else {
        return `background-image: url(${card_img_pc.card_img_url_b});`
        // return `background-image: url(${require('@/assets/outsourcedCourse/card-init-no-effict.png')});`
      }
    },
    bgiStyleGive() {
      return (num, list) => {
        let giveNum = list.length
        const { card_img_pc } = this.xueBaCardConfig
        if (!card_img_pc) return
        console.log(giveNum, num, 'giveNumgiveNumgiveNumgiveNum')
        if (giveNum >= num) {
          return `background-image: url(${card_img_pc.card_img_url_a});`
        } else {
          return `background-image: url(${card_img_pc.card_img_url_b});`
        }
      }
    },
    buttonColor() {
      return (type, text) => {
        const { card_img_pc } = this.xueBaCardConfig
        if (!card_img_pc) return ''
        if (text && text === '待使用') return card_img_pc.button_style_a
        if (type === 1) {
          return `${card_img_pc.button_style_a}`
        } else if (type === 2) {
          return `${card_img_pc.button_style_b}; opacity: 0.9;`
        }
      }
    },
    // 按钮显示的文字
    showText() {
      return (index, list) => {
        let statusMap = {
          0: '待使用',
          consume: '已使用',
          manage_deduct: '已使用',
          deduct_expire: '已过期'
          // manage_deduct: '已使用',
        }
        if (list[index]) {
          let res = list[index].verb_id
          return statusMap[res || 0]
        } else if (!this.cardInfo.quantity) {
          return '抢光了'
        } else {
          return '点击领取'
        }
      }
    },
    showTextRe() {
      return (index, list) => {
        let statusMap = {
          0: '待使用',
          consume: '已使用',
          manage_deduct: '已使用',
          deduct_expire: '已过期'
          // manage_deduct: '已使用',
        }
        if (this.rewardList.length < 4) {
          if (index === 0) {
            return '点击领取'
          } else {
            return '已使用'
          }
        } else {
          if (list[index]) {
            let res = list[index].verb_id
            return statusMap[res || 0]
          } else if (!this.cardInfo.quantity) {
            return '抢光了'
          } else {
            return '点击领取'
          }
        }
      }
    },
    // 他人赠送的
    showTextGive() {
      return (index, type) => {
        let statusMap = {
          0: '待使用',
          consume: '已使用',
          manage_deduct: '已使用',
          deduct_expire: '已过期'
          // manage_deduct: '已使用',
        }
        let list = type === 'passive' ? this.giveCardDataList : this.presentRewardList
        if (list[index - 1]) {
          let res = list[index - 1].verb_id
          return statusMap[res || 0]
        }
      }
    },
    // 初始卡 判断是否置灰 “学霸卡”和“可用于兑换外部付费好课”文字置灰
    isDisableStyle1() {
      if (this.cardInfo.can_get_geek_num > 0) {
        return false
      } else if (this.officialList.length &&
        [0, 'consume', 'manage_deduct', undefined].includes(
          this.officialList[0].verb_id
        )
      ) {
        return false
      } else {
        return true
      }
    },
    // 字体颜色
    card_font_style() {
      return (type) => {
        const { card_img_pc } = this.xueBaCardConfig
        if (!card_img_pc) return ''
        return `color:${
          type === 'a'
            ? card_img_pc.card_font_style_a
            : card_img_pc.card_font_style_b
        }`
      }
    },
    dtMoerCourses() {
      return (type, index) => {
        let { audience_id, audience_name } = this.xueBaCardConfig
        if (type === 'area') {
          return `area_${audience_id}_more_${index}`
        } else if (type === 'eid') {
          return `element_${audience_id}_more_${index}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${this.cardName}活动首页`,
            page_type: `${this.cardName}活动首页`,
            container: audience_name,
            container_id: '',
            click_type: 'button',
            content_name: '查看更多可兑换好课' + index,
            terminal: 'PC'
          })
        }
      }
    },
    dtBotton() {
      return (type, name) => {
        let { audience_id, audience_name } = this.xueBaCardConfig
        if (type === 'area') {
          return `area_${audience_id}_receive`
        } else if (type === 'eid') {
          return `element_${audience_id}_receive`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${this.cardName}活动首页`,
            page_type: `${this.cardName}活动首页`,
            container: audience_name,
            container_id: '',
            click_type: 'button',
            content_name: name,
            terminal: 'PC'
          })
        }
      } 
    },
    // 埋点
    dtListBody() {
      return (item, type, index) => {
        let dt_id = `${item.course_from}_${item.course_id}`
        if (type === 'area') {
          return `area_${dt_id}`
        } else if (type === 'eid') {
          return `element_${dt_id}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${this.cardName}活动首页`,
            page_type: `${this.cardName}活动首页`,
            container:
              index <= 7
                ? `${this.xueBaCardConfig.audience_name}_8`
                : `${this.xueBaCardConfig.audience_name}_16`,
            container_id: '',
            click_type: 'data',
            content_type: '培养项目',
            act_type: '11',
            content_id: item.course_id,
            content_name: item.course_name,
            terminal: 'PC'
          })
        }
      }
    },
    dtBtnMain() {
      return (type, name) => {
        if (type === 'area') {
          return `area_${name}`
        } else if (type === 'eid') {
          return `element_${name}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${this.cardName}学霸卡活动落地页`,
            page_type: '学霸卡活动落地页',
            container: '',
            click_type: 'button',
            content_type: '',
            act_type: '',
            content_id: '',
            content_name: name,
            terminal: 'PC',
            page_id: '',
            container_id: ''
          })
        }
      }
    },
    dtEid() {
      return (val) => {
        return `element_${val}`
      }
    },
    dtAreaid() {
      return (val) => {
        return `area_${val}`
      }
    },
    dtRemark() {
      return (val) => {
        return JSON.stringify({ 
          page: `${this.cardName}学霸卡活动落地页`,
          page_type: '学霸卡活动落地页',
          container: '',
          click_type: 'button',
          content_type: '',
          act_type: '',
          content_id: '',
          content_name: val,
          terminal: 'PC',
          page_id: '',
          container_id: ''
        })
      }
    },
    dtSubscript() {
      return (type) => {
        let name = this.isSub ? '退订领卡提醒' : '订阅领卡提醒'
        if (type === 'area') {
          return `area_${name}`
        } else if (type === 'eid') {
          return `element_${name}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${this.cardName}学霸卡活动落地页`,
            page_type: '学霸卡活动落地页',
            container: '',
            click_type: 'button',
            content_type: '',
            act_type: '',
            content_id: '',
            content_name: name,
            terminal: 'PC',
            page_id: '',
            container_id: ''
          })
        }
      }
    }
  },
  async created() {
    await this.getUserActiveInfo()
    await this.getHomePageInfoFn()
    await this.getRewardDetails()
    // 获取数据判断是不是已经领取官方发放
    await this.getRewardDetails(100)
    await this.getCardDataList()
    await this.getGiveCard()
    await this.presentReward()
    await this.getSubscribeStatus()
    await this.getAcctinfos()
    // await this.getActiveOrders()
    // 如果是分享链接直接调用赠送劝学卡
    if (this.$route.query.staff_id && this.$route.query.staff_name) {
      this.activityPresent()
    }
  },
  mounted() {
    // 邀请劝学
    // this.$nextTick(() => {
    //   if (!this.$route.query.position) return
    //   setTimeout(() => {
    //     const element = document.getElementById('invite-main_id')
    //     if (element) {
    //       console.log(element)
    //       element.scrollIntoView({ behavior: 'smooth' })
    //     }
    //   }, 2000)
    // })
  },
  methods: {
    handlePack() {
      this.packup = !this.packup
    },
    openSeeRecord() {
      this.recordDialog = true
    },
    // 获取已经使用卡券数量
    // getActiveOrders() {
    //   let params = {
    //     current: this.pagination.current,
    //     size: this.pagination.size,
    //     sourceFrom: this.xueBaCardConfig.acct_type_code
    //   }
    //   getActiveOrders(params).then((res) => {
    //     this.usedNum = res.total
    //   })
    // },
    // 获取他人赠送的卡券
    activityPresent() {
      const userInfo = JSON.parse(sessionStorage.getItem('login_user'))
      let params = {
        from: this.$route.query.staff_id,
        from_name: this.$route.query.staff_name,
        acct_type_code: this.xueBaCardConfig.acct_type_code,
        to_batch: [userInfo.staff_id],
        object_id: this.xueBaCardConfig.activity_id,
        object_name: this.xueBaCardConfig.activity_name,
        object_type: 'XuebaActivity',
        object_type_name: '活动',
        trans_amt: '1',
        notify_type: 0 // 手动赠送1 ，用户进入页面自动领的 0
      }
      activityPresent(params).then((res) => {
        if (res.success_count) {
          let num = 3 - this.giveCardDataList.length - 1
          this.$message({
            message: `学霸卡领取成功。您通过他人赠送还可以再领取<span style="color:red;font-weight:600">${num}张</span> 学霸卡`,
            type: 'success',
            dangerouslyUseHTMLString: true,
            duration: 5000,
            customClass: 'XuebaActivity_msg'
          })
          setTimeout(() => {
            const element = document.getElementById('learning-reward-success')
            if (element) {
              console.log(element)
              element.scrollIntoView({ behavior: 'smooth' })
            }
          }, 800)
        }
        // 赠送失败
        if (res.fail_count) {
          let isRepeat = res.fail_names.includes('您已领取过')
          if (isRepeat) {
            // let str = res.fail_names.split('[')
            // let newStr = str[1].replace(/]/g, '')
            this.$message({
              message: '您已领取过该同事赠送的学霸卡',
              type: 'error',
              duration: 5000,
              customClass: 'XuebaActivity_msg'
            })
          } else if (this.giveCardDataList.length === 3) {
            this.$message({
              message: '学霸卡领取失败。您收到他人赠送的学霸卡已达上限（最多3张）。',
              type: 'error',
              duration: 5000,
              customClass: 'XuebaActivity_msg'
            })
          } 
          // else {
          //   this.$message({
          //     message: res.fail_names,
          //     type: 'error',
          //     duration: 5000,
          //     customClass: 'XuebaActivity_msg'
          //   })
          // }
        }
        console.log(res, '赠送接口')
        this.getAcctinfos()
        this.getHomePageInfoFn()
        this.getGiveCard()
      })
    },
    openGiveDialog() {
      if (!this.isGetOfficial) {
        return
      }
      console.log(1111)
      this.giveDialog = true
    },
    async getSubscribeStatus() {
      let res = await getSubscribeStatus(this.xueBaCardConfig.acct_type_code)
      this.isSub = res
    },
    messageSubscribe() {
      if (!this.isSub) {
        messageSubscribe({}, this.xueBaCardConfig.acct_type_code).then(
          (res) => {
            this.$message.success('订阅成功')
            this.getSubscribeStatus()
          }
        )
      } else {
        messageUnsubscribe({}, this.xueBaCardConfig.acct_type_code).then(
          (res) => {
            this.$message.success('退订成功')
            this.getSubscribeStatus()
          }
        )
      }
    },
    async getAcctinfos() {
      let params = {
        acct_type_codes: this.xueBaCardConfig.acct_type_code + 'Trans'
      }
      const result = await getAcctinfos(params)
      console.log(result, '查看有没有余额有余额解锁赠送劝学卡')
      //   查看有没有余额有余额解锁赠送劝学卡
      // this.consumePoint = Number(result.consume_point)
    },
    // 达标卡 右上角图片
    // getClass(item) {
    //   if (
    //     this.getNums >= 1 &&
    //     1 >= this.getNums &&
    //     this.cardInfo.can_get_reward_num &&
    //     this.cardInfo.can_get_geek
    //   ) {
    //     return `` // 待领取
    //   } else if (
    //     this.getNums >= 1 &&
    //     item < this.getNums &&
    //     this.cardsOrder[item] &&
    //     [0, 'consume', 'manage_deduct', undefined].includes(
    //       this.cardsOrder[item].verb_id
    //     )
    //   ) {
    //     if ([0, undefined].includes(this.cardsOrder[item].verb_id)) {
    //       return `card-stand-1` // 待兑换
    //     } else {
    //       return `card-stand-2` // 已兑换
    //     }
    //   } else {
    //     if (
    //       this.cardsOrder[item] &&
    //       this.cardsOrder[item].verb_id === 'deduct_expire'
    //     ) {
    //       return `card-stand-4` // 已失效
    //     } else {
    //       return `card-stand-3` // 未解锁
    //     }
    //   }
    // },
    getClass(i) {
      let index = i - 1
      // 如果可以领取第一张需要可领取
      if (this.rewardList.length < 4) {
        if (index === 0) {
          return this.canGetReward ? '' : `card-stand-3` // 未解锁
        } else {
          if (this.rewardList.length === 0) {
            return `card-stand-3`
          } else if (this.rewardList.length === 1) {
            if (index > 2) {
              return `card-stand-2` // 已兑换
            } else {
              return `card-stand-3` // 未解锁
            }
          } else if (this.rewardList.length === 2) {
            if (index > 1) {
              return `card-stand-2` // 已兑换
            } else {
              return `card-stand-3` // 未解锁
            }
          } else if (this.rewardList.length === 3) {
            if (index > 0) {
              return `card-stand-2` // 已兑换
            } else {
              return `card-stand-3` // 未解锁
            }
          }
        }
      } else {
        if (this.rewardList[index] && [0, 'consume', 'manage_deduct', undefined].includes(this.rewardList[index].verb_id)) {
          if ([0, undefined].includes(this.rewardList[index].verb_id)) {
            return `card-stand-1` // 待兑换
          } else {
            return `card-stand-2` // 已兑换
          }
        } else {
          if (this.rewardList[index] && this.rewardList[index].verb_id === 'deduct_expire') {
            return `card-stand-4` // 已失效
          } else {
            return `card-stand-3` // 未解锁
          }
        }          
      }
    },
    // 初始卡 右上角图片
    getClass1() {
      if (this.cardInfo.can_get_geek_num > 0) {
        return `` // 待领取
      } else if (this.officialList.length && [0, 'consume', 'manage_deduct', undefined].includes(this.officialList[0].verb_id)) {
        if ([0, undefined].includes(this.officialList[0].verb_id)) {
          return `card-stand-1` // 待兑换
        } else {
          return `card-stand-2` // 已兑换
        }
      } else {
        return `card-stand-4` // 已失效
      }
    },
    //  他人赠送的, 获得奖励 右上角图片
    getClassGive(num, list) {
      let index = num - 1
      let giveNum = list.length
      if (
        giveNum >= num &&
        list[index] &&
        [0, 'consume', 'manage_deduct', undefined].includes(
          list[index].verb_id
        )
      ) {
        if ([0, undefined].includes(list[index].verb_id)) {
          return `card-stand-1` // 待兑换
        } else {
          return `card-stand-2` // 已兑换
        }
      } else {
        if (list[index] && list[index].verb_id === 'deduct_expire') {
          return `card-stand-4` // 已失效
        } else {
          return `card-stand-3` // 未解锁
        }
      }
    },
    // 达标卡 判断是否置灰 “学霸卡”和“可用于兑换外部付费好课”文字置灰
    // isDisableStyle(item) {
    //   if (
    //     this.getNums >= 1 &&
    //     item >= this.getNums &&
    //     this.cardInfo.can_get_reward_num &&
    //     this.cardInfo.can_get_geek
    //   ) {
    //     return false
    //   } else if (
    //     this.getNums >= 1 &&
    //     item < this.getNums &&
    //     this.cardsOrder[item] &&
    //     [0, 'consume', 'manage_deduct', 'deduct_expire', undefined].includes(
    //       this.cardsOrder[item].verb_id
    //     )
    //   ) {
    //     return false
    //   } else {
    //     return true
    //   }
    // },
    //  他人赠送置灰
    isDisableStyleGive(num) {
      let giveNum = this.giveCardDataList.length
      return giveNum >= num
    },
    initData() {
      this.getHomePageInfoFn()
      this.getRewardDetails()
      this.getCardDataList()
      // 后端异步需要延后调用
      setTimeout(() => {
        this.getAcctinfos()
      }, 1000)
      this.isGetOfficial = true
    },
    // 获取学霸卡基础信息
    async getUserActiveInfo() {
      try {
        let res = await getUserActiveInfo({ activity_id: this.activityId })
        console.log(res, '获取学霸卡基础信息')
        this.xueBaCardConfig = res
        this.courseList = res.course_list || []
        let nowTime = new Date().getTime()
        let activeTime = new Date(res.end_time).getTime()
        if (nowTime > activeTime) {
          this.$message.error(res.activity_name + '活动已结束，敬请关注后续活动')
        }
      } catch (error) {
        console.log('获取基础信息: ', error)
      }
    },
    // 赠送的
    // async getPresentRecord() {
    //   let params = {
    //     activityId: this.xueBaCardConfig.activity_id,
    //     current: 1,
    //     size: 10
    //   }
    //   const res = await getPresentRecord(params)
    //   console.log(res, '赠送')
    //   this.presentRecordList = res.records || []
    //   //   获得奖励的有多少张
    //   let presentFilterList = this.presentRecordList.filter(
    //     (item) => item.reward_amt
    //   )
    //   this.numberOfRewards = presentFilterList.length
    // },
    handlerGiveXuebaka() {
      this.getHomePageInfoFn()
      this.getRewardDetails()
      this.getCardDataList()
      this.getAcctinfos()
    },
    // 积分授予-查询是否可以领取geek
    async getHomePageInfoFn() {
      try {
        this.userInfo = JSON.parse(sessionStorage.getItem('login_user'))
        let res = await getHomePageInfo({
          staff_id: this.userInfo.staff_id,
          acct_type_code: this.xueBaCardConfig.acct_type_code,
          activity_id: this.activityId
        })
        this.cardInfo = res
      } catch (error) {
        if (error.code === 0 && error.data) {
          this.cardInfo = error.data
        }
      }
    },
    // 点击领取卡券 节流方法
    throttleFn: throttle(function (type, status) {
      this.getCoupon(type, status)
    }, 500),
    // 点击领取卡券
    getCoupon(type, status) {
      switch (type) {
        case 1:
          // 初始卡
          if (status) {
            this.claimPointsFn(this.xueBaCardConfig.acct_type_code)
          } else {
            // hasGetGeek-是否已经领取，canGetGeekNum-可领取的兑换券的数量，formalStaff-是否正式员工，totalBalance-已领取数量，limit-最大限制数量
            const {
              has_get_geek: hasGetGeek,
              can_get_geek_num: canGetGeekNum,
              formal_staff: formalStaff,
              total_balance: totalBalance,
              limit
            } = this.cardInfo
            if (!formalStaff) {
              this.$message.warning('非集团本部正式员工不可领取')
            } else if (totalBalance >= limit) {
              this.$message.warning('已达到领取数量上限')
            } else if (!hasGetGeek && canGetGeekNum <= 0) {
              this.$message.warning(
                '本轮学霸卡已发放完毕，暂时无法领取，敬请期待后续轮次的发放活动'
              )
            } else if (hasGetGeek) {
              this.$message.warning(
                '你本轮次已领取过学霸卡，每人限领1次，敬请期待后续轮次的发放活动'
              )
            }
          }
          break
        case 2:
          // 达标卡
          if (status) {
            this.claimPointsFn('reward')
          } else {
            // hasGetGeekReward-是否已经领取，canGetRewardNum-可领取的兑换券的数量，canGetGeek-是否可以领取，formalStaff-是否正式员工，totalBalance-已领取数量，limit-最大限制数量
            const {
              has_get_geek_reward: hasGetGeekReward,
              can_get_reward_num: canGetRewardNum,
              can_get_geek: canGetGeek,
              formal_staff: formalStaff,
              total_balance: totalBalance,
              limit
            } = this.cardInfo
            let canBeClaimed = canGetRewardNum > 0 && canGetGeek
            if (!formalStaff) {
              this.$message.warning('非集团本部正式员工不可领取')
            } else if (totalBalance >= limit) {
              this.$message.warning('已达到领取数量上限')
            } else if (!canBeClaimed) {
              this.$message.warning(
                '暂无领取资格，请查看活动规则并完成学习条件后再点击领取'
              )
            } else if (!hasGetGeekReward && canGetRewardNum <= 0) {
              this.$message.warning(
                '本轮学霸卡已发放完毕，暂时无法领取，敬请期待后续轮次的发放活动'
              )
            } else if (hasGetGeekReward) {
              this.$message.warning(
                '你本轮次已领取过学霸卡，每人限领1次，敬请期待后续轮次的发放活动'
              )
            }
          }
          break
        default:
          break
      }
    },
    // 发请求领取积分卡
    async claimPointsFn(type) {
      try {
        let payload = {
          staff_id: this.userInfo.staff_id,
          staff_name: this.userInfo.staff_name,
          acct_type_code: this.xueBaCardConfig.acct_type_code,
          grant_amt: '1',
          grant_type: type,
          busi_id: this.xueBaCardConfig.activity_id
        }
        await claimPoints(payload)
        this.$message.success('学霸卡领取成功')
        this.initData()
      } catch (error) {
        console.log('领取兑换券ERR：', error)
        this.initData()
      }
    },
    // 发请求获取领用详情
    getRewardDetails(size) {
      let careteSize = size
      queryGeekRecord({
        staff_id: this.userInfo.staff_id,
        acct_type_code: this.xueBaCardConfig.acct_type_code,
        current: this.pagination.current,
        size: careteSize || this.pagination.size,
        activity_id: this.activityId
      }).then((res) => {
        if (careteSize) {
          let list = res.records || []
          this.isGetOfficial = list.some(item => item.receive_verb_id === 'receive')
          this.usedNum = list.filter(item => (item.verb_id === 'consume' || item.verb_id === 'manage_deduct')).length
          this.overdueNum = list.filter(item => item.verb_id === 'deduct_expire').length
          return
        }
        this.tableData = res.records
        console.log('tableData: ', this.tableData)
        // 分页数据处理
        this.pagination.total = res.total
        const { size } = this.pagination
        let pages = Math.floor(res.total / size)
        let pageNum = pages > 0 ? pages : 1
        let remainder = res.total > size ? res.total % size : 0
        this.pagination.pageNum = remainder > 0 ? pageNum + 1 : pageNum
      })
    },
    // 五张自主领取的卡片
    getCardDataList() {
      queryGeekRecord({
        staff_id: this.userInfo.staff_id,
        acct_type_code: this.xueBaCardConfig.acct_type_code,
        current: 1,
        size: 10,
        receive_verb_id: 'receive',
        activity_id: this.activityId
      }).then((res) => {
        // 学习奖励
        this.rewardList = res.records.filter(item => item.busi_code === 'reward')
        this.cardDataList = res.records
        // 官方发放
        this.officialList = res.records.filter(item => item.busi_code === this.xueBaCardConfig.acct_type_code)
        console.log(
          this.cardDataList,
          'cardDataListcardDataListcardDataListcardDataListcardDataList五张自主领取的卡片'
        )
      })
    },
    // 三张他人赠送的
    getGiveCard() {
      queryGeekRecord({
        staff_id: this.userInfo.staff_id,
        acct_type_code: this.xueBaCardConfig.acct_type_code,
        current: 1,
        size: 10,
        receive_verb_id: 'present_passive',
        activity_id: this.activityId
      }).then((res) => {
        this.giveCardDataList = res.records
        // 判断可以领取学习卡券或者有他人赠送的时候展开 学习奖励他人赠送区域
        // setTimeout(() => {
        //   if (this.canGetReward || this.giveCardDataList.length) {
        //     this.packup = false
        //   }
        // }, 800)
        console.log(res, '他人赠送···············')
      })
    },
    // 赠送获得的奖励卡券报酬
    presentReward() {
      queryGeekRecord({
        staff_id: this.userInfo.staff_id,
        acct_type_code: this.xueBaCardConfig.acct_type_code,
        current: 1,
        size: 10,
        receive_verb_id: 'present_reward',
        activity_id: this.activityId
      }).then((res) => {
        this.presentRewardList = res.records
        console.log(res, '赠送获得的奖励卡券报酬')
      })
    },
    // 学霸卡领用详情
    openRewardDetails() {
      this.dialogVisible = true
    },
    // 处理列表的"说明"字段
    resolveDescData(item) {
      if (['consume', 'manage_deduct'].includes(item.verb_id)) {
        return `使用时间：${item.deduct_time}`
      } else if (item.verb_id === 'deduct_expire') {
        return `失效时间：${item.expire_time}`
      } else {
        return `领取7天后，卡券失效，请及时使用`
      }
    },
    // 获取内容中心图片
    getCourseCoverUrl(data) {
      if (HTTPS_REG.test(data)) {
        return data
      }
      if (data) {
        const envName = env[process.env.NODE_ENV]
        return `${envName.contentcenter}content-center/api/v1/content/imgage/${data}/preview`
      }
      return 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
    },
    // 领取和使用详情 - 去兑换课程
    toManagePage() {
      window.open(this.xueBaCardConfig.course_more_link)
    },
    // 点击顶部图片跳转
    goTo() {
      let url = this.xueBaCardConfig.activity_detail_link || ''
      if (url) window.open(url)
    },
    // 外部好课专区
    toActiveDetail() {
      window.open(this.xueBaCardConfig.course_more_link)
    },
    // 已兑课程
    openDialogCourse() {
      this.dialogVisibleCourse = true
    },
    // 活动规则
    toRulesDetail() {
      window.open(this.xueBaCardConfig.activity_detail_link)
    },
    // 跳转到课程详情
    toCourseDetail(href) {
      window.open(href)
    },
    // 分页 上一页
    toPrev() {
      if (this.pagination.current <= 1) return
      this.pagination.current--
      this.getRewardDetails()
    },
    // 分页 下一页
    toNext() {
      if (this.pagination.current >= this.pagination.pageNum) return
      this.pagination.current++
      this.getRewardDetails()
    },
    openBanner() {
      window.open(this.xueBaCardConfig.banner_img_mid[0].href, '_blank')
    }
  },
  beforeDestroy() {},
  filters: {}
}
</script>

<style lang="less">

.XuebaActivity_msg.el-message {
    top: 30%!important;
    .el-message__content { 
      font-size: 16px;
    }
  }
.coupon-dialog {
  border-radius: 8px;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
  .el-table .table-header-style {
    height: 46px;
  }
  .el-table .table-header-style th {
    background: #f5f5f5;
  }
  .el-table th > .cell {
    color: #00000099;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }
}
</style>
<style lang="less" scoped>
.color-005 {
  color: #0052d9;
  cursor: pointer;
}
.color-6600 {
 color: #ff6600;
}
.official-top {
  display: flex;
  height: 28px;
  line-height: 28px;
  margin-bottom: 12px;
  position: relative;
  .official-top-left {
    width: 28px;
    height: 28px;
    border-radius: 50%;
    background-color: #fff;
  }
  .official-top-text {
    color: #000000e6;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 28px;
    padding-left: 8px;
    /deep/.el-icon-warning-outline {
      color: #00000099;
      transform: rotate(180deg);
    }
  }
  .official-top-flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .official-top-right {
    color: #00000099;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    position: absolute;
    right: 5px;
    span {
      color: #0052d9;
      cursor: pointer;
    }
  }
  .right15 {
    right: 16px;
  }
}
.swiper-button-prev,
.swiper-button-next {
  width: 24px;
  height: 24px;
  top: 55%;
}
.swiper-button-prev:hover {
  background: var(--font-gy-426-disabled, #00000042);
  border-radius: 3px;
}
.swiper-button-next:hover {
  background: var(--font-gy-426-disabled, #00000042);
  border-radius: 3px;
}
.swiper-button-next:after,
.swiper-rtl .swiper-button-prev:after {
  font-size: 14px;
  color: #fff;
  font-weight: 600;
}
.swiper-button-prev:after,
.swiper-rtl .swiper-button-next:after {
  font-size: 14px;
  color: #fff;
  font-weight: 600;
}
.active-page {
  width: 100%;
  // height: 100vh;
  // overflow: auto;
  .banner-card {
    width: 100%;
    min-width: max-content;
    height: 200px;
    // background: linear-gradient(99deg, #2475F9 5.9%, #0052D9 100.57%);
    // background-image: url('../../../assets/outsourcedCourse/after_bg_sanjieke.png');
    background-position: 50%;
    background-size: cover;
    background-repeat: no-repeat;
    .banner-content {
      position: relative;
      width: 1100px;
      min-width: 1100px;
      height: 100%;
      padding-top: 75px;
      margin: 0 auto;
      position: relative;
      cursor: pointer;
      //   background-image: url('../../../assets/outsourcedCourse/bg_center.png');

      background-size: cover;
      background-repeat: no-repeat;
      .before_bg {
        position: absolute;
        height: 100%;
        top: 0;
        right: 0;
      }
      .banner-image {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        z-index: 1;
        cursor: pointer;
      }
      .cloud-icon {
        width: 131px;
        position: absolute;
        left: 4px;
        bottom: 19.55px;
        z-index: 1;
      }
      .card-icon {
        width: 179px;
        position: absolute;
        right: -7px;
        bottom: 11.7px;
        z-index: 1;
      }
      .toolbar {
        display: flex;
        align-items: center;
        padding-top: 24px;
        position: relative;
        z-index: 2;
        .left {
          display: flex;
          line-height: 32px;
          color: #ffffff;
          font-size: 14px;
          font-weight: 500;
          padding-right: 48px;
          .info {
            padding: 8px 12px;
            font-size: 20px;
            font-weight: 500;
            color: #fff;
            line-height: 22px;
            border-radius: 8px;
            // background: #ffffff80;
            background: #1854b5;
          }
          .link {
            text-decoration-line: underline;
            text-underline-offset: 2px;
            cursor: pointer;
          }
          .wide {
            line-height: 24px;
            color: #f4f8ff;
          }
        }
        .right {
          .common {
            height: 32px;
            padding: 5px 20px;
            border-radius: 64px;
            color: #0052d9;
            font-size: 14px;
            font-weight: 600;
            background: #e8f4ff;
            border-color: #e8f4ff;
            &.is-plain:hover {
              background: #fff;
              border-color: #fff;
              color: #2f74e1;
            }
            & + .el-button {
              margin-left: 12px;
            }
          }
        }
      }
    }
  }
  .contain-main {
    width: 1100px;
    min-width: 1100px;
    margin: -42px auto 0;
    position: relative;
    z-index: 2;
    .coupon-bar {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .coupon-card {
        width: 540px;
        padding: 20px 28px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 0 8px 0 #eeeeee99;
        .card {
          width: 484px;
          height: 127px;
          padding: 16px 24px;
          .title {
            font-size: 18px;
            font-weight: 700;
            line-height: 22px;
            margin-bottom: 2px;
          }
          .subtitle {
            font-size: 12px;
            line-height: 20px;
            & > span {
              border-bottom: 1px dotted;
              padding-bottom: 6px;
            }
          }
          .lower-part {
            padding-top: 10px;
            position: relative;
            p {
              font-size: 12px;
              line-height: 16px;
              & + p {
                margin-top: 4px;
              }
            }
            .el-button {
              width: 100px;
              height: 28px;
              border-radius: 64px;
              font-size: 12px;
              font-weight: 500;
              padding: 0;
              color: #ffffff;
              position: absolute;
              right: 0;
              bottom: 0;
            }
          }
        }
        .coupon-init {
          background: url('~@/assets/outsourcedCourse/card-init.png') no-repeat
            center / cover;
          .title {
            color: #0c3102;
          }
          .subtitle {
            color: #3e653d;
          }
          .lower-part {
            p {
              color: #6e794f;
              & + p {
                color: #31571999;
              }
            }
          }
          .receive-init {
            background: #2a648e;
            border-color: #2a648e;
            &.is-plain:hover {
              background: #2a648e;
              opacity: 0.8;
            }
            &.disabled {
              background: #c2dbec;
              border-color: #c2dbec;
              &:hover {
                background: #c2dbec;
                opacity: 1;
              }
            }
          }
        }
        .coupon-qualify {
          background: url('~@/assets/outsourcedCourse/card-qualify.png')
            no-repeat center / cover;
          .title {
            background-image: -webkit-linear-gradient(top, #805524, #573a18);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
          }
          .subtitle {
            color: #6e491f;
          }
          .lower-part {
            p {
              color: #6e491fe6;
              & + p {
                color: #6e491f99;
              }
            }
          }
          .receive-qualify {
            background: linear-gradient(276deg, #8b5300 14.56%, #ae864a 95.9%);
            border: 0;
            &.is-plain:hover {
              background: linear-gradient(
                276deg,
                #8b5300 14.56%,
                #ae864a 95.9%
              );
              opacity: 0.8;
            }
            &.disabled {
              background: linear-gradient(
                276deg,
                #eac896 14.56%,
                #eedcb9 95.9%
              );
              &:hover {
                background: linear-gradient(
                  276deg,
                  #eac896 14.56%,
                  #eedcb9 95.9%
                );
                opacity: 1;
              }
            }
          }
        }
        .introduce {
          padding-top: 12px;
          color: #5c3d00;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
        }
      }
    }
    .coupon-bar-new {
      display: flex;
      flex-direction: column;
      padding: 20px 28px;
      // height: 287px;
      border-radius: 8px;
      background-color: #fff;
      filter: drop-shadow(0 0 8px #eeeeee99);
      .title {
        color: #000000e6;
        font-size: 18px;
        font-weight: 600;
        line-height: 32px;
        display: flex;
        justify-content: space-between;
        .title-date {
          color: #00000066;
          font-family: 'PingFang SC';
          font-size: 12px;
          font-style: normal;
          font-weight: 500;
          line-height: 20px;
          margin-left: 24px;
        }
        .title-date_b {
          color: #0052d9;
        }
        .subscription {
          display: flex;
          align-items: center;
          .subscription-btn {
            display: flex;
            height: 32px;
            padding: 0 16px;
            justify-content: center;
            align-items: center;
            gap: 4px;
            border-radius: 6px;
            background: #0052d9;
            color: #fff;
            font-size: 14px;
            cursor: pointer;
            img {
              width: 16px;
              height: 16px;
            }
          }
          &-tips {
            color: #00000066;
            font-family: 'PingFang SC';
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            padding-right: 20px;
          }
        }
      }

      .card-list-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 18px;
        .official {
          width: 254px;
          height: 259px;
          margin-right: 4px;
          border-radius: 9px;
          background: #f8f8f8;
          padding: 16px;
          margin-right: 16px;
        }
        .official-top {
          display: flex;
          height: 28px;
          line-height: 28px;
          margin-bottom: 12px;
          position: relative;
          .official-top-left {
            width: 28px;
            height: 28px;
            border-radius: 50%;
            background-color: #fff;
          }
          .official-top-text {
            color: #000000e6;
            font-family: 'PingFang SC';
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 28px;
            padding-left: 8px;
            /deep/.el-icon-warning-outline {
              color: #00000099;
              transform: rotate(180deg);
            }
          }
          .official-top-flex {
            display: flex;
            justify-content: space-between;
            align-items: center;
          }
          .official-top-right {
            color: #00000099;
            font-family: 'PingFang SC';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            position: absolute;
            right: 5px;
            span {
              color: #0052d9;
              cursor: pointer;
            }
          }
        }

        .official-right {
          background: #f9f9f9;
          display: flex;
          position: relative;
          z-index: 3;
          .stack1 {
            width: 194px;
            height: 127px;
            background-color: #d5d5d5;
            position: absolute;
            z-index: 2;
            border-radius: 8px;
          }
          // .stack2 {
          //   width: 195px;
          //   height: 125px;
          //   background-color: #e6e6e6;
          //   position: absolute;
          //   z-index: 1;
          //   bottom: -12px;
          //   left: 18px;
          //   border-radius: 8px;
          // }
        }
        .official-tips {
          color: #333333;
          font-family: 'PingFang SC';
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          padding-top: 16px;
        }

        .share-polite {
          flex: 1;
          height: 259px;
          border-radius: 9px;
          background: #f8f8f8;
          padding: 16px;
          .share-po-display {
            display: flex;
            justify-content: space-between;
            .give-pc {
              width: 20px;
              height: 20px;
            }
          }
          .give-right-btn {
            display: flex;
            width: 122px;
            height: 127px;
            border-radius: 8px;
            border: 1px solid #ffc37d;
            background: #fff4ee;
            margin-right: 12px;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            .give-btn {
              width: 90px;
              height: 40px;
              line-height: 40px;
              text-align: center;
              border-radius: 8px;
              background: #ffddc4;
              color: #8d3b00;
              margin-top: 12px;
              font-weight: 600;
              font-size: 14px;
            }
          }
          .give-text {
            width: 30px;
            height: 100%;
            color: #d97500;
            writing-mode: vertical-rl;
            text-align: center;
            line-height: 30px;
            border-radius: 6px 2px 2px 6px;
            background: #f9f0d7;
          }
          .give-right {
            height: 100%;
            width: 100%;
            display: flex;
            border-radius: 0 6px 6px 0;
            flex: 1;
          }
          .no-data {
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 127px;
            img {
              width: 92px;
              height: 92px;
            }
            .data-text {
              color: #00000066;
              font-family: 'PingFang SC';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
              padding-top: 10px;
            }
          }
          .share-po-tips {
            color: #333333;
            font-family: 'PingFang SC';
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            margin-top: 16px;
          }
        }
        .card-init {
          position: relative;
          z-index: 55;
          width: 222px !important;
          height: 127px;
          padding: 12px 16px;
          background: url('~@/assets/outsourcedCourse/card-init-new.png')
            no-repeat 0 0;
          background-size: 100% 100%;
          // &::after {
          //   // 待领取
          //   position: absolute;
          //   content: '';
          //   right: 0;
          //   top: 0;
          //   width: 40px;
          //   height: 40px;
          //   background: url('~@/assets/outsourcedCourse/wait-get.png') no-repeat
          //     0 0;
          //   background-size: 100% 100%;
          //   z-index: 1;
          // }
          .init-disable-class1 {
            color: #00000099;
          }
          .init-disable-class2 {
            color: #00000066;
          }
        }
        .card-stand {
          position: relative;
          width: 194px;
          height: 127px;
          margin-right: 12px;
          padding: 12px 16px 12px 20px;
          background: url('~@/assets/outsourcedCourse/card-stand-new.png')
            no-repeat 0 0;
          background-size: 100% 100%;
          &:last-child {
            margin-right: 0;
          }
           &::after { // 待领取
            position: absolute;
            content: '';
            right: 0;
            top: 0;
            width: 40px;
            height: 40px;
            background: url('~@/assets/outsourcedCourse/wait-get.png') no-repeat 0 0;
            background-size: 100%;
            z-index: 1;
          }
          .stand-disable-class1 {
            color: #00000099;
          }
          .stand-disable-class2 {
            color: #00000066;
          }
        }
        .card-stand-1::after {
          // 待兑换
          background-image: url('~@/assets/outsourcedCourse/card-stand-1.png');
        }
        .card-stand-2::after {
          // 已兑换
          background-image: url('~@/assets/outsourcedCourse/card-stand-2.png');
        }
        .card-stand-3::after {
          // 未解锁
          background-image: url('~@/assets/outsourcedCourse/card-stand-3.png');
        }
        .card-stand-4::after {
          // 已失效
          background-image: url('~@/assets/outsourcedCourse/card-stand-4.png');
        }
        .card-stand-5::after {
          background-image: unset;
        }
        .sub-title {
          color: #805524;
          font-size: 14px;
          font-weight: 600;
          line-height: 22px;
          display: flex;
          align-items: center;
          font-family: 'Source Han Sans CN';

          .geek-font {
            display: inline-block;
            padding: 0 5px;
            height: 16px;
            border: 1px solid #ab8143;
            color: #ab8143;
            line-height: 15px;
            border-radius: 8px;
            font-size: 10px;
            text-align: center;
            margin-left: 8px;
          }
        }
        .title-1 {
          margin-top: 2px;
          padding-bottom: 5px;
          margin-bottom: 5px;
          color: #6e491f;
          font-size: 12px;
          line-height: 20px;
        }
        .title-warm {
          padding-top: 5px;
          color: #ff6600;
          font-size: 12px;
          line-height: 16px;
          border-top: 1px dotted #486646;
        }
        .right-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .title-name {
          color: #333333;
          font-family: 'PingFang SC';
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
          padding-top: 6px;
        }
        .botton-box {
          margin-top: 11px;
          display: flex;
          justify-content: flex-end;
        }
        .el-button {
          width: 72px;
          height: 22px;
          border-radius: 64px;
          font-size: 12px;
          font-weight: 500;
          padding: 0;
          color: #ffffff;
        }
        .button-customer {
          width: 72px;
          height: 22px;
          border-radius: 64px;
          text-align: center;
          line-height: 22px;
          font-size: 12px;
          font-weight: 500;
          color: #fff;
          cursor: pointer;
          background: linear-gradient(276deg, #8b5300 14.56%, #ae864a 95.9%);
          border-color: unset;
          &.is-plain:hover {
            // opacity: 0.8;
          }
          &.disabled {
            // opacity: 0.4;
          }
          &.disabled-gray {
            background: linear-gradient(276deg, #d4d4d4 14.56%, #d8d8d8 95.9%);
            border-color: unset;
          }
        }
      }
      .card-list-content-study {
        position: relative;
        width: 100%;
        height: 127px;
        margin-top: 0;
        z-index: 100;
        .card-stand-position-1 {
          position: absolute;
          z-index: 400;
          box-shadow: 4px 2px 5px -5px rgba(0, 0, 0, 0.4);
        }
        .card-stand-position-2 {
          position: absolute;
          z-index: 300;
          left: 10px;
          box-shadow: 4px 2px 5px -5px rgba(0, 0, 0, 0.4);
        }
        .card-stand-position-3 {
          position: absolute;
          z-index: 200;
          left: 20px;
          box-shadow: 4px 2px 5px -5px rgba(0, 0, 0, 0.4);
        }
        .card-stand-position-4 {
          position: absolute;
          z-index: 100;
          left: 30px;
          box-shadow: 4px 2px 5px -5px rgba(0, 0, 0, 0.4);
        }
        .card-more1 {
          width: 320px;
          height: 127px;
          background: #c8e8ff;
          border-radius: 8px;
          position: absolute;
          right: 35px;
          z-index: 30;
        }
        .card-more2 {
          width: 320px;
          height: 127px;
          background: #d8eeff;
          border-radius: 8px;
          position: absolute;
          right: 17px;
          z-index: 20;
        }
        .card-more3 {
          width: 320px;
          height: 127px;
          background: #e7f4ff;
          border-radius: 8px;
          position: absolute;
          right: 0px;
          z-index: 10;
        }
      }
      .text-box {
        color: #00000099;
        font-size: 14px;
        line-height: 22px;
        .text-title {
          color: #00000099;
          font-size: 14px;
          font-weight: 500;
          line-height: 22px;
        }
        .warm {
          color: #ed7b2f;
          font-size: 14px;
          line-height: 22px;
        }
        .text-attention {
          color: #ed7b2f;
          font-weight: 500;
        }
      }
    }

    .learning-reward {
      height: auto;
      margin-top: 20px;
      position: relative;
      padding-top: 36px;
      .pack-up-btn {
        position: absolute;
        right: 28px;
        top: 20px;
        width: 18px;
        height: 18px;
        background-color: #639effff;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        /deep/.el-icon-arrow-up {
          color: #fff;
          font-size: 12px;
        }
        .packup-arrow {
          transform: rotateX(180deg);
        }
      }
      .pack-up-false {
        // position: absolute;
        // left: 28px;
        // top: 15px;
        display: flex;
        .official-top__left {
          width: 254px;
          background-color: #f8f8f8;
          margin-bottom: 0;
          height: 50px;
          align-items: center;
          padding: 0 16px;
          border-radius: 9px;
          margin-right: 14px;
        }
        .official-top__right {
          width: 746px;
          background-color: #f8f8f8;
          margin-bottom: 0;
          height: 50px;
          align-items: center;
          padding: 0 16px;
          border-radius: 9px;
        }
      }
      &_left {
        flex: 1;
        .give-away {
          width: 774px;
          height: 260px;
          border-radius: 9px;
          background: #f8f8f8;
          padding: 16px;
          .give-text {
            width: 30px;
            height: 100%;
            color: #d97500;
            writing-mode: vertical-rl;
            text-align: center;
            line-height: 30px;
            border-radius: 6px 2px 2px 6px;
            background: #f9f0d7;
          }
          .give-right {
            display: flex;
            border-radius: 0 6px 6px 0;
            .card-stand {
              width: 238px;
            }
          }
          .no-data {
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 127px;
            img {
              width: 92px;
              height: 92px;
            }
            .data-text {
              color: #00000066;
              font-family: 'PingFang SC';
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: 22px;
              padding-top: 10px;
            }
          }
        }
        .give-tips {
          color: #333333;
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          margin-top: 16px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .learning-reward_right {
          width: 254px;
          height: 260px;
          border-radius: 9px;
          background: #f8f8f8;
          padding: 16px;
          .reward-title {
            color: #000000e6;
            font-family: 'PingFang SC';
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px;
            padding-bottom: 16px;
          }
        }
      }

      .invite-main_title {
        color: #000000e6;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
      }
      .give-explain-content {
        color: #00000066;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
        padding: 6px 12px;
        background: #f9f9f9;
        margin: 10px 0 20px 0;
      }
    }
    .learning-reward-packup-arrow {
      padding: 16px 28px;
    }
    .general-banner {
      width: 100%;
      height: 128px;
      margin: 20px 0 10px;
      border-radius: 8px;
      cursor: pointer;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .invite-main {
      padding: 20px 28px;
      background-color: #fff;
      margin-top: 20px;
      border-radius: 8px;
      &_title {
        color: #000000e6;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
      }
      .give-explain-content {
        color: #00000066;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
        padding: 6px 12px;
        background: #f9f9f9;
        margin: 10px 0 20px 0;
      }
    }
    .course-list {
      padding: 21px 28px;
      background: #fff;
      margin-top: 20px;
      border-radius: 8px;
      box-shadow: 0 0 8px 0 #eeeeee99;
      .title-col {
        display: flex;
        // justify-content: space-between;
        align-items: center;
        .title {
          color: #000000e6;
          font-size: 18px;
          font-weight: 600;
          line-height: 22px;
          margin-right: 20px;
        }
        .link {
          color: #ff6600;
          font-size: 16px;
          font-weight: 500;
          line-height: 22px;
          text-decoration-line: underline;
          text-underline-offset: 2px;
          cursor: pointer;
        }
      }
      .list {
        margin: 20px -28px 0 0;
        display: flex;
        flex-wrap: wrap;
        .course-item {
          width: 240px;
          margin: 0 28px 24px 0;
          cursor: pointer;
          .cover {
            width: 100%;
            height: 160px;
            margin-bottom: 12px;
            border-radius: 6px;
          }
          .title {
            color: #333;
            font-weight: 500;
            font-size: 14px;
            line-height: 22px;
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            & > span {
              height: 18px;
              line-height: 18px;
              padding: 1px 4px;
              margin-right: 6px;
              justify-content: center;
              align-items: center;
              gap: 10px;
              border-radius: 2px;
              background: #f5f5f7;
              font-size: 12px;
              font-weight: 500;
              color: #777777;
            }
          }
        }
      }
      & > .link {
        text-align: center;
        span {
          cursor: pointer;
          color: #ff6600;
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
          text-decoration-line: underline;
        }
      }
    }
  }
  .usage-details {
    .head {
      padding: 16px 32px;
      color: #000000e6;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      border-bottom: 1px solid #e7e7e7;
      position: relative;
      .close {
        width: 16px;
        height: 16px;
        position: absolute;
        top: 20px;
        right: 34px;
        cursor: pointer;
      }
    }
    .body {
      padding: 24px 32px;
      .pagination {
        padding-top: 16px;
        display: flex;
        justify-content: flex-end;
        .box {
          display: flex;
          padding: 6px 16px 6px 8px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          border-radius: 6px;
          border: 0.5px solid #e7e7e7;
          background: #fff;
          color: #00000099;
          font-size: 12px;
          line-height: 20px;
          user-select: none;
          & + .box {
            margin-left: 16px;
          }
        }
        .btn {
          cursor: pointer;
          &:hover {
            color: #0052d9;
            background: #f3f7ff;
            .icon-grey {
              display: none;
            }
            .icon-active {
              display: block;
            }
          }
          & > img {
            margin: 0 !important;
          }
        }
        .prev {
          .icon-grey {
            transform: rotate(90deg);
          }
          .icon-active {
            transform: rotate(180deg);
            display: none;
          }
        }
        .next {
          padding: 6px 8px 6px 16px;
          .icon-grey {
            transform: rotate(-90deg);
          }
          .icon-active {
            display: none;
          }
        }
        .num {
          padding: 6px 16px;
          .current {
            color: #0052d9;
          }
          .total {
            color: #000000e6;
          }
        }
        .disable {
          background: #f5f5f5;
          cursor: no-drop;
          &:hover {
            background: #f5f5f5;
            color: #00000099;
            .icon-grey {
              display: block;
            }
            .icon-active {
              display: none;
            }
          }
        }
      }
    }
  }
  .status-label {
    font-size: 12px;
    line-height: 20px;
    padding: 1px 8px;
    border-radius: 4px;
  }
  .status-no-effict {
    color: #ed7b2f;
    border: 1px solid var(---Warning5-Normal, #ed7b2f);
    background: var(---Warning1-Light, #fef3e6);
  }
  .status-waite-use {
    color: #0052d9;
    border: 1px solid var(---Brand8-Normal, #0052d9);
    background: var(---Brand1-Light, #ecf2fe);
  }
  .status-oready-used {
    color: #00a870;
    border: 1px solid var(---Success5-Normal, #00a870);
    background: var(---Success1-Light, #e8f8f2);
  }
}
.error-cover {
  width: 100%;
  height: 100%;
}
.image-box {
  width: 100%;
  height: 100%;
}
.img-16 {
  width: 16px;
  height: 16px;
}
.important {
  color: #ed7b2f !important;
  font-weight: normal;
}
</style>
