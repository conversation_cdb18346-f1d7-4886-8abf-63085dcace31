<template>
  <div class="tutor-container">
    <link rel="stylesheet" :href="linkHref">
    <router-view></router-view>
  </div>
</template>
  
<script>
export default {
  data() {
    return {
      linkHref: ''
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        let root = document.documentElement
        if (val.staff_name && !document.getElementById('tutor-common-head')) {
          root.style.setProperty('--app-height', 'calc(100% - 62px)')
          this.headerIsLoad = true
          this.linkHref = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeaderStyle.css` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeaderStyle.css`  
          const commonHeaderJsUrl = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeader.js` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeader.js`
          this.loadHeadJS(commonHeaderJsUrl, function() {
            if (window.$qlCommonHeader) {
              window.$qlCommonHeader.create({
                staff_name: val.staff_name,
                dev: !(process.env.NODE_ENV === 'production')
              })

              setTimeout(() => {
                let el = document.getElementsByClassName('common-header-occupy')
                if (el.length > 0) {
                  console.log('El', el[0].offsetHeight)
                  document.getElementById('app').style.height = `calc(100% - ${el[0].offsetHeight}px)`
                }
              }, 500)
            }
          })
        } else {
          root.style.setProperty('--app-height', '100%')
        }
      },
      immediate: true
    }
  },
  methods: {
    loadHeadJS (url, callback) {
      var script = document.createElement('script')
      var fn = callback || function() {}
      script.type = 'text/javascript'
      script.id = 'tutor-common-head'
      // IE
      if (script.readyState) {
        script.onreadystatechange = function() {
          if (script.readyState === 'loaded' || script.readyState === 'complete') {
            script.onreadystatechange = null
            fn()
          }
        }
      } else {
        // 其他浏览器
        script.onload = function() {
          fn()
        }
      }
      script.src = url
      document.getElementsByTagName('head')[0].appendChild(script)
    }
  }
}
</script>
<style lang="less">
#tutorApp {
  height: var(--app-height);
}
</style>
<style lang="less" scoped>
.tutor-container {
  height: 100%;
}
</style>
