<template>
  <div class="task-list" :class="{ 'show': showTaskPanel }">
    <div class="course-process-header">
      <span>
        <span class="process-text">{{ $langue('Mooc_ProjectDetail_TrainingProgress_Progress', { defaultText: '培训进度' }) }}</span>
        <!-- <span class="process-num">{{ `共${courseProcess.task_sum}个任务` }}</span> -->
        <span class="process-num">{{ $langue('Mooc_Home_ListItems_TotalTasks', {count: courseProcess.task_sum, defaultText: `共${courseProcess.task_sum}项任务` }) }}</span>
      </span>
      <span @click="handleHide" class="process-hide">{{ $langue('Mooc_ProjectDetail_Comments_Hide', { defaultText: '隐藏' }) }}</span>
    </div>
    <div class="course-process">
      <div class="course-process-wrapper">
        <div class="course-process-content">
          <div class="task-compulsory course-process-item">
            <span class="num">{{ courseProcess.requiredProcess }}</span>
            <span class="text">{{ $langue('Mooc_ProjectDetail_TrainingProgress_RequiredProgress', { defaultText: '应学进度' }) }}</span>
          </div>
          <div class="line"></div>
          <div class="task-elective course-process-item">
            <span class="num">{{ courseProcess.noRequireProcess }}</span>
            <span class="text">{{ $langue('Mooc_ProjectDetail_TrainingProgress_NonRequiredProgress', { defaultText: '选学进度' }) }}</span>
          </div>
        </div>
        <span class="process-icon" :class="courseProcessIcon"></span>
      </div>
      <div class="course-process-btn">
        <span>
          <el-checkbox v-model="required" @change="changeCheckbox">{{ $langue('Mooc_ProjectDetail_TaskList_OnlyRequire', { defaultText: '只看应学任务' }) }}</el-checkbox>
          <el-checkbox v-model="unFinished" @change="changeCheckbox">{{ $langue('Mooc_ProjectDetail_TaskList_OnlyNotFinished', { defaultText: '只看未完成任务' }) }}</el-checkbox>
        </span>
        <span class="put-away" @click="expandHandle" v-if="showExpandBtn">
          <span>{{ isExpandAll ? $langue('Mooc_ProjectDetail_TaskList_RetractAll', { defaultText: '全部收起' }) : $langue('Mooc_ProjectDetail_TaskList_ExpandAll', { defaultText: '全部展开' }) }}</span>
          <span class="after-icon" :class="[isExpandAll ? 'icon-down' : 'icon-up']"></span>
        </span>
      </div>
      <div class="task-list-content">
        <el-tree ref="taskTree" :data="taskTreeData" node-key="task_id" :indent="24" :props="{
          'label': 'task_name',
          'children': 'sub_tasks'
        }" :default-expand-all="isExpandAll" icon-class=" " :filter-node-method="filterNode"
          >
          <div class="custom-tree-node" slot-scope="{ node, data }">
            <span :class="[node.expanded ? 'tree-icon-down' : 'tree-icon-right', node.childNodes.length === 0 && 'is-leaf', data.iconName]"></span>
            <!-- 阶段 -->
            <div class="task-stage task-node" v-if="data.task_type === 'stage'">
              <span>{{ node.label }}</span>
            </div>
            <!-- 任务组 -->
            <div class="task-group task-node" :class="{ 'has-children': node.childNodes.length }"
              v-if="data.task_type === 'group'">
              <span class="group-icon"></span>
              <span class="group-text">{{ node.label }}</span>
            </div>
            <!-- 任务 -->
            <div class="task-item task-node" :id="'taskID_' + data.task_id" :class="{ 'task-selected': task_id * 1 === data.task_id }"
              v-if="data.task_type === 'task'" @click="clickTask(data)"
              :dt-eid="dtTask('eid', {node, data})" :dt-areaid="dtTask('area', {node, data})" :dt-remark="dtTask('remark', {node, data})">
              <span class="task-item-left">
                <el-tooltip effect="dark" :content="$langue(resourceTypeIcon(data.resource_type).langName, { defaultText: resourceTypeIcon(data.resource_type).defaultText})" placement="top-start">
                  <span class="task-icon" :class="resourceTypeIcon(data.resource_type).className"></span>
                </el-tooltip>
                <span class="task-item-info">
                  <span class="task-item-title" :title="node.label">{{ node.label }}</span>
                  <span class="task-item-label">
                    <span class="label" :class="[data.required ? 'require' : 'norequire']">{{ data.required ? $langue('Mooc_ProjectDetail_TaskList_RequiredTask', { defaultText: '应学' }) : $langue('Mooc_ProjectDetail_TaskList_ElectiveTask', { defaultText: '选学' })
                    }}</span>
                    <span v-if="data.task_status === 2">{{ $langue('Mooc_ProjectDetail_TaskList_TaskDisable', { defaultText: '任务已失效，无法学习' }) }}</span>
                    <!-- 任务类型为第三方、作业时，不显示 -->
                    <span v-else-if="!['22', '23'].includes(data.act_type)">{{ data.word_num ? `${data.word_num}${$langue('Mooc_ProjectDetail_TaskList_Words', { defaultText: '字' })}` : data.duration ? `${data.duration}${$langue('Mooc_ProjectDetail_TaskList_Minute', { defaultText: '分钟' })}` : '-'}}</span>
                  </span>
                </span>
              </span>
              <span style="width: 20px" v-if="!isPreview">
                <el-tooltip effect="dark" :content="taskProcessIcon(data, true).tooltips" placement="top-start">
                  <span class="after-icon" :class="taskProcessIcon(data, true).className" style="margin-top: 4px;"></span>
                </el-tooltip>
              </span>
            </div>
          </div>
        </el-tree>
      </div>
    </div>
  </div>
</template>
<script>

import trainTaskList from 'mixins/trainTaskList'
import { mapState } from 'vuex'
import {
  cardDetail,
  personDetail
} from '@/config/mooc.api.conf.js'
export default {
  name: 'taskList',
  components: {},
  props: {
    showTaskPanel: Boolean,
    courseProcess: Object,
    // 极客课程信息
    geekInfo: {
      type: Object,
      default: () => {
        return {
          isGeek: false, // 是否极客
          canPreview: false, // 是否支持预览
          courseAcquisitionType: 1, // 1,统一购买-用户不需要够买 2，体系课-需要购买才试学
          purchased: false, // 是否购买
          total: 0, // 试学总章节数
          study: 0, // 已试学章节数
          previewRecords: [], // 预览章节列表
          courseFrom: 'geekBang'
        }
      }
    }
  },
  mixins: [trainTaskList],
  data() {
    return {
      simpleRes: {}
    }
  },
  computed: {
    ...mapState(['moocLang']),
    courseProcessIcon() {
      let className = ''
      switch (this.courseProcess.learn_status) {
        case 1:
          className = this.moocLang === 'en-us' ? 'en-icon-wkx' : 'icon-wks'
          break
        case 2:
          className = this.moocLang === 'en-us' ? 'en-icon-jxz' : 'icon-jxz'
          break
        case 3:
          className = this.moocLang === 'en-us' ? 'en-icon-ywc' : 'icon-ywc'
          break

        default:
          break
      }
      return className
    },
    unlocked_by_step() {
      return this.courseProcess.unlocked_by_step
    },
    isPreview() {
      return this.$route.query.previewType === 'preview'
    }
  },
  // watch: {
  //   courseProcess(val) {
  //     this.unlocked_by_step = val.unlocked_by_step
  //   }
  // },
  async mounted() {
    // 拦截项目报错问题
    const { previewType } = this.$route.query
    const val = previewType === 'preview' ? 1 : 0
    const simpleRes = await cardDetail({ mooc_course_id: this.mooc_course_id, preview: val })
    this.simpleRes = simpleRes
    const perRes = await personDetail({ mooc_course_id: this.mooc_course_id })
    this.$emit('getPerRes', perRes)
    let { have_auth_to_view, have_released, course_status } = simpleRes
    let { start_time, end_time, course_period_type, course_end_time, delayed } = perRes
    // 如果已经逾期，更新了最后学习时间那么就先用更新的 course_end_time 学员的时间，end_time项目时间
    let commEndTime = delayed ? course_end_time : end_time
    const isPassTime = course_period_type === 1 && commEndTime && (new Date().getTime() > new Date(commEndTime).getTime())
    if (course_status === 1 && isPassTime) { // 已逾期的项目就是已结束
      this.$emit('isPageError', simpleRes, perRes, '已结束')
      return
    }
    const isStartTime = course_period_type === 1 && start_time && (new Date(start_time).getTime()) > new Date().getTime()
    if (course_status === 1 && isStartTime) {
      this.$emit('isPageError', simpleRes, perRes, '未开始')
      return
    }
    if (!have_released || !have_auth_to_view) {
      this.$emit('isPageError', simpleRes, perRes)
      return
    }
    this.getTaskData().then(() => {
      // 在任务详情列表获取到列表数据之后调用筛选过滤方法
      this.setFilterRef()
      // 将当前任务滚动到任务列表可视区域
      let currentTaskID = this.taskAllData.find(item => item.task_id === this.task_id * 1)?.task_id
      let currentTaskDOM = document.getElementById(`taskID_${currentTaskID}`)
      currentTaskDOM.scrollIntoView({ block: 'center' })
    })
  },
  methods: {
    dtTask(type, { node, data }) {
      if (type === 'eid') {
        return `element_${data.mooc_course_id || ''}_${data.act_id || ''}`
      }
      if (type === 'area') {
        return `area_${data.mooc_course_id || ''}_${data.act_id || ''}`
      }
      if (type === 'remark') {
        return JSON.stringify({
          page: data.task_name || '',
          page_type: '任务详情页',
          container: '',
          content_type: data.resource_type_name || '',
          click_type: 'data',
          content_id: data.act_id || '',
          container_id: '',
          content_name: data.act_name || '',
          act_type: data.act_type || '',
          terminal: 'PC'
        })
      }
    },
    clickTask(data) {
      const { isGeek, courseAcquisitionType, canPreview, purchased, previewRecords, total, study, courseFrom } = this.geekInfo
      // 极客 && 需要购买 && 支持试学 && 未购买课程
      if (isGeek && courseAcquisitionType === 2 && canPreview && !purchased) {
        // 已试学完可试学章数
        if (study >= total) {
          let index = previewRecords.findIndex(e => e.outsourced_course_id === data.act_id)
          if (index === -1) {
            this.$emit('handleStudyTips', 3, this.simpleRes.consume_appid)
            return
          }
        }
        this.handleTask({ ...data, geekPreview: true }, courseFrom)
      } else {
        this.handleTask(data)
      }
    },
    handleHide() {
      this.$store.commit('setTaskPanel', false)
      this.$emit('changeSourceTaskListShow', false)
    }
  }
}
</script>
<style lang="less" scoped>
.task-list {
  position: absolute;
  box-sizing: border-box;
  background: white;
  width: 0;
  height: 100%;
  right: 0;
  top: 0;
  box-shadow: 0 0 16px 0 #0000001f;
  border-radius: 4px;
  transition: width 0.3s;
  overflow: hidden;
  z-index: 100;

  &.show {
    width: 400px;
  }
}

.after-icon {
  width: 16px;
  height: 16px;
  display: inline-block;
}

.course-process-header {
  height: 54px;
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eeeeeeff;

  .process-text {
    font-weight: 600;
    color: #000000e6;
  }

  .process-num {
    color: #00000066;
    font-size: 12px;
    margin-left: 8px;
  }

  .process-hide {
    color: #0052d9ff;
    font-size: 12px;
    cursor: pointer;
  }
}

.course-process {
  height: calc(100% - 54px);
  overflow-y: auto;

  .course-process-wrapper {
    position: relative;
    padding: 12px 14px;
    border-bottom: 1px solid #eeeeeeff;

    .course-process-content {
      background: #fafafaff;
      border-radius: 3px;
      display: flex;
      align-items: center;
      height: 70px;

      .line {
        width: 1px;
        height: 24px;
        background: #eeeeeeff;
      }

      .course-process-item {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;

        .num {
          color: #000000e6;
          font-size: 16px;
          font-weight: 600;
        }

        .text {
          color: #00000099;
          font-size: 12px;
          margin-top: 8px;
        }
      }
    }

    .process-icon {
      position: absolute;
      right: 2px;
      top: 2px;
      width: 52px;
      height: 52px;
      display: inline-block;
    }

    .icon-jxz {
      background: url("~@/assets/mooc-img/status-jxz.png") no-repeat center / cover;
    }
    .en-icon-jxz { // 英文
      background: url("~@/assets/mooc-img/en-status-jxz.png") no-repeat center / cover;
    }
    .icon-wks {
      background: url("~@/assets/mooc-img/status-wks.png") no-repeat center / cover;
    }
    .en-icon-wkx { // 英文
      background: url("~@/assets/mooc-img/en-status-wks.png") no-repeat center / cover;
    }
    .icon-ywc {
      background: url("~@/assets/mooc-img/status-ywc.png") no-repeat center / cover;
    }
    .en-icon-ywc { // 英文
      background: url("~@/assets/mooc-img/en-status-ywc.png") no-repeat center / cover;
    }
  }

  .course-process-btn {
    // height: 38px;
    line-height: 38px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #eeeeeeff;

    :deep(.el-checkbox) {
      margin-right: 16px;

      .el-checkbox__label {
        color: #000000e6;
        font-weight: 400;
      }
    }

    .put-away {
      cursor: pointer;
      font-size: 12px;
      color: #00000099;
      display: flex;
      align-items: center;
    }

    .icon-down {
      background: url("~@/assets/mooc-img/down.png") no-repeat center / cover;
    }

    .icon-up {
      background: url("~@/assets/mooc-img/up.png") no-repeat center / cover;
    }
  }

}

:deep(.task-list-content) {
  font-size: 12px;
  margin-bottom: 20px;
  .el-tree-node {
    white-space: wrap;
  }
  .el-tree-node__expand-icon{
    padding: 0;
  }
  .tree-icon {
    width: 18px;
    height: 18px;
    padding: 0 4px 0 0;
    margin: 0 4px;
    box-sizing: content-box;
    background: url("~@/assets/mooc-img/right.png") no-repeat center / cover;
    cursor: pointer;
    flex-shrink: 0;
  }
  .tree-icon-down {
    transform: rotate(90deg);
    transition: transform .3s;
  }
  .tree-icon-right {
    transform: rotate(0deg);
    transition: transform .3s;
  }

  // 隐藏字节点图标
  .tree-icon.is-leaf {
    opacity: 0;
    cursor: default;
  }

  .custom-tree-node {
    width: 100%;
    overflow: hidden;
    display: flex;
    align-items: center;
  }

  .el-tree-node__content {
    height: auto;
    cursor: default;
    background: transparent;

    &:hover {
      background: transparent;
    }
  }

  .el-tree>.el-tree-node>.el-tree-node__content {
    border-bottom: 1px solid #eeeeeeff;

  }

  .is-focusable>.el-tree-node__content {
    background: #F9FBFC;

  }

  .el-tree-node__children{
    .task-icon{
      margin: 4px 6px!important;
    }
  }
  .task-node {
    display: flex;

    .group-icon {
      width: 14px;
      height: 14px;
      margin: 0 6px;
      background: url("~@/assets/mooc-img/group.png") no-repeat center / cover;
      flex-shrink: 0;
    }
    .group-text{
      display: inline-block;
    }
  }

  .task-stage {
    padding: 8px 0;
    line-height: 18px;
    color: #000000e6;
    font-weight: 600;
    cursor: pointer;

  }

  .task-group {
    display: flex;
    align-items: center;
    padding: 8px 0;
    line-height: 18px;
  }

  .task-group.has-children {
    cursor: pointer;
  }

  .task-item {
    width: 100%;
    justify-content: space-between;
    padding: 4px 14px 4px 0;
    cursor: pointer;

    .task-icon {
      width: 16px;
      height: 16px;
      display: inline-block;
      margin: 4px 12px;
    }
    .task-item-left {
      flex: 1;
      overflow: hidden;
      display: flex;
      margin-right: 20px;

      .task-item-info {
        display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;
      }

      .task-item-title {
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;

      }

      .task-item-title,
      .task-item-label {
        display: inline-block;
        height: 24px;
        line-height: 24px;

        .label {
          padding: 4px;
          border-radius: 2px;
          margin-right: 10px;
        }

        .require {
          background: #fdf6ecff;
          color: #FF7548;
        }

        .norequire {
          background: #CCF2E2;
          color: #00B368;
        }
      }
    }
  }

  .task-selected .task-item-title {
    color: #0052d9;
  }
}
</style>
