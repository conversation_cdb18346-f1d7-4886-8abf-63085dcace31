<template>
  <div class="desc-box">
    <div class="desc-top">
      <div class="desc-tag-box" v-if="courseType === 'net'">
        <div class="tag-left">
          <div class="tag-content">
            <span class="label label-height25">{{ $langue('Article_Lable', { defaultText: '标签' }) }}：</span>
            <div class="tag-list-box">
              <!-- 标签显示 -->
              <template v-if="courseData.net_course_id">
                <sdc-label-show v-if="isPreview"
                ref="labelShow" 
                :labelNodeEnv="labelNodeEnv" 
                :actType="2" 
                :courseId="course_id"
                :showBurialPoint="true" 
                :courseInfo="courseInfo('')"
                :isPreview="true"
                :previewLbael="previewLbael"
                >
                </sdc-label-show>
                <sdc-label-show v-else
                ref="labelShow" 
                :labelNodeEnv="labelNodeEnv" 
                :actType="2" 
                :courseId="course_id"
                :showBurialPoint="true" 
                :courseInfo="courseInfo('')"
                >
                </sdc-label-show>
              </template>
            </div>
          </div>
        </div>
      </div>
      <active-jump class="mgb-16" :dtArg="dtArg"></active-jump>
      <div :class="['des-content', { 'more-des-content': !isMore }]">
        <sdc-mce-preview 
        class="text-preview" 
        :urlConfig="editorConfig.urlConfig"
        :content="courseData.course_desc ?? ''"
        >
        </sdc-mce-preview>
        <div v-if="isPreviewMore" class="transparent-more">
        <span 
          class="more-text" 
          @click="isMore = !isMore"
          :dt-eid="dtCommon('eid')"
          :dt-remark="dtCommon('remark')"
          :dt-areaid="dtCommon('areaid')" 
          >
          {{ isMore ? $langue('Mooc_Home_More', { defaultText: '展开查看更多...' }) : $langue('NetCourse_Retract', { defaultText: '收起' })}}
        </span>
        </div>
      </div>
    </div>
    <!-- 猜你喜欢 -->
    <!-- <guessCard></guessCard> -->
  </div>
</template>
<script>
// import guessCard from './guessCard.vue'
import ActiveJump from '@/views/components/activeJump.vue'
export default {
  components: {
    // guessCard
    ActiveJump
  },
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    },
    specialUsers: {
      type: Boolean,
      default: false
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    courseType: {
      type: String,
      default: 'net'
    },
    dtPageType: {
      type: String,
      default: '网课详情页-新版'
    }
  },
  data() {
    return {
      editorConfig: {
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/{contentId}`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview`
        }
      },
      isPreviewMore: false,
      isMore: false
    }
  },
  watch: {
    courseData: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val.course_desc) {
          // 添加窗口大小改变监听器
          this.$nextTick(() => {
            const wt = document.getElementsByClassName('text-preview')[0].offsetHeight
            if (wt > 130) {
              this.isPreviewMore = true
              this.isMore = true
            }
          })
        }
      }
    }
  },
  computed: {
    dtArg() {
      return {
        page: this.courseData.course_name,
        page_type: this.dtPageType,
        container: '课程介绍',
        content_name: '订阅抽奖入口',
        course_id: this.course_id
      }
    },
    previewLbael() {
      return this.courseData.labels || []
    },
    labelNodeEnv() {
      return process.env.NODE_ENV === 'production' ? 'production' : 'test'
    },
    course_id() {
      return this.courseData.course_id || parseInt(this.$route.query.course_id)
    },
    // label-show组件所需要的埋点数据
    courseInfo() {
      return (type) => {
        let { course_id } = this.$route.query
        const click_type = type === 'btn' ? 'button' : 'data'
        const container = type === 'btn' ? `介绍-打标签` : `介绍-标签`
        return {
          mooc_course_id: course_id,
          page: this.courseData.course_name, // 任务名称
          page_type: this.dtPageType,
          container, // 板块的名称
          click_type,
          terminal: 'PC'
        }
      }
    },
    dtCommon() {
      return (type) => {
        const content_name = this.isMore ? '更多' : '收起'
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: this.dtPageType, 
            container: `介绍`,
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name,
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${content_name}`
        } else {
          return `area_${this.course_id}_${content_name}`
        }
      }
    }
  },
  methods: {
    input(val) {
      this.courseData.labels = val
      setTimeout(() => {
        this.$emit('handleCourseDetail')
        this.$refs.labelShow && this.$refs.labelShow.getLabelList()
      }, 500)
    }
  }
}
</script>
<style lang="less" scoped>
.desc-box {
  .desc-top {
    padding: 16px 24px 24px;
    background-color: #fff;
    border-radius: 0 0 8px 8px;

    .desc-tag-box {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;

      .tag-left {
        display: flex;
        align-items: center;
      }

      .tag-left {
        .tag-content {
          display: flex;
          line-height: 20px;

          .label {
            color: rgba(0, 0, 0, 0.4);
            width: 50px;
          }

          .label-height25 {
            height: 25px;
            line-height: 25px;
          }

          .tag-list-box {
            flex: 1;
            display: flex;
            flex-wrap: wrap;
          }
        }
      }
    }

    .des-content {
      height: 166px;
      overflow: hidden;
      position: relative;
      .transparent-more {
        height: 53px;
        flex-shrink: 0;
        background: linear-gradient(180deg, #ffffff63 0%, #FFF 100%);
        position: absolute;
        bottom: 0px;
        width: 100%;
        display: flex;
        align-items: flex-end;
        justify-content: center;
        cursor: pointer;

        .more-text {
          color: #0052D9
        }
      }
    }

    .more-des-content {
      height: unset;
      overflow: unset;
      .text-preview {
        padding-bottom: 24px;
        background: #fff;
      }
      .transparent-more {
        height: unset;
        background: unset;
      }
    }
    .mgb-16 {
      margin-bottom: 16px;
    }
  }
}
</style>
