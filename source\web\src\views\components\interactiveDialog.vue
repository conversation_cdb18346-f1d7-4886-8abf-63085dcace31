<template>
  <div class="wrapper" v-if="visible">
    <div class="main">
      <div class="content">
        <div class="title">{{interactiveObj.title}}</div>
        <div v-if="interactiveObj.introduction" class="introduction" v-html="interactiveObj.introduction"></div>
        <div class="options">
          <div v-for="(item, index) in interactiveObj.select_content" :key="item.question_id" class="option-item margin-16px">
            <!-- <div v-if="item.question_text" class="question-text margin-16px">{{item.question_text}}</div> -->
            <el-checkbox-group class="question-multi" v-if="questionMulti(item)" v-model="selected_answer[index]">
              <div v-for="subItem in item.options" :key="subItem.option_value" class="question-item margin-16px">
                <el-checkbox :label="subItem.option_value">{{subItem.option_text}}</el-checkbox>
              </div>
            </el-checkbox-group>
            <el-radio-group class="question-single" v-else v-model="selected_answer[index]">
              <div v-for="subItem in item.options" :key="subItem.option_value" class="question-item margin-16px">
                <el-radio :label="subItem.option_value" >{{subItem.option_text}}</el-radio>
              </div>
            </el-radio-group>
          </div>
        </div>
      </div>
      <div class="btn">
         <el-tooltip class="item" effect="dark" :disabled="!btnDisabled || !interactiveObj.continue_studying_tips" :content="interactiveObj.continue_studying_tips" placement="top">
          <!-- 增加span标签解决qq浏览器不弹tips问题 -->
          <span>
            <el-button type="primary" size="small" class="continue-study" :disabled="btnDisabled" @click="continueStudy">{{ $langue('Mooc_ProjectDetail_TrainingProgress_ContinueStudy', { defaultText: '继续学习' }) }}</el-button>
          </span>
        </el-tooltip>
      </div>
    </div>
  </div>
</template>

<script>
import { getCourseInteraction, saveInteractionRecord } from '@/config/mooc.api.conf.js'
export default {
  name: 'interactiveDialog',
  props: {
    course_id: {
      type: Number,
      default: 0
    },
    record_id: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      visible: false,
      interactiveObj: {},
      configSelectList: [],
      selected_answer: [],
      btnDisabled: true,
      interactiveConfigId: '',
      interactionTime: -1
    }
  },
  mounted() {
    this.getInteractionDetail()
  },
  computed: {
    questionMulti() {
      return (item) => {
        return item.choose_type_config?.type === 'multi' || item.vote_type_config?.can_max_vote_nums > 1
      }
    }
  },
  watch: {
    selected_answer: {
      handler(val) {
        let select_content = this.interactiveObj.select_content
        let flag = select_content.every((item, index) => {
          if (item.active_type === 'choose' && item.choose_type_config.completion_conditions === 'correct') {
            let newVal = typeof val[index] === 'string' ? val[index] : JSON.parse(JSON.stringify(val[index])).sort().join(',')
            return newVal === item.correct_answer.sort().join(',')
          } else if (item.active_type === 'vote' && item.vote_type_config?.can_max_vote_nums > 1) {
            return val[index].length && val[index].length <= item.vote_type_config.can_max_vote_nums
          } else {
            return typeof (val[index]) === 'string' ? !!val[index] : val[index]?.length > 0
          }
        })
        this.btnDisabled = !flag
      },
      deep: true
    }
  },
  methods: {
    getInteractionDetail() {
      let params = {
        courseId: this.course_id,
        actType: 2
      }
      getCourseInteraction(params).then(res => {
        this.interactiveConfigId = res?.id || ''
        this.configSelectList = (res?.configurations_of_select || []).sort((a, b) => a.active_time - b.active_time)
      })
    },
    getVideoTime(s) {
      const curr = Math.floor(s)
      if (curr === this.interactionTime || this.visible) return
      this.interactionTime = curr
      this.interactiveObj = this.configSelectList.find(
        (item) => parseInt(item.active_time) === curr
      ) || {}
      
      if (!this.interactiveObj.select_content || this.interactiveObj.select_content?.length === 0) return
      this.selected_answer = this.interactiveObj.select_content.map(item => {
        return this.questionMulti(item) ? [] : ''
      })
      this.visible = true
      this.$emit('changePlayStatus', 'pause')
    },
    continueStudy() {
      this.visible = false
      this.btnDisabled = true
      // 1秒后，重新初始化，弹窗1秒内只会弹出一次
      // setTimeout(() => {
      //   this.interactionTime = -1
      // }, 1000)
      this.$emit('changePlayStatus', 'play')
      let answers = this.interactiveObj.select_content.map((item, index) => {
        let active_answer = this.selected_answer[index] instanceof Array ? this.selected_answer[index].sort().join(',') : this.selected_answer[index]
        return {
          question_id: item.question_id,
          active_answer
        }
      })
      let params = {
        interactive_config_id: this.interactiveConfigId,
        interactive_id: this.interactiveObj.interactive_id,
        record_id: this.record_id,
        answers
      }
      saveInteractionRecord(params).then(res => {

      })
    }
  }
}
</script>
<style lang="less" scoped>
.wrapper{
  z-index: 999;
}
.main{
  position: relative;
  width: 738px;
  height: 404px;
  border-radius: 4px;
  background: #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,.3);
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  padding: 24px 12px 0 32px;
  z-index: 999;
  .content{
    padding-right: 12px;
    overflow: auto;
    height: calc(100% - 64px);
  }
  .title{
    color: #000000e6;
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
  }
  .introduction{
    padding: 12px;
    background: #FAFAFA;
    margin: 24px 0;
    color: #00000099;
    :deep(.sdc-editor-preview .content-wrapper){
      background: #FAFAFA;
    }
  }
  :deep(.options){
    .margin-16px{
      margin-bottom: 16px;

    }
    .el-checkbox-group{
      display: inline-block;
    }
    .question-item{
      .el-radio,.el-checkbox{
        display: flex;
        position: relative;
      }
      .el-radio__input,.el-checkbox__input{
        position: absolute;
        top: 3px;
      }
      .el-radio__label,.el-checkbox__label{
        color: #000000e6;
        white-space: pre-line;
        line-height: 20px;
        padding-left: 28px;
        word-break: break-word;
      }
      .el-radio__inner,.el-checkbox__inner{
        border: 1px solid #B3B3B3;
      }
    }
  }
  .btn{
    position: fixed;
    height: 64px;
    line-height: 64px;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    background: #fff;
    .continue-study{
      width: 240px;
    }
  }
}
</style>
