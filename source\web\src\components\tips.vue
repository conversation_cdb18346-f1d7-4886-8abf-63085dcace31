<template>
  <div class="custom-container" :style="{ background: backgroundColor, color: color, lineHeight: lineHeight }">
    <i :class="IconName"></i>
    <span class="custom-tips-title">{{ title }} <span v-if="clickTitle" class="text-blue"
        @click="confirm">{{ clickTitle }}</span></span>
  </div>
</template>
<script>

export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    backgroundColor: {
      type: String,
      default: ''
    },
    color: {
      type: String,
      default: ''
    },
    IconName: {
      type: String,
      default: ''
    },
    lineHeight: {
      type: String,
      default: ''
    },
    clickTitle: {
      type: String,
      default: ''
    }
  },
  methods: {
    confirm() {
      this.$emit('confirm')
    }
  }
}
</script>
<style lang="less" scoped>
.custom-container {
  padding: 0 16px;

  .custom-tips-title {
    margin-left: 5px;
  }
}
</style>
