<template>
  <div class="question-view">
    <div class="header">
      <span class="title">{{ type === 'font' ? '课前' : '课后'}}问卷</span>
      <div class="tab">
        <div class="tab-status" v-if="isEmpty">
          <el-tooltip :content="getTooltipContent" placement="top" :disabled="!isDisabled">
            <el-button
              type="text"
              class="btn-color"
              @click="createQuestion"
              :disabled="isDisabled"
            >创建/关联问卷</el-button>
          </el-tooltip>
        </div>

        <!-- 课前问卷按钮组 -->
        <div class="tab-status" v-else-if="type === 'font'">
          <el-button type="text" class="btn-color" @click="createQuestion">查看/编辑问卷</el-button>
          <!-- <el-button type="text" class="btn-color" @click="remindFun" v-if="type === 'after'">催办设置</el-button> -->
        </div>

        <!-- 课后问卷按钮组 -->
        <div class="tab-status-after" v-else-if="type === 'after'">
          <div class="tab-status-after-left">
            <div class="start-mode">
              <span v-if="selfQuestionData[0].survey.is_auto_start === 0">手动启动：</span>
              <span v-if="selfQuestionData[0].survey.is_auto_start === 1">自动启动：</span>
            </div>

            <div class="start-status">
              <span v-if="selfQuestionData[0].survey.status === null">未启动收集</span>
              <span v-if="selfQuestionData[0].survey.status === 1">收集中</span>
              <span v-if="selfQuestionData[0].survey.status === 2">已结束</span>
            </div>

            <div class="start-btn">
              <div class="start-btn-item" v-if="[null].includes(selfQuestionData[0].survey.status)">
                <el-button
                  type="text"
                  class="btn-color"
                  @click="startSurvey"
                  v-if="selfQuestionData[0].survey.is_auto_start === 0"
                >点击启动</el-button>
                <el-button type="text" class="btn-color" @click="changeStartMode">修改启动方式</el-button>
              </div>

              <!-- 收集中显示 -->
              <div class="start-btn-item" v-if="selfQuestionData[0].survey.status === 1">
                <el-button
                  type="text"
                  class="btn-color"
                  @click="showFeedback({ isCanEdit: true })"
                >关闭问卷并发送总结</el-button>
                <span
                  :style="{ color: selfQuestionData[0].report_overview.collected_rate >= 75 ? '#56C08D' : '' }"
                >{{ selfQuestionData[0].report_overview.collected_rate >= 75 ? '（填写率已达75%）' : '（填写率需达75%）' }}</span>
              </div>

              <!-- 已结束显示 -->
              <div class="start-btn-item" v-if="selfQuestionData[0].survey.status === 2">
                <span v-if="selfQuestionData[0].survey.feedback_type === 1">反馈总结已发送至活动负责人邮箱</span>
                <span v-if="selfQuestionData[0].survey.feedback_type === 2">反馈总结已发送至学员、讲师和活动负责人邮箱</span>
                <el-button
                  type="text"
                  class="btn-color"
                  style="margin-left: 16px;"
                  @click="showFeedback({ isCanEdit: false })"
                >点击查看</el-button>
              </div>
            </div>
          </div>

          <div class="tab-status-after-right">
            <el-button
              type="text"
              class="btn-color"
              :disabled="[1, 2].includes(selfQuestionData[0].survey.status)"
              @click="createQuestion"
            >编辑问卷</el-button>
            <el-button type="text" class="btn-color" @click="previewQuestion">预览问卷</el-button>
            <el-button
              type="text"
              class="btn-color"
              @click="remindFun(false)"
              :disabled="[2].includes(selfQuestionData[0].survey.status)"
            >催办设置</el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="contain">
      <template v-if="!isEmpty">
        <div class="contain-box" v-for="(item, index) in selfQuestionData" :key="index">
          <div class="question-title">
            <i>
              <img src="~@/assets/mooc-img/questionnaire-icon.png" alt />
              <span>{{ item.survey.survey_name }}</span>
            </i>
            <div style="margin-left: auto">
              <el-button type="text" class="bule-t" @click="refreshScore(item)" v-if="type === 'after'">刷新评分</el-button>
              <el-button type="text" class="bule-t" @click="showMoreData(item)">查看更多统计数据</el-button>
            </div>
          </div>

          <div class="detail">
            <div class="create-author">{{ item.survey.creator_name }}</div>
            <span></span>
            <div
              class="time"
            >创建于 {{ item.survey.created_at ? item.survey.created_at.split(' ')[0] : '-' }}</div>
            <span></span>
            <img src="~@/assets/mooc-img/usergroup.png" alt />
            <i
              style="margin-left: auto; font-style: normal;color: #00000099;"
              v-if="type === 'after'"
            >各项评价分数每晚定时刷新，如未配置评分收集问题，则不显示相关评分</i>
          </div>

          <div class="data-contain">
            <div class="data-box-first data-box">
              <div class="item">
                <div>{{ doneFillCount(item) }}</div>
                <div>填写率</div>
              </div>

              <div class="item">
                <div>{{ shouldFillCount(item) }}</div>
                <div>应填人次</div>
              </div>

              <div class="item">
                <div>{{ item.report_overview.collected_count ? item.report_overview.collected_count : '-' }}</div>
                <div>填写人次</div>
              </div>
            </div>
            <template v-if="type === 'after'">
              <div class="xian"></div>
              <div class="data-box-second data-box">
                <div class="item">
                  <div>{{ computedTotalScore(item) }}</div>
                  <div>综合评价</div>
                </div>

                <div class="item">
                  <div>{{ computedActivityScore(item) }}</div>
                  <div>内容评价</div>
                </div>

                <div class="item">
                  <div>{{ computedLecturerScore(item) }}</div>
                  <div>讲师评价</div>
                </div>

                <div class="item">
                  <div>{{ computedOrganizeScore(item) }}</div>
                  <div>组织评价</div>
                </div>
              </div>
            </template>
            <div class="xian"></div>
            <div class="data-box-third data-box">
              <div class="item">
                <div>{{ item.report_overview.viewed_count ? item.report_overview.viewed_count : '-' }}</div>
                <div>问卷浏览人次</div>
              </div>

              <div class="item">
                <div>{{ formatDuration(item.report_overview.duration_avg) }}</div>
                <div>平均填写时长</div>
              </div>
            </div>
          </div>
        </div>
      </template>
      <div class="empty" v-else>暂未配置问卷</div>
    </div>
    <remind
      v-if="isShowRemind"
      :visible.sync="isShowRemind"
      @getRemindInfo="getRemindInfo"
      :remindInfo="remindInfo"
      :isShowStartMode="isShowStartMode"
      :time="{
        start: activityInfo.start_time,
        end: activityInfo.end_time
      }"
    ></remind>
    <questionManageDialog
      v-if="isShowQuestionManage"
      :visible.sync="isShowQuestionManage"
      :sid="selfQuestionData.length > 0 ? selfQuestionData[0].survey.wj_id : ''"
      :type="type"
      @getQuestionData="getQuestionData"
      :questionType="isEmpty ? 'add' : 'edit'"
      :otherOption="otherOption"
    ></questionManageDialog>
    <start-mode-dialog
      v-if="isShowStartModeDialog"
      :visible.sync="isShowStartModeDialog"
      @cancel="startModeDialogCancel"
      @confirm="handleStartModeConfirm"
      :startMode="startMode"
    ></start-mode-dialog>
  </div>
</template>

<script>
import remind from './remind.vue'
import questionManageDialog from '@/views/components/questionManageDialog.vue'
import { mapState } from 'vuex'
import {
  getRemindInfoApi,
  getQuestionDataStatisticsApi,
  createQuestionApi,
  bindQuestionApi,
  updateQuestionRemindApi,
  startSurveyApi,
  saveSurveyStartTypeApi
} from '@/config/classroom.api.conf.js'
import startModeDialog from './startMode/startModeDialog.vue'
export default {
  computed: {
    ...mapState({
      activityInfo: state => state.activity.activityInfo
    }),
    // 获取提示文案
    getTooltipContent() {
      const now = new Date()
      const startTime = new Date(this.activityInfo.start_time)
      const endTime = new Date(this.activityInfo.end_time)

      if (now >= startTime && now <= endTime) {
        return '活动已开始，无法关联课前问卷'
      } else if (now > endTime) {
        return '活动已结束，无法关联课前问卷'
      }
      return ''
    },
    // 判断按钮是否禁用
    isDisabled() {
      if (this.type === 'font') {
        return this.isActivityStarted || this.isActivityEnded
      } else {
        return false
      }
    },
    // 判断活动是否已开始
    isActivityStarted() {
      console.log('this.activityInfo.start_time', this.activityInfo.start_time)
      if (!this.activityInfo.start_time) return false
      const now = new Date()
      const startTime = new Date(this.activityInfo.start_time)
      return now >= startTime
    },
    // 判断活动是否已结束
    isActivityEnded() {
      if (!this.activityInfo.end_time) return false
      const now = new Date()
      const endTime = new Date(this.activityInfo.end_time)
      return now >= endTime
    },
    getCurrentFormattedTime() {
      const now = new Date()

      const year = now.getFullYear()
      const month = now.getMonth() + 1 // getMonth()返回0-11
      const day = now.getDate()
      const hours = now.getHours()
      const minutes = now.getMinutes()
      const seconds = now.getSeconds()

      // 组合成所需格式，不补零
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }
  },
  components: {
    remind,
    questionManageDialog,
    startModeDialog
  },
  props: {
    type: {
      type: String,
      default: 'font' // font 课前问卷 after 课后问卷
    },
    questionData: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    questionData: {
      handler(newVal) {
        if (newVal.length === 0) {
          this.isEmpty = true
        } else {
          this.isEmpty = false
        }
        this.selfQuestionData = newVal
        console.log(newVal, 'iooo')
        if (newVal && newVal.length > 0) {
          this.startMode = newVal[0].survey.is_auto_start
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    this.getQuestionRemindInfo()
  },
  data() {
    return {
      isEmpty: false,
      isShowRemind: false,
      isShowQuestionManage: false,
      remindInfo: {
        enabled_remaind: false,
        message_type: '',
        reminder_begin_time: '',
        reminder_end_time: '',
        reminder_fixed_send_times: '',
        reminder_fixed_week_time: '',
        reminder_gap_time: '',
        reminder_send_times: '',
        reminder_task_id: '',
        act_id: ''
      },
      selfQuestionData: [],
      otherOption: {
        mode: 'normal', // normal 正常模式 checkData 查看统计数据
        customAdd: false, // 是否添加自定义
        customUrl: '', // 自定义问卷链接
        isCustomCss: true // 是否添加自定义样式
      },
      isShowStartModeDialog: false,
      startMode: 1, // 0 手动启动 1 自动启动
      isShowStartMode: false // 是否显示启动方式
    }
  },
  methods: {
    computedTotalScore(item) {
      return item.survey_score.total_score ? this.truncateNumber(item.survey_score.total_score, 2) : '-'
    },
    computedActivityScore(item) {
      return item.survey_score.activity_score ? this.truncateNumber(item.survey_score.activity_score, 2) : '-'
    },
    computedLecturerScore(item) {
      return item.survey_score.lecturer_score ? this.truncateNumber(item.survey_score.lecturer_score, 2) : '-'
    },
    computedOrganizeScore(item) {
      return item.survey_score.organize_score ? this.truncateNumber(item.survey_score.organize_score, 2) : '-'
    },
    shouldFillCount(item) {
      if (this.type === 'font') {
        return item.report_overview.reg_count || '-'
      } else {
        return item.report_overview.survey_send_count || '-'
      }
    },
    doneFillCount(item) {
      return item.report_overview.collected_rate ? this.truncateNumber(item.report_overview.collected_rate, 2) + '%' : '-'
    },
    truncateNumber(value, precision) {
      const multiplier = Math.pow(10, precision)
      return Math.floor(value * multiplier) / multiplier
    },
    previewQuestion() {
      window.open(this.selfQuestionData[0].survey.wj_url)
    },
    showFeedback({ isCanEdit = false }) {
      this.$emit('showFeedback', {
        isCanEdit,
        survey_id: this.selfQuestionData[0].survey.survey_id || ''
      })
    },
    // 启动问卷调查
    startSurvey() {
      startSurveyApi({
        activity_id: this.$route.query.activity_id || '',
        survey_id: this.selfQuestionData[0].survey.survey_id
      }).then(res => {
        this.selfQuestionData[0].survey.status = 1
        this.$message.success('问卷启动成功')
      })
    },
    // 修改问卷启动方式
    handleStartModeConfirm(startMode) {
      saveSurveyStartTypeApi({
        activity_id: this.$route.query.activity_id || '',
        survey_id: this.selfQuestionData[0].survey.survey_id,
        is_auto_start: startMode
      }).then(res => {
        this.$message.success('问卷启动方式修改成功')
        this.selfQuestionData[0].survey.is_auto_start = startMode
      })
      this.isShowStartModeDialog = false
    },
    startModeDialogCancel() {
      this.isShowStartModeDialog = false
    },
    changeStartMode() {
      this.isShowStartModeDialog = true
    },
    // 添加格式化时间的方法
    formatDuration(seconds) {
      if (!seconds) return '-'
      const minutes = Math.floor(seconds / 60)
      const remainingSeconds = seconds % 60
      if (minutes === 0) {
        return `${remainingSeconds}秒`
      }
      return `${minutes}分${remainingSeconds}秒`
    },
    remindFun() {
      this.isShowRemind = true
    },
    createQuestion() {
      // 如果是查看，直接调用组件查看问卷
      if (this.selfQuestionData.length > 0) {
        this.isShowQuestionManage = true
        return
      }
      createQuestionApi({
        survey_name: `${this.activityInfo.activity_name}-${
          this.type === 'font' ? '课前' : '课后'
        }问卷`,
        survey_desc:
          '感谢您参与本次活动，我们非常重视每位学员的宝贵意见，期待您的反馈！'
      })
        .then(res => {
          if (!res) {
            this.$message.error('问卷创建失败, 请稍后再试')
            return
          }
          this.otherOption.customUrl = res += '&mode=qlearningCheckData'
          // this.otherOption.customUrl = `https://wj-learn.woa.com/edit/v2.html?sid=10099`
          this.isShowQuestionManage = true
        })
        .catch(() => {
          this.$message.error('问卷创建失败, 请稍后再试')
        })
    },
    async getQuestionData(data) {
      const {
        act_id = '',
        act_name = '',
        resource_url = '',
        openMode = '', // add 新增  edit 编辑
        remindSetting = {},
        startMode = 1 // 0 手动启动 1 自动启动
      } = data
      let obj = {
        wj_id: act_id,
        wj_url: resource_url,
        survey_name: act_name,
        survey_id: '', // 新增为空，编辑沿用之前拿到的（给数据库更新用作标识）
        // status: 1, // 1 勾选了，3 未勾选
        category: this.type === 'font' ? 0 : 1,
        creator_name: this.$store.state.userInfo.staff_name,
        created_at: this.getCurrentFormattedTime
      }
      if (this.type === 'after' && openMode === 'add') {
        obj.is_auto_start = startMode
      }
      let result = ''
      try {
        if (this.selfQuestionData.length === 0) {
          let dto = {
            surveys: [obj],
            activity_id: this.$route.query.activity_id || '',
            remind_setting: this.handleRemindInfo(remindSetting)
          }
          let res = await bindQuestionApi(dto)
          result = res[0]
        } else {
          result = obj
        }
        this.$set(this.selfQuestionData, 0, {
          survey: result,
          report_overview: {},
          report_score: {},
          survey_score: {}
        })
        // 更新催办信息
        this.remindInfo = this.handleRemindInfo(remindSetting)
        // 更新启动方式
        this.selfQuestionData[0].survey.is_auto_start = startMode
        // 更新问卷状态
        this.selfQuestionData[0].survey.status = null
        this.isShowQuestionManage = false
      } catch (error) {
        this.$message.error('问卷绑定失败, 请稍后再试')
      }
    },
    getRemindInfo(data) {
      const { remindData = {} } = data
      let remindInfo = this.handleRemindInfo(remindData)
      remindInfo.act_id = this.$route.query.activity_id || ''
      updateQuestionRemindApi(remindInfo).then(res => {
        this.remindInfo = res
        this.$message.success('催办更新成功')
      })
    },
    handleRemindInfo(data) {
      let {
        message_type,
        time_1,
        time_2,
        reminder_fixed_week_time,
        reminder_gap_time,
        reminder_fixed_send_times,
        date,
        addWeekList,
        enabled_remaind,
        reminder_task_id
      } = data
      let reminder_send_times = reminder_gap_time ? time_1 : time_2
      let reminder_begin_time, reminder_end_time
      if (message_type && message_type.length > 0) {
        message_type = message_type.join(';')
      }
      if (addWeekList && addWeekList.length > 0) {
        reminder_fixed_send_times = addWeekList
          .map(item => {
            return item.value
          })
          .join(';')
      }
      if (date && date.length > 0) {
        reminder_begin_time = date[0]
        reminder_end_time = date[1]
      }
      return {
        reminder_begin_time, // 起止催办时间 - 开始时间
        reminder_end_time, // 起止催办时间 - 结束时间
        reminder_send_times, // 间隔天数的具体时间
        reminder_fixed_send_times, // 固定时间催办 格式 xx:xx:xx;aa:aa:aa
        reminder_fixed_week_time, // 每周 - 周几催办 ？
        reminder_gap_time, // 每周 - 周几的具体时间
        message_type: message_type, // 催办渠道
        enabled_remaind: enabled_remaind,
        reminder_task_id
      }
    },
    getQuestionRemindInfo() {
      let activity_id = this.$route.query.activity_id
      if (activity_id && this.type === 'after') {
        getRemindInfoApi({
          activity_id
        }).then(res => {
          if (!res) return
          this.remindInfo = res
        })
      }
    },
    // 查看更多统计数据
    showMoreData(item) {
      this.isShowQuestionManage = true
      this.otherOption.mode = 'checkData'
    },
    // 刷新评分
    refreshScore(item) {
      // let res = {
      //   survey: {
      //     survey_id: 101725,
      //     survey_name: 'newsdkfjdskfj',
      //     act_type: 4,
      //     course_id: 2797,
      //     act_id: 2797,
      //     category: 0,
      //     wj_id: '10601',
      //     wj_url: 'https://wj-learn.woa.com/s2/10601/722d/',
      //     survey_desc: null,
      //     status: 1,
      //     creator_id: 68317,
      //     creator_name: 'vincentyqwu',
      //     created_at: '2025-03-27 14:58:45',
      //     update_id: 170407,
      //     update_name: 'v_yhacao',
      //     updated_at: '2025-04-02 14:56:23'
      //   },
      //   report_overview: {
      //     viewed_count: 2,
      //     collected_count: 3,
      //     collected_rate: 4,
      //     reg_count: 5,
      //     duration_avg: 8,
      //     status: null,
      //     channel: null,
      //     ua_device: null,
      //     ua_os: null
      //   },
      //   survey_score: {
      //     activity_id: null,
      //     total_score: null,
      //     organize_score: null,
      //     activity_score: null,
      //     lecturer_score: null
      //   }
      // }
      // this.selfQuestionData.find((item, index) => {
      //   if (item.survey.survey_id === res.survey.survey_id) {
      //     this.$set(this.selfQuestionData, index, res)
      //   }
      // })
      getQuestionDataStatisticsApi({
        survey_id: item.survey.survey_id,
        activity_id: this.$route.query.activity_id || ''
      }).then(res => {
        this.selfQuestionData.find((item, index) => {
          if (item.survey.survey_id === res.survey.survey_id) {
            this.$set(this.selfQuestionData, index, res)
          }
        })
        this.$message.success('评分刷新成功')
      })
    }
  }
}
</script>

<style lang="less" scoped>
.question-view {
  .header {
    display: flex;
    align-items: center;
    .title {
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      color: #000000cc;
      margin-right: 24px;
    }
    .tab {
      flex: 1;
      .tab-status {
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        gap: 0 24px;
        :deep(.el-button--text) {
          padding-top: 0;
          padding-bottom: 0;
          margin-left: 0;
        }
        .not {
          color: gray;
          margin-left: 24px;
        }
        .btn-color {
          color: #0052d9;
          cursor: pointer;
        }
      }
      .tab-status-after {
        display: flex;
        align-items: center;
        gap: 0 24px;
        font-size: 14px;
        .tab-status-after-left {
          display: flex;
          align-items: center;
          color: #00000066;
          .start-mode {
            // margin-right: 24px;
          }
          .start-status {
            margin-right: 24px;
          }
          .start-btn {
            .start-btn-item {
              :deep(.disable-gray) {
                color: #00000066 !important;
              }
            }
          }
        }
        .tab-status-after-right {
          margin-left: auto;
          display: flex;
          align-items: center;
          gap: 0 16px;
          :deep(.el-button) {
            padding: 0;
            margin-left: 0;
          }
          &:last-child {
            margin-right: 28px;
          }
        }
      }
    }
  }

  .contain {
    margin-top: 13px;
    border-radius: 8px;
    background: #f9f9f9;
    .contain-box {
      height: 100%;
      padding: 17px 16px 16px 16px;
      .question-title {
        display: flex;
        align-items: center;
        > i {
          color: #000000e6;
          font-size: 16px;
          font-weight: 500;
          line-height: 22px;
          display: flex;
          align-items: center;
          font-style: normal;
          span {
            max-width: 558px;
            word-break: keep-all;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          img {
            width: 20px;
            margin: 0 8px 0 0;
          }
        }
        div {
          display: flex;
          align-items: center;
          gap: 0 20px;
          :deep(.el-button--text) {
            padding-top: 0;
            padding-bottom: 0;
            margin-left: 0;

            color: #0052d9;
            font-size: 14px;
            line-height: 22px;
          }
        }
      }
      .detail {
        display: flex;
        align-items: center;
        margin: 13px 0 0 32px;
        .time {
          color: #00000099;
        }
        .create-author {
          color: #00000099;
        }
        > span {
          width: 1px;
          height: 16px;
          background: #ccc;
          display: block;
          margin: 0 15px;
        }
        > img {
          width: 16px;
        }
      }
      .data-contain {
        display: flex;
        align-items: center;
        height: 85px;
        margin-top: 12px;
        padding: 0 20px 0 32px;
        background-color: #fff;
        border-radius: 4px;
        .data-box-first,
        .data-box-second,
        .data-box-third {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 0 16px;
        }
        .data-box-first {
          min-width: 274px;
        }
        .data-box-second {
          min-width: 272px;
        }
        .data-box-third {
          min-width: 200px;
        }
        .xian {
          width: 1px;
          height: 48px;
          background: #eee;
          margin: 0 20px;
          flex-shrink: 0;
        }
        .data-box {
          flex-shrink: 0;
          .item {
            div {
              text-align: center;
              &:nth-child(1) {
                color: #000000e6;
                font-size: 20px;
                font-weight: 600;
                margin-bottom: 6px;
                white-space: nowrap;
              }
              &:nth-child(2) {
                font-size: 14px;
                color: #00000099;
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
    .empty {
      background: #f9f9f9;
      color: #00000099;
      height: 185px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
.mr-15 {
  margin-right: 15px;
}
</style>
