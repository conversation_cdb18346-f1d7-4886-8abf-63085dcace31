.course-make-style {
  display: flex;
  background-color: #f6f7f9;
  .tab-box {
    flex: 1;
    .header {
      padding: 24px 28px;
      border-bottom: 1px solid rgba(238,238,238,1);
      color: rgba(0,0,0,0.8);
      font-size: 16px;
      font-weight: 700;
      background-color: #fff;
    }
    .ul-tabs-chapter {
      height: 32px;
      line-height: 32px;
      border: 0;
      display: flex;
      margin-top: 20px;
      .li-tab-item {
        padding: 0 20px;
        width: 100px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        margin: 0;
        border: 0;
        font-family: "PingFang SC";
        font-size: 14px;
        color: #00000099;
        box-sizing: border-box;
        cursor: pointer;
      }
      .li-tab-item.active {
        background-color: #ffffff;
        color: #0052d9;
        text-align: center;
        font-weight: 600;
      }
    }
    .content-main {
      display: flex;
    }
  }
  .step-left {
    flex: 1;
    background-color: #fff;
    margin-bottom: 90px;

    .content {
      .tips {
        display: flex;
        padding: 28px 28px 22px;
        line-height: 20px;

        .label {
          color: #0052d9;
        }

        .text {
          flex: 1;
          color: #999;
        }
      }

      :deep(.el-steps) {
        padding: 31px 28px 24px 16px;

        .el-step {
          &:last-of-type .el-step__line {
            display: unset;
          }

          .el-step__head {
            top: 6px;

            .el-step__line {
              background-color: #e4f2ff;
            }

            .el-step__icon {
              bottom: 6px;
              width: 24px;
              height: 24px;
              background-color: #3464e0;
              border: unset;
              font-size: 16px;
              color: #fff;
              font-weight: bold;
              transform: scale(0.5, 0.5);
            }
          }

          .el-step__main {
            padding-left: 8px;

            .el-step__title {
              padding-bottom: 0;
              font-size: 16px;
              font-weight: bold;
              line-height: 24px;
              color: #333;

              >span {
                display: inline-block;
                line-height: 24px;
              }

              .result-tips,
              .cofirm-text {
                margin-left: 16px;
                font-weight: 400;
                font-size: 14px;
                color: #999;
              }

              .sumbmit-btn,
              .demo-generat {
                margin-left: 20px;
              }

              .demo-generat {
                padding-right: 28px;
                color: #0052d9;
                font-size: 14px;
                font-weight: 400;
                line-height: 22px;
                background: url(~@/assets/img/loading.gif) no-repeat right/16px 16px;
              }
            }

            .el-step__description {
              padding: 20px 0 33px;
            }

            .el-step__description.is-finish {
              color: #333;
            }
          }
        }
      }
    }
  }
  .chapters-content {
    min-height: 696px;
    padding: 32px 28px 28px;
    background-color: #fff;
    height: 100%;
    .chapters-upload {
      width: 366px;
      background: #FBFBFB;
    }
  }

  .buttom {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70px;
    line-height: 70px;
    background-color: #fff;
    text-align: center;
    z-index: 99;

    .inner {
      display: inline-block;
      padding-left: 188px;
      text-align: left;

      @media screen and (max-width: 1660px) {
        width: 1158px;
      }

      @media screen and (min-width: 1661px) {
        width: 1440px;
      }

      .el-button {
        margin: 0 20px 0 0;
        width: 104px;
      }

      .tips {
        margin-left: 20px;
        color: #999;
      }
    }
  }
}
