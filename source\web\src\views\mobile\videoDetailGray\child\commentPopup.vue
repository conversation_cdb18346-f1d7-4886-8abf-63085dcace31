<template>
  <van-popup class="comment-container"  get-container="body" v-model="show" :overlay="true" position="bottom" :close-on-click-overlay="false">
    <div class="body">
      <div class="top-title">
        <span class="placeholder"></span>
        <span class="title">评价</span>
        <span class="close-icon" @click="close"></span>
      </div>
      <div class="content">
        <span
        v-if="isShowZan"
        class="btn-box"
        @click="debounceHandleLike"
        :dt-remark="dtBtn('remark')"
        :dt-areaid="dtBtn('areaid')"
        :dt-eid="dtBtn('eid')">
          <span :class="['like-btn', { 'active-like-btn': isLike }]">
            <span class="icon"></span>
            <span>{{ `点赞 (${courseData.praise_count || 0})` }}</span>
          </span>
        </span>
        <sdc-comment-mob :params="commentParams" :commentDtInfo="commentDtInfo" bodyContaniner=".comment-container"></sdc-comment-mob>
      </div>
    </div>
  </van-popup>
</template>
<script>
import { Toast } from 'vant'
import {
  netCheckPraised,
  netDeletePraise,
  netAddPraise
} from 'config/api.conf'
import { mapState } from 'vuex'
import { debounce } from 'utils/tools'
export default {
  props: {
    commentShow: {
      type: Boolean,
      default: false
    },
    courseData: {
      type: Object,
      default: () => ({})
    },
    isShowZan: {
      type: Boolean,
      default: true
    },
    // 默认网络课
    courseType: {
      type: String,
      default: 'net'
    }
  },
  data() {
    return {
      commentParams: null,
      isLike: false,
      show: false,
      commentDtInfo: {}
    }
  },
  watch: {
    commentShow(val) {
      this.show = val
    },
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          const hostUrl = process.env.NODE_ENV === 'development' ? process.env.VUE_APP_PORTAL_HOST_WOA : window.origin
          this.commentParams = {
            userName: val.staff_name,
            actId: this.course_id,
            appId: 'A9BiosXihR0h46ThNsAX',
            urlConfig: {
              getComments: `${hostUrl}/training/api/netcourse/user/course-comment/get_comments`,
              addComment: `${hostUrl}/training/api/netcourse/user/course-comment/add`,
              deleteComment: `${hostUrl}/training/api/netcourse/user/course-comment/delete/`,
              like: `${hostUrl}/training/api/netcourse/user/course-comment/praised`,
              sticky: `${hostUrl}/training/api/netcourse/user/course-comment/sticky`,
              show: `${hostUrl}/training/api/netcourse/user/course-comment/show`
            }
          }
        }
      },
      immediate: true
    },
    courseData: {
      immediate: true,
      handler(val) {
        if (this.course_id) {
          console.log(val, '评价的组件courseData')
          this.commentDtInfo = {
            mooc_course_id: this.course_id,
            page: val.course_name,
            page_type: this.pageTypeName + '详情页面-移动新版',
            container: '评价',
            ...this.userInfo
          }
          this.getLikeStatus()
        }
      }
    }
  },
  computed: {
    ...mapState(['userInfo']),
    course_id() {
      return this.courseData.course_id || this.$route.query.course_id || this.$route.query.activity_id
    },
    dtBtn() {
      return (type) => {
        const data = {
          page: this.courseData.course_name,
          page_type: '网课详情页面-移动新版',
          container: '底部导航-评价',
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: '点赞',
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'H5'
        }
        if (type === 'remark') {
          return JSON.stringify(data)
        } else if (type === 'eid') {
          return `element_${this.course_id}_${'点赞'}`
        } else {
          return `area_${this.course_id}_${'点赞'}`
        }
      }
    },
    pageTypeName() {
      let obj = {
        'net': '网课',
        'face': '面授课',
        'activity': '活动'
      }
      return obj[this.courseType]
    }
  },
  mounted() {
  },
  methods: {
    close () {
      this.show = false
      this.$emit('update:commentShow', false)
    },
    getLikeStatus() {
      const params = { net_course_id: this.course_id }
      netCheckPraised(params).then(res => {
        this.isLike = res
      })
    },
    // 点赞
    debounceHandleLike: debounce(function () {
      this.handleLike()
    }, 250),
    handleLike() {
      const params = { net_course_id: this.course_id }
      const commonApi = this.isLike ? netDeletePraise : netAddPraise
      let count = this.courseData.praise_count || 0
      commonApi(params).then((res) => {
        this.getLikeStatus()
        this.courseData.praise_count = this.isLike ? --count : ++count
        this.$emit('update:courseData.praise_count', this.courseData.praise_count)
        const tips = this.isLike ? '取消点赞成功' : '点赞成功'
        if (res.credit && res.credit !== '0') { // 通用积分
          Toast(`${tips}, ${this.$langue('Mooc_Common_Alert_CommonPoint', { point: +res.credit, defaultText: `通用积分+${+res.credit}` })}`)
        } else {
          Toast(tips)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.comment-container {
  height: calc(100% - 210px);

  .comment {
    padding-top: 20px;
  }
  .body {
    height: 100%;
    overflow: hidden;
    .top-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 16px;
      border-bottom: 1px solid #EDEDED;

      .title {
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        color: #000000e6;
      }

      .close-icon {
        display: inline-block;
        width: 24px;
        height: 24px;
        background: url('~@/assets/img/close.png') no-repeat center / cover;
      }
    }
  }
  .content {
    height: calc(100% - 45px);
    overflow: auto;
    .btn-box {
      display: flex;

      .like-btn {
        margin-top: 15px;
        margin-left: 16px;
        border-radius: 24px;
        background: #F4F4F4;
        height: 36px;
        font-size: 12px;
        font-weight: 400;
        line-height: 16px;
        color: #333333;
        padding: 0 31px;
        line-height: 36px;
        display: flex;
        align-items: center;

        .icon {
          display: inline-block;
          width: 16px;
          height: 16px;
          background: url('~@/assets/img/mobile/like-icon.png') no-repeat center / cover;
          margin-right: 8px;
        }
      }

      .active-like-btn {
        background: #FCF5E8;
        color: #FF8000;

        .icon {
          background: url('~@/assets/img/mobile/liked-icon.png') no-repeat center / cover;
        }
      }
    }
  }

}
</style>
