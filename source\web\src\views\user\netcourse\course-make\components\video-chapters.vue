<template>
  <div class="chapters-preview">
    <div class="chapters-video" v-if="approveStatus">
      <approveVideo  
        :content_id.sync="videoInfo.content_id"
        :getCurPlayTime="false"
        :autoPlay="false"
        ref="approveVideoRef"
        />
    </div>
    <div class="chapters-video" v-else>
      <Video
        class="video-box"
        ref="videoRef"
        v-if="(videoInfo.src || videoInfo.content_id) && videoInfo.status === 13 && (videoInfo.duration || (estDur && estDur !== '0'))"
        :source_src.sync="videoInfo.src"
        :content_id.sync="videoInfo.content_id"
        :autoPlay="autoPlay"
        :playTime="playTime"
        :needCapture="true"
        @getCaptureFile="getCaptureFile"
      />
      <!-- 转码中 -->
      <div v-else class="trans-coding">
        <img :src='require("@/assets/mooc-img/transcoding.png")' alt="">
        <div class="drans-coding-msg">素材尚未转码完成</div>
        <div class="warning-msg">请在视频完成转码后，再配置分段章节信息</div>
      </div>
    </div>
    <!-- <span class="chapters-msg">{{ videoInfo.msg }}</span> -->
    <div class="tips" v-show="videoInfo.process_duration > 0">
      预计生成时长：{{ transforTime(videoInfo.process_duration) }}
    </div>
    <div id="output"></div>
  </div>
</template>

<script>
import { Video } from '@/components/index'
import approveVideo from './approve-video.vue'
import { transforTime } from 'utils/tools'
import env from 'config/env.conf.js'

export default {
  name: 'demoPreview',
  components: {
    Video,
    approveVideo
  },
  props: {
    videoInfo: {
      type: Object,
      default: () => {
        return {
          duration: 0,
          process_duration: 0,
          request_id: '',
          src: '',
          msg: '',
          content_id: '',
          status: 0
        }
      }
    },
    approveStatus: {
      type: Boolean,
      default: false
    },
    autoPlay: {
      type: Boolean,
      default: false
    },
    // 设置播放时间
    playTime: {
      type: Number,
      default: 0
    },
    estDur: [Number, String]
  },
  data() {
    return {
      transforTime,
      imageUrl: ''
    }
  },
  watch: {
  },
  methods: {
    pause() {
      if (this.approveStatus) {
        this.$refs.approveVideoRef && this.$refs.approveVideoRef.pause()
      } else {
        this.$refs.videoRef && this.$refs.videoRef.pause()
      }
    },
    getCaptureFile(file) {
      // 拿到截屏文件后 上传到内容中心
      this.upload(file)
    },
    // 截屏封面上传
    upload(file) {
      this.$sdc.loading('图片上传中')
      let that = this
      /* eslint-disable*/
      let authUrl = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_PORTAL_HOST_WOA : process.env.VUE_APP_PORTAL_HOST
      new contentCenter.uploadFile({
        file: file,
        type: 0, // 0表示上传图片，1视频 2音频 3文档
        appId: 'QLearningService',
        operateAuthUrl:`${authUrl}/training-portal-common/api/v1/portal/user/common/uploadOperateSignature`,
        // isPublic: true,
        isPublic: false,
        onSuccess(res) {
          that.$sdc.loading.hide()
          that.$message.success('图片上传成功')
          // console.log(res, '上传图片返回的res------')
          // that.handleImg(res[0].content_id)
          if (res && res[0]) {
            if (res[0].file_url) {
              that.imageUrl = res[0].file_url
            } else if (res[0].content_id) {
              const envName = env[process.env.NODE_ENV]
              that.imageUrl = `${envName.contentcenter}content-center/api/v1/content/imgage/${res[0].content_id}/preview`
            }
            // that.upAgainUrl = that.imageUrl
            // 拿到url
            that.$emit('getImageUrl', that.imageUrl)
          }
        },
        onError(err) {
          that.$sdc.loading.hide()
          that.$message.error(err)
        },
        onProgress(info) {
          const percent = parseInt(info.percent * 10000) / 100
          if (percent >= 100) {
            setTimeout(() => {
              // that.$refs['upload'].clearFiles()
            }, 2000);
          }  
        }
      })
      /* eslint-disable*/
    },
  }
}
</script>

<style lang="less" scoped>
.chapters-preview {
  .chapters-video {
    display: inline-block;
    padding: 19px 20px;
    border-radius: 3px;
    background-color: #F8F8F8;
  }
  .video-box {
    width: 324px;
    height: 182px;
    border-radius: 3px;
  }
  .trans-coding {
    width: 306px;
    height: 172px;
    text-align: center;
    font-family: "PingFang SC";
    font-size: 14px;
    font-weight: 400;
    img {
      margin-top: 5px;
      width: 100px;
      height: 100px;
    }
    .drans-coding-msg {
      margin-top: 16px;
      color: #000000e6;
      line-height: 22px;
    }
    .warning-msg {
      margin-top: 4px;
      color: #00000066;
      line-height: 22px;
    }
  }
  .chapters-msg {
    position: relative;
    bottom: 4px;
    margin-left: 20px;
    color: #d63535;
    font-size: 14px;
    line-height: 22px;
  }
  .tips {
    margin-top: 5px;
    color: #d63535;
  }
}
</style>
