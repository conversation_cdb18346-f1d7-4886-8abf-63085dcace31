<template>
  <div class="propertySet-page">
    <div class="title">
      <p>图文属性设置</p>
      <p></p>
    </div>
    <div class="propertySet-content">
      <el-form :model="form" :rules="rules" label-position="right" ref="form">
        <div class="base-set">
          <p class="base-set-title">基础信息</p>
          <el-form-item label="文章类型" prop="content_type" class="special-class is-required">
           <el-cascader
            :disabled="[4, 5].includes(Number(form.content_type)) || approveStatus"
            placeholder="请选择" 
            v-model="form.content_type" 
            :options="contentTypeOption"
            :props="contentTypeProps" 
            @change="handleContentChange"
            >
            </el-cascader>
            <p v-if="!approveStatus" class="test-tips"><i class="el-icon-warning color-red mgr-5"></i>若需<span class="color-red">「课程测试」</span>体验效果，请先<span class="color-red text-undeline" @click="toInstructionsPage">查看填写说明</span>，以免耽误进度哦！</p>
          </el-form-item>
          <el-form-item v-if="form.content_type == 3" class="is-required" label="相关内容">
            <span class="related-content" @click="showRelatedContentHandle"
              v-if="!(form.relation_content && form.relation_content.content_module_id) && !approveStatus"><i>+</i>关联内容</span>
            <div class="related-content-item" v-if="form.relation_content && form.relation_content.content_module_id">
              <span :class="['tags', getModuleClass(form.relation_content.content_module_id)]">{{
                form.relation_content.content_module_name }}</span>
              <span class="">{{ form.relation_content.content_name }}</span>
              <i class="el-icon-error" @click="delRelatedItem" v-if="!approveStatus"></i>
            </div>
          </el-form-item>
          <el-form-item v-if="form.content_type == 1" class="from-url special-class is-required" label="原文链接"
            prop="from_url">
            <el-input v-model="form.from_url" placeholder="请输入原文链接" clearable :disabled="approveStatus"></el-input>
          </el-form-item>

          <!-- <el-form-item label="分类" prop="classify_id" class="special-class is-required">
            <el-cascader v-model="form.classify_id" :options="classifyList" :props="classifyProps" collapse-tags
              clearable>
            </el-cascader>
          </el-form-item> -->
          <el-form-item class="labels-box is-required" :class="{'labels-add-disabled': approveStatus}" label="内容标签" prop="label_list">
                  <sdc-addlabel v-model="form.label_list" class="project-tag-box" :recommend="{
                  title: this.formData.graphic_name,
                  desc: this.formData.new_graphic_text
                }" 
                :labelNodeEnv="labelNodeEnv"
                @getSelectedLabelList="getSelectedLabelList" 
                />
          </el-form-item>
          <!-- <el-form-item class="labels-box is-required" label="标签" prop="labels" >
                  <sdc-addlabel v-model="form.label_list" class="project-tag-box" :recommend="{
                  title: this.formData.graphic_name,
                  desc: this.$parent.$refs['editor'].getContent()
                }" 
                :labelNodeEnv="labelNodeEnv"
                @getSelectedLabelList="getSelectedLabelList" 
                />
          </el-form-item> -->
           <!-- <el-form-item class="labels-box is-required" label="标签" prop="labels"> -->
          <!--<el-form-item class="labels-box is-required" label="标签" prop="labels">
            <div class="label-special-class">
              <el-select v-model="form.labels" allow-create default-first-option multiple filterable remote
                :multiple-limit="5" size="mini" @focus="labelFocus" placeholder="请输入关键词" :remote-method="remoteSearchTag"
                :loading="tagSearchform.loading">
                <el-option v-for="item in tagSearchform.tagList" :key="item.lable_id" :label="item.name"
                  :value="item.name">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
          <div class="recommend-tag-box">
            <p class="title">推荐标签：</p>
            <div class="recommend-tag">
              <el-tag v-for="item in recommendLabelList" :key="item" @click="addRecommendLabel(item)"
                :class="[form.labels.findIndex((e) => e === item) >= 0 ? 'el-tag--info' : '']">{{ item
                }}</el-tag>
            </div>
          </div> -->
          <el-form-item label="运营标题" class="customer-form-item">
            <el-input
              v-model.trim="form.course_statement.operation_title"
              :disabled="approveStatus"
              placeholder="后续宣推中将优先使用该运营标题，学习界面则显示原内容标题"
              clearable>
            </el-input>
            <span class="custom-el-input-count">{{handleValidor(form.course_statement.operation_title, 50, '1')}}/50</span>
          </el-form-item>
          <el-form-item class="cut-img-upload is-required" label="文章封面">
            <img v-if="approveStatus" width="200px" height="125px" style="object-fit: cover" :src="form.is_self_upload === 0 ? form.cover_image : form.cover_image_id" class="avatar" />
            <cut-img-upload v-else ref="upload" @handleSuccess="handleSuccessImage" :dialogImageUrl="form.cover_image"
              :autoImgUrl="form.cover_image_id" @handleClearImg="handleClearImg" @handleImgEdit="handleImgEdit"
              :cover_imgage_storage_type="form.cover_imgage_storage_type">
              <template v-slot:text>
                <p>建议图片尺寸：360*240px或3:2</p>
              </template>
              <template v-slot:createImg>
                <p class="text-orange" style="display: flex; align-items: center" @click="handleAutoImg"><img class="icon" style="width:16px; height: 16px; margin-right: 4px" src="~@/assets/img/tips.png" alt="">快速生成封面图</p>
              </template>
            </cut-img-upload>
          </el-form-item>
        </div>

        <div class="base-set" v-if="isSupperAdmin">
          <p class="base-set-title">来源信息</p>
          <el-form-item class="parent-content" label="父内容ID">
            <div class="parent-content-id" :class="{'mgb-10': showParentId && ([null, undefined, ''].includes(form.course_statement.parent_content_act_type) || !form.course_statement.parent_content_id)}">
              <div class="content-type">
                <el-select v-model="form.course_statement.parent_content_act_type" :disabled="approveStatus" placeholder="请选择内容类型" clearable>
                  <el-option
                    v-for="item in parent_content_act_type_options"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
                <span class="error-tip-text-customer" v-show="showParentId && [null, undefined, ''].includes(form.course_statement.parent_content_act_type)">请选择内容类型</span>
              </div>
              <div class="content-id">
                <el-input v-model.trim="form.course_statement.parent_content_id" :disabled="approveStatus" placeholder="请输入内容ID" clearable></el-input>
                <span class="error-tip-text-customer" v-show="showParentId && !form.course_statement.parent_content_id">请输入内容ID</span>
              </div>
            </div>
            <span class="get-content-id" @click="toGetContentId" v-if="!approveStatus">如何获取内容ID</span>
          </el-form-item>

          <el-form-item class="creation-source" label="创作来源" prop="course_statement.creation_source">
            <el-radio-group class="creation-source-radio" v-model="form.course_statement.creation_source" :disabled="approveStatus || specialFileDisabled" @change="creationSourceChange">
              <el-radio :label="item.value" v-for="item in creation_source_Options" :key="item.value">{{item.label}}</el-radio>
            </el-radio-group>
            <div class="contact-person contact-person-radio" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
            <div class="origin-warm" v-if="form.course_statement.creation_source === 0">
              <p>(1)「创作组织」从拟定内容主题、提纲、访谈、收集素材到制作课件，一条龙完成。</p>
              <p>(2)内容创作人所属的最小组织单元，优先填“组”。</p>
            </div>
            <div class="origin-warm" v-if="form.course_statement.creation_source === 3">
                符合以下任一的共创模式：
                <p>（1）「创作组织」拟定主题、提供提纲和素材、组织立项，邀约创作者，由创作者主导内容生产，「创作组织」参与辅助和赋能。创作者拟定主题、提供提纲和素材。</p>
                （2）「创作组织」辅助立项，由创作者主导内容制作，「创作组织」参与辅助和赋能。
            </div>

            <div class="creation_source_sub_content" v-if="[0, 1, 3].includes(form.course_statement.creation_source)">
              <!-- PGC -->
              <div v-if="form.course_statement.creation_source === 0" key="PGC">
                <el-form-item class="mgb-0" label="创作组织" :rules="pgcCreationOrgRules" prop="course_statement.pgc_creation_org" label-width="85px" key="pgc_creation_org">
                  <sdc-unit-selector
                  class="dep-selector"
                  ref="pgcCreationOrgRef"
                  v-model="form.course_statement.pgc_creation_org"
                  :disabled="approveStatus || specialFileDisabled"
                  multiple
                  @change="validateField($event, 'pgc_creation_org')"
                  placeholder="请选择分享人所属的最小组织单元，如组、中心、部门"
                  />
                  <div class="contact-person" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
                </el-form-item>
                <!-- <el-form-item class="mgb-0" label="联合创建组织" label-width="85px" style="margin-bottom: 0px">
                  <sdc-unit-selector
                    class="dep-selector" 
                    ref="pgcJointCreationRef"
                    v-model="form.course_statement.pgc_joint_creation"
                    :disabled="approveStatus"
                    multiple
                    placeholder="请选择联合创建组织"
                    @change="validateField($event, 'pgc_joint_creation')"
                    />
                </el-form-item> -->
              </div>
              <!-- PUGC -->
              <div v-if="form.course_statement.creation_source === 3" key="PUGC">
                <el-form-item label="创作组织" :rules="pugcCreationOrgRules" prop="course_statement.pugc_creation_org" label-width="85px" key="pugc_creation_org">
                  <sdc-unit-selector
                  class="dep-selector"
                  ref="pugcCreationOrgRef"
                  v-model="form.course_statement.pugc_creation_org"
                  :disabled="approveStatus || specialFileDisabled"
                  multiple
                  @change="validateField($event, 'pugc_creation_org')"
                  placeholder="请选择分享人所属的最小组织单元，如组、中心、部门"
                  />
                  <div class="contact-person" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
                </el-form-item>
                <el-form-item class="mgb-0" label="联合创建组织" label-width="85px" style="margin-bottom: 0px">
                  <sdc-unit-selector
                    class="dep-selector" 
                    ref="pugcJointCreationRef"
                    v-model="form.course_statement.pugc_joint_creation"
                    :disabled="approveStatus || specialFileDisabled"
                    multiple
                    placeholder="请选择联合创建组织"
                    @change="validateField($event, 'pugc_joint_creation')"
                    />
                    <div class="contact-person" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
                </el-form-item>
              </div>
              <!-- OGC -->
              <div class="OGC-content" v-if="form.course_statement.creation_source === 1" key="OGC">
                <el-form-item label="供应商名称" :rules="supplierNameRules" prop="course_statement.ogc_supplier_name" label-width="100px" class="course-texTarea-input">
                  <el-input 
                    v-model.trim="form.course_statement.ogc_supplier_name" 
                    :disabled="approveStatus"
                    placeholder="请填写供应商公司全称或个人全名"
                    clearable>
                  </el-input>
                  <span class="custom-el-input-count">{{handleValidor(form.course_statement.ogc_supplier_name, 100, '2')}}/100</span>
                </el-form-item>
                <el-form-item class="ogc-purchase-org" label="采购组织" :rules="purchaseOrgRules" prop="course_statement.ogc_purchase_org" label-width="100px" key="ogc_purchase_org">
                  <sdc-unit-selector
                    class="dep-selector" 
                    ref="purchaseOrgRef"
                    v-model="form.course_statement.ogc_purchase_org"
                    :disabled="approveStatus || specialFileDisabled"
                    multiple
                    @change="validateField($event, 'ogc_purchase_org')"
                    placeholder="请选择采购组织"
                    />
                    <div class="contact-person" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
                </el-form-item>
                <el-form-item class="ogc-purchase-type" label="采购方式" label-width="100px" prop="course_statement.ogc_purchase_type">
                  <el-radio-group v-model="form.course_statement.ogc_purchase_type" :disabled="approveStatus">
                    <el-radio :label="item.value" v-for="item in purchase_type_Options" :key="item.value">{{item.label}}</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="外部讲师" label-width="100px">
                    <el-input v-model="form.course_statement.ogc_out_teachers" :disabled="approveStatus" placeholder="请输入外部人员名称，用多个；隔开" clearable></el-input>
                </el-form-item>
              <el-form-item label="采购成本" prop="course_statement.ogc_purchase_amount" label-width="100px" key="ogc_purchase_amount">
                <el-input-number class="ogc_purchase_amount" v-model="form.course_statement.ogc_purchase_amount" :disabled="approveStatus" label="请输入采购成本"></el-input-number> 元
              </el-form-item>
              </div>
            </div>
          </el-form-item>
        </div>

        <div class="more-set">
          <div class="more-set-title">
            <p class="base-set-title">运营信息</p>
            <div @click="moreSetOpen = moreSetOpen ? false : true"><span class="open-btn">{{ moreSetOpen ? '收起'
              : '展开' }}</span> <i :class="[moreSetOpen ? 'el-icon-arrow-up' : 'el-icon-arrow-down']"></i>
            </div>
          </div>
          <div v-show="moreSetOpen" class="more-set-body">
            <el-form-item v-if="isSupperAdmin" label="人力成本" prop="course_statement.human_cost" key="human_cost">
              <el-input-number class="human_cost" v-model="form.course_statement.human_cost" :disabled="approveStatus" label="请输入人力成本"></el-input-number> 人天
            </el-form-item>
          
            <el-form-item label="内容创作者" prop="authors" class="is-required label-two-line">
              <sdc-staff-selector multiple v-model="form.authors" @change="changeAuth" ref="authSelector" :disabled="approveStatus"
                size="small"></sdc-staff-selector>
            </el-form-item>
            <el-form-item label="内容管理员" class="is-required label-two-line">
              <sdc-staff-selector multiple v-model="form.administrators" @change="changeAdm" ref="adminSelector" :disabled="approveStatus"
                size="small"></sdc-staff-selector>
              <p class="tip tip1">作者和管理员均可进行编辑、删除操作，请谨慎配置</p>
            </el-form-item>
            <el-form-item label="内容管理组织" prop="dept_id" class="label-two-line">
              <sdc-unit-selector ref="deptSelectorRef" v-model="form.dept_id" size="small" @change='handleUnitSelect' placeholder="请选择管理员所属的最小组织单元，如组、中心、部门"
                :props="deptProps" :disabled="approveStatus || specialFileDisabled" />
                <div style="margin-left: 85px" class="contact-person" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
            </el-form-item>
            <el-form-item class="isOpen is-required" label="内容访问权限" label-width="100px" prop="is_open" >
              <div style="margin-top: 9px;display: flex;align-items: flex-end;">
                  <el-radio-group v-model="form.is_open" :disabled="approveStatus">
                    <el-radio :label="0" style="">仅对集团正式员工开放</el-radio>
                    <el-radio :label="2">仅对集团正式员工、毕业生、实习生开放</el-radio>
                    <el-radio :label="1" style="margin-right: 10px;">自定义开放范围</el-radio>
                  </el-radio-group>
                  <div v-show="form.is_open == 1" style="margin-left: -30px;">
                    <AudienceSelector v-show="form.is_open == 1" :showComfirm="!approveStatus" :audience="true" :showTab="['unit', 'group', 'import']" multiple
                    :env="audienceEnv" importNumber='1000' :createStudentID="true" :isShowCount="false"
                    v-model="form.target_list" @changeRule="changeStaffAuth" appCode="qlearning" />
                  </div>
              </div>
            </el-form-item>

            <el-form-item v-if="isSupperAdmin" class="is_required" label="是否必修">
              <el-radio-group v-model="form.course_statement.is_required" :disabled="approveStatus || specialFileDisabled">
                <el-radio :label="false">否，全员选修</el-radio>
                <el-radio :label="true">指定人群必修或全员必修</el-radio>
              </el-radio-group>
              <div class="contact-person contact-person-radio" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
            </el-form-item>
            <el-form-item v-if="isSupperAdmin" label="认证等级">
              <el-radio-group v-model="form.course_statement.certification_level" :disabled="approveStatus">
                <el-radio v-for="item in levelList" :key="item.code" :label="item.code" :disabled="item.disabled">{{ item.name }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="isSupperAdmin" label="运营分级">
              <el-radio-group v-model="form.course_statement.operation_level" :disabled="approveStatus">
                <el-radio v-for="item in operation_type_options" :key="item.value" :label="item.value" @change="changeOperationType(item)">{{ item.label }}</el-radio>
              </el-radio-group>
              <div class="operation_level_sub_content" v-if="operation_project_name_options.length">
                <el-form-item label="分级项目" label-width="85px">
                  <el-select v-model="form.course_statement.operation_project_name" :disabled="approveStatus" placeholder="请选择分级项目" clearable>
                    <el-option
                      v-for="item in operation_project_name_options"
                      :key="item.item_name"
                      :label="item.item_name"
                      :value="item.item_name">
                    </el-option>
                  </el-select>
                </el-form-item>
              </div>
            </el-form-item>
            <el-form-item v-if="isSupperAdmin" label="内容专家评分" prop="course_statement.expert_score" class="expert-rating" label-width="100px">
              <el-input class="expert-rating-input"
                type="number"
                v-model="form.course_statement.expert_score" 
                :disabled="approveStatus"
                placeholder="请输入评估分数"
                clearable>
              </el-input>
              <div class="tip-warm">请输入0-100的数字，可填写两位小数</div>
            </el-form-item>
            <el-form-item v-if="isSupperAdmin" label="内容用户评分" class="expert-rating" label-width="100px">
              {{ form.course_statement.user_score ? `${resolveScore(form.course_statement.user_score) }分` : '-'}}
              <span class="tip-warm">自动拉取用户评价分</span>
            </el-form-item>
            <template v-if="isSupperAdmin">
              <el-form-item class="join_recommend" label="是否加入推荐流" label-width="100px">
                <el-radio-group v-model="form.course_statement.join_recommend" :disabled="approveStatus || specialFileDisabled">
                  <el-radio :label="false">否，不加入推荐池</el-radio>
                  <el-radio :label="true">是，加入推荐池</el-radio>
                </el-radio-group>
              </el-form-item>
              <div class="contact-person contact-person-radio" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
              <div class="red-tips-customer"><i class="el-icon-warning mgr-5"></i>合规类、应知应会类、应学类课程等请勿选择加入推荐流。</div>
            </template>

            <el-form-item v-if="isSupperAdmin" label="展示课程推荐" class="is-show-recommend" label-width="100px">
              <el-checkbox v-model="form.is_show_recommend" :disabled="approveStatus">是</el-checkbox>
              <div class="tip-warm mgl-15">（预计访问量高时，提前关闭该选项，提升页面加载速度）</div>
            </el-form-item>
            <el-form-item label="是否同步给小Q同学" class="is-required" prop="ai_sync_flag" v-if="isSuperAdmin">
              <el-radio-group v-model="form.ai_sync_flag" :disabled="specialFileDisabled">
                <el-radio :label="1">同步
                  <el-tooltip class="item" effect="dark" content="数据同步给小Q同学知识库，权限与本页面设置相同" placement="bottom-start">
                    <i class="el-icon-warning-outline async-icon"></i>
                  </el-tooltip>
                </el-radio>
                <el-radio :label="0">不同步</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="数据有效时间" class="is-required" prop="ai_expire_type" v-if="isSuperAdmin && form.ai_sync_flag === 1">
              <el-radio-group v-model="form.ai_expire_type" :disabled="specialFileDisabled">
                <el-radio :label="1">长期</el-radio>
                <el-radio :label="2">自定义到期时间
                  <el-tooltip class="item" effect="dark" content="到期后，数据将在小Q同学知识库不可使用" placement="bottom-start">
                    <i class="el-icon-warning-outline async-icon"></i>
                  </el-tooltip>
                </el-radio>
              </el-radio-group>
              <div v-if="form.ai_expire_type === 2" class="trim-content">
                <el-date-picker 
                  size="mini"
                  v-model="form.ai_expire_end_time"
                  type="datetime" 
                  value-format="yyyy-MM-dd HH:mm:ss"
                  default-time="00:00:00" 
                  @change="setExpireTime"
                >
                </el-date-picker>
              </div>
            </el-form-item>
            <el-form-item label="备注" class="graphic_remark">
              <el-input style="margin-top: 9px;"
                type="textarea"
                :rows="5"
                placeholder="请输入备注"
                v-model="form.graphic_remark"
                :disabled="approveStatus"
              >
              </el-input>
            </el-form-item>

            <el-form-item v-if="isSupperAdmin" label="添加到课单" class="add-to-course">
              <span class="course-num">已添加到{{ formData?.graphic_status === 4 ? cl_ids.length : cl_ids_count
              }}个课单</span><el-link v-if="!approveStatus" @click="handleAddCourseDialogShow" type="primary"
                :underline="false">点击此处修改或新增</el-link>
            </el-form-item>
            <!-- 延伸学习 -->
            <div class="extand-box">
              <div class="extand-title">
                <span class="title">延伸学习</span>
                <p class="tip">此处关联的内容将在图文详情页中展示</p> <span v-if="!approveStatus" class="addExtandItem" @click="showAddExtandLearn = true">+ 新增内容</span>
              </div>
              <div class="extand-table">
                <el-table :data="tableData.list" max-height="216px" style="width: 100%">
                  <el-table-column prop="content_name" label="内容标题" width="306" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <span
                        :class="['tags', getModuleClass(scope.row.content_module_id)]">{{ scope.row.content_module_name }}</span><span>{{ scope.row.content_name }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" v-if="!approveStatus">
                    <template slot-scope="scope">
                      <div class="icon-btns">
                        <i class="icon-up" @click="handleUp(scope.row, scope.$index)"></i>
                        <i class="icon-delete" @click="handleDelete(scope.row, scope.$index)"></i>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <!-- 一键生成封面图弹窗组件 -->
    <sdc-img-cover ref="sdcImgCoverRef" :visible.sync="autoImgCoverShow" :imgInfo="imgInfo"
      @handleImgCoverOk="handleImgCoverOk">
    </sdc-img-cover>
    <AddCourseDialog :visible.sync="addCourseDialogShow" :itemData.sync="addCourseDialogData"
      @addedHandle="getCourseCount" />
    <AddCourseSelfDialog :visible.sync="addCourseDialogShowSelf" :itemData.sync="addCourseDialogData" :cl_ids="cl_ids"
      @addedHandle="getCheckedClIds" />
    <AddExtandLearnDialog v-if="showRelatedContent" title="关联内容" :checkedData="form.relation_content" page="graphic"
      :prodData.sync="prodData" :showFirst="false" @closeAddExDialog="closeAddExDialog" />
    <!-- 延伸学习 -->
    <AddExtandLearnDialog v-if="showAddExtandLearn" :prodData.sync="prodData" @closeAddExDialog="getExtandData" page="graphic" ref="extandLearn" />
    <!-- 转载--原创弹窗说明 -->
    <copyright v-if="copyrightShow" :visible.sync="copyrightShow" :typeOriginal="typeOriginal" @handleCheck="handleCheck">
    </copyright>
    <!-- 特殊字段的修改弹窗 -->
    <el-dialog
      class="update-dialog"
      title="提示"
      :visible.sync="showDialog"
      width="600px"
      :close-on-click-modal="false"
      :before-close="handleClose">
      <div class="update-content-box">
        <span class="dialog-label">修改内容:</span>
        <el-input class="update-text-input"
          v-model="updateText" 
          placeholder="请输入修改的内容"
          clearable>
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleOk">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { AudienceSelector } from '@tencent/sdc-audience'
import { CutImgUpload, AddCourseDialog, AddCourseSelfDialog, AddExtandLearnDialog } from '@/components/index'
import { getNetCourseClassify, getTagList, getContentAddedCount, getRecommendLabels, getOperationApi, queryExtendContentPage } from 'config/api.conf'
import { qlearningModuleTypes } from 'utils/constant'
import { throttle } from 'utils/tools'
import copyright from '@/views/user/components/copyright.vue'
import { mapState } from 'vuex'
let allTarget = 2015587
const studentTarget = '2030108'
const EXP = /^\s*(-?(\d+(\.\d{0,2})?|\.\d{1,2}))\s*$/

export default {
  components: {
    CutImgUpload,
    AddCourseDialog,
    AddCourseSelfDialog,
    AudienceSelector,
    AddExtandLearnDialog,
    copyright
  },
  props: {
    formData: {
      type: Object
    },
    visitorCheck: {
      type: Boolean
    },
    barkInfo: { // 后端拿到的文章数据的备份
      type: Object,
      default: () => {}
    },
    approveStatus: { // 审批状态
      type: Boolean,
      default: false
    }
  },
  data() {
    const validAmount = (rule, value, callback) => {
      if ((value && (value < 0 || !EXP.test(value))) || value === 0) {
        return callback(new Error('采购成本需大于0, 至多可填写两位小数'))
      } else {
        callback()
      }
    }
    const validHumanCost = (rule, value, callback) => {
      if (!this.approveStatus && ((value && (value <= 0 || !EXP.test(value))) || value === 0)) {
        return callback(new Error('人力成本需大于0, 至多可填写两位小数'))
      } else {
        callback()
      }
    }
    const validExpertRating = (rule, value, callback) => {
      if (value && (value < 0 || value > 100 || !EXP.test(value))) {
        return callback(new Error('输入的值要大于等于0小于等于100, 至多可填写两位小数'))
      } else {
        callback()
      }
    }
    const validExpireType = (rule, value, callback) => {
      if (!value || (value === 2 && !this.form.ai_expire_end_time)) {
        return callback(new Error('请选择数据有效时间'))
      } else {
        callback()
      }
    }
    return {
      showDialog: false, // 是否显示特殊字段弹窗
      updateText: '', // 修改的内容
      bakArg: {}, // 父组件传递的参数
      showAddExtandLearn: false,
      parent_content_act_type_options: [
        { label: '面授课', value: 1 },
        { label: '活动', value: 4 },
        { label: '网络课', value: 2 },
        { label: 'MOOC', value: 11 },
        { label: 'SPOC', value: 27 },
        { label: '直播', value: 5 },
        { label: '文章', value: 18 },
        { label: '案例', value: 16 },
        { label: '课单', value: 15 },
        { label: '行家', value: 19 }
      ],
      creation_source_Options: [
        { label: '培训or业务团队独立开发(PGC)', value: 0 },
        { label: '培训团队联合业务作者合作创作（PUGC）', value: 3 },
        { label: '员工自发原创（UGC）', value: 2 },
        { label: '外部引入（OGC）', value: 1 }
      ],
      purchase_type_Options: [
        { label: '个人按需购买', value: 0 },
        { label: '公司统一采购', value: 1 },
        { label: '账号采购', value: 2 }
      ],
      courseLevelList: [
        { code: 0, name: '无', disabled: false }, 
        { code: 1, name: '公司级', disabled: false }, 
        { code: 2, name: 'BG级', disabled: false },
        { code: 3, name: '部门级', disabled: false }
      ],
      // 分级目录选项
      operation_project_name_options: [],
      // 分级目录集合
      operation_project_name_options_map: {},
      innerProps: { // 内部成员
        staffID: 'teacher_id',
        staffName: 'teacher_name'
      },
      deptProps: { // 内容管理组织
        UnitFullName: 'dept_name',
        unitID: 'dept_id'
      },
      contentTypeOption: [
        {
          label: '原创',
          value: '88',
          children: [
            { label: '原创文章', value: '0' }, // 要传给后台0
            { label: '原创笔记', value: '3' }
          ]
        },
        { label: '转载', value: '1' },
        { label: '活动推广', value: '2' },
        { label: 'AI创建', value: '4', disabled: true },
        { label: '人工运营', value: '5', disabled: true }
      ],
      contentTypeProps: {
        emitPath: false
      },
      classifyList: [],
      classifyProps: {
        multiple: true,
        label: 'item_name',
        value: 'item_id',
        children: 'child',
        checkStrictly: true,
        emitPath: false
      },
      tagSearchform: {
        tagList: [],
        loading: false
      },
      form: {
        content_type: '',
        from_url: '',
        classify_id: [],
        label_list: [],
        is_self_upload: 0, // 0：自己上传 1：智能生成
        cover_image: '', // 自己上传返回的图片id
        cover_image_id: '', // 智慧生成返回的图片id
        is_open: 0, // 0全部 1部分
        target_list: '', // 目标学员
        authors: [], // 内容开发人
        administrators: [],
        cl_id: '', // 选中的课单id
        relation_content: null,
        dept_id: '', // 内容管理组织id
        dept_name: '', // 内容管理组织名
        is_show_recommend: true, // 展示课程推荐
        graphic_remark: '', // 备注
        ai_sync_flag: 0, // 是否同步给小Q同学 0-不同步 1-同步
        ai_expire_type: 1, // 数据有效时间 1-长期 2-自定义到期时间
        ai_expire_end_time: '', // 自定义到期时间
        course_statement: {
          operation_title: '', // 运营标题
          parent_content_act_type: null, // 父内容类型
          parent_content_id: '', // 父内容ID
          creation_source: 0, // 创作来源
          pgc_creation_org: [], // PGC创作组织
          // pgc_joint_creation: [], // PGC联合创作组织
          pugc_creation_org: [], // PUGC创作组织
          pugc_joint_creation: [], // PUGC联合创作组织
          ogc_supplier_name: '', // 供应商名称
          ogc_purchase_org: [], // 采购组织
          ogc_out_teachers: '', // 外部讲师
          ogc_purchase_type: null, // 采购方式
          ogc_purchase_amount: undefined, // 采购成本
          human_cost: undefined, // 人力成本
          is_required: false, // 是否纳入应学
          certification_level: 0, // 认证等级
          operation_level: 3, // 运营分级
          operation_project_name: '', // 分级项目
          expert_score: null, // 内容专家评分
          user_score: 0, // 内容用户评分
          join_recommend: true // 是否加入推荐流
        }
      },
      recommendLabelList: [],
      autoImgCoverShow: false,
      imgInfo: {},
      coverImgBackData: {}, // 一键封面数据回传
      moreSetOpen: true,
      addCourseDialogShow: false,
      addCourseDialogShowSelf: false,
      addCourseDialogData: {
        module_id: 8,
        module_name: '图文'
      },
      cl_ids: [],
      cl_ids_count: 0,
      classify_full_name: [],
      audienceEnv: process.env.NODE_ENV,
      showRelatedContent: false,
      domAttrDelTimer: null,
      prodData: {
        prod_id: this.$route.query.graphic_id || sessionStorage.getItem('graphic_id'),
        prod_type: 8
      },
      copyrightShow: false,
      typeOriginal: -1,
      tableData: {
        list: JSON.parse(sessionStorage.getItem('extend_contents')) || [],
        page_no: 1,
        page_size: 6,
        total: 0
      },
      rules: {
        'course_statement.creation_source': [{ required: true, message: '请选择创作来源', trigger: 'change' }],
        'course_statement.ogc_purchase_type': [{ required: true, message: '请选择采购方式', trigger: ['change'] }],
        'course_statement.ogc_purchase_amount': [{ validator: validAmount, trigger: ['blur', 'change'] }],
        dept_id: [{ required: true, message: '请选择内容管理组织', trigger: 'blur' }],
        'course_statement.human_cost': [{ validator: validHumanCost, trigger: ['blur', 'change'] }],
        'course_statement.expert_score': [{ validator: validExpertRating, trigger: ['blur', 'change'] }],
        ai_sync_flag: [{ required: true, message: '是否同步给小Q同学', trigger: 'change' }],
        ai_expire_type: [{ validator: validExpireType, required: true, message: '请选择数据有效时间', trigger: 'change' }]
      }
    }
  },
  watch: {
    form: {
      handler() {
        this.handlerFormData()
      },
      deep: true
    },
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          this.initAuthorsAndManager(val)
        }
      },
      immediate: true
    },
    '$store.state.userDepInfo': {
      handler(val) {
        // 默认拉取内容管理组织 兼容旧数据
        this.$nextTick(() => {
          this.initDepart(val)
        })
      }
    },
    visitorCheck(val) {
      if (val) {
        this.getClassifyList()
        this.domAttrDelTimer = setInterval(() => {
          this.handlerDomAttrDel()
        }, 1000)
      }
    },
    // 封面图、标签任何一个发生改变
    isOtherChanged(val) {
      this.$emit('filesChanged', val)
    },
    // 封面图
    isCoverImageChanged(val) {
      this.$emit('imgChange', val)
    }
  },
  computed: {
    ...mapState(['userLimitInfo']),
    // 封面图
    isCoverImageChanged() {
      let cover_image = this.barkInfo.is_self_upload === 0 ? this.barkInfo.cover_image_id : ''
      let cover_image_id = this.barkInfo.is_self_upload === 1 ? this.barkInfo.cover_image_id : ''
      if (this.form.cover_image !== cover_image || this.form.cover_image_id !== cover_image_id) {
        return true
      } else {
        return false
      }
    },
    // 标签改变
    isLabelChanged() {
      let isSame = this.form.label_list.every(item => this.barkLabelIds.includes(item.label_id))
      if (this.form.label_list.length !== this.barkLabelIds.length || !isSame) {
        return true
      } else {
        return false
      }
    },
    // 封面图、标签任何一个发生改变
    isOtherChanged() {
      return this.isCoverImageChanged
    },
    graphic_id() {
      return this.formData.graphic_id || this.$route.query.graphic_id || sessionStorage.getItem('graphic_id')
    },
    labelNodeEnv() {
      return process.env.NODE_ENV === 'production' ? 'production' : 'test'
    },
    showParentId() {
      return ![null, undefined, ''].includes(this.form?.course_statement?.parent_content_act_type) || this.form?.course_statement?.parent_content_id
    },
    // PGC创作组织
    pgcCreationOrgRules() {
      return { required: this.form.course_statement.creation_source === 0, trigger: 'blur', validator: this.validPgcCreationOrg }
    },
    // PUGC创作组织
    pugcCreationOrgRules() {
      return { required: this.form.course_statement.creation_source === 3, trigger: 'blur', validator: this.validPugcCreationOrg }
    },
    supplierNameRules() {
      return { required: this.form.course_statement.creation_source === 1, message: '请输入供应商名称', trigger: 'blur' }
    },
    purchaseOrgRules() {
      return { required: this.form.course_statement.creation_source === 1, message: '请选择采购组织', trigger: 'blur' }
    },
    // 是否是 超管、公司级管理员、BG级、部门级、大类经理
    isSupperAdmin() {
      let { supper_admin, mooc_company_admin, mooc_bgadmin, mooc_dept_admin, admin } = this.userLimitInfo
      return supper_admin || mooc_company_admin || mooc_bgadmin || mooc_dept_admin || admin
    },
    // 是否是超管
    isSuperAdmin() {
      let { supper_admin } = this.userLimitInfo
      return supper_admin
    },
    // 角色权限
    levelList() {
      // let { supper_admin, mooc_bgadmin, mooc_company_admin, mooc_dept_admin } = this.userLimitInfo
      const id = this.$route.query.graphic_id
      if (!id) {
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.form.course_statement.certification_level = 0
      }
      // else if (supper_admin || mooc_company_admin) { // 公司
      //   this.courseLevelList.forEach((e) => e.disabled = false)
      // } else if (mooc_bgadmin) { // bg
      //   this.courseLevelList.forEach((e) => e.disabled = e.code === 1)
      //   // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      //   this.form.course_statement.certification_level = 1
      // } else if (mooc_dept_admin) { // 部门
      //   this.courseLevelList.forEach((e) => e.disabled = e.code !== 3)
      //   // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      //   this.form.course_statement.certification_level = 3
      // }
      return this.courseLevelList
    },
    // 运营分级
    operation_type_options() {
      let list = [
        { label: '非体系', value: 3, pid: 528 },
        { label: '基础', value: 2, pid: 525 },
        { label: '中坚', value: 1, pid: 526 },
        { label: '头部', value: 0, pid: 527 }
      ]
      if (process.env.NODE_ENV !== 'production') {
        list = [
          { label: '非体系', value: 3, pid: 526 },
          { label: '基础', value: 2, pid: 569 },
          { label: '中坚', value: 1, pid: 571 },
          { label: '头部', value: 0, pid: 573 }
        ]
      }
      return list
    },
    // 备份的标签id集合
    barkLabelIds() {
      return (this.barkInfo.label_list || []).map(item => item.label_id)
    },
    // "创作来源、创作组织、联合创作组织、管理组织、采购组织、是否加入推荐池、是否必修"特殊字段禁用，发布后，只有超管才能编辑
    specialFileDisabled() {
      let status = this.barkInfo.graphic_status + ''
      return status === '1' && !this.isSuperAdmin
    },
    // 创作来源是否修改
    isCreationSourceChanged() {
      let creation_source = this.barkInfo.course_statement?.creation_source || 0
      return this.form.course_statement.creation_source !== creation_source
    },
    // PGC创作组织是否修改
    isPgcCreationOrgChanged() {
      try {
        let pgc_creation_org = this.barkInfo.course_statement?.pgc_creation_org || []
        return !this.areArraysEqual(pgc_creation_org, this.form.course_statement.pgc_creation_org, 'UnitID')
      } catch (error) {
        return false
      }
    },
    // PUGC创作组织是否修改
    isPugcCreationOrgChanged() {
      try {
        let pugc_creation_org = this.barkInfo.course_statement?.pugc_creation_org || []
        return !this.areArraysEqual(pugc_creation_org, this.form.course_statement.pugc_creation_org, 'UnitID')
      } catch (error) {
        return false
      }
    },
    // 联合创作组织是否修改
    isJointCreationOrgChanged() {
      try {
        let pugc_joint_creation = this.barkInfo.course_statement?.pugc_joint_creation || []
        return !this.areArraysEqual(pugc_joint_creation, this.form.course_statement.pugc_joint_creation, 'UnitID')
      } catch (error) {
        return false
      }
    },
    // 管理组织是否修改
    isDeptIdChanged() {
      return this.form.dept_id !== this.barkInfo.dept_id
    },
    // 采购组织是否修改
    isPurchaseOrgChanged() {
      try {
        let ogc_purchase_org = this.barkInfo.course_statement?.ogc_purchase_org || []
        return !this.areArraysEqual(ogc_purchase_org, this.form.course_statement.ogc_purchase_org, 'UnitID')
      } catch (error) {
        return false
      }
    },
    // 是否加入推荐池是否修改
    isJoinRecommendChanged() {
      let join_recommend = this.barkInfo.course_statement?.join_recommend || false
      return !!(this.form.course_statement?.join_recommend !== join_recommend)
    },
    // 是否必修是否修改
    isIsRequiredChanged() {
      let is_required = this.barkInfo.course_statement?.is_required || false
      return this.form.course_statement.is_required !== is_required
    },
    // 是否有特殊字段发生改变
    isSpecialFileChanged() {
      return this.isCreationSourceChanged || this.isPgcCreationOrgChanged || this.isPugcCreationOrgChanged || this.isJointCreationOrgChanged || this.isDeptIdChanged || this.isPurchaseOrgChanged || this.isJoinRecommendChanged || this.isIsRequiredChanged
    },
    // 是否需要弹出特殊字段的修改弹窗
    showSpecialFileDialog() {
      let status = this.barkInfo.graphic_status + ''
      return status === '1' && this.isSuperAdmin && this.isSpecialFileChanged
    }
  },
  mounted() {
    this.getExtandTable()
    const loginUser = JSON.parse(sessionStorage.getItem('login_user'))
    if (loginUser?.staff_id) {
      this.initAuthorsAndManager(loginUser)
    }
  },
  methods: {
    // 判断两个对象数组是否相等，id相等极为相等，不考虑数组顺序问题
    areArraysEqual(arr1, arr2, key) {
      // 提取两个数组的id
      let ids1 = arr1.map(item => item[key])
      let ids2 = arr2.map(item => item[key])
      // 对id数组进行排序
      ids1.sort()
      ids2.sort()
      // 比较两个排序后的id数组是否相等
      return JSON.stringify(ids1) === JSON.stringify(ids2)
    },
    handleClose() {
      this.showDialog = false
      this.updateText = ''
    },
    handleOk() {
      if (!this.updateText.trim()) {
        return this.$message.warning('请输入修改内容！')
      }
      this.bakArg.newParams.graphic.admin_edit_content = this.updateText
      this.$emit('vaildAfterCode', this.bakArg)
      this.showDialog = false
      this.updateText = ''
    },
    // 特殊字段修改后弹窗提示
    showSpecialDialogFn(newParams, code) {
      this.bakArg = { newParams, code }
      this.bakArg.newParams.graphic.admin_edit_content = ''
      if (this.showSpecialFileDialog) {
        this.showDialog = true
      } else {
        this.$emit('vaildAfterCode', this.bakArg)
      }
    },
    getExtandTable() {
      if (!this.graphic_id) return
      const params = {
        act_id: this.graphic_id,
        act_type: 18
      }
      queryExtendContentPage(params).then((data) => {
        sessionStorage.setItem('extend_contents', JSON.stringify(data || []))
        this.tableData.list = data || []
      })
    },
    // 查看填写说明
    toInstructionsPage() {
      window.open('https://iwiki.woa.com/p/4012202789')
    },
    resolveScore(val) {
      return val ? val.toFixed(2) : 0
    },
    // 运营分级发生改变
    changeOperationType(item) {
      this.form.course_statement.operation_project_name = ''
      this.getOperationInfo(item)
    },
    // 获取分级项目
    async getOperationInfo(item) {
      if (!this.operation_project_name_options_map[item.value]) {
        getOperationApi(item.pid).then(res => {
          this.operation_project_name_options_map[item.value] = res || []
          this.operation_project_name_options = res || []
        }).catch(err => {
          console.log('err: ', err)
        })
      } else {
        this.operation_project_name_options = this.operation_project_name_options_map[item.value]
      }
    },
    getLevelObjectOption() {
      let obj = this.operation_type_options.find(item => item.value === this.form.course_statement.operation_level)
      if (obj) {
        this.getOperationInfo(obj)
      }
    },
    // 内容管理组织
    handleUnitSelect(val) {
      this.form.dept_id = val.dept_id
      this.form.dept_name = val.UnitFullName
      if (this.form.dept_id) {
        this.$refs.form.clearValidate('dept_id')
      }
    },
    // 整体表单校验
    viladeteForm() {
      let newFilePass = false
      this.$refs['form'].validate((valid) => {
        if (valid) {
          newFilePass = true
        } else {
          newFilePass = false
        }
      })
      return newFilePass
    },
    // PGC校验创作组织 只能单选，但是要兼容旧数据回显
    validPgcCreationOrg(rule, value, callback) {
      if (!this.form.course_statement.pgc_creation_org.length) {
        return callback(new Error('请选择创作组织'))
      } else if (this.form.course_statement.pgc_creation_org.length > 1) {
        return callback(new Error('创作组织仅能选择一个组织'))
      } else {
        callback()
      }
    },
    // PUGC校验创作组织 只能单选，但是要兼容旧数据回显
    validPugcCreationOrg(rule, value, callback) {
      if (!this.form.course_statement.pugc_creation_org.length) {
        return callback(new Error('请选择创作组织'))
      } else if (this.form.course_statement.pugc_creation_org.length > 1) {
        return callback(new Error('创作组织仅能选择一个组织'))
      } else {
        callback()
      }
    },
    // 切换"创作来源时"，清除所属分类下的表单校验 编辑时逻辑需要完善
    creationSourceChange(value) {
      switch (value) {
        case 0:
          this.initPUGC()
          this.initOGC()
          break
        case 3:
          this.initPGC()
          this.initOGC()
          break
        case 2:
          this.initPGC()
          this.initPUGC()
          this.initOGC()
          break
        case 1:
          this.initPGC()
          this.initPUGC()
          break
        default:
          break
      }
      this.$refs['form'].clearValidate(['course_statement.pgc_creation_org', 'course_statement.pugc_creation_org', 'course_statement.ogc_supplier_name', 'course_statement.ogc_purchase_org', 'course_statement.ogc_purchase_type', 'course_statement.ogc_purchase_amount'])
    },
    // 重置PGC
    initPGC() {
      this.form.course_statement.pgc_creation_org = []
      // this.form.course_statement.pgc_joint_creation = []
    },
    // 重置PUGC
    initPUGC() {
      this.form.course_statement.pugc_creation_org = []
      this.form.course_statement.pugc_joint_creation = []
    },
    // 重置OGC
    initOGC() {
      this.form.course_statement.ogc_supplier_name = ''
      this.form.course_statement.ogc_purchase_org = []
      this.form.course_statement.ogc_purchase_type = null
      this.form.course_statement.ogc_out_teachers = ''
      this.form.course_statement.ogc_purchase_amount = undefined
    },
    // 手动检验字段
    validateField(value, file) {
      if (file !== 'pugc_joint_creation') {
        this.$refs['form'].validateField(`course_statement.${file}`)
      }
      if (['pgc_creation_org', 'pugc_creation_org', 'pugc_joint_creation', 'ogc_purchase_org'].includes(file)) {
        this.form.course_statement[file] = Array.isArray(value) ? value : [value]
      }
    },
    // 字符个数限制
    handleValidor(value, num, type) {
      if (value) {
        const china = value.match(/[\u4e00-\u9fa5]/g)
        const zhCount = china && china.join('').length
        const enCount = Math.ceil((value.length - zhCount) / 2)
        const total = zhCount + enCount
        if (total > num) {
          switch (type) {
            case '1':
              this.form.course_statement.operation_title = value.slice(0, -1)
              break
            case '2':
              this.form.course_statement.ogc_supplier_name = value.slice(0, -1)
              break
            default:
              break
          }
        }
        return total || 0
      }
      return 0
    },
    toGetContentId() {
      window.open('https://iwiki.woa.com/p/4009876544')
    },
    getSelectedLabelList(val) {
      this.form.label_list = val.map(item => {
        return {
          ...item,
          label_type_association: 1
        }
      })
      this.$emit('labelChanged', JSON.parse(JSON.stringify(this.form.label_list)))
      this.$nextTick(() => {
        this.$refs.form.validateField('course_labels')
      })
    },
    labelFocus: throttle(function (e) {
      let graphic_text = this.$parent.$refs['editor'].getContent()
      if (this.formData?.graphic_name.trim() && graphic_text.trim()) {
        this.getRecmLabels({ title: this.formData.graphic_name, content: graphic_text })
      }
    }, 1000),
    initData(formData) {
      this.form = formData
      this.form.ai_sync_flag = formData.ai_sync_flag ? 1 : 0 
      this.form.ai_expire_type = formData.ai_expire_type ? formData.ai_expire_type : 1
      this.ai_expire_end_time = formData.ai_expire_type === 2 && formData.ai_expire_end_time ? formData.ai_expire_end_time : ''
      const { graphic_name, graphic_desc, cover_image_id, graphic_id, is_self_upload, cl_id, authors, course_statement: { pgc_creation_org, pugc_creation_org, pugc_joint_creation, ogc_purchase_org } } = formData
      // this.form.cover_image = is_self_upload === 0 ? cover_image_id : ''
      this.$set(this.form, 'cover_image', is_self_upload === 0 ? cover_image_id : '') // 解决封面图片路径不响应式
      this.form.cover_image_id = is_self_upload === 1 ? cover_image_id : ''
      this.cl_ids = cl_id && cl_id.length > 0 ? cl_id.split(',') : []

      // 来源信息回显 新增字段的成员和组织回显已经选中的
      this.$nextTick(() => {
        if (pgc_creation_org?.length) { // PGC创作组织
          this.$refs.pgcCreationOrgRef && this.$refs.pgcCreationOrgRef.setSelected(pgc_creation_org)
        }
        // if (pgc_joint_creation?.length) { // PGC联合创作组织
        //   this.$refs.pgcJointCreationRef && this.$refs.pgcJointCreationRef.setSelected(pgc_joint_creation)
        // }
        if (pugc_creation_org?.length) { // PUGC 创作组织
          this.$refs.pugcCreationOrgRef && this.$refs.pugcCreationOrgRef.setSelected(pugc_creation_org)
        }
        if (pugc_joint_creation?.length) { // PUGC联合创作组织
          this.$refs.pugcJointCreationRef && this.$refs.pugcJointCreationRef.setSelected(pugc_joint_creation)
        }
        if (ogc_purchase_org?.length) { // 采购组织
          this.$refs.purchaseOrgRef && this.$refs.purchaseOrgRef.setSelected(ogc_purchase_org)
        }
        
        if (formData.dept_id && formData.dept_name) { // 内容管理组织
          this.$refs.deptSelectorRef.setSelected([{ dept_id: formData.dept_id, UnitName: formData.dept_name, UnitFullName: formData.dept_name }])
        } else {
          this.initDepart(this.$store.state.userDepInfo)
        }
      })

      // 内容管理员、内容开发人回显处理
      this.$refs.adminSelector.setSelected(formData.administrators ? formData.administrators : [])
      if (authors) this.$refs.authSelector.setSelected(authors)

      // 初始化时清空表单校验
      this.$nextTick(() => {
        this.$refs['form'].clearValidate()
      })

      // 获取分级项目下拉选项
      this.getLevelObjectOption()

      const addCourseParam = {
        content_name: graphic_name,
        cover_img_url: cover_image_id,
        description: graphic_desc,
        href: `https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=${graphic_id}`,
        item_id: graphic_id,
        origin: location.origin
      }
      this.addCourseDialogData = {
        ...this.addCourseDialogData,
        ...addCourseParam
      }
      // if (formData.graphic_status === 1) this.getCourseCount()
      if (formData.graphic_status !== 4) this.getCourseCount()
    },
    handlerFormData() {
      let copyFormData = JSON.parse(JSON.stringify(this.formData))
      const {
        content_type,
        from_url,
        classify_id,
        label_list,
        is_self_upload,
        cover_image,
        cover_image_id,
        is_open,
        // target_list, // 目标学员
        authors,
        administrators,
        cl_id,
        relation_content,
        dept_id,
        dept_name,
        is_show_recommend,
        graphic_remark,
        course_statement,
        ai_sync_flag,
        ai_expire_type,
        ai_expire_end_time
      } = this.form
      if (is_open === 0) {
        this.form.target_list = allTarget
      }
      if (is_open === 2) {
        this.form.target_list = studentTarget
      }
      if (is_open === 1 && this.form.target_list === allTarget) {
        this.form.target_list = ''
      }
      if (classify_id?.length > 0) this.recursionNodeData(this.classifyList)
      else this.classify_full_name = []
      copyFormData.content_type = content_type
      copyFormData.from_url = from_url
      copyFormData.classify_id = classify_id
      copyFormData.label_list = label_list
      copyFormData.is_self_upload = is_self_upload
      copyFormData.cover_image_id = is_self_upload === 0 ? cover_image : cover_image_id
      copyFormData.is_open = this.form.is_open
      copyFormData.target_list = this.form.target_list
      copyFormData.authors = authors
      copyFormData.administrators = administrators
      copyFormData.cl_id = cl_id
      copyFormData.classify_full_name = this.classify_full_name
      copyFormData.relation_content = relation_content
      // 内容管理组织数据处理
      copyFormData.dept_id = dept_id || ''
      copyFormData.dept_name = dept_name || ''
      copyFormData.is_show_recommend = is_show_recommend
      copyFormData.graphic_remark = graphic_remark
      copyFormData.course_statement = { ...copyFormData.course_statement, ...course_statement }
      copyFormData.ai_sync_flag = ai_sync_flag
      copyFormData.ai_expire_type = ai_expire_type
      copyFormData.ai_expire_end_time = ai_expire_end_time
      this.$emit('update:formData', copyFormData)
    },
    initAuthorsAndManager(userInfo) {
      let authorsArr = [{ StaffID: userInfo.staff_id, StaffName: userInfo.staff_name }]
      if (this.$refs.authSelector) this.$refs.authSelector.setSelected(authorsArr)
      if (this.$refs.adminSelector) this.$refs.adminSelector.setSelected(authorsArr)
    },
    // 内容管理组织给默认值
    initDepart(departInfo) {
      if (departInfo.dept_id && departInfo.dept_full_name) {
        let DepartArr = [{ dept_id: departInfo.dept_id, UnitName: departInfo.dept_full_name, UnitFullName: departInfo.dept_full_name }]
        this.$refs.deptSelectorRef && this.$refs.deptSelectorRef.setSelected(DepartArr)
      }
    },
    getClassifyList() {
      getNetCourseClassify().then((res) => {
        this.classifyList = res
        this.handlerFormData()
      })
    },
    getRecmLabels(obj) {
      const params = {
        ...obj,
        num: 6
      }
      getRecommendLabels(params).then(res => {
        this.recommendLabelList = res
      })
    },
    getCourseCount() {
      if (!this.graphic_id) return
      const params = {
        module_id: 8,
        item_id: this.graphic_id
      }
      getContentAddedCount(params).then(res => {
        this.cl_ids_count = res
      })
    },
    remoteSearchTag(name) {
      if (!name) return
      this.tagSearchform.loading = true
      this.tagSearchform.tagList = []
      const params = { name: name.trim(), count: 300 }
      setTimeout(() => {
        getTagList(params).then((res) => {
          this.tagSearchform.loading = false
          this.tagSearchform.tagList = res
        })
      }, 100)
    },
    handlerLabelTagLimit() {
      const input = document.querySelector('.label-special-class input')
      input.setAttribute('maxLength', 15)
    },
    addRecommendLabel(label) {
      if (this.form.label_list.includes(label)) return
      const idx = this.form.label_list.findIndex((e) => e === label)
      if (idx > 0) this.form.label_list.splice(idx, 1)
      else {
        if (this.form.label_list.length >= 5) this.$message.warning('一个图文最多支持五个标签')
        else this.form.label_list.push(label)
      }
    },
    // 图片编辑
    handleImgEdit() {
      this.$refs.sdcImgCoverRef.outEdit({
        ...this.coverImgBackData,
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        type: 'outEdit'
      })
    },
    // 一键封面回传
    handleImgCoverOk(row) {
      this.form.is_self_upload = 1
      this.form.cover_image_id = row.url
      this.$emit('handleImg', row.url)

      // 清空裁剪封面
      this.form.cover_image = ''
      this.form.cover_imgage_storage_type = 'zhihui'
      this.coverImgBackData = {
        id: row.id
      }
    },
    handleAutoImg() {
      this.autoImgCoverShow = true
      this.imgInfo = {
        title: this.formData.graphic_name,
        env: process.env.NODE_ENV === 'production' ? 'production' : 'test',
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        showEdit: true
      }
    },
    // 内容中心图片回传
    handleSuccessImage(url) {
      this.form.is_self_upload = 0
      this.form.cover_image = url
      this.$emit('handleImg', url)

      // 清空一键生成封面
      this.form.cover_image_id = ''
      this.form.cover_imgage_storage_type = 'contentcenter'
    },
    // 清空图片
    handleClearImg() {
      this.$emit('handleImg', '')
      this.form.cover_image = ''
      this.form.cover_image_id = ''
    },
    changeStaffAuth(obj) {
      const objV = {
        type: 3,
        val: obj.id
      }
      this.$emit('handStafSelector', objV)
    },
    changeAuth(arr) {
      this.changeKeyName(1, arr)
    },
    changeAdm(arr) {
      this.changeKeyName(2, arr)
    },
    changeKeyName(type, arrData) {
      if (type === 1) {
        if (arrData.length > 10) {
          this.$message.warning('最多添加10人')
          this.$refs.authSelector.setSelected(arrData.slice(0, 10))
        }
      } else {
        if (arrData.length > 5) {
          this.$message.warning('最多添加5人')
          this.$refs.adminSelector.setSelected(arrData.slice(0, 5))
        }
      }
      let arr = []
      if (type === 1) {
        arr = arrData.length > 10 ? arrData.slice(0, 10) : arrData
      } else {
        arr = arrData.length > 5 ? arrData.slice(0, 5) : arrData
      }
      // const arr = arrData.length > 5 ? arrData.slice(0, 5) : arrData
      const newArr = arr.map((e) => {
        return { staffid: e.StaffID, staffname: e.StaffName.split('(')[0] }
      })
      const obj = {
        type: type,
        val: newArr.length > 0 ? JSON.stringify(newArr) : ''
      }
      this.$emit('handStafSelector', obj)
    },
    getCheckedClIds(cids) {
      this.cl_ids = cids
      this.form.cl_id = cids.toString()
    },
    strToIntArr(ids) {
      if (!ids) return ''
      let a = ids.map((e) => {
        return parseInt(e)
      })
      return a
    },
    handleAddCourseDialogShow() {
      if (this.formData.graphic_status !== 4) {
        this.addCourseDialogShow = true
        this.addCourseDialogShowSelf = false
      } else {
        this.addCourseDialogShow = false
        this.addCourseDialogShowSelf = true
      }
    },
    recursionNodeData(tree, innerCall) {
      if (!innerCall) {
        this.classify_full_name = []
      }
      if (tree.length) {
        tree.forEach(e => {
          // if (this.form.classify_id.includes(e.item_id) && !e.child?.length) {
          if (this.form.classify_id.includes(e.item_id)) {
            this.classify_full_name.push(e.item_full_name)
          }
          if (e.child?.length) {
            this.recursionNodeData(e.child, true)
          }
        })
      }
    },
    showRelatedContentHandle() {
      this.showRelatedContent = true
    },
    closeAddExDialog(relatedContent) {
      this.form.relation_content = relatedContent ? relatedContent[0] : null
      const copyFormData = JSON.parse(JSON.stringify(this.formData))
      copyFormData.relation_content = this.form.relation_content
      this.$emit('update:formData', copyFormData)
      this.showRelatedContent = false
    },
    // 延伸学习
    getExtandData(data) {
      this.tableData.list = JSON.parse(sessionStorage.getItem('extend_contents')) || []
      this.showAddExtandLearn = false
    },
    getModuleClass(module_id) {
      let cardType = qlearningModuleTypes.find((item) => module_id === item.module_id)
      if (cardType) {
        return cardType.moduleClassName
      }
    },
    delRelatedItem() {
      this.form.relation_content = null
      this.$forceUpdate()
    },
    handlerDomAttrDel() {
      const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]')
      if ($el.length > 0) {
        Array.from($el).map(item => item.removeAttribute('aria-owns'))
      }
    },
    // 同意协议勾选
    handleCheck(val) {
      if (val) {
        if (this.form.content_type === '1') {
          // 第一次进页面
          localStorage.setItem('tencent_qlearnging_netcource_ai_forward_content', true) // 转载
        } else {
          localStorage.setItem('tencent_qlearnging_netcource_ai_origin_content', true) // 原创
        }
      }
    },
    // 文章类型
    handleContentChange(val) {
      const originFlag = localStorage.getItem('tencent_qlearnging_netcource_ai_origin_content')
      const forwardFlag = localStorage.getItem('tencent_qlearnging_netcource_ai_forward_content')
      if ((!originFlag && ['0', '3'].includes(val)) || (!forwardFlag && val === '1')) {
        this.copyrightShow = true
      }
      if (val === '0' || val === '3') {
        this.typeOriginal = 1
        // 第一次进页面
      } else if (val === '1') {
        this.typeOriginal = 0
      }
    },
    handleUp(row, index) {
      this.tableData.list.unshift(this.tableData.list.splice(index, 1)[0])
      sessionStorage.setItem('extend_contents', JSON.stringify(this.tableData.list))
    },
    handleDelete(row, index) {
      this.tableData.list.splice(index, 1)
      sessionStorage.setItem('extend_contents', JSON.stringify(this.tableData.list))
    },
    // 设置有效时间
    setExpireTime(value) {
      this.form.ai_expire_end_time = value
    }
  },
  beforeDestroy() {
    clearInterval(this.domAttrDelTimer)
  }
}
</script>

<style lang="less" scoped>
.propertySet-page {
  padding-bottom: 20px;

  .update-dialog {
    .update-content-box {
      position: relative;
      display: flex;
      align-items: center;
      &::after {
        position: absolute;
        left: 75px;
        bottom: -18px;
        content: '（注：修改内容将通过企微机器人同步提醒给课程管理员，请留意。）';
        color: #d63535;
        font-size: 12px;
      }
    }
    .dialog-label {
      flex-shrink: 0;
      margin-right: 20px;
    }
    .update-text-input {}
  }
  :deep(.trim-content .el-date-editor .el-input__inner){
    padding: 0 30px;
  }

  :deep(.el-form-item__error) {
    top: 98%;
    left: 87px;
  }

  .title {
    p:first-child {
      height: 58px;
      line-height: 58px;
      padding-left: 16px;
      color: #333333;
      font-size: 20px;
      font-weight: bold;
    }

    p:last-child {
      border-top: 1px solid rgba(232, 232, 232, 1);
      margin-top: 9px;
    }
  }

  .propertySet-content {
    :deep(.el-input) {
      width: 320px;
    }

    .labels-box {
      :deep(.el-input) {
        min-height: 32px;
        height: unset;
      }
      :deep(.custom-height) {
        display: flex;
        align-items: center;
      }
    }

    .labels-add-disabled {
      :deep(.add-label-outermost) {
        .label-select-component {
          .cascader-component {
            .el-button[type=button] {
              display: none;
            }
          }
        }
      }
    }

    :deep(.el-form-item) {
      margin-bottom: 24px !important;

      .el-form-item__label {
        width: 75px;
        margin-right: 12px;
        padding: unset;
      }

      .el-form-item__label:before {
        margin-right: unset;
      }
    }

    .base-set,
    .more-set {
      .async-icon {
        color:#E34D59;
        margin-left: 6px;
      }
      .trim-content {
        .el-date-editor {
          width: 280px;
        }
      }
      .base-set-title {
        font-weight: 600;
        margin-bottom: 20px;
        padding-left: 12px;
        color: #333333;
        height: 20px;
        border-left: solid 4px #0052D9;
      }

      .more-set-title {
        display: flex;
        justify-content: space-between;
        align-content: center;

        >:last-child {
          cursor: pointer;
          color: #3464E0;
          padding-right: 16px;
        }
      }
    }

    .test-tips {
      padding-left: 16px;
      color: #9d9d9d;
      font-size: 12px;
      line-height: 18px;
      .color-red {
        color: #d63535;
      }
      .text-undeline {
        text-decoration: underline;
        cursor: pointer;
      }
      .mgr-5 {
        margin-right: 5px;
      }
    }

    .base-set {
      padding-top: 20px;

      .related-content {
        i {
          font-style: normal;
          font-size: 16px !important;
        }

        display: flex;
        font-size: 12px;
        color: #0052D9;
        cursor: pointer;
      }

      .related-content-item {
        display: flex;
        align-items: center;

        span:nth-child(2) {
          margin: 0 16px 0 8px;
          display: inline-block;
          width: 209px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        i {
          cursor: pointer;
          width: 16px;
          height: 16px;
          color: #9A9A9A;
        }
      }

      .recommend-tag-box {
        margin-top: -16px;
        margin-bottom: 16px;

        .title {
          font-size: 12px;
          color: #666;
          margin-bottom: 8px;
        }

        .title,
        .recommend-tag {
          padding-left: 86px;
        }

        .recommend-tag {
          margin-left: unset !important;
          padding-right: 16px;

          :deep(.el-tag) {
            cursor: pointer;
            height: 20px;
            line-height: 12px;
            padding: 4px 4px;
            margin: 0 8px 6px;
          }
        }
      }

      .cut-img-upload {
        display: flex;
      }

      // 父内容ID
      .parent-content {
        margin-bottom: 15px !important;
        .parent-content-id {
          display: flex;
          .content-type {
            position: relative;
            width: 140px;
            /deep/.el-select, /deep/.el-input {
              width: 100%;
              input {
                padding: 5px 30px 5px 8px;
              }
            }
          }
          .content-id {
            position: relative;
            margin-left: 20px;
            width: 150px;
            /deep/.el-input {
              width: 100%;
              input {
                padding: 5px 30px 5px 8px;
              }
            }
          }
          .error-tip-text-customer {
            position: absolute;
            bottom: -25px;
            left: 0;
            width: 100%;
            color: red;
            font-size: 12px;
          }
        }
      }
      .get-content-id {
        padding: 0 20px;
        color: #0052D9;
        cursor: pointer;
        font-weight: bold;
      }
      .mgb-10 {
        margin-bottom: 10px;
      }

      .creation-source {
        .creation-source-radio {
          display: flex;
          flex-direction: column;
          margin: 9px 15px 0;
          .el-radio {
            margin: 0 0 5px;
          }
        }
        .origin-warm {
          margin: 0 10px 10px;
          font-size: 12px;
          line-height: 18px;
          color: #999999;
        }

        .creation_source_sub_content {
          margin: 5px;
          padding: 8px;
          background: #f9f9f9;
          border-radius: 4px;
          .el-form-item {
            margin-bottom: 20px !important;
          }

          .mgb-0 {
            margin-bottom: 0px !important;
          }

          :deep(.el-form-item__error) {
            top: 100%;
            left: 10px;
          }

          :deep(.sdc-selector) {
            .el-input__inner {
              padding: 5px;
            }
            .selector-container .container-inner .tags {
              height: 38px;
            }
          }

          .ugc-sub-item {
            .ugc_is_original {
              .el-input {
                width: 300px;
              }
            }
          }

          .OGC-content {
            .course-texTarea-input {
              :deep(.el-input__inner) {
                padding-right: 55px;
              }
            }
            .el-input {
              width: 280px;
            }
            .ogc-purchase-org {
              margin-bottom: 11px !important;
            }
            .ogc-purchase-type {
              // margin-bottom: 15px !important;
             .el-form-item__content {
              .el-radio-group {
                margin-top: 9px;
                display: flex;
                flex-direction: column;
                .el-radio {
                  margin: 0 0 5px 0;
                  &:last-child {
                    margin-bottom: 0;
                  }
                }
              }
             } 
            }
            :deep(.ogc_purchase_amount.el-input-number) {
              width: 180px;
              line-height: 32px;
              .el-input {
                width: 180px;
              }
              .el-input-number__decrease, .el-input-number__increase {
                width: 40px;
                height: 30px;
                line-height: 30px;
              }
            }
          }

        }
      }

      .customer-form-item {
        :deep(.el-input__inner) {
          padding-right: 38px;
        }
        .custom-el-input-count {
          right: 18px;
        }
      }

      .custom-el-input-count {
        color: #ACACAC;
        background: #FFF;
        position: absolute;
        font-size: 12px;
        bottom: 6px;
        right: 6px;
        line-height: 20px;
      }

    }

    .more-set {
      :deep(.sdc-staff-selector), :deep(.sdc-unit-selector) {
        width: 320px;
      }

      .tip {
        color: rgba(0, 0, 0, 0.4);
        font-size: 12px;
        line-height: 16px;
      }

      .tip1 {
        width: 320px;
        position: relative;
        left: 82px;
      }

      .course-num {
        margin-right: 20px;
        color: rgba(0, 0, 0, 0.9);
      }

      .extand-title {
        display: flex;
        justify-content: space-between;
        padding-right: 16px;

        .addExtandItem {
          color: #0052D9;
          font-size: 12px;
        }
      }

      .add-to-course {

        :deep(.el-form-item__label),
        :deep(.el-form-item__content) {
          line-height: unset;
        }

        :deep(label) {
          margin-left: 12px;
        }
      }

      :deep(.human_cost.el-input-number) {
        width: 180px;
        line-height: 32px;
        .el-input {
          width: 180px;
        }
        .el-input-number__decrease, .el-input-number__increase {
          width: 40px;
          height: 30px;
          line-height: 30px;
        }
      }

      .is_required, .expert-rating, .is-show-recommend {
        .tip-warm {
          margin-left: 16px;
          font-size: 12px;
          line-height: 18px;
          color: #999999;
        }
        .mgl-15 {
          margin-left: -15px;
        }
      }

      .operation_level_sub_content {
        margin: 5px;
        padding: 8px;
        background: #f9f9f9;
        border-radius: 4px;
        .el-form-item {
          margin-bottom: 0 !important;
          :deep(.el-input) {
            width: 290px;
          }
        }
      }

      .expert-rating {
        .expert-rating-input {
          width: 180px;
          :deep(.el-input__inner[type=number]) {
            padding-right: 30px;
          }
        }
        :deep(.el-form-item__error) {
          left: 12px;
        }
        .tip-warm {
          margin-left: 11px;
        }
      }

      .join_recommend {
        margin-bottom: 0 !important;
      }
      .red-tips-customer {
        color: #d63535;
        font-size: 12px;
        height: 12px;
        line-height: 12px;
        margin: 0 0 24px 78px;
      }
      
      .isOpen {
        margin-bottom: 32px !important;
        :deep(.selector-container) {
          display: none;
        }

        :deep(.el-radio-group) {
          label {
            display: block;
            margin: 0 0 10px 0;
          }
        }

        :deep(.sdc-selector) {
          height: 32px;

          .el-button {
            width: 64px;
            height: 32px;
            padding: unset;
            justify-content: center;
            border-radius: 4px;
          }
        }

        :deep(.modal-body-audience) {
          .el-input {
            width: 100%;
          }

          .el-input__inner {
            padding: 0 28px;
          }
        }
      }

      .graphic_remark {
        :deep(.el-textarea) {
          width: 70%;
        }
      }
    }

    .base-set,.more-set {
      .label-two-line {
        :deep(.el-form-item__label) {
          line-height: 16px;
        }
      }
    }

    :deep(.sdc-unit-selector) {
      .num {
        display: none;
      }
    }

    :deep(.sdc-selector) {
      .el-button {
        color: #999;
      }

      .selector-container {
        .el-tag {
          background: rgba(231, 231, 231, 1);
          color: rgba(0, 0, 0, 0.9);
          height: 24px;
          line-height: 24px;
        }

        .el-icon-close {
          font-size: 16px;
        }
      }

      .selector-container,
      .suffix-open {
        display: flex;
        align-items: center;
        height: 32px;
        line-height: 32px;
        padding: unset;

        .el-input {
          width: unset;
        }

        .el-input,
        .el-input__inner {
          height: 28px;
        }
      }

      .suffix-open {
        display: flex;
        align-items: center;

        .el-button {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }

    .flex-row {
      display: flex;
      align-items: center;
    }
  }
  .extand-box {
    .extand-title {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-right: 16px;
      margin-bottom: 8px;

      .title {
        margin-left: 20px;
      }

      .tip {
        color: rgba(0, 0, 0, 0.4);
        font-size: 12px;
        margin-left: -40px;
      }

      .addExtandItem {
        color: #0052D9;
        font-size: 12px;
        cursor: pointer;
      }
    }

    .extand-table {
      padding: 0 16px;

      :deep(.el-table) {
        th {
          background: rgba(245, 245, 245, 1);
          font-weight: 400;
          color: rgba(0, 0, 0, 0.8);
          border-bottom: solid 1px #eeeeee;
        }

        th,
        td {
          height: 36px;
          padding: unset;
        }

        td {
          color: rgba(0, 0, 0, 0.6);
        }
      }

      .icon-btns {
        i {
          font-size: 16px;
          width: 16px;
          height: 16px;
          display: inline-block;
          font-size: 16px;
          cursor: pointer;
        }

        i:first-child {
          margin-right: 8px;
        }

        .icon-up {
          background: url('~@/assets/img/icon-up.png') no-repeat center / cover;
        }

        .icon-delete {
          background: url('~@/assets/img/icon-delete.png') no-repeat center / cover;
        }
      }

      :deep(.el-table--scrollable-x .el-table__body-wrapper) {
        overflow-x: hidden;
      }
    }
  }
  .contact-person {
    margin-left: 10px;
    font-size: 12px;
    color: #999999;
    line-height: 18px;
    font-style: italic;
  }
  .contact-person-radio {
    margin: -6px 0 5px 85px;
  }
}
</style>
