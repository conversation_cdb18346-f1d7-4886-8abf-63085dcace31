<template>
  <div class="page-box">
    <div class="top-title">
      <span>配置列表</span>
    </div>
    <div class="content-top-wrap">
      <div class="content-top">
        <el-form ref="form" :model="form" inline>
          <el-form-item label="选择年份">
            <el-select v-model="form.year" placeholder="请选择" size="small" style="width: 240px;">
              <el-option
                v-for="item in timerList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="人群分类">
            <el-select v-model="form.category" placeholder="请选择" size="small" style="width: 240px;">
              <el-option
                v-for="item in personType"
                :key="item.value"
                :label="item.label"
                :value="item.label">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="内容标题">
            <el-input
              style="width: 360px;"
              placeholder="请输入内容标题"
              v-model="form.course_name"
              size="small"
            />
          </el-form-item>
          <el-row>
            <el-form-item class="btn-right">
              <el-button @click="handleReset" size="small" plain>
                <i class="el-icon-refresh"></i><span>重置</span>
              </el-button>
              <el-button type="primary" @click="onSearch(1)" size="small">搜索</el-button>
            </el-form-item>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="content-main">
      <div class="table-top">
        <el-button type="primary" size="small" @click="setDialogShow=true">添加</el-button>
        <el-button size="small" @click="handleDelete('all')">批量删除</el-button>
        <div class="select-num">已选择<span class="num"> {{ multipleData.length || 0 }} </span>条内容</div>
      </div>
      <el-table
        ref="table"
        :data="tableParams.list"
        style="width: 100%"
        header-row-class-name="table-header-style"
        row-class-name="table-row-style"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      >
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column  label="年份" prop="year" width="200" sortable="custom"></el-table-column>
        <el-table-column  label="人群分类" prop="category" width="200" sortable="custom"></el-table-column>
        <el-table-column  label="序号" prop="order_no" width="200"></el-table-column>
        <el-table-column  label="内容标题" prop="course_name" show-overflow-tooltip></el-table-column>
        <el-table-column  label="内容链接" prop="course_url" show-overflow-tooltip>
          <template slot-scope="{ row }">
            <a style="color: #3464E0;"  target="_blank" :href="row.course_url">{{ row.course_url }}</a>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="160">
          <template slot-scope="{ row }">
            <el-button type="text" @click="handleEdit(row)">编辑</el-button>
            <el-button type="text" @click="handleSort(row)">排序</el-button>
            <el-button type="text" @click="handleDelete('single', row)" class="color-red" >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="table-pagination">
        <el-pagination
          :hide-on-single-page="tableParams.list.length === 0"
          @size-change="handleSizeChange"
          @current-change="onSearch"
          :page-size="form.size"
          :current-page="form.current"
          :page-sizes="[10, 20, 30, 50, 100]"
          layout="total,  prev, pager, next, sizes, jumper"
          :total="tableParams.total"
        >
        </el-pagination>
      </div>
    </div>
    <setDialog v-if="setDialogShow" ref="setDialogRef" :visible.sync="setDialogShow" @getQueryData="getQueryData"></setDialog>
  </div>
</template>
<script>
import setDialog from './setDialog'
import { rankQuery, deleteRank, sortRank } from '@/config/api.conf'
export default {
  components: {
    setDialog
  },
  data() {
    return {
      form: {
        year: '',
        category: '',
        course_name: '',
        current: 1,
        size: 10,
        type: 'desc',
        order_by: 'updated_at'
      }, 
      tableParams: {
        total: 0,
        list: []
      },
      setDialogShow: false,
      personType: [
        { label: '全员', value: 0 },
        { label: 'T族', value: 1 },
        { label: 'P族', value: 2 },
        { label: 'M族', value: 3 },
        { label: 'D族', value: 4 },
        { label: 'S族', value: 5 },
        { label: '领导力', value: 6 },
        { label: '新员工', value: 7 }
      ],
      multipleData: []
    }
  },
  computed: {
    timerList() {
      const currentYear = new Date().getFullYear()
      let years = []
      for (let i = 2020; i <= currentYear; i++) {
        years.push({
          label: i,
          value: i
        })
      }
      return years
    }
  },
  mounted() {
    this.getQueryData()
  },
  methods: {
    // 编辑
    handleEdit(row) {
      this.setDialogShow = true
      this.$nextTick(() => {
        this.$refs.setDialogRef.edit(row)
      })
    },
    handleSortChange(a, b) {
      // const order = {
      //   '全员': 0,
      //   'T族': 1,
      //   'P族': 2,
      //   'S族': 3,
      //   'M族': 4,
      //   'D族': 5,
      //   '新员工': 6,
      //   '领导力': 7
      // }
      // return order[a.category] - order[b.category]
      this.form.order_by = a.prop
      this.form.type = a.order === 'descending' ? 'desc' : 'asc'
      console.log('aaaaa', a)
      this.getQueryData()
    },
    onSearch(num) {
      this.form.current = num
      this.getQueryData()
    },
    getQueryData() {
      const { year, course_name, size, current, category, order_by, type } = this.form
      const params = {
        year,
        course_name,
        current,
        size,
        category,
        order_by: {
          order_by,
          type
        }
      }
      rankQuery(params).then((res) => {
        this.tableParams.list = res.records
        this.tableParams.total = res.total
      })
    },
    handleReset() {
      this.form = {
        year: '',
        category: '',
        course_name: '',
        current: 1,
        size: 10,
        type: 'desc',
        order_by: 'updated_at'
      }
      this.getQueryData()
    },
    handleSizeChange(val) {
      this.form.size = val
      this.getQueryData()
    },
    handleSelectionChange(val) {
      console.log('selection-change', val)
      this.multipleData = val
    },
    handleDelete(type, row) {
      const mId = this.multipleData?.length && this.multipleData.map((e) => e.id)
      let id = type === 'all' ? mId : [row.id]
      deleteRank(id).then((res) => {
        this.$message.success('删除成功')
        this.getQueryData()
      })
    },
    handleSort(row) {
      this.$prompt('请输入排序', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        closeOnClickModal: false,
        inputValidator: function(e) {
          if (!e) {
            return '排序不能为空'
          } else if (e * 1 < 1) {
            return '仅支持输入1-10的数字'
          } else {
            let reg = /^[+]{0,1}[0-9](\d*)$/
            if (!reg.test(e)) {
              return '请输入正整数'
            }
            return true
          }
        },
        inputValue: row.order_no
      }).then(({ value }) => {
        const params = {
          id: row.id,
          order_no: value
        }
        sortRank(params).then(res => {
          this.$message.success('修改成功')
          this.getQueryData()
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
.page-box {
  padding: 4px 0;
  height: 100%;
  background: #fff;
  border-radius: 4px;
  opacity: 1;
  box-shadow: 0 0 8px 0 #eeeeeeff;
  .top-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f3f3f3ff;
    padding: 16px 20px;
    span {
      color: #000000e6;
      font-size: 24px;
      font-weight: 600;
    }
  }
  .content-top-wrap {
    margin-bottom: 20px;
    padding: 0 20px;
  }
  .content-top {
    border-radius: 4px;
    background: #FAFAFA;
    padding: 20px 20px 0 20px;
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    .btn-right {
      float: right;
    }

    .el-form /deep/ .el-form-item {
      margin-bottom: 16px;
      margin-right: 18px;
    }
    .el-button {
      width: 80px;
    }
    .el-form--inline /deep/ .el-form-item__label {
      color: #00000099;
    }
  }
  .content-main {
    padding: 0 20px;
    .table-top {
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      .el-button + .el-button {
        margin-left: 16px;
      }
      .el-button {
        width: 80px;
      }
      .select-num {
        margin-left: 16px;
        color: #00000099;
        .num {
          color: #0052D9;
        }
      }
    }
    .color-red {
      color: red !important;
    }
    /deep/ .el-table {
      border: 1px solid #eee;
      .table-header-style th {
        padding: 6px 0;
        color: #00000099;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 14px;
      }
    }
  }
}
</style>
