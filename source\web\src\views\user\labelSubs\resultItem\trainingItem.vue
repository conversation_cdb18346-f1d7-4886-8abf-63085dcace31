<template>
    <div class="labelCardItem">
        <div class="courseImage">
            <defaultImage :info="info"/>
            <span class="boutique1" v-if="info.origin_data.excellent_status === 1">精品</span>
            <span class="partake">{{info.origin_data.member_list?.includes(String($store.state.userInfo.staff_id)) ? '已参与' : '未参与'}}</span>
            <span class="duration">共{{info.origin_data.task_count||0}}项任务</span>
            <img class="update_status_outcourse" :src="resolveImgUrl(info)" alt="" v-if="showOutcourseIcon(info)">
            <img class="update_status" :src="require('@/assets/img/Updating.png')" alt="" v-if="info.origin_data.serial_type == 2 && !showOutcourseIcon(info)">
        </div>
        <div  class="courseInfo">
            <moduleTitle :info="info"/>
            <div class="courseView flex align-center">
                <!-- <div class="flex align-center" v-if="info.origin_data.scorer_count > 20 ">
                    <img :src="require('@/assets/img/label/flower2x.png')">
                    <span>{{info.origin_data.score.toFixed(1)}}分</span>
                </div>
                <span v-else>评分人数不足</span> -->
                <div class="flex align-center" v-if="info.origin_data.show_join_count == 1">
                    <img :src="require('@/assets/img/label/people2x.png')">
                    <span>{{info.origin_data.member_count||0}}人参与</span>
                </div>
            </div>
            <relatedContent :info="info" :curModuleId="curModuleId" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :subsType="subsType" :contentType="contentType"></relatedContent>
        </div>
    </div>
</template>
<script>
import defaultImage from './child/defaultImage.vue'
import moduleTitle from './child/moduleTitle.vue'
import relatedContent from './child/relatedContent.vue'
import { pcCoverLogo } from '@/utils/outsourcedCourseMap.js'
export default {
  name: 'trainingItem',
  props: ['info', 'curModuleId', 'curLabelId', 'sortSubsIds', 'subsType', 'contentType'],
  data() {
    return {}
  },
  components: {
    defaultImage,
    moduleTitle,
    relatedContent
  },
  computed: {},
  methods: {
    showOutcourseIcon(item) {
      let coverLogoArray = Object.keys(pcCoverLogo) || []
      return coverLogoArray.includes(item?.origin_data?.resource_from)
    },
    resolveImgUrl(item) {
      return pcCoverLogo[item.origin_data.resource_from] || pcCoverLogo['geekBang']
    }
  }
}
</script>
<style lang="less" scoped>
@import '../style/item.less';
</style>
