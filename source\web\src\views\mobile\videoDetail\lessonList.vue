<template>
  <div class="lesson-list-main">
    <div class="title-card">
      <div class="pub-flex pub-l-t">
        <div :class="[{'overflow-l1': !isExpand, 'overflow-l2': isExpand}]" class="title">{{ basicInfo.name }}</div>
        <div class="num">{{ curIndex }} / {{ basicInfo.courses_count }}</div>
        <div class="expand-btn" @click="expandSwitch">
          <span :class="[{'packUp-icon': isExpand}, 'expand-icon']"></span>
        </div>
      </div>
      <div class="data-list pub-flex" v-if="isExpand">
        <span>{{ basicInfo.courses_count }}{{ $langue('NetCourse_Contents', { defaultText: '个内容' }) }}</span>
        <span>{{$langue('NetCourse_Creator', { defaultText: '创建人' })}}：{{ basicInfo.creator_name }}</span>
        <span>{{ basicInfo.created_at }}</span>
      </div>
    </div>
    <div class="card-list" v-if="isExpand">
      <courseCard class="content-list-box" v-for="(item, index) in list" :detailsInfo="item" :key="index" :curIndex="curIndex"></courseCard>
    </div>
    <div class="look-btn" v-if="isExpand" @click="toCourseDetail">{{ $langue('NetCourse_ViewCourseList', { defaultText: '查看课表详情' }) }}</div>
  </div>
</template>
<script>
import {
  getCourselistBasic,
  getContentsList
} from 'config/api.conf'
// import { Toast } from 'vant'
import courseCard from '../components/courseCard.vue'

export default {
  components: {
    courseCard
  },
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 是否展开
      isExpand: false,
      basicInfo: {},
      list: []
    }
  },
  computed: {
    areaId() {
      return this.$route.query.area_id
    },
    curIndex() {
      const item = this.list.find(v => v.item_id === this.$route.query.course_id)
      console.log(this.list, this.$route.query.course_id)
      return item?.order_no ? item.order_no : 0
    }
  },
  created() {
  },
  mounted() {
    this.areaId && this.getCourselistBasicFn()
  },
  methods: {
    getCourselistBasicFn() {
      getCourselistBasic({
        cl_id: this.areaId
      }).then(res => {
        this.basicInfo = res
        this.expandSwitch()
        this.getContentsListFn()
      })
    },
    getContentsListFn() {
      getContentsList(this.areaId).then(res => {
        console.log(res)
        this.list = res.map(v => {
          let createdTime = v.content_created_time.split(' ')
          return {
            module_name: v.module_name,
            module_id: v.module_id,
            content_name: v.content_name,
            description: '',
            play_total_count: v.content_view_count,
            word_num: v.word_num,
            praise_count: v.content_avg_score,
            avg_score: v.content_avg_score,
            created_time: createdTime[0],
            photo_url: v.cover_img_url,
            content_url: v.href,
            updated_at: createdTime[0],
            content_id: v.item_id,
            item_id: v.item_id,
            notShowPhoto: true,
            order_no: v.order_no,
            origin_data: {
              expert_name: v.creator_name,
              meet_num: 0,
              avg_score: v.content_avg_score,
              start_time: '',
              end_time: ''
            }
          }
        })
      })
    },
    expandSwitch() {
      this.isExpand = !this.isExpand
      this.$emit('expandSwitch', this.isExpand)
    },
    toCourseDetail() {
      window.open(`https://ihr.tencent.com/2K0j9f/${this.areaId}`)
    }
  }
}
</script>
<style lang="less" scoped>
@import '~@/assets/css/graphic-common.less';
.lesson-list-main {
  width: 100vw;
  background: #fff;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  .title-card {
    padding: 13px 16px;
    border-bottom: 1px solid #eeeeee;
    .title {
      color: #000000e6;
      font-size: 14px;
      line-height: 22px;
      padding-right: 8px;
      flex: 1;
      max-height: 44px;
    }
    .num {
      color: #00000099;
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      padding: 0 8px;
      flex-shrink: 0;
    }
    .expand-btn {
      padding-left: 16px;
      flex-shrink: 0;
      .expand-icon {
        background: url('~@/assets/img/mobile/arrow-down.png') no-repeat center/cover;
        height: 16px;
        width: 16px;
        display: inline-block;
        transform: rotate(0deg);
        transition: all 0.3s;
        margin-top: 2px
      }
      .packUp-icon {
        transform: rotate(180deg);
      }
    }
    .data-list {
      color: #00000066;
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
      padding-top: 4px;
      margin-bottom: -6px;
      &> span {
        padding-right: 16px;
      }
    }
  }
  .card-list {
    padding: 0px 16px 34px;
    background: #fff;
    flex: 1;
    overflow: auto;
    .content-list-box {
      border-bottom: 0.5px solid #E7E7E7;
    }
  }
  .look-btn {
    width: 98px;
    height: 32px;
    line-height: 32px;
    border-radius: 70px;
    color: #0052d9;
    font-size: 12px;
    background: #ffffff;
    box-shadow: 0 0 12px 0 #dddddd;
    text-align: center;
    position: fixed;
    left: calc(50% - 49px);
    bottom: 34px;
  }
}
.pub-flex {
  display: flex;
  align-items: center;
  justify-content: left;
}
.pub-l-t {
  align-items: flex-start;
  justify-content: left;
}
</style>
