<template>
  <div>
    <div>
      <link rel="stylesheet" :href="linkHref">
      <div>
        <div :class="[{'menu-creator-container': menuHigh}]" class="creator-center-container">
          <convention-banner style="margin: 12px auto;" class="creator-center-container-banner"  />
          <div style="display:flex">
            <left-menu></left-menu>
            <div class="right-main">
              <router-view></router-view>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 信息安全声明 -->
    <statement :visible.sync="statementShow"></statement>
  </div>
</template>
<script>
import { getAvatar } from 'utils/tools'
import {
  isAuthority
} from 'config/api.conf'
import statement from '../components/statement.vue'
import conventionBanner from '@/views/components/convention-banner.vue'
import leftMenu from '@/views/components/leftMenu.vue'
export default {
  components: {
    statement,
    conventionBanner,
    leftMenu
  },
  data() {
    return {
      linkHref: '',
      avatar: require('@/assets/img/avatar.png'),
      defaultAvatar: require('@/assets/img/avatar.png'),
      statementShow: false,
      courseList: [
        { id: 1, name: '活动管理', routerName: 'activityList', testUrl: '', prdUrl: '' },
        { id: 2, name: '班级管理', routerName: '', testUrl: 'https://test-learn.woa.com/manage/class', prdUrl: 'https://learn.woa.com/manage/class' },
        { id: 3, name: '课程管理', routerName: 'courseList', testUrl: '', prdUrl: '' },
        { id: 4, name: '文章管理', routerName: 'graphic-page', testUrl: '', prdUrl: '' },
        { id: 5, name: '案例管理', routerName: '', testUrl: '', prdUrl: '' },
        { id: 6, name: '网络课管理', routerName: '', testUrl: '', prdUrl: '' },
        { id: 7, name: '行家工作台', routerName: '', testUrl: '', prdUrl: '' },
        { id: 8, name: '讲师工作台', routerName: '', testUrl: '', prdUrl: '' }
      ]
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          this.avatar = getAvatar(this.$store.state.userInfo.staff_name)
          if (!window.$qlCommonHeader) {
            this.linkHref = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeaderStyle.css` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeaderStyle.css`  
            const commonHeaderJsUrl = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeader.js` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeader.js`
            this.loadHeadJS(commonHeaderJsUrl, function() {
              if (window.$qlCommonHeader) {
                window.$qlCommonHeader.create({
                  staff_name: val.staff_name,
                  dev: !(process.env.NODE_ENV === 'production')
                })
  
                setTimeout(() => {
                  let el = document.getElementsByClassName('common-header-occupy')
                  if (el.length > 0) {
                    console.log('El', el[0].offsetHeight)
                    document.getElementById('app').style.height = `calc(100% - ${el[0].offsetHeight}px)`
                  }
                }, 500)
              }
            })
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    menuHigh() {
      return this.courseList.some((v) => v.routerName === this.$route.name)
    }
  },
  async mounted() {
    // 判断用户是否有权限-信息安全声明
    await isAuthority()
    const flag = localStorage.getItem('tencent_qlearnging_netcource_ai_statement')
    if (!flag) {
      this.statementShow = true
    }
  },
  methods: {
    loadHeadJS (url, callback) {
      var script = document.createElement('script')
      var fn = callback || function() {}
      script.type = 'text/javascript'
      script.id = 'graphic-common-head'
      // IE
      if (script.readyState) {
        script.onreadystatechange = function() {
          if (script.readyState === 'loaded' || script.readyState === 'complete') {
            script.onreadystatechange = null
            fn()
          }
        }
      } else {
        // 其他浏览器
        script.onload = function() {
          fn()
        }
      }
      script.src = url
      document.getElementsByTagName('head')[0].appendChild(script)
    }
  }
}
</script>
<style>
body {
  overflow: hidden;
}
#app {
  height: calc(100% - 50px);
  overflow-y: auto;
}
</style>
<style lang="less">
@import '~assets/css/graphic-common.less';
@import '~assets/css/ai-common.less';
@import '~assets/css/center.less';
@import '~assets/css/common.less';
.creator-center-container {
  @media screen and (max-width: 1660px) {
    width: 1416px;
  }
  @media screen and (min-width: 1661px) {
    width: 1440px;
  }
  // display: flex;
  margin: auto;
  padding-top: 12px;
  .left-main {
    position: sticky;
    top: 12px;
    height: 815px;
    width: 180px;
    background-color: #fff;
    z-index: 99;
    padding-top: 16px;
    border-radius: 4px;
    .avatar-box {
      color: rgba(0,0,0,0.8);
      text-align: center;
      margin-bottom: 12px;
      :deep(.el-avatar) {
        width: 56px;
        height: 56px;
        border-radius: 50%;
        margin-bottom: 10px;
      }
    }
    .current-box {
      width: 156px;
      text-align: center;
      padding: 12px;
      border-radius: 4px;
      opacity: 1;
      background: rgba(248,249,253,1);
      margin: 0 auto;
      color: rgba(0,0,0,0.6);
      p {
        line-height: 20px;
        height: 20px;
        margin-bottom: 10px;
      }
      .current-item-text {
        font-size: 14px;
        font-weight: bold;
        color: #0052D9
      }
    }
    .share-btn-box {
      width: 156px;
      line-height: 36px;
      position: relative;
      margin: 0 auto;
      padding-bottom: 16px;
      padding-top: 16px;
      border-bottom: 1px solid #eeeeee;
      cursor: pointer;
      .share-btn {
        border-radius: 28px;
        color: #fff;
        text-align: center;
        line-height: 36px;
        font-weight: 500;
        display: flex;
      }
      .text-left {
        width: 110px;
        border-radius: 28px 0 0 28px;
        background-color: #0052D9;
        position: relative;
        display: flex;
        align-items: center;
      }
      .text-left:hover , .i-right:hover{
        background-color: #0D5FE7;
      }
      .i-right {
        display: inline-block;
        background-color: #0052D9;
        width: 46px;
        border-radius: 0 28px 28px 0;
        i {
          float: right;
          margin-right: 20px;
          margin-top: 10px;
        }
      }
      .i-right:hover {
        i {
          transform: rotate(-180deg);
          transition: transform .3s;
        }
        .share-card-content {
          visibility: visible;
          opacity: 1;
        }
      }
      .share-icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-right: 8px;
        margin-left: 20px;
        background: url('~@/assets/img/share-icon.png') no-repeat center/cover;
      }
      .text-left::after {
        content: '';
        position: absolute;
        top: 12px;
        width: 1px;
        height: 12px;
        background-color: #6198fc;
        right: -0.5px;
        opacity: 1;
      }
      .el-icon-caret-bottom {
        font-size: 16px;
        color: #fff;
      }
      .share-card-content {
        position: absolute;
        right: -180px;
        top: 16px;
        width: 164px;
        height: 210px;
        box-shadow: 0 2px 12px 0 #0000001a;
        padding: 8px 6px;
        border-radius: 8px;
        background-color: #fff;
        visibility: hidden; // 隐藏
        opacity: 0; // 隐藏
        transition: all 0.6s ease-in-out;
        .triangle { // 三角型
          width: 0;
          height: 0;
          border-top: 15px solid transparent;
          border-right: 20px solid #fff;
          border-bottom: 15px solid transparent;
          left: -10px;
          top: 5px;
          position: absolute;
          z-index: -1;
        }
        li+li {
          margin-top: 4px;
        }
        .item-li {
          display: flex;
          align-items: center;
          color: #000000;
          height: 36px;
          padding: 0 12px;
          cursor: pointer;
        }
        .item-li:hover {
          background-color:rgba(243,245,247,1);
          color: #0052D9
        }
        .li-icon {
          width: 20px;
          height: 20px;
          margin-right: 8px;
        }
        .ai-icon{
          background: url('~@/assets/img/li-ai-icon.png') no-repeat center/cover;
        }
        .img-icon {
          background: url('~@/assets/img/li-img-icon.png') no-repeat center/cover;
        }
        .ask-icon {
          background: url('~@/assets/img/li-ask-icon.png') no-repeat center/cover;
        }
        .send-icon {
          background: url('~@/assets/img/li-send-icon.png') no-repeat center/cover;
        }
        .teach-icon {
          background: url('~@/assets/img/li-teach-icon.png') no-repeat center/cover;
        }
      }
    }
    .share-btn:hover {
      .text-left::after {
        opacity: 0;
      }
    }
    .menu-box {
      margin-top: 16px;
      .menu-ul {
        .menu-f-icon {
          display: inline-block;
          width: 20px;
          height: 20px;
          background: url('~@/assets/img/menu-icon.png') no-repeat center/cover;
          margin-right: 4px;
  
        }
        .menu-f {
          display: flex;
          align-items: center;
          padding-left: 20px;
          height: 36px;
          font-weight: bold;
        }
        li+li {
          margin-top: 4px;
        }
        li {
          padding-left: 32px;
          cursor: pointer;
          height: 36px;
          line-height: 36px;
          width: 156px;
          margin: auto;
          color: rgba(0,0,0,0.6);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        li:hover {
          background-color: #F5F7F9;
          color: #0052D9
        }
        .active-li {
          background-color: #F5F7F9;
          color: #0052D9
        }
      }
    }
  }
  .right-main {
    flex: 1;
    border-radius: 4px;
    background: #fff;
    margin-left: 12px;
  }
}
.menu-creator-container {
  height: 100%;
  .left-main {
    // height: 100%;
  }
}
</style>
