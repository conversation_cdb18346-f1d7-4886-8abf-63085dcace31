<template>
  <div v-if="(courseData.support_type && [2, 3].includes(courseData.support_type)) || !courseData.support_type" class="net-page" id="net-page" @drop="dropIframe" @dragover="allowDrop" :dt-areaid="dtOutCoursePage('area')" :dt-remark="dtOutCoursePage('remark')">
    <div class="contain-main">
      <div class="left">
        <div class="content-top">
          <div class="name">
            <span class="tag word">{{filterResourceName}}</span>
            <!-- <img class="geek-time" src="@/assets/mooc-img/comment/geek-time.png" alt="极客时间"> -->
            <span>{{ courseData.course_title }}</span>
          </div>
          <div class="info info1">
            <div class="info-left">
              <span class="time">{{ courseData.created_at }}</span>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content">
                  <span>{{ courseData.author }}</span><span v-if="courseData.author_intro">({{ courseData.author_intro }})</span>
                </div>
                <p class="create">
                  <span>{{ courseData.author }}</span><span v-if="courseData.author_intro">({{ courseData.author_intro | nameFilter }})</span>
                </p>
              </el-tooltip>
            </div>
            <div class="info-right">
              <span><i class="icon-view"></i>({{ zanAndcollect.view_count || 0 }})</span>
              <span v-if="isIndependent_course" @click="handleLikeOrFav(1)" :class="[zanAndcollect.isZan ? 'icon-zan-active' : '']"><i class="icon-zan"></i>({{ zanAndcollect.praise_count || 0 }})</span>
              <span v-if="isIndependent_course" ><i class="icon-comment"></i>({{ zanAndcollect.comment_count || 0 }})</span>
              <span v-if="isIndependent_course" @click="handleLikeOrFav(2)" :class="[zanAndcollect.isCollect ? 'icon-collect-active' : '']"><i class="icon-collect"></i> ({{ zanAndcollect.fav_count || 0 }})</span>
              <div class="jf-tip" v-if="isShowJfTip"><i class="jf-icon"></i>{{ $langue('Mooc_Common_Alert_CommonPoint', { point: 1, defaultText: '通用积分+1' }) }}</div>
            </div>
          </div>
          <div class="info info2">
            <div class="info-left">
              <div class="info-label" v-if="courseData.labels && courseData.labels.length">
                <span class="label">{{$langue('Article_Lable', { defaultText: '标签' })}}：</span>
                <div class="tag-list-box">
                  <div class="tag-list" v-for="(item, index) in courseData.labels" :key="index">
                    <el-tooltip effect="dark" :content="item.category_full_name" placement="top-start">
                      <span class="tag-value" @click="searchGo(item)">{{ item.label_name }}</span>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
            <div class="info-right right-icons">
              <!-- 打标签按钮 -->
              <!-- <i class="icon-addlabel"></i>
              &nbsp;
              <sdc-labeling v-model="courseData.labels" class="project-tag-box" :recommend="{
                  title: this.courseData.course_name,
                  desc: this.courseData.course_desc
                }"
                :course_name="this.courseData.course_name"
                :course_id="this.courseData.net_course_id"
                :labelNodeEnv="labelNodeEnv"
                @getSelectedLabelList="getSelectedLabelList"
                @input="input"
                style="cursor: pointer;"
              /> -->
              
              <!-- <Less v-model="courseData.labels" class="project-tag-box" :recommend="{
                    title: this.courseData.course_name,
                    desc: this.courseData.course_desc
                  }"
                  :course_name="this.courseData.course_name"
                  :course_id="this.courseData.net_course_id"
                   @getSelectedLabelList="getSelectedLabelList"></Less> -->
                  <!-- <span>&nbsp;</span> -->
              <!-- <span @click="addCourseDialogShow = true"><i class="icon-add"></i> {{ $langue('Article_AddCourseList', { defaultText: '添加到课单' }) }}</span> -->
              <div class="fullscreen-content" v-if="courseData.recourse_iframe_url" >
                <span style="margin-right: 0;" @click="handerAmplify"><img class="fullscreen-icon mgr-4" src="@/assets/outsourcedCourse/fullscreen_btn.png" alt="">全屏模式</span>
                <img v-if="showFullTip && !isDDI" class="triangle" src="" alt="">
                <div v-if="showFullTip && !isDDI" class="fullscreen-tip">课程画面太小？点此进入全屏模式 <img @click="showFullTip=false" class="close-btn" src="@/assets/outsourcedCourse/close.png" alt=""></div>
              </div>
              <div class="share-box">
                <span @click="handleShow()" :dt-areaid="dtCommon('areaid', '分享')" :dt-eid="dtCommon('eid', '分享')" :dt-remark="dtCommon('remark', '分享')">
                  <i class="icon-share mgr-4"></i>{{ $langue('Article_Share', { defaultText: '分享' }) }}
                </span>
                <img v-if="showShare && isDDI" class="triangle" src="" alt="">
                <div v-if="showShare && isDDI" class="share-tip">如果您觉得不错，也可以转发给其他有管理经验的小伙伴测一测哦~ <img @click="handleShare" class="close-btn" src="@/assets/outsourcedCourse/close.png" alt=""></div>
              </div>
            </div>
          </div>
          <div v-if="courseData.recourse_iframe_url" class="video-main">
          <!-- <div class="video-main"> -->
            <!-- 播放器-->
            <div :class="['content-box', { 'content-box-amplify': isAmplify }]" @drop="dropIframe" @dragover="allowDrop">
              <iframe class="outsourcedCourse-task-iframe video-box" id="geekTaskIframe" :src="courseData.recourse_iframe_url" frameborder="0" allowfullscreen></iframe>
              <FullscreenBtn ref="fullscreenBtn" :isFullscreen="isAmplify" @handerAmplify="handerAmplify"></FullscreenBtn>
            </div>
          </div>
          <div v-else class="video-box">
            <el-image lazy fit="fill" :src="courseData.photo_url ? courseData.photo_url : require('@/assets/img/default_bg_img.png')" class="item-image">
              <div slot="error" class="image-slot">
                <i class="default-icon-picture"></i>
              </div>
            </el-image>
          </div>
        </div>
        <div class="content-bottom">
          <el-tabs v-model="tabActiveName">
            <el-tab-pane 
            :label="$langue(tabItem.label, { defaultText: tabItem.text })" 
            :name="tabItem.name" 
            v-for="(tabItem) in tabList"
            :key="tabKey(tabItem.name)"
            >
          </el-tab-pane>
          </el-tabs>
          <div v-show="tabActiveName === 'desc'" class="desc-box">
            <sdc-mce-preview :urlConfig="editorConfig.urlConfig" :content="courseData.course_intro ?? '暂无简介'"></sdc-mce-preview>
          </div>
          <div v-show="tabActiveName === 'comment'" class="comment-box">
            <div v-if="isIndependent_course && loadComment" id="commentBox">
              <sdc-comment :params="commentParams" @setCommentCount="setCommentCount" />
            </div>
          </div>
        </div>
      </div>
      <div class="right">
        <div class="right-fix-btns" v-if="isIndependent_course">
          <el-tooltip
            effect="dark"
            :content="$langue('Mooc_ProjectDetail_BasicInfo_Prais', { defaultText: '点赞' })"
            popper-class="zanAndCollectTooltip"
            placement="left-start"
          >
            <div :class="[zanAndcollect.isZan ? 'do-zan-active' : 'do-zan']" @click="handleLikeOrFav(1)" ></div>
          </el-tooltip>
          <el-tooltip
            effect="dark"
            :content="$langue('Mooc_ProjectDetail_BasicInfo_collect', { defaultText: '收藏' })"
            popper-class="zanAndCollectTooltip"
            placement="left-start"
          >
            <div
              :class="[zanAndcollect.isCollect ? 'do-collect-active' : 'do-collect']" @click="handleLikeOrFav(2)"></div>
          </el-tooltip>
        </div>
      </div>
    </div>
    <!-- 分享 -->
    <ShareDialog ref="shareDialog" :isShow.sync="sharedialogShow" />
    <!-- 添加到课单 -->
    <AddCourseDialog :visible.sync="addCourseDialogShow" :itemData.sync="addCourseDialogData" />
  </div>
  <notSupported v-else :courseInfo="courseData"/>
</template>

<script>
import { 
  getGeekCourseDetail, 
  checkPraised, 
  addPraise, 
  deletePraise, 
  checkFavorited, 
  addFavorited, 
  deleteFavorite, 
  geekStudyRecord, 
  getSummaryData,
  closeDDIShare,
  showDDIShare
} from 'config/mooc.api.conf.js'
import { mapState } from 'vuex'
import translate from 'mixins/translate.vue'
import ShareDialog from '@/views/components/shareDialog'
import { AddCourseDialog } from '@/components/index'
import FullscreenBtn from './components/fullscreenBtn.vue'
import { pageExposure } from '@/utils/tools.js'
import env from 'config/env.conf.js'
import notSupported from './notSupported.vue'
export default {
  mixins: [translate],
  components: {
    ShareDialog,
    AddCourseDialog,
    FullscreenBtn,
    notSupported
  },
  data() {
    return {
      showFullTip: true,
      showShare: false,
      act_type: '102', // 课程类型 102-极客时间
      editorConfig: {
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/{contentId}`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview`
        }
      },
      zanAndcollect: { // 点赞数据
        isZan: false,
        isCollect: false,
        praise_count: 0, // 点赞数
        fav_count: 0, // 收藏数
        view_count: 0, // 浏览量
        comment_count: 0 // 评论量
      },
      commentParams: {},
      loadComment: false,
      courseData: { // 课程详情
        labels: []
      },
      sharedialogShow: false, // 分享
      addCourseDialogShow: false, // 添加到课单
      addCourseDialogData: { // 需要修改
        module_id: 1,
        module_name: '网络课'
      },
      countTimer: null, // 15s上报的timeId
      isShowJfTip: false, // 是否显示通用积分
      tabActiveName: 'desc',
      tabList: [
        { label: 'Mooc_TaskDetail_Audio_Description', name: 'desc', text: '简介' }
      ],
      // 上报id
      graphic_access_record_id: '',
      studyRecordErrNum: 0,
      isAmplify: false // 全屏
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          const hostUrl = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_PORTAL_HOST_WOA : process.env.VUE_APP_PORTAL_HOST
          this.commentParams = {
            userName: val.staff_name,
            actId: this.course_id,
            appId: 'A9BiosXihR0h46ThNsAX',
            scrollTarget: '.graphic-user-page',
            urlConfig: {
              getComments: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/-1/comment-page`,
              addComment: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/add-comment`,
              deleteComment: `${hostUrl}/training/api/outsourcedCourse/user/interaction/-1/delete-comment/`,
              like: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/praised-comment`, 
              sticky: `${hostUrl}/training/api/outsourcedCourse/user/interaction/-1/sticky-comment`,
              show: `${hostUrl}/training/api/outsourcedCourse/user/interaction/-1/show-comment`
            }
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState(['userInfo', 'moocLang']),
    // 独立课程
    isIndependent_course() {
      return this.courseData.independent_course === 1
    },
    // 课程id
    course_id() {
      return this.$route.query.course_id || ''
    },
    // mooc id
    mooc_course_id() {
      return this.$route.query.mooc_course_id || ''
    },
    // 类型
    filterResourceName() {
      let { course_type } = this.courseData
      let name = ''
      if (this.isVideoType) {
        name = this.$langue('Mooc_Common_ResourceType_Video', { defaultText: '视频' })
      } else if (course_type === 'graphic') {
        name = this.$langue('Mooc_Common_ResourceType_Article', { defaultText: '文章' })
      } else if (course_type === 'series') {
        // name = '专栏课程'
        name = '系列课程'
      }
      return name
    },
    isVideoType() {
      return ['video', 'Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(this.courseData.course_type)
    },
    tabKey() {
      return (name) => {
        return name + new Date().getTime()
      }
    },
    task_id() {
      return this.$route.query.task_id || ''
    },
    isDDI() {
      return this.courseData.recourse_from === 'DDIexam'
    },
    // 页面曝光
    dtOutCoursePage() {
      return (type) => {
        const { course_title } = this.courseData
        if (type === 'area') {
          return `area_${this.course_id}`
        } else if (type === 'remark') {
          return JSON.stringify({ 
            page: course_title,
            page_type: '外部课程详情页',
            container: course_title,
            act_type: '102',
            content_type: this.filterResourceName,
            terminal: 'PC'
          })
        } else {
          return ``
        }
      }
    },
    dtCommon() {
      return (type, val) => {
        const { course_title } = this.courseData
        if (type === 'remark') {
          return JSON.stringify({
            page: course_title,
            page_type: '外部课程详情页',
            container: course_title,
            act_type: '102',
            content_type: this.filterResourceName,
            terminal: 'PC',
            click_type: 'button',
            content_name: '分享'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${val}`
        } else {
          return `area_${this.course_id}_${val}`
        }
      }
    }
  },
  mounted() {
    window.addEventListener('beforeunload', () => {
      // 离开当前页面学习记录归档
      if (this.courseData.recourse_iframe_url) { 
        const recordParam = {
          from: this.$route.query.from || '',
          area_id: this.$route.query.area_id || '',
          course_id: this.course_id,
          sharer_id: this.$route.query.share_staff_id || '',
          sharer_name: this.$route.query.share_staff_name || '',
          from_type: this.courseData.recourse_from || 'geekBang',
          record_id: this.graphic_access_record_id
        }

        let blob = new Blob([JSON.stringify(recordParam)], { type: 'application/json; charset=UTF-8' })
        navigator.sendBeacon('/training/api/outsourcedCourse/user/info/study/record', blob) // 上报接口
      }
    })

    // 获取课程详情
    this.getCourseInfo()
    this.getZanAndCollectStatus()
    this.getSummaryInfo()
  },
  methods: {
    handleShare() {
      closeDDIShare(this.course_id).then((res) => {
        this.showShare = false
      })
    },
    creatViewTimer() {
      let _this = this
      let durtation = 0
      if (this.countTimer) clearInterval(this.countTimer)
      this.countTimer = setInterval(function () {
        durtation++
        if (durtation % 15 === 0) {
          _this.viewRecord() // 浏览器时长需每15秒记录一次
        }
      }, 1000)
    },
    // 学习上报
    viewRecord() {
      const recordParam = {
        from: this.$route.query.from || '',
        area_id: this.$route.query.area_id || '',
        course_id: this.course_id,
        sharer_id: this.$route.query.share_staff_id || '',
        sharer_name: this.$route.query.share_staff_name || '',
        from_type: this.courseData.recourse_from,
        record_id: this.graphic_access_record_id
      }
      // 上报接口
      geekStudyRecord(recordParam).then((data) => {
        if (data) {
          this.graphic_access_record_id = data
        }
        this.studyRecordErrNum = 0
      }).catch(() => {
        this.studyRecordErrNum++
        if (this.studyRecordErrNum >= 3) {
          clearInterval(this.countTimer)
          this.graphic_access_record_id = ''
        }
      })
    },
    // 课程详情
    getCourseInfo() {
      getGeekCourseDetail(this.course_id, { loading: true }).then(async (data) => {
        // console.log('课程详情：data: ', data)
        document.title = `${data.course_title}_Q-Learning`
        this.loadComment = true
        this.courseData = data
        this.courseData.labels = data.labels || []

        if (data.independent_course === 1) {
          this.tabList.push({ label: 'Mooc_ProjectDetail_Notice_Comments', name: 'comment', text: '评论' })
        }

        if (data.course_type === 'CaseStudy') {
          this.$messageBox(`${this.$langue('NetCourse_NotSupportRecourse', { defaultText: '暂不支持该类型资源查看！' })}！`, this.$langue('Mooc_TaskDetail_ThirdParty_Alert', { defaultText: '提示' }), {
            distinguishCancelAndClose: true,
            confirmButtonText: this.$langue('NetCourse_Ok', { defaultText: '好的' }),
            cancelButtonText: this.$langue('Mooc_Common_Alert_Cancel', { defaultText: '取消' })
          })
        }
        this.creatViewTimer()
        // ddi测评浮窗显示与否
        if (this.courseData.recourse_from === 'DDIexam') {
          this.showShare = await showDDIShare(this.course_id)
        }
        // 添加到课单
        const { course_title, course_intro, outsourced_course_id } = data
        const path = location.hostname.endsWith('.woa.com') ? `${process.env.VUE_APP_PORTAL_HOST_WOA}/training/outsourcedCourse/iframe/play?course_id=${outsourced_course_id}` : `${process.env.VUE_APP_PORTAL_HOST}/training/outsourcedCourse/iframe/play?course_id=${outsourced_course_id}`
        this.addCourseDialogData = {
          ...this.addCourseDialogData,
          content_name: course_title,
          cover_img_url: '', // 无封面图
          description: course_intro,
          href: path,
          item_id: outsourced_course_id,
          origin: location.origin
        }

        // 详情页曝光上报
        pageExposure({
          page_type: '外部课程iframe详情页',
          content_type: '网络课',
          act_type: '2',
          content_name: data.course_title,
          content_id: this.course_id
        })
      }).catch((err) => {
        if (err.code === 403 || err.code === 500) {
          sessionStorage.setItem('401Msg', err.message)
          this.$router.replace({
            name: '401'
          })
        }
      })
    },
    // 获取统计数据
    getSummaryInfo() {
      const params = { act_type: this.act_type, course_id: this.course_id }
      getSummaryData(params).then(res => {
        this.zanAndcollect = {
          ...this.zanAndcollect, ...res
        }
      })
    },
    // 点赞、收藏状态
    getZanAndCollectStatus() {
      const params = { act_type: this.act_type, course_id: this.course_id }
      checkPraised(params).then((res) => {
        this.zanAndcollect.isZan = res
      })
      checkFavorited(params).then(res => {
        this.zanAndcollect.isCollect = res
      })
    },
    // 点赞、收藏前置
    handleLikeOrFav(scene) {
      const params = { act_type: this.act_type, course_id: this.course_id }
      if (scene === 1) {
        // 点赞/取消点赞
        checkPraised(params).then(res => {
          const PAndFCommonAPI = res ? deletePraise : addPraise
          const tip = res ? this.$langue('Mooc_Common_Alert_CancelPraiseSucessed', { defaultText: '取消点赞成功' }) : this.$langue('Mooc_Common_Alert_PraiseSucessed', { defaultText: '点赞成功' })
          this.zanAndcollect.praise_count = res ? (this.zanAndcollect.praise_count === null || this.zanAndcollect.praise_count === 0 ? 0 : this.zanAndcollect.praise_count - 1) : this.zanAndcollect.praise_count + 1
          this.handlerCommonInt(PAndFCommonAPI, params, tip)
        })
      } else {
        // 收藏/取消收藏
        checkFavorited(params).then(res => {
          const PAndFCommonAPI = res ? deleteFavorite : addFavorited
          const tip = res ? this.$langue('Mooc_Common_Alert_CancelCollectSucessed', { defaultText: '取消收藏成功' }) : this.$langue('Mooc_Common_Alert_CollectSucessed', { defaultText: '收藏成功' })
          this.zanAndcollect.fav_count = res ? (this.zanAndcollect.fav_count === null || this.zanAndcollect.fav_count === 0 ? 0 : this.zanAndcollect.fav_count - 1) : this.zanAndcollect.fav_count + 1
          this.handlerCommonInt(PAndFCommonAPI, params, tip)
        })
      }
    },
    // 点赞、收藏
    handlerCommonInt(PAndFCommonAPI, params, tip) {
      PAndFCommonAPI(params).then(data => {
        if (PAndFCommonAPI === addPraise || PAndFCommonAPI === deletePraise) this.zanAndcollect.isZan = PAndFCommonAPI === addPraise ? Boolean(true) : Boolean(false)
        if (PAndFCommonAPI === addFavorited || PAndFCommonAPI === deleteFavorite) this.zanAndcollect.isCollect = PAndFCommonAPI === addFavorited ? Boolean(true) : Boolean(false)
        if (PAndFCommonAPI === addPraise || PAndFCommonAPI === addFavorited) {
          if (data.credit && data.credit !== '0') {
            this.isShowJfTip = true
            setTimeout(() => {
              this.isShowJfTip = false
            }, 6000)
            this.$message.success(`${tip}，${this.$langue('Mooc_Common_Alert_CommonPoint', { point: +data.credit, defaultText: `通用积分+${+data.credit}` })}`)
          } else this.$message.success(tip)
        } else this.$message.success(tip)
      })
    },
    // 点击标签跳转
    searchGo(item) {
      const envName = env[process.env.NODE_ENV]
      let href = `${envName.trainingPath}label-subs?isLabelGatherPage=true&label_id=${item.label_id}&label_name=${item.label_name}`
      window.open(href)
    },
    // 评论
    setCommentCount() { },
    // 分享
    handleShow() {
      this.sharedialogShow = true
      let params = {}
      let userInfo = JSON.parse(sessionStorage.getItem('login_user'))
      if (window.top === window.self) { // iframe独立访问
        params = { 
          url: `https://sdc.qq.com/s/MGoSJh?scheme_type=outsourced&resource_type=iframe&course_id=${this.course_id}&share_staff_id=${userInfo.staff_id}&share_staff_name=${userInfo.staff_name}`,
          taskTitle: this.courseData.course_title,
          scene: `${this.course_id}_zh-CN_iframe`,
          page: 'pages/webview/outsourced/iframe/index'
        }
      } else { // 嵌套在mooc
        const url = process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/TZELHU' : 'http://s.test.yunassess.com/s/hoo9Gg'
        params = {
          url: `${url}?scheme_type=mooc&mooc_course_id=${this.mooc_course_id}&task_id=${this.task_id}&from=mooc`,
          scene: `${this.mooc_course_id}_${this.task_id}_mooc`,
          page: 'pages/webview/mooc/taskDetail'
        }
      }
      const customText = `【腾讯学堂】 ${this.userInfo.staff_name}向你推荐了一个有趣的游戏化管理测评，快来一起参与挑战吧~[${params.url}]`
      params.customText = customText
      this.$nextTick(() => {
        this.$refs.shareDialog.initCode(params)
      })
    },
    handerAmplify() {
      this.isAmplify = !this.isAmplify
    },
    dropIframe(event) {
      console.log('event: ', event)
    },
    allowDrop(event) {
      event.preventDefault()
    }
  },
  filters: {
    nameFilter(value) {
      let limitNum = 40
      if (value.length > limitNum) {
        return value.substr(0, limitNum / 2) + '...' + value.substr(value.length - limitNum / 2)
      }
      return value
    }
  },
  beforeDestroy() {
    clearInterval(this.countTimer)
    this.countTimer = null
  }
}
</script>
<style lang="less">
</style>
<style lang="less" scoped>
.net-page {
  width: 100%;
  height: 100%;
  overflow: auto;
  .contain-main {
    width: 100%;
    display: flex;
    justify-content: center;
    padding-top: 10px;

    .left {
      width: 1148px;
      background-color: #fff;
      padding: 24px;
      margin-bottom: 20px;

      .content-top {
        .name {
          display: flex;
          align-items: center;
          color: #000000;
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 22px;
          word-break: break-word;

          span:last-child {
            flex: 1;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }

        .tag {
          padding: 0 6px;
          border-radius: 2px;
          margin-right: 10px;
        }

        .word {
          color: #0052D9;
          border: 1px solid #0052D9;
        }

        .geek-time {
          height: 20px;
          margin-right: 10px;
        }

        .info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 16px;

          .info-left,
          .info-right {
            display: flex;
          }

          .info-left {

            .create,
            .time {
              text-overflow: ellipsis;
              overflow: hidden;
              word-break: break-all;
              white-space: nowrap;
              color: rgba(0, 0, 0, 0.6);
            }

            .time {
              // width: 140px;
              max-width: 180px;
            }

            .create {
              max-width: 624px;
              margin-left: 16px;
            }

            .info-classify,
            .info-label {
              display: flex;

              .label {
                flex-shrink: 0;
                color: rgba(0, 0, 0, 0.4) !important;
              }

              p {
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                white-space: nowrap;

                span {
                  cursor: pointer;
                  color: #3464E0;

                  i {
                    font-style: normal;
                  }
                }
              }
            }

            .info-label {
              line-height: 20px;

              .tag-list-box {
                display: flex;
                flex-wrap: wrap;
              }

              .tag-value {
                background-color: rgba(235, 239, 252, 1);
                height: 20px;
                font-size: 12px;
                color: rgba(0, 82, 217, 1);
                padding: 4px;
                border-radius: 2px;
                display: inline-block;
                margin-right: 12px;
                line-height: 10px;
                cursor: pointer
              }
            }

            .fh {
              color: rgba(0, 0, 0, 0.4);
            }
          }

          .info-right {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.4);
            flex-shrink: 0;
            .share-box {
              position: relative;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            span {
              display: flex;
              align-items: center;
              justify-content: center;
              width: fit-content;
              margin-right: 18px;
            }

            span:nth-child(2),
            span:nth-child(4),
            .editor {
              cursor: pointer;
            }

            i {
              display: inline-block;
              width: 14px;
              height: 14px;
            }

            .icon-view {
              background: url("~@/assets/img/watch.png") no-repeat center /cover;
            }

            .icon-zan {
              background: url("~@/assets/img/zan1.png") no-repeat center /cover;
            }

            .icon-zan-active {
              color: #3464e0;

              i {
                background: url("~@/assets/img/zan1-active.png") no-repeat center /cover;
              }
            }

            .icon-comment {
              background: url("~@/assets/img/comment.png") no-repeat center /cover;
            }

            .icon-collect {
              background: url("~@/assets/img/fav2.png") no-repeat center /cover;
            }

            .icon-collect-active {
              color: #3464e0;

              i {
                background: url("~@/assets/img/fav2-active.png") no-repeat center /cover;
              }
            }

            .icon-add {
              background: url("~@/assets/img/add.png") no-repeat center / cover;
            }

            .icon-share {
              background: url("~@/assets/img/share.png") no-repeat center /cover;
            }

            .jf-icon {
              background: url("~@/assets/img/integral-icon.png") no-repeat center / cover;
              display: block;
              width: 20px;
              height: 20px;
              margin-right: 4px;
            }

            .jf-tip {
              color: #ff7548;
              position: absolute;
              right: 20px;
              top: -22px;
              display: flex;
              align-items: center;
            }
          }

          .right-icons {
            color: rgba(0, 0, 0, 0.6);

            span {
              margin-right: 16px;
              cursor: pointer;
            }
            .fullscreen-content {
              position: relative;
              padding: 4px 8px 4px 6px;
              border-radius: 2px;
              background: #F2F6FF;
              margin-right: 16px;
              cursor: pointer;
            }
            .fullscreen-icon {
              width: 16px;
            }
            .triangle {
              position: absolute;
              right: 0;
              bottom: -8px;
              width: 45px;
              height: 6px;
              background-image: url('~@/assets/outsourcedCourse/subtract.png');
            }
            .fullscreen-tip{
              position: absolute;
              right: 0;
              bottom: -54px;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 274px;
              height: 46px;
              font-size: 14px;
              color: #ffffff;
              border-radius: 6px;
              background: var(---Brand7-Hover, #266FE8);
              z-index: 1;
              cursor: default;
            }
            .share-tip{
              position: absolute;
              right: 0;
              bottom: -64px;
              display: flex;
              align-items: center;
              justify-content: center;
              width: 320px;
              line-height: 18px;
              font-size: 14px;
              color: #ffffff;
              border-radius: 6px;
              background: var(---Brand7-Hover, #266FE8);
              z-index: 1;
              cursor: default;
              padding: 10px 16px;
            }
            .close-btn {
              width: 16px;
              height: 16px;
              margin-left: 16px;
              cursor: pointer;
            }
            .mgr-4 {
              margin-right: 4px;
            }
          }
        }

        .info1{
          height: 22px;
          line-height: 22px;
          .info-right {
            position: relative;
          }
        } 

        .info2 {
          border-bottom: solid 1px #eeeeee;
          padding-bottom: 8px;
          margin-bottom: 20px;
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
        }

        .video-box {
          width: 1100px;
          height: 619px;
          border-radius: 4px;
          background-color: #F8F8F8;
          border: solid 1px #ECECEC;

          :deep(.el-image) {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .default-icon-picture {
              display: inline-block;
              width: 400px;
              height: 300px;
              background: url('~@/assets/img/default_bg_img.png') no-repeat;
            }
          }
        }
        .video-main {
          width: 100%;
          min-height: 460px;
          position: relative;
          .content-box {
            width: 100%;
            height: 100%;
            position: relative;
          }
          .content-box-amplify {
            width: 100vw;
            min-width: 1024px;
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            z-index: 99999;
            .outsourcedCourse-task-iframe {
              width: 100%;
              height: 100%;
            }
          }
        }
      }

      .content-bottom {
        margin-top: 36px;
        .caption-box {
          position: relative;
          border: 1px solid rgba(238, 238, 238, 1);
          border-radius: 3px;
        }
      }

      :deep(.el-tabs) {
        margin-bottom: 20px;

        .el-tabs__header {
          border-bottom: solid 1px #eeeeee;
          margin: 0px;
        }

        .el-tabs__item {
          color: rgba(0, 0, 0, 0.4);
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 10px
        }

        .is-active {
          color: #0052D9 !important;
          font-weight: 700;
        }
      }
    }

    .right {
      // width: 272px;
      height: 100%;
      margin-left: 20px;

      .mbt-20 {
        margin-bottom: 20px;
      }

      .right-fix-btns {
        position: fixed;
        bottom: 80px;
        div {
          width: 50px;
          height: 50px;
          box-shadow: 0 0 4px 0 rgba(102, 102, 102, 0.3);
          border-radius: 50%;
          cursor: pointer;
          margin-bottom: 20px;
        }
        div:last-child {
          margin: 0;
        }
        .do-zan {
          background: url('~@/assets/img/do-zan.png') no-repeat center / cover;
        }
        .do-zan:hover,
        .do-zan-active:hover {
          background: url('~@/assets/img/do-zan-hover.png') no-repeat center /
            cover;
        }
        .do-zan-active {
          background: url('~@/assets/img/do-zan-active.png') no-repeat center /
            cover;
        }
        .do-collect {
          background: url('~@/assets/img/do-fav.png') no-repeat center / cover;
        }
        .do-collect:hover,
        .do-collect-active:hover {
          background: url('~@/assets/img/do-fav-hover.png') no-repeat center /
            cover;
        }
        .do-collect-active {
          background: url('~@/assets/img/do-fav-active.png') no-repeat center /
            cover;
        }
      }
    }
  }
}
</style>
