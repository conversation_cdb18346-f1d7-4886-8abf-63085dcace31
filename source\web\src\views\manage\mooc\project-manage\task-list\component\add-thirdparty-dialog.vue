<template>
  <div class="add-thirdparty-dialog">
    <el-dialog
      width="900px"
      :visible="visible"
      :title="type === 'edit' ? '编辑第三方任务' : '添加第三方任务'"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <!-- 第三方任务 -->
      <div class="third-party">
        <el-form ref="form" :model="form" :rules="rules" label-width="120px" size="medium">
          <el-form-item label="任务名称" prop="task_name">
            <el-input v-model="form.task_name" maxlength="50" show-word-limit clearable></el-input>
          </el-form-item>
          <el-form-item label="完成条件说明" prop="finished_condition_desc">
            <el-input v-model="form.finished_condition_desc" maxlength="100" show-word-limit clearable></el-input>
          </el-form-item>
          <el-form-item class="coustom-form-url" label="任务链接" prop="urlRules" :rules="urlRules()">
            <el-row class="flex-row">
              <el-tag size="small" class="tag">PC端</el-tag>
              <el-input v-model="form.resource_url" placeholder="请输入跳转链接" clearable></el-input>
              <el-button class="test-link" type="text" @click="urlTest">测试跳转</el-button>
            </el-row>
            <el-row class="flex-row">
              <el-tag size="small" class="tag">移动端</el-tag>
              <el-radio v-model="form.mobile_resource_type" label="H5">H5页面</el-radio>
              <el-radio v-model="form.mobile_resource_type" label="WechatMini">小程序</el-radio>
            </el-row>
            <el-row class="flex-row" v-if="form.mobile_resource_type === 'WechatMini'">
              <div class="tag-empty"></div>
              <el-input v-model="form.wechat_mini_appid" placeholder="请输入小程序AppID" clearable></el-input>
            </el-row>
            <el-row class="flex-row">
              <div class="tag-empty"></div>
              <el-input v-model="form.resource_url_mobile" :placeholder="form.mobile_resource_type === 'H5' ? '请输入移动端跳转链接' : '请输入小程序页面路径'" clearable></el-input>
              <el-button class="test-link" type="text" @click="redirectTo('config')">查看配置要求</el-button>
            </el-row>
          </el-form-item>
          <CustomTips
            title="请确保填写的跳转链接能在对应的终端打开，链接为空时无法跳转" 
            IconName="el-icon-warning" 
            backgroundColor="#fdf6ec" 
            color="#FF7548"
            lineHeight="30px"
            class="outlink-custom-tips"
            >
          </CustomTips>
          <el-form-item label="结果查询接口" prop="third_party_interface">
            <el-input v-model="form.third_party_interface" type="textarea" rows="4" placeholder="请填写结果查询接口"></el-input>
            <el-button class="test-link" type="text" @click="redirectTo('explain')">查询接口说明文档</el-button>
          </el-form-item>
          <el-form-item label="补充信息显示" class="extensions" prop="extensions">
            <el-table :data="form.extensions" v-if="form.extensions.length">
              <el-table-column prop="key" label="变量名称" width="285">
                <template slot-scope="scope">
                  <div class="extensions-column">
                    <el-input v-if="scope.row.isEditkey" clearable maxlength="50" show-word-limit v-model="scope.row.key" @blur="blurExtensionsInput(scope)"></el-input>
                    <span v-else>{{ scope.row.key }}</span>
                    <i class="el-icon-edit" style="margin-left: 12px; cursor: pointer;" @click="editExtensions(scope)"></i>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="显示名称" width="285">
                <template slot-scope="scope">
                  <div class="extensions-column">
                    <el-input v-if="scope.row.isEditname" clearable maxlength="50" show-word-limit v-model="scope.row.name" @blur="blurExtensionsInput(scope)"></el-input>
                    <span v-else>{{ scope.row.name }}</span>
                    <i class="el-icon-edit" style="margin-left: 12px; cursor: pointer;" @click="editExtensions(scope)"></i>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作">
                <template slot-scope="scope">
                  <!-- <el-button type="text" @click="handleEdit(scope.row, scope.$index)">编辑</el-button> -->
                  <el-button class="checkout" type="text" @click="handleDelete(scope.$index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-button class="test-link" type="text" @click="addField">+ 添加显示字段</el-button>
          </el-form-item>
          <CustomTips
          title="补充信息将会展示给学员，请确保第三方平台能够在接口返回的补充信息中包含设定的显示字段" 
          IconName="el-icon-warning" 
          backgroundColor="#fdf6ec" 
          color="#FF7548"
          lineHeight="30px"
          class="outlink-custom-tips"
          >
        </CustomTips>
          <el-form-item label="任务详细说明" class="thirdparty-detail-tincy">
            <sdc-mce-editor 
            ref="editor" 
            :env="editorEnv" 
            :content="form.task_desc"
            :catalogue.sync="editorConfig.catalogue"
            :urlConfig="editorConfig.urlConfig"
            :options="editorConfig.options"
            :insertItems="insertItems"
            />
          </el-form-item>
          <el-form-item label="任务性质：">
            <el-radio-group v-model="form.required">
              <el-radio :label="true">应学</el-radio>
              <el-radio :label="false">选学</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="任务状态：">
            <el-radio-group v-model="form.lock_status">
              <el-radio :label="1">解锁任务</el-radio>
              <el-radio :label="2">锁定任务</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <!-- <el-dialog
          :title="fieldDialogInfo.type === 'create'?'添加显示字段':'编辑显示字段'"
          :visible.sync="fieldDialogInfo.visible"
          width="430px"
          :before-close="closeFieldEdit">
          
          <el-form :model="fieldForm" size="small" :rules="fieldFormRules" ref="fieldForm" label-width="80px" class="demo-ruleForm">
            <el-form-item label="变量名称" prop="key">
              <el-input v-model="fieldForm.key" clearable placeholder="请输入接口返回的变量名称"></el-input>
            </el-form-item>
            <el-form-item label="显示名称" prop="name">
              <el-input v-model="fieldForm.name" maxlength="10" show-word-limit clearable placeholder="请输入学员界面的显示名称"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="closeFieldEdit" size="small">取 消</el-button>
            <el-button type="primary" @click="confirmFieldEdit" size="small">确 定</el-button>
          </span>
        </el-dialog> -->
      </div>
      <div slot="footer" class="dialog-footer">
        <convention-confirm v-model="isChooseConvention" style="margin-left: 20px;" />
        <el-button size="small" @click="cancel" style="margin-left: auto;">取 消</el-button>
        <el-button size='small' @click="submit" type="primary" :disabled="!isChooseConvention">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import pager from '@/mixins/pager'
import CustomTips from '@/components/tips.vue'
import conventionConfirm from '@/views/components/convention-confirm.vue'
export default {
  mixins: [pager],
  components: {
    CustomTips,
    conventionConfirm
  },
  props: {
    type: {
      type: String,
      default: 'create'
    },
    visible: {
      type: Boolean,
      default: false
    },
    currentNode: {
      type: Object
    },
    treeNode: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    // const extensionsKey = (rule, value, callback) => {
    //   console.log(rule, value)
    //   let flag = value.some(item => item.key)
    //   if (!flag) {
    //     callback(new Error('请输入变量名称'))
    //   }
    // }
    // const extensionsName = (rule, value, callback) => {
    //   console.log(rule, value)
    //   let flag = value.some(item => item.name)
    //   if (!flag) {
    //     callback(new Error('请输入显示'))
    //   }
    // }
    return {
      form: {
        extensions: [],
        required: true,
        lock_status: 1,
        mobile_resource_type: 'H5'
      },
      rules: {
        task_name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        finished_condition_desc: [{ required: true, message: '请输入完成条件说明', trigger: 'blur' }],
        third_party_interface: [{ required: true, message: '请填写结果查询接口', trigger: 'blur' }]
        // extensions: [{ required: true, validator: extensionsKey, trigger: 'blur' }],
        // extensionsName: [{ required: true, validator: extensionsName, trigger: 'blur' }]
      },
      insertItems: [
        'link',
        'table',
        'image',
        'profile',
        'embed',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorConfig: {
        catalogue: false,
        options: { // 非必传字段
          selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
              formatselect fontsizeselect lineheight |
              bold italic underline strikethrough |
              forecolor backcolor |
              dent align bullist numlist |
              insert codesample |
              fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      },
      editorEnv: process.env.NODE_ENV,
      fieldDialogInfo: {},
      fieldForm: {},
      fieldFormRules: {
        key: [
          { required: true, message: '请输入变量名称', trigger: 'blur' }
        ],
        name: [
          { required: true, message: '请输入显示名称', trigger: 'blur' }
        ]
      },
      isEditExtensions: false,
      isChooseConvention: false,
      setting: {
        act_type: '23',
        columns: [
          {
            column_code: 'task_name',
            column_name: '任务名称',
            column_type: 'text',
            call_type: ['sync'],
            manual_review: false
          },
          {
            column_code: 'finished_condition_desc',
            column_name: '完成条件说明',
            column_type: 'text',
            call_type: ['sync'],
            manual_review: false
          },
          {
            column_code: 'task_desc',
            column_name: '任务详细说明',
            column_type: 'richText',
            call_type: ['sync'],
            manual_review: false
          }
        ]
      }
    }
  },
  methods: {
    // 初始化数据
    initData(data) {
      this.form = {
        ...data
      }
      if (typeof (data.extensions) === 'string') {
        if (data.extensions) {
          this.form.extensions = JSON.parse(data.extensions)
        } else {
          this.form.extensions = []
        }
      }     
    },
    urlTest () {
      const reg = /(http|https):\/\/([\w.]+\/?)\S*/
      if (!this.form.resource_url) return this.$message.error('请输入链接')
      else if ((this.form.resource_url && !reg.test(this.form.resource_url))) return this.$message.error('原文链接请输入http://或https://开头的链接地址')
      window.open(this.form.resource_url)
    },
    redirectTo(type) {
      if (type === 'config') {
        window.open('https://iwiki.woa.com/pages/viewpage.action?pageId=4008670334')
      } else if (type === 'explain') {
        window.open('https://iwiki.woa.com/pages/viewpage.action?pageId=4008525899')
      }
    },
    submit() {
      // let flagName = false
      // const compareName = (list) => {
      //   list.forEach((e) => {
      //     if (e.task_name === this.form.task_name && this.currentNode.id !== e.id) {
      //       flagName = true
      //     }
      //     if (e.sub_tasks?.length) {
      //       compareName(e.sub_tasks)
      //     }
      //   })
      // }
      // compareName(this.treeNode)
      // if (flagName) {
      //   this.$message.warning('添加失败，项目中已存在相同任务名称的外部链接')
      //   return
      // }

      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.form.extensions.length) {
            let flag = true
            let handleData = this.form.extensions.map(item => {
              if (!item.key || !item.name) flag = false
              return {
                'key': item.key,
                'name': item.name
              }
            })
            if (!flag) {
              this.$message.warning('请填写补充信息')
              return
            }
            this.form.extensions = JSON.stringify(handleData)
          } else {
            this.form.extensions = ''
          }
          this.form.task_desc = this.$refs['editor'].getContent()
          window.$informationReview && window.$informationReview.contentReview(this.form, this.setting).then(res => {
            if (res.success && res.status_code * 1 === 1) return

            this.$emit('updateTreeList', [{
              ...this.form,
              id: this.currentNode.id || '',
              resource_type: 'ThirdParty',
              resource_type_name: '第三方任务',
              module_id: -1000,
              act_type: 23
            }], this.type)
            this.$emit('update:visible', false)
          }).catch(err => {
            console.log(err, 'rrr')
          })
        } else {
          return false
        }
      })
      console.log(this.form)
    },
    cancel() {
      this.$emit('update:visible', false)
    },
    addField() {
      this.fieldDialogInfo = {
        visible: true,
        type: 'create'
      }
      this.form.extensions.push({
        key: 'key',
        name: 'name'
      })
    },
    editExtensions(scope) {
      console.log(scope)
      this.$set(this.form.extensions[scope.$index], `isEdit${scope.column.property}`, true)
    },
    blurExtensionsInput(scope) {
      if (!scope.row[scope.column.property]) {
        this.$message.warning(`请输入${scope.column.label}`)
      } else {
        this.$set(this.form.extensions[scope.$index], `isEdit${scope.column.property}`, false)
      }
    },
    handleEdit(row, index) {
      this.fieldDialogInfo = {
        visible: true,
        type: 'edit',
        index 
      }
      this.fieldForm = {
        key: row.key,
        name: row.name
      }
    },
    handleDelete(index) {
      this.form.extensions.splice(index, 1)
    },
    closeFieldEdit() {
      this.fieldDialogInfo.visible = false
      this.fieldForm = {}
    },
    confirmFieldEdit() {
      this.$refs['fieldForm'].validate((valid) => {
        if (valid) {
          if (this.fieldDialogInfo.type === 'edit') {
            this.form.extensions[this.fieldDialogInfo.index].key = this.fieldForm.key
            this.form.extensions[this.fieldDialogInfo.index].name = this.fieldForm.name
          } else {
            this.form.extensions.push(this.fieldForm)
          }
          this.closeFieldEdit()
        }
      })
    },
    urlRules() {
      let validator = (rule, value, callback) => {
        const { resource_url, resource_url_mobile, mobile_resource_type, wechat_mini_appid } = this.form
        const reg = /(http|https):\/\/([\w.]+\/?)\S*/
        if (!resource_url_mobile && !resource_url) {
          callback(new Error('请输入课程跳转链接'))
        } else {
          if ((resource_url && !reg.test(resource_url)) || (resource_url_mobile && mobile_resource_type === 'H5' && !reg.test(resource_url_mobile))) {
            callback(new Error('格式错误，请输入http或https开头的链接地址'))
          } else if (mobile_resource_type === 'WechatMini' && !wechat_mini_appid && resource_url_mobile) {
            callback(new Error('请输入小程序AppID'))
          } else if (reg.test(value)) {
            this.$refs['form'].clearValidate('resource_url')
            callback()
          }
          callback()
        } 
      }
      return {
        validator,
        trigger: ['blur'],
        required: true
      }
    }
  }
}
</script>
<style lang="less" scoped>
.third-party{
  :deep(.el-form-item__content){
    .el-input,.el-textarea{
      width: 524px;

    }
  }
  .coustom-form-url {
    margin-bottom: 20px;
    :deep(.el-form-item__label::before) {
      content: '*';
      color: #F81D22;
      margin-right: 4px;
    }
    .text-blue {
      margin-left: 10px;
    }
    .flex-row{
      display: flex;
      align-items: center;
      .el-input {
        width: 452px;
      }
      .tag{
        text-align: center;
        width: 60px;
        margin-right: 10px;
        height: 20px;
        line-height: 20px;
        color: #0052D9;
        background-color: #EAEFFC;
        border: unset;
      }
      .tag-empty{
        width: 60px;
        margin-right: 10px;
      }
      :deep(.el-radio){
        font-weight: 400;
        .el-radio__label{
          font-size: 12px;
        }
      }
    }
    .flex-row:first-child{
      margin-top: 0;
    }
    .flex-row{
      margin-top: 20px;
    }
  }
  .test-link{
    margin-left: 12px;
  }
  .outlink-custom-tips {
    margin-bottom: 20px;
    margin-left: 120px;
  }
  :deep(.thirdparty-detail-tincy) {

    .tox.tox-tinymce {
      border: 1px solid #ccc !important;
      height: 180px;

      .tox-sidebar-wrap .tox-edit-area {
        min-height: 180px !important;
      }
    }
  }
  .extensions{
    .extensions-column{
      display: flex;
      align-items: center;
      :deep(.el-input__inner) {
        padding-right: 75px;
      }
    }
    .el-input{
      width: 225px;
    }
  }
}
.dialog-footer {
  display: flex;
  align-items: center;
}

</style>
