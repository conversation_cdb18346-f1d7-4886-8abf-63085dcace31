<template>
  <el-dialog class="has-course-dialog" title="已兑课程" :visible.sync="dialogVisible" width="856px" @open="handlerOpen">
    <div class="content-xueba">
      <el-form :inline="true" :model="hasData" class="has-form-inline">
        <el-form-item label="内容来源">
          <el-select v-model="hasData.sourceFrom" placeholder="内容来源" clearable>
            <el-option v-for="item in sourceForm" :key="item.sourceFrom" :label="item.sourceFromName" :value="item.sourceFrom">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="内容类型">
          <el-select v-model="hasData.actType" placeholder="内容类型" clearable>
            <el-option v-for="item in optionsAct" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button icon="el-icon-refresh" size="small" @click="reset">重置</el-button>
          <el-button type="primary" size="small" @click="onSubmit">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="tableData" style="width: 100%;" class="custom-table-header" height="519px" :header-cell-style="headerCellStyle">
        <el-table-column label="名称" width="349" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="url_link" @click="handlerOpenUrlCurse(scope.row)">{{scope.row.course_name}}</span>
          </template>
        </el-table-column>
        <el-table-column label="内容来源" width="100">
          <template slot-scope="scope"> {{scope.row.source_from_name}} </template>
        </el-table-column>
        <el-table-column label="内容类型" width="100">
          <template slot-scope="scope"> {{scope.row.act_type_name}} </template>
        </el-table-column>
        <el-table-column label="兑换时间" width="240">
          <template slot-scope="scope"> {{scope.row.created_at}} </template>
        </el-table-column>
      </el-table>
      <el-pagination class="pagination-coures-dialog" :pager-count="5" background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pagination.current" :page-sizes="[10, 30, 50, 100]" :page-size="pagination.size" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total">
      </el-pagination>
    </div>
    <span slot="footer" class="dialog-footer">
      <!-- <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="dialogVisible = false">确 定</el-button> -->
    </span>
  </el-dialog>
</template>

<script>
import { getActiveOrders, outsourceSourceFrom } from '@/config/mooc.api.conf.js'
const defaultData = {
  sourceFrom: '',
  actType: ''
}
export default {
  props: ['isShow'],
  data() {
    return {
      pagination: {
        current: 1,
        size: 10,
        total: 100
      },
      sourceForm: [],
      hasData: Object.assign({}, defaultData),
      optionsAct: [{ label: '培养项目', value: 11 }],
      tableData: []
    }
  },
  computed: {
    dialogVisible: {
      set(val) {
        this.$emit('update:isShow', val)
      },
      get() {
        return this.isShow
      }
    },
    headerCellStyle() {
      return {
        background: '#F5F5F5',
        color: '#00000099',
        fontSize: '14px',
        fontWeight: '400'
      }
    }
  },
  mounted() {
    this.outsourceSourceFrom()
  },
  methods: {
    async outsourceSourceFrom() {
      const res = await outsourceSourceFrom({})
      this.sourceForm = res
    },
    handlerOpen() {
      this.current = 1
      this.hasData = Object.assign({}, defaultData)
      this.getActiveOrders()
    },
    async getActiveOrders() {
      const { sourceFrom, actType } = this.hasData
      let params = {
        current: this.pagination.current,
        size: this.pagination.size,
        sourceFrom: sourceFrom,
        actType: actType
      }
      const res = await getActiveOrders(params)
      this.pagination.total = res.total
      this.tableData = res.records || []
      console.log(res, '已兑换课程列表')
    },
    handlerOpenUrlCurse(val) {
      window.open(val.course_url, '_blank')
    },
    onSubmit() {
      this.pagination.current = 1
      this.getActiveOrders()
    },
    reset() {
      this.pagination.current = 1
      this.hasData = Object.assign({}, defaultData)
      this.getActiveOrders()
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.getActiveOrders()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getActiveOrders()
    }
  }
}
</script>
<style lang="less">
.content-xueba {
  padding: 0 32px;
  .el-button--default {
    background-color: #fff;
    color: #0052d9;
    border: 1px solid #0052d9;
  }
  .el-button--default:hover {
    background-color: #fff;
    color: #0052d9;
    border: 1px solid #0052d9;
  }
}
.el-select-dropdown{
  .el-select-dropdown__item.selected{
    color: #0052d9;
    font-weight: 600;
    font-family: "PingFang SC";
    span{
      color: #0052d9;
    }
  }
}
</style>
<style lang="less" scoped>
.url_link {
  color: #0052d9;
  cursor: pointer;
}
.has-course-dialog {
  /deep/.el-dialog {
    border-radius: 8px;
  }
  /deep/.el-dialog__footer {
    padding: 0;
  }
  /deep/.el-dialog__header {
    padding: 24px 32px;
    border-bottom: 1px solid #e7e7e7;
    .el-dialog__title{
      color: #000000e6;
      font-family: "PingFang SC";
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
    }
    .el-dialog__headerbtn{
      top: 28px;
      right: 32px
    }
  }
  /deep/.el-dialog__body {
    max-height: 830px;
    padding: 16px 0;
    overflow: auto;
  }
}
.custom-table-header {
  border: 1px solid #f5f5f5;
  border-bottom: 0px;
  border-radius: 4px;
  /deep/ .el-table__header-wrapper{
    .gutter{
      background: rgb(245, 245, 245);
    }
  }
}
.el-select-dropdown{
  .el-select-dropdown__item.selected{
    color: #0052d9;
    font-weight: 600;
    font-family: "PingFang SC";
    span{
      color: #0052d9;
    }
  }
}
/deep/ .el-table__row{
  .cell{
    color: #000000e6;
    font-family: "PingFang SC";
    font-size: 14px;
    line-height: 22px;
  }
}
.has-form-inline {
  background: #f8f8f8;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  /deep/.el-form-item {
    margin-bottom: 0px;
    margin-right: 24px;
    .el-form-item__label{
      color: #00000099;
      font-family: "PingFang SC";
      font-size: 14px;
      font-weight: 400;
    }
    .el-form-item__content{
      max-width: 195px;
    }
    &:last-child{
      margin-right: 0;
      margin-left: 16px;
      button{
        font-family: "PingFang SC";
        font-size: 14px;
        font-weight: 400;
        width: 80px;
        height: 32px;
      }
      .el-button--primary{
        background: #0052D9;
        margin-left: 16px;
      }
    }
  }
}
.pagination-coures-dialog {
  position: relative;
  /deep/.el-input__inner {
    height: 32px;
    border: 1px solid var(--Gray-Gray4-, #dcdcdc);
  }
  /deep/ .el-pagination__sizes input {
    height: 32px !important;
    line-height: 32px;
  }
  /deep/.el-input__icon {
    line-height: 20px !important;
  }
  /deep/.el-pagination__sizes {
    height: 32px !important;
    /deep/.el-input {
      width: 96px !important;
      height: 32px !important;
    }

    /deep/.btn-prev {
      border: none;
    }
  }
  /deep/.el-pager {
    .number,
    .more {
      width: 32px;
      height: 32px !important;
      border: 1px solid var(--Gray-Gray4-, #dcdcdc);
      background: var(--Gray-White, #fff);
      text-align: center;
      line-height: 32px;
    }
  }
  /deep/.el-pagination.is-background .btn-next,
  /deep/.el-pagination.is-background .btn-prev,
  /deep/ .el-pagination.is-background .el-pager li {
    margin: 0 8px;
  }
  /deep/.el-pagination__jump {
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
    background: #f3f3f3;
    border-radius: 3px;
  }
}
/deep/.el-pagination .el-pagination__total {
  height: 32px !important;
  line-height: 32px !important;
  margin-right: 136px !important;
  color: #00000099 !important;
  position: absolute;
  left: 0;
}
/deep/.el-pagination .el-pager .number.active {
  background: #0052d9 !important;
  color: #fff !important;
  border-color: #0052d9 !important;
}
/deep/.el-pagination.is-background .btn-next,
/deep/.el-pagination.is-background .btn-prev,
/deep/.el-pagination.is-background .el-icon-more,
/deep/.el-pagination.is-background .el-icon-d-arrow-right {
  background-color: #fff !important;
  border: none;
}
/deep/.el-pagination .el-pagination__jump input {
  height: 28px !important;
}
</style>
