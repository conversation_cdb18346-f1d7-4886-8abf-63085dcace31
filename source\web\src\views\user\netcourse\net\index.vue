<template>
  <div class="net-page">
    <div class="contain-main">
      <div class="left">
        <div class="content-top">
          <div class="name">
            <span class="tag word">{{filterResourceName}}</span>
            <span>{{ courseData.course_name }}</span>
          </div>
          <div class="info info1" style="margin-bottom: 12px;">
            <div class="info-left">
              <span class="time">{{ courseData.created_at ||  $langue('NetCourse_CreateTime', { defaultText: '创建时间' })+ ': --' }}</span>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content">
                  <span class="create">{{ teacher_name }}</span>
                </div>
                <p class="create">
                  <span>{{ teacher_name }}</span>
                </p>
              </el-tooltip>
            </div>
            <div class="info-right">
              <span><i class="icon-view"></i>({{ courseData.view_count || 0 }})</span>
              <span 
                @click="handleLikeOrFav(1)"
                :dt-eid="dtdianzan('eid')"
                :dt-remark="dtdianzan('remark')"
                :class="[zanAndcollect.isZan ? 'icon-zan-active' : '']">
                  <i class="icon-zan"></i>({{ courseData.praise_count || 0 }})
              </span>
              <span><i class="icon-comment"></i>({{ courseData.recomm_count || 0 }})</span>
              <span 
                @click="handleLikeOrFav(2)" 
                :class="[zanAndcollect.isCollect ? 'icon-collect-active' : '']"
                :dt-eid='dtCollect("eid")'
                :dt-remark="dtCollect('remark')"
                >
                <i class="icon-collect"></i> ({{ courseData.fav_count || 0 }})
              </span>
              <div class="jf-tip" v-if="isShowJfTip"><i class="jf-icon"></i>{{ $langue('Mooc_Common_Alert_CommonPoint', { point: 1, defaultText: '通用积分+1' }) }}</div>
            </div>
          </div>
          <div class="info info2" style="align-items: center;">
            <div class="info-left" style="margin-right: 40px;">
              <div class="info-label">
                <span class="label label-height25">{{$langue('Article_Lable', { defaultText: '标签' })}}：</span>
                <div class="tag-list-box">
                  <sdc-label-show ref="labelShow" :labelNodeEnv="labelNodeEnv" :actType="2" :courseId="course_id" :showBurialPoint="true" :courseInfo="courseInfo"></sdc-label-show>
                  
                  <!-- <div class="tag-list" v-for="(item, index) in courseData.labels" :key="index" :dt-areaid="dtTags(item, 'area')" :dt-eid="dtTags(item, 'eid')" :dt-remark="dtTags(item, 'remark')">
                    <el-tooltip effect="dark" :content="item.category_full_name" placement="top-start">
                      <span class="tag-value" @click="searchGo(item)">{{ item.label_name }}</span>
                    </el-tooltip>
                  </div> -->
                </div>
              </div>
            </div>
            <div class="info-right right-icons icon-center">
              <span @click="addCourseDialogShow = true" :dt-eid="dtAddCourse('eid')" :dt-remark="dtAddCourse('remark')"><i class="icon-add"></i> {{ $langue('Article_AddCourseList', { defaultText: '添加到课单' }) }}</span>
              <span v-if="!isFormMooc" @click="handleShow()" :dt-eid="dtShareCourse('eid')" :dt-remark="dtShareCourse('remark')"><i class="icon-share"></i>{{ $langue('Article_Share', { defaultText: '分享' }) }}</span>
              <!-- <span><i class="icon-mobile"></i>移动端查看</span> -->
            </div>
          </div>
          <div v-if="isVideoType || courseData.course_type === 'Audio'" class="video-main">
            <Video
              class="video-box" 
              :content_id.sync="courseData.content_id" 
              @getCurCaption="getCurCaption" 
              @handleRecord="handleRecord" 
              @getCurrentTime="getCurrentTime"
              :source_src="courseData.file_url"
              :playTime="playTime"
              ref="vidioDOM" 
              :fullscreenToggle="!enableInteractive"
              :progressControl="progressControl"
              :playbackRateMenuButton="!enableInteractive"
              @loadVideoSucess="loadVideoSucess"
            ></Video>
            <div class="current-time-tips" v-if="isCurrentTimeShow && playTime > 0">
              <i class="el-icon-close" @click="isCurrentTimeShow = false"></i>
              <span>{{ $langue('NetCourse_PlayAt', {seconds: playTime, defaultText: `上次播放至${playTime || 0}秒` }) }}</span>
              <span class="tips-btn" @click="toCurrentTime">{{ $langue('NetCourse_ClickGo', { defaultText: '点击跳转' }) }}</span>
            </div>
            <!-- 视频播放互动弹窗 -->
            <InteractiveDialog v-if="enableInteractive" ref="interactiveDialog" class="interactive-dialog" :course_id="course_id" :record_id="learnRecordId" @changePlayStatus="changePlayStatus"/>
          </div>
          <!-- 章节预览 -->
          <div v-if="isVideoType || courseData.course_type === 'Audio'" :class="[{'chapter-preview-box': chapterSummaryList.length > 0}, {'chapter-box-0': chapterSummaryList.length === 0}]">
            <div class="ai-tips-box" v-if="showChapterTips">
              <i class="el-icon-warning" style="color: #0052D9"></i>
              <span class="tips">当前显示的章节数据为智能生成，如有错漏，<a href="https://km.tencent.com/openkm/url/lpciih" target="_blank">可点此反馈</a></span>
            </div>
            <swiper :options="swiperOption" v-if="chapterSummaryList.length > 0" ref="mySwiper" class="swiper-preview-box">
              <swiper-slide v-for="(item, index) in chapterSummaryList" :key="index" :class="['chapter-card-item', {'playing-card-item': isPlaying(item.chapter_time_point, index)}]">
                <el-image 
                  fit="fill" 
                  :src="item.imgUrl"
                  class="preview-img"
                  :data-time="item.chapter_time_point"
                  >
                  <div class="image-slot" slot="placeholder">
                    <i class="el-icon-loading"></i>
                  </div>
                  <div class="error-cover" slot="error">
                    <img :src="require('@/assets/mooc-img/default_bg_img.png')" alt="" />
                  </div>
                </el-image>
                <span class="playing-btn" v-if="isPlaying(item.chapter_time_point, index)">
                  <span class="playing-icon"></span>
                  播放中</span>
                <div class="time-progress-box" :data-time="item.chapter_time_point">
                  <span class="chapter-time" :data-time="item.chapter_time_point">{{ item.chapter_time }}</span>
                  <span class="time-line-icon" :data-time="item.chapter_time_point"></span>
                </div>
                <div class="chapter-title" :data-time="item.chapter_time_point">{{ item.chapter_title }}</div>
              </swiper-slide>
              <!-- <div class="swiper-pagination" slot="pagination"></div> @click.stop="slideTo('pre')" -->
              <div class="swiper-button-prev" slot="button-prev">
                <span class="icon-left"></span>
              </div>
              <div class="swiper-button-next" slot="button-next">
                <span class="icon-right"></span>
              </div>
            </swiper>
          </div>
          <Scorm 
          v-else-if="['Flash', 'Scorm', 'Doc'].includes(courseData.course_type)"
          class="scorm-box" 
          :courseData="courseData"
          :scormType = "courseData.course_type"
          @handleScormRecord="handleScormRecord" 
          />
          <div v-else class="video-box">
            <el-image lazy fit="fill" v-if="!isFormMooc"
              :src="courseData.photo_url ? courseData.photo_url : require('@/assets/img/default_bg_img.png')"
              class="item-image">
              <div slot="error" class="image-slot">
                <i class="default-icon-picture"></i>
              </div>
            </el-image>
          </div>
        </div>
        <div class="content-bottom">
             <el-tabs v-model="tabActiveName">
                <el-tab-pane
                  :label="$langue(tabItem.label, { defaultText: tabItem.text })" 
                  :name="tabItem.name" 
                    v-for="(tabItem) in tabList"
                  :key="tabKey(tabItem.name)"
                >
                  <span slot="label" 
                    :dt-eid="dtContent(tabItem, 'eid')"
                    :dt-remark="dtContent(tabItem, 'remark')" 
                    >
                    {{ $langue(tabItem.label, { defaultText: tabItem.text }) }}
                  </span>
                </el-tab-pane>
            </el-tabs>
          <div v-show="tabActiveName === 'desc'" class="desc-box">
            <sdc-mce-preview :urlConfig="editorConfig.urlConfig" :content="courseData.course_desc ?? ''"></sdc-mce-preview>
          </div>
          <div v-if="tabActiveName === 'caption'" :class="[{'en-caption-box': moocLang === 'en-us'},'caption-box']">
            <div class="caption-title"><span>{{ $langue('NetCourse_Timestamp', { defaultText: '时间点' }) }}</span><span>{{ $langue('NetCourse_TextScript', { defaultText: '文本内容' }) }}</span></div>
            <div class="caption-content">
              <div v-for="(item, index) in captionData" :key="index"
                :class="[captionCurTime >= item.IntStartTime && captionCurTime < item.IntEndTime ? 'caption-item-active' : '', 'caption-item']"
                :id="item.IntStartTime">
                <span>{{ item.startTime?.split(',')[0] }}</span>
                <p>{{ item.caption }}</p>
              </div>
            </div>
            <div class="caption-content-shadow"></div>
          </div>
          <div v-show="tabActiveName === 'comment'" class="comment-box">
            <div v-if="loadComment" id="commentBox">
              <sdc-comment :params="commentParams" @setCommentCount="setCommentCount" />
            </div>
          </div>
        </div>
      </div>
      <div class="right" v-if="!isFormMooc">
        <NoteList :title="$langue('NetCourse_Experience', { defaultText: '笔记心得' })" :paramsData.sync="noteParams" scene="note" class="mbt-20"></NoteList>
        <ChapterSummary :title="$langue('Chapter_Summary', { defaultText: '章节纪要' })" v-if="isShowChapterSummary" :list="chapterSummaryList" :paramsData.sync="noteParams" @toChaptersPosition="toChaptersPosition" scene="summary" class="mbt-20" />
        <ContentList :title="$langue('NetCourse_Extended', { defaultText: '延伸学习' })" v-if="extandList.length > 0" :list="extandList" :paramsData.sync="noteParams" scene="extand" class="mbt-20" />
        <ContentList :title="$langue('NetCourse_Recommended', { defaultText: '内容推荐' })" v-if="courseData.is_show_recommend && recommendList.length > 0" :list="recommendList" :paramsData.sync="noteParams"
          scene="recommend" class="mbt-20" />
      </div>
      <!-- 切换双语图标 -->
      <!-- 双语暂时注释 -->
      <div class="change-lang-main" v-if="!isFormMooc" @click="handleChangelang">
        <img :src="langIcon" alt="">
        <div class="change-lang-title">{{moocLang === 'en-us' ? '简体中文' : 'English'}}</div>
      </div>
    </div>
    <AddCourseDialog :visible.sync="addCourseDialogShow" :itemData.sync="addCourseDialogData" />
    <ShareDialog ref="shareDialog" :isShow.sync="sharedialogShow" />
  </div>
</template>

<script>
import axios from 'axios'
import { AddCourseDialog, Video, Scorm } from '@/components/index'
import NoteList from '@/views/components/note-list'
import ContentList from '@/views/components/list'
import ChapterSummary from '@/views/components/chapter-summary.vue'
import ShareDialog from '@/views/components/shareDialog'
import InteractiveDialog from '@/views/components/interactiveDialog'
import {
  getNetCourseInfo, 
  netViewRecord, 
  getContentsList, 
  getExtanContentList, 
  getRecommendList, 
  isSysBusy, 
  netCheckPraised, 
  netAddPraise, 
  netDeletePraise, 
  netCheckFavorited, 
  netAddFavorite, 
  netDeleteFavorite, 
  getNetCourseChapterList,
  targetAB
} from 'config/api.conf'
import { getlabelSpecialUsers } from 'config/mooc.api.conf.js'
import { mapState } from 'vuex'
import MoocJs from 'sdc-moocjs-integrator'
import translate from 'mixins/translate.vue'
import { getQueryVariable, pageExposure } from 'utils/tools'
// import { swiper, swiperSlide } from 'vue-awesome-swiper'
// import 'swiper/css/swiper.css'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]
let vm
export default {
  mixins: [translate],
  components: {
    Video,
    Scorm,
    AddCourseDialog,
    NoteList,
    ContentList,
    ChapterSummary,
    ShareDialog,
    InteractiveDialog
    // swiper,
    // swiperSlide
  },
  data() {
    return {
      SpecialUsers: false, // 是否是特殊人员
      editorConfig: {
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/{contentId}`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview`
        }
      },
      widthSize: 0,
      commentParams: {},
      labelNodeEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test',
      loadComment: false,
      courseData: {
        content_type: '',
        comment_count: 0,
        fav_count: 0
      },
      courseList: [],
      extandList: [],
      chapterSummaryList: [],
      // swiper: null,
      recommendList: [],
      addCourseDialogData: {
        module_id: 1,
        module_name: '网络课'
      },
      addCourseDialogShow: false,
      sharedialogShow: false,
      countTimer: null,
      zanAndcollect: {
        isZan: false,
        isCollect: false
      },
      isShowJfTip: false,
      tabActiveName: 'desc',
      captionData: [],
      captionCurTime: null,
      studyRecordQuery: { // video学习记录
        act_id: this.handleQueryData('course_id'),
        area_id: this.handleQueryData('area_id'),
        from: this.handleQueryData('jump_from') || this.handleQueryData('from') || '',
        learn_record_id: 0,
        is_finish: 0,
        is_review: 0,
        total_study_time: 0,
        my_study_progress: 0,
        course_duration: 0
        // 下面是 scorm 课件时才需要
        // data_model: '',
        // scorm_item_id: 1,
      },
      learnRecordId: 0,
      scromRecordQuery: null,
      noteParams: {
        act_id: this.handleQueryData('course_id'),
        act_type: 2, // 16
        course_name: '',
        module_id: 1
      },
      playTime: 0,
      isCurrentTimeShow: false,
      // curTimeShouTimer: null,
      progressControl: true,
      enableInteractive: false,
      viewRecordTime: null,
      chapterData: {
        chapter_content_list: [],
        chapter_ai_content_list: []
      },
      tabList: [
        { label: 'Mooc_TaskDetail_Audio_Description', name: 'desc', text: '简介' }
      ]
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          const hostUrl = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_PORTAL_HOST_WOA : process.env.VUE_APP_PORTAL_HOST
          this.commentParams = {
            userName: val.staff_name,
            actId: this.course_id,
            appId: 'A9BiosXihR0h46ThNsAX',
            scrollTarget: '.graphic-user-page',
            urlConfig: {
              getComments: `${hostUrl}/training/api/netcourse/user/course-comment/get_comments`,
              addComment: `${hostUrl}/training/api/netcourse/user/course-comment/add`,
              deleteComment: `${hostUrl}/training/api/netcourse/user/course-comment/delete/`,
              like: `${hostUrl}/training/api/netcourse/user/course-comment/praised`,
              sticky: `${hostUrl}/training/api/netcourse/user/course-comment/sticky`,
              show: `${hostUrl}/training/api/netcourse/user/course-comment/show`
            }
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState(['userInfo', 'moocLang']),
    swiper() {
      return this.$refs.mySwiper.$swiper
    },
    swiperOption() {
      return {
        loop: false,
        // autoplay: {
        //   delay: 3000,
        //   stopOnLastSlide: false,
        //   disableOnInteraction: false
        // },
        autoplay: false,
        direction: 'horizontal',
        slidesPerGroup: this.widthSize <= 1440 ? 3 : 5,
        slidesPerView: 'auto',
        freeMode: false,
        // 显示分页
        // pagination: {
        //   el: '.swiper-pagination',
        //   clickable: true // 允许分页点击跳转
        // },
        navigation: { // 设置点击箭头
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        on: {
          click: function(e) {
            if (e.target.getAttribute('data-time')) {
              let playTime = e.target.getAttribute('data-time') * 1
              vm.toChaptersPosition(playTime)
            }
          }
        }
      }
    },
    course_id() {
      return this.$route.query.course_id ? parseInt(this.handleQueryData('course_id')) : ''
    },
    isFormMooc() {
      return this.$route.query.from === 'mooc'
    },
    isFormSpoc() {
      return this.$route.query.from === 'spoc'
    },
    teacher_name() {
      let { inner_teacher_names, out_teacher_names } = this.courseData
      let name = ''
      if (inner_teacher_names?.length) {
        inner_teacher_names.forEach((e) => {
          if (!e.teacher_name) return
          name += `${e.teacher_name}， `
        })
      }
      if (out_teacher_names?.length) {
        out_teacher_names.forEach((e) => {
          if (!e.teacher_name) return
          name += `${e.teacher_name}， `
        })
      }
      name = name.slice(0, -2)
      return name
    },
    filterResourceName() {
      let { course_type } = this.courseData
      let name = ''
      if (this.isVideoType) {
        name = this.$langue('Mooc_Common_ResourceType_Video', { defaultText: '视频' })
      } else if (course_type === 'Audio') {
        name = this.$langue('Mooc_Common_ResourceType_Audio', { defaultText: '音频' })
      } else if (course_type === 'Article') {
        name = this.$langue('Mooc_Common_ResourceType_Article', { defaultText: '文章' })
      } else if (course_type === 'Doc') {
        name = this.$langue('Mooc_Common_ResourceType_Doc', { defaultText: '文档' })
      } else if (course_type === 'Scorm') {
        name = 'Scorm'
      } else if (course_type === 'Flash') {
        name = this.$langue('Mooc_Common_ResourceType_Zip', { defaultText: '压缩包' })
      }
      return name
    },
    isVideoType() {
      return ['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(this.courseData.course_type)
    },
    // 双语图标
    langIcon() {
      return this.moocLang === 'en-us' ? require('@/assets/img/china.png') : require('@/assets/img/english.png')
    },
    tabKey() {
      return (name) => {
        return name + new Date().getTime()
      }
    },
    isShowChapterSummary() {
      return this.chapterSummaryList.some(e => e.chapter_content)
    },
    showChapterTips() {
      const { chapter_ai_content_list, chapter_content_list } = this.chapterData
      return !chapter_content_list?.length && chapter_ai_content_list?.length
    },
    // 埋点
    dtdianzan() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页', 
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '点赞',
            terminal: 'PC'
          })
        } else {
          let { mooc_course_id, task_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_${task_id}_like`
        }
      }
    },
    dtCollect() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页',
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '收藏',
            terminal: 'PC'
          })
        } else {
          let { mooc_course_id, task_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_${task_id}_collect`
        }
      }
    },
    dtAddCourse() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页', 
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '添加到课单',
            terminal: 'PC'
          })
        } else {
          let { mooc_course_id, task_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_${task_id}_addCourseList`
        }
      }
    },
    dtShareCourse() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页', 
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '分享',
            terminal: 'PC'
          })
        } else {
          let { mooc_course_id, task_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_${task_id}_share`
        }
      }
    },
    dtContent() {
      return (item, type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name, // 任务名称
            page_type: '网课详情页',
            container: '', // 板块的名称
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: item.text,
            terminal: 'PC'
          })
        } else {
          let { mooc_course_id, task_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_${task_id}_${item.name}`
        }
      }
    },
    // dtTags () {
    //   return (item, type) => {
    //     let { mooc_course_id } = this.getRouterQuery()
    //     if (type === 'area') {
    //       return `area_${mooc_course_id}_${item.label_id}`
    //     } else if (type === 'eid') {
    //       return `area_${mooc_course_id}_${item.label_id}`
    //     } else {
    //       return JSON.stringify({
    //         page: this.courseData.course_name, // 任务名称
    //         page_type: '网课详情页',
    //         container: '标签', // 板块的名称
    //         click_type: 'data',
    //         content_type: item.category_name,
    //         content_id: item.label_id,
    //         content_name: item.label_name,
    //         terminal: 'PC'
    //       })
    //     }
    //   }
    // },
    // label-show组件所需要的埋点数据
    courseInfo() {
      let { mooc_course_id } = this.getRouterQuery()
      return {
        mooc_course_id,
        page: this.courseData.course_name, // 任务名称
        page_type: '网课详情页',
        container: '标签', // 板块的名称
        click_type: 'data',
        terminal: 'PC'
      }
    },
    // 是否显示内容文稿
    showManuscript() {
      // 只有当show_manuscript有值时 并且show_manuscript 为 false时才隐藏内容文稿，其他情况为显示
      if (this.courseData.course_statement && this.courseData.course_statement.show_manuscript === false) {
        return false
      } else {
        return true
      }
    }
  },
  beforeRouteEnter(to, from, next) {
    targetAB().then((res) => {
      if (res) {
        next((vm) => {
          // 通过 `vm` 访问组件实例
          vm.$router.replace({
            path: '/netcourse/grayPlay',
            query: {
              ...to.query
            }
          })
        })
      } else {
        next()
      }
    })
  },
  created() {
    vm = this
    this.Infolabel()
  },
  mounted() {
    this.getlabelSpecialInfo()
    this.getChapterList()
    window.addEventListener('beforeunload', (e) => {
      // 离开当前页面学习记录归档
      if (((this.isVideoType || this.courseData.course_type === 'Audio') && this.studyRecordQuery.total_study_time) || this.scromRecordQuery) { 
        let param = {}
        if (this.scromRecordQuery) {
          param = this.scromRecordQuery
        } else {
          param = this.studyRecordQuery
        }
        param.is_archive = true
        let blob = new Blob([JSON.stringify(param)], { type: 'application/json; charset=UTF-8' })
        navigator.sendBeacon('/training/api/netcourse/user/courseinfo/add-study-record', blob)
      }
    })
    // 初始化
    this.widthSize = window.screen.width

    // 添加窗口大小改变监听器
    window.addEventListener('resize', () => {
      vm.widthSize = document.body.clientWidth
    })
    let flag = /[a-zA-Z0-9]/g.test(this.handleQueryData('course_id'))
    if (!flag) { // 非数字字母组合的的情况跳转
      sessionStorage.setItem('401Msg', this.$langue('Mooc_Common_Alert_DataNotExisit', { defaultText: '数据不存在' }))
      this.$router.replace({
        name: '401'
      })
      return
    }
    this.getCourseDetail().then((res) => {
      this.loadComment = true
      // 任务已完成时，开启任务同步弹窗
      if (this.courseData.is_finish * 1 === 1 && (this.isFormMooc || this.isFormSpoc)) {
        MoocJs.complete('init')
      }
      if (this.$route.query.jump_from === 'CourseList' && this.$route.query.area_id) {
        this.getCourseList(this.$route.query.area_id)
      }
      this.getSysBusyStatus()
      this.getExtandList()
      this.getZanAndCollectStatus()
    }).catch((err) => {
      if (err.code === 403 || err.code === 500) {
        sessionStorage.setItem('401Msg', err.message)
        this.$router.replace({
          name: '401'
        })
        if (this.isFormMooc || this.isFormSpoc) {
          MoocJs.sendErrorInfo(err.message)
        }
      }
    })
    if (this.isFormMooc || this.isFormSpoc) {
      MoocJs.setPause(() => {
        this.$refs.vidioDOM.vedioPlayer.pause()
      })
      MoocJs.setPlay(() => {
        this.$refs.vidioDOM.vedioPlayer.play()
      })
    }
    MoocJs.messageListener((res) => {
      if (res.events === 'tencent-mooc-lang') {
        this.$store.commit('setMoocLang', res.params)
        this.getLangJS()
      }
    })
  },
  methods: {
    handleQueryData(name) {
      let query = getQueryVariable(name)
      if (query) {
        let index = query.indexOf('http')
        if (index > 0) {
          let val = query.substring(0, index)
          return name === 'course_id' ? parseInt(val) : val
        } else {
          return name === 'course_id' ? parseInt(query) : query
        }
      }
      return query
    },
    // slideTo(type) {
    //   // console.log(this.$refs.mySwiper, 'mySwiper--------')
    //   if (type === 'pre') {
    //     this.$refs.mySwiper.slideTo(-2)
    //   } else {
    //     this.$refs.mySwiper.slideTo(2)
    //   }
    // },
    loadVideoSucess() { 
      // 如果是v8跳转过来就定位v8的位置
      const { targetTime } = this.$route.query
      if (targetTime > -1) {
        const playTime = targetTime * 1
        this.toChaptersPosition(playTime)
      }
    },
    // 视频定位到章节为止
    toChaptersPosition(chapter_time_point) {
      // 互动优先级更高，开启互动后不可点击章节跳转进度
      if (this.enableInteractive) {
        this.$message.warning('已开启互动，不可跳转进度')
        return
      }
      this.captionCurTime = chapter_time_point
      this.$nextTick(() => {
        this.$refs && this.$refs.vidioDOM && this.$refs.vidioDOM.vedioPlayer && this.$refs.vidioDOM.vedioPlayer.currentTime(this.captionCurTime)
      })
    },
    // 判断为播放中的章节
    isPlaying(chapter_time, index) {
      let next_chapter_time = 0
      if (index < this.chapterSummaryList.length - 1) {
        next_chapter_time = this.chapterSummaryList.length > 0 && this.chapterSummaryList[index + 1].chapter_time_point
      } else {
        next_chapter_time = this.studyRecordQuery.course_duration
      }
      return (this.studyRecordQuery.my_study_progress >= chapter_time * 1) && (this.studyRecordQuery.my_study_progress < next_chapter_time * 1)
    },
    // async Infolabel() {
    //   this.labelNodeEnv = {
    //     env: process.env.NODE_ENV === 'production' ? 'production' : 'test'
    //   }
    // },
    // 获取用户是不是特殊人员
    getlabelSpecialInfo() {
      getlabelSpecialUsers().then(res => {
        this.SpecialUsers = res
      })
    },
    getRouterQuery() {
      let { mooc_course_id, task_id } = this.$route.query
      return {
        mooc_course_id: mooc_course_id || '',
        task_id: task_id || ''
      }
    },
    async Infolabel() {
      this.labelNodeEnv = process.env.NODE_ENV === 'production' ? 'production' : 'test'
    },
    // 切换双语
    handleChangelang() {
      const lang = this.moocLang === 'en-us' ? 'zh-cn' : 'en-us'
      this.$store.commit('setMoocLang', lang)
      this.getLangJS()
    },
    input (val) {
      this.courseData.labels = val
      setTimeout(() => {
        this.getCourseDetail()
        this.$refs.labelShow && this.$refs.labelShow.getLabelList()
      }, 500)
    },
    getSelectedLabelList() {
    },
    getCurCaption(data) {
      if (!this.captionData.length > 0) this.captionData = data
      if (this.captionData && this.captionData.length && this.tabList.length < 3 && this.courseData.show_comments !== false && this.showManuscript) {
        this.tabList.push({ label: 'Mooc_TaskDetail_Audio_ContentDoc', name: 'caption', text: '内容文稿' })
      }
    },
    handleRecord(param) {
      if (param.evt === 'loadedmetadata') {
        // 视频加载完成后拿到播放时长
        let duration = 0
        if (param.duration) { duration = Math.floor(param.duration) }
        // 防止更换视频，过滤多余的章节时间节点
        if (duration) {
          this.chapterSummaryList = this.chapterSummaryList.filter(its => {
            return its.chapter_time_point <= duration
          })
        }
      }
      let status = ''
      if (param.evt === 'play') {
        this.studyRecordQuery.course_duration = Math.floor(param.duration)
        // 重新学习，重置数据
        if (this.studyRecordQuery.is_finish === 1) {
          this.studyRecordQuery.is_finish = 0
          this.studyRecordQuery.is_review = 1 // 重播
          this.studyRecordQuery.total_study_time = 0
          this.learnRecordId = 0
        }
        if (!this.countTimer) {
          this.creatViewTimer(param)
        }
        if (this.isFormMooc || this.isFormSpoc) {
          MoocJs.play()
        }
      }

      if (param.evt === 'pause' || param.evt === 'ended') {
        if (param.evt === 'ended') { // 学习完
          this.studyRecordQuery.is_finish = 1
          status = 'ended'
        }
        // 延迟1秒调用方法，（视频播放完成会先触发pause，再触发ended），解决同时触发pause、ended情况
        if (this.viewRecordTime) clearTimeout(this.viewRecordTime)
        this.viewRecordTime = setTimeout(() => {
          this.viewRecord('', '', status)
        }, 1000)
        clearInterval(this.countTimer)
        this.countTimer = null

        if (param.evt === 'pause' && (this.isFormMooc || this.isFormSpoc)) {
          MoocJs.pause()
        } else if (param.evt === 'ended' && (this.isFormMooc || this.isFormSpoc)) {
          MoocJs.complete()
        }
      }
    },
    handleScormRecord(scormdata) {
      if (scormdata.is_finish && (this.isFormMooc || this.isFormSpoc)) {
        MoocJs.complete()
      }
      this.viewRecord(scormdata, 'scorm')
    },
    getCurrentTime(curTime) {
      const captionBox = this.$el.querySelector('.caption-content')
      this.captionCurTime = Number(curTime.toFixed(2))
      let curDom = document.getElementsByClassName('caption-item-active')[0]?.previousElementSibling
      if (curDom) {
        captionBox.scrollTop = curDom.offsetTop - 18
      }
      this.studyRecordQuery.my_study_progress = Math.floor(this.captionCurTime) // 视频当前播放时长

      this.$refs.interactiveDialog && this.$refs.interactiveDialog.getVideoTime(curTime)
    },
    changePlayStatus(status) {
      if (status === 'play') {
        this.$refs.vidioDOM.vedioPlayer.play()
      } else if (status === 'pause') {
        this.$refs.vidioDOM.vedioPlayer.pause()
      }
      if (this.isFormMooc) {
        MoocJs.postMessage('interactiveDialog', status === 'pause')
      }
    },
    creatViewTimer() {
      let _this = this
      let durtation = 0
      let { course_duration } = this.studyRecordQuery
      this.countTimer = setInterval(function () {
        let { my_study_progress, is_finish } = _this.studyRecordQuery
        durtation++
        _this.studyRecordQuery.total_study_time++
        if (durtation % 15 === 0) {
          _this.viewRecord() // 浏览器时长需每15秒记录一次
        }

        // 比视频时长提前10秒触发完成上报
        if (course_duration >= 60 && my_study_progress + 10 >= course_duration && !is_finish) {
          _this.studyRecordQuery.is_finish = 1
          _this.viewRecord()
          clearInterval(_this.countTimer)
          _this.countTimer = null
          if (_this.isFormMooc || _this.isFormSpoc) {
            MoocJs.complete()
          }
        }
      }, 1000)
    },
    viewRecord(videoParam, recordType, status) {
      const { previewType } = this.$route.query
      // 预览不做上报
      if (previewType === 'preview') {
        return
      }
      let recordParam = {}
      if (recordType && recordType === 'scorm') {
        recordParam = videoParam
        recordParam.act_id = this.course_id
        recordParam.learn_record_id = this.learnRecordId
        this.scromRecordQuery = recordParam
      } else {
        if (!this.studyRecordQuery.total_study_time) return
        this.studyRecordQuery.learn_record_id = this.learnRecordId
        recordParam = this.studyRecordQuery
      }
      netViewRecord(recordParam).then((data) => {
        if (data) {
          if (status) {
            this.learnRecordId = 0
          } else {
            this.learnRecordId = data
          }
        }
      })
    },
    getCourseDetail() {
      const { share_staff_id, share_staff_name } = this.$route.query
      const reFun = getNetCourseInfo({ act_id: this.course_id, share_staff_id: share_staff_id || '', share_staff_name: share_staff_name || '' }).then((data) => {
        if (data.status === 3) {
          sessionStorage.setItem('401Msg', `${this.$langue('NetCourse_CourseOffLine', { defaultText: '该课程已下架' })}！`)
          this.$router.replace({
            name: '401'
          })
          return
        }
        // 评论区是否显示
        if (data.show_comments !== false && !this.tabList.find(item => item.label === 'Mooc_ProjectDetail_Notice_Comments')) {
          this.tabList.push(
            { label: 'Mooc_ProjectDetail_Notice_Comments', name: 'comment', text: '评论' }
          )
        }
        document.title = `${data.course_name}_Q-Learning`
        this.noteParams.course_name = data.course_name
        this.courseData = data
        this.courseData.labels = data.course_labels
        const { course_name, photo_url, course_desc, net_course_id, content_id } = data
        const net_url = location.hostname.endsWith('.woa.com') ? `${process.env.VUE_APP_PORTAL_HOST_WOA}/training/netcourse/play?course_id=${net_course_id}` : `${process.env.VUE_APP_PORTAL_HOST}/training/netcourse/play?course_id=${net_course_id}`
        this.addCourseDialogData = {
          ...this.addCourseDialogData,
          content_name: course_name,
          cover_img_url: photo_url,
          description: course_desc,
          href: net_url,
          item_id: net_course_id,
          origin: location.origin
        }
        this.courseData.content_id = content_id

        // 续播
        this.playTime = data.my_study_progress
        if (this.playTime) {
          this.isCurrentTimeShow = true
          // let curTimeShouTimer = setTimeout(() => {
          //   this.isCurrentTimeShow = false
          // }, 15000)
          // this.$once('hook:beforeDestroy', () => {
          //   clearTimeout(curTimeShouTimer)
          //   curTimeShouTimer = null
          // })
        }
        // 文稿
        if (data.captions?.length > 0) this.readCaptionFile(data.captions)
        // 互动能力
        this.enableInteractive = data.enable_interactive
        // limit_progress_bar 是否限制进度条 true限制  false不限制
        this.progressControl = !data.limit_progress_bar
        // this.enableInteractive = data.enable_interactive
        /*
          course_type:
            Video 纯视频文件
            Flash 压缩包文件
            Scorm 标准课件
            CaseStudy 案例库
            Audio 纯音频文件
            Doc 文档
            Video-2d 视频文件(PPT-2D)
            Video-ppt 视频文件(PPT)
        */
        // if (!data.content_id && data.content_type === 'PGC') {
        //   this.$messageBox(`${this.$langue('NetCourse_PlayerNotSupport', { defaultText: '新版播放器暂不支持该课程，将跳转至旧版播放器观看' })}！`, this.$langue('Mooc_TaskDetail_ThirdParty_Alert', { defaultText: '提示' }), {
        //     distinguishCancelAndClose: true,
        //     confirmButtonText: this.$langue('Mooc_TaskDetail_Confirm', { defaultText: '确认' }),
        //     cancelButtonText: this.$langue('Mooc_Common_Alert_Cancel', { defaultText: '取消' })
        //   }).then(() => {
        //     let url = `${process.env.VUE_APP_V8_HOST_WOA}/user/net?act_id=${data.net_course_id}`
        //     // 判断参数是否需要带mooc跳转
        //     if (this.isFormMooc) {
        //       url = url + '&from=mooc'
        //     }
        //     window.open(url, '_self')
        //   }).catch(action => {
        //   })
        // }
        if (data.course_type === 'CaseStudy') {
          this.$messageBox(`${this.$langue('NetCourse_NotSupportRecourse', { defaultText: '暂不支持该类型资源查看！' })}！`, this.$langue('Mooc_TaskDetail_ThirdParty_Alert', { defaultText: '提示' }), {
            distinguishCancelAndClose: true,
            confirmButtonText: this.$langue('NetCourse_Ok', { defaultText: '好的' }),
            cancelButtonText: this.$langue('Mooc_Common_Alert_Cancel', { defaultText: '取消' })
          })
        }

        // 详情页曝光上报
        pageExposure({
          page_type: '网络课详情页',
          content_type: '网络课',
          act_type: '2',
          content_name: course_name,
          content_id: net_course_id
        })
      })
      return reFun
    },
    secondsToMinutes(seconds) {
      let minutes = Math.floor(seconds / 60) // 计算分钟
      let remainingSeconds = seconds % 60 // 计算剩余秒数
      remainingSeconds = remainingSeconds.toString().padStart(2, '0')
      return {
        minutes: minutes,
        seconds: remainingSeconds
      }
    },
    // 获取网络课章节列表
    getChapterList() {
      getNetCourseChapterList(this.course_id).then((data) => {
        const { chapter_ai_content_list, chapter_content_list } = data
        const chapterList = chapter_content_list?.length ? chapter_content_list : chapter_ai_content_list?.length ? chapter_ai_content_list : []
        this.chapterData = data
        this.chapterSummaryList = (chapterList || []).map(item => {
          let { minutes, seconds } = this.secondsToMinutes(item.chapter_time_point)
          const url = item.chapter_cover_content_id ? `${envName.contentcenter}content-center/api/v1/content/imgage/${item.chapter_cover_content_id}/preview` : item.chapter_cover_url
          return {
            ...item,
            chapter_content: item.chapter_content ? item.chapter_content.replace(/(\\r\\n|\\n|\n|\r\n)+/g, '<br>') : '',
            chapter_time: minutes + ':' + seconds,
            imgUrl: url
          }
        })
      })
    },
    // 跳转时间
    toCurrentTime() {
      this.isCurrentTimeShow = false
      this.$refs.vidioDOM.vedioPlayer.currentTime(this.playTime)
    },
    getCourseList(cl_id) {
      getContentsList(cl_id).then((data) => {
        this.courseList = data
      })
    },
    getExtandList() {
      const params = {
        act_id: this.course_id,
        act_type: 2
      }
      getExtanContentList(params).then((data) => {
        this.extandList = data
      })
    },
    getSysBusyStatus() {
      isSysBusy().then(res => {
        if (res !== '1') {
          this.getConRecommendList()
        }
      })
    },
    getConRecommendList() {
      // 当天创建的数据凌晨12点之前，不请求数据
      let curDateLastTime = new Date().setHours(0, 0, 0, 0)
      let createTimeS = this.courseData?.created_at ? `${this.courseData.created_at.split(' ')[0]} 23:59:59` : ''
      const createTime = new Date(createTimeS).getTime()
      if (curDateLastTime > createTime) {
        const params = {
          module_id: 1,
          item_id: this.course_id
        }
        getRecommendList(params).then((data) => {
          this.recommendList = data || []
          if (this.recommendList.length > 5) {
            this.recommendList = this.recommendList.splice(0, 5)
          }
        })
      }
    },
    getZanAndCollectStatus() {
      const params = { net_course_id: this.course_id }
      netCheckPraised(params).then(res => {
        this.zanAndcollect.isZan = res
      })
      netCheckFavorited(params).then(res => {
        this.zanAndcollect.isCollect = res
      })
    },
    handleLikeOrFav(scene) {
      const params = { net_course_id: this.course_id }
      if (scene === 1) {
        netCheckPraised(params).then(res => {
          const PAndFCommonAPI = res ? netDeletePraise : netAddPraise
          const tip = res ? this.$langue('Mooc_Common_Alert_CancelPraiseSucessed', { defaultText: '取消点赞成功' }) : this.$langue('Mooc_Common_Alert_PraiseSucessed', { defaultText: '点赞成功' })
          this.courseData.praise_count = res ? (this.courseData.praise_count === null || this.courseData.praise_count === 0 ? 0 : this.courseData.praise_count - 1) : this.courseData.praise_count + 1
          this.handlerCommonInt(PAndFCommonAPI, params, tip)
        })
      } else {
        netCheckFavorited(params).then(res => {
          const PAndFCommonAPI = res ? netDeleteFavorite : netAddFavorite
          const tip = res ? this.$langue('Mooc_Common_Alert_CancelCollectSucessed', { defaultText: '取消收藏成功' }) : this.$langue('Mooc_Common_Alert_CollectSucessed', { defaultText: '收藏成功' })
          this.courseData.fav_count = res ? (this.courseData.fav_count === null || this.courseData.fav_count === 0 ? 0 : this.courseData.fav_count - 1) : this.courseData.fav_count + 1
          this.handlerCommonInt(PAndFCommonAPI, params, tip)
        })
      }
    },
    handlerCommonInt(PAndFCommonAPI, params, tip) {
      PAndFCommonAPI(params).then(data => {
        if (PAndFCommonAPI === netAddPraise || PAndFCommonAPI === netDeletePraise) this.zanAndcollect.isZan = PAndFCommonAPI === netAddPraise ? Boolean(true) : Boolean(false)
        if (PAndFCommonAPI === netAddFavorite || PAndFCommonAPI === netDeleteFavorite) this.zanAndcollect.isCollect = PAndFCommonAPI === netAddFavorite ? Boolean(true) : Boolean(false)
        if (PAndFCommonAPI === netAddPraise || PAndFCommonAPI === netAddFavorite) {
          if (data.credit && data.credit !== '0') {
            this.isShowJfTip = true
            setTimeout(() => {
              this.isShowJfTip = false
            }, 6000)
            this.$message.success(`${tip}，${this.$langue('Mooc_Common_Alert_CommonPoint', { point: +data.credit, defaultText: `通用积分+${+data.credit}` })}`)
          } else this.$message.success(tip)
        } else this.$message.success(tip)
      })
    },
    searchGo(item) {
      let href = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_V8_HOST_WOA : process.env.VUE_APP_V8_HOST
      href = `${href}/mat/user/search?keywords=${item.label_name}&from_page=ql新首页&type=label`
      window.open(href)
    },
    fromUrlGo() {
      const openUrl = this.courseData.from_url
      if (openUrl) window.open(openUrl)
    },
    handleShow() {
      this.sharedialogShow = true
      const url = process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/b5GaSG' : 'http://s.test.yunassess.com/s/hoo9Gg'
      let userInfo = JSON.parse(sessionStorage.getItem('login_user'))
      this.$nextTick(() => {
        const href = `${url}?course_id=${this.course_id}&scheme_type=netcourse&share_staff_id=${userInfo.staff_id}&share_staff_name=${userInfo.staff_name}`
        this.$refs.shareDialog.initCode({ 
          url: href,
          scene: `${this.course_id}_${userInfo.staff_id}`,
          page: 'pages/networkCourse/video/videoDetail',
          customText: `【${this.courseData.course_name}】${href}`
        })
      })
    },
    readCaptionFile(captions) {
      captions.forEach(item => {
        if (item.attachement_type === 'Caption') {
          axios({
            url: item.url,
            method: 'GET',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
            }
          }).then((response) => {
            if (response.status === 200 && response.data) {
              try {
                let data = response.data?.split(/\n\n|\r\n\r\n/)
                const captionArr = data?.map(str => {
                  let obj = {}
                  const captionItemArr = str.split(/[(\r\n)\r\n|(\r\n\r\n)\r\n\r\n]+/)
                  captionItemArr.map((e, idx) => {
                    if (idx === 1) {
                      const time = JSON.parse(JSON.stringify(e))
                      obj.startTime = e.split('-->')[0]
                      obj.endTime = e.split('-->')[1]
                      const endTimeCopy = JSON.parse(JSON.stringify(time.split('-->')[1]))
                      const startTimeCopy = JSON.parse(JSON.stringify(time.split('-->')[0]))
                      obj.IntStartTime = startTimeCopy ? this.timeToSec(startTimeCopy) : 0
                      obj.IntEndTime = endTimeCopy ? this.timeToSec(endTimeCopy) : 0
                    }
                    if (idx === 2) obj.caption = e
                  })
                  return obj
                })
                this.captionData = captionArr
                if (this.captionData && this.captionData.length && this.tabList.length < 3 && this.courseData.show_comments !== false && this.courseData.show_comments !== false && this.showManuscript) {
                  this.tabList.push({ label: 'Mooc_TaskDetail_Audio_ContentDoc', name: 'caption', text: '内容文稿' })
                }
              } catch (error) { }
            }
          })
        }
      })
    },
    timeToSec(timeStr) {
      let time = timeStr.split(',')[0]
      let s = ''
      const hour = time.split(':')[0]
      const min = time.split(':')[1]
      const sec = time.split(':')[2]
      let misecond = timeStr.split(',')[1].substr(0, 2)
      misecond = Number(parseInt(misecond) / 100)
      s = Number(hour * 3600) + Number(min * 60) + Number(sec)
      return (s * 100 + misecond * 100) / 100
    },
    setCommentCount() { }
  },
  beforeDestroy() {
    clearInterval(this.countTimer)
    this.countTimer = null
    MoocJs.removeEvent()
    window.removeEventListener('resize', () => {})
  }
}
</script>
<style lang="less">
</style>
<style lang="less" scoped>
.project-add-label /deep/.cascader-component button>span>span{
  font-size: 12px;
}
.tag-form-item {
    :deep(.el-input) {
      min-height: 32px;
      height: unset;
    }
  }
.net-page {
   :deep(.sdc-editor-preview) {
      width: 100%;
      .content-wrapper {
        width: 100%;
      }
      .file-count {
        padding: 0 0 8px 24px;
      }
      .editor-file-list {
        margin: 0 24px 36px 24px;
      }
      .desc,
      .editor-content {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        word-break: break-word;
        ol,
        ul {
          padding: revert;
        }
      }
      .desc {
        margin: 0 24px 0 24px;
      }
      .editor-content {
        padding: 20px 24px 32px 24px;
      }
    }
  .contain-main {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    padding-top: 10px;

    .left {
      background-color: #fff;
      padding: 24px;
      margin-bottom: 20px;

      .content-top {
        .name {
          display: flex;
          align-items: center;
          color: #000000;
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 22px;
          word-break: break-word;

          span:last-child {
            flex: 1;
          }
        }

        .tag {
          padding: 0 6px;
          border-radius: 2px;
          margin-right: 10px;
        }

        .word {
          color: #0052D9;
          border: 1px solid #0052D9;
        }

        .info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 16px;

          .info-left,
          .info-right {
            display: flex;
          }
          .icon-center {
            align-items: center;
          }
          .info-left {

            .create,
            .time {
              text-overflow: ellipsis;
              overflow: hidden;
              word-break: break-all;
              white-space: nowrap;
              color: rgba(0, 0, 0, 0.6);
            }

            .time {
              width: 140px;
            }

            .create {
              margin-left: 16px;
            }

            .info-classify,
            .info-label {
              display: flex;

              .label {
                color: rgba(0, 0, 0, 0.4) !important;
                width: 50px;
                // width: 80px;
              }
              .label-height25 {
                height: 25px;
                line-height: 25px;
              }

              p {
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                white-space: nowrap;

                span {
                  cursor: pointer;
                  color: #3464E0;

                  i {
                    font-style: normal;
                  }
                }
              }
            }

            .info-label {
              line-height: 20px;

              .tag-list-box {
                flex: 1;
                display: flex;
                flex-wrap: wrap;
              }

              .tag-value {
                background-color: rgba(235, 239, 252, 1);
                height: 20px;
                font-size: 12px;
                color: rgba(0, 82, 217, 1);
                padding: 4px;
                border-radius: 2px;
                display: inline-block;
                margin-right: 12px;
                line-height: 10px;
                cursor: pointer
              }
            }

            .fh {
              color: rgba(0, 0, 0, 0.4);
            }
          }

          .info-right {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.4);
            flex-shrink: 0;

            span {
              display: flex;
              align-items: center;
              justify-content: center;
              width: fit-content;
              margin-right: 18px;
            }

            span:nth-child(2),
            span:nth-child(4),
            .editor {
              cursor: pointer;
            }

            .goback {
              cursor: pointer;
              color: #3464E0;
            }

            i {
              display: inline-block;
              width: 14px;
              height: 14px;
            }

            .icon-view {
              background: url("~@/assets/img/watch.png") no-repeat center /cover;
            }

            .icon-zan {
              background: url("~@/assets/img/zan1.png") no-repeat center /cover;
            }

            .icon-zan-active {
              color: #3464e0;

              i {
                background: url("~@/assets/img/zan1-active.png") no-repeat center /cover;
              }
            }

            .icon-comment {
              background: url("~@/assets/img/comment.png") no-repeat center /cover;
            }

            .icon-collect {
              background: url("~@/assets/img/fav2.png") no-repeat center /cover;
            }

            .icon-collect-active {
              color: #3464e0;

              i {
                background: url("~@/assets/img/fav2-active.png") no-repeat center /cover;
              }
            }

            .icon-edit {
              background: url("~@/assets/img/edit.png") no-repeat center / cover;
            }

            .icon-add {
              background: url("~@/assets/img/add.png") no-repeat center / cover;
              margin-bottom: 1px;
            }

            .icon-addlabel {
              background: url("~@/assets/img/addlabel.png") no-repeat center / cover;
              margin-bottom: 1px;
            }

            .icon-share {
              background: url("~@/assets/img/share.png") no-repeat center /cover;
            }

            .icon-mobile {
              background: url("~@/assets/img/mobile.png") no-repeat center /cover;
            }

            .jf-icon {
              background: url("~@/assets/img/integral-icon.png") no-repeat center / cover;
              display: block;
              width: 20px;
              height: 20px;
              margin-right: 4px;
            }

            .jf-tip {
              color: #ff7548;
              position: absolute;
              right: 20px;
              top: -22px;
              display: flex;
              align-items: center;
            }
          }

          .right-icons {
            color: rgba(0, 0, 0, 0.6);

            span {
              margin-right: 16px;
              cursor: pointer;
            }
          }
        }

        .info1{
          height: 22px;
          line-height: 22px;
          .info-right {
            position: relative;
          }
        } 

        .info2 {
          border-bottom: solid 1px #eeeeee;
          padding-bottom: 8px;
          margin-bottom: 20px;
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
        }

        .video-box {
          border-radius: 4px;
          background-color: #F8F8F8;
          border: solid 1px #ECECEC;

          :deep(.el-image) {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .default-icon-picture {
              display: inline-block;
              width: 400px;
              height: 300px;
              background: url('~@/assets/img/default_bg_img.png') no-repeat;
            }
          }
        }
        .video-main {
          position: relative;
          display: flex;
          .current-time-tips {
            position: absolute;
            left: 10px;
            bottom: 50px;
            background: #0F1010;
            color: #fff;
            height: 26px;
            line-height: 26px;
            font-size: 14px;
            display:flex;
            align-items:center;
            padding: 0 5px;
            border-radius: 2px;
            .el-icon-close {
              margin-right: 5px;
              cursor: pointer;
              font-size: 16px
            }
            .tips-btn {
              cursor: pointer;
              color:#3464e0;
              margin-left: 5px;
            }
          }
          .interactive-dialog{
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            top: 0;
            background: #00000080;
          }
        }
        .chapter-preview-box {
          width: 1100px;
          // height: 190px;
          margin-top: 20px;
          padding: 0 12px;
          background: #F9F9F9;
          border-radius: 6px;
        }
        .chapter-box-0 {
          display: none;
        }
        .ai-tips-box {
          padding-top: 12px;
          color: #0052D9;
          .tips {
            margin-left: 9px;
            a {
              text-decoration: underline;
              color: #0052D9;
              cursor: pointer;
            }
          }
        }
        .swiper-preview-box {
          display: flex;
          width: 1076px;
          height: 200px;
          padding: 20px 0 0px;
          align-items: flex-start;
          gap: 0px;
          border-radius: 6px;
          background: #F9F9F9;
          ::v-deep .chapter-card-item {
            position: relative;
            display: flex;
            flex-direction: column;
            width: 172px !important;
            .preview-img.el-image {
              margin-right: 12px;
              margin-bottom: 8px;
              width: 160px;
              height: 90px;
              border-radius: 4px;
            }
            .playing-btn {
              display: inline-block;
              width: 70px;
              height: 20px;
              line-height: 20px;
              padding-left: 4px;
              position: absolute;
              left: 9px;
              top: 7px;
              border-radius: 4px;
              background: #D91A00;
              color: #ffffff;
              font-family: "PingFang SC";
              font-size: 14px;
              font-weight: 400;
            }
            .playing-icon {
              display: inline-block;
              width: 12px;
              height: 12px;
              vertical-align: middle;
              background: url('~@/assets/mooc-img/icon_live.png') no-repeat center / cover;
            }
            .time-progress-box {
              width: 172px;
              height: 24px;
              display: flex;
              align-items: center;
            }
            .chapter-time {
              display: flex;
              height: 24px;
              line-height: 24px;
              padding: 0 4px;
              justify-content: center;
              align-items: center;
              gap: 10px;
              border-radius: 4px;
              background: var(---Gray1-Hover, #F3F3F3);
              color: #6c7390;
              font-family: "PingFang SC";
              font-size: 14px;
              font-weight: 400;
            }
            .time-line-icon {
              width: 100%;
              height: 1px;
              background-color: #00000042;
            }
            .chapter-title {
              margin-top: 4px;
              width: 160px;
              color: #000000e6;
              font-family: "PingFang SC";
              font-size: 12px;
              font-weight: 600;
              line-height: 20px;
              word-break: break-all;
              display: -webkit-box;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 2;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }
          .chapter-card-item:hover {
            position: relative;
            display: flex;
            width: 176px !important;
            height: 182px;
            padding: 8px;
            flex-direction: column;
            align-items: flex-start;
            border-radius: 8px;
            border: 1px solid var(---Brand2-Focus, #D4E3FC);
            background: var(--Color, #FFF);
            box-shadow: 0 8px 10px -5px #00000014, 0 16px 24px 2px #0000000a, 6px 6px 30px 5px #0000000d;
            cursor: pointer;
            z-index: 1000;
            transform: translate(-8px, -8px);
            .playing-btn {
              position: absolute;
              left: 17px;
              top: 15px;
            }
            .chapter-time {
              color: #000000e6;
            }
            .time-line-icon {
              display: none;
            }
            .chapter-title {
              color: #266fe8;
              font-weight: 600;
            }
          }
          ::v-deep .playing-card-item {
            .preview-img {
              border-radius: 4px;
              border: 1px solid #0052D9;
            }
            .chapter-time {
              border-radius: 4px;
              background: var(---Brand1-Light, #ECF2FE);
              color: #0034b5;
              font-size: 14px;
            }
            .chapter-title {
              color: #0034b5;
              font-weight: 600;
            }
          }
          ::v-deep .swiper-button-prev, .swiper-button-next {
            &:after {
              display: none;
            }
            &.swiper-button-disabled {
              display: none;
            }
            top: 69px;
            width: 24px;
            height: 24px;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 12px;
            background: var(--Color, #FFF);
            box-shadow: 0 8px 10px -5px #00000014, 0 16px 24px 2px #0000000a, 0 6px 30px 5px #0000000d;
            .icon-left, .icon-right {
              display: inline-block;
              width: 20px;
              height: 20px;
              font-size: 20px;
            }
            .icon-left {
              background: url('~@/assets/mooc-img/chevron-left.png') no-repeat center / cover;
            }
            .icon-right {
              background: url('~@/assets/mooc-img/chevron-right.png') no-repeat center / cover;
            }
          }
        }
      }

      .content-bottom {
        margin-top: 36px;
        .en-caption-box {
          .caption-item {
            span:first-child {
              margin-right: 55px !important;
            }
          }
        }
        .caption-box {
          position: relative;
          border: 1px solid rgba(238, 238, 238, 1);
          border-radius: 3px;

          .caption-title {
            height: 36px;
            line-height: 36px;
            padding-left: 19px;
            color: rgba(0, 0, 0, 0.8);
            background-color: #F5F7F9;

            span:first-child {
              margin-right: 40px;
            }
          }

          .caption-content {
            height: 380px;
            padding: 16px 0 0 20px;
            overflow-y: auto;

            .caption-item {
              display: flex;
              align-items: baseline;
              margin-bottom: 16px;

              span {
                // width: 40px;
                margin-right: 26px;
                display: inline-block;
                color: rgba(0, 0, 0, 0.6);
              }

              p {
                color: rgba(0, 0, 0, 0.4);
                line-height: 22px;
                letter-spacing: 0.28px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                -webkit-box-orient: vertical;
              }
            }

            .caption-item-active {

              span,
              p {
                color: #0052D9;
              }
            }
          }

          .caption-content-shadow {
            width: 100%;
            height: 100px;
            position: absolute;
            bottom: 0;
            opacity: 0.5299999713897705;
            background: linear-gradient(180deg, rgba(217, 217, 217, 0) 0%, rgba(216, 216, 216, 0.4) 100%);
          }
        }
      }

      :deep(.el-tabs) {
        margin-bottom: 20px;

        .el-tabs__header {
          border-bottom: solid 1px #eeeeee;
          margin: 0px;
        }

        .el-tabs__item {
          color: rgba(0, 0, 0, 0.4);
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 10px
        }

        .is-active {
          color: #0052D9 !important;
          font-weight: 700;
        }
      }
    }

    .right {
      width: 272px;
      height: 100%;
      margin-left: 20px;

      .mbt-20 {
        margin-bottom: 20px;
      }
    }
  }
}
.change-lang-main {
  width: 64px;
  height: 64px;
  border-radius: 6px;
  background-color: #fff;
  text-align: center;
  border: 0.5px solid  #DCDCDC;
  box-shadow: 0 8px 10px -5px #00000014, 0 16px 24px 2px #0000000a, 0 6px 30px 5px #0000000d;
  cursor: pointer;
  margin-left: 20px;
  img {
    width: 24px;
    height: 24px;
    margin-top: 12px;
  }
  .change-lang-title {
    color: #00000099;
    line-height: 20px;
    font-size: 12px;
    margin-top: 2px;
  }
}
@media screen and (max-width: 1660px) {
  .contain-main .left {
    width: 866px !important;

    .info {
      .info-left {

        .info-classify p,
        .info-label p {
          max-width: 196px !important;
        }

        .create {
          max-width: 342px;
        }
      }
    }

    .video-box,
    .scorm-box {
      width: 818px;
      height: 460px;
    }
    .chapter-preview-box {
      width: 818px !important;
    }
    .swiper-preview-box {
      width: 794px !important;
    }

    // .tag-list-box {
    //   width: 630px;
    // }
  }

  .create {
    max-width: 420px;
  }
}

@media screen and (min-width: 1661px) {
  .contain-main .left {
    width: 1148px !important;

    .info {
      .info-left {

        .info-classify p,
        .info-label p {
          max-width: 360px !important;
        }

        .create {
          max-width: 624px;
        }
      }
    }

    .video-box,
    .scorm-box {
      width: 1100px;
      height: 619px;
    }
    .chapter-preview-box {
      width: 1100px;
      // height: 190px;
    }

    // .tag-list-box {
    //   width: 850px;
    // }
  }

  .create {
    max-width: 690px;
  }
}
</style>
