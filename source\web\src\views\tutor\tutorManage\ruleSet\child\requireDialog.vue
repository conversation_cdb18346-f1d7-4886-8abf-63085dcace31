<template>
  <el-Dialog 
  :visible="visible" 
  width="600px" 
  :title="dialogTitle" 
  :close-on-click-modal="false" 
  @close="cancel"
  custom-class="custom-require-dialog"
  >
    <template v-if="requreList.length > 0">
      <CustomTips 
      title="修改后实时生效，请谨慎操作；仅支持配置QL平台的网络课、培养项目及考试" 
      IconName="el-icon-warning-outline" 
      backgroundColor="#fdf6ec"
      color="#FF7548" 
      class="rule-tips"
      >
      </CustomTips>
      <div v-for="(e, index) in requreList" :key="index">
        <el-form inline>
          <el-row>
            <el-form-item>
              <el-select v-model="e.act_type" placeholder="请选择内容类型" @change="handleChange(e)">
                <el-option 
                :label="item.label" 
                :value="item.act_type" 
                v-for="(item, i) in typeOptions"
                :key="i"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-input v-model="e.course_id" placeholder="请输入内容ID" @blur="handleSearch(e)"></el-input>
            </el-form-item>
            <el-form-item class="ri-btn">
              <i class="el-icon-delete" @click="handleDelete(index)"></i>
              <el-button type="text" @click="handleSearch(e)">重新查询</el-button>
            </el-form-item>
          </el-row>
          <el-row class="tips-row" v-if="e.isCourseShow">
            <div class='blue-tips' v-if="e.enabled" @click="toDetail(e)">{{ e.course_name }}</div>
            <div class="red-tips" v-else>此内容已被下架或删除，不支持配置，请移除</div>
          </el-row>
        </el-form>
      </div>
      <div class="operate-btn-box">
        <div class="operate-add" @click="handleAdd">
          <i class="el-icon-plus"></i>新增
        </div>
        <el-button type="text" size="small" @click="toWiki">如何获取内容ID</el-button>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取消</el-button>
        <el-button @click="confirm" type="primary" size="small" :disabled="disableBtn">保存</el-button>
      </span>
    </template>
    <template v-else>
      <div class="empty-require-box">
        <img src="@/assets/img/empty_2x.png">
        <div class="empty-tips">暂未配置认证要求</div>
        <el-button class="add-btn" type="primary" icon="el-icon-plus" size="small" @click="handleAdd">新增</el-button>
        <el-button type="text" size="small" @click="toWiki">如何获取内容ID</el-button>
      </div>
    </template>
  </el-Dialog>
</template>
<script>
import CustomTips from '@/components/tips.vue'
import { saveRequire, requireSingleSearch } from '../../../api/tutor.api.conf'
export default {
  components: {
    CustomTips
  },
  props: {
    visible: Boolean,
    requireData: Object
  },
  data() {
    return {
      typeOptions: [
        { label: '网络课', act_type: 2, module_id: 1 },
        { label: '考试', act_type: 20, module_id: 11 },
        { label: '培养项目', act_type: 11, module_id: 10 }
      ],
      requreList: [],
      requirePrams: {}
    }
  },
  watch: {
    requireData: {
      deep: true,
      immediate: true,
      handler(val) {
        this.requreList = JSON.parse(JSON.stringify(val.rule_course_info))
        this.requreList = this.requreList.map((e) => {
          return {
            ...e,
            isCourseShow: !!e.course_id
          }
        })
        this.requirePrams = {
          bg_id: val.bg_id,
          bg_name: val.bg_name
        }
      }
    }
  },
  computed: {
    dialogTitle() {
      return `修改认证要求-${this.requirePrams.bg_name}`
    },
    disableBtn() {
      return this.requreList.some((e) => !e.act_type || !e.course_id || !e.enabled)
    }
  },
  mounted() {
  },
  methods: {
    // 查询
    handleSearch(e) {
      if (!e.course_id) {
        this.$set(e, 'isCourseShow', false)
      }
      if (e.act_type && e.course_id) {
        requireSingleSearch({
          act_type: e.act_type,
          course_id: e.course_id
        }).then((res) => {
          this.requreList = this.requreList.map((v) => {
            if (v.course_id === res.course_id) {
              return {
                ...v,
                ...res,
                isCourseShow: !!res.course_id // 用来判断查询后的显示, 避免没有查询就显示
              }
            }
            return {
              ...v
            }
          })
        })
      }
    },
    handleChange(row) {
      this.$set(row, 'course_id', '')
      this.handleSearch(row)
    },
    toDetail(e) {
      if (!e.course_url) return
      window.open(e.course_url, '_blank')
    },
    toWiki() {
      const url = '//iwiki.woa.com/p/4009876544'
      window.open(url, '_blank')
    },
    // 删除
    handleDelete(index) {
      this.requreList.splice(index, 1)
      if (index === 0) { // 最后一条数据直接保存
        this.confirm('delete')
      }
    },
    // 新增
    handleAdd() {
      this.requreList.push({
        act_type: '',
        course_id: ''
      })
    },
    cancel() {
      this.$emit('update:visible', false)
    },
    confirm(val) {
      const params = {
        bg_id: this.requirePrams.bg_id,
        rule_course_info: this.requreList
      }
      saveRequire(params).then(() => {
        if (val !== 'delete') {
          this.cancel()
        }
        this.$message.success('保存成功')
        this.$emit('updateCertificate')
      })
    }
  }
}
</script>
<style lang="less" scoped>
:deep(.custom-require-dialog) {
  .el-dialog__header {
    padding: 24px 32px 0px;
    font-size: 16px;
    font-weight: bolder;
    border-bottom: unset
  }

  .el-dialog__body {
    padding: 24px 32px 10px;
    .el-form {
      border-bottom: 1px solid #E7E7E7;
      margin-bottom: 16px;
      // padding-bottom: 12px;
      .el-form-item {
        margin-right: 16px;
        margin-bottom: 12px;
      }
      .ri-btn {
        margin-right: unset;
        i {
          color: #D54941;
          margin-left: 8px;
          margin-right: 24px;
          cursor: pointer;
        }
        .el-button {
          font-weight: bold;
        }
      }
      .blue-tips {
        color: #0052D9;
        line-height: 22px;
        text-decoration: underline;
        cursor: pointer;
        display: inline-block;
      }
      .red-tips {
        color: #D54941;
        line-height: 22px
      }
    }
    .tips-row {
      margin-bottom: 12px;
    }
    .operate-btn-box {
      display: flex;
      align-items: center;
      margin-top: 16px;
      .operate-add {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 32px;
        color: #0052d9;
        width: 376px;
        border-radius: 3px;
        border: 1px solid #0052D9;
        margin-right: 16px;
        cursor: pointer;
        i {
          margin-right: 4px;
        }
      }
      .el-button {
        font-weight: bold;
      }
    }
  }

  .rule-tips {
    display: flex;
    align-items: center;
    height: 40px;
    border-radius: 3px;
    margin-bottom: 16px;
  }

  :deep(.el-dialog__footer) {
    padding: 0 32px 24px;
  }
  .empty-require-box {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding-bottom: 42px;
    .empty-tips {
      color: #00000099;
      line-height: 24px;
      font-size: 16px;
    }
    img {
      width: 160px;
      height: 160px;
      margin-bottom: 9px;
    }
    .add-btn {
      margin-top: 35px;
      width: 200px;
      margin-bottom: 16px;
    }
  }
}
</style>
