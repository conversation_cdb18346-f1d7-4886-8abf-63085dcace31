<template>
  <div class="portrait-upload">
    <div class="upload-img-box" v-if="uploadImgShow" @click="handleAuthorize">
      <span class="upload-icon"></span>
      <div class="upload-text">上传视频</div>
    </div>
    <div v-else>
      <el-upload
        v-show="portrait === null"
        class="upload-box"
        action=""
        ref="upload"
        accept=".mp4"
        :drag="true"
        :with-credentials="true"
        :show-file-list="false"
        :auto-upload="false"
        :before-upload="beforeAvatarUpload"
        :on-change="onChange"
      >
        <span class="upload-icon"></span>
        <div class="upload-text">上传视频</div>
      </el-upload>
    </div>
    <div class="has-upload" v-show="portrait !== null">
      <video
        v-show="picture_type === 'video'"
        id="portrait-video"
        :style="{ width: videoPlay ? 'auto' : '110px' }"
        :src="videoSrc"
        autoplay
        muted
      ></video>
      <img
        v-show="picture_type === 'img'"
        style="object-fit: cover"
        :src="imgSrc"
        id="portrait-img"
      />
      <div class="btn" v-show="!isUploading">
        <i class="el-icon-upload2" @click="upAgain"></i>
        <i class="el-icon-delete" @click="clearPortrait"></i>
      </div>
    </div>
    <div class="portrait-desc">
      <div class="guide-title">
        <span>人像上传指引</span>
        <el-button type="text" @click="startCheck" v-show="isShowCheck"
          >开始检测</el-button
        >
      </div>
      <div class="guide-limit">
        <p>- 请上传宽<span class="warn-text">高比2:3，15秒以内</span>，不大于<span class="warn-text">100M</span>的mp4格式的视频</p>
        <p>- 人脸在视频中占比面积尽量大于1/2</p>
        <p>- 拍摄时可配合手部动作，避免张嘴和较大幅度晃动头部</p>
        <p>- 建议先预览虚拟讲师效果后，满意后再全部生成</p>
        <p>- 请使用本人自拍视频，如未获得本人授权，严禁上传使用他人形象</p>
      </div>
      <div class="tracking-text">
        <span class="status" v-show="uploading">{{
          percent === 100 ? '上传完成' : '上传中'
        }}</span>
        <span class="percent" v-show="uploading">{{ percent }}%</span>
        <span
          :class="[
            'status',
            {
              'status-fail':
                trackerTip === '校验失败，请重新上传，保证人脸清晰可见'
            }
          ]"
          >{{ trackerTip }}</span
        >
        <i
          class="el-icon-check icon"
          v-show="trackerTip === '人脸校验通过' && percent === 100"
        ></i>
      </div>
      <el-progress
        v-show="uploading"
        :percentage="percent"
        :stroke-width="4"
        color="#0052d9"
        :show-text="false"
      ></el-progress>
    </div>
    <img-cropper
      :picWh="[240, 360]"
      ref="cropperRef"
      @getFile="getFile"
      @upAgain="upAgain"
      :imgInfo="imgInfo"
    />
    <video
      v-show="picture_type === 'video'"
      id="video-check"
      ref="videoCheck"
      :src="videoSrc"
      autoplay
      muted
    ></video>
    <img v-show="picture_type === 'img'" id="img-check" :src="imgSrc" alt="" />
     <!-- 个人信息授权 -->
    <authorize :visible.sync="authorizeShow" @handleAgree="handleAgree"></authorize>
  </div>
</template>

<script>
import '@/assets/js/tracking/tracking-min.js'
import '@/assets/js/tracking/data/face-min.js'
import '@/assets/js/tracking/data/mouth-min.js'
import imgCropper from '@/views/components/img-cropper'
import { getVideoInfo } from 'config/api.conf'
// import env from 'config/env.conf.js'
import { getExplorer } from 'utils/tools'
import authorize from '@/views/user/components/authorize.vue'
import env from 'config/env.conf.js'
// const envName = env[process.env.NODE_ENV]
const explorer = getExplorer()
const agent = /macintosh|mac os x/i.test(navigator.userAgent)
const envName = env[process.env.NODE_ENV]
export default {
  name: 'portraitUpload',
  components: {
    imgCropper,
    authorize
  },
  props: {
    virtualInfo: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      fileList: [],
      pageList: [],
      picture_type: '',
      percent: 0,
      uploading: false,
      isUploading: false,
      portrait: null,
      tracker: null,
      trackerTask: null,
      videoSrc: '',
      imgSrc: '',
      flag: true,
      success: 0,
      fail: 0,
      result: -1, // 人像校验结果，-1未上传，0 失败， 1成功
      trackerTip: '',
      imgInfo: {},
      picture_id: '',
      videoPlay: false,
      isClickCheck: true,
      authorizeShow: false,
      uploadImgShow: true
    }
  },
  watch: {
    virtualInfo(val) {
      this.picture_id = val.picture_id || ''
      this.picture_type = val.picture_type || ''
      if (this.picture_id) {
        this.portrait = {}
      }
      const src =
        this.picture_id &&
        `${envName.contentcenter}content-center/api/v1/content/imgage/${this.picture_id}/preview`
      if (this.picture_type === 'video') {
        this.videoSrc = src
      } else {
        this.imgSrc = src
      }

      this.$emit('onPortraitChange', {
        file: null,
        picture_id: this.picture_id,
        picture_type: val.picture_type || ''
      })
    }
  },
  computed: {
    // 如果是mac的safari和qq浏览器，需要用户手动触发播放一次
    isShowCheck() {
      return (
        this.isClickCheck &&
        this.picture_type === 'video' &&
        agent &&
        (explorer === 'Safari' || explorer === 'qq')
      )
    }
  },
  mounted() {
    // 监听开始播放
    this.$refs.videoCheck.addEventListener('play', this.initTracker)
    // 监听播放结束
    this.$refs.videoCheck.addEventListener('ended', this.endedEvent)
  },
  beforeDestroy() {
    this.tracker = null
    this.trackerTask = null
    this.$refs.videoCheck.removeEventListener('play', this.initTracker)
    this.$refs.videoCheck.removeEventListener('ended', this.endedEvent)
  },
  methods: {
    // 开始检测按钮
    startCheck() {
      document.getElementById('portrait-video').play()
      document.getElementById('video-check').play()
      this.isClickCheck = false
    },
    // 监听播放结束
    endedEvent() {
      if (this.portrait && this.portrait.type) {
        this.trackerTask && this.trackerTask.stop()
        const flag =
          Math.floor((this.success / (this.success + this.fail)) * 100) > 80
        this.result = flag ? 1 : 0
        setTimeout(() => {
          this.trackerTask && this.trackerTask.stop()
          this.trackerTip = flag
            ? '人脸校验通过'
            : '校验失败，请重新上传，保证人脸清晰可见'
          if (this.picture_id) {
            this.$emit('onPortraitChange', {
              file: flag ? this.portrait : null,
              picture_id: flag ? this.picture_id : '',
              picture_type: 'video'
            })
          }
        }, 500)
      }
    },
    // 删除上传的人像
    clearPortrait() {
      this.result = -1
      this.uploading = false
      this.videoPlay = false
      this.trackerTask && this.trackerTask.stop()
      this.portrait = null
      this.picture_id = ''
      this.videoSrc = ''
      this.imgSrc = ''
      this.trackerTip = ''
      this.$emit('onPortraitChange', {})
    },
    // 重新上传
    upAgain() {
      this.$refs.cropperRef.close()
      this.$nextTick(() => {
        this.$refs['upload'].$refs['upload-inner'].handleClick()
      })
    },
    // 授权弹窗
    handleAuthorize() {
      this.authorizeShow = true
    },
    // 同意人像授权
    handleAgree() {
      this.uploadImgShow = false
      this.authorizeShow = false
    },
    // 图片限制大小
    beforeAvatarUpload(file) {
      const that = this
      const isSize = new Promise(function (resolve, reject) {
        let _URL = window.URL || window.webkitURL
        let img = new Image()
        img.onload = function () {
          that.imgInfo = {
            width: img.width,
            height: img.height
          }
        }
        img.src = _URL.createObjectURL(file)
      }).then(() => {
        return file
      })
      return isSize
    },
    // 获取裁剪的图片文件
    getFile(file) {
      const size = file.file.size / 1024 / 1024 > 100
      if (size) {
        this.$message.error('上传文件大小不能超过100MB！')
        return
      }
      this.onUpload(file.file, 0)
      const blob = new Blob([file.file], { type: 'image/png' })
      const reader = new FileReader()
      reader.onload = (e) => {
        this.imgSrc = e.target.result
      }
      reader.readAsDataURL(blob)
      this.$refs.cropperRef.close()
      setTimeout(() => {
        this.portrait = file.file
        this.initTracker()
      }, 500)
    },
    // 选择文件改变
    onChange(file) {
      this.clearPortrait()
      const fileType = file.raw.type
      if (
        !['video/mp4'].includes(
          fileType
        )
      ) {
        this.$message.error('只支持上传mp4格式的视频')
      } else {
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(fileType)) {
          this.picture_type = 'img'
          this.$nextTick(() => {
            this.$refs.cropperRef.open(file.raw || file)
          })
        } else {
          const size = file.raw.size / 1024 / 1024 > 100
          if (size) {
            this.$message.error('上传文件大小不能超过100MB！')
            return
          }
          getVideoInfo({
            file: file.raw
          }).then((res) => {
            if (res && res.duration < 16000) {
              this.onUpload(file.raw, 1)
              const url = URL.createObjectURL(file.raw)
              this.videoSrc = url
              this.picture_type = 'video'
              this.portrait = file.raw
              setTimeout(() => {
                this.videoPlay = true
              }, 500)
            } else {
              this.$message.error('头像视频不宜超过15秒，请重新上传！')
            }
          })
        }
      }
    },
    // 上传
    onUpload(file, type) {
      this.percent = 0
      this.uploading = true
      this.isUploading = true
      const that = this
      const authUrl = location.hostname.endsWith('.woa.com')
        ? process.env.VUE_APP_PORTAL_HOST_WOA
        : process.env.VUE_APP_PORTAL_HOST
      /* eslint-disable */
      new contentCenter.uploadFile({
        file,
        type, // 0表示上传图片，1视频 2音频 3文档
        appId: 'QLearningService',
        operateAuthUrl: `${authUrl}/training-portal-common/api/v1/portal/user/common/uploadOperateSignature`,
        onSuccess(res) {
          that.picture_id = res[0] && res[0].content_id
          if (that.result !== -1) {
            that.$emit('onPortraitChange', {
              file: that.result === 1 ? that.portrait : null,
              picture_id: that.result === 1 ? that.picture_id : '',
              picture_type: that.picture_type
            })
          }
          that.isUploading = false
        },
        onError(err) {
          that.$message.error(err)
          that.isUploading = false
        },
        onProgress(info) {
          that.percent = parseInt(info.percent * 10000) / 100
        }
      })
    },
    // 人脸检测
    initTracker() {
      if (this.portrait && this.portrait.type) {
        this.success = 0
        this.fail = 0
        this.trackerTip = ''
        if (this.tracker) {
          this.trackerTask = window.tracking.track(
            `#${this.picture_type}-check`,
            this.tracker
          )
          this.flag = true
          this.trackerTask.run()
        } else {
          // 人脸捕捉 设置各种参数 实例化人脸捕捉实例对象,注意canvas上面的动画效果。
          this.tracker = new window.tracking.ObjectTracker(['face', 'mouth']) // tracker实例
          this.tracker.setInitialScale(4)
          this.tracker.setStepSize(2) // 设置步长
          this.tracker.setEdgesDensity(0.1)
          try {
            this.trackerTask = window.tracking.track(
              `#${this.picture_type}-check`,
              this.tracker
            ) // 开始追踪
          } catch (e) {
            this.trackerTip = '访问资源失败，请重试上传'
          }
          // 开始捕捉方法 一直不停的检测人脸直到检测到人脸
          this.tracker.on('track', (e) => {
            if (this.flag) {
              this.flag = false
              if (e.data.length > 0) {
                this.success++
              } else {
                this.fail++
              }
              this.trackerTip = '人脸校验中...'
              setTimeout(() => {
                this.flag = true
              }, 1000)
            }
          })
        }
        if (this.picture_type === 'img') {
          setTimeout(() => {
            this.trackerTask && this.trackerTask.stop()
            this.result = this.success === 1 ? 1 : 0
            this.trackerTip =
              this.success === 1
                ? '人脸校验通过'
                : '校验失败，请重新上传，保证人脸清晰可见'
            if (this.picture_id) {
              this.$emit('onPortraitChange', {
                file: this.success === 1 ? this.portrait : null,
                picture_id: this.success === 1 ? this.picture_id : '',
                picture_type: 'img'
              })
            }
          }, 200)
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.portrait-upload {
  height: 167px;
  display: flex;
  position: relative;
  :deep(.el-upload-dragger), .upload-img-box {
    width: 112px;
    height: 167px;
    border-radius: 3px;
    background-color: #fbfbf9;
    border: 1px dashed #dedede;
    text-align: center;
    cursor: pointer;
    .upload-icon {
      margin-top: 51px;
      display: inline-block;
      width: 40px;
      height: 40px;
      background: url(~@/assets/img/upload.png) no-repeat center/cover;
    }
    .upload-text {
      margin-top: -4px;
      color: #666;
      font-size: 14px;
    }
  }
  .has-upload {
    height: 167px;
    border-radius: 3px;
    position: relative;
    border: 1px dashed #d9d9d9;
    box-sizing: border-box;
    #portrait-video,
    #portrait-img {
      height: 165px;
      border-radius: 3px;
    }
    #portrait-img {
      width: 110px;
    }
    .btn {
      width: 100%;
      height: 165px;
      opacity: 0;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 3px;
      i + i {
        margin-left: 15px;
      }
      i {
        font-size: 24px;
        color: #fff;
        cursor: pointer;
      }
    }
  }
  .has-upload:hover {
    .btn {
      background-color: rgba(0, 0, 0, 0.4);
      opacity: 1;
      transition: opacity 0.3s;
    }
  }
  .portrait-desc {
    flex: 1;
    margin-left: 20px;
    font-size: 14px;
    line-height: 22px;
    .guide-title {
      color: #333;
      font-weight: bold;
      > span {
        margin-right: 16px;
      }
    }
    .guide-limit {
      margin: 6px 0 18px;
      .warn-text{
        color: #d63535;
      }
    }
    .progress {
      margin: 6px 0 6px 0;
      line-height: 22px;
      .status {
        margin-right: 8px;
        color: #666;
      }
      .icon {
        border-radius: 50%;
        border: unset;
        background-color: #00a870;
        color: #fff;
      }
    }
    :deep(.el-progress) {
      width: 246px;
      .el-progress-bar {
        .el-progress-bar__outer {
          background-color: #f2f2f2;
        }
      }
    }
    .tracking-text {
      margin: 6px 0 6px 0;
      line-height: 22px;
      .status {
        margin-right: 8px;
        color: #666;
      }
      .status-fail {
        color: #d63535;
      }
      .percent {
        margin-right: 8px;
        color: #0052d9;
      }
      .icon {
        font-size: 12px;
        padding: 2px;
        border-radius: 50%;
        border: unset;
        background-color: #00a870;
        color: #fff;
        transform: scale(0.88);
      }
    }
  }
  #video-check {
    z-index: -1;
    height: auto;
  }
  #img-check {
    z-index: -2;
    height: 450px;
  }
  #video-check,
  #img-check {
    position: fixed;
    top: 0;
    right: 300px;
    width: 300px !important;
  }
}
</style>
