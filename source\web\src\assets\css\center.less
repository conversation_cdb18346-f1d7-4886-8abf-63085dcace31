.tincy-form {
  .tox.tox-tinymce {
    border:1px solid #ccc !important;
    height: 450px;
    .tox-sidebar-wrap .tox-edit-area {
      min-height: 300px !important;
    }
  }
}
.el-cascader {
  line-height: 32px;
}

.el-input__icon {
  line-height: 32px;
}

.el-input {
  height: 32px;

  .el-input__inner {
    line-height: 32px;
    height: 32px;
    padding: 5px 30px 5px 8px
  }
}
.el-input--prefix .el-input__inner {
  padding-left: 30px;
}
.el-textarea__inner {
  padding: 5px 10px
}

.el-textarea__inner:focus,
.el-input__inner:focus {
  border-color: #0052D9;
}
.el-form {
  .el-form-item__label, .el-form-item__content {
    color: #000000
  }
}
.online-course-dropdown {
  padding: 4px;
  li + li {
    margin-top: 4px;
  }
  .el-dropdown-menu__item {
    padding: 0 12px
  }
  .el-dropdown-menu__item:focus, 
  .el-dropdown-menu__item:not(.is-disabled):hover {
    color: rgba(0,82,217,1);
    font-size: 14px;
    background: rgba(245,247,249,1);
  }
}