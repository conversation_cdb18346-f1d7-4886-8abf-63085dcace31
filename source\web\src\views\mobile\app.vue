<template>
  <div id="app">
    <router-view></router-view>
  </div>
</template>

<script>
// 移动端适配
import 'amfe-flexible/index.js'
import { getLoginUser, getUserDep } from 'config/api.conf'
import translateLang from '@/views/mobile/mixins/translateLang.js'
import { loadJS } from '@/utils/tools.js'

export default {
  name: 'mobile',
  mixins: [translateLang],
  created() {
    this.loadVerify()
    this.loadWxSDK()
    this.getMobileLangJS()
  },
  computed: {
    course_id() {
      return this.$route.query.course_id || ''
    }
  },
  mounted() {
    this.getUser()
    // ios微信浏览器，第一次进入唤起底部导航栏
    const u = navigator.userAgent
    const isiOS = !!u.match(/iP(hone|od|ad).*OS\s([\d_]+)\slike\sMac\sOS\sX/)
    console.log(isiOS, '是不是isiOS')
    if (isiOS) {
      window.history.replaceState({}, 'title', '')
    }
  },
  methods: {
    // 获取登陆用户信息
    getUser() {
      getLoginUser().then((res) => {
        const userInfo = {
          staff_id: res.staff_id,
          staff_name: res.staff_name
        }
        this.getUserDepData(res.staff_id)
        this.$store.commit('setUserInfo', userInfo)
        this.autoInstanceMob(userInfo)
        sessionStorage.setItem('login_user', JSON.stringify(userInfo))
      })
    },
    // 获取用户所属部门
    getUserDepData(staff_id) {
      getUserDep({ staff_id }).then((res) => {
        this.$store.commit('setUserDepInfo', res)
        sessionStorage.setItem('login_user_dep', JSON.stringify(res))
      })
    },
    // 引入微信小程序 SDK
    loadWxSDK() {
      let weixin = document.createElement('script')
      weixin.type = 'text/javascript'
      weixin.src = 'https://res.wx.qq.com/open/js/jweixin-1.3.2.js'
      document.getElementsByTagName('head')[0].appendChild(weixin)
    },
    loadVerify() {
      loadJS(`//${location.host}/training/verify/index.js?ts=${Math.random()}`, () => {
        window.$informationReview.create({
          dev: process.env.NODE_ENV !== 'production'
        })
      })
    }
  }
}
</script>

<style lang="less">
@import '~assets/mobileCss/vant.less';
#app {
  font-size: 14px;
  font-family: @PingFangSC;
  font-weight: 400;
  background-color: #F6F7F9;
}

// 文字超出两行省略号
.overflow-l2 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden; //溢出内容隐藏
  text-overflow: ellipsis; //文本溢出部分用省略号表示
  display: -webkit-box; //特别显示模式
  -webkit-line-clamp: 2; //行数
  line-clamp: 2;
  -webkit-box-orient: vertical; //盒子中内容竖直排列
  word-break: break-all;
  word-wrap: break-word;
}
.overflow-l3 {
  text-overflow: -o-ellipsis-lastline;
  overflow: hidden; //溢出内容隐藏
  text-overflow: ellipsis; //文本溢出部分用省略号表示
  display: -webkit-box; //特别显示模式
  -webkit-line-clamp: 3; //行数
  line-clamp: 3;
  -webkit-box-orient: vertical; //盒子中内容竖直排列
  word-break: break-all;
  word-wrap: break-word;
}
//清除浮动
.clearfix:after {
  content: "";
  display: block;
  height: 0;
  clear: both;
  visibility: hidden;
}
// 文字超出一行省略号
.overflow-l1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; // 默认不换行；
  word-wrap: break-word;
}
</style>
