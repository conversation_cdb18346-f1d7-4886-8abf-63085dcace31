export default {
  data() {
    return {}
  },
  computed: {
    // 字符串去除html标签提取文字
    stripHtmlTagsButKeepCertainTags2() {
      return (input) => {
        if (!input) return ''
        return input.replace(/<[^>]*>|&nbsp;/g, '')
      }
    },
    // 字符串去除html标签提取文字
    stripHtmlTagsButKeepCertainTags() {
      return (input) => {
        if (!input) return ''
        // 先处理保留的标签内容，将其替换为临时占位符
        const placeholder = 'PLACEHOLDER_FOR_HIGHLIGHTED_TEXT'
        const highlightedPattern = /<span class='highlight'>(.*?)<\/span>/gi
        const placeholders = []
        let match
        while ((match = highlightedPattern.exec(input)) !== null) {
          placeholders.push(match[1])
          input = input.replace(match[0], placeholder)
        }
        // 去除所有 HTML 标签
        const regex = /<\/?[^>]+(>|$)/g
        input = input.replace(regex, '')
        // 恢复被替换的保留标签内容
        placeholders.forEach(content => {
          input = input.replace(placeholder, `<span class='highlight'>${content}</span>`)
        })
        return input
      }
    }
  },
  methods: {
    getUrl() {
      // let pas = window.location.hostname.endsWith('.woa.com')
      // if (pas) {
      return process.env.NODE_ENV === 'production' ? 'https://portal.learn.woa.com' : 'https://test-portal-learn.woa.com'
      // } else {
      //   return process.env.NODE_ENV === 'production' ? 'https://learn.woa.com' : 'http://test.v8.learn.oa.com'
      // }
    },
    specailJump(url, page_id) {
      if (url) {
        if (url.indexOf('?') !== -1) {
          let arrParams = this.getUrlParams(url.split('?')[1])
          if (!arrParams.includes('from')) {
            window.open(url + '&from=LabelSubscribe')
          } else {
            window.open(url)
          }
        } else {
          window.open(url + '?from=LabelSubscribe')
        }
      } else {
        window.open(`${this.getUrl()}/user/special?page_id=${page_id}&from=LabelSubscribe`)
      }
    },
    getUrlParams(strUrl) {
      if (!strUrl) return []
      let urlArr = strUrl.split('&')
      let arr = []
      urlArr.forEach(item => {
        if (item.indexOf('=') !== -1) {
          let name = item.split('=')[0]
          arr.push(name)
        }
      })
      return arr
    }
  }
}
