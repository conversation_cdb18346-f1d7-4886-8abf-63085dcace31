import COS from 'cos-js-sdk-v5'
import {
  operatesignature,
  getFile
} from 'config/api.conf'

const fileTypeArr = [{
  suffix: ['mp4', 'mkv', 'avi', 'wmv', 'mpg', 'rmvb', 'mpv', 'flv', 'mov', 'ts'],
  file_type: 1,
  file_type_name: '视频'
},
{
  suffix: ['mp3', 'aac', 'wav', 'wma', 'flac', 'cda', 'ape'],
  file_type: 2,
  file_type_name: '音频'
},
{
  suffix: ['xls', 'xlsx', 'doc', 'docx', 'ppt', 'pptx', 'htm', 'pdf', 'txt', 'xps', 'csv'],
  file_type: 3,
  file_type_name: '文档'
},
{
  suffix: ['zip', 'rar', 'iso', '7z', 'gz', 'gzip'],
  file_type: 4,
  file_type_name: '压缩包'
},
{
  suffix: ['png', 'jpg', 'bmp', 'jpeg', 'gif', 'webp'],
  file_type: 5,
  file_type_name: '图片',
  prompt: '请选择小于2M的图片',
  size: 2 * 1024 * 1024
}
]

const credentialsQueue = []

function getUploadAuthorization(file_name) {
  return operatesignature({
    file_name
  }).then((res) => {
    let credentials = res.credentials
    if (!res || !credentials) {
      return
    }
    // 鉴权信息在COS的getAuthorization需要被用到，需要先存储起来
    credentialsQueue.push(res)
    return res
  })
};

const cos = new COS({
  getAuthorization(options, callback) {
    let data = credentialsQueue[credentialsQueue.length - 1]
    // eslint-disable-next-line standard/no-callback-literal
    callback({
      TmpSecretId: data.credentials.tmp_secret_id,
      TmpSecretKey: data.credentials.tmp_secret_key,
      XCosSecurityToken: data.credentials.session_token,
      StartTime: data.start_time, // 时间戳，单位秒，如：1580000000，建议返回服务器时间作为签名的开始时间，避免用户浏览器本地时间偏差过大导致签名错误
      ExpiredTime: data.expired_time, // 时间戳，单位秒，如：1580000900
      ScopeLimit: false // 细粒度控制权限需要设为 true，会限制密钥只在相同请求时重复使用--如果是要单次使用，即每次上传都需要获取一次签名时，那么就需要改为false
    })
  }
})

/**
 * Object:
 * - file: 上传文件的内容，可以为 File 对象 或者 Blob 对象
 */
let uploadCOS = function ({
  file,
  onHashProgress = () => {},
  onProgress = () => {},
  onSuccess = () => {},
  onError = () => {}
}) {
  if (!file) {
    onError('请选择文件')
    return
  }
  // 取消上传用的id
  let uploadTaskId = null

  getUploadAuthorization(file.name).then(authData => {
    let suffixName = file.name.split('.').reverse()[0]
    let file_type
    let file_type_name
    let file_size = true
    let prompt
    for (let item of fileTypeArr) {
      if (item.suffix.includes(suffixName.toLowerCase())) {
        file_type = item.file_type
        file_type_name = item.file_type_name
        switch (file_type) {
          case 5:
            file_size = file.size < item.size
            prompt = item.prompt
            break
          default:
            break
        }
        break
      }
    }

    if (!file_type && !file_type_name) {
      onError('请选择正确的文件格式')
      return
    }
    if (!file_size) {
      onError(prompt)
      return
    }

    cos.sliceUploadFile({
      Bucket: authData.bucket,
      Region: authData.region,
      Key: authData.key,
      Body: file,
      onTaskReady(taskId) {
        uploadTaskId = taskId
      },
      onHashProgress,
      onProgress
    },
    // 错误或成功回调
    function (err, data) {
      // cos鉴权完成后删除储存的鉴权信息
      credentialsQueue.shift()

      if (err) {
        onError(err)
      } else {
        // 返回的数据格式是：Bucket（桶名字）、ETag、Key（对象的键）、Location（直接访问地址）
        // 后续步骤：
        // 将返回的结果--传递到我们的后台，我们要对其访问的地址等信息进行储存
        // 我们后台完成之后，返回一个id值，作为一个关联关系值，租户继续他的业务逻辑，以后需要查上传的视频时，就将该id进行用于查询。
        getFile({
          // app_id写死
          app_id: 'A9BiosXihR0h46ThNsAX',
          file_name: file.name,
          file_name_new: authData.file_name_new,
          file_size: file.size,
          file_type,
          file_type_name,
          storage_id: data.Key
        }).then(res => {
          if (res) {
            onSuccess(res)
          } else {
            onError(res)
          }
        }).catch(err => {
          onError(err)
        })
      }
    }
    )
  })

  return function cancelUpload() {
    if (uploadTaskId !== null) {
      cos.cancelTask(uploadTaskId)
    } else {
      let count = 0
      let intervalid = setInterval(() => {
        count++
        if (uploadTaskId !== null || count >= 50) {
          clearInterval(intervalid)(uploadTaskId && cos.cancelTask(uploadTaskId))
        }
      }, 100)
    }
  }
}

export default uploadCOS
