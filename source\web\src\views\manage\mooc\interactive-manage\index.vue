<template>
  <div class="interaction-page">
    <h3>课程互动配置</h3>
    <div class="interaction-add">
      <div class="interaction-radio">
        <el-row>
          <el-col :span="12">
            <div class="interaction-radio-content">
              <label>互动功能开关</label>
              <el-radio-group v-model="courseConfigInfo.enable_interactive" @change="interactiveChange">
                <el-radio :label="true">开启</el-radio>
                <el-radio :label="false">关闭</el-radio>
              </el-radio-group>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <div class="interaction-radio-content">
              <label>进度条控制</label>
              <el-radio-group v-model="courseConfigInfo.limit_progress_bar" @change="progressChange">
                <el-radio :label="false" :disabled="courseConfigInfo.enable_interactive">不限制</el-radio>
                <el-radio :label="true">不可拖动进度条</el-radio>
              </el-radio-group>
            </div>
          </el-col>
        </el-row>
      </div>
      <div class="interaction-add-button">
        <el-button type="primary" size="mini" @click="handleAdd">新增互动</el-button>
      </div>
      <div class="interaction-dialogform">
        <el-dialog :title="dialogTitle" :visible.sync="configDialogFormVisible" :close-on-click-modal="false" @close="resetinteractiveDialogForm" width="82%">
          <el-form :model="interactiveDialogForm" :rules="interactionConfigDialogFormRules" ref="interactiveDialogForm">
            <el-row>
              <el-col :span="24">
                <el-form-item label="互动标题" prop="title" :label-width="labelWidth">
                  <el-input
                    type="textarea"
                    :rows="3"
                    placeholder=""
                    v-model="interactiveDialogForm.title">
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item label="互动时间点" prop="activeTime">
                  <el-time-picker
                    ref="timePicker"
                    v-model="interactiveDialogForm.activeTime"
                    arrow-control
                    placeholder="任意时间点"
                    @focus="handleTimePickerFocus"
                    @change="changeTimePoint"
                  >
                  </el-time-picker>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item prop="activeType" label="互动类型" :label-width="labelWidth">
                  <el-select v-model="interactiveDialogForm.activeType" placeholder="请选择" @change="changeActiveType">
                    <el-option label="选择类互动" value="choose"></el-option>
                    <el-option label="投票类互动" value="vote"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item v-if="interactiveDialogForm.activeType === 'choose'" prop="interactionTypeRadio">
                  <div class="inter-radio-select">选择类型 
                    <el-radio v-model="interactiveDialogForm.interactionTypeRadio" label="single" @change="handleRadioChange">单选</el-radio>
                    <el-radio v-model="interactiveDialogForm.interactionTypeRadio" label="multi" @change="handleRadioChange">多选</el-radio>
                  </div>
                </el-form-item>
                <el-form-item  v-if="interactiveDialogForm.activeType === 'vote'" prop="votingRestrictions">
                  <div class="inter-radio-select">投票限制 
                    <el-radio>
                        至多可选择<input v-model.number="interactiveDialogForm.votingRestrictions" @change="votingRestrictionsChange" style="width: 20px; height: 20px; border: 1px solid blue; margin: 0 4px;">项
                    </el-radio>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-form-item :label-width="labelRadioWidth">
                <div class="flex-box">
                  <div v-if="interactiveDialogForm.activeType === 'choose'">
                    <div>
                      <label class="label-box">完成条件</label>
                      <el-radio v-model="interactiveDialogForm.completionConditions" label="correct">选择正确</el-radio>
                      <el-radio v-model="interactiveDialogForm.completionConditions" label="choose">选择即可</el-radio>
                    </div>
                  </div>
                  <div v-if="interactiveDialogForm.activeType === 'vote'">
                    <div>
                      <label class="label-box">完成条件</label>
                      <el-radio v-model="interactiveDialogForm.votingJust" label="choose">投票即可</el-radio>
                    </div>
                  </div>
                  <div class="tips">用户达成此条件时，则视为完成互动，可继续学习</div>
                </div>
              </el-form-item>
            </el-row>        
            <el-form-item label="提示语" :label-width="formLabelWidth">        
              <el-input
                type="textarea"
                :rows="2"
                placeholder="为用户提供有关完成的提示语，将在用户点击“继续学习”按钮，但未达成完成条件时对用户显示"
                v-model="interactiveDialogForm.promptMessage">
              </el-input>           
            </el-form-item>
            <el-form-item label="互动介绍" :label-width="formLabelWidth">
              <el-input
                type="textarea"
                :rows="3"
                placeholder="可在此向用户提供当前互动的背景介绍和完成指引"
                v-model="interactiveDialogForm.interactiveIntroduction">
              </el-input>
            </el-form-item>
            <el-row>
              <el-form-item label="互动选项">
                <el-col :span="24">
                  <div class="interactive-options-list" v-for="(item, index) in interactiveDialogForm.arrOptionsList" :key="item.index">
                    <div class="interactive-options">
                      <div class="interactive-options-content">
                        <el-form-item v-model="item.option_value">
                          <el-row>
                            <el-col :span="interactiveDialogForm.activeType === 'choose'? 12 : 24">
                              <div class="interactive-options-serialNum">序号：{{ index + 1}}</div>
                            </el-col>
                            <el-col :span="12" v-if="interactiveDialogForm.activeType === 'choose'">
                              <el-checkbox v-model="item.selected" @change="handleCheckboxChange($event, item, index)">设为正确选项</el-checkbox>
                            </el-col>
                          </el-row>
                        </el-form-item>
                      </div>
                      <div class="delete-btn"><i class="el-icon-delete" @click="handleDeletOptinsItem(index)" :style="iconStyles"></i></div>
                    </div>
                    <el-row>
                      <el-col :span="24">
                        <div class="interactive-options-box">
                          <el-form-item label="选项内容：">
                            <el-input type="textarea" :rows="3" v-model="item.option_text"></el-input>
                          </el-form-item>                  
                        </div>
                      </el-col>
                    </el-row>
                  </div>        
                  <el-button @click="addClick">+ 添加选项</el-button>
                </el-col>
              </el-form-item>
            </el-row>
          </el-form>
          <div slot="footer" class="dialog-footer">
            <el-button @click="resetinteractiveDialogForm()">取消</el-button>
            <el-button type="primary" @click="handleSubmit('interactiveDialogForm')">{{ isEditing ? '保存修改' : '确认新增' }}</el-button>
          </div>
        </el-dialog>
        <el-dialog title="提示" :visible.sync="showDialog" width="30%">
          <p>当前选择类型为单选，仅可选择1个正确选项，是否替换当前选项为正确选项？</p>
          <span slot="footer" class="dialog-footer">
            <el-button @click="confirmReplace">确认</el-button>
            <el-button @click="cancelReplace">取消</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
    <div class="interaction-table-list">
      <el-table :data="courseInteractionConfigeTableData" max-width="864" height="378">
        <el-table-column prop="question_id" label="互动ID" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="active_time" label="时间点" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="title" label="互动标题" min-width="240" show-overflow-tooltip></el-table-column>
        <el-table-column prop="active_type" label="互动类型" min-width="80" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.active_type === 'choose'">选择</span>
            <span v-else-if="scope.row.active_type === 'vote'">投票</span>
          </template>
        </el-table-column>
        <el-table-column prop="creator_name" label="创建人" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="created_at" label="创建时间" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="operate" label="操作" min-width="160" fixed="right" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleEdit(scope.$index, scope.row)">编辑</el-button>
            <el-button
              type="text"
              @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="interaction-pagination">
      <div class="interaction-pagination-block">
        <el-pagination
          @size-change="handleInteractionPaginationSizeChange"
          @current-change="handlenteractionPaginationCurrentChange"
          :current-page="interactionConfigPaginationParams.page_no"
          :page-sizes="[10, 20, 30, 50]"
          :page-size="interactionConfigPaginationParams.page_size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="interactionConfigPaginationParams.total">
        </el-pagination>  
      </div>
    </div>
    <h3 class="title-box">互动记录</h3>
    <div class="interaction-record-button">
      <div class="interaction-record-export">
        <el-button type="primary" size="mini" @click="exportRecordsList">导出</el-button>
      </div>
    </div>
    <div class="interaction-table-list">
      <el-table :data="courseInteractionRecordTableData" max-width="864" height="378">
        <el-table-column prop="serialNumber" label="序号" width="60" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="creator_name" label="用户名" min-width="120" show-overflow-tooltip></el-table-column>
        <el-table-column prop="question_id" label="互动ID" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column prop="title" label="互动标题" min-width="240" show-overflow-tooltip></el-table-column>
        <el-table-column prop="active_type" label="互动类型" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.active_type === 'choose'">选择</span>
            <span v-else-if="scope.row.active_type === 'vote'">投票</span>
          </template>
        </el-table-column>
        <el-table-column prop="active_answer" label="互动行为" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ (scope.row.active_answer).replace(/,/g, ';') }}
          </template>
        </el-table-column>
        <el-table-column prop="enabled_correct" label="是否正确" min-width="100" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.active_type === 'choose' ? scope.row.enabled_correct ? "是" : "否" : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="互动时间" min-width="160" show-overflow-tooltip></el-table-column>
        <el-table-column prop="operate" label="操作" min-width="120" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="handleInteractionRecordDetail(scope.$index, scope.row, true)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="interaction-pagination">
      <div class="interaction-pagination-block">
        <el-pagination
          @size-change="handleInteractionRecordSizeChange"
          @current-change="handleInteractionRecordCurrentChange"
          :current-page="interactionRecordPaginationParams.page_no"
          :page-sizes="[10, 20, 30, 50, 100, 150, 200]"
          :page-size="interactionRecordPaginationParams.page_size"
          layout="total, sizes, prev, pager, next, jumper"
          :total="interactionRecordPaginationParams.total">
          共：显示总数 行
        </el-pagination>  
      </div>
    </div>
    <div>
      <el-dialog
        title="互动记录详情"
        :visible.sync="recordsDialogFormVisible"
        width="82%"
        :close-on-click-modal="false"
        @close="closeRecordsDialogForm"
        center>
        <el-form class="records-body" ref="recordsDetailDialogForm" v-model="recordsDetailDialogForm">
          <el-form-item>
            <div class="record-detail-list-header">
              <div class="record-detail-list-title">
                <div class="record-detail-list-title-item">用户：{{ recordsDetailDialogForm.creator_name }}</div>
                <div class="record-detail-list-title-item">互动时间：{{ recordsDetailDialogForm.created_at }}</div>
              </div>
              <div class="record-detail-list-title">
                <div class="record-detail-list-title-item">互动ID：{{ recordsDetailDialogForm.question_id }}</div>
                <div v-if="recordsDetailDialogForm.active_type === 'choose'" class="record-detail-list-title-item">互动类型：选择</div>
                <div v-else class="record-detail-list-title-item">互动类型：投票</div>
              </div>
              <div class="record-detail-list-title">
                <div class="record-detail-list-title-item">用户互动行为：{{ recordsDetailDialogForm.active_answer }}</div>
                <div v-if="recordsDetailDialogForm.enabled_correct === true" class="record-detail-list-title-item">是否正确：是</div>
                <div v-if="recordsDetailDialogForm.enabled_correct === false" class="record-detail-list-title-item">是否正确：否</div>
              </div>
            </div>
          </el-form-item>
          <el-form-item v-if="recordsDetailDialogForm.title">
            <div class="record-detail-list-activeTitle">{{ recordsDetailDialogForm.title }}</div>
          </el-form-item>
          <el-form-item v-if="recordsDetailDialogForm.introduction">
            <div class="record-detail-list-activeIntroduction">{{ recordsDetailDialogForm.introduction }}</div>
          </el-form-item>
          <el-form-item>
            <div v-for="(item, index) in recordsDetailDialogForm.optionsArray" :key='index'>
              <div class="check-p" v-if="recordsDetailDialogForm.active_type == 'choose'">
                <el-checkbox v-model="item.selected" @change="handleRecordCheckboxChange(item)" disabled>
                </el-checkbox>
                <div class="check-text">{{ item.optionValue }}、{{ item.optionText }}</div>
              </div>
              <div v-else>{{ item.optionValue }}、{{ item.optionText }}</div>
            </div>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="closeRecordsDialogForm()">关闭</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import env from 'config/env.conf.js'
import { getCourseInteractionPage, getCourseInteractionDelete, getCourseInteractionNew, getCourseInteractionRecordPage, getCourseInteractionDetails, getCourseInteractionRevise, getCourseDetailInfo, updateInteractiveStatus, updateProgressBar, getInteractiveRecordInfo } from '@/config/mooc.api.conf.js'
export default {
  data() {
    return {
      courseConfigInfo: {},
      courseInteractionConfigeTableData: [], // 互动配置表格数据
      courseInteractionRecordTableData: [], // 互动记录表格数据
      interactionConfigPaginationParams: {
        page_no: 1,
        page_size: 10,
        total: 0
      },
      interactionRecordPaginationParams: {
        page_no: 1,
        page_size: 10,
        total: 0
      },
      configDialogFormVisible: false,
      recordsDialogFormVisible: false,
      interactiveDialogForm: {
        title: '',
        activeTime: new Date(2023, 1, 1, 0, 0),
        activeType: 'choose',
        interactionTypeRadio: 'single',
        completionConditions: 'correct',
        votingRestrictions: '',
        votingJust: 'choose',
        promptMessage: '',
        interactiveIntroduction: '',
        arrOptionsList: [{
          option_value: 1,
          option_text: '', // 选项内容
          selected: false
        }]
      },
      newInteractiveDialogForm: null,
      interactionConfigDialogFormRules: {
        title: [
          { required: true, message: '请输入互动标题', trigger: 'blur' }
          // { min: 3, max: 5, message: '长度在 3 到 5 个字符', trigger: 'blur' }
        ],
        activeTime: [
          { required: true, message: '请输入互动时间点', trigger: 'change' }
        ],
        activeType: [
          { required: true, message: '请输入互动类型', trigger: 'change' }
        ]
      },
      formLabelWidth: '90px',
      labelWidth: '90px',
      labelRadioWidth: '30px',
      firstLoad: true,
      defaultTime: 'Fri Oct 20 2023 00:00:00 GMT+0800 (中国标准时间)', // 设置默认时间格式对象
      totalSeconds: '', // 互动时间传参设置
      selectedOptionValues: [],
      showDialog: false, // 控制是否显示提示框
      selectedCorrectCount: 0, // 记录选中的正确选项数量
      selectedOptionIndex: -1, // 记录选中的正确选项的索引
      currentOptionIndex: -1, // 当前点击选项索引
      isEditing: true,
      recordsDetailDialogForm: {
        optionsArray: {}
      },
      isVoteTpye: false, // 互动记录详情为投票类型则不展示checkbox
      isChoosechecked: true
    }
  },
  watch: {
    'interactiveDialogForm.activeTime': {
      handler(val) {
      }
    }
  },
  computed: {
    courseId() {
      return this.$route.query?.net_course_id || 0
    },
    iconStyles() {
      if (this.isDeleteDisabled) {
        return {
          pointerEvents: 'none',
          opacity: '0.5'
        }
      }
      return {}
    },
    isDeleteDisabled() {
      return this.interactiveDialogForm.arrOptionsList.length <= 1
    },
    dialogTitle() {
      return this.isEditing ? '编辑互动' : '新建互动'
    }
  },
  created() {
    // 在组件创建时赋初值，例如从某个数据源中获取初始值
    this.interactiveDialogForm.activeTime = null
  },
  mounted() {
    this.getCourseDetailInfoFn()
    this.getCourseInteractionConfigTableList()
    this.getCourseInteractionRecordTableList()
  },
  beforeDestroy() {
    // 在组件销毁前清除值
    this.interactiveDialogForm.activeTime = null
  },
  methods: {
    // 获取课程互动配置信息
    getCourseDetailInfoFn() {
      getCourseDetailInfo(this.courseId).then(res => {
        this.courseConfigInfo = res
      })
    },
    // 互动功能开关
    interactiveChange(e) {
      if (e) {
        this.courseConfigInfo.limit_progress_bar = true
      }
      this.updateInteractiveStatusFn()
    },
    // 进度条控制
    progressChange(e) {
      this.updateProgressBarFn()
    },
    updateInteractiveStatusFn() {
      updateInteractiveStatus({
        act_type: 2,
        course_id: this.courseId,
        status: this.courseConfigInfo.enable_interactive ? 1 : 0
      }).then(res => {
        this.$message.success('修改成功')
      }).catch(() => {
        this.courseConfigInfo.enable_interactive = !this.courseConfigInfo.enable_interactive
        this.courseConfigInfo.limit_progress_bar = !this.courseConfigInfo.limit_progress_bar
      })
    },
    updateProgressBarFn() {
      updateProgressBar({
        act_type: 2,
        course_id: this.courseId,
        status: this.courseConfigInfo.limit_progress_bar ? 1 : 0
      }).then(res => {
        this.$message.success('修改成功')
      })
    },
    handleRecordCheckboxChange(item) {
      if (this.recordsDetailDialogForm.active_type === 'choose') {
        item.selected = false
      }
    },
    handleCheckboxChange(select, item, index) {
      if (this.interactiveDialogForm.activeType === 'choose' && !this.interactiveDialogForm.interactionTypeRadio && item.selected) {
        item.selected = false
        this.$message.warning('请先选择类型')
        return
      }
      this.currentOptionIndex = index
      // 选择类互动&&单选
      if (this.interactiveDialogForm.activeType === 'choose' && this.interactiveDialogForm.interactionTypeRadio === 'single') {
        if (select) {
          if (this.selectedCorrectCount === 1) {
            item.selected = false
            // 如果已经选择了一个正确选项，弹出确认框
            this.showDialog = true
          } else if (this.selectedCorrectCount === 0) {
            // 如果没有选择正确选项，将当前选项标记为正确
            this.selectedCorrectCount = 1
            this.selectedOptionIndex = index
          }
        } else {
          if (!item.selected && this.selectedOptionIndex === index) {
            this.selectedCorrectCount = 0
            this.selectedOptionIndex = -1
          } else if (!item.selected && this.selectedCorrectCount === 1) {
            // 当前点击和当前项选中都为falses,并且存在已选项，打开弹窗
            this.showDialog = true
          }
        }
      }
    },
    confirmReplace() {
      // 用户确认替换当前选项为正确选项
      const item = this.interactiveDialogForm.arrOptionsList[this.currentOptionIndex]
      item.selected = true
      this.selectedCorrectCount = 1
      this.selectedOptionIndex = this.currentOptionIndex
      // 类型为单选时，清空其它选项
      if (this.interactiveDialogForm.activeType === 'choose') {
        this.interactiveDialogForm.arrOptionsList.map((v, i) => {
          if (i !== this.currentOptionIndex) {
            v.selected = false
          }
        })
      }
      this.showDialog = false
    },
    cancelReplace() {
      this.showDialog = false
    },
    handleRadioChange (value) {
      if (value === 'single') {
        this.interactiveDialogForm.interactionTypeRadio = 'single' // 设置选中值为单选
        this.interactiveDialogForm.arrOptionsList.map(v => {
          v.selected = false
        })
        this.selectedCorrectCount = 0
        this.selectedOptionIndex = -1
      } else if (value === 'multi') {
        this.interactiveDialogForm.interactionTypeRadio = 'multi' // 设置选中值为多选
      }
    },
    // 点击编辑拿到详情数据
    handleEdit(index, row) {
      this.configDialogFormVisible = true
      this.isEditing = true
      getCourseInteractionDetails(row.id, row.interactive_id).then((res) => {
        this.interactiveDialogForm.interactive_id = res.interactive_id
        this.interactiveDialogForm.id = res.id
        this.interactiveDialogForm.title = res.title
        let cur = new Date(new Date().setHours(0, 0, 0, 0))
        let setS = cur.setSeconds(cur.getSeconds() + res.active_time)

        this.interactiveDialogForm.activeTime = new Date(setS)
        this.totalSeconds = res.active_time
        this.interactiveDialogForm.activeType = res.select_content[0].active_type
        if (res.select_content[0].active_type && res.select_content[0].active_type === 'choose') {
          this.interactiveDialogForm.interactionTypeRadio = res.select_content[0].choose_type_config.type
          this.interactiveDialogForm.completionConditions = res.select_content[0].choose_type_config.completion_conditions
        }
        if (res.select_content[0].active_type && res.select_content[0].active_type === 'vote') {
          this.interactiveDialogForm.votingRestrictions = res.select_content[0].vote_type_config.can_max_vote_nums
          this.interactiveDialogForm.votingJust = res.select_content[0].vote_type_config.completion_conditions
        }
        this.interactiveDialogForm.promptMessage = res.continue_studying_tips
        this.interactiveDialogForm.interactiveIntroduction = res.introduction
        this.interactiveDialogForm.arrOptionsList = res.select_content[0].options.map(item => ({
          option_value: item.option_value,
          option_text: item.option_text,
          selected: false
        }))
        res.select_content[0].correct_answer.map((item) => {
          let index = this.interactiveDialogForm.arrOptionsList.findIndex(v => v.option_value === item)
          if (index !== -1) {
            this.interactiveDialogForm.arrOptionsList[index].selected = true
          }
        })
        if (res.select_content[0].correct_answer.length > 0) {
          this.selectedCorrectCount = 1
        }
      })
    },
    handleAdd() {
      this.selectedCorrectCount = 0
      this.selectedOptionIndex = -1
      this.configDialogFormVisible = true
      this.isEditing = false
    },
    // 重置表单数据
    resetinteractiveDialogForm() {
      this.configDialogFormVisible = false
      this.$refs.interactiveDialogForm.resetFields()
      this.interactiveDialogForm = {
        title: '',
        activeTime: null,
        activeType: 'choose',
        interactionTypeRadio: 'single',
        completionConditions: 'correct',
        votingRestrictions: '',
        votingJust: 'choose',
        promptMessage: '',
        interactiveIntroduction: '',
        arrOptionsList: [{
          option_value: 1, // 选项对应值 
          option_text: '', // 选项内容
          selected: false
        }]
      }
      this.selectedCorrectCount = 0
      this.selectedOptionIndex = -1
    },
    getCourseInteractionConfigTableList() {
      let params = {
        act_type: 2,
        course_id: this.courseId, 
        current: this.interactionConfigPaginationParams.page_no,
        size: this.interactionConfigPaginationParams.page_size
      }
      getCourseInteractionPage(params).then((res) => {
        this.courseInteractionConfigeTableData = res.records
        this.interactionConfigPaginationParams.total = res.total
        this.interactionConfigPaginationParams.page_no = res.current
        this.interactionConfigPaginationParams.page_size = res.size
      })
    },
    handleInteractionPaginationSizeChange(size) {
      this.interactionConfigPaginationParams.page_size = size
      this.getCourseInteractionConfigTableList()
    },
    handlenteractionPaginationCurrentChange(current) {
      this.interactionConfigPaginationParams.page_no = current
      this.getCourseInteractionConfigTableList()
    },
    handleDelete(row) {
      this.$messageBox
        .confirm('确认删除该条数据吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          getCourseInteractionDelete(row.id, row.interactive_id).then((res) => {
            this.$message({
              type: 'success',
              message: '已删除'
            })
            this.getCourseInteractionConfigTableList()
          })
        })
    },
    // 获取互动记录列表和详情数据
    getCourseInteractionRecordTableList() {
      let params = {
        act_type: 2,
        course_id: this.courseId,
        current: this.interactionRecordPaginationParams.page_no,
        size: this.interactionRecordPaginationParams.page_size
      }
      getCourseInteractionRecordPage(params).then((res) => {
        this.courseInteractionRecordTableData = res.records
        this.interactionRecordPaginationParams.total = res.total
        this.interactionRecordPaginationParams.page_no = res.current
        this.interactionRecordPaginationParams.page_size = res.size
      })
    },
    handleInteractionRecordSizeChange(size) {
      this.interactionRecordPaginationParams.page_size = size
      this.getCourseInteractionRecordTableList()
    },
    handleInteractionRecordCurrentChange(current) {
      this.interactionRecordPaginationParams.page_no = current
      this.getCourseInteractionRecordTableList()
    },
    // 获取详情数据
    handleInteractionRecordDetail(index, row, type) {
      if (type === true) {
        this.recordsDialogFormVisible = true
      }
      getInteractiveRecordInfo(row.id).then(res => {
        this.recordsDetailDialogForm = res
        const jsonArray = JSON.parse(res.select_content)
        if (jsonArray) {
          this.recordsDetailDialogForm.select_content = jsonArray
          const optionsArray = JSON.parse(JSON.stringify(res.select_content.options))
          if (res.select_content && res.active_type === 'choose') {
            let answerList = res.active_answer.split(',')
            answerList.forEach(e => {
              let ind = optionsArray.findIndex(v => v.optionValue === e)
              if (ind !== -1) {
                optionsArray[ind].selected = true
              } else {
                optionsArray[ind].selected = false
              }
            })
          }
          this.recordsDetailDialogForm.optionsArray = optionsArray
        }
      })
    },
    closeRecordsDialogForm() {
      this.recordsDialogFormVisible = false
      this.$refs.recordsDetailDialogForm.resetFields()
    },
    // 导出详情表格数据
    exportRecordsList() {
      let params = new FormData()
      params.append('course_id', this.courseId)
      params.append('current', this.interactionRecordPaginationParams.page_no)
      params.append('size', this.interactionRecordPaginationParams.page_size)
      params.append('act_type', 2)
      axios({
        url: `${
          env[process.env.NODE_ENV].trainingPath
        }/api/courseInteraction/manage/interaction/export-interactive-record`,
        method: 'POST',
        data: params,
        responseType: 'blob'
      }).then((res) => {
        if (res.status === 200 && res.data) {
          const url = window.URL.createObjectURL(new Blob([res.data]))
          const link = document.createElement('a')
          link.href = url
          link.setAttribute('download', '互动记录列表.xlsx')
          document.body.appendChild(link)
          link.click()
          this.$message.success('导出成功')
        } else {
          this.$message.error('导出失败')
        }
      })
    },
    // 删除单个选项
    handleDeletOptinsItem(index) {
      if (this.interactiveDialogForm.arrOptionsList.length > 1) {
        this.interactiveDialogForm.arrOptionsList.splice(index, 1)
        // 更新剩余选项的序号和index值
        for (let i = 0; i < this.interactiveDialogForm.arrOptionsList.length; i++) {
          this.interactiveDialogForm.arrOptionsList[i].option_value = i + 1
        }
        if (this.interactiveDialogForm.activeType === 'choose' && this.interactiveDialogForm.interactionTypeRadio === 'single') {
          let index = this.interactiveDialogForm.arrOptionsList.findIndex(v => v.selected)
          if (index === -1) {
            this.selectedCorrectCount = 0
            this.selectedOptionIndex = -1
          }
        }
      }
    },
    // 添加选项
    addClick() {
      const newIndex = this.interactiveDialogForm.arrOptionsList.length + 1
      if (this.interactiveDialogForm.arrOptionsList.length < 30) {
        this.interactiveDialogForm.arrOptionsList.push({ option_text: '', option_value: newIndex, selected: false })
      } else {
        this.$message({
          type: 'warning',
          message: '至多可配置30个选项!'
        })
      }
    },
    changeActiveType(val) {
      if (val === 'vote') {
        this.interactiveDialogForm.votingRestrictions = 1
        this.interactiveDialogForm.arrOptionsList = [{ option_text: '', option_value: 1 }]
      }
    },
    // 提交新增互动表单数据
    postInteractiveDialogForm() {
      const newArrOptionsList = this.interactiveDialogForm.arrOptionsList
      for (let i = newArrOptionsList.length - 1; i >= 0; i--) {
        if (newArrOptionsList[i].selected === false) {
          delete newArrOptionsList[i].selected
        }
      }
      let params = {
        act_type: 2,
        course_id: this.courseId,
        title: this.interactiveDialogForm.title,
        active_time: this.totalSeconds,
        continue_studying_tips: this.interactiveDialogForm.promptMessage,
        introduction: this.interactiveDialogForm.interactiveIntroduction,
        select_content: [{
          question_text: this.interactiveDialogForm.title,
          active_type: this.interactiveDialogForm.activeType,
          options: newArrOptionsList
        }]
      }
      // 选择不同的互动类型传不同参数
      if (this.interactiveDialogForm.activeType === 'choose') {
        params.select_content[0].choose_type_config = {
          type: this.interactiveDialogForm.interactionTypeRadio,
          completion_conditions: this.interactiveDialogForm.completionConditions
        }
      } else if (this.interactiveDialogForm.activeType === 'vote') {
        if (this.interactiveDialogForm.votingRestrictions <= 0) {
          this.$message.warning('请输入投票限制至多可选择项')
          return
        }
        params.select_content[0].vote_type_config = {
          can_max_vote_nums: this.interactiveDialogForm.votingRestrictions,
          completion_conditions: this.interactiveDialogForm.votingJust
        }
      }
      // 处理设置选项的option_value参数
      if (this.interactiveDialogForm.arrOptionsList.length >= 0) {
        this.selectedOptionValues = this.interactiveDialogForm.arrOptionsList
          .filter((item) => item.selected)
          .map((item) => item.option_value)
        params.select_content[0].correct_answer = this.selectedOptionValues
      }
      // 提交表单调新增接口
      getCourseInteractionNew(params).then((res) => {
        this.$message.success('新增成功')
        this.resetinteractiveDialogForm()
        this.getCourseInteractionConfigTableList()
      })
    },
    handleSubmit(interactiveDialogForm) {
      this.$refs[interactiveDialogForm].validate((valid) => {
        if (valid) {
          // 新增提交数据
          if (!this.isEditing) {
            this.postInteractiveDialogForm()
          } else {
            // 编辑提交数据
            this.editInteractiveDialogForm()
          }
        } else {
          return false
        }
      })
    },
    // 修改更新数据
    editInteractiveDialogForm() {
      const newArrOptionsList = this.interactiveDialogForm.arrOptionsList
      for (let i = newArrOptionsList.length - 1; i >= 0; i--) {
        if (newArrOptionsList[i].selected === false) {
          delete newArrOptionsList[i].selected
        }
      }
      let params = {
        act_type: 2,
        course_id: this.courseId,
        id: this.interactiveDialogForm.id,
        interactive_id: this.interactiveDialogForm.interactive_id,
        title: this.interactiveDialogForm.title,
        active_time: this.totalSeconds,
        continue_studying_tips: this.interactiveDialogForm.promptMessage,
        introduction: this.interactiveDialogForm.interactiveIntroduction,
        select_content: [{
          question_text: this.interactiveDialogForm.title,
          active_type: this.interactiveDialogForm.activeType,
          options: newArrOptionsList
        }]
      }
      // 选择不同的互动类型传不同参数
      if (this.interactiveDialogForm.activeType === 'choose') {
        params.select_content[0].choose_type_config = {
          type: this.interactiveDialogForm.interactionTypeRadio,
          completion_conditions: this.interactiveDialogForm.completionConditions
        }
      } else if (this.interactiveDialogForm.activeType === 'vote') {
        params.select_content[0].vote_type_config = {
          can_max_vote_nums: this.interactiveDialogForm.votingRestrictions,
          completion_conditions: this.interactiveDialogForm.votingJust
        }
      }
      // 处理设置选项的option_value参数
      if (this.interactiveDialogForm.arrOptionsList.length >= 0) {
        this.selectedOptionValues = this.interactiveDialogForm.arrOptionsList
          .filter((item) => item.selected)
          .map((item) => item.option_value)
        params.select_content[0].correct_answer = this.selectedOptionValues
      }
      getCourseInteractionRevise(params).then((res) => {
        this.$message.success('修改成功')
        this.resetinteractiveDialogForm()
        this.getCourseInteractionConfigTableList()
      })
    },
    handleTimePickerFocus() {
      if (!this.interactiveDialogForm.activeTime) {
        this.interactiveDialogForm.activeTime = this.defaultTime
      }
    },
    changeTimePoint(val) {
      if (this.firstLoad) {
        const dateObject = new Date(val)
        const hours = dateObject.getHours() // 获取小时
        const minutes = dateObject.getMinutes() // 获取分钟
        const seconds = dateObject.getSeconds() // 获取秒
        this.totalSeconds = (hours * 3600) + (minutes * 60) + seconds
        this.interactiveDialogForm.activeTime = dateObject
      }
      // 当点击清空时，将日期设置为0点
      let date = new Date(this.interactiveDialogForm.activeTime)
      if (date.getFullYear() === 1970) {
        this.interactiveDialogForm.activeTime = this.defaultTime
      }
    },
    votingRestrictionsChange(e) {
      if (e.target._value <= 0) {
        this.interactiveDialogForm.votingRestrictions = 1
      } else if (e.target._value > 30) {
        this.interactiveDialogForm.votingRestrictions = 30
      }
    }
  }
}
</script>

<style scoped lang="less">
.interaction-page{
  width: 100%;
  min-height: 100vh;
  background: #fff;
  padding: 0 14px 0;
}
.interaction-add{
  width: 100%;
  min-height: 10px;
  margin: 20px 0;
  position: relative;
}
.interaction-radio{
  width: 100%;
  height: 50%;
  position: relative;
}
.interaction-radio-content{
  padding: 5px 10px;
  & > label {
    display: inline-block;
    padding-right: 12px;
    font-weight: bold;
  }
}
.interaction-add-button{
  position: absolute;
  top: 0px;
  right: 50px;
}
.interaction-record-button{
  width: 100%;
  min-height: 10px;
  margin: 10px 0;
  padding: 0 10px;
  position: relative;
}
.interaction-record-export{
  position: absolute;
  top: -28px;
  right: 50px;
}
.interaction-pagination-block{
  margin: 0 auto;
}
.interaction-dialogform{
  position: relative;
  :deep(.el-dialog) {
    .el-dialog__body {
      max-height: 70vh;
      overflow-y: auto;
    }
  }
}
.interactive-options-list{
  margin: 20px;
  .interactive-options{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    .delete-btn:hover {
      pointer-events: auto;
      cursor: pointer;
    }
  }
}
.interactive-options-content{
  display: flex;
  flex-direction: row;
  justify-content:flex-start;
  align-self: center;
}
.interactive-options-box{
}
.interaction-completionConditions{
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  position: relative;
}
.interaction-radio-title{
  margin: 0 5px;
  position: absolute;
  right: 230px;
  top: 5px;
}
.record-detail-list-header{
  width: 600px;
  height: auto;
  font-size: 16px;
  font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
  line-height: 1.5;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.record-detail-list-title{
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}
.record-detail-list-title-item{
  width: 50%;
}
.record-detail-list-activeTitle{
  width: 90%;
  font-size: 17px;
  font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
}
.record-detail-list-activeIntroduction{
  width: 90%;
  line-height: 1.5;
  font-size: 14px;
  font-family: 'Franklin Gothic Medium', 'Arial Narrow', Arial, sans-serif;
  background-color: rgb(241, 243, 245);
  padding: 6px 10px;
  border-radius: 6px;
}
.interaction-items{
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}
.el-pagination{
  margin-top: 6px;
  justify-content: center
}
.check-p {
  display: flex;
  margin-bottom: 12px;
  :deep(.el-checkbox__input.is-disabled.is-checked) {
    .el-checkbox__inner {
      background-color: #0052D9;
      border-color: #fff;
    }
  }
  .check-text {
    margin-left: 8px;
    padding-right: 20px;
    padding-right: 16px;
    line-height: 1.8;
  }
}
.records-body {
  width: 100%;
  max-height: 70vh;
  overflow-x: hidden;
  overflow-y: auto;
}
.title-box {
  padding-top: 30px;
}
.inter-radio-select {
  margin: 4px 4px 4px 20px;
}
.interaction-table-list {
  :deep(.el-table) {
    .el-table__header-wrapper table {
      width: 864px !important;
    }
    .el-table__body-wrapper table {
      width: max-content !important;
    }
    .el-table__header-wrapper table, .el-table .el-table__body-wrapper table {
      width: max-content !important;
    }
  }
}
.el-button.el-button--text:hover,
.el-button.el-button--text:focus {
  border-color: transparent !important;
  background-color: transparent !important;
}
.tips {
  margin-left: 20px;
  color: #a6a6a6;    
}
.label-box {
  padding-right: 12px;
  margin-left: -6px;
}
.flex-box {
  display: flex;
  align-items: center;
}
</style>
