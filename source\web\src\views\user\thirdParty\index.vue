<template>
  <div class="third-party">
    <div class="third-party-wrapper">
      <p class="title">{{ thirdPartyInfo.act_name }}</p>
      <div class="operate">
        <el-button @click="goStudy" type="primary" size="small">点击前往学习</el-button>
        <el-button class="refresh-status" @click="refreshStatus" size="small" v-if="!thirdPartyStatus.is_finished">刷新任务状态</el-button>
      </div>
      <div class="explanation-item">
        <div class="item-label">
          <span class="line"></span>
          <span class="label-text">{{ $langue('Mooc_TaskDetail_ThirdParty_ConditionDesc', { defaultText: '完成条件说明' }) }}</span>
        </div>
        <div class="item-value">{{ thirdPartyInfo.finished_condition_desc || "-" }}</div>
      </div>
      <div class="explanation-item">
        <div class="item-label">
          <span class="line"></span>
          <span class="label-text">{{ $langue('Mooc_TaskDetail_ThirdParty_TaskStatus', { defaultText: '任务状态' }) }}</span>
        </div>
        <div class="item-value">
          <div>
            <span class="task-status-icon" :class="taskProcess.className">{{ taskProcess.text }}</span>
            <span v-if="taskProcess.className === 'done-icon'">{{$langue('Mooc_ProjectDetail_TrainingProgress_FinishedTime', { defaultText: '完成时间：' })}}{{ thirdPartyStatus.finished_time }}</span>
          </div>
          <CustomTips
            v-if="!thirdPartyStatus.is_finished"
            class="refresh-tips"
            :title="$langue('Mooc_TaskDetail_ThirdParty_ClickToRefreshTaskStatus', { defaultText: '如已满足任务完成条件，可点击上方按钮刷新任务状态' })" 
            IconName="el-icon-warning" 
            backgroundColor="#fdf6ec" 
            color="#FF7548"
            lineHeight="40px"
            >
          </CustomTips>
        </div>
      </div>
      <div class="explanation-item" v-if="thirdPartyInfo.extensions && thirdPartyInfo.extensions.length">
        <div class="item-label">
          <span class="line"></span>
          <span class="label-text">{{ $langue('Mooc_TaskDetail_ThirdParty_TaskRemark', { defaultText: '其他补充信息' }) }}</span>
        </div>
        <div class="item-value">
          <div class="replenish-info" v-for="(item, index) in thirdPartyInfo.extensions" :key="index">
            <span class="field-name">{{item.name}}：</span>
            <span class="value">{{item.value || '-'}}</span>
          </div>
        </div>
      </div>
      <div class="explanation-item">
        <div class="item-label">
          <span class="line"></span>
          <span class="label-text">{{ $langue('Mooc_TaskDetail_ThirdParty_TaskDetailDesc', { defaultText: '任务详细说明' }) }}</span>
        </div>
        <div class="item-value des-value" v-if="thirdPartyInfo.task_desc">
          <sdc-mce-preview
            v-if="thirdPartyInfo.task_desc"
            ref="editor"
            :urlConfig="editorConfig.urlConfig"
            :catalogue.sync="editorConfig.catalogue"
            :content="thirdPartyInfo.task_desc"
          >
          </sdc-mce-preview>
        </div>
        <div class="empty" v-else>
          <img class="empty-img" src="@/assets/img/empty.png" alt="" />
          <div class="empty-text">{{ $langue('Mooc_Common_Alert_NoData', { defaultText: '暂无内容' }) }}</div>
        </div>
      </div>
    </div>
    <el-dialog :title="$langue('Mooc_Common_Authority_TaskAlert', { defaultText: '任务提示' })" :visible.sync="mobileVisible" width="358px" class="mobile-dialog"
      :before-close="closeMobileDialog">
      <div class="mobile-dialog-content">
        <p class="task-name">该第三方任务暂不支持PC端访问，请在移动端进行学习</p>
        <div class="qrcode-img">
          <img v-if="qrUrl" :src="qrUrl" />
        </div>
        <p>{{ $langue('Mooc_Common_Authority_ViewByWechat', { defaultText: '请使用微信/企业微信扫码查看任务' }) }}</p>
        <el-input v-model="urlMobile" type="text" disabled class="copy-btn">
          <template slot="append">
            <el-button @click="doCopy()">{{ $langue('Mooc_ProjectDetail_Copy', { defaultText: '复制' }) }}</el-button>
          </template>
        </el-input>

      </div>
    </el-dialog>
  </div>
</template>
<script>
import CustomTips from '@/components/tips.vue'
import { getMobileQrcode, refreshTaskUser, getSingleTaskInfo } from '@/config/mooc.api.conf.js'
import MoocJs from 'sdc-moocjs-integrator'
import translate from 'mixins/translate.vue'
export default {
  name: '',
  mixins: [translate],
  components: {
    CustomTips
  },
  data() {
    return {
      thirdPartyInfo: {},
      thirdPartyStatus: {},
      disabledRefresh: false,
      qrUrl: '',
      mobileVisible: false,
      editorConfig: {
        catalogue: false,
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      },
      taskExtensions: {}
    }
  },
  computed: {
    isPreview() {
      return this.$route.query.previewType === 'preview'
    },
    mooc_course_id() {
      return this.$route.query?.mooc_course_id || ''
    },
    task_id() {
      return this.$route.query?.task_id || ''
    },
    taskProcess() {
      let className = ''
      let text = ''
      if (this.thirdPartyStatus?.is_finished === true) {
        className = 'done-icon' // 已完成
        text = this.$langue('Mooc_ProjectDetail_TaskList_TaskFinished', { defaultText: '任务已完成' })
      } else {
        // loading-icon 进行中 / wait-icon 未开始
        className = this.thirdPartyStatus?.is_finished === false ? 'loading-icon' : 'wait-icon'
        text = this.thirdPartyStatus?.is_finished === false ? this.$langue('Mooc_ProjectDetail_TaskList_TaskInProgress', { defaultText: '任务进行中' }) : this.$langue('Mooc_ProjectDetail_TaskList_TaskNotStart', { defaultText: '任务未开始' })
      }

      return { className, text }
    },
    urlMobile() {
      // 小程序线上短码地址
      const url = process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/TZELHU' : 'http://s.test.yunassess.com/s/urrd9E'
      return `${url}?scheme_type=mooc&mooc_course_id=${this.mooc_course_id}&task_id=${this.task_id}`
    }
  },
  mounted() { 
    MoocJs.messageListener((res) => {
      if (res.events === 'taskInfo') {
        let info = res.params
        this.thirdPartyStatus = {
          is_finished: info.is_finished,
          finished_time: info.finished_time || ''
        }
        if (typeof (info.extensions) === 'string' && info.extensions) {
          this.taskExtensions = JSON.parse(info.extensions)
        }
        this.getData()   
      } else if (res.events === 'tencent-mooc-lang') {
        if (res.events === 'tencent-mooc-lang') {
          localStorage.setItem('sdc-sys-def-lang', res.params)
          this.getLangJS()
        } 
      }
    })
  },
  methods: {
    getData() {
      getSingleTaskInfo(this.task_id).then(res => {
        this.thirdPartyInfo = res
        // eslint-disable-next-line no-useless-escape
        if (['<p><br data-mce-bogus=\"1\"></p>', '<p><br></p>'].includes(res.task_desc)) {
          this.thirdPartyInfo.task_desc = ''
        }
        if (typeof (res.extensions) === 'string' && res.extensions) {
          let extensions = JSON.parse(res.extensions)
          extensions = extensions.map(item => {
            return {
              name: item.name,
              value: this.taskExtensions[item.key] || ''
            }
          })
          this.thirdPartyInfo.extensions = extensions
        }   
      })
    },
    goStudy() {
      if (this.thirdPartyInfo.resource_url) {
        window.open(this.thirdPartyInfo.resource_url, '_blank')
      } else {
        this.mobileVisible = true
        this.getMobileUrl()
      }
    },
    closeMobileDialog() {
      this.mobileVisible = false
    },
    refreshStatus() {
      if (this.isPreview) return
      if (this.disabledRefresh) {
        this.$message.warning(`${this.$langue('Mooc_TaskDetail_ThirdParty_RefreshHighFrequency', { defaultText: '刷新频率过高，请在1分钟后重试' })}~`)
        return
      }
      this.disabledRefresh = true
      const timer = setTimeout(() => {
        this.disabledRefresh = false
      }, 60000)
      this.$once('hook:beforeDestory', () => {
        clearTimeout(timer)
      })
      let params = {
        mooc_course_id: this.mooc_course_id,
        task_id: this.task_id,
        staff_id: this.$store.state.userInfo.staff_id
      }
      refreshTaskUser(params).then(() => {
        this.$message.success(this.$langue('Mooc_TaskDetail_ThirdParty_RefreshStatusSucessed', { defaultText: '刷新任务状态成功' }))
        if (this.$route.query.from === 'mooc') {
          MoocJs.postMessage('updateStatus')
        }
      })
    },
    getMobileUrl() {
      const params = {
        scene: `${this.mooc_course_id}_${this.task_id}_mooc`,
        page: 'pages/webview/mooc/taskDetail',
        env_version: process.env.NODE_ENV === 'production' ? 'release' : 'trial'
      }
      return getMobileQrcode(params).then((res) => {
        this.qrUrl = `data:image/png;base64,${res}`
      })
    },
    doCopy() {
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      input.value = this.urlMobile
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      this.$message.success(this.$langue('Mooc_Common_Alert_CopySucessed', { defaultText: '复制成功' }))
      // 复制后移除输入框
      input.remove()
    }
  }
}
</script>
<style lang="less" scoped>

.third-party {
  height: 100%;
  display: flex;
  justify-content: center;
  padding-top: 10px;

  .third-party-wrapper {
    width: 1000px;
    background-color: #fff;
    padding: 24px;

    .title {
      font-size: 16px;
      font-weight: 600;
      color: #000000ff;
    }

    .operate {
      margin-top: 24px;
    }

    .refresh-status {
      margin-left: 28px;
    }
  }
}

.explanation-item {
  color: #00000099;
  margin-top: 32px;
  .des-value {
    word-break: break-all;
  }

  .item-label {
    display: flex;
    align-items: center;

    .line {
      display: inline-block;
      width: 4px;
      height: 16px;
      background: #0052d9ff;
      margin-right: 12px;
    }
  }

  .item-value {
    padding-left: 16px;
    margin-top: 16px;
    color: #000000e6;

    .radio-item {
      margin-right: 32px;
    }

    .radio-icon {
      width: 16px;
      height: 16px;
      border: 1px solid #dcdcdcff;
      background: #ffffffe6;
      border-radius: 50%;
      display: inline-block;
      position: relative;
      top: 3px;
      margin-right: 8px;
    }
  }

  .task-status-icon {
    position: relative;
    padding-left: 24px;
    margin-right: 20px;
    white-space: break-spaces;
    &::before {
      content: "";
      width: 18px;
      height: 18px;
      display: inline-block;
      position: absolute;
      top: 1px;
      left: 0;
    }
  }

  .wait-icon {
    color: #00000099;

    &::before {
      background: url("~@/assets/mooc-img/wait.png") no-repeat center / cover;
    }
  }

  .loading-icon {
    color: #0052D9;

    &::before {
      background: url("~@/assets/mooc-img/loading.png") no-repeat center / cover;
    }
  }

  .done-icon {
    color: #00a870ff;

    &::before {
      background: url("~@/assets/mooc-img/done.png") no-repeat center / cover;
    }
  }

  .refresh-tips {
    margin-top: 12px;
  }

  .replenish-info {
    margin-bottom: 8px;

    .field-name {
      color: #00000099;
    }

    .value {
      margin-left: 8px;
    }
  }

  .empty {
    padding-top: 20px;
    color: #999;
    text-align: center;

    .empty-img {
      margin-bottom: 20px;
      width: 178px;
      height: 130px;
    }
  }
}

:deep(.mobile-dialog) {
  .el-dialog__header {
    border: none;
  }

  .el-dialog__body {
    padding-top: 0;
  }

  .mobile-dialog-content {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    .qrcode-img{
      width: 200px;
      height: 200px;
      margin-bottom: 20px;
      img{
        width: 100%;
        height: 100%;
      }
    }
    .copy-btn {
      margin-top: 20px;
    }

    .el-input-group__append {
      color: #000000e6;
    }

  }
}
</style>
