module.exports = {
  root: true,
  env: {
    node: true
  },
  'extends': [
    'plugin:vue/essential',
    '@vue/standard'
  ],
  rules: {
    'camelcase': 'off',
    'valid-typeof': 'off',
    'no-sequences': 'off',
    'no-return-assign': 'off',
    'no-trailing-spaces': 'off',
    'operator-linebreak': 'off',
    'space-before-function-paren': 'off',
    'prefer-promise-reject-errors': 'off',
    // 'indent': 'off',
    'no-console': 'off',
    'no-debugger': process.env.NODE_ENV === 'development' ? 'off' : 'error'
  },
  parserOptions: {
    parser: 'babel-eslint'
  }
}
