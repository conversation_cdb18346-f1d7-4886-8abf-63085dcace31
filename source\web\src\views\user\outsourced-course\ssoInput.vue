<template>
    <div>
        acsUrl: <input type="text" name="acsUrl" v-model="formData.acsUrl" /><br>
        <form method="post" :action="formData.acsUrl" id="samlform">
          SAMLResponse: <input type="text" name="SAMLResponse" v-model="formData.samlResponse" /><br>
          relayState: <input type="text" name="RelayState" v-model="formData.relayState" /><br>
            <input type="submit" value="Submit" />
        </form>
    </div>
</template>
<script>
// import { postSSO } from 'config/mooc.api.conf.js'
export default {
  name: '',
  data() {
    return {
      formData: {
        samlResponse: '',
        acsUrl: '',
        relayState: ''
      }
    }
  },
  mounted() {
    // let { fullPath } = this.$route
    // let param = ''
    // if (fullPath.indexOf('?')) {
    //   param = fullPath.split('?')[1]
    // }
    // if (param) {
    //   this.requestApi(param)
    // }
  },
  methods: {
    // requestApi(params) {
    //   postSSO(params).then(res => {
    //     console.log(res, 'aaaaaaa~~~~~~~~~~~')
    //     this.formData = res
    //     // this.$nextTick(() => {
    //     //   document.getElementById('samlform').submit()
    //     // })
    //   })
    // }
  }
}
</script>

<style scoped>
div{
  padding: 10px;
}
input{
  width: 200px;
  height: 30px;
  border: 1px solid #ddd;
  margin: 5px;
}
</style>
