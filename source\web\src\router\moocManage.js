const manage = [
  // 培训管理端
  {
    path: '/mooc',
    name: 'mooc',
    component: () => import('views/manage/index.vue'),
    meta: {
      title: '培养项目管理端'
    },
    children: [
      {
        path: 'manage/project-list',
        name: 'project-list',
        component: () => import('views/manage/mooc/trainManage/projectList/index.vue'),
        meta: {
          title: '培养项目管理端',
          breadcrumb: ['培养项目管理端', '项目列表']
        }
      },
      {
        path: 'manage/project-create',
        name: 'project-create',
        component: () => import('views/manage/mooc/trainManage/projectList/project-create.vue'),
        meta: {
          title: '创建项目',
          breadcrumb: ['培养项目管理端', '项目创建']
        }
      },
      {
        path: 'manage/banner-list',
        name: 'banner-list',
        component: () => import('views/manage/mooc/banner-manage/banner-list/index.vue'),
        meta: {
          title: '轮播图管理',
          breadcrumb: ['轮播图管理', '项目列表']
        }
      }
    ]
  },
 
  // mooc后台管理
  {
    path: '/mooc/manage',
    name: '/mooc/manage',
    component: () => import('views/manage/index-mooc-manage.vue'),
    meta: {
      title: 'MOOC',
      breadcrumb: ['培养项目管理', '项目管理']
    },
    children: [
      {
        path: '/mooc/manage/basic-setting',
        name: 'basic-setting',
        component: () => import('views/manage/mooc/project-manage/basic-setting/index.vue'),
        meta: {
          title: '基础设置',
          breadcrumb: ['培养项目管理', '基础设置']
        }
      },
      {
        path: '/mooc/manage/task-list',
        name: 'task-list',
        component: () => import('views/manage/mooc/project-manage/task-list/index.vue'),
        meta: {
          title: '任务组织',
          breadcrumb: ['培养项目管理', '任务组织']
        }
      },
      {
        path: '/mooc/manage/members',
        name: 'members',
        component: () => import('views/manage/mooc/project-manage/members/index.vue'),
        meta: {
          title: '学员管理',
          breadcrumb: ['培养项目管理', '学员管理']
        }
      },
      {
        path: '/mooc/manage/regist-setting',
        name: 'regist-setting',
        component: () => import('views/manage/mooc/project-manage/regist-setting/index.vue'),
        meta: {
          title: '报名管理',
          breadcrumb: ['培养项目管理', '报名管理']
        }
      },
      {
        path: '/mooc/manage/interactive',
        name: 'interactive',
        component: () => import('views/manage/mooc/project-manage/interactive/index.vue'),
        meta: {
          title: '互动管理',
          breadcrumb: ['培养项目管理', '互动管理']
        }
      },
      {
        path: '/mooc/manage/report',
        name: 'report',
        component: () => import('views/manage/mooc/project-manage/report/index.vue'),
        meta: {
          title: '数据统计',
          breadcrumb: ['培养项目管理', '数据统计']
        }
      },
      {
        path: '/mooc/manage/advanced-setting',
        name: 'advanced-setting',
        component: () => import('views/manage/mooc/project-manage/advanced-setting/index.vue'),
        meta: {
          title: '高级设置',
          breadcrumb: ['培养项目管理', '高级设置']
        }
      },
      {
        path: '/mooc/manage/work-task-detail',
        name: 'work-task-detail',
        component: () => import('views/manage/mooc/project-manage/workTaskDetail/index.vue'),
        meta: {
          title: '作业任务详情',
          breadcrumb: ['培养项目管理', '作业任务详情']
        }
      },
      {
        path: '/mooc/manage/external-info',
        name: 'external-info',
        component: () => import('views/manage/geekbang/externalCourse/externalContentInfo/index.vue'),
        meta: {
          title: '外部内容信息',
          breadcrumb: ['培养项目管理', '外部内容信息']
        }
      }
    ]
  },
  {
    path: '/mooc/manage/work',
    name: 'work',
    component: () => import('views/manage/mooc/project-manage/work/index.vue'),
    meta: {
      title: '作业',
      breadcrumb: ['培养项目管理', '作业']
    }
  },
  // {
  //   path: '/mooc/manage/questionnair',
  //   name: 'questionnair',
  //   component: () => import('views/manage/mooc/project-manage/questionnair/index.vue'),
  //   meta: {
  //     title: '问卷'
  //   }
  // },
  {
    path: '/mooc/manage/interactive-manage',
    name: 'interactive-manage',
    component: () => import('views/manage/mooc/interactive-manage/index.vue'),
    meta: {
      title: '互动管理',
      breadcrumb: ['培养项目管理', '互动管理']
    }
  }
]

export default manage
