<template>
  <div class="reply-content">
    <el-image
      :src="
        winCommentData.emp_name
          ? winCommentData.emp_name === '小腾老师'
            ? xt
            : getAvatar(winCommentData.emp_name)
          : avatar
      "
      class="avatar"
    >
      <div class="image-slot" slot="placeholder">
        <i class="el-icon-loading"></i>
      </div>
      <div class="error-avatar" slot="error">
        <img :src="avatar" alt="" />
      </div>
    </el-image>
    <div class="comment">
      <div class="container">
        <div class="top">
          <span class="user-name">{{ winCommentData.emp_name }}</span>
          <span
            class="mine tag"
            v-show="winCommentData.emp_name === winCommentData.userName"
            >我的</span
          >
          <span
            class="author tag"
            v-show="
              winCommentData.emp_name === winCommentData.authorName &&
              winCommentData.authorName !== winCommentData.userName
            "
            >作者</span
          >
          <span class="to-top tag" v-show="winCommentData.up_pass === 1"
            >置顶</span
          >
        </div>
        <div class="content">{{ winCommentData.content }}</div>
        <div class="bottom">
          <span class="date">{{ winCommentData.created_at }}</span>
          <div class="btn-box">
            <span class="like-box handle-box" @click="onLike">
              <span
                :class="[
                  'like-icon',
                  ' icon',
                  { 'like-active': winCommentData.is_like }
                ]"
              ></span>
              <span class="like-count">{{
                winCommentData.praise_count || 0
              }}</span>
            </span>
            <span class="reply-box handle-box" @click="onReply">
              <span class="reply-icon icon"></span>
              <span class="reply btn">回复</span>
            </span>
            <span>
              <span
                class="top-box handle-box"
                v-show="winCommentData.up_pass !== 1"
                @click="onTop"
              >
                <span class="top-icon icon"></span>
                <span class="to-top btn">置顶</span>
              </span>
              <span
                class="cancel-top-box handle-box"
                v-show="winCommentData.up_pass === 1"
                @click="onTop"
              >
                <span class="cancel-top-icon icon"></span>
                <span class="cancel-top btn">取消置顶</span>
              </span>
              <span
                class="show-box handle-box"
                v-show="winCommentData.need_show !== 1"
                @click="onShow"
              >
                <span class="show-icon icon"></span>
                <span class="show btn">显示</span>
              </span>
              <span
                class="hide-box handle-box"
                v-show="winCommentData.need_show === 1"
                @click="onShow"
              >
                <span class="hide-icon icon"></span>
                <span class="hide btn">隐藏</span>
              </span>
            </span>
            <span
              class="delete-box handle-box"
              @click="deleteComment" >
              <span class="delete-icon icon"></span>
              <span class="delete btn">删除</span>
            </span>
          </div>
        </div>
      </div>
      <comment-textarea
        v-show="winCommentData.isShowArea"
        :reply_staff_name="winCommentData.emp_name"
        :userName="winCommentData.userName"
        @onHandleComment="onHandleReply"
      />
      <div
        class="scroll-area"
        v-infinite-scroll="onLoad"
        infinite-scroll-distance="20px"
        infinite-scroll-disabled="disabled"
      >
        <div class="reply-content" v-for="item in replyData" :key="item.id">
          <el-image
            lazy
            :src="
              item.emp_name
                ? item.emp_name === '小腾老师'
                  ? xt
                  : getAvatar(item.emp_name)
                : avatar
            "
            class="avatar"
          >
            <div class="image-slot" slot="placeholder">
              <i class="el-icon-loading"></i>
            </div>
            <div class="error-avatar" slot="error">
              <img :src="avatar" alt="" />
            </div>
          </el-image>
          <div class="reply">
            <div class="top">
              <span class="user-name">{{ item.emp_name }}</span>
              <span v-show="item.reply_staff_name"> 回复 </span>
              <span v-show="item.reply_staff_name" class="target-name">{{
                item.reply_staff_name
              }}</span>
              <span
                class="mine tag"
                v-show="item.emp_name === winCommentData.userName && type !== 'manage'"
                >我的</span
              >
              <span
                class="author tag"
                v-show="
                  item.emp_name === winCommentData.authorName &&
                  winCommentData.authorName !== winCommentData.userName || (type === 'manage' && item.emp_name === '小腾老师')
                "
                >作者</span
              >
            </div>
            <pre class="content">{{ item.content }}</pre>
            <div class="bottom">
              <span class="date">{{ item.created_at }}</span>
              <div class="btn-box">
                <span class="like-box handle-box" @click="onLikeReply(item.id)">
                  <span
                    :class="[
                      'like-icon',
                      ' icon',
                      { 'like-active': item.is_like }
                    ]"
                  ></span>
                  <span class="like-count">{{ item.praise_count || 0 }}</span>
                </span>
                <span
                  class="reply-box handle-box"
                  @click="onReplyAgain(item.id)"
                >
                  <span class="reply-icon icon"></span>
                  <span class="reply btn">回复</span>
                </span>
                <span
                  class="delete-box handle-box"
                  @click="deleteReply(item.id)"
                >
                  <span class="delete-icon icon"></span>
                  <span class="delete btn">删除</span>
                </span>
              </div>
            </div>
            <comment-textarea
              v-show="item.isShowArea"
              :reply_staff_name="item.emp_name"
              :userName="winCommentData.userName"
              @onHandleComment="onHandleReplyAgain"
            />
          </div>
        </div>
        <loading-text :loading="loading" :total="total" />
      </div>
    </div>
  </div>
</template>
<script>
import commentTextarea from './comment-textarea.vue'
import loadingText from './loading-text.vue'
import { InfiniteScroll } from 'element-ui'
import {
  // commentTableList,
  // topComment,
  // hideComment,
  // deleteComment,
  // likeComment,
  // replyComment,
  commentTableListManage,
  topCommentManage,
  hideCommentManage,
  deleteCommentManage,
  likeCommentManage,
  replyCommentManage
} from '@/config/mooc.api.conf.js'
import { getAvatar } from 'utils/tools'

const commentTableList = ''
const topComment = ''
const hideComment = ''
const deleteComment = ''
const likeComment = ''
const replyComment = ''

const targetArr = [1, 0]

export default {
  name: 'reply-content',
  components: {
    commentTextarea,
    loadingText
  },
  props: {
    // 当前弹窗的评论信息
    winCommentData: {
      type: Object,
      default: () => {}
    },
    type: {
      type: String
    }
  },
  data() {
    return {
      xt: require('@/assets/mooc-img/comment/xt.png'),
      avatar: require('@/assets/mooc-img/comment/avatar.png'),
      replyData: [], // 回复的数据
      // active: 0, // 当前出现的回复
      pageIndex: 1,
      loading: false,
      total: 0,
      isFinished: false,
      getAvatar
    }
  },
  watch: {
    winCommentData() {
      this.pageIndex = 1
      this.loading = false
      this.isFinished = false
      this.total = 0
      this.replyData = []
      this.onLoad()
    }
  },
  computed: {
    disabled() {
      return this.loading || this.isFinished
    }
  },
  directives: {
    'infinite-scroll': InfiniteScroll
  },
  methods: {
    // 获取回复的数据
    onLoad() {
      if (this.loading) return
      this.loading = true
      const api = this.type === 'manage' ? commentTableListManage : commentTableList
      api({
        pid: this.winCommentData.id,
        act_id: this.winCommentData.act_id,
        order_type: 1,
        page_no: this.pageIndex,
        page_size: 10
      })
        .then((res) => {
          this.pageIndex++
          const arr = []
          // 先去掉手动添加的数据
          const filterData = this.replyData.filter((i) => {
            return !i.is_new
          })
          // 去重
          res &&
            res.records.forEach((i) => {
              const index = filterData.findIndex((j) => {
                return i.id === j.id
              })
              if (index === -1) {
                // 2分钟以内时间为刚刚
                i.created_at = new Date().getTime() - new Date(i.created_at.replace(/-/g, '/')).getTime() >= 3 * 60 * 1000 ? i.created_at : '刚刚'
                arr.push(i)
              }
            })
          this.replyData = this.replyData.concat(arr)
          this.total = res && res.total
          this.isFinished = this.replyData.length >= this.total
          this.$emit('setTotal', {
            id: this.winCommentData.id,
            total: this.total
          })
          this.loading = false
        })
        .catch(() => {
          this.isFinished = true
          this.loading = false
        })
    },
    // 点赞/取消点赞, 针对评论
    onLike() {
      const api = this.type === 'manage' ? likeCommentManage : likeComment
      const is_like = this.winCommentData.is_like
      api({
        id: this.winCommentData.id,
        act_id: this.winCommentData.act_id
      }).then(() => {
        if (is_like) {
          this.winCommentData.praise_count--
        } else {
          this.winCommentData.praise_count++
        }
        this.winCommentData.is_like = !is_like
        this.$emit('setLike', {
          id: this.winCommentData.id,
          is_like: !is_like
        })
        this.$message({
          type: 'success',
          message: is_like ? '取消点赞成功' : '点赞成功',
          duration: 2000
        })
      })
    },
    // 回复
    onReply() {
      this.winCommentData.isShowArea = true
      this.replyData.forEach((i) => {
        i.isShowArea = false
      })
    },
    // 置顶/取消置顶，针对评论
    onTop() {
      const up_pass = this.winCommentData.up_pass || 0
      const msg = ['置顶成功', '取消置顶成功']
      const api = this.type === 'manage' ? topCommentManage : topComment
      api({
        id: this.winCommentData.id,
        sticky: targetArr[up_pass]
      }).then(() => {
        this.winCommentData.up_pass = targetArr[up_pass]
        this.$emit('setTop', {
          id: this.winCommentData.id,
          up_pass: targetArr[up_pass]
        })
        this.$message({
          type: 'success',
          message: msg[up_pass],
          duration: 2000
        })
      })
    },
    // 显示/隐藏
    onShow() {
      let flag = false
      const need_show = this.winCommentData.need_show || 0
      const msg = ['显示成功', '隐藏成功']
      const api = this.type === 'manage' ? hideCommentManage : hideComment
      api({
        id: this.winCommentData.id,
        show: targetArr[need_show]
      }).then(() => {
        this.winCommentData.need_show = targetArr[need_show]
        // 隐藏的同时取消置顶
        if (need_show === 1 && this.winCommentData.up_pass) {
          this.winCommentData.up_pass = 0
          flag = true // 用来标志操作过隐藏
        }
        this.$emit('setShow', {
          id: this.winCommentData.id,
          need_show: targetArr[need_show],
          up_pass: this.winCommentData.up_pass,
          Showflag: flag
        })
        this.$message({
          type: 'success',
          message: msg[need_show],
          duration: 2000
        })
      })
    },
    // 删除评论
    deleteComment() {
      this.$messageBox
        .confirm(
          `删除后评论以及相关回复将无法恢复，确定要删除选中的评论吗？`,
          '删除评论提醒',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }
        )
        .then(() => {
          const api =
            this.type === 'manage' ? deleteCommentManage : deleteComment
          api(this.winCommentData.id).then(() => {
            this.$emit('deleteWinComment', {
              id: this.winCommentData.id,
              total: this.total
            })
          })
        })
    },
    // 发表回复，针对评论
    onHandleReply(obj) {
      const api = this.type === 'manage' ? replyCommentManage : replyComment
      api({
        app_id: this.winCommentData.app_id,
        act_id: this.winCommentData.act_id,
        content: obj.value,
        pid: this.winCommentData.id
      }).then((id) => {
        this.replyData.push({
          id,
          is_new: true,
          emp_name: this.winCommentData.userName,
          up_pass: 0,
          is_like: false,
          praise_count: 0,
          need_show: 1,
          isShowArea: false,
          content: obj.value,
          created_at: obj.date
        })
        this.winCommentData.isShowArea = false
        this.total++
        this.$emit('setTotal', {
          id: this.winCommentData.id,
          total: this.total
        })
        this.$message({
          type: 'success',
          message: '评论成功',
          duration: 2000
        })
      })
    },
    // 点赞/取消点赞, 针对回复
    onLikeReply(id) {
      const api = this.type === 'manage' ? likeCommentManage : likeComment
      const index = this.replyData.findIndex((i) => {
        return i.id === id
      })
      const is_like = this.replyData[index].is_like
      api({
        id,
        act_id: this.winCommentData.act_id
      }).then(() => {
        this.replyData[index].is_like = !is_like
        if (is_like) {
          this.replyData[index].praise_count--
        } else {
          this.replyData[index].praise_count++
        }
        this.$message({
          type: 'success',
          message: is_like ? '取消点赞成功' : '点赞成功',
          duration: 2000
        })
      })
    },
    // 点击回复的回复按钮
    onReplyAgain(id) {
      this.activeReply = id
      this.winCommentData.isShowArea = false
      this.replyData = this.replyData.map((e) => {
        if (e.id === id) {
          return {
            ...e,
            isShowArea: true
          }
        }
        return { ...e, isShowArea: false }
      })
    },
    // 删除回复
    deleteReply(id) {
      this.$messageBox
        .confirm(
          `回复删除后将无法恢复，确定要删除选中的回复吗？`,
          '删除回复提醒',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }
        )
        .then(() => {
          const api =
            this.type === 'manage' ? deleteCommentManage : deleteComment
          api(id).then(() => {
            const index = this.replyData.findIndex((i) => i.id === id)
            this.replyData.splice(index, 1)
            this.total--
            this.$emit('setTotal', {
              id: this.winCommentData.id,
              total: this.total
            })
            this.$message({
              type: 'success',
              message: '删除成功',
              duration: 2000
            })
          })
        })
    },
    // 发表回复的回复
    onHandleReplyAgain(obj) {
      const api = this.type === 'manage' ? replyCommentManage : replyComment
      api({
        app_id: this.winCommentData.app_id,
        act_id: this.winCommentData.act_id,
        content: obj.value,
        pid: this.winCommentData.id,
        reply_staff_name: obj.reply_staff_name
      }).then((id) => {
        const index = this.replyData.findIndex((i) => i.id === this.activeReply)
        this.replyData[index].isShowArea = false
        this.replyData.push({
          id,
          emp_name: this.winCommentData.userName,
          is_new: true,
          up_pass: 0,
          is_like: false,
          praise_count: 0,
          need_show: 1,
          isShowArea: false,
          content: obj.value,
          created_at: obj.date,
          reply_staff_name: obj.reply_staff_name
        })
        this.total++
        this.$emit('setTotal', {
          id: this.winCommentData.id,
          total: this.total
        })
        this.$message({
          type: 'success',
          customClass: 'comment-message',
          message: '评论成功',
          duration: 2000
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
.scroll-area {
  margin-top: 24px;
}
.reply-content {
  .avatar {
    width: 48px;
    height: 48px;
    margin-right: 20px;
    border-radius: 24px;
    line-height: 48px;
    text-align: center;
    color: #999;
    img {
      width: 48px;
      height: 48px;
      line-height: 48px;
      border-radius: 24px;
    }
  }
}
.comment-content,
.reply-content {
  display: flex;
  margin-top: 24px;
  &:first-of-type {
    margin-top: 0;
  }

  .comment,
  .reply {
    flex: 1;

    .container {
      padding: 22px 20px 18px;
      background-color: #f9f9f9;
    }

    .top {
      margin-bottom: 12px;
      line-height: 20px;

      .user-name,
      .target-name {
        font-size: 16px;
        font-weight: 700;
      }

      .tag {
        margin-left: 16px;
        display: inline-block;
        width: 40px;
        height: 20px;
        border-radius: 3px;
        font-size: 12px;
        color: #fff;
        text-align: center;
      }

      .mine {
        background-color: #0052d9;
      }

      .to-top {
        background-color: #c82a29;
      }

      .author {
        font-weight: bold;
        background-color: #57ccce;
      }
    }

    .content {
      line-height: 24px;
    }

    .bottom {
      margin-top: 14px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.4);

      .btn-box {
        float: right;

        .handle-box {
          margin-left: 28px;
          cursor: pointer;

          .icon {
            display: inline-block;
            width: 14px;
            height: 14px;
            margin-right: 4px;
            vertical-align: middle;
            position: relative;
            bottom: 1px;
          }

          .like-icon {
            background: url(~@/assets/mooc-img/comment/like.png) no-repeat center/cover;
          }

          .like-active {
            background: url(~@/assets/mooc-img/comment/like-active.png) no-repeat
              center/cover;
          }

          .reply-icon {
            background: url(~@/assets/mooc-img/comment/reply.png) no-repeat center/cover;
          }

          .top-icon {
            background: url(~@/assets/mooc-img/comment/top-reply.png) no-repeat center/cover;
          }

          .cancel-top-icon {
            background: url(~@/assets/mooc-img/comment/no-top-reply.png) no-repeat
              center/cover;
          }

          .show-icon {
            background: url(~@/assets/mooc-img/comment/visible-reply.png) no-repeat
              center/cover;
          }

          .hide-icon {
            background: url(~@/assets/mooc-img/comment/hide.png) no-repeat center/cover;
          }

          .delete-icon {
            background: url(~@/assets/mooc-img/comment/delete.png) no-repeat center/cover;
          }
        }
      }
    }

    .reply-count {
      color: rgba(0, 0, 0, 0.4);
      margin-top: 10px;

      .btn {
        margin-left: 12px;
        color: #3464e0;
        cursor: pointer;
      }
    }

    .comment-textarea {
      margin-top: 16px;
    }
  }
  .reply .bottom .btn-box {
    margin-right: 20px;
  }
}
</style>
