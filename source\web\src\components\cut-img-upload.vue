<template>
  <div class="upload-img-wrap">
    <div class="upload-img-content"  v-show="imageUrl">
      <img
        width="200px"
        height="125px"
        style="object-fit: cover"
        :src="imageUrl"
        class="avatar"
      />
      <div class="delete-bg">
        <span @click="previewImg"><i class="el-icon-view"></i></span>
        <span v-show="cover_imgage_storage_type === 'zhihui'" @click="handleImgEdit"><i class="el-icon-edit"></i></span>
        <span @click="upAgain"><i class="el-icon-upload2"></i></span>
        <span @click="clearImg"><i class="el-icon-delete"></i></span>
      </div>
    </div>
    <el-upload
      v-show="!imageUrl"
      class="avatar-uploader"
      action=""
      ref="upload"
      :accept="accept"
      :show-file-list="false"
      :http-request="upload"
      :on-change="imgChange"
      :class="{ width270: width === 270 }"
      :before-upload="beforeAvatarUpload"
    >
      <img
        v-if="upAgainUrl"
        width="200px"
        height="125px"
        style="object-fit: cover"
        :src="upAgainUrl"
        class="avatar"
      />
      <div class="upload-icon">
        <i class="el-icon-upload-custom"></i>
        <p>上传图片</p>
      </div>
    </el-upload>
      <div slot="tip" class="el-upload__tip coverWrap">
        <slot name="text"></slot>
        <p>图片小于{{size}}M，支持jpg、png、jpeg、bmp等格式</p>
        <slot name="createImg"></slot>
      </div>
    <vue-cropper
      :picWh="[360, 240]"
      :fixedNumber="fixedNumber"
      :fixed="false"
      ref="cropperRef"
      @getFile="getFile"
      @upAgain="upAgain"
      :imgInfo="imgInfo"
    />
    <!-- 查看照片 -->
    <el-dialog :modal="false" :visible.sync="imageViewerShow">
      <img class="close" src="~@/assets/mooc-img/close-circle.png" @click="imageViewerShow = false" alt="">
      <img class="preview-image" :src="imageUrl" alt width="100%" />
    </el-dialog>
  </div>
</template>

<script>
import { VueCropper } from '@/components/index'
import env from 'config/env.conf.js'
export default {
  name: 'upload-image',
  components: {
    VueCropper
  },
  props: {
    dialogImageUrl: {
      type: String
    },
    autoImgUrl: {
      type: String
    },
    width: {
      type: Number,
      default: 200
    },
    height: {
      type: Number,
      default: 200
    },
    cover_imgage_storage_type: {
      type: String,
      default: ''
    },
    fixedNumber: {
      type: Array
    },
    accept: {
      type: String,
      default: '.jpg,.jpeg,.png'
    },
    directlyUpload: { // 智能章节
      type: Boolean,
      default: true
    },
    size: {
      type: String,
      default: '2'
    }
  },
  data() {
    return {
      imageUrl: '',
      imgInfo: {},
      upAgainUrl: '',
      imageViewerShow: false
    }
  },
  watch: {
    dialogImageUrl: {
      handler(nVal) {
        if (nVal) {
          this.handleImg(nVal)
        }
      },
      immediate: true
    },
    // 一键封面
    autoImgUrl(nVal) {
      if (nVal) {
        this.imageUrl = nVal
      }
    }
  },
  methods: {
    clearImg() {
      this.imageUrl = ''
      this.upAgainUrl = ''
      this.$emit('handleClearImg')
    },
    // 重新上传
    upAgain() {
      this.imageUrl = ''
      this.$refs.cropperRef.close()
      this.$nextTick(() => {
        this.$refs['upload'].$refs['upload-inner'].handleClick()
        this.imageUrl = this.upAgainUrl // 防止用户不上传
      })
    },
    handleImgEdit() {
      this.$emit('handleImgEdit')
    },
    getFile(file) {
      const isLt2M = file.file.size / 1024 / 1024 < this.size
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }
      this.upload(file.file)
    },
    // 图片拼接
    handleImg(data) {
      let url = ''
      if (data.indexOf('content-center') > -1 || data.indexOf('contentcenter') > -1 || data.indexOf('https://') > -1 || data.indexOf('http://') > -1) {
        url = data
      } else {
        const envName = env[process.env.NODE_ENV]
        url = `${envName.contentcenter}content-center/api/v1/content/imgage/${data}/preview`
      }
      this.$nextTick(() => {
        this.imageUrl = url
        this.upAgainUrl = this.imageUrl
      })
    },
    upload(file) {
      this.$sdc.loading('图片上传中')
      let that = this
      /* eslint-disable*/
      let authUrl = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_PORTAL_HOST_WOA : process.env.VUE_APP_PORTAL_HOST
      new contentCenter.uploadFile({
        file: file,
        type: 0, // 0表示上传图片，1视频 2音频 3文档
        appId: 'QLearningService',
        operateAuthUrl:`${authUrl}/training-portal-common/api/v1/portal/user/common/uploadOperateSignature`,
        isPublic: that.directlyUpload,
        onSuccess(res) {
          that.$sdc.loading.hide()
          that.handleImg(res[0].content_id)
          // that.$emit('handleSuccess', res[0].content_id, file)
          // that.handleImg(res[0].file_url)
          that.$emit('handleSuccess', res[0].file_url, file, res[0].content_id)
          that.$refs.cropperRef.close()
        },
        onError(err) {
          that.$sdc.loading.hide()
          that.$message.error(err)
        },
        // onProgress(info) {
        //   var percent = parseInt(info.percent * 10000) / 100
        //   const loading = that.$loading({
        //     lock: true,
        //     text: '图片上传中',
        //     spinner: 'el-icon-loading',
        //     background: 'rgba(0, 0, 0, 0.7)'
        //   })
        //   if (percent >= 100) {
        //     setTimeout(() => {
        //       loading.close();
        //     }, 2000);
        //   }  
        // }
      })
      /* eslint-disable*/
    },
    // 图片限制大小
    beforeAvatarUpload(file) {
      let _this = this
      const isSize = new Promise(function (resolve, reject) {
        let _URL = window.URL || window.webkitURL
        let img = new Image()
        img.onload = function () {
          _this.imgInfo = {
            width:img.width,
            height:img.height
          }
        }
        img.src = _URL.createObjectURL(file)
      }).then(
        () => {
          return file
        }
      )
      return isSize
    },
    imgChange(file) {
      const isJPG = file.raw.type
      if (!['image/jpeg', 'image/jpg', 'image/png', 'image/bmp'].includes(isJPG)) {
        this.$message.error('请选择正确的文件格式')
      } else {
        this.$nextTick(() => {
          this.$refs.cropperRef.open(file.raw || file)
        })
      }
    },
    previewImg() {
      this.imageViewerShow = true
    }
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/upload-images.less';
  .upload-img-wrap .upload-img-content .delete-bg span {
    height: 24px;
    padding: 0 11px;
    & + span {
      margin: 0;
      padding: 0 11px;
      border-left: 1px solid #fff
    }
  }
  .upload-img-wrap /deep/ .el-dialog__wrapper .el-dialog {
    min-width: 600px;
    max-width: 90%;
  }
  .upload-img-wrap /deep/ .el-dialog__wrapper .el-dialog .el-dialog__header {
    display: none;
  }
  .upload-img-wrap /deep/ .el-dialog__wrapper .el-dialog .el-dialog__body {
    background-color: transparent;
    padding: 0;
    position: relative;
    .close {
      width: 32px;
      height: 32px;
      cursor: pointer;
      position: absolute;
      top: -16px;
      right: -16px;
    }
    .preview-image {
      margin: 0 !important;
    }
  }
  
</style>
