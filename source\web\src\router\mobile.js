import {
  getRouter
} from 'sdc-vue'
import VueRouter from 'vue-router'
const originalPush = VueRouter.prototype.push
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

const routes = [{
  path: '/mobile',
  name: 'mobile',
  component: () => import('views/mobile/index.vue'),
  children: [
    {
      path: 'netcourse/play',
      name: 'play',
      component: () => import('views/mobile/videoDetail/index.vue'),
      meta: {
        title: '视频详情'
      }
    },
    {
      path: 'netcourse/grayPlay',
      name: 'grayPlay',
      component: () => import('views/mobile/videoDetailGray/index.vue'),
      meta: {
        title: '视频详情-新版'
      }
    },
    {
      path: 'face',
      name: 'face',
      component: () => import('views/mobile/face/index.vue'),
      meta: {
        title: '面授课'
      }
    },
    {
      path: 'activity/detail',
      name: 'activity',
      component: () => import('views/mobile/activity/index.vue'),
      meta: {
        title: '活动详情'
      }
    },
    {
      path: 'activity/meetingReview',
      name: 'meetingReview',
      component: () => import('views/mobile/activity/meetingReview.vue'),
      meta: {
        title: '活动腾讯会议回看'
      }
    },
    {
      path: 'word',
      name: 'word',
      component: () => import('views/mobile/wordDetail/index.vue'),
      meta: {
        title: '文档详情'
      }
    },
    {
      path: 'mooc/taskDetail',
      name: 'taskDetailMob',
      component: () => import('views/mobile/taskDetail/index.vue'),
      meta: {
        title: '任务详情'
      }
    },
    {
      path: 'mooc/taskAbnormal',
      name: 'taskAbnormal',
      component: () => import('views/mobile/taskDetail/taskAbnormal.vue'),
      meta: {
        title: '出错了'
      }
    },
    {
      path: 'material/play',
      name: 'materialPlay',
      component: () => import('views/mobile/materialDetail/index.vue'),
      meta: {
        title: '课程素材'
      }
    }, {
      path: 'pointLottery/lottery',
      name: 'lottery',
      component: () => import('views/mobile/pointLottery/lottery.vue'),
      meta: {
        title: '积分抽奖'
      }
    },
    // 暂不支持访问
    {
      path: 'error',
      name: 'mobileError',
      component: () => import('views/mobile/error/error.vue'),
      meta: {
        title: '出错了'
      }
    },
    {
      path: 'absent',
      name: 'absent',
      component: () => import('views/mobile/error/absent.vue'),
      meta: {
        title: '暂不支持访问'
      }
    },
    {
      path: 'pointLottery/lottery',
      name: 'lottery',
      component: () => import('views/mobile/pointLottery/lottery.vue'),
      meta: {
        title: '积分抽奖'
      }
    },
    {
      path: 'outsourcedCourse/video/play',
      name: 'video',
      component: () => import('views/mobile/outsourcedCourse/videoDetail/index.vue'),
      meta: {
        title: '极客视频详情'
      }
    },
    {
      path: 'outsourcedCourse/graphic/play',
      name: 'graphic',
      component: () => import('views/mobile/outsourcedCourse/graphic/index.vue'),
      meta: {
        title: '极客文章详情'
      }
    },
    {
      path: 'outsourcedCourse/notSupported',
      name: 'video',
      component: () => import('views/mobile/outsourcedCourse/notSupported/index.vue'),
      meta: {
        title: '极客视频详情'
      }
    },
    {
      path: 'activePage/home',
      name: 'activePage',
      component: () => import('views/mobile/outsourcedCourse/activePage/index.vue'),
      meta: {
        title: '活动首页'
      }
    },
    {
      path: 'activePage/home/<USER>',
      name: 'activePageSanjieke',
      component: () => import('views/mobile/outsourcedCourse/activePage/sanjiekeMobile.vue'),
      meta: {
        title: '活动首页'
      }
    },
    {
      path: 'activePage/xueba',
      name: 'xuebaCardMobile',
      component: () => import('views/mobile/outsourcedCourse/activePage/xuebaCardMobile.vue'),
      meta: {
        title: '活动首页'
      }
    },
    {
      path: 'activePage/xueba/harvard',
      name: 'xuebaAccount',
      component: () => import('views/mobile/outsourcedCourse/activePage/accountXuebaMobile.vue'),
      meta: {
        title: '哈佛精品文库好文免费看'
      }
    },    
    {
      path: 'outsourcedCourse/iframe/play',
      name: 'iframe',
      component: () => import('views/mobile/outsourcedCourse/iframe/index.vue'),
      meta: {
        title: '情景测评页详情'
      }
    },
    {
      path: 'outsourcedCourse/iframeNoLearnRecords/play',
      name: 'iframe',
      component: () => import('views/mobile/outsourcedCourse/iframeNoLearnRecords/index.vue'),
      meta: {
        title: ''
      }
    },
    {
      path: 'taskFinish',
      name: 'taskFinish',
      component: () => import('views/mobile/taskDetail/taskFinish.vue'),
      meta: {
        title: '完成课程页'
      }
    }
  ]
}]

export default getRouter(routes, {
  base: ['test', 'production'].includes(process.env.NODE_ENV) ? '/training' : '',
  mode: 'history'
})
