<template>
    <header class='labelInfo flex align-center'>
        <h4 class="labelName">{{ labelId ? ('「' + labelName + '」') : ( !subsType ? "订阅" : (subsType === 1 ? '订阅标签': '订阅专区' ))}}相关内容</h4>
        <template v-if="labelDetail.subscribe_type == 2 && labelId">
            <el-button class="btn checkBtn" type="primary" plain size="mini" @click="specailJump(labelDetail.label_jump_url, labelDetail.label_id)">查看专区首页</el-button>
            <el-button class="btn cancelBtn" type="danger" plain size="mini" @click.stop="cancelSubs">取消订阅</el-button>
        </template>
        <template v-else-if="labelDetail.subscribe_type == 1 && labelId">
            <article class="labelInfoView" v-if="labelId && !isLabelGatherPage">
                <p class="categoryFullName">标签分类：<span style=" color: #00000099;">{{ labelDetail.category_full_name }}</span></p>
                <div class="labelCount">
                    <span>{{labelDetail.subscribe_count}}</span>订阅
                    <span class="and">·</span>
                    <span>{{labelDetail.content_count}}</span>内容
                </div>
                <el-button class="btn cancelBtn" style="margin-left: 28px;" type="danger" plain size="mini" @click.stop="cancelSubs">取消订阅</el-button>
            </article>
        </template>
        <active-jump class="mgl-32" :dtArg="dtArg"></active-jump>
    </header>
</template>
<script>
import specailJump from '../../specailJump'
import ActiveJump from '@/views/components/activeJump.vue'
export default {
  name: 'detailTop',
  props: ['labelId', 'labelName', 'isLabelGatherPage', 'labelDetail', 'subsType'],
  components: {
    ActiveJump
  },
  data() {
    return {}
  },
  mixins: [specailJump],
  computed: {
    dtArg() {
      return {
        page: this.labelName || document.title,
        page_type: '标签订阅内容页',
        container: this.labelId ? '标签相关内容' : '我订阅的内容',
        content_name: '订阅抽奖入口',
        course_id: this.labelId || Math.random().toFixed(3) * 1000
      }
    }
  },
  methods: {
    cancelSubs() {
      this.$parent.cancelSubs({ 'label_name': this.labelName, ...this.labelDetail }, null)
    }
  }
}
</script>
<style lang="less" scoped>
.labelInfo{
    padding: 16px 24px 14px;
    line-height: 24px;
    border-bottom: 1px solid var(--gray-gray-3, #E7E7E7);
    h4.labelName{
        font-weight: 600;
        font-size: 16px;
    }
    .labelInfoView{
        display: flex;
        align-items: center;
        margin-left: 20px;
        p.categoryFullName{
            font-size:12px;
            line-height:20px;
            color: #00000066;
        }
        div.labelCount{
            height: 20px;
            display: flex;
            align-items: center;
            color: #00000099;
            font-size: 12px;
            line-height:20px;
            margin-left:24px;
            font-weight:400;
            span{
                color: #0052d9;
                font-weight:500;
                margin-right: 2px;
            }
            span.and{
                display: inline-block;
                color: #00000099;
                font-size:22px;
                width: 6px;
                margin: 0 8px;
            }
        }
    }
    .btn {
        min-width:64px;
        line-height: 20px;
        padding: 2px 8px;
    }
    .cancelBtn {
        background: none;
        color: #D54941;
        border-color: #D54941;
        margin-left: 16px;
    }
    .checkBtn {
        background: #0052D9;
        color: #fff;
        border-color: #0052D9;
        margin-left: 32px;
    }
    .mgl-32 {
        margin-left: 32px;
    }
}
</style>
