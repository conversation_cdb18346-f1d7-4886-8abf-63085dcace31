<template>
  <div class="orange-tips">
      <img src="@/assets/mooc-img/warning-icon.png" alt="" style="margin-right: 4px;width: 20px;height: 20px;">
      <span class="orange-tips-title">{{ title }}</span>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: ''
    }
  }
}
</script>

<style scoped lang="less">
.orange-tips {
  display: flex;
  align-items: center;
  height: 40px;
  background: #FCF6ED;
  border-radius: 3px;
  padding: 0 8px;
  width: fit-content;
  .orange-tips-title {
    color: #ff7548;
    font-family: "PingFang SC";
    font-size: 14px;
    line-height: 20px;
  }
}
</style>
