<template>
  <div class="tutor-manage-container">
    <div class="top-title">
      <span>导师管理</span>
    </div>
    <div class="main-content">
      <div class="form-box">
        <el-form ref="form" :model="form" inline>
          <el-form-item label="导师名称：">
            <el-input style='width: 215px;' v-model="form.tutor_name" placeholder="请输入导师名称" clearable>
              <i slot="suffix" class="el-input__icon el-icon-search"></i>
            </el-input>
          </el-form-item>
          <el-form-item label="所属组织：">
            <sdc-unit-selector placeholder="请选择组织" ref="customSelectRef" v-model="form.org_id" selectClass="custom-select-unit" showFullTag/>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select v-model="form.tutor_status" placeholder="请选择状态" style="width:215px" clearable>
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="入库类型：">
            <el-select v-model="form.tutor_join_type" placeholder="请选择入库类型" style="width:215px" clearable>
              <el-option
                v-for="item in joinTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="入库时间：">
            <el-date-picker
              style="width:260px"
              size="small"
              v-model="createTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00','23:59:59']"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item class="right-btn">
            <!-- <el-button style="width:60px" @click="handleExport" size='small'>导出</el-button> -->
            <el-button style="width:80px" @click="handleReset" class="custom-plain" size='small' icon="el-icon-refresh">重置</el-button>
            <el-button style="width:60px" @click="handleSearch()" type="primary" size='small'>搜索</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="add-btn-box">
        <el-button type="primary" style="width:80px" size='small' @click="handleAdd('1')">新增导师</el-button>
        <el-button v-if="userRole.super_admin" class="mid-btn custom-plain" style="width:102px" size='small' @click="handleAdd('2')">免认证录入</el-button>
        <CustomTips
        title="仅显示权限范围内组织下的导师信息" 
        IconName="el-icon-warning-outline" 
        backgroundColor="#fdf6ec" 
        color="#FF7548"
        lineHeight="40px"
        class="c-tips"
        >
        </CustomTips>
      </div>
      <el-table
      :data="tableData.records"
      style="width: 100%"
      header-row-class-name="tutor-table-header"
      row-class-name="tutor-table-row"
      class='tutor-table'
      > 
        <el-table-column prop="tutor_name" label="导师名称" width="120" fixed="left"></el-table-column>
        <el-table-column prop="plevel" label="专业职级" show-overflow-tooltip>
          <template slot-scope="{row}">
            <span>{{ row.plevel || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="mlevel" label="管理职级">
          <template slot-scope="{row}">
            <span>{{ row.mlevel || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="seniority_year" label="司龄"></el-table-column>
        <el-table-column prop="dept_full_name" label="所属组织">
          <template slot-scope="{row}">
            <span class="cellContentTwo" v-if="isHide" @mouseover="isShowTooltip(row.dept_full_name, $event)">{{ row.dept_full_name }}</span>
            <el-tooltip v-else :content="row.dept_full_name" placement="top">
              <span class="cellContentTwo" @mouseover="isShowTooltip(row.dept_full_name, $event)">{{ row.dept_full_name }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="direct_supervisor" label="直接上级" width="120">
          <template slot-scope="{row}">
            <span>{{ row.direct_supervisor || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="tutoring_nums" width="150">
          <template #header>
            <span>累计辅导次数</span> 
            <el-tooltip effect="dark" placement="top" popper-class="tooltip-guidance">
              <div slot="content">仅统计导师系统上线<br/>后的新员工辅导记录</div>
              <i class="icon-tips"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="tutoring_valid_nums" width="160">
          <template #header>
            <span>累计有效辅导次数</span> 
            <el-tooltip effect="dark" placement="top" popper-class="tooltip-guidance">
              <div slot="content">仅统计导师系统上线<br/>后的新员工有效辅导记录</div>
              <i class="icon-tips"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="qualifications_status" label="门槛资格" width="100">
          <template slot-scope="{row}">
            <span v-if="[null, -1].includes(row.qualifications_status)">-</span>
            <span v-else-if="row.qualifications_status === 1" class="satisfy common-color">满足</span>
            <div v-else class="ds-flex">
              <span class="no-satisfy common-color">不满足</span>
              <el-popover
                placement="top"
                popper-class="popover-show-box rule-popover"
                trigger="hover"
              >
                <div class="title">门槛资格校验</div>
                <div 
                v-for="(item, index) in row.threList" 
                :key="index" 
                :class="[item.meet_requirements ? 'r-bg' : 'err-bg', 'common-bg']"
                >
                  <i :class="[item.meet_requirements ? 'el-icon-circle-check' : 'el-icon-circle-close']"></i>
                  <span class="q-content">{{ item.requirements_content }}</span>
                </div>
                <i slot="reference" class="icon-tips td-tips"></i>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="certification_status" label="认证要求" width="100">
          <template slot-scope="{row}">
            <span v-if="[null, -1].includes(row.certification_status)">-</span>
            <span v-else-if="row.certification_status === 1" class="satisfy common-color">满足</span>
            <div v-else class="ds-flex">
              <span class="no-satisfy common-color">不满足</span>
              <el-popover
                placement="top"
                popper-class="popover-show-box"
                trigger="hover"
              >
                <div class="title">认证要求校验</div>
                <!-- 认证中 -->
                <div class="tips" v-if="row.tutor_status === 0">（重新认证时需要再次通过考试）</div>
                <div 
                v-for="(item, index) in row.certList" 
                :key="index" 
                :class="[item.meet_requirements ? 'r-bg' : 'err-bg', 'common-bg', 'require-bg']"
                @click="toRequireDetail(item)"
                >
                  <i :class="[item.meet_requirements ? 'el-icon-circle-check' : 'el-icon-circle-close']"></i>
                  <span class="q-content">{{ item.requirements_content }}</span>
                </div>
                <i slot="reference" class="icon-tips td-tips"></i>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <!-- 导师状态：-1：禁用；0-认证中；1-合格；2-免认证；3-风险 -->
        <el-table-column prop="tutor_status" label="状态" width="100">
          <template slot-scope="{row}">
            <span 
            :class="[
              'common-color',
              'ds-flex',
              {'disabled-color': [0, -1].includes(row.tutor_status)}, 
              {'satisfy': row.tutor_status === 1}, 
              {'no-id': row.tutor_status === 2}, 
              {'no-satisfy': row.tutor_status === 3}
            ]">
              {{ status_info[row.tutor_status] }}
              <!-- last_tutor_status--禁用，认证中  -->
              <!-- tutor_status 合格，风险，认证中，禁用 -->
              <i @click="handleDetail(row, '2')" v-if="[-1, 0].includes(row.tutor_status) || ([-1, 0].includes(row.last_tutor_status) && [1, 3].includes(row.tutor_status))" class="icon-tips td-tips"></i>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="tutor_status_update_time" label="状态更新时间" width="160"></el-table-column>
        <el-table-column prop="certificate_issuance_status" label="聘书" width="120">
          <template slot-scope="{row}">
            <span>{{ row.certificate_issuance_status === 1 ? '已发放' : '未发放' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="certificate_issuance_time" label="发放时间" width="160">
          <template slot-scope="{row}">
            <span>{{ row.certificate_issuance_time || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="tutor_join_type" label="入库类型" width="120">
          <!-- 导师加入类型：1-管理员添加；2-系统录入；3-自动同步； -->
          <template slot-scope="{row}">
            <span>{{ join_type[row.tutor_join_type]}}</span>
            <i @click="handleDetail(row, '1')" v-if="[1].includes(row.tutor_join_type) && row.entry_reason" class="icon-tips td-tips"></i>
          </template>
        </el-table-column>
        <el-table-column prop="tutor_join_time" label="入库时间" width="160"></el-table-column>
        <el-table-column prop="creator_name" label="添加人" width="120"></el-table-column>
        <el-table-column label="操作" width="100" fixed="right">
          <template slot-scope="{row}">
            <!-- -1：禁用；0-认证中；1-合格；2-免认证；3-风险 -->
            <span @click="handleResetId(row, '1')" class="no-id operate-btn" v-if="row.tutor_status === -1">重新认证</span>
            <!-- 非禁用 -->
            <span @click="handleResetId(row, '2')" class="disabled-color operate-btn" v-else>禁用</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
      v-if="tableData.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="current"
      :page-sizes="[5, 10, 20, 30, 50, 100]"       
      :page-size="size"
      layout="total,  prev, pager, next, sizes, jumper"
      :total="tableData.total"
      >
      </el-pagination>
    </div>
    <!-- 新增导师 -->
    <addTutor v-if="addTutorShow" :visible.sync="addTutorShow" ref="addTutorRef" @onSearch="handleSearch"></addTutor>
    <!-- 录入详情 -->
    <commonDialog v-if="commonDialogShow" :visible.sync="commonDialogShow" :detailInfo="commonDialogInfo" ref="commonDialogRef"></commonDialog>
    <!-- 禁用-重新认证 -->
    <operateDialog v-if="operateDialogShow" :visible.sync="operateDialogShow" :detailInfo="operateDialogInfo" ref="operateDialogRef" @onSearch="handleSearch"></operateDialog>
  </div>
</template>
<script>
import CustomTips from '@/components/tips.vue'
import pager from '@/mixins/pager'
import addTutor from './child/addTutor.vue'
import { getTuTorList, tutorExportAPI } from '../../api/tutor.api.conf'
import commonDialog from './child/commonDialog.vue'
import operateDialog from './child/operateDialog.vue'
import { mapState } from 'vuex'
const status_info = {
  '-1': '禁用',
  '0': '认证中',
  '1': '合格',
  '2': '免认证',
  '3': '风险'
}
const join_type = {
  '1': '管理员添加',
  '2': '系统录入',
  '3': '自动同步'
}
export default {
  components: {
    CustomTips,
    addTutor,
    commonDialog,
    operateDialog
  },
  mixins: [pager],
  data() {
    return {
      isHide: true,
      status_info,
      join_type,
      form: {
        tutor_name: '',
        org_id: '',
        tutor_status: '',
        tutor_join_type: ''
      },
      statusOptions: [ // -1：禁用；0-认证中；1-合格；2-免认证；3-风险
        { value: -1, label: '禁用' },
        { value: 0, label: '认证中' },
        { value: 1, label: '合格' },
        { value: 2, label: '免认证' },
        { value: 3, label: '风险' }
      ],
      joinTypeOptions: [
        { value: 1, label: '管理员添加' },
        { value: 2, label: '系统录入' },
        { value: 3, label: '自动同步' }
      ],
      createTime: [],
      tableData: {
        records: [],
        total: 0
      },
      addTutorShow: false,
      commonDialogShow: false,
      commonDialogInfo: null,
      operateDialogShow: false,
      operateDialogInfo: null
    }
  },
  computed: {
    ...mapState({
      userRole: state => state.userRole
    })
  },
  mounted() {
    this.handleSearch()
  },
  methods: {
    onSearch(page_no = 1) {
      const join_start_time = this.createTime?.length ? this.createTime[0] : ''
      const join_end_time = this.createTime?.length ? this.createTime[1] : ''
      const { tutor_name, org_id, tutor_status, tutor_join_type } = this.form
      const params = {
        tutor_name,
        org_id,
        tutor_status,
        tutor_join_type,
        join_start_time,
        join_end_time,
        page_no,
        page_size: this.size
      }
      getTuTorList(params).then((res) => {
        this.tableData.records = res.records.map((e) => {
          const data = e.risk_check_content ? JSON.parse(e.risk_check_content) : ''
          return {
            ...e,
            seniority_year: e.seniority_year ? Number(e.seniority_year).toFixed(1) : 0,
            certList: data.cert_risk_detail || [], // 认证要求
            threList: data.threshold_risk_detail || [] // 门槛资格
          }
        })
        this.tableData.total = res.total
      })
    },
    // 导出
    handleExport() {
      const join_start_time = this.createTime?.length ? this.createTime[0] : ''
      const join_end_time = this.createTime?.length ? this.createTime[1] : ''
      const { tutor_name, org_id, tutor_status, tutor_join_type } = this.form
      const params = {
        tutor_name,
        org_id,
        tutor_status,
        tutor_join_type,
        join_start_time,
        join_end_time,
        page_no: this.current,
        page_size: this.size
      }
      tutorExportAPI(params).then((res) => {
        this.$message.success('导出成功')
      })
    },
    toRequireDetail(e) {
      if (!e.course_url) return
      window.open(e.course_url, '_blank')
    },
    // 禁用-重新认证
    handleResetId(row, type) {
      this.operateDialogShow = true
      this.$nextTick(() => {
        this.$refs.operateDialogRef.initData(row)
      })
      if (type === '2') {
        this.operateDialogInfo = {
          title: `禁用导师${row.tutor_name}`,
          label: '禁用原因：',
          tips: '禁用后，此员工将无法担任新员工导师，之后如需担任导师，需重新完成认证，确定禁用吗？',
          confirmBtn: '确定禁用',
          btnType: 'danger'
        }
        return
      }
      this.operateDialogInfo = {
        title: `重新认证导师${row.tutor_name}`,
        label: '重新认证原因：',
        tips: '发起重新认证后，如果公司+BG的认证要求中包含考试内容，则此员工需要重新通过所有的考试，才可以担任新员工导师，确定重新认证吗？',
        confirmBtn: '确定',
        btnType: 'primary'
      }
    },
    // 新增导师
    handleAdd(val) {
      this.addTutorShow = true
      this.$nextTick(() => {
        this.$refs.addTutorRef.initData(val)
      })
    },
    // 录入详情，重新认证
    handleDetail(row, type) {
      this.commonDialogShow = true
      this.commonDialogInfo = {}
      if (type === '1') {
        this.commonDialogInfo = {
          title: '录入详情',
          addLabel: '添加人：',
          timeLalbe: '操作时间：',
          resonLabel: '入库原因：',
          window_type: 1,
          tutor_staff_id: row.tutor_staff_id
        }
      } else {
        if (row.tutor_status === -1) { // 禁用
          this.commonDialogInfo = {
            title: '禁用详情',
            addLabel: '操作人：',
            timeLalbe: '禁用时间：',
            resonLabel: '禁用原因：',
            window_type: -1,
            tutor_staff_id: row.tutor_staff_id
          }
        } else {
          // 认证中才有操作记录
          this.commonDialogInfo = {
            title: '重新认证操作记录',
            addLabel: '操作人：',
            timeLalbe: '重新认证时间：',
            resonLabel: '重新认证原因：',
            window_type: 0,
            tutor_staff_id: row.tutor_staff_id
          }
        }
      }
    },
    handleSearch() {
      this.current = 1
      this.size = 10
      this.onSearch(1)
    },
    handleReset() {
      this.createTime = []
      this.$refs.customSelectRef.clearSelected()
      for (let pre in this.form) {
        this.form[pre] = ''
      }
      this.handleSearch()
    },
    isShowTooltip(val, e) {
      const creatDom = document.createElement('span')
      creatDom.innerText = val
      // 强制一行显示，并且字体大小要与表格单元格字体大小一致，方便计算宽度
      creatDom.style.whiteSpace = 'pre'
      creatDom.style.fontSize = '14px'
      document.body.appendChild(creatDom)
      const width = creatDom.offsetWidth
      creatDom.remove()
      // 判断文本长度是否大于表格单元格宽度。乘于2，表示两行
      if (width >= e.target.offsetWidth * 2) {
        this.isHide = false
      } else {
        this.isHide = true
      }
    }
  }
}
</script>
<style lang="less">
.tooltip-guidance {
  line-height: 20px;
}
.popover-show-box {
  width: 224px;
  padding: 8px 10px 0px;
  border-radius: 6px;
  box-shadow: 2px 12px 32px 0 #0000001a;
  background-color: #fff;
  .title {
    color: #000000e6;
    font-size: 12px;
    line-height: 20px;
    margin-bottom: 8px;
  }
  .tips {
    color: #00000099;
    margin-bottom: 8px;
    line-height: 20px;
    font-size: 12px;
  }
  .require-bg {
    text-decoration: underline;
    cursor: pointer;
  }
  .common-bg {
    margin-bottom: 8px;
    padding: 2px 8px;
    border-radius: 3px;
    i {
      font-size: 14px;
      margin-right: 4px;
    }
  }
  .r-bg {
    background-color: #E3F9E9;
    color: #2BA471;
  }
  .err-bg {
    background-color: #FFF0ED;
    color: #D54941;
  }
}
.rule-popover {
  width: 186px;
}
</style>
<style lang="less" scoped>
.tutor-manage-container {
  background-color: #fff;
  border-radius: 4px;
  .icon-tips {
    display: inline-block;
    width: 12px;
    height: 12px;
    background: url('~@/assets/tutor/tips.png') no-repeat center / cover;
    margin-left: 10px;
    cursor: pointer;
  }
  .top-title {
    color: #000000e6;
    font-size: 16px;
    font-weight: bold;
    line-height: 24px;
    border-bottom: 1px solid #DCDCDC;
    padding: 16px 20px;
  }
  :deep(.custom-select-unit) {
    width: 240px;
  }
  :deep(.el-table__fixed-right) {
    height: 100% !important;
  }
  .main-content {
    padding: 20px;
    .form-box {
      padding: 16px 20px 0px;
      border-radius: 4px;
      background: #FAFAFA;
      .el-form-item {
        margin-bottom: 16px;
        margin-right: 36px;
        :deep(.el-form-item__label) {
          color: #00000099;
        }
      }
      .right-btn {
        padding-left: 35px;
        .el-button+.el-button {
          margin-left: 16px;
        }
      }
    }
    .add-btn-box {
      display: flex;
      align-items: center;
      margin-top: 16px;
      margin-bottom: 16px;
      .mid-btn {
        margin-left: 16px;
      }
      .c-tips {
        width: 276px;
        border-radius: 3px;
        padding: 0 8px;
        margin-left: 24px;
      }
    }
    .tutor-table {
      :deep(table.el-table__header) {
        width: max-content !important;
      }
      :deep(table.el-table__body) {
        width: max-content !important;
      }
    }
    :deep(.tutor-table) {
      .tutor-table-header {
        height: 48px;
  
        th {
          background: #F5F5F5;
          font-weight: bold;
          color: #00000099;
          border-bottom: none;
        }
      }
  
      .tutor-table-row {
        height: 50px;
  
        td {
          height: 60px;
          color: #000000e6;
          border-color: #eee;
        }
        .cellContentTwo {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
      }
      .td-tips {
        margin-left: 8px;
      }
      .ds-flex {
        display: flex;
        align-items: center;
      }
      .common-color {
        font-weight: bold;
        line-height: 14px;
        font-size: 14px;
      }
      .satisfy {
        color: #2ba471;
      }
      .no-satisfy {
        color: #e37318;
      }
      .disabled-color {
        color: #d54941;
      }
      .no-id {
        color: #0052D9;
      }
      .operate-btn {
        cursor: pointer;
        line-height: 22px;
      }
    }
  }
}
</style>
