<template>
  <!-- 送出劝学卡弹窗 -->
  <van-popup class="card-detail-pop" @open="showToast" v-model="showExchanged" position="bottom" round closeable safe-area-inset-bottom>
    <div class="title">送出记录</div>
    <div>
      <div class="table">
        <el-table :data="tableData" :header-cell-style="headerCellStyle" style="width: 100%">
          <el-table-column label="赠送人">
            <template slot-scope="scope">
              <span class="table-ceel_content">
                {{scope.row.staff_name}}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="送出时间">
            <template slot-scope="scope">
              <span class="table-ceel_content"> {{scope.row.created_at}}</span>
            </template>
          </el-table-column>
          <el-table-column label="赠送状态">
            <template slot-scope="scope"> <span class="table-ceel_content" :style="`color:${reward_amt(scope.row.reward_amt).color}`">{{reward_amt(scope.row.reward_amt).name}}</span></template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="foot-page">
      <Pagination v-model="currentPage" :page-count="pageCount" mode="simple" @change="pageChange">
        <template #prev-text>
          <span class="prev-btn">
            <van-icon name="arrow-left" style="margin-right: 4px" /> 上一页
          </span>
        </template>
        <template #next-text>
          <span class="next-btn">
            下一页<van-icon name="arrow" style="margin-left: 4px" />
          </span>
        </template>
        <template #pageDesc>
          <span class="page-desc">
            <span class="current">{{ currentPage }}</span> / {{ pageCount }}
          </span>
        </template>
      </Pagination>
    </div>
  </van-popup>
</template>

<script>
import { getPresentRecord } from '@/config/mooc.api.conf.js'
import { Pagination } from 'vant'
import { mapState } from 'vuex'
export default {
  props: ['isShow', 'xueBaCardConfig'],
  components: { Pagination },

  data() {
    return {
      currentPage: 1,
      pageSize: 5,
      pageCount: 0,
      selectGiveList: [],
      optionsAct: [{ label: '培养项目', value: 11 }],
      tableData: []
    }
  },
  created() {},
  computed: {
    ...mapState(['userInfo']),
    showExchanged: {
      set(val) {
        this.$emit('update:isShow', val)
      },
      get() {
        return this.isShow
      }
    },
    headerCellStyle() {
      return {
        background: '#F5F5F5',
        color: '#00000099',
        fontSize: '12px',
        fontWeight: '500'
      }
    },
    reward_amt() {
      return (amt) => {
        return amt
          ? { name: '已送出有奖励', color: '#00A870' }
          : { name: '已送出无奖励', color: '#0052D9' }
      }
    },
    // 处理列表的"说明"字段
    resolveDescData() {
      return (item) => {
        if (item.verb_id === 'consume' || item.verb_id === 'manage_deduct') {
          return `使用时间：${this.resolveTime(item.deduct_time)}`
        } else if (item.verb_id === 'deduct_expire') {
          return `失效时间：${this.resolveTime(item.expire_time)}`
        } else {
          return `领取7天后，卡券失效，请及时使用`
        }
      }
    }
  },
  methods: {
    // 赠送获得的奖励卡券报酬
    // presentReward() {
    //   queryGeekRecord({
    //     staff_id: this.userInfo.staff_id,
    //     acct_type_code: this.xueBaCardConfig.acct_type_code,
    //     current: 1,
    //     size: 10,
    //     receive_verb_id: 'present_reward',
    //     activity_id: this.$route.query.activityId
    //   }).then((res) => {
    //     this.pageCount = res.pages
    //     this.tableData = res.records || []
    //     console.log(res, '赠送获得的奖励卡券报酬')
    //   })
    // },
    // 赠送的
    async getPresentRecord() {
      let params = {
        activityId: this.$route.query.activityId,
        current: this.currentPage,
        size: 5
      }
      const res = await getPresentRecord(params)
      console.log(res, '赠送')
      //   获得奖励的有多少张
      this.pageCount = res.pages
      this.tableData = res.records || []
    },
    handlerOpenUrlCurse(val) {
      window.open(val.course_url, '_blank')
    },
    // 处理时间数据
    resolveTime(time) {
      console.log(time, 'resolveTimeresolveTime')
      if (!time) return ''
      let srt = time.split(':').slice(0, -1).join(':')
      return srt.replace(/-/g, '/')
    },
    showToast() {
      this.getPresentRecord()
    },
    // 券领取详情分页改变
    pageChange(page) {
      this.currentPage = page
      this.getPresentRecord()
    }
  }
}
</script>
<style>
</style>
<style lang="less" scoped>
.url_link {
  color: #0052d9 !important;
  cursor: pointer;
}

/deep/.el-table th.gutter {
  background-color: #f5f5f5;
}

/* 定义checkbox的选中颜色 */
/deep/.el-checkbox__input.is-checked .el-checkbox__inner,
/deep/.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #0052d9;
  border-color: #0052d9;
}

/* 定义checkbox未选中时的边框颜色 */
// /deep/.el-checkbox__input .el-checkbox__inner {
//   border-color: #0052D9;
// }

/* 定义checkbox的对勾颜色 */
.el-checkbox__input.is-checked .el-checkbox__inner::after {
  color: #fff;
}
.b_0052D9 {
  color: #0052d9;
}
.card-detail-pop {
  //   max-height: 396px;
  padding: 16px 0;
  .title {
    color: #000000e6;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    line-height: 26px;
  }
  .table {
    margin-top: 16px;
    // overflow: auto;
    .table-ceel_content {
      color: #000000e6;
      font-family: 'PingFang SC';
      font-size: 10px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
    }
    /* 去掉hover效果 */
    /deep/.el-table .el-table__body tr.el-table__row:hover {
      background-color: #fff;
    }
    /deep/.el-table--enable-row-hover .el-table__body tr:hover > td {
      background-color: #fff;
    }
    /deep/.el-checkbox__inner {
      width: 16px;
      height: 16px;
      border-radius: 3px;
    }
    .table-title {
      width: 100%;
      //   display: flex;
      height: 38px;
      line-height: 38px;
      color: #00000099;
      font-size: 12px;
      font-weight: 500;
      background-color: #f5f5f5;
      & > div {
        display: inline-block;
        padding-left: 16px;
      }
    }
    .item-content {
      max-height: 300px;
      overflow: auto;
    }
    .item {
      display: flex;
      color: #000000e6;
      height: 60px;
      line-height: 60px;
      box-shadow: 0 -1px 0 0 #eee inset;
      & > div {
        font-size: 10px;
        padding-left: 14px;
        &:nth-child(n + 3) {
          padding-left: 16px;
        }
      }
    }
    .no {
      display: flex;
      align-items: center;
      width: 56px;
    }
    .time {
      width: 99px;
    }
    .status {
      width: 192px;
    }
    .status-text {
      line-height: 16px;
      text-align: center;
      border-radius: 4px;
      padding: 1px 8px;
      font-size: 10px;
    }
    .status-no-effict {
      color: #ed7b2f;
      border: 1px solid var(---Warning5-Normal, #ed7b2f);
      background: var(---Warning1-Light, #fef3e6);
    }
    .status-waite-use {
      color: #0052d9;
      border: 1px solid var(---Brand8-Normal, #0052d9);
      background: var(---Brand1-Light, #ecf2fe);
    }
    .status-oready-used {
      color: #00a870;
      border: 1px solid var(---Success5-Normal, #00a870);
      background: var(---Success1-Light, #e8f8f2);
    }
    .desc {
      padding: 0 16px;
      width: 116px;
      text-align: left !important;
      // width: 235px;
    }
    // .course-link {
    //   white-space: nowrap;
    //   cursor: pointer;
    //   color: #0052d9;
    //   padding-right: 16px;
    // }
  }
  .foot-page {
    margin-top: 20px;
    padding: 0 16px;
    .prev-btn {
      color: #00000099;
      font-size: 12px;
      line-height: 20px;
    }
    .next-btn {
      color: #0052d9;
      font-size: 12px;
      line-height: 20px;
    }
    .page-desc {
      color: #000000e6;
      font-size: 12px;
      line-height: 20px;
      padding: 6px 16px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      .current {
        color: #0052d9;
      }
    }
    /deep/.van-pagination__prev {
      height: 32px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      background: var(---White, #fff);
    }
    /deep/.van-pagination__page-desc {
      height: 32px;
    }
    /deep/.van-pagination__next {
      height: 32px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      background: #f3f7ff;
    }
  }
  /deep/.van-popup__close-icon--top-right {
    color: #000000;
    top: 23px;
    right: 22px;
  }
  /deep/.van-popup__close-icon {
    font-size: 12px;
    font-weight: 700;
  }
}
.foot-page {
  margin-top: 20px;
  padding: 0 16px;
  .prev-btn {
    color: #00000099;
    font-size: 12px;
    line-height: 20px;
  }
  .next-btn {
    color: #0052d9;
    font-size: 12px;
    line-height: 20px;
  }
  .page-desc {
    color: #000000e6;
    font-size: 12px;
    line-height: 20px;
    padding: 6px 16px;
    border-radius: 6px;
    border: 0.5px solid var(---Gray3-, #e7e7e7);
    .current {
      color: #0052d9;
    }
  }
  /deep/.van-pagination__prev {
    height: 32px;
    border-radius: 6px;
    border: 0.5px solid var(---Gray3-, #e7e7e7);
    background: var(---White, #fff);
  }
  /deep/.van-pagination__page-desc {
    height: 32px;
  }
  /deep/.van-pagination__next {
    height: 32px;
    border-radius: 6px;
    border: 0.5px solid var(---Gray3-, #e7e7e7);
    background: #f3f7ff;
  }
}
</style>
