<template>
  <div class='my-work'>
    <div class="work-sbmit-date">
      <div class="flex-d">
        <p style="height:28px;line-height: 28px;">
          <span class="color">{{ $langue('Mooc_TaskDetail_HomeWork_HomeworkSubmitTime', { defaultText: '作业提交时间' }) }}：</span>
          <span class="value">{{ !workInfo.start_time && !workInfo.end_time ? '不限制' : `${workInfo.start_time || '不限制'} 至 ${workInfo.end_time || '不限制'}` }}</span>
        </p>
        <!-- 合格，不合格，待批阅 -->
        <el-tooltip 
        v-if="[1, 3, 4].includes(workInfo.status) && isInWorkSubmitTime"
        :disabled="![3, 4].includes(workInfo.status)"
        effect='dark' 
        placement='top' 
        :content="$langue('Api_Mooc_Homework_HaveReviewScore', { defaultText: '作业已有批阅分数，如需修改作业内容，请先联系项目管理员退回' })">
        <span><el-button v-if="!isPreview" :disabled="[3, 4].includes(workInfo.status)" size="mini" @click="handleDeitWork(1)">{{ $langue('Mooc_TaskDetail_HomeWork_Revoke', { defaultText: '撤回并修改' }) }}</el-button></span>
        </el-tooltip>
        <el-button v-if="isBack && !isEdit && !isPreview" type="primary" size="mini" @click="handleDeitWork(2)">修改作业</el-button>
      </div>
    </div>
    <div class="edit-content" v-if="isEdit">
      <div class='work-edit' v-if="workInfo.work_types.includes('Text')">
        <sdc-mce-preview
        v-if="isPreview"
        ref="editor"
        :urlConfig="editorConfig.urlConfig"
        :catalogue.sync="editorConfig.catalogue"
        :content="work.content || '-'"
        >
        </sdc-mce-preview>
        <sdc-mce-editor
        v-else
        :insertItems="insertItems" 
        class="course-tincy" 
        ref='editor' 
        @getWordCount="getWordCount" 
        :env='editorEnv' 
        :content='work.content' 
        :catalogue.sync='editorConfig.catalogue' 
        :urlConfig='editorConfig.urlConfig' 
        :options='editorConfig.options'
        />
      </div>
      <div class='update-box'>
        <div style="display: flex;align-items: center;color: #00000099;">
          {{ $langue('Mooc_TaskDetail_HomeWork_UploadAttachments', { defaultText: '上传附件' }) }}
          <el-tooltip class='item' effect='dark' placement='top-start'>
            <div slot='content'>
              <p>1. 视频格式：支持wmv、mp4、flv、avi、rmvb、mpg、mkv、mov，单个视频小于2G</p>
              <p>2. 音频格式：支持w4v、m4a、wma、wav、mp3、amr，单个音频小于500M</p>
              <p>3. 文档格式：支持doc、docx、ppt、pptx、xls、xlsx、pdf，单个文档小于100M</p>
              <p>4. 图片格式：支持jpg、jpeg、gif、png、bmp、ico、svg，单个图片小于10M</p>
              <p>5. 压缩包格式：支持zip、rar，单个压缩包小于1G</p>
            </div>
            <img style="margin-left: 4px;width: 16px;height: 16px;" src='../../../assets/img/error-circle.png' />
          </el-tooltip>
        </div>
        <div :class="[{'disabled-upload': isPreview}, 'item-update']" v-for='(item, index) in uploadFileList' :key='index'>
          <el-upload
          action
          :http-request="onUpload" 
          :show-file-list="false"
          :auto-upload="true"
          :accept="item.accept"
          :multiple="false"
          :disabled="isPreview"
          :before-upload="beforeAvatarUpload"
          >
            <span :id="btnId(item.type)" style="display: flex;align-items: center;">
              <img style="width:16px;height:16px;" :src='item.img' alt />
              <span style="color: #000000e6;font-size: 14px;">{{item.text}}</span>
            </span>
          </el-upload>
        </div>
      </div>
      <div class="upload-progress-status">
        <div class="upload-item-box" v-for="(item, index) in fileData" :key="index">
          <span class="progress-left-content" @mouseover="titleOver($event, index)">
            <el-tooltip effect="dark" :disabled="!item.isOverflow" :content="item.file_name" placement="top-start">
              <span class="progress-title">{{ item.file_name }}</span>
            </el-tooltip>
            <span class="progress-loading">
              <!-- 加载中 -->
              <i class="el-icon-loading" v-if="item.status === 1"></i>
              <!-- 加载成功 -->
              <i class="el-icon-success" v-if="item.status === 2"></i>
              <!-- 加载失败 -->
              <i class="el-icon-error" v-if="item.status === 0"></i>
              {{ 
                item.status === 1 ? `${item.processValue}%` : 
                item.status === 2 ? '上传成功' :
                item.status === 0 ? '文件格式有误' : ''
              }}
            </span>
          </span>
          <span class="progress-right-content">
            <span v-if="item.file_size">文件大小 {{ changeSize(item.file_size) }}</span>
            <span v-if ="item.created_at" class="progress-time">上传日期： {{ item.created_at }}</span>
            <el-button v-if="item.status === 2" type="text" @click="resetUpload(item)">重新上传</el-button>
            <el-button type="text" @click="deleteUpload(item)">{{ $langue('Mooc_ProjectDetail_Comments_Delete', { defaultText: '删除' }) }}</el-button>
          </span>
        </div>
      </div>
    </div>
    <div class="preview-content" v-else>
      <div class="p-cur">
        <sdc-mce-preview 
        ref="preview" 
        :urlConfig="editorConfig.urlConfig" 
        :catalogue.sync="editorConfig.catalogue" 
        :content="work.content"
        >
        </sdc-mce-preview>
      </div>
      <div class="appendix-c">
        <appendix 
          ref="appendix" 
          :isPreview="true" 
          :fileData="fileData" 
          @deleteFile="deleteFile"
          >
        </appendix>
      </div>
    </div>
    <div class="my-work-footer" v-if="isEdit && !isPreview">
      <div class="footer-content">
        <span style="margin-right: 24px;"><span style="margin-right:10px;">当前字数：{{work.count}} </span> 预计所需阅读时间{{work.date}}分钟</span>
        <span class="footer-tips">{{ $langue('Mooc_TaskDetail_HomeWork_AutoSave', { defaultText: '每3分钟自动保存草稿' }) }} <span v-if="myWorkInfo.last_submit_time">，上次保存时间：{{myWorkInfo.last_submit_time}} </span></span>
        <el-button @click="confirmBut('draft')" :disabled="workDisabled">{{ $langue('Mooc_TaskDetail_HomeWork_SaveDraft', { defaultText: '保存草稿' }) }}</el-button>
        <div class="m-left">
          <el-button type="primary" @click="handlePerview">{{ $langue('Mooc_TaskDetail_HomeWork_Preview', { defaultText: '预览' }) }}</el-button>
        </div>
        <el-tooltip :disabled="!workDisabled && isInWorkSubmitTime" effect="dark" :content="submitContent" placement="top">
          <span> <el-button :disabled="workDisabled || !isInWorkSubmitTime"  type="primary" @click="submitWork">{{ $langue('Mooc_TaskDetail_HomeWork_SubmitHomeWork', { defaultText: '提交作业' }) }}</el-button></span>
        </el-tooltip>
      </div>
    </div>
  </div>

</template>

<script>
import appendix from '../../components/appendix.vue'
import {
  studentHomework,
  getHomeworkRecord,
  revocationHomeworkRecord,
  draftStudentHomework
} from 'config/api.conf'
import { handlerDateFormat } from '@/utils/tools.js'
let saveDraftTimer = null
export default {
  components: {
    appendix
  },
  props: ['workInfo'],
  data() {
    return {
      myWorkInfo: {
        last_submit_time: ''
      },
      isEdit: false,
      editorEnv: process.env.NODE_ENV,
      startTime: '',
      work: {
        content: '',
        count: 0,
        date: 0
      },
      fileData: [],
      resetIndex: -1,
      updaiteList: [
        {
          id: '1',
          img: require('@/assets/img/Image-files.png'),
          text: this.$langue('Mooc_TaskDetail_HomeWork_UploadImage', { defaultText: '上传图片' }),
          type: 'Image',
          accept: '.jpg,.jpeg, .gif, .png, .bmp, .ico, .svg'
        },
        {
          id: '2',
          img: require('@/assets/img/Video-file.png'),
          text: this.$langue('Mooc_TaskDetail_HomeWork_UploadVideo', { defaultText: '上传视频' }),
          type: 'Video',
          accept: '.wmv,.mp4,.flv,.avi,.rmvb,.mpg,.mkv,.mov'
        },
        {
          id: '3',
          img: require('@/assets/img/Notes.png'),
          text: this.$langue('Mooc_TaskDetail_HomeWork_UploadDoc', { defaultText: '上传文档' }),
          type: 'Doc',
          accept: '.doc,.docx,.ppt,.pptx,.xls,.xlsx,.pdf'
        },
        {
          id: '4',
          img: require('@/assets/img/File-music.png'),
          text: this.$langue('Mooc_TaskDetail_HomeWork_UploadAudio', { defaultText: '上传音频' }),
          type: 'Audio',
          accept: '.w4v,.m4a,.wma,.wav,.mp3,.amr,.mpeg'
        },
        {
          id: '5',
          img: require('@/assets/img/Folder-open.png'),
          text: this.$langue('Mooc_TaskDetail_HomeWork_UploadZip', { defaultText: '上传压缩包' }),
          type: 'Zip',
          accept: '.zip,.rar'
        }
      ],
      fileTypeArr: [
        {
          suffix: ['jpg', 'jpeg', 'gif', 'png', 'bmp', 'ico', 'svg'],
          file_type: '0',
          file_type_name: '图片',
          size: 10485760,
          size_name: '10MB',
          upload_type: 'Image'
        },
        {
          suffix: ['wmv', 'mp4', 'flv', 'avi', 'rmvb', 'mpg', 'mkv', 'mov'],
          file_type: '1',
          file_type_name: '视频',
          size: 2147483648,
          size_name: '2GB',
          upload_type: 'Video'
        },
        {
          suffix: ['w4v', 'm4a', 'wma', 'wav', 'mp3', 'amr', 'mpeg'],
          file_type: '2',
          file_type_name: '音频',
          size: 524288000,
          size_name: '500MB',
          upload_type: 'Audio'
        },
        {
          suffix: ['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf', 'msword', 'vnd.ms-excel', 'vnd.ms-powerpoint'],
          file_type: '3',
          file_type_name: '文档',
          size: 104857600,
          size_name: '100MB',
          upload_type: 'Doc'
        },
        {
          suffix: ['zip', 'rar'],
          file_type: '4',
          file_type_name: '压缩包',
          size: 1073741824,
          size_name: '1GB',
          upload_type: 'Zip'
        }
      ],
      accepts: '.wmv,.mp4,.flv,.avi,.rmvb,.mpg,.mkv,.mov,.w4v,.m4a,.wma,.wav,.mp3,.amr,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.pdf,.zip,.rar,.jpg,.jpeg, .gif, .png, .bmp, .ico, .svg',
      insertItems: [
        'link',
        'table',
        'image',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorConfig: {
        catalogue: false,
        options: {
          // 非必传字段
          selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
                    formatselect fontsizeselect lineheight |
                    bold italic underline strikethrough |
                    forecolor backcolor |
                    dent align bullist numlist |
                    insert codesample |
                    fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      }
    }
  },
  computed: {
    isPreview() {
      return this.$route.query.previewType === 'preview'
    },
    isInWorkSubmitTime() {
      // 获取当前时间
      const timeStamp = Date.parse(new Date())
      // 开始
      const startDate = this.workInfo.start_time && Date.parse(this.workInfo.start_time)
      // 结束
      const endDate = this.workInfo.end_time && Date.parse(this.workInfo.end_time)
      // 时间不存在表示未设置，即不限制提交时间
      if ((startDate && timeStamp < startDate) || (endDate && timeStamp > endDate)) {
        return false
      } else {
        return true
      }
    },
    submitContent() {
      let msg = ''
      if (this.workDisabled) {
        msg = this.$langue('Mooc_TaskDetail_HomeWork_CanntSubmitNoContent', { defaultText: '未填写作业内容，无法提交' })
      }
      // 获取当前时间
      const timeStamp = Date.parse(new Date())
      // 开始
      const startDate = Date.parse(this.workInfo.start_time)
      // 结束
      const endDate = Date.parse(this.workInfo.end_time)
      if (startDate && timeStamp < startDate) {
        msg = this.$langue('Api_Mooc_Homework_HomeworkNotOpen', { defaultText: '继续学习' })
      }
      if (endDate && timeStamp > endDate) {
        msg = this.$langue('Api_Mooc_Homework_HomeWorkDeadline', { defaultText: '作业已截止提交' })
      }
      return msg
    },
    // 未提交转态
    unopenedState() {
      return [null, 0, 2].includes(this.workInfo.status) 
    },
    // 退回状态
    isBack() {
      return this.workInfo.status === 2
    },
    workDisabled() {
      if (this.workInfo.work_types.includes('Text')) {
        if (!this.fileData.length && !this.work.count) {
          return true
        }
      } else {
        if (!this.fileData.length) {
          return true
        }
      }
      return false
    },
    uploadFileList() {
      return this.updaiteList.filter((e) => this.workInfo.work_types.includes(e.type))
    },
    btnId() {
      return (val) => {
        console.log('udddd', val)
        return `${val}-upload-btn`
      }
    },
    changeSize() {
      return (size) => {
        return Math.ceil((size / 1048576) * 10) / 10 + 'M'
      }
    }
  },
  created() {
    this.startTime = this.$moment().format('YYYY-MM-DD HH:mm:ss')
    this.getWorkRecord()
  },
  methods: {
    // 获取作业记录
    getWorkRecord() {
      let { homework_id, act_id } = this.$route.query
      let params = {
        act_id,
        homework_id
      }
      getHomeworkRecord(params).then((res) => {
        this.work.content = res?.content
        let attachments = (res?.homework_attachments || []).map((item) => {
          return {
            content_type: item.attachment_type,
            file_name: item.file_name,
            content_id: item.content_id,
            file_size: item.file_size,
            status: 2,
            processValue: 100,
            created_at: item.created_at
          }
        })
        this.fileData = attachments
        this.isEdit = !res?.status
        this.$emit('myWorkRecord', res)
        if (this.isEdit) {
          this.createSaveDraftTimer()
        }
        if (res) {
          this.myWorkInfo = res
        }
      })
    },
    handleArrow() {
      this.arrow = !this.arrow
    },
    deleteFile(val) {
      this.fileData = this.fileData.filter(
        (item) => item.content_id !== val.content_id
      )
    },
    submitWork() {
      if (
        this.workInfo.status === 1 ||
        this.workInfo.status === 3 ||
        this.workInfo.status === 4
      ) {
        this.$confirm(
          '您已提交过作业了，如需修改作业内容，请先撤回作业。',
          '提交作业',
          {
            confirmButtonText: '确定'
          }
        )
        return false
      }
      this.$confirm(
        '<p>作业提交后允许撤回修改内容，确定提交吗？</p><p style="color:#ED7B2F"><i class="el-icon-warning-outline" style="padding-right:5px;"></i>过了截止时间或有批阅分数后无法撤回修改</p>',
        '提交作业',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true
        }
      ).then(() => {
        this.confirmBut()
      })
    },
    handlePerview() {
      let dataWork = {
        content: (this.$refs['editor'] && this.$refs['editor'].getContent()) || '',
        fileData: this.fileData
      }
      sessionStorage.setItem('work_preview', JSON.stringify(dataWork))
      let routeData = this.$router.resolve({
        path: '/work/preview'
      })
      window.open(routeData.href, '_blank')
    },
    handleDeitWork(type) {
      // 1 撤回作业 2 修改作业
      type === 1 ? this.confirmButBack() : this.confirmButEdit()
    },
    // 撤回作业
    confirmButBack() {
      this.$confirm(
        '撤回后作业状态会变更为“未提交”，请尽快完成修改并重新提交',
        '确定要撤回并修改这个作业吗？',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }
      ).then(() => {
        let params = {
          record_id: this.myWorkInfo.record_id,
          act_id: this.myWorkInfo.act_id
        }
        revocationHomeworkRecord(params).then((res) => {
          this.$emit('getExplainInfo')
          this.$message.success('撤回成功')
          this.isEdit = true
          this.createSaveDraftTimer()
        })
      })
    },
    // 修改作业
    confirmButEdit() {
      this.$confirm(
        '<p>确定后，您可以对最近一次提交的作业内容进行修改并重新提交，请留意作业提交时间</p><p style="color:#ED7B2F"><i class="el-icon-warning-outline" style="padding-right:5px;"></i>如有需要请自行存档当前版本的作业内容</p>',
        '确定要修改作业吗？',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true
        }
      ).then(() => {
        this.isEdit = true
        this.createSaveDraftTimer()
      })
    },
    getWordCount(val) {
      this.work.count = val
      // 以一分钟看600个字计算
      this.work.date =
        val > 0
          ? val % 600 === 0
            ? val / 600
            : (Math.ceil((val * 10) / 600) / 10).toFixed(1)
          : 0
    },
    // 每3分钟自动保存一次
    createSaveDraftTimer() {
      if (!this.isEdit || saveDraftTimer) return
      let that = this
      console.log('createSaveDraftTimer')
      saveDraftTimer = setInterval(() => {
        that.confirmBut('draft', true)
      }, 180000) // 3分钟
      // }, 30000) // 30秒
    },
    // 确认提交
    confirmBut(type, isAuto) {
      if (this.workDisabled) return
      let { homework_id, task_id, act_id } = this.$route.query
      let homework_attachments = this.fileData.map((item) => {
        return {
          attachment_type: item.content_type,
          file_name: item.file_name,
          content_id: item.content_id,
          file_extension: item.content_type,
          file_size: item.file_size
        }
      })
      let content = (this.$refs['editor'] && this.$refs['editor'].getContent()) || ''
      let params = {
        act_id,
        homework_id,
        content: content,
        word_num: this.work.count,
        task_id,
        homework_attachments: homework_attachments,
        start_time: this.startTime
      }
      if (type === 'draft') {
        draftStudentHomework(params).then((res) => {
          this.myWorkInfo.last_submit_time = res.last_submit_time
          if (!isAuto) {
            this.$message.success('保存成功')
          }
        })
      } else {
        studentHomework(params).then((res) => {
          console.log(res)
          this.isEdit = false
          saveDraftTimer && clearInterval(saveDraftTimer)
          saveDraftTimer = null
          this.$message.success('提交成功')
          this.getWorkRecord()
          this.$emit('getExplainInfo', 'finish', res)
        })
      }
    },
    // 取消上传
    deleteUpload(item) {
      this.fileData = this.fileData.filter((e) => e.content_id !== item.content_id)
    },
    // 重新上传
    resetUpload(item) {
      this.resetIndex = this.fileData.findIndex((e) => e.content_id === item.content_id)
      document.getElementById(`${item.content_type}-upload-btn`).click()
    },
    // 视频上传
    onUpload({ file }) {
      let that = this
      const authUrl = location.hostname.endsWith('.woa.com')
        ? process.env.VUE_APP_PORTAL_HOST_WOA
        : process.env.VUE_APP_PORTAL_HOST
      /* eslint-disable*/
      new contentCenter.uploadFile({
        file,
        type: that.curFile.update_type, // 0表示上传图片，1视频 2音频 3文档
        appId: 'QLearningService',
        operateAuthUrl: `${authUrl}/training-portal-common/api/v1/portal/user/common/uploadOperateSignature`,
        onSuccess(res) {
          console.log('预览', that.fileData)
          that.fileData = that.fileData.map((e) => {
            if(e.file_name === file.name) {
              return {
                ...res[0],
                ...e,
                file_size:  file.size,
                content_type: that.curFile.upload_type,
                created_at: handlerDateFormat(new Date(), '-'),
                status: 2 // 上传成功
              }
            }
            return e
          })
        },
        onError(err) {
          console.log(err)
          that.$message.error(err || '上传失败')
          if (that.fileData?.length) {
            that.fileData.forEach((e) => {
              if(e.file_name === file.name) {
                e.status = 0
                e.file_name = file.name
              } else {
                that.fileData.push({
                  file_name: file.name,
                  status: 0 // 上传失败
                })
              }
            })
          } else {
            that.fileData.push({
              file_name: file.name,
              status: 0 // 上传失败
            })
          }
        },
        onProgress(info) {
          const percent = parseInt(info.percent * 10000) / 100
          if (that.fileData?.length) {
            let index = 0
            if(that.resetIndex >= 0) { // 重新上传
              index = that.resetIndex
              that.resetIndex = -1 // 重置避免影响
            } else { // 正常上传
              index = that.fileData.findIndex((e) => e.file_name === file.name) 
            }
            if(index >= 0) {
              that.fileData[index] = {
                status: 1,
                processValue: percent,
                file_name: file.name,
              }
            } else {
              that.fileData.push({
                processValue: percent,
                file_name: file.name,
                status: 1 // 上传中
              })
            }
          } else {
            that.fileData.push({
              processValue: percent,
              file_name: file.name,
              status: 1 // 上传中
            })
          }
          // 更新数据
          that.fileData = that.fileData.slice()
        }
      })
    },
    titleOver(e, index) {
      const target = e.target
      this.$set(
        this.fileData[index],
        'isOverflow',
        target.scrollWidth > target.clientWidth
      )
    },
    beforeAvatarUpload(file) {
      this.curFile = {}
      let typeObj = JSON.parse(JSON.stringify(this.fileTypeArr))
      let arrL = typeObj.length
      let isSize = false
      let fileType = file.type ? file.type : file.name.substring(file.name.lastIndexOf('.') + 1)
      for (let i = 0; i < arrL; i++) {
        let suffixL = typeObj[i].suffix.length
        for (let v = 0; v < suffixL; v++) {
          let suffix = typeObj[i].suffix
          let reg = RegExp(suffix[v])
          if (reg.exec(fileType)) {
            let size = typeObj[i].size
            isSize = file.size < size
            if (isSize) {
              this.curFile = typeObj[i]
              this.curFile.size = file.size
              break
            } else {
              this.$message.error(`上传${typeObj[i].file_type_name}大小不能超过 ${typeObj[i].size_name}!`)
              return false
            }
          }
        }
      }
      return isSize
    }
  },
  beforeDestroy() {
    saveDraftTimer && clearInterval(saveDraftTimer)
    saveDraftTimer = null
  },
}
</script>
<style lang="less" scoped>
.my-work {
  :deep(.work-edit) {
    width: 952px;
    margin-bottom: 16px;
    .tox.tox-tinymce {
      border: 1px solid #ccc !important;
      height: 450px;

      .tox-sidebar-wrap .tox-edit-area {
        min-height: 450px !important;
      }
    }
  }
  .edit-content {
    .y-color {
      color: #ff7548ff;
    }
    .result {
      color: #000;
      line-height: 22px;
      white-space: pre-wrap;
    }
  }
  .update-box {
    display: flex;
    align-items: center;
    margin-bottom: 24px;
    height: 46px;
    padding-left: 12px;
    background: #FAFAFA;
    border-radius: 4px;
  }
  .item {
    color: #00000066;
    margin-right: 24px;
  }
  .item-update {
    display: flex;
    align-items: center;
    margin: 2px 24px 0 0;
    cursor: pointer;
    color: #00000066;
    // line-height: 46px;
    :deep(.upload-box) {
      .el-upload {
        width: 100%;
        height: 100%;
        .el-upload-dragger {
          width: 80px;
          height: 20px;
          border: unset;
          background-color: unset;
        }
        .split-line {
          margin: 0 8px;
          color: #333;
        }
        .upload-text {
          color: #666;
        }
      }
    }
    span {
      padding-left: 4px;
    }
  }
  :deep(.disabled-upload) {
    .el-upload {
      cursor: not-allowed;
    }
  }
  .p-cur {
    padding-top: 12px;
  }
}
.work-sbmit-date {
  position: absolute;
  right: 24px;
  top: 27px;
  .flex-d {
    display: flex;
    align-items: center;
    p {
      padding-right: 28px;
    }
    .color {
      color: #00000099;
    }
  }
}
.preview-content {
  .appendix-c {
    padding-bottom: 20px;
  }
}
.my-work-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 70px;
  line-height: 70px;
  background-color: #fff;
  z-index: 99;
  .footer-content {
    text-align: center;
    color: #00000066;
  }
  .footer-content-work {
    width: 1100px;
    margin: auto;
    text-align: right;
  }
  .footer-tips {
    margin-right: 120px;
  }
  .m-left {
    margin-left: 20px;
    margin-right: 20px;
    display: inline-block;
  }
  .inner {
    @media screen and (max-width: 1660px) {
      width: 1158px;
    }
    @media screen and (min-width: 1661px) {
      width: 1440px;
    }
    text-align: right;
    margin: 0 auto;
  }
  .el-button {
    width: 104px;
  }
}
.upload-progress-status {
  background: #fafafa;
  // width: 800px;
  margin-top: 16px;
  font-size: 14px;
  .upload-item-box {
    line-height: 22px;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    .progress-left-content {
      display: flex;
      align-items: center;
      .progress-title {
        overflow: hidden;
        word-break: break-all;
        text-overflow: ellipsis;
        max-width: 220px;
        display: inline-block;
        white-space: nowrap;
      }
      .progress-loading {
        margin-left: 8px;
        color: #0052d9;
        i {
          margin-right: 5px
        }
        .el-icon-success {
          color: #2BA471;
        }
        .el-icon-error {
          color: #D54941;
        }
      }
    }
    .progress-right-content {
      color: #00000066;
      font-size: 12px;
      .progress-time {
        margin-left: 16px;
      }
      .el-button {
        padding: unset;
        margin-left: 40px;
      }
      .el-button+.el-button {
        margin-left: 20px;
      }
    }
  }
}
</style>
