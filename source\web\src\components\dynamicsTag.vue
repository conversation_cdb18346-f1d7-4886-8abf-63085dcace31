<template>
  <div class="dynamics-container" v-if="isTarget">
    <!-- 数据标签 -->
    <div 
    class="rankTag-main" 
    v-if="forMatData.tag_type === 4" 
    :dt-eid="dtRemarkData('eid')"
    :dt-remark="dtRemarkData('remark')"
    :dt-areaid="dtRemarkData('areaid')" 
    >
      <div class="data-tag common">{{ forMatData.firstLabel }}</div>
    </div>
    <!-- 排行榜标签 -->
    <div v-else>
      <div 
      class="rankTag-main" 
      v-if="forMatData.isShowTag" 
      @click="toRank()" 
      :dt-eid="dtRemarkData('eid')"
      :dt-remark="dtRemarkData('remark')"
      :dt-areaid="dtRemarkData('areaid')" 
      >
        <div :class="['first', 'common', {'no-second': !forMatData.secondLabel}]">{{ forMatData.firstLabel }}</div>
        <div class="second common" v-if="forMatData.secondLabel">{{ forMatData.secondLabel  }}</div>
      </div>
    </div>
  </div>
</template>
<script>
import axios from 'axios'
import { mapState } from 'vuex'
export default {
  props: {
    tagData: {
      type: Object,
      default: () => ({})
    },
    dtData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      forMatData: {
        isShowTag: false
      },
      curQuqarter: 1,
      year: 1970,
      useInfo: {},
      positionInfo: {}
    }
  },
  watch: {
    tagData: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val && val.data_cal_date_time) {
          this.getYearQuater()
        }
      }
    }
  },
  computed: {
    ...mapState(['isTarget']),
    commDomain() {
      return process.env.NODE_ENV === 'production' ? '//portal.learn.woa.com' : '//test-portal-learn.woa.com'
    },
    dtRemarkData() {
      return (type) => {
        if (JSON.stringify(this.dtData) === '{}') return false
        const { page, page_type, container, click_type, content_type, container_id, page_id, terminal, id } = this.dtData
        const courseId = id || page_id
        if (type === 'remark') {
          return JSON.stringify({
            page,
            page_type, 
            container,
            click_type,
            content_type,
            content_id: this.tagData.id,
            content_name: `${this.forMatData.firstLabel}${this.forMatData.secondLabel}`,
            act_type: this.tagData.act_type,
            container_id,
            page_id,
            terminal
          })
        } else if (type === 'eid') {
          return `element_${courseId}_${this.tagData.id}`
        } else {
          return `area_${courseId}_${this.tagData.id}`
        }
      }
    }
  },
  mounted() {
    this.handleLabel()
  },
  methods: {
    toRank() {
      // 当class_id为0，对应“全员”
      // 当class_id为4，class_key为18时对应“T族”
      // 当class_id为4，class_key为14时对应“P族”
      // 当class_id为4，class_key为22时对应“D族”
      // 当class_id为4，class_key为19时对应“M族”
      // 当class_id为4，class_key为17时对应“S族”
      // 当class_id为6，对应“领导力”
      // 当class_id为7，对应“新员工”
      const { user_category, date_category, class_id, class_key } = this.tagData
      const recommendInfo = {
        'search': '5',
        'study': '1',
        'latest': '2',
        'mostViewed': '4',
        'mostGrowth': '3',
        '5-8': '11',
        '9-11': '12',
        'over12': '13',
        'freshman': '6',
        'leaderPower': '7',
        'teamLeader': '8',
        'director': '9',
        'GM': '10'
      }
      let commonUrl = `${this.commDomain}/rank/index`
      let url = ''
      if (date_category === 'quarter') { // 季榜-年榜
        url = `${commonUrl}?class_id=${class_id}&class_key=${class_key}&date=${date_category}&year=${this.curYear}&quarter=${this.curQuqarter}`
      } else if (date_category === 'year') {
        url = `${commonUrl}?class_id=${class_id}&class_key=${class_key}&date=${date_category}&year=${this.curYear}`
      } else { // 日榜-月榜
        url = `${commonUrl}?recommend_type=${recommendInfo[user_category]}&date=${date_category}&position_id=${class_id}`
      }
      window.open(url)
    },
    async handleLabel() {
      let positionLalbel = ''
      const { class_value, class_id, class_key, user_category, data_cal_date_time, date_category } = this.tagData
      // 没有数据就不显示
      if (['year', 'quarter'].includes(date_category) && !data_cal_date_time) {
        this.formatLabel(null)
        return
      }
      if (this.tagData && this.tagData.tag_type) {
        if (this.tagData.tag_type * 1 === 4) { // 数据标签
          this.forMatData = {
            firstLabel: this.tagData.tag_text,
            tag_type: 4
          }
        } else { // 排行榜标签
          // 用户是否领导力不是则不显示标签
          if (['leaderPower', 'teamLeader', 'director', 'GM'].includes(user_category)) {
            await this.getPosition()
            if (!this.positionInfo) {
              this.formatLabel(null)
              return
            }
          }
          if (user_category === 'freshman') {
            // 新人
            await this.getUserInfo() // 先获取用户信息
            // 新员工： 入职未超过365天
            const { join_date } = this.useInfo
            if (join_date) {
              const compareTime = 365 * 24 * 60 * 60 * 1000
              const curTime = Date.now()
              // 不是新人
              if (curTime - new Date(join_date).getTime() > compareTime) {
                this.formatLabel(null)
                return
              }
            }
          }
          let id = class_id * 1
          if ([2, 5].includes(id)) {
            const { belongDeptId, PositionId } = this.useInfo
            if (id === 5 && class_key * 1 === belongDeptId) { // 部门
              positionLalbel = '本部门'
            } else if (id === 2 && class_key * 1 === PositionId) { // 职位
              positionLalbel = '本职位'
            } 
            this.formatLabel(positionLalbel)
          } else {
            let str = class_value.match(/^[a-zA-Z0-9]+/)
            positionLalbel = str ? str[0] : class_value
            this.formatLabel(positionLalbel)
          }
        }
      }
    },
    // 职位信息
    getPosition() {
      return new Promise((resolve) => {
        const url = process.env.NODE_ENV === 'production' ? `${this.commDomain}/portal-qlearning-adapter/` : `${this.commDomain}/portal-qlearning-adapter/`
        axios.get(
          `${url}api/adapter/common/user/get_level_name`,
          { withCredentials: true }
        ).then((res) => {
          this.positionInfo = res.data.data
          resolve(res.data.data)
        })
      })
    },
    // 用户信息
    getUserInfo() {
      return new Promise((resolve) => {
        const url = process.env.NODE_ENV === 'production' ? `${this.commDomain}/v8` : `${this.commDomain}/v8`
        axios.get(
          `${url}/api/user/base-info/user/get-user-basic-info`,
          { withCredentials: true }
        ).then((res) => {
          this.useInfo = res.data.data
          resolve(res.data.data)
        })
      })
    },
    getYearQuater() {
      const { data_cal_date_time } = this.tagData
      const date = new Date(data_cal_date_time)
      const year = date.getFullYear()
      const currentMonth = date.getMonth() // 获取当前月份，0-11
      let curQuqarter = 0
      if (currentMonth < 3) {
        curQuqarter = 'Q1' // 春季（1月份到3月份
      } else if (currentMonth < 6) {
        curQuqarter = 'Q2' // 夏季（4月份到6月份
      } else if (currentMonth < 9) {
        curQuqarter = 'Q3' // 秋季（7月份到9月份
      } else {
        curQuqarter = 'Q4' // 冬季（10月份到12月份
      }
      this.curQuqarter = curQuqarter
      this.curYear = year
    },
    formatLabel(positionLalbel) {
      let isShowTag = true
      let firstLabel = ''
      let secondLabel = ''
      let { user_category, date_category, top_category, tag_type } = this.tagData
      let top10_days = 0
      if (['year', 'quarter'].includes(date_category)) {
        top10_days = this.tagData.top10_days && this.tagData.top10_days > 30 ? 30 : this.tagData.top10_days
      } else {
        top10_days = this.tagData.top10_days && this.tagData.top10_days > 12 ? 12 : this.tagData.top10_days
      }
      let type = tag_type * 1
      const dateInfo = {
        'day': '日',
        'month': '月',
        'quarter': '季度',
        'year': '年度'
      }
      const menuInfo = {
        'search': '热搜',
        'study': '热学',
        'latest': '新课',
        'mostViewed': '口碑',
        'mostGrowth': '飙升',
        '5-8': '职级5-8',
        '9-11': '职级9-11',
        'over12': '专家',
        'freshman': '新人',
        'leaderPower': '领导力',
        'teamLeader': '组长',
        'director': '总监',
        'GM': 'GM'
      }
      if (type === 1) { // 效果标签
        if (date_category === 'quarter') { // 季度好课 XX年QX好课
          firstLabel = `${this.curYear}年${this.curQuqarter}好课`
        } else if (date_category === 'year') { // 年度好课 XX年度好课
          firstLabel = `${this.curYear}年度好课`
        } else {
          // 【鹅厂热学月榜】霸榜2月
          // 【鹅厂热学月榜】霸榜3天
          firstLabel = `${positionLalbel}${menuInfo[user_category]}${dateInfo[date_category]}榜`
          secondLabel = `霸榜${top10_days}${dateInfo[date_category]}`
        }
      } else if (type === 2) { // 动态标签
        // 【鹅厂热学榜】昨日TOP3
        // 【鹅厂热学榜】上月TOP3
        firstLabel = `${positionLalbel}${menuInfo[user_category]}榜`
        const dayLabel = date_category === 'day' ? '昨日' : '上月'
        secondLabel = `${dayLabel}TOP${top_category}`
      } else if (type === 3) { // 历史动态标签
        // 【鹅厂热学日榜】历史最高第3名
        // 【鹅厂热学月榜】历史最高第3名
        firstLabel = `${positionLalbel}${menuInfo[user_category]}${dateInfo[date_category]}榜`
        secondLabel = `历史最高第${top_category}名`
      }
      // 当不需要显示动态标签需要通知出去
      if (!positionLalbel) {
        isShowTag = false
        this.$emit('isShowDynamicsTag', isShowTag)
      }
      this.forMatData = {
        firstLabel,
        secondLabel,
        tag_type: type,
        isShowTag: !!positionLalbel
      }
    }
  }
}
</script>
<style lang="less" scoped>
.dynamics-container {
  .rankTag-main {
    display: flex;
    align-items: center;
    margin-right: 16px;
    .common {
      font-size: 12px;
      font-weight: 500;
      line-height: 22px;
      height: 22px;
      padding: 0 6px;
      cursor: pointer;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .first {
      background: linear-gradient(90deg, #FCF1DA 0%, #ECD09D 100%);
      color: #996B2D;
      border-radius: 4px 0 0 4px;
      max-width: 108px;
    }
    .second {
      background: #FDF4E0;
      color: #c29344;
      border-radius: 0px 4px 4px 0px;
      max-width: 107px;
    }
    .no-second {
      border-radius: 4px;
    }
    .data-tag {
      background: #FDF4E0;
      color: #c29344;
      border-radius: 4px
    }
  }
}
</style>
