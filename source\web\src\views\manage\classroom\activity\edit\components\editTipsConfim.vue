<template>
  <div class="edit-tips-confim">
    <el-dialog width="668px" :visible="visible" title="提示" @close="close"
      :close-on-click-modal="false" :before-close="close" custom-class="edit-tips-confim-dialog">
      <el-form ref="form" :model="form" label-width="100px" :rules="rules">
        <el-form-item label="修改原因" prop="tips">
          <el-input type="textarea" v-model="form.tips" :autosize="{ minRows: 5, maxRows: 10 }"
            placeholder="请输入提示内容"></el-input>
            <span class="tips-content">您不是该活动负责人或创建人，如果修改活动，系统会发送一封知悉邮件给该负责人。</span>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close" class="btn1">取消</el-button>
        <el-button type="primary" @click="submit" class="btn2">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        tips: ''
      },
      rules: {
        tips: [
          { required: true, message: '请输入提示内容', trigger: 'blur' }
        ] 
      }
    }
  },
  created() {
  },
  mounted() {
  
  },
  methods: {
    close() {
      this.$emit('update:visible', false) 
    },
    submit() {
      this.$emit('getEditReason', this.form.tips)
      this.close()
    }
  }
}
</script>
<style scoped lang="less">
.edit-tips-confim {
  :deep(.edit-tips-confim-dialog) {
    .el-dialog__header {
      padding: 24px 30px 24px 32px;
      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
        border-bottom: solid 1px #EEEEEE;
      }
    }

    .el-dialog__body {
      padding: 24px 32px 0 0;
    }

    .el-dialog__footer {
      margin-top: 79px;
      padding: 0 32px 0 0;
      height: 80px;
      .dialog-footer {
        display: flex;
        align-items: center;
        height: 100%;
        width: 100%;
        .btn1, .btn2 {
          height: 32px;
          width: 60px;
          border-radius: 3px;
          text-align: center;
          line-height: 32px;
          padding: 0;
        }
        .btn1 {
          margin-left: auto;
        }
      }
    }

    .tips-content {
      margin: 8px 0 0 0;
      font-size: 14px;
      color: #00000066;
    }
  }
}
</style>
