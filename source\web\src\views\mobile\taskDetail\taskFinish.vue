<template>
  <div class="task-finish">
    <div class="content">
      <img
        :src="require('@/assets/mooc-img/task-finish.png')"
        alt=""
        class="finish-icon"
      />
      <p class="tips1">当前任务已完成，可继续项目学习</p>
      <div class="btn-box">
        <el-button plain style="margin-right:14px;" @click="toParent('prevTask')" v-if="prevTaskBtnShow">上一个任务</el-button>
        <el-button type="primary" @click="toParent('nextTask')" v-if="nextTaskBtnShow">下一个任务</el-button>
      </div>
    </div>
  </div>
</template>

<script>

import MoocJs from 'sdc-moocjs-integrator'

export default {
  data() {
    return {
      prevTaskBtnShow: false,
      nextTaskBtnShow: false
    }
  },
  methods: {
    toParent(type) {
      if (type === 'prevTask') {
        MoocJs.postMessage('previous')
      } else {
        MoocJs.postMessage('next')
      }
    }
  },
  mounted () {
    MoocJs.messageListener((res) => {
      console.log('父页面传过来的参数', res)
      if (res.events === 'switchTaskBtnShow') {
        this.prevTaskBtnShow = res.params.prevTaskBtnShow
        this.nextTaskBtnShow = res.params.nextTaskBtnShow
      }
    })
    
    MoocJs.complete()
  }
}
</script>

<style lang="less">
.task-finish {
  // padding: 16px 0 0 0;
  width: 100%;
  height: 100%;
  .content {
    width: 100%;
    height: 100%;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-radius: 4px;
    .finish-icon {
      width: 160px;
    }
    .tips1 {
      margin-top: 24px;
      color: #000000;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
    }
    .btn-box {
      display: flex;
      margin-top: 24px;
    }
  }
}
</style>
