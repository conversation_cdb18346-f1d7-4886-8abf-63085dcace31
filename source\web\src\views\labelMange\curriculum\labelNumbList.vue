<template>
  <div>
    <el-dialog
      title="标签列表"
      :visible.sync="visible"
      width="60%"
      :before-close="handleClose"
      :close-on-click-modal="click_modal"
      @open="getlist"
    >
      <div>
        <div class="showtop">
          <el-input
            class="top_inp"
            size="small"
            placeholder="请输入内容"
            v-model="showtop"
            clearable
          >
          </el-input>
          <el-button
            class="btn_top"
            type="primary"
            size="small"
            @click="findlabel"
            >搜索</el-button
          >
        </div>
        <div class="tabe">
          <el-table
            :data="tableData"
            header-row-class-name="table-header-style"
            row-class-name="table-row-style"
            :header-cell-style="{
              background: '#f5f5f5'
            }"
          >
            <el-table-column
              v-for="item of tableList"
              :key="item.prop"
              :column-key="item.prop"
              :prop="item.prop"
              :label="item.label"
              :width="item.width ? item.width : 'auto'"
              :filters="item.filters ? item.filters : undefined"
              :filter-multiple="false"
              show-overflow-tooltip
              align="left"
            >
            </el-table-column>
          </el-table>
        </div>
        <div class="table-pagination">
          <el-pagination
            :hide-on-single-page="tableParams.list.length === 0"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="tableParams.page_no"
            :page-sizes="[10, 20, 30, 50, 100]"
            :page-size="tableParams.page_size"
            layout="total,  prev, pager, next, sizes, jumper"
            :total="tableParams.total"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { labelsAPI } from 'config/api.conf'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    itemData: {}
  },
  data() {
    return {
      tableData: [],
      click_modal: false,
      showtop: '',
      tableList: [
        { label: '标签分类', prop: 'category_full_name' },
        { label: '标签名称', prop: 'label_name' },
        { label: '创建时间', prop: 'created_at' }
      ],
      tableParams: {
        page_no: 1,
        page_size: 10,
        total: 0,
        list: [
          { label: '标签分类', prop: 'category_full_name' },
          { label: '标签名称', prop: 'label_name' },
          { label: '创建时间', prop: 'created_at' }
        ]
      }
    }
  },
  methods: {
    getlist() {
      let params = {
        category_id: this.itemData.category_id,
        label_name: this.showtop,
        size: this.tableParams.page_size,
        current: this.tableParams.page_no
      }
      labelsAPI(params).then((res) => {
        this.tableData = res.records
        this.tableParams.total = res.total
      })
    },
    findlabel() {
      this.getlist()
    },
    handleClose() {
      this.$emit('update:visible', false)
    },
    handleSizeChange(size) {
      this.tableParams.page_size = size
      this.getlist()
    },
    handleCurrentChange(current) {
      this.tableParams.page_no = current
      this.getlist()
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.el-input__inner {
  color: #000 !important;
}
/deep/.el-table .table-row-style td {
  color: #000 !important;
}
.showtop {
  display: flex;
  .top_inp {
    width: 30%;
  }
  .btn_top {
    width: 60px;
    margin-left: 20px;
  }
}
.tabe {
  margin-top: 20px;
}
</style>
