<template>
  <div class="buttom">
    <div class="inner">
      <div class="convention-confirm-box" v-if="isNeedConvention" :style="conventionStyle">
        <convention-confirm v-model="isChooseConvention" />
      </div>
      <div class="btn-inner">
        <el-button v-show="cancelText" @click="cancel" plain>{{ cancelText }}</el-button>
        <el-button v-show="submitText && !isNeedConvention" :disabled="disabledBtn" :type="submitTextType" @click="submit">{{ submitText }}</el-button>
        <el-button v-show="isNeedConvention" :disabled="!isChooseConvention || disabledBtn" :type="submitTextType" @click="submit">{{ submitText }}</el-button>
        <el-button v-show="otherText" :type="otherTextType" @click="submit">{{ otherText }}</el-button>
      </div>
    </div>
  </div>
</template>
<script>
import conventionConfirm from './convention-confirm.vue'
export default {
  components: {
    conventionConfirm
  },
  props: {
    cancelText: {
      type: String,
      default: '取消'
    },
    submitText: {
      type: String,
      default: '保存'
    },
    submitTextType: {
      type: String,
      default: 'primary'
    },
    otherTextType: {
      type: String,
      default: 'primary'
    },
    // cancelBtnShow: {
    //   type: Boolean,
    //   default: true
    // },
    // submitBtnShow: {
    //   type: Boolean,
    //   default: true
    // },
    otherText: {
      type: String,
      default: ''
    },
    disabledBtn: {
      type: Boolean,
      default: false
    },
    isNeedConvention: {
      type: Boolean,
      default: false
    },
    conventionStyle: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      isChooseConvention: false
    }
  },
  methods: {
    cancel() {
      this.$emit('cancel')
    },
    submit() {
      this.$emit('save')
    }
  }
}
</script>
<style lang="less" scoped>
  .buttom {
   position: fixed;
   bottom: 0;
   left: 0;
   width: 100%;
   height: 70px;
   line-height: 70px;
   background-color: #fff;
   text-align: right;
   z-index: 99;
   display: flex;
   .convention-confirm-box {
    margin-left: 160px;
   }
   .btn-inner {
    margin-left: auto;
   }
   .inner {
     @media screen and (max-width: 1660px) {
       width: 1158px;
     }
     @media screen and (min-width: 1661px) {
       width: 1440px;
     }
    display: flex;
    align-items: center;
    margin: 0 auto;
   }
   .el-button {
     margin: 0 20px 0 0;
     min-width: 104px;
   }
 }
</style>
