<template>
  <div class="introduce-container">
    <div class="share-box">
      <div class="share-box-title">选择适合你的分享组织形式</div>
      <div class="share-box-content">
        <div class="share-box-content-item" :class="{'disable': !item.permission}" v-for="item in shareList" :key="item.id" :x-operation-code="item.permissionCode" x-operation-model="disable">
          <img class="share-box-content-item-preview" :src="item.icon" alt="" />
          <div class="share-box-content-item-info">
            <div class="share-box-content-item-info-title">
              <span class="title" @click="toLink(item)">{{ item.title }}</span>
              <span class="label">{{ item.label }}</span>
            </div>
            <div class="share-box-content-item-info-desc">
              <span>{{ item.desc }}</span>
              <span class="link" v-if="item.id === 3">查看完整流程指引</span>
            </div>
          </div>
          <img class="share-box-content-item-arrow" @click="toLink(item)" src="@/assets/classroomImg/arrow-right-circle.png" alt="" />
        </div>
      </div>
    </div>

    <div class="introduce-box">
      <div class="introduce-title">开班开课：<span>腾讯会议&腾讯问卷等多种工具能力加持，助你高效组织分享</span></div>
      <div class="box">
        <div class="box-title"><img src="@/assets/classroomImg/thinking-problem.png" alt="为什么使用 Q-Learning 组织分享"/>为什么使用 Q-Learning 组织分享</div>
        <div class="box-content">
          <div class="box-content-introduce">
            <div class="box-content-item">
              <div class="box-content-item-title">灵活的线上下组织形式</div>
              <div class="box-content-item-content">支持腾讯会议 & 线下授课，一键生成腾讯会议，学员自主报名线下/线上场次，全自动分发会议链接、自动考勤</div>
              <div class="box-content-item-content">AI会议回看、课堂纪要等进阶功能，让开班更省心，学习更高效</div>
            </div>
            <div class="box-content-item">
              <div class="box-content-item-title">完善的问卷能力</div>
              <div class="box-content-item-content">问卷、投票、抽奖应有尽有，文本示例文本示例文本示例文本示例文本示例文本示例</div>
              <div class="box-content-item-content">文本示例文本示例文本示例文本示例文本示例文本示例文本示例文本示例文本示例文本示例文本示例文本示例</div>
            </div>
            <div class="box-content-item">
              <div class="box-content-item-title">丰富的课前课后服务</div>
              <div class="box-content-item-content">问卷、投票、抽奖应有尽有，文本示例文本示例文本示例文本示例文本示例文本示例</div>
              <div class="box-content-item-content">文本示例文本示例文本示例文本示例文本示例文本示例文本示例文本示例文本示例文本示例文本示例文本示例</div>
            </div>
            <div class="box-content-item">
              <div class="box-content-item-title">安全便捷的管理系统</div>
              <div class="box-content-item-content">报名权限灵活可控，日历提醒，讲师工作台，文本示例文本示例文本示例文本示例文本示例文本示例</div>
            </div>
          </div>
          <div class="box-content-preview">
            <img class="img" src="@/assets/mooc-img/default_bg_img.png" alt=""/>
            <img class="img" src="@/assets/mooc-img/default_bg_img.png" alt=""/>
            <img class="img" src="@/assets/mooc-img/default_bg_img.png" alt=""/>
            <img class="img" src="@/assets/mooc-img/default_bg_img.png" alt=""/>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: '',
  components: {},
  data() {
    return {
      shareList: [
        {
          id: 1,
          icon: require('@/assets/classroomImg/activity.png'),
          title: '活动：快速发起一次分享活动报名',
          label: '仅需 5 分钟即可完成创建',
          desc: '形态介绍指引，说清楚适用于什么场景，文案示例文案文案示例文案文案示例文案示例文案示例文案示例文案示例文案示例文',
          // permissionCode: 'QLV8_Activity',
          permission: true,
          routerName: 'activityPage',
          testUrl: '',
          prdUrl: ''
        },
        {
          id: 2,
          icon: require('@/assets/classroomImg/class.png'),
          title: '班级：基于已有的课程，建立一个新的班级并发起报名',
          label: '仅需 5 分钟即可完成创建',
          desc: '形态介绍指引，说清楚适用于什么场景，文案示例文案文案示例文案文案示例文案示例文案示例文案示例文案示例文案示例文',
          permissionCode: 'QLV8_MCMCM',
          permission: true,
          routerName: '',
          testUrl: '//test-learn.woa.com/manage/class/edit',
          prdUrl: '//learn.woa.com/manage/class/edit'
        },
        {
          id: 3,
          icon: require('@/assets/classroomImg/course.png'),
          title: '课程：开发认证一门新的课程',
          label: '审批流程预计：xx 小时',
          desc: '形态介绍指引，说清楚适用于什么场景，文案示例文案文案示例文案文案示例文案示例文案示例文案示例文案示例文案示例文，',
          permissionCode: 'QLV8_MFFC_FCM_BG',
          permission: true,
          routerName: '',
          testUrl: '//test-learn.woa.com/manage/face/bgcourse',
          prdUrl: '//learn.woa.com/manage/face/bgcourse'
        }
      ]
    }
  },
  watch: {},
  computed: {},
  created() {},
  mounted() {},
  beforeDestroy() {},
  methods: {
    toLink (v) {
      console.log(v.routerName)
      const { routerName, testUrl, prdUrl } = v
      let query = {}
      if (this.$route.query.page_id) {
        query = {
          page_id: this.$route.query.page_id
        }
      }
      if (routerName) {
        this.$router.push({ 
          name: routerName,
          query
        })
      } else if (testUrl || prdUrl) {
        let url = process.env.NODE_ENV === 'production' ? prdUrl : testUrl
        window.open(url)
      } else {
        this.$message.warning('暂无跳转链接')
      }
    }
  }
}
</script>

<style lang="less" scoped>
  .introduce-container {
    width: 100%;
    height: 100%;
    // overflow-y: auto;
    display: flex;
    flex-direction: column;
    .introduce-box {
      width: 100%;
      flex: 1;
      padding: 28px 20px 20px 20px;
      background-color: #fff;
      .introduce-title {
        font-size: 20px;
        line-height: 24px;
        font-weight: 600;
        font-family: "PingFang SC";
        display: flex;
        align-items: center;
        margin-bottom: 20px;
        &> span {
          font-size: 16px;
          font-family: PingFang SC;
          font-weight: 400;
        }
      }
      .box {
        width: 100%;
        padding: 16px;
        border-radius: 6px;
        background: linear-gradient(90deg, #FBFBFB -0.69%, #FBFDFF 99.46%);
        .box-title {
          width: fit-content;
          line-height: 32px;
          padding: 0 16px;
          font-size: 12px;
          border-radius: 44px;
          background-color: #006FFF;
          font-weight: 600;
          color: #fff;
          display: flex;
          align-items: center;
          &> img{
            width: 16px;
            height: 16px;
            margin-right: 10px;
          }
        }
        .box-content {
          .box-content-introduce {
            padding: 10px 0 8px 22px;
            .box-content-item {
              margin-bottom: 8px;
              .box-content-item-title {
                line-height: 22px;
                font-size: 14px;
                font-weight: 600;
                color: #1E53DD;
                margin-bottom: 2px;
                position: relative;
                &::before {
                  content: '';
                  width: 5px;
                  height: 5px;
                  border-radius: 50%;
                  background-color: #1E53DD;
                  margin-right: 6px;
                  display: block;
                  position: absolute;
                  left: -14px;
                  top: 10px;
                }
              }
              .box-content-item-content {
                color: #111111;
                font-size: 12px;
                line-height: 20px;
              }
            }
          }
          .box-content-preview {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
            .img {
              width: 160px;
              height: 90px;
              border-radius: 2px;
              background-color: #D9D9D9;
              object-fit: cover;
            }
          }
        }
      }
    }
    .share-box {
      padding: 20px;
      margin-bottom: 12px;
      background-color: #fff;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      .share-box-title {
        line-height: 24px;
        font-size: 20px;
        font-weight: 600;
        color: #000000cc;
        margin-bottom: 20px;
      }
      .share-box-content {
        flex: 1;
        overflow-y: auto;
        .share-box-content-item {
          padding: 28px 28px 24px 32px;
          border-radius: 6px;
          background: linear-gradient(90deg, #FFF -0.7%, #F8FBFF -0.69%, #FBFDFF 99.46%);
          display: flex;
          align-items: center;
          &:not(:last-child) {
            margin-bottom: 20px;
          }
          .share-box-content-item-preview {
            width: 48px;
            height: 48px;
            margin-right: 32px;
            flex-shrink: 0;
            cursor: pointer;
          }
          .share-box-content-item-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            .share-box-content-item-info-title {
              color: #000000cc;
              font-family: "PingFang SC";
              font-size: 16px;
              font-style: normal;
              font-weight: 600;
              line-height: 24px;
              display: flex;
              align-items: center;
              margin-bottom: 14px;
              .title {
                margin-right: 12px;
                cursor: pointer;
                &:hover {
                  color: #006FFF;
                }
              }
              .label {
                width: fit-content;
                display: flex;
                padding: 0 16px;
                gap: 8px;
                border-radius: 44px;
                background-color: #ECF2FE;
                color: #0052d9;
                font-family: "PingFang SC";
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;
              }
            }
            .share-box-content-item-info-desc {
              color: #00000066;
              font-family: "PingFang SC";
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
              letter-spacing: 0.24px;
              .link {
                color: #0052d9;
                font-family: "PingFang SC";
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;
                letter-spacing: 0.24px;
                text-decoration-line: underline;
                text-decoration-style: solid;
                text-decoration-skip-ink: none;
                text-decoration-thickness: auto;
                text-underline-offset: auto;
                text-underline-position: from-font;
                cursor: pointer;
                &:hover {
                  color: #006FFF;
                }
              }
            }
          }
          .share-box-content-item-arrow {
            width: 24px;
            height: 24px;
            margin-left: 24px;
            flex-shrink: 0;
            cursor: pointer;
          }
        }
        .disable {
          background: #FAFAFA;
          cursor: no-drop;
          pointer-events: none;
          .share-box-content-item-preview,
          .share-box-content-item-arrow {
            cursor: no-drop;
          }
          .share-box-content-item-info-title {
            .title {
              color: #00000066;
              &:hover {
                color: #00000066 !important;
                cursor: no-drop;
              }
            }
            .label {
              opacity: 0.6;
            }
          }
          .share-box-content-item-info-desc {
            color: #00000033;
            .link {
              color: #00000033 !important;
              &:hover {
                color: #00000033 !important;
                cursor: no-drop;
              }
            }
          }
        }
      }
    }
  }
</style>
