<template>
  <div class="online-course-page-container">
    <p class="page-title">我的在线课程</p>
    <div class="search-top">
      <div class="tab-box">
        <ul>
          <li 
          :class="{ 'active-tab': tabValue === item.value }"
          v-for="item in initTabList" 
          :key="item.value"
          @click="handleTab(item.value)"
          >
            <span>{{ item.label }}</span>
          </li>
        </ul>
      </div>
      <el-input class="creator-center-kwsearch" placeholder="标题关键词搜索" v-model="course_name" style="width:240px" @input="handleInput"></el-input>
    </div>
    <div class="main-table">
      <div class="sort-box">
        <span>排序：</span>
        <ul>
          <li 
          :class="{ 'active-sort': sortValue=== e.value }"
          v-for="e in sortList" 
          :key="e.value"
          @click="handleSort(e.value)"
          >
          {{ e.label }}
          </li>
        </ul>
      </div>
      <div class="card-list">
        <div class="item-card" v-for="v in tableData.records" :key="v.net_course_id">
          <ul>
            <li class="item-card-content">
              <div @click="handleComplete(v)" :class="[{'not-allow-click': ![0,1,null].includes(v.status)}, 'video-cover']">
                <el-image
                  lazy 
                  fit="fill"
                  :src="getItemImg(v)"
                >
                <div class="image-slot" slot="error">
                  <img :src="require('@/assets/img/default_bg_img.png')" alt="" />
                </div>
                </el-image>
                <div class="cover-timer" v-show="v.duration">
                  <span>
                    {{ transforTime(v.duration) }}
                  </span>
                </div>
              </div>
              <div class="text">
                <p class="video-title">
                  <span class="course-tag">网络课</span>
                  <span :class="[{'not-allow-click': ![0,1,null].includes(v.status)}, 'course-name']" @click="handleComplete(v)">{{ v.course_name }}</span>
                </p>
                <div class="bottom-icon">
                  <div class="bottom-left">
                    <span class="icon-box"> <span class="watch-icon icon"></span>{{transformUnit(v.view_count)}}</span>
                    <span class="icon-box"> <span class="like-icon icon"></span>{{transformUnit(v.praise_count)}}</span>
                    <span class="icon-box"> <span class="collect-icon icon"></span>{{transformUnit(v.fav_count)}}</span>
                    <span class="icon-box">{{v.updated_at}}</span>
                    <!-- 0-待审核；1-通过审核；2-审核不通过 -->
                    <span class="tool-tips-box" v-if='[0, 2].includes(v.info_sec_status)'>
                      <span  :class="['icon-box', v.info_sec_status === 0 ? 'deal-color' : 'no-pass-color']">{{ v.info_sec_status === 0 ? '待审核' : '审核不通过' }}</span>
                      <el-tooltip popper-class="safe-tool"  :content="secContent(v.info_sec_status)" placement="top-end">
                        <i class="el-icon-warning-outline"></i>
                      </el-tooltip>
                    </span>
                    <span style="margin-left: 16px" v-else>
                      <el-tooltip v-if='v.status === 5' effect="dark" :content="v.status_name" placement="bottom-start">
                        <span  class="icon-box deal-color">处理中<i class="el-icon-arrow-down el-icon--right"></i> </span>
                      </el-tooltip>
                      <span v-else :class="[getStatusColor(v.status),'icon-box']">{{v.status_name}}</span>
                    </span>
                    <el-tooltip effect="dark" :content="v.review_failed_reason" placement="bottom-start">
                      <i v-if='v.status === 8 && v.review_failed_reason' class="el-icon-warning-outline"></i>
                    </el-tooltip>
                  </div>
                  <!-- 首先 信安待审核-0 禁止 通过后 自己的状态 true禁止，false 不禁止 -->
                  <!-- 0-课程状态为待审核，(null, 1)-审核通过，2-直接显示审核不通过） -->
                  <div :class="[{'right-not-allow': delStatus(v)}, 'bottom-right']">
                    <el-tooltip v-if="!v.status"  class="item" effect="dark" content="发布" placement="top-start">
                      <span class="other-icon-box" @click="handleEdit(v)"> <span class="stop-publish-icon right-icon publish-icon"></span></span>
                    </el-tooltip>
                    <!-- 在用草稿审核未通过显示编辑 -->
                    <el-tooltip v-else  class="item" effect="dark" content="编辑" placement="top-start">
                      <!-- 信安审核不通过可以直接编辑 -->
                      <span :class="[{'disabled-edit-icon': ![2].includes(v.info_sec_status) && ![1, 3, 4, 9].includes(v.status)},'other-icon-box']" @click="handleEdit(v)"> <span class="stop-edit-icon right-icon edit-icon"></span></span>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="分享" placement="top-start">
                      <span :class="[{'disabled-share-icon': [2].includes(v.info_sec_status) || [3, 4, 5, 9].includes(v.status)},'other-icon-box']"  @click="handleShare(v)"> <span class="stop-share-icon right-icon share-icon"></span></span>
                    </el-tooltip>
                    <el-tooltip v-if="[3].includes(v.status)" class="item" effect="dark" content="上架" placement="top-start">
                      <span :class="[{'disabled-up-icon': [2].includes(v.info_sec_status) || [4, 5, 9].includes(v.status)},'other-icon-box']"  @click="handleStartUse(v)"> <span class="stop-up-icon right-icon up"></span></span>
                    </el-tooltip>
                    <el-tooltip v-else class="item" effect="dark" content="下架" placement="top-start">
                      <span :class="[{'disabled-down-icon': [2].includes(v.info_sec_status) || [3, 4, 5, 9].includes(v.status)},'other-icon-box']"  @click="handleStop(v.net_course_id)"> <span class="stop-down-icon right-icon down"></span></span>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="添加到课单" placement="top-start">
                      <span :class="[{'disabled-add-icon': [2].includes(v.info_sec_status) || [3, 4, 5, 9].includes(v.status)},'other-icon-box']"  @click="handleAddCourse(v)"> <span class="stop-add-icon right-icon add-circle"></span></span>
                    </el-tooltip>
                    <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
                      <span :class="[{'disabled-del-icon': ![0, 3, 4, 6, 8, 9].includes(v.status) && ![2].includes(v.info_sec_status)},'other-icon-box']"  @click="handleDelete(v)"> <span class="stop-del-icon right-icon del"></span></span>
                    </el-tooltip>
                    <!-- <el-dropdown placement="bottom-start">
                      <el-tooltip class="item" effect="dark" content="更多操作" placement="top-start">
                        <span :class="[{'not-use-status': [null, 6].includes(v.status)},'other-icon-box']"><span class="stop-other-icon right-icon other-icon"></span></span>
                      </el-tooltip>
                      <el-dropdown-menu class="online-course-dropdown" slot="dropdown">
                        <el-dropdown-item v-show="![0, 3, 4, 8, 9].includes(v.status)" @click.native="handleStop(v.net_course_id)">下架</el-dropdown-item>
                        <el-dropdown-item v-show="![0, 3, 4, 8, 9].includes(v.status)" @click.native="handleAddCourse(v)">添加到课单</el-dropdown-item>
                        <el-dropdown-item v-show="[3].includes(v.status)" @click.native="handleStartUse(v)">启用</el-dropdown-item>
                        <el-dropdown-item v-show="[0, 3, 4, 6, 8, 9].includes(v.status)" @click.native="handleDelete(v)">删除</el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown> -->
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <el-pagination
      v-if="tableData.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="current"
      :page-sizes="[5, 10, 20, 30, 50, 100]"
      :page-size="size"
      layout="total,  prev, pager, next, sizes, jumper"
      :total="tableData.total"
      >
      </el-pagination>
      <div class="empty" v-if = 'tableData.records.length === 0'>
        <span class="empty-img"></span>
        <div class="empty-text">暂无信息(内容)</div>
      </div>
    </div>
    <!-- 分享 -->
    <shareDialog :isShow.sync="shareDialog" ref="shareDialog"></shareDialog>
    <!-- 添加到课单 -->
    <addCourse :visible.sync="addCouseDialog" :itemData="addCourseData"></addCourse>
  </div>
</template>
<script>
import { getOnlineResult, getOnlineStatistics, stopCourse, startCourse, deleteCourse } from '@/config/api.conf.js'
import pager from '@/mixins/pager'
import { transformUnit, transforTime, debounce } from 'utils/tools'
import shareDialog from '@/views/components/shareDialog.vue'
import addCourse from '@/components/add-course-dialog'
import { mapState } from 'vuex'
import env from 'config/env.conf.js'
const tabInfo = {
  total_count: '',
  ready_count: 0,
  use_count: 1,
  destroy_count: 3,
  process_count: 5,
  draft_count: 4,
  not_pass_count: 8,
  failed_count: 9
}
export default {
  mixins: [pager],
  components: {
    shareDialog,
    addCourse
  },
  data() {
    return {
      course_name: '',
      tabValue: 'total_count',
      sortValue: 1,
      transformUnit,
      transforTime,
      shareDialog: false,
      addCouseDialog: false,
      addCourseData: {},
      iframeParams: {},
      initTabList: [],
      tabList: [
        { label: '全部', value: 'total_count' },
        { label: '待发布', value: 'ready_count' },
        { label: '审核未通过', value: 'not_pass_count' },
        { label: '在用', value: 'use_count' },
        { label: '下架', value: 'destroy_count' },
        { label: '处理中', value: 'process_count' },
        { label: '草稿', value: 'draft_count' },
        { label: '新增失败', value: 'failed_count' }
      ],
      sortList: [
        { label: '最近发布', value: 1 },
        { label: '浏览量', value: 2 },
        { label: '点赞量', value: 3 },
        { label: '收藏量', value: 4 },
        { label: '内容时长', value: 5 }
      ]
    }
  },
  computed: {
    ...mapState(['userInfo']),
    secContent() {
      return (val) => {
        return val === 0 ? '请留意企微“小腾老师”机器人消息提醒。如有疑问，可联系graywu。' : '提交信息包含敏感内容，请仔细仔细检查并重新修改后提交。如有疑问，可联系graywu。'
      }
    },
    delStatus() {
      return (v) => {
        if ([0].includes(v.info_sec_status)) { // 待审核禁止
          return true // 禁止
        }
        if (v.info_sec_status === 2) { // 审核不通过
          return false // 不禁止
        }
        if ([5, 6].includes(v.status)) {
          return true
        }
      }
    }
  },
  mounted() {
    this.onSearch(1)
    this.getStatistics()
  },
  methods: {
    onSearch(page_no) {
      const params = {
        page_no,
        page_size: this.size,
        status: tabInfo[this.tabValue],
        course_name: this.course_name,
        order_by_desc: this.sortValue
      }
      getOnlineResult(params).then((res) => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    getStatistics() {
      const list = JSON.parse(JSON.stringify(this.tabList))
      getOnlineStatistics().then((res) => {
        this.initTabList = list.map((e) => {
          for (let prep in res) {
            if (prep === e.value) {
              return {
                ...e,
                label: `${e.label}${res[prep]}`
              }
            }
          }
        })
      })
    },
    // 启用
    handleStartUse({ net_course_id }) {
      startCourse({ net_course_id, status: 1 }).then((res) => {
        this.$message.success('启用成功')
        this.onSearch(1)
        this.getStatistics()
      })
    },
    // 删除
    handleDelete({ net_course_id }) {
      deleteCourse([net_course_id]).then((res) => {
        this.$message.success('删除成功')
        this.onSearch(1)
        this.getStatistics()
      })
    },
    // 分享
    handleShare(v) {
      this.shareDialog = true
      const url = process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/b5GaSG' : 'http://s.test.yunassess.com/s/hoo9Gg'
      let userInfo = JSON.parse(sessionStorage.getItem('login_user'))
      this.$nextTick(() => {
        const href = `${url}?course_id=${v.net_course_id}&scheme_type=netcourse&share_staff_id=${userInfo.staff_id}&share_staff_name=${userInfo.staff_name}`
        this.$refs.shareDialog.initCode({
          url: href,
          scene: `${v.net_course_id}_${userInfo.staff_id}`,
          page: 'pages/networkCourse/video/videoDetail',
          customText: `【${v.course_name}】${href}`
        })
      })
    },
    // 编辑
    handleEdit({ course_type, net_course_id, status }) {
      // if (status === 1 || status === 3) { // 发布完成
      if (status) { // 发布完成
        if (['Video-ppt', 'Video-2d'].includes(course_type)) {
          const name = this.getPathName(course_type)
          if (name) {
            this.$router.push({
              name,
              query: { net_course_id }
            })
          }
        } else {
          this.$router.push({
            name: 'couserUpload',
            query: { net_course_id }
          })
        }
      // } else if (!status) { // 完成转码
      } else {
        this.$router.push({
          name: 'aiCoursePublish',
          query: { net_course_id }
        })
      }
      // else { // 2d ppt 直接上传
      //   const name = this.getPathName(course_type)
      //   if (name) {
      //     this.$router.push({
      //       name,
      //       query: { net_course_id }
      //     })
      //   }
      // }
    },
    // 发布完成
    handleComplete({ status, net_course_id }) {
      if (status === 1) {
        const { href } = this.$router.resolve({
          name: 'play',
          query: { course_id: net_course_id }
        })
        window.open(href, '_blank')
      } else if (!status) {
        this.$router.push({
          name: 'aiCoursePublish',
          query: { net_course_id }
        })
      }
    },
    getPathName(course_type) {
      switch (course_type) {
        case 'Video':
          return 'couserUpload'
        case 'Video-ppt':
          return 'pptCourse'
        case 'Video-2d':
          return '2DCourse'
        case 'Video-3d':
          return '2DCourse'
      }
    },
    handleTab(value) {
      this.tabValue = value
      this.onSearch(1)
    },
    
    handleSort(value) {
      this.sortValue = value
      this.onSearch(1)
    },
    handleInput: debounce(function () {
      this.onSearch(1)
    }, 500),
    // 下架
    
    handleStop(netCourseId) {
      this.$messageBox
        .prompt(
          '点击确认弹窗下架后课程将无法浏览，是否确认下架？',
          '下架',
          {
            closeOnClickModal: false,
            customClass: 'confirm-input-message-box',
            iconClass: 'el-icon-warning-outline',
            cancelButtonClass: 'el-button--default',
            confirmButtonClass: 'el-button--danger',
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            inputPlaceholder: '请输入“下架”确认此次操作',
            inputErrorMessage: '请输入“下架”',
            inputValidator: function (val) {
              return !!val && val.trim() === '下架'
            }
          }
        )
        .then(({ value }) => {
          if (value && value.trim() === '下架') {
            stopCourse(netCourseId).then((res) => {
              this.onSearch(1)
              this.getStatistics()
              this.$message.success('下架成功')
            })
          }
        })
    },

    // 添加到课单
    handleAddCourse(v) {
      const url = location.hostname.endsWith('.woa.com') ?
        `${process.env.VUE_APP_PORTAL_HOST_WOA}/training/netcourse/play?course_id=${v.net_course_id}` : 
        `${process.env.VUE_APP_PORTAL_HOST}/training/netcourse/play?course_id=${v.net_course_id}`
      this.addCourseData = {
        content_name: v.course_name,
        href: url,
        module_id: 1,
        module_name: '网络课',
        item_id: v.net_course_id,
        origin: location.origin
      }
      console.log('添加课单数据追钟', this.addCourseData)
      this.addCouseDialog = true
    },
    getStatusColor(value = 0) {
      // status 全部-为空 状态 0-待发布、1-在用、2-暂停、3-下架（出库）、4-草稿、5-处理中
      switch (value) {
        case 0 :
          return 'await-color' // 待发布 --- 完成转码
        case null:
          return 'await-color'
        case 1 :
          return 'use-color' // 在用 ---- 发布完成
        case 3 :
          return 'stop-color' // 下架
        // case 5 : // 处理中
        //   return 'deal-color'
      }
    },
    getItemImg({ photo_storage_type, photo_url, photo_id }) {
      // if (photo_storage_type === 'contentcenter' && photo_url) {
      //   const regex = /^https?:\/\//
      //   if (regex.test(photo_url)) {
      //     return photo_url
      //   } else {
      //     const envName = env[process.env.NODE_ENV]
      //     return `${envName.contentcenter}content-center/api/v1/content/imgage/${photo_url}/preview`
      //   }
      // }
      // return photo_id
      if (!photo_url && !photo_id) {
        return require('@/assets/img/default_bg_img.png')
      }
      const regex = /^https?:\/\//
      if (regex.test(photo_url)) {
        return photo_url
      } else {
        const envName = env[process.env.NODE_ENV]
        return `${envName.contentcenter}content-center/api/v1/content/imgage/${photo_id}/preview`
      }
    }
  }
}
</script>
<style lang="less">
.el-tooltip__popper.safe-tool {
  background: #fff;
  color: #D35A21;
  line-height: 16px;
  box-shadow: 0 3px 20px 2px #5d5d5d38;
  width: 352px;
  padding: 16px;
  .popper__arrow::after {
    border-top-color: #fff
  }
}
</style>
<style lang="less" scoped>
.online-course-page-container {
  background-color: #F6F7F9;
  font-size: 14px;
  .not-allow-click {
    cursor: not-allowed !important;
    pointer-events: none !important;
  }
  .page-title {
    color: rgba(0, 0, 0, 0.8);
    font-size: 16px;
    font-weight: 700;
    height: 24px;
    line-height: 24px;
  }

  .search-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
    margin-bottom: 12px;

    .tab-box {
      ul {
        display: flex;

        li {
          width: 100px;
          height: 32px;
          border: 1px solid rgba(238, 238, 238, 1);
          background: rgba(255, 255, 255, 1);
          border-radius: 4px;
          line-height: 32px;
          text-align: center;
          color: rgba(0, 0, 0, 0.6);
          font-size: 14px;
          cursor: pointer;
        }

        li+li {
          margin-left: 8px;
        }

        .active-tab {
          background: rgba(0, 82, 217, 1);
          color: #fff
        }
      }
    }
  }

  .main-table {
    background-color: #fff;
    border-radius: 4px;
    padding: 16px 28px;
    height: 1048px;
    .sort-box {
      display: flex;
      align-items: center;
      padding-bottom: 15px;
      border-bottom: 1px solid rgba(238, 238, 238, 1);

      span:first-of-type {
        color: rgba(0, 0, 0, 0.4);
      }

      ul {
        display: flex;
        margin-left: 8px;

        li {
          color: rgba(0, 0, 0, 0.6);
          font-size: 14px;
          height: 22px;
          line-height: 22px;
          cursor: pointer;
        }

        li+li {
          margin-left: 24px;
        }

        .active-sort {
          color: #0052D9
        }
      }
    }

    .card-list {
      max-height: 940px;
      overflow-y: auto;
      .item-card {
        border-bottom: 1px solid rgba(238, 238, 238, 1);
        padding: 10px 0;

        &-content {
          display: flex;

          .video-cover {
            width: 108px;
            height: 72px;
            margin-right: 12px;
            position: relative;
            cursor: pointer;
            .el-image,.image-slot img {
              width: 108px;
              height: 72px;
            }
            :deep(.el-image__inner) {
              border-radius: 2px;
            }
            .cover-timer {
              position: absolute;
              bottom: 0px;
              width: 108px;
              height: 24px;
              background: linear-gradient(180deg, rgba(217,217,217,0) 0%, rgba(51,51,51,1) 100%);
              text-align: right;
              line-height: 24px;
              span {
                color: rgba(255,255,255,1);
                font-size: 12px;
                height: 16px;
                line-height: 16px;
                margin-right: 4px;
              }
            }
          }

          .text {
            color: rgba(51, 51, 51, 1);
            flex: 1;
            .video-title {
              margin-bottom: 36px;
              display: flex;
              align-items: center;
              .course-tag {
                display: inline-block;
                font-size: 12px;
                height: 18px;
                line-height: 16px;
                border-radius: 2px;
                border: 1px solid rgba(0, 82, 217, 1);
                color: #0052D9;
                padding: 0 7px;
                margin-right: 8px;
              }
              .course-name {
                display: inline-block;
                height: 20px;
                line-height: 20px;
                word-break: break-all;
                cursor: pointer;
              }
            }

            .bottom-icon {
              display: flex;

              .bottom-left {
                display: flex;
                align-items: center;
                flex: 1;
                height: 16px;
                .tool-tips-box {
                  display: flex;
                  align-items: center;
                  margin-left: 16px;
                }
                .el-icon-warning-outline {
                  font-size: 16px;
                  color: #E34D59;
                  margin-left: 4px;
                }

                .icon-box {
                  display: flex;
                  align-items: center;
                  color: rgba(0, 0, 0, 0.4);
                  font-size: 12px;
                }

                .icon-box+.icon-box {
                  margin-left: 16px;
                }

                .icon {
                  width: 16px;
                  height: 16px;
                  display: inline-block;
                  margin-right: 3px;
                }

                .watch-icon {
                  background: url('~@/assets/img/watch.png') no-repeat center/cover;
                }

                .like-icon {
                  background: url('~@/assets/img/zan1.png') no-repeat center/cover;
                }

                .collect-icon {
                  background: url('~@/assets/img/fav2.png') no-repeat center/cover;
                }
                .await-color {
                  color: #FF8B6C
                }
                .use-color {
                  color: rgba(0,168,112,1)
                }
                .stop-color {
                  color: rgba(227,77,89,1)
                }
                .deal-color {
                  color:rgba(0,82,217,1);
                  cursor: pointer;
                }
                .no-pass-color {
                  color: #E34D59;
                }
              }

              .bottom-right {
                display: flex;
                align-items: center;
                height: 16px;

                span+span {
                  margin-left: 20px;
                }

                .el-dropdown {
                  margin-left: 20px;
                }

                .right-icon {
                  display: inline-block;
                  width: 16px;
                  height: 16px;
                }
                .other-icon-box {
                  width: 24px;
                  height: 24px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  border-radius: 50%;
                  cursor: pointer;

                  .edit-icon {
                    background: url('~@/assets/img/edit-allow.png') no-repeat center/cover;
                  }
                  .publish-icon {
                    background: url('~@/assets/img/publish.png') no-repeat center/cover;
                  }
                  .share-icon {
                    background: url('~@/assets/img/share.png') no-repeat center/cover;
                  }
                  .other-icon {
                    background: url('~@/assets/img/other-icon.png') no-repeat center/cover;
                  }
                  .down {
                    background: url('~@/assets/img/down.png') no-repeat center/cover;
                  }
                  .up {
                    background: url('~@/assets/img/up.png') no-repeat center/cover;
                  }
                  .add-circle {
                    background: url('~@/assets/img/add-circle.png') no-repeat center/cover;
                  }
                  .del {
                    background: url('~@/assets/img/del.png') no-repeat center/cover;
                  }
                }

                .other-icon-box:hover {
                  background: rgba(245, 247, 249, 1);

                  .edit-icon {
                    background: url('~@/assets/img/edit-icon-hover.png') no-repeat center/cover;
                  }
                  .publish-icon {
                    background: url('~@/assets/img/hover-publish.png') no-repeat center/cover;
                  }
                  .share-icon {
                    background: url('~@/assets/img/share-hover.png') no-repeat center/cover;
                  }
                  .other-icon {
                    background: url('~@/assets/img/other-icon-hover.png') no-repeat center/cover;
                  }
                  .down {
                    background: url('~@/assets/img/down-active.png') no-repeat center/cover;
                  }
                  .up {
                    background: url('~@/assets/img/up-active.png') no-repeat center/cover;
                  }
                  .add-circle {
                    background: url('~@/assets/img/add-circle-active.png') no-repeat center/cover;
                  }
                  .del {
                    background: url('~@/assets/img/del-active.png') no-repeat center/cover;
                  }
                }
              }
              .right-not-allow {
                cursor: not-allowed;
                pointer-events: none;
                .other-icon-box {
                  .stop-edit-icon {
                    background: url('~@/assets/img/edit.png') no-repeat center/cover;
                  }
                  .stop-publish-icon {
                    background: url('~@/assets/img/stop-publish.png') no-repeat center/cover;
                  }
                  .stop-share-icon {
                    background: url('~@/assets/img/stop-share.png') no-repeat center/cover;
                  }
                  .stop-other-icon {
                    background: url('~@/assets/img/other-stop.png') no-repeat center/cover;
                  }
                  .down {
                    background: url('~@/assets/img/down-1.png') no-repeat center/cover;
                  }
                  .up {
                    background: url('~@/assets/img/up-1.png') no-repeat center/cover;
                  }
                  .add-circle {
                    background: url('~@/assets/img/add-circle-1.png') no-repeat center/cover;
                  }
                  .del {
                    background: url('~@/assets/img/del-1.png') no-repeat center/cover;
                  }
                }
              }
              .not-use-status {
                cursor: not-allowed;
                pointer-events: none;
                .stop-other-icon {
                  background: url('~@/assets/img/other-stop.png') no-repeat center/cover !important;
                }
              }
              .disabled-share-icon {
                cursor: not-allowed;
                pointer-events: none;
                .stop-share-icon {
                  background: url('~@/assets/img/stop-share.png') no-repeat center/cover !important;
                }
              }
              .disabled-edit-icon {
                cursor: not-allowed;
                pointer-events: none;
                .stop-edit-icon {
                  background: url('~@/assets/img/edit.png') no-repeat center/cover !important;
                }
              }
              .disabled-down-icon {
                cursor: not-allowed;
                pointer-events: none;
                .stop-down-icon {
                  background:  url('~@/assets/img/down-1.png') no-repeat center/cover !important;
                }
              }
              .disabled-up-icon {
                cursor: not-allowed;
                pointer-events: none;
                .stop-up-icon {
                  background:  url('~@/assets/img/up-1.png') no-repeat center/cover !important;
                }
              }
              .disabled-add-icon {
                cursor: not-allowed;
                pointer-events: none;
                .stop-add-icon {
                  background:  url('~@/assets/img/add-circle-1.png') no-repeat center/cover !important;
                }
              }
              .disabled-del-icon {
                cursor: not-allowed;
                pointer-events: none;
                .stop-del-icon {
                  background:  url('~@/assets/img/del-1.png') no-repeat center/cover !important;
                }
              }
            }
          }
        }
      }

      .item-card:last-of-type {
        border-bottom: 1px solid rgba(238, 238, 238, 1);
      }

      .item-card:hover {
        background: rgba(247, 251, 255, 0.5);
      }
    }
  }

  .empty {
    text-align: center;
    margin-top: 140px;

    .empty-text {
      margin-top: 17px;
      color: rgba(0, 0, 0, 1);
      font-size: 16px;
      font-weight: 500;
    }

    .empty-img {
      display: inline-block;
      width: 160px;
      height: 160px;
      background: url(~@/assets/img/empty.png) no-repeat center/contain;
    }
  }
}
</style>
