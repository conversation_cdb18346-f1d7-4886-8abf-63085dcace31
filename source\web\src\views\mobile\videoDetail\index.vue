<template>
  <div class="video-container">
    <div
      id="main-area"
      :class="['main-fixed',{ 'h100vh' : isVertical }, { 'flex-box': isCourse && lessonExpand }]"
    >
      <!-- <van-nav-bar
        :title="courseData.course_name"
        :fixed="false"
        :border="false"
      /> -->
      <Scorm
        v-if="['Flash', 'Scorm', 'Doc'].includes(courseData.course_type)"
        :class="[{'fullScreen-video-box': isFullscreen}, 'scorm-box']"
        :content_id="courseData.content_id"
        :courseData="courseData"
        @handleScormRecord="handleScormRecord"
      />
      <div class="play-video" :class="{'hidden-progress-control': !courseData.limit_progress_bar && isFullscreen }" v-else-if="['Video', 'Video-2d', 'Video-3d', 'Video-ppt', 'Audio'].includes(courseData.course_type)">
        <!-- {'fullScreen-video-box': isFullscreen}, { 'full-vertical-box': isFullscreen }, -->
        <Video
          :class="['video-box']"
          ref="videoRef"
          :content_id.sync="courseData.content_id"
          @getCurCaption="getCurCaption"
          @handleRecord="handleRecord"
          @getCurrentTime="getCurrentTime"
          @onPlay="onPlay"
          @onEnded="onEnded"
          @fullscreenchange="fullscreenchange"
          :playTime="playTime"
          :volumePanel="false"
          :autoPlay="false"
          :vertical="isVertical || isInteractive"
          :fullscreenToggle="!isInteractive"
          :progressControl="!courseData.limit_progress_bar"
          :source_src="courseData.file_url"
          :playbackRateMenuButton="!courseData.enable_interactive"
        >
        </Video>

        <!-- 进入假全屏时进度条 -->
        <van-slider v-if="!courseData.limit_progress_bar && isFullscreen" class="video-progress" :class="{'progress-animation': isShowProgress}" v-model="videoProgress" :min="0" :max="videoProgressMaxValue" vertical @change="progressChange" />

        <!-- 全屏退出按钮 -->
        <!-- <button class="exit-btn" v-if="!isShowFullScreenBtn"></button> -->
        <div
        v-show="isCurrentTimeShow && playTime > 0"
        :class="[
          {'en-vertical-dialog': (isVertical || isInteractive) && moocLang === 'en-us' },
          { 'vertical-dialog': (isVertical || isInteractive) && moocLang === 'zh-cn'  },
          {'en-current-time-tips': moocLang === 'en-us' && !isVertical && !isInteractive},
          {'current-time-tips': moocLang === 'zh-cn' && !isVertical && !isInteractive},
          'common-time-tips'
          ]"
        >
          <div>
            <van-icon name="cross" @click="isCurrentTimeShow = false" />
            <span>{{ $langue('NetCourse_PlayAt', {seconds: playTime, defaultText: `上次播放至${playTime || 0}秒` }) }}&nbsp;</span>
          </div>
          <span class="tips-btn" @click="toCurrentTime">{{ $langue('NetCourse_ClickGo', { defaultText: '点击跳转' }) }}</span>
        </div>
      </div>
      <div v-else class="video-box">
        <van-image
          lazy
          fit="fill"
          :src="
            courseData.photo_url
              ? courseData.photo_url
              : require('@/assets/img/default_bg_img.png')
          "
        >
        </van-image>
      </div>
      <div class="tabs-card" v-show="!lessonExpand && !isFullscreen">
        <van-tabs
          v-model="activeKey"
          color="#0052D9"
          title-active-color="#0052D9"
          @click="changeTabs"
        >
          <van-tab
            v-for="item in tabList"
            :key="item.key"
            :name="item.key"
          >
            <template #title>
              <span
                :dt-eid="dtContent(item, 'eid')"
                :dt-remark="dtContent(item, 'remark')"
              >{{ $langue(item.title, { defaultText: item.text }) }}</span>
            </template>
          </van-tab>
        </van-tabs>
      </div>
    </div>
    <div
      :class="[{ 'fixed-main-content':  activeKey === 'text' || activeKey === 'des' || activeKey === 'comment' || activeKey === 'extand'}, 'main-content']"
    >
      <!-- 简介 -->
      <desContent
        v-show="activeKey === 'des'"
        :courseData.sync="courseData"
        @toComment="activeKey = 'comment'"
        :isPreview="isPreview"
      ></desContent>
      <!-- 章节 -->
      <chapterInfo v-show="activeKey === 'chapter'"
       ref="chapterInfoRef"
       :isInteractive="isInteractive"
       :chapterSummaryList="chapterSummaryList"
       :duration="duration"
       @changePlaytime="changePlaytime"
       :chapterData="chapterData"
      >
      </chapterInfo>
      <!-- 文稿 -->
      <textDraft
        v-show="activeKey === 'text' && captionData && captionData.length"
        ref="textDraftRef"
        :captionData.sync="captionData"
        @toCaption="toCaption"
      >
      </textDraft>
      <!-- 笔记 -->
      <notes v-if="activeKey === 'note'" :courseData="courseData"></notes>
      <sdc-comment-mob
        v-if="activeKey === 'comment' && commentParams"
        :params="commentParams"
      ></sdc-comment-mob>
      <!-- 延伸学习 -->
      <div v-if="activeKey === 'extand'" class="extent-tab">
        <div v-if="loading" class="loading-text">
          {{ $langue('Mooc_Common_Alert_Loading', { defaultText: '正在加载中' }) }}...
        </div>
        <div v-else-if="extandList.length === 0" class="video-learn-empty">
          <img src="@/assets/img/mobile/empty-note.png" alt="" />
          <div class="empty-text">{{ $langue('NetCourse_NoCourse', { defaultText: '暂无相关课程' }) }}～</div>
        </div>
        <div v-else class="more-learn">
          <CategoryCard
            class="content-inner"
            v-for="(item, index) in extandList"
            :detailsInfo="item"
            :key="index"
          />
        </div>
      </div>
    </div>
    <!-- 听音频 -->
    <div id="drag-service" v-show="audioUrl && !lessonExpand && !isVertical && !isInteractive">
      <span class="icon"></span>
      <span>{{ $langue('NetCourse_ListenVideo', { defaultText: '听视频' }) }}</span>
    </div>
    <!-- 双语按钮 -->
    <!-- 双语暂时注释 -->
    <!-- <div
      v-if="!isFormMooc"
      id="drag-lang"
      :class="['drag-lang', { 'lang-icon-vertical': (isVertical || isInteractive) }]"
    >
      <span
        :class="[moocLang === 'en-us' ? 'el-icon-zh' : 'el-icon-en', 'icon']"
      ></span>
      <span class="text">{{ moocLang === 'en-us' ? '中文' : 'Eng' }}</span>
    </div> -->
    <!-- 横屏提示 -->
    <van-popup
      class="vertical-popup"
      v-model="verticalTips"
      :close-on-click-overlay="false"
    >
      <div class="bg">
        <img class="vertical" src="@/assets/img/mobile/vertical.png" alt="" />
      </div>
      <div class="bottom">
        <div class="text">
          {{ $langue('NetCourse_SupportHorizontalScreen', { defaultText: '当前内容为互动课程，仅支持横屏播放' }) }}
        </div>
        <div class="text">{{ $langue('NetCourse_HorizontalPhone', { defaultText: '请横置手机观看' }) }}</div>
        <span class="confirm" @click="verticalClose">{{$langue('NetCourse_Ok', { defaultText: '好的' })}}</span>
      </div>
    </van-popup>
    <!-- 横屏互动 -->
    <van-popup
      class="interactive-popup"
      v-if="showInteractive"
      v-model="showInteractive"
      :close-on-click-overlay="false"
    >
      <div class="content">
        <div class="title">{{ questionData?.title || '' }}</div>
        <div
          class="desc"
          v-if="questionData?.introduction"
          v-html="questionData?.introduction"
        ></div>
        <div
          class="question"
          v-for="(item, index) in questionData?.select_content"
          :key="item.question_id"
        >
          <!-- <div class="question-name" v-html="item.question_text"></div> -->
          <van-radio-group
            v-if="
              item?.choose_type_config?.type === 'single' ||
              (item?.active_type === 'vote' &&
                item?.vote_type_config?.can_max_vote_nums === 1)
            "
            v-model="selected[index]"
            checked-color="#0052D9"
          >
            <van-radio
              v-for="(option, index) in item.options"
              :key="index"
              :name="option.option_value"
              >{{ option.option_text }}</van-radio
            >
          </van-radio-group>
          <van-checkbox-group v-model="selected[index]" v-else>
            <van-checkbox
              v-for="(option, optionIndex) in item.options"
              :key="optionIndex"
              :name="option.option_value"
              shape="square"
              >{{ option.option_text }}</van-checkbox
            >
          </van-checkbox-group>
        </div>
      </div>
      <div class="bottom">
        <span
          :class="['continue', { 'continue-disabled': continueDisabled }]"
          @click="onContinue"
          >{{ $langue('Mooc_Common_Alert_ContinueStudy', { defaultText: '继续学习' }) }}</span
        >
      </div>
    </van-popup>
  </div>
</template>
<script>
// import Video from '../components/mobileVideo.vue'
import { Video, Scorm } from '@/components/index'
import {
  getNetCourseInfo,
  getExtanContentList,
  netViewRecord,
  getNetCourseChapterList,
  getCoursePreviewInfo,
  targetAB
} from 'config/api.conf'
import {
  urlForDownloadApi,
  saveInteractionRecord,
  getCourseInteraction
} from 'config/mooc.api.conf'
import desContent from './desContent.vue'
import chapterInfo from './chapterInfo.vue'
import textDraft from './textDraft.vue'
import notes from './notes.vue'
import CategoryCard from '../components/CategoryCard.vue'
// import lotteryDrawPop from './child/lotteryDrawPop.vue'
import axios from 'axios'
import MoocJs from 'sdc-moocjs-integrator'
import env from 'config/env.conf.js'
import { Toast } from 'vant'
import { mapState } from 'vuex'
import translateLang from '@/views/mobile/mixins/translateLang.js'
import { pageExposure } from '@/utils/tools.js'

const envName = env[process.env.NODE_ENV]
export default {
  mixins: [translateLang],
  components: {
    Video,
    Scorm,
    desContent,
    chapterInfo,
    textDraft,
    notes,
    // lotteryDrawPop,
    CategoryCard
  },
  data() {
    return {
      isPreview: false, // 是否是预览
      videoProgress: 0, // 假全屏时的进度条
      videoProgressMaxValue: 0, // 假全屏时的进度条最大值
      isShowProgress: true, // 是否在样式上显示进度条
      initTop: 0,
      activeKey: 'des',
      courseData: {
        content_type: '',
        comment_count: 0,
        fav_count: 0
      },
      chapterSummaryList: [], // 章节列表
      captionData: [],
      countTimer: null,
      viewTimer: null,
      studyRecordQuery: {
        act_id: this.$route.query.course_id,
        area_id: this.$route.query.area_id || '',
        from: this.$route.query.jump_from || this.$route.query.from || '',
        learn_record_id: 0,
        is_finish: 0,
        is_review: 0,
        total_study_time: 0,
        my_study_progress: '',
        course_duration: 0
      },
      duration: 0,
      playTime: 0,
      extandList: [],
      loading: false,
      commentParams: null,
      lessonExpand: false,
      isInteractive: false, // 是否开启互动
      isVertical: false, // 是否横屏
      // isShowFullScreenBtn: true, // 是否显示全屏按钮
      verticalTips: false,
      showInteractive: false,
      selected: [],
      mainAreaHeight: 304,
      interactionData: {},
      continueDisabled: true,
      questionData: {},
      audioUrl: null,
      interactionTime: -1, // 互动弹窗的时间，禁止跨过互动
      isPageHidden: false, // 是否熄屏
      lastTime: 0, // 上一次播放的时间，禁止快进快退
      lastHiddenTime: 0, // 上次熄屏的播放时间，禁止熄屏后快进快退
      tabList: [
        { title: 'Mooc_TaskDetail_Audio_Description', key: 'des', text: '简介' },
        { title: 'Mooc_TaskDetail_Audio_Chapter', key: 'chapter', text: '章节' },
        // { title: 'NetCourse_TextContent', key: 'note', text: '文稿' },
        { title: 'NetCourse_Note', key: 'note', text: '笔记' },
        // { title: 'NetCourse_Comment', key: 'comment', text: '讨论' },
        { title: 'NetCourse_Extended', key: 'extand', text: '延伸学习' }
      ],
      // jumpShow: false,
      isFullscreen: false,
      isCurrentTimeShow: true,
      chapterData: {
        chapter_content_list: [],
        chapter_ai_content_list: []
      }
    }
  },
  computed: {
    ...mapState(['moocLang']),
    course_id() {
      return this.$route.query.course_id || ''
    },
    shareStaffId() {
      return this.$route.query.share_staff_id || ''
    },
    isFormMooc() {
      return (
        this.$route.query.from === 'mooc' || this.$route.query.from === 'spoc'
      )
    },
    isCourse() {
      return (
        (this.$route.query.jump_from === 'CourseList' ||
          this.$route.query.from === 'CourseList') &&
        this.$route.query.area_id
      )
    },
    mainContentStyle() {
      return {
        top:
          this.isCourse && this.activeKey === 'text'
            ? this.mainAreaHeight + 'px'
            : '',
        marginTop:
          this.isCourse && this.activeKey !== 'text'
            ? this.mainAreaHeight + 'px'
            : ''
      }
    },
    dtContent () {
      return (item, type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.questionData?.title, // 任务名称
            page_type: '网课详情页',
            container: '', // 板块的名称
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: item.text,
            terminal: 'H5'
          })
        } else {
          let { mooc_course_id, task_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_${task_id}_${item.key}`
        }
      }
    }
  },
  beforeRouteEnter(to, from, next) {
    // if (window.top === window.self) { // 网络课
    // } else {
    //   next()
    // }
    targetAB({ userAgent: 'mobile' }).then((res) => {
      if (res) {
        next((vm) => {
          // 通过 `vm` 访问组件实例
          vm.$router.replace({
            name: 'grayPlay',
            query: {
              ...to.query
            }
          })
        })
      } else { // mooc
        next()
      }
    })
  },
  created() {
    try {
      this.isPreview = JSON.parse(this.$route.query.is_preview)
      if (this.isPreview) {
        this.tabList = [{ title: 'Mooc_TaskDetail_Audio_Description', key: 'des', text: '简介' }]
      }
    } catch (error) {
      this.isPreview = false
    }
    // 添加message事件监听
    window.addEventListener('visibilitychange', this.visibilitychange, false)
    window.addEventListener('beforeunload', this.beforeunloadEvent, false)
    console.log('????????????????????????????')
  },
  mounted() {
    if (this.isFormMooc) {
      MoocJs.setPause(() => {
        this.$refs.videoRef.vedioPlayer.pause()
      })
      MoocJs.setPlay(() => {
        this.$refs.videoRef.vedioPlayer.play()
      })
    }
    // 先初始化双语，再弹窗，避免双语没有加载出来
    this.$nextTick(() => {
      this.initDrag()
      this.initLang()
    })

    if (this.isPreview) {
      this.getPreviewInfo()
      return
    }

    this.getCourseDetail().then((e) => {
      // 任务已完成时，开启任务同步弹窗
      console.log('任务已完成数据', this.courseData)
      if (this.courseData.is_finish * 1 === 1 && this.isFormMooc) {
        MoocJs.complete('init')
      }
    })
    MoocJs.messageListener((res) => {
      if (res.events === 'tencent-mooc-lang') {
        this.$store.commit('setMoocLang', res.params)
        this.getMobileLangJS()
      }
    })
    if (this.$route.query.mini && this.$route.query.history) {
      window.addEventListener('popstate', async () => {
        // 为了避免只调用一次，再次调用一次
        this.pushHistory(document.title, location.href)
        this.beforeunloadEvent()
        // 调用微信的返回事件
        window.parent &&
          window.parent.postMessage({
            data: 'navigateBack'
          })
      })
      this.pushHistory(document.title, location.href)
    }
    const { targetTime } = this.$route.query
    if (targetTime > -1) {
      // 如果带有章节时间点参数，定位到对应的章节时间点
      this.activeKey = 'chapter'
      this.getChapterList()
      this.playTime = targetTime * 1
      this.$nextTick(() => {
        if (this.$refs.chapterInfoRef) {
          this.$refs.chapterInfoRef.slidToContent(this.playTime)
        }
        this.toCurrentTime()
      })
    }
  },
  beforeDestroy() {
    this.viewTimer = null
    this.countTimer = null
    clearInterval(this.viewTimer)
    clearInterval(this.countTimer)
    MoocJs.removeEvent()
    // 移除监听
    window.removeEventListener('visibilitychange', this.visibilitychange)
    window.removeEventListener('beforeunload', this.beforeunloadEvent)
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          const hostUrl =
            process.env.NODE_ENV === 'development'
              ? process.env.VUE_APP_PORTAL_HOST_WOA
              : window.origin
          this.commentParams = {
            userName: val.staff_name,
            actId: this.course_id,
            appId: 'A9BiosXihR0h46ThNsAX',
            urlConfig: {
              getComments: `${hostUrl}/training/api/netcourse/user/course-comment/get_comments`,
              addComment: `${hostUrl}/training/api/netcourse/user/course-comment/add`,
              deleteComment: `${hostUrl}/training/api/netcourse/user/course-comment/delete/`,
              like: `${hostUrl}/training/api/netcourse/user/course-comment/praised`,
              sticky: `${hostUrl}/training/api/netcourse/user/course-comment/sticky`,
              show: `${hostUrl}/training/api/netcourse/user/course-comment/show`
            }
          }
        }
      },
      immediate: true
    },
    selected: {
      handler(val) {
        // 判断是否可以继续学习
        let res = false
        const data = this.questionData?.select_content
        for (let i = 0; i < val.length; i++) {
          // 单选
          if (typeof val[i] === 'string') {
            // 选了，并且是正确答案
            data[i].correct_answer = data[i].correct_answer || ''
            const answer =
              typeof data[i].correct_answer === 'string'
                ? data[i].correct_answer
                : data[i].correct_answer[0]
            // 选了，并且是正确答案
            if (
              val[i] === '' ||
              (data[i].choose_type_config?.completion_conditions ===
                'correct' &&
                answer !== val[i])
            ) {
              res = true
              break
            }
            // 多选
          } else {
            // 选了，并且，如果是投票不超过最大数量限制
            if (
              val[i].length === 0 ||
              (data[i].active_type === 'vote' &&
                data[i]?.vote_type_config?.can_max_vote_nums < val[i].length)
            ) {
              res = true
              break
              // 选了，并且是正确答案
            } else if (
              data[i].choose_type_config?.completion_conditions === 'correct'
            ) {
              const answer = JSON.parse(JSON.stringify(data[i].correct_answer))
              answer.sort((a, b) => a * 1 - b * 1)
              const select = JSON.parse(JSON.stringify(val[i]))
              select.sort((a, b) => a * 1 - b * 1)
              if (answer.join(',') !== select.join(',')) {
                res = true
                break
              }
            }
          }
        }
        this.continueDisabled = res
      },
      deep: true
    }
  },
  methods: {
    // 获取课程实时预览信息
    getPreviewInfo() {
      getCoursePreviewInfo(this.course_id).then(res => {
        document.title = res.course_name
        this.courseData = res
        this.courseData.labels = res.course_labels
      }).catch(err => {
        console.log('err: ', err)
      })
    },
    // 假全屏时的进度条
    progressChange(value) {
      this.$refs.videoRef.vedioPlayer.currentTime(value)
    },
    // 获取网络课章节列表
    getChapterList() {
      getNetCourseChapterList(this.course_id).then((res) => {
        const { chapter_ai_content_list, chapter_content_list } = res
        const chapterList = chapter_content_list?.length ? chapter_content_list : chapter_ai_content_list?.length ? chapter_ai_content_list : []
        this.chapterData = res
        // console.log(res, '获取网络课章节列表-------')
        this.chapterSummaryList = chapterList.map(item => {
          let { minutes, seconds } = this.secondsToMinutes(item.chapter_time_point)
          let chapter_time = minutes + ':' + seconds
          const url = item.chapter_cover_content_id ? `${envName.contentcenter}content-center/api/v1/content/imgage/${item.chapter_cover_content_id}/preview` : item.chapter_cover_url
          return {
            ...item,
            chapter_content: item.chapter_content ? item.chapter_content.replace(/(\\r\\n|\\n|\n|\r\n)+/g, '<br>') : '',
            chapter_time,
            imgUrl: url
          }
        })
      })
    },
    secondsToMinutes(seconds) {
      let minutes = Math.floor(seconds / 60) // 计算分钟
      let remainingSeconds = seconds % 60 // 计算剩余秒数
      remainingSeconds = remainingSeconds.toString().padStart(2, '0')
      return {
        minutes: minutes,
        seconds: remainingSeconds
      }
    },
    getRouterQuery() {
      let { mooc_course_id, taskId } = this.$route.query
      return {
        mooc_course_id: mooc_course_id || '',
        task_id: taskId || ''
      }
    },
    // 跳转至对应的播放时间
    toCurrentTime() {
      this.isCurrentTimeShow = false
      this.lastTime = this.playTime

      const u = navigator.userAgent
      const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)

      this.$nextTick(() => {
        if (isiOS) {
          this.$refs.videoRef.vedioPlayer.play()
        }
        this.$refs.videoRef.vedioPlayer.currentTime(this.playTime)
        this.videoProgress = this.playTime
        this.$refs.videoRef.vedioPlayer.play()
      })
    },
    // 章节改变当前时间节点
    changePlaytime(chapter_time) {
      // if (this.isInteractive) { // 互动已全屏，点不到章节
      //   Toast('已开启互动，不可跳转进度')
      //   return
      // }
      this.$nextTick(() => {
        this.playTime = chapter_time
        this.$refs.videoRef.vedioPlayer.currentTime(this.playTime)
        this.$refs.videoRef.vedioPlayer.play()
      })
    },
    // 插入浏览器历史
    pushHistory(title = 'title', url = '#') {
      let state = {
        title,
        url
      }
      window.history.pushState(state, state.title, state.url)
    },
    beforeunloadEvent() {
      // 离开当前页面学习记录归档
      if (!this.studyRecordQuery.total_study_time) return
      const params = {
        ...this.studyRecordQuery,
        is_archive: true
      }
      let blob = new Blob([JSON.stringify(params)], {
        type: 'application/json; charset=UTF-8'
      })
      navigator.sendBeacon(
        '/training/api/netcourse/user/courseinfo/add-study-record',
        blob
      )
    },
    // 如果之前熄屏了，则回到熄屏之前的播放时间
    visibilitychange() {
      // 熄屏上报一次
      if (document.hidden) {
        this.beforeunloadEvent()
      }
      if (
        this.courseData.enable_interactive &&
        this.courseData.limit_progress_bar
      ) {
        if (document.hidden) {
          this.isPageHidden = true
          this.$refs.videoRef.vedioPlayer.pause()
        } else {
          this.$refs.videoRef.vedioPlayer.currentTime(this.lastHiddenTime)
          this.isPageHidden = false
        }
      }
    },
    getCurCaption(data) {
      if (this.isPreview) return
      if (!this.captionData.length > 0) {
        this.captionData = data
        if (
          this.captionData &&
          this.captionData.length &&
          this.tabList.length < 5 && this.courseData.show_comments !== false
        ) {
          this.tabList.splice(2, 0, {
            title: 'NetCourse_TextContent',
            key: 'text',
            text: '文稿'
          })
          this.$nextTick(() => {
            this.activeKey = 'text'
          })
        }
      }
    },
    handleRecord(param) {
      if (param.evt === 'loadedmetadata') {
        // 视频加载完成后拿到播放时长
        if (param.duration) {
          this.duration = Math.floor(param.duration)
        }
        // 防止更换视频，过滤多余的章节时间节点
        if (this.duration) {
          this.chapterSummaryList = this.chapterSummaryList.filter(its => {
            return its.chapter_time_point <= this.duration
          })
        }
      }
      let status = ''
      if (param.evt === 'play') {
        this.studyRecordQuery.course_duration = Math.floor(param.duration)
        // 重新学习，重置数据
        if (this.studyRecordQuery.is_finish === 1) {
          this.studyRecordQuery.is_finish = 0
          this.studyRecordQuery.is_review = 1 // 重播
          this.studyRecordQuery.total_study_time = 0
          this.learnRecordId = 0
        }
        if (!this.countTimer) {
          this.creatViewTimer()
        }
        if (this.isFormMooc) {
          MoocJs.play()
        }
      }

      if (param.evt === 'pause' || param.evt === 'ended') {
        // 息屏状态不更新课程状态
        if (this.isPageHidden) return

        if (param.evt === 'ended') {
          // 学习完
          this.studyRecordQuery.is_finish = 1
          status = 'ended'
        }

        // 延迟1秒调用方法，（视频播放完成会先触发pause，再触发ended），解决同时触发pause、ended情况
        if (this.viewRecordTime) clearTimeout(this.viewRecordTime)
        this.viewRecordTime = setTimeout(() => {
          this.viewRecord(status)
        }, 1000)
        clearInterval(this.countTimer)
        this.countTimer = null

        if (param.evt === 'pause' && this.isFormMooc) {
          MoocJs.pause()
        } else if (param.evt === 'ended' && this.isFormMooc) {
          MoocJs.complete()
        }
      }
    },
    creatViewTimer() {
      let _this = this
      let durtation = 0
      let { course_duration } = this.studyRecordQuery
      this.countTimer = setInterval(function () {
        let { my_study_progress, is_finish } = _this.studyRecordQuery
        _this.studyRecordQuery.total_study_time++
        durtation++
        if (durtation % 15 === 0) {
          _this.viewRecord() // 浏览器时长需每15秒记录一次
        }

        // 比视频时长提前10秒触发完成上报
        if (course_duration >= 60 && my_study_progress + 10 >= course_duration && !is_finish) {
          _this.studyRecordQuery.is_finish = 1
          _this.viewRecord()
          clearInterval(_this.countTimer)
          _this.countTimer = null
          if (_this.isFormMooc) {
            MoocJs.complete()
          }
        }
      }, 1000)
    },
    // 学习上报
    viewRecord(status) {
      const { moocPreview } = this.$route.query
      if (!this.studyRecordQuery.total_study_time || moocPreview * 1 === 1) return
      this.studyRecordQuery.learn_record_id = this.learnRecordId
      netViewRecord(this.studyRecordQuery).then((data) => {
        if (data) {
          if (status === 'ended') {
            this.learnRecordId = 0
          } else {
            this.learnRecordId = data
          }
        }
      })
    },
    getCourseDetail(isChangeTab) {
      const { cl_id } = this.$route.query
      const FUNC = getNetCourseInfo({
        act_id: this.course_id,
        share_staff_id: this.shareStaffId || ''
      })
        .then((data) => {
          // 详情页曝光上报
          pageExposure({
            page_type: '移动端网络课详情页',
            content_type: '网络课',
            act_type: '2',
            content_name: data.course_name,
            content_id: this.course_id,
            terminal: 'H5'
          })
          if (data.support_mobile !== 1) {
            let link =
              process.env.NODE_ENV === 'production'
                ? 'https://sdc.qq.com/s/b5GaSG'
                : 'http://s.test.yunassess.com/s/hoo9Gg'
            this.$router.replace({
              name: 'mobileError',
              query: {
                type: 2,
                href: encodeURIComponent(`${link}?course_id=${this.course_id}`)
              }
            })
            if (this.isFormMooc) {
              MoocJs.sendErrorInfo(this.$langue('Mooc_TaskDetail_ContentNotSupportedPC1', { defaultText: '很抱歉，当前内容暂不支持移动端访问' }))
            }
            return
          }
          // 假全屏进度条最大限制赋值
          try {
            this.videoProgressMaxValue = data.duration || data.est_dur * 60
          } catch (error) {
            this.videoProgressMaxValue = 0
          }
          let cre = data.created_at.split(':')
          cre = [cre[0], cre[1]]
          cre = cre.join(':')
          data.created_at = cre
          document.title = data.course_name
          this.courseData = data
          const index = this.tabList.findIndex((e) => e.title === 'NetCourse_Comment')
          if (this.courseData.show_comments !== false && index === -1) {
            this.tabList.splice(3, 0, {
              title: 'NetCourse_Comment',
              key: 'comment',
              text: '讨论'
            })
          }
          this.courseData_brief = JSON.parse(JSON.stringify(data))
          this.courseData_brief.created_at =
          this.courseData_brief.created_at.split(' ')[0]
          if (!this.playTime) this.playTime = data.my_study_progress
          // 是否开启互动
          if (data.enable_interactive) {
            window.parent &&
              window.parent.postMessage({
                data: 'vertical'
              })
            this.getInteraction()
            this.isInteractive = true
            // 互动开启全屏
            this.isFullscreen = true
            this.verticalTips = true
          } else {
            // 续播
            if (data.my_study_progress > 0 && !isChangeTab) {
              if (this.playTime) {
                this.isCurrentTimeShow = true
                // let curTimeShouTimer = setTimeout(() => {
                //   this.isCurrentTimeShow = false
                // }, 15000)
                // this.$once('hook:beforeDestroy', () => {
                //   clearTimeout(curTimeShouTimer)
                //   curTimeShouTimer = null
                // })
              }
            }
          }

          const src =
            process.env.NODE_ENV === 'production'
              ? `https://learn.woa.com/mobilenet/net?act_id=${this.course_id}`
              : `https://test-learn.woa.com/mobilenet/net?act_id=${this.course_id}`
          // 兼容处理，如果content_id没有跳转到v8
          if (!data?.content_id && data?.cl_id) {
            // 网课
            const url = `${src}&jump_from=CourseList&project=0&source=ql&from=CourseList&area_id=${cl_id}`
            window.location.replace = url
          } else if (!data?.content_id) {
            // 网络课移动化
            window.location.replace = src
          }
          data.content_id && this.getOriginUrl(data.content_id)
          if (data.captions?.length > 0) this.readCaptionFile(data.captions)
        })
        .catch((err) => {
          if (err.code) {
            if (this.isFormMooc && (err.code === 403 || err.code === 500)) {
              MoocJs.sendErrorInfo(err.message)
              return
            }
            let type = 0
            if (err.code === 403) {
              type = 3
              if (err.message.includes('权限')) {
                type = 5
              }
            } else if (err.code === 500) {
              type = 5
            }
            this.$router.replace({
              name: 'mobileError',
              query: {
                type
              }
            })
          }
        })
      return FUNC
    },
    // 点击跳转确定
    // handlejumpConfirm() {
    //   this.lastTime = this.playTime
    //   this.$refs.videoRef.vedioPlayer.currentTime(this.playTime)
    //   this.$refs.videoRef.vedioPlayer.play()
    // },
    // 跳转到上次播放时间
    // lastPlayTime(my_study_progress) {
    //   Dialog.alert({
    //     className: (this.isInteractive ? 'vertical-dialog ' : '') + 'task-dialog',
    //     confirmButtonText: this.$langue('NetCourse_ClickGo', { defaultText: '点击跳转' }),
    //     confirmButtonColor: '#0052D9',
    //     cancelButtonText: this.$langue('Mooc_Common_Alert_Cancel', { defaultText: '取消' }),
    //     showCancelButton: true,
    //     message: `${this.$langue('NetCourse_PlayAt', {
    //       seconds: my_study_progress,
    //       defaultText: `上次播放至${my_study_progress}秒`
    //     })}`
    //   }).then(() => {
    //     this.lastTime = my_study_progress
    //     this.$refs.videoRef.vedioPlayer.currentTime(my_study_progress)
    //     this.$refs.videoRef.vedioPlayer.play()
    //   }).catch(() => {
    //   })
    // },
    getInteraction() {
      getCourseInteraction({
        courseId: this.course_id,
        actType: 2
      }).then((res) => {
        res.configurations_of_select = res.configurations_of_select || []
        res.configurations_of_select.sort(
          (a, b) => a.active_time - b.active_time
        )
        this.interactionData = res
      })
    },
    getOriginUrl(contentId) {
      urlForDownloadApi(contentId).then((res) => {
        this.audioUrl = res
      })
    },
    changeTabs(val) {
      if (this.isPreview) return
      this.activeKey = val
      if (this.activeKey === 'des') {
        this.getCourseDetail('changeTab')
      } else if (this.activeKey === 'extand') {
        this.getExtandList()
      } else if (this.activeKey === 'chapter') {
        // 获取章节列表
        this.getChapterList()
      }
    },
    getExtandList() {
      const params = {
        act_id: this.course_id,
        act_type: 2
      }
      this.loading = true
      getExtanContentList(params)
        .then((data) => {
          this.loading = false
          this.extandList = (data || []).map((item) => {
            return {
              module_name: item.content_module_name, // 类型名称
              module_id: item.content_module_id, // 类型id
              content_name: item.content_name, // 内容名称
              content_url: item.href,
              content_id: item.content_item_id,
              description: item.course_desc, // 简介
              play_total_count: item.view_count, // 查看次数
              word_num: item.word_num, // 图文/笔记 - 字数
              praise_count: '', // 图文/笔记/案例/码客 - 点赞数
              avg_score: item.avg_score, // 得分
              created_time: item.content_created_time, // 时间
              photo_url: this.getItemImg(item),
              origin_data: {
                expert_name: '', // 行家-人员名称
                meet_num: 0, // 咨询量
                avg_score: '', // 评分
                start_time: '', // 活动开始时间
                end_time: '' // 活动结束时间
              }
            }
          })
        })
        .catch(() => {
          this.loading = false
        })
    },
    // 跳转至字幕
    toCaption(data) {
      const { IntStartTime } = data
      this.playTime = IntStartTime
      if (this.$refs?.videoRef) {
        this.$refs.videoRef.play()
      }
    },
    getItemImg({ photo_storage_type, photo_url, photo_id }) {
      if (photo_storage_type === 'contentcenter') {
        const envName = env[process.env.NODE_ENV]
        return `${envName.contentcenter}content-center/api/v1/content/imgage/${photo_url}/preview`
      }
      return photo_id
    },
    readCaptionFile(captions) {
      captions.forEach((item) => {
        if (item.attachement_type === 'Caption') {
          axios({
            url: item.url,
            method: 'GET',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
            }
          }).then((response) => {
            if (response.status === 200 && response.data) {
              try {
                // let data = response.data?.split('\n\n')
                let data = response.data?.split(/\n\n|\r\n\r\n/)
                const captionArr = data?.map((str) => {
                  let obj = {}
                  // const captionItemArr = str.split(/[(\r\n)\r\n]+/)
                  const captionItemArr = str.split(/[(\r\n)\r\n|(\r\n\r\n)\r\n\r\n]+/)
                  captionItemArr.map((e, idx) => {
                    if (idx === 1) {
                      const time = JSON.parse(JSON.stringify(e))
                      obj.startTime = e.split('-->')[0]
                      obj.endTime = e.split('-->')[1]
                      const endTimeCopy = JSON.parse(
                        JSON.stringify(time.split('-->')[1])
                      )
                      const startTimeCopy = JSON.parse(
                        JSON.stringify(time.split('-->')[0])
                      )
                      obj.IntStartTime = startTimeCopy
                        ? this.timeToSec(startTimeCopy)
                        : 0
                      obj.IntEndTime = endTimeCopy
                        ? this.timeToSec(endTimeCopy)
                        : 0
                    }
                    if (idx === 2) obj.caption = e
                  })
                  return obj
                })
                this.captionData = captionArr
                if (
                  this.captionData &&
                  this.captionData.length &&
                  this.tabList.length < 5 && this.courseData.show_comments !== false
                ) {
                  this.tabList.splice(2, 0, {
                    title: 'NetCourse_TextContent',
                    key: 'text',
                    text: '文稿'
                  })
                  this.activeKey = 'text'
                }
              } catch (error) {}
            }
          })
        }
      })
    },
    timeToSec(timeStr) {
      let time = timeStr.split(',')[0]
      let s = ''
      const hour = time.split(':')[0]
      const min = time.split(':')[1]
      const sec = time.split(':')[2]
      let misecond = timeStr.split(',')[1].substr(0, 2)
      misecond = Number(parseInt(misecond) / 100)
      s = Number(hour * 3600) + Number(min * 60) + Number(sec)
      return (s * 100 + misecond * 100) / 100
    },
    expandSwitch(v) {
      this.lessonExpand = v
      this.$nextTick(() => {
        let el = document.getElementById('main-area')
        this.mainAreaHeight = el.offsetHeight + 7
      })
    },
    initDrag() {
      let that = this
      const btnEl = document.getElementById('drag-service')
      if (btnEl) {
        let disY
        let height
        btnEl.addEventListener('touchstart', function (e) {
          e.preventDefault()
          height = e.view.innerHeight - 40
          disY = e.touches[0].clientY - this.offsetTop
          that.initTop = this.offsetTop
        })

        btnEl.addEventListener('touchmove', function (e) {
          let top = e.touches[0].clientY - disY

          if (Math.abs(top - that.initTop) < 5) { // 兼容鸿蒙系统，点击时偏移几个像素处理成点击
            return
          }

          that.initTop = top
          that.isMoved = true

          if (top >= 0 && top <= height) {
            btnEl.style.top = top + 'px'
          }
        })
        btnEl.addEventListener('touchend', function (e) {
          // 判断是否有拖动，有则初始化为false，无则为点击
          if (that.isMoved) {
            that.isMoved = false
          } else {
            let paramStr = ''
            if (that.isCourse) {
              paramStr += `&from=CourseList&area_id=${that.$route.query.area_id}`
            }
            const seconds = Math.floor(that.$refs.videoRef.vedioPlayer.currentTime())
            window.open(
              `https://sdc.qq.com/s/sYkoua?scheme_type=audio&course_id=${that.course_id}${paramStr}&currentTime=${seconds}`
            )
          }
        })
      }
    },
    initLang() {
      let that = this
      const btnEl = document.getElementById('drag-lang')
      if (btnEl) {
        let disY
        let height
        btnEl.addEventListener('touchstart', function (e) {
          e.preventDefault()
          height = e.view.innerHeight - 40
          disY = e.touches[0].clientY - this.offsetTop
        })
        btnEl.addEventListener('touchmove', function (e) {
          that.isMoved = true
          let top = e.touches[0].clientY - disY
          if (top >= 0 && top <= height) {
            btnEl.style.top = top + 'px'
          }
        })
        btnEl.addEventListener('touchend', function (e) {
          // 判断是否有拖动，有则初始化为false，无则为点击
          if (that.isMoved) {
            that.isMoved = false
          } else {
            // 双语切换
            let lang = that.moocLang === 'en-us' ? 'zh-cn' : 'en-us'
            that.$store.commit('setMoocLang', lang)
            that.getMobileLangJS()
            window.parent &&
              window.parent.postMessage({
                lang,
                type: 'changeLang'
              })
          }
        })
      }
    },
    // 关闭横屏提示
    verticalClose() {
      this.verticalTips = false
      const my_study_progress = this.courseData.my_study_progress
      if (my_study_progress > 0) {
        this.isCurrentTimeShow = true
        // let curTimeShouTimer = setTimeout(() => {
        //   this.isCurrentTimeShow = false
        // }, 15000)
        // this.$once('hook:beforeDestroy', () => {
        //   clearTimeout(curTimeShouTimer)
        //   curTimeShouTimer = null
        // })
      }
    },
    // 继续学习
    onContinue() {
      if (this.continueDisabled) {
        if (this.questionData?.continue_studying_tips) {
          Toast({
            className: 'interaction-toast',
            message: this.questionData?.continue_studying_tips
          })
        }
        return
      }
      const answers = this.questionData?.select_content.map((item, index) => {
        const active_answer =
          typeof this.selected[index] === 'string'
            ? this.selected[index]
            : this.selected[index].sort((a, b) => a * 1 - b * 1).join(',')
        return {
          question_id: item.question_id,
          active_answer
        }
      })
      saveInteractionRecord({
        interactive_config_id: this.interactionData.id,
        interactive_id: this.questionData?.interactive_id,
        record_id: this.learnRecordId || 0,
        answers
      }).then(() => {})
      this.showInteractive = false
      this.$refs.videoRef.vedioPlayer.play()
      if (this.isFormMooc) {
        MoocJs.postMessage('interactiveDialog', this.showInteractive)
      }
    },
    // 监听播放时长
    getCurrentTime(time) {
      this.videoProgress = time
      // 文稿滚动内容
      this.$nextTick(() => {
        if (this.$refs.textDraftRef) {
          this.$refs.textDraftRef.scrollTopContent(time)
        }
        if (this.$refs.chapterInfoRef) {
          this.$refs.chapterInfoRef.slidToContent(time)
        }
      })

      const curr = Math.floor(time)
      this.studyRecordQuery.my_study_progress = curr
      // 互动能力相关
      if (this.interactionTime === curr || this.showInteractive) return
      this.interactionTime = curr
      // 如果视频被拖动了，并且此时设置为不可拖动，则回到拖动之前的位置
      if (
        this.courseData.enable_interactive &&
        this.courseData.limit_progress_bar
      ) {
        // 记录熄屏的时间
        if (this.isPageHidden) {
          return
        } else {
          this.lastHiddenTime = curr
        }
        // 如果是快进或快退
        if (Math.abs(curr - this.lastTime) > 2) {
          this.$refs.videoRef.vedioPlayer.pause()
          this.$refs.videoRef.vedioPlayer.currentTime(this.lastTime)
          return
        } else {
          this.lastTime = curr
        }
      }
      let confSelect = this.interactionData.configurations_of_select || []
      this.questionData = confSelect.find(
        (item) => parseInt(item.active_time) === curr
      )
      // 如果没有问题
      if (
        !this.questionData ||
        this.questionData?.select_content.length === 0
      ) {
        return
      }
      this.selected = this.questionData?.select_content.map((item) => {
        return item.choose_type_config?.type === 'single' ? '' : []
      })
      this.showInteractive = true
      this.$refs.videoRef.vedioPlayer.pause()
      if (this.isFormMooc) {
        MoocJs.postMessage('interactiveDialog', this.showInteractive)
      }
    },
    // 播放
    onPlay() {
      // 有互动弹窗时，不允许继续播放
      if (
        this.courseData.enable_interactive &&
        this.courseData.limit_progress_bar &&
        this.showInteractive
      ) {
        this.$refs.videoRef.vedioPlayer.pause()
        this.$refs.videoRef.vedioPlayer.currentTime(this.lastTime)
      }
    },
    // 播放结束
    onEnded() {
      setTimeout(() => {
        this.lastTime = 0
      }, 1000)
    },
    // 全屏切换
    fullscreenchange(data) {
      let _this = this
      function checkStyleChange() {
        const currentStyle = window.getComputedStyle(document.querySelector('.vjs-control-bar')).getPropertyValue('opacity')
        if (currentStyle === '0') {
          _this.isShowProgress = false
        } else if (currentStyle === '1') {
          _this.isShowProgress = true
        }
        requestAnimationFrame(checkStyleChange) // 或者使用 setInterval(checkStyleChange, 100)
      }
      checkStyleChange()

      try {
        let timeDom = document.querySelector('.vjs-duration-display')
        let timeString = timeDom.textContent || ''
        let timeArray = timeString.split(':')
        let h = 0
        let m = 0
        let s = 0
        if (timeArray.length === 2) {
          m = parseInt(timeArray[0])
          s = parseInt(timeArray[1])
        } else if (timeArray.length === 3) {
          h = parseInt(timeArray[0])
          m = parseInt(timeArray[1])
          s = parseInt(timeArray[2])
        }

        let time = (h * 3600 + m * 60 + s) || 0
        console.log('time~~~~~~~~~~~~: ', time)
        if (time) {
          this.videoProgressMaxValue = time
        }
      } catch (error) {
        console.log('error: ', error)
      }
      this.isFullscreen = data
      this.isVertical = this.isFullscreen
      // this.isShowFullScreenBtn = false
      // window.parent && window.parent.postMessage({
      //   data: 'vertical'
      // })
    }
  }
}
</script>

<style lang="less" scoped>
.video-container {
  overflow: hidden;
  .h100vh {
    height: 100vh;
  }
  .main-fixed {
    position: fixed;
    z-index: 99;
    width: 100%;
    :deep(.video-component) {
      border: unset;
      border-radius: unset;
      background-color: black;
    }
  }
  .main-content {
    position: relative;
    z-index: 9;
    margin-top: 255px;
    .comment {
      background: #fff;
      padding-top: 10px;
      height: 100%;
      overflow-y: auto;
    }
  }
  .fixed-main-content {
    position: fixed;
    width: 100%;
    top: 255px;
    margin-top: unset;
    height: calc(100% - 255px);
  }

  .hidden-progress-control { // 假全屏时隐藏进度条 改用自己写的进度条
    /deep/ .vjs-progress-control {
      display: none !important;
    }
  }

  /deep/ .video-js {
    .vjs-progress-holder {
      height: .3em;
    }
    .vjs-play-progress:before {
      font-size: 1.0em;
      top: -.343333333333333em;
    }
  }

  .play-video {
    position: relative;
    .video-progress {
      opacity: 0;
      animation: all .5s ease;
      position: absolute;
      top: 20px;
      left: 40px;
      width: 8px;
      height: calc(100vh - 40px);
      z-index: 9999999;
      /deep/.van-slider__button-wrapper {
        // opacity: 0;
        .van-slider__button {
          width: 20px;
          height: 20px;
        }
      }
    }
    .progress-animation {
      opacity: 1;
    }
    .exit-btn {
      display: inline-block;
      position: fixed;
      left: 6px;
      bottom: 12px;
      width: 24px;
      height: 24px;
      background: url('~@/assets/mooc-img/exit-btn.png') no-repeat center / cover;
    }
    .common-time-tips {
      position: absolute;
      z-index: 999;
      background: #0F1010;
      color: #fff;
      line-height: 20px;
      font-size: 14px;
      padding: 5px;
      border-radius: 2px;
      .van-icon-cross {
        margin-right: 5px;
        cursor: pointer;
        font-size: 16px
      }
      .tips-btn {
        cursor: pointer;
        color:#3464e0;
        margin-left: 5px;
      }
    }
    .current-time-tips {
      left: 10px;
      bottom: 50px;
      font-size: 14px;
      display:flex;
      align-items:center;
    }
    .en-current-time-tips {
      display: flex;
      align-items:unset;
      flex-direction: column;
      bottom: 50px;
      left: 10px;
      .tips-btn {
        margin-left: 20px;
      }
    }
    .vertical-dialog {
      top: 110px;
      left: 70px;
      display: flex;
      align-items: center;
      white-space: nowrap;
    }
    .en-vertical-dialog {
      top: 215px;
      left: 70px;
      display: flex;
      align-items: center;
      white-space: nowrap;
    }
  }
  .video-box,
  .scorm-box {
    height: 210px;
  }
  .fullScreen-video-box {
    height: 100vh;
  }
  .full-vertical-box {
    width: 100vh !important;
    height: 100vw !important;
  }
  .van-image {
    width: 100%;
    height: 100%;
  }
  :deep(.van-tabs--line) {
    .van-tabs__wrap {
      height: 38px;
    }
    .van-tab--active {
      font-weight: bold;
    }
    .van-tabs__line {
      width: 28px;
    }
    .van-tab {
      flex: auto;
    }
  }
  .loading-text {
    height: 80px;
    line-height: 80px;
    text-align: center;
    color: #00000066;
  }
  .video-learn-empty {
    padding-top: 20px;
    text-align: center;
    .empty-text {
      margin: 16px 0 20px;
    }
  }
  .extent-tab {
    height: 100%;
    overflow-y: auto;
  }
  .more-learn {
    padding: 0 16px;
    background: #fff;
    .content-inner {
      border-bottom: 0.5px solid #e7e7e7;
    }
  }
  #drag-service {
    height: 40px;
    padding: 0 8px;
    background: #0052d9;
    border: 1px solid #dcdcdc;
    border-radius: 20px 0 0 20px;
    border-right-color: transparent;
    box-shadow: 0 0 24px 0 #dcdcdc99;
    position: fixed;
    top: calc(47% + 20px + 86px); // 前面有中英文切换按钮，培养项目按钮
    right: 0;
    z-index: 99;
    color: #fff;
    font-size: 10px;
    display: flex;
    align-items: center;
    user-select: none;
    .icon {
      width: 16px;
      height: 16px;
      background: url('~@/assets/img/mobile/switch-icon.png') no-repeat
        center/cover;
      margin-right: 3px;
      display: inline-block;
    }
  }
  .drag-lang {
    width: 60px;
    height: 40px;
    padding-left: 8px;
    background: #fff;
    border: 1px solid #dcdcdc;
    border-radius: 20px 0 0 20px;
    border-right-color: transparent;
    box-shadow: 0 0 24px 0 #dcdcdc99;
    position: fixed;
    top: 53%;
    right: 0;
    z-index: 99;
    font-size: 10px;
    display: flex;
    align-items: center;
    user-select: none;
    .el-icon-en {
      background: url('~@/assets/img/english.png') no-repeat center / cover;
    }
    .el-icon-zh {
      background: url('~@/assets/img/china.png') no-repeat center / cover;
    }
    .icon {
      width: 20px;
      height: 20px;
      margin-right: 4px;
      display: inline-block;
    }
  }
  .lang-icon-vertical {
    top: 16px;
    right: 196px;
    z-index: 9999;
    padding: 0;
    width: 50px;
    height: 50px;
    flex-shrink: 0;
    border-radius: 36px;
    background: #fff;
    box-shadow: 0 0 12px 0 #99999999;
    text-align: center;
    .icon {
      margin: 0;
      width: 24px;
      height: 23px;
      transform: translateX(12px) rotate(90deg);
    }
    .text {
      display: none;
    }
  }
  .vertical-popup {
    width: 310px;
    border-radius: 12px;
    .bg {
      height: 201px;
      line-height: 201px;
      background: linear-gradient(270deg, #3683ea 0%, #83b8ff 95.82%);
      text-align: center;
      .vertical {
        width: 163px;
        height: 145px;
      }
    }
    .bottom {
      padding: 24px 0;
      font-size: 16px;
      text-align: center;
      .text {
        color: #2c3645;
        line-height: 24px;
      }
      .confirm {
        display: inline-block;
        margin-top: 24px;
        width: 247px;
        height: 48px;
        line-height: 48px;
        border-radius: 24px;
        background: linear-gradient(90deg, #0e69ff 0%, #4f91ff 100%);
        color: #fff;
        font-weight: 600;
      }
    }
  }

  .interactive-popup {
    border-radius: 6px;
    width: 90vh;
    height: 90vw;
    transform-origin: 0 0;
    transform: translateX(45vw) translateY(-45vh) rotate(90deg);
    display: flex;
    flex-direction: column;
    .content {
      flex: 1;
      padding: 24px 32px;
      overflow-y: auto;
      .title {
        margin-bottom: 16px;
        color: #000000e6;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
      }
      .desc {
        padding: 12px;
        background: #fafafa;
        color: #00000099;
        line-height: 22px;
      }
      .question {
        .question-name {
          padding-top: 10px;
          font-weight: bold;
        }
        .van-checkbox-group,
        .van-radio-group {
          padding-top: 4px;
          :deep(.van-checkbox),
          :deep(.van-radio) {
            align-items: unset;
            margin-top: 12px;
            .van-checkbox__icon {
              padding-top: 2px;
              .van-icon {
                border-radius: 3px;
              }
            }
            .van-checkbox__label,
            .van-radio__label {
              color: #000000e6;
              line-height: 22px;
              white-space: pre-line;
            }
          }
        }
      }
    }
    .bottom {
      height: 64px;
      line-height: 64px;
      box-shadow: 0 -4px 12px 0 #0000000f;
      text-align: center;
      .continue {
        display: inline-block;
        height: 40px;
        line-height: 40px;
        width: 240px;
        border-radius: 3px;
        color: #ffffffe6;
        background: #0052d9;
      }
      .continue-disabled {
        background: #b5c7ff;
      }
    }
  }
  :deep(.task-dialog) {
    text-align: center;
    .van-dialog__content {
      padding: 45px 20px;
    }
  }
}
</style>
