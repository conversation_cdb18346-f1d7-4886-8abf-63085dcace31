<template>
  <div class="make-share-page">
    <div class="big-title-tips">
      <p class="title">在线课程制作和分享</p>
      <P class="tips">根据你的准备情况，选择你的课程制作和分享方式</P>
    </div>
    <div class="card-box">
      <ul>
        <li class="card" v-for="(item, index) in cardList" :key="index" @click="toPath(item)">
          <div class="l-content">
            <img class="explain-img" :src="require('@/assets/img/' + item.pngName + '.png')" alt="">
            <div class="card-content">
              <p class="big-title">{{ item.bigTitle }}</p>
              <p class="explain-title">{{ item.explainTitle }}</p>
            </div>
          </div>
          <img class="arrow-img" src="@/assets/img/right-arrow.png" alt="">
        </li>
      </ul>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      cardList: [
        { pngName: 'ai-make', createRouteName: 'courseQuick', bigTitle: 'AI快捷做课：无需拍摄，快捷生成在线课程视频', explainTitle: '仅需提供授课PPT和演讲稿，即可快捷生成包括PPT画面、讲解声音、讲解人像的在线课程。' },
        { pngName: 'ai-upload', createRouteName: 'couserUpload', bigTitle: '直接上传：上传已有视频素材', explainTitle: '如你当前已完成了在线课程的视频制作，即可在此直接上传视频素材并配置生成在线课程。' },
        { pngName: 'ai-video', createRouteName: '', bigTitle: '申请拍摄资源', explainTitle: '如你所在的项目需要申请人力支持在线课程的拍摄和制作，请点击这里填写申请。' }
      ]
    }
  },
  methods: {
    toPath({ createRouteName }) {
      if (createRouteName) {
        this.$router.push({ name: createRouteName })
        return
      }
      window.open('https://iwiki.woa.com/pages/viewpage.action?pageId=1743568356', '_blank')
    }
  }
}
</script>
<style lang="less" scoped>
.make-share-page {
  padding: 28px 20px;
  height: calc(100vh - 70px);

  .card-box {
    margin-top: 28px;
  }

  li+li {
    margin-top: 20px;
  }

  .card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(90deg, rgba(255, 255, 255, 1) 0%, rgba(248, 251, 255, 1) 0%, rgba(251, 253, 255, 1) 100%);
    padding: 28px 28px 28px 32px;
    border-radius: 6px;
    cursor: pointer;

    .l-content {
      display: flex;
      align-items: center;

      .explain-img {
        height: 48px;
        width: 48px;
      }
    }

    .card-content {
      margin-left: 32px;

      .big-title {
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.8);
        margin-bottom: 12px;
      }

      .explain-title {
        color: rgba(0, 0, 0, 0.4);
        font-size: 12px;
      }
    }

    .arrow-img {
      height: 14px;
      width: 14px;
    }
  }

  .big-title-tips {
    .title {
      font-size: 24px;
      font-weight: 700;
      color: #000000;
      margin-bottom: 12px;
    }

    .tips {
      color: rgba(0, 0, 0, 0.6);
      font-size: 14px;
    }
  }
}</style>
