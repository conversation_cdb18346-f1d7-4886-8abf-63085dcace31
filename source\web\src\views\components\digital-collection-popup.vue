<template>
  <el-dialog
    :visible.sync="isShowPopup"
    custom-class="digital-collection-popup"
    :title="`${pageData.certificateName}：${pageData.album_name || '-'}`"
    width="850px"
    :lock-scroll="false"
    :close-on-click-modal="false"
    :before-close="closeDialog"
  >
    <div> 
      领取二维码(限微信访问): 
      <p class="qr-code">
        <img :src="`data:image/png;base64,${pageData.nftUrl}`" alt="" />
      </p>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    isShowPopup: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pageData: {
        certificateName: '-',
        album_name: '-',
        nftUrl: ''
      }
    }
  },
  methods: {
    // 获取学员证书列表
    onInit(e) {
      this.pageData = e
    },
    // 关闭弹窗
    closeDialog() {
      this.$emit('update:isShowPopup', false)
    }
  }
}
</script>

<style lang="less" scoped>
:deep(.digital-collection-popup) {
  .el-dialog__body {
    padding: 24px 32px;
  }
  .container {
    width: 100%;
    
  }
  .qr-code {
    text-align: center;
    margin-top: -14px;
    & > img {
      width: 200px;
      height: 200px;
    }
  }
}
</style>
