import { getActivityDetail } from '@/config/classroom.api.conf.js'

const state = {
  activityInfo: '' // 活动信息
}
const mutations = {
  SET_ACTIVITY_INFO(state, payload) {
    state.activityInfo = {
      ...state.activityInfo,
      ...payload
    }
  }
}
const actions = {
  getActivityInfo(context, option) {
    getActivityDetail(option).then((res) => {
      context.commit('SET_ACTIVITY_INFO', res)
    })
  }
}

const getters = {
  activityInfo: state => state.activityInfo
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
