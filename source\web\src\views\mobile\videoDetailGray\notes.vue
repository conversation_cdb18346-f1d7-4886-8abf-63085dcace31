<template>
  <div class="notes-content">
    <div class="video-notes" v-if="noteList.length > 0">
      <div class="sort-btn">
        <div class="content-inner">
          <span
            @click="changeSortType('view_count')"
            class="hot"
            :class="{ 'high-light': activeSortType === 'view_count' }"
            :dt-remark="dtTab('remark', '热门排序')" 
            :dt-areaid="dtTab('areaid', '热门排序')"
            :dt-eid="dtTab('eid', '热门排序')"
            >{{ $langue('NetCourse_Hot', { defaultText: '热门排序' }) }}</span
          >
          <span
            @click="changeSortType('creator_at')"
            :class="{ 'high-light': activeSortType === 'creator_at' }"
            :dt-remark="dtTab('remark', '最新排序')" 
            :dt-areaid="dtTab('areaid', '最新排序')"
            :dt-eid="dtTab('eid', '最新排序')"
            >{{ $langue('NetCourse_Newest', { defaultText: '最新排序' }) }}</span
          >
        </div>
      </div>
      <!-- "没有更多内容了～" -->
      <van-list
        v-model="listLoading"
        :finished="listFinished"
        :finished-text="finishedText"
        @load="getNoteList"
        class="notes-card"
      >
        <CategoryCard
          class="content-inner"
          v-for="(item, index) in noteList"
          :key="index"
          :detailsInfo="item"
          pageType="笔记"
          :courseData="courseData"
          :tabType="activeSortType === 'view_count' ? '热门排序' : '最新排序'"
        />
      </van-list>
    </div>
    <div v-else class="video-notes-empty">
      <img src="@/assets/img/mobile/empty-note.png" alt="" />
      <div class="empty-text">{{$langue('NetCourse_NoNotoice', { defaultText: '暂无相关笔记，快去发表吧' })}}～</div>
    </div>
  </div>
</template>
<script>
import CategoryCard from '../components/CategoryCard.vue'
import { getRelationGraphicH5 } from 'config/api.conf'
export default {
  name: 'videoNotes',
  components: {
    CategoryCard
  },
  props: {
    module_id: {
      type: String,
      default: '1'
    },
    item_id_key: {
      type: String,
      default: 'course_id'
    },
    courseData: {
      type: Object,
      default: () => ({})
    },
    // 默认网络课
    courseType: {
      type: String,
      default: 'net'
    }
  },
  data() {
    return {
      noteList: [],
      activeSortType: 'view_count',
      pageNo: 1,
      listLoading: false,
      listFinished: false
    }
  },
  computed: {
    pageTypeName() {
      let obj = {
        'net': '网课',
        'face': '面授课',
        'activity': '活动'
      }
      return obj[this.courseType]
    },
    finishedText() {
      return this.$langue('Mooc_Common_Alert_NoContent', { defaultText: '没有更多内容了' }) + '～'
    },
    course_id() {
      return this.courseData.course_id || this.$route.query.course_id || this.$route.query.activity_id
    },
    dtTab() {
      return (type, val) => {
        const data = {
          page: this.courseData.course_name,
          page_type: this.pageTypeName + '详情页面-移动新版', 
          container: '笔记',
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: val,
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'H5'
        }
        if (type === 'remark') {
          return JSON.stringify(data)
        } else if (type === 'eid') {
          return `element_${this.course_id}_${val}`
        } else {
          return `area_${this.course_id}_${val}`
        }
      }
    }
  },
  created() {
    this.getNoteList()
  },
  methods: {
    changeSortType(val) {
      this.activeSortType = val
      this.listFinished = false
      this.pageNo = 1
      this.noteList = []
      this.getNoteList()
    },
    getNoteList() {
      const params = {
        page_no: this.pageNo,
        page_size: 10,
        item_id: this.$route.query[this.item_id_key] || '',
        module_id: this.module_id, // 课程类型-网络课传1
        order_by_desc: this.activeSortType
      }
      getRelationGraphicH5(params).then((data) => {
        this.pageNo++
        this.listLoading = false
        const result = (data.records || []).map((item) => {
          return {
            module_name: '文章',
            module_id: 8,
            content_name: item.graphic_name,
            content_url: item.url,
            content_id: item.graphic_id,
            description: item.graphic_desc,
            play_total_count: item.view_count,
            praise_count: item.praise_count,
            word_num: item.graphic_number,
            created_time: item.creator_at.split(' ')[0]
          }
        })
        this.noteList = this.noteList.concat(result)
        if (this.noteList.length >= data.total) {
          this.listFinished = true
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.notes-content {
  background: #fff;
  height: 100%;
  overflow-y: auto;
  padding-bottom: 114px;
  .loading-text {
    height: 80px;
    line-height: 80px;
    text-align: center;
    color: #00000066;
  }
  .video-notes {
    background: #fff;
    .sort-btn {
      padding: 0 16px;
      color: #00000066;
      line-height: 48px;
      .hot {
        margin-right: 20px;
      }
      .high-light {
        color: #0052d9;
      }
    }
    .content-inner {
      border-bottom: 0.5px solid #e7e7e7;
    }
    .notes-card {
      padding: 0 16px;
    }
  }
  .video-notes-empty {
    padding: 20px 0;
    text-align: center;
    .empty-text {
      margin: 16px 0 20px;
    }
  }
}
</style>
