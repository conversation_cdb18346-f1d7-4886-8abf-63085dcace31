<template>
  <div class="distributeAdd">
    <Breadcrumb
      :breadcrumbs="$route.query.id ? ['分发页管理', '编辑分发页'] : null"
    ></Breadcrumb>
    <div class="manage-layout">
      <div class="basics-set">
        <p class="set-title">分发页基础设置</p>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="100px"
          class="demo-ruleForm"
        >
          <el-form-item label="分发页名称" prop="forward_name">
            <el-input
              v-model="ruleForm.forward_name"
              placeholder="请输入分发页名称"
              class="wt-50"
              maxlength="50"
              show-word-limit
            ></el-input>
          </el-form-item>
          <el-form-item label="分发页状态" prop="type">
            <el-radio-group v-model="ruleForm.status">
              <el-radio :label="1">在用</el-radio>
              <el-radio :label="2">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <div class="relation-set">
        <div class="left">
          <p class="set-title">关联设置</p>
          <!-- <p class="tips">
            至多关联10个分发项。请在配置分发项时，保障关联在同一分发页的分发项开放范围无人员交叉
          </p> -->
        </div>
        <div class="right">
          <button class="el-button el-button--primary" @click="relationExam">
            <i class="el-icon-circle-plus-outline"></i><span>添加分发项</span>
          </button>
        </div>
      </div>
      <el-table
        max-height="515"
        v-if="tableData.length"
        :data="tableData"
        tooltip-effect="dark"
        style="width: 100%"
      >
        <el-table-column prop="forward_url" label="分发项地址">
        </el-table-column>
        <el-table-column
          prop="target_list"
          label="目标学员"
          :show-overflow-tooltip="true"
          min-width="80"
          max-width="120"
        ></el-table-column>
        <el-table-column
          prop="creator_name"
          label="创建人姓名"
          :show-overflow-tooltip="true"
        >
        </el-table-column>
        <el-table-column
          prop="created_at"
          label="创建时间"
          :show-overflow-tooltip="true"
          max-width="120"
        >
        </el-table-column>
        <el-table-column width="300" label="操作" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="handleDetails(scope.row)"
              >编辑</el-button
            >
            <el-button type="text" @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="footer">
        <el-button type="primary" @click="save">发布</el-button>
        <el-button @click="cancel">取消</el-button>
      </div>
    </div>
    <exam-modal
      ref="exam-modal"
      @selected="selected"
      :List="tableData"
    ></exam-modal>
  </div>
</template>
<script>
import Breadcrumb from '../../components/breadcrumb.vue'
import examModal from './examModel.vue'
import { getForwardInfo, saveForward } from '@/config/api.conf.js'
export default {
  name: 'distributeAdd',
  data () {
    return {
      authoriz_value: '',
      ruleForm: {
        forward_name: '',
        status: 1
      },
      rules: {
        forward_name: [
          { required: true, message: '请输入分发页名称', trigger: 'blur' },
          { min: 0, max: 50, message: '长度限制50个字符内', trigger: 'blur' }
        ]
      },
      tableData: []
    }
  },
  components: {
    Breadcrumb,
    examModal
  },
  computed: {
    forward_id () {
      return this.$route.query.id
    }
  },
  mounted () {
    if (this.forward_id) {
      getForwardInfo(this.forward_id).then(res => {
        this.ruleForm = res
        this.tableData = res.forward_item_list || []
      })
    }
  },
  methods: {
    relationExam () {
      const $modal = this.$refs['exam-modal']
      $modal.params.url = ''
      $modal.params.authoriz_value = ''
      $modal.params.id = ''
      $modal.examModal = true
    },
    selected (params) {
      if (params.id) {
        this.tableData.forEach(item => {
          if (item.id === params.id) {
            item.forward_url = params.url
            item.target_list = params.authoriz_value
          }
        })
      } else {
        this.tableData.push({
          forward_url: params.url,
          target_list: params.authoriz_value
        })
      }
    },
    handleDetails (item) {
      const $modal = this.$refs['exam-modal']
      $modal.params.url = item.forward_url
      $modal.params.authoriz_value = item.target_list
      $modal.params.id = item.id
      $modal.examModal = true
    },
    handleDelete (row) {
      this.$messageBox
        .confirm('确定删除吗？', '删除分发页', {
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          confirmButtonClass: 'delete-btn'
        })
        .then(() => {
          this.tableData.splice(
            this.tableData.findIndex(item => item.id === row.id),
            1
          )
          this.$message.success('已删除')
        })
        .catch(() => {
          this.$message.error('已取消')
        })
    },
    save () {
      this.$refs['ruleForm'].validate(valid => {
        if (!valid) return false
        if (!this.tableData.length) {
          this.$message.warning('请添加分发项')
          return false
        }
        let params = {
          forward_id: this.forward_id,
          forward_name: this.ruleForm.forward_name,
          status: this.ruleForm.status,
          forward_item_list: this.tableData
        }
        saveForward(params).then(() => {
          this.$message.success('保存成功')
          this.cancel()
        })
      })
    },
    cancel () {
      this.$router.push({
        name: 'distributePage'
      })
    }
  }
}
</script>
<style lang="less" scoped>
.distributeAdd {
  padding: 15px;
  width: 100%;
  height: 100%;
  .manage-layout {
    background: #fff;
    border-radius: 6px;
    padding: 20px 50px;
    height: 94%;
    min-height: 800px;
    .basics-set {
      .status {
        padding: 15px;
        span {
          margin-right: 20px;
        }
      }
    }
    .demo-ruleForm {
      margin-top: 20px;
    }
    .set-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      border-left: solid 4px #3464e0;
      padding-left: 8px;
      line-height: 24px;
    }
    .relation-set {
      margin-bottom: 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .left {
        display: flex;
        align-items: center;
      }
      .tips {
        color: #ff4025;
        margin-left: 15px;
      }
    }
    .footer {
      position: absolute;
      bottom: 6%;
      right: 5%;
    }
  }
}
.wt-50 {
  width: 50%;
}
</style>
