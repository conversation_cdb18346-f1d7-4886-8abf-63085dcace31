<template>
  <el-dialog
  custom-class="notice-dialog none-border-dialog dialog-center" 
  :visible.sync="visible" 
  width="644px"
  top="-150px" 
  :before-close="handleNoticeClose">
    <div slot="title">
      <p class="publish-title">{{ noticeInfo.title }}</p>
      <p class="publish-time">
        <span class="publish-icon"></span>
        <span>{{$langue("Mooc_ProjectDetail_Notice_PublishTime", { defaultText: '发布时间' })}}：{{ noticeInfo.prepare_publish_time }}</span>
      </p>
    </div>
    <sdc-mce-preview
      v-if="tincyValue"
      ref="editor"
      :urlConfig="editorConfig.urlConfig"
      :catalogue.sync="editorConfig.catalogue"
      :content="noticeInfo.content"
    >
    </sdc-mce-preview>
    <!-- <span v-html="noticeInfo.content"></span> -->
    <span></span>
    <span slot="footer" class="dialog-footer">
      <el-button type="primary" size="small" @click="handleNoticeClose">{{ $langue('Mooc_ProjectDetail_Notice_IKnow', { defaultText: '知道了' }) }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean
    },
    noticeInfo: {
      type: Object
    }
  },
  data() {
    return {
      editorConfig: {
        catalogue: false,
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/{contentId}`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview`
        }
      }
    }
  },
  computed: {
    tincyValue() {
      return this.noticeInfo?.content ? this.noticeInfo?.content.replace(/(<([^>]+)>)/ig, '') || this.noticeInfo?.content.includes('data-content') : false
    }
  },
  methods: {
    handleNoticeClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
  :deep(.notice-dialog) {
    border-radius: 3px;
    background: linear-gradient(180deg, #ecf4ffff 0%, #ffffffff 30%, #ffffffff 100%);
    box-shadow: 0 0 16px 0 #00000029;
    .publish-title {
      line-height: 22px;
    }
    .el-dialog__headerbtn {
      top: 25px;
    }
    .sdc-editor-preview {
      height: 375px;
      overflow: auto;
      word-break: keep-all;
      .editor-content p {
        word-break: break-all;
      }
    }
    .publish-time {
      color: #00000099;
      font-size: 14px;
      font-weight: 400;
      margin-top: 8px;
      display: flex;
      align-items: center;
      line-height: 22px;
      .publish-icon {
        background: url('~@/assets/mooc-img/time.png') no-repeat center/cover;
        width: 16px;
        height: 16px;
        display: inline-block;
        margin-right: 8px;
      }
    }
  }
</style>
