<template>
  <div class="includes-content-main" v-if="emptyShow">
    <div class="top-tab">
      <div class="tab-le">
        <span class="title">{{ $langue('NetCourse_IncludesThisContent', { defaultText: '包含此内容' }) }}</span>
        <span :class="['tab-bg', {'active-tab-bg': activeTab === item.container_module_id}]" v-for="(item, index) in filterTabList" :key="index" @click="changeTab(item)">
          <span class="tab-label">{{ $langue(item.enLabel, { defaultText: item.label }) }}</span>
        </span>
      </div>
    </div>
    <div class="includes-swiper-main">
      <div class="course-main" v-for="(item, i) in includesList" :key="i">
        <div class="course-top">
          <div class="couse-title-box">
            <span class="left-title" @click="toPath(item)" :dt-eid="dtToPath('eid', item)" :dt-remark="dtToPath('remark', item)">
              <span class="title overflow-l1">
                <span>
                  {{ item.act_type === 11 ? $langue('Mooc_ProjectDetail_TrainingPrograms', { defaultText: '培养项目' }) : 
                      item.act_type === 15 ? $langue('Mooc_CourseType_CourseList', { defaultText: '课单' }) : 
                        item.act_type === 13 ? $langue('NetCourse_Portal', { defaultText: '专区' }) : ''
                  }}：
                </span>
                {{ item.item_name }}
              </span>
              <span class="course-num"><i class="el-icon-arrow-right"></i></span>
            </span>
          </div>
          <div class="course-play-box">
            <span class="item-icon">
              <i class="view-icon"></i>
              <span>{{transformUnitW(item.item_pv || 0)}}{{ $langue('NetCourse_Views', { defaultText: '次观看' }) }}</span>
            </span>
            <span class="item-icon">
              <i class="contents-icon"></i>
              <span>{{item.item_content_nums}}{{ $langue('Mooc_ContentNums', { defaultText: '门内容' }) }}</span>
            </span>
          </div>
        </div>
        <div class="swiper-content">
          <swiper :options="swiperOption" :ref="includesSwiperRef(item)" :class="['special-swiper-box', {'mooc-swiper-box': item.act_type === 11}]">
            <swiper-slide 
            v-for="(e, index) in item.container_contents" 
            :key="index" 
            class="item-special-swiper"
            :data-item_id="e.item_id"
            :data-i="i"
            :dt-areaid="dtIncludesList('areaid', item, e)"
            >
              <!-- 培养项目 -->
              <div class="swiper-body mooc-swiper-body" v-if="item.act_type === 11" :data-item_id="e.item_id" :data-i="i">
                <span class="content-name overflow-l2" :data-item_id="e.item_id" :data-i="i">
                  <span class="tag-name" v-if="e.resource_type_name">{{ e.resource_type_name }}</span>
                  {{ e.item_name }}
                </span>
                <div class="mooc-bottom">
                  <span v-show="e.word_num">{{e.word_num || 0}}{{ $langue('Mooc_ProjectDetail_TaskList_Words', { defaultText: '字' }) }}</span>
                  <span class="circle" v-show="e.word_num && e.duration">·</span>
                  <span v-show="e.duration">{{e.duration || 0}}{{ $langue('Mooc_ProjectDetail_TaskList_Minute', { defaultText: '分钟' }) }}</span>
                </div>
              </div>
              <!-- 课单，专区 -->
              <div class="swiper-body" v-else :data-item_id="e.item_id" :data-i="i">
                <div class="item-left-img" :data-item_id="e.item_id" :data-i="i">
                  <el-image fit="fill" :src="e.item_pic_url" class="extend-cover" :data-item_id="e.item_id" :data-i="i">
                    <div class="image-slot" slot="placeholder">
                      <i class="el-icon-loading"></i>
                    </div>
                    <div class="error-cover" slot="error">
                      <img :src="formatModuleMap(e.module_id)" :data-item_id="e.item_id" :data-i="i" alt="" />
                    </div>
                  </el-image>
                  <span class="content-type" v-if="e.act_type_name"><span>{{ $langue(e.langKey, { defaultText: e.act_type_name }) }}</span></span>
                  <span class="time" v-if="showModuleTips(e)">{{ showModuleTips(e) }}</span>
                </div>
                <div class="item-right">
                  <div class="content-name overflow-l2" :data-item_id="e.item_id" :data-i="i">{{ e.item_name }}</div>
                  <div class="mid-tag" v-if="e.item_labels?.length">
                    <span class="tag overflow-l1" :data-item_id="e.item_id" :data-i="i" v-for="(item, i) in e.item_labels" :key="i">
                      {{ item }}
                    </span>
                  </div>
                  <div class="bottom-icon" v-if="e.act_type === 5">
                    <span class="view-span"><i class="card-time"></i>{{ forMatTime(e) }}</span>
                  </div>
                  <div class="bottom-icon" :data-item_id="e.item_id" :data-i="i" v-if="![5, 11].includes(e.act_type)">
                    <span class="view-span">
                      <i class="view-icon"></i>
                      <span :data-item_id="e.item_id" :data-i="i">{{ transformUnitW(e.item_pv || 0) }}{{ $langue('NetCourse_Views', { defaultText: '次观看' }) }}</span>
                    </span>
                    <!-- <span class="view-span">
                      <i class="score-icon"></i>
                      <span :data-item_id="e.item_id" :data-i="i">{{ e.item_score || 0 }}{{ $langue('Mooc_ProjectDetail_Score_Point', { defaultText: '分' }) }}</span>
                    </span> -->
                  </div>
                </div>
              </div>
            </swiper-slide>
            <div
            v-if="item.container_contents.length"
            :class="['check-more-box', 'swiper-slide', {'mooc-check-more': item.act_type === 11}]" 
            @click="toPath(item)"
            >
              <span class="tips">查看更多</span>
              <span class="more-icon"></span>
            </div>
            <!-- 左边箭头 -->
            <div class="swiper-button-prev" slot="button-prev"  :dt-areaid="dtCommon('areaid', '上一页', item)" @click="handleRight('上一页', item)">
              <span class="icon-left"></span>
            </div>
            <!-- 右边箭头 -->
            <div class="swiper-button-next" slot="button-next" :dt-areaid="dtCommon('areaid', '下一页', item)" @click="handleRight('下一页', item)">
              <span class="icon-right"></span>
            </div>
          </swiper>
        </div>
      </div>
    </div>
    <!-- <Empty :emptyShow="emptyShow" /> -->
    <el-pagination
      class="includes-pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page.sync="page"
      :page-sizes="[5, 10, 20, 50]"
      :page-size="pageSize"
      layout="sizes, prev, pager, next"
      :total="containerTotal">
    </el-pagination>
  </div>
</template>
<script>
import { includesContent } from 'config/api.conf'
import { actTypes } from '@/utils/moduleMap.js'
import { transformUnitW, formatModuleMap } from 'utils/tools'
import { mapState } from 'vuex'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]
export default {
  components: {
  },
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    },
    dtPageType: {
      type: String,
      default: '网课详情页-新版'
    },
    module_id: {
      type: [String, Number],
      default: 1
    }
  },
  data() {
    return {
      transformUnitW,
      formatModuleMap,
      activeTab: '',
      includesList: [],
      page: 1,
      pageSize: 5,
      containerTotal: 0,
      tabShowList: [
        { enLabel: 'Mooc_Common_JoinType_All', label: '全部', container_module_id: '', act_type: '' },
        { enLabel: 'NetCourse_Portal', label: '专区', container_module_id: 12, act_type: 13 },
        { enLabel: 'Mooc_CourseType_CourseList', label: '课单', container_module_id: 15, act_type: 15 },
        { enLabel: 'Mooc_CourseType_Mooc', label: '培养项目', container_module_id: 10, act_type: 11 }
      ],
      filterTabList: [{ enLabel: 'Mooc_Common_JoinType_All', label: '全部', container_module_id: '', act_type: '' }]
    }
  },
  computed: {
    ...mapState(['isBusy']),
    course_id() {
      return this.courseData.course_id || parseInt(this.$route.query.course_id)
    },
    emptyShow() {
      return this.includesList.length
    },
    swiperOption() {
      return {
        loop: false,
        // autoplay: {
        //   delay: 3000,
        //   stopOnLastSlide: false,
        //   disableOnInteraction: false
        // },
        autoplay: false,
        direction: 'horizontal',
        slidesPerView: 'auto',
        freeMode: false,
        lazy: {
          loadPrevNext: true
        },
        // 显示分页
        // pagination: {
        //   el: '.swiper-pagination',
        //   clickable: true // 允许分页点击跳转
        // },
        navigation: { // 设置点击箭头
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        on: {
          click: (e) => {
            const aId = e.target.getAttribute('data-item_id')
            const i = e.target.getAttribute('data-i') * 1
            if (aId) {
              const v = this.includesList[i].container_contents.find((e) => e.item_id === aId.toString())
              const project = this.includesList[i]
              this.toLink(project, v)
            }
          }
        }
      }
    },
    includesSwiperRef() {
      return (e) => {
        return `includesSwiperRef${e.item_id}`
      }
    },
    showModuleTips() {
      return ({ duration, word_num, play_total_count, module_id, item_sub_contents }) => {
        let tips = ''
        if ([1, 2, 4].includes(module_id)) { // 网络课, 面授课，活动
          tips = `${duration || 0}` + this.$langue('Mooc_ProjectDetail_TaskList_Minute', { defaultText: '分钟' })
        } else if ([7, 8].includes(module_id)) { // 案例，文章
          tips = `${word_num || 0}` + this.$langue('Mooc_ProjectDetail_TaskList_Words', { defaultText: '字' })
        } else if ([10].includes(module_id)) { // 培养项目
          tips = `${play_total_count || 0}` + this.$langue('Mooc_ProjectDetail_BasicInfo_Tasks', { defaultText: '项任务' })
        }
        return tips
      }
    },
    forMatTime() {
      return ({ start_time, end_time }) => {
        const endTime = end_time ? end_time.split(' ')[1] : ''
        return `${start_time} - ${endTime}`
      }
    },
    dtToPath() {
      return (type, row) => {
        const tabNameInfo = this.tabShowList.find((v) => this.activeTab === v.container_module_id)
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: this.dtPageType, 
            container: `包含此内容-${tabNameInfo.label}`,
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: row.item_name,
            act_type: row.act_type || '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${row.item_id}`
        }
      }
    }
  },
  watch: {
    isBusy(val) {
      if (val === '0') {
        setTimeout(() => {
          this.getIncludes()
        }, 800)
      }
    }
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val
      this.getIncludes()
    },
    handleCurrentChange(val) {
      this.page = val
      this.getIncludes()
    },
    changeTab(row) {
      this.activeTab = row.container_module_id
      this.page = 1
      this.getIncludes()
    },
    getIncludes() {
      if (this.isBusy === '1') return 
      // module_id: 专区12，课单15，培养项目10
      const param = {
        module_id: this.module_id,
        item_id: this.course_id,
        page: this.page,
        page_size: this.pageSize,
        container_module_id: this.activeTab
      }
      this.includesList = []
      includesContent(param).then((res) => {
        this.includesList = res?.records || []
        this.containerTotal = res?.total || 0
        const allPage = res?.pages || 0
        // 兼容第一页查不到数据的时候，从第二页查找数据
        if (!this.includesList?.length && this.page < allPage) {
          this.page++
          this.getIncludes() 
        }
        this.includesList.forEach((e) => {
          e.container_contents.forEach((j) => {
            let actMap = actTypes.find((el) => { return el.act_type === j.act_type })
            j.module_id = actMap?.module_id || ''
            j.act_type_name = actMap?.act_type_name || ''
            j.langKey = actMap?.langKey || ''
            j.item_labels = j.item_labels?.split(';') || []
            // 埋点
            window.BeaconReport('at_show_area', {
              eid: this.dtIncludesList('area', e, j),
              remark: this.dtIncludesList('remark', e, j)
            })
          })
          this.tabShowList.forEach((v) => {
            if (e.act_type === v.act_type) {
              this.filterTabList.push(v)
            }
          })
        })
        // 把第一次的结果tab数据缓存起来，后面过滤的时候tab数据不变
        if (!this.activeTab) {
          const map_list = new Map()
          this.filterTabList = this.filterTabList.filter((e) => !map_list.has(e.label) && map_list.set(e.label, 1)) 
          sessionStorage.setItem('filter_tab_list', JSON.stringify(this.filterTabList))
        } else {
          this.filterTabList = JSON.parse(sessionStorage.getItem('filter_tab_list'))
        }
      })
    },
    toPath(v) {
      const tabNameInfo = this.tabShowList.find((v) => this.activeTab === v.container_module_id)
      window.BeaconReport('at_click', {
        eid: `element_${this.course_id}_${v.item_id}_查看更多`,
        remark: JSON.stringify({
          page: this.courseData.course_name,
          page_type: this.dtPageType, 
          container: `包含此内容-${tabNameInfo.label}`,
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: '查看更多',
          act_type: v.act_type || '',
          page_id: '',
          container_id: '',
          terminal: 'PC'
        })
      })
      // 专区配置了url
      if (v.act_type === 13 && v.custom_link_url) {
        window.open(v.custom_link_url)
        return
      }
      let url = ''
      if (v.act_type === 11) { // 培养项目
        url = `${envName.courseWoaHost}training/mooc/projectDetail?mooc_course_id=${v.item_id}`
      } else if (v.act_type === 15) { // 课单
        url = `${envName.courseWoaHost}courselist/course-detail?id=${v.item_id}`
      } else if (v.act_type === 13) {
        url = `${envName.courseWoaHost}user/special?scheme_type=homepage&page_id=${v.item_id}`
      }
      if (!url) return
      window.open(url)
    },
    toLink(row, e) {
      window.BeaconReport('at_click', {
        eid: this.dtIncludesList('eid', row, e),
        remark: this.dtIncludesList('remark', row, e)
      })
      if (!e.item_url) return
      let url = e.item_url
      if (row.act_type === 13 && Number(e.act_type) === 2) { // 专区-网络课
        url = `${url}&from=SpecialArea&area_id=${row.item_id}`
      }
      console.log('打印链接', row, url)
      window.open(url)
    },
    dtIncludesList(type, row, item) {
      const tabNameInfo = this.tabShowList.find((v) => this.activeTab === v.container_module_id)
      if (type === 'remark') {
        return JSON.stringify({
          page: this.courseData.course_name,
          page_type: this.dtPageType, 
          container: `包含此内容-${tabNameInfo.label}`,
          click_type: 'data',
          content_type: item.module_name,
          content_id: item.item_id,
          content_name: item.item_name,
          act_type: item.act_type || '',
          page_id: '',
          container_id: '',
          terminal: 'PC'
        })
      } else if (type === 'eid') {
        return `element_${this.course_id}_${row.item_id}_${item.item_id}`
      } else {
        return `area_${this.course_id}_${row.item_id}_${item.item_id}`
      }
    },
    handleRight(val, item) {
      console.log('item', item)
      window.BeaconReport('at_click', {
        eid: this.dtCommon('eid', val, item),
        remark: this.dtCommon('remark', val, item)
      })
    },
    dtCommon(type, val, row) {
      const tabNameInfo = this.tabShowList.find((v) => this.activeTab === v.container_module_id)
      if (type === 'remark') {
        return JSON.stringify({
          page: this.courseData.course_name,
          page_type: this.dtPageType, 
          container: `包含此内容-${tabNameInfo.label}`,
          click_type: 'button',
          content_type: '',
          content_id: row.item_id,
          content_name: val,
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'PC'
        })
      } else if (type === 'eid') {
        return `element_${row.item_id}_${val}`
      } else {
        return `area_${row.item_id}_${val}`
      }
    }
  }
}
</script>
<style lang="less" scoped>
.includes-content-main {
  margin-top: 20px;
  padding: 20px 24px;
  background-color: #fff;
  border-radius: 8px;
  .top-tab {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .title {
      color: #333333;
      font-size: 16px;
      font-weight: bold;
      margin-right: 32px;
    }
    .tab-bg {
      background-color: #EFEFEF;
      color: #333333;
      font-size: 12px;
      display: inline-block;
      height: 22px;
      line-height: 22px;
      padding: 0 12px;
      border-radius: 4px;
      margin-right: 8px;
      cursor: pointer;
    }
    .active-tab-bg {
      background: #F5F8FF;
      color: #0052D9;
    }
    .tab-ri {
      i {
        font-weight: bold;
        font-size: 16px;
        color: #999999;
        margin-left: 4px;
        cursor: pointer;
      }
      i:hover {
        color: #666666
      }
      .last-right-page, .first-left-page {
        pointer-events: unset;
        cursor: not-allowed;
        color: #DCDCDC;
      }
      .last-right-page:hover, .first-left-page:hover {
        color: #DCDCDC;
      }
    }
  }
  .course-main {
    margin-top: 20px;
    background: #F6F6F6;
    padding: 12px;
    border-radius: 6px;
    .course-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .couse-title-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
    
        .left-title {
          display: flex;
          align-items: center;
          line-height: 22px;
          cursor: pointer;
    
          .title {
            color: #333333;
            font-size: 16px;
            font-weight: bold;
            display: inline-block;
          }
    
          .course-num {
            margin-left: 16px;
    
            i {
              color: #777777;
              font-weight: bold;
              font-size: 14px;
            }
          }
        }
      }
    }

    .course-play-box {
      display: flex;
      align-items: center;
      color: #777777;
      height: 20px;
      flex-shrink: 0;

      i {
        width: 14px;
        height: 14px;
        margin-right: 4px;
      }

      .item-icon + .item-icon {
        margin-left: 16px;
      }
      .item-icon {
        display: flex;
        align-items: center;

        .view-icon {
          background: url("~@/assets/img/watch.png") no-repeat center / cover;
        }
        .contents-icon {
          background: url("~@/assets/img/includes-content.png") no-repeat center / cover;
        }
      }
    }
    .swiper-content {
      // display: flex;
      // align-items: flex-start;
      .special-swiper-box {
        display: flex;
        align-items: center;
        margin-top: 8px;
        
        .item-special-swiper {
          width: 336px;
          height: 100px;
          margin-right: 12px;
          background-color: #fff;
          padding: 8px;
          cursor: pointer;
          border-radius: 6px;
          display: flex;
          .swiper-body {
            display: flex;
            align-items: flex-start;
          }
          .item-left-img {
            position: relative;
            font-size: 12px;
            font-weight: 500;
            width: 128px;
            height: 84px;
            margin-right: 16px;
            .extend-cover {
              width: 128px;
              height: 84px;
              border-radius: 4px;
              flex-shrink: 0;
              display: flex;
              align-items: center;
              justify-content: center;
              .error-cover img {
                width: 128px;
                height: 84px;
              }
            }
            .content-type {
              position: absolute;
              top: 4px;
              left: 4px;
              background: #0052D9;
              color: #fff;
              padding: 0 4px;
              border-radius: 4px;
              height: 18px;
              display: flex;
              align-items: center;
              font-size: 12px;
              line-height: 18px;
            }
            .time {
              position: absolute;
              bottom: 4px;
              right: 4px;
              background: #00000099;
              color: #fff;
              padding: 0 4px;
              border-radius: 4px;
              height: 18px;
              display: flex;
              align-items: center;
            }
          }
          .item-right {
            .content-name {
              color: #333333;
              font-weight: bold;
              line-height: 20px;
              opacity: 0.8;
            }
            .mid-tag {
              margin-top: 2px;
              flex-wrap: wrap;
              align-items: center;
              overflow: hidden;
              width: 176px;
              height: 22px;
              .tag {
                height: 22px;
                line-height: 22px;
                padding: 0px 6px;
                border-radius: 4px;
                background: #F5F7FA;
                color: #777777;
                margin-right: 8px;
                max-width: 82px;
                display: inline-block;
                flex-shrink: 0;
                font-size: 12px;
              }
            }
            .bottom-icon {
              margin-top: 4px;
              display: flex;
              color: #777777;
              font-size: 12px;
              .view-span {
                display: flex;
                align-items: center;
                line-height: 16px;
              }
              i {
                display: inline-block;
                width: 16px;
                height: 16px;
                margin-right: 4px;
              }
              .view-icon {
                background: url("~@/assets/img/watch.png") no-repeat center /cover;
              }
              .score-icon {
                background: url("~@/assets/img/score-line.png") no-repeat center /cover;
                margin-left: 20px;
              }
            }
          }
        }
       
        ::v-deep .swiper-button-prev,
        .swiper-button-next {
          &:after {
            display: none;
          }
  
          &.swiper-button-disabled {
            display: none;
          }
  
          top: 53px;
          width: 32px;
          height: 32px;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 50%;
          background: var(--Color, #FFF);
          box-shadow: 0 8px 10px -5px #00000014,
          0 16px 24px 2px #0000000a,
          0 6px 30px 5px #0000000d;
  
          .icon-left,
          .icon-right {
            display: inline-block;
            width: 20px;
            height: 20px;
            font-size: 20px;
          }
  
          .icon-left {
            background: url('~@/assets/mooc-img/chevron-left.png') no-repeat center / cover;
          }
  
          .icon-right {
            background: url('~@/assets/mooc-img/chevron-right.png') no-repeat center / cover;
          }
        }
        .swiper-button-prev:hover, .swiper-button-next:hover {
          background-color: #0052D9;
          box-shadow: 0 4px 18.67px 2.67px #0000000d, 0 10.67px 13.33px 1.33px #0000000f, 0 6.67px 6.67px -4px #0000001a;
          .icon-left {
            background: url('~@/assets/img/white-left.png') no-repeat center / cover;
          }
  
          .icon-right {
            background: url('~@/assets/img/white-right.png') no-repeat center / cover;
          }
        }
      }
      .mooc-swiper-box {
        height: 88px;
        .item-special-swiper {
          height: 88px;
          padding: 12px;
          .mooc-swiper-body {
            display: flex;
            align-items: flex-start;
            flex-direction: column;

            .tag-name {
              padding: 0 4px;
              border-radius: 2px;
              background: #F5F5F7;
              line-height: 18px;
              color: #777777;
              font-size: 12px;
              margin-right: 8px;
            }
            .content-name {
              line-height: 20px;
              font-weight: bold;
              color: #333333;
              opacity: 0.8;
            }
            
            .mooc-bottom {
              color: #777777;
              font-size: 12px;
              height: 17px;
              margin-top: 4px;
              .circle {
                margin-left: 4px;
                margin-right: 4px;
              }
            }
          }
        }
      }
  
      .check-more-box {
        background-color: #fff;
        color: #006FFF;
        padding: 6px;
        font-size: 12px;
        height: 100px;
        max-width: 36px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        border-radius: 0 4px 4px 0;
        cursor: pointer;
        .tips {
          writing-mode: vertical-rl;
          letter-spacing: 2px;
        }
        .more-icon {
          display: inline-block;
          width: 16px;
          height: 16px;
          background: url("~@/assets/img/includes-more.png") no-repeat center / cover;
          margin-top: 4px;
        }
      }
      .mooc-check-more {
        height: 88px;
      }
    }
    @media screen and (max-width: 1660px) {
      .left-title {
        width: 400px
      }
    }

    @media screen and (min-width: 1661px) {
      .left-title {
        width: 690px;
      }
    }
  }
  :deep(.includes-pagination) {
    .btn-prev, .btn-next {
      border: unset
    }
    .el-pager {
      li {
        border: unset;
        border-radius: 3px;
        border: 1px solid #DCDCDC;
        line-height: 22px;
      }
      li + li {
        margin-left: 8px;
      }
      .btn-quicknext {
        border: unset;
      }
      .more::before {
        line-height: 24px;
      }
      .number.active {
        background: #0052D9 !important;
        color: #fff !important;
      }
    }
  }
}
</style>
