<template>
  <div class="user">
    <link rel="stylesheet" :href="linkHref" />
    <!-- 顶部区域 -->
    <!-- <div class="header-nav">
      <div class="header">
        <div class="f-left">
          <img src="@/assets/mooc-img/logo.png" class="logo" alt="" @click="toPage('home')" />
        </div>
        <div class="mid">
          <ul>
            <li 
              :class="[{ 'isShowLine': activeNav === item.menu_id }, 'mid-li-item']" 
              v-for="(item, index) in navList" 
              :key="index" 
              @click="toPath(item)"
              >
              {{ item.menu_name }}
            </li>
          </ul>
        </div>
        <div class="f-right">
          <el-autocomplete 
          class="search" 
          popper-class="mooc-autocomplete" 
          v-model="moocValue"
          :fetch-suggestions="querySearch" 
          :placeholder="$langue('Mooc_Home_Search_Search', { defaultText: '搜索' })"
          clearable 
          @keyup.enter.native="enterQuerySearch"
          @select="handleSelect"
          >
            <i class="el-icon-search" slot="suffix" @click="enterQuerySearch"></i>
            <template slot-scope="{ item }">
              <div class="search-item">
                <span class="search-label">{{ item.label }}</span>
                <span class="search-time">{{ item.value }}</span>
              </div>
            </template>
          </el-autocomplete>
          <div>
            <el-avatar size="large" class="user-avatar f-left" :src="avatar" @click.native="toPage('personal')">
              <img :src="defaultAvatar" />
            </el-avatar>
            <span class="staff-name">{{ staffName }}</span>
          </div>
        </div>
      </div>
    </div> -->
    <!-- 内容区域 -->
    <div id="content">
      <router-view :key="$route.fullPath" ref="container" />
    </div>
    <!-- 培养项目弹窗 -->
    <train-Project v-if="trainProjectDialog" :visible.sync="trainProjectDialog"></train-Project>
  </div>
</template>
  
<script>
import { getStatistics, searchMooc, getNavData } from 'config/mooc.api.conf'
import { getAvatar } from 'utils/tools'
import trainProject from './src/trainProject.vue'
import { mapState } from 'vuex'
const statusInfo = {
  1: 'Mooc_ProjectDetail_TrainingProgress_NotStarted', // 未开始
  2: 'Mooc_ProjectDetail_TrainingProgress_InProgress', // 进行中
  3: 'Mooc_ProjectDetail_TrainingProgress_Delayed', // 已逾期
  4: 'Mooc_ProjectDetail_TrainingProgress_Finished' // 已完成
}
const statusInfoZNCN = {
  1: '未开始', // 未开始
  2: '进行中', // 进行中
  3: '已逾期', // 已逾期
  4: '已完成' // 已完成
}
export default {
  name: 'user',
  components: {
    trainProject
  },
  data() {
    return {
      moocValue: '',
      options: [],
      loading: false,
      avatar: require('@/assets/img/avatar.png'),
      defaultAvatar: require('@/assets/img/avatar.png'),
      staffName: '',
      message: 120,
      addCourseShow: false,
      linkVisible: false,
      messageVisible: false,
      notEdit: false, // 当前页面不是课单编辑
      isCenter: false, // 当前页面是否是创作者中心
      statisticsData: {},
      trainProjectDialog: false,
      statusInfo,
      statusInfoZNCN,
      navList: [],
      activeNav: '',
      linkHref: ''
    }
  },
  watch: {
    // 监听路由变化，显示顶部菜单
    $route: {
      handler(to) {
        // this.isCenter = to.name === 'content-manage' || to.name === 'message'
        // this.notEdit = to.name !== 'course-edit'
        // if (
        //   to.name !== 'course-edit' &&
        //   this.$store.state.messageCount.total === undefined
        // ) {
        //   getMessageCount().then((res) => {
        //     this.$store.commit('setMessageCount', res)
        //   })
        // }
      },
      immediate: true
    },
    '$store.state.userInfo': {
      handler(val) {
        this.avatar = getAvatar(val.staff_name)
        this.staffName = val.staff_name
        // if (val.staff_name) {
        //   this.watermark.textContent = val.staff_name
        // }
        let root = document.documentElement
        if (val.staff_name && !document.getElementById('graphic-common-head') && !['mooc', 'spoc'].includes(this.$route.query.from)) {
          root.style.setProperty('--app-height', 'calc(100% - 50px)')
          this.headerIsLoad = true
          this.linkHref = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeaderStyle.css` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeaderStyle.css`  
          const commonHeaderJsUrl = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeader.js` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeader.js`
          this.loadHeadJS(commonHeaderJsUrl, function() {
            if (window.$qlCommonHeader) {
              window.$qlCommonHeader.create({
                staff_name: val.staff_name,
                dev: !(process.env.NODE_ENV === 'production')
              })

              setTimeout(() => {
                let el = document.getElementsByClassName('common-header-occupy')
                if (el.length > 0) {
                  console.log('El', el[0].offsetHeight)
                  document.getElementById('app').style.height = `calc(100% - ${el[0].offsetHeight}px)`
                }
              }, 500)
            }
          })
        } else {
          root.style.setProperty('--app-height', '100%') 
        }
      },
      immediate: true
    }
  },
  computed: {
    ...mapState(['bgColor'])
  },
  mounted() {
    // this.getNav()
  },
  methods: {
    // 获取登陆用户信息
    loadHeadJS(url, callback) {
      var script = document.createElement('script')
      var fn = callback || function () { }
      script.type = 'text/javascript'
      script.id = 'graphic-common-head'
      // IE
      if (script.readyState) {
        script.onreadystatechange = function () {
          if (script.readyState === 'loaded' || script.readyState === 'complete') {
            script.onreadystatechange = null
            fn()
          }
        }
      } else {
        // 其他浏览器
        script.onload = function () {
          fn()
        }
      }
      script.src = url
      document.getElementsByTagName('head')[0].appendChild(script)
    },
    toPath(item) {
      this.activeNav = item.menu_id
      window.open(item.url)
    },
    getNav() {
      const params = {
        lang: 'zh',
        page_id: 0,
        menu_group: 2
      }
      getNavData(params).then((res) => {
        console.log('res', res)
        this.navList = res
      })
    },
    getStatisticsData() {
      getStatistics().then((res) => {
        this.statisticsData = res
      })
    },
    // 搜索课单
    querySearch(query, cb) {
      this.moocValue = query
      this.options = []
      searchMooc({ keywords: this.moocValue, size: 10 }).then((res) => {
        if (res.content?.length) {
          res.content.forEach((e, i) => {
            res.weight.forEach((v, j) => {
              if (i === j) {
                this.options.push({
                  label: e, 
                  value: this.$langue('Mooc_Home_Search_SearchProjectCount', { count: v, defaultText: `搜索${v}次` })
                })
              }
            })
          })
        }
        cb(this.options)
      })
    },
    // 回车搜索
    enterQuerySearch() {
      this.handleSelect({ label: this.moocValue })
    },
    handleSelect(item) {
      this.moocValue = item.label
      const obj = {
        production: {
          woa: 'https://learn.woa.com/mat/user/search',
          oa: 'http://v8.learn.oa.com/mat/user/search'

        },
        test: {
          woa: 'https://test-learn.woa.com/mat/user/search',
          oa: 'http://test.v8.learn.oa.com/mat/user/search'
        }
      }
      const key = location.hostname.endsWith('.woa.com') ? 'woa' : 'oa'
      const url = process.env.NODE_ENV === 'production' ? obj['production'][key] : obj['test'][key]
      const commonUrl = url + `?keywords=${item.label}`
      window.open(commonUrl)
    },
    // 跳转到对应的页面
    toPage(key) {
      const obj = {
        production: {
          // Qlearning首页
          home: 'https://portal.learn.woa.com/user/home',
          // Qlearning个人中心
          personal: 'https://learn.woa.com/user/my'
          // // 写文章
          // note: 'http://ntsapps.oa.com/note/main/note-write/note-write-page',
          // // 写案例
          // case: 'http://anli.oa.com/case/',
          // // 成为行家
          // hj: 'http://hangjia.woa.com/blank-main/hangj_main-page/expert-step-one',
          // // 讲师/课程认证
          // apply: 'http://lec.learn.oa.com/#/staff/apply',
          // // 申请直播
          // live: 'http://live.learn.oa.com'
        },
        test: {
          // Qlearning首页
          home: 'https://test-portal-learn.woa.com/user/home',
          // Qlearning个人中心
          personal: 'https://test-learn.woa.com/user/my'
          // // 写文章
          // note: 'http://test.ntsapps.oa.com/note/main/note-write/note-write-page',
          // // 写案例
          // case: 'http://anli.oa.com/case/',
          // // 成为行家
          // hj: 'http://hangjia.woa.com/blank-main/hangj_main-page/expert-step-one',
          // // 讲师/课程认证
          // apply: 'http://lec.learn.oa.com/#/staff/apply',
          // // 申请直播
          // live: 'http://live.learn.oa.com'
        }
      }
      this.linkVisible = false
      if (process.env.NODE_ENV === 'production') {
        // 生产环境
        window.open(obj['production'][key])
      } else {
        // 测试环境
        window.open(obj['test'][key])
      }
    },
    // 跳转到详情
    toDetail() {
      const { last_view_id } = this.statisticsData
      const { href } = this.$router.resolve({
        name: 'projectDetail',
        query: {
          mooc_course_id: last_view_id
        }
      })
      window.open(href, '_blank')
    },
    // 跳到课单广场页面
    toHome() {
      this.linkVisible = false
      this.$router.push({
        name: 'home'
      })
    },
    // 跳到创作者中心页面
    toCreatorCenter() {
      const { href } = this.$router.resolve({
        name: 'creator-center'
      })
      window.open(href, '_blank')
    },
    // 创建成功, 课单广场/创作者中心重新查询
    onCreated() {
      const container = this.$refs.container
      container.getCourse && container.getCourse()
      container.onSearch && container.onSearch(1)
    },
    // 打开创建弹窗
    toAdd() {
      this.addCourseShow = true
      this.linkVisible = false
    },
    // 跳转到消息页面
    toMessage(type) {
      this.$router.push({
        name: 'message',
        query: {
          type
        }
      })
    }
  }
}
</script>
<style lang="less">
@import '~assets/css/graphic-common.less';
@import '~assets/css/common.less';
@import '~assets/css/center.less';
.mooc-autocomplete{
  width: 300px !important;
  .el-scrollbar{
    .search-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .search-label {
      display: block;
      width: 180px;
      font-size: 13px;
      color: #434343;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .search-time {
      float: right;
      font-size: 13px;
      color: #999;
    }
  }
}
</style>
<style lang="less">
#app{
  height: calc(100% - 62px) !important;
}
</style>
<style lang="less" scoped>
.user {
  background-color: #f0f4fa;
  height: 100%;
  min-width: 1440px;
  position: relative;
  display: flex;
  flex-direction: column;
  #content {
    width: 100%;
    background-color: #f0f4fa;
    flex-grow: 1;
    // margin-top: 60px;
  }
}
</style>
