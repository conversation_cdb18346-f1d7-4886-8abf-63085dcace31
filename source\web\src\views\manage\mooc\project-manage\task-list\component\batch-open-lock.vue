<template>
  <div class="batch-open-lock-dialog">
    <el-dialog
      width="430px"
      :visible="visible"
      :title="dialogInfo.title"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <div class="status-body">
        <p class="num">已选择{{initData.num}}个任务</p>
        <el-radio-group v-model="status">
          <el-radio 
          v-for="item in dialogInfo.radioOptions" 
          :label="item.label"
          :key="item.value"
          >
          {{ item.value }}
        </el-radio>
        </el-radio-group>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取 消</el-button>
        <el-button @click="submit" size="small" type="primary">确认关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    initData: {
      type: Object,
      default: () => ({})
    }
  },
  computed: {
    dialogInfo() {
      const { type } = this.initData
      if (type === 'lock') {
        return {
          title: '设置任务状态',
          radioOptions: [
            { label: 1, value: '解锁任务' },
            { label: 2, value: '锁定任务' }
          ]
        }
      }
      return {
        title: '设置任务性质',
        radioOptions: [
          { label: true, value: '应学' },
          { label: false, value: '选学' }
        ]
      }
    }
  },
  data() {
    return {
      status: 1
    }
  },
  mounted() {
    const { type } = this.initData
    this.status = type === 'lock' ? 1 : true
  },
  methods: {
    submit() {
      const { type } = this.initData
      this.$emit('handleLockStatus', this.status, type)
      this.cancel()
    },
    cancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less"  scoped>
.batch-open-lock-dialog {
  .status-body {
    .num {
      color: rgba(0, 0, 0, 0.6);
      margin-bottom: 24px;
    }
  }
}
</style>
