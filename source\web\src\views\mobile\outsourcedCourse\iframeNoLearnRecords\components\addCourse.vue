<template>
  <van-popup
    class="add-course-popup"
    v-model="visible"
    round
    :overlay="true"
    position="bottom"
    get-container="body"
    :style="{ height: '50%' }"
    @click-overlay="cancel"
  >
    <div class="popup-body">
      <div class="title">{{ $langue('Article_AddCourseList', { defaultText: '添加到课单' }) }}</div>
      <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
        <van-list
          v-if="tableData.length"
          v-model="loading"
          :finished="finished"
          :finished-text="$langue('Mooc_Home_NoMore', { defaultText: '没有更多了' })"
          @load="onLoad"
          ref="pullRefreshList"
        >
          <van-checkbox-group v-model="checkGroup">
            <div
              :class="[
                { 'disabled-check-box': item.have_content },
                'check-box'
              ]"
              v-for="(item, index) in tableData"
              :key="index"
            >
              <span>{{ item.name }}</span>
              <van-checkbox
                :name="item.cl_id"
                :disabled="item.have_content"
                ref="checkboxes"
              />
            </div>
          </van-checkbox-group>
        </van-list>
        <div v-else class="empty-list-box">
          <img :src="require('@/assets/img/mobile/empty-note.png')" />
          <div>{{ $langue('Mooc_Common_NoData', { defaultText: '暂无数据' }) }}</div>
        </div>
      </van-pull-refresh>
      <div class="confirm-btn" @click="confirm">{{ $langue('Mooc_TaskDetail_Confirm', { defaultText: '确认' }) }}</div>
    </div>
  </van-popup>
</template>
<script>
import { getUserCourseList, addClassList } from 'config/api.conf'
import { Toast } from 'vant'
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    courseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      size: 5,
      current: 1,
      checkGroup: [],
      tableData: [],
      loading: false,
      finished: false,
      refreshing: false,
      visible: false
    }
  },
  watch: {
    show(val) {
      this.visible = val
      if (val) {
        this.current = 1
        this.tableData = []
        // 滚动条回到顶部
        this.$nextTick(() => {
          if (this.$refs.pullRefreshList) {
            this.$refs.pullRefreshList.$el.scrollTop = 0
            this.checkGroup = []
          }
        })
        this.onLoad()
      }
    }
  },
  computed: {
    course_id() {
      return this.$route.query.course_id || ''
    }
  },
  methods: {
    onLoad() {
      this.loading = true
      let params = {
        page_no: this.current++,
        page_size: this.size,
        module_id: 1 || '',
        item_id: this.course_id || ''
      }
      getUserCourseList(params).then((res) => {
        if (this.refreshing) {
          this.tableData = []
          this.refreshing = false
        }
        this.tableData.push(...res.records)
        // 复选框回显
        this.tableData.forEach((v) => {
          if (v.have_content) {
            this.checkGroup.push(v.cl_id)
          }
        })
        this.loading = false
        if (this.tableData.length >= res.total) {
          this.finished = true
        }
      })
    },
    // 确认
    confirm() {
      const haveCheck = []
      this.tableData.forEach((v) => {
        if (v.have_content) {
          haveCheck.push(v.cl_id)
        }
      })
      if (this.checkGroup?.length === haveCheck?.length) {
        Toast(this.$langue('Mooc_TaskDetail_AddOneRecord', { defaultText: '请至少添加一条数据' }))
        return
      }
      const list = this.checkGroup.filter((e) => !haveCheck.includes(e))
      const { course_title, course_intro } = this.courseData
      const href = `https://ihr.tencent.com/5FD89y/${this.course_id}`
      const params = {
        cl_ids: list,
        content_details: [
          {
            content_name: course_title,
            description: course_intro,
            href,
            module_id: 1,
            module_name: '网络课',
            item_id: this.course_id
          }
        ]
      }
      addClassList(params).then((res) => {
        this.cancel()
        Toast(this.$langue('Mooc_TaskDetail_AddSucessed', { defaultText: '添加成功' }))
      })
    },
    cancel() {
      this.visible = false
      this.$emit('update:show', false)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      // 重新加载数据
      this.current = 1
      this.tableData = []
      this.onLoad()
    },
    toggle(index) {
      this.$refs.checkboxes[index].toggle()
    }
  }
}
</script>
<style lang="less" scoped>
.add-course-popup {
  overflow-y: hidden;
  .popup-body {
    height: 100%;
    position: relative;
    display: flex;
    flex-direction: column;
    .van-pull-refresh {
      flex: 1;
    }
    .van-list {
      width: 100%;
      height: 100%;
      overflow: auto;
    }
    .empty-list-box {
      text-align: center;
      color: #000000e6;
      font-size: 14px;
      margin-top: 10px;
      img {
        width: 160px;
        height: 160px;
      }
    }
  }
  .check-box {
    span {
      max-width: 310px;
    }
    min-height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #000000e6;
    font-size: 16px;
    margin: 0 16px;
    border-bottom: 1px solid #efefef;
  }
  .disabled-check-box {
    color: #00000042;
    :deep(.van-checkbox__icon--disabled) {
      .van-icon {
        background-color: #ebedf0;
        border-color: unset;
        color: #fff;
      }
    }
  }
  .title {
    height: 48px;
    color: #000000e6;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    line-height: 48px;
    border-bottom: 1px solid #efefef;
  }
  .confirm-btn {
    width: 100%;
    height: 56px;
    line-height: 56px;
    text-align: center;
    color: #0052d9ff;
    font-size: 16px;
    font-weight: 600;
    border-top: 1px solid #ebedf0;
    background-color: #fff;
  }
}
</style>
