<template>
  <el-dialog
  class="set-year-dialog"
  :title="isEdit ? '添加内容' : '编辑'" 
  :visible.sync="visible" 
  width="900px" 
  :close-on-click-modal="false"
  :before-close="closeDialog"
  >
    <div class="dialog-main">
      <el-form ref="form" :model="form" inline :rules="rules">
        <el-row>
          <el-form-item label="选择年份" prop="year">
            <el-select v-model="form.year" placeholder="请选择年份" size="small" class="select-w">
              <el-option
                v-for="item in timerList"
                :key="item.value"
                :label="item.label"
                :value="item.label">
              </el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-form-item label="人群分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择人群分类" size="small" class="select-w">
            <el-option
              v-for="item in personType"
              :key="item.value"
              :label="item.label"
              :value="item.label">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="order_no">
          <el-input
            placeholder="请输入"
            v-model="form.order_no"
            size="small"
            class="input-w"
          />
          <span class="input-tips">仅支持输入1-10的数字</span>
        </el-form-item>
      </el-form>
      <div class="content-m" v-if="isEdit">
        <div class="content-title">内容管理</div>
        <div class="content-form">
          <div class="dform-title common">
            <div class="c-t">内容标题</div>
            <el-input
              placeholder="请输入内容标题"
              v-model="searchData.keywords"
              size="small"
              class="dform-title"
            />
          </div>
          <div class="dform-type common">
            <div class="c-t">内容类型</div>
            <div class="dform-content">
              <el-radio v-model="searchData.moduleId" :label="undefined" @change="clearSelect(null)">全部</el-radio>
              <el-radio 
                v-model="searchData.moduleId" 
                v-for="item of moduleInfo" 
                :class="[item.module_id === 15 ? 'showFalse' : '']"
                :key="item.module_id" 
                :label="item.module_id" 
                @change="clearSelect(null)"
                >
                {{ item.module_name }}
              </el-radio>
            </div>
          </div>
          <!-- 一级分类 -->
          <div class="dform-type common" v-if="classify_field && categoryLevel1.length > 0">
            <span class="c-t">{{ classify_field[0].text }}</span>
            <div class="dform-content">
              <el-radio v-model="classifySelect[classify_field[0].field]" @change="clearSelect(0)" :label="undefined">全部</el-radio>
              <el-radio 
                v-model="classifySelect[classify_field[0].field]" 
                @change="clearSelect(0)"
                v-for="item of categoryLevel1" 
                :key="item.item_id" 
                :label="item.item_id"
                >
                {{ item.item_name }}
              </el-radio>
            </div>
          </div>
          <!-- 二级分类 -->
          <div class="dform-type common" v-if="classify_field && categoryLevel2.length > 0" >
            <span class="c-t">{{ classify_field[1].text }}</span>
            <div class="dform-content">
              <el-radio v-model="classifySelect[classify_field[1].field]" @change="clearSelect(1)" :label="undefined">全部</el-radio>
              <el-radio 
                v-model="classifySelect[classify_field[1].field]" 
                @change="clearSelect(1)"
                v-for="item of categoryLevel2" 
                :key="item.item_id" 
                :label="item.item_id"
                >
                {{ item.item_name }}
              </el-radio>
            </div>
          </div>
          <!-- 三级分类 -->
          <div class="dform-type common" v-if="classify_field && categoryLevel3.length > 0">
            <span class="c-t">{{ classify_field[2].text }}</span>
            <div class="dform-content">
              <el-radio v-model="classifySelect[classify_field[2].field]" :label="undefined" @change="clearSelect(null)">全部</el-radio>
              <el-radio 
                v-model="classifySelect[classify_field[2].field]" 
                v-for="item of categoryLevel3" 
                :key="item.item_id"
                :label="item.item_id" 
                @change="clearSelect(null)"
                >
                {{ item.item_name }}
              </el-radio>
            </div>
          </div>
          <div class="dform-grade common">
            <div class="c-t">认证等级</div>
            <el-select v-model="searchData.courseLevel" placeholder="请选择认证等级" size="small" class="grade-select-w">
              <el-option
                v-for="item in certificationLevelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </div>
          <div class="opera-btn">
            <el-button size="small" icon="el-icon-refresh" @click="reset()">重置</el-button>
            <el-button size="small" type="primary" @click="onSearch(1)">搜索</el-button>
          </div>
          <el-table
            ref="table"
            :data="tableParams.list"
            header-row-class-name="table-header-style"
            row-class-name="table-row-style"
            @selection-change="handleSelectionChange"
          >
          <el-table-column
            type="selection"
            width="55">
          </el-table-column>
            <el-table-column
              v-for="item of tableCols"
              :key="item.prop"
              :prop="item.prop"
              :label="item.label"
              :width="item.width ? item.width : 'auto'"
              :sortable="item.sortable ? item.sortable : false"
              style="overflow: hidden"
              show-overflow-tooltip
              align="left"
            >
              <template slot-scope="{ row }">
                <a style="color: #3464E0;" v-if="item.prop === 'content_url'" target="_blank" :href="row.content_url">{{ row.content_url }}</a>
                <span v-else>{{ row[item.prop] ? row[item.prop] : '-' }}</span>
              </template>
            </el-table-column>
          </el-table>
          <div class="table-pagination">
            <el-pagination
              :hide-on-single-page="tableParams.list.length === 0"
              @size-change="handleSizeChange"
              @current-change="onSearch"
              :current-page="searchData.pageNum"
              :page-size="searchData.pageSize"
              :page-sizes="[5, 10, 20, 30, 50, 100]"
              layout="total,  prev, pager, next, sizes, jumper"
              :total="tableParams.total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="closeDialog">取 消</el-button>
      <el-button size="small" type="primary" @click="handleSave">{{ isEdit ? '添加' : '提交' }}</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { searchsiteApi, getModuleInfoApi, getNetClassifyApi } from '@/config/mooc.api.conf'
import { addContent, editRank } from '@/config/api.conf'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      form: {
        year: '',
        category: '',
        order_no: ''
      },
      certificationLevelOptions: [
        { label: '全部', value: '' },
        { label: '公司级', value: 1 },
        { label: 'BG级', value: 2 },
        { label: '部门级', value: 3 },
        { label: '个人分享', value: 4 }
      ],
      personType: [
        { label: '全员', value: 0 },
        { label: 'T族', value: 1 },
        { label: 'P族', value: 2 },
        { label: 'M族', value: 3 },
        { label: 'D族', value: 4 },
        { label: 'S族', value: 5 },
        { label: '领导力', value: 6 },
        { label: '新员工', value: 7 }
      ],
      tableCols: [
        { label: '内容ID', prop: 'item_id' },
        { label: '内容标题', prop: 'content_name' },
        { label: '内容类型', prop: 'module_name' },
        { label: '认证等级', prop: 'course_level' },
        { label: '内容标签', prop: 'labelsText' },
        { label: '内容链接', prop: 'content_url' }
      ],
      tableParams: {
        total: 0,
        list: []
      },
      searchData: {
        keywords: '',
        moduleId: '',
        courseLevel: '',
        classify: [],
        pageNum: 1,
        pageSize: 5
      },
      // 分类选项数据
      classifySelect: {},
      moduleInfo: [],
      netClassify: [],
      singleSelectdata: [],
      isEdit: true,
      rules: {
        order_no: [
          { required: true, pattern: /^([1-9]|10)$/, message: '请输入1-10的正整数', trigger: ['change', 'blur'] }
        ],
        year: [ 
          { required: true, message: '请选择年份', trigger: ['change', 'blur'] }
        ],
        category: [ 
          { required: true, message: '请选择人群分类', trigger: ['change', 'blur'] }
        ]
      }
    }
  },
  computed: {
    timerList() {
      const currentYear = new Date().getFullYear()
      let years = []
      for (let i = 2020; i <= currentYear; i++) {
        years.push({
          label: i,
          value: i
        })
      }
      return years
    },
    currentModuleInfoItem() {
      console.log('moduleInfo', this.moduleInfo)
      return this.moduleInfo.find(i => i.module_id === this.searchData.moduleId) || null
    },
    // 模块下的分类配置
    classify_field() {
      if (!this.currentModuleInfoItem || !this.currentModuleInfoItem.classify_field) {
        return null
      }
      return this.currentModuleInfoItem.classify_field
    },
    // 一级分类选项
    categoryLevel1() {
      if (this.searchData.moduleId === 1) {
        return this.netClassify
      } else if (this.moduleInfo.length !== 0 && this.searchData.moduleId) {
        return this.currentModuleInfoItem.classify_data || []
      }
      return []
    },
    // 二级分类选项
    categoryLevel2() {
      let fieldValue = this.classifySelect[this.classify_field[0].field]
      if (this.categoryLevel1.length > 0 && fieldValue) {
        let item = this.categoryLevel1.find(i => i.item_id === fieldValue)
        return (item && item.child) ? item.child : []
      }
      return []
    },
    // 三级分类选项
    categoryLevel3() {
      let fieldValue = this.classifySelect[this.classify_field[1].field]
      if (this.categoryLevel2.length > 0 && fieldValue) {
        let item = this.categoryLevel2.find(i => i.item_id === fieldValue)
        return (item && item.child) ? item.child : []
      }
      return []
    }
  },
  watch: {
    'searchData.moduleId': {
      immediate: true,
      handler(val) {
        this.searchData.classify = []
        if (!this.classify_field) {
          return
        }

        let classifySelect = {}
        for (let item of this.classify_field) {
          if (item.field) {
            classifySelect[item.field] = this.classifySelect[item.field] || undefined
          }
        }
        this.classifySelect = classifySelect
      }
    },
    classifySelect: {
      deep: true,
      handler() {
        let classify = []
        for (let field of Object.keys(this.classifySelect)) {
          if (this.classifySelect[field]) {
            classify.push({ field, id: this.classifySelect[field] })
          }
        }
        this.searchData.classify = classify
      }
    }
  },
  created() {
    // 设置默认值
    // 模块默认全部
    if (!this.searchData.moduleId) {
      this.$set(this.searchData, 'moduleId', undefined)
    }
  },
  mounted() {
    this.getNetClassify()
    this.getModuleInfo()
    this.getSearchData()
    if (this.searchData.classify) {
      for (let item of this.searchData.classify) {
        this.classifySelect[item.field] = item.id
      }
    }
  },
  methods: {
    edit(data) {
      this.isEdit = false
      const { year, category, order_no, id } = data
      this.form = {
        year,
        category,
        order_no,
        id
      }
    },
    getNetClassify() {
      getNetClassifyApi().then(res => {
        this.netClassify = res.data
      })
    },
    clearSelect(level) {
      if (level === null) return

      // 选择上级分类后需要把下级分类的选项清空，否则搜索会出错
      if (level === 0) {
        if (this.classify_field[1]) {
          this.classifySelect[this.classify_field[1].field] = undefined
        }
        if (this.classify_field[2]) {
          this.classifySelect[this.classify_field[2].field] = undefined
        }
      } else if (level === 1) {
        if (this.classify_field[2]) {
          this.classifySelect[this.classify_field[2].field] = undefined
        }
      }
    },
    getModuleInfo() {
      getModuleInfoApi().then(res => {
        let moduleArr = [] 
        let result = []
        // 返回的moduleInfo是个对象，需要转换成数组
        for (let key of Object.keys(res.moduleInfo)) {
          // 0是综合，综合不需要作为选项，过滤掉
          if (key === '0') continue
          moduleArr.push(res.moduleInfo[key])
        }
        for (let item of moduleArr) {
          if (item.module_id !== 99) {
            result.push(item)
          }
        }
        this.moduleInfo = result
      })
    },
    getSearchData() {
      const { keywords, classify, courseLevel, moduleId, pageNum, pageSize } = this.searchData
      const params = {
        keywords,
        classify,
        sortBy: 'created_at',
        courseLevel,
        moduleId,
        pageNum,
        pageSize
      }
      searchsiteApi(params).then((res) => {
        this.tableParams.total = res.total
        this.tableParams.list = res.list.map((e) => {
          return {
            ...e,
            labelsText: e.labels?.length > 0 && e.labels.join(';')
          }
        })
      })
    },
    closeDialog() {
      this.$emit('update:visible', false)
      this.$refs['form'].resetFields()
    },
    handleSave() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          let commonApi = null
          let params = null
          if (!this.isEdit) { // 编辑
            commonApi = editRank
            params = this.form
          } else { // 新增
            commonApi = addContent
            if (this.singleSelectdata.length === 0) return this.$message.warning('请至少勾选一条数据')
            const { year, category, order_no } = this.form
            const { content_name, item_id, content_url, act_type } = this.singleSelectdata[0]
            params = {
              year,
              category,
              order_no,
              act_type,
              course_id: item_id,
              course_name: content_name,
              course_url: content_url
            }
          }
          commonApi(params).then((res) => {
            this.$message.success('添加成功')
            this.$emit('getQueryData')
            this.closeDialog()
          })
        } else {
          return false
        }
      })
    },
    handleSelectionChange(e) {
      if (e.length > 1) {
        this.$message.warning('请勾选单个内容')
        this.$refs.table.clearSelection()
      } else {
        this.singleSelectdata = e
      }
    },
    handleSizeChange(val) {
      this.searchData.pageSize = val
      this.getSearchData()
    },
    onSearch(num) {
      this.searchData.pageNum = num
      this.getSearchData()
    },
    reset() {
      this.searchData = {
        keywords: '',
        sortBy: 'created_at',
        courseLevel: '',
        order_no: '',
        pageNum: 1,
        pageSize: 5
      }
      this.getSearchData()
    }
  }
}
</script>
<style lang="less" scoped>
.set-year-dialog {
  :deep(.el-dialog) {
    .el-dialog__header {
      border-bottom: 0;
      padding: 32px 32px 16px;
    }
    .el-dialog__body {
      padding: 0 32px 14px;
      .el-dialog__footer {
        padding: 8px 0 24px;
      }
    }
  }
  :deep(.el-form-item) {
    margin-right: 44px;
    .select-w {
      width: 304px;
    }
    .input-w {
      width: 120px;
    }
    .input-tips {
      margin-left: 12px;
      color: #00000099;
    }
    .grade-select-w {
      width: 280px;
    }
    .el-form-item__label {
      padding-right: 20px;
      color: #000000e6;
    }
  }
  .content-m {
    .content-title {
      color: #00000099;
      font-weight: bold;
    }
    .common {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      line-height: 32px;
      .c-t {
        color: #000000e6;
        flex-shrink: 0;
        margin-right: 20px;
      }
    }
    .content-form {
      padding: 16px;
      background-color: #F9F9F9;
      border-radius: 4px;
      margin-top: 8px;
      :deep(.el-checkbox-group) {
        .el-checkbox {
          margin-right: 16px;
          margin-bottom: 16px;
          .el-checkbox__label {
            color: #000000e6;
          }
        }
      }
      .dform-type {
        display: flex;
        align-items: flex-start;
      }
      .opera-btn {
        text-align: right;
        margin-bottom: 16px;
        .el-button {
          width: 80px;
        }
      }
      :deep(.el-table) {
        border: 1px solid #eee;
        .table-header-style th {
          padding: 6px 0;
          color: #00000099;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 14px;
        }
      }
    }
  }
}
</style>
