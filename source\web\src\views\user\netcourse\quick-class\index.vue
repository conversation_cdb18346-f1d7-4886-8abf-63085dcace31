<template>
  <div class="quick-class-page">
    <div class="top">
      <!-- <div class="left-video">
        <div class="video-box">
          <video src="" controls></video>
        </div>
      </div> -->
      <div class="right-content">
        <p class="title">AI快捷做课</p>
        <div class="tips-content">
          <p class="single-tips">作为业务骨干，你是否满腹才学，但面对镜头总觉得手足无措、羞于表达？</p>
          <p class="single-tips">作为资深讲师，你是否希望自己的课程内容能快速转为在线视频，将知识传递给更多人？</p>
          <p class="single-tips">作为培训团队，你是否被繁琐冗长的供应商沟通、约拍修改、后期制作、上传入库流程所困扰？</p>
        </div>
        <!-- <p class="tips-content">针对这些疑问，我们为你准备了<span class="video-tips">60秒速学视频</span>，通过它，人人都可以快速学会如何使用AI做课工具，点击下方播放器即可查看。更多说明也可查看我们的文档：</p> -->
        <p class="tips-content">欢迎使用AI快捷做课功能，无需长时间拍摄、无需授课经验，即可助你快速生成在线课程内容， <a
            href="https://sdc.qq.com/s/Zkma1k?scheme_type=mooc&mooc_course_id=Y59RWLpX" target="_blank">《AI做课工具 使用教程》</a>
        </p>
        <!-- <el-button type="text" :disabled="true">AI课程制作介绍</el-button> -->
      </div>
    </div>
    <div class="page-tips">你可以在以下AI做课方式中，选择适合你的模式</div>
    <div class="card-box">
      <ul>
        <li class="card" v-for="(item, index) in cardList" :key="index" @click="toPath(item)">
          <div class="l-content">
            <div class="card-content">
              <p class="big-title">
                <span class="item-title">{{ item.bigTitle }}</span>
                <!-- <span class="demo-btn" @click="handleCheck($event)">查看Demo</span> -->
              </p>
              <p class="explain-title">{{ item.explainTitle }}</p>
            </div>
          </div>
          <img class="arrow-img" src="@/assets/img/right-arrow.png" alt="">
        </li>
      </ul>
    </div>
    <div class="demoVideoDialog" v-if="demoVideoDialog">
      <div class="dialog-body">
        <video src="" controls></video>
        <span class="dialog-close" @click="demoVideoDialog = false"></span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      demoVideoDialog: false,
      cardList: [
        { createRouteName: 'pptCourse', bigTitle: '模式一：PPT+合成语音模式', explainTitle: '提供授课PPT、讲稿（填写在PPT备注中），即可快捷生成包括PPT画面、授课语音的在线课程' },
        { createRouteName: '2DCourse', bigTitle: '模式二：PPT+合成语音+人像模式', explainTitle: '提供授课PPT、讲稿（填写在PPT备注中）和10秒人像录像，即可快捷生成包括PPT画面、授课语音和人像的在线课程' }
        // { routeName: 'ai-upload', bigTitle: '模式三：PPT+合成语音+3D虚拟人模式', explainTitle: '如你所在的项目需要申请人力支持在线课程的拍摄和制作，请点击这里填写申请。' }
      ]
    }
  },
  methods: {
    toPath({ createRouteName }) {
      const { net_course_id } = this.$route.query
      if (net_course_id) { // 处理在用状态更换模式
        this.$router.push({ name: createRouteName, query: { net_course_id } })
        return
      }
      this.$router.push({ name: createRouteName })
    },
    // 查看demo
    handleCheck(e) {
      e.stopPropagation()
      this.demoVideoDialog = true
    }
  }
}
</script>
<style lang="less" scoped>
.quick-class-page {
  padding: 32px 28px;

  .top {
    display: flex;

    .left-video {
      margin-right: 33px;

      .video-box {
        width: 306px;
        height: 172px;
        border-radius: 3px;

        video {
          height: 100%;
        }
      }
    }

    .right-content {
      .title {
        color: rgba(0, 0, 0, 0.9);
        font-size: 24px;
        font-weight: 700;
        margin-bottom: 16px;
      }

      .tips-content {
        color: rgba(0, 0, 0, 0.6);
        font-size: 14px;
        margin-bottom: 12px;

        .single-tips+.single-tips {
          margin-top: 5px;
        }

        .video-tips {
          color: rgba(237, 123, 47, 1);
        }

        a {
          color: #0052D9
        }
      }
    }
  }

  .page-tips {
    color: rgba(237, 123, 47, 1);
    margin-top: 36px;
    margin-bottom: 16px;
  }

  .card-box {
    margin-top: 28px;

    li+li {
      margin-top: 20px;
    }

    .card {
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: linear-gradient(90deg, rgba(255, 255, 255, 1) 0%, rgba(248, 251, 255, 1) 0%, rgba(251, 253, 255, 1) 100%);
      padding: 28px 28px 28px 32px;
      border-radius: 6px;
      cursor: pointer;

      .l-content {
        display: flex;
        align-items: center;

        .explain-img {
          height: 48px;
          width: 48px;
        }
      }

      .card-content {
        margin-left: 32px;

        .big-title {
          color: rgba(51, 51, 51, 1);
          font-size: 16px;
          font-weight: 700;
          margin-bottom: 12px;

          .item-title {
            height: 24px;
            line-height: 24px;
            display: inline-block;
          }

          .demo-btn {
            color: rgba(52, 100, 224, 1);
            font-size: 14px;
            font-weight: 400;
            cursor: pointer;
            margin-left: 20px;
            display: inline-block;
            height: 22px;
            line-height: 22px;
          }
        }

        .explain-title {
          color: rgba(0, 0, 0, 0.4);
          font-size: 12px;
        }
      }

      .arrow-img {
        height: 14px;
        width: 14px;
      }
    }

    .big-title-tips {
      .title {
        font-size: 24px;
        font-weight: 700;
        color: #000000;
        margin-bottom: 12px;
      }

      .tips {
        color: rgba(0, 0, 0, 0.6);
        font-size: 14px;
      }
    }
  }

  .demoVideoDialog {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;

    .dialog-body {
      width: 1440px;
      height: 900px;
      background: rgba(0, 0, 0, 0.4);
      padding: 90px 80px;

      video {
        height: 720px;
        width: 1280px
      }

      .dialog-close {
        background: url('~@/assets/img/video-close.png') no-repeat center/cover;
        width: 36px;
        height: 36px;
        display: inline-block;
        margin-left: 622px;
        margin-top: 12px;
        cursor: pointer;
      }
    }
  }
}</style>
