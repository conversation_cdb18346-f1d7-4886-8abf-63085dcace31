<template>
  <div class="agg-list">
    <div class="agg-header">
      <span class="agg-title">聚合报名页面</span>
      <el-button type="primary" size="small" @click="createdAgg">新增聚合页</el-button>
    </div>
    <div class="agg-body">
      <!-- 筛选 -->
      <el-form :inline="true" :model="form" class="demo-form-inline">
        <div class="form-row">
          <el-form-item label="页面名称">
            <el-input style="width:298px" size="small" clearable v-model="form.polymer_name" placeholder="请输入页面名称"></el-input>
          </el-form-item>
          <el-form-item label="结束时间" class="label-generous">
            <el-date-picker
              v-model="filterTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              range-separator="至"
              start-placeholder="开始日期时间"
              end-placeholder="结束日期时间"
              size="small">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="负责人或创建人" class="label-generous">
            <el-input style="width:248px" size="small" clearable v-model="form.manager" placeholder="请输入负责人或创建人"></el-input>
          </el-form-item>
        </div>
        <div class="form-row flex-box flex-between">
          <div class="form-row-left">
            <el-form-item label="页面状态">
              <el-radio v-model="form.status" v-for="item in statusList" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
            </el-form-item>
            <el-form-item label="页面布局" class="label-generous">
              <el-radio v-model="form.style_type" v-for="item in layoutList" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
            </el-form-item>
          </div>
          <div class="form-row-right">
            <el-form-item class="original">
              <div class="flex-box">
                <div @click="handleReset" class="reset"><i class="el-icon-refresh"></i><span>重置</span></div>
                <el-button class="search" type="primary" size="small" @click="onSearch()">检索</el-button>
              </div>
            </el-form-item>
          </div>
        </div>
      </el-form>
      <!-- 列表 -->
      <div class="agg-table">
        <div class="sort-box">
          <span>排序：</span>
          <ul>
            <li 
              :class="{ 'active-sort': form.order_by === e.value }"
              v-for="e in sortList" 
              :key="e.value"
              @click="handleSort(e.value)"
              >
            {{ e.label }}
            </li>
          </ul>
        </div>
        <div class="card-list">
          <div class="item-card" v-for="(v) in tableData.records" :key="v.activity_id">
            <ul>
              <li class="item-card-content">
                <div class="text">
                  <div class="video-title">
                    <span :class="['course-name']" @click="hamdlerUrl(v)">{{ v.polymer_name }}</span>
                    <div :class="['bottom-right']">
                        <el-tooltip class="item" effect="dark" content="编辑" placement="top-start">
                          <span :class="['other-icon-box']" @click="editAgg(v)"> <span class="stop-edit-icon right-icon edit-icon"></span></span>
                        </el-tooltip>
                        <el-tooltip class="item" effect="dark" content="分享" placement="top-start">
                          <span :class="['other-icon-box', { 'disabled-icon':  [2, 3].includes(v.status * 1) }]" @click="handleShare(v)"> <span class="stop-share-icon right-icon share-icon"></span></span>
                        </el-tooltip>
                        <el-tooltip class="item" effect="dark" content="上架" placement="top-start" v-if="[2].includes(v.status * 1)">
                          <span :class="['other-icon-box']" @click="handleUp(v)"> <span class="stop-up-icon right-icon up"></span></span>
                        </el-tooltip>
                        <el-tooltip class="item" effect="dark" content="下架" placement="top-start" v-else>
                          <span :class="['other-icon-box', { 'disabled-icon': v.status * 1 !== 1 }]" @click=" handleDown(v)"> <span class="stop-down-icon right-icon down"></span></span>
                        </el-tooltip>
                        <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
                          <span :class="['other-icon-box', { 'disabled-icon': v.status * 1 === 1 }]" @click="deleteAgg(v)"> <span class="stop-del-icon right-icon del"></span></span>
                        </el-tooltip>
                    </div>
                  </div>
                  <div class="content-center bottom-icon">
                    <div class="bottom-left">
                      <span class="icon-box">
                        状态：<span :class="[getStatusColor(v.status),'text-box']">{{v | statusName }}</span>
                      </span>
                      <span class="icon-box">
                        有效时间：<span class="text-box">{{ v.start_time | timeFormat }} ~ {{ v.end_time | timeFormat }}</span>
                      </span>
                      <span class="icon-box">
                        负责人：<span class="text-box">{{ v.creator_name }}</span>
                      </span>
                      <span class="icon-box">
                        关联内容：<span class="text-box">{{ v.content_count || 0 }}门</span>
                      </span>
                      <span class="icon-box">
                        报名人数：<span class="text-box">{{ v.user_count || 0 }}人</span>
                      </span>
                    </div>
                    <div class="bottom-right">
                      <span class="icon-box">
                        布局：<span class="text-box">{{ v.style_type | layoutName }}</span>
                      </span>
                      <span class="icon-box">
                        创建时间：<span class="text-box">{{ v.created_at | timeFormat }}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </li>
            </ul>
          </div>
        </div>
        <el-pagination
          v-if="tableData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="current"
          :page-sizes="[5, 10, 20, 30, 50, 100]"
          :page-size="size"
          layout="total,  prev, pager, next, sizes, jumper"
          :total="tableData.total"
        >
        </el-pagination>
        <div class="empty" v-if="tableData.records.length === 0">
          <span class="empty-img"></span>
          <div class="empty-text">还没有创建过聚合页哦，快来添加聚合页吧！</div>
        </div>
      </div>

      <!-- <el-table header-row-class-name="table-header-style" row-class-name="table-row-style" :data="tableData.records" style="width: 100%" class="agg-table">
        <el-table-column prop="polymer_name" label="页面名称">
        </el-table-column>
        <el-table-column label="页面状态" width="100">
          <template slot-scope="scope">
            <span>{{ Number(scope.row.status) === 1 ? '在用' : '停用' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="已关联班级/活动">
          <template slot-scope="scope">
            <span class="table-course-title">{{ scope.row.count }}个</span>
          </template>
        </el-table-column>
        <el-table-column label="创建人">
          <template slot-scope="scope">
            <span class="table-course-title">{{ scope.row.creator_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="创建时间">
          <template slot-scope="scope">
            <span class="table-course-title">{{ scope.row.created_at }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div class="operat-btn-box">
              <el-link type="primary" :underline="false" @click="hamdlerUrl(scope.row)">页面链接</el-link>
              <el-link type="primary" :underline="false" @click="editAgg(scope.row)">编辑</el-link>
              <el-link type="primary" :underline="false" @click="deleteAgg(scope.row)">删除</el-link>
              <el-link type="primary" :underline="false" @click="doCopy(scope.row)">复制分享链接</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="current" :page-sizes="[5, 10, 20, 30, 50, 100]" :page-size="size" layout="total,  prev, pager, next, sizes, jumper" :total="tableData.total">
      </el-pagination> -->
    </div>

    <QrCodeDialog v-if="copyShow.show" :visible.sync="copyShow.show" :url="copyShow.url" :copyTitle="copyShow.title" isAppletQrCode :appletPath="copyShow.appletPath" :scene="copyShow.scene" />
  </div>
</template>

<script>
import { getPolymerList, getDeletePolymerList, setPolymerActivate } from '@/config/mooc.api.conf.js'
import pager from '@/mixins/pager'
import { transformUnit, timeToDate } from 'utils/tools'
import QrCodeDialog from '@/views/components/qrCodeDialog'

const defaultForm = {
  polymer_name: '',
  start_time: '',
  end_time: '',
  manager: '',
  status: null,
  style_type: null,
  order_by: 'manage_name',
  sort_by: 'desc' // 升序asc 降序desc
}

export default {
  name: 'polymerList',
  components: {
    QrCodeDialog
  },
  mixins: [pager],
  data() {
    return {
      statusList: [
        { label: '不限', value: null },
        { label: '在用', value: 1 },
        { label: '停用', value: 2 },
        { label: '草稿', value: 3 }
      ],
      layoutList: [
        { label: '不限', value: null },
        { label: '列表布局', value: 1 },
        { label: '卡片布局', value: 2 }
      ],
      sortList: [
        { label: '负责人', value: 'manage_name' },
        { label: '有效时间-开始时间', value: 'start_time' },
        { label: '有效时间-结束时间', value: 'end_time' },
        { label: '报名人数', value: 'user_count' },
        { label: '关联内容数量', value: 'content_count' }
      ],
      form: Object.assign({}, defaultForm),
      filterTime: [],
      tableData: {
        total: 0,
        records: []
      },
      copyShow: {
        show: false,
        title: '',
        url: '',
        scene: '',
        appletPath: ''
      }
    }
  },
  computed: {},
  filters: {
    timeFormat(date) {
      if (!date) return '--'

      const [day, time] = date.split(' ')
      const dayArr = day.split('-')
      const timeArr = time.split(':')
    
      return `${dayArr[0]}/${dayArr[1]}/${dayArr[2]} ${timeArr[0]}:${timeArr[1]}`
    },
    statusName(data) {
      const statusMap = { 1: '在用', 2: '停用', 3: '草稿' }
      if (data.status in statusMap) {
        return statusMap[data.status]
      }
      return data.status
    },
    layoutName(data) {
      const layouts = { 1: '列表布局', 2: '卡片布局' }
      return layouts[data] || data
    }
  },
  created() {
    this.onSearch()
  },
  methods: {
    handleReset() {
      this.form = Object.assign({}, defaultForm)
      this.filterTime = []
      this.onSearch()
    },
    handleSort(val) {
      this.form.order_by = val
      this.onSearch()
    },
    // 分页
    onSearch(current = 1) {
      this.current = current
      this.getPolymerListFn()
    },
    createdAgg() {
      this.$router.push({
        name: 'createAggregate'
      })
    },
    async getPolymerListFn() {
      let params = {
        ...this.form,
        size: this.size,
        current: this.current
      }
      params.start_time = this.filterTime ? this.filterTime[0] : ''
      params.end_time = this.filterTime ? this.filterTime[1] : ''
      const data = await getPolymerList(params)
      console.log(data, 'list')
      this.tableData.records = data.records
      this.tableData.total = data.total
    },
    hamdlerUrl(row) {
      const routeData = this.$router.resolve({
        name: 'aggregate', // 目标路由的名称
        query: {
          polymer_id: row.polymer_id
        }
      })
      window.open(routeData.href, '_blank')
    },
    editAgg(row) {
      const routeData = this.$router.resolve({
        name: 'createAggregate', // 目标路由的名称
        query: {
          polymer_id: row.polymer_id
        }
      })
      window.open(routeData.href, '_blank')
    },
    handleShare(row) {
      this.copyShow.title = row.polymer_name
      this.copyShow.url = `https://sdc.qq.com/s/YJEYHb?scheme_type=polymer&polymer_id=${row.polymer_id}`
      this.copyShow.scene = row.polymer_id
      this.copyShow.appletPath = 'pages/polymer/index'
      this.copyShow.show = true
    },
    handleUp(v) {
      this.setPolymerActivateFn(v, 1)
    },
    handleDown(v) {
      this.$confirm('是否确认下架?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.setPolymerActivateFn(v, 2)
      })
    },
    setPolymerActivateFn(row, status) {
      setPolymerActivate({ polymer_id: row.polymer_id, status }).then(res => {
        this.$message({
          type: 'success',
          message: status === 1 ? '启用成功' : '下降成功'
        })
        this.onSearch()
      })
    },
    deleteAgg(row) {
      this.$confirm(`确定删除：${row.polymer_name}?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        getDeletePolymerList({ polymer_id: row.polymer_id }).then(res => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          this.onSearch()
        })
      }).catch(() => {})
    },
    getStatusColor (val) {
      val = Number(val)
      if (val === 1) {
        return 'use-color'
      } else if (val === 2) {
        return 'stop-color'
      }
    },
    handlerTransformUnit(v) {
      return transformUnit(v)
    },
    handlerTimeToDate(val) {
      return timeToDate(val)
    },
    doCopy(val) {
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      let httpHosturl = ''
      if (process.env.NODE_ENV === 'production') {
        httpHosturl = `https://portal.learn.woa.com/training/aggregate/detail?polymer_id=${val.polymer_id}`
      } else {
        httpHosturl = `https://test-portal-learn.woa.com/training/aggregate/detail?polymer_id=${val.polymer_id}`
      }
      input.value = httpHosturl
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      this.$message.success('复制成功')
      // 复制后移除输入框
      input.remove()
    }
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/activity-common.less';

:deep(.qcode-box .dialog-center) {
  position: absolute !important;
}
.agg-list {
  min-width: 1180px;
  .agg-header {
    height: 64px;
    padding: 20px 24px;
    border-bottom: 1px solid #EEEEEE;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .agg-title {
      font-family: PingFang SC;
      font-weight: 600;
      font-size: 16px;
      line-height: 24px;
      letter-spacing: 0px;
      color: #000000CC;
    }
  }
  .agg-body { 
    padding: 16px 20px 4px;
    background-color: #fff;
                   
    .demo-form-inline {
      padding: 12px 16px 0;
      background-color: #FAFAFA;
      border-radius: 4px;
      .form-row {
        width: 100%;
      }
      .reset,
      .search {
        width: 80px;
        height: 32px;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .reset {
        cursor: pointer;
        margin-right: 16px;
        color: #0052D9;
        border: solid 1px #0052D9;
        border-radius: 3px;
        &:hover {
          border-color: #2f5aca;
          color: #2f5aca;
        }
        i {
          margin-right: 4px;
        }
      }
    }
    :deep(.el-form) {
      .el-form-item__label {
        color: #00000099;
      }
      .form-row .el-form-item {
        min-width: 316px;
        margin-bottom: 12px;
        margin-right: 50px;
        &:nth-last-child(1) {
          margin-right: 0;
        }
      }
      .original {
        min-width: auto;
        margin-right: 0;
      }
    }
    :deep(.el-date-editor--datetimerange) {
      width: 360px;
      .el-range-input {
        width: 41%;
      }
      .el-range-editor--small .el-range-separator {
        flex-shrink: 0;
      }
      .el-range-separator {
        min-width: 22px;
      }
    }
    :deep(.el-radio) {
      color: #000000E5;
      .el-radio__input.is-checked+.el-radio__label {
        color: #000000E5;
      }
    }
  }
  
  .agg-table {
    background-color: #fff;
    border-radius: 4px;
    padding: 16px 0px;
    .sort-box {
      display: flex;
      align-items: center;
      padding-bottom: 15px;
      border-bottom: 1px solid rgba(238, 238, 238, 1);

      span:first-of-type {
        color: rgba(0, 0, 0, 0.4);
      }

      ul {
        display: flex;
        margin-left: 8px;

        li {
          color: rgba(0, 0, 0, 0.6);
          font-size: 14px;
          height: 22px;
          line-height: 22px;
          cursor: pointer;
        }

        li+li {
          margin-left: 24px;
        }

        .active-sort {
          color: #0052D9
        }
      }
    }
    .card-list {
      width: 100%;
      overflow-x: auto;

      .item-card {
        min-width: 1026px;
        border-bottom: 1px solid rgba(238, 238, 238, 1);
        padding: 10px 0;

        &-content {
          display: flex;

          .text {
            color: rgba(51, 51, 51, 1);
            width: 100%;
            .video-title {
              margin-bottom: 16px;
              display: flex;
              align-items: center;
              // 一行省略, 超出显示省略号。宽度自适应
              .course-name {
                flex: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: inline-block;
                height: 20px;
                line-height: 20px;
                word-break: break-all;
                cursor: pointer;
              }
              .bottom-right {
                min-width: 156px;
                flex-shrink: 1;
                display: flex;
                align-items: center;
                height: 16px;
                margin-left: 56px;
                span+span {
                  margin-left: 20px;
                }

                .el-dropdown {
                  margin-left: 20px;
                }

                .right-icon {
                  display: inline-block;
                  width: 16px;
                  height: 16px;
                }
                .other-icon-box {
                  width: 24px;
                  height: 24px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  border-radius: 50%;
                  cursor: pointer;
                  background: #FBFBFB;

                  .edit-icon {
                    background: url('~@/assets/img/edit-allow.png') no-repeat center/cover;
                  }
                  .share-icon {
                    background: url('~@/assets/img/share.png') no-repeat center/cover;
                  }
                  .down {
                    background: url('~@/assets/img/down.png') no-repeat center/cover;
                  }
                  .up {
                    background: url('~@/assets/img/up.png') no-repeat center/cover;
                  }
                  .del {
                    background: url('~@/assets/img/del.png') no-repeat center/cover;
                  }
                }

                .other-icon-box:hover {
                  background: rgba(245, 247, 249, 1);

                  .edit-icon {
                    background: url('~@/assets/img/edit-icon-hover.png') no-repeat center/cover;
                  }
                  .share-icon {
                    background: url('~@/assets/img/share-hover.png') no-repeat center/cover;
                  }
                  .down {
                    background: url('~@/assets/img/down-active.png') no-repeat center/cover;
                  }
                  .up {
                    background: url('~@/assets/img/up-active.png') no-repeat center/cover;
                  }
                  .del {
                    background: url('~@/assets/img/del-active.png') no-repeat center/cover;
                  }
                }

                .disabled-icon {
                  cursor: not-allowed;
                  pointer-events: none;
                  .edit-icon {
                    background: url('~@/assets/img/edit.png') no-repeat center/cover !important;
                  }
                  .share-icon {
                    background: url('~@/assets/img/stop-share.png') no-repeat center/cover !important;
                  }
                  .up {
                    background:  url('~@/assets/img/up-1.png') no-repeat center/cover !important;
                  }
                  .down {
                    background:  url('~@/assets/img/down-1.png') no-repeat center/cover !important;
                  }
                  .del {
                    background:  url('~@/assets/img/del-1.png') no-repeat center/cover !important;
                  }
                }
              }
            }
            
            .bottom-icon {
              display: flex;

              .icon-box {
                display: flex;
                align-items: center;
                color: #00000066;
                font-size: 12px;
              }

              .icon-box+.icon-box {
                margin-left: 12px;
              }

              .text-box {
                font-family: PingFang SC;
                font-weight: 400;
                font-size: 12px;
                line-height: 16px;
                letter-spacing: 0px;
                color: #00000099;
              }

              .bottom-left {
                display: flex;
                align-items: center;
                flex: 1;
                height: 16px;
                .tool-tips-box {
                  display: flex;
                  align-items: center;
                  margin-left: 16px;
                }
                .el-icon-warning-outline {
                  font-size: 16px;
                  color: #E34D59;
                  margin-left: 4px;
                }

                .use-color {
                  color: #00A870;
                }
                .stop-color {
                  color: #E34D59;
                }
              }

              .bottom-right {
                flex-shrink: 0;
                display: flex;
                align-items: center;
                height: 16px;
                margin-left: 56px;

                span+span {
                  margin-left: 20px;
                }

                .text-box {
                  color: #00000066;
                }
              }
            }
          }
        }
      }

      .item-card:last-of-type {
        border-bottom: 1px solid rgba(238, 238, 238, 1);
      }

      .item-card:hover {
        background: rgba(247, 251, 255, 0.5);
      }
    }
  }

  .empty {
    text-align: center;
    margin: 15px 0 32px;
    .empty-text {
      line-height: 22px;
      color: #333333;
      font-size: 14px;
      font-weight: 400;
    }
    .empty-img {
      display: inline-block;
      width: 160px;
      height: 160px;
      background: url(~@/assets/classroomImg/activity-empty.png) no-repeat center/contain;
    }
  }
}
</style>
