<template>
  <van-popup
  class="chapterSummary-container"
  v-model="show"
  :overlay="false"
  position="bottom"
  get-container="body"
  >
    <div class="body">
      <div class="top-title">
        <span class="placeholder"></span>
        <span class="title">章节·纪要</span>
        <span
        class="close-icon"
        @click="handleClose"
        >
      </span>
      </div>
      <div class="main-content" v-if="summaryList.length">
        <div class="tools-box">
          <div class='tools-lf'>
            <span class="switch-box">
              <van-switch
              v-model="checked" @click="delDtClick" @change="changeSwitch" size="24px" active-color="#0052D9" inactive-color="#DCDCDC"/>
              <span :class="['label', {'active-label': checked}]">纪要</span>
            </span>
            <span class="play-status">
              <span class="icon play-icon"></span>
              <span>{{ playIndex }}</span>
            </span>
          </div>
          <div class="tools-lr" @click="handleFeedBack" :dt-eid="dtBtn('eid', '反馈')" :dt-remark="dtBtn('remark', '反馈')" :dt-areaid="dtBtn('areaid', '反馈')">
            <span class="tips-icon icon"></span>
            <span class="tips-title">反馈</span>
          </div>
        </div>
        <div class="chapter-list-box">
          <div
          :class="['item-chapter', {'summary-item-active': captionCurTime >= item.startTime && captionCurTime < item.endTime}]"
          v-for="(item, index) in summaryList"
          :key="index"
          @click="toChapterPosition(item)"
          :dt-eid="dtChapterList('eid', item)"
          :dt-remark="dtChapterList('remark', item)"
          :dt-areaid="dtChapterList('areaid', item)"
          >
            <!-- <span class="timer">{{ item.chapter_time }}</span> -->
            <div class="title-content">
              <span class="title-box"><span class="icon"></span>{{ item.chapter_title }}</span>
              <div class="content">{{ item.chapter_content }}</div>
            </div>
            <div class="time-content">
              <span class="time-icon"></span>
              {{ item.totalMin }}min
            </div>
          </div>
          <div class="no-more">没有更多内容了~</div>
        </div>
      </div>
      <div v-else class="empty-chapter">
        <img lazy-load src="@/assets/img/mobile/empty-chapter.png" alt="" />
        <span class="warm-text">当前课程内容暂无章节～</span>
      </div>
    </div>
  </van-popup>
</template>
<script>
import { setSummaryApi, getSummaryApi } from '@/config/api.conf.js'
export default {
  props: {
    isShowChapter: {
      type: Boolean,
      default: false
    },
    chapterSummaryList: {
      type: Array,
      default: () => ([])
    },
    captionCurTime: [Number, String],
    courseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      show: false,
      checked: true,
      summaryList: []
    }
  },
  watch: {
    isShowChapter: {
      immediate: true,
      handler(val) {
        this.show = val
        if (val) {
          this.getSummaryStatus()
        }
      }
    },
    chapterSummaryList: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val.length) {
          this.summaryList = val
        }
      }
    }
  },
  computed: {
    playIndex() {
      const index = this.summaryList.findIndex((v) => this.captionCurTime >= v.startTime && this.captionCurTime < v.endTime)
      return `${index + 1}/${this.summaryList.length}`
    },
    course_id() {
      return this.$route.query.course_id || ''
    },
    dtBtn() {
      return (type, val) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页-新版',
            container: `详情-章节纪要`,
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: val,
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${val}`
        } else {
          return `area_${this.course_id}_${val}`
        }
      }
    },
    dtChapterList() {
      return (type, val) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页-新版',
            container: `详情-章节纪要`,
            click_type: 'data',
            content_type: '',
            content_id: '',
            content_name: val.chapter_title,
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${val.chapter_time}`
        } else {
          return `area_${this.course_id}_${val.chapter_time}`
        }
      }
    }
  },
  methods: {
    getSummaryStatus() {
      getSummaryApi(this.course_id).then((res) => {
        this.checked = !!res
        this.formatList(this.checked)
      })
    },
    toChapterPosition(item) {
      this.$emit('toChapterPosition', item)
    },
    // 反馈
    handleFeedBack() {
      window.open('https://km.tencent.com/openkm/url/lpciih', '_blank')
    },
    handleClose() {
      this.show = false
      this.$emit('update:isShowChapter', false)
    },
    delDtClick() {
      const label = this.checked ? '纪要关闭' : '纪要开启'
      window.BeaconReport('at_click', {
        eid: `element_${this.course_id}_${label}`,
        remark: JSON.stringify({
          page: this.courseData.course_name,
          page_type: '网课详情页面-移动新版',
          container: '详情-章节纪要',
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: label,
          act_type: '',
          page_id: '',
          container_id: '',
          terminal: 'H5'
        })
      })
    },
    // 纪要开关
    async changeSwitch(val) {
      const status = val ? 1 : 0
      await setSummaryApi(this.course_id, { status })
      this.formatList(val)
    },
    formatList(val) {
      if (val) { // 关闭
        this.summaryList = JSON.parse(JSON.stringify(this.chapterSummaryList))
        return
      }
      // 开启
      this.summaryList = this.summaryList.map((e) => {
        return {
          chapter_time: e.chapter_time,
          chapter_title: e.chapter_title,
          startTime: e.startTime,
          chapter_time_point: e.chapter_time_point,
          endTime: e.endTime,
          totalMin: e.totalMin
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.chapterSummary-container {
  height: calc(100% - 210px);
  .body {
    height: 100%;
    overflow: hidden;
    .top-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 16px;
      border-bottom: 1px solid #EDEDED;
      .title {
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        color: #000000e6;
      }
      .close-icon {
        display: inline-block;
        width: 24px;
        height: 24px;
        background: url('~@/assets/img/close.png') no-repeat center / cover;
      }
    }
    .main-content {
      height: calc(100% - 40px);
      overflow: auto;
      padding-bottom: 36px;

      .tools-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        line-height: 24px;
        padding: 16px;
        font-size: 12px;
        .icon {
          display: inline-block;
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
        .tools-lf {
          display: flex;
          align-items: center;
          color: #333333;
          .switch-box {
            display: flex;
            align-items: center;
            .label {
              margin-left: 4px;
              color: #DCDCDC;
            }
            .active-label {
              color: #0052D9;
            }
          }
          .play-status {
            display: flex;
            align-items: center;
            margin-left: 16px;
          }
          .play-icon {
            background: url('~@/assets/img/icon-live.png') no-repeat center / cover;
          }
        }
        .tools-lr {
          display: flex;
          align-items: center;
          color: #777777;
          .tips-icon {
            background: url('~@/assets/img/mobile/tips.png') no-repeat center / cover;
          }
        }
      }
      .chapter-list-box {
        padding: 0 16px;
        .item-chapter {
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
          .icon {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 4px;
            background: url('~@/assets/img/icon-live.png') no-repeat center / cover;
            display: none;
            float: left;
            margin-top: 2px;
          }
          .timer {
            border-radius: 2px;
            width: 56px;
            height: 22px;
            line-height: 22px;
            display: inline-block;
            border: 1px solid #0052d9;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #0052D9;
            font-size: 12px;
          }
          .title-content {
            // margin-left: 16px;
            .title-box {
              font-size: 14px;
              font-weight: bold;
              line-height: 22px;
              color: #333333;
              word-break: break-all;
              display: flex;
              align-items: center;
            }
            .content {
              color: #666666;
              font-family: "PingFang SC";
              font-size: 14px;
              line-height: 22px;
              letter-spacing: 0.28px;
              margin-top: 4px;
              word-break: break-all;
            }
          }
          .time-content {
            display: flex;
            align-items: center;
            padding: 0 8px;
            height: 20px;
            line-height: 20px;
            color: #0052d9;
            font-size: 12px;
            border-radius: 4px;
            background: #ECF2FE;
            .time-icon {
              width: 14px;
              height: 14px;
              margin-right: 4px;
              background: url('~@/assets/img/mobile/time-bule.png') no-repeat center / cover;
            }
          }
        }
        .summary-item-active {
          :deep(.title-content) {
            .title-box {
              color: #0052d9;
            }
            .icon {
             display: block;
            }
            .content {
              color: #333333;
            }
          }
        }
        .item-chapter + .item-chapter {
          margin-top: 16px;
        }
      }
      .no-more {
        color: #00000066;
        text-align: center;
        margin-top: 16px;
        font-size: 14px;
      }
    }
    .empty-chapter {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 16px;
      img {
        width: 160px;
        height: 160px;
        margin: 0 auto;
      }
      .warm-text {
        margin-top: 16px;
        color: #000000e6;
        text-align: center;
        font-size: 12px;
      }
    }
  }
}
</style>
