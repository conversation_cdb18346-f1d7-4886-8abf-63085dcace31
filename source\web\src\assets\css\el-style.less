// elementUI默认样式修改
// 按钮
.el-button:focus,
.el-button:hover {
    color: #0052D9;
    border-color: rgb(179, 203, 244);
    background-color: rgb(230, 238, 251)
}

.el-button:active {
    color: rgb(0, 74, 195);
    border-color: rgb(0, 74, 195);
    outline: 0
}
.el-button.is-plain:focus,
.el-button.is-plain:hover {
    background: #FFF;
    border-color: #0052D9;
    color: #0052D9
}

.el-button.is-active,
.el-button.is-plain:active {
    color: rgb(0, 74, 195);
    border-color: rgb(0, 74, 195)
}
.el-button.custom-plain {
  border-color: #3464e0ff;
  color: #0052d9ff;
}
.el-button--primary {
  color: #FFF;
  background-color: #0052D9;
  border-color: #0052D9
}

.el-button--primary:focus,
.el-button--primary:hover {
  background: rgb(51, 117, 225);
  border-color: rgb(51, 117, 225);
  color: #FFF
}

.el-button--primary:active {
  background: rgb(0, 74, 195);
  border-color: rgb(0, 74, 195);
  color: #FFF;
  outline: 0
}

.el-button--primary.is-active {
  background: rgb(0, 74, 195);
  border-color: rgb(0, 74, 195);
  color: #FFF
}

.el-button--primary.is-disabled,
.el-button--primary.is-disabled:active,
.el-button--primary.is-disabled:focus,
.el-button--primary.is-disabled:hover {
  color: #FFF;
  background-color: rgb(128, 169, 236);
  border-color: rgb(128, 169, 236)
}

.el-button--primary.is-plain {
  color: #0052D9;
  background: rgb(230, 238, 251);
  border-color: rgb(153, 186, 240)
}

.el-button--primary.is-plain:focus,
.el-button--primary.is-plain:hover {
  background: #0052D9;
  border-color: #0052D9;
  color: #FFF
}

.el-button--primary.is-plain:active {
  background: rgb(0, 74, 195);
  border-color: rgb(0, 74, 195);
  color: #FFF;
  outline: 0
}

.el-button--primary.is-plain.is-disabled,
.el-button--primary.is-plain.is-disabled:active,
.el-button--primary.is-plain.is-disabled:focus,
.el-button--primary.is-plain.is-disabled:hover {
  color: rgb(102, 151, 232);
  background-color: rgb(230, 238, 251);
  border-color: rgb(204, 220, 247)
}
// 删除按钮
.el-button--danger {
  border: 1px solid #E34D59;
  background-color: #E34D59;
  color: #fff
}

.el-button--danger:hover, .el-button--danger:focus{
  border: 1px solid #F36D78;
  background-color: #F36D78;
  color: #fff
}

.el-button--danger:active {
  border: 1px solid #C9353F;
  background-color: #C9353F;
  color: #fff
}

.el-button--danger.is-disabled,
.el-button--danger.is-disabled:active,
.el-button--danger.is-disabled:focus,
.el-button--danger.is-disabled:hover {
  background-color: #F9D7D9;
  border-color: #F9D7D9;
  color: #fff
}

// 删除按钮-plan
.el-button--danger.is-plain {
  border: 1px solid #E34D59;
  background-color: #fff;
  color: #E34D59
}

.el-button--danger.is-plain:hover, .el-button--danger.is-plain:focus {
  border: 1px solid #F36D78;
  color: #F36D78;
  background-color: #fff;
}

.el-button--danger.is-plain:active {
  border: 1px solid rgba(201, 53, 63, 1);
  background: rgba(231, 231, 231, 1);
  color: #C9353F
}

.el-button--danger.is-plain.is-disabled,
.el-button--danger.is-plain.is-disabled:active,
.el-button--danger.is-plain.is-disabled:focus,
.el-button--danger.is-plain.is-disabled:hover {
  border: 1px solid rgba(248, 185, 190, 1);
  background: rgba(238, 238, 238, 1);
  color: #F8B9BE
}
//el-radio
.el-radio-button__orig-radio:checked+.el-radio-button__inner {
  background-color: #0052D9;
  border-color: #0052D9;
}
// text按钮
.el-button--text {
  padding: unset;
  border-color: transparent !important;
  color: #0052D9 !important;
}

.el-button--text:hover, .el-button--text:focus {
  color: #5d83e6 !important;
  background:#fff;
  border-color: transparent;
}

.el-button--text:active {
  color: #0052D9 !important;
  background:#fff;
  border-color: transparent;
}

.el-button--text.is-disabled,
.el-button--text.is-disabled:active,
.el-button--text.is-disabled:focus,
.el-button--text.is-disabled:hover {
  color: #9ab2f0 !important
}

// el-tag
.el-tag {
  background-color: rgba(235,239,252);
  border-color: rgb(204, 220, 247);
  display: inline-block;
  height: 32px;
  padding: 0 10px;
  line-height: 30px;
  font-size: 12px;
  color: #0052D9;
  border-radius: 4px;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  white-space: nowrap;
  border: unset;
}
.el-tag.is-hit {
  border-color: #0052D9
}

.el-tag .el-tag__close {
  color: #0052D9
}

.el-tag .el-tag__close:hover {
  color: #FFF;
  background-color: #0052D9
}

.el-tag.el-tag--info {
  background-color: #f4f4f5;
  border-color: #e9e9eb;
  color: #909399
}

.el-tag.el-tag--info.is-hit {
  border-color: #909399
}

// .el-tag.el-tag--info .el-tag__close {
//   color: #909399
// }

.el-tag.el-tag--info .el-tag__close:hover {
  color: #FFF;
  background-color: #909399
}

.el-input__icon {
  line-height: 32px;
}
.el-textarea__inner {
  padding: 5px 10px
}
.el-textarea__inner:focus,.el-input__inner:focus {
  border-color: #0052D9;
}

// el-form
.el-form {
  .el-form-item {
    margin-bottom: 20px;
  }
  .el-form-item__label,.el-form-item__content{
   line-height: 32px; 
   color: rgba(0,0,0,0.8);
  }
  .el-form--label-right .el-form-item__label {
    text-align: right
  }
}
.el-select__tags{
  .el-tag{
    height: 24px;
    display: flex;
    align-items: center;
    background: #e7e7e7;
    color: rgba(0, 0, 0, 0.9);
  }
  .el-tag__close{
    background: #e7e7e7 !important;
    font-size: 15px;
    color: #909399 !important;
    top: 2px;
  }
}

.label-special-class{
  .el-input,input{
    min-height: 32px !important;
    height: unset;
  }
}
.el-cascader-panel {
  display: flex;
  border-radius: 4px;
  font-size: 14px
}

.el-cascader-panel.is-bordered {
  border: 1px solid #E4E7ED;
  border-radius: 4px
}

.el-cascader-menu {
  min-width: 180px;
  box-sizing: border-box;
  color: #606266;
  border-right: solid 1px #E4E7ED
}

.el-cascader-menu:last-child {
  border-right: none
}

.el-cascader-menu:last-child .el-cascader-node {
  padding-right: 20px
}

.el-cascader-menu__wrap {
  height: 204px
}

.el-cascader-menu__list {
  position: relative;
  min-height: 100%;
  margin: 0;
  padding: 6px 0;
  list-style: none;
  box-sizing: border-box
}

.el-cascader-menu__hover-zone {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none
}

.el-cascader-menu__empty-text {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  text-align: center;
  color: #C0C4CC
}

.el-cascader-node {
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 30px 0 20px;
  height: 34px;
  line-height: 34px;
  outline: 0
}

.el-cascader-node.is-selectable.in-active-path {
  color: #606266
}

.el-cascader-node.in-active-path,
.el-cascader-node.is-active,
.el-cascader-node.is-selectable.in-checked-path {
  color: #0052D9;
  font-weight: 700
}

.el-cascader-node:not(.is-disabled) {
  cursor: pointer
}

.el-cascader-node:not(.is-disabled):focus,
.el-cascader-node:not(.is-disabled):hover {
  background: #F5F7FA
}

.el-cascader-node.is-disabled {
  color: #C0C4CC;
  cursor: not-allowed
}

.el-cascader-node__prefix {
  position: absolute;
  left: 10px
}

.el-cascader-node__postfix {
  position: absolute;
  right: 10px
}

.el-cascader-node__label {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  padding: 0 10px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis
}

.el-cascader-node>.el-radio {
  margin-right: 0
}

.el-cascader-node>.el-radio .el-radio__label {
  padding-left: 0
}

.el-cascader {
  display: inline-block;
  position: relative;
  font-size: 14px;
  line-height: 32px;
  cursor: pointer;
}

.el-cascader:not(.is-disabled):hover .el-input__inner {
  cursor: pointer;
  border-color: #C0C4CC
}

.el-cascader .el-input .el-input__inner:focus,
.el-cascader .el-input.is-focus .el-input__inner {
  border-color: #0052D9
}

.el-cascader .el-input {
  cursor: pointer
}

.el-cascader .el-input .el-input__inner {
  text-overflow: ellipsis
}

.el-cascader .el-input .el-icon-arrow-down {
  -webkit-transition: -webkit-transform .3s;
  transition: -webkit-transform .3s;
  transition: transform .3s;
  transition: transform .3s, -webkit-transform .3s;
  font-size: 14px
}

.el-cascader .el-input .el-icon-arrow-down.is-reverse {
  -webkit-transform: rotateZ(180deg);
  transform: rotateZ(180deg)
}

.el-cascader .el-input .el-icon-circle-close:hover {
  color: #909399
}

.el-cascader--medium {
  font-size: 14px;
  line-height: 36px
}

.el-cascader--small {
  font-size: 13px;
  line-height: 32px
}

.el-cascader--mini {
  font-size: 12px;
  line-height: 28px
}

.el-cascader.is-disabled .el-cascader__label {
  z-index: 2;
  color: #C0C4CC
}

.el-cascader__dropdown {
  margin: 5px 0;
  font-size: 14px;
  background: #FFF;
  border: 1px solid #E4E7ED;
  border-radius: 4px;
  -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1)
}

.el-cascader__tags {
  display: flex !important;
}

.el-cascader__tags .el-tag {
  height: 24px;
  display: inline-flex;
  align-items: center;
  max-width: 100%;
  margin: 2px 0 2px 6px;
  text-overflow: ellipsis;
  color: rgba(0,0,0,0.9);
  background: #E7E7E7;
}

.el-cascader__tags .el-tag:not(.is-hit) {
  border-color: transparent
}

.el-cascader__tags .el-tag>span {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis
}

.el-cascader__tags .el-tag .el-icon-close {
  -webkit-box-flex: 0;
  -ms-flex: none;
  flex: none;
  background: #e7e7e7;
  // color: rgba(0, 0, 0, 0.9);
  font-size: 15px;
  color: #909399;
  top: 1px;
}

.el-cascader__tags .el-tag .el-icon-close:hover {
  background-color: #909399
}

.el-cascader__suggestion-panel {
  border-radius: 4px
}

.el-cascader__suggestion-list {
  max-height: 204px;
  margin: 0;
  padding: 6px 0;
  font-size: 14px;
  color: #606266;
  text-align: center
}

.el-cascader__suggestion-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: 34px;
  padding: 0 15px;
  text-align: left;
  outline: 0;
  cursor: pointer
}

.el-cascader__suggestion-item:focus,
.el-cascader__suggestion-item:hover {
  background: #F5F7FA
}

.el-cascader__suggestion-item.is-checked {
  color: #0052D9;
  font-weight: 700
}

.el-cascader__suggestion-item>span {
  margin-right: 10px
}

.el-cascader__empty-text {
  margin: 10px 0;
  color: #C0C4CC
}

.el-cascader__search-input {
  -webkit-box-flex: 1;
  -ms-flex: 1;
  flex: 1;
  height: 24px;
  min-width: 60px;
  margin: 2px 0 2px 15px;
  padding: 0;
  color: #606266;
  border: none;
  outline: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box
}

.el-cascader__search-input::-webkit-input-placeholder {
  color: #C0C4CC
}

.el-cascader__search-input:-ms-input-placeholder {
  color: #C0C4CC
}

.el-cascader__search-input::-ms-input-placeholder {
  color: #C0C4CC
}

.el-cascader__search-input::placeholder {
  color: #C0C4CC
}

// el-link
.el-link.el-link--primary{
  color: #0052D9;
}

// el-dialog
.el-dialog {
  border-radius: 4px;
  .el-dialog__header{
    padding: 20px;
    font-size: 16px;
    font-weight: bolder;
    border-bottom: solid 1px #DCDFE6;
  }
  // 默认按钮
  .el-button--default {
    background-color: #F3F5F7;
    border: 1px solid #F3F5F7;
    color: #000000
  }

  .el-button--default:hover, .el-button--default:focus {
    border: 1px solid #DCDCDC;
    background-color: #DCDCDC;
    color: #000000
  }

  .el-button--default:active {
    border: 1px solid #C5C5C5;
    background-color: #C5C5C5;
    color: #000000
  }
  .el-dialog__close:hover{
    color: #0052D9;
  }
}

// el-upload
.upload-icon{
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color:#666666;
  .el-icon-upload-custom {
    width: 40px;
    height: 40px;
    background: url(~@/assets/img/upload.png) no-repeat center/cover;
  }
  p {
    line-height: 20px;
    margin-top: 5px;
  }
}

// el-tab
.el-tabs__nav{
  .el-tabs__item{
    height: 20px;
    line-height: 20px;
    margin-bottom: 6px;
    color: rgba(0,0,0,0.6);      
  }
  .is-active{
    color: #0052D9;
    font-weight: bold;
  }
  .el-tabs__active-bar{
    background-color: #0052D9;
  }
}

// 消息提示框
.el-message {
  top: 54px !important;
  min-width: 40px !important;
  padding: 20px;
  line-height: 20px;
  border-radius: 4px;
  opacity: 1;
  background-color: #fff !important;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12),
    0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  font-family: @PingFangSC;
  border: 0 !important;

  .el-message__icon {
    margin-right: 5px;
    font-size: 20px;
  }

  .el-icon-success {
    color: #0ad0b6 !important;
  }

  .el-icon-error {
    color: #f81d22 !important;
  }

  .el-icon-warning,
  .el-icon-warning-outline {
    color: #ff7649 !important;
  }

  .el-icon-warning-outline {
    margin-right: 5px;
  }

  .el-message__content {
    color: #666 !important;
  }
}

// 确认弹窗样式
.el-message-box {
  padding: 28px 32px 24px !important;
  width: 430px;
  box-sizing: border-box;
  border-radius: 4px;
  border: none;

  .el-message-box__header {
    padding: 0;
    line-height: 24px;

    .el-message-box__title {
      font-weight: 700;
      font-size: 16px;
      line-height: 24px;
      color: #333;
    }

    .el-message-box__headerbtn {
      display: none;
    }
  }

  .el-message-box__content {
    padding: 16px 0 24px;
    line-height: 24px;
    color: #666;
  }

  .el-message-box__btns {
    padding: 0;

    .el-button {
      height: 32px;
      margin-left: 8px;
      border-radius: 3px;
      font-size: 14px;
      font-weight: 400;
    }

    .el-button--primary {
      background-color: #0052D9;
      border-color: #0052D9;
      color: #fff;
    }

    .el-button--primary:hover {
      background-color: #266FE8;
      border-color: #266FE8;
      color: #fff;
    }

    .el-button--primary:active {
      background-color: #0034B5;
      border-color: #0034B5;
      color: #fff;
    }
  }
}

.zanAndCollectTooltip{
  font-size: 14px;
  padding: 16px;
  align-items: center;
  justify-content: center;
}
.previewWarnOnce {
  .el-icon-warning{
    color: #0052D9 !important;
  }
  .el-icon-close{
    font-size: 12px;
    color: rgba(0,0,0,0.9);
    font-weight: bold;
  }
}
.el-message-box__wrapper {
  .graphic_del{
    width: 540px;
    .el-icon-warning{
      color: #FB6161 !important;
      font-size: 38px !important;
    }
    .el-button:last-child{
      background-color: #FB6161 !important;
      border-color: #FB6161 !important;
    }
    .el-message-box__errormsg{
      display: flex;
      padding-left: 19px;
    }
  }
  .el-icon-warning-outline {
    color: #ff7649 !important;
  }
}

.el-tooltip__popper {
  max-width: 400px;
}
// 单选框组
.el-radio-group {
  .el-radio {
    margin-right: unset;
  }
  .el-radio + .el-radio {
    margin-left: 16px;
  }
  .el-radio__inner {
    width: 16px;
    height: 16px;
  }
  .el-radio__inner::after {
    width: 8px;
    height: 8px
  }
  .el-radio__label {
    color: rgba(0,0,0,0.9);
  }
  .el-radio__input.is-checked+.el-radio__label {
    color: rgba(0,0,0,0.9);
  }
}

// 多选框
.el-checkbox {
  color: rgba(0,0,0,0.9);
  .el-checkbox__inner {
    width: 16px;
    height: 16px;
  }
  .el-checkbox__input.is-checked+.el-checkbox__label {
    color: rgba(0,0,0,0.9);
  }
  .el-checkbox__input.is-checked .el-checkbox__inner,
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #0052D9;
    border-color: #fff
  }
  .el-checkbox__inner::after {
    position: absolute;
    border-color: #fff;
    left: 5px;
    top: 2px
  }
  .el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
    background-color: #fff;
  }
}
.sdc-selector {
  height: 32px;
  .selector-container{
    .el-tag{
      background: rgba(231,231,231,1);
      color: rgba(0,0,0,0.9);
      height: 24px;
      line-height: 24px;
    }
    .el-icon-close{
      font-size: 16px;
    }                
  }
  .selector-container,.suffix-open{
    display: flex;
    align-items: center;
    height: 32px;
    line-height: 32px;
    padding: unset;
    // .el-input{
    //   width: unset;
    // }
    .el-input,.el-input__inner{
      height: 28px; 
    }
  }
  .suffix-open {
    display: flex;
    align-items: center;
    .el-button{
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
  .suffix-num {
    display: none
  }
  .num {
    color: rgba(0,0,0,0.4);
  }
}

//表格样式
.el-table {
  width: 100%;
  .el-table__header-wrapper table,.el-table__body-wrapper table{
    width: 100% !important;
  }
  .el-table__body, .el-table__footer, .el-table__header{
    table-layout: auto;
  }
  .table-header-style {
    height: 50px;

    th {
      background: #EEEEEE;
      font-weight: bold;
      color: rgba(0,0,0,0.8);
      border-bottom: none;
    }
  }

  .table-row-style {
    height: 50px;

    td {
      height: 50px;
      color: rgba(0,0,0,0.6);
      padding: 9px 0 10px;
      border-color: #eee;

      .delete-btn {
        color: red;
      }
    }
  }

  .table-row-card-style {
    height: 148px;

    td {
      padding: unset;
    }
  }
}
// 分页样式
.el-pagination {
  margin-top: 20px;
  padding: 0;
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  .el-pagination__total {
    height: 24px !important;
    line-height: 24px !important;
    position: relative;
    top: 0px;
    margin-right: 16px;
    font-size: 14px;
    color: #334151;
  }

  .el-pager {
    .number {
      background-color: #fff !important;
      font-size: 12px;
      font-weight: 400;
      min-width: 24px;
      height: 24px !important;
      line-height: 24px;
    }

    .more {
      height: 24px !important;
    }

    .number.active {
      color: #0052D9 !important;
      border-color: #DCDCDC !important;
    }
  }

  .btn-prev,
  .btn-next {
    background-color: #fff;
    min-width: 24px;
    height: 24px;
  }

  .el-pagination__sizes {
    margin: 0 5px;
    height: 24px;
    line-height: 24px;
    input {
      height: 24px !important;
      line-height: 24px !important;
      font-size: 12px;
      color: #333;
    }
    .el-input {
      height: 24px;
      .el-input__icon {
        line-height: 24px;
      }
    }
  }

  .el-pagination__jump {
    height: 24px;
    line-height: 24px;
    margin-left: 0;
    font-size: 12px;

    input {
      height: 24px !important;
      border-radius: 2px;
    }
    .el-input__inner {
      padding: 0 3px
    }
  }
}
.el-form .el-form-item__label {
  color: rgba(0,0,0,0.8);
}