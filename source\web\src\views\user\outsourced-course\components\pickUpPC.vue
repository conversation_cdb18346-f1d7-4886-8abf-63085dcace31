<template>
  <div class="redeem-courses-pupop">
    <el-dialog custom-class="redeem-courses-dialog" :visible.sync="visible" width="400px" :close-on-click-modal="false" :show-close="false" @close="handleClose">
      <div class="redeem-courses-main">
        <img class="left-top-pic" src="@/assets/mooc-img/left-top-pic.png" alt="">
        <div class="body">
          <div class="warm">{{$route.query.staff_name || '腾讯学堂'}}</div>
          <div class="title">邀你共读”哈佛精品文库“ </div>
          <div class="coupon-content">
            <span v-if="isGraphic">
              <span>接受邀请，即可阅读当前文章</span>
              <br />
              并解锁<span class="text-05">价值¥1200的哈佛精品文库</span>
            </span>
            <span v-else>
              <span>接受邀请，即可解锁</span>
              <br />
              <span class="text-05">价值¥1200的哈佛精品文库</span>
            </span>
          </div>
          <el-button :class="['btn', { 'disabled-btn': !isQuantity }]" size='medium' :disabled="!isQuantity" @click="userOperator">{{isGraphic ? '接受邀请，去阅读文章' : '接受邀请，点击解锁权限' }}</el-button>
          <div class="footer-tips" v-if="!isQuantity">
            名额已领完，如有疑问请联系minnaluan或v_xxyhe
          </div>
        </div>
      </div>
      <img v-if="!isQuantity" class="close-img" src="@/assets/mooc-img/close-grey.png" alt="" @click="handleClose" />
    </el-dialog>
  </div>
</template>

<script>
export default {
  components: {},
  props: {
    visible: {
      type: Boolean
    },
    isGraphic: {
      type: Boolean,
      default: false
    },
    isQuantity: {
      type: Boolean,
      default: true
    }
  },
  computed: {},
  data() {
    return {}
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    userOperator() {
      this.$emit('handlerReceive')
    }
  }
}
</script>
<style lang="less">
.redeem-courses-dialog {
  background: transparent;
  box-shadow: none;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
.loading-mask-geek {
  .el-icon-loading {
    font-size: 40px;
    color: #fff;
  }
  .el-loading-text {
    color: #e9e9e9;
  }
}
.popper_select_pupop {
  border-radius: 6px;
  background: #fff;
  box-shadow: 0 6px 30px 5px #0000000d, 0 16px 24px 2px #0000000a,
    0 8px 10px -5px #00000014;
  border: none;
  font-size: 12px;
  .popper__arrow {
    display: none;
  }
  .popper-content {
    font-size: 12px;
  }
  .el-select-dropdown__item {
    height: 52px;
    line-height: 20px;
    margin: 5px 12px;
    padding: 6px 8px;
    border-radius: 4px;
    border: 1px solid #eee;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    // background-color: #fff;
    // border-radius: 4px;
    // border: 1px solid var(---Brand7-Hover, #266FE8);
  }
  .el-select-dropdown__item.selected {
    color: unset;
    border-radius: 4px;
    border: 1px solid var(---Brand7-Hover, #266fe8);
    font-weight: 400;
  }
  .num {
    color: #00000066;
  }
  .num-b {
    color: #0052d9;
  }
}
</style>
<style lang="less" scoped>
.redeem-courses-pupop {
  text-align: center;
  .redeem-courses-main {
    position: relative;
    overflow: hidden;
    border-radius: 24px;
    background: linear-gradient(180deg, #e3f2ff 0%, #e3f2ff 0%, #eaf5ff 35.99%);
    .left-top-pic {
      position: absolute;
      width: 89px;
      left: 4px;
      top: 5px;
      z-index: 1;
    }
    .body::before,
    .body::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #e9f4ff;
    }
    .body::before {
      left: 0;
      transform: translate(-50%, -50%);
    }
    .body::after {
      right: 0;
      transform: translate(50%, -50%);
    }
    .body {
      position: relative;
      border-radius: 26px;
      margin: 34px 24px 24px;
      padding: 20px 15.5px;
      background: #fff;
    }
    .btn {
      font-size: 14px;
      font-weight: 500;
      margin: 0 auto;
      color: #ffffff;
      display: flex;
      width: 232px;
      height: 36px;
      padding: 9px 20px;
      justify-content: center;
      align-items: center;
      border-radius: 24px;
      background: linear-gradient(90deg, #488dff 0%, #1766ff 100%);
    }
    .disabled-btn {
      opacity: 0.2;
      background: linear-gradient(90deg, #488dff 0%, #1766ff 100%);
    }
    .warm {
      color: #0052d9;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
    }
    .title {
      margin-top: 16px;
      margin-bottom: 4px;
      color: #00000099;
      font-size: 14px;
      line-height: 20px;
      text-align: center;
    }
    .coupon-content {
      width: 86%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 8px;
      color: #000000;
      font-size: 14px;
      font-weight: 600;
      text-align: center;
      line-height: 22px;
      background: #f9fbfc;
      margin: 12px auto;
      .text-05 {
        color: #0052d9;
      }
    }
    .footer-tips {
      color: #e34d59;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      text-align: center;
      margin-top: 12px;
    }
  }
  .close-img {
    width: 28px;
    margin-top: 16px;
    cursor: pointer;
  }
}
</style>
