<template>
  <div class="image-text-page">
    <!-- <div class="form-label-box" v-if="false">
      <span>确认发布</span>
      <span>检查确认在线课程信息</span>
    </div> -->
    <el-form :model="form" :rules="rules" ref="form" label-width="146px" class="image-text-form">
      <el-form-item label="内容标题" prop="course_name">
        <el-input v-model="form.course_name" placeholder="请输入内容，课程名格式：XXXXX（课程名）| YYY（项目名）" clearable
          :disabled="approveStatus"></el-input>
        <span class="custom-el-input-count" style="top: 6px; bottom: auto;">{{ handleValidor(form.course_name, 50, '1') }}/50</span>
        <p v-if="!approveStatus" class="test-tips"><i class="el-icon-warning color-red mgr-5"></i>若需<span class="color-red">「课程测试」</span>体验效果，请先<span class="color-red text-undeline" @click="toInstructionsPage">查看填写说明</span>，以免耽误进度哦！</p>
      </el-form-item>
      <el-form-item label="运营标题" class="input-style">
        <el-input 
          class="course-name-input" 
          v-model.trim="form.course_statement.operation_title"
          :disabled="approveStatus"
          placeholder="后续宣推中将优先使用该运营标题，学习界面则显示原内容标题"
          clearable>
        </el-input>
        <span class="custom-el-input-count">{{handleValidor(form.course_statement.operation_title, 50, '2')}}/50</span>
      </el-form-item>
      <el-form-item label="父内容ID">
        <div class="parent-content-id">
          <div class="content-type">
            <el-select v-model="form.course_statement.parent_content_act_type" :disabled="approveStatus" placeholder="请选择内容类型" clearable>
              <el-option
                v-for="item in parent_content_act_type_options"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
            <span class="error-tip-text-customer" v-show="showParentId && [null, undefined, ''].includes(form.course_statement.parent_content_act_type)">请选择内容类型</span>
          </div>
          <div class="content-id">
            <el-input v-model.trim="form.course_statement.parent_content_id" :disabled="approveStatus" placeholder="请输入内容ID" clearable></el-input>
            <span class="error-tip-text-customer" v-show="showParentId && !form.course_statement.parent_content_id">请输入内容ID</span>
          </div>
          <p class="get-content-id" @click="toGetContentId" v-if="!approveStatus">如何获取内容ID</p>
        </div>
      </el-form-item>
      <!-- <el-form-item label="是否原创" prop="is_original">
        <div class="original-form">
          <el-radio-group v-model="form.is_original" @change="handleOrigin" :disabled="approveStatus">
            <el-radio :label="1">原创</el-radio>
            <el-radio :label="0">转载</el-radio>
          </el-radio-group>
          <el-input v-show="form.is_original === 0" v-model="form.link_url" placeholder="请输入原文链接"
            style="margin-left: 16px;" clearable :disabled="approveStatus">
          </el-input>
        </div>
      </el-form-item> -->
      <el-form-item class="cut-img-upload" label="封面图" prop="photo_url">
        <!-- <img v-if="approveStatus" width="200px" height="125px" style="object-fit: cover" :src="form.photo_url" class="avatar" /> -->
        <img v-if="approveStatus" width="200px" height="125px" style="object-fit: cover" :src="autoImgUrl" class="avatar" />
        <cut-img-upload 
        v-else 
        ref="upload" 
        @handleSuccess="handleSuccessImage" 
        :dialogImageUrl="dialogImageUrl"
        :autoImgUrl="autoImgUrl" 
        @handleClearImg="handleClearImg" 
        @handleImgEdit="handleImgEdit"
        :cover_imgage_storage_type="form.photo_storage_type"
        >
        <!-- :directlyUpload="directlyUpload" -->
          <template v-slot:text>
            <p>建议图片尺寸：360*240px或3:2宽高比</p>
          </template>
          <template v-slot:createImg>
            <p class="text-orange" style="display: flex; align-items: center" @click="handleAutoImg"><img class="icon" style="width:16px; height: 16px; margin-right: 4px" src="~@/assets/img/tips.png" alt="">快速生成封面图</p>
          </template>
        </cut-img-upload>

      </el-form-item>
      <el-form-item label="课程时长（分钟）" class="time-input" prop="est_dur"
        v-if="$route.query.net_course_id && form.status !== '4'">
        <el-input-number v-model="form.est_dur" controls-position="right" :min="0" :max="9999"
          :disabled="approveStatus">
        </el-input-number>
        <!-- <span class="time-label">分钟</span> -->
        <div class="time-warm">提示：课程播放页面的"完成学习"按钮会在课时所示时间之后才显示，请按照课程实际时长填写</div>
      </el-form-item>
      <el-form-item label="内容创作者" prop="inner_teacher_names">
        <template>
          <div class="inside-person">
            <el-checkbox v-model="form.innerChecked" :disabled="approveStatus" @change="handleInnerChecked">内部员工</el-checkbox>
            <sdc-staff-selector 
            v-if="form.innerChecked" 
            multiple 
            ref="innerSelectorRef" 
            @change="changeInnerAuth"
            v-model="form.inner_teacher_names" 
            size="small" 
            :props="innerProps" 
            placeholder="人员选择器，支持多人，至多10人"
            :disabled="approveStatus" 
            />
          </div>
          <span class="red-tips"><i class="el-icon-warning mgr-5"></i>请准确填写内容分享人，若非真人授课，请填写课程开发人；发布后分享人将获得积分激励。详情请见《QLearning学习积分Q&A》。</span>
        </template>
      </el-form-item>
      <el-form-item prop="out_teacher_names">
        <template>
          <div class="open-person">
            <el-checkbox @change="handleOutChecked" v-model="form.outChecked"
              :disabled="approveStatus">组织团体或外部人员</el-checkbox>
            <el-input v-if="form.outChecked" v-model="form.out_teacher_names" placeholder="请输入组织团体名称或外部分享者名称，多个分享者可用分号分隔"
              clearable :disabled="approveStatus"></el-input>
          </div>
        </template>
      </el-form-item>
      <el-form-item label="课程简介" class="tincy-form" prop="course_desc">
        <div v-if="approveStatus" class="sdc-preview">
          <sdc-mce-preview
            ref="editor"
            :urlConfig="editorConfig.urlConfig"
            :catalogue.sync="editorConfig.catalogue"
            :content="form.course_desc"
          >
          </sdc-mce-preview>
        </div>
        <div v-else>
          <sdc-mce-editor 
          ref="editor" 
          selector="course_desc" 
          :env="editorEnv" 
          :content="form.course_desc"
          :catalogue.sync="editorConfig.catalogue" 
          :urlConfig="editorConfig.urlConfig" 
          :options="editorConfig.options"
          @getWordCount="getWordCount" 
          :insertItems="insertItems" 
          />
          <div class="ticy-tips-content" v-if="!getTincyWordCount" @click="handeEditFocus">
            <p>1、请附上课程内容说明。</p>
            <p>2、若是长视频，请务必附上课程目录结构和每个章节的时间点，格式如下：</p>
            <p class="tincy-indent">一、什么是长视频（00:00-01:10）</p>
            <p class="tincy-indent">二、长视频的特点介绍（01:10-02:30）</p>
            <p class="tincy-indent">三、长视频的应用场景（02:30-05:30）</p>
            <p>若为短视频只包括一个知识点，则无须提供目录和时间点。</p>
          </div>
        </div>
      </el-form-item>

      <template v-if="isAdd">
        <el-form-item label="内容标签" class="tag-form-item" prop="course_labels">
          <sdc-addlabel v-model="form.course_labels"
          :recommend="{ title: form.course_name, desc: course_desc_realtime }" 
          :labelNodeEnv="labelNodeEnv" @getSelectedLabelList="getSelectedLabelList" />
        </el-form-item>
      </template>

      <template v-else>
        <el-form-item label="管理员打的内容标签" class="tag-form-item" prop="course_labels_admin" label-width="150px">
          <div class="tag-box">
            <sdc-addlabel v-show="!approveStatus" v-model="course_labels_map.admin"
            :recommend="{ title: form.course_name, desc: course_desc_realtime }" 
            :labelNodeEnv="labelNodeEnv" @getSelectedLabelList="updateAdminLabel" />
          </div>
          <div class="tag-box" v-show="approveStatus">
            <div class="custom-tag" v-for="item in course_labels_map.admin" :key="item.label_id">{{ item.label_name }}</div>
          </div>
        </el-form-item>
        <el-form-item label="机器打的内容标签" class="tag-form-item" label-width="150px">
          <div class="tag-box mgl-6">
            <el-tag class="custom-tag robat-tags" :class="{'click-tag': !approveStatus}" :closable="!approveStatus" disable-transitions v-for="(tag, index) in course_labels_map.robot" :key="index" @close="delRobotLabel(tag)">{{tag.label_name}}</el-tag>
            <span v-if="!course_labels_map.robot.length">暂无标签</span>
          </div>
        </el-form-item>
        <el-form-item label="用户打的内容标签" class="tag-form-item" label-width="150px">
          <div class="tag-box mgl-6">
            <div class="custom-tag" :class="{'click-tag': !approveStatus}" v-for="item in course_labels_map.user" :key="item.label_id">{{ item.label_name }}</div>
            <span v-if="!course_labels_map.user.length">暂无标签</span>
          </div>
        </el-form-item>
      </template>
      
      <el-form-item label="创作来源" prop="course_statement.creation_source">
        <el-radio-group v-model="form.course_statement.creation_source" :disabled="approveStatus || specialFileDisabled" @change="creationSourceChange">
          <el-radio :label="item.value" v-for="item in creation_source_Options" :key="item.value">{{item.label}}</el-radio>
        </el-radio-group>
        <div class="contact-person contact-person-radio" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
        <div class="origin-warm" v-if="form.course_statement.creation_source === 0">
          <p>(1)「创作组织」从拟定内容主题、提纲、访谈、收集素材到制作课件，一条龙完成。</p>
          <p>(2) 内容创作人所属的最小组织单元，优先填“组”。</p>
        </div>
        <div class="origin-warm" v-if="form.course_statement.creation_source === 3">
            符合以下任一的共创模式：
            <p>（1）「创作组织」拟定主题、提供提纲和素材、组织立项，邀约创作者，由创作者主导内容生产，「创作组织」参与辅助和赋能。创作者拟定主题、提供提纲和素材。</p>
            （2）「创作组织」辅助立项，由创作者主导内容制作，「创作组织」参与辅助和赋能。
        </div>

        <div class="creation_source_sub_content" v-if="![null, undefined, ''].includes(form.course_statement.creation_source)">
          <!-- PGC -->
          <div v-if="form.course_statement.creation_source === 0" key="PGC">
            <el-form-item label="创作组织" :rules="pgcCreationOrgRules" prop="course_statement.pgc_creation_org" label-width="100px" key="pgc_creation_org" style="margin-bottom: 0px">
              <sdc-unit-selector
              class="dep-selector"
              ref="pgcCreationOrgRef"
              v-model="form.course_statement.pgc_creation_org"
              :disabled="approveStatus || specialFileDisabled"
              multiple
              @change="validateField($event, 'pgc_creation_org')"
              placeholder="请选择分享人所属的最小组织单元，如组、中心、部门"
              />
              <div class="contact-person" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
            </el-form-item>
            <!-- <el-form-item label="联合创建组织" label-width="100px" style="margin-bottom: 0px">
              <sdc-unit-selector
                class="dep-selector" 
                ref="pgcJointCreationRef"
                v-model="form.course_statement.pgc_joint_creation"
                :disabled="approveStatus"
                multiple
                placeholder="请选择联合创建组织"
                @change="validateField($event, 'pgc_joint_creation')"
                />
            </el-form-item> -->
          </div>
          <!-- PUGC -->
          <div v-if="form.course_statement.creation_source === 3" key="PUGC">
            <el-form-item label="创作组织" :rules="pugcCreationOrgRules" prop="course_statement.pugc_creation_org" label-width="100px" key="pugc_creation_org">
              <sdc-unit-selector
              class="dep-selector"
              ref="pugcCreationOrgRef"
              v-model="form.course_statement.pugc_creation_org"
              :disabled="approveStatus || specialFileDisabled"
              multiple
              @change="validateField($event, 'pugc_creation_org')"
              placeholder="请选择分享人所属的最小组织单元，如组、中心、部门"
              />
              <div class="contact-person" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
            </el-form-item>
            <el-form-item label="联合创建组织" label-width="100px" style="margin-bottom: 0px">
              <sdc-unit-selector
                class="dep-selector" 
                ref="pugcJointCreationRef"
                v-model="form.course_statement.pugc_joint_creation"
                :disabled="approveStatus || specialFileDisabled"
                multiple
                placeholder="请选择联合创建组织"
                @change="validateField($event, 'pugc_joint_creation')"
                />
              <div class="contact-person" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
            </el-form-item>
          </div>
          <!-- UGC -->
          <div class="ugc-sub-item" v-if="form.course_statement.creation_source === 2" key="UGC">
            <el-form-item class="ugc_is_original" label="是否原创" prop="course_statement.ugc_is_original" label-width="80px" key="ugc_is_original" style="margin-bottom: 10px;">
              <el-radio-group v-model="form.course_statement.ugc_is_original" :disabled="approveStatus">
                <el-radio :label="true">原创</el-radio>
                <el-radio :label="false">转载</el-radio>
              </el-radio-group>
              <el-input v-show="form.course_statement.ugc_is_original === false" v-model="form.course_statement.ugc_link_url" placeholder="请输入原文链接"
                style="margin-left: 16px;" clearable :disabled="approveStatus">
              </el-input>
            </el-form-item>
          </div>
          <!-- OGC -->
          <div v-if="form.course_statement.creation_source === 1" key="OGC">
            <el-form-item label="供应商名称" :rules="supplierNameRules" prop="course_statement.ogc_supplier_name" label-width="100px" class="course-texTarea-input">
              <el-input 
                v-model.trim="form.course_statement.ogc_supplier_name" 
                :disabled="approveStatus"
                placeholder="请填写供应商公司全称或个人全名"
                clearable>
              </el-input>
              <span class="custom-el-input-count">{{handleValidor(form.course_statement.ogc_supplier_name, 100, '3')}}/100</span>
            </el-form-item>
            <el-form-item label="采购组织" :rules="purchaseOrgRules" prop="course_statement.ogc_purchase_org" label-width="100px" key="ogc_purchase_org">
              <sdc-unit-selector
                class="dep-selector" 
                ref="purchaseOrgRef"
                v-model="form.course_statement.ogc_purchase_org"
                :disabled="approveStatus || specialFileDisabled"
                multiple
                @change="validateField($event, 'ogc_purchase_org')"
                placeholder="请选择采购组织"
                />
              <div class="contact-person" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
            </el-form-item>
            <el-form-item label="采购方式" label-width="100px" prop="course_statement.ogc_purchase_type">
              <el-radio-group v-model="form.course_statement.ogc_purchase_type" :disabled="approveStatus">
                <el-radio :label="item.value" v-for="item in purchase_type_Options" :key="item.value">{{item.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="外部讲师" label-width="100px">
                <el-input v-model="form.course_statement.ogc_out_teachers" :disabled="approveStatus" placeholder="请输入外部人员名称，用多个；隔开" clearable></el-input>
            </el-form-item>
          <el-form-item label="采购成本" v-if="isBmBgComSuper" prop="course_statement.ogc_purchase_amount" label-width="100px" key="ogc_purchase_amount">
            <el-input-number class="ogc_purchase_amount" v-model="form.course_statement.ogc_purchase_amount" :disabled="approveStatus" label="请输入采购成本"></el-input-number> 元
          </el-form-item>
          </div>
        </div>
      </el-form-item>

      <el-form-item label="人力成本" v-if="isBmBgComSuper" prop="course_statement.human_cost" key="human_cost">
        <el-input-number class="human_cost" v-model="form.course_statement.human_cost" :disabled="approveStatus" label="请输入人力成本"></el-input-number> 人天
      </el-form-item>
      <el-form-item label="是否必修" v-if="isBmBgComSuper">
        <el-radio-group v-model="form.course_statement.is_required" :disabled="approveStatus || specialFileDisabled">
          <el-radio :label="false">否，全员选修</el-radio>
          <el-radio :label="true">指定人群必修或全员必修</el-radio>
        </el-radio-group>
        <div class="contact-person" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
      </el-form-item>
      <el-form-item label="认证等级">
        <el-radio-group v-model="form.course_level" :disabled="approveStatus">
          <el-radio v-for="item in levelList" :key="item.code" :label="item.code" :disabled="item.disabled">{{ item.name }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="运营分级" v-if="isSuperOrCompanyAdmin">
        <el-radio-group v-model="form.course_statement.operation_level" :disabled="approveStatus">
          <el-radio v-for="item in operation_type_options" :key="item.value" :label="item.value" @change="changeOperationType(item)">{{ item.label }}</el-radio>
        </el-radio-group>
        <div class="creation_source_sub_content" v-if="operation_project_name_options.length">
          <el-form-item label="分级项目" label-width="100px" style="margin-bottom: 0px">
            <el-select v-model="form.course_statement.operation_project_name" :disabled="approveStatus" placeholder="请选择分级项目" clearable>
              <el-option
                v-for="item in operation_project_name_options"
                :key="item.item_name"
                :label="item.item_name"
                :value="item.item_name">
              </el-option>
            </el-select>
          </el-form-item>
        </div>
      </el-form-item>
      <el-form-item label="内容专家评分" v-if="isSuperOrCompanyAdmin" prop="course_statement.expert_score" class="expert-rating">
        <el-input class="expert-rating-input"
          type="number"
          v-model="form.course_statement.expert_score" 
          :disabled="approveStatus"
          placeholder="请输入评估分数"
          clearable>
        </el-input>
        <span class="tip-warm">请输入0-100的数字，可填写两位小数</span>
      </el-form-item>
      <el-form-item label="内容用户评分" v-if="isSuperOrCompanyAdmin" class="expert-rating">
        {{ form.course_statement.user_score ? `${resolveScore(form.course_statement.user_score) }分` : '-'}}
        <span class="tip-warm">自动拉取用户评价分</span>
      </el-form-item>
      <el-form-item label="是否加入推荐流">
        <el-radio-group v-model="form.course_statement.join_recommend" :disabled="approveStatus || specialFileDisabled">
          <el-radio :label="false">否，不加入推荐池</el-radio>
          <el-radio :label="true">是，加入推荐池</el-radio>
        </el-radio-group>
        <div class="contact-person contact-person-radio" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
        <div class="red-tips-customer"><i class="el-icon-warning mgr-5"></i>合规类、应知应会类、应学类课程等请勿选择加入推荐流。</div>
      </el-form-item>

      <!-- 进阶配置 -->
      <!-- <div :class="[{ 'close-basics': !showAdvanced }, 'open-basics']">
        <div class="line"></div>
        <span class="open-btn" @click="showAdvanced = !showAdvanced">进阶配置<span
            :class="[{ 'basisc-active-icon': showAdvanced }, 'btn-basisc-icon']"></span></span>
      </div> -->
      <el-row>
        <el-form-item label="课程管理员" prop="course_admins" class="manage-person">
          <sdc-staff-selector multiple ref="adminsSelectorRef" @change="changeCourseAuth" v-model="form.course_admins"
            size="small" :props="adminProps" placeholder="人员选择器，默认拉取上传者，支持多人，至多10人" :disabled="approveStatus" />
        </el-form-item>
        <el-form-item label="内容管理组织" prop="dept_id" class="manage-person">
          <sdc-unit-selector ref="deptSelectorRef" v-model="form.dept_id" size="small" @change='handleUnitSelect' placeholder="请选择管理员所属的最小组织单元，如组、中心、部门"
            :props="deptProps" :disabled="approveStatus || specialFileDisabled" />
          <div class="contact-person" v-if="!approveStatus && specialFileDisabled">本字段若需修改，请联系graywu。</div>
        </el-form-item>
        <el-form-item class="isOpen" label="课程访问权限" prop="limit">
          <div class="target-person">
            <div class="open-target-radio">
              <el-radio-group v-model="form.limit" @change="handleOpenPart" :disabled="approveStatus">
                <el-radio :label="0">仅对集团正式员工开放</el-radio>
                <el-radio :label="2">仅对集团正式员工、毕业生、实习生开放</el-radio>
                <el-radio :label="1">自定义开放范围</el-radio>
              </el-radio-group>
            </div>
            <AudienceSelector v-if="form.limit === 1" :showComfirm="!approveStatus"  audience :showTab="['unit', 'group', 'import']" multiple
              v-model="form.target_list" ref="selector" appCode="qlearning" :env="audienceEnv" importNumber='1000'
              :isShowCount="false" :createStudentID="true"/>
          </div>
        </el-form-item>
        <el-form-item v-if="directlyUpload" label="外挂字幕"
          :class="[{ 'none-upload-btn': subttileSetInfo.caption_id }, 'subttile-box']">
          <div class="subttile-form">
            <el-radio-group v-model="form.show_caption" @change="handleOpenSubttile" :disabled="approveStatus">
              <el-radio :label="0">关闭</el-radio>
              <el-radio :label="1">开启</el-radio>
            </el-radio-group>
            <el-upload v-show="form.show_caption === 1 && !subttileSetInfo.caption_id" action="" accept=".srt,.vtt"
              :with-credentials="true" :http-request="onUpload" :file-list="subttileSetInfo.fileList"
              :show-file-list="false">
              <div id="srt-upload-btn" class="upload-btn">
                <i class="el-icon-upload2"></i>
                <span>上传字幕文件（支持srt文件）</span>
              </div>
            </el-upload>
            <span class="subttile-tips">如使用AI做课功能，建议关闭此功能</span>
          </div>
        </el-form-item>
        <!-- 字幕已上传 -->
        <subttile-upload v-show="form.show_caption === 1 && subttileSetInfo.caption_id && directlyUpload"
          ref="subttileUpload" @confirmOnUpload="confirmOnUpload" @againUpload="againUpload" />
        <!-- 内容状态只在编辑显示 -->
        <!-- <el-form-item label="内容状态" prop="status"
          v-if="$route.query.net_course_id && !['0', null, '4', '6', '7', '8'].includes(form.status)">
          <el-radio-group v-model="form.status">
            <el-radio label="1">在用</el-radio>
            <el-radio label="3">停用</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="是否支持VPN/NGN" v-if="isSuperOrCompanyAdmin">
          <!-- <el-checkbox-group v-model="form.allow_vpn" :disabled="approveStatus"> -->
          <el-checkbox-group v-model="form.allow_vpn" disabled>
            <el-checkbox label="1">VPN</el-checkbox>
            <el-checkbox label="2">NGN</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="是否支持移动端" v-if="isSuperOrCompanyAdmin">
          <el-radio-group v-model="form.support_mobile" :disabled="approveStatus">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否展示文稿">
          <el-radio-group v-model="form.course_statement.show_manuscript" :disabled="approveStatus">
            <el-radio :label="true">是</el-radio>
            <el-radio :label="false">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="展示课程推荐" class="recomed" v-if="isSuperAdmin">
          <el-checkbox v-model="form.is_show_recommend" :disabled="approveStatus">是</el-checkbox><span class="tip-warm">（预计访问量高时，提前关闭该选项，提升页面加载速度）</span>
        </el-form-item>
        <el-form-item label="是否同步给小Q同学" class="is-required" prop="ai_sync_flag" v-if="isSuperAdmin">
          <el-radio-group v-model="form.ai_sync_flag" :disabled="specialFileDisabled">
            <el-radio :label="1">同步
              <el-tooltip class="item" effect="dark" content="数据同步给小Q同学知识库，权限与本页面设置相同" placement="bottom-start">
                <i class="el-icon-warning-outline async-icon"></i>
              </el-tooltip>
            </el-radio>
            <el-radio :label="0">不同步</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="数据有效时间" class="is-required" prop="ai_expire_type" v-if="isSuperAdmin && form.ai_sync_flag === 1">
          <el-radio-group v-model="form.ai_expire_type" :disabled="specialFileDisabled">
            <el-radio :label="1">长期</el-radio>
            <el-radio :label="2">自定义到期时间
              <el-date-picker 
                v-if="form.ai_expire_type === 2"
                v-model="form.ai_expire_end_time"
                type="datetime" 
                value-format="yyyy-MM-dd HH:mm:ss"
                default-time="00:00:00" 
              >
              </el-date-picker>
              <el-tooltip class="item" effect="dark" content="到期后，数据将在小Q同学知识库不可使用" placement="bottom-start">
                <i class="el-icon-warning-outline async-icon"></i>
              </el-tooltip>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            type="textarea"
            :rows="5"
            placeholder="请输入备注"
            v-model="form.remark"
            :disabled="approveStatus"
          >
          </el-input>
        </el-form-item>

        <!-- <div class="submit-must-tips">
          <span class="submit-label">{{$route.query.net_course_id ? '提交前必检查' : '提交前必填'}}</span>
          <div>
            <el-link type="primary" href="https://doc.weixin.qq.com/sheet/e3_AFkAXgbdAFwlEq9ShItSTG8r20W9G?scode=AJEAIQdfAAo4QV7z7DAFkAXgbdAFw&tab=BB08J2" target="_blank">《请点击此打开问卷，补充填写相关统计字段》</el-link>
            <div class="tips">
              <p>说明：</p>
              <p>1、此问卷补充的字段信息，将用于新报表统计汇总，方便各级管理员随时统计使用。</p>
              <p>2、为保障统计准确，<span class="red-tips">请大家务必及时、准确无误填写。</span>（若有需要，请在各内容重编辑页修改。）</p> 
              <p>3、由于QL系统底层数据重构中，在功能上线前，将临时采用问卷方式进行信息收集；功能上线时便可统一入库，避免各管理员后续的二次返工补充。</p> 
            </div>
          </div>
        </div> -->

        <div class="add-course-tips" v-show="form.status === '1'">
          <span>添加到课单</span>
          <span class="add-course-list">已添加到{{ courseCount }}个课单</span>
          <el-button type="text" @click="addEditCourse">点击此处修改或新增</el-button>
        </div>
        <div class="main-table">
          <div class="table-tips">
            <div class='tips-label'><span>延伸学习</span><span>此处关联的内容将在详情页中显示</span></div>
            <el-button type="text" icon="el-icon-plus" @click="showAddExtandLearn = true"
              :disabled="approveStatus">新增内容</el-button>
          </div>
          <div class="upload-table-box">
            <el-table :data="tableData" style="width: 100%" header-row-class-name="table-header-style"
              row-class-name="table-row-style">
              <el-table-column prop="content_name" label="内容标题" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span
                    :class="['tags', getModuleClass(scope.row.content_module_id)]">{{ scope.row.content_module_name }}</span><span>{{ scope.row.content_name }}</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="92">
                <template slot-scope="scope">
                  <div class="operation-box">
                    <span class="operation-icon-up" @click="handleUp(scope.row, scope.$index)"></span>
                    <span><i class="el-icon-delete" @click="handleDelete(scope.row, scope.$index)"></i></span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-row>
    </el-form>
    <!-- 一键生成课单封面 -->
    <sdc-img-cover ref="sdcImgCoverRef" :visible.sync="autoImgCoverShow" :imgInfo="imgInfo"
      @handleImgCoverOk="handleImgCoverOk">
    </sdc-img-cover>
    <!-- 添加到课单 -->
    <AddCourseDialog :visible.sync="addCourseDialogShow" :itemData.sync="addCourseDialogData" @addedHandle="getCourseCount" />
    <AddCourseSelfDialog :visible.sync="addCourseDialogShowSelf" :itemData.sync="addCourseSelfDialogData"
      :cl_ids="form.cl_ids" @addedHandle="getCheckedClIds" />
    <!-- 延伸学习 -->
    <AddExtandLearnDialog v-if="showAddExtandLearn" @closeAddExDialog="closeAddExDialog" :prodData="prodData"
      ref="extandLearn" />
    <!-- 转载--原创弹窗说明 -->
    <copyright v-if="copyrightShow" :visible.sync="copyrightShow" :typeOriginal="form.is_original"
      @handleCheck="handleCheck"></copyright>
    <!-- 特殊字段的修改弹窗 -->
    <el-dialog
      class="update-dialog"
      title="提示"
      :visible.sync="showDialog"
      width="600px"
      :close-on-click-modal="false"
      :before-close="handleClose">
      <div class="update-content-box">
        <span class="dialog-label">修改内容:</span>
        <el-input class="update-text-input"
          v-model="updateText" 
          placeholder="请输入修改的内容"
          clearable>
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取 消</el-button>
        <el-button type="primary" @click="handleOk">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { CutImgUpload, AddCourseDialog, AddExtandLearnDialog, AddCourseSelfDialog } from '@/components/index'
import subttileUpload from './subttile-upload.vue'
import { AudienceSelector } from '@tencent/sdc-audience'
import { getContentAddedCount, getExtanContentList, getOperationApi } from '@/config/api.conf.js'
import { qlearningModuleTypes } from 'utils/constant'
import copyright from '@/views/user/components/copyright.vue'
import env from 'config/env.conf.js'
import { mapState } from 'vuex'
const envName = env[process.env.NODE_ENV]
const allTarget = 2015587
const studentTarget = '2030108'
const EXP = /^\s*(-?(\d+(\.\d{0,2})?|\.\d{1,2}))\s*$/

export default {
  components: {
    CutImgUpload,
    AudienceSelector,
    AddCourseDialog,
    AddExtandLearnDialog,
    subttileUpload,
    AddCourseSelfDialog,
    copyright
  },
  props: {
    courseInfo: {
      type: Object,
      default: () => ({})
    },
    videoStatus: { // add是新增，publish是发布
      type: String,
      default: 'add'
    },
    directlyUpload: { // 判断字幕设置展示与否---直接上传字幕设置展示在上方
      type: Boolean,
      default: true
    },
    pptName: {
      type: String,
      default: ''
    },
    pptImg: {
      type: String,
      default: ''
    },
    approveStatus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    // const validClassifyId = (rule, value, callback) => {
    //   if (!this.form.classify_id?.length) {
    //     return callback(new Error('请选择分类'))
    //   } else {
    //     callback()
    //   }
    // }
    const validLinkUrl = (rule, value, callback) => {
      if (value === '') {
        return callback(new Error('请选择是否原创'))
      } else if (value === 0 && !this.form.link_url) {
        return callback(new Error('请输入转载链接'))
      } else {
        callback()
      }
    }
    const validImg = (rule, value, callback) => {
      if (!value && !this.form.photo_id) {
        return callback(new Error('请选择封面'))
      } else {
        callback()
      }
    }
    // 内部员工
    const validInner = (rule, value, callback) => {
      if (this.form.innerChecked && !value?.length) {
        return callback(new Error('人员选择器，支持多人，至多10人'))
      } else {
        callback()
      }
    }
    // 外部员工
    const validOutTeacher = (rule, value, callback) => {
      if (this.form.outChecked && !value?.length) {
        return callback(new Error('请输入组织团体名称或外部分享者名称，多个分享者可用分号分隔'))
      } else {
        callback()
      }
    }
    const validOutName = (rule, value, callback) => {
      if (value === 1 && (!this.form.target_list || this.form.target_list * 1 === allTarget || this.form.target_list === studentTarget)) {
        return callback(new Error('请选择部分开放人员'))
      } else {
        callback()
      }
    }
    const validAmount = (rule, value, callback) => {
      if ((value && (value < 0 || !EXP.test(value))) || value === 0) {
        return callback(new Error('采购成本需大于0, 至多可填写两位小数'))
      } else {
        callback()
      }
    }
    const validUgcIsOriginal = (rule, value, callback) => {
      if (value === null || value === '') {
        return callback(new Error('请选择是否原创'))
      } else if (value === false && !this.form.course_statement.ugc_link_url) {
        return callback(new Error('请输入转载链接'))
      } else {
        callback()
      }
    }
    const validUgcCreatorType = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('请选择创作人类型'))
      } else if (value === 1 && !this.form.course_statement.ugc_creator_staff.length) {
        return callback(new Error('请选择创作人'))
      } else if (value === 2 && !this.form.course_statement.ugc_creator_staff_input) {
        return callback(new Error('请输入外部人员名称'))
      } else {
        callback()
      }
    }
    const validHumanCost = (rule, value, callback) => {
      if (!this.approveStatus && ((value && (value <= 0 || !EXP.test(value))) || value === 0)) {
        return callback(new Error('人力成本需大于0, 至多可填写两位小数'))
      } else {
        callback()
      }
    }
    const validExpertRating = (rule, value, callback) => {
      if (value && (value < 0 || value > 100 || !EXP.test(value))) {
        return callback(new Error('输入的值要大于等于0小于等于100, 至多可填写两位小数'))
      } else {
        callback()
      }
    }
    const validAdminLabel = (rule, value, callback) => {
      if (!this.course_labels_map.admin.length) {
        return callback(new Error('请添加标签'))
      } else {
        callback()
      }
    }
    const validExpireType = (rule, value, callback) => {
      if (!value || (value === 2 && !this.form.ai_expire_end_time)) {
        return callback(new Error('请选择数据有效时间'))
      } else {
        callback()
      }
    }
    return {
      showDialog: false, // 是否显示特殊字段弹窗
      updateText: '', // 修改的内容
      bakArg: {}, // 父组件传递的参数
      course_desc_bak: null, // 课程描述备份 用来比较课程描述是否发生改变
      parent_content_act_type_options: [
        // { label: '面授课', value: 0 },
        // { label: '活动', value: 1 },
        // { label: '网络课', value: 2 },
        // { label: 'MOOC', value: 3 },
        // { label: 'SPOC', value: 4 },
        // { label: '直播', value: 5 },
        // { label: '文章', value: 6 },
        // { label: '案例', value: 7 },
        // { label: '课单', value: 8 },
        // { label: '行家', value: 9 }
        { label: '面授课', value: 1 },
        { label: '活动', value: 4 },
        { label: '网络课', value: 2 },
        { label: 'MOOC', value: 11 },
        { label: 'SPOC', value: 27 },
        { label: '直播', value: 5 },
        { label: '文章', value: 18 },
        { label: '案例', value: 16 },
        { label: '课单', value: 15 },
        { label: '行家', value: 19 }
      ],
      creation_source_Options: [
        { label: '培训or业务团队独立开发(PGC)', value: 0 },
        { label: '培训团队联合业务作者合作创作（PUGC）', value: 3 },
        { label: '员工自发原创（UGC）', value: 2 },
        { label: '外部引入（OGC）', value: 1 }
      ],
      purchase_type_Options: [
        { label: '个人按需购买', value: 0 },
        { label: '公司统一采购', value: 1 },
        { label: '账号采购', value: 2 }
      ],
      // 分级目录选项
      operation_project_name_options: [],
      // 分级目录集合
      operation_project_name_options_map: {},
      course_desc_realtime: '', // 富文本"课程描述"实时数据,作为添加标签弹窗的"推荐标签"接口的参数
      form: {
        course_name: '',
        is_original: '',
        link_url: '', // 转载链接
        course_labels: [], // 标签
        photo_url: '',
        photo_id: '',
        photo_storage_type: '',
        course_admins: [],
        innerChecked: true,
        inner_teacher_names: [],
        outChecked: false,
        course_desc: '',
        out_teacher_names: '',
        show_caption: 0,
        limit: 0, // 权限
        target_list: '', // 权限人员
        status: '',
        dept_id: '', // 内容管理组织id
        dept_name: '', // 内容管理组织名
        extend_list: [], // 延伸学习
        caption_id: '', // 字幕文件id
        caption_name: '',
        caption_size: '',
        cl_ids: [],
        course_statement: {
          operation_title: '', // 运营标题
          parent_content_act_type: null, // 父内容类型
          parent_content_id: '', // 父内容ID
          creation_source: 0, // 创作来源
          pgc_creation_org: [], // PGC创作组织
          pgc_joint_creation: [], // PGC联合创作组织
          pugc_creation_org: [], // PUGC创作组织
          pugc_joint_creation: [], // PUGC联合创作组织
          ugc_is_original: null, // 是否原创
          ugc_link_url: '', // 转载链接
          ugc_creator_type: null, // 创作人类型
          ugc_creator_staff: [], // 创作人 （内部员工）
          ugc_creator_staff_input: '', // 创作人（外包需要特殊处理）
          ogc_supplier_name: '', // 供应商名称
          ogc_purchase_org: [], // 采购组织
          ogc_out_teachers: '', // 外部讲师
          ogc_purchase_type: null, // 采购方式
          ogc_purchase_amount: undefined, // 采购成本
          human_cost: undefined, // 人力成本
          is_required: false, // 是否纳入应学
          operation_level: 3, // 运营分级
          operation_project_name: '', // 分级项目
          expert_score: null, // 内容专家评分
          user_score: 0, // 内容用户评分
          join_recommend: true, // 是否加入推荐流
          show_manuscript: true // 是否展示文稿
        },
        course_level: 0, // 认证等级 
        allow_vpn: ['1', '2'], // 支持VPN/NGN
        support_mobile: 1, // 是否支持移动端
        is_show_recommend: true, // 展示课程推荐
        remark: '', // 备注
        ai_sync_flag: 1, // 是否同步给小Q同学 0-不同步 1-同步
        ai_expire_type: 1, // 数据有效时间 1-长期 2-自定义到期时间
        ai_expire_end_time: '' // 数据有效结束时间
      },
      courseLevelList: [
        { code: 0, name: '无', disabled: false }, 
        { code: 1, name: '公司级', disabled: false }, 
        { code: 2, name: 'BG级', disabled: false },
        { code: 3, name: '部门级', disabled: false }
      ],
      subttileSetInfo: { // 字幕设置传递数据
        caption_name: '',
        caption_id: '',
        caption_size: '',
        fileList: [],
        percent: 0
      },
      audienceEnv: process.env.NODE_ENV,
      tagLoading: false,
      getTincyWordCount: 0,
      imgInfo: {},
      tableData: [],
      labelNodeEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test',
      addCourseDialogShowSelf: false,
      addCourseDialogData: {},
      autoImgCoverShow: false,
      addCourseDialogShow: false,
      addCourseSelfDialogData: {
        module_id: 1,
        module_name: '网络课'
      },
      prodData: {
        prod_id: 1,
        prod_type: '网络课'
      },
      courseCount: 0,
      showAddExtandLearn: false, // 延伸学习
      // showAdvanced: false, // 进阶配置
      recommendTagList: [],
      coverImgId: '',
      copyrightShow: false,
      dialogImageUrl: '', // 内容中心
      autoImgUrl: '', // 一键封面
      innerProps: {
        staffID: 'teacher_id',
        staffName: 'teacher_name'
      },
      adminProps: {
        staffID: 'admin_id',
        staffName: 'admin_name'
      },
      deptProps: {
        UnitFullName: 'dept_name',
        unitID: 'dept_id'
      },
      insertItems: [
        'link',
        'table',
        'image',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorConfig: {
        catalogue: false,
        options: { // 非必传字段
          selector: '#course_desc',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
              formatselect fontsizeselect lineheight |
              bold italic underline strikethrough |
              forecolor backcolor |
              dent align bullist numlist |
              insert codesample |
              fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      },
      editorEnv: process.env.NODE_ENV,
      rules: {
        course_name: [
          { required: true, message: '请输入内容标题', trigger: 'blur' }
        ],
        is_original: [
          { required: true, validator: validLinkUrl, trigger: 'change' }
        ],
        course_labels: [
          { required: true, message: '请添加标签', trigger: 'change' }
        ],
        // classify_id: [
        //   { type: 'array', required: true, validator: validClassifyId, tirgger: 'change' }
        // ],
        photo_url: [
          { required: true, validator: validImg, trigger: 'blur' }
        ],
        course_desc: [
          { required: true, message: '请输入课程简介', trigger: 'blur' }
        ],
        limit: [
          { required: true, validator: validOutName, trigger: 'blur' }
        ],
        course_admins: [
          { required: true, message: '请选择课程管理员', trigger: 'blur' }
        ],
        est_dur: [
          { required: true, message: '请输入时长', trigger: 'blur' }
        ],
        inner_teacher_names: [
          { required: true, validator: validInner, trigger: 'blur' }
        ],
        out_teacher_names: [
          { required: true, validator: validOutTeacher, trigger: 'blur' }
        ],
        status: [
          { required: true, message: '请选择内容状态', trigger: 'blur' }
        ],
        dept_id: [
          { required: true, message: '请选择内容管理组织', trigger: 'blur' }
        ],
        course_labels_admin: [
          { required: true, message: '请添加标签', trigger: 'blur', validator: validAdminLabel }
        ],
        'course_statement.creation_source': [{ required: true, message: '请选择创作来源', trigger: 'change' }],
        'course_statement.ogc_purchase_amount': [{ validator: validAmount, trigger: ['blur', 'change'] }],
        'course_statement.ugc_is_original': [{ required: true, validator: validUgcIsOriginal, trigger: 'change' }],
        'course_statement.ugc_creator_type': [{ required: true, validator: validUgcCreatorType, trigger: ['change'] }],
        'course_statement.human_cost': [{ validator: validHumanCost, trigger: ['blur', 'change'] }],
        // course_level: [{ required: true, message: '请选择认证等级', trigger: 'change' }],
        'course_statement.expert_score': [{ validator: validExpertRating, trigger: ['blur', 'change'] }],
        'course_statement.ogc_purchase_type': [{ required: true, message: '请选择采购方式', trigger: ['change'] }],
        ai_sync_flag: [{ required: true, message: '是否同步给小Q同学', trigger: 'change' }],
        ai_expire_type: [{ validator: validExpireType, required: true, message: '请选择数据有效时间', trigger: 'change' }]
      },
      course_labels_map: { // 标签合集
        admin: [], // 管理员
        robot: [], // 机器
        user: [] // 用户
      },
      isDescChanged: false, // 课程描述是否更新
      initData: true // 第一次初始化获取拓展的接口数据
    }
  },
  provide() {
    return {
      handleCreateGraghic: () => { }
    }
  },
  watch: {
    courseInfo: {
      deep: true,
      immediate: true,
      handler(val) {
        console.log('val-----------------------------------: ', val)
        if (!val.net_course_id) return
        const net_course_id = this.$route.query.net_course_id
        if (net_course_id) {
          // 处理标签数据
          this.resulveLabelData(val)
        }

        // 新增的人员和组织字段处理
        let course_statement = null
        try {
          course_statement = JSON.parse(JSON.stringify(val.course_statement))
        } catch (error) {
          course_statement = null
        }

        // 新增字段 有值数据处理
        if (course_statement) {
          const resolveArray = ['pgc_creation_org', 'pgc_joint_creation', 'pugc_creation_org', 'pugc_joint_creation', 'ogc_purchase_org']
          resolveArray.forEach(item => {
            try {
              if (!course_statement[item]) { // 兼容编辑时字段为null的情况
                course_statement[item] = []
              } else {
                course_statement[item] = JSON.parse(course_statement[item])
              }
            } catch (error) {
              course_statement[item] = []
            }
          })

          // 特殊处理UGC的创作人字段 (已废弃)
          if (course_statement.ugc_creator_type === 1) {
            // 内部员工
            try {
              course_statement['ugc_creator_staff'] = JSON.parse(course_statement['ugc_creator_staff'])
            } catch (error) {
              course_statement['ugc_creator_staff'] = [] 
            }
          } else if (course_statement.ugc_creator_type === 2) {
            // 外部员工
            try {
              let ugc_creator_staff_array = JSON.parse(course_statement['ugc_creator_staff'])
              course_statement['ugc_creator_staff_input'] = ugc_creator_staff_array.map(item => item.StaffName).join(';')
            } catch (error) {
              course_statement['ugc_creator_staff_input'] = ''
            }
          }

          // 特殊处理OGC的外部讲师字段 ogc_out_teachers
          if (course_statement.ogc_out_teachers) {
            try {
              let ogc_out_teachers_array = JSON.parse(course_statement['ogc_out_teachers'])
              course_statement['ogc_out_teachers'] = ogc_out_teachers_array.map(item => item.StaffName).join(';')
            } catch (error) {
              course_statement['ogc_out_teachers'] = ''
            }
          }
        } else {
          // 新增字 没值初始化数据
          course_statement = {
            operation_title: '',
            parent_content_act_type: null,
            parent_content_id: null,
            creation_source: 0,
            pgc_creation_org: [],
            pgc_joint_creation: [],
            pugc_creation_org: [],
            pugc_joint_creation: [],
            ugc_is_original: null,
            ugc_link_url: '',
            ugc_creator_type: null,
            ugc_creator_staff: [],
            ugc_creator_staff_input: '',
            ogc_supplier_name: '',
            ogc_purchase_org: [],
            ogc_out_teachers: '',
            ogc_purchase_type: null,
            ogc_purchase_amount: undefined,
            human_cost: undefined,
            is_required: false,
            operation_level: 3,
            operation_project_name: '',
            expert_score: null,
            user_score: 0,
            join_recommend: null,
            show_manuscript: true
          }
        }

        const {
          ogc_purchase_amount,
          pgc_creation_org,
          pgc_joint_creation,
          pugc_creation_org,
          pugc_joint_creation,
          // ugc_creator_staff,
          ogc_purchase_org,
          human_cost,
          ugc_is_original,
          ugc_link_url
        } = course_statement

        this.form = {
          // ...val,
          ...JSON.parse(JSON.stringify(val)),
          course_level: val.course_level === 4 ? 0 : val.course_level,
          is_original: ugc_is_original ? 1 : 0,
          link_url: ugc_link_url,
          // limit: Number(Boolean(val.target_list)), // 限制
          limit: val.target_list * 1 === allTarget ? 0 : val.target_list === studentTarget ? 2 : 1, // 限制
          innerChecked: Boolean(val.inner_teacher_names?.length), // 内部
          outChecked: Boolean(val.out_teacher_names?.length), // 外部
          status: val.status,
          course_statement: {
            ...course_statement,
            ogc_purchase_amount: [null, '', 0, '0'].includes(ogc_purchase_amount) ? undefined : ogc_purchase_amount,
            human_cost: [null, '', 0, '0'].includes(human_cost) ? undefined : human_cost
          },
          allow_vpn: val.allow_vpn ? val.allow_vpn.split(';') : [],
          is_show_recommend: val.is_show_recommend !== 0,
          ai_sync_flag: val.ai_sync_flag === undefined ? 1 : val.ai_sync_flag ? 1 : 0,
          ai_expire_type: val.ai_expire_type ? val.ai_expire_type : 1,
          ai_expire_end_time: val.ai_expire_type === 2 ? val.ai_expire_end_time : ''
        }
        // 分类
        // this.form.classify_id = (val.course_classifies || []).map((e) => e.classify_id)
        // 标签
        // this.form.course_labels = val.course_labels.map((e) => e.name)
        // 外部员工
        this.form.out_teacher_names = (val.out_teacher_names || []).map((e) => e.teacher_name).join(';')
        // 添加到课单
        this.form.cl_ids = val?.cl_ids ? val.cl_ids.split(',') : []
        // 内部员工，管理员，组织
        this.$nextTick(() => {
          if (val.inner_teacher_names?.length) {
            this.$refs.innerSelectorRef.setSelected(val.inner_teacher_names)
          }
          if (val.course_admins?.length) {
            this.$refs.adminsSelectorRef.setSelected(val.course_admins)
          }
          if (val.dept_id && val.dept_name) {
            this.$refs.deptSelectorRef.setSelected([{ dept_id: val.dept_id, UnitName: val.dept_name, UnitFullName: val.dept_name }])
          }
          // 新增字段的 成员和组织回显已经选中的
          if (pgc_creation_org?.length) { // PGC创作组织
            this.$refs.pgcCreationOrgRef && this.$refs.pgcCreationOrgRef.setSelected(pgc_creation_org)
          }
          if (pgc_joint_creation?.length) { // PGC联合创作组织
            this.$refs.pgcJointCreationRef && this.$refs.pgcJointCreationRef.setSelected(pgc_joint_creation)
          }
          if (pugc_creation_org?.length) { // PUGC 创作组织
            this.$refs.pugcCreationOrgRef && this.$refs.pugcCreationOrgRef.setSelected(pugc_creation_org)
          }
          if (pugc_joint_creation?.length) { // PUGC联合创作组织
            this.$refs.pugcJointCreationRef && this.$refs.pugcJointCreationRef.setSelected(pugc_joint_creation)
          }
          // if (ugc_creator_staff?.length) { // 创作人
          //   this.$refs.ugcCreatorStaffRef.setSelected(ugc_creator_staff)
          // }
          if (ogc_purchase_org?.length) { // 采购组织
            this.$refs.purchaseOrgRef && this.$refs.purchaseOrgRef.setSelected(ogc_purchase_org)
          }
        })
        this.dialogImageUrl = ''
        const imgUrl = val.photo_storage_type === 'contentcenter' && val.photo_id ? `${envName.contentcenter}content-center/api/v1/content/imgage/${val.photo_id}/preview` : val.photo_url
        this.autoImgUrl = imgUrl
        // 获取推荐标签默认值
        // if (this.form.course_name) {
        //   this.getRecommend()
        // }
        // 获取封面
        // if (this.form.photo_storage_type === 'contentcenter') {
        //   this.form.photo_url = val.photo_url
        //   this.form.photo_id = ''
        // } else {
        //   this.form.photo_id = val.photo_id
        //   this.form.photo_url = ''
        // }
        // 字幕设置数据
        if (val.captions?.length) {
          this.subttileSetInfo = {
            caption_name: val.captions[0]?.file_name,
            caption_id: val.captions[0]?.content_id,
            caption_size: val.captions[0]?.size
          }
        }
        this.$nextTick(() => {
          this.$refs.subttileUpload.initData(this.subttileSetInfo)
        })
        // 添加到课单所需数据
        const url = location.hostname.endsWith('.woa.com') ?
          `${process.env.VUE_APP_PORTAL_HOST_WOA}/training/netcourse/play?course_id=${net_course_id}` :
          `${process.env.VUE_APP_PORTAL_HOST}/training/netcourse/play?course_id=${net_course_id}`
        this.addCourseDialogData = {
          module_id: 1,
          module_name: '网络课',
          content_name: this.form.course_name,
          href: url,
          item_id: net_course_id,
          origin: location.origin
        }

        if (net_course_id) {
          if (!this.initData) return
          this.initData = false
          // 延伸学习列表获取
          this.getExtandList()
          // 课单数量获取
          this.getCourseCount()
          // 编辑时获取运营分级对应的分级项目
          this.getLevelObjectOption()
        }
      }
    },
    '$store.state.userDepInfo': {
      deep: true,
      immediate: true,
      handler(val) {
        // 默认拉取组织
        const userDepInfo = this.$store.state.userDepInfo
        if (!userDepInfo) return
        if (!this.courseInfo?.dept_id && userDepInfo?.dept_id) {
          this.$nextTick(() => {
            this.$refs.deptSelectorRef.setSelected([{ dept_id: userDepInfo.dept_id, UnitName: userDepInfo.dept_full_name, UnitFullName: userDepInfo.dept_full_name }])
          })
        }
        // 内部员工
        // if (!this.courseInfo?.inner_teacher_names) {
        //   this.$nextTick(() => {
        //     // this.$refs.innerSelectorRef.setSelected({ teacher_id: userDepInfo.staff_id, teacher_name: `${userDepInfo.emp_name_en}(${userDepInfo.emp_name_ch})` })
        //     this.$refs.adminsSelectorRef.setSelected([{ admin_id: userDepInfo.staff_id, admin_name: `${userDepInfo.emp_name_en}(${userDepInfo.emp_name_ch})` }])
        //   })
        // }
        if (!this.courseInfo?.course_admins?.length && userDepInfo?.staff_id) {
          this.$nextTick(() => {
            this.$refs.adminsSelectorRef.setSelected([{ admin_id: userDepInfo.staff_id, admin_name: `${userDepInfo.emp_name_en}(${userDepInfo.emp_name_ch})` }])
          })
        }
      }
    },
    pptName(val) {
      if (val && !this.form.course_name) {
        this.form.course_name = val.split('.')[0]
        this.$refs['form'].clearValidate(['course_name'])
        // this.getRecommend()
      }
    },
    pptImg(val) {
      if (val && !this.form.photo_id) {
        this.form.photo_id = val
        this.form.photo_url = ''
        this.form.photo_storage_type = 'other'
      }
    },
    // 内容标题、封面图、标签、描述任何一个发生改变
    isOtherChanged(val) {
      this.$emit('filesChanged', val)
    },
    isInfoSafeChange(val) {
      this.$emit('safeChange', val)
    }
  },
  computed: {
    ...mapState(['userLimitInfo']),
    // 内容标题
    isTitleChanged() {
      return !!(this.form.course_name !== this.courseInfo.course_name)
    },
    // 运营标题
    isStatementTitleChanged() {
      return !!(this.courseInfo.course_statement?.operation_title && (this.form.course_statement.operation_title !== this.courseInfo.course_statement.operation_title))
    },
    // 封面图
    isCoverImgChanged() {
      return !!(this.form.photo_url !== this.courseInfo.photo_url)
    },
    // 简介
    isDesc() {
      return !!(this.$refs['editor'].getContent() !== this.courseInfo.course_desc)
    },
    // 标签
    isLabelChanged() {
      let isSame = this.course_labels_map.admin.every(item => this.adminLabelIds.includes(item.label_id))
      return !!(this.course_labels_map.admin.length !== this.adminLabelIds.length || !isSame)
    },
    // 内容标题，封面图、标签、描述任何一个发生改变
    isOtherChanged() {
      return !!(this.isTitleChanged || this.isCoverImgChanged || this.isDescChanged)
    },
    // 内容标题、运营标题，封面图、标签、描述任何一个发生改变
    isInfoSafeChange() {
      return !!(this.isTitleChanged || this.isStatementTitleChanged || this.isCoverImgChanged || this.isDescChanged)
    },
    approveUrl() {
      if (this.form.photo_storage_type === 'zhihui' || this.form.photo_storage_type === 'other') {
        return this.form.photo_id
      }
      return `${envName.contentcenter}content-center/api/v1/content/imgage/${this.form.photo_url}/preview`
    },
    showParentId() {
      return ![null, undefined, ''].includes(this.form.course_statement.parent_content_act_type) || this.form.course_statement.parent_content_id
    },
    // PGC创作组织
    pgcCreationOrgRules() {
      return { required: this.form.course_statement.creation_source === 0, trigger: 'blur', validator: this.validPgcCreationOrg }
    },
    // PUGC创作组织
    pugcCreationOrgRules() {
      return { required: this.form.course_statement.creation_source === 3, trigger: 'blur', validator: this.validPugcCreationOrg }
    },
    supplierNameRules() {
      return { required: this.form.course_statement.creation_source === 1, message: '请输入供应商名称', trigger: 'blur' }
    },
    purchaseOrgRules() {
      return { required: this.form.course_statement.creation_source === 1, message: '请选择采购组织', trigger: 'blur' }
    },
    // 角色权限
    levelList() {
      let { supper_admin, mooc_bgadmin, mooc_company_admin, mooc_dept_admin } = this.userLimitInfo
      const id = this.$route.query.net_course_id
      if (!id) { // 新建默认选中无
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.form.course_level = 0
      }
      // 根据身份禁用相应radio
      if (supper_admin || mooc_company_admin) { // 公司
        this.courseLevelList.forEach((e) => e.disabled = false)
      } else if (mooc_bgadmin) { // bg
        this.courseLevelList.forEach((e) => e.disabled = e.code === 1)
      } else if (mooc_dept_admin) { // 部门
        this.courseLevelList.forEach((e) => e.disabled = e.code !== 3 && e.code !== 0)
      }
      return this.courseLevelList
    },
    // 是否是公司管理员或者超级管理员
    isSuperOrCompanyAdmin() {
      let { supper_admin, mooc_company_admin } = this.userLimitInfo
      return supper_admin || mooc_company_admin
    },
    // 是否是 部门级、BG级、公司级管理员、超管
    isBmBgComSuper() {
      let { mooc_dept_admin, mooc_bgadmin, mooc_company_admin, supper_admin } = this.userLimitInfo
      return mooc_dept_admin || mooc_bgadmin || mooc_company_admin || supper_admin
    },
    // 是否是超管
    isSuperAdmin() {
      let { supper_admin } = this.userLimitInfo
      return supper_admin
    },
    // 运营分级
    operation_type_options() {
      let list = [
        { label: '非体系', value: 3, pid: 528 },
        { label: '基础', value: 2, pid: 525 },
        { label: '中坚', value: 1, pid: 526 },
        { label: '头部', value: 0, pid: 527 }
      ]
      if (process.env.NODE_ENV !== 'production') {
        list = [
          { label: '非体系', value: 3, pid: 526 },
          { label: '基础', value: 2, pid: 569 },
          { label: '中坚', value: 1, pid: 571 },
          { label: '头部', value: 0, pid: 573 }
        ]
      }
      return list
    },
    // 管理员打标ID集合
    adminLabelIds() {
      let labels = (this.courseInfo.course_labels || []).filter(item => item.label_type_association === 1)
      return labels.map(item => item.label_id) || []
    },
    // 新建
    isAdd() {
      return !this.$route.query.net_course_id
    },
    // 编辑时标签提交的字段
    courseLabels() {
      return [...this.course_labels_map.admin, ...this.course_labels_map.robot, ...this.course_labels_map.user]
    },
    // "创作来源、创作组织、联合创作组织、管理组织、采购组织、是否加入推荐池、是否必修"特殊字段禁用，发布后，只有超管才能编辑
    specialFileDisabled() {
      let status = this.courseInfo.status + ''
      return status === '1' && !this.isSuperAdmin
    },
    // 创作来源是否修改
    isCreationSourceChanged() {
      let creation_source = this.courseInfo.course_statement?.creation_source || 0
      return this.form.course_statement.creation_source !== creation_source
    },
    // PGC创作组织是否修改
    isPgcCreationOrgChanged() {
      try {
        let pgc_creation_org = JSON.parse(this.courseInfo.course_statement?.pgc_creation_org || '[]')
        return !this.areArraysEqual(pgc_creation_org, this.form.course_statement.pgc_creation_org, 'UnitID')
      } catch (error) {
        return false
      }
    },
    // PUGC创作组织是否修改
    isPugcCreationOrgChanged() {
      try {
        let pugc_creation_org = JSON.parse(this.courseInfo.course_statement?.pugc_creation_org || '[]')
        return !this.areArraysEqual(pugc_creation_org, this.form.course_statement.pugc_creation_org, 'UnitID')
      } catch (error) {
        return false
      }
    },
    // 联合创作组织是否修改
    isJointCreationOrgChanged() {
      try {
        let pugc_joint_creation = JSON.parse(this.courseInfo.course_statement?.pugc_joint_creation || '[]')
        return !this.areArraysEqual(pugc_joint_creation, this.form.course_statement.pugc_joint_creation, 'UnitID')
      } catch (error) {
        return false
      }
    },
    // 管理组织是否修改
    isDeptIdChanged() {
      return this.form.dept_id !== this.courseInfo.dept_id
    },
    // 采购组织是否修改
    isPurchaseOrgChanged() {
      try {
        let ogc_purchase_org = JSON.parse(this.courseInfo.course_statement?.ogc_purchase_org || '[]')
        return !this.areArraysEqual(ogc_purchase_org, this.form.course_statement.ogc_purchase_org, 'UnitID')
      } catch (error) {
        return false
      }
    },
    // 是否加入推荐池是否修改
    isJoinRecommendChanged() {
      let join_recommend = this.courseInfo.course_statement?.join_recommend || false
      return !!(this.form.course_statement?.join_recommend !== join_recommend)
    },
    // 是否必修是否修改
    isIsRequiredChanged() {
      let is_required = this.courseInfo.course_statement?.is_required || false
      return this.form.course_statement.is_required !== is_required
    },
    // 是否有特殊字段发生改变
    isSpecialFileChanged() {
      return this.isCreationSourceChanged || this.isPgcCreationOrgChanged || this.isPugcCreationOrgChanged || this.isJointCreationOrgChanged || this.isDeptIdChanged || this.isPurchaseOrgChanged || this.isJoinRecommendChanged || this.isIsRequiredChanged
    },
    // 是否需要弹出特殊字段的修改弹窗
    showSpecialFileDialog() {
      let status = this.courseInfo.status + ''
      return status === '1' && this.isSuperAdmin && this.isSpecialFileChanged
    }
  },
  mounted() {
    // this.getClassifyData()
    // this.handlerLabelTagLimit()
    // 关闭页面给提示
    window.addEventListener('beforeunload', e => this.beforeunloadHandler(e))
    // this.addClassTincy()
    if (!this.$route.query.net_course_id) {
      this.getLevelObjectOption()
    }
  },
  beforeDestroy() {
    // 移除监听
    window.removeEventListener('beforeunload', this.beforeunloadHandler)
  },
  methods: {
    // 判断两个对象数组是否相等，id相等极为相等，不考虑数组顺序问题
    areArraysEqual(arr1, arr2, key) {
      // 提取两个数组的id
      let ids1 = arr1.map(item => item[key])
      let ids2 = arr2.map(item => item[key])
      // 对id数组进行排序
      ids1.sort()
      ids2.sort()
      // 比较两个排序后的id数组是否相等
      return JSON.stringify(ids1) === JSON.stringify(ids2)
    },
    handleClose() {
      this.showDialog = false
      this.updateText = ''
    },
    handleOk() {
      if (!this.updateText.trim()) {
        return this.$message.warning('请输入修改内容！')
      }
      this.bakArg.pageData.admin_edit_content = this.updateText
      this.$emit('vaildAfterCode', this.bakArg)
      this.showDialog = false
      this.updateText = ''
    },
    // 特殊字段修改后弹窗提示
    showSpecialDialogFn(pageData, value) {
      this.bakArg = { pageData, value }
      this.bakArg.pageData.admin_edit_content = ''
      if (this.showSpecialFileDialog) {
        this.showDialog = true
      } else {
        this.$emit('vaildAfterCode', this.bakArg)
      }
    },
    // 查看填写说明
    toInstructionsPage() {
      window.open('https://iwiki.woa.com/p/4012202789')
    },
    // PGC校验创作组织 只能单选，但是要兼容旧数据回显
    validPgcCreationOrg(rule, value, callback) {
      if (!this.form.course_statement.pgc_creation_org.length) {
        return callback(new Error('请选择创作组织'))
      } else if (this.form.course_statement.pgc_creation_org.length > 1) {
        return callback(new Error('创作组织仅能选择一个组织'))
      } else {
        callback()
      }
    },
    // PUGC校验创作组织 只能单选，但是要兼容旧数据回显
    validPugcCreationOrg(rule, value, callback) {
      if (!this.form.course_statement.pugc_creation_org.length) {
        return callback(new Error('请选择创作组织'))
      } else if (this.form.course_statement.pugc_creation_org.length > 1) {
        return callback(new Error('创作组织仅能选择一个组织'))
      } else {
        callback()
      }
    },
    // 编辑时处理标签回显数据
    resulveLabelData(val) {
      if (!this.form?.course_labels.length && val.course_labels && val.course_labels.length) {
        this.course_labels_map.admin = val.course_labels.filter(item => item.label_type_association === 1)
        this.course_labels_map.robot = val.course_labels.filter(item => item.label_type_association === 2)
        this.course_labels_map.user = val.course_labels.filter(item => item.label_type_association === 3)
      }
    },
    // 运营分级发生改变
    changeOperationType(item) {
      this.form.course_statement.operation_project_name = ''
      this.getOperationInfo(item)
    },
    // 获取分级项目
    async getOperationInfo(item) {
      if (!this.operation_project_name_options_map[item.value]) {
        getOperationApi(item.pid).then(res => {
          this.operation_project_name_options_map[item.value] = res || []
          this.operation_project_name_options = res || []
        }).catch(err => {
          console.log('err: ', err)
        })
      } else {
        this.operation_project_name_options = this.operation_project_name_options_map[item.value]
      }
    },
    getLevelObjectOption() {
      // if (!['', null, undefined, 3, '3'].includes(this.form.course_statement.operation_level)) {
      let obj = this.operation_type_options.find(item => item.value === this.form.course_statement.operation_level)
      if (obj) {
        this.getOperationInfo(obj)
      }
      // }
    },
    resolveScore(val) {
      return val ? val.toFixed(2) : 0
    },
    // 切换创作人类型
    // changeUgcCreatorType(value) {
    //   if (value === 1) {
    //     this.form.course_statement.ugc_creator_staff_input = ''
    //     this.$refs['form'].clearValidate(['ugc_creator_staff_input'])
    //   } else {
    //     this.form.course_statement.ugc_creator_staff = []
    //     this.$refs['form'].clearValidate(['ugc_creator_staff'])
    //   }
    // },
    // 手动检验字段
    validateField(value, file) {
      if (file !== 'pgc_joint_creation' || file !== 'pugc_joint_creation') {
        this.$refs['form'].validateField(`course_statement.${file}`)
      }
      if (['pgc_creation_org', 'pgc_joint_creation', 'pugc_creation_org', 'pugc_joint_creation', 'ogc_purchase_org'].includes(file)) {
        this.form.course_statement[file] = Array.isArray(value) ? value : [value]
      }
      // else if (file === 'ugc_creator_type' && this.form.course_statement.ugc_creator_type === 1) {
      //   this.form.course_statement['ugc_creator_staff'] = value
      // }
    },
    // 切换"创作来源时"，清除所属分类下的表单校验 编辑时逻辑需要完善
    creationSourceChange(value) {
      switch (value) {
        case 0:
          this.initPUGC()
          this.initUGC()
          this.initOGC()
          break
        case 3:
          this.initPGC()
          this.initUGC()
          this.initOGC()
          break
        case 2:
          this.initPGC()
          this.initPUGC()
          this.initOGC()
          break
        case 1:
          this.initPGC()
          this.initPUGC()
          this.initUGC()
          break
        default:
          break
      }
      this.$refs['form'].clearValidate(['course_statement.pgc_creation_org', 'course_statement.pugc_creation_org', 'course_statement.ugc_is_original', 'course_statement.ugc_creator_type', 'course_statement.ugc_creator_staff', 'course_statement.ugc_creator_staff_input', 'course_statement.ogc_supplier_name', 'course_statement.ogc_purchase_org', 'course_statement.ogc_purchase_type', 'course_statement.ogc_purchase_amount'])
    },
    // 重置PGC
    initPGC() {
      this.form.course_statement.pgc_creation_org = []
      this.form.course_statement.pgc_joint_creation = []
    },
    // 重置PUGC
    initPUGC() {
      this.form.course_statement.pugc_creation_org = []
      this.form.course_statement.pugc_joint_creation = []
    },
    // 重置UGC
    initUGC() {
      this.form.course_statement.ugc_is_original = null
      this.form.course_statement.ugc_link_url = ''
      this.form.course_statement.ugc_creator_type = null
      this.form.course_statement.ugc_creator_staff = []
      this.form.course_statement.ugc_creator_staff_input = ''
    },
    // 重置OGC
    initOGC() {
      this.form.course_statement.ogc_supplier_name = ''
      this.form.course_statement.ogc_purchase_org = []
      this.form.course_statement.ogc_purchase_type = null
      this.form.course_statement.ogc_out_teachers = ''
      this.form.course_statement.ogc_purchase_amount = undefined
    },
    toGetContentId() {
      window.open('https://iwiki.woa.com/p/4009876544')
    },
    // 校验表单，成功则返回请求入参
    vaildCourseInfo() {
      let params = null
      // 简介--需要校验
      if (this.getTincyWordCount) {
        this.form.course_desc = this.$refs['editor'].getContent()
      }
      if (this.showParentId) {
        if ([null, undefined, ''].includes(this.form.course_statement.parent_content_act_type)) {
          this.$message.warning('请选择内容类型')
          return
        }
        if (!this.form.course_statement.parent_content_id) {
          this.$message.warning('请输入内容ID')
          return
        }
      }
      this.$refs['form'].validate((valid) => {
        if (valid) {
          params = this.getParams()
        }
      })
      return params
    },
    // 将表格数据转换为请求入参
    getParams() {
      // 简介---不需要校验
      if (this.getTincyWordCount) {
        this.form.course_desc = this.$refs['editor'].getContent()
      }
      let {
        course_name,
        est_dur,
        dept_id,
        dept_name,
        status,
        course_admins,
        inner_teacher_names,
        caption_id,
        caption_name,
        caption_size,
        course_url,
        photo_url,
        photo_id,
        photo_storage_type,
        show_caption,
        is_original,
        // link_url,
        course_labels,
        course_classifies,
        limit,
        target_list,
        cl_ids,
        course_desc,
        course_statement,
        allow_vpn,
        support_mobile,
        is_show_recommend,
        remark,
        course_level,
        ai_sync_flag, 
        ai_expire_type,
        ai_expire_end_time
      } = this.form

      if (ai_sync_flag === 0 || ai_expire_type === 1) {
        ai_expire_end_time = ''
      }
      // 外部讲师
      const out_teacher_names =
        this.form.out_teacher_names && this.form.out_teacher_names.split(';')
      const outTeacher = (out_teacher_names || []).map((item) => {
        return {
          teacher_id: null,
          teacher_name: item
        }
      })

      // 目标学员
      const targetList = limit === 0 ? allTarget : limit === 2 ? studentTarget : target_list
      const captions = [{
        content_id: caption_id,
        file_name: caption_name,
        size: caption_size
      }]

      const params = {
        content_type: 'UGC',
        course_name,
        est_dur,
        dept_id,
        dept_name,
        status,
        course_admins,
        inner_teacher_names,
        out_teacher_names: outTeacher,
        target_list: targetList,
        course_desc: (this.$route.query.net_course_id && course_desc === this.course_desc_bak) ? this.courseInfo.course_desc : course_desc, // 兼容v8数据 编辑时，现在的描述和组件初始化时的描述对比是否一样 一样的话说明没改，提交后端返回的描述数据，不一样的话就是改了，提交现在最新的描述数据
        course_url,
        photo_url,
        photo_id,
        photo_storage_type,
        show_caption,
        is_original,
        course_labels: this.isAdd ? course_labels : this.courseLabels, // 新建时不用拆分显示标签用course_labels就行，编辑时由于拆分显示了标签，所以提交时需要提交"修改并合并成一个数组"的courseLabels字段
        course_classifies,
        extend_list: this.tableData,
        cl_ids: cl_ids.join(),
        captions: caption_id ? captions : null,
        allow_vpn: allow_vpn.join(';'),
        support_mobile,
        is_show_recommend: is_show_recommend ? 1 : 0,
        remark,
        course_level,
        course_statement: JSON.parse(JSON.stringify(course_statement)),
        ai_sync_flag, 
        ai_expire_type,
        ai_expire_end_time
      }

      const content_type_map = {
        0: 'PGC',
        1: 'OGC',
        2: 'UGC',
        3: 'PUGC'
      }
      params.content_type = content_type_map[params.course_statement.creation_source]

      // 处理新增字段 需要JSON.stringify处理的数据 回显时用
      const resolveArray = ['pgc_creation_org', 'pgc_joint_creation', 'pugc_creation_org', 'pugc_joint_creation', 'ogc_purchase_org']

      // UGC-创作人 (现已废弃)
      if (params.course_statement['ugc_creator_type'] === 1) { // 内部员工
        resolveArray.push('ugc_creator_staff')
      } else { // 外部员工
        let strArray = params.course_statement['ugc_creator_staff_input'] ? params.course_statement['ugc_creator_staff_input'].split(';') : []
        strArray = strArray.map(item => {
          return { StaffName: item }
        })
        params.course_statement['ugc_creator_staff'] = JSON.stringify(strArray)
        delete params.course_statement.ugc_creator_staff_input
      }
      // OGC-外部讲师
      if (params.course_statement['ogc_out_teachers']) {
        let strArray = params.course_statement['ogc_out_teachers'].split(';') ? params.course_statement['ogc_out_teachers'].split(';') : []
        strArray = strArray.map(item => {
          return { StaffName: item }
        })
        params.course_statement['ogc_out_teachers'] = JSON.stringify(strArray)
      } else {
        params.course_statement['ogc_out_teachers'] = JSON.stringify([])
      }
      // 各成员字段
      resolveArray.forEach(item => {
        if (params.course_statement[item] === null) {
          params.course_statement[item] = JSON.stringify([])
        } else {
          params.course_statement[item] = JSON.stringify(params.course_statement[item])
        }
      })
      // 人力成本
      if ([undefined, '', 0].includes(params.course_statement.human_cost)) {
        params.course_statement.human_cost = null
      }
      // OGC-采购成本
      if ([undefined, '', 0].includes(params.course_statement.ogc_purchase_amount)) {
        params.course_statement.ogc_purchase_amount = null
      }
      // 内容的专家评分
      params.course_statement.expert_score === '' && (params.course_statement.expert_score = null)
      if (typeof params.course_statement.expert_score === 'string') {
        params.course_statement.expert_score = +params.course_statement.expert_score
      }

      // 认证等级处理
      params.course_statement.certification_level = params.course_level

      // 是否原创 转载时赋值给旧的字段
      params.is_original = params.course_statement.ugc_is_original ? 1 : 0
      params.link_url = params.course_statement.ugc_link_url

      // 是否原创 转载时的地址处理（原来的逻辑）
      // if (is_original === 0) {
      //   params.link_url = link_url
      // }

      return params
    },
    // 课单数量
    getCourseCount() {
      const net_course_id = this.$route.query.net_course_id
      const params = {
        module_id: 1,
        item_id: net_course_id
      }
      getContentAddedCount(params).then((res) => {
        this.courseCount = res
      })
    },
    // 延伸学习列表
    getExtandList() {
      const id = this.$route.query.net_course_id
      const params = {
        act_id: id,
        act_type: 2
      }
      getExtanContentList(params).then((data) => {
        this.tableData = data
      })
    },
    // 添加到课单
    addEditCourse() {
      const id = this.$route.query.net_course_id
      if (id) {
        this.addCourseDialogShow = true
        return
      }
      this.addCourseDialogShowSelf = true
    },
    getCheckedClIds(ids) {
      this.form.cl_ids = ids
      this.courseCount = ids.length
    },
    // 延伸学习新增内容
    closeAddExDialog(list) {
      this.showAddExtandLearn = false
      if (list?.length) {
        this.tableData = this.tableData.concat(list)
      }
    },
    handleImgEdit() {
      this.$refs.sdcImgCoverRef.outEdit({
        id: this.coverImgId,
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        type: 'outEdit'
      })
    },
    handleClearImg() {
      this.form.photo_id = ''
      this.form.photo_url = ''
    },
    handleSuccessImage(val, file, id) {
      this.dialogImageUrl = id
      this.form.photo_url = val
      this.form.photo_id = id
      // 清空一键生成封面
      this.autoImgUrl = ''
      this.form.photo_storage_type = 'contentcenter'
      if (this.dialogImageUrl) {
        this.$refs.form.clearValidate('photo_url')
      }
    },
    handleAutoImg() {
      this.autoImgCoverShow = true
      this.imgInfo = {
        title: this.form.course_name,
        env: process.env.NODE_ENV === 'production' ? 'production' : 'test',
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        showEdit: true
      }
    },
    // 一键封面回传
    handleImgCoverOk(row) {
      console.log(row, 'row')
      this.autoImgUrl = row.url
      // 清空裁剪封面
      this.dialogImageUrl = ''
      this.form.photo_url = row.url
      this.form.photo_id = row.id
      this.form.photo_storage_type = 'zhihui'
      this.coverImgId = row.id
      if (this.autoImgUrl) {
        this.$refs.form.clearValidate('photo_url')
      }
    },
    handleUp(row, index) {
      this.tableData.unshift(this.tableData.splice(index, 1)[0])
    },
    handleDelete(row, index) {
      this.tableData.splice(index, 1)
    },
    // 外部员工
    handleOutChecked(val) {
      if (!val) {
        this.form.out_teacher_names = ''
        this.$refs.form.clearValidate('out_teacher_names')
        this.form.innerChecked = true
      }
    },
    // 内部员工
    handleInnerChecked(val) {
      if (!val) {
        this.form.inner_teacher_names = []
        this.$refs.innerSelectorRef.setSelected([])
        this.$refs.form.clearValidate('inner_teacher_names')
        this.form.outChecked = true
      }
    },
    // 内部员工
    changeInnerAuth(val) {
      if (val.length > 10) {
        this.$message.warning('最多添加10人')
        this.$refs.innerSelectorRef.setSelected(val.slice(0, 10))
      }
      const arr = val.length > 10 ? val.slice(0, 10) : val
      this.form.inner_teacher_names = arr
      if (this.form.inner_teacher_names.length > 0) {
        this.$refs.form.clearValidate('inner_teacher_names')
      }
    },
    // 管理员
    changeCourseAuth(val) {
      if (val.length > 10) {
        this.$message.warning('最多添加10人')
        this.$refs.deptSelectorRef.setSelected(val.slice(0, 10))
      }
      const arr = val.length > 10 ? val.slice(0, 10) : val
      this.form.course_admins = arr
      if (this.form.course_admins.length > 0) {
        this.$refs.form.clearValidate('course_admins')
      }
    },
    // 同意协议勾选
    handleCheck(val) {
      if (val) {
        if (this.form.is_original === 0) {
          this.$refs.form.clearValidate('link_url')
          // 第一次进页面
          localStorage.setItem('tencent_qlearnging_netcource_ai_origin', true) // 原创
        } else {
          localStorage.setItem('tencent_qlearnging_netcource_ai_forward', true) // 转载
        }
      }
    },
    // 是否原创
    handleOrigin(val) {
      const originFlag = localStorage.getItem('tencent_qlearnging_netcource_ai_origin')
      const forwardFlag = localStorage.getItem('tencent_qlearnging_netcource_ai_forward')
      if ((val === 0 && !originFlag) || (val === 1 && !forwardFlag)) {
        this.copyrightShow = true
      }
    },
    // 部分学员
    handleOpenPart(val) {
      if ([0, 2].includes(val)) {
        this.$refs.form.clearValidate('limit')
      }
      if (val === 1) {
        this.form.target_list = ''
      }
    },
    // 所属组织单元
    handleUnitSelect(val) {
      this.form.dept_id = val.dept_id
      this.form.dept_name = val.UnitFullName
      if (this.form.dept_id) {
        this.$refs.form.clearValidate('dept_id')
      }
    },
    // 字幕上传
    onUpload(options) {
      this.$refs.subttileUpload.onUpload(options)
    },
    // 关闭字幕
    handleOpenSubttile(val) {
      if (val === 0) {
        this.subttileSetInfo = {
          caption_name: '',
          caption_id: '',
          caption_size: '',
          fileList: []
        }
        this.form.caption_id = ''
        this.form.caption_name = ''
        this.form.caption_size = ''
      }
    },
    // 字幕上传回传数据
    confirmOnUpload(data) {
      if (data?.caption_id) {
        this.form.caption_id = data.caption_id
        this.form.caption_name = data.caption_name
        this.form.caption_size = data.caption_size
      }
      this.subttileSetInfo = JSON.parse(JSON.stringify(data))
    },
    // 重新上传
    againUpload() {
      document.getElementById('srt-upload-btn').click()
    },
    beforeunloadHandler(e) {
      let _this = this
      const routeList = ['pptCourse', '2DCourse', 'couserUpload', 'aiCoursePublish']
      if (routeList.includes(_this.$route.name)) {
        e = e || window.event
        // 兼容IE8和Firefox 4之前的版本
        if (e) {
          e.returnValue = '关闭提示'
        }
        // Chrome, Safari, Firefox 4+, Opera 12+ , IE 9+
        return '关闭提示'
      } else {
        window.onbeforeunload = null
      }
    },
    getModuleClass(module_id) {
      let cardType = qlearningModuleTypes.find((item) => module_id === item.module_id)
      if (cardType) {
        return cardType.moduleClassName
      }
    },
    // 获取焦点
    handeEditFocus() {
      this.$refs['editor'].editor.editorManager.get('course_desc').focus()
    },
    getWordCount(val) {
      // 防止只上传图片
      const textVal = this.$refs['editor'].getContent()
      let imgVal = null
      textVal.replace(/<img.*?(?:>|\/>)/gi, (match, capture) => {
        imgVal = capture
      })
      // 第一次进来备份组件初始化处理过的描述信息
      if (!this.course_desc_bak && textVal !== `<p><br data-mce-bogus="1"></p>`) {
        this.course_desc_bak = textVal
      }
      this.getTincyWordCount = val || imgVal
      if (this.getTincyWordCount) {
        this.$refs.form.clearValidate('course_desc')
        this.course_desc_realtime = textVal
      } else {
        this.form.course_desc = ''
        this.course_desc_realtime = ''
      }
      // let textValue = this.$refs['editor'].getContent()
      // if (textValue !== this.courseInfo.course_desc) {
      this.$nextTick(() => {
        this.isDescChanged = !!(textVal !== this.course_desc_bak)
      })
    },
    getSelectedLabelList(val) {
      this.form.course_labels = val.map(item => {
        return {
          ...item,
          label_type_association: 1
        }
      })
      this.$nextTick(() => {
        this.$refs.form.validateField('course_labels')
      })
    },
    // 处理管理员打的标签
    updateAdminLabel(list) {
      this.course_labels_map.admin = list.map(item => {
        return {
          ...item,
          label_type_association: 1
        }
      })
      // 机器打的标签 重复的需要删除 保留管理员打标
      let robotLabel = this.course_labels_map.robot
      for (let i = robotLabel.length - 1; i >= 0; i--) {
        if (list.some(item => item.label_id === robotLabel[i].label_id)) {
          this.course_labels_map.robot.splice(i, 1)
        }
      }
      // 用户打的标签 重复的需要删除 保留管理员打标
      let userLabel = this.course_labels_map.user
      for (let i = userLabel.length - 1; i >= 0; i--) {
        if (list.some(item => item.label_id === userLabel[i].label_id)) {
          this.course_labels_map.user.splice(i, 1)
        }
      }
      this.$nextTick(() => {
        this.$refs.form.validateField('course_labels_admin')
      })
    },
    // 删除机器标
    delRobotLabel(item) {
      let index = this.course_labels_map.robot.findIndex(subItem => subItem.label_id === item.label_id)
      this.course_labels_map.robot.splice(index, 1)
    },
    handleValidor(value, num, type) {
      if (value) {
        const china = value.match(/[\u4e00-\u9fa5]/g)
        const zhCount = china && china.join('').length
        const enCount = Math.ceil((value.length - zhCount) / 2)
        const total = zhCount + enCount
        if (total > num) {
          switch (type) {
            case '1':
              this.form.course_name = value.slice(0, -1)
              break
            case '2':
              this.form.course_statement.operation_title = value.slice(0, -1)
              break
            case '3':
              this.form.course_statement.ogc_supplier_name = value.slice(0, -1)
              break
            default:
              break
          }
        }
        return total || 0
      }
      return 0
    }
  }
}
</script>
<style lang="less">
.image-text-page .image-text-form {
  .sdc-selector .selector-container .container-inner .tags {
      height: 38px;
    }
}
.course-tincy {
  .tox-collection__item:nth-child(4) {
    display: none;
  }

  .tox-collection__item:nth-child(5) {
    display: none;
  }
}
.ogc_purchase_amount.el-input-number,.human_cost.el-input-number {
    line-height: 32px;
    .el-input-number__decrease, .el-input-number__increase {
      width: 40px;
      height: 30px;
      line-height: 30px;
    }
  }
.creation_source_sub_content {
  .sdc-selector .selector-container--normal {
    height: 100%;
    padding-top: 1px;
    padding-left: 0;
  }
  .suffix-open {
    height: 100%;
    .el-button--default {
      height: 100%;
    }
  }
}
.ugc-sub-item {
  .el-form-item__label {
    text-align: left;
  }
  .ugc_is_original {
    display: flex;
    align-items: center;
    .el-form-item__content {
      display: flex;
      align-items: center;
      margin-left: 0 !important;
      .el-radio-group {
        flex-shrink: 0;
      }
    }
    .el-input {
      width: 500px;
    }
  }
}
</style>
<style lang="less" scoped>
.update-dialog {
  .update-content-box {
    position: relative;
    display: flex;
    align-items: center;
    &::after {
      position: absolute;
      left: 75px;
      bottom: -18px;
      content: '（注：修改内容将通过企微机器人同步提醒给课程管理员，请留意。）';
      color: #d63535;
      font-size: 12px;
    }
  }
  .dialog-label {
    flex-shrink: 0;
    margin-right: 20px;
  }
  .update-text-input {}
}
.tag-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  // margin: 3px 0 0 0;
  // width: 700px;
  .custom-tag {
    cursor: pointer;
    margin: 6px 6px 6px 0;
    background: #f7f8fa;
    min-width: 69px;
    margin-right: 15px;
    border-radius: 0 15px 15px 0;
    color: rgba(0,0,0,.6);
    font-family: PingFang SC;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    height: 24px;
    line-height: 22px;
    border: 1px solid #dcdcdc;
    padding: 0 10px;
    box-sizing: border-box;
    white-space: nowrap;
  }
  .click-tag {
    border-color: #0052d9;
    border: 1px solid #0052d9!important;
    background: #f2f3ff!important;
    color: #0052d9!important;
  }
  .robat-tags /deep/.el-icon-close:hover {
    color: #3464e0;
    background-color: #f2f3ff;
  }
}
.mgl-6 {
  margin-left: 6px;
}

.mgr-5 {
  margin-right: 5px;
}

.image-text-page {
  .image-text-form {
    .custom-el-input-count {
      color: #ACACAC;
      background: #FFF;
      position: absolute;
      font-size: 12px;
      bottom: 6px;
      right: 6px;
      line-height: 20px;
    }
    .async-icon {
      color:#E34D59;
      margin-left: 6px;
    }
  }

  .red-tips {
    color: #d63535;
    font-size: 12px;
  }

  .test-tips {
    color: #9d9d9d;
    font-size: 12px;
    .color-red {
      color: #d63535;
    }
    .text-undeline {
      text-decoration: underline;
      cursor: pointer;
    }
  }

  .red-tips-customer {
    color: #d63535;
    font-size: 12px;
    height: 12px;
    line-height: 12px;
  }

  .none-upload-btn {
    margin-bottom: 12px;
  }

  .tincy-form {
    position: relative;

    // :deep(.disabledTincy) {
    //   position: relative;
    //   left: 0;
    //   top: 0;
    //   opacity: .5;
    //   width: 100%;
    //   height: 340px;
    //   background: #000;
    //   z-index: 99;
    //   pointer-events: none;
    // }
    .sdc-preview {
      max-height: 350px;
      overflow: auto;
      margin: 5px 0 0 0;
    }
    .ticy-tips-content {
      position: absolute;
      line-height: 32px;
      bottom: 100px;
      left: 20px;
      color: #0006;

      .tincy-indent {
        text-indent: 20px
      }
    }
  }
  .special-class-input,
  .tag-form-item {
    // margin-bottom: unset;
    :deep(.el-input) {
      min-height: 32px;
      height: unset;
    }
    :deep(.custom-height) {
      display: flex;
      align-items: center;
    }
    // :deep(.el-form-item__label) {
    //   margin-top: 6px;
    // }
  }
  .subttile-box {

    .upload-btn {
      width: 242px;
      height: 32px;
      border-radius: 3px;
      color: rgba(0, 0, 0, 0.9);
      border: 1px solid rgba(220, 220, 220, 1);
      margin-left: 16px;

      .el-icon-upload2 {
        margin-right: 10px;
      }
    }
  }

  .form-label-box {
    font-size: 16px;
    font-weight: bold;
    color: #000000;
    margin-bottom: 20px;

    span:last-of-type {
      color: rgba(0, 0, 0, 0.4);
      font-size: 14px;
      margin-left: 16px;
    }
  }
  .parent-content-id {
    display: flex;
    .content-type {
      position: relative;
      width: 195px;
    }
    .content-id {
      position: relative;
      margin-left: 20px;
      width: 195px;
    }
    .error-tip-text-customer {
      position: absolute;
      bottom: -25px;
      left: 0;
      width: 100%;
      color: red;
      font-size: 12px;
    }
    .get-content-id {
      margin-left: 20px;
      color: #0052D9;
      cursor: pointer;
      font-weight: bold;
    }
  }

  .original-form,
  .subttile-form {
    height: 32px;
    display: flex;

    .subttile-tips {
      color: rgba(0, 0, 0, 0.4);
      font-size: 12px;
      margin-left: 16px;
    }

    .el-radio-group {
      display: flex;
      align-items: center;
    }
  }

  .recommend-label-box {
    margin-top: 8px;
    color: #666666;

    ul {
      display: flex;
      align-items: center;
      line-height: 20px;

      li {
        height: 20px;
        line-height: 20px;
        background-color: #F5F5F5;
        padding: 0 4px;
        border-radius: 2px;
        font-size: 12px;
        cursor: pointer;
      }

      li+li {
        margin-left: 8px;
      }

      .activeTag {
        color: #0052D9;
        background-color: #EBEFFC;
      }

      .disabledTag {
        cursor: not-allowed;
        pointer-events: none;
      }
    }
  }

  .inside-person,
  .open-person,
  .target-person {
    display: flex;

    .el-checkbox {
      margin-right: 16px;
    }
  }

  .inside-person {
    // margin-bottom: 20px;

    :deep(.sdc-selector) {
      width: 100%;
    }
  }

  .target-person {
    .open-target-radio {
      margin-right: 13px;
    }
  }

  // .open-basics {
  //   position: relative;
  //   padding-top: 32px;
  //   padding-bottom: 32px;

  //   .line {
  //     width: 100%;
  //     height: 1px;
  //     background-color: #EEEEEE;
  //   }

  //   .open-btn {
  //     position: absolute;
  //     font-size: 12px;
  //     color: #999999;
  //     display: flex;
  //     align-items: center;
  //     justify-content: center;
  //     top: 24px;
  //     left: 50%;
  //     transform: translateX(-50%);
  //     background-color: #fff;
  //     width: 120px;
  //     cursor: pointer;
  //   }

  //   .btn-basisc-icon {
  //     display: inline-block;
  //     width: 16px;
  //     height: 16px;
  //     background: url('~@/assets/img/open-basics.png') no-repeat center/cover;
  //     margin-left: 8px;
  //     transition: transform .3s;
  //   }

  //   .basisc-active-icon {
  //     transform: rotate(180deg);
  //     transition: transform .3s;
  //   }
  // }

  // .close-basics {
  //   padding-top: 32px;
  //   padding-bottom: unset;
  // }

  // .manage-person {
  //   :deep(.sdc-selector) {
  //     width: 100%;
  //   }
  // }

  .time-input {
    .el-input-number {
      width: 100%;

      :deep(.el-input-number__increase),
      :deep(.el-input-number__decrease) {
        line-height: 15px;
        width: 35px
      }

      :deep(.el-input-number__decrease) {
        bottom: -2px;
      }

      :deep(.el-input-number__increase) {
        top: 4px;
      }

      :deep(.el-input__inner) {
        padding-left: 8px;
        padding-right: 40px;
        text-align: left;
      }
    }
    
    .time-warm {
      font-size: 12px;
      color: rgba(0, 0, 0, 0.4);
    }
    .time-label {
      color: #000000;
      font-size: 14px;
      margin-left: 8px;
    }
  }

  .creation_source_sub_content {
      background: #f9f9f9;
      border-radius: 4px;
      margin-top: 5px;
      padding: 8px 16px;
      width: 800px;
      // height: 100px;
      .el-form-item {
        margin-bottom: 20px;
      }
    }

    .contact-person {
      font-size: 12px;
      color: #999999;
      line-height: 18px;
      font-style: italic;
    }
    .contact-person-radio {
      margin: -8px 0 5px 0;
    }

    .origin-warm {
      margin: 0 0 10px;
      font-size: 12px;
      line-height: 14px;
      color: #999999;
    }

  // .course-texTarea-input {
  //   position: relative;
  //   :deep(.el-form-item__content) {
  //     width: 1089px;
  //   }
  //   :deep(.el-input) {
  //     .el-input__inner {
  //       padding-right: 70px;
  //     }
  //     .el-input__suffix {
  //       position: absolute;
  //       right: 43px;
  //     }
  //   }
  // }

  .add-course-tips {
    margin-bottom: 20px;
    margin-left: 28px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.8);

    .add-course-list {
      margin: 0 20px 0 12px;
      color: rgba(0, 0, 0, 0.9);
    }
  }

  .main-table {
    padding-left: 32px;
    display: grid;

    .table-tips {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 9px;

      .tips-label {
        font-size: 14px;
        padding-left: 10px;

        span:first-of-type {
          color: rgba(0, 0, 0, 0.8);
        }

        span:last-of-type {
          color: rgba(0, 0, 0, 0.4);
          margin-left: 12px;
          font-size: 12px;
        }
      }
    }

    .upload-table-box {
      padding-left: 78px;

    }

    .operation-box {
      display: flex;
      align-items: center;
      cursor: pointer;

      .operation-icon-up {
        background: url('~@/assets/img/icon-up.png') no-repeat center/cover;
        width: 16px;
        height: 16px;
        display: inline-block;
        margin-right: 8px;
      }
    }
  }
  .tip-warm {
    opacity: 0.4;
    margin-left: 16px;
    color: #000000;
    font-size: 14px;  
  }
  .expert-rating-input {
    width: 180px;
  }
}
.submit-must-tips {
  margin-bottom: 20px;
  display:flex;
  .submit-label {
    margin-left: 20px;
  }
  .submit-label::before {
    content: '*';
    color: red;
    margin-right: 2px;
  }
  .el-link {
    margin-left: 7px;
  }
  .tips {
    color: #0006;
    margin-top: 10px;
    margin-left: 12px;
    line-height: 24px
  }
  .red-tips {
    color: red
  }
}
</style>
