<template>
  <div class="bottom-icon-box" v-if="bottomShow">
    <div class="card">
      <div
      class="item-icon-content"
      @click="handleBtChoice(item)"
      v-for="(item, index) in bottomIconInfo"
      :key="index"
      :dt-remark="dtBottomTab('remark', item)"
      :dt-areaid="dtBottomTab('areaid', item)"
      :dt-eid="dtBottomTab('eid', item)"
      >
        <div :class="['icon', item.icon]"></div>
        <div class="label">{{ item.label }}</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    },
    scrollID: {
      type: String,
      default: 'scroll-main-content'
    },
    courseType: {
      type: String,
      default: 'net'
    }
  },
  data() {
    return {
      scrollEl: null,
      bottomShow: true,
      bottomIconInfo: [
        { icon: 'home', label: '首页' },
        { icon: 'comment', label: '评价' },
        { icon: 'add', label: '添加' },
        { icon: 'share', label: '分享' }
      ]
    }
  },
  computed: {
    course_id() {
      return this.courseData.course_id || this.$route.query.course_id || this.$route.query.activity_id
    },
    pageTypeName() {
      let obj = {
        'net': '网课',
        'face': '面授课',
        'activity': '活动'
      }
      return obj[this.courseType]
    },
    dtBottomTab() {
      return (type, row) => {
        const data = {
          page: this.courseData.course_name,
          page_type: this.pageTypeName + '详情页面-移动新版',
          container: '底部导航',
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: row.label,
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'H5'
        }
        if (type === 'remark') {
          return JSON.stringify(data)
        } else if (type === 'eid') {
          return `element_${this.course_id}_${row.label}`
        } else {
          return `area_${this.course_id}_${row.label}`
        }
      }
    }
  },
  watch: {
    '$route.query.from': {
      immediate: true,
      deep: true,
      handler(val) {
        console.log(val, '$route.query.from')
        if (val === 'mooc') {
          // 去掉分享
          this.bottomIconInfo = [
            { icon: 'home', label: '首页' },
            { icon: 'comment', label: '评价' },
            { icon: 'add', label: '添加' }
          ]
        }
      }
    }
  },
  mounted() {
    if (this.scrollID === 'window') {
      window.addEventListener('scroll', this.scrollEvent, true)
    } else {
      this.scrollEl = document.getElementById(this.scrollID)
      this.scrollEl.addEventListener('scroll', this.scrollEvent, true)
    }
  },
  beforeDestroy() {
    window.removeEventListener('beforeunload', this.beforeunloadEvent)
    window.removeEventListener('scroll', this.scrollEvent)
  },
  methods: {
    initShow() {
      this.bottomShow = true
    },
    scrollEvent(e) {
      // 获取当前滚动位置
      let scrollTop = 0
      this.scrollID === 'window' ? scrollTop = window.scrollTop || document.documentElement.scrollTop : scrollTop = e.target.scrollTop
      // 滚动高度 250是视频的高度
      if (scrollTop > 50) {
        // // 向下滚动，隐藏菜单栏
        this.bottomShow = false
      } else {
        // 向上滚动，显示菜单栏
        this.bottomShow = true
      }
    },
    // 底部按钮显示
    handleBtChoice(val) {
      this.$emit('handleBtChoice', val)
    }
  }
}
</script>
<style lang="less" scoped>
  .bottom-icon-box {
    position: fixed;
    bottom: 34px;
    width: 100%;
    padding: 0 20px;
    z-index: 999;
    .card {
      height: 64px;
      background: #fff;
      margin: 0 auto;
      border-radius: 8px;
      box-shadow: 0 0 15px -5px #00000033;
      padding: 8px 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      .item-icon-content {
        display: flex;
        flex-direction: column;
        .icon {
          width: 24px;
          height: 24px;
        }
        .home {
          background: url('~@/assets/img/mobile/home.png') no-repeat center / cover;
        }
        .comment {
          background: url('~@/assets/img/mobile/comment.png') no-repeat center / cover;
        }
        // .add {
        //   background: url('~@/assets/img/mobile/add-icon.png') no-repeat center / cover;
        // }
        .share {
          background: url('~@/assets/img/mobile/share-icon.png') no-repeat center / cover;
        }
        .add {
          background: url('~@/assets/img/mobile/cellect-active.png') no-repeat center / cover;
        }
        .label {
          font-size: 11px;
          line-height: 20px;
          color: #333333;
          margin-top: 4px;
        }
      }
    }
  }
</style>
