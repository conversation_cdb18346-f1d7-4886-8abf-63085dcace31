<template>
  <div class="outlink-dialog">
    <el-dialog
      width="800px"
      :visible="visible"
      :title="outLinkType === 'add' ? '添加外部链接': '编辑外部链接'"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <div class="content-body">
        <el-form ref="form" :model="form" label-width="100px" :rules="rules">
          <el-form-item class="input-style" label="任务名称" prop="task_name">
            <el-input v-model="form.task_name" placeholder="请输入任务名称" clearable></el-input>
            <span class="custom-el-input-count">{{handleValidor(form.task_name, 50, '1')}}/50</span>
          </el-form-item>
          <el-form-item class="coustom-form-url" label="跳转链接" prop="urlRules" :rules="urlRules()">
            <el-row class="flex-row">
              <el-tag size="small" class="tag">PC端</el-tag>
              <el-input v-model="form.resource_url" placeholder="请输入PC端跳转链接" clearable></el-input>
              <el-link class="test-link" type="primary" :underline="false" @click="urlTest">测试跳转</el-link>
            </el-row>
            <el-row class="flex-row">
              <el-tag class="tag">移动端</el-tag>
              <el-input v-model="form.resource_url_mobile" placeholder="请输入移动端跳转链接" clearable></el-input>
              <el-link class="test-link" type="primary" :underline="false" @click="redirectTo()">查看配置要求</el-link>
            </el-row>
          </el-form-item>
          <CustomTips
            title="系统仅支持https的链接，并确保填写的跳转链接能在对应的终端打开，链接为空时无法跳转" 
            IconName="el-icon-warning" 
            backgroundColor="#fdf6ec" 
            color="#FF7548"
            lineHeight="30px"
            class="outlink-custom-tips"
            >
          </CustomTips>
          <el-form-item label="时长/字数">
            <el-input-number v-model="isWordNum" controls-position="right" :min="1"></el-input-number>
            <el-select  v-model="numType" class="is-duration">
              <el-option label="分钟" :value="1"></el-option>
              <el-option label="字数" :value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="任务简介">
            <el-input v-model="form.task_desc" type="textarea" rows="4" placeholder="请输入任务简介"></el-input>
            <span class="custom-el-input-count">{{handleValidor(form.task_desc, 500)}}/500</span>
          </el-form-item>
          <el-form-item label="完成条件" prop="condition">
             <el-radio-group v-model="form.finished_condition.type">
              <el-radio label="1">由课程完成条件决定</el-radio>
              <el-radio label="2">至少学习</el-radio>
            </el-radio-group>
            <el-input-number :disabled="form.finished_condition.type !== '2'" v-model="form.finished_condition.condition" controls-position="right" :min="0" class="condition">
            </el-input-number>分钟
            <div v-if="form.finished_condition.type === '2' && isWordNum && form.finished_condition.condition > isWordNum" class="video-durtion-tips">{{`外链课程时长${isWordNum}分钟，请合理设置完成条件`}}</div>
          </el-form-item>
          <el-form-item label="任务性质">
            <el-radio-group v-model="form.required">
              <el-radio :label="true">应学</el-radio>
              <el-radio :label="false">选学</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="任务状态">
            <el-radio-group v-model="form.lock_status">
              <el-radio :label="1">解锁任务</el-radio>
              <el-radio :label="2">锁定任务</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- <el-form-item label="解锁时间">
            <el-date-picker
              v-model="form.unlock_time"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetime"
              placeholder="请选择任务解锁时间">
            </el-date-picker>
          </el-form-item> -->
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <convention-confirm v-model="isChooseConvention" style="margin-left: 20px;" />
        <el-button @click="cancel" size="small" style="margin-left: auto;">取 消</el-button>
        <el-button @click="submit" type="primary" size="small" :disabled="!isChooseConvention">{{ currentNode?.id ? '保 存' : '确 定' }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import CustomTips from '@/components/tips.vue'
import conventionConfirm from '@/views/components/convention-confirm.vue'
export default {
  components: {
    CustomTips,
    conventionConfirm
  },
  props: {
    outLinkType: {
      type: String,
      default: 'add'
    },
    visible: {
      type: Boolean,
      default: false
    },
    currentNode: {
      type: Object
    },
    treeNode: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      form: {
        task_name: '',
        resource_url: '',
        resource_url_mobile: '',
        task_desc: '',
        required: true,
        lock_status: 1,
        task_type: 'task',
        task_type_name: '任务',
        resource_type: 'Other',
        resource_type_name: '外链',
        word_number: '',
        duration: '',
        // condition: ''
        finished_condition: {
          type: '1',
          condition: null
        }
      },
      numType: 1,
      isWordNum: 0,
      rules: {
        task_name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }]
        // condition: [{ required: true, message: '请输入学习时间', trigger: 'blur' }]
      },
      isChooseConvention: false,
      setting: {
        act_type: '99',
        columns: [
          {
            column_code: 'task_name',
            column_name: '任务名称',
            column_type: 'text',
            call_type: ['sync'],
            manual_review: false
          },
          {
            column_code: 'task_desc',
            column_name: '任务简介',
            column_type: 'richText',
            call_type: ['sync'],
            manual_review: false
          }
        ]
      }
    }
  },
  computed: {
    ...mapState(['projectManageInfo'])
  },
  methods: {
    // 初始化数据
    initData(data) {
      const { word_number, duration, finished_condition = {} } = data
      this.form = {
        ...data,
        // condition: data.finished_condition.condition || ''
        finished_condition: {
          type: finished_condition?.type ? finished_condition.type : '1',
          condition: finished_condition?.condition ? finished_condition.condition : null
        }
      }
      if (word_number) {
        this.isWordNum = word_number
        this.numType = 0
      } else {
        this.isWordNum = duration
        this.numType = 1
      }
    },
    urlTest () {
      const reg = /(http|https):\/\/([\w.]+\/?)\S*/
      if (!this.form.resource_url) return this.$message.error('请输入链接')
      else if ((this.form.resource_url && !reg.test(this.form.resource_url))) return this.$message.error('原文链接请输入http://或https://开头的链接地址')
      window.open(this.form.resource_url)
    },
    redirectTo() {
      window.open('https://iwiki.woa.com/pages/viewpage.action?pageId=4008670334')
    },
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.numType === 1) { // 分钟
            this.form.duration = this.isWordNum
            this.form.word_number = 0 
          } else { // 字数
            this.form.duration = 0
            this.form.word_number = this.isWordNum
          }
          // this.form = {
          //   ...this.form,
          //   finished_condition: {
          //     type: 2,
          //     condition: this.form.condition
          //   }
          // }
          if (this.form.finished_condition.type === '1') {
            this.form.finished_condition.condition = null
          }
          // 接入人工审核
          window.$informationReview && window.$informationReview.contentReview(this.form, this.setting).then(res => {
            if (res.success && res.status_code * 1 === 1) return

            this.$emit('updateTreeList', [{
              ...this.form,
              id: this.currentNode?.id || '',
              module_id: 99,
              act_type: 99
            }], this.outLinkType) // edit, add
            this.$emit('update:visible', false)
          }).catch(err => {
            console.log(err, 'rrr')
          })
        } else {
          return false
        }
      })
    },
    cancel() {
      this.$refs['form'].resetFields()
      this.$emit('update:visible', false)
    },
    handleValidor(value, num, type) {
      if (!value) return 0
      const china = value.match(/[\u4e00-\u9fa5]/g)
      const zhCount = china && china.join('').length
      const enCount = Math.ceil((value.length - zhCount) / 2)
      const total = zhCount + enCount
      if (total > num) {
        if (type === '1') {
          this.form.task_name = value.slice(0, -1)
        } else {
          this.form.task_desc = value.slice(0, -1)
        }
      }
      return zhCount + enCount 
    },
    urlRules() {
      let validator = (rule, value, callback) => {
        const { resource_url, resource_url_mobile } = this.form
        const reg = /(https):\/\/([\w.]+\/?)\S*/
        if (!resource_url_mobile && !resource_url) {
          callback(new Error('请输入课程跳转链接'))
        } else {
          if ((resource_url && !reg.test(resource_url)) || (resource_url_mobile && !reg.test(resource_url_mobile))) {
            callback(new Error('格式错误，请输入https开头的链接地址'))
          } else if (reg.test(value)) {
            this.$refs['form'].clearValidate('resource_url')
            callback()
          }
          callback()
        } 
      }
      return {
        validator,
        trigger: ['blur'],
        required: true
      }
    }
  }
}
</script>
<style lang="less" scoped>
.outlink-dialog {
  .content-body{
    padding-right: 40px;
    .flex-row{
      display: flex;
      align-items: center;
      .tag{
        text-align: center;
        min-width: 60px;
        margin-right: 10px;
        height: 20px;
        line-height: 20px;
        color: #0052D9;
        background-color: #EAEFFC;
        border: unset;
      }
    }
    .input-style {
      position: relative;
      :deep(.el-input) {
        .el-input__inner {
          padding-right: 70px;
        }
        .el-input__suffix {
          position: absolute;
          right: 43px;
        }
      }
    }
    .custom-el-input-count {
      color: #ACACAC;
      background: #FFF;
      position: absolute;
      font-size: 12px;
      bottom: 6px;
      right: 6px;
      line-height: 20px;
    }
    .coustom-form-url {
      margin-bottom: 20px;
      :deep(.el-form-item__label::before) {
        content: '*';
        color: #F81D22;
        margin-right: 4px;
      }
      .text-blue {
        margin-left: 10px;
      }
    }
    .outlink-custom-tips {
      margin-left: 100px;
      margin-bottom: 20px;
    }
    .sp-bt{
      justify-content: space-between;
      margin-bottom: 0px !important;
    }
    .flex-row:first-child{
      margin-bottom: 20px;
    }
    .is-duration{
      width: 80px;
      margin-left: 24px;
    }
    .condition{
      width: 130px;
      margin: 0 10px 0 10px;
    }
    :deep(.is-controls-right){
      width: 130px;
      height: 32px !important;
      line-height: 32px;
      .el-input-number__decrease,.el-input-number__increase{
        line-height: 16px !important;
      }
    }
    :deep(.el-date-editor .el-input__inner){
      padding-left: 30px;
    }
    .test-link{
      width: 140px;
    }
  }
  .video-durtion-tips {
    color: red;
    font-size: 10px;
  }
}
.dialog-footer {
  display: flex;
  align-items: center;
}
</style>
