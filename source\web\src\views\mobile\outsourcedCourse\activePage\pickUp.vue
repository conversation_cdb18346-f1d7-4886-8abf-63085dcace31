<template>
  <van-popup class="redemption-popup" v-model="show" round :close-on-click-overlay="false">
    <div class="content">
      <img class="left-top-pic" :src="require('@/assets/img/mobile/geekBang/left-top-pic.png')" alt="">
      <div class="content-main">
        <div class="warm">{{$route.query.staff_name || '腾讯学堂'}}</div>
        <div class="title">邀你共读”哈佛精品文库“ </div>
        <div class="coupon-content">
          <span v-if="isGraphic">
            <span>接受邀请，即可阅读当前文章</span>
            <br />
            并解锁<span class="text-05">价值¥1200的哈佛精品文库</span>
          </span>
          <span v-else>
            <span>接受邀请，即可解锁</span>
            <br />
            <span class="text-05">价值¥1200的哈佛精品文库</span>
          </span>
        </div>
        <van-button class="btn" @click="userOperator" :disabled="!isQuantity">接受邀请，点击解锁权限</van-button>
        <div class="footer-tips" v-if="!isQuantity">
          名额已领完，如有疑问请联系minnaluan或v_xxyhe
        </div>
      </div>
    </div>
    <img v-if="!isQuantity" class="close-icon" @click="$emit('input', false)" :src="require('@/assets/img/mobile/geekBang/right.png')" alt="" />
  </van-popup>
</template>

<script>
export default {
  name: 'pickup',
  props: {
    value: {
      type: Boolean,
      default: false,
      require: true
    },
    isGraphic: {
      type: Boolean,
      default: false
    },
    isQuantity: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    value(newValue) {
      console.log(newValue, 'newValuenewValuenewValue')
      this.show = newValue
    }
  },
  data() {
    return {
      show: false
    }
  },
  computed: {
  },
  created() {},
  mounted() {},
  methods: {
    userOperator() {
      if (!this.isQuantity) return
      this.$emit('handlerReceive')
    }
  }
}
</script>

<style lang="less" scoped>
.is-use {
  border-radius: 2px;
  background: #fdf6ec;
  padding: 3px 4px;
  color: #ff7548;
  font-family: 'PingFang SC';
  font-size: 10px;
  line-height: 12px;
  margin-left: 8px;
}
.redemption-popup {
  width: 312px;
  background-color: rgba(255, 255, 255, 0);
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: unset;
  top: 25%;
  right: 0;
  left: 0;
  bottom: 0;
  margin: auto;
  .content {
    position: relative;
    padding: 34px 12px 12px;
    width: 100%;
    border-radius: 24px;
    background: linear-gradient(180deg, #e3f2ff 0%, #e3f2ff 0%, #eaf5ff 35.99%);
    .left-top-pic {
      position: absolute;
      left: 4px;
      top: 8px;
      width: 89px;
      z-index: 1;
    }
    .content-main {
      position: relative;
      padding: 20px 7px;
      display: flex;
      flex-direction: column;
      align-items: center;
      border-radius: 16px;
      background-color: #fff;
      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background-color: #e9f4ff;
      }
      &::before {
        left: 0;
        transform: translate(-50%, -50%);
      }
      &::after {
        right: 0;
        transform: translate(50%, -50%);
      }
    }
    .color-cupton-num {
      color: #ed7b2f;
      font-weight: 500;
      line-height: 20px;
    }
    .warm {
      color: #0052d9;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
    }
    .title {
      margin-top: 16px;
       color: #00000099;
      font-size: 12px;
      line-height: 20px;
      text-align: center;
    }
    .coupon-content {
      width: 86%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 8px;
      color: #000000;
      font-size: 12px;
      font-weight: 600;
      text-align: center;
      line-height: 22px;
      background: #F9FBFC;
      margin: 12px 0;
      .text-05 {
        color: #0052d9;
      }
    }
    .btn {
      font-size: 12px;
      margin: 0 auto;
      color: #ffffff;
      display: flex;
      width: 232px;
      height: 36px;
      padding: 9px 20px;
      justify-content: center;
      align-items: center;
      border-radius: 24px;
      background: linear-gradient(90deg, #488dff 0%, #1766ff 100%);
    }
    .btn-long {
      width: auto;
    }
    .footer-tips {
      color: #e34d59;
      font-family: 'PingFang SC';
      font-size: 10px;
      font-style: normal;
      font-weight: 400;
      line-height: 16px;
      text-align: center;
       margin-top: 12px;
    }
  }
  .close-icon {
    width: 28px;
    margin-top: 16px;
  }
}
</style>
