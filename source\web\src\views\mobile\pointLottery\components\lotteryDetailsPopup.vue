<template>
  <div class="lottery-details-popup">
    <van-popup v-model="popupShow" round position="bottom" :overlay-style="{ background: 'rgba(0, 0, 0, 0.4)'}">
      <div class="popup-card">
        <div class="popup-head">
          <div class="title">抽奖次数明细</div>
          <img class="close" @click="onClose" src="../../../../assets/img/mobile/lottery/close.png" alt="" srcset="">
        </div>
        <div class="popup-body">
          <div class="count">
            <div class="residue-number">可用抽奖次数：<span class="blue-color">{{ detailsInfo.lottery_credit_balance || 0 }}</span></div>
            <span></span>
            <div class="get-more blue-color">
              <span @click="goGetMore">查看抽奖规则</span>
              <img @click="goGetMore" class="icon" src="../../../../assets/img/mobile/lottery/arrow-right.png" alt="" srcset="">
            </div>
          </div>
          <div class="list-card">
            <div class="content">
              <div class="item">
                <div class="table-wrap">
                  <table class="table-card">
                    <tr>
                      <th width="30%">类型</th>
                      <th width="43%">来源</th>
                      <th width="27%">获取时间</th>
                    </tr>

                    <tr class="border" v-for="(item, index) in pageData.list" :key="index">
                      <td class="td">{{ item.change_type }}</td>
                      <td class="td align-left">{{ item.remark }}</td>
                      <td class="td">{{ item.created_time }}</td>
                    </tr>
                  </table>
                </div>
              </div>
            </div>
            <van-loading v-if="loading">加载中...</van-loading>
            <div class="finished-text" v-if="finished">没有更多了</div>
          </div>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import {
  Popup,
  Loading
} from 'vant'
import { handlerDateFormat } from '@/utils/tools.js'
import { getPersonalLotteryDetailAPI } from '@/config/lottery.api.conf.js'

export default {
  props: {
    value: {
      type: Boolean,
      default: true,
      require: true
    }
  },
  components: {
    [Popup.name]: Popup,
    [Loading.name]: Loading
  },
  watch: {
  },
  data() {
    return {
      popupShow: this.value,
      detailsInfo: {},
      pageData: {
        list: [],
        current: 1,
        size: 8,
        total: 0
      },
      loading: true,
      finished: false
    }
  },
  created() {
    this.getPageData()
  },
  mounted() {
    this.$nextTick(() => {
      this.handlerScroll()
    })
  },
  methods: {
    getPageData() {
      getPersonalLotteryDetailAPI({
        current: this.pageData.current,
        size: this.pageData.size
      }).then(res => {
        this.detailsInfo = res
        this.loading = false
        this.pageData.total = res.vos.total
        res.vos.records.map(v => {
          v.created_time = handlerDateFormat(v.created_time)
        })
        this.pageData.list = this.pageData.list.concat(res.vos.records)
        if (this.pageData.total <= this.pageData.list.length) {
          this.finished = true
        }
      }).catch(() => {
        this.loading = false
        this.finished = false
      })
    },
    // 关闭弹窗
    onClose() {
      this.popupShow = false
      this.$emit('input', this.popupShow)
    },
    handlerScroll() {
      console.log('初始化')
      let that = this
      let fun = that.debounce(e => {
        // 距离顶部
        let scrollTop = e.target.scrollTop
        // 可视区域
        let clientHeight = e.target.clientHeight
        // 滚动条总高度
        let scrollHeight = e.target.scrollHeight
        // 当距离顶部的值加上可视区域的值大于等于总高度，则滚动条触底
        if (Math.ceil(scrollTop + clientHeight) >= (scrollHeight - 50)) {
          console.log('滚动到底部')
          if (this.finished) return
          if (this.pageData.total > this.pageData.list.length && !this.loading) {
            this.loading = true
            ++this.pageData.current
            this.getPageData()
          }
        }
      }, 300)
      let box = document.querySelector('.list-card')
      box.addEventListener('scroll', function (e) {
        fun(e)
      })
    },
    debounce(handle, delay = 500) {
      let timer = null
      return function () {
        let _self = this
        let _args = arguments
        clearTimeout(timer)
        timer = setTimeout(function() {
          handle.apply(_self, _args)
        }, delay)
      }
    },
    goGetMore() {
      this.$emit('goGetMore')
    }
  },
  beforeDestroy() {
  }
}
</script>

<style lang='less' scoped>
  .popup-card {
    width: 100vw;
    height: 80vh;
    letter-spacing: 0.5px;
    color: rgba(16, 16, 16, 1);
    display: flex;
    flex-direction: column;
    .popup-head {
      padding: 16px;
      position: relative;
      flex-shrink: 0;
      .title {
        text-align: center;
        color: #000000e6;
        font-size: 18px;
        font-weight: 600;
        line-height: 26px;
      }
      .close {
        width: 24px;
        height: 24px;
        position: absolute;
        top: 17px;
        right: 16px;
      }
    }
    .popup-body {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      .count {
        height: 48px;
        border-radius: 8px;
        background-color: #ECF2FE;
        text-align: center;
        display: flex;
        color: #000000e6;
        margin: 20px 16px 0;
        &>span {
          width: 1px;
          height: 10px;
          background: #C9DEFF;
          margin: 19px 0;
        }
        & > div {
          width: 50%;
          font-size: 14px;
          line-height: 48px;
        }
        .residue-number {
          margin-right: 24px;
        }
        .get-more {
          display: flex;
          justify-content: center;
          align-items: center;
          .icon {
            width: 16px;
            height: 16px;
            margin-left: 4px;
          }
        }
        .blue-color {
          color: #0052D9;
        }
      }
      .list-card {
        flex: 1;
        overflow-y: auto;
        padding: 0px 14px 20px;
        .van-loading {
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .finished-text {
          color: #969799;
          font-size: 14px;
          text-align: center;
        }
        .item {
          font-size: 14px;
          margin: 24px 0;
        }
        .table-wrap {
          border-radius: 4px;
          border: 1px solid #EEE;
          font-size: 12px;
          overflow: hidden;
        }
        .table-card {
          border-collapse: collapse;
          th {
            line-height: 20px;
            background-color: #EEEEEE;
            color: #00000099;
            padding: 8px 14px;
            text-align: left;
          }
          td {
            padding: 16px 14px;
            text-align: left;
            line-height: 20px;
          }
          .align-left {
            text-align: left;
          }
          .border {
            border-bottom: 1px solid #EEEEEE;
          }
        }
      }
    }
  }
</style>
