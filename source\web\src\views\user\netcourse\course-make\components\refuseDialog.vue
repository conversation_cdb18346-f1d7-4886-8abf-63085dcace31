<template>
  <el-dialog 
  custom-class="refuse-project-dialog dialog-center none-border-dialog" 
  title="提示" 
  :visible.sync="refuseShow" 
  width="430px"
  top="-150px"
  :show-close="false"
  :close-on-click-modal="false"
  >
    <div class="project-body">
      <span class="tips">请认真填写拒绝的理由</span>
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item prop="reason">
          <el-input
            type="textarea"
            placeholder="请输入拒绝的原因，此项必填"
            v-model="form.review_failed_reason"
            maxlength="200"
            show-word-limit
            :autosize="{ minRows: 5, maxRows: 6 }"
          >
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel" size="small">取 消</el-button>
      <el-button type="danger" @click="confirm(2)" size="small">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    refuseShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        review_failed_reason: ''
      },
      rules: {
        review_failed_reason: [{ required: true, message: this.$t('Mooc_ProjectDetail_EnterExitReason'), trigger: 'blur' }]
      }
    }
  },
  methods: {
    // 取消
    cancel() {
      this.$emit('update:refuseShow', false)
    },
    // 确定
    confirm() {
      this.$emit('refuseConfirm', 2, this.form.review_failed_reason)
    }
  }
}
</script>
<style lang="less" scoped>
:deep(.refuse-project-dialog) {
  .el-dialog__body {
    padding: 15px 32px;
    .el-form {
      margin-top: 10px;
    }
  }
}
</style>
