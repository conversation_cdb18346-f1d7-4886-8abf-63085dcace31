.flex{
    display: flex;
}
.flex-wrap{
    flex-wrap: wrap;
}
.flex-1{
    flex: 1;
}
.flex-10{
    flex: 10;
}
.align-center{
    align-items: center;
}
.align-start{
    align-items: flex-start;
}
.justify-between{
    justify-content: space-between;
}
.justify-end{
    justify-content: flex-end;
}
.relative{
    position: relative;
}
.center{
    text-align: center;
}
.linkKey{
    color: #0052D9;
    cursor: pointer;
}
.pointer{
    cursor: pointer;
}