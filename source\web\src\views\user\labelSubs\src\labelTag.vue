<template>
  <div class="table-tags-content">
    <el-tag v-for="(tag, index) in tags" :key="index" :closable="close" disable-transitions class="custom-tag margin-16" :type="tag.type" :class="{'clicked-tag': tag.clicked}" @close="handleTagClose(tag)" @click="addToTaga(tag, true)" size="small">
      <img v-if="tag.clicked && tag.label_type === 2" :src="imgeuesr">
      <img v-if="!tag.clicked && tag.label_type === 2" :src="imgeuesrto">
      <el-popover :disabled="isPopover" placement="bottom-start" trigger="hover" @show="handleMouseEnter(tag)">
        <div style="display: inline-block;height: 24px;min-width: 28px;" slot="reference">
          {{tag.label_name}}
        </div>
        <article class="labelDetailBox">
          <header class="labelDetailBox-header">
            <h3 class="label-tags">{{ tag.label_name }}</h3>
          </header>
          <p class="p1" v-if="!isPopover">{{ editable.category_full_name }}</p>
          <p class="p2">{{ editable.subscribe_count }}订阅&nbsp; · &nbsp;{{ editable.content_count }}内容</p>
          <footer>
            <el-button style="font-family: 'PingFang SC'" type="danger" plain size="mini" v-if="tag.clicked" @click="addToTaga(tag,false, index)">取消订阅</el-button>
            <el-button style="font-family: 'PingFang SC'" type="primary" size="mini" class="subsBtn" v-else @click="addToTaga(tag)">立即订阅</el-button>
            <el-button style="font-family: 'PingFang SC'" plain size="mini" @click="checkLabelInfo(tag)">查看相关内容</el-button>
          </footer>
        </article>
      </el-popover>
    </el-tag>
  </div>
</template>

<script>
import axios from 'axios'
export default {
  props: {
    deitailInfo: {
      type: Boolean,
      default: false
    },
    tags: {
      type: [],
      default: () => []
    },
    // X 关闭按钮
    close: {
      type: Boolean,
      default: false
    },
    clickAdd: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      editable: {},
      isPopover: false,
      imgeuesr: require('../assets/img/usergroup.png'),
      imgeuesrto: require('../assets/img/usergroup (1).png')
    }
  },
  computed: {
    httpHost() {
      let httpHost = ''
      if (process.env.NODE_ENV === 'production') {
        httpHost = window.location.hostname.endsWith('.woa.com')
          ? '//portal.learn.woa.com'
          : '//portal.learn.oa.com'
      } else {
        httpHost = window.location.hostname.endsWith('.woa.com')
          ? '//test-portal-learn.woa.com'
          : '//test.portal.learn.oa.com'
      }
      return httpHost
    }
  },
  methods: {
    addToTaga(tag, type, index) {
      if (this.close && type) return false
      this.$emit('addToTaga', tag)
      // 已订阅的标签取消订阅
      if (this.close) {
        const nextTag = this.tags[index + 1] || null
        if (nextTag && nextTag.category_full_name) {
          this.editable = nextTag
        } else {
          nextTag && this.handleMouseEnter(nextTag)
        }
        this.isPopover = true
        setTimeout(() => {
          this.isPopover = false
        }, 600)
      }
    },
    handleTagClose(tag) {
      this.$emit('handleTagClose', tag)
    },
    // 查看相关内容
    checkLabelInfo(tag) {
      if (tag.clicked) {
        window.open(
          `${this.httpHost}/training/label-subs?subsType=0&label_id=${tag.label_id}`
        )
      } else {
        window.open(
          `${this.httpHost}/training/label-subs` +
            '?isLabelGatherPage=' +
            true +
            '&label_id=' +
            tag.label_id +
            '&label_name=' +
            tag.label_name
        )
      }
    },
    handleMouseEnter(tag) {
      if (this.deitailInfo) {
        this.editable = tag
      } else {
        if (tag.category_full_name) {
          this.editable = tag
          return false
        }
        axios
          .post(
            `${this.httpHost}/training/api/label/subscribe/getLabelDetailInfo`,
            {
              label_id: tag.label_id
            },
            {
              withCredentials: true
            }
          )
          .then((res) => {
            if (res.data.code === 200) {
              const { data } = res.data
              this.editable = data
              Object.assign(tag, data)
            }
          })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.table-tags-content {
  display: inline-block;
  line-height: 1;
}
.margin-16 {
  margin-right: 16px;
}
.custom-tag {
  height: auto !important;
  cursor: pointer;
  padding: 0 8px;
  background: #f7f8fa;
  margin-right: 20px; /* 设置标签之间的间距 */
  border-radius: 0 15px 15px 0; /* 添加右边的半圆效果 */
  color: #00000099;
  font-family: 'PingFang SC';
  font-size: 12px;
  font-style: normal;
  border: 1px solid #dcdcdc;
  line-height: 22px;
  margin-top: 15px;
  // min-width: 0px;
  img {
    height: 16px;
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 2px;
    margin-right: 5px;
  }
  /deep/ .el-popover__reference{
    height: 22px !important;
  }
  /deep/ .el-tag__close {
    color: #0052d9;
    font-size: 16px;
  }
  /deep/ .el-tag__close:hover {
    background-color: #f5f7f9; /* 你想要的颜色 */
    color: #0052d9;
  }
}
.clicked-tag {
  margin-top: 15px;
  border-color: #0052d9;
  border: 1px solid #0052d9 !important;
  background: #f2f3ff !important;
  color: #0052d9 !important;
  font-weight: 400;
  // background: var(--brand-brand-1-light, #ECF2FE);
}
.labelDetailBox {
  > header {
    h4 {
      font-size: 14px;
      font-size: 12px;
    }
    a {
      color: #00000042;
      line-height: 20px;
      font-size: 12px;
    }
  }
  .labelDetailBox-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .p1,
  .p2 {
    color: #00000066;
    margin-top: 6px;
    line-height: 20px;
    font-size: 12px;
    font-weight: 400;
    font-family: 'PingFang SC';
    font-style: normal;
  }
  .p2 {
    color: #00000099;
    align-self: stretch;
  }
  footer {
    margin-top: 6px;
    /deep/.el-button {
      padding: 2px 7px !important;
      line-height: 20px !important;
    }
    /deep/.el-button--primary.el-button {
      background: #0052D9;
      color: #fff;
      border-color: #0052D9;
    }
    /deep/.el-button--danger.is-plain {
      background: none;
      color: #d54941;
      border-color: #d54941;
    }
    /deep/.el-button--danger.is-plain:hover {
      background: none;
      color: #d54941;
    }
  }
  .label-tags {
    color: #000000e6;
    font-family: 'PingFang SC';
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 22px;
  }
}
</style>
