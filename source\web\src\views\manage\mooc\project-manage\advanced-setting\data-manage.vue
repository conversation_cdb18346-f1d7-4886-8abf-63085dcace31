<template>
  <div class="data-manage">
    <uploadFlex 
      ref="uploadFlex"
      :is-disabled="tableData.total === 20 || isApprove"
      @handleSuccess="handleUploadSuccess"
      >
      <template slot="submit-btn">
        <el-button size="small" type="primary" :disabled="tableData.total === 20 || isApprove">
          <div class="upload-btn" style="">
            <img class="upload-icon" src="@/assets/mooc-img/upload.png" alt="" srcset="">
            <span>上传资料</span>
          </div>
        </el-button>
      </template>
    </uploadFlex>
    <div class="table">
      <div class="table-head">
        <div class="hr" :class="`column-${index}`" v-for="(label,index) in labelList" :key="index">{{ label }}</div>
      </div>
      <div class="table-body">
        <el-tree
          :data="tableData.records"
          node-key="id"
          draggable
          @node-drop="handleDrop"
          :allow-drop="allowDrop"
          :allow-drag="allowDrag"
          :render-content="renderContent">
        </el-tree>
      </div>
    </div>
    <el-pagination
      v-if="tableData.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="current"
      :page-sizes="[5, 10, 20, 30, 50, 100]"
      :page-size="size"
      layout="total, prev, pager, next, sizes, jumper"
      :total="tableData.total"
      ></el-pagination>
  </div>
</template>

<script>
import pager from '@/mixins/pager'
import uploadFlex from './component/upload-flex.vue'
import { saveResourceBatch, getResourceList, resourceDelete, setAllowDownload, setResourceSort, resourceViewOrDownload } from '@/config/mooc.api.conf'
import { mapState } from 'vuex'
export default {
  mixins: [pager],
  components: {
    uploadFlex
  },
  data () {
    return {
      labelList: ['序号', '资料名称', '类型', '大小', '允许下载', '上传时间', '查看次数', '下载次数', '操作'],
      tableData: {
        records: [],
        total: 0
      },
      flexTypeList: ['', '视频', '音频', '图片', '压缩包', '文档'],
      flexTypes: {
        'Video': '视频',
        'Audio': '音频',
        'Doc': '文档',
        'Zip': '压缩包',
        'Image': '图片'
      }
    }
  },
  mounted() {
    this.onSearch()
  },
  computed: {
    ...mapState(['projectManageInfo']),
    typeText() {
      return (type) => {
        return isNaN(type * 1) ? this.flexTypes[type] : this.flexTypeList[type]
      }
    },
    isApprove() {
      // 审批-审批中
      return this.$route.query.approve === '1' || this.projectManageInfo.course_status === 6
    }
  },
  methods: {
    onSearch(page_no = 1) {
      const { mooc_course_id } = this.$route.query
      const params = {
        mooc_course_id,
        current: page_no,
        size: this.size
      }
      getResourceList(params).then((res) => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    // 文件上传成功
    handleUploadSuccess(res) {
      const { mooc_course_id } = this.$route.query
      const params = {
        mooc_course_id: mooc_course_id,
        resource_type: res.file_type,
        content_id: res.content_id,
        file_size: res.file_size,
        resource_name: res.file_name,
        allow_download: true
      }
      saveResourceBatch(params).then(res => {
        this.onSearch(1)
      })
    },
    // 渲染
    renderContent(h, { node, data, store }) {
      return (
        <div class="custom-tree-node">
          <div class="column-0">{ data.order_no }</div>
          <div class="column-1">
            <el-tooltip effect="dark" content={data.resource_name} placement="top-start">
              <el-link disabled={ this.isApprove } on-click={ () => this.handleViewOrDownload(data) } type="primary" underline={ false }>{ data.resource_name }</el-link>
            </el-tooltip>
          </div>
          <div class="column-2">{ this.typeText(data.resource_type) }</div>
          <div class="column-3">{ this.bytesToSize(data.file_size || 0) }</div>
          <div class="column-4">
            <el-switch
              disabled={ data.resource_type === 'Zip' || this.isApprove}
              v-model={ data.allow_download }
              active-color="#0052D9"
              inactive-color="#dcdfe6"
              on-change={ () => this.handleSetAllowDownload(data) }>
            </el-switch>
            <span class="tips">{ data.allow_download ? '允许' : '不允许' }</span>
          </div>
          <div class="column-5">{ this.getDate(data.created_at, '-') }</div>
          <div class="column-6">{ data.view_count || 0 }</div>
          <div class="column-7">{ data.download_count || 0 }</div>
          <div class="column-8">
            <el-link disabled={ this.isApprove } on-click={ () => this.handleDelete(data) } type="primary" underline={ false }>移除</el-link>
            <i class="el-icon-rank" style="color: rgba(0, 0, 0, 0.6);"></i>
          </div>
        </div>)
    },
    // 拖拽成功完成时触发的事件
    handleDrop(draggingNode, dropNode, dropType, ev) {
      const { mooc_course_id } = this.$route.query
      const params = {
        mooc_course_id,
        resource_id: Number(draggingNode.data.id),
        target_no: dropNode.data.order_no
      }
      setResourceSort(params).then(res => {
        this.$message.success('设置成功')
        this.onSearch(1)
      })
    },
    // 拖拽时判定目标节点能否被放置
    allowDrop(draggingNode, dropNode, type) {
      if (type === 'prev' || type === 'next') {
        return true
      } else {
        return false
      }
    },
    // 判断节点能否被拖拽
    allowDrag(draggingNode) {
      if (this.isApprove) return false
      return true
    },
    // 单行移除
    handleDelete({ resource_name, id }) {
      this.$messageBox.confirm(`移除《${resource_name}》文件后无法恢复，确定移除吗？`, '移除资料', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        const { mooc_course_id } = this.$route.query
        resourceDelete({ id, mooc_course_id }).then((res) => {
          this.$message.success('移除成功')
          this.onSearch(1)
        })
      })
    },
    // 是否下载设置
    handleSetAllowDownload({ id, allow_download }) {
      const params = {
        allow_download: allow_download,
        id: id
      }
      setAllowDownload(params).then((res) => {
        this.$message.success('设置成功')
        this.onSearch()
      })
    },
    // 文件查看或下载
    handleViewOrDownload(row) {
      const { mooc_course_id } = this.$route.query
      const { content_id, resource_type } = row
      const params = {
        mooc_course_id,
        content_id,
        resource_type,
        operate_type: resource_type !== 'Zip' ? 'view' : 'download'
      }
      resourceViewOrDownload(params).then((res) => {
        let curIndex = this.tableData.records.findIndex(v => v.content_id === content_id)
        if (resource_type !== 'Zip') {
          ++this.tableData.records[curIndex].view_count
        } else {
          ++this.tableData.records[curIndex].download_count
        }
        this.$refs.uploadFlex.handleViewFile(row)
      })
    },
    getDate(time = '', style = '/') {
      const now = time ? new Date(time) : new Date()
      const year = now.getFullYear()
      const month = now.getMonth() + 1
      const date = now.getDate()
      let h = now.getHours()
      let m = now.getMinutes()
      let s = now.getSeconds()
      return year + style + this.dateStyle(month) + style + this.dateStyle(date) + ' ' + this.dateStyle(h) + ':' + this.dateStyle(m) + ':' + this.dateStyle(s)
    },
    dateStyle(v) {
      return Number(v) < 10 ? `0${v}` : v
    },
    bytesToSize(b) {
      if (b === 0) return '0B'
      let k = 1024
      let size = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']
      let i = Math.floor(Math.log(b) / Math.log(k))
      return (b / Math.pow(k, i)).toPrecision(3) + size[i]
    }
  }
}
</script>

<style lang="less" scoped>
.table {
  margin-top: 16px;
  border: 1px solid #eeeeeeff;
  border-radius: 4px;
  .table-head {
    height: 48px;
    background: #F5F5F5FF;
    color: rgba(0, 0, 0, 0.6);
    font-size: 14px;
    font-weight: 400;
    font-family: "PingFang SC";
    display: flex;
    .hr{
      height: 48px;
      line-height: 48px;
      padding: 0 16px;
      flex: 1;
    }
  }
  .table-body {
    :deep(.el-tree){
      .el-tree-node__content {
        width: 100%;
        height: 60px;
        .el-icon-caret-right:before {
          content: '';
        }
      }
      .el-tree-node__expand-icon.is-leaf {
        width: 1px;
        padding: 16px 0px;
      }
      .custom-tree-node {
        display: flex;
        width: 100%;
        height: 60px;
        border-bottom: 1px solid rgb(238, 238, 238);
        & > div {
          flex: 1;
          padding: 0 16px;
          color: rgba(0, 0, 0, 0.9);
          font-size: 14px;
          font-weight: 400;
          font-family: "PingFang SC";
          display: flex;
          align-items: center;
        }
      }
      .column-0 {
        width: 60px;
        max-width: 60px;
      }
      .column-1 {
        min-width: 268px;
      }
      .column-2 {
        min-width: 72px;
        justify-content: center;
      }
      .column-3 {
        min-width: 72px;
        justify-content: center;
      }
      .column-4 {
        min-width: 118px;
        .tips {
          margin-left: 8px;
        }
      }
      .column-5 {
        min-width: 178px;
      }
      .column-6 {
        min-width: 88px;
        justify-content: center;
      }
      .column-7 {
        min-width: 88px;
        justify-content: center;
      }
      .column-8 {
        min-width: 272px;
        .el-icon-rank {
          color: rgba(0, 0, 0, 0.6);
          margin-left: 12px;
        }
      }
    }
    :deep(.el-tooltip.el-link.el-link--primary) {
      width: 100%;
      display: -webkit-box;
      white-space: normal;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
    }
    :deep(.el-switch.is-disabled.is-checked) {
      opacity: 0.5;
    }
  }
  .column-0 {
    width: 60px;
    max-width: 60px;
  }
  .column-1 {
    min-width: 268px;
  }
  .column-2 {
    min-width: 72px;
    text-align: center;
  }
  .column-3 {
    min-width: 72px;
    text-align: center;
  }
  .column-4 {
    min-width: 118px;
  }
  .column-5 {
    min-width: 178px;
  }
  .column-6 {
    min-width: 88px;
    text-align: center;
  }
  .column-7 {
    min-width: 88px;
    text-align: center;
  }
  .column-8 {
    min-width: 272px;
  }
}
.upload-btn {
  display: flex;
  align-items: center;
  .upload-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}
</style>
