import env from 'config/env.conf.js'
import router from 'router/mobile.js'
// 将数字转换为时分秒
import { Toast } from 'vant'
import moment from 'moment'
import { Message } from 'element-ui'

export const transforTime = (times) => {
  if (times <= 0) {
    return '0秒'
  } else {
    const hh = parseInt(times / 3600) // 小时
    const shh = Math.floor(times - hh * 3600)
    const mm = parseInt(shh / 60)
    const ss = Math.floor(shh - mm * 60)
    let hour = hh > 0 ? hh + '小时' : ''
    let min = hh > 0 ? (mm + '分钟') : (mm > 0 ? mm + '分钟' : '')
    let sec = hh > 0 ? (ss + '秒') : (mm > 0 ? (ss > 0 ? ss + '秒' : '') : ss > 0 ? ss + '秒' : '')
    return hour + min + sec
  }
}
export const transforNcTime = (times) => {
  if (times <= 0) {
    return '00:00:00'
  } else {
    const hh = parseInt(times / 3600) // 小时
    const shh = Math.floor(times - hh * 3600)
    const mm = parseInt(shh / 60)
    const ss = Math.floor(shh - mm * 60)
    let hour = hh < 10 ? `0${hh}:` : `${hh}:`
    let min = mm < 10 ? `0${mm}:` : `${mm}:`
    let sec = ss < 10 ? `0${ss}` : `${ss}`
    return hour + min + sec
  }
}
export const getQueryVariable = (variable) => {
  const query = decodeURIComponent(window.location.search.substring(1))
  const vars = query.split('&')
  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=')
    if (pair[0] === variable) {
      let delValue = ''
      const needList = pair.filter((item) => item !== variable)
      needList.forEach((item, index) => {
        if (index === needList.length - 1) {
          delValue += item
        } else {
          delValue += `${item}=`
        }
      })
      return delValue
    }
  }
  if (variable === 'area_id') {
    return ''
  }
  return false
}
// 通过用户英文名获取对于的头像地址
export const getAvatar = (useName) => {
  if (window.location.host.endsWith('.woa.com')) {
    return `//r.hrc.woa.com/photo/150/${useName}.png`
  } else {
    return `//r.hrc.oa.com/photo/150/${useName}.png`
  }
}

// 防抖
export const debounce = (fn, time) => {
  time = time || 200
  // 定时器
  let timer = null
  return function (...args) {
    var _this = this
    if (timer) {
      clearTimeout(timer)
    }
    timer = setTimeout(function () {
      timer = null
      fn.apply(_this, args)
    }, time)
  }
}

// 将数字转换成‘万’单位，最多保留一位小数
export const transformUnit = (count) => {
  let str = (count || 0) + ''
  if (str.length > 5) {
    str = Math.round(str / 1000) / 10 + '万'
  }
  return str
}
export const transformUnitW = (count) => {
  let str = (count || 0) + ''
  if (str.length >= 5) {
    str = Math.round(str / 1000) / 10 + 'W'
  }
  return str
}
// 时间转换成 xxxx年-xx月-xx日
export const timeToDate = (val) => {
  let str = '--'
  if (val) {
    let timeArr = val.split(' ')[0].split('-')
    str = `${timeArr[0]}年${timeArr[1]}月${timeArr[2]}日`
  }
  return str
}

export const throttle = (func, wait) => {
  let timeout = null
  return function () {
    const context = this
    const args = arguments
    if (!timeout) {
      timeout = setTimeout(() => {
        timeout = null
        func.apply(context, args)
      }, wait)
    }
  }
}

// 图片拼接
export const handleImgUrl = (image_id) => {
  const envName = env[process.env.NODE_ENV]
  if (image_id.indexOf('//static.taishan') > 0) {
    return image_id
  } else {
    return `${envName.contentcenter}content-center/api/v1/content/imgage/${image_id}/preview`
  }
}
export const textEllipsis = function (val, { minLength = 20, before = 10, after = 6 } = {}) {
  if (!val) return ''
  const len = val.length
  if (len > minLength) {
    return val.substring(0, before) + '...' + val.substring(len - after, len)
  }
  return val
}
// 获取当前时间格式
export const getDate = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth() + 1
  const date = now.getDate()
  let h = now.getHours()
  let m = now.getMinutes()
  let s = now.getSeconds()
  h = h < 10 ? '0' + h : h
  m = m < 10 ? '0' + m : m
  s = s < 10 ? '0' + s : s
  return year + '/' + month + '/' + date + ' ' + h + ':' + m + ':' + s
}

// 获取当前时间格式
export const handlerDateFormat = (d, t = '/') => {
  const now = new Date(d)
  const year = now.getFullYear()
  let month = now.getMonth() + 1
  let date = now.getDate()
  let h = now.getHours()
  let m = now.getMinutes()
  let s = now.getSeconds()
  month = month < 10 ? '0' + month : month
  date = date < 10 ? '0' + date : date
  h = h < 10 ? '0' + h : h
  m = m < 10 ? '0' + m : m
  s = s < 10 ? '0' + s : s
  return year + t + month + t + date + ' ' + h + ':' + m + ':' + s
}
// 秒转成时分秒
export const fomatSecond = (mss) => {
  if (!mss) return '--'
  const curM = parseInt(mss)
  let days = Math.floor(curM / (1000 * 60 * 60 * 24))
  days = days < 10 && days > 0 ? `0${days}:` : days > 10 ? `${days}:` : ''
  let hours = Math.floor((curM % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
  hours = hours < 10 ? `0${hours}` : hours
  let minutes = Math.floor((curM % (1000 * 60 * 60)) / (1000 * 60))
  minutes = minutes < 10 ? `0${minutes}` : minutes
  let seconds = Math.round((curM % (1000 * 60)) / 1000)
  seconds = seconds < 10 ? `0${seconds}` : seconds
  return `${days}${hours}:${minutes}:${seconds} `
}
// 获取当前浏览器
export const getExplorer = () => {
  const explorer = window.navigator.userAgent
  if (explorer.indexOf('QQBrowser') >= 0 || explorer.indexOf('QQ') >= 0) {
    return 'qq'
  } else if (
    explorer.indexOf('Safari') >= 0 &&
    explorer.indexOf('MetaSr') >= 0
  ) {
    return '搜狗'
  } else if (window.ActiveXObject || 'ActiveXObject' in window) {
    // IE
    return 'IE'
  } else {
    // 非IE
    if (explorer.indexOf('LBBROWSER') >= 0) {
      return '猎豹'
    } else if (explorer.indexOf('Firefox') >= 0) {
      return '火狐'
    } else if (explorer.indexOf('Maxthon') >= 0) {
      return '遨游'
    } else if (explorer.indexOf('Chrome') >= 0) {
      return '谷歌'
    } else if (explorer.indexOf('Opera') >= 0) {
      return '欧朋'
    } else if (explorer.indexOf('TheWorld') >= 0) {
      return '世界之窗'
    } else if (explorer.indexOf('Safari') >= 0) {
      return 'Safari'
    } else {
      return '其他'
    }
  }
}

// 检查域名是否在小程序webview的白名单内
export const checkWebviewHost = (url) => {
  const arr = [
    'https://auth-mgate.woa.com',
    'https://csig.lexiangla.com',
    'https://ihr.tencent.com',
    'https://iwiki.woa.com',
    'https://km.tencent.com',
    'https://km.woa.com',
    'https://lexiangla.com',
    'https://m-learn.woa.com',
    'https://mybucket-1258938271.cos.ap-chengdu.myqcloud.com',
    'https://ntsgw.woa.com',
    'https://oa.m.tencent.com',
    'https://panshi.tencent.com',
    'https://portal.learn.woa.com',
    'https://test-portal-learn.woa.com',
    'https://test-learn.woa.com',
    'https://learn.woa.com',
    'https://video-learn.woa.com',
    'https://exam.woa.com',
    'https://sdc.qq.com',
    'https://hangjia.woa.com',
    'https://qianlong.woa.com',
    'https://ntsapps.woa.com'
  ]
  return arr.findIndex((i) => url.startsWith(i)) > -1
}

// 证书详情页跳转 - 第三方证书系统
export const certificateView = (certificateBatchNo) => {
  window.open(process.env.NODE_ENV === 'production' ? `https://ihr.tencent.com/cF0jB1/${certificateBatchNo}` : `https://ihr.tencent.com/cC4bD1/${certificateBatchNo}`)
}

// 跳转移动端mooc任务详情页
export const toMoocDetailMob = (data, mooc_course_id, from, class_id, isVertical, lang, preview = false, moocPreview = 0) => {
  console.log(data)
  const {
    act_type,
    task_id,
    lock_status,
    unlock_time,
    lock_time
  } = data
  const spocConfigCourse = ['1', '98']
  let msg
  if (lock_status === 2) {
    if (from === 'mooc') {
      if (unlock_time) {
        msg = `${this.$langue('Mooc_TaskDetail_TaskUnLockedNow', { defaultText: '任务暂未解锁' })}\n${this.$langue('Mooc_TaskDetail_UnlockTime', { defaultText: '解锁时间' })}：${unlock_time}`
      } else {
        msg = this.$langue('Mooc_Common_Authority_NotStudyByAdminLocked', { defaultText: '任务已被管理员锁定，无法学习' })
      }
    } else if (from === 'spoc' && !spocConfigCourse.includes(act_type)) {
      // unlock_time 为任务开始时间、lock_time为任务结束时间
      let start = unlock_time ? moment(unlock_time).valueOf() : 0
      let end = lock_time ? moment(lock_time).valueOf() : 0
      let timeStamp = moment().valueOf()
      if (start && timeStamp < start) {
        msg = `${this.$langue('Mooc_TaskDetail_TaskUnLockedNow', { defaultText: '任务暂未解锁' })}\n${this.$langue('Mooc_TaskDetail_UnlockTime', { defaultText: '解锁时间' })}：${unlock_time}`
      } else if (end && timeStamp > end) {
        msg = this.$langue('Mooc_TaskDetail_TaskOverTime', { defaultText: '当前任务已过期，无法继续学习' })
      } else {
        msg = '任务开始时间、任务结束时间校验异常'
      }
    }
  } else if (data.unlocked_by_step) {
    msg = this.$langue('Mooc_ProjectDetail_TaskList_UnlockByPreTask', { defaultText: '完成上一个应学任务后解锁' })
  } else if (data.task_status === 2) {
    msg = `${this.$langue('Mooc_ProjectDetail_TaskList_TaskDisable', { defaultText: '任务已失效，无法学习' })}\n请联系管理员调整`
  }
  if (msg) {
    Toast({
      className: isVertical ? 'interaction-toast' : '',
      message: msg
    })
    return
  }
  // 针对spoc岗前任务 线上任务类型[18, 10, 2, 99]
  const preworkTaskTypes = ['18', '10', '2', '99']
  if (data.resource_from === 'gangqian' && preworkTaskTypes.includes(act_type)) {
    let query = {
      mooc_course_id: mooc_course_id,
      task_id: task_id,
      from: from,
      class_id: class_id,
      lang: lang
    }
    router.replace({
      name: 'taskDetailMob',
      query
    })
  }
  const wxUrl = toWechatMiniPage(data, mooc_course_id, from, class_id, isVertical, lang)
  console.log('window.wx.miniProgram', wxUrl)
  console.log('wxUrl', window.wx.miniProgram)
  if (wxUrl) {
    window.wx.miniProgram.redirectTo({
      url: wxUrl,
      fail: function (res) {
        console.log('失败', res)
      },
      success: function (res) {
        console.log('成功', res)
      },
      complete: function (res) {
        console.log('完成', res)
      }
    })
  } else {
    // 2:网课课、20:考试系统、21:课程素材、22:作业、23:第三方任务、99:外链 18文章
    // 网课课：Scorm、Zip也不支持访问
    let url = `${window.location.origin}/training/mobile/mooc/taskDetail?mooc_course_id=${mooc_course_id}&task_id=${task_id}&from=${from}&lang=${lang}`
    console.log('文章跳转链接', url)
    if (from === 'spoc' && class_id) {
      url += `&class_id=${class_id}`
    }
    // window.location.replace(url)
    let query = {
      mooc_course_id: mooc_course_id,
      task_id: task_id,
      from: from,
      lang: lang,
      class_id: class_id,
      debugger: false,
      moocPreview
    }
    if (preview) {
      query.preview = preview
    }
    router.replace({
      name: 'taskDetailMob',
      query
    })
  }
}
export const toWechatMiniPage = (res, mooc_course_id, from, class_id) => {
  const { task_id, act_id } = res
  let url = ''
  if (res.act_type === '18') {
    url = `/pages/networkCourse/article/index?mooc_course_id=${mooc_course_id}&task_id=${task_id}&from=${from}`
  }

  // 素材
  if (res.act_type * 1 === 21) {
    if (res.resource_type === 'Article') {
      url = `/pages/networkCourse/article/index?mooc_course_id=${mooc_course_id}&task_id=${task_id}&from=${from}`
    }
    if (res.resource_type === 'Audio') {
      url = `/pages/networkCourse/audio/index?mooc_course_id=${mooc_course_id}&task_id=${task_id}&from=${from}`
    }
  }

  // 作业
  if (res.act_type * 1 === 22) {
    url = `/pages/mooc/work/index?homework_id=${act_id}&task_id=${task_id}&act_id=${mooc_course_id}&from=${from}`
  }

  // 第三方
  if (res.act_type * 1 === 23) {
    url = `/pages/mooc/thirdPartyTasks/index?mooc_course_id=${mooc_course_id}&task_id=${task_id}&from=${from}`
  }

  if (url && from === 'spoc' && class_id) {
    url += `&class_id=${class_id}`
  }
  return url
}
// 跳转到不支持移动端访问页面
export const toUnsupportedPage = ({ mooc_course_id, task_id, from, class_id }) => {
  let link = ''
  if (from === 'mooc') {
    link = `${process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/TZELHU' : 'http://s.test.yunassess.com/s/urrd9E'}?mooc_course_id=${mooc_course_id}&task_id=${task_id}&from=${from}`
  } else if (from === 'spoc') {
    // spoc PC 页面
    link = ''
  }

  let url = `/pages/error/errorPage?type=2&href=${encodeURIComponent(link)}&from=${from}&mooc_course_id=${mooc_course_id}`

  if (from === 'spoc' && class_id) {
    url += `&class_id=${class_id}`
  }
  // 任务关联资源不支持移动端查看
  console.log('11111111111111111111', window.wx.miniProgram)
  window.wx.miniProgram.redirectTo({
    url,
    fail: function (res) {
      console.log('失败', res)
    },
    success: function (res) {
      console.log('成功：', res)
    },
    complete: function (res) {
      console.log('complete', res)
    }
  })
}
// 默认图cdn处理
export const formatModuleMap = (id) => {
  const moduleMap = {
    1: { pic: 'https://xue.m.tencent.com/qlmini/image/default/netCourse.png', text: '网络课' },
    2: { pic: 'https://xue.m.tencent.com/qlmini/image/default/faceCourse.png', text: '面授课' },
    3: { pic: 'https://xue.m.tencent.com/qlmini/image/default/live.png', text: '直播' },
    4: { pic: 'https://xue.m.tencent.com/qlmini/image/default/activity.png', text: '活动' },
    5: { pic: 'https://xue.m.tencent.com/qlmini/image/default/marker.png', text: '码客' },
    6: { pic: 'https://xue.m.tencent.com/qlmini/image/default/hangjia.png', text: '行家咨询' },
    7: { pic: 'https://xue.m.tencent.com/qlmini/image/default/case.png', text: '案例' },
    8: { pic: 'https://xue.m.tencent.com/qlmini/image/default/article.png', text: '文章' },
    9: { pic: 'https://xue.m.tencent.com/qlmini/image/default/article.png', text: '图文' },
    10: { pic: 'https://xue.m.tencent.com/qlmini/image/default/mooc.png', text: '培养项目' },
    15: { pic: 'https://xue.m.tencent.com/qlmini/image/default/courselist.png', text: '课单' },
    16: { pic: 'https://xue.m.tencent.com/qlmini/image/default/document.png', text: '文档' },
    99: { pic: 'https://xue.m.tencent.com/qlmini/image/default/link.png', text: '外链' },
    23: { pic: 'https://xue.m.tencent.com/qlmini/image/default/faceCourse.png', text: '班级' }
  }
  return moduleMap[id] ? moduleMap[id].pic : require('@/assets/img/mobile/default-image.png')
}
// 获取图片背景颜色
export const getImgBgColor = (url, _callback) => {
  let image = new Image()
  // 处理跨源污染
  // image.setAttribute('crossOrigin', 'anonymous') // anonymous/use-credentials
  image.onload = function () {
    const canvas = document.createElement('canvas')
    canvas.width = image.naturalWidth
    canvas.height = image.naturalHeight
    const context = canvas.getContext('2d')
    context.drawImage(image, 0, 0, image.naturalWidth, image.naturalHeight)
    /**
     * 获取左右各两个边缘像素点的色值
     * 左边两点
     * p1 [x = 10, y = 10]
     * p2 [x = 10, y = height - 10]
     * 右边两点
     * p3 [x = width - 10, y = 10]
     * p4 [x = width - 10, y = height - 10]
     */
    const data = context.getImageData(0, 0, image.width, image.height).data
    const p1 = data.slice(
      (image.width * 10 + 10) * 4,
      (image.width * 10 + 10) * 4 + 4
    )
    const p2 = data.slice(
      (image.width * (image.height - 10) + 10) * 4,
      (image.width * (image.height - 10) + 10) * 4 + 4
    )
    const p3 = data.slice(
      (image.width * 10 + (image.width - 10)) * 4,
      (image.width * 10 + (image.width - 10)) * 4 + 4
    )
    const p4 = data.slice(
      (image.width * (image.height - 10) + (image.width - 10)) * 4,
      (image.width * (image.height - 10) + (image.width - 10)) * 4 + 4
    )
    // 获取像素平均值
    const average = []
    for (let i in p1) {
      average[i] = Math.round((p1[i] + p2[i] + p3[i] + p4[i]) / 4)
    }
    if (typeof _callback === 'function') {
      _callback(`rgba(${average.join(',')})`)
    }
    console.log('bg_color', `rgba(${average.join(',')})`)
  }
  image.onerror = function (e) {
    console.log('error', e)
  }
  image.src = url
}

/**
 * 详情页曝光上报
 * @param {object} data - page_type,content_type,act_type,content_name,content_id
 *  */
export function pageExposure(data = {}, num = 0) {
  if (num === 0) console.log(`[pageExposure - ${data.content_type}]`, data)
  if (num >= 5) return
  setTimeout(() => {
    console.log('qlCommonHeader', window.$dtRegionalExposurePeporting && window.$dtRegionalExposurePeporting.pageExposure)
    if (window.$dtRegionalExposurePeporting && window.$dtRegionalExposurePeporting.pageExposure) {
      window.$dtRegionalExposurePeporting.pageExposure(data)
    } else {
      console.log(num++)
      pageExposure(data, num++)
    }
  }, 1000)
}

// 外部js引入
export function loadJS(url, callback) {
  var script = document.createElement('script')
  var fn = callback || function () { }
  script.type = 'text/javascript'
  // IE
  if (script.readyState) {
    script.onreadystatechange = function () {
      if (script.readyState === 'loaded' || script.readyState === 'complete') {
        script.onreadystatechange = null
        fn(url)
      }
    }
  } else {
    // 其他浏览器
    script.onload = function () {
      fn(url)
    }
  }
  script.src = url
  document.getElementsByTagName('head')[0].appendChild(script)
}

// 文本复制
export function copyToClipboard(text, successMsg = '复制成功') {
  var tempInput = document.createElement('input')
  tempInput.value = text
  tempInput.id = 'creatDom'
  document.body.appendChild(tempInput)
  tempInput.select()

  if (document.execCommand('copy')) {
    document.execCommand('copy')
    Message.success(successMsg)
    let creatDom = document.getElementById('creatDom')
    creatDom.parentNode.removeChild(creatDom)
  } else {
    console.error('复制失败')
  }
}

export function commonJump(data) {
  const { module_id = '', item_id = '' } = data

  let isHandler = false
  // 只处理小程序环境
  if (window.__wxjs_environment === 'miniprogram') {
    // 文章
    if (module_id === 8) {
      isHandler = true
      window.wx.miniProgram.navigateTo({
        url: `/pages/networkCourse/article/index?graphic_id=${item_id}`
      })
    }

    // 培养项目
    if (module_id === 10) {
      isHandler = true
      window.wx.miniProgram.navigateTo({
        url: `/pages/mooc/projectDetails/index?mooc_course_id=${item_id}`
      })
    }

    // 文档
    if (module_id === 16) {
      isHandler = true
      window.location.href = `${process.env.VUE_APP_V8_HOST_WOA}/training/mobile/word?word_id=' + ${item_id}`
      // 实际id都是正式环境id, 所以直接跳转到正式环境
      // window.location.href = `//learn.woa.com/training/mobile/word?word_id=' + ${item_id}`
    }
  }
  return isHandler
}
