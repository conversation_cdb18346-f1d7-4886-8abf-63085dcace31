<template>
    <div>
      <el-dialog
      :title='"「"+info.label_name+"」关联课程"'
      :visible.sync="visible"
      width="980px"
      custom-class="specialDataBox"
      :before-close="handleClose"
      :close-on-click-modal="click_modal">
      <div class="searchTop">
        <el-input class="inpt" size="small" v-model="keyword" clearable placeholder="请输入课程名称"></el-input>
        <el-select v-model="level" size="small" placeholder="请选择认证等级">
            <el-option
            v-for="item in courseLevel"
            :key="item.value"
            :label="item.label"
            :value="item.value">
            </el-option>
        </el-select>
        <el-select v-model="module_id" size="small" placeholder="请选择课程类型">
            <el-option
            v-for="item in courseType"
            :key="item.value"
            :label="item.label"
            :value="item.value">
            </el-option>
        </el-select>
        <div class="btns">
          <el-button size="small" icon="el-icon-refresh" @click="resetSpecailSubs">重置</el-button>
          <el-button size="small" type="primary" @click="pageFrom=1;getCourseData()">搜索</el-button>
          <el-button size="small" type="primary" @click="exportData">导出数据</el-button>
        </div>
      </div>
      <div style="margin-bottom: 24px">
        <el-table
        :data="tableData"
        style="width: 100%;border: 1px solid #ddd;border-radius: 4px;overflow: hidden">
        <el-table-column
          prop="title"
          label="课程名称"
          width="240">
          <template slot-scope="{ row }">
            <a class="courseLink" :href="row.href" target="_blank">{{ row.title }}</a>
          </template>
        </el-table-column>
        <el-table-column
          prop="origin_data.course_level"
          label="认证等级"
          width="100">
          <template slot-scope="{ row }">
            <span>{{ row.origin_data.course_level | levelName }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="module_name"
          label="类型"
          width="115">
          <!-- <template slot-scope="{ row }">
            <span> {{ row.origin_data.course_type | moduleName }}</span>
          </template> -->
        </el-table-column>
        <el-table-column
          prop="origin_data.est_dur"
          label="时长/字数"
          width="110">
          <template slot-scope="{ row }">
            <span v-if="row.origin_data.est_dur"> {{ row.origin_data.est_dur }}分钟</span>
            <span v-else-if="row.origin_data.wordNum">{{ row.origin_data.wordNum }}字</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="origin_data.creator_name"
          label="创建人"
          width="150">
          <template slot-scope="{ row }">
            <span v-if="row.origin_data.creator_name"> {{ row.origin_data.creator_name }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="created_at"
          label="创建时间"
          width="200">
        </el-table-column>
      </el-table>
      </div>
      <div>
        <el-pagination
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          background
          layout="slot,  prev, pager, next, sizes, jumper"
          style="padding: 2px 0;text-align: right;"
          :total="courseTotal">
            <span class="total">
                共 <a>{{ courseTotal }}</a> 条
            </span>
        </el-pagination>
      </div>
    </el-dialog>
    <sdc-export-data code="label_course" :isRequest.sync="isRequest" :env="env" :params='JSON.stringify(exportParams)'></sdc-export-data>
    </div>
  </template>
  
<script>
import {
  find_labelCourse_page
  // export_labelCourse_page
} from 'config/api.conf'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    info: {}
  },
  data() {
    return {
      click_modal: false,
      courseLevel: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '公司级',
          value: '1'
        },
        {
          label: 'BG级',
          value: '2'
        },
        {
          label: '部门级',
          value: '3'
        },
        {
          label: '其他',
          value: '0'
        }
      ],
      courseType: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '网络课',
          value: 1
        },
        {
          label: '培养项目',
          value: 10
        },
        {
          label: '文章',
          value: 8
        },
        {
          label: '案例',
          value: 7
        },
        {
          label: '课单',
          value: 15
        },
        {
          label: '行家',
          value: 6
        },
        {
          label: '直播',
          value: 3
        },
        {
          label: '面授课',
          value: 2
        },
        {
          label: '活动',
          value: 4
        },
        {
          label: '文档',
          value: 16
        },
        {
          label: 'k吧文章',
          value: 20
        }
      ],
      keyword: '',
      level: '',
      module_id: '',
      labelInfo: {},
      pageFrom: 1,
      pageSize: 10,
      courseTotal: 0,
      tableData: [],
      isRequest: false,
      exportParams: {},
      env: process.env.NODE_ENV
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.getCourseData()
        }
      }
    }
  },
  methods: {
    getCourseData() {
      let params = {
        'searchType': 'label',
        'sortBy': 'created_at',
        'sortOrder': 'desc',
        'pageFrom': this.pageFrom,
        'pageSize': this.pageSize,
        'filters': {
          'module_id': this.module_id ? ['=', this.module_id] : ['in', 1, 2, 3, 4, 6, 7, 8, 10, 15, 16, 20],
          'label_system_ids': ['=', this.info.label_id],
          'title.keyword': this.keyword ? ['like', '*' + this.keyword + '*'] : null
        }
      }
      if (this.level) {
        params.filters.course_level = ['=', this.level]
      }
      find_labelCourse_page(params).then(res => {
        console.log(res)
        this.courseTotal = res.total
        this.tableData = res.content
      })
    },
    resetSpecailSubs() {
      this.module_id = ''
      this.keyword = ''
      this.level = ''
      this.pageFrom = 1
      this.getCourseData()
    },
    exportData() {
      this.exportParams = {
        'searchType': 'label',
        'sortBy': 'created_at',
        'sortOrder': 'desc',
        'filters': {
          'module_id': this.module_id ? ['=', this.module_id] : ['in', 1, 2, 3, 4, 6, 7, 8, 10, 15, 16, 20],
          'label_system_ids': ['=', this.info.label_id],
          'title.keyword': this.keyword ? ['like', '*' + this.keyword + '*'] : null,
          'course_level': this.level ? ['=', this.level] : ['in', 0, 1, 2, 3]
        }
      }
      this.isRequest = true
      // export_labelCourse_page(params).then(res => {
      //   this.$message({
      //     type: 'success',
      //     message: '导出成功'
      //   })
      // })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.pageFrom = 1
      this.getCourseData()
    },
    handleCurrentChange(val) {
      this.pageFrom = val
      this.getCourseData()
    },
    handleClose() {
      this.module_id = ''
      this.keyword = ''
      this.level = ''
      this.pageFrom = 1
      this.tableData = []
      this.courseTotal = 0
      this.$emit('update:visible', false)
    }
  },
  filters: {
    levelName(val) {
      switch (val) {
        case 1:
          return '公司级'
        case 2:
          return 'BG级'
        case 3:
          return '部门级'
        case 0:
          return '其他'
        default:
          return '-'
      }
    },
    moduleName(val) {
      if (!val) return '-'
      let name = val + ''
      switch (name) {
        case '1':
          return '网络课'
        case '2':
          return '面授课'
        case '3':
          return '直播'
        case '4':
          return '活动'
        case '6':
          return '行家'
        case '7':
          return '案例'
        case '8':
          return '文章'
        case '10':
          return '培养项目'
        case '15':
          return '课单'
        case '16':
          return '文档'
        case '20':
          return 'k吧文章'
        case 'Video-ppt':
          return '视频'
        case 'Video':
          return '视频'
        case 'Audio-ppt':
          return '音频'
        case 'Audio':
          return '音频'
        default:
          return name || '-'
      }
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.specialDataBox.el-dialog{
  font-family: "PingFang SC";
  border-radius: 8px;
  overflow: hidden;
  .el-dialog__header{
    padding: 14px 32px 16px;
    border-bottom: 1px solid #EFEFEF;
    .el-dialog__title{
      color: #000000;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
    }
    .el-dialog__headerbtn{
      top: 18px;
      right: 31px;
    }
  }
  .el-dialog__body{
    padding: 15px 32px;
    .searchTop{
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      .inpt{
        height: 32px;
        width: 180px;
        .el-input{
          border: 1px solid #DCDCDC;
          &::placeholder{
            color: #00000066;
          }
        }
        .el-input.is-active .el-input__inner, .el-input__inner:focus{
          border-color:#0052D9
        }
      }
      .el-select{
        width: 180px;
        margin-left: 16px;
      }
      .btns{
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }
      .el-button{
        height: 32px;
        margin-left: 16px;
        border-radius: 3px;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
      }
      .el-button.el-button--default{
        background: #fff;
        width: 80px;
        border: 1px solid #0052D9;
        color: #0052D9;
      }
      .el-button.el-button--default:hover{
        background: none;
      }
      .el-button.el-button--primary{
        width: 80px;
        background: #0052D9;
        color: #ffffff;
        border-color: #0052D9;
      }
      .el-button.el-button--primary:last-child{
        width: 88px;
      }
    }
    .el-table__row td,.has-gutter th{
      padding-top: 15px;
      padding-bottom: 15px;
      div{
        padding-right: 12px;
        padding-left: 28px;
        color: #000000e6;
        font-size: 14px;
        line-height: 20px;
      }
    }
    .has-gutter tr{
      th{
        background: #F4F8FF;
        font-size: 14px;
        font-weight: 500;
        font-family: "PingFang SC";
        div{
          color: #00000099;
        }
      }
    }
    .el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li{
        margin: 0;
    }
    .el-pagination .el-pager .number{
        line-height: 22px;
    }
    .el-pagination.is-background .btn-next, .el-pagination.is-background .btn-prev, .el-pagination.is-background .el-pager li{
        background: #fff;
    }
    .el-pagination {
        height: 24px;
        .total{
            height: 24px;
            display: inline-block;
            margin-right: 21px;
            color: #333333;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 24px;
            a{
                color: #3464e0;
            }
        }
        .el-pagination__total{
            font-size: 14px;
            color: #333333;
        }
        .el-pagination__sizes{
            height: 24px;
            color: #333333;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            .el-input__inner{
                color: #333333;
                font-family: "PingFang SC";
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
            }
        }
        .el-pagination__jump {
            height: 24px;
            line-height: 24px;
            font-size: 12px;
            .el-pagination__editor.el-input{
                height: 24px;
                width: 60px;
                padding: 0;
                margin: 0 5px;
            }
        }
    }
  }
}
.courseLink {
    display: -webkit-box;
    width: 190px;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    color: #0052d9;
    text-overflow: ellipsis;
    font-family: "PingFang SC";
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: normal;
}
</style>
