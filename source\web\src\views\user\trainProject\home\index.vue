<template>
  <div id="home" ref="home">
    <!-- 顶部轮播图 -->
    <div class="banner" :class="{ 'banner-bg': !this.bgColor }" :style="{ background: this.bgColor }">
      <el-carousel
      :interval="3000"
      :arrow="bannerList.length > 1 ? 'always' : 'never'"
      :indicator-position="bannerList.length > 1 ? undefined : 'none'"
      class="banner-style"
      height="400px"
      :autoplay="bannerList.length > 1"
      @change="onBannerChange"
      >
        <el-carousel-item v-for="item in bannerList" :key="item.id">
          <el-image 
          :src="item.img_content_id ? item.img_content_id : banner" 
          fit="fill"
          @click="toBannerLink(item.link_url)"
          :dt-eid="dtBannerEid(item)"
          :dt-remark="dtBannerRemak(item)"
          :dt-areaid="dtBannerAreaid(item)"  
          >
            <div class="image-slot" slot="placeholder">
              <i class="el-icon-loading"></i>
            </div>
            <div class="banner-error-cover" slot="error">
              <img :src="banner" alt="" />
            </div>
          </el-image>
        </el-carousel-item>
      </el-carousel>
    </div>

    <div class="container clearfix">
      <div class="search-content">
        <div class="search-box">
          <!-- 选择分类 -->
          <div class="classify">
            <div class="tag classify-parent">
              <div class="label">{{ $langue('Mooc_Home_Search_LabelType', { defaultText: '标签分类' }) }}</div>
              <div class="tag-box">
                <span 
                v-for="(item, index) in classifyList"
                :key="item.category_id"
                :class="['tag-item', { 'active-tag': activeClassify === item.category_id }]"
                @click="onClassify(item.category_id, index)"
                :dt-eid="dtTagEid(item)"
                :dt-remark="dtTagRemak(item)"
                :dt-areaid="dtTagAreaid(item)"  
                >
                  {{ item.category_name }}
                </span>
              </div>
            </div>
            <!-- 子级分类 -->
            <div class="classify-child" v-if="activeClassify">
              <div class="classify-child-tag">
                <span 
                v-for="e in classifyChild" 
                :key="e.category_id"
                :class="['tag-item', { 'active-tag': activeChildClassify === e.category_id }]"
                @click="onChildClassify(e.category_id)"
                :dt-eid="dtTagEid(e)"
                :dt-remark="dtTagRemak(e)"
                :dt-areaid="dtTagAreaid(e)"  
                >
                  {{ e.category_name }}
                </span>
              </div>
            </div>
          </div>
          <!-- 选择标签 -->
          <div class="tag" v-if="tagList.length > 1 && activeChildClassify">
            <div class="label">{{ $langue('Mooc_ProjectDetail_Desc_Label', { defaultText: '项目标签' }) }}</div>
            <div class="tag-box">
              <span v-for="item in tagList" :key="item.label_id"
                :class="['tag-item', { 'active-tag': activeTag === item.label_id }]" @click="onTagChange(item.label_id)">
                {{ item.label_name }}
              </span>
              <span 
                class="expand" 
                v-if="tagList.length < tagTotal" 
                @click="searchMore"
                :dt-eid="dtTagEid('更多')"
                :dt-remark="dtTagRemak('更多')"
                >更多 <i class="el-icon-arrow-down"></i>
              </span>
            </div>
          </div>
          <!-- 选择查询条件 -->
          <div class="sort">
            <div class="label">{{ $langue('Mooc_Home_Search_Sort', { defaultText: '排序' }) }}</div>
            <div class="tag-box">
              <span 
              v-for="item in sortList" 
              :key="item.value"
              :class="['tag-item', { 'active-tag': order_by === item.value }]" 
              @click="onSortChange(item.value)"
              :dt-eid="dtBannerEid(item, 'search')"
              :dt-remark="dtBannerRemak(item, 'search')"
              :dt-areaid="dtBannerAreaid(item, 'search')"  
              >
                {{ item.label }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <!-- 课单列表 -->
      <div class="course-list clearfix" v-if="courseListData && courseListData.length > 0">
        <div 
        v-for="(item, index) in courseListData" 
        :key="index"
        :class="['course-item', { 'course-item-last': index % 4 === 3 }]" 
        @click="toListDetail(item.mooc_course_id)"
        :dt-eid="dtTagEid(item, 'detail')"
        :dt-remark="dtTagRemak(item, 'detail')"
        :dt-areaid="dtTagAreaid(item, 'detail')"  
        >
          <img class="geek-time" v-if="showOutcourseIcon(item)" :src="typeImg(item)" alt="">
          <div v-if="item.serial_type === 2 && !showOutcourseIcon(item)" :class="[moocLang === 'en-us' ? 'en-update-status' : 'update-status', 'common-update-status']"></div>
          <div class="course-tag">
            <span class="excellent-tag" v-if="item.excellent_status === 1">{{ $langue('Mooc_Home_ListItems_Excellent', { defaultText: '精品' }) }}</span>
            <!-- <span class="excellent-tag jike-time" v-if="item.resource_from === 'geekBang'">极客时间</span> -->
          </div>
          <div class="part-status" v-if="item.course_status_name"><span>{{ $langue('Mooc_Home_ListItems_Joined', { defaultText: '已参与' }) }}</span></div>
          <div class="course-total"><span>{{ $langue('Mooc_Home_ListItems_TotalTasks', {count: item.task_count || 0, defaultText: `共${item.task_count}项任务` }) }}</span></div>
          <el-image 
          lazy 
          fit="fill" 
          :src="item.url"
          class="course-cover"
          >
            <div class="image-slot" slot="placeholder">
              <i class="el-icon-loading"></i>
            </div>
            <div class="error-cover" slot="error">
              <img :src="require('@/assets/mooc-img/default_bg_img.png')" alt="" />
            </div>
          </el-image>
          <el-tooltip effect="light" :content="item.course_title" placement="top-start" :disabled="!item.isOverflow">
            <div class="curse-title overflow-l2" @mouseover="titleOver($event, index)">
              {{ item.course_title }}
            </div>
          </el-tooltip>
          <div class="course-data">
            <!-- <span v-if="item.scorer_count < 20" class="study-num">{{ $langue('Mooc_ProjectDetail_BasicInfo_LowerScorer', { defaultText: '评分人数不足' }) }}</span> -->
            <!-- <span v-else class="rate-box">
              <el-rate class="rate-num" v-model="item.startScore" disabled :colors="{ 5: '#D35A21' }">
              </el-rate>
              <span>{{ item.score }}</span>
            </span> -->
            <span class="study-num" v-show="item.show_join_count !== false">{{ `${item.user_count}${$langue('Mooc_Home_ListItems_JoinStudy', { defaultText: '人参与学习' })}` }}</span>
          </div>
        </div>
      </div>
      <!-- 加载中或者有数据 -->
      <div class="bottom-text" v-show="!isEmpty">
        <i class="el-icon-loading" v-show="!isFinished"></i>
        {{ isFinished ? '已经到底部了~' :  $langue('Mooc_Common_Alert_Loading', { defaultText: '正在加载中' }) }}
      </div>
      <!-- 无数据 -->
      <div class="bottom-text" v-show="isEmpty">
        <img class="empty-img" :src="empty" alt="" />
        <div class="empty-text">{{ $langue('Mooc_Common_NoData', { defaultText: '暂无数据' }) }}</div>
      </div>
    </div>

    <!-- 我的培养项目悬浮按钮 -->
    <div class="my-mooc-btn">
      <el-popover
        ref="popover"
        placement="left" 
        width="300" 
        trigger="hover"
        popper-class="project-detail-popper"
        @show="getStatisticsData"
        >
        <div class="popover-card-content">
          <div class="top-content">
            <span class="label">{{ $langue('Mooc_Home_MyProject_MyProjects', { defaultText: '我的培养项目' }) }}</span>
            <el-button type="text" @click="trainProjectDialog=true">{{ $langue('Mooc_Home_MyProject_ProjectDetal', { defaultText: '详情' }) }}<i class="el-icon-arrow-right"></i></el-button>
          </div>
          <div class="mid-content">
            <div>
              <p class="mid-label" @click="trainProjectDialog=true">{{ statisticsData.course_count || 0 }}</p>
              <p>{{ $langue('Mooc_Home_MyProject_JoinProjects', { defaultText: '参与项目' }) }}</p>
            </div>
            <div class="line"></div>
            <div>
              <p class="mid-label" @click="trainProjectDialog=true">{{ statisticsData.finish_count || 0 }}</p>
              <p>{{ $langue('Mooc_Home_MyProject_FinishedProject', { defaultText: '完成项目' }) }}</p>
            </div>
          </div>
          <div class="bottom-content" v-if="statisticsData.last_view_title">
            <p class="recent-study">{{ $langue('Mooc_Home_MyProject_LearnRecently', { defaultText: '最近学习' }) }}</p>
            <p class="recent-title" @click="toCourseDetail">{{ statisticsData.last_view_title }}</p>
            <div class="bottom-progress">
              <span :class="[{'no-start-tag': statisticsData.course_status === 1}, {'finsh-tag': statisticsData.course_status === 4}, {'no-result': statisticsData.course_status === 3}, 'tag']">{{ $langue(statusInfo[statisticsData.course_status], { defaultText: statusInfoZNCN[statisticsData.course_status] }) }}</span>
              <span class="main-progress">{{ $langue('Mooc_ProjectDetail_TrainingProgress_RequiredProgress', { defaultText: '应学进度' }) }}：{{ `${statisticsData.required_finish || 0}/${statisticsData.required_count || 0}` }}</span>
            </div>
          </div>
        </div>
        <div slot="reference">
          <div class="btn-box">
            <img class="logo-icon" :src="require('@/assets/mooc-img/course.png')" />
            <div class="btn-text">培养<br>项目</div>
          </div>
        </div>
      </el-popover>
    </div>

    <!-- 培养项目弹窗 -->
    <train-Project v-if="trainProjectDialog" :visible.sync="trainProjectDialog"></train-Project>
  </div>
</template>

<script>
// import { transformUnit, getAvatar } from 'utils/tools'
import { getClassifyTag, getTrainList, getBannerList, getTrainClassifyTree, getStatistics } from '@/config/mooc.api.conf.js'
import env from 'config/env.conf.js'
import { mapState } from 'vuex'
import { pcCoverLogo } from '@/utils/outsourcedCourseMap.js'
import trainProject from './trainProject.vue'

const envName = env[process.env.NODE_ENV]
// 节流阀
let flag = false
const statusInfo = {
  1: 'Mooc_ProjectDetail_TrainingProgress_NotStarted', // 未开始
  2: 'Mooc_ProjectDetail_TrainingProgress_InProgress', // 进行中
  3: 'Mooc_ProjectDetail_TrainingProgress_Delayed', // 已逾期
  4: 'Mooc_ProjectDetail_TrainingProgress_Finished' // 已完成
}
const statusInfoZNCN = {
  1: '未开始', // 未开始
  2: '进行中', // 进行中
  3: '已逾期', // 已逾期
  4: '已完成' // 已完成
}

export default {
  name: 'home',
  components: {
    trainProject
  },
  data() {
    return {
      // 我的培养项目
      trainProjectDialog: false,
      statisticsData: {},
      statusInfo,
      statusInfoZNCN,

      bannerList: [],
      bgColor: '',
      activeClassify: '',
      activeChildClassify: '',
      activeTag: '',
      order_by: '1',
      banner: require('@/assets/mooc-img/banner.png'),
      empty: require('@/assets/img/empty.png'),
      isEmpty: true,
      courseListData: [],
      // 课单列表查询入参
      params: {
        page_no: 1,
        page_size: 10,
        category_id: '', // 分类id
        label_id: '', // 标签id
        order_by: '' // 排序id 1-最新发布，2-最多参与，3-评分最高，4-点赞最多
      },
      isFinished: false,
      total: 0,
      // transformUnit,
      // getAvatar,
      isAddEvent: false, // 判断是否添加事件， 已添加事件，不再重复添加
      rateValue: 0,
      page_no: 1,
      tagTotal: 0,
      classifyList: [
        { 
          category_name: '全部', 
          category_id: '', 
          sub_categories: [{ category_name: '全部', category_id: '' }] 
        }
      ],
      classifyChild: [
        { category_name: '全部', category_id: '' }
      ],
      tagList: [
        { label_name: '全部', label_id: '' }
      ]
    }
  },
  computed: {
    ...mapState(['moocLang']),
    sortList() {
      return [
        { label: this.$langue('Mooc_Home_Search_New', { defaultText: '最新发布' }), value: '1' },
        { label: this.$langue('Mooc_Home_Search_Hot', { defaultText: '最多参与' }), value: '2' },
        { label: this.$langue('Mooc_Home_Search_HighScore', { defaultText: '评分最高' }), value: '3' },
        { label: this.$langue('Mooc_Home_Search_HighPraise', { defaultText: '点赞最多' }), value: '4' }
      ]
    },
    // 埋点
    dtBannerEid() {
      return (row, type) => {
        if (type === 'search') {
          return `element_${row.value}_${row.label}_排序`
        }
        return `element_${row.id}_${row.banner_name}_banner`
      }
    },
    dtBannerRemak() {
      return (row, type) => {
        if (type === 'search') {
          return JSON.stringify({
            page: document.title,
            page_type: '项目首页', 
            container: '排序',
            click_type: 'button',
            content_type: '',
            content_id: row.value,
            content_name: row.label,
            terminal: 'PC',
            act_type: ''
          })
        }
        return JSON.stringify({
          page: document.title,
          page_type: '项目首页', 
          container: 'banner',
          click_type: 'data',
          content_type: '',
          content_id: row.id,
          content_name: row.banner_name,
          terminal: 'PC',
          act_type: ''
        })
      }
    },
    dtBannerAreaid() {
      return (row, type) => {
        if (type === 'search') { 
          return `area_${row.value}_${row.label}_排序`
        }
        return `area_${row.id}_${row.banner_name}_banner`
      }
    },
    dtTagEid() {
      return (row, type) => {
        if (type === 'detail') { // 详情
          return `element_${row.mooc_course_id}_列表`
        }
        if (typeof row === 'string') { // 标签分类-更多
          return `element_${row}_标签分类`
        }
        return `element_${row.category_id}_标签分类`
      }
    },
    
    dtTagRemak() {
      return (row, type) => {
        if (type === 'detail') { // 详情
          return JSON.stringify({
            page: document.title,
            page_type: '项目首页', 
            container: '列表',
            click_type: 'data',
            content_type: '',
            content_id: row.mooc_course_id,
            content_name: row.course_title,
            terminal: 'PC',
            act_type: 11
          })
        }
        if (typeof row === 'string') {
          return JSON.stringify({
            page: document.title,
            page_type: '项目首页', 
            container: '标签分类',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: row,
            terminal: 'PC',
            act_type: ''
          })
        }
        return JSON.stringify({
          page: document.title,
          page_type: '项目首页', 
          container: '标签分类',
          click_type: 'data',
          content_type: '',
          content_id: row.category_id,
          content_name: row.category_name,
          terminal: 'PC',
          act_type: ''
        })
      }
    },
    dtTagAreaid() {
      return (row, type) => {
        if (type === 'detail') { // 详情
          return `area_${row.mooc_course_id}_列表`
        }
        return `area_${row.category_id}_标签分类`
      }
    }
  },
  watch: {
    $route: {
      handler(to) {
        // 监听路由变化，显示我的培养项目弹窗
        if (to.name === 'home' && to.query?.to === 'myItem') {
          this.trainProjectDialog = true
        }
      },
      immediate: true
    }
  },
  created() {
    this.getBannerList()
    this.getClassify()
    this.getCourse()
  },
  beforeDestroy() {
    if (this.$refs.home) {
      this.$refs.home.removeEventListener('scroll', this.scrollEvent)
    }
  },
  methods: {
    typeImg(item) {
      return pcCoverLogo[item.resource_from] || pcCoverLogo['geekBang']
    },
    // 是否显示封面左上角类型图标
    showOutcourseIcon(item) {
      let coverLogoArray = Object.keys(pcCoverLogo) || []
      return coverLogoArray.includes(item.resource_from)
    },
    // 获取banner，暂无数量限制，数量就先写死100吧
    getBannerList() {
      getBannerList({
        act_type: '11',
        current: 1,
        size: 100
      }).then((res) => {
        this.bannerList = (res || []).map((e) => {
          const img_content_id = `${envName.contentcenter}content-center/api/v1/content/imgage/${e.img_content_id}/preview`
          return {
            ...e,
            img_content_id
          }
        })
        if (this.bannerList.length > 0) {
          this.bgColor = this.bannerList[0].bg_color
          this.$store.commit('setBgColor', this.bgColor)
        }
      })
    },
    // banner切换改变左右背景颜色
    onBannerChange(index) {
      this.bgColor = this.bannerList[index].bg_color
      this.$store.commit('setBgColor', this.bgColor)
    },
    // 点击banner跳转
    toBannerLink(url) {
      window.open(url)
    },
    // 获取分类树
    getClassify() {
      getTrainClassifyTree().then((res) => {
        const classifyTree = res.filter((v) => v.category_name !== '其他')
        this.classifyList.push(...classifyTree)
        this.classifyChild.push(...this.classifyList[0].sub_categories)
        this.activeClassify = this.classifyList?.length && this.classifyList[0].category_id
      })
    },
    // 获取分类下的标签
    getTag() {
      this.activeTag = '' // 默认标签选中全部
      const category_id = this.activeChildClassify ? this.activeChildClassify : this.activeClassify
      const params = {
        page_no: this.page_no,
        page_size: 20,
        category_id
      }
      getClassifyTag(params).then((res) => {
        this.tagList.push(...res.records)
        this.tagTotal = res.total
      })
    },
    searchMore() {
      this.page_no++
      this.getTag()
    },
    // 滚动加载事件
    scrollEvent() {
      const home = document.getElementById('home')
      if (flag || this.isFinished) return
      // scrollTop就是触发滚轮事件时滚轮的高度
      const scrollTop = home.scrollTop
      // 变量clientHeight是可视区的高度
      const clientHeight = home.clientHeight
      // 变量scrollHeight是滚动条的总高度
      const scrollHeight = home.scrollHeight
      // 判断滚动条是否到底部
      if (scrollTop + clientHeight >= scrollHeight - 40) {
        flag = true
        this.params.page_no++
        // 监听滚动事件，设置500毫秒节流
        setTimeout(() => {
          this.getMoreCourse()
        }, 500)
      }
    },
    // 获取课单列表
    getCourse() {
      flag = true
      this.params.page_no = 1
      this.params.category_id = this.activeChildClassify ? this.activeChildClassify : this.activeClassify
      this.isFinished = false
      this.isEmpty = false
      this.courseListData = []
      getTrainList(this.params)
        .then((res) => {
          this.courseListData = this.formatList(res.records)
          this.total = res.total
          this.isFinished = this.courseListData.length >= this.total
          if (this.courseListData.length === 0) {
            this.isEmpty = true
          }
          flag = false
          if (!this.isAddEvent) {
            this.$nextTick(() => {
              // 已添加事件，不再重复添加
              this.isAddEvent = true
              // 监听滚动事件，滚动到底部加载课单
              this.$refs.home.addEventListener('scroll', this.scrollEvent)
            })
          }
        })
        .catch(() => {
          this.isFinished = true
        })
    },
    // 获取更多课单
    getMoreCourse() {
      getTrainList(this.params)
        .then((res) => {
          this.courseListData = this.courseListData.concat(this.formatList(res.records))
          this.total = res.total
          this.isFinished = this.courseListData.length >= this.total
          flag = false
        })
        .catch(() => {
          this.isFinished = true
        })
    },
    // 分类
    onClassify(category_id, index) {
      this.activeClassify = category_id
      this.classifyChild = [{ category_name: this.$langue('Mooc_Common_JoinType_All', { defaultText: '全部' }), category_id: '' }]
      this.params.label_id = '' // 每次都清空标签
      this.classifyChild.push(...this.classifyList[index].sub_categories)
      this.activeChildClassify = '' // 子级默认全部
      this.getCourse()
    },
    // 子级分类
    onChildClassify(category_id) {
      this.activeChildClassify = category_id
      this.tagList = [{ label_name: this.$langue('Mooc_Common_JoinType_All', { defaultText: '全部' }), label_id: '' }]
      this.params.label_id = '' // 每次都清空标签
      this.page_no = 1
      this.getTag()
      this.getCourse()
    },
    // 标签切换
    onTagChange(val) {
      this.activeTag = val
      this.params.label_id = val
      this.getCourse()
    },
    // 排序条件切换
    onSortChange(val) {
      this.order_by = val
      this.params.order_by = val
      this.getCourse()
    },
    // 查看课单详情
    toListDetail(mooc_course_id) {
      const { href } = this.$router.resolve({
        name: 'projectDetail',
        query: {
          mooc_course_id
        }
      })
      window.open(href, '_blank')
    },
    formatList(list) {
      return (list || []).map((v) => {
        let url = ''
        // if (v.cover_image_storage_type === 'zhihui' || v.cover_image_storage_type === 'other' || v.cover_image_storage_type === 'geekBang') {
        if (['zhihui', 'other', 'geekBang', 'imooc', 'sanjieke'].includes(v.cover_image_storage_type)) {
          url = v.cover_image
        } else {
          url = `${envName.contentcenter}content-center/api/v1/content/imgage/${v.cover_image_id}/preview`
        }
        let startScore = ''
        v.score = Number((v.score).toFixed(1))
        const value = Number(v.score.toString().split('.')[0])
        // 数据失真处理
        const compareNum = Number((v.score - value).toFixed(1))
        if (compareNum < 0.3) {
          startScore = Math.floor(v.score)
        } else if ((compareNum < 0.8) && (compareNum >= 0.3)) {
          startScore = Math.floor(v.score) + 0.5
        } else {
          startScore = Math.round(v.score)
        }
        return {
          ...v,
          url,
          score: v.score,
          startScore
        }
      })
    },
    // 判断标题是否超出两行
    titleOver(e, index) {
      const target = e.target
      this.$set(
        this.courseListData[index],
        'isOverflow',
        target.scrollHeight > target.clientHeight
      )
    },
    getStatisticsData() {
      getStatistics().then((res) => {
        this.statisticsData = res
        this.$nextTick(() => {
          this.$refs.popover.updatePopper()
        })
      })
    },
    // 跳转到详情
    toCourseDetail() {
      const { last_view_id } = this.statisticsData
      const { href } = this.$router.resolve({
        name: 'projectDetail',
        query: {
          mooc_course_id: last_view_id
        }
      })
      window.open(href, '_blank')
    }
  }
}
</script>
<style lang="less" scoped>
#content{
  height: calc(100% - 60px);
}
.project-detail-popper {
  padding: 16px;
  top: 557px !important;
  .mid-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .top-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 32px;
    margin-bottom: 8px;
    .label {
      color: #000000e6;
      font-size: 14px;
      font-weight: bold;
    }
    .el-button {
      padding: unset
    }
  }
  .mid-content {
    height: 70px;
    border-radius: 3px;
    opacity: 1;
    background: #fafafaff;
    padding: 12px 45px;
    .mid-label {
      font-size: 16px;
      font-weight: bold;
      color: #000000e6;
      height: 24px;
      text-align: center;
      cursor: pointer;
    }
    .line {
      width: 1px;
      height: 24px;
      background: #EEEEEE
    }
  }
  .bottom-content {
    margin-top: 17px;
    font-size: 12px;
    .recent-study {
      color: #000000e6;
      font-weight: bold;
      height: 20px;
      margin-bottom: 12px;
    }
    .recent-title {
      cursor: pointer;
    }
    .bottom-progress {
      color: #000000e6;
      margin-top: 8px;
      .tag {
        border-radius: 2px;
        padding: 0px 8px;
        background: #ebeffcff;
        color: #0052d9ff;
        display: inline-block;
        height: 20px;
        line-height: 20px;
      }
      .no-start-tag {
        background: #fdf6ecff;
        color: #ff7548ff;
      }
      .finsh-tag {
        background: #ccf2e2ff;
        color: #00b368ff;
      }
      .no-result {
        background: #fdeceeff;
        color: #e34d59ff;
      }
      .main-progress {
        margin-left: 12px;
      }
    }
  }
}
</style>
<style lang="less" scoped>

#home {
  background-color: #f0f4fa;
  height: 100%;
  overflow-y: auto;

  .banner {
    // width: 1440px;
    margin: 0 auto;
    :deep(.banner-style) {
      width: 1200px;
      margin: 0 auto;

      .el-carousel__container {
        cursor: pointer;

        .el-carousel__item {
          line-height: 240px;
          text-align: center;

          .el-image {
            width: 100%;
            height: 100%;
          }
        }

        .el-carousel__arrow {
          color: #fff;
          background: rgba(216, 216, 216, 0.39);
          font-size: 18px;

          i {
            font-weight: 700;
          }
        }

        .el-carousel__arrow--left {
          left: 40px;
        }

        .el-carousel__arrow--right {
          right: 40px;
        }
      }

      .el-carousel__indicators {

        .el-carousel__indicator {
          padding: 12px 6px;

          .el-carousel__button {
            height: 4px;
            border-radius: 2px;
          }
        }
      }
      .banner-error-cover img {
        width: 1200px;
        height: 400px;
      }
    }
  }

  .banner-bg {
    // background: url(~@/assets/img/banner.png) no-repeat center/cover;
  }

  .container {
    // padding: 28px 0 24px;
    // margin-bottom: 20px;
    padding-bottom: 28px;
    min-height: calc(100% - 394px);
    background-color: #fff;

    .search-content {
      padding: 30px 0px;
      border-bottom: 1px solid #eeeeeeff;

      .search-box {
        margin: 0 auto;
        width: 1200px;
      }
    }

    .tag,
    .sort {
      .label {
        // padding: 3px 20px 0 0;
        font-size: 16px;
        color: #333333ff;
        font-weight: bold;
        line-height: 24px;
        margin-right: 32px;
      }

      .tag-box {
        flex: 1;

        .tag-item {
          float: left;
          padding: 1px 12px;
          margin-right: 16px;
          border: 1px solid #eeeeeeff;
          height: 24px;
          line-height: 20px;
          border-radius: 52px;
          color: #00000099;
          cursor: pointer;
          background: #ffffffff;
          margin-bottom: 20px;

          &:last-of-type {
            margin-right: 0;
          }
        }

        .active-tag {
          background: #ebeffcff;
          color: #0052d9;
          border-radius: 52px;
          border: unset;
        }
      }
    }

    .tag {
      display: flex;
      // margin-top: 20px;
      // margin-left: 23px;

      .expand {
        display: inline-block;
        margin-bottom: 16px;
        width: 50px;
        line-height: 22px;
        color: #0052d9;
        float: left;
        cursor: pointer;

        .el-icon-arrow-down {
          font-weight: 900;
        }
      }
    }

    .classify {
      .classify-child {
        margin-left: 64px;
        margin-bottom: 20px;

        .classify-child-tag {
          // height: 30px;
          line-height: 30px;
          border-radius: 2px;
          background: #fafafaff;
          padding: 0 16px;

          // width: 555px;
          .tag-item {
            margin-right: 28px;
            color: #00000099;
            height: 22px;
            line-height: 22px;
            cursor: pointer;
          }

          .active-tag {
            color: #0052d9ff;
          }
        }
      }
    }

    .sort {
      display: flex;

      .tag-item {
        margin-bottom: unset !important;
      }
    }

    .course-list {
      width: 1200px;
      margin: 26px auto 0;

      .course-item {
        position: relative;
        width: 285px;
        float: left;
        margin: 0 20px 28px 0;
        cursor: pointer;
        .common-update-status {
          position: absolute;
          top: 0;
          z-index: 3;
        }
        .update-status {
          background: url('~@/assets/mooc-img/updateing.png') no-repeat center/cover;
          width: 58px;
          height: 20px;
        }
        .en-update-status {
          background: url('~@/assets/mooc-img/updateingEn.png') no-repeat center/cover;
          width: 71px;
          height: 20px;
        }
        .geek-time {
          // width: 60px;
          height: 25px;
          position: absolute;
          left: 0;
          top: 0;
          z-index: 1;
        }
        .course-tag {
          position: absolute;
          right: 8px;
          top: 8px;
          z-index: 2;

          .official-tag,
          .excellent-tag {
            display: inline-block;
            padding: 0 11px;
            font-size: 12px;
            height: 18px;
            line-height: 18px;
            border-radius: 10px;
            color: #fff;
          }

          .official-tag {
            background-color: #1374ff;
          }

          .excellent-tag {
            margin-left: 15px;
            background: #b20000;
          }
          .jike-time {
            background: #F2995F;
          }
        }

        .part-status {
          position: absolute;
          left: 12px;
          top: 150px;
          z-index: 2;
          font-size: 12px;
          color: #fff;
          line-height: 28px;
          text-align: center;
          padding: 0 8px;
          height: 28px;
          border-radius: 40px;
          opacity: 1;
          background: #00000066;
        }

        .course-total {
          position: absolute;
          right: 12px;
          top: 150px;
          z-index: 2;
          font-size: 12px;
          color: #fff;
          line-height: 28px;
          text-align: center;
          padding: 0 8px;
          height: 28px;
          border-radius: 40px;
          opacity: 1;
          background: #00000066;
        }

        .course-cover {
          width: 285px;
          height: 190px;
          line-height: 190px;
          text-align: center;
          color: #666;
          border-radius: 4px;

          .error-cover img {
            width: 285px;
            height: 190px;
          }
        }

        .curse-title {
          height: 44px;
          line-height: 22px;
          font-family: @PingFangSC;
          font-weight: bold;
          color: #000000e6;
          margin-top: 10px;
          margin-bottom: 8px;
        }

        .course-data {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 20px;
          line-height: 20px;

          .rate-box {
            display: flex;
            align-items: center;
            color: #D35A21;
            font-size: 12px
          }

          :deep(.el-rate) {
            height: 16px;

            .el-rate__icon {
              font-size: 16px;
            }

            .el-rate__text {
              font-size: 12px;
            }
          }

          .study-num {
            color: #00000099;
            font-size: 12px;
          }
        }
      }

      .course-item-last {
        margin-right: 0;
      }
    }

    .bottom-text {
      padding-top: 4px;
      color: #999;
      text-align: center;

      .empty-img {
        margin-bottom: 20px;
        width: 178px;
        height: 130px;
      }
    }
  }

  .my-mooc-btn {
    width: 46px;
    position: fixed;
    top: 636px;
    transform: translateX(696px);
    right: 50%;
    .btn-box {
      padding: 12px 0px;
      border-radius: 22px;
      background-color: #fff;
      box-shadow: 0 0 16px 0 #eaeaeacc;
      cursor: pointer;
      .logo-icon {
        width: 24px;
        height: 24px;
        margin: 0 auto 4px;
        display: block;
      }
      .btn-text {
        font-size: 14px;
        text-align: center;
        color: #666666;
        font-family: "PingFang SC";
        font-size: 12px;
        line-height: 14px;
      }
    }
  }
}
</style>
