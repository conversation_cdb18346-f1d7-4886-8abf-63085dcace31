<template>
  <div class="task-detail-container task-error-container">
    <div class="task-error-content">
      <img class="error-img" v-if="params.inaccessible" src="@/assets/mooc-img/page-inaccessible.png" alt="无法访问">
      <img class="error-img" v-else-if="params.noPermission" src="@/assets/mooc-img/page-noPermission.png" alt="无访问权限">
      <div class="tips">{{ params.tips }}</div>
      <p v-if="params.timeText">{{ params.timeText }}</p>
      <div v-if="params.mobileViewUrl" class="mob-box">
        <img :src="params.mobileViewUrl" />
        <p>{{ $langue('Mooc_Common_Authority_ViewByWechat', { defaultText: '请使用微信/企业微信扫码查看任务' }) }}</p>
      </div>
      <!-- 有权限但未报名 -->
      <div class="application-btn" v-if="!params.noPermission && !params.register && params.isShowJoinBtn" >
        <el-button class="join-btn" type="primary" @click="handleJoin">立即报名加入项目</el-button>
      </div>
      <p v-if="!params.noPermission && params.isShowBack" class="back" @click="backHome">{{ $langue('Mooc_TaskDetail_Navigation_ReturnProjectHomeNew', { defaultText: '返回培养项目首页' }) }}</p>
      <div v-if="params.isConcatAdmin">
        <p v-if="params.isConcatAdmin !== 3">{{ params.isConcatAdmin | concatAdminText }}</p>
        <div class="contact-admin" v-if="params.concatAdmins.length">
          <span v-for="item in params.concatAdmins" :key="item.id">
            <img :src="avatarSrc(item.admin_name)" class="avatar" @error="setDefaultImage" />
            <span class="admin-name">{{ item.admin_name }}</span>
          </span>
        </div>
        <div class="contact-admin-empty" v-else>暂未设置管理员</div>
      </div>
    </div>
  </div>
</template>
<script>
import { moocEnroll } from '@/config/mooc.api.conf.js'
import { debounce } from '@/utils/tools.js'
export default {
  name: 'taskErrorPage',
  props: {
    autoSignUpParams: {
      type: Object,
      default: () => ({
        register: false,
        resource_from: null,
        register_confirm: 1
      })
    },
    pageError: Object
  },
  data() {
    return {
      params: {},
      defaultAvatar: require('@/assets/mooc-img/avatar.png')
    }
  },
  filters: {
    concatAdminText(flag) {
      // flag === 3 隐藏文字
      if (flag === 1) {
        return '联系项目管理员'
      } else if (flag === 2) {
        return '如有需要，请联系项目管理员延长学习结束时间。'
      }
    }
  },
  computed: {
    avatarSrc() {
      return (name) => {
        let url = ''
        name = name.replace(/\(.*?\)/g, '')
        if (window.location.host.endsWith('.woa.com')) {
          url = `//learn.woa.com/rhrc/photo/150/${name}.png`
        } else {
          url = `//r.hrc.oa.com/photo/150/${name}.png`
        }
        return url
      }
    },
    isAutoSignUp () { // 是否自动报名
      if (!Object.keys(this.autoSignUpParams).length && !Object.keys(this.params).length) {
        return false
      }
      const { register_confirm, resource_from, register } = this.autoSignUpParams
      return (register_confirm === 0 && !resource_from && !register && (!this.params.noPermission && !this.params.register && this.params.isShowJoinBtn))
    }
  },
  watch: {
    pageError: {
      deep: true,
      immediate: true,
      handler(newVal) {
        this.params = Object.assign({ // 没有对应字段可以不传
          noPermission: false, // 展示无权限图标
          inaccessible: false, // 展示无法访问图标
          tips: '',
          isConcatAdmin: 0, // 0 隐藏管理员，其他状态值见 concatAdminText
          concatAdmins: [], // 管理员
          timeText: '', // 时间提示
          register: false, // 没有报名
          isShowJoinBtn: false,
          isShowBack: true
        }, newVal)
        console.log(this.params, newVal, 'iiii')
      }
    },
    isAutoSignUp: {
      deep: true,
      immediate: true,
      handler(newVal) {
        console.log('自动报名--', newVal)
        if (newVal) {
          console.log('自动报名执行------')
          this.debounceAutoSignUp()
        }
      }
    }
  },
  methods: {
    // 自动报名防抖
    debounceAutoSignUp: debounce(function () {
      this.autoSignUp()
    }, 500),
    // 自动报名
    autoSignUp() {
      const params = {
        mooc_course_id: this.$route.query?.mooc_course_id || '',
        join_type: '2' // 自动报名为2
      }
      moocEnroll(params).then((res) => {
        this.$message.success(this.$langue('Mooc_ProjectDetail_BasicInfo_RegistSucessedAuto', { defaultText: '已自动报名加入培养项目，请留意项目的学习时间' }))
        window.location.reload()
      }).catch(res => {
        if (res?.title) {
          const message = res.code && res.code !== 200 ? (res.message || res.data) : '网络异常，请稍后重试！'
          this.$message.error(message)
        }
      })
    },
    handleJoin() {
      this.$emit('handleJoin')
    },
    backHome() {
      if (this.$route.path.indexOf('/mooc/taskDetail') > -1) {
        this.$router.push({
          path: '/mooc/projectDetail',
          query: {
            mooc_course_id: this.$route.query?.mooc_course_id || ''
          }
        })
      } else {
        this.$router.push('/mooc/home')
      }
    },
    setDefaultImage(e) {
      e.target.src = this.defaultAvatar
    }
  }
}
</script>
<style lang='less' scoped>
.task-error-container {
  min-width: 1200px;
  height: 100%;
  overflow-y: hidden;
  overflow-x: auto;
  position: relative;
  background: #ffffff;
  display: flex;
  .application-btn {
    display: flex;
    justify-content: center;
    align-content: center;
    .join-btn{
      padding: 16px 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #ffffff;
      text-align: center;
      font-family: "PingFang SC";
      font-style: normal;
      font-weight: 500;
    }
  }
  .task-error-content {
    margin: auto;
    max-width: 860px;
    padding: 80px 0;
    width: fit-content;

    p {
      margin-top: 20px;
      height: 20px;
      line-height: 20px;
    }

    .tips {
      font-size: 18px;
      font-weight: 600;
      margin: 24px 0;
      max-width: 460px;
      line-height: 28px;
    }

    .back {
      color: #0052D9;
      cursor: pointer;
      text-align: center;
    }
  }

  .error-img {
    width: 240px;
    height: 148px;
  }

  .contact-admin {
    display: flex;
    flex-wrap: wrap;
    margin-top: 20px;

    &>span {
      margin: 0 36px 16px 0;
      width: 250px;
      display: flex;
      align-items: center;
    }

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 80px;
      margin-right: 8px;
    }
    .admin-name{
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }
  .contact-admin-empty{
    color:#a9a9a9;
    margin-top: 20px;
    font-size: 12px;
  }
  .mob-box{
    img{
      width: 120px;
      height: 120px;
    }
  }
}
</style>
