<template>
  <div class="course-main" v-if="courseDetail.content_basic_list.length">
    <div class="couse-title-box">
      <div class="title overflow-l1">{{ $langue('Mooc_CourseType_CourseList', { defaultText: '课单' }) }}：「 {{ courseDetail.name }} 」</div>
      <div class="course-play-num">
        <div class="left-title">
          <span class="course-play-box">
            <i class="play-icon"></i>
            <span>{{ courseDetail.pv || 0 }}</span>
          </span>
          <span class="course-num" @click="toCourse" :dt-eid="dtToCourse('eid')" :dt-remark="dtToCourse('remark')">
            <span>({{ currentArea }})</span>
            <i class="el-icon-arrow-right"></i>
          </span>
        </div>
        <span 
        @click="handleFavCourse" 
        :dt-eid="dtToCourse('eid', 'like')" 
        :dt-remark="dtToCourse('remark', 'like')" 
        class="btn"
        >{{ $langue('Mooc_SaveCourseList', { defaultText: '收藏课单' }) }}</span>
      </div>
    </div>
    <div class="course-swiper-box">
      <div 
      v-for="(e, index) in courseDetail.content_basic_list" 
      :key="index"
      :class="['item-course-swiper', { 'active-item-swiper': e.item_id == course_id }]"
      @click="toLink(e)"
      :dt-eid="dtCourseList('eid', e)"
      :dt-remark="dtCourseList('remark', e)"
      :dt-areaid="dtCourseList('areaid', e)" 
      >
        <div class="item-top overflow-l1">
          <i v-show="e.item_id == course_id" class="live-icon"></i>
          <span class="live-content">{{ e.content_name }}</span>
        </div>
        <div class="item-bottom">
          <span>{{ showModuleName(e) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getCourseDetailAPI,
  favCourse,
  getCourseViewCount
} from 'config/api.conf'
import { transformUnitW } from 'utils/tools'
import { actTypes } from '@/utils/moduleMap.js'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]
export default {
  props: {
    tabActiveName: {
      type: String,
      default: ''
    },
    courseData: {
      type: Object,
      default: () => ({})
    },
    dtPageType: {
      type: String,
      default: '网课详情页-新版'
    }
  },
  data() {
    return {
      transformUnitW,
      courseDetail: {
        content_basic_list: [],
        name: '',
        view_count: 0
      },
      actTypes
    }
  },
  computed: {
    course_id() {
      return this.courseData.course_id || parseInt(this.$route.query.course_id)
    },
    currentArea() {
      const index = this.courseDetail.content_basic_list.findIndex((e) => parseInt(e.item_id) === this.course_id)
      return `${index + 1}/${this.courseDetail.content_basic_list.length}`
    },
    showModuleName() {
      return (e) => {
        let tips = ''
        let row = this.actTypes.find((v) => v.module_id === e.module_id)
        if ([1].includes(e.module_id)) { // 网络课
          tips = `·${e.duration || 0}` + this.$langue('Mooc_ProjectDetail_TaskList_Minute', { defaultText: '分钟' })
        } else if ([7, 8].includes(e.module_id)) { // 案例-文章
          tips = `·${e.word_num || 0}` + this.$langue('Mooc_ProjectDetail_TaskList_Words', { defaultText: '字' })
        } else if ([10].includes(e.module_id)) { // 培养项目
          tips = `·${e.task_count || 0}` + this.$langue('Mooc_ProjectDetail_BasicInfo_Tasks', { defaultText: '项任务' })
        } else if ([15].includes(e.module_id)) { // 课单
          tips = `·${e.task_count || 0}` + this.$langue('NetCourse_Contents', { defaultText: '个内容' })
        }
        return `${this.$langue(row.langKey, { defaultText: row.act_type_name })}` + tips
      }
    },
    dtToCourse() {
      return (type, val) => {
        const { area_id } = this.$route.query
        const name = val === 'like' ? '收藏课单' : this.courseDetail.name
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: this.dtPageType, 
            container: '课单',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: name,
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${area_id}_${name}`
        }
      }
    },
    dtCourseList() {
      return (type, row) => {
        const { area_id } = this.$route.query
        const actInfo = this.actTypes.find((e) => row.module_id === e.module_id)
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: this.dtPageType, 
            container: '课单',
            click_type: 'data',
            content_type: row.module_name,
            content_id: row.item_id,
            content_name: row.content_name,
            act_type: actInfo?.act_type || '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${area_id}_${row.item_id}`
        } else {
          return `area_${this.course_id}_${area_id}_${row.item_id}`
        }
      }
    }
  },
  mounted() {
    this.getCourseList()
  },
  methods: {
    delscroll(val) {
      this.settimeout = setTimeout(() => {
        const domName = '.course-swiper-box'
        const scrollName = 'active-item-swiper'
        if (!domName) return
        const captionBox = this.$el.querySelector(domName)
        let curDom = document.getElementsByClassName(scrollName)[0]?.previousElementSibling
        if (curDom) {
          // 前面一条数据距离上方的位置+他自己的数据的高度，100有一个默认高度
          captionBox.scrollTop = curDom.offsetTop + curDom.clientHeight - 170
        }
      })
    },
    // 课单跳转
    toCourse() {
      const url = `${envName.courseWoaHost}courselist/course-detail?id=${this.courseDetail.cl_id}`
      window.open(url)
    },
    // 单个详情跳转
    async toLink(e) {
      if (!e.href) return
      await getCourseViewCount(this.$route.query.area_id)
      window.open(e.href)
    },
    // 收藏课单
    handleFavCourse() {
      const { area_id } = this.$route.query
      const param = {
        cl_id: area_id,
        cl_name: this.courseDetail.name
      }
      favCourse(param).then((res) => {
        this.$message.success(this.$langue('NetCourse_CourseListSaved', { defaultText: '课单收藏成功' }))
      })
    },
    getCourseList() {
      const { area_id, from } = this.$route.query
      const param = {
        from,
        share_staff_name: '',
        share_staff_id: ''
      }
      getCourseDetailAPI(area_id, param).then((res) => {
        this.courseDetail = res
        this.delscroll()
      })
    }
  }
}
</script>
<style lang="less" scoped>
.course-main {
  background: #fff;
  padding: 20px 24px;
  border-radius: 8px;
  margin-bottom: 20px;

  .couse-title-box {
    .title {
      max-width: 350px;
      color: #333333;
      font-size: 16px;
      font-weight: bold;
      display: inline-block;
      margin-bottom: 8px;
    }
    .course-play-num {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 16px;
      .left-title {
        display: flex;
        align-items: center;
        .course-num {
          display: flex;
          align-items: center;
          color: #777777;
          font-size: 14px;
          cursor: pointer;
          i {
            color: #777777;
            font-weight: bold;
            font-size: 14px;
            margin-left: 4px;
          }
        }
        .course-play-box {
          display: flex;
          align-items: center;
          color: #777777;
          line-height: 20px;
          margin-right: 16px;
      
          i {
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }
          .play-icon {
            background: url("~@/assets/img/play-line.png") no-repeat center / cover;
          }
        }
      }
      .btn {
        padding: 2px 8px;
        border-radius: 4px;
        border: 1px solid #006FFF;
        color: #006FFF;
        cursor: pointer;
      }

    }
  }
  .course-swiper-box {
    border-radius: 4px;
    background: #F6F6F6;
    padding: 12px 8px;
    max-height: 188px;
    overflow: auto;

    .item-course-swiper {
      height: 28px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 6px;
      padding: 0 8px; 

      i {
        width: 16px;
        height: 16px;
        display: inline-block;
        margin-right: 4px;
      }

      .item-top {
        width: 212px;
        .live-icon {
          background: url("~@/assets/img/icon-live.png") no-repeat center / cover;
          flex-shrink: 0;
          position: relative;
          top: 2px;
        }

        .live-content {
          line-height: 22px;
          color: #333333;
          word-break: break-all;
        }
      }

      .item-bottom {
        font-size: 12px;
        color: #777777;
        flex-shrink: 0;

        .circle {
          font-size: 12px;
          margin-left: 4px;
          margin-right: 4px;
          display: inline-block;
        }

        .timer {
          display: inline-block;
        }
      }
    }
    .item-course-swiper:last-of-type {
      margin-bottom: unset;
    }
    .active-item-swiper {
      background: #fff;
      border-radius: 3px;
      .item-top {
        .live-content {
          color: #0052D9;
        }
      }
      .item-bottom {
        color: #333333;
      }
    }
  }
}
</style>
