<template>
  <div class="base-set-page">
    <div class="header-top">
      <span class="title">基础设置</span>
      <!-- 6-审核中，3-已结束 -->
      <el-button 
        :disabled="projectManageInfo.course_status === 2 || projectManageInfo.course_status === 6" 
        v-show="isShowEdit && !isApprove && [null, 1, 2].includes(projectManageInfo.info_sec_status)" 
        type="primary" 
        size="small" 
        @click="isShowEdit=false"
        >
        编辑
      </el-button>
    </div>
    <!-- 基础设置 -->
    <ShowProject v-if="isShowEdit"></ShowProject>
    <!-- 编辑 -->
    <projectCreate v-else pageEntryType="baseSet" @handleCancel="isShowEdit=true"></projectCreate>
  </div>
</template>
<script>
import ShowProject from './showProject.vue'
import projectCreate from '../../components/ProjectCreate.vue'
import { mapState } from 'vuex'
export default {
  components: {
    ShowProject,
    projectCreate
  },
  data() {
    return {
      form: {},
      isShowEdit: true
    }
  },
  computed: {
    ...mapState(['projectManageInfo']),
    isApprove() { // 审核预览
      return this.$route.query.approve === '1'
    }
  },
  watch: {
    // 审核页面的编辑
    projectManageInfo: {
      deep: true,
      immediate: true,
      handler(val) {
        const approve = this.$route.query.approve
        if (approve === '0' && typeof val.course_status === 'number' && val.course_status !== 6) {
          this.isShowEdit = false
        }
      }
    }
  }
}
</script>

<style lang="less" scoped>
.base-set-page {
  background-color: white;
  .header-top {
    // height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(243, 243, 243, 1);
    padding: 16px 20px;
    .title {
      font-size: 16px;
      height: 24px;
      line-height: 24px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.9)
    }
  }
}
</style>
