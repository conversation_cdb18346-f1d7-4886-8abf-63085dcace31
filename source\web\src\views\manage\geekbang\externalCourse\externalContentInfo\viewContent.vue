<template>
  <div class="content">
    <div class="base-info-box">
      <div class="base-info-content">
        <div class="item-box">
          <span class="label">内容来源：</span>
          <span class="value">{{ handleRecourse(courseInfo.recourse_from) }}</span>
        </div>
        <div class="item-box">
          <span class="label">内容类型：</span>
          <span class="value">{{ handleRecourseCourseClassify(courseInfo.recourse_course_classify) }}</span>
        </div>
        <div class="item-box">
          <span class="label">内容ID：</span>
          <span class="value">{{ courseInfo.outsourced_course_id }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { 
  getMoocOutsourcedInfo
} from '@/config/mooc.api.conf.js'
export default {
  components: {
  },
  data() {
    return {
      courseInfo: {
        recourse_from: '***',
        recourse_course_classify: '***',
        outsourced_course_id: '***'
      },
      recourseOptions: [
        { label: '极客时间', value: 'geekBang' }
      ],
      recourseContentOptions: [
        { label: '极客时间 - 音频图文专栏', value: 1, name: '音频图文专栏' },
        { label: '极客时间 - 视频课专栏', value: 2, name: '视频课专栏' },
        { label: '极客时间 - 每日一课视频', value: 3, name: '每日一课视频' },
        { label: '极客时间 - 大厂案例视频', value: 4, name: '大厂案例视频' }
      ]
    }
  },
  computed: {
  },
  mounted() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      const id = this.$route.query.mooc_course_id
      getMoocOutsourcedInfo(id).then(res => {
        console.log('外部课程信息：', res)
        if (res) {
          this.courseInfo = res
        }
      })
    },
    handleRecourseCourseClassify(type) {
      let index = this.recourseContentOptions.findIndex(v => v.value === type * 1)
      if (index !== -1) return this.recourseContentOptions[index].name
      return type
    },
    handleRecourse(value) {
      let index = this.recourseOptions.findIndex(v => v.value === value)
      if (index !== -1) return this.recourseOptions[index].label
      return value
    }
  }
}
</script>
  
<style lang="less" scoped>
.content {
  padding: 20px 20px 40px;

  .item-box {
    color: rgba(0, 0, 0, 0.4);
    font-size: 14px;

    .value {
      color: rgba(0, 0, 0, 0.9);
    }
    .label {
      width: 115px;
      text-align: right;
      display: inline-block;
    }

    .label,
    .value {
      line-height: 22px;
    }
  }

  .item-box+.item-box {
    margin-top: 20px;
  }
}
</style>
