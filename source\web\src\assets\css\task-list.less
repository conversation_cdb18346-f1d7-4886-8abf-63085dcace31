.project-task-tree {
  background-color: white;
  // min-height: 600px;
  border-radius: 4px;
  position: relative;
  height: 100%;

  .task-title {
    padding: 16px 20px;
    font-size: 14px;
    font-weight: bold;
    border-bottom: 1px solid #f3f3f3;
  }

  .task-body {
    padding: 23px 20px 80px;
  }

  .task-operate-btns {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    &-left {
      display: flex;
      .batch-btn {
        margin: 0 10px;
      }

      .el-select {
        width: 124px;
      }
      .all-chioce {
        cursor: pointer;
        display: flex;
        align-items: center;
        margin-left: 10px;
        color: #666;
        .arrow-icon {
          display: inline-block;
          width: 16px;
          height: 16px;
          margin-left: 8px;
          background: url('~@/assets/mooc-img/bottom-arrow.png') no-repeat center/cover;
        }
        .icon-arrow-down {
          transform: rotate(-90deg);
          transition: transform .3s;
        }
        .icon-arrow-right {
          transform: rotate(0deg);
          transition: transform .3s;
        }
      }
    }

    &-right {
      display: flex;
      align-items: center;
      color: #333;
      font-family: "PingFang SC";
      font-size: 14px;

      .tag {
        height: 20px;
        line-height: 20px;
        border-radius: 2px;
        background: #ebeffc;
        text-align: center;
        margin-right: 40px;
        color: #0052D9;
        border: unset;
        padding: 0 8px;
      }
    }
  }
  .top-fixed {
    position: absolute;
  }
  .custom-table {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 48px;
    // line-height: 48px;
    background: #f5f5f5ff;
    color: #00000099;
    border-bottom: none;
    .item-th {
      margin-left: 10px;
      display: inline-block;
      line-height: 24px;
      .task-checkall {
        :deep(.el-checkbox__input) {
          margin-left: 12px;
        }
        :deep(.el-checkbox__label) {
          padding-left: 24px;
          color: #00000099;
        }
      }
    }
    .first-item-th {
      margin-left: 65px;
    }
    .edit-item-th {
      margin-left: 65px;
      padding-left: 40px;
    }
  }
  .table-wrap {
    border-radius: 4px;
    // border: 1px solid #eeeeee;
  }
  :deep(.el-tree) {
    color: #000000e6;
    .tree-icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      box-sizing: content-box;
      background: url("~@/assets/mooc-img/right.png") no-repeat center / cover;
      margin-right: 32px;
      margin-left: 12px;
    }
    .tree-icon-down {
      transform: rotate(90deg);
      transition: transform .3s;
    }
    .tree-icon-right {
      transform: rotate(0deg);
      transition: transform .3s;
    }
    .stage-row {
      background: #EEEEEE;
    }
    .group-row {
      background: #fafafaff;
    }
    .first-tree-column {
      // width: 1400px !important;
      margin-left: unset !important;
    }
    .need-left-column {
      margin-left: 65px !important;
    }
    .parent-stage-task {
      margin-left: 35px !important;
    }
    .parent-group-task {
      margin-left: unset !important;
    }
    .tree-icon.is-leaf {
      cursor: default;
      display: none;
    }
    .el-tree-node__expand-icon {
      position: absolute;
      padding-left: 55px;
      left: -30px;
    }
    .el-tree-node__content {
      display: flex;
      height: 60px;
      line-height: 60px;
    }
    .tree-row {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 100%;
      border: 1px solid #eeeeeeff;
      .item-tree-column {
        display: flex;
        align-items: center;
        margin-left: 10px;
        .el-checkbox {
          margin-left: 12px;
          margin-right: 24px;
        }
        .check-cover {
          display: inline-block;
          padding-left: 40px;
        }
        .el-switch {
          margin-right: 10px;
        }
        .el-icon-rank {
          margin-left: 10px;
          color: #0052D9
        }
        .input-text-box {
          display: flex;
          align-items: center;
        }
        .task-check-box {
          display: flex;
          align-items: center;
          img {
            width: 16px;
            height: 16px;
            margin-right: 8px;
          }
        }
        .el-button + .el-button {
          margin-left: 20px;
        }
        .btn-box {
          display: flex;
          align-items: center;
          .el-link {
            margin-left: 20px;
          }
          i {
            color:#00000099
          }
        }
      }
      .show-tree-label, .tree-text-label {
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        -o-text-overflow: ellipsis;
        margin-right: 16px;
        display: inline-block;
        height: 24px;
        line-height: 24px;
      }
      .show-tree-label {
        // width: 1200px;
      }
      .tree-text-label {
        @media screen and (max-width: 1660px) {
          width: 150px;
        }
        @media screen and (min-width: 1661px) {
          width: 260px;
        }
      }
    }
  }
  .input-style {
    position: relative;
    :deep(.el-input) {
      line-height: 32px;

      .el-input__inner {
        padding-right: 70px;
      }

      .el-input__suffix {
        position: absolute;
        right: 43px;
      }
    }

    .custom-el-input-count {
      position: absolute;
      right: 5px;
      width: 40px;
    }
  }
  
}