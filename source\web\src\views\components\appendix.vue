<template>
  <div class="appendix" v-if="list.length">
    <p><span>附件：共{{list.length}}个</span><span class="arrow" @click="handleArrow">{{arrow ? '展开' : '收起'}} <i class="el-icon-arrow-up" v-show="!arrow"></i> <i v-show="arrow" class="el-icon-arrow-down"></i></span></p>
    <div class="appendix-list" v-show="!arrow">
      <div :style="margin" class="a-item" v-for="(item, index) in list" :key="index">
        <p class="exhibit" :style="width">
          <img style="width:32px; height:32px" :src="resourceImg(item.resource_type,item.file_name)" alt="">
          <span>{{textEllipsis(item.file_name)}}</span>
        </p>
        <div :style="width" class="handle-top">
          <div class="content">
            <p @click="preview(item)"  v-if="item.resource_type !== 'Zip'"><img src="../../assets/img/work/examine.png" alt=""> <span>查看</span></p>
            <p @click="urlForDownload(item)"><img src="../../assets/img/work/download.png" alt=""> <span>下载</span></p>
            <p v-if="!isPreview" @click="deleteAppendix(item)"><img src="../../assets/img/work/delete.png" alt=""> <span>删除</span></p>
          </div>

        </div>
      </div>
    </div>
    <!-- 上传资料预览 -->
    <workPrviewDialog ref="workPreviewDialogRef"></workPrviewDialog>
  </div>
</template>

<script>
import workPrviewDialog from '@/views/components/work-preview-dialog.vue'
import { textEllipsis } from '@/utils/tools.js'
import { operatesignature } from 'config/api.conf'
import { workSourceInfo } from 'config/mooc.api.conf'
export default {
  components: {
    workPrviewDialog
  },
  props: {
    size: {
      type: String,
      default: 'samll'
    },
    isPreview: {
      type: Boolean,
      default: false
    },
    fileData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      arrow: false
    }
  },
  computed: {
    width() {
      return `width:${this.size === 'samll' ? '280' : '230'}px;`
    },
    margin() {
      return `margin-right: ${this.size === 'samll' ? '40' : '0'}px`
    },
    list() {
      return this.fileData.map((e) => ({
        ...e,
        allow_download: e.allow_download || true,
        resource_name: e.file_name,
        resource_type: e.content_type
      }))
    },
    resourceImg() {
      let imgName = ''
      return (type, name) => {
        switch (type) {
          case 'Video':
            imgName = 'icon-video'
            break
          case 'Audio':
            imgName = 'icon-mp3'
            break
          case 'Image':
            imgName = 'icon-jpg'
            break
          case 'Zip':
            imgName = 'icon-zip'
            break
          case 'Doc' :
            const name_subtr = name.lastIndexOf('.') && name.substr(name.lastIndexOf('.') + 1) 
            switch (name_subtr) {
              case 'doc' : case 'docx' :
                imgName = 'icon-word'
                break
              case 'ppt' : case 'pptx':
                imgName = 'icon-ppt'
                break
              case 'pdf' :
                imgName = 'icon-pdf'
                break
              case 'xls' : case 'xlsx' :
                imgName = `icon-excel`
                break
            }
            break
        }
        return imgName ? require(`@/assets/mooc-img/${imgName}.png`) : require('@/assets/img/default_bg_img.png')
      }
    }
  },
  methods: {
    textEllipsis,
    handleArrow() {
      this.arrow = !this.arrow
    },
    // 预览
    preview(item) {
      console.log('预览', item)
      let curItem = {
        content_id: item.content_id,
        resource_type: item.resource_type,
        resource_name: item.resource_name,
        file_size: item.file_size,
        mooc_course_id: item.record_id,
        allow_download: item.allow_download
      }
      if (item.resource_type === 'Zip') return
      console.log('范德萨富士达雷锋精神的垃圾斯大林')
      this.$nextTick(() => {
        this.$refs.workPreviewDialogRef.handleViewFile(curItem)
      })
    },
    // 获取下载文件源地址
    urlForDownload(obj) {
      console.log('我都作业下载', obj)
      const signatureParams = {
        content_id: obj.content_id,
        operate: 'download'
      }
      operatesignature(signatureParams).then((signature) => {
        workSourceInfo(obj.content_id, {
          app_id: 'QLearningService',
          signature: signature
        }).then((res) => {
          this.getBlob(res).then((bolb) => {
            this.saveAs(bolb, obj.resource_name)
          })
        })
      })
    },
    // 获取 blob 格式文件
    getBlob(url) {
      return new Promise((resolve) => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', url, true)
        xhr.responseType = 'blob'
        xhr.onload = () => {
          if (xhr.status === 200) {
            resolve(xhr.response)
          }
        }
        xhr.send()
      })
    },
    // 文件下载
    saveAs(blob, filename) {
      let link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      link.click()
    },
    deleteAppendix(val) {
      this.$confirm(val.file_name, '确定要删除这个附件吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        this.$emit('deleteFile', val)
        this.$message({
          type: 'success',
          message: '删除成功!'
        })
      })
    }
  }
}
</script>

<style  lang="less" scoped>
.appendix {
  margin-top: 28px;
  .arrow {
    padding-left: 16px;
    color: #3464e0ff;
    cursor: pointer;
  }
  .appendix-list {
    display: flex;
    flex-wrap: wrap;
    padding: 10px;
    background-color: #fafafaff;
    margin-top: 8px;
    .a-item {
      position: relative;
      overflow: hidden;
      margin-right: 40px;
      .exhibit {
        display: flex;
        align-items: center;
        cursor: pointer;
        padding: 10px;
        color: #3464e0ff;
        span {
          padding-left: 5px;
        }
      }
      .handle-top {
        z-index: 999;
        position: absolute;
        opacity: 0;
        height: 52px;
        border-radius: 4px;
        padding: 10px;
        top: 0;
        background-color: #000;
        .content {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          p {
            display: flex;
            align-items: center;
            cursor: pointer;
            color: #ffffffff;
            margin-right: 32px;
            span {
              padding-left: 3px;
            }
          }
        }
      }
    }
    .a-item:hover .handle-top {
      opacity: 0.6;
      transition: all 0.3s;
    }
    .a-item:nth-child(3n) {
      margin-right: 0 !important;
    }
  }
}
</style>
