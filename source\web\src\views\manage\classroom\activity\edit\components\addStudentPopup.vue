<template>
  <div class="add-student-popup activity-common">
    <el-dialog :visible.sync="visible" width="800px" :close-on-click-modal="false" :before-close="handlerClose">
      <div slot="title" class="dialog-header">添加学员</div>
      <div class="dialog-content">
        <div class="title-row">请配置此批学员加入后的初始状态</div>
        <div class="form-item ">
          <span class="form-item-label">参加形式</span>
          <div class="form-item-content m-0">
            <el-checkbox-group v-model="join_type" size="small" @change="handlerCheckboxChange($event, 'join_type')">
              <el-checkbox v-for="item in participationForm" :label="item.value" :key="item.value" :disabled="item.checked">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="form-item">
          <span class="form-item-label">报名状态</span>
          <div class="form-item-content m-0">
            <el-checkbox-group v-model="reg_status" size="small" @change="handlerCheckboxChange($event, 'reg_status')">
              <el-checkbox v-for="item in registStatus" :label="item.value" :key="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="form-item">
          <span class="form-item-label">考勤状态</span>
          <div class="form-item-content m-0">
            <div class="flex-row flex-a-start flex-j-between">
              <el-checkbox-group v-model="status" size="small" class="mr-12" @change="handlerCheckboxChange($event, 'status')">
                <el-checkbox v-for="item in signInStatus" :label="item.value" :key="item.value">{{ item.label }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
        <div class="title-row mb-12">请录入此批添加的学员名单</div>
        <div class="student-select-card">
          <div class="import-options">
            <el-radio v-model="radio" label="1">批量文本录入</el-radio>
            <el-radio v-model="radio" label="2">员工选择器</el-radio>
          </div>
          <template v-if="radio === '1'">
            <el-input
              type="textarea"
              :autosize="{ minRows: 5, maxRows: 5}"
              placeholder="请输入学员英文名，多名学员使用中文分号分隔，单次至多添加300人"
              v-model="submitForm.staff_names"
              @change="handlerStaffsData">
            </el-input>
          </template>
          <template v-else>
            <sdc-staff-selector 
              multiple 
              ref="adminsSelectorRef" 
              v-model="staff_list"
              size="small" 
              :props="adminProps" 
              placeholder="请选择学员"
              @change="studentChange"
            />
          </template>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="handlerClose" size="small">取消</el-button>
        <el-button class="confirm-btn" type="primary" @click="handlerConfirm" size="small">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addActivityStudentApi } from '@/config/classroom.api.conf.js'
import { mapState } from 'vuex'

export default {
  name: 'addStudentPopup',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      participationForm: [
        { label: '线下授课', value: 1, checked: true },
        { label: '在线会议', value: 2, checked: true }
      ],
      registStatus: [
        { label: '已报名', value: 0 },
        { label: '未报名霸课', value: 6 },
        { label: '排队候补', value: 2 },
        // { label: '待上级审核', value: 3 },
        { label: '审核未通过', value: 11 },
        { label: '已注销报名', value: 1 }
      ],
      signInStatus: [
        { label: '暂无数据', value: -1 },
        { label: '全勤', value: 4 },
        { label: '缺勤', value: 5 },
        { label: '部分缺勤', value: 18 },
        { label: '临时取消', value: 19 }
      ],
      submitForm: {
        class_id: '',
        act_type: 4,
        join_type: 1,
        reg_status: 0,
        status: -1,
        staff_names: '',
        staff_list: []
      },
      join_type: [1],
      reg_status: [0],
      status: [-1],
      radio: '1',
      staff_list: [],
      adminProps: {
        staffID: 'staff_id',
        staffName: 'staff_name'
      }
    }
  },
  watch: {},
  computed: {
    ...mapState({
      $activityInfo: state => state.activity.activityInfo
    }),
    activityId () {
      return this.$route.query.activity_id
    }
  },
  created() { },
  mounted() { 
    this.handlerPrticipationData()
  },
  beforeDestroy() { },
  methods: {
    // 处理当前活动 参加形式
    handlerPrticipationData() {
      const { teaching_type } = this.$activityInfo
      const joinTypeValues = teaching_type.split(';').map(Number)
      const participationForm = this.participationForm.map(item => {
        if (joinTypeValues.includes(item.value)) {
          item.checked = false
          return item
        }
        return item
      })

      const joinType = participationForm.find(item => joinTypeValues.includes(item.value))?.value
      if (joinType) {
        this.submitForm.join_type = joinType
        this.join_type = [joinType]
      }
    },
    handlerCheckboxChange(e, name) {
      if (!e.length) {
        this[name] = [this.submitForm[name] * 1]
      }
      if (e.length > 1) {
        let val = [e[e.length - 1]]
        this[name] = val
        this.submitForm[name] = val.join()
      }
    },
    handlerStaffsData(val) {
      // 将字符串分割，并去除空格
      let staff_names = val.replace(/；/g, ';').split(';').filter(item => item)
      if (staff_names.length > 300) {
        this.submitForm.staff_names = staff_names.slice(0, 300).join('；')
        this.$message.warning('单次至多添加300人')
      }
    },
    studentChange(val) {
      this.submitForm.staff_list = val.map(item => {
        const { staff_id, staff_name } = item
        return {
          staff_id,
          staff_name
        }
      })
    },
    handlerClose() {
      this.submitForm = {
        class_id: '',
        act_type: 4,
        join_type: 1,
        reg_status: 0,
        status: 4,
        staff_names: '',
        staff_list: []
      }
      this.join_type = [1]
      this.reg_status = [0]
      this.status = [4]
      this.radio = '1'
      this.$emit('update:visible', false)
    },
    handlerConfirm() {
      const { radio, submitForm, join_type, reg_status, status, activityId } = this
      if ((radio === '1' && !submitForm.staff_names) || (radio === '2' && !submitForm.staff_list.length)) {
        this.$message.warning(radio === '1' ? '请输入学员英文名' : '请选择学员')
        return
      }
      const params = {
        ...submitForm,
        class_id: Number(activityId),
        join_type: Number(join_type.join()),
        reg_status: Number(reg_status.join()),
        status: Number(status.join())
      }
      if (radio === '1') {
        params.staff_names = submitForm.staff_names.replace(/；/g, ';')
        delete params.staff_list
      } else {
        delete params.staff_names
      }
      addActivityStudentApi(params).then(res => {
        this.$message.success('添加成功')
        this.handlerClose()
        this.$emit('success', true)
      })
    }
  }
}
</script>

<style lang="less" scoped>
  @import '~assets/css/activity-common.less';

  .add-student-popup {

    .title-row {
      color: #000000cc;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 22px;
      margin-bottom: 24px;
    }
    .student-select-card {
      height: 178px;
      padding: 16px;
      gap: 12px;
      border-radius: 4px;
      background: #F6F6F6;
      .import-options {
        margin-bottom: 12px;
      }
      :deep(.el-radio) {
        margin-right: 16px;
        .el-radio__input.is-checked+.el-radio__label,
        .el-radio__label {
          color: #000000e6;
        }
      }
    }
  }
</style>
