<template>
  <div class="activity-list-page">
    <div class="activity-list-page-header">
      <p>我创建的活动</p>
      <div class="search-area">
        <ul>
          <li :class="{ 'active': searchParams.status === item.value }" v-for="item in statusList" :key="item.value" @click="changeStatus(item.value)">{{ `${item.label} ${item.count}` }}</li>

          <li class="search-input">
            <el-input v-model="searchParams.activity_name" placeholder="标题关键词搜索" class="search-input-box" @input="inputChange">
              <i slot="suffix" class="el-input__icon el-icon-search icon-search"></i>
            </el-input>
          </li>
          <el-button type="primary" size="small" class="create-btn" @click="handleAdd">创建活动</el-button>
        </ul>
      </div>
    </div>
    <div class="main-table">
      <div class="sort-box">
        <span>排序：</span>
        <ul>
          <li 
          :class="{ 'active-sort': searchParams.order_by === e.value }"
          v-for="e in sortList" 
          :key="e.value"
          @click="handleSort(e.value)"
          >
          {{ e.label }}
          </li>
        </ul>
      </div>
      <div class="card-list">
        <div class="item-card" v-for="(v) in tableData.records" :key="v.activity_id">
          <ul>
            <li class="item-card-content">
              <div @click="handleComplete(v)" :class="['video-cover']">
                <el-image
                  lazy 
                  fit="fill"
                  :src="getItemImg(v)"
                >
                <div class="image-slot" slot="error">
                  <img :src="require('@/assets/img/default_bg_img.png')" alt="" />
                </div>
                </el-image>
              </div>
              <div class="text">
                <p class="video-title">
                  <span class="course-tag">活动</span>
                  <span :class="['course-name']" @click="handleComplete(v)">{{ v.activity_name }}</span>
                </p>
                <div class="content-center bottom-icon">
                  <div class="bottom-left">
                    <span class="icon-box"> 活动时间：{{v.start_time | timeFormat}} - {{v.end_time | timeFormat}}</span>
                    <span style="margin-left: 16px">
                      <span :class="[getStatusColor(v),'icon-box']">{{v | statusName }}</span>
                    </span>
                  </div>
                </div>
                <div class="bottom-icon">
                  <div class="bottom-left">
                    <span class="icon-box"> <span class="watch-icon icon"></span>{{handlerTransformUnit(v.view_count)}}</span>
                    <span class="icon-box"> <span class="collect-icon icon"></span>{{handlerTransformUnit(v.fav_count)}}</span>
                    <span class="icon-box"> <span class="registed-icon icon"></span>{{handlerTransformUnit(v.registed_count)}}</span>
                    <span class="icon-box"> <span class="user-score-icon icon"></span>{{ v | userScore }}</span>
                    <span class="icon-box">{{timeToDate(v.updated_at)}}</span>
                  </div>
                  <div :class="['bottom-right']">
                      <el-tooltip class="item" effect="dark" content="编辑" placement="top-start">
                        <span :class="['other-icon-box', { 'disabled-icon': v.status * 1 === 6 }]" @click="handleEdit(v)"> <span class="stop-edit-icon right-icon edit-icon"></span></span>
                      </el-tooltip>
                      <el-tooltip class="item" effect="dark" content="分享" placement="top-start">
                        <span :class="['other-icon-box', { 'disabled-icon': [3, 4, 6, 9].includes(v.status * 1) }]" @click="handleShare(v)"> <span class="stop-share-icon right-icon share-icon"></span></span>
                      </el-tooltip>
                      <el-tooltip class="item" effect="dark" content="上架" placement="top-start" v-if="[3, 7, 8].includes(v.status * 1)">
                        <span :class="['other-icon-box']" @click="handleUp(v)"> <span class="stop-up-icon right-icon up"></span></span>
                      </el-tooltip>
                      <el-tooltip class="item" effect="dark" content="下架" placement="top-start" v-else>
                        <span :class="['other-icon-box', { 'disabled-icon': v.status * 1 !== 1 }]" @click=" handleDown(v)"> <span class="stop-down-icon right-icon down"></span></span>
                      </el-tooltip>
                      <el-tooltip class="item" effect="dark" content="添加到课单" placement="top-start">
                        <span :class="['other-icon-box', { 'disabled-icon': [4, 6, 7].includes(v.status * 1) }]" @click="handleAddCourse(v)"> <span class="stop-add-icon right-icon add-circle"></span></span>
                      </el-tooltip>
                      <el-tooltip class="item" effect="dark" content="删除" placement="top-start">
                        <span :class="['other-icon-box', { 'disabled-icon': [1, 6, 8].includes(v.status * 1) }]" @click="handleDelete(v)"> <span class="stop-del-icon right-icon del"></span></span>
                      </el-tooltip>
                  </div>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
      <el-pagination
        v-if="tableData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="tableData.current"
        :page-sizes="[5, 10, 20, 30, 50, 100]"
        :page-size="size"
        layout="total,  prev, pager, next, sizes, jumper"
        :total="tableData.total"
      >
      </el-pagination>
      <div class="empty" v-if="tableData.records.length === 0">
        <span class="empty-img"></span>
        <div class="empty-text">还没有创建过活动哦，快来发起活动吧！</div>
      </div>
    </div>

    <div class="introduce-card" v-if="tableData.records.length === 0">
      <div class="card-title flex-box"><img src="@/assets/classroomImg/thinking-problem.png" alt="为什么使用 Q-Learning 组织团队分享"/>为什么使用 Q-Learning 组织团队分享</div>
      <div class="card-content">
        <div class="card-content-introduce flex-box flex-between">
          <div class="card-content-item flex-box flex-column">
            <div class="card-content-item-title text-box">灵活的开展形式</div>
            <div class="card-content-item-subtitle text-box">不再受地理位置的限制</div>
            <div class="card-content-item-p">支持腾讯会议 开班</div>
            <div class="card-content-item-p">系统自动创建会议、自动考勤</div>
            <div class="card-content-item-p">学员可自主选择线上或线下参与活动</div>
            <div class="card-content-preview">
              <img class="img" src="@/assets/classroomImg/introduce_1.png" alt=""/>
            </div>
          </div>

          <div class="card-content-item flex-box flex-column">
            <div class="card-content-item-title text-box">丰富的课前课后功能</div>
            <div class="card-content-item-subtitle text-box">学习不止于学堂，更在课前课后</div>
            <div class="card-content-item-p">报名前问卷收集学员疑问</div>
            <div class="card-content-item-p">结课后，为学员提供回看视频</div>
            <div class="card-content-item-p">还有 AI 转写纪要，让教学更有成效</div>
            <div class="card-content-preview">
              <img class="img" src="@/assets/classroomImg/introduce_2.png" alt=""/>
            </div>
          </div>

          <div class="card-content-item flex-box flex-column">
            <div class="card-content-item-title text-box">完善的培训管理功能</div>
            <div class="card-content-item-subtitle text-box">为你的开班运营降本增效</div>
            <div class="card-content-item-p">联动企微班级群运营、自动化学员和讲师日历</div>
            <div class="card-content-item-p">一站式申请直播、完善的权限管理体系...</div>
            <div class="card-content-item-p">丰富的管理功能，让开展活动便捷高效</div>
            <div class="card-content-preview">
              <img class="img" src="@/assets/classroomImg/introduce_3.png" alt=""/>
            </div>
          </div>
        </div>
      </div>
    </div>

    <AddCourseDialog :visible.sync="addCourseDialogShow" :itemData.sync="addCourseDialogData" />
    <QrCodeDialog v-if="copyShow.show" :visible.sync="copyShow.show" :url="copyShow.url" :copyTitle="copyShow.title" isAppletQrCode :appletPath="copyShow.appletPath" :scene="copyShow.scene" />
  </div>
</template>

<script>
import env from 'config/env.conf.js'
import { transformUnit, debounce, timeToDate } from 'utils/tools'
import { getActivityCountApi, getActivityList, activityShelfApi, activityOffShelfApi, activityDeleteApi } from 'config/classroom.api.conf'
import pager from '@/mixins/pager'
import QrCodeDialog from '@/views/components/qrCodeDialog'
import { AddCourseDialog } from '@/components/index'
import { mapState } from 'vuex'

export default {
  components: {
    QrCodeDialog,
    AddCourseDialog
  },
  mixins: [pager],
  data() {
    return {
      timeToDate,
      statusNameObj: { 1: '上架', 3: '下架', 4: '草稿', 6: '待审核', 101: '未开始', 102: '进行中', 103: '已结束' },
      statusList: [
        { label: '全部', key: 'all_count', value: '', count: 0 },
        { label: '未开始', key: 'not_start_count', value: 101, count: 0 },
        { label: '进行中', key: 'progress_count', value: 102, count: 0 },
        { label: '已结束', key: 'finished_count', value: 103, count: 0 },
        { label: '已下架', key: 'removed_count', value: 3, count: 0 },
        { label: '草稿', key: 'draft_count', value: 4, count: 0 }
      ],
      sortList: [
        { label: '最近发布', value: '0' },
        { label: '报名人数', value: '1' },
        { label: '活动评价', value: '2' },
        { label: '浏览次数', value: '3' },
        { label: '收藏人数', value: '4' }
      ],
      searchParams: {
        activity_name: '',
        status: '',
        order_by: '0'
      },
      copyShow: {
        show: false,
        title: '',
        url: '',
        scene: '',
        appletPath: ''
      },
      addCourseDialogShow: false,
      addCourseDialogData: {}
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.userInfo
    }),
    pageId() {
      return this.$route.query.page_id || ''
    },
    allDataCount() {
      return this.statusList.find(item => item.key === 'all_count').count
    }
  },
  filters: {
    userScore(v) {
      // return v.comment_count <= 20 ? '暂无评分' : v.total_score || 0 + '分'
      return v.total_score ? v.total_score + '分' : '暂无评分'
    },
    timeFormat(date) {
      let str = '--'
      if (date) {
        let dayArr = date.split(' ')[0].split('-')
        let timeArr = date.split(' ')[1].split(':')
        str = `${dayArr[0]}/${dayArr[1]}/${dayArr[2]} ${timeArr[0]}:${timeArr[1]}`
      }
      return str
    },
    statusName(data) {
      if (data.status === 1) {
        const now = Date.now()
        const start = new Date(data.start_time).getTime()
        const end = new Date(data.end_time).getTime()
        if (now < start) {
          return '未开始'
        } else if (now < end) {
          return '进行中'
        } else {
          return '已结束'
        }
      }
      let statusNameObj = { 1: '上架', 3: '下架', 4: '草稿', 6: '待审核', 101: '未开始', 102: '进行中', 103: '已结束' }
      return statusNameObj[data.status]
    }
  },
  created() {
    this.onSearch(1)
    this.getActivityCount()
  },
  methods: {
    getItemImg({ photo_url = '', photo_id = '' }) {
      if (!photo_url && !photo_id) {
        return require('@/assets/img/default_bg_img.png')
      }
      const regex = /^https?:\/\//
      if (regex.test(photo_url)) {
        return photo_url
      } else {
        const envName = env[process.env.NODE_ENV]
        return `${envName.contentcenter}content-center/api/v1/content/imgage/${photo_id}/preview`
      }
    },
    getActivityCount() {
      getActivityCountApi().then(res => {
        Object.keys(res).forEach(key => {
          this.statusList.find(item => item.key === key).count = res[key]
        })
      })
    },
    inputChange: debounce(function () {
      const reg = /[^a-zA-Z0-9\u4e00-\u9fa5]/g
      if (reg.test(this.searchParams.activity_name)) {
        this.searchParams.activity_name = this.searchParams.activity_name.replace(reg, '')
        return
      }
      this.onSearch(1)
    }, 500),
    handleSort(val) {
      this.searchParams.order_by = val
      this.onSearch(1)
    },
    changeStatus(val) {
      this.searchParams.status = val
      this.onSearch(1)
    },
    onSearch(current) {
      let params = {
        ...this.searchParams,
        current,
        size: this.size,
        order_by: this.searchParams.order_by
      }
      getActivityList(params).then(res => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    handleComplete(row) {
      const isTeacher = row.head_teacher_id.includes(this.userInfo.staff_id)
      if (row.status === 4 && !isTeacher) {
        this.$message.warning('您不是活动负责人，无法预览')
        return
      }
      const baseUrl = window.location.origin
      window.open(`${baseUrl}/training/activity/detail?activity_id=${row.activity_id}${row.status === 4 && isTeacher ? '&preview=true' : ''}`)
    },
    handleAdd() {
      let query = {}
      if (this.$route.query.page_id) {
        query = {
          page_id: this.$route.query.page_id
        }
      }
      this.$router.push({
        name: 'activityPage',
        query
      })
    },
    handleEdit(v) {
      if ([3, 4].includes(v.status)) {
        this.$router.push({
          name: 'activityPage',
          query: {
            activity_id: v.activity_id
          }
        })
      } else {
        this.$router.push({
          name: 'activityEdit',
          query: {
            activity_id: v.activity_id
          }
        })
      }
    },
    handleShare(v) {
      this.copyShow.title = v.activity_name 
      this.copyShow.url = `https://sdc.qq.com/s/JZH8Eg?scheme_type=activityDetail&activity_id=${v.activity_id}`
      this.copyShow.scene = v.activity_id
      this.copyShow.appletPath = 'pages/activity/index'
      this.copyShow.show = true
    },
    handleUp(v) {
      activityShelfApi(v.activity_id).then(res => {
        this.$message.success('启用成功')
        this.onSearch(1)
        this.getActivityCount()
      })
    },
    handleDown(v) {
      this.$confirm('是否确认下架?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        activityOffShelfApi(v.activity_id).then(res => {
          this.$message.success('下架成功')
          this.onSearch(1)
          this.getActivityCount()
        })
      })
    },
    handleDelete(v) {
      if (v.apply_live) {
        const h = this.$createElement
        this.$msgbox({
          title: '提示',
          closeOnClickModal: false,
          customClass: 'activity-delete-msgbox',
          message: h('p', null, [
            h('span', null, '当前活动创建时曾申请直播，删除活动后，如需修改直播申请相关信息，可访问 '),
            h('a', { attrs: { href: 'https://live.learn.woa.com' }, style: { color: '#409EFF' } }, 'https://live.learn.woa.com'),
            h('span', null, '。如有直播相关疑问，可企微咨询“小T(连线HR)”。是否确认删除活动？')
          ]),
          showCancelButton: true,
          confirmButtonText: '确定'
        }).then(action => {
          if (action === 'confirm') {
            this.deleteSctivity(v)
          }
        })
      } else {
        this.$confirm('确定删除该活动吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.deleteSctivity(v)
        })
      }
    },
    deleteSctivity(v) {
      activityDeleteApi(v.activity_id).then(res => {
        this.$message.success('删除成功')
        this.onSearch(this.current)
        this.getActivityCount()
      })
    },
    handleAddCourse(v) {
      const { activity_name, photo_url, remark, activity_id } = v
      let href = `https://sdc.qq.com/s/JZH8Eg?scheme_type=activityDetail&activity_id=${activity_id}`
      this.addCourseDialogData = {
        module_id: 4,
        module_name: '活动',
        content_name: activity_name,
        cover_img_url: photo_url,
        description: remark,
        href,
        item_id: activity_id,
        origin: location.origin
      }
      this.addCourseDialogShow = true
    },
    getStatusColor(data) {
      if (data.status === 1) {
        const now = Date.now()
        const start = new Date(data.start_time).getTime()
        const end = new Date(data.end_time).getTime()
        if (now < start) {
          return 'gray-color'
        } else if (now < end) {
          return 'use-color'
        } else {
          return 'stop-color'
        }
      }
      // status 1-上架，3-下架，4-草稿，6-待审核 101-未开始，102-进行中,103-已结束
      if ([3, 6, 101].includes(data.status)) {
        return 'await-color'
      } else if ([1, 102].includes(data.status)) {
        return 'use-color'
      } else if ([103].includes(data.status)) {
        return 'stop-color'
      } else if ([4, 101].includes(data.status)) {
        return 'gray-color'
      }
    },
    handlerTransformUnit(v) {
      return transformUnit(v)
    }
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/activity-common.less';

:deep(.qcode-box .dialog-center) {
  position: absolute !important;
}
.activity-list-page {
  height: 100%;
  .activity-list-page-header {
    >p {
      color: rgba(0, 0, 0, 0.8);
      font-size: 16px;
      font-weight: 700;
      line-height: 24px;
    }
    .search-area {
      margin: 20px 0 12px 0;
      ul {
        display: flex;
        gap: 0 8px;
        li {
          min-width: 100px;
          height: 32px;
          border: 1px solid #eeeeee;
          background: #ffffff;
          border-radius: 4px;
          line-height: 32px;
          text-align: center;
          color: rgba(0, 0, 0, 0.6);
          font-size: 14px;
          cursor: pointer;
          &.active {
            background: #0052d9;
            color: #fff;
          }
          &.search-input {
            margin-left: auto;
            width: 240px;
            :deep(.el-input__inner) {
              height: 32px;
            }
            .icon-search {
              &::before {
                content: '';
                display: inline-block;
                width: 16px;
                height: 16px;
                background: url('~@/assets/img/mobile/search.png') no-repeat;
                background-size: 100% 100%;
              }
            }
          }
        }
        .create-btn {
          margin: 0 12px;
          .el-button {
            width: 100px;
            height: 32px;
            background: #0052d9;
            color: #fff;
            border-radius: 4px;
          }
        }
      }
    }
  }
  .main-table {
    background-color: #fff;
    border-radius: 4px;
    padding: 16px 28px;
    .sort-box {
      display: flex;
      align-items: center;
      padding-bottom: 15px;
      border-bottom: 1px solid rgba(238, 238, 238, 1);

      span:first-of-type {
        color: rgba(0, 0, 0, 0.4);
      }

      ul {
        display: flex;
        margin-left: 8px;

        li {
          color: rgba(0, 0, 0, 0.6);
          font-size: 14px;
          height: 22px;
          line-height: 22px;
          cursor: pointer;
        }

        li+li {
          margin-left: 24px;
        }

        .active-sort {
          color: #0052D9
        }
      }
    }
    .card-list {
      .item-card {
        border-bottom: 1px solid rgba(238, 238, 238, 1);
        padding: 10px 0;

        &-content {
          display: flex;

          .video-cover {
            width: 108px;
            height: 72px;
            margin-right: 12px;
            position: relative;
            cursor: pointer;
            .el-image,.image-slot img {
              width: 108px;
              height: 72px;
            }
            :deep(.el-image__inner) {
              border-radius: 2px;
            }
            .cover-timer {
              position: absolute;
              bottom: 0px;
              width: 108px;
              height: 24px;
              background: linear-gradient(180deg, rgba(217,217,217,0) 0%, rgba(51,51,51,1) 100%);
              text-align: right;
              line-height: 24px;
              span {
                color: rgba(255,255,255,1);
                font-size: 12px;
                height: 16px;
                line-height: 16px;
                margin-right: 4px;
              }
            }
          }

          .text {
            color: rgba(51, 51, 51, 1);
            width: calc(100% - 108px - 12px);
            .video-title {
              margin-bottom: 10px;
              display: flex;
              align-items: center;
              .course-tag {
                display: inline-block;
                font-size: 12px;
                height: 18px;
                line-height: 16px;
                border-radius: 2px;
                border: 1px solid rgba(0, 82, 217, 1);
                color: #0052D9;
                padding: 0 7px;
                margin-right: 8px;
                flex-shrink: 0;
              }
              // 一行省略, 超出显示省略号。宽度自适应
              .course-name {
                flex-shrink: 1;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                display: inline-block;
                height: 20px;
                line-height: 20px;
                word-break: break-all;
                cursor: pointer;
              }
            }

            .content-center {
              margin-bottom: 10px;
            }

            .bottom-icon {
              display: flex;

              .bottom-left {
                display: flex;
                align-items: center;
                flex: 1;
                height: 16px;
                .tool-tips-box {
                  display: flex;
                  align-items: center;
                  margin-left: 16px;
                }
                .el-icon-warning-outline {
                  font-size: 16px;
                  color: #E34D59;
                  margin-left: 4px;
                }

                .icon-box {
                  display: flex;
                  align-items: center;
                  color: rgba(0, 0, 0, 0.4);
                  font-size: 12px;
                }

                .icon-box+.icon-box {
                  margin-left: 16px;
                }

                .icon {
                  width: 16px;
                  height: 16px;
                  display: inline-block;
                  margin-right: 3px;
                }

                .watch-icon {
                  background: url('~@/assets/img/watch.png') no-repeat center/cover;
                }

                .like-icon {
                  background: url('~@/assets/img/zan1.png') no-repeat center/cover;
                }

                .collect-icon {
                  background: url('~@/assets/img/fav2.png') no-repeat center/cover;
                }

                .registed-icon {
                  background: url('~@/assets/img/label/people2x.png') no-repeat center/cover;
                }

                .user-score-icon {
                  background: url('~@/assets/img/score-line.png') no-repeat center/cover;
                }

                .await-color {
                  color: #FF8B6C
                }
                .use-color {
                  color: rgba(0,168,112,1)
                }
                .stop-color {
                  color: rgba(227,77,89,1)
                }
                .deal-color {
                  color:rgba(0,82,217,1);
                  cursor: pointer;
                }
                .no-pass-color {
                  color: #E34D59;
                }
                .gray-color {
                  color: rgba(0, 0, 0, 0.4);
                }
              }

              .bottom-right {
                display: flex;
                align-items: center;
                height: 16px;

                span+span {
                  margin-left: 20px;
                }

                .el-dropdown {
                  margin-left: 20px;
                }

                .right-icon {
                  display: inline-block;
                  width: 16px;
                  height: 16px;
                }
                .other-icon-box {
                  width: 24px;
                  height: 24px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  border-radius: 50%;
                  cursor: pointer;

                  .edit-icon {
                    background: url('~@/assets/img/edit-allow.png') no-repeat center/cover;
                  }
                  .share-icon {
                    background: url('~@/assets/img/share.png') no-repeat center/cover;
                  }
                  .other-icon {
                    background: url('~@/assets/img/other-icon.png') no-repeat center/cover;
                  }
                  .down {
                    background: url('~@/assets/img/down.png') no-repeat center/cover;
                  }
                  .up {
                    background: url('~@/assets/img/up.png') no-repeat center/cover;
                  }
                  .add-circle {
                    background: url('~@/assets/img/add-circle.png') no-repeat center/cover;
                  }
                  .del {
                    background: url('~@/assets/img/del.png') no-repeat center/cover;
                  }
                }

                .other-icon-box:hover {
                  background: rgba(245, 247, 249, 1);

                  .edit-icon {
                    background: url('~@/assets/img/edit-icon-hover.png') no-repeat center/cover;
                  }
                  .share-icon {
                    background: url('~@/assets/img/share-hover.png') no-repeat center/cover;
                  }
                  .other-icon {
                    background: url('~@/assets/img/other-icon-hover.png') no-repeat center/cover;
                  }
                  .down {
                    background: url('~@/assets/img/down-active.png') no-repeat center/cover;
                  }
                  .up {
                    background: url('~@/assets/img/up-active.png') no-repeat center/cover;
                  }
                  .add-circle {
                    background: url('~@/assets/img/add-circle-active.png') no-repeat center/cover;
                  }
                  .del {
                    background: url('~@/assets/img/del-active.png') no-repeat center/cover;
                  }
                }

                .disabled-icon {
                  cursor: not-allowed;
                  pointer-events: none;
                  .edit-icon {
                    background: url('~@/assets/img/edit.png') no-repeat center/cover !important;
                  }
                  .share-icon {
                    background: url('~@/assets/img/stop-share.png') no-repeat center/cover !important;
                  }
                  .up {
                    background:  url('~@/assets/img/up-1.png') no-repeat center/cover !important;
                  }
                  .down {
                    background:  url('~@/assets/img/down-1.png') no-repeat center/cover !important;
                  }
                  .add-circle {
                    background:  url('~@/assets/img/add-circle-1.png') no-repeat center/cover !important;
                  }
                  .del {
                    background:  url('~@/assets/img/del-1.png') no-repeat center/cover !important;
                  }
                }
              }
              .right-not-allow {
                cursor: not-allowed;
                pointer-events: none;
                .other-icon-box {
                  .stop-edit-icon {
                    background: url('~@/assets/img/edit.png') no-repeat center/cover;
                  }
                  .stop-share-icon {
                    background: url('~@/assets/img/stop-share.png') no-repeat center/cover;
                  }
                  // .stop-other-icon {
                  //   background: url('~@/assets/img/other-stop.png') no-repeat center/cover;
                  // }
                  .down {
                    background: url('~@/assets/img/down-1.png') no-repeat center/cover;
                  }
                  .up {
                    background: url('~@/assets/img/up-1.png') no-repeat center/cover;
                  }
                  .add-circle {
                    background: url('~@/assets/img/add-circle-1.png') no-repeat center/cover;
                  }
                  .del {
                    background: url('~@/assets/img/del-1.png') no-repeat center/cover;
                  }
                }
              }
              // .not-use-status {
              //   cursor: not-allowed;
              //   pointer-events: none;
              //   .stop-other-icon {
              //     background: url('~@/assets/img/other-stop.png') no-repeat center/cover !important;
              //   }
              // }
              .disabled-share-icon {
                cursor: not-allowed;
                pointer-events: none;
                .stop-share-icon {
                  background: url('~@/assets/img/stop-share.png') no-repeat center/cover !important;
                }
              }
              .disabled-edit-icon {
                cursor: not-allowed;
                pointer-events: none;
                .stop-edit-icon {
                  background: url('~@/assets/img/edit.png') no-repeat center/cover !important;
                }
              }
              .disabled-down-icon {
                cursor: not-allowed;
                pointer-events: none;
                .stop-down-icon {
                  background:  url('~@/assets/img/down-1.png') no-repeat center/cover !important;
                }
              }
              .disabled-up-icon {
                cursor: not-allowed;
                pointer-events: none;
                .stop-up-icon {
                  background:  url('~@/assets/img/up-1.png') no-repeat center/cover !important;
                }
              }
              .disabled-add-icon {
                cursor: not-allowed;
                pointer-events: none;
                .stop-add-icon {
                  background:  url('~@/assets/img/add-circle-1.png') no-repeat center/cover !important;
                }
              }
              .disabled-del-icon {
                cursor: not-allowed;
                pointer-events: none;
                .stop-del-icon {
                  background:  url('~@/assets/img/del-1.png') no-repeat center/cover !important;
                }
              }
            }
          }
        }
      }

      .item-card:last-of-type {
        border-bottom: 1px solid rgba(238, 238, 238, 1);
      }

      .item-card:hover {
        background: rgba(247, 251, 255, 0.5);
      }
    }
  }
  .introduce-card {
    margin-top: 12px;
    padding: 20px 28px 32px;
    background: #ffffff;
    border-radius: 8px;
    width: 100%;
    user-select: none;
    pointer-events: none;
    .card-title {
      width: fit-content;
      line-height: 32px;
      padding: 0 16px;
      font-size: 12px;
      border-radius: 44px;
      background-color: #006FFF;
      font-weight: 600;
      color: #fff;
      &> img{
        width: 16px;
        height: 16px;
        margin-right: 10px;
      }
    }
    .card-content {
      margin-top: 15px;
      padding: 20px 21px;
      border-radius: 8px;
      background: #FCFCFC;
      .card-content-introduce {
        .card-content-item {
          margin-bottom: 8px;
          .text-box {
            line-height: 22px;
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 8px;
          }
          .card-content-item-title,
          .card-content-item-subtitle {
            color: #0052d9;
          }
          .card-content-item-p {
            color: #111111;
            font-family: "PingFang SC";
            font-size: 12px;
            font-weight: 400;
            line-height: 20px;
            font-style: normal;
          }
          .card-content-preview {
            width: 276px;
            height: 160px;
            margin-top: 8px;
            .img {
              width: 100%;
              height: 100%;
              border-radius: 4px;
              background: linear-gradient(180deg, #F8FCFF 0%, #F0F9FF 100%);
              object-fit: cover;
            }
          }
        }
      }
    }
  }
  .empty {
    text-align: center;
    margin: 15px 0 32px;

    .empty-text {
      line-height: 22px;
      color: #333333;
      font-size: 14px;
      font-weight: 400;
    }

    .empty-img {
      display: inline-block;
      width: 160px;
      height: 160px;
      background: url(~@/assets/classroomImg/activity-empty.png) no-repeat center/contain;
    }
  }
}
</style>
