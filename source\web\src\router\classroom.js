const classroom = [
  {
    path: '/creator-center/face',
    name: 'classroom',
    component: () => import('views/manage/classroom/index.vue'),
    redirect: '/training/creator-center/face/introduce',
    meta: {
      title: '',
      isAuth: true
    },
    children: [
      {
        path: '/creator-center/face/introduce',
        name: 'introduce',
        component: () => import('views/manage/classroom/introduce/index.vue'),
        meta: {
          title: '开班开课入口页'
        }
      },
      {
        path: '/manage/activity/activityinfo',
        name: 'activityPage',
        component: () => import('views/manage/classroom/activity/index.vue'),
        meta: {
          title: '活动'
        }
      },
      {
        path: '/creator-center/activity-list',
        name: 'activityList',
        component: () => import('views/manage/classroom/activity/activityList/index.vue'),
        meta: {
          title: '活动列表'
        }
      },
      {
        path: '/manage/activity/activityEdit',
        name: 'activityEdit',
        component: () => import('views/manage/classroom/activity/edit/index.vue'),
        meta: {
          title: '活动编辑'
        }
      }
    ]
  },
  {
    path: '/activity/activityAdd',
    name: 'activityAdd',
    component: () => import('views/manage/classroom/activity/index.vue'),
    meta: {
      title: '活动'
    }
  },
  {
    path: '/activity/activityList',
    name: 'activityList',
    component: () => import('views/manage/classroom/activity/activityList/index.vue'),
    meta: {
      title: '活动列表'
    }
  },
  {
    path: '/activity/activityEdit',
    name: 'activityEdit',
    component: () => import('views/manage/classroom/activity/edit/index.vue'),
    meta: {
      title: '活动编辑'
    }
  },
  {
    path: '/manage/faceClass',
    name: 'faceClass',
    component: () => import('views/manage/classroom/faceClass/index.vue'),
    meta: {
      title: '面授班'
    }
  },
  {
    // 班级活动审核
    path: '/manage/activity/audit',
    name: 'activityAudit',
    component: () => import('views/manage/classroom/audit/index.vue'),
    meta: {
      title: '班级活动审核'
    }
  }
]
export default classroom
