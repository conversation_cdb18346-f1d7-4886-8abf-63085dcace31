<template>
  <div id="project-detail-page" :class="[{'scroll-show-container': isScrollShow}, 'project-detail-container']" v-if="noPermissionView">
    <CustomTips
      v-if="isPreview"
      title="项目预览中，可查看任务详情，但无法更新任务状态及培训进度"
      IconName="el-icon-warning-outline"
      backgroundColor="#fdf6ec"
      color="#FF7548"
      lineHeight="40px"
      >
    </CustomTips>
    <!-- 顶部详情 -->
    <div class="card">
      <div class="card-img">
        <!-- <img class="geek-time" v-if="isGeek" src="@/assets/mooc-img/geek_time.png" alt="极客时间"> -->
        <img class="geek-time" v-if="showOutcourseIcon" :src="typeImg" alt="">
        <div :class="[moocLang === 'en-us' ? 'en-update-status' : 'update-status', 'common-update-status']" v-if="cardInfo.serial_type === 2 && !isGeek"></div>
        <div class="course-tag">
          <span class="excellent-tag" v-if="cardInfo.excellent_status === 1">{{ $langue('Mooc_Home_ListItems_Excellent', { defaultText: '精品' }) }}</span>
        </div>
        <el-image lazy fit="fill" :src="editImgUrl" class="course-cover">
          <div class="image-slot" slot="placeholder">
            <i class="el-icon-loading"></i>
          </div>
          <div class="error-cover" slot="error">
            <img :src="require('@/assets/mooc-img/default_bg_img.png')" alt="" />
          </div>
        </el-image>
      </div>
      <div class="card-content">
        <div class="top-title">
          <div class="left">
            <el-tooltip
            effect="dark"
            :content="cardInfo.course_title"
            placement="bottom"
            :disabled="course_title_overflow"
            >
              <span class="title overflow-l1" @mouseover="titleOver($event)">{{ cardInfo.course_title }}</span>
            </el-tooltip>
            <div class="external-courses" v-if="isGeek">
              <a class="a" target="_blank" :href="externalCoursesLink">
                <span class="icon"></span>{{ outsourceLink.special_page_content }}
                <span class="arrow"></span>
              </a>
            </div>
          </div>
          <div class="right-opera-mobile">
            <el-popover
              popper-class="mobile-popove-box"
              placement="bottom"
              trigger="hover"
              @show="getMobileUrl"
              >
                <div class="popove-body">
                  <p class="title">{{ $langue('Mooc_ProjectDetail_BasicInfo_ViewByMobile', { defaultText: '移动端查看' }) }}</p>
                  <img :src="qrUrl" v-if="qrUrl"/>
                  <div class="qrcode-tips">{{$langue('Mooc_ProjectDetail_StudyByWechat', { defaultText: '请使用微信/企业微信扫码查看培养项目' })  }}</div>
                  <el-input v-model="urlMobile" type="text" disabled>
                    <template slot="append">
                      <el-button @click="doCopy()">{{ $langue('Mooc_ProjectDetail_Copy', { defaultText: '复制' }) }}</el-button>
                    </template>
                  </el-input>
                </div>
              <span slot="reference" class="mobile-box"><span class="mobile-icon"></span><span class="mobile-tips">{{ $langue('Mooc_ProjectDetail_BasicInfo_ViewByMobile', { defaultText: '移动端查看' }) }}</span></span>
            </el-popover>
            <span v-if="isShowProjectManage" class="set-box" @click="toProjectManage"><span class="set-icon"></span>{{ $langue('Mooc_ProjectDetail_BasicInfo_ProjManagement', { defaultText: '项目管理' }) }}</span>
          </div>
        </div>
        <!-- 极客时间 -->
        <div class="mid-title">
          <template v-if="(isGeek && noJoinSource) || cardInfo.consume_appid">
            <div class="integral">900积分</div>
            <div class="free">
              {{outsourceLink.activity_page_content}}
              <span v-if="geekPricedCourses && geekPurchaseInfo.course_val && noJoinSource">（需 <b class="important">{{geekPurchaseInfo.course_val}}</b> 张学霸卡，
               <span style="text-decoration: underline;cursor: pointer;" @click="toSourceDetail">点此查看获取方式和活动规则</span>）
              </span>
            </div>
          </template>
          <template v-else>
            <span class="time-icon"></span>
            <span class="label mgR-36" v-html="courseType(personInfo)">
            </span>
          </template>
          <span class="source-detail-content" v-if="isGeek && geekPricedCourses && !noJoinSource" @click="toSourceDetail">
            <span class="help-icon"></span>
            <!-- <span class="source-detail">查看如何获得学霸卡</span> -->
            <span class="source-detail">点击查看活动详情</span>
          </span>
        </div>
        <div class="bottom-title">
          <div class="label"><span class="task-icon icon"></span>{{cardInfo.task_count}}{{ $langue('Mooc_ProjectDetail_BasicInfo_Tasks', { defaultText: '项任务' }) }}</div>
          <div class="label" v-show="cardInfo.show_join_count"><span class="person-icon icon"></span>{{cardInfo.user_count}}{{ $langue('Mooc_ProjectDetail_BasicInfo_Students', { defaultText: '人参与学习' }) }}</div>
          <div class="label">
            <span class="like-icon icon"></span>
            <span>{{ personInfo.scorer_count >= 20 ? personInfo.score :  $langue('Mooc_ProjectDetail_BasicInfo_LowerScorer', { defaultText: '评分人数不足' }) }}</span>
            <span v-if="personInfo.register" class="grade-btn" @click="gradeDialogShow = true" :dt-eid="dtBtn('eid', {text: '去评分'})" :dt-areaid="dtBtn('area', {text: '去评分'})" :dt-remark="dtBtn('remark', {text: '去评分'})">
              {{ scoreStatus > 0 ? $langue('Mooc_ProjectDetail_BasicInfo_Scored', { defaultText: '已评分' }) : $langue('Mooc_ProjectDetail_BasicInfo_Score', { defaultText: '去评分' }) }}
              <i class="el-icon-arrow-right"></i>
            </span>
          </div>
        </div>
        <div class="bottom-icon-content">
          <div class="left-icon">
            <span
              @click="handlePraise"
              :class="[personInfo.praise ? 'person-like-label' : 'person-no-like-label','label']"
              :dt-eid="dtdianzan('eid')"
              :dt-remark="dtdianzan('remark')"
            >
              <span class="like-icon no-like-icon icon"></span>
              {{ personInfo.praise ? `${$langue('Mooc_ProjectDetail_BasicInfo_Praised', { defaultText: '已点赞' })}(${personInfo.praise_count || 0})` : `${$langue('Mooc_ProjectDetail_BasicInfo_Prais', { defaultText: '点赞' })}(${personInfo.praise_count || 0})` }}
            </span>
            <span
              @click="handleFav"
              :class="[personInfo.favorite ? 'person-fav-label' : 'person-no-fav-label','label']"
              :dt-eid='dtCollect("eid")'
              :dt-remark="dtCollect('remark')"
            >
              <span class="fav-icon fav-no-icon icon"></span>

              {{ personInfo.favorite ? `${$langue("Mooc_ProjectDetail_BasicInfo_collected", { defaultText: '已收藏' })}(${personInfo.fav_count || 0})` : `${$langue('Mooc_ProjectDetail_BasicInfo_collect', { defaultText: '收藏' })}(${personInfo.fav_count || 0})` }}
            </span>
            <span
              @click="activeName = 'fourth'"
              :class="[personInfo.comment ? 'person-chat-label' : 'person-no-chat-label','label']"
              :dt-eid='dtComments("eid")'
              :dt-remark="dtComments('remark')"
            >
              <span class="chat-icon chat-no-icon icon"></span>
              {{ `${$langue("Mooc_ProjectDetail_BasicInfo_Comments", { defaultText: '评论' })}(${personInfo.comment_count || 0})` }}
            </span>
          </div>
          <div class="right-opera">
            <span v-if="cardInfo.enable_grant_certificate === 1" class="certificate-label"><span class="certify-icon"></span>{{ $langue('Mooc_ProjectDetail_BasicInfo_GetCertificate' , { defaultText: '完成项目获得证书' }) }}</span>
            <!-- course_status 0-待发布，1-已发布，3-已结束 ---项目状态 -->
            <!-- 超级管理员开启了自主报名且在学习时间内 -->
            <span v-if="cardInfo.enabled_registration && showOutBtn && is_target">
                <!-- <el-button v-if="isGeek && geekPricedCourses && showInvite" size='small' type="primary"  @click="handlerOpenActive">
                    <span class="invite-img">邀请同事，得学霸卡</span>
                </el-button> -->
              <!-- 极客 体系课-试学 -->
              <template v-if="isGeekFreeLearningSection">
                <span class="exchange-btn" @click="purchaseCourses">无需试学，直接兑换</span>
                <el-button style="margin-left:20px;" type="primary" size='small' @click="activeName = 'second'">试学课程</el-button>
              </template>
              <!-- 极客 体系课-不支持试学 -->
              <template v-else-if="geekCoursesToPurchased">
                <el-button style="margin-left:20px;" type="primary" size='small' @click="purchaseCourses">兑换课程</el-button>
              </template>
              <!-- 体系课程已购买显示退出 -->
              <!-- <template v-else-if="havePurchasedCourses">
                <el-button  size='small'>
                 <span>退出项目</span>
                </el-button>
              </template> -->
              <template v-else>
                <template v-if="controlBtn">
                  <el-button style="margin-left:20px;" v-if="isGeek" type="primary" size='small' @click="handleEnroll">加入课程</el-button>
                  <el-button style="margin-left:20px;" v-else type="primary" size='small' @click="handleEnroll" :dt-eid="dtBtn('eid', {text: '报名加入项目'})" :dt-areaid="dtBtn('area', {text: '报名加入项目'})" :dt-remark="dtBtn('remark', {text: '报名加入项目'})">{{ $langue('Mooc_ProjectDetail_BasicInfo_RegistProj', { defaultText: '报名加入项目' }) }}</el-button>
                </template>
                <el-button v-else-if="!controlBtn && !isGeek && trainProgressInfo.join_type && [2, 3].includes(trainProgressInfo.join_type)" size='mini' @click="cancelEnrollShow=true" :dt-eid="dtBtn('eid', {text: '退出项目'})" :dt-areaid="dtBtn('area', {text: '退出项目'})" :dt-remark="dtBtn('remark', {text: '退出项目'})">{{ $langue('Mooc_ProjectDetail_Exit', { defaultText: '退出项目' }) }}</el-button>
              </template>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div class="bottom-content">
      <!-- 左侧公告 -->
      <div class="left-progress" v-if="personInfo.register">
        <div class="top-progress">
          <div class="top-box">
            <span class="train-icon"></span>
            <span class="lf-title">{{ $langue('Mooc_ProjectDetail_TrainingProgress_Progress', { defaultText: '培训进度' }) }}</span>
            <span class="train-status" :class="trainProgressIcon"></span>
          </div>
          <div class="progress-box">
            <el-progress :percentage="percentageValue" style="width: 245px" :style="{'color': progressColor}" :color="progressColor" :stroke-width="8"></el-progress>
            <el-tooltip effect="dark" :content="$langue('Mooc_ProjectDetail_TrainingProgress_FinishedAllRequired', { defaultText: '完成所有应学任务即完成培养项目' })" placement="top">
              <span class="tips-i"><i class="el-icon-warning-outline"></i></span>
            </el-tooltip>
          </div>
          <div class="lastly-study-box">
            <!-- 未开始 -->
            <div v-if="trainProgressInfo.learn_status === 1" class="no-start-project">
              <div class="no-start-title">{{ noStartTime ? `${$langue('Mooc_ProjectDetail_TrainingProgress_LessStartTime', { defaultText: '未到学习开始时间' })}${trainProgressInfo.proj_start_time}` : $langue('Mooc_ProjectDetail_TrainingProgress_NoRequireRecord', { defaultText: '暂无应学任务学习记录，请尽快开始学习"' }) }}</div>
              <geek-tips v-if="geekPricedCourses" num="1" :link="sourceDetailLink"></geek-tips>
              <el-tooltip :disabled="!noStartTime" effect="dark" :content="$langue('Api_Mooc_Project_ProjectNotStart', { defaultText: '项目未开始，无法开始学习' })" placement="top">
                <span>
                  <el-button :disabled="noStartTime" type="primary" size="medium" @click="handleStart('1')" :dt-eid="dtBtn('eid', {text: '开始学习'})" :dt-areaid="dtBtn('area', {text: '开始学习'})" :dt-remark="dtBtn('remark', {text: '开始学习'})">
                    {{ $langue('Mooc_ProjectDetail_TrainingProgress_BeginStudy', { defaultText: '开始学习' }) }}
                  </el-button>
                </span>
              </el-tooltip>
            </div>
            <!-- 进行中 -->
            <div v-else-if="trainProgressInfo.learn_status === 2" class="going-project">
              <div v-if="endProject">{{ $langue('Mooc_ProjectDetail_TrainingProgress_CannotStudyProjEnd', { defaultText: '项目已结束，无法继续学习' }) }}</div>
              <div v-else class="last-study">
                <span class="lastly-tag">{{ $langue("Mooc_ProjectDetail_TrainingProgress_LastStudy", { defaultText: '上次学习' }) }}</span>
                <span class="lastly-title">{{ trainProgressInfo.latest_task_name }}</span>
              </div>
              <geek-tips v-if="geekPricedCourses" num="1" :link="sourceDetailLink"></geek-tips>
              <el-button :disabled="endProject" type="primary" size="medium" @click="handleStart('2')" :dt-eid="dtBtn('eid', {text: '继续学习'})" :dt-areaid="dtBtn('area', {text: '继续学习'})" :dt-remark="dtBtn('remark', {text: '继续学习'})">{{ $langue('Mooc_Common_Alert_ContinueStudy', { defaultText: '继续学习' }) }}</el-button>
            </div>
            <!-- 已完成 -->
            <div v-else-if="trainProgressInfo.learn_status === 4" class="finish-project">
              <p class="finsh-tag">{{ $langue('Mooc_ProjectDetail_TrainingProgress_ProjectFinished', { defaultText: '已完成培养项目' }) }}</p>
              <p class="finsh-time">{{ $langue('Mooc_ProjectDetail_TrainingProgress_FinishedTime', { defaultText: '完成时间：' }) }}{{ trainProgressInfo.finish_time }}</p>
              <geek-tips v-if="geekPricedCourses" num="1" :link="sourceDetailLink"></geek-tips>
            </div>
            <!-- 逾期 -->
            <div v-else-if="trainProgressInfo.learn_status === 3" class="end-project">
              <div class="icon-tips"><i class="el-icon-warning-outline"></i></div>
              <span class="end-project-title"> {{ $langue('Mooc_ProjectDetail_TrainingProgress_DelayCanNotStudy', { defaultText: '逾期未完成所有应学任务，无法继续学习，如有需要，请联系项目管理员延长学习结束时间。' }) }}</span>
              <geek-tips v-if="geekPricedCourses" num="1" :link="sourceDetailLink"></geek-tips>
            </div>
            <div class="progress-show-data">
              <div class="item-data">
                <P>{{ `${trainProgressInfo.required_task_finish_count || 0}/${trainProgressInfo.required_task_count || 0}` }}</P>
                <P>{{ $langue('Mooc_ProjectDetail_TrainingProgress_RequiredProgress', { defaultText: '应学进度' }) }}</P>
              </div>
              <div class="line"></div>
              <div class="item-data">
                <P>{{ `${trainProgressInfo.non_required_task_finish_count || 0}/${trainProgressInfo.non_required_task_count || 0}` }}</P>
                <P>{{ $langue('Mooc_ProjectDetail_TrainingProgress_NonRequiredProgress', { defaultText: '选学进度' }) }}</P>
              </div>
              <div v-if="cardInfo.enable_grant_certificate === 1" class="line"></div>
              <div v-if="cardInfo.enable_grant_certificate === 1" @click="handleChekcCertificate" :class="[{'active-certificate': trainProgressInfo.certificate_count > 0}, 'item-data']">
                <P>{{ trainProgressInfo.certificate_count || 0 }}</P>
                <P>{{ $langue('Mooc_ProjectDetail_TrainingProgress_Certification', { defaultText: '证书' }) }}</P>
              </div>
            </div>
          </div>
        </div>
        <div class="bottom-notice">
          <div class="notice-title-box">
            <span class="item-icon-box">
              <span class="notice-icon"></span>
              <span class="notice-title">{{ $langue('Mooc_ProjectDetail_Notice_ProjNotices', { defaultText: '项目公告' }) }}</span>
            </span>
            <span class="item-icon-box" @click="handleFlagRead('all')">
              <span class="flag-icon"></span>
              <span class="flag-title">{{ $langue('Mooc_ProjectDetail_Notice_Readed', { defaultText: '标记为已读' }) }}</span>
            </span>
          </div>
          <div class="notice-content-box" v-if="noticeList.length">
            <div class="notice-card" v-for="(v,index) in noticeList" :key="index">
              <div class="notice-content" @click="toNotice(v)">
                <span :class="[{'no-view-index': !v.viewed }, 'tips-num']">{{ `${index + 1}.` }}</span>
                <span :class="[{'no-view': !v.viewed},'notice-value']">{{ v.title }}</span>
              </div>
              <div class="publish-time">
                  <span class="publish-icon"></span>
                  <span>{{$langue("Mooc_ProjectDetail_Notice_PublishTime", { defaultText: '发布时间' })}}：{{ v.prepare_publish_time }}</span>
              </div>
            </div>
          </div>
          <div class="bottom-text" v-show="isEmpty">
            <img class="empty-img" :src="empty" alt="" />
            <div class="empty-text">{{$langue('Mooc_Common_Alert_NoData', { defaultText: '暂无内容' })}}~</div>
          </div>
        </div>
      </div>
      <div :class="[{'no-register-tab-content': personInfo.register}, 'tab-content']">
        <!-- 只用于样式 -->
        <div class="fix-bg"></div>
        <el-tabs v-model="activeName">
          <el-tab-pane name="first" :key="tabKey('first')">
            <span slot="label" :dt-eid="dtTab('eid', { text: '项目介绍'})" :dt-remark="dtTab('remark', { text: '项目介绍' })" :dt-areaid="dtTab('areaid', { text: '项目介绍' })">{{ $langue('Mooc_ProjectDetail_Desc_Introduce', { defaultText: '项目介绍' }) }}</span>
          </el-tab-pane>
          <el-tab-pane name="second" :key="tabKey('second')">
            <span slot="label" :dt-eid="dtTab('eid', { text: '任务列表'})" :dt-remark="dtTab('remark', { text: '任务列表' })" :dt-areaid="dtTab('areaid', { text: '任务列表' })">{{ $langue('Mooc_TaskDetail_Navigation_TaskList', { defaultText: '任务列表' }) }}</span>
          </el-tab-pane>
          <el-tab-pane v-if="personInfo.register" name="third" :key="tabKey('third')">
            <span slot="label" :dt-eid="dtTab('eid', { text: '学习资料'})" :dt-remark="dtTab('remark', { text: '学习资料' })" :dt-areaid="dtTab('areaid', { text: '学习资料' })">{{ $langue('Mooc_ProjectDetail_Documents_LearnResources', { defaultText: '学习资料' }) }}</span>
          </el-tab-pane>
          <el-tab-pane v-if="cardInfo.show_comments || cardInfo.show_comments === null" name="fourth" :key="tabKey('fourth')">
            <span slot="label" :dt-eid="dtTab('eid', { text: '项目评论'})" :dt-remark="dtTab('remark', { text: '项目评论' })" :dt-areaid="dtTab('areaid', { text: '项目评论' })">{{ $langue('Mooc_ProjectDetail_Comments_ProjectComment', { defaultText: '项目评论' }) }}</span>
          </el-tab-pane>
        </el-tabs>
        <!-- 项目介绍 -->
        <projectIntroduction
        v-if="activeName === 'first'"
        :detailData="detailData"
        :adminList="adminList"
        :courseType="detailData.resource_from"
        :register="personInfo.register"
        >
        </projectIntroduction>
        <!-- 任务列表 -->
        <taskList
        v-if="activeName === 'second'"
        :personInfo="personInfo"
        :trainProgressInfo=trainProgressInfo
        :registerBTtn="controlBtn && cardInfo.enabled_registration && is_target"
        :register="personInfo.register"
        :geekInfo="taskGeekInfo"
        @handleEnroll="handleEnroll"
        @handlePurchase="purchaseCourses"
        @handleStudyTips="handleStudyTips"
        :dtTitle="cardInfo.course_title"
        ></taskList>
        <!-- 学习资料 -->
        <studyMeans v-if="activeName === 'third'"></studyMeans>
        <!-- 项目评论 -->
        <div v-if="activeName === 'fourth' && cardInfo.show_comments !== false" class="comment-box">
          <CustomTips
            v-if="isDisabledComment && !isPreview"
            class="auto-tips"
            :title="$langue('Mooc_ProjectDetail_Comments_CannotCommentNotInProj', { defaultText: '暂未加入培养项目，无法发布评论' })"
            IconName="el-icon-warning"
            backgroundColor="#fdf6ec"
            color="#FF7548"
            lineHeight="40px"
            :clickTitle="customTipsClickText"
            @confirm="handleStudyEnroll"
            :dt-eid="dtTips('eid')"
            :dt-areaid="dtTips('area')"
            :dt-remark="dtTips('remark')"
            >
          </CustomTips>
          <sdc-comment
            :params="commentParams"
            @setCommentCount="setCommentCount"
          />
        </div>
      </div>
    </div>
    <!-- 切换双语图标 -->
    <!-- 双语暂时注释 -->
    <div class="change-lang-main" @click="handleChangelang">
      <img :src="langIcon" alt="">
      <div class="change-lang-title">{{moocLang === 'en-us' ? '简体中文' : 'English'}}</div>
    </div>
    <!-- 返回顶部 -->
    <transition name="scroll">
      <div class="back-top-icon" v-if="isScrollShow" @click="handleBackTop">
        <img :src="require('@/assets/mooc-img/back-top.png')">
        <p class="top-tag">TOP</p>
      </div>
    </transition>
    <!-- 评分 -->
    <gradeDialog v-if="gradeDialogShow" :visible.sync="gradeDialogShow" :scoreStatus="scoreStatus" @getScore="getScore"></gradeDialog>
    <!-- 取消报名 -->
    <cancelEnroll v-if="cancelEnrollShow" :visible.sync="cancelEnrollShow" @outProject="getPersonStatus"></cancelEnroll>
    <!-- 公告详情 -->
    <noticeDialog v-if="noticeDialog" :visible.sync="noticeDialog" :noticeInfo="noticeInfo"></noticeDialog>
    <!-- 证书列表弹窗 -->
    <certificate-list-popup ref="certificateListPopup" moduleType="user" :isShowCertificatePopup.sync="isShowCertificatePopup"></certificate-list-popup>
    <!-- 极客 - 课程兑换弹窗 -->
    <!--  -->
    <redeemCoursesPopup v-if="isGeek && redeemCoursesShow"  :courseType="detailData.resource_from" :visible.sync="redeemCoursesShow" :purchaseData="geekPurchaseInfo" :consume_appid="cardInfo.consume_appid" :status="redeemPopupStatus" @handleRegistered="handleRegistered" @handleClose="redeemPopupStatus = -1" @redeemCourses="purchaseCourses"></redeemCoursesPopup>
    <!-- 分享 -->
    <xue-ba-share :isShow.sync="giveDialog" :courseType="detailData.resource_from" shearType="detail"></xue-ba-share>
  </div>
  <!--页面状态处理 -->
  <taskErrorPage v-else :pageError="pageError" :autoSignUpParams="autoSignUpParams" @handleJoin="handleEnroll"/>
</template>
<script>
import CustomTips from '@/components/tips.vue'
import taskList from './taskList.vue'
import gradeDialog from './child/gradeDialog'
import cancelEnroll from './child/cancelEnroll'
import projectIntroduction from './projectIntroduction.vue'
import studyMeans from './studyMeans.vue'
import noticeDialog from './child/noticeDialog.vue'
import CertificateListPopup from '@/views/components/certificate-list-popup.vue'
import taskErrorPage from '../../../components/taskErrorPage.vue'
import redeemCoursesPopup from '../../outsourced-course/components/redeemCoursesPopup.vue'
import XueBaShare from '../../outsourced-course/components/xueBaShare.vue'
import {
  projectDetailAPI,
  personDetail,
  cardDetail,
  moocPraise,
  moocfav,
  moocEnroll,
  getUerScore,
  deleteMoocPraise,
  deleteMoocfav,
  getDetailNoticeList,
  noticeNoPreview,
  getTrainProgress,
  getMobileQrcode,
  getCoursePurchaseInfo,
  getGeekCourseDetail,
  getOutsourceLinkConfig,
  purchaseSourceFromConfig
} from '@/config/mooc.api.conf.js'
import { mapState } from 'vuex'
// import env from 'config/env.conf.js'
import translate from 'mixins/translate.vue'
import { pcCoverLogo } from '@/utils/outsourcedCourseMap.js'
import { pageExposure, debounce } from '@/utils/tools.js'

var geekTips = {
  props: ['num', 'link'],
  template: `
    <div class="geek-tips">
      <div class="tips">学习进度达标，即可获赠 {{num}} 张外部好课学霸卡</div>
      <div class="btn">
        <span class="help-icon"></span>
        <a class="source-detail" target="_blank" :href="link">点击查看活动详情</a>
      </div>
    </div>
  `
}

export default {
  mixins: [translate],
  components: {
    taskList,
    gradeDialog,
    cancelEnroll,
    projectIntroduction,
    studyMeans,
    noticeDialog,
    CustomTips,
    taskErrorPage,
    CertificateListPopup,
    redeemCoursesPopup,
    XueBaShare,
    geekTips
  },
  data() {
    return {
      // externalCoursesLink: 'https://sdc.qq.com/s/sYkoua?scheme_type=homepage&page_id=493', // 外部好课专区
      activeName: 'first',
      checkList: 1,
      detailData: {
        course_labels: [],
        course_admins: '',
        course_desc: '',
        course_desc_detail: ''
      },
      personInfo: {},
      cardInfo: {},
      gradeDialogShow: false,
      mobileShareShow: true,
      cancelEnrollShow: false,
      empty: require('@/assets/img/empty.png'),
      scoreStatus: 0,
      course_title_overflow: true,
      noticeList: [], // 公告列表
      isEmpty: false,
      noticeDialog: false,
      trainProgressInfo: {},
      noticeInfo: {},
      commentParams: {},
      adminList: [],
      pageError: {
        noPermission: true,
        inaccessible: true,
        tips: '',
        isConcatAdmin: 0, // 0 隐藏管理员
        concatAdmins: [],
        timeText: ''
      },
      isDisabledComment: false,
      isShowCertificatePopup: false,
      editImgUrl: '',
      logoSrc: require('@/assets/mooc-img/tencent-study.png'),
      qrUrl: '',
      isScrollShow: false,
      geekCouresInfo: {}, // 极客课程信息
      geekPurchaseInfo: {}, // 购买信息
      redeemCoursesShow: false,
      redeemPopupStatus: -1,
      outsourceLink: {},
      giveDialog: false,
      sourceFromConfig: []
    }
  },
  computed: {
    ...mapState(['userInfo', 'userLimitInfo', 'moocLang']),
    // 自动报名参数
    autoSignUpParams() {
      const { register = false, resource_from = null, register_confirm = 1 } = this.personInfo
      return {
        register,
        resource_from,
        register_confirm
      }
    },
    // 专区名称
    zoneName() {
      let resource_from = this.detailData.resource_from
      let consume_appid = this.cardInfo.consume_appid
      let zonetext = ''
      if (consume_appid && consume_appid === 'guanaiyueXueba') {
        zonetext = '关爱月好课专区'
      } else if (resource_from === 'geekBang') {
        zonetext = '极客时间课程专区'
      } else if (resource_from === 'sanjieke') {
        zonetext = '三节课好课专区'
      } else {
        zonetext = '外部好课专区'
      }
      return zonetext
    },
    // 埋点
    dtdianzan() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.cardInfo.course_title,
            page_type: '项目详情页',
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '点赞',
            terminal: 'PC'
          })
        } else {
          let { mooc_course_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_like`
        }
      }
    },
    dtCollect() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.cardInfo.course_title,
            page_type: '项目详情页',
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '收藏',
            terminal: 'PC'
          })
        } else {
          let { mooc_course_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_collect`
        }
      }
    },
    dtComments() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.cardInfo.course_title,
            page_type: '项目详情页',
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '评论',
            terminal: 'PC'
          })
        } else {
          let { mooc_course_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_comments`
        }
      }
    },
    showOutcourseIcon() {
      let coverLogoArray = Object.keys(pcCoverLogo) || []
      return coverLogoArray.includes(this.detailData?.resource_from)
    },
    typeImg() {
      return pcCoverLogo[this.detailData.resource_from] || pcCoverLogo['geekBang']
    },
    // 是否是外部课程
    isGeek() {
      return this.sourceFromConfig.includes(this.detailData.resource_from)
    },
    // 是否显示按钮
    showInvite() {
      return this.detailData.resource_from === 'sanjieke'
    },
    // 是否是外部课程试学
    isGeekFreeLearningSection() {
      // 极客类型 && 课程需要购买 && 用户还没购买 && 是否支持试学
      return this.isGeek && this.geekCouresInfo?.course_acquisition_type === 2 && !this.geekCouresInfo?.purchased && this.geekCouresInfo?.can_preview
    },
    // 外部课程 需要购买还未购买的非试学课程
    geekCoursesToPurchased() {
      // 极客类型 && 课程需要购买 && 用户还没购买 && 不支持试学
      return this.isGeek && this.geekCouresInfo?.course_acquisition_type === 2 && !this.geekCouresInfo?.purchased && !this.geekCouresInfo?.can_preview
    },
    // 极客 需要购买课程
    geekPricedCourses() {
      return this.isGeek && this.geekCouresInfo?.course_acquisition_type === 2
    },
    // 已经购买的课程
    havePurchasedCourses() {
      return this.isGeek && this.geekCouresInfo?.purchased
    },
    // 任务栏 - 极客信息
    taskGeekInfo() {
      return {
        isGeekFreeLearningSection: this.isGeekFreeLearningSection,
        isGeek: this.isGeek,
        canPreview: this.geekCouresInfo.can_preview,
        courseAcquisitionType: this.geekCouresInfo.course_acquisition_type,
        total: this.geekPurchaseInfo.allow_preview_num || 0,
        study: this.geekPurchaseInfo.previewed_num || 0,
        previewRecords: this.geekPurchaseInfo.preview_records || [],
        courseStockTotal: this.geekPurchaseInfo.course_stock_total,
        courseFrom: this.detailData.resource_from
      }
    },
    percentageValue() {
      const { required_task_finish_count, required_task_count } = this.trainProgressInfo
      return Number(((required_task_finish_count / required_task_count) * 100).toFixed(1)) || 0
    },
    trainProgressIcon() {
      let className = ''
      switch (this.trainProgressInfo.learn_status) {
        case 1:
          className = this.moocLang === 'en-us' ? 'en-icon-wkx' : 'icon-wks' // 未开始
          break
        case 2:
          className = this.moocLang === 'en-us' ? 'en-icon-jxz' : 'icon-jxz' // 进行中
          break
        case 3:
          className = this.moocLang === 'en-us' ? 'en-icon-yyq' : 'icon-yyq' // 已逾期
          break
        case 4:
          className = this.moocLang === 'en-us' ? 'en-icon-ywc' : 'icon-ywc' // 已完成
          break
        default:
          break
      }
      return className
    },
    progressColor() {
      return this.trainProgressInfo.learn_status === 4 ? '#00a870' : this.trainProgressInfo.learn_status === 3 ? '#00000042' : '#266FE8'
    },
    // 没有到学习开始时间
    noStartTime() {
      const { course_start_time, course_period_type } = this.personInfo
      return course_period_type === 1 && new Date().getTime() < new Date(course_start_time).getTime()
    },
    // 不限起止时间--项目进行中结束项目的情况
    endProject() {
      const { course_period_type } = this.trainProgressInfo
      const { course_status } = this.detailData
      return course_period_type === 3 && course_status === 4
    },
    mooc_course_id() {
      return this.$route.query.mooc_course_id || ''
      // TODO: 调试用
      // return 'q7In9W9K'
    },
    noPermissionView() {
      const { have_auth_to_view, have_released, course_status, course_period_type, start_time, end_time, register } = this.cardInfo
      const isPassTime = course_status === 1 && course_period_type === 1 && end_time && (new Date().getTime() > new Date(end_time).getTime()) && !register // 已逾期的
      const isStartTime = course_period_type === 1 && start_time && (new Date(start_time).getTime()) > new Date().getTime() // 未开始
      if (isPassTime) return false
      if (isStartTime) return false
      return have_released && have_auth_to_view
    },
    isShowProjectManage() {
      const { staff_id } = this.userInfo
      const { supper_admin } = this.userLimitInfo
      return supper_admin || (this.adminList || []).some((e) => e.admin_id === staff_id.toString())
    },
    courseType() {
      return (personInfo) => {
        const { course_start_time, course_end_time, period_day, course_period_type, register, reg_time, start_time, end_time } = personInfo
        if (course_period_type === 3) {
          return `${this.$langue('Mooc_ProjectDetail_BasicInfo_ProjStudyTime', { defaultText: '项目学习时间：' })} <span style="color: #000000e6;">${this.$langue('Mooc_ProjectDetail_BasicInfo_NotLimitTime', { defaultText: '不限定项目学习时间' })}<span/>`
          // '不限定项目学习时间'
        }
        if (register && !this.isPreview) {
          return course_period_type === 1 ? `${this.$langue('Mooc_ProjectDetail_BasicInfo_ProjStudyTime', { defaultText: '项目学习时间：' })}${course_start_time} ${this.$langue('Mooc_Home_MyProject_To', { defaultText: ' 至 ' })} ${course_end_time}` :
            `${this.$langue('Mooc_ProjectDetail_BasicInfo_ProjStudyTime', { defaultText: '项目学习时间：' })}${reg_time} ${this.$langue('Mooc_Home_MyProject_To', { defaultText: ' 至 ' })} ${course_end_time}`
        } else {
          // 限定项目学习起止时间
          return course_period_type === 1 ? `${this.$langue('Mooc_ProjectDetail_BasicInfo_LimitStudyCycle', { defaultText: '限定项目学习起止时间：' })}：${start_time} ${this.$langue('Mooc_Home_MyProject_To', { defaultText: ' 至 ' })} ${end_time}` :
            `${this.$langue('Mooc_ProjectDetail_BasicInfo_LimitStudyTime', { days: period_day, defaultText: `限定项目学习周期：${period_day}天` })}`
        }
      }
    },
    showOutBtn() {
      // 限定项目学习时间, 限定学习时间内可以报名，其他的放开报名
      const { course_end_time, course_period_type, course_start_time, start_time, end_time } = this.personInfo
      const starTime = course_start_time || start_time
      const endTime = course_end_time || end_time
      return course_period_type === 1 ?
        new Date(starTime).getTime() < new Date().getTime() && new Date().getTime() < new Date(endTime).getTime() : true
    },
    urlMobile() {
      const url = process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/Zkma1k' : 'http://s.test.yunassess.com/s/UxEYPE'
      return `${url}?scheme_type=mooc&mooc_course_id=${this.mooc_course_id}`
    },
    controlBtn() {
      // 项目上架中，用户有没有报名，再看用户是否在学习时间内(3个条件)
      return this.detailData.course_status === 1 && !this.personInfo.register && this.showOutBtn
    },
    // 是否未加入课程
    noJoinSource() {
      return this.detailData.course_status === 1 && !this.personInfo.register
    },
    // 在目标学员中
    is_target() {
      return this.cardInfo.is_target
    },
    // 双语图标
    langIcon() {
      return this.moocLang === 'en-us' ? require('@/assets/img/china.png') : require('@/assets/img/english.png')
    },
    tabKey() {
      return (name) => {
        return name + new Date().getTime()
      }
    },
    isPreview() {
      return this.$route.query.previewType === 'preview'
    },
    customTipsClickText() {
      if (this.controlBtn && this.cardInfo.enabled_registration && this.is_target) {
        if (this.isGeekFreeLearningSection) {
          return '点击兑换并加入项目'
        } else {
          return this.$langue('Mooc_ProjectDetail_BasicInfo_ClickRegistProj', { defaultText: '点击报名加入项目' })
        }
      }
      return ''
    },
    // 课程详情链接
    sourceDetailLink() {
      return this.outsourceLink.activity_page_link
      // const path = process.env.NODE_ENV === 'production' ? 'https://portal.learn.woa.com' : 'https://test-portal-learn.woa.com'
      // const { resource_from } = this.detailData
      // let pathObj = {
      //   'geekBang': `${path}/training/outsourcedCourse/user/activePage`,
      //   'sanjieke': `${path}/training/outsourcedCourse/user/activePage/sanjieke`
      // }
      // return pathObj[resource_from]
    },
    externalCoursesLink() { // 外部好课专区
      // let objMap = {
      //   'geekBang': {
      //     'link1': 'https://sdc.qq.com/s/sYkoua?scheme_type=homepage&page_id=493', // 需要购买
      //     'link2': 'https://sdc.qq.com/s/1ZyHTE?scheme_type=moduledetail&config_id=4785&name=%E5%A4%96%E9%83%A8%E6%8A%80%E6%9C%AF%E5%A5%BD%E8%AF%BE&page_id=107&lang=zh' // 无需购买
      //   },
      //   'sanjieke': {
      //     'link1': 'https://sdc.qq.com/s/sYkoua?scheme_type=homepage&page_id=503',
      //     'link2': 'https://sdc.qq.com/s/1ZyHTE?scheme_type=moduledetail&config_id=4785&name=%E5%A4%96%E9%83%A8%E6%8A%80%E6%9C%AF%E5%A5%BD%E8%AF%BE&page_id=107&lang=zh'
      //   }
      // }
      // let link1 = 'https://sdc.qq.com/s/sYkoua?scheme_type=homepage&page_id=493' // 需要购买
      let link2 = 'https://sdc.qq.com/s/1ZyHTE?scheme_type=moduledetail&config_id=4785&name=%E5%A4%96%E9%83%A8%E6%8A%80%E6%9C%AF%E5%A5%BD%E8%AF%BE&page_id=107&lang=zh' // 无需购买
      
      return (this.isGeek && this.geekCouresInfo?.course_acquisition_type === 2) || this.cardInfo.consume_appid ? this.outsourceLink.special_page_link : link2
    }
  },
  watch: {
    activeName(val) {
      if (val === 'fourth') {
        let { staff_name, staff_id } = this.$store.state.userInfo
        let isAdmin = this.adminList.some(item => item.admin_id === staff_id + '')
        if (!this.personInfo.register && !isAdmin) {
          this.isDisabledComment = true
        } else {
          this.isDisabledComment = false
        }
        const hostUrl = location.hostname.endsWith('.woa.com')
          ? process.env.VUE_APP_PORTAL_HOST_WOA
          : process.env.VUE_APP_PORTAL_HOST
        this.commentParams = {
          userName: staff_name,
          actId: this.mooc_course_id,
          appId: 'QLearningService',
          scrollTarget: '#project-detail-page',
          urlConfig: {
            getComments: `${hostUrl}/training/api/mooc/user/course-comment/get_comments`,
            addComment: `${hostUrl}/training/api/mooc/user/course-comment/add`,
            deleteComment: `${hostUrl}/training/api/mooc/user/course-comment/delete/`,
            like: `${hostUrl}/training/api/mooc/user/course-comment/praised`,
            sticky: `${hostUrl}/training/api/mooc/user/course-comment/sticky`,
            show: `${hostUrl}/training/api/mooc/user/course-comment/show`
          },
          isDisabled: this.isDisabledComment
        }
      }
    },
    detailData: {
      deep: true,
      handler(data) {
        const { cover_image } = data
        this.editImgUrl = cover_image
        // const { cover_image_storage_type, cover_image_id, cover_image } = data
        // const envName = env[process.env.NODE_ENV]
        // if (['zhihui', 'other', 'geekBang', 'imooc'].includes(cover_image_storage_type)) {
        //   this.editImgUrl = cover_image
        // } else {
        //   this.editImgUrl = `${envName.contentcenter}content-center/api/v1/content/imgage/${cover_image_id}/preview`
        // }
      }
    }
  },
  // course_status 0-待发布，1-已发布，3-已结束 ---项目状态
  // learn_status 1-未开始, 2-进行中，3-逾期， 4-已完成 ----学员状态
  created() {},
  async mounted() {
    // 初始化双语
    let lang = this.$route.query?.lang
    if (lang) {
      lang = ['en-US', 'en-us'].includes(lang) ? 'en-us' : 'zh-cn'
    } else {
      lang = localStorage.getItem('sdc-sys-def-lang') || 'zh-cn'
    }
    this.$store.commit('setMoocLang', lang || 'zh-cn')
    await this.purchaseSourceFromConfig()
    this.getLangJS()
    await this.getDetail()
    this.getCardData()

    // 添加滚动监听事件
    this.$nextTick(() => {
      let scrollEl = document.querySelector('#project-detail-page')
      scrollEl && scrollEl.addEventListener('scroll', this.windowScrollListener)
      if (!scrollEl) {
        setTimeout(() => {
          document.querySelector('#project-detail-page').addEventListener('scroll', this.windowScrollListener)
        }, 1000)
      }
    })
    // 自动报名
    this.debounceAutoSignUp()
  },
  destroyed () {
    window.removeEventListener('scroll', this.windowScrollListener)
  },
  methods: {
    // 学霸卡活动特殊课程
    async purchaseSourceFromConfig() {
      const res = await purchaseSourceFromConfig()
      this.sourceFromConfig = res || []
      this.getDetail()
      this.getCardData()
    },
    toSourceDetail() {
      window.open(this.outsourceLink.rule_page_link)
      // const { resource_from } = this.detailData
      // if (resource_from === 'geekBang') {
      //   window.open('https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=37119')
      // } else {
      //   window.open(this.sourceDetailLink)
      // }
    },
    dtTab(type, { text }) {
      let { mooc_course_id } = this.getRouterQuery()
      if (type === 'remark') {
        return JSON.stringify({
          page: this.cardInfo.course_title,
          page_type: '项目详情页',
          container: '',
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: 'text',
          terminal: 'PC'
        })
      } else if (type === 'eld') {
        return `element_${mooc_course_id}_${text}`
      }
      return `area_${mooc_course_id}_${text}`
    },
    dtBtn(type, { text }) {
      let { mooc_course_id } = this.getRouterQuery()
      if (type === 'remark') {
        return JSON.stringify({
          page: this.cardInfo.course_title,
          page_type: '项目详情页',
          container: '',
          click_type: 'button',
          content_type: '培养项目',
          content_id: mooc_course_id,
          container_id: '',
          content_name: text,
          act_type: '11',
          terminal: 'PC'
        })
      }
      if (type === 'area') {
        return `area_${mooc_course_id}_${text}`
      }
      return `element_${mooc_course_id}_${text}`
    },
    dtTips(type) {
      let tips = '点击报名加入项目'
      if (this.controlBtn && this.cardInfo.enabled_registration && this.is_target) {
        if (this.isGeekFreeLearningSection) {
          tips = '点击兑换并加入项目'
        } else {
          tips = '点击报名加入项目'
        }
      }
      if (type === 'eid') {
        return `element_${this.mooc_course_id}_${tips}`
      }
      if (type === 'area') {
        return `area_${this.mooc_course_id}_${tips}`
      }

      return JSON.stringify({
        page: this.cardInfo.course_title,
        page_type: '项目详情页',
        container: '项目评论',
        click_type: 'button',
        content_type: '',
        content_id: '',
        content_name: tips,
        terminal: 'PC'
      })
    },
    getRouterQuery() {
      let { mooc_course_id } = this.$route.query
      return {
        mooc_course_id: mooc_course_id || ''
      }
    },
    windowScrollListener() {
      const scrollTop = document.querySelector('#project-detail-page').scrollTop
      if (scrollTop > 280) {
        this.isScrollShow = true
      } else {
        this.isScrollShow = false
      }
    },
    // 评论回调
    setCommentCount(val) {
      this.personInfo.comment_count = val
      this.personInfo.comment = !!val
    },
    getMobileUrl() {
      const params = {
        scene: `${this.mooc_course_id}_${this.moocLang}`,
        page: `pages/mooc/projectDetails/index`,
        env_version: process.env.NODE_ENV === 'production' ? 'release' : 'trial'
      }
      getMobileQrcode(params).then((res) => {
        this.qrUrl = `data:image/png;base64,${res}`
      })
    },
    // 开始学习
    handleStart(val) {
      const { frist_task_id, latest_task_id } = this.trainProgressInfo
      const task_id = val === '1' ? frist_task_id : latest_task_id
      const { previewType } = this.$route.query
      this.$router.push({
        name: 'taskDetail',
        query: {
          mooc_course_id: this.mooc_course_id,
          task_id,
          previewType
        }
      })
    },
    // 证书列表弹窗
    handleChekcCertificate() {
      const { staff_id } = this.userInfo
      this.isShowCertificatePopup = true
      this.$nextTick(() => {
        this.$refs.certificateListPopup.onSearch(staff_id)
      })
    },
    // 公告详情
    toNotice(row) {
      this.noticeDialog = true
      this.noticeInfo = row
      if (row.viewed) return // 单条数据已读不再查看
      this.handleFlagRead('single', row, 'noTips')
    },
    // 标记为已读
    handleFlagRead(type, row, value) {
      const readList = this.noticeList.filter((v) => v.viewed)
      if (readList?.length === this.noticeList?.length) {
        return this.$message.warning(this.$langue('Mooc_ProjectDetail_Notice_NonRead', { defaultText: '当前暂无未读公告' }))
      }
      let nitice_id = []
      if (type === 'single') {
        nitice_id = [row.notice_id]
      } else {
        nitice_id = this.noticeList.map((v) => v.notice_id)
      }
      noticeNoPreview(nitice_id).then((res) => {
        this.getDetailNotice()
        if (value !== 'noTips') {
          this.$message.success(this.$langue('Mooc_ProjectDetail_Notice_AllReaded', { defaultText: '全部公告标记为已读' }))
        }
      })
    },
    async getDetailNotice() {
      const { previewType } = this.$route.query
      const val = previewType === 'preview' ? 1 : 0
      const progressAPI = await getTrainProgress({ mooc_course_id: this.mooc_course_id, preview: val })
      const noticeListAPI = await getDetailNoticeList({ act_id: this.mooc_course_id })
      Promise.all([noticeListAPI, progressAPI]).then((res) => {
        this.noticeList = res[0] // 公告列表
        this.trainProgressInfo = res[1] // 培训进度
        this.isEmpty = !this.noticeList?.length
        if (this.noticeList?.length) {
          const noViewed = this.noticeList[0].viewed
          const noticeTime = new Date(this.noticeList[0].prepare_publish_time).getTime() // 最新公告发布时间
          const latestTime = new Date(this.trainProgressInfo.latest_study_time).getTime() // 最近学习时间
          if (this.personInfo.register && latestTime < noticeTime && !noViewed) { // 已注册且最近学习时间早于公告发布时间且没有预览
            this.noticeDialog = true
            this.noticeInfo = this.noticeList[0]
            this.handleFlagRead('single', this.noticeList[0], 'noTips')
          }
        }
      })
    },
    // 获取用户评分
    getScore() {
      getUerScore({ mooc_course_id: this.mooc_course_id }).then((res) => {
        this.scoreStatus = res || 0
      })
    },
    // 报名
    handleEnroll(receiveData) {
      this.$messageBox.confirm(
        (receiveData && receiveData.msg) || this.$langue('Mooc_ProjectDetail_BasicInfo_SureRegist', { defaultText: '报名后将加入项目进行学习，请留意项目的学习起止时间，是否确定报名？' }),
        (receiveData && receiveData.title) || this.$langue('Mooc_ProjectDetail_BasicInfo_RegistProj', { defaultText: '报名加入项目' }),
        {
          // confirmButtonText: this.$langue('Mooc_Common_Sure_join', { defaultText: '确定报名' }),
          // cancelButtonText: this.$langue('Mooc_Common_Join_Cancel', { defaultText: '我再想想' })
          cancelButtonText: (receiveData && receiveData.cancelButtonText) || this.$langue('Mooc_Common_Alert_Cancel', { defaultText: '取消' }),
          confirmButtonText: (receiveData && receiveData.confirmButtonText) || this.$langue('Mooc_Common_Sure', { defaultText: '确定' })
        }).then(() => {
        const params = {
          mooc_course_id: this.mooc_course_id,
          join_type: '3'
        }
        moocEnroll(params).then(async(res) => {
          this.$message.success(receiveData?.toast || this.$langue('Mooc_ProjectDetail_BasicInfo_RegistSucessed', { defaultText: '报名成功' }))
          await this.getPersonStatus()
          if (receiveData && receiveData.needJumpTo) { // 任务列表报名完成需要跳转
            setTimeout(() => {
              receiveData.callback && receiveData.callback(receiveData.data)
            }, 1000)
          }
        }).catch(res => {
          if (res?.title) {
            const message = res.code && res.code !== 200 ? (res.message || res.data) : '网络异常，请稍后重试！'
            this.$message.error(message)
          }
        })
      })
    },
    // 点赞
    handlePraise() {
      const commonPraise = this.personInfo.praise ? deleteMoocPraise : moocPraise
      commonPraise({ mooc_course_id: this.mooc_course_id }).then((res) => {
        const { credit } = res
        let msg = ''
        if (!this.personInfo.praise) {
          msg = credit ? this.$langue('Mooc_Common_Alert_PraiseSucessed', { defaultText: '点赞成功' }) + ',' + this.$langue('Mooc_Common_Alert_CommonPoint', { point: +1, defaultText: '通用积分+1' }) : this.$langue('Mooc_Common_Alert_PraiseSucessed', { defaultText: '点赞成功' })
          this.personInfo.praise = true
          this.personInfo.praise_count++
        } else {
          this.personInfo.praise = false
          msg = credit ? this.$langue('Mooc_Common_Alert_CancelPraiseSucessed', { defaultText: '取消点赞成功' }) + ',' + this.$langue('Mooc_Common_Alert_CommonPoint', { point: -1, defaultText: '通用积分-1' }) : this.$langue('Mooc_Common_Alert_CancelPraiseSucessed', { defaultText: '取消点赞成功' })
          this.personInfo.praise_count--
        }
        this.$message.success(msg)
      })
    },
    // 收藏
    handleFav() {
      const commonFav = this.personInfo.favorite ? deleteMoocfav : moocfav
      commonFav({ mooc_course_id: this.mooc_course_id }).then((res) => {
        const { credit } = res
        let msg = ''
        if (!this.personInfo.favorite) {
          msg = credit ? this.$langue('Mooc_Common_Alert_CollectSucessed', { defaultText: '收藏成功' }) + ',' + this.$langue('Mooc_Common_Alert_CommonPoint', { point: +1, defaultText: '通用积分+1' }) : this.$langue('Mooc_Common_Alert_CollectSucessed', { defaultText: '收藏成功' })
          this.personInfo.favorite = true
          this.personInfo.fav_count++
        } else {
          msg = credit ? this.$langue('Mooc_Common_Alert_CancelCollectSucessed', { defaultText: '取消收藏成功' }) + ',' + this.$langue('Mooc_Common_Alert_CommonPoint', { point: -1, defaultText: '通用积分-1' }) : this.$langue('Mooc_Common_Alert_CancelCollectSucessed', { defaultText: '取消收藏成功' })
          this.personInfo.favorite = false
          this.personInfo.fav_count--
        }
        this.$message.success(msg)
      })
    },
    async getCardData() {
      const { previewType } = this.$route.query
      const val = previewType === 'preview' ? 1 : 0
      const res = await cardDetail({ mooc_course_id: this.mooc_course_id, preview: val }, true)
      this.cardInfo = res
      if (this.sourceFromConfig.includes(this.detailData.resource_from)) {
        // this.getGeekBangDetail()
        this.getOutsourceLinkConfig()
      }
      this.getPersonStatus()
      // 详情页曝光上报
      pageExposure({
        page_type: '培养项目详情页',
        content_type: '培养项目',
        act_type: '11',
        content_name: res.course_title,
        content_id: this.mooc_course_id
      })
      // 管理员
      this.adminList = (this.cardInfo.admin_list || []).map((e) => {
        const name = e.admin_name.split('(')[0]
        return {
          ...e,
          staff_name: e.admin_name,
          url: `//learn.woa.com/rhrc/photo/150/${name}.png`
        }
      })
    },
    async getPersonStatus() {
      const res = await personDetail({ mooc_course_id: this.mooc_course_id })
      this.personInfo = res
      this.personInfo.score = res.score?.toFixed(1) || 0
      if (this.personInfo.register) { // 加入项目后默认到列表页
        this.activeName = 'second'
      }
      // 错误页提示
      let { have_auth_to_view, have_released, course_status } = this.cardInfo
      let { start_time, end_time, course_period_type, course_end_time, register, delayed } = this.personInfo
      // 如果已经逾期，更新了最后学习时间那么就先用更新的 course_end_time 学员的时间，end_time项目时间
      let commEndTime = delayed ? course_end_time : end_time
      let tips = ''
      const isPassTime = course_period_type === 1 && commEndTime && (new Date().getTime() > new Date(commEndTime).getTime())
      if (course_status === 1 && isPassTime) { // 已逾期
        // 逾期没有报名给错误提示
        if (!register) {
          tips = this.$langue('Mooc_ProjectDetail_BasicInfo_ProjectEnded', { defaultText: '培养项目已结束，无法访问。' })
          this.pageError = {
            inaccessible: true,
            noPermission: false,
            tips,
            isConcatAdmin: this.adminList.length ? 1 : 0, // 0-隐藏管理员
            concatAdmins: this.adminList,
            register,
            isShowBack: false
          }
          return
        }
        // 已报名展示相关页面
        this.getScore()
        this.getDetailNotice()
        this.getDetail()
        return
      }
      const isStartTime = course_period_type === 1 && start_time && (new Date(start_time).getTime()) > new Date().getTime()
      if (course_status === 1 && isStartTime) { // 未开始
        tips = this.$langue('Mooc_Common_Authority_NotStudyByTime', { defaultText: '项目未开始，无法进行任务学习' })
        this.pageError = {
          inaccessible: true,
          noPermission: false,
          tips,
          isConcatAdmin: 0, // 0-隐藏管理员
          concatAdmins: [],
          register,
          isShowBack: true,
          timeText: start_time
            ? `${this.$langue('Mooc_Common_Authority_StartTime', {
              defaultText: '开始时间'
            })}：${start_time}`
            : ''
        }
        return
      }
      if (!have_released || !have_auth_to_view) { // 没有权限
        if (course_status === 2) {
          tips = this.$langue('Mooc_ProjectDetail_BasicInfo_ProjectEnded', { defaultText: '培养项目已结束，无法访问。' })
        } else if (course_status === 3) {
          tips = this.$langue('Mooc_Common_Alert_CourseDestoryTips', { defaultText: '培养项目已下架，无法访问。' })
        } else {
          tips = !have_released ? this.$langue('Mooc_ProjectDetail_BasicInfo_ProjectUnPublished', { defaultText: '培养项目暂未发布，无法访问。' }) : !have_auth_to_view ? this.$langue('Mooc_ProjectDetail_BasicInfo_ProjectNoArthority', { defaultText: '您暂时没有访问该项目的权限！' }) : ''
        }
        this.pageError = {
          inaccessible: !have_released || course_status === 2,
          noPermission: !have_auth_to_view,
          tips,
          isConcatAdmin: this.adminList.length ? 1 : 0, // 0-隐藏管理员
          concatAdmins: this.adminList,
          register
        }
        return
      }
      this.getScore()
      this.getDetailNotice()
      this.getDetail()
    },
    getDetail() {
      projectDetailAPI(this.mooc_course_id).then((res) => {
        this.detailData = res
        if (this.sourceFromConfig.includes(res.resource_from)) {
          this.getGeekBangDetail()
          // this.getOutsourceLinkConfig()
        }
      }).catch((err) => {
        if (err.code === 401) {
          sessionStorage.setItem('401Msg', err.message)
          this.$router.replace({
            name: '401'
          })
        }
      })
    },
    // 去项目管理
    toProjectManage() {
      const testUrl = `//test-portal-learn.woa.com/training/mooc/manage/basic-setting?mooc_course_id=${this.mooc_course_id}`
      const prdUrl = `//portal.learn.woa.com/training/mooc/manage/basic-setting?mooc_course_id=${this.mooc_course_id}`
      const url = process.env.NODE_ENV === 'production' ? prdUrl : testUrl
      window.open(url)
    },
    // 回到顶部
    handleBackTop() {
      document.querySelector('#project-detail-page').scrollTop = 0
      this.isScrollShow = false
    },
    // 切换双语
    handleChangelang() {
      const lang = this.moocLang === 'en-us' ? 'zh-cn' : 'en-us'
      this.$store.commit('setMoocLang', lang)
      this.getLangJS()
    },
    // 判断标题是否超出两行
    titleOver(e) {
      const target = e.target
      this.course_title_overflow = !target.scrollWidth > 580
    },
    doCopy() {
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      input.value = `【${this.cardInfo.course_title}】${this.urlMobile}`
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      this.$message.success(this.$langue('Mooc_Common_Alert_CopySucessed', { defaultText: '复制成功' }))
      // 复制后移除输入框
      input.remove()
    },
    // 特殊课程链接
    async getOutsourceLinkConfig() {
      let resource = this.cardInfo.consume_appid ? this.cardInfo.consume_appid : this.detailData.resource_from
      const res = await getOutsourceLinkConfig({ resourceConfig: resource })
      // activity_page_link 活动落地页 rule_page_link 活动规则 ；special_page_link 外部好课专区
      this.outsourceLink = res
    },
    // 极客课程信息
    getGeekBangDetail() {
      getGeekCourseDetail(this.mooc_course_id, { params: { acct_type_code: this.detailData.resource_from } }).then(res => {
        this.geekCouresInfo = res
        // if (this.isGeekFreeLearningSection) {
        this.getGeekCoursePurchaseInfo()
        // }
      })
    },
    // 极客课程购买信息
    getGeekCoursePurchaseInfo() {
      getCoursePurchaseInfo(this.mooc_course_id).then(res => {
        if (res.course_acquisition_type === 2) {
          res.course_val = res.course_val ? res.course_val : 1
        }
        this.geekPurchaseInfo = res
      })
    },
    handlerOpenActive() {
      // if (!this.isQuantity) {
      //   return
      // }
      this.giveDialog = true
    },
    // 直接兑换极客课程
    purchaseCourses() {
      const { user_account_num, course_stock_total, course_val } = this.geekPurchaseInfo
      // 课程名额 >= 1 && 用户兑换券 >= 课程价值（兑换券）=== 兑换课程
      if (course_stock_total > 0 && user_account_num >= course_val) {
        this.redeemPopupStatus = 0
      } else if (course_stock_total === 0) {
        // 兑换名额不足
        this.redeemPopupStatus = 1
      } else if (course_val > user_account_num) {
        // 兑换券不足
        this.redeemPopupStatus = 2
      }
      if (this.redeemPopupStatus > -1) {
        this.redeemCoursesShow = true
      }
    },
    // 极客 试学提示
    handleStudyTips(status) {
      this.redeemPopupStatus = status
      this.redeemCoursesShow = true
    },
    handleStudyEnroll() {
      if (this.isGeekFreeLearningSection) {
        this.purchaseCourses()
      } else {
        this.handleEnroll()
      }
    },
    // 极客报名初始化数据
    handleRegistered() {
      this.getCardData()
      this.getDetailNotice()
      this.getDetail()
    },
    // 自动报名防抖
    debounceAutoSignUp: debounce(function () {
      const { register_confirm, resource_from, register } = this.personInfo
      console.log('自动报名---', (register_confirm === 0 && !resource_from && !register && this.controlBtn && !this.isGeek))
      console.log('自动报名---', register_confirm, !resource_from, !register, this.controlBtn, !this.isGeek)
      if (register_confirm === 0 && !resource_from && !register && this.controlBtn && !this.isGeek) {
        this.autoSignUp()
      }
    }, 1000),
    // 自动报名
    autoSignUp() {
      const params = {
        mooc_course_id: this.mooc_course_id,
        join_type: '2' // 自动报名
      }
      moocEnroll(params).then((res) => {
        this.$message.success(this.$langue('Mooc_ProjectDetail_BasicInfo_RegistSucessed_Auto', { defaultText: '已自动报名加入培养项目，请留意项目的学习时间' }))
        this.getPersonStatus()
      }).catch(res => {
        if (res?.title) {
          const message = res.code && res.code !== 200 ? (res.message || res.data) : '网络异常，请稍后重试！'
          this.$message.error(message)
        }
      })
    }
  }
}
</script>
<style lang="less">
.mobile-popove-box {
  width: 300px;
  padding: 24px 24px;
  .title {
    color: #000000e6;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 28px;
    text-align: left;
  }
  .popove-body {
    text-align: center;
    .qrcode-tips {
      margin-top: 20px;
      margin-bottom: 20px;
    }
    .el-input-group__append{
      color: #000000e6;
    }
  }
  img {
    width: 200px;
    height: 200px;
  }
}

.geek-tips {
  margin-top: 4px;
  .tips {
    color: #ed7b2f;
    font-size: 12px;
    line-height: 20px;
  }
  .btn {
    margin-top: 8px;
    font-size: 14px;
    line-height: 22px;
    display: flex;
    align-items: center;
    .help-icon {
      background: url('~@/assets/mooc-img/help_circle.png') no-repeat center/cover;
      width: 16px;
      height: 16px;
      display: inline-block;
      margin-right: 4px;
    }
    .source-detail {
      color: #0052d9;
      text-decoration-line: underline;
      cursor: pointer;
    }
  }
}
</style>
<style lang="less" scoped>
#project-detail-page {
  height: 100%;
  overflow: auto;
}
.project-detail-container {
  height: 100%;
  padding: 16px 20px;
  .custom-container {
    width: 1200px;
    margin: 0 auto;
    margin-bottom: 16px;
  }
  .card {
    display: flex;
    width: 1200px;
    margin: 0 auto;
    background-color: #fff;
    padding: 16px 20px;
    &-img {
      margin-right: 26px;
      position: relative;
      .common-update-status {
        position: absolute;
        top: 0;
        z-index: 3;
      }
      .update-status {
        background: url('~@/assets/mooc-img/updateing.png') no-repeat center/cover;
        width: 58px;
        height: 20px;
      }
      .en-update-status {
        background: url('~@/assets/mooc-img/updateingEn.png') no-repeat center/cover;
        width: 71px;
        height: 20px;
      }
      .geek-time {
        // width: 60px;
        height: 24px;
        position: absolute;
        left: 0;
        top: 0;
        z-index: 1;
      }
      .course-tag {
        position: absolute;
        right: 8px;
        top: 8px;
        z-index: 2;

        .official-tag,
        .excellent-tag {
          display: inline-block;
          padding: 0 11px;
          font-size: 12px;
          height: 18px;
          line-height: 18px;
          border-radius: 10px;
          color: #fff;
        }

        .official-tag {
          background-color: #1374ff;
        }

        .excellent-tag {
          margin-left: 15px;
          background: #b20000;
        }
      }
      .el-image,.error-cover img {
        width: 221px;
        height: 148px;
        border-radius: 4px;
      }
    }
    &-content {
      flex: 1;
      .label {
        color: #00000099;
        font-size: 14px;
        height: 22px;
        line-height: 22px;
        display: flex;
        align-items: center;
      }
      .top-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;
        .left {
          display: flex;
          align-items: center;
          flex-shrink: 0;
        }
        .title {
          color: #000000e6;
          font-size: 16px;
          font-weight: 600;
          max-width: 450px;
        }
        .right-opera-mobile {
          display: flex;
        }
        .mobile-box, .set-box{
          display: flex;
          align-items: center;
          color: #00000099;
        }
        .mobile-tips {
          flex-shrink: 0;
        }
        .set-box {
          margin-left: 24px;
          cursor: pointer;
        }
        .mobile-icon {
          background: url('~@/assets/mooc-img/mobile.png') no-repeat center/cover;
          width: 16px;
          height: 16px;
          display: inline-block;
          margin-right: 8px;
        }
        .set-icon {
          background: url('~@/assets/mooc-img/project-set.png') no-repeat center/cover;
          width: 16px;
          height: 16px;
          display: inline-block;
          margin-right: 8px;
        }
      }
      .external-courses {
        padding: 3px 8px;
        margin-left: 20px;
        border-radius: 16px;
        background: #E8F8F2;
        .a {
          display: flex;
          align-items: center;
          color: #00a870;
        }
        .icon {
          background: url('~@/assets/mooc-img/lesson-green.png') no-repeat center/cover;
          width: 16px;
          height: 16px;
          display: inline-block;
          margin-right: 4px;
        }

        .arrow {
          background: url('~@/assets/mooc-img/chevron-right-green.png') no-repeat center/cover;
          width: 16px;
          height: 16px;
          display: inline-block;
        }
      }
      .mid-title {
        margin-bottom: 12px;
        height: 28px;
        line-height: 28px;
        display: flex;
        align-items: center;
        .integral {
          color: #00000066;
          font-size: 14px;
          line-height: 22px;
          margin-right: 12px;
          text-decoration: line-through;
        }
        .free {
          color: #0052d9;
          font-size: 14px;
          line-height: 22px;
          margin-right: 36px;
        }

        .source-detail-content {
          display: flex;
          align-items: center;
          padding: 2px 8px;
          color: #0052d9;
          border-radius: 4px;
          // border: 1px solid #0052D9;
          cursor: pointer;
        }

        .source-detail {
          color: #0052d9;
          font-size: 14px;
          line-height: 22px;
          text-decoration: underline;
        }
        .time-icon {
          background: url('~@/assets/mooc-img/time.png') no-repeat center/cover;
          width: 13px;
          height: 13px;
          display: inline-block;
          margin-right: 5.5px;
        }
        .help-icon {
          background: url('~@/assets/mooc-img/help_circle.png') no-repeat center/cover;
          width: 16px;
          height: 16px;
          display: inline-block;
          margin-right: 4px;
        }
      }
      .mgR-36 {
        margin-right: 28px;
      }
      .bottom-title {
        display: flex;
        .label {
          margin-right: 36px;
        }
        .icon {
          width: 20px;
          height: 20px;
          display: inline-block;
          margin-right: 8px;
        }
        .task-icon {
          background: url('~@/assets/mooc-img/task.png') no-repeat center/cover;
        }
        .person-icon {
          background: url('~@/assets/mooc-img/person.png') no-repeat center/cover;

        }
        .like-icon {
          background: url('~@/assets/mooc-img/like.png') no-repeat center/cover;

        }
        .grade-btn {
          height: 22px;
          margin-left: 16px;
          display: inline-block;
          color: #0052D9;
          cursor: pointer;
        }
        .grade-btn:hover {
          color: #5d83e6
        }
      }
      .bottom-icon-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 14px;
        position: relative;
        .left-icon {
          display: flex;
          .label {
            margin-right: 37px;
          }
          .person-like-label {
            color: #0052d9ff;
            cursor: pointer;
            .like-icon {
              background: url('~@/assets/mooc-img/comment/mooc-like-active.png') no-repeat center/cover;
            }
          }
          .person-no-like-label {
            cursor: pointer;
            .no-like-icon {
              background: url('~@/assets/mooc-img/comment/mooc-like.png') no-repeat center/cover;
            }
          }
          .person-no-fav-label {
            cursor: pointer;
            .fav-no-icon {
              background: url('~@/assets/mooc-img/mooc-fav.png') no-repeat center/cover;
            }
          }
          .person-fav-label {
            color: #0052d9ff;
            cursor: pointer;
            .fav-icon {
              background: url('~@/assets/mooc-img/mooc-fav-num.png') no-repeat center/cover;
            }

          }
          .person-chat-label {
            color: #0052d9ff;
            cursor: pointer;
            .chat-icon {
              background: url('~@/assets/mooc-img/icon_chat-num.png') no-repeat center/cover;
            }
          }
          .person-no-chat-label {
            cursor: pointer;
            .chat-no-icon {
              background: url('~@/assets/mooc-img/icon_chat.png') no-repeat center/cover;
            }
          }
          .icon {
            width: 14px;
            height: 14px;
            display: inline-block;
            margin-right: 3px;
          }
        }
        .right-opera {
          display: flex;
          text-align: right;
          position: absolute;
          top: -20px;
          right: 0px;
          .certificate-label {
            color: #ed7b2fff;
            display: inline-block;
            margin-right: 32px;
            display: flex;
            align-items: center;
          }
          .certify-icon {
            background: url('~@/assets/mooc-img/certify.png') no-repeat center/cover;
            width: 24px;
            height: 24px;
            display: inline-block;
            margin-right: 4px;
          }
          .exchange-btn {
            position: absolute;
            width: 150px;
            top: 40px;
            right: 0px;
            text-align: right;
            color: #0052d9;
            font-size: 14px;
            line-height: 22px;
            text-decoration-line: underline;
            cursor: pointer;
          }
          .invite-img {
           position: relative;
          margin-left: 24px;
          }
          .invite-img::before {
            content: '';
            position: absolute;
            width: 16px;
            height: 16px;
            left: -24px;
            top: 0px;
            background: url('~@/assets/mooc-img/vuesax-bulk.png') no-repeat center/cover;
          }
        }
      }
    }
  }
  .no-register-tab-content {
    width: 886px !important;
  }
  .tab-content {
    background-color: #fff;
    margin: 16px auto 0;
    width: 1200px;
    :deep(.el-tabs) {
      color: #00000099;
      .el-tabs__header {
        padding: 16px 24px 0px;
        margin:unset;
      }
      .el-tabs__active-bar {
        margin-top: 16px;
      }
      .el-tabs__nav {
        height: 40px;
        border-bottom: 1px solid #eeeeeeff;
        width: 100%;
        .el-tabs__item {
          height: 22px;
          line-height: 22px;
        }
      }
    }
  }
  .comment-box{
    padding: 24px;
    background-color: #fff;
  }

  .filter-data-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 36px;
  }
  .bottom-content {
    width: 1200px;
    margin: 0 auto;
    display: flex;
    font-size: 12px;
    min-height: calc(100% - 170px);
  }
  .left-progress {
    width: 298px;
    // padding: 14px 16px;

    margin-right: 14px;
    margin-top: 16px;
    border-radius: 4px;
    box-shadow: 0 0 8px 0 #eeeeee99;
    .top-progress {
      background-color: #fff;
    }
    .top-box {
      display: flex ;
      align-items: center;
      padding: 4px 4px 4px 16px;

      .lf-title {
        font-size: 14px;
        font-weight: bold;
        color: #000000e6;
        height: 22px;
        line-height: 22px;
      }
      .train-icon {
        background: url('~@/assets/mooc-img/train.png') no-repeat center/cover;
        width: 32px;
        height: 32px;
        display: inline-block;
        margin-right: 8px;
      }
      .train-status {
        width: 52px;
        height: 52px;
        display: inline-block;
        margin: 0 0 0 auto;
      }

      .icon-jxz {
        background: url("~@/assets/mooc-img/status-jxz.png") no-repeat center / cover;
      }
      .en-icon-jxz { // 英文
        background: url("~@/assets/mooc-img/en-status-jxz.png") no-repeat center / cover;
      }
      .icon-wks {
        background: url("~@/assets/mooc-img/status-wks.png") no-repeat center / cover;
      }
      .en-icon-wkx { // 英文
        background: url("~@/assets/mooc-img/en-status-wks.png") no-repeat center / cover;
      }
      .icon-ywc {
        background: url("~@/assets/mooc-img/status-ywc.png") no-repeat center / cover;
      }
      .en-icon-ywc { // 英文
        background: url("~@/assets/mooc-img/en-status-ywc.png") no-repeat center / cover;
      }
      .icon-yyq {
        background: url("~@/assets/mooc-img/status-yyq.png") no-repeat center / cover;
      }
      .en-icon-yyq { // 英文
        background: url("~@/assets/mooc-img/en-status-yyq.png") no-repeat center / cover;
      }
    }
    .progress-box {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-left: 14px;
      margin-right: 16px;
      border-top: 1px solid #eeeeeeff;
      padding: 12px 6px 8px 0px;
      :deep(.el-progress ){
        .el-progress__text {
          font-size: 12px !important;
          margin-left: 16px;
          color: inherit;
        }
        .el-progress-bar__outer {
          width: 200px;
        }
      }
      .tips-i {
        // margin-left: 8px;
        display: inline-block;
        i {
          font-size: 16px;
        }
      }
    }
    .lastly-study-box {
      padding: 0 16px 16px 16px;
      .el-button {
        width: 266px;
        margin-top: 20px;
      }
      .no-start-project {
        margin-bottom: 20px;
        .no-start-title {
          color: #00000099;
        }
      }
      .going-project {
        margin-bottom: 20px;
        .last-study{
          word-break: break-all;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
        }
        .lastly-tag {
          padding: 0 4px;
          height: 20px;
          border-radius: 2px;
          opacity: 1;
          background: #ebeffcff;
          color: #0052d9ff;
          display: inline-block;
          line-height: 20px;
          text-align: center;
          margin-right: 12px;
        }
        .lastly-title {
          color: #00000099;
          line-height: 20px;
        }
      }
      .finish-project {
        margin-bottom: 50px;
        .finsh-tag {
          color: #00a870ff;
          font-size: 14px;
          font-weight: 600;
        }
        .finsh-time {
          color: #00000099;
          font-size: 12px;
          font-weight: 400;
          margin-top: 8px;
          white-space: break-spaces;
        }
      }
      .end-project {
        display: flex;
        line-height: 20px;
        margin-bottom: 28px;
        i {
          color: #FF7548
        }
        .end-project-title {
          color: #00000099;
          margin-left: 8px;
          height: 68px;
          line-height: 20px;
        }
      }
      .progress-show-data {
        display: flex;
        width: 266px;
        padding: 12px 10px;
        justify-content: space-between;
        align-items: flex-start;
        color: #00000099;
        border-radius: 3px;
        background: #fafafaff;
        // margin-top: 20px;
        .item-data {
          text-align:center;
          cursor: not-allowed;
          pointer-events: none;
          flex: 1;
          p:first-of-type {
            margin-bottom: 8px;
            color: #000000e6;
            font-size: 16px;
            font-weight: bold;
          }
        }
        .line{
          height: 24px;
          width: 1px;
          background: #eee;
        }
        .active-certificate {
          cursor: pointer;
          pointer-events: unset
        }
      }
    }
    .bottom-notice {
      margin-top: 12px;
      background-color: #fff;
      padding: 14px 0px 28px;
      .notice-title-box {
        display: flex;
        justify-content: space-between;
        margin: 0px 16px 0px;
        padding-bottom: 14px;
        border-bottom: 1px solid #eeeeeeff;
        margin-bottom: 12px;
        .item-icon-box {
          display: flex;
          align-items: center;
          flex-shrink: 0;
        }
      }
      .notice-icon {
        background: url('~@/assets/mooc-img/notice-icon.png') no-repeat center/cover;
        width: 32px;
        height: 32px;
        display: inline-block;
        margin-right: 6px;
      }
      .flag-icon {
        background: url('~@/assets/mooc-img/clear.png') no-repeat center/cover;
        width: 16px;
        height: 16px;
        display: inline-block;
        margin-right: 4px;
        cursor: pointer;
      }
      .flag-title {
        color: #00000099;
        font-size: 12px;
        height: 20px;
        line-height: 20px;
        cursor: pointer;
      }
      .notice-title {
        color: #000000e6;
        font-size: 14px;
        font-weight: 600;
      }
      .notice-card + .notice-card {
        margin-top: 12px;
      }
      .notice-content-box {
        padding: 0px 16px 0px;
        height: 274px;
        overflow: auto;
        .notice-content {
          font-size: 14px;
          line-height: 22px;
          // font-weight: bold;
          color: #000000;
          cursor: pointer;
          word-break: break-all;
          .tips-num {
            margin-right: 5px;
          }
          .no-view-index {
            color: #0052D9;
          }
          .notice-value {
            color: #000000;
            position: relative;
          }
          .no-view::after {
            content: '.';
            position: absolute;
            top: -18px;
            left: -24px;
            font-size: 30px;
            color: red;
          }
        }
        .publish-time {
          display: flex;
          align-items: center;
          color: #00000066;
          margin-top: 5px;
          .publish-icon {
            background: url('~@/assets/mooc-img/time.png') no-repeat center/cover;
            width: 16px;
            height: 16px;
            display: inline-block;
            margin-right: 8px;
          }
        }
      }
      .bottom-text {
        padding-top: 4px;
        color: #999;
        text-align: center;
        color: #000000;
        font-size: 16px;
        .empty-img {
          margin-bottom: 20px;
          width: 160px;
        }
      }
    }
  }
}
.scroll-show-container {
  position: relative;
  .tab-content {
    .fix-bg, .el-tabs {
      width: 1200px;
    }
    :deep(.task-list-content){

      .filter-data-box  {
        width: 1160px;
      }
    }
  }
  .no-register-tab-content {
    .fix-bg, .el-tabs {
      width: 886px;
    }
    :deep(.task-list-content){

      .filter-data-box  {
        width: 846px;
      }
    }
  }
  .left-progress {
    position: sticky;
    top: 2px;
    height: 800px;
  }
  .el-tabs {
    position: fixed;
    top: 80px;
    z-index: 99;
    background: #fff;
  }
  .fix-bg {
    background: #f0f4fa;
    position: fixed;
    height: 50px;
    top: 30px;
    width: 886px;
    z-index: 99;
  }
  :deep(.task-list-content){
    .filter-data-box {
      position: fixed;
      top: 136px;
      z-index: 99;
      background: #fff;
    }
  }
  :deep(.el-tree) {
    position: relative;
    margin-top: 130px;
  }
}
.back-top-icon {
  width: 64px;
  height: 64px;
  position: fixed;
  z-index: 10;
  right: 50%;
  bottom: 130px;
  transform: translateX(690px);
  border: 0.5px solid #dcdcdc;
  border-radius: 6px;
  background: #ffffff;
  box-shadow: 0 6px 30px 5px #0000000d, 0 16px 24px 2px #0000000a, 0 8px 10px -5px #00000014;
  text-align: center;
  padding-top: 10px;
  cursor: pointer;
  .top-tag {
    color: #00000099;
    font-size: 12px;
    line-height: 20px;
  }
  img {
    width: 24px;
    height: 24px;
  }
}
.scroll-enter-active, .scroll-leave-active {
  transition: all .2s;
}
.auto-tips {
  margin-bottom: 12px;
  font-size: 14px;
  width: 1158px !important;
  :deep(.custom-tips-title) {
    .text-blue {
      margin-left: 28px;
    }
  }
}
.change-lang-main {
  position: fixed;
  top: 80px;
  right: 50%;
  transform: translateX(690px);
  width: 64px;
  height: 64px;
  border-radius: 6px;
  background-color: #fff;
  text-align: center;
  border: 0.5px solid  #DCDCDC;
  box-shadow: 0 8px 10px -5px #00000014, 0 16px 24px 2px #0000000a, 0 6px 30px 5px #0000000d;
  cursor: pointer;
  z-index: 99;
  img {
    width: 24px;
    height: 24px;
    margin-top: 12px;
  }
  .change-lang-title {
    color: #00000099;
    line-height: 20px;
    font-size: 12px;
    margin-top: 2px;
  }
}
.important {
  color: #ED7B2F !important;
  font-weight: normal;
}
@media screen and (max-width: 1660px) {
  .change-lang-main,.back-top-icon {
    transform: translateX(590px);
  }
}
</style>
