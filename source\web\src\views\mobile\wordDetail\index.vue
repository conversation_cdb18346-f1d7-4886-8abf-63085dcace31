<template>
  <div :class="['word-detail', { 'scroll-disabled': showExitScreen }]">
    <div class="word-header">
      <span class="word-title">
        <span class="tag">文档</span>
        <span class="title overflow-l2">{{ wordInfo.title }}</span>
      </span>
      <div class="creator-info">
        <span class="creator">{{ wordInfo.creator_name }}</span>
        <span class="create-time">{{
          wordInfo.created_at &&
          wordInfo.created_at.substring(0, wordInfo.created_at.length - 3)
        }}</span>
        <span class="cellect" @click="handleCellect">
          <i :class="['icon', { 'cellect-active': wordInfo.fav_status }]"></i>
          <span :class="{ 'active-color': wordInfo.fav_status }"
            >({{ wordInfo.collect > 0 ? wordInfo.collect : 0 }})</span
          >
        </span>
        <span class="praise" @click="handlePraise">
          <i :class="['icon', { 'praise-active': wordInfo.good_status }]"></i>
          <span :class="{ 'active-color': wordInfo.good_status }"
            >({{ wordInfo.file_good > 0 ? wordInfo.file_good : 0 }})</span
          >
        </span>
      </div>
      <!-- 标签展示 -->
      <sdc-label-show-mob class="mgt-5" ref="labelShow" :labelNodeEnv="labelNodeEnv" :actType="10" :courseId="word_id" :isH5="isH5" :isMock="isMock" @toSearchPage="toSearchPage"></sdc-label-show-mob>
    </div>
    <div class="word-content">
      <div class="word-btn">
        <span class="word-type-style">{{ wordInfo.word_type }}</span>
        <span class="word-desc">文件类型：{{ wordInfo.suffix }}文件</span>
        <span class="download"></span>
        <span class="full-screen" @click="handleFullscreen(1)"></span>
      </div>
      <!-- 嵌入的h5页面 -->
      <div
        :class="['word-ifame', { 'full-screen-style': showExitScreen }]"
        @scroll.prevent
        @touchmove.prevent
        @mousewheel.prevent
      >
        <span
          v-show="showExitScreen"
          class="exit-full-screen"
          @click="handleFullscreen(0)"
        >
          <span class="exit-icon"></span>
        </span>
        <iframe
          v-if="src"
          id="wordIframe"
          :src="src"
          frameborder="0"
          allowfullscreen
          webkitallowfullscreen
          mozallowfullscreen
        ></iframe>
        <span v-else-if="src === '-1'">文档正在转码中，请耐心等待...</span>
      </div>
    </div>
    <van-sticky>
      <van-tabs
        v-model="active"
        color="#0052D9"
        title-active-color="#0052D9"
        @click="changeTab"
      >
        <van-tab
          v-for="(item, index) in tabList"
          :title="
            item.key === 'comment'
              ? item.title + '·' + (wordInfo.comment_count || 0)
              : item.title
          "
          :key="index"
        ></van-tab>
      </van-tabs>
    </van-sticky>
    <div class="main-content">
      <!-- 简介 -->
      <desContent v-show="active === 0" :wordInfo.sync="wordInfo"></desContent>
      <!-- 笔记 -->
      <notes v-if="active === 1" module_id="16" item_id_key="word_id"></notes>
      <sdc-comment-mob
        v-if="active === 2 && commentParams"
        :params="commentParams"
      ></sdc-comment-mob>
      <!-- 延伸学习 -->
      <div v-if="active === 3">
        <div v-if="loading" class="loading-text">加载中...</div>
        <div v-else-if="extandList.length === 0" class="learn-empty">
          <img src="@/assets/img/mobile/empty-note.png" alt="" />
          <div class="empty-text">暂无相关课程～</div>
        </div>
        <div v-else class="more-learn">
          <CategoryCard
            class="content-inner"
            v-for="(item, index) in extandList"
            :detailsInfo="item"
            :key="index"
          />
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import axios from 'axios'
import {
  getWordDetail,
  getWordUrl,
  getRecommendLearning,
  wordPraise,
  wordCellect,
  wordAddRecord
} from 'config/api.conf'
import desContent from './desContent.vue'
import notes from '../videoDetail/notes.vue'
import CategoryCard from '../components/CategoryCard.vue'
import { Toast } from 'vant'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]
export default {
  components: {
    desContent,
    notes,
    CategoryCard
  },
  data() {
    return {
      isH5: false, // 是否是h5环境 还是小程序环境
      isMock: false, // 是否启动本地数据调试
      labelNodeEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test',
      active: 0,
      wordInfo: {
        content_type: '',
        comment_count: 0,
        fav_count: 0
      },
      studyRecordQuery: {
        learn_record_id: 0,
        is_finish: 0,
        is_review: 0,
        total_study_time: 0
      },
      extandList: [],
      tabList: [
        { title: '简介', key: 'des' },
        { title: '笔记', key: 'note' },
        { title: '讨论', key: 'comment' },
        { title: '延伸学习', key: 'extand' }
      ],
      loading: false,
      commentParams: null,
      word_id: '',
      src: '',
      showExitScreen: false,
      btnDisabled: false,
      isPageExposure: false
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name && this.word_id) {
          this.setComentParams()
        }
      },
      immediate: true
    }
  },
  created() {
    this.word_id = this.$route.query.word_id || ''
    if (this.$store.state.userInfo) {
      this.setComentParams()
    }
    this.getWordDetail()
  },
  beforeDestroy() {
    this.clearRecordTimer()
  },
  methods: {
    // 获取当前环境是不是小程序
    getMiniProgramEnv() {
      // 通过判断navigator.userAgent中包含miniProgram字样
      let userAgent = navigator.userAgent
      return /miniProgram/i.test(userAgent)
    },
    toSearchPage({ url, item }) {
      if (this.getMiniProgramEnv()) {
        // 小程序内直接跳转
        window.wx.miniProgram.navigateTo({
          url: url || ''
        })
      } else {
        let keywords = item.label_name || ''
        window.location.href = `https://sdc.qq.com/s/yJyZMs?scheme_type=search&keywords=${keywords}`
      }
    },
    setComentParams() {
      const hostUrl =
        process.env.NODE_ENV === 'development'
          ? process.env.VUE_APP_PORTAL_HOST_WOA
          : window.origin
      this.commentParams = {
        userName: this.$store.state.userInfo.staff_name,
        actId: this.word_id,
        actType: '10',
        appId: 'A9BiosXihR0h46ThNsAX',
        urlConfig: {
          getComments: `${hostUrl}/training/api/businessCommon/common/comment/getUserComments`,
          addComment: `${hostUrl}/training/api/businessCommon/common/comment/add`,
          deleteComment: `${hostUrl}/training/api/businessCommon/common/comment/delete/`,
          like: `${hostUrl}/training/api/businessCommon/common/comment/praised`,
          sticky: `${hostUrl}/training/api/businessCommon/common/comment/sticky`,
          show: `${hostUrl}/training/api/businessCommon/common/comment/show`
        }
      }
    },
    // 切换tab
    changeTab(val) {
      this.active = val
      if (this.active === 3) {
        this.getExtandList()
      }
    },
    // 获取文档详情
    getWordDetail() {
      if (process.env.NODE_ENV !== 'production') {
        try {
          axios.get(`${envName.v8MobileHost}api/user/file-prod/file-prod/detail`, {
            withCredentials: true,
            params: {
              file_prod_id: this.word_id
            }
          }).then(response => {
            let res = response?.data?.data || {}
            console.log('res-------------:: ', res)
            document.title = res.title || '文档详情'
            this.getWord(res.file_id)
            res.word_type =
              typeof res.suffix === 'string' && res.suffix[0].toLocaleUpperCase()
            this.wordInfo = res
            this.startAddRecord()
            this.pageExposure()
          }).catch(err => {
            console.log('err: ', err)
          })
        } catch (error) {
          console.log('error: ', error)
        }
      } else {
        getWordDetail({
          file_prod_id: this.word_id
        }).then((res) => {
          document.title = res.title || '文档详情'
          this.getWord(res.file_id)
          res.word_type =
            typeof res.suffix === 'string' && res.suffix[0].toLocaleUpperCase()
          this.wordInfo = res
          this.startAddRecord()
          this.pageExposure()
        })
      }
    },
    // 获取文档地址
    getWord(content_id) {
      getWordUrl({
        content_id
      }).then((res) => {
        this.src = res?.file_info?.doc_url || -1
      })
    },
    // 点赞 积分+1要做？？？
    handlePraise() {
      if (this.btnDisabled) return
      this.btnDisabled = true
      const key = [1, 0]
      wordPraise({
        file_prod_id: this.word_id,
        good_status: key[this.wordInfo.good_status],
        bad_status: 0
      })
        .then((res) => {
          this.wordInfo.good_status = key[this.wordInfo.good_status]
          let tip = ''
          if (this.wordInfo.good_status === 1) {
            this.wordInfo.file_good++
            tip = '点赞成功'
          } else {
            this.wordInfo.file_good--
            tip = '取消点赞成功'
          }
          if (res.credit && res.credit !== '0') {
            Toast(`${tip}, 通用积分+${res.credit}`)
          } else {
            Toast(`${tip}`)
          }
          this.btnDisabled = false
        })
        .catch(() => {
          this.btnDisabled = false
        })
    },
    // 收藏 积分+1要做？？？
    handleCellect() {
      if (this.btnDisabled) return
      this.btnDisabled = true
      const key = [1, 0]
      wordCellect({
        file_prod_id: this.word_id,
        fav_status: key[this.wordInfo.fav_status]
      })
        .then((res) => {
          this.wordInfo.fav_status = key[this.wordInfo.fav_status]
          let tip = ''
          if (this.wordInfo.fav_status === 1) {
            this.wordInfo.collect++
            tip = '收藏成功'
          } else {
            this.wordInfo.collect--
            tip = '取消收藏成功'
          }
          if (res.credit && res.credit !== '0') {
            Toast(`${tip}, 通用积分+${res.credit}`)
          } else {
            Toast(`${tip}`)
          }
          this.btnDisabled = false
        })
        .catch(() => {
          this.btnDisabled = false
        })
    },
    // 全屏
    handleFullscreen(val) {
      this.showExitScreen = !!val
    },
    getExtandList() {
      const params = {
        prod_id: this.word_id,
        prod_type: 10
      }
      this.loading = true
      getRecommendLearning(params)
        .then((res) => {
          this.extandList = (res.records || []).map((item) => {
            return {
              module_name: item.module_name, // 类型名称
              module_id: item.module_id, // 类型id
              content_name: item.title, // 内容名称
              // content_url: item.href,
              content_id: item.conn_prod_id,
              // description: item.course_desc, // 简介
              play_total_count: item.view_count, // 查看次数
              // word_num: item.word_num, // 图文/笔记 - 字数
              // praise_count: '', // 图文/笔记/案例/码客 - 点赞数
              avg_score: item.avg_score, // 得分
              created_time: item.created_at, // 时间
              // photo_url: this.getItemImg(item),
              origin_data: {
                expert_name: '', // 行家-人员名称
                meet_num: 0, // 咨询量
                avg_score: '', // 评分
                start_time: '', // 活动开始时间
                end_time: '' // 活动结束时间
              }
            }
          })
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    getItemImg({ photo_storage_type, photo_url, photo_id }) {
      if (photo_storage_type === 'contentcenter') {
        return `${envName.contentcenter}content-center/api/v1/content/imgage/${photo_url}/preview`
      }
      return photo_id
    },
    // 学习上报
    addRecod() {
      wordAddRecord({
        is_review: false,
        learn_record_id: this.wordInfo.learn_record_id,
        act_id: this.word_id
      }).then((res) => {
        if (res) {
          this.wordInfo.learn_record_id = res
        }
      })
    },
    // 开始上报
    startAddRecord() {
      this.clearRecordTimer()
      this.timer = setInterval(() => {
        this.addRecod()
      }, 15000)
    },
    // 结束上报
    clearRecordTimer() {
      if (this.timer) {
        clearInterval(this.timer)
        this.timer = null
      }
    },
    // 详情页曝光上报
    pageExposure() {
      if (!this.isPageExposure) {
        this.isPageExposure = true
        window.$dtRegionalExposurePeporting && window.$dtRegionalExposurePeporting.pageExposure({
          page_type: '移动端文档详情页',
          content_type: '文档',
          act_type: '10',
          content_name: this.wordInfo.title,
          content_id: this.word_id,
          terminal: 'H5'
        })
      }
    }
  }
}
</script>

<style lang="less" scoped>
.word-detail {
  .word-header {
    margin-bottom: 2px;
    padding: 12px 16px 8px;
    background-color: #fff;
    .word-title {
      position: relative;
      .tag {
        position: absolute;
        top: 2px;
        left: 0;
        width: 38px;
        height: 18px;
        line-height: 16px;
        border-radius: 2px;
        border: 1px solid #ff8b6c;
        background: #ff8b6c33;
        color: #ff8b6c;
        font-size: 12px;
        text-align: center;
      }
      .title {
        color: #000000e6;
        font-size: 16px;
        font-weight: 600;
        text-indent: 48px;
        line-height: 24px;
      }
    }
    .creator-info {
      margin-top: 12px;
      color: #00000066;
      font-size: 12px;
      line-height: 20px;
      .creator,
      .praise {
        margin-right: 16px;
      }
      .praise,
      .cellect {
        float: right;
        .icon {
          float: left;
          margin-right: 2px;
          width: 16px;
          height: 16px;
          position: relative;
          top: 1px;
        }
        .active-color {
          color: #0052d9;
        }
      }
      .praise {
        .icon {
          background: url('~@/assets/img/mobile/praise-grey.png') no-repeat
            center / cover;
        }
        .praise-active {
          background: url('~@/assets/img/mobile/praise-active.png') no-repeat
            center / cover;
        }
      }
      .cellect {
        .icon {
          background: url('~@/assets/img/mobile/cellect.png') no-repeat center /
            cover;
        }
        .cellect-active {
          background: url('~@/assets/img/mobile/cellect-active.png') no-repeat
            center / cover;
        }
      }
    }
    .mgt-5 {
      margin-top: 5px;
    }
    /deep/.label-show .empty {
      margin-bottom: 0;
    }
  }
  .word-content {
    margin-bottom: 8px;
    padding: 12px 16px;
    background-color: #fff;
    .word-btn {
      margin-bottom: 13px;
      height: 20px;
      .word-type-style {
        float: left;
        width: 24px;
        height: 20px;
        line-height: 20px;
        border-radius: 2px;
        background: #fef3e6;
        color: #ed7b2f;
        font-size: 12px;
        text-align: center;
      }
      .word-desc {
        float: left;
        margin-left: 12px;
        color: #000000e6;
        font-size: 12px;
        line-height: 20px;
      }
      .full-screen,
      .download {
        float: right;
        width: 20px;
        height: 20px;
      }
      .full-screen {
        margin-right: 16px;
        background: url('~@/assets/img/mobile/full-screen.png') no-repeat center /
          cover;
      }
      .download {
        background: url('~@/assets/img/mobile/download-disabled.png') no-repeat
          center / cover;
      }
    }
    .word-ifame {
      width: 100%;
      height: 477px;
      position: relative;
      .exit-full-screen {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 32px;
        height: 32px;
        border-radius: 16px;
        background: #ffffff;
        box-shadow: 0 0 8px 0 #22222233;
        text-align: center;
        .exit-icon {
          display: inline-block;
          margin-top: 6px;
          width: 20px;
          height: 20px;
          background: url('~@/assets/img/mobile/exit-full-screen.png') no-repeat
            center / cover;
        }
      }
      #wordIframe {
        width: 100%;
        height: 100%;
      }
    }
    .full-screen-style {
      position: fixed;
      padding: 16px 24px;
      height: 100vh;
      top: 0;
      left: 0;
      background-color: #fff;
      z-index: 99999999;
    }
  }
  .main-content {
    margin-top: 4px;
    position: relative;
    z-index: 9;
    .comment {
      background: #fff;
      padding-top: 10px;
    }
  }
  :deep(.van-tabs--line) {
    .van-tabs__wrap {
      height: 38px;
    }
    .van-tab--active {
      font-weight: bold;
    }
    .van-tabs__line {
      width: 28px;
    }
  }
  /deep/.van-tab__text {
    font-size: 12px;
  }
  .loading-text {
    height: 80px;
    line-height: 80px;
    text-align: center;
    color: #00000066;
  }
  .learn-empty {
    padding: 20px 0;
    background-color: #fff;
    text-align: center;
    .empty-text {
      margin: 16px 0 20px;
    }
  }
  .more-learn {
    padding: 0 16px;
    background: #fff;
    .content-inner {
      border-bottom: 0.5px solid #e7e7e7;
    }
  }
}
.scroll-disabled {
  height: 100%;
  overflow: hidden;
}
</style>
