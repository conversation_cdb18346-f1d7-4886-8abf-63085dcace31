<template>
  <!-- 素材详情 -->
  <div class="material-detail-container">
    <div class="header-box">
      <div class="header-top">
        <span class="title">素材详情</span>
        <div>
          <el-button type="primary" size="small" @click="edit">编辑</el-button>
          <!-- <el-button plain size="small">重新转码</el-button> -->
          <el-button type="danger" plain size="small" @click="handleDelete">删除</el-button>
        </div>
      </div>
      <div class="header-bottom">
        <div class="tag-title-box">
          <span class="tag-label" v-if="filterResourceName">{{ filterResourceName }}</span>
          <span class="tag-title">{{ detailData.file_name || detailData.file_show_name }}</span>
        </div>
        <div class="code-status">
          <span class="item-value"><span class="label">转码状态：</span>{{ statusLabel(detailData.status) }}</span>
          <span class="item-value"><span class="label">存放位置：</span>{{ detailData.content_id ? '云资源' : '内网资源' }}</span>
          <span class="item-value"><span class="label">来源：</span>{{ origin_info[detailData.content_type] }}</span>
          <span class="item-value"><span class="label">上传人：</span>{{ detailData.creator_name }}</span>
          <span class="item-value"><span class="label">上传时间：</span>{{ detailData.created_at }}</span>
        </div>
      </div>
    </div>
    <div class="tab-main">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <!-- <el-tab-pane label="学习详情" name="two">学习详情</el-tab-pane> -->
        <el-tab-pane label="引用详情" name="first"></el-tab-pane>
      </el-tabs>
      <div class="quote-detail">
        <el-table :data="tableData" header-row-class-name="table-header-style" row-class-name="table-row-style"
          style="width: 100%">
          <el-table-column prop="course_name" label="资源名称">
            <!-- <template v-slot="prop">
              <a class="content-url" target="_blank" :href="prop.row.file_id">{{ prop.row.file_show_name }}</a> 
            </template> -->
          </el-table-column>
          <el-table-column prop="resource_type" label="资源类型" show-overflow-tooltip></el-table-column>
          <el-table-column prop="course_status_name" label="资源状态"></el-table-column>
          <el-table-column prop="created_at" label="引用时间"></el-table-column>
          <el-table-column prop="creator_name" label="引用人"></el-table-column>
          <el-table-column prop=" " label="操作">
            <template slot-scope="scope">
              <el-button @click="toPath(scope.row, '0')" type="text" size="small" v-if="scope.row.resource_type === '培养项目'">管理项目</el-button>
              <el-button @click="toPath(scope.row, '1')" type="text" size="small" v-if="scope.row.resource_type === '网络课'">编辑课程</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>
<script>
import { metaerialDetail, deleteMaterial, quoteSearch } from '@/config/mooc.api.conf.js'
import { mapState } from 'vuex'
const origin_info = {
  'PGC': '个人分享',
  'UGC': '组织/项目'
}
export default {
  data() {
    return {
      activeName: 'first',
      detailData: {},
      origin: '',
      options: [],
      origin_info,
      tableData: []
    }
  },
  computed: {
    ...mapState(['userLimitInfo']),
    filterResourceName() {
      const { file_type } = this.detailData
      let name = ''
      if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(file_type)) {
        name = '视频'
      } else if (file_type === 'Audio') {
        name = '音频'
      } else if (file_type === 'Article') {
        name = '文章'
      } else if (file_type === 'Doc') {
        name = '文档'
      } else if (file_type === 'Scorm') {
        name = 'Scorm'
      } else if (file_type === 'Flash') {
        name = '压缩包'
      }
      return name
    },
    statusLabel() {
      return (val) => {
        const failStatusList = [3, 6, 9, 12, 16]
        const goingStatusList = [2, 4, 5, 7, 8, 10, 11, 14, 15]
        return failStatusList.includes(val) ? '转码失败' : 
          goingStatusList.includes(val) ? '转码中' : 
            val === 13 ? '转码成功' : val === 1 ? '待转码' : '' 
      }
    }
  },
  mounted() {
    this.getDetail()
    this.queoteDetail()
  },
  methods: {
    queoteDetail() {
      const { file_id } = this.$route.query
      quoteSearch(file_id).then((res) => {
        this.tableData = res
      })
    },
    getDetail() {
      const { file_id } = this.$route.query
      metaerialDetail(file_id).then((res) => {
        this.detailData = res
      })
    },
    edit() {
      const { file_id, file_type } = this.detailData
      if (this.detailData?.content_id) {
        const { href } = this.$router.resolve({
          name: 'materialUpload',
          query: { uploadType: file_type, file_id }
        })
        window.open(href, '_blank')
        return
      }
      const url = process.env.NODE_ENV === 'production' ? `https://learn.woa.com/manage/upload/edit/${file_id}` : `https://test-learn.woa.com/manage/upload/edit/${file_id}`
      window.open(url, '_blank')
    },
    handleDelete() {
      this.$confirm(`${this.detailData.file_show_name}`, '确定删除素材吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
        // type: 'warning'
      }).then(() => {
        deleteMaterial(this.detailData.file_id).then((res) => {
          console.log('删除', res)
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
          const index = this.tableData.records.findIndex((e) => e.file_id === this.detailData.file_id)
          this.tableData.records.splice(index, 1)
        })
      })
    },
    toPath(row, type) {
      // 培养项目
      if (type === '0') {
        const { href } = this.$router.resolve({
          name: 'project-list'
        })
        window.open(href, '_blank')
        return
      }
      // 编辑
      const obj = {
        production: {
          woa: 'https://learn.woa.com/manage/net/eidt',
          oa: 'http://v8.learn.oa.com/manage/net/eidt'

        },
        test: {
          woa: 'https://test-learn.woa.com/manage/net/eidt',
          oa: 'http://test.v8.learn.oa.com/manage/net/eidt'
        }
      }
      const key = location.hostname.endsWith('.woa.com') ? 'woa' : 'oa'
      const url = process.env.NODE_ENV === 'production' ? obj['production'][key] : obj['test'][key]
      const commonUrl = url + `?net_course_id=${row.course_id}`
      window.open(commonUrl)
    },
    handleClick() {

    }
  }
}
</script>
<style lang="less" scoped>
.material-detail-container {
  background: #F6F7F9;

  .header-box {
    background-color: #fff;
    border-radius: 4px;

    .header-top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 56px;
      padding: 0 20px;
      border-bottom: 1px solid #f3f3f3ff;

      .title {
        font-size: 16px;
        font-weight: bold;
      }

      .el-button {
        width: 88px;
      }
    }

    .header-bottom {
      color: #000000e6;
      padding: 30px 0px 36px 36px;

      .tag-title-box {
        display: flex;
        align-items: center;
      }

      .tag-label {
        // width: 38px;
        height: 18px;
        border-radius: 2px;
        opacity: 1;
        border: 1px solid #0052d9ff;
        background: #ecf2feff;
        color: #0052D9;
        padding: 0 7px;
        margin-right: 16px;
      }

      .tag-title {
        font-weight: bold;
        width: 600px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
        display: inline-block;
      }

      .code-status {
        margin-top: 16px;

        .item-value+.item-value {
          margin-left: 48px;
        }

        .item-value {
          .label {
            color: #00000099;
          }
        }
      }
    }
  }

  .tab-main {
    margin-top: 12px;
    background-color: #fff;
    border-radius: 4px;
  }

  :deep(.el-tabs) {
    color: #00000099;

    .el-tabs__header {
      padding: 16px 24px 0px;
      margin: unset;
    }

    .el-tabs__active-bar {
      margin-top: 12px;
      // width: 24px !important;
      // transform: translateX(16px) !important;
    }

    .el-tabs__nav {
      height: 40px;
      border-bottom: 1px solid #eeeeeeff;
      width: 100%;

      .el-tabs__item {
        height: 22px;
        line-height: 22px;
      }
    }
  }

  .quote-detail {
    padding: 20px 20px 25px 20px;

    .header-form {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .form {
      display: flex;

      .item-form+.item-form {
        margin-left: 44px
      }

      .label {
        color: #00000099;
      }

      .el-input,
      .el-select {
        width: 280px;
      }
    }

    .btn {
      .el-button+.el-button {
        margin-left: 15px;
      }
    }
  }
}</style>
