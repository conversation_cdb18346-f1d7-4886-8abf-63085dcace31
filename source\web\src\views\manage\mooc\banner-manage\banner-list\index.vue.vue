<template>
  <div class="banner-list">
    <div class="top-title">
      <span>轮播图管理</span>
      <el-button type="primary" @click="handleAdd" icon="el-icon-plus" size='small'>新建轮播图</el-button>
    </div>
    <el-form ref="form" :model="form" inline label-width="80px">
      <el-form-item label="标题">
        <el-input v-model="form.banner_name" placeholder="请输入标题" clearable></el-input>
      </el-form-item>
      <el-form-item label="状态">
        <el-select v-model="form.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch(1)" size='small'>搜索</el-button>
        <el-button @click="handleReset" size='small'>重置</el-button>
      </el-form-item>
    </el-form>
    <el-table
      :data="tableData.records"
      style="width: 100%"
      header-row-class-name="table-header-style"
      row-class-name="table-row-style"
    >
      <el-table-column label="序号" width="60">
        <template slot-scope="scope">
          <span>{{ handlerTableIndex(scope.row.order_no) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="banner_name" label="标题" show-overflow-tooltip min-width="200">
        <template slot-scope="scope">
          <span class="table-course-title" @click="toManagePage(scope.row)">{{ scope.row.banner_name }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="img_content_id" label="图片" width="260">
        <template slot-scope="scope">
          <div @click="toLink(scope.row.link_url)">
            <el-image lazy fit="fill" :src="handleImgUrlFn(scope.row.img_content_id)"  class="table-banner">
              <div class="image-slot" slot="placeholder">
                <i class="el-icon-loading"></i>
              </div>
              <div class="error-cover" slot="error">
                <img :src="require('@/assets/mooc-img/default_bg_img.png')" alt="" />
              </div>
            </el-image>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" min-width="100">
        <template slot-scope="scope">
          <div class="statr-box" :class="[`color-${scope.row.status}`]">{{ handlerStatus(scope.row.status) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="creator_name" label="创建人" min-width="120" show-overflow-tooltip></el-table-column>
      <el-table-column prop="created_at" label="创建时间" min-width="180">
        <template slot-scope="scope">
          <span>{{ handlerDateFormatFn(scope.row.created_at) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="250">
        <template slot-scope="scope">
          <div class="operat-btn-box">
            <el-link type="primary" @click="handlerSort(scope.row)" :underline="false">排序</el-link>
            <el-link type="primary" @click="handleEdit(scope.row)" :underline="false">编辑</el-link>
            <el-link type="primary" v-if="[0, 9].includes(scope.row.status)" @click="handlerOperate('upper', scope.row, scope.$index)" :underline="false">上架</el-link>
            <el-link type="primary" v-else-if="scope.row.status === 1" @click="handlerOperate('lower', scope.row, scope.$index)" :underline="false">下架</el-link>
            <el-link @click="handlerOperate('del' , scope.row, scope.$index)" type="danger" :underline="false">删除</el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="tableData.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="current"
      :page-sizes="[5, 10, 20, 30, 50, 100]"
      :page-size="size"
      layout="total,  prev, pager, next, sizes, jumper"
      :total="tableData.total"
      >
      </el-pagination>
      <!-- 添加/编辑轮播图 -->
      <addBannerDialog ref="addBannerDialogRef" :visible.sync="addBannerDialogVisible" @onSearch="onSearch"></addBannerDialog>
      <!-- 排序 -->
      <sortDialog ref="sortDialogRef" :visible.sync="sortDialogVisible" @onSearch="onSearch"></sortDialog>
  </div>
</template>

<script>
import pager from '@/mixins/pager'
import { getBannerListApi, deleteBannerApi, bannerStatusApi } from '@/config/mooc.api.conf.js'
import { handlerDateFormat, handleImgUrl } from '@/utils/tools.js'
import addBannerDialog from './component/add-banner-dialog.vue'
import sortDialog from './component/sort-dialog.vue'
let operateType = {
  'del': '删除',
  'upper': '上架',
  'lower': '下架'
}
let statusType = {
  '0': '已下架',
  '1': '上架中',
  '9': '待上架'
}
export default {
  mixins: [pager],
  components: {
    addBannerDialog,
    sortDialog
  },
  data() {
    return {
      form: {
        banner_name: '',
        status: null
      },
      addBannerDialogVisible: false,
      sortDialogVisible: false,
      statusOptions: [
        { label: '全部', value: null },
        { label: '上架中', value: 1 },
        { label: '已下架', value: 0 },
        { label: '待上架', value: 9 }
      ],
      tableData: {
        records: [],
        total: 0
      }
    }
  },
  mounted() {
    this.onSearch(1)
  },
  methods: {
    // 列表查询
    onSearch(current = 1) {
      this.current = current
      let { banner_name, status } = this.form
      let params = {
        act_type: '11',
        banner_name,
        status,
        current,
        size: this.size
      }
      if (params.status === null) {
        delete params.status
      }
      getBannerListApi(params).then((res) => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    // 新增
    handleAdd() {
      this.addBannerDialogVisible = true
      this.$nextTick(() => {
        this.$refs.addBannerDialogRef.initData({
          moduleName: 'add'
        })
      })
    },
    // 编辑
    handleEdit(row) {
      this.addBannerDialogVisible = true
      this.$nextTick(() => {
        this.$refs.addBannerDialogRef.initData({
          moduleName: 'edit',
          banner_type: row.banner_type,
          banner_name: row.banner_name,
          link_url: row.link_url,
          img_content_id: row.img_content_id,
          status: row.status,
          id: row.id
        })
      })
    },
    handlerOperate(type, row, index) {
      this.$messageBox.confirm(`确定${operateType[type]}选中的轮播图吗？`, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        switch (type) {
          case 'del':
            this.handlerDelete(row, index)
            break
          case 'upper':
            this.handlerUpper(row, index)
            break
          case 'lower':
            this.handlerUpper(row, index)
            break
          default:
            break
        }
      })
    },
    // 删除
    handlerDelete({ id }, index) {
      deleteBannerApi(id).then(res => {
        if (this.tableData.records.length <= 1) {
          this.onSearch(1)
        } else {
          this.tableData.records.splice(index, 1)
          --this.tableData.total
        }
        this.$message.success('删除成功')
      })
    },
    // 上/下架
    handlerUpper({ id, status }, index) {
      const statusVal = status === 1 ? 0 : 1
      bannerStatusApi({ id: Number(id), status: statusVal }).then(res => {
        this.tableData.records[index].status = statusVal
        this.$message.success(`${statusVal === 1 ? '上架' : '下架'}成功`)
      })
    },
    // 排序
    handlerSort({ id, order_no }) {
      this.sortDialogVisible = true
      this.$refs.sortDialogRef.initData({
        id: Number(id),
        order_no
      })
    },
    // 重置
    handleReset() {
      this.form = {
        banner_name: '',
        status: null
      }
      this.onSearch()
    },
    toLink(link) {
      window.open(link)
    },
    // 处理table下标
    handlerTableIndex(i) {
      return i < 10 ? `0${i}` : i
    },
    handlerDateFormatFn(d) {
      return handlerDateFormat(d)
    },
    handleImgUrlFn(d) {
      if (d && d.length >= 32) {
        return handleImgUrl(d)
      }
      return require('@/assets/mooc-img/default_bg_img.png')
    },
    handlerStatus (s) {
      return statusType[s]
    }
  }
}
</script>
<style lang="less" scoped>
.banner-list {
  padding: 20px;
  min-height: 100%;
  background: #fff;
  .top-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    span {
      font-size: 24px;
      font-weight: bold;
    }
  }
  .table-course-title {
    color: #0052D9;
    cursor: pointer;
  }
  .operat-btn-box {
    .el-link + .el-link {
      margin-left: 10px;
    }
  }
  .table-banner img {
    width: 230px;
    height: 80px;
    object-fit: cover;
    cursor: pointer;
  }
  .statr-box {
    height: 20px;
    line-height: 20px;
    padding: 0 10px;
    font-size: 12px;
    border-radius: 4px;
    display: inline-block;
    box-sizing: border-box;
    white-space: nowrap;
    border: unset;
  }
  .color-0 {
    background-color: #cccccc;
    border-color: #e7faf8;
    color: #fff
  }
  .color-1 {
    background-color: #e7faf8;
    border-color: #cef6f0;
    color: #0ad0b6
  }
  .color-9 {
    background-color: #85e8db;
    border-color: #cef6f0;
    color: #fff
  }
}
</style>
