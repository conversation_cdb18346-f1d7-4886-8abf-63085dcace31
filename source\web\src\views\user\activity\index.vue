<template>
  <div class="face-page">
    <div :class="['contain-main']">
      <div class="left">
        <div class="left-top">
          <div class="left-top-flex">
            <div class="left-top-flex-left">
              <div class="top-header">
                <div class="top-header-l">
                  <el-image lazy fit="fill" :src="courseData.photo_url ? courseData.photo_url : require('@/assets/img/activity-default.png')" class="item-image">
                    <div slot="error" class="image-slot">
                      <i class="default-icon-picture"></i>
                    </div>
                  </el-image>
                  <span class="top-header-l-tips">活动</span>
                </div>
                <div class="top-header-r">
                  <el-tooltip class="item" effect="dark" :content="courseData.activity_name" placement="top">
                    <div class="face-title">{{courseData.activity_name}}</div>
                  </el-tooltip>
                  <div class="desc-tag-box">
                    <div class="tag-left">
                      <div class="tag-content">
                        <div class="tag-list-box">
                          <!-- 标签显示 -->
                          <template v-if="courseData.activity_id">
                            <sdc-label-show ref="labelShow" :labelNodeEnv="labelNodeEnv" :actType="4" :courseId="activity_id" :showBurialPoint="true" :courseInfo="courseInfo('')">
                            </sdc-label-show>
                          </template>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="top-info">
                    <span class="top-iconInfo">
                      <i class="icon-view"></i>
                      <span>{{ courseData.view_count || 0 }} {{ $langue('NetCourse_Views', { defaultText: '次观看' }) }}</span>
                      <i class="icon-scores"></i>
                      <span> {{courseData.avg_score || 0}}分</span>
                      <span class="time">{{ courseData.created_at || $langue('NetCourse_CreateTime', { defaultText: '创建时间' }) + ':--' }}</span>
                    </span>
                  </div>
                </div>
              </div>
              <!-- 点赞收藏分享 -->
              <div class="user-operat-box">
                <div class="operat-left">
                  <div :class="['item-operat', {'add-operat':isAddCourse}]" @click="addCourseDialogShow = true" :dt-areaid="dtCommon('areaid', '添加到课单')" :dt-eid="dtCommon('eid', '添加到课单')" :dt-remark="dtCommon('remark', '添加到课单')">
                    <i class="icon-add"></i>
                    <span>{{ $langue('Article_AddCourseList', { defaultText: '添加到课单' }) }}</span>
                  </div>
                  <div :class="['item-operat', {'active-isCollect-operat': zanAndcollect.isCollect}]" @click="handleLikeOrFav(2)" :dt-areaid="dtCommon('areaid',  zanAndcollect.isCollect ? '取消收藏' : '已收藏')" :dt-eid="dtCommon('eid', zanAndcollect.isCollect ? '取消收藏' : '已收藏')" :dt-remark="dtCommon('remark', zanAndcollect.isCollect ? '取消收藏' : '已收藏')">
                    <i class="icon-collect"></i>
                    {{ zanAndcollect.isCollect ? $langue('Mooc_ProjectDetail_BasicInfo_collected', { defaultText: '已收藏'}) : $langue('Mooc_ProjectDetail_BasicInfo_collect', { defaultText: '收藏'}) }}
                  </div>
                  <div class="item-operat white-note-btn" @click="createNote" :dt-areaid="dtCommon('areaid', '写笔记')" :dt-eid="dtCommon('eid', '写笔记')" :dt-remark="dtCommon('remark', '写笔记')">
                    <i class="white-icon"></i>
                    <span>{{ $langue('NetCourse_Notes', { defaultText: '写笔记' }) }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="left-top-flex-right">
              <div class="operat-right">
                <img class="operat-shore-code" :src="qrUrl" alt="">
                <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 扫码即可分享至微信<br /> 或<span class="shore-operat-link" @click="handleShow()" :dt-areaid="dtCommon('areaid', '分享')" :dt-eid="dtCommon('eid', '分享')" :dt-remark="dtCommon('remark', '分享')">点击复制链接进行分享</span></p>
              </div>
            </div>
          </div>
        </div>
        <!-- apply -->
        <div class="class-content">
          <div class="class-content-item" :dt-eid="dtTableList('eid', courseData)" :dt-areaid="dtTableList('area', courseData)" :dt-remark="dtTableList('remark', courseData)">
            <div class="item-class-info">
              <div class="item-class-info_left">
                <div class="info-lable"><i class="icon icon-time"></i> {{courseData.start_end_time}}</div>
                <div class="info-lable"><i class="icon icon-name"></i> {{courseData.head_teacher_name}}</div>
                <div class="info-lable"><i class="icon icon-city"></i> <span v-html="teachingType(courseData)"></span> </div>
              </div>
              <div class="item-class-info_right">
                <!-- 可报名 -->
                <div v-if="statusBtn === 1" :class="['status-button', 'status1', {'registered-btn': registered }]" @click="handlerBeforeApply(1, '报名')" :dt-eid="dtTable('eid', '报名', courseData)" :dt-areaid="dtTable('area', '报名', courseData)" :dt-remark="dtTable('remark', '报名', courseData)">
                  <span class="button-text">报名</span>
                  <span class="button-info" v-if="courseData.is_limit_student_count !== 0">剩余名额 {{surplusQuota}}</span>
                </div>
                <!-- 已报满-等待列表 -->
                <div v-else-if="statusBtn === 2 && courseData.allow_waiting_list" :class="['status-button', 'status1', {'registered-btn': registered }]" @click="handlerBeforeApply(1, '加入等待列表')">
                  <span class="button-text">加入等待列表</span>
                  <span class="button-info">排队人数 {{courseData.waiting_count}}</span>
                </div>
                <!-- 线下已满，无等待列表，线上报名 -->
                <div v-else-if="statusBtn === 2 && courseData.teaching_type.indexOf('2')" :class="['status-button', 'status1', {'registered-btn': registered }]" @click="handlerBeforeApply(1, '加入等待列表')">
                  <span class="button-text">报名</span>
                  <span class="button-info">线下名额0，线上名额不限</span>
                </div>
                <!-- 报名名额已报满 -->
                <div v-else-if="statusBtn === 2 && !courseData.allow_waiting_list && courseData.teaching_type.indexOf('2') === -1" class="status-button status2" >
                  <span class="button-text">开班提醒</span>
                  <span class="button-info">已无剩余名额</span>
                </div>
                <!-- 等待列表中 -->
                <div v-else-if="statusBtn === 3" :class="['status-button', 'status2', {'registered-btn': registered }]" @click="handlerCancelApply(2, '退出等待队列')">
                  <span class="button-text">已报名候补中</span>
                  <span class="button-info">点击可取消报名</span>
                </div>
                <!-- 可注销 -->
                <div v-else-if="statusBtn === 5" :class="['status-button', 'status2', {'registered-btn': registered }]" @click="handlerCancelApply(2, '取消报名')">
                  <span class="button-text" >已报名{{ attendStatusName }}</span>
                  <span class="button-info">点击可取消报名</span>
                </div>
                <!-- 上级审核 -->
                <div v-else-if="statusBtn === 7" :class="['status-button', 'status2', {'registered-btn': registered }]" @click="handlerCancelApply(2, '取消报名')">
                  <span class="button-text">已报名，待审批</span>
                  <span class="button-info">点击可取消报名</span>
                </div>
                <!-- 已报名-不可注销 -->
                <div v-else-if="statusBtn === 6 && courseData.reg_status === 0" class="status-button status2">
                  <span class="button-text">已报名{{ attendStatusName }}</span>
                  <span class="button-info">已超过注销截止时间</span>
                </div>
                <!-- 已报名-不可注销-等待队列 -->
                <div v-else-if="statusBtn === 6 && courseData.reg_status === 2" class="status-button status2">
                  <span class="button-text">已报名候补中</span>
                  <span class="button-info">已超过注销截止时间</span>
                </div>
                <!-- 已报名-不可注销-上级审批 -->
                <div v-else-if="statusBtn === 6 && courseData.reg_status === 3" class="status-button status2">
                  <span class="button-text">已报名，待审批</span>
                  <span class="button-info">已超过注销截止时间</span>
                </div>
                <!-- 报名名额已报满-已截止报名 -->
                <div v-else-if="statusBtn === 4" class="status-button status2">
                  <span class="button-text">开班提醒</span>
                  <span class="button-text">已截止报名</span>
                </div>
                <!-- 未报名霸课 -->
                <div v-else-if="statusBtn === 8" class="status-button status2">
                  <span class="button-text">已报名{{ attendStatusName }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="tabs-content">
          <el-tabs v-model="tabActiveName">
            <el-tab-pane :label="$langue(tabItem.label, { defaultText: tabItem.text })" :name="tabItem.name" v-for="(tabItem, index) in tabList" :key="index">
              <span slot="label" :dt-areaid="dtCommon('areaid', tabItem.text)" :dt-eid="dtCommon('eid', tabItem.text)" :dt-remark="dtCommon('remark', tabItem.text)">
                {{tabItem.text}}
              </span>
            </el-tab-pane>
          </el-tabs>
          <!-- 活动介绍 -->
          <netDesc v-if="tabActiveName === 'desc'" dtPageType="活动详情页-新版" :courseData="courseData" @handleCourseDetail="getCourseDetail" :specialUsers="specialUsers" :isPreview="false" courseType="face" />
          <!-- 笔记 -->
          <netNotes v-if="tabActiveName === 'notes'" :act_type="4" dtPageType="活动详情页-新版" :courseData="courseData"></netNotes>
          <!-- 讨论 -->
          <netComment v-if="tabActiveName === 'comment'" dtPageType="活动详情页-新版" :courseData="courseData" />
        </div>
        <!-- 包含此内容 -->
        <div class="includes-content">
          <incudesContent :module_id="4" :courseData="courseData" dtPageType="活动详情页-新版" />
        </div>
      </div>
      <div class="right">
        <!-- 右侧 -->
        <rightArea v-if="!isFormMooc && isShowRight " ref="rightSideRef" :courseData="courseData" @noRightCard="noRightCard"></rightArea>
        <!-- 右侧图标模块 -->
        <rightSideIcon :modelTabValue="modelTabValue"></rightSideIcon>
      </div>
    </div>
    <AddCourseDialog :visible.sync="addCourseDialogShow" :itemData.sync="addCourseDialogData" @addedHandle="isAddCourse=true" />
    <ShareDialog ref="shareDialog" :title="courseData.activity_name" :isShow.sync="sharedialogShow" />
    <!-- 报名选择参加方式 -->
    <chooseParticipationMethodPopup v-if="chooseParticipationMethodVisible" :visible.sync="chooseParticipationMethodVisible" :courseData="courseData" :regStatus="statusBtn" @confirm="handlerParticipationApply"></chooseParticipationMethodPopup>
    <!-- 报名审批 -->
    <registrationApprovalPopup v-if="registrationApprovalVisible" :visible.sync="registrationApprovalVisible" :courseData="courseData" @confirm="handlerSelectApprover"></registrationApprovalPopup>
    <!-- 学员问卷 -->
    <studentQuestionnairePopup v-if="studentQuestionnaireVisible" :visible.sync="studentQuestionnaireVisible" :courseData="courseData" :joinType="joinType" :parentStaff="parentStaff" @onClose="getCourseDetail"></studentQuestionnairePopup>
  </div>
</template>

<script>
import { AddCourseDialog } from '@/components/index'
import ShareDialog from '@/views/components/shareDialog'
import netComment from './components/netcomment'
import netNotes from '../netcourse/grayPlay/components/netNotes'
import netDesc from '../netcourse/grayPlay/components/netDesc'
import incudesContent from '../netcourse/grayPlay/components/incudesContent'
import rightArea from './components/rightArea'
import rightSideIcon from '../netcourse/grayPlay/components/rightSideIcon'
import chooseParticipationMethodPopup from '@/views/components/chooseParticipationMethodPopup.vue'
import registrationApprovalPopup from '@/views/components/registrationApprovalPopup.vue'
import studentQuestionnairePopup from '@/views/components/studentQuestionnairePopup.vue'

import MoocJs from 'sdc-moocjs-integrator'
import {
  getActivityInfo,
  setMemberStatus,
  activityCheckFavorite,
  activityDeleteFavorite,
  activityAddFavorite
} from 'config/api.conf'
import { getlabelSpecialUsers, getMobileQrcode } from 'config/mooc.api.conf.js'
import { mapState } from 'vuex'
export default {
  name: 'face',
  components: {
    AddCourseDialog,
    ShareDialog,
    netComment,
    netNotes,
    netDesc,
    incudesContent,
    rightArea,
    rightSideIcon,
    chooseParticipationMethodPopup,
    registrationApprovalPopup,
    studentQuestionnairePopup
  },
  data() {
    return {
      labelNodeEnv:
        process.env.NODE_ENV === 'production' ? 'production' : 'test',
      courseData: {},
      isShowRight: true,
      statusBtn: 0,
      cityDown: false,
      tabActiveName: 'desc',
      specialUsers: false,
      registered: false,
      zanAndcollect: {
        isZan: false,
        isCollect: false
      },
      isAddCourse: false,
      addCourseDialogShow: false,
      sharedialogShow: false,
      addCourseDialogData: {
        module_id: 4,
        module_name: '活动'
      },
      modelTabValue: 'video_model',
      qrUrl: '',
      tabList: [
        {
          label: 'Mooc_ProjectDetail_Notice_Introduce',
          name: 'desc',
          text: '活动介绍'
        },
        { label: 'NetCourse_Note', name: 'notes', text: '笔记' },
        {
          label: 'Mooc_ProjectDetail_Notice_Comments',
          name: 'comment',
          text: '讨论'
        }
      ],
      chooseParticipationMethodVisible: false,
      registrationApprovalVisible: false,
      studentQuestionnaireVisible: false,
      joinType: 1,
      joinTypeName: '',
      parentStaff: {
        parent_staff_id: '',
        parent_name: ''
      }
    }
  },
  mounted() {
    window.addEventListener('message', (e) => {
      // 来自问卷手动调用完成的方法
      const { event, type } = e.data
      if (type === 'questionnaire' && event === 'completeStatusUpdata') {
        this.handlerApply(1, '', this.joinType)
      }
    })
    this.$store.dispatch('getIsBusy')
    this.getCourseDetail()
    this.codeTopHead()
    if (this.$route.query.preview) {
      this.tabList = this.tabList.filter(item => item.name !== 'comment')
    }
  },
  computed: {
    ...mapState({
      showPicture: (state) => state.net.showPicture,
      volume: (state) => state.net.volume,
      playbackRate: (state) => state.net.playbackRate,
      playStatus: (state) => state.net.playStatus,
      userInfo: (state) => state.userInfo
    }),
    activity_id() {
      return this.$route.query.activity_id
        ? parseInt(this.$route.query.activity_id)
        : ''
    },
    isFormMooc() {
      return this.$route.query.from === 'mooc'
    },
    teachingType() {
      return (val) => {
        if (val && val.teaching_type) {
          const { teaching_type, location, city } = val
          // 1 线下面试 2 在线授课 3 网络研讨会',
          const typeObj = {
            1: '线下地址：' + `${city}-${location}`,
            2: '线上参加：报名成功后自动推送活动链接',
            3: '线上参加：报名成功后自动推送活动链接'
          }
          const types = teaching_type.split(';').map(Number)
          return types.map(type => typeObj[type]).join('<br/>')
        }
        return ''
      }
    },
    surplusQuota() {
      const { max_student_count, regist_count } = this.courseData
      if (!max_student_count) return 0
      return Number(max_student_count) - Number(regist_count)
    },
    // 按钮状态 1-可报名 2-已报满-等待列表 3-已加入等待列表 4-截至报名 5-已报名-可注销 6-已报名-不可注销
    // statusBtn() {
    //   const { my_status } = this.courseData
    //   return my_status || 1
    // },
    // label-show组件所需要的埋点数据
    courseInfo() {
      return (type) => {
        let { activity_id } = this.$route.query
        const click_type = type === 'btn' ? 'button' : 'data'
        const container = type === 'btn' ? `介绍-打标签` : `介绍-标签`
        return {
          mooc_course_id: activity_id,
          page: this.courseData.activity_name, // 任务名称
          page_type: '活动页详情-新版',
          container, // 板块的名称
          click_type,
          terminal: 'PC'
        }
      }
    },
    dtTable() {
      return (type, name, item) => {
        if (type === 'eid') {
          return `element_${item ? item.activity_id : ''}_${name}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.activity_name,
            page_type: '活动页情页-新版',
            container: '',
            click_type: 'button',
            content_type: '报名',
            content_id: '',
            content_name: '',
            button_name: name,
            terminal: 'PC'
          })
        } else {
          return `area_${item ? item.activity_id : ''}_${name}`
        }
      }
    },
    dtCommon() {
      return (type, val) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.activity_name,
            page_type: '活动详情页-新版',
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: val,
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.activity_id}_${val}`
        } else {
          return `area_${this.activity_id}_${val}`
        }
      }
    },
    dtTableList() {
      return (type, item) => {
        if (type === 'eid') {
          return `element_${this.activity_id}_${item.class_id}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.activity_name,
            page_type: '活动详情页-新版',
            container: '',
            click_type: 'data',
            content_type: '活动',
            content_id: '',
            content_name: item.class_name,
            terminal: 'PC'
          })
        } else {
          return `area_${this.activity_id}_${item.class_id}`
        }
      }
    },
    attendStatusName() {
      const { attend_status } = this.courseData
      switch (attend_status) {
        case 4:
          return '，全勤'
        case 5:
          return '，缺勤'
        case 18:
          return '，部分缺勤'
        case 19:
          return '，临时取消'
        default:
          return ''
      }
    }
  },
  methods: {
    initData() {
      this.loadComment = true
      // 任务已完成时，开启任务同步弹窗
      if (this.courseData.is_finish * 1 === 1 && this.isFormMooc) {
        MoocJs.complete('init')
      }
      this.getZanAndCollectStatus()
    },
    // 报名之前的处理
    handlerBeforeApply(type, message) {
      this.joinType = ''
      this.parentStaff = {
        parent_staff_id: '',
        parent_name: ''
      }
      const { teaching_type, before_class_survey, need_appovel } = this.courseData
      let teachingType = teaching_type.split(';')
      this.joinTypeName = message
      if (teachingType.length > 1) {
        this.chooseParticipationMethodVisible = true
        return
      }
      this.joinType = teaching_type
      // 需要审核，且参与方式为线下
      if (need_appovel && teachingType[0] === '1') {
        this.registrationApprovalVisible = true
        return
      }
      if (before_class_survey?.wj_url) {
        this.studentQuestionnaireVisible = true
      } else {
        this.handlerApply(type, message, teaching_type)
      }
    },
    // 报名 - 选择参与方式
    handlerParticipationApply(e) {
      this.joinType = e.join_type
      const { need_appovel, before_class_survey } = this.courseData
      // 需要审核，且参与方式为线下
      if (need_appovel && this.joinType === 1) {
        this.registrationApprovalVisible = true
        return
      }
      if (before_class_survey?.wj_url) {
        this.studentQuestionnaireVisible = true
      } else {
        this.handlerApply(1, this.joinTypeName, this.joinType)
      }
    },
    // 报名审核 - 选择审核人员
    handlerSelectApprover(e) {
      this.parentStaff = {
        parent_staff_id: e.parent_staff_id,
        parent_name: e.parent_name.split('(')[0]
      }
      const { before_class_survey } = this.courseData
      if (before_class_survey?.wj_url) {
        this.studentQuestionnaireVisible = true
      } else {
        this.handlerApply(1, '', this.joinType)
      }
    },
    // 报名
    // type 1报名, 2取消报名 
    handlerApply(type, message, joinType) {
      let params = {
        activity_id: this.activity_id,
        action_code: type,
        ...((type === 1 && joinType) && { join_type: Number(joinType) }),
        ...((type === 1 && this.parentStaff.parent_staff_id) && { ...this.parentStaff })
      }
      setMemberStatus(params).then(res => {
        if (res) {
          let msg = message ? '成功' + message : '提交成功'
          this.$message.success(msg)
        } else {
          this.$message(message + '失败')
        }
        this.getCourseDetail()
      })
    },
    // 取消报名
    handlerCancelApply(type, message) {
      this.$confirm('是否确认注销报名？', '注销报名', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.handlerApply(type, message)
      })
    },
    // 二维码
    codeTopHead() {
      const params = {
        scene: `${this.activity_id}`,
        page: 'pages/activity/index',
        env_version: process.env.NODE_ENV === 'production' ? 'release' : 'trial'
      }
      getMobileQrcode(params).then((res) => {
        this.qrUrl = `data:image/png;base64,${res}`
      })
    },
    handleLikeOrFav() {
      const params = { activity_id: this.activity_id }
      activityCheckFavorite(params).then((res) => {
        const PAndFCommonAPI = res ? activityDeleteFavorite : activityAddFavorite
        let tipsCancel = this.$langue(
          'Mooc_Common_Alert_CancelCollectSucessed',
          { defaultText: '取消收藏成功' }
        )
        let tipsSucess = this.$langue('Mooc_Common_Alert_CollectSucessed', {
          defaultText: '收藏成功'
        })
        const tip = res ? tipsCancel : tipsSucess
        this.courseData.fav_count = res
          ? this.courseData.fav_count === null ||
            this.courseData.fav_count === 0
            ? 0
            : this.courseData.fav_count - 1
          : this.courseData.fav_count + 1
        this.handlerCommonInt(PAndFCommonAPI, params, tip)
      })
    },
    handlerCommonInt(PAndFCommonAPI, params, tip) {
      PAndFCommonAPI(params).then((data) => {
        this.zanAndcollect.isCollect =
          PAndFCommonAPI === activityAddFavorite ? Boolean(true) : Boolean(false)
        this.$message.success(tip)
      })
    },
    formatDateTime(date) {
      const dateTime = new Date(date)
      const formattedDate = this.$moment(dateTime).format('YYYY/MM/DD')
      const getDay = dateTime.getDay()
      const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'] // 获取星期信息
      return `${formattedDate}（${weekday[getDay]}）` // 组合日期和星期信息并返回
    },
    formatDateTimeStartEnd(start, end) {
      const dateTimeStart = new Date(start)
      const dateTimeEnd = new Date(end)
      const formattedDateStart =
        this.$moment(dateTimeStart).format('YYYY/MM/DD')
      const formattedDateEnd = this.$moment(dateTimeEnd).format('YYYY/MM/DD')
      const ltStart = this.$moment(dateTimeStart).format('HH:mm')
      const ltEnd = this.$moment(dateTimeEnd).format('HH:mm')
      const weekArr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekdayStart = weekArr[dateTimeStart.getDay()] // 获取星期信息
      const weekdayEnd = weekArr[dateTimeEnd.getDay()] // 获取星期信息
      const startDateJoin = `${formattedDateStart}(${weekdayStart}) ${ltStart}`
      const endDateJoin = `${formattedDateEnd}(${weekdayEnd}) ${ltEnd}`
      return `${startDateJoin} - ${endDateJoin}` // 组合日期和星期信息并返回
    },
    getZanAndCollectStatus() {
      const params = { activity_id: this.activity_id }
      activityCheckFavorite(params).then((res) => {
        this.zanAndcollect.isCollect = res
      })
    },
    async getCourseDetail() {
      try {
        const { share_staff_id, share_staff_name } = this.$route.query
        let params = {
          activity_id: this.activity_id,
          share_staff_id: share_staff_id || '',
          share_staff_name: share_staff_name || '',
          preview: this.$route.query.preview || false
        }
        const data = await getActivityInfo(params)
        document.title = `${data.activity_name}_Q-Learning`
        this.courseData = data
        this.courseData.course_id = data.activity_id
        this.courseData.course_name = data.activity_name
        this.courseData.course_desc = data.description
        const { activity_name, photo_url, description, activity_id, start_time, end_time, my_status } =
          this.courseData
        this.statusBtn = my_status
        const net_url = location.hostname.endsWith('.woa.com')
          ? `${process.env.VUE_APP_PORTAL_HOST_WOA}/training/activity/detail?activity_id=${activity_id}`
          : `${process.env.VUE_APP_PORTAL_HOST}/training/activity/detail?activity_id=${activity_id}`
        this.addCourseDialogData = {
          ...this.addCourseDialogData,
          content_name: activity_name,
          cover_img_url: photo_url,
          description: description,
          href: net_url,
          item_id: this.activity_id,
          origin: location.origin
        }
        this.courseData.start_end_time = this.formatDateTimeStartEnd(start_time, end_time)
        console.log(this.courseData, 'courseData')
        this.initData()
      } catch (err) {
        if (err.code === 403 || err.code === 500) {
          sessionStorage.setItem('401Msg', err.message)
          this.$router.replace({
            name: '401'
          })
          if (this.isFormMooc) {
            MoocJs.sendErrorInfo(err.message)
          }
        }
      }
    },
    // 获取用户是不是特殊人员
    getlabelSpecialInfo() {
      getlabelSpecialUsers().then((res) => {
        this.specialUsers = res
      })
    },
    input(val) {
      this.courseData.labels = val
      setTimeout(() => {
        this.getCourseDetail()
        this.$refs.labelShow && this.$refs.labelShow.getLabelList()
      }, 500)
    },
    // 创建笔记
    createNote() {
      const { href } = this.$router.resolve({
        name: 'create',
        query: {
          from: 'ql',
          id: this.$route.query.activity_id,
          name: this.courseData.activity_name,
          type: 3,
          module_id: 4
        }
      })
      window.open(href)
    },
    handleShow() {
      this.sharedialogShow = true
      this.$nextTick(() => {
        this.$refs.shareDialog.initCode({
          url: `https://sdc.qq.com/s/JZH8Eg?scheme_type=activityDetail&activity_id=${this.activity_id}&project=0&source=ql`,
          scene: `${this.activity_id}`,
          page: 'pages/activity/index'
        })
      })
    },
    noRightCard(val) {
      this.isShowRight = val
    },
    handlerVisible(val) {
      this.cityDown = val
    }
  }
}
</script>

<style lang="less" scoped>
.face-page {
  :deep(.sdc-editor-preview) {
    width: 100%;

    .content-wrapper {
      width: 100%;
    }

    .file-count {
      padding: 0 0 8px 24px;
    }

    .editor-file-list {
      margin: 0 24px 36px 24px;
    }

    .desc,
    .editor-content {
      -webkit-user-select: none;
      -moz-user-select: none;
      -ms-user-select: none;
      user-select: none;
      word-break: break-word;

      ol,
      ul {
        padding: revert;
      }
    }

    .desc {
      margin: 0 24px 0 24px;
    }

    // .editor-content {
    //   padding: 20px 24px 32px 24px;
    // }
  }

  .contain-main {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    padding-top: 10px;

    .left {
      margin-bottom: 20px;
      .left-top {
        background-color: #fff;
        border-radius: 8px;
        padding: 24px;
        position: relative;
        display: flex;
        .top-header {
          display: flex;
          flex: 1;
          &-l {
            margin-right: 20px;
            position: relative;
            /deep/.el-image {
              width: 135px;
              height: 90px;
              img {
                border-radius: 3px;
              }
            }
            .top-header-l-tips {
              position: absolute;
              left: 4px;
              top: 4px;
              font-size: 12px;
              border-radius: 2px;
              display: inline-block;
              width: 32px;
              height: 18px;
              line-height: 18px;
              text-align: center;
              color: #fff;
              background-color: #0052d9;
            }
          }
          &-r {
            overflow: hidden;
            width: 100%;
            .face-title {
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              color: #000000;
              font-size: 18px;
              line-height: 26px;
              font-weight: bold;
              margin-bottom: 10px;
            }
            .desc-tag-box {
              display: flex;
              justify-content: space-between;
              margin-bottom: 10px;

              .tag-left,
              .tag-right {
                display: flex;
                align-items: center;
              }

              .tag-left {
                .tag-content {
                  display: flex;
                  line-height: 20px;

                  .label {
                    color: rgba(0, 0, 0, 0.4);
                    width: 50px;
                  }

                  .label-height25 {
                    height: 25px;
                    line-height: 25px;
                  }

                  .tag-list-box {
                    flex: 1;
                    display: flex;
                    flex-wrap: wrap;
                  }
                }
              }

              .tag-right {
                font-size: 12px;
                color: rgba(0, 0, 0, 0.4);
                flex-shrink: 0;
                border-radius: 24px;
                background: #f5f5f7;
                height: 24px;
                line-height: 24px;
                cursor: pointer;
                position: relative;
                width: 88px;
                .project-add-label {
                  position: relative;
                  :deep(.cascader-component) {
                    .el-button {
                      span {
                        width: 88px;
                        font-size: 12px;
                        margin-left: 10px;
                        display: inline-block;
                        color: rgba(0, 0, 0, 0.4) !important;
                      }
                    }
                  }
                }
                .project-tag-box {
                  position: relative;
                  :deep(.cascader-component) {
                    .marker-tag {
                      margin-left: 36px;
                    }
                  }
                }
                i {
                  display: inline-block;
                  width: 16px;
                  height: 16px;
                  position: absolute;
                  left: 16px;
                }

                .icon-addlabel {
                  background: url('~@/assets/img/addlabel.png') no-repeat center /
                    cover;
                }
              }

              .tag-right:hover {
                background: #f2f8ff;
                color: #0052d9;
                :deep(.cascader-component) {
                  .el-button {
                    span {
                      color: #0052d9 !important;
                    }
                  }
                }
                .icon-addlabel {
                  background: url('~@/assets/img/addlabel2.png') no-repeat
                    center / cover;
                }
              }
            }

            .top-info {
              display: flex;
              align-items: center;
              color: #777777;
              // padding-bottom: 16px;
              margin-bottom: 10px;
              // border-bottom: 1px solid #EEEEEE;
              i {
                display: inline-block;
                margin-right: 4px;
                width: 16px;
                height: 16px;
              }
              .top-iconInfo {
                margin-right: 40px;
                flex-shrink: 0;
                display: flex;
                align-items: center;
                height: 20px;
                .icon-view {
                  background: url('~@/assets/img/view.png') no-repeat
                    center/cover;
                }
                .icon-scores {
                  background: url('~@/assets/img/icon-scores.png') no-repeat
                    center/cover;
                  margin-left: 16px;
                }
                .time {
                  margin-left: 16px;
                }
              }
            }
          }
        }
        .operat-right {
          width: 154px;
          height: 100%;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          justify-content: space-between;
          color: #777777;
          .operat-shore-code {
            width: 100px;
            height: 100px;
          }
          .shore-operat-link {
            text-decoration: underline;
            color: #0052d9;
            cursor: pointer;
          }
        }
        .user-operat-box {
          margin-top: 10px;
          // border-top: 1px solid #EEEEEE;
          // padding-top: 20px;
          color: #777777;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .operat-left {
            display: flex;
          }

          .item-operat {
            background-color: #f5f5f7;
            padding: 0 16px;
            margin-right: 16px;
            border-radius: 24px;
            height: 32px;
            line-height: 32px;
            display: flex;
            align-items: center;
            cursor: pointer;
            position: relative;
            i {
              display: inline-block;
              width: 16px;
              height: 16px;
              line-height: 16px;
              margin-right: 4px;
            }
            .mid-line {
              margin-left: 12px;
              margin-right: 12px;
              color: #d9d9d9;
            }
          }
          .active-isCollect-operat {
            background: #f2f8ff;
            color: #0052d9;
            .icon-collect {
              background: url('~@/assets/img/active-fav.png') no-repeat center /
                cover;
            }
          }
          .white-note-btn {
            flex-shrink: 0;
            margin-right: 0;
            i {
              width: 20px;
              height: 20px;
              background: url('~@/assets/img/white-icon.png') no-repeat center /
                cover;
            }
          }
          .shore-operat:hover {
            background: #f2f8ff;
            color: #0052d9;
            .icon-share {
              background: url('~@/assets/img/qrcode-hover.png') no-repeat center /
                cover;
            }
          }
          .add-operat:hover {
            background: #f2f8ff;
            color: #0052d9;
            .icon-add {
              background: url('~@/assets/img/active-add.png') no-repeat center /
                cover;
            }
          }
          .icon-zan-active {
            background: #f2f8ff;
            color: #0052d9;
            i {
              background: url('~@/assets/img/zan1-active.png') no-repeat center /
                cover;
            }
          }
          .white-note-btn:hover {
            background: #f2f8ff;
            color: #0052d9;
            i {
              background: url('~@/assets/img/white-icon.png') no-repeat center /
                cover;
            }
          }
          .active-appreciate-operat {
            background: #f2f8ff;
            color: #0052d9;
            i {
              background: url('~@/assets/img/do-admire-active.png') no-repeat
                center / cover;
            }
          }
          .icon-comment {
            background: url('~@/assets/img/comment.png') no-repeat center /
              cover;
          }
          .icon-zan {
            background: url('~@/assets/img/zan1.png') no-repeat center / cover;
          }
          .icon-share {
            background: url('~@/assets/img/qrcode.png') no-repeat center / cover;
          }
          .icon-collect {
            background: url('~@/assets/img/fav2.png') no-repeat center / cover;
          }
          .icon-add {
            background: url('~@/assets/img/add.png') no-repeat center / cover;
          }
          .more-btn {
            background: url('~@/assets/img/more-line.png') no-repeat center /
              cover;
          }
          .icon-appreciate {
            background: url('~@/assets/img/icon_re.png') no-repeat center /
              cover;
          }
          .jf-tip {
            color: #ff7548;
            position: absolute;
            right: 20px;
            top: -22px;
            display: flex;
            align-items: center;
          }
          .jf-icon {
            background: url('~@/assets/img/integral-icon.png') no-repeat center /
              cover;
            display: block;
            width: 20px;
            height: 20px;
            margin-right: 4px;
          }
        }
        .left-top-flex {
          width: 100%;
          display: flex;
          justify-content: space-between;
          .left-top-flex-left {
            flex: 1;
          }
          .left-top-flex-right {
            width: 154px;
          }
        }
      }

      .tabs-content {
        margin-top: 20px;
        position: relative;
        :deep(.el-tabs) {
          background-color: #fff;
          padding: 16px 24px 0 24px;
          border-radius: 8px 8px 0 0;

          .el-tabs__header {
            border-bottom: solid 1px #eeeeee;
            margin: 0px;
          }

          .el-tabs__item {
            color: rgba(0, 0, 0, 0.4);
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 10px;
          }

          .is-active {
            color: #0052d9 !important;
            font-weight: 700;
          }
          .el-tabs__nav-wrap::after {
            height: 1px;
            background-color: #eeeeee;
          }
        }
        .face-city:hover {
          background: #fafafa;
        }
        .face-city {
          position: absolute;
          right: 24px;
          top: 10px;
          height: 28px;
          .face-city-cur {
            position: absolute;
            text-align: center;
            height: 28px;
            line-height: 28px;
            width: 108px;
            display: flex;
            align-items: center;
            justify-content: center;
            .icon-Local {
              background: url('~@/assets/img/Local.png') no-repeat center /
                cover;
              display: block;
              width: 16px;
              height: 16px;
              margin-right: 4px;
            }
            .down-edg {
              transform: rotate(180deg);
              transition: all 0.3s;
            }
          }
          .face-city-select {
            opacity: 0;
            /deep/ .el-input__inner {
              width: 108px;
              text-align: right;
              border: none;
            }
          }
          /deep/.popper-city {
            width: auto;
          }
        }
      }
    }
  }
  .class-content {
    max-height: 429px;
    overflow: hidden;
    &-item {
      padding: 16px 24px;
      background-color: #fff;
      margin-top: 20px;
      border-radius: 8px;
      .item-title {
        color: #333333;
        font-family: 'PingFang SC';
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
      }
      .item-tips {
        margin: 10px 0;
        span {
          padding: 2px 6px;
          font-size: 12px;
          line-height: 18px;
          color: #777;
          margin-right: 8px;
          border-radius: 4px;
          background: #f5f7fa;
        }
      }

      .item-class-info {
        padding: 10px 18px 10px 12px;
        border-radius: 8px;
        background: #f8f8f8;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &_left {
          .info-lable {
            display: flex;
            align-items: center;
            line-height: 22px;
            font-size: 14px;
            margin-bottom: 10px;
            align-items: flex-start;
            padding-top: 2px;
            .icon {
              display: block;
              width: 24px;
              height: 24px;
              margin-right: 12px;
              position: relative;
              top: -2px;
            }
            .icon-time {
              background: url('~@/assets/img/time-class.png') no-repeat center /
                cover;
            }
            .icon-name {
              background: url('~@/assets/img/teacher-class.png') no-repeat
                center / cover;
            }
            .icon-city {
              background: url('~@/assets/img/local-class.png') no-repeat center /
                cover;
            }
          }
          .info-lable:last-child {
            margin-bottom: 0;
          }
        }
        &_right {
          .status-button {
            width: 140px;
            height: 72px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            cursor: pointer;
            .button-text {
              font-size: 14px;
              font-style: normal;
              font-weight: 500;
              line-height: 22px;
              margin-bottom: 4px;
            }
            .button-info {
              font-family: 'PingFang SC';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 16px;
            }
          }
          .status1 {
            background-color: #0052d9;
            color: #fff;
          }
          .status2 {
            color: #333333;
            background-color: #fff;
            border-color: #fff;
            .button-info {
              color: #777777;
            }
          }
          .status3 {
            color: #bcbec2;
            background-color: #c8c9cc;
            border-color: #c8c9cc;
            cursor: not-allowed;
          }
          .status4 {
            color: #fff;
            background-color: #f56c6c;
          }
          .registered-btn {
            color: #fff;
            background-color: #c8c9cc;
            border-color: #c8c9cc;
            cursor: not-allowed;
          }
        }
      }
      .item-class-info:hover {
        background: linear-gradient(90deg, #f9f9f9 0%, #f1f5fd 97.29%);
      }
    }
    &-item:last-child {
      // border-bottom: none;
    }
  }
}
@media screen and (max-width: 1660px) {
  .contain-main .left {
    width: 760px;
    .tag-list-box {
      width: 400px;
    }
    .video-box,
    .scorm-box {
      width: 711px;
      height: 401px;
    }
  }
  .article-fullen-main .left {
    width: 1180px;
  }
  .face-title {
    max-width: 380px;
  }
}

@media screen and (min-width: 1661px) {
  .contain-main .left {
    width: 1000px;
    .tag-list-box {
      width: 600px;
    }
    .video-box,
    .scorm-box {
      width: 951px;
      height: 536px;
    }
    .chapter-preview-box {
      width: 952px;
    }
  }
  .article-fullen-main .left {
    width: 1420px;
  }
  .face-title {
    max-width: 620px;
  }
}
</style>
