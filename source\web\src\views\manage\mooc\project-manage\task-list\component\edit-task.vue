<template>
  <div class="outlink-dialog">
    <el-dialog 
    width="800px" 
    :visible="visible" 
    :title="title" 
    :close-on-click-modal="false" 
    :before-close="cancel"
    >
      <div class="content-body">
        <el-form ref="form" :model="form" label-width="120px" :rules="rules">
          <el-form-item class="input-style" label="任务名称" prop="task_name">
            <el-input v-model.trim="form.task_name" placeholder="请输入任务名称" clearable></el-input>
            <span class="custom-el-input-count">{{handleValidor(form.task_name, 50)}}/50</span>
          </el-form-item>
          <el-form-item :label="['Exam'].includes(form.resource_type) || form.act_type * 1 === 32 ? '已选问卷' : (form.resource_type == 'Practice' ? '已选练习' : '已选课程')" prop="act_name">
            <div class="flex-row sp-bt">
              <div>
                <el-tag v-if="form.resource_type" size="small" class="tag">{{ form.resource_type_name }}</el-tag>
                <span>{{ form.act_name }}</span>
              </div>
              <el-button 
              type="primary" 
              size="mini"
              @click="changeList"
              v-if="form.act_type * 1 !== 32"
              >
              {{ `更换${form.act_type * 1 === 21  ? '素材' : form.resource_type === 'Exam' ? '考试' : (form.resource_type == 'Practice' ? '练习' : '课程')}` }}
              </el-button>

              <el-button 
              type="primary" 
              size="mini"
              @click="editQuestionnair"
              v-else
              >
              编辑问卷
              </el-button>
            </div>
          </el-form-item>

          <el-form-item label="任务性质">
            <el-radio-group v-model="form.required">
              <el-radio :label="true">应学</el-radio>
              <el-radio :label="false">选学</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- 1 解锁任务  2 锁定任务 -->
          <el-form-item label="任务状态">
            <el-radio-group v-model="form.lock_status">
              <el-radio :label="1">解锁任务</el-radio>
              <el-radio :label="2">锁定任务</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="完成条件" v-if="form.act_type * 1 !== 32 && !['Exam', 'Practice'].includes(form.resource_type)">
            <el-radio-group v-model="form.finished_condition.type">
              <el-radio v-if="['Video', 'Audio', 'Scorm'].includes(form.resource_type) && form.act_type * 1 !== 21" label="1">由课程完成条件决定</el-radio>
              <el-radio v-else-if="form.resource_type==='Scorm' && form.act_type * 1 === 21" label="1">由素材完成条件决定</el-radio>
              <el-radio label="2">至少学习</el-radio>
            </el-radio-group>
            <el-input-number :disabled="form.finished_condition.type !== '2'"  v-model="form.finished_condition.condition" controls-position="right" :min="0" class="condition">
            </el-input-number>分钟
            <div v-if="form.finished_condition.type === '2' && form.duration && form.finished_condition.condition > form.duration" class="video-durtion-tips">{{`${form.resource_type_name}时长${form.duration}分钟，学习时长必须不大于视频时长`}}</div>
          </el-form-item>
          <!-- <el-form-item label="解锁时间">
            <el-date-picker v-model="form.unlock_time" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" placeholder="请选择任务解锁时间">
            </el-date-picker>
          </el-form-item> -->
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取 消</el-button> 
        <el-button @click="submit" size="small" type="primary">保 存</el-button>
      </div>
    </el-dialog>

    <!-- 添加课程 -->
    <AddCourseDialog 
    v-if="addLineCourseShow" 
    ref="addLineCourse" 
    @changeSingleData="changeSingleData"
    :visible.sync="addLineCourseShow"
    entryType="change"
    />
    <!-- 添加考试 -->
    <AddExamDialog 
    ref="addExamDialog" 
    v-if="addExamDialogShow" 
    @changeSingleData="changeSingleData"
    :visible.sync="addExamDialogShow"
    entryType="change"
    />
    <!-- 添加练习 -->
    <AddPracticeDialog 
    v-if="addPracticeShow" 
    ref="addPracticeDialog" 
    @changeSingleData="changeSingleData"
    :visible.sync="addPracticeShow"
    entryType="change"
    />
    <!-- 添加课程素材 -->
    <AddMaterialDialog 
    v-if="addMaterialShow" 
    ref="addMaterial" 
    :visible.sync="addMaterialShow"
    @changeSingleData="changeSingleData"
    entryType="change"
    />
    <!-- 名字重复错误提示 -->
    <addErrorDialog :visible.sync="addErrorDialogShow" :taskNameList="taskNameList"></addErrorDialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { AddCourseDialog, AddExamDialog, AddPracticeDialog, AddMaterialDialog } from './index'
import addErrorDialog from './add-error-dialog.vue'
export default {
  components: {
    AddCourseDialog,
    AddExamDialog,
    AddPracticeDialog,
    AddMaterialDialog,
    addErrorDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentNode: {
      type: Object
    },
    treeNode: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      form: {
        task_name: '',
        resource_url: '',
        resource_url_mobile: '',
        task_desc: '',
        finished_condition: {
          type: '1',
          condition: null
        },
        required: true,
        lock_status: 1
      },
      addLineCourseShow: false,
      addExamDialogShow: false,
      addPracticeShow: false,
      addMaterialShow: false,
      addErrorDialogShow: false,
      taskNameList: [],
      rules: {
        task_name: [{ required: true, message: '请输入任务名称', trigger: 'blur' }],
        act_name: [{ required: true, message: '请选择内容', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapState(['projectManageInfo']),
    title() {
      if (this.form.act_type * 1 === 32) return '编辑问卷'
      return this.form.resource_type === 'Exam' ? '编辑考试' : this.form.resource_type === 'Practice' ? '编辑练习' : this.form.act_type * 1 === 21 ? '编辑课程素材' : '编辑线上课程'
    }
  },
  created() {
    // if (this.scene === 'edit') {
    //   this.form.task_name = this.currentNode.task_name
    // }
    // console.log(this.currentNode)
  },
  mounted() {
    window.questionReEdit = (data) => {
      this.form.act_name = data.act_name
    }
  },
  methods: {
    // 初始化数据
    initData(data) {
      const { 
        lock_status, 
        finished_condition
      } = data
      this.form = {
        ...data,
        lock_status: lock_status > 1 ? 2 : 1,
        finished_condition: {
          type: finished_condition?.type ? finished_condition.type : '1',
          condition: finished_condition?.condition ? finished_condition.condition : null
        }
      }
    },
    submit() {
      // this.taskNameList = []
      // let newTaskList = []
      // if (this.currentNode?.id) { // 当前阶段
      //   newTaskList = (this.currentNode?.sub_tasks || []).filter((e) => e.task_type === 'task')
      // } else { // 当前任务树
      //   newTaskList = JSON.parse(JSON.stringify(this.treeNode)) 
      // }
      // newTaskList.forEach((v) => {
      //   if (v.task_name === this.form.task_name && this.currentNode.id !== v.id) {
      //     this.taskNameList.push({
      //       name: v.task_name
      //     })
      //   }
      // })
      // if (this.taskNameList?.length) {
      //   this.addErrorDialogShow = true
      //   return
      // }
      if (this.form.duration && this.form.duration < this.form.finished_condition.condition) {
        return
      }
      // 学习时长置空
      if (this.form.finished_condition.type === '1') {
        this.form.finished_condition.condition = null
      }
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const list = [{
            ...this.form,
            id: this.currentNode?.id || '',
            content_name: this.form.task_name, // 在线课程
            exam_name: this.form.task_name, // 考试--练习
            act_name: this.form.act_name
          }]
          this.$emit('updateTreeList', list, 'edit')
          this.$emit('update:visible', false)
        } else {
          return false
        }
      })
    },
    cancel() {
      this.$refs['form'].resetFields()
      this.$emit('update:visible', false)
    },
    changeList() {
      const { resource_type, act_type } = this.form
      if (resource_type === 'Exam') {
        this.addExamDialogShow = true
      } else if (resource_type === 'Practice') {
        this.addPracticeShow = true
      } else if (act_type * 1 === 21) {
        this.addMaterialShow = true
      } else {
        this.addLineCourseShow = true
      }
    },
    editQuestionnair () {
      // this.$emit('continueCreateQuestionnaire', { type: 'edit', cutmonId: this.currentNode?.id || '' })
      this.$emit('openQuestionConfirm', 'edit')
    },
    changeSingleData(row) {
      const { exam_name, task_name, resource_type_name, resource_type } = row
      this.form = {
        ...row,
        task_name: this.form.task_name,
        act_name: ['Practice', 'Exam'].includes(resource_type) ? exam_name : task_name,
        lock_status: 1,
        resource_type,
        resource_type_name
      }
      if (row.act_type * 1 === 21) {
        this.form.finished_condition = {
          type: '2',
          condition: 1
        }
      } else {
        this.form.finished_condition = {
          type: '1',
          condition: null
        }
      }
    },
    handleValidor(value, num) {
      if (!value) return 0
      const china = value.match(/[\u4e00-\u9fa5]/g)
      const zhCount = china && china.join('').length
      const enCount = Math.ceil((value.length - zhCount) / 2)
      const total = zhCount + enCount
      if (total > num) {
        this.form.task_name = value.slice(0, -1)
      }
      return zhCount + enCount 
    }
  }
}
</script>
<style lang="less" scoped>
.outlink-dialog {
  .content-body {
    padding-right: 40px;
    .flex-row {
      display: flex;
      align-items: flex-start;
      line-height: 32px;
      .tag {
        text-align: center;
        margin-right: 10px;
        height: 20px;
        line-height: 20px;
        color: #0052D9;
        background-color: #EAEFFC;
        border: unset;
      }
      .el-button {
        margin-left: 10px;
        margin-top: 5px;
      }
    }
    .input-style {
      position: relative;
      :deep(.el-form-item__content) {
        width: 496px;
      }
      :deep(.el-input) {
        .el-input__inner {
          padding-right: 70px;
        }
        .el-input__suffix {
          position: absolute;
          right: 43px;
        }
      }
    }
    .custom-el-input-count {
      color: #ACACAC;
      background: #FFF;
      position: absolute;
      font-size: 12px;
      bottom: 6px;
      right: 6px;
      line-height: 20px;
    }
    .sp-bt {
      justify-content: space-between;
      margin-bottom: 0px !important;
    }
    .flex-row:first-child {
      margin-bottom: 20px;
    }

    .is-duration {
      width: 80px;
      margin-left: 24px;
    }
    .condition {
      width: 130px;
      margin: 0 10px 0 10px;
    }

    :deep(.is-controls-right) {
      width: 130px;
      height: 32px !important;
      line-height: 32px;

      .el-input-number__decrease,
      .el-input-number__increase {
        line-height: 16px !important;
      }
    }

    :deep(.el-date-editor .el-input__inner) {
      padding-left: 30px;
    }

    .test-link {
      width: 100px;
    }
  }
  .video-durtion-tips {
    color: red;
    font-size: 10px;
  }
}
</style>
