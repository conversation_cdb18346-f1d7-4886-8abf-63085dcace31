<template>
  <div class="add-banner-dialog">
    <el-dialog 
      title="" 
      :visible.sync="visible"
      width="900px" 
      :close-on-click-modal="false"
      :before-close="closeDialog"
      >
        <div class="add-banner-body">
            <div class="search-box">

              <el-form label-width="90px" :model="form2" :rules="formRules">
                <el-form-item label="上传时间" prop="upload_time">
                  <el-date-picker
                    class="w-360"
                    v-model="form2.upload_time"
                    type="datetimerange"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    range-separator="-"
                    start-placeholder="开始日期时间"
                    end-placeholder="结束日期时间">
                  </el-date-picker>

                </el-form-item>
              </el-form>

              <searchBox v-model="searchData" @chooseChange="chooseChange"  @onSearch="onSearch(1)" @onReset="handleReset(true)"></searchBox>

              <div class="search-table">
                <el-table
                  ref="multipleTable"
                  v-loading="tableLoading"
                  :data="qlsearchContentTable"
                  :header-cell-style="{background:'#eef1f6'}"
                  class="content-table qlcontent-table"
                  @selection-change="qlsearchSelectionChange"
                  height="280px"
                  >
                  <el-table-column
                    :selectable="isCanSelect"
                    type="selection"
                    align="left"
                    width="55">
                  </el-table-column>
                  <el-table-column align="left" label="内容标题" prop="content_name" show-overflow-tooltip></el-table-column>
                  <el-table-column align="left" label="内容类型" prop="module_name" width="120"></el-table-column>
                  <el-table-column align="left" label="认证等级" prop="module_name" width="120">
                    <template v-slot="prop">
                      <span>{{ prop.row.course_level ? courseLevelLabel[prop.row.course_level * 1] : '-' }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="内容标签" prop="labelsText" show-overflow-tooltip ></el-table-column>
                  <el-table-column align="left" label="是否添加" prop="isSelect" show-overflow-tooltip width="90">
                    <template v-slot="prop">
                      <span>{{ prop.row.isSelect && prop.row.selectType === 'normal' ? '已添加' : '-' }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column align="left" label="内容链接" prop="content_url" show-overflow-tooltip>
                    <template v-slot="prop">
                      <a class="content-url" target="_blank" :href="prop.row.content_url">{{ prop.row.content_url }}</a>
                    </template>
                  </el-table-column>
                </el-table>
                <div class="table-pagination">
                  <el-pagination
                    :hide-on-single-page="qlsearchContentTable.length === 0"
                    @current-change="onSearch"
                    :current-page="searchData.pageNum"
                    :page-size="searchData.pageSize"
                    layout="total, prev, pager, next, jumper"
                    :total="searchData.total"
                  >
                  </el-pagination>
                </div>
              </div>
            </div>

        </div>
        <div slot="footer" class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="handleAddRecommendCourse">添加</el-button>
        </div>
      </el-dialog>
  </div>
</template>
<script>
import searchBox from '../../bannersList/detailPage/contentSearchForm.vue'
import { searchsiteApi, checkRecommendCourse } from '@/config/mooc.api.conf'
export default {
  components: { searchBox },
  watch: {
    visible (val) {
      if (val) {
        this.onSearch(1)
      }
    }
  },
  data () {
    return {
      // 用于搜索的
      form2: {
        upload_time: '' // 上传时间
      },
      searchData: {
        // pageId: '565',
        // pageConfigId: 4913,
        keywords: '',
        startTime: '',
        endTime: '',
        labels: [],
        classify: [],
        sortBy: 'created_at',
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      courseLevelLabel: {
        0: '-',
        1: '公司级',
        2: 'BG级',
        3: '部门级',
        4: '个人分享'
      },
      course_list: [],
      qlsearchContentTable: [],
      qlsearchContentSelect: [], // 暂时选中的
      addedCourseList: [], // 已添加的课程
      tableLoading: false,
      formRules: {
        // upload_time: [
        //   {
        //     required: true,
        //     message: '请选择上传时间',
        //   }
        // ]
      }
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    simpleTableData: {
      type: Array,
      default () {
        return []
      }
    }
  },
  methods: {
    isCanSelect(row) {
      return !row.isSelect
    },
    chooseChange() {
      this.onSearch(1)
    },
    // 检查课程是否已经推荐过
    handleAddRecommendCourse() {
      let courseList = []
      this.qlsearchContentSelect.forEach(item => {
        if (!item.isSelect) {
          courseList.push({
            course_name: item.content_name,
            recommend_item_id: item.item_id,
            recommend_module_id: item.module_id,
            recommend_act_type: item.act_type || '',
            course_url: item.content_url,
            course_publish_time: item.created_at,
            course_level: item.course_level || ''
          })
        }
      })
      checkRecommendCourse(courseList).then(res => {
        res.forEach(item => {
          if (item.add) {
            this.addedCourseList.push(item)
          }
        })

        if (this.addedCourseList.length > 0) {
          let addedCourse_name = this.addedCourseList.map(item => item.course_name).join('，')
          this.$message({
            type: 'warning',
            message: `以下课程：${addedCourse_name} 在90天内已经推荐过，请重新选择`
          })
          this.banSelectCourse()
          this.addedCourseList = []
        } else {
          this.addedCourseList = this.qlsearchContentSelect.filter(item => !item.isSelect)

          if (this.addedCourseList.length === 0) {
            this.$message.warning('请选择要推荐的课程')
            return
          }
          
          this.$emit('added', this.addedCourseList)
        }
      }).catch(() => {
        this.$message.error('查询是否已经添加课程失败，请稍后重试')
      })
    },
    closeDialog() {
      this.qlsearchContentTable = []
      this.qlsearchContentSelect = []
      this.handleReset()
      this.$emit('update:visible', false)
    },
    banSelectCourse (type) {
      this.qlsearchContentTable.forEach(item => {
        this.addedCourseList.forEach(item2 => {
          let item_id1 = item.item_id
          let item_module1 = item.module_id * 1
          let item_id2 = item2.recommend_item_id || item2.item_id
          let item_module2 = (item2.recommend_module_id * 1) || (item2.module_id * 1)
          if ((item_id1 === item_id2) && (item_module1 === item_module2)) {
            this.$refs.multipleTable.toggleRowSelection(item, true)
            if (type !== 'normal' && (!item.selectType || item.selectType !== 'normal')) {
              item.selectType = 'other'
            } else {
              item.selectType = 'normal'
            }
            item.isSelect = true
          }
        })
      })
      this.addedCourseList = []

      // this.qlsearchContentSelect.forEach(item => {
      //   this.addedCourseList.forEach(item2 => {
      //     if ((item.item_id === item2.recommend_item_id) && (item.module_id === item2.recommend_module_id)) {
      //       this.$refs.multipleTable.toggleRowSelection(item, true)
      //       if (type !== 'normal' && (!item.selectType || item.selectType !== 'normal')) {
      //         item.selectType = 'other'
      //       } else {
      //         item.selectType = 'normal'
      //       }
      //       item.isSelect = true
      //     }
      //   })
      // })
      this.qlsearchContentTable = JSON.parse(JSON.stringify(this.qlsearchContentTable))
    },
    onSearch(num) {
      this.searchData.pageNum = num
      this.qlContentSearch()
    },
    handleReset(refresh = false) {
      this.searchData = {
        keywords: '',
        startTime: '',
        endTime: '',
        labels: [],
        classify: [],
        sortBy: 'created_at',
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
      this.form2.upload_time = ''
      this.qlsearchContentTable = []
      refresh && this.onSearch(1)
    },
    qlsearchSelectionChange(e) {
      this.qlsearchContentSelect = e
      console.log(this.qlsearchContentSelect, '暂时选中')
    },
    qlContentSearch () {
      if (this.form2.upload_time && this.form2.upload_time.length > 0) {
        this.searchData.startTime = this.form2.upload_time[0]
        this.searchData.endTime = this.form2.upload_time[1]
      } else {
        this.searchData.startTime = ''
        this.searchData.endTime = ''
      }
      let data = { ...this.searchData }
      if (data.moduleId === 0) {
        delete data.moduleId
      }
      data.filter = 'created_at'
      delete data.total
      this.tableLoading = true
      searchsiteApi(data).then(res => {
        res.list.map(v => {
          if (v.labels) {
            v.labelsText = v.labels.join(';')
          }
        })
        this.qlsearchContentTable = res.list
        this.$nextTick(() => {
          const recommendMap = {}

          // 遍历res2来填充哈希表
          this.simpleTableData.forEach(item2 => {
            const key = `${item2.recommend_item_id}-${item2.recommend_module_id}`
            if (!recommendMap[key]) {
              recommendMap[key] = true
            }
          })

          // 现在，遍历qlsearchContentTable，并使用哈希表进行快速查找
          this.qlsearchContentTable.forEach(item => {
            const key = `${item.item_id}-${Number(item.module_id)}`
            if (recommendMap[key]) {
              this.$refs.multipleTable.toggleRowSelection(item, true)
              item.isSelect = true
              item.selectType = 'normal'
            } else {
              item.isSelect = false
            }
          })
          this.$refs.multipleTable.doLayout()
          this.tableLoading = false
        })

        this.searchData.total = res.total
      })
    }
  }
}
</script>

<style lang="less" scoped>
.add-banner-dialog {
  :deep(.el-dialog) {
    .el-dialog__header {
      border-bottom: 0;
    }
    .el-dialog__body {
      padding: 0;
      .el-dialog__footer {
        padding: 8px 0 24px;
      }
    }
  }
  .add-banner-body {
    padding: 10px 32px 0;
    .search-box {
      border-radius: 4px;
      background: #FAFAFA;
      padding: 20px 16px 16px 0;
      .j-end {
        display: flex;
        justify-content: flex-end;
        & /deep/ .el-button {
          min-width: 80px;
        }
      }
    }
    .select-course {
      padding: 0 16px 16px;
      background: #FAFAFA;
      .content-url {
        color: #3464E0;
      }
      .el-table /deep/ th.is-leaf {
        background-color: #EEEEEE !important;
      }
    }
  }
  .dialog-footer {
    margin: 20px 0 0 0;
  }
  .table-pagination {
    margin: 20px 0 0 0;
    display: flex;
    justify-content: space-between;
  }
  .table-pagination /deep/ .el-pagination {
    margin-top: 0px;
    margin-left: auto;
  }
  .reset-btn:hover {
    border-color: #3464E0;
    background-color: #F5F7F9;
    color: #3464E0;
  }
  .date-box {
    .el-date-editor /deep/ .el-range-input {
      width: 40%;
    }
    .el-date-editor /deep/ .el-range-separator {
      width: 8%;
    }
  }

  .w-256 {
    width: 256px;
  }
  .w-360 {
    width: 360px;
  }
  .mt-8 {
    margin-top: 8px;
  }
  .mr-12 {
    margin-right: 12px;
  }
  .color-gray {
    color: #00000099;
  }
}
.pub-p-r48 {
  :deep(.el-input__inner) {
    padding-right: 48px;
  }
}
</style>
