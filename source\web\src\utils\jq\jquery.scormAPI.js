﻿/* eslint-disable*/
/*
*
*version 1.0
*by tassa<PERSON><PERSON><PERSON> @ 11-8-3
*
*
*按照scorm标准，LMS要和SCO课件交互，需要实现一个API对象，并提供相应的接口。
*本插件旨在简化操作（不同版本，如1.2和2004标准，API的签名都完全不一样），让LMS可以相对统一和简单的对定义API
*
*先暂时只支持SCORM1.2标准和SCORM2004标准，用version参数“1.2”和“2004”来标识
*
*还没有完全的测试。。。。。。。。。
*
/////////////////////////////////////////////////////////
*
*edit by tassadarliu @ 11-8-15
*改善编码方式，使之更适合jquery插件的规范
*添加成员变量scormValues，给setValue和getValue添加较为普及的预制操作，编写程序的时候只需要在commit中做持久层操作即可(还可能需要在initialize中初始数据)
*错误处理——待改善
*
*
/////////////////////////////////////////////////////////
*
*
*edit by tassadarliu @ 11-8-16
*抛弃scorm2004标准，干脆只支持scorm1.2，处理部分error信息
*
*
/////////////////////////////////////////////////////////
*
*
*eidt by tassadarliu @ 11-8-17
*改写initialize，从ReLoad Project中直接copy了两个函数，what a shame...
*研究了ReloadAPIAdaptor.js以后，决定暂时不把错误处理和datamodel处理的那么细，暂时只做粗犷的处理
*再次重构，让所有外部可替换的函数具有继承的效果，而且只暴露initialize finish commits三个函数
*
*如果需要再次精化，使引擎更完美，还要进一步改写setValue和getValue
*
/////////////////////////////////////////////////////////
*
*
*edit by tassadarliu @ 11-8-23
*提供一对setter和getter把scormValues用一个string来输入输出，这部分可能会有些隐藏的bug
*ps：确实存在bug，对separator的选取几乎是不可能的
*
*
*
*
////////////////////////////////////////////////////////
*
*edit by tassadarliu @ 11-8-31
*换了一种将array扁平化的方式输出scormValueString，输入的时候则直接用到了eval，bug情况啥尚未明了
*错误处理很弱，只有默认为sco课件都不太依赖与error handler了
*其实scormValues，也就是cmi datamodel应该是一个复杂的数据结构，用array的方式虽然减少了冗余，但是还是存在错误处理等方面的问题
*
*
*
///////////////////////////////////////////////////////
*
*
*version2.0
*by tassadarliu@ 11-9-2
*用一个庞大的json对象来实现cmi datamodel，开始实现setvalue和getvalue
*把scormValue的接口都改成了scormModel更为准确
*
*
*
///////////////////////////////////////////////////////
*
*
*edit by tassadarliu@ 11-9-5
*字符串中含有"或'会很麻烦，所以尽量不用eval
*
*
//////////////////////////////////////////////////////
*
*
*
*edit by tassadarliu@ 11-9-7
*没有找到适合的重构方式，虽然有单元测试，可靠性有一定保障，但代码可读性较差
*
*
*
*
/////////////////////////////////////////////////////
*
*edit by tassadarliu@ 11-9-8
*还是不放心eval，虽然用eval来实现LMSGetValue会很爽，还是用一些列if判断来做，虽然看起来繁琐了，但至少效率没问题，安全有保障
*
*
*
*
*
*
*
*
*/
/// <reference path="json2.js" />
/// <reference path="jquery-1.4.1-vsdoc.js" />
import jQuery from './jquery.min.js'

(function($) {
    //const field
    var CMISTRING4096LENGTH = 4096;
    var CMISTRING255LENGTH = 255;
    var LESSONSTATUS = "passed,completed,failed,incomplete,browsed,not attempted";
    var EXIT = "time-out,suspend,logout";
    var SPACE = " ";
    var DATAMODELVERSION = "3.4";
    var INTERACTIONTYPE = "true-false,choice,fill-in,matching,performance,sequencing,likert,numeric";
    //warning: this regex is not perfect yet
    var INTERACTIONCHOICE = /\w(,\w)*$/;
    var INTERACTIONMATCHING = /\w.\w(,\w.\w)*$/;
    var INTERACTIONRESULT = "correct,wrong,unanticipated,neutral";

    //private field
    var idModel = {
        "id": ""
    };
    var patternModel = {
        "pattern": ""
    };
    var objectiveModel = {
        "id": "",
        "score": {
            "_children": "raw,min,max",
            "raw": "",
            "min": "",
            "max": ""
        },
        "status": ""
    };
    var interactionModel = {
        "id": "",
        "objectives": {
            "_count": "0",
            "idModelArray": []
        },
        "time": "",
        "type": "",
        "correct_responses": {
            "_count": "0",
            "patternArray": []
        },
        "weighting": "",
        "student_response": "",
        "result": "",
        "latency": ""
    };

    //warning: cmi.core.credit is not certain now
    //warning: cmi.student_data.* are needed to be init from manifest
    //warning: cmi.launch_data are needed to be init from manifest
    //warning: we pretend to be able to controll the audio,speed,language,text
    //warning: cant understand what "Value is controlled by the SCO" is   
    //THIS IS THE MOST IMPORTANT PART OF THE LMS RTM
    var cmiModel = { "cmi": {
        "suspend_data": "",
        "launch_data": "",
        "comments": "",
        "comments_from_lms": "",
        "core": {
            "_children": "student_id,student_name,lesson_location,credit,lesson_status,entry,score,total_time,lesson_mode,exit,session_time",
            "student_id": "",
            "student_name": "",
            "lesson_location": "",
            "credit": "credit",
            "lesson_status": "not attempted",
            "entry": "ab-initio",
            "score": {
                "_children": "raw,min,max",
                "raw": "",
                "min": "",
                "max": ""
            },
            "total_time": "0000:00:00.00",
            "lesson_mode": "normal",
            "exit": "",
            "session_time": ""
        },
        "objectives": {
            "_children": "id,score,status",
            "_count": "0",
            "objectiveModelArray": []
        },
        "student_data": {
            "_children": "mastery_score, max_time_allowed, time_limit_action",
            "mastery_score": "",
            "max_time_allowed": "",
            "time_limit_action": ""
        },
        "student_preference": {
            "_children": "audio,language,speed,text",
            "audio": "",
            "language": "",
            "speed": "",
            "text": ""
        },
        "interactions": {
            "_children": "id,objectives,time,type,correct_responses,weighting,student_response,result,latency",
            "_count": "0",
            "interactionModelArray": []
        }
    }
    };
    var lastError = "0";
    var isInitialize = false;

    //private method
    //warning: assume that the two input time has no problems
    function _addTimeSpan(time1, time2) {
        var time1Array = time1.toString().split(":");
        var time2Array = time2.toString().split(":");

        //bad format
        if (time1Array.length != 3 || time2Array.length != 3)
            return "0:0:0";

        var second = Number(time1Array[2]) + Number(time2Array[2]);
        second = second.toFixed(2);
        var carry = 0;
        if (second >= 60) {
            second -= 60;
            carry = 1;
        }

        var minute = Number(time1Array[1]) + Number(time2Array[1]) + carry;
        if (minute >= 60) {
            minute -= 60;
            carry = 1;
        }
        else
            carry = 0;

        var hour = Number(time1Array[0]) + Number(time2Array[0]) + carry;

        return hour.toString() + ":" + minute.toString() + ":" + second.toString();
    }

    function _checkTime(value) {
        if (value.indexOf(":") == -1) {
            return false;
        }

        var cmiArray = value.split(":");
        if (cmiArray.length < 3) {
            return false;
        }

        if (isNaN(cmiArray[0]) || isNaN(cmiArray[1]) || isNaN(cmiArray[2])) {
            return false;
        }

        if (parseInt(cmiArray[0]) < 0 || parseInt(cmiArray[0]) > 23) {
            return false;
        }

        if (parseInt(cmiArray[1]) < 0 || parseInt(cmiArray[1]) > 59) {
            return false;
        }

        if (cmiArray[2].indexOf(".") != -1) {
            var cmiDecArray = cmiArray[2].split(".");
            if (cmiDecArray.length != 2) {
                return false;
            }
            if (isNaN(cmiDecArray[0]) || isNaN(cmiDecArray[1])) {
                return false;
            }
            if (cmiDecArray[0].length != 2) {
                return false;
            }
            if (parseInt(cmiDecArray[0]) > 59) {
                return false;
            }
            if (cmiDecArray[1].length > 2) {
                return false;
            }
        }
        else {
            if (cmiArray[2].length != 2) {
                return false;
            }
            if (parseInt(cmiArray[2]) > 59) {
                return false;
            }
        }
        return true;
    }

    //#region
    function _dealWithSetCore(element, value) {
        var elementArray = element.toString().split(".");

        //avoid undefined
        if (elementArray.length < 3) {
            lastError = "201";
            return "false";
        }
        else {
            if (elementArray[2].toString() == "score") {
                //layer3
                //avoid undefined                    
                if (elementArray.length < 4) {
                    lastError = "201";
                    return "false";
                }
                else if (elementArray[3].toString() == "_children" && elementArray.length == 4) {
                    lastError = "402";
                    return "false";
                }
                else if (elementArray[3].toString() == "raw" && elementArray.length == 4) {
                    if (isNaN(value) || value > 100 || value < 0) {
                        lastError = "405";
                        return "false";
                    }
                    else {
                        cmiModel.cmi.core.score.raw = value;
                        lastError = "0";
                        return "true";
                    }
                }
                else if (elementArray[3].toString() == "min" && elementArray.length == 4) {
                    if (isNaN(value) || value > 100 || value < 0) {
                        lastError = "405";
                        return "false";
                    }
                    else {
                        cmiModel.cmi.core.score.min = value;
                        lastError = "0";
                        return "true";
                    }
                }
                else if (elementArray[3].toString() == "max" && elementArray.length == 4) {
                    if (isNaN(value) || value > 100 || value < 0) {
                        lastError = "405";
                        return "false";
                    }
                    else {
                        cmiModel.cmi.core.score.max = value;
                        lastError = "0";
                        return "true";
                    }
                }
                else {
                    lastError = "201";
                    return "false";
                }
            }
            else if (elementArray[2].toString() == "_children" && elementArray.length == 3) {
                lastError = "402";
                return "false";
            }
            else if (elementArray[2].toString() == "student_id" && elementArray.length == 3) {
                lastError = "403";
                return "false";
            }
            else if (elementArray[2].toString() == "student_name" && elementArray.length == 3) {
                lastError = "403";
                return "false";
            }
            else if (elementArray[2].toString() == "lesson_location" && elementArray.length == 3) {
                if (value.toString().length > CMISTRING255LENGTH) {
                    lastError = "405";
                    return "false";
                }
                else {
                    cmiModel.cmi.core.lesson_location = value;
                    lastError = "0";
                    return "true";
                }
            }
            else if (elementArray[2].toString() == "credit" && elementArray.length == 3) {
                lastError = "403";
                return "false";
            }
            else if (elementArray[2].toString() == "lesson_status" && elementArray.length == 3) {
                if (LESSONSTATUS.indexOf(value.toString()) == -1) {
                    lastError = "405";
                    return "false";
                }
                else {
                    cmiModel.cmi.core.lesson_status = value;
                    lastError = "0";
                    return "true";
                }
            }
            else if (elementArray[2].toString() == "entry" && elementArray.length == 3) {
                lastError = "403";
                return "false";
            }
            else if (elementArray[2].toString() == "total_time" && elementArray.length == 3) {
                lastError = "403";
                return "false";
            }
            else if (elementArray[2].toString() == "lesson_mode" && elementArray.length == 3) {
                lastError = "403";
                return "false";
            }
            else if (elementArray[2].toString() == "exit" && elementArray.length == 3) {
                if (EXIT.indexOf(value) != -1) {
                    cmiModel.cmi.core.exit = value;
                    if (value == "suspend")
                        cmiModel.cmi.core.entry = "resume";
                    else
                        cmiModel.cmi.core.entry = "";

                    lastError = "0";
                    return "true";
                }
                else {
                    lastError = "405";
                    return "false";
                }
            }
            else if (elementArray[2].toString() == "session_time" && elementArray.length == 3) {
                if (_checkTime(value)) {
                    cmiModel.cmi.core.session_time = value;
                    lastError = "0";
                    return "true";
                }
                else {
                    lastError = "405";
                    return "false";
                }
            }
            else {
                lastError = "201";
                return "false";
            }
        }
    }

    function _dealWithGetCore(element) {
        var elementArray = element.toString().split(".");

        if (elementArray.length < 3) {
            lastError = "201";
            return "";
        }
        //layer2
        else {
            if (elementArray[2] == "_children" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.core._children;
            }
            else if (elementArray[2] == "student_id" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.core.student_id;
            }
            else if (elementArray[2] == "student_name" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.core.student_name;
            }
            else if (elementArray[2] == "lesson_location" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.core.lesson_location;
            }
            else if (elementArray[2] == "credit" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.core.credit;
            }
            else if (elementArray[2] == "lesson_status" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.core.lesson_status;
            }
            else if (elementArray[2] == "entry" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.core.entry;
            }
            else if (elementArray[2] == "score") {
                if (elementArray.length < 4) {
                    lastError = "201";
                    return "";
                }
                else {
                    if (elementArray[3] == "raw" && elementArray.length == 4) {
                        lastError = "0";
                        return cmiModel.cmi.core.score.raw;
                    }
                    else if (elementArray[3] == "_children" && elementArray.length == 4) {
                        lastError = "0";
                        return cmiModel.cmi.core.score._children;
                    }
                    else if (elementArray[3] == "min" && elementArray.length == 4) {
                        lastError = "0";
                        return cmiModel.cmi.core.score.min;
                    }
                    else if (elementArray[3] == "max" && elementArray.length == 4) {
                        lastError = "0";
                        return cmiModel.cmi.core.score.max;
                    }
                    else {
                        lastError = "201";
                        return "";
                    }
                }
            }
            else if (elementArray[2] == "total_time" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.core.total_time;
            }
            else if (elementArray[2] == "lesson_mode" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.core.lesson_mode;
            }
            else if (elementArray[2] == "exit" && elementArray.length == 3) {
                lastError = "404";
                return "";
            }
            else if (elementArray[2] == "session_time" && elementArray.length == 3) {
                lastError = "404";
                return "";
            }
            else {
                lastError = "201";
                return "";
            }
        }
    }
    //#endregion

    function _dealWithSetObjectives(element, value) {
        var elementArray = element.toString().split(".");

        //avoid undefined  
        if (elementArray.length < 3) {
            lastError = "201";
            return "false";
        }
        else {
            if (elementArray[2].toString() == "_children" && elementArray.length == 3) {
                lastError = "402";
                return "false";
            }
            else if (elementArray[2].toString() == "_count" && elementArray.length == 3) {
                lastError = "402";
                return "false";
            }
            else if (!isNaN(elementArray[2])) {
                //layer3
                //#region
                if (elementArray[3] == "id" && elementArray.length == 4) {
                    //got white space
                    if (value.toString().indexOf(SPACE) != -1) {
                        lastError = "405";
                        return "false";
                    }
                    else {
                        var n = elementArray[2];
                        var count = Number(cmiModel.cmi.objectives._count);
                        //this cant be decide by count!!
                        if (cmiModel.cmi.objectives.objectiveModelArray[n] == undefined) {
                            var newModel = { "id": "", "score": { "_children": "raw,min,max", "raw": "", "min": "", "max": "" }, "status": "" };
                            newModel.id = value;
                            cmiModel.cmi.objectives.objectiveModelArray[n] = newModel;
                            count += 1;
                            cmiModel.cmi.objectives._count = count.toString();
                        }
                        else
                            cmiModel.cmi.objectives.objectiveModelArray[n].id = value;

                        lastError = "0";
                        return "true";
                    }
                }
                else if (elementArray[3] == "score" && elementArray.length == 5) {
                    if (elementArray.length < 5) {
                        lastError = "201";
                        return "false";
                    }
                    else {
                        if (elementArray[4] == "_children" && elementArray.length == 5) {
                            lastError = "402";
                            return "false";
                        }
                        else if (elementArray[4] == "raw") {
                            if (isNaN(value) || value > 100 || value < 0) {
                                lastError = "405";
                                return "false";
                            }
                            else {
                                var n = elementArray[2];
                                var count = Number(cmiModel.cmi.objectives._count);
                                //this cant be decide by count!!
                                if (cmiModel.cmi.objectives.objectiveModelArray[n] == undefined) {
                                    var newModel = { "id": "", "score": { "_children": "raw,min,max", "raw": "", "min": "", "max": "" }, "status": "" };
                                    newModel.score.raw = value;
                                    cmiModel.cmi.objectives.objectiveModelArray[n] = newModel;
                                    count += 1;
                                    cmiModel.cmi.objectives._count = count.toString();
                                }
                                else
                                    cmiModel.cmi.objectives.objectiveModelArray[n].score.raw = value;

                                lastError = "0";
                                return "true";
                            }
                        }
                        else if (elementArray[4] == "min") {
                            if (isNaN(value) || value > 100 || value < 0) {
                                lastError = "405";
                                return "false";
                            }
                            else {
                                var n = elementArray[2];
                                var count = Number(cmiModel.cmi.objectives._count);
                                //this cant be decide by count!!
                                if (cmiModel.cmi.objectives.objectiveModelArray[n] == undefined) {
                                    var newModel = { "id": "", "score": { "_children": "raw,min,max", "raw": "", "min": "", "max": "" }, "status": "" };
                                    newModel.score.min = value;
                                    cmiModel.cmi.objectives.objectiveModelArray[n] = newModel;
                                    count += 1;
                                    cmiModel.cmi.objectives._count = count.toString();
                                }
                                else
                                    cmiModel.cmi.objectives.objectiveModelArray[n].score.min = value;

                                lastError = "0";
                                return "true";
                            }
                        }
                        else if (elementArray[4] == "max") {
                            if (isNaN(value) || value > 100 || value < 0) {
                                lastError = "405";
                                return "false";
                            }
                            else {
                                var n = elementArray[2];
                                var count = Number(cmiModel.cmi.objectives._count);
                                //this cant be decide by count!!
                                if (cmiModel.cmi.objectives.objectiveModelArray[n] == undefined) {
                                    var newModel = { "id": "", "score": { "_children": "raw,min,max", "raw": "", "min": "", "max": "" }, "status": "" };
                                    newModel.score.max = value;
                                    cmiModel.cmi.objectives.objectiveModelArray[n] = newModel;
                                    count += 1;
                                    cmiModel.cmi.objectives._count = count.toString();
                                }
                                else
                                    cmiModel.cmi.objectives.objectiveModelArray[n].score.max = value;

                                lastError = "0";
                                return "true";
                            }
                        }
                        else {
                            lastError = "201";
                            return "false";
                        }
                    }
                }
                else if (elementArray[3] == "status" && elementArray.length == 4) {
                    if (LESSONSTATUS.indexOf(value.toString()) == -1) {
                        lastError = "405";
                        return "false";
                    }
                    else {
                        var n = elementArray[2];
                        var count = Number(cmiModel.cmi.objectives._count);
                        //this cant be decide by count!!
                        if (cmiModel.cmi.objectives.objectiveModelArray[n] == undefined) {
                            var newModel = { "id": "", "score": { "_children": "raw,min,max", "raw": "", "min": "", "max": "" }, "status": "" };
                            newModel.status = value;
                            cmiModel.cmi.objectives.objectiveModelArray[n] = newModel;
                            count += 1;
                            cmiModel.cmi.objectives._count = count.toString();
                        }
                        else
                            cmiModel.cmi.objectives.objectiveModelArray[n].status = value;

                        lastError = "0";
                        return "true";
                    }
                }
                else {
                    lastError = "201";
                    return "false";
                }
                //end of layer3
                //#endregion
            }
            else {
                lastError = "201";
                return "false";
            }
        }
    }

    function _dealWithGetObjectives(element) {
        var elementArray = element.toString().split(".");

        //avoid undefined  
        if (elementArray.length < 3) {
            lastError = "201";
            return "";
        }
        else {
            if (elementArray[2] == "_children" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.objectives._children;
            }
            else if (elementArray[2] == "_count" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.objectives._count;
            }
            else if (!isNaN(elementArray[2])) {
                var index = Number(elementArray[2]);
                //general exception
                if (cmiModel.cmi.objectives.objectiveModelArray[index] == undefined) {
                    lastError = "101";
                    return "";
                }
                //avoid undefined
                else if (elementArray[3] == "id" && elementArray.length == 4) {
                    lastError = "0";
                    return cmiModel.cmi.objectives.objectiveModelArray[index].id;
                }
                else if (elementArray[3] == "status" && elementArray.length == 4) {
                    lastError = "0";
                    return cmiModel.cmi.objectives.objectiveModelArray[index].status;
                }
                else if (elementArray[3] == "score" && elementArray.length == 5) {
                    if (elementArray.length < 5) {
                        lastError = "201";
                        return "";
                    }
                    else if (elementArray[4] == "_children" && elementArray.length == 5) {
                        lastError = "0";
                        return cmiModel.cmi.objectives.objectiveModelArray[index].score._children;
                    }
                    else if (elementArray[4] == "raw") {
                        lastError = "0";
                        return cmiModel.cmi.objectives.objectiveModelArray[index].score.raw;
                    }
                    else if (elementArray[4] == "min") {
                        lastError = "0";
                        return cmiModel.cmi.objectives.objectiveModelArray[index].score.min;
                    }
                    else if (elementArray[4] == "max") {
                        lastError = "0";
                        return cmiModel.cmi.objectives.objectiveModelArray[index].score.max;
                    }
                    else {
                        lastError = "201";
                        return "";
                    }
                }
                else {
                    lastError = "201";
                    return "";
                }
            }
            else {
                lastError = "201";
                return "";
            }
        }
    }

    function _setIndexANewModel(index, newModel) {
        cmiModel.cmi.interactions.interactionModelArray[index] = newModel;
        var count = Number(cmiModel.cmi.interactions._count);
        count += 1;
        cmiModel.cmi.interactions._count = count.toString();
    }

    function _isStudentResponseValid(index, value) {
        if (value == "")
            return true;

        var type = cmiModel.cmi.interactions.interactionModelArray[index].type;
        if (type == "true-false") {
            var validCharacter = value.toString().charAt(0);
            if (validCharacter == "0" || validCharacter == "1" || validCharacter == "t" || validCharacter == "f")
                return true;
        }
        else if (type == "choice") {
            if (INTERACTIONCHOICE.test(value))
                return true;
        }
        else if (type == "fill-in") {
            if (value.toString().charAt(0) != " ")
                return true;
        }
        else if (type == "matching") {
            if (INTERACTIONMATCHING.test(value))
                return true;
        }
        else if (type == "performance") {
            if (value.toString().length <= CMISTRING255LENGTH)
                return true;
        }
        else if (type == "sequencing") {
            //can't understand
            return true;
        }
        else if (type == "likert") {
            return true;
        }
        else if (type == "numeric") {
            if (!isNaN(value))
                return true;
        }
        else
            return false;
    }

    function _dealWithSetInteractions(element, value) {
        var elementArray = element.toString().split(".");

        //avoid undefined  
        if (elementArray.length < 3) {
            lastError = "201";
            return "false";
        }
        else {
            if (elementArray[2] == "_children" && elementArray.length == 3) {
                lastError = "402";
                return "false";
            }
            else if (elementArray[2] == "_count" && elementArray.length == 3) {
                lastError = "402";
                return "false";
            }
            //layer3
            else if (!isNaN(elementArray[2])) {
                var index = Number(elementArray[2]);
                //new item
                if (cmiModel.cmi.interactions.interactionModelArray[index] == undefined) {
                    var newModel = {
                        "id": "",
                        "objectives": {
                            "_count": "0",
                            "idModelArray": []
                        },
                        "time": "",
                        "type": "",
                        "correct_responses": {
                            "_count": "0",
                            "patternArray": []
                        },
                        "weighting": "",
                        "student_response": "",
                        "result": "",
                        "latency": ""
                    };
                    if (elementArray[3] == "id" && elementArray.length == 4) {
                        //got white space
                        if (value.toString().indexOf(SPACE) != -1) {
                            lastError = "405";
                            return "false";
                        }
                        else {
                            lastError = "0";
                            newModel.id = value;
                            _setIndexANewModel(index, newModel);
                            return "true";
                        }
                    }
                    else if (elementArray[3] == "time" && elementArray.length == 4) {
                        if (_checkTime(value)) {
                            lastError = "0";
                            newModel.time = value;
                            _setIndexANewModel(index, newModel);
                            return "true";
                        }
                        else {
                            lastError = "405";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "type" && elementArray.length == 4) {
                        if (INTERACTIONTYPE.indexOf(value) != -1) {
                            lastError = "0";
                            newModel.type = value;
                            _setIndexANewModel(index, newModel);
                            return "true";
                        }
                        else {
                            lastError = "405";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "weighting" && elementArray.length == 4) {
                        if (!isNaN(value)) {
                            lastError = "0";
                            newModel.weighting = value;
                            _setIndexANewModel(index, newModel);
                            return "true";
                        }
                        else {
                            lastError = "405";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "student_response" && elementArray.length == 4) {
                        if (_isStudentResponseValid(index, value)) {
                            lastError = "0";
                            newModel.student_response = value;
                            _setIndexANewModel(index, newModel);
                            return "true";
                        }
                        else {
                            lastError = "405";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "result" && elementArray.length == 4) {
                        if (INTERACTIONRESULT.indexOf(value) != -1 || !isNaN(value)) {
                            lastError = "0";
                            newModel.result = value;
                            _setIndexANewModel(index, newModel);
                            return "true";
                        }
                        else {
                            lastError = "405";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "latency" && elementArray.length == 4) {
                        if (_checkTime(value)) {
                            lastError = "0";
                            newModel.latency = value;
                            _setIndexANewModel(index, newModel);
                            return "true";
                        }
                        else {
                            lastError = "405";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "objectives") {
                        //layer4
                        if (elementArray[4] == "_count" && elementArray.length == 5) {
                            lastError = "402";
                            return "false";
                        }
                        else if (!isNaN(elementArray[4]) && elementArray[5] == "id" && elementArray.length == 6) {
                            var secondIndex = Number(elementArray[4]);
                            if (value.toString().indexOf(SPACE) == -1) {
                                var newIdModel = { "id": value };
                                newModel.objectives.idModelArray[secondIndex] = newIdModel;
                                _setIndexANewModel(index, newModel);
                                lastError = "0";
                                return "true";
                            }
                            else {
                                lastError = "405";
                                return "false";
                            }
                        }
                        else {
                            lastError = "201";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "correct_responses") {
                        //layer4
                        if (elementArray[4] == "_count" && elementArray.length == 5) {
                            lastError = "402";
                            return "false";
                        }
                        else if (!isNaN(elementArray[4]) && elementArray[5] == "pattern" && elementArray.length == 6) {
                            var secondIndex = Number(elementArray[4]);
                            if (_isStudentResponseValid(index, value)) {
                                var newPatternModel = { "pattern": value };
                                newModel.correct_responses.patternArray[secondIndex] = newIdModel;
                                _setIndexANewModel(index, newModel);
                                lastError = "0";
                                return "true";
                            }
                            else {
                                lastError = "405";
                                return "false";
                            }
                        }
                        else {
                            lastError = "201";
                            return "false";
                        }
                    }
                    else {
                        lastError = "201";
                        return "false";
                    }
                }
                //modify
                else {
                    if (elementArray[3] == "id" && elementArray.length == 4) {
                        //got white space
                        if (value.toString().indexOf(SPACE) != -1) {
                            lastError = "405";
                            return "false";
                        }
                        else {
                            lastError = "0";
                            cmiModel.cmi.interactions.interactionModelArray[index].id = value;
                            return "true";
                        }
                    }
                    else if (elementArray[3] == "time" && elementArray.length == 4) {
                        if (_checkTime(value)) {
                            lastError = "0";
                            cmiModel.cmi.interactions.interactionModelArray[index].time = value;
                            return "true";
                        }
                        else {
                            lastError = "405";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "type" && elementArray.length == 4) {
                        if (INTERACTIONTYPE.indexOf(value) != -1) {
                            lastError = "0";
                            cmiModel.cmi.interactions.interactionModelArray[index].type = value;
                            return "true";
                        }
                        else {
                            lastError = "405";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "weighting" && elementArray.length == 4) {
                        if (!isNaN(value)) {
                            lastError = "0";
                            cmiModel.cmi.interactions.interactionModelArray[index].weighting = value;
                            return "true";
                        }
                        else {
                            lastError = "405";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "student_response" && elementArray.length == 4) {
                        if (_isStudentResponseValid(index, value)) {
                            lastError = "0";
                            cmiModel.cmi.interactions.interactionModelArray[index].student_response = value;
                            return "true";
                        }
                        else {
                            lastError = "405";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "result" && elementArray.length == 4) {
                        if (INTERACTIONRESULT.indexOf(value) != -1 || !isNaN(value)) {
                            lastError = "0";
                            cmiModel.cmi.interactions.interactionModelArray[index].result = value;
                            return "true";
                        }
                        else {
                            lastError = "405";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "latency" && elementArray.length == 4) {
                        if (_checkTime(value)) {
                            lastError = "0";
                            cmiModel.cmi.interactions.interactionModelArray[index].latency = value;
                            return "true";
                        }
                        else {
                            lastError = "405";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "objectives") {
                        //layer4
                        if (elementArray[4] == "_count" && elementArray.length == 5) {
                            lastError = "402";
                            return "false";
                        }
                        else if (!isNaN(elementArray[4]) && elementArray[5] == "id" && elementArray.length == 6) {
                            var secondIndex = Number(elementArray[4]);
                            //no white space
                            if (value.toString().indexOf(SPACE) == -1) {
                                //avoid undefined
                                if (cmiModel.cmi.interactions.interactionModelArray[index].objectives.idModelArray[secondIndex] == undefined) {
                                    var newIdModel = { "id": value };
                                    cmiModel.cmi.interactions.interactionModelArray[index].objectives.idModelArray[secondIndex] = newIdModel;
                                    var count = Number(cmiModel.cmi.interactions.interactionModelArray[index].objectives._count);
                                    count += 1;
                                    cmiModel.cmi.interactions.interactionModelArray[index].objectives._count = count.toString();
                                }
                                else {
                                    cmiModel.cmi.interactions.interactionModelArray[index].objectives.idModelArray[secondIndex].id = value;
                                }
                                lastError = "0";
                                return "true";
                            }
                            else {
                                lastError = "405";
                                return "false";
                            }
                        }
                        else {
                            lastError = "201";
                            return "false";
                        }
                    }
                    else if (elementArray[3] == "correct_responses") {
                        //layer4
                        if (elementArray[4] == "_count" && elementArray.length == 5) {
                            lastError = "402";
                            return "false";
                        }
                        else if (!isNaN(elementArray[4]) && elementArray[5] == "pattern" && elementArray.length == 6) {
                            //avoid undefined
                            if (_isStudentResponseValid(index, value)) {
                                if (cmiModel.cmi.interactions.interactionModelArray[index].correct_responses.patternArray[secondIndex] == undefined) {
                                    var newPatternModel = { "pattern": value };
                                    cmiModel.cmi.interactions.interactionModelArray[index].correct_responses.patternArray[secondIndex] = newPatternModel;
                                    var count = Number(cmiModel.cmi.interactions.interactionModelArray[index].correct_responses._count);
                                    count += 1;
                                    cmiModel.cmi.interactions.interactionModelArray[index].correct_responses._count = count.toString();
                                }
                                else {
                                    cmiModel.cmi.interactions.interactionModelArray[index].correct_responses.patternArray[secondIndex].pattern = value;
                                }
                                lastError = "0";
                                return "true";
                            }
                            else {
                                lastError = "405";
                                return "false";
                            }
                        }
                        else {
                            lastError = "201";
                            return "false";
                        }
                    }
                    else {
                        lastError = "201";
                        return "false";
                    }
                }
            }
            else {
                lastError = "201";
                return "false";
            }
        }
    }

    function _dealWithGetInteractions(element) {
        var elementArray = element.toString().split(".");

        //avoid undefined  
        if (elementArray.length < 3) {
            lastError = "201";
            return "";
        }
        else {
            if (elementArray[2] == "_children" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.interactions._children;
            }
            else if (elementArray[2] == "_count" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.interactions._count;
            }
            //layer3
            else if (!isNaN(elementArray[2])) {
                var index = Number(elementArray[2]);
                if (cmiModel.cmi.interactions.interactionModelArray[index] == undefined) {
                    lastError = "101";
                    return "";
                }
                else {
                    if (elementArray[3] == "id" && elementArray.length == 4) {
                        lastError = "404";
                        return "";
                    }
                    else if (elementArray[3] == "time" && elementArray.length == 4) {
                        lastError = "404";
                        return "";
                    }
                    else if (elementArray[3] == "type" && elementArray.length == 4) {
                        lastError = "404";
                        return "";
                    }
                    else if (elementArray[3] == "weighting" && elementArray.length == 4) {
                        lastError = "404";
                        return "";
                    }
                    else if (elementArray[3] == "student_response" && elementArray.length == 4) {
                        lastError = "404";
                        return "";
                    }
                    else if (elementArray[3] == "result" && elementArray.length == 4) {
                        lastError = "404";
                        return "";
                    }
                    else if (elementArray[3] == "latency" && elementArray.length == 4) {
                        lastError = "404";
                        return "";
                    }
                    else if (elementArray[3] == "objectives") {
                        if (elementArray[4] == "_count" && elementArray.length == 5) {
                            lastError = "0";
                            return cmiModel.cmi.interactions.interactionModelArray[index].objectives._count;
                        }
                        else if (!isNaN(elementArray[4]) && elementArray[5] == "id" && elementArray.length == 6) {
                            lastError = "404";
                            return "";
                        }
                        else {
                            lastError = "201";
                            return "";
                        }
                    }
                    else if (elementArray[3] == "correct_responses") {
                        if (elementArray[4] == "_count" && elementArray.length == 5) {
                            lastError = "0";
                            return cmiModel.cmi.interactions.interactionModelArray[index].correct_responses._count;
                        }
                        else if (!isNaN(elementArray[4]) && elementArray[5] == "pattern" && elementArray.length == 6) {
                            lastError = "404";
                            return "";
                        }
                        else {
                            lastError = "201";
                            return "";
                        }
                    }
                    else {
                        lastError = "201";
                        return "";
                    }
                }
            }
            else {
                lastError = "201";
                return "";
            }
        }
    }

    function _getErrorString(errorCode) {
        switch (errorCode) {
            case "0": { return "No error"; break }
            case "101": { return "General exception"; break }
            case "201": { return "Invalid argument error"; break }
            case "202": { return "Element cannot have children"; break }
            case "203": { return "Element not an array - Cannot have count"; break }
            case "301": { return "Not initialized"; break }
            case "401": { return "Not implemented error"; break }
            case "402": { return "Invalid set value, element is a keyword"; break }
            case "403": { return "Element is read only"; break }
            case "404": { return "Element is write only"; break }
            case "405": { return "Incorrect Data Type"; break }
            default: { return ""; break }
        }
        // just to be safe...
        return;
    }

    function _getDiagnostic(errorCode) {
        if (errorCode == "") {
            errorCode = lastError;
        }
        switch (errorCode) {
            case "0": { return "No error. No errors were encountered. Successful API call."; break }
            case "101": { return "General exception. An unexpected error was encountered."; break }
            case "201": { return "Invalid argument error. A call was made to a DataModel element that does not exist."; break }
            case "202": { return "Element cannot have children. A call was made to an Element that does not support _children"; break }
            case "203": { return "Element is not an array.  Cannot have count. A call was made to an Element that does not support _count."; break }
            case "301": { return "Not initialized. The SCO has not yet been initialized.  It needs to call LMSInitialize() first."; break }
            case "401": { return "Not implemented error.  A call was made to a DataModel element that is not supported."; break }
            case "402": { return "Invalid set value, element is a keyword.  Keyword values cannot be changed"; break }
            case "403": { return "Element is read only.  A call was made to set the value of a read-only element."; break }
            case "404": { return "Element is write only.  A call was made to get the value of a write-only element."; break }
            case "405": { return "Incorrect Data Type.  The syntax of a call to change an element was incorrect."; break }
            default: { return ""; break }
        }
        // just to be safe...
        return;
    }

    //element format:
    //layer0  layer1  layerX
    //  cmi  . xxx   . xxx
    //warning: refactor this function to make it easier to read, if timeable
    function _setValue(element, value) {
        //test
        //alert("SetValue!!" + element.toString() + " : " + value.toString());

        //To make sure things go right
        value = value.toString();

        if (!isInitialize) {
            lastError = "301";
            return "false";
        }

        //make the element layered
        var elementArray = element.toString().split(".");

        //layer0
        //this condition is not complete, but not a big deal, cos the scos are not likely to pass some "imaginary" value
        if (elementArray.length < 2 || elementArray.length > 6 || elementArray[0].toString() != "cmi") {
            lastError = "201";
            return "false";
        }

        //layer1
        if (elementArray[1].toString() == "launch_data" && elementArray.length == 2) {
            lastError = "403";
            return "false";
        }
        //layer1
        else if (elementArray[1].toString() == "comments_from_lms" && elementArray.length == 2) {
            lastError = "403";
            return "false";
        }
        //layer1
        else if (elementArray[1].toString() == "suspend_data" && elementArray.length == 2) {
            if (value.toString().length > CMISTRING4096LENGTH) {
                lastError = "405";
                return "false";
            }
            else {
                //suspend_data is a string!!
                cmiModel.cmi.suspend_data = value.toString();
                lastError = "0";
                return "true";
            }
        }
        //layer1
        else if (elementArray[1].toString() == "comments" && elementArray.length == 2) {
            if (value.toString().length > CMISTRING4096LENGTH) {
                lastError = "405";
                return "false";
            }
            else {
                //suspend_data is a string!!
                cmiModel.cmi.comments = value.toString();
                lastError = "0";
                return "true";
            }
        }
        //layer1
        else if (elementArray[1].toString() == "core") {
            //layer2
            //refactored also
            return _dealWithSetCore(element, value);
        }
        //layer1
        else if (elementArray[1].toString() == "objectives") {
            //layer2
            //too complicated, so refactored            
            return _dealWithSetObjectives(element, value);
        }
        //layer1
        else if (elementArray[1].toString() == "student_data") {
            if (elementArray.length < 3) {
                lastError = "201";
                return "false";
            }
            else {
                if (elementArray[2].toString() == "_children" && elementArray.length == 3) {
                    lastError = "402";
                    return "false";
                }
                else if ((elementArray[2].toString() == "mastery_score" || elementArray[2].toString() == "max_time_allowed" || elementArray[2].toString() == "time_limit_action") && elementArray.length == 3) {
                    lastError = "403";
                    return "false";
                }
                else {
                    lastError = "201";
                    return "false";
                }
            }
        }
        //layer1
        //temperarily not supported
        else if (elementArray[1].toString() == "student_preference") {
            lastError = "401";
            return "false";
        }
        //layer1
        else if (elementArray[1].toString() == "interactions") {
            return _dealWithSetInteractions(element, value);
        }
        else {
            lastError = "201";
            return "false";
        }
    }

    //element format:
    //layer0  layer1  layerX
    //  cmi  . xxx   . xxx
    //warning: refactor this function to make it easier to read, if timeable
    function _getValue(element) {
        //test
        // alert("GetValue!!! " + element.toString());

        if (!isInitialize) {
            lastError = "301"
            return "";
        }

        //make the element layered
        var elementArray = element.toString().split(".");

        //layer0
        //this condition is not complete, but not a big deal, cos the scos are not likely to pass some "imaginary" value
        if (elementArray.length < 2 || elementArray.length > 6 || elementArray[0].toString() != "cmi") {
            lastError = "201";
            return "";
        }

        //special return
        if (element == "cmi._version" && elementArray.length == 2) {
            lastError = "0";
            return DATAMODELVERSION;
        }

        //layer1
        if (elementArray[1] == "suspend_data" && elementArray.length == 2) {
            lastError = "0";
            return cmiModel.cmi.suspend_data;
        }
        //layer1
        else if (elementArray[1] == "launch_data" && elementArray.length == 2) {
            lastError = "0";
            return cmiModel.cmi.launch_data;

        }
        //layer1
        else if (elementArray[1] == "comments" && elementArray.length == 2) {
            lastError = "0";
            return cmiModel.cmi.comments;

        }
        //layer1
        else if (elementArray[1] == "comments_from_lms" && elementArray.length == 2) {
            lastError = "0";
            return cmiModel.cmi.comments_from_lms;
        }
        //layer1
        else if (elementArray[1] == "core") {
            return _dealWithGetCore(element);
        }
        //layer1
        else if (elementArray[1] == "objectives") {
            return _dealWithGetObjectives(element);
        }
        //layer1
        else if (elementArray[1] == "student_data") {
            if (elementArray.length > 3) {
                lastError = "201";
                return "";
            }
            else if (elementArray[2] == "mastery_score" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.student_data.mastery_score;
            }
            else if (elementArray[2] == "max_time_allowed" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.student_data.max_time_allowed;
            }
            else if (elementArray[2] == "time_limit_action" && elementArray.length == 3) {
                lastError = "0";
                return cmiModel.cmi.student_data.time_limit_action;
            }
            else {
                lastError = "201";
                return "";
            }
        }
        //layer1
        //temperarily not supported
        else if (elementArray[1] == "student_preference") {
            lastError = "401";
            return "";
        }
        //layer1
        else if (elementArray[1] == "interactions") {
            return _dealWithGetInteractions(element);
        }
        //layer1
        else {
            lastError = "201";
            return "";
        }
    }

    function _initialize(param, userInit) {
        if (isInitialize) {
            lastError = "101";
            return "false";
        }

        if (param != "") {
            lastError = "201";
            return "false";
        }

        lastError = "0";
        isInitialize = true;
        return userInit();
    }

    function _finish(param, userFinish) {
        if (!isInitialize) {
            lastError = "301"
            return "false";
        }

        if (param != "") {
            lastError = "201";
            return "false";
        }

        cmiModel.cmi.core.total_time = _addTimeSpan(cmiModel.cmi.core.total_time, cmiModel.cmi.core.session_time);

        lastError = "0";
        isInitialize = false;
        return userFinish();
    }

    function _commit(param, userCommit) {
        if (!isInitialize) {
            lastError = "301";
            return "false";
        }

        if (param != "") {
            lastError = "201";
            return "false";
        }

        lastError = "0";
        return userCommit();
    }

    //constructor
    window.$.fn.scormAPI = function(options) {
        var defaults = {
            init: function() { return "true"; },
            finish: function() { return "true"; },
            commit: function() { return "true"; }
        };

        var opts = window.$.fn.extend(true, {}, defaults, options);

        //
        //API is a globle object, for some secure reasons
        //
        window.API = {};
        //overrideable
        API.LMSInitialize = function(param) { return _initialize(param, opts.init); };
        API.LMSFinish = function(param) { return _finish(param, opts.finish); };
        API.LMSCommit = function(param) { return _commit(param, opts.commit); };
        //overrideable not
        API.LMSGetValue = _getValue;
        API.LMSSetValue = _setValue;
        API.LMSGetLastError = function() { return lastError };
        API.LMSGetErrorString = _getErrorString;
        API.LMSGetDiagnostic = _getDiagnostic;
    };

    //public method
    window.$.fn.scormAPI.setScormModel = function(value) {
        cmiModel = value;
    };

    window.$.fn.scormAPI.getScormModel = function() {
        return cmiModel;
    };

    window.$.fn.scormAPI.setScormModelString = function(jsonString) {
        try {
            cmiModel = JSON.parse(jsonString);
        }
        catch (err) {
            //bad format
        }
    }

    window.$.fn.scormAPI.getScormModelString = function() {
        try {
            return JSON.stringify(cmiModel);
        }
        catch (err) {
            //unexpected
        }
    }

    //dangerous action
    window.$.fn.scormAPI.customSetValue = function(element, data) {
        eval('cmiModel.' + element.toString() + '="' + data.toString() + '"');
    }

    window.$.fn.scormAPI.userErrorOccurs = function() {
        lastError = "101";
    }

})(jQuery);