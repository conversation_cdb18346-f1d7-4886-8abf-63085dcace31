<template>
  <div :class="extendOptions.type == 'edit'?'editTitleBox':''" class="list-dialog a-c-dialog">
    <el-dialog width="960px" :visible="visible" :close-on-click-modal="false" :before-close="cancel" :show-close="false">
      <div slot="title">
        <div class="title-box" style="display: flex; align-items: center; ">
          <span v-if="!extendOptions.showAddOutLink_createAggreagte" style="font-size: 16px; color: #000000e6; font-weight: 600;">{{ addNewCourseType === 'front' ? '课前学习' : '课后学习' }}</span>
          <span v-else>{{ extendOptions.title }} <span v-if="extendOptions.type != 'edit'" class="tips">注意：如班级/活动关联了课前问卷，用户通过聚合报名页提交报名时，将跳过课前问卷填写环节</span></span>
          <img :src="require('@/assets/img/close.png')" alt="" @click="cancel" style="width: 22px; cursor: pointer;">
        </div>
        <div v-if="extendOptions.title && extendOptions.type == 'edit'" class="editTitle">
          <span>{{ extendOptions.courseTitle }}</span>
        </div>
        <div v-if="extendOptions.showAddOutLink_createAggreagte" class=" course-title  course-body-title">
          <div class="serach-item">
            <span class="associationList">关联至目录：</span>
            <el-select v-model="associatedDirectory" placeholder="请选择目录" size="mini">
              <el-option v-for="item in catalogList" :key="item.category_name" :label="item.category_name" :value="item.category_name"></el-option>
            </el-select>
          </div>
        </div>
      </div>
      <div class="course-body">
        <div v-if="!extendOptions.showAddOutLink_createAggreagte" class="course-body-title">
          <div class="check-label-warning">
            <i class="el-icon-warning-outline"></i>
            <span>仅支持使用本人上传或有使用权限的课程素材</span>
          </div>
        </div>

        <div v-if="extendOptions.type != 'edit'" class="serach-box">
          <div class="serach-item">
            <el-cascader placeholder="请选择课程分类" v-model="form.classifyId" :options="classifiyOptions"
              :props="classifyProps" label="classify_name" clearable @change="handleClassify" size="mini">
            </el-cascader>
          </div>

          <div class="serach-item">
            <el-select v-model="form.module_id" placeholder="请选择课程类型" clearable @change="handleModuleChange" size="mini">
              <el-option v-for="item in moduleInfo" :key="item.module_id" :label="item.module_name"
                :value="item.module_id">
              </el-option>
            </el-select>
          </div>

          <div class="serach-item">
            <el-input clearable v-model="form.course_name" placeholder="请输入课程名称"
              suffix-icon="el-icon-search" size="mini"></el-input>
          </div>

          <div class="serach-item serach-btn">
            <el-button size="small" plain @click="handleReset" class="btn refresh" icon="el-icon-refresh">
              重置
            </el-button>
            <el-button type="primary" size="small" @click="onSearch(1)" class="btn">搜索</el-button>
            <el-button v-if="!extendOptions.showAddOutLink_createAggreagte" type="primary" size="small"
              @click="handleCreateCourse">添加外链内容</el-button>
            <!-- <el-dropdown @command="handleCommand" trigger="click" class="add-course">
              <el-button type="primary" size="small">创建课程<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="video">视频</el-dropdown-item>
                <el-dropdown-item command="v8">音频</el-dropdown-item>
                <el-dropdown-item command="Article">文章</el-dropdown-item>
                <el-dropdown-item command="Doc">文档</el-dropdown-item>
                <el-dropdown-item command="v8">scorm</el-dropdown-item>
                <el-dropdown-item command="v8">压缩包</el-dropdown-item>
                <el-dropdown-item command="Other" v-if="extendOptions.showAddOutLink">外链内容</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown> -->
          </div>

        </div>

        <el-table v-if="extendOptions.type != 'edit'" ref="mainTable" :data="tableData.records" max-height="551px"
          @selection-change="selectionChange" @sort-change="sortChange" class="add-table"
          header-row-class-name="add-header-style">
          <!-- 单选 -->
          <el-table-column width="55" v-if="entryType === 'change'" :key="1">
            <template slot-scope="scope">
              <el-radio @change="handleRowChange(scope.row)" v-model="tableRadio"
                :label="scope.row.item_id">&nbsp;</el-radio>
            </template>
          </el-table-column>
          <!-- 多选 -->
          <el-table-column width="55" type="selection" v-else :key="2" :selectable="selectable"></el-table-column>
          <el-table-column prop="item_id" label="课程id" width="101"></el-table-column>
          <el-table-column prop="content_name" label="课程名称" show-overflow-tooltip width="252">
            <template v-slot="prop">
              <a class="content-url" target="_blank" :href="prop.row.content_url">{{ prop.row.content_name }}</a>
            </template>
          </el-table-column>
          <el-table-column prop="classify_names" label="课程分类" show-overflow-tooltip width="112">
            <template slot-scope="scope">
              <span>{{ scope.row.classify_names || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="类型" width="97">
            <template slot-scope="scope">
              <span>{{ scope.row.module_name }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="" label="创建人"></el-table-column> -->
          <el-table-column prop="created_at" label="创建时间" width="180" sortable="custom">
          </el-table-column>
        </el-table>
        <el-pagination v-if="tableData.total && extendOptions.type != 'edit'" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" :current-page="current" :page-sizes="[5, 10, 20, 30, 50, 100]"
          :page-size="size" layout="total,  prev, pager, next, sizes, jumper" :total="tableData.total"
          style="margin: 12px 0 0 0;">
        </el-pagination>
      </div>
      <div slot="footer" :class="extendOptions.type == 'edit' ? '' : 'dialog-footer'">
        <el-button size="small" @click="cancel">取 消</el-button>
        <el-button v-if="extendOptions.type == 'edit'" size='small' @click="editSubmit" type="primary">确 定</el-button>
        <el-button v-else size='small' @click="submit" type="primary">添 加</el-button>
      </div>
    </el-dialog>
    <!-- 添加外链 -->
    <AddOutLinkShow ref="addOutLinkDialogRef" v-if="addOutLinkShowDialogShow" outLinkType="add"
      @updateTreeList="getOutLinkInfo" :visible.sync="addOutLinkShowDialogShow" />
    <AddErrorDialog :visible.sync="addErrorDialogShow" :taskNameList="taskNameList"></AddErrorDialog>
  </div>
</template>
<script>
import pager from '@/mixins/pager'
import { getSearchsite, searchCourse, getClassify, getSearchCourseId } from 'config/api.conf'
import { AddErrorDialog } from '@/views/manage/mooc/project-manage/task-list/component/index.js'
import { mapState } from 'vuex'
import { actTypes } from '@/utils/moduleMap.js'
import AddOutLinkShow from './addOutCourse'
export default {
  mixins: [pager],
  components: {
    AddErrorDialog,
    AddOutLinkShow
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentNode: {
      type: Object
    },
    treeNode: {
      type: Array,
      default: () => ([])
    },
    entryType: { // 更换课程--添加课程区分
      type: String
    },
    addNewCourseType: {
      type: String,
      default: 'front' // front: 课前, after: 课后
    },
    extendOptions: {
      type: Object,
      default: () => ({
        showAddOutLink: false,
        banSameCourse: false,
        sameCourseList: [],
        showAddOutLink_createAggreagte: false,
        title: '新增内容',
        courseTitle: '课程名称',
        catalog: '',
        type: 'add'
      })
    },
    moduleTypes: {
      type: Array,
      default: () => [1, 2, 3, 4, 5, 6, 7, 8, 10, 15, 16, 17, 20]
    },
    useQuery: {
      type: String,
      default: 'searchCourse'
    },
    catalogList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableRadio: '',
      form: {
        classifyId: '',
        classify: [],
        course_name: '',
        module_id: ''
      },
      classifiyOptions: [],
      classifyProps: {
        multiple: false,
        label: 'classify_name',
        value: 'classify_id',
        children: 'child',
        checkStrictly: true,
        emitPath: false
      },
      // contentModuleTypes: [
      //   { module_name: '视频', module_id: 1, course_type: ['Video', 'Video-2d', 'Video-3d', 'Video-ppt'], module_value: 0 },
      //   { module_name: '音频', module_id: 1, course_type: ['Audio'], module_value: 1 },
      //   { module_name: '文章', module_id: 8, course_type: ['Article'], module_value: 2 },
      //   { module_name: '文档', module_id: 16, course_type: ['Doc'], module_value: 3 },
      //   { module_name: 'scorm', module_id: 1, course_type: ['Scorm'], module_value: 4 },
      //   { module_name: '压缩包', module_id: 1, course_type: ['Flash'], module_value: 5 }
      // ],
      tableData: {
        records: [],
        total: 0
      },
      selectionList: [],
      addErrorDialogShow: false,
      taskNameList: [],
      size: 5,
      sortOrder: '',
      sort_by: '',
      addOutLinkShowDialogShow: false,
      moduleInfo: [],
      associatedDirectory: ''
    }
  },
  computed: {
    ...mapState(['userInfo']),
    filterResourceName() {
      return ({ course_type, module_id }) => {
        let name = ''
        if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(course_type)) {
          name = '视频'
        } else if (course_type === 'Audio') {
          name = '音频'
        } else if (course_type === 'Article' || module_id === 8) {
          name = '文章'
        } else if (course_type === 'Doc' || module_id === 16) {
          name = '文档'
        } else if (course_type === 'Scorm') {
          name = 'Scorm'
        } else if (course_type === 'Series') {
          name = '系列课'
        } else if (course_type === 'Flash') {
          name = '压缩包'
        }
        return name
      }
    }
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          const { title, catalog } = this.extendOptions
          if (title === '编辑内容' && catalog) {
            this.associatedDirectory = catalog
          } else {
            this.associatedDirectory = this.catalogList[0].category_name
          }
          // 清空数据
          this.$nextTick(() => {
            this.selectionList = []
            this.$refs.mainTable.clearSelection()
            this.$refs.mainTable.clearSort()
          })
        }
      },
      immediate: true
    },
    catalogList: {
      handler(val) {
        if (val && val.length && !this.associatedDirectory) {
          this.associatedDirectory = val[0].category_name
        }
      },
      immediate: true
    }
  },
  created() {
    this.getClassifyData()
    this.getModuleInfo()
  },
  methods: {
    // 检查是否存在相同课程,需要对比item_id和act_type，并且是两个数据进行对比tableData.records和this.extendOptions.sameCourseList对比,如果存在相同课程，则选中该课程并且禁用勾选
    // checkSameCourse() {
    //   if (this.extendOptions.banSameCourse) {
    //     this.tableData.records.forEach((v) => {
    //       if (this.extendOptions.sameCourseList.some(item => item.item_id === v.item_id && item.act_type === v.act_type)) {
    //         this.$refs.mainTable.toggleRowSelection(v, true)
    //         v.disabled = true
    //       }
    //     })
    //   }
    // },
    getModuleInfo() {
      getSearchCourseId().then(res => {
        let moduleArr = []
        let result = []
        // 返回的moduleInfo是个对象，需要转换成数组
        for (let key of Object.keys(res.moduleInfo)) {
          // 0是综合，综合不需要作为选项，过滤掉
          if (key === '0') continue
          moduleArr.push(res.moduleInfo[key])
        }
        for (let item of moduleArr) {
          if (this.moduleTypes.includes(item.module_id)) {
            result.push(item)
          }
        }
        if (this.useQuery === 'searchCourse') {
          result.unshift({
            module_id: 'all',
            module_name: '全部'
          })
          this.form.module_id = 'all'
        } else if (this.useQuery === 'getSearchsite') {
          const ids = result.map((v) => v.module_id)
          result.unshift({
            module_id: 'all',
            module_name: '全部',
            ids
          })
          this.form.module_id = 'all'
        }
        this.moduleInfo = result
        
        this.onSearch(1)
      })
    },
    handleCreateCourse() {
      this.addOutLinkShowDialogShow = true
    },
    selectable(row) {
      if (!this.extendOptions.banSameCourse) return true
      if (!this.extendOptions.sameCourseList.length) return true
      const isSame = this.extendOptions.sameCourseList.some(item => {
        return item.item_id === row.item_id && item.act_type === row.act_type
      })
      return !isSame
    },
    onSearch(page_no = 1) {
      const { classify, course_name, module_id, classifyId } = this.form
      const course_type_info = this.moduleInfo.find((v) => v.module_id === module_id)
      const data = {
        classify: classifyId && classify ? classify : [],
        keywords: course_name,
        pageNum: page_no,
        pageSize: this.size,
        sort_order: this.sortOrder,
        sort_by: this.sort_by
      }

      const ALL_MODULE_ID = 'all'
      const ARTICLE_MODULES = [8, 16] // 文章和图文模块ID
      
      if (module_id === ALL_MODULE_ID) {
        const allModule = this.moduleInfo.find(v => v.module_id === module_id)
        if (this.useQuery === 'searchCourse') {
          data.moduleId = ''
        } else if (this.useQuery === 'getSearchsite') {
          data.moduleIds = allModule?.ids
        }
      } else {
        data.moduleId = course_type_info?.module_id
      }
      // 设置模块相关参数
      if (data.moduleId && !ARTICLE_MODULES.includes(data.moduleId)) {
        data.courseType = course_type_info?.course_type
      }
      
      const currentApi = this.useQuery === 'searchCourse' ? searchCourse : getSearchsite
      currentApi(data).then(res => {
        const records = this.useQuery === 'searchCourse' ? res.list : res.records
        this.tableData.records = records.map((v) => {
          const time = v.updated_at.split('.')[0]
          // 以防首字母小写处理
          const course_type = v.course_type ? v.course_type.slice(0, 1).toUpperCase() + v.course_type.slice(1).toLowerCase() : ''
          return {
            ...v,
            updated_at: time,
            course_type
          }
        })
        this.tableData.total = res.total
        // this.$nextTick(() => {
        //   this.checkSameCourse()
        // })
      })
    },
    getClassifyData() {
      getClassify({ act_type: 2 }).then((res) => {
        this.classifiyOptions = res
        // 分类再次操作卡死处理
        setTimeout(() => {
          const $el = document.querySelectorAll('.el-cascader-panel .el-cascader-node[aria-owns]')
          if ($el.length > 0) {
            Array.from($el).map(item => item.removeAttribute('aria-owns'))
          }
        }, 200)
      })
    },
    // 勾选分类
    handleClassify(val) {
      const formatClassify = (list) => {
        list.forEach((e) => {
          if (e.classify_id === val) {
            this.form.classify = [
              {
                field: `classify_level_${e.level}`,
                id: val
              }
            ]
          } else {
            if (e.child?.length) {
              formatClassify(e.child)
            }
          }
        })
      }
      formatClassify(this.classifiyOptions)
    },
    handleCommand(val) {
      let url = ''
      if (val === 'video') { // 视频
        url = process.env.NODE_ENV === 'production' ? '//portal.learn.woa.com/training/creator-center/course-share' : '//test-portal-learn.woa.com/training/creator-center/course-share'
      } else if (val === 'v8') { // 音频--scorm--压缩包
        url = process.env.NODE_ENV === 'production' ? 'https://learn.woa.com/manage/net/eidt' : 'http://test.v8.learn.oa.com/manage/net/eidt'
      } else if (val === 'Article') { // 文章
        url = process.env.NODE_ENV === 'production' ? '//portal.learn.woa.com/training/graphic/user/create' : '//test-portal-learn.woa.com/training/graphic/user/create'
      } else if (val === 'Doc') {
        url = '//learn.woa.com/user/profile/add'
      } else if (val === 'Other') {
        this.addOutLinkShowDialogShow = true
        return
      }
      window.open(url)
    },
    // 单选
    handleRowChange(val) {
      this.selectionList = [val]
    },
    // 多选
    selectionChange(val) {
      this.selectionList = val
    },
    sortChange({ column, prop, order }) {
      this.sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : ''
      this.sort_by = this.sortOrder ? 'created_at' : ''
      this.onSearch()
    },
    getOutLinkInfo(data) {
      let obj = {}
      if (data.length > 0) {
        obj = {
          content_name: data[0].content_name,
          act_type: data[0].act_type,
          module_type: data[0].module_type,
          act_id: '',
          resource_url: data[0].resource_url,
          content_mobile_url: data[0].relation_content_url_mobile,
          finished_condition_desc: data[0].finished_condition_desc,
          relation_content_desc: data[0].relation_content_desc,
          resource_type: data[0].resource_type,
          resource_type_name: data[0].resource_type_name,
          relation_content_resource_type: data[0].relation_content_resource_type,
          module_name: data[0].resource_type_name,
          module_id: data[0].module_id
        }

        // 如果是小程序，添加小程序appid
        if (data[0].relation_content_resource_type === 'WechatMini' && data[0].wechat_mini_appid) {
          obj.wechat_mini_appid = data[0].wechat_mini_appid
        }
      }
      this.$emit('update:visible', false)
      this.$emit('handleShowSetDialog', [obj])
    },
    submit() {
      if (this.selectionList.length === 0) {
        this.$message.error('请选择一个课程')
        return
      }
      const urlMap = {
        Video: `${process.env.VUE_APP_V8_HOST_WOA}/training/mobile/outsourcedCourse/video/play?course_id=`,
        Graphic: `${process.env.VUE_APP_V8_HOST_WOA}/training/mobile/outsourcedCourse/graphic/play?course_id=`,
        Series: `${process.env.VUE_APP_V8_HOST_WOA}/training/mobile/outsourcedCourse/iframe/play?course_id=`
      }
      this.selectionList = this.selectionList.map((v) => {
        let resource_type = ''
        let resource_type_name = ''
        let isNum = true
        if (v.module_id === 1) {
          if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(v.course_type)) {
            resource_type = 'Video'
            resource_type_name = '视频'
          } else if (v.course_type === 'Flash') {
            resource_type = 'Zip'
            resource_type_name = '压缩包'
          } else if (v.course_type === 'Audio') {
            resource_type = 'Audio'
            resource_type_name = '音频'
          } else if (v.course_type === 'Scorm') {
            resource_type = 'Scorm'
            resource_type_name = 'Scorm'
          } else if (v.course_type === 'Series') {
            resource_type = 'Series'
            resource_type_name = '系列课'
          }
          isNum = /^\d+$/.test(v.item_id)
        } else {
          if (v.module_id === 8) { // 文章
            resource_type = 'Article'
            resource_type_name = '文章'
          } else if (v.module_id === 16) { // 文档
            resource_type = 'Doc'
            resource_type_name = '文档'
          }
        }
        let actMap = actTypes.find((el) => { return el.module_id === v.module_id })
        // 如果module_id为1时，item_id非纯数字
        if (!isNum) {
          actMap.act_type = 102
        }
        if (actMap?.act_type === 102) {
          if (['Video', 'Graphic', 'Series'].includes(v.course_type)) {
            v.resource_url_mobile = urlMap[v.course_type] + v.item_id
          } else {
            v.resource_url_mobile = v.content_url
          }
        }

        const curCatalogIndex = this.catalogList.findIndex(v => v.category_name === this.associatedDirectory)
        let category_id = ''
        if (curCatalogIndex > -1) category_id = this.catalogList[curCatalogIndex].category_id
        return {
          ...v,
          id: this.currentNode?.id || '',
          required: true,
          act_id: v.item_id,
          resource_type,
          resource_type_name,
          task_name: v.content_name,
          act_name: v.content_name,
          radio: 1,
          isWordNum: '',
          word_number: v.word_num,
          act_type: actMap?.act_type || '',
          resource_content_type: v.content_type || '',
          resource_url: v.content_url,
          category_name: this.associatedDirectory,
          category_id: category_id || ''
        }
      })
      this.onSearch(1)
      this.$emit('update:visible', false)
      this.$emit('handleShowSetDialog', this.selectionList)
      if (this.entryType === 'change') {
        this.$emit('changeSingleData', ...this.selectionList)
      }
    },
    editSubmit() {
      this.onSearch(1)
      this.$emit('update:visible', false)
      this.$emit('handleEditCatalog', this.associatedDirectory)
    },
    handleReset() {
      this.form = {
        classifyId: '',
        classify: [],
        course_name: '',
        module_id: 'all'
      }
      this.current = 1
      this.size = 5
      this.sortOrder = ''
      this.sort_by = ''
      this.$refs.mainTable.clearSort()

      const { showAddOutLink_createAggreagte: showInAggregate, catalog: defaultCatalog } = this.extendOptions
      if (showInAggregate) {
        const defaultCategoryName = this.catalogList?.[0]?.category_name || ''
        this.associatedDirectory = defaultCatalog || defaultCategoryName
      }
      this.onSearch()
    },
    cancel() {
      this.$emit('update:visible', false)
    },
    handleModuleChange(e) {
      if (!e) {
        this.form.module_id = 'all'
      }
      // 当module_value变化时，立即搜索
      this.onSearch(1)
    }
  }
}
</script>
<style lang="less" scoped>
.editTitleBox{
  /deep/ .el-dialog__header{
    border:none;
  }
  /deep/ .el-dialog__body{
    display: none;
  }
}
.editTitle {
  height: 33px;
  font-size: 14px;
  margin: 24px 0;
  opacity: 0.8;
  color: #000000CC;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.course-title {
  margin-bottom: 0 !important;
}

.associationList {
  margin-right: 12px;
  color: #000000E5
}

.a-c-dialog {
  :deep(.el-input__icon) {
    line-height: 32px !important;
  }
}

.tips {
  font-weight: 400 !important;
  color: #E34D59 !important;
  margin-left: 24px;
  font-size: 14px;
  margin-bottom: 24px;
  display: inline-block;
}

.el-input__inner {
  height: 32px;
}

.list-dialog {
  .content-url {
    color: #0052d9;
    cursor: pointer;
  }

  .course-body-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 24px;
  }

  .serach-box {
    display: flex;
    align-items: center;
    gap: 0 16px;
    margin: 0 0 12px 0;
    height: 32px;

    .serach-item {
      &:nth-child(1) {
        .el-cascader {
          width: 180px;
        }
      }

      &:nth-child(2) {
        .el-select {
          width: 180px;

          .el-input__icon {
            line-height: 32px !important;
          }
        }
      }

      .el-input {
        width: 180px;
      }

    }

    .serach-btn {
      margin: 0 0 0 16px;

      .btn {
        width: 80px;

        &:nth-child(2) {
          margin-left: 16px;
          width: 100px;

          .el-button--primary {
            width: 100%;
          }
        }
      }
    }

    .check-label-warning {
      width: 100%;
      color: #ff7548;
      background-color: rgba(253, 246, 236, 1);
      padding: 10px 16px;
      display: flex;
      align-items: center;
      font-size: 14px;

      i {
        margin-right: 10px;
        font-size: 20px;
      }
    }

    :deep(.el-table) {
      .add-header-style {
        >th {
          background-color: #F4F8FF;

          &:nth-child(1) {
            .cell {
              padding: 0 0 0 13px;
            }
          }
        }
      }

      :deep(.el-pagination) {
        .el-pagination__sizes {
          .el-input__icon {
            line-height: 24px !important;
          }
        }
      }

      :deep(.el-dialog) {
        border-radius: 9px;

        .el-dialog__header {
          .title-box {
            display: flex;
            align-items: center;
            justify-content: space-between;

            span {
              font-size: 16px;
              color: #000000e6;
              font-weight: 600;
            }

            img {
              width: 22px;
              cursor: pointer;
            }
          }

          .el-dialog__title {
            font-size: 16px;
          }

          .el-dialog__close {}
        }

        .refresh {
          background-color: #fff;
          border: 1px solid #0052D9;
          color: #0052D9;
        }

        .add-course {
          margin-left: 16px;
          width: 100px;

          .el-button--primary {
            width: 100%;
          }
        }
      }
    }
  }

  .check-label-warning {
    width: 100%;
    color: #ff7548;
    background-color: rgba(253, 246, 236, 1);
    padding: 10px 16px;
    display: flex;
    align-items: center;
    font-size: 14px;

    i {
      margin-right: 10px;
      font-size: 20px;
    }
  }

  :deep(.el-table) {
    .add-header-style {
      >th {
        background-color: #F4F8FF;

        &:nth-child(1) {
          .cell {
            padding: 0 0 0 13px;
          }
        }
      }
    }
  }

  :deep(.el-pagination) {
    .el-pagination__sizes {
      .el-input__icon {
        line-height: 24px !important;
      }
    }
  }

  :deep(.el-dialog) {
    border-radius: 9px;

    .el-dialog__header {
      .title-box {
        display: flex;
        align-items: center;
        justify-content: space-between;

        span {
          font-size: 16px;
          color: #000000e6;
          font-weight: 600;
        }

        img {
          width: 22px;
          cursor: pointer;
        }
      }

      .el-dialog__title {
        font-size: 16px;
      }

      .el-dialog__close {}
    }

    .dialog-footer {
      margin: 24px 0 4px 0;
    }

    .el-dialog__body {
      padding: 24px 32px 0;
    }
  }
}
</style>
