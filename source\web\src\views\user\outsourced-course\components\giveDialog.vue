<template>
  <el-dialog class="give-course-dialog" :top="top" :visible.sync="dialogVisible" :width="width" @open="handlerOpen" :close-on-click-modal="false">
    <div slot="title">
      <!-- 送出学霸卡 <span class="title-tips">当前已送出 <span class="tips_b">{{giveNumber}}</span> 张学霸卡（共可送出 10 张），获得奖励 <span class="tips_b">{{numberOfRewards}}</span> 张「{{xueBaCardConfig.card_name}}」专用卡</span> -->
      <span v-if="giveType === 'account'">链接分享：访问链接，即可加入学习哈佛管理导师相关内容</span>
      <span v-else>链接分享：被分享者访问链接，即可领取学霸卡</span>
    </div>
    <div :class="['share-box', { 'share-box_account': giveType === 'account' }]">
      <div class="share-rq-code">
        <img id="codeImg" :src="qrUrl" alt="img" srcset="">
      </div>
      <div class="share-url-box">
        <span class="copy-btn" style="line-height: 24px;" @click="handleCopyImg" :dt-areaid="dtButton('area')" :dt-eid="dtButton('eid')" :dt-remark="dtButton('remark', '复制二维码图片')">复制二维码图片</span>
        <div class="url-text">
          <el-input style="padding-right: 16px;" v-model="urlText" type="text" disabled></el-input>
          <span class="copy-btn" @click="doCopy" :dt-areaid="dtButton('area')" :dt-eid="dtButton('eid')" :dt-remark="dtButton('remark', '复制专属分享链接')">复制专属分享链接</span>
        </div>
      </div>
    </div>
    <div class="content-xueba" v-if="giveType === 'xueba'">
      <div class="content-xueba-title">
        <span v-if="giveType === 'account'">定向分享：选中分享对象，系统将向其推送邀约信息</span>
        <span v-else>定向分享：选中分享对象，向其赠送学霸卡</span>
      </div>
      <el-form :inline="true" :model="hasData" class="has-form-inline">
        <el-form-item label="" style="margin-right: 24px;">
          <sdc-staff-selector size="small" ref="staffSelect" @change="staffChange" v-model="staffName" showFullTag placeholder="请选择人员"></sdc-staff-selector>
          <!-- <el-input style="width:240px;" v-model="hasData.staffName" placeholder="请输入员工英文名"></el-input> -->
        </el-form-item>
        <el-form-item label="">
          <sdc-unit-selector size="small" ref="unitSelector1" placeholder="请选择组织架构" @change="unitChange" v-model="unitValue" showFullTag />
          <!-- <el-input style="width:240px;" v-model="hasData.orgName" placeholder="请输入组织架构关键字"></el-input> -->
        </el-form-item>
        <el-form-item style="float:right;">
          <el-button icon="el-icon-refresh" size="small" @click="reset">重置</el-button>
          <el-button type="primary" size="small" @click="onSubmit" :dt-areaid="dtButton('area')" :dt-eid="dtButton('eid')" :dt-remark="dtButton('remark', '复制专属分享链接')">查询</el-button>
        </el-form-item>
      </el-form>
      <div class="select-tips-content" v-if="selectList.length">
        <div class="select-tips-item" v-for="(item, index) in selectList" :key="index">
          {{item.staff_name}} <i class="el-icon-close" @click="handelrCloseName(item)"></i>
        </div>
        <div class="select-tips-item" @click="handelrCloseName('all')">
          全部清除<i class="el-icon-close"></i>
        </div>
      </div>
      <el-table ref="multipleTable" :data="tableData" style="width: 100%;" @selection-change="handleSelectionChange" @select="handleSelect" class="custom-table-header" :header-cell-style="headerCellStyle">
        <el-table-column type="selection" width="55" :selectable="checkSelectable">
        </el-table-column>
        <el-table-column label="员工姓名" width="180">
          <template slot-scope="scope"> {{scope.row.staff_name}} </template>
        </el-table-column>
        <el-table-column prop="org_name" label="所属组织" width="349">
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope"> <span :style="canGetReward(scope.row).style">{{ canGetReward(scope.row).name }}</span> </template>
        </el-table-column>
      </el-table>
      <el-pagination class="pagination-coures-dialog" :pager-count="5" background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pagination.current" :page-sizes="[5, 10, 30, 50]" :page-size="pagination.size" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total">
      </el-pagination>
    </div>
    <span slot="footer" class="dialog-footer" v-if="giveType === 'xueba'">
      <el-button size="small" type="primary" :disabled="!selectList.length" @click="handelrGiveXueBaCard">{{giveType === 'account' ? '邀请学习' : '送出学霸卡'}} </el-button>
    </span>
  </el-dialog>
</template>

<script>
import {
  getActiveStaffs,
  activityPresent,
  getMobileQrcode
} from '@/config/mooc.api.conf.js'
const defaultData = {
  staffName: '',
  orgName: ''
}
export default {
  props: {
    width: {
      type: String,
      default: '856px'
    },
    top: {
      type: String,
      default: '6%'
    },
    giveType: {
      type: String,
      default: 'xueba'
    },
    isShow: {
      type: Boolean,
      default: false
    },
    xueBaCardConfig: {
      type: Object,
      default: () => {}
    },
    giveNumber: {
      type: Number
    },
    numberOfRewards: {
      type: Number
    },
    consumePoint: {
      type: Number
    },
    isGraphic: {
      type: Boolean,
      default: false
    },
    course_id: {
      type: [String, Number],
      default: ''
    }
  },
  data() {
    return {
      qrUrl: '',
      staffName: '',
      unitValue: '',
      pagination: {
        current: 1,
        size: 5,
        total: 0
      },
      hasData: Object.assign({}, defaultData),
      tableData: [],
      selectList: [],
      lastSelected: []
    }
  },
  mounted() {
    setTimeout(() => {
      this.getMobileCode()
    }, 1000)
  },
  watch: {
    'xueBaCardConfig.activity_id'(newvalue) {
      if (newvalue) {
        // this.getActiveStaffs('first')
        // this.getMobileCode()
      }
    }
  },
  computed: {
    dialogVisible: {
      set(val) {
        this.$emit('update:isShow', val)
      },
      get() {
        return this.isShow
      }
    },
    activityId() {
      return this.xueBaCardConfig.activity_id || this.$route.query.activityId
    },
    urlText() {
      let userInfo = JSON.parse(sessionStorage.getItem('login_user')) || {}
      console.log(userInfo, 'userInfouserInfo')
      let url = ''
      if (this.giveType === 'account') {
        url = this.isGraphic
          ? `https://sdc.qq.com/s/9bd9km?scheme_type=outsourced&resource_type=graphic&course_id=${this.course_id}`
          : `https://sdc.qq.com/s/PTUrrm?scheme_type=harvard&activityId=${this.activityId}`
      } else if (this.giveType === 'xueba') {
        url = `https://sdc.qq.com/s/hxbgLe?scheme_type=xueba&activityId=${this.activityId}`
      }
      // let url = this.giveType === 'account' ? 'https://sdc.qq.com/s/PTUrrm?scheme_type=harvard' : 'https://sdc.qq.com/s/hxbgLe?scheme_type=xueba'
      return `${url}&staff_name=${userInfo.staff_name}&staff_id=${userInfo.staff_id}`
    },
    headerCellStyle() {
      return {
        background: '#F5F5F5',
        color: '#********',
        fontSize: '14px',
        fontWeight: '400'
      }
    },
    canGetReward() {
      return (val) => {
        // 是否可以领取奖励can_get_reward, 是否可以赠送can_present
        const { can_get_reward, can_present } = val
        if (this.giveType === 'account') {
          if (can_present) {
            return { name: '可接受邀请', style: 'color: #00A870' }
          } else {
            return { name: '已有权限', style: 'color: #E34D59' }
          }
        } else {
          if (can_get_reward && can_present) {
            return {
              name: `送出后将获得 1 张「${this.xueBaCardConfig.card_name}」专用卡`,
              style: 'color: #00A870'
            }
          } else if (!can_present) {
            return { name: '受赠额度已满，不可赠送', style: 'color: #E34D59' }
          } else if (!can_get_reward) {
            return { name: '送出后无奖励', style: 'color: #E34D59' }
          }
        }
      }
    },
    // 埋点
    dtButton() {
      return (type, name) => {
        const { activity_id, audience_id, audience_name, card_name } =
          this.xueBaCardConfig
        if (type === 'area') {
          return `area_${audience_id}_${activity_id}`
        } else if (type === 'eid') {
          return `element_${audience_id}_${activity_id}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${card_name}活动首页`,
            page_type: `${card_name}活动首页`,
            container: audience_name,
            container_id: '',
            click_type: 'button',
            content_name: name,
            terminal: 'PC'
          })
        }
      }
    }
  },
  methods: {
    handelrCloseName(val) {
      if (val === 'all') {
        this.selectList = []
        this.$refs.multipleTable.clearSelection()
        return
      }
      this.selectList = this.selectList.filter(
        (item) => item.staff_id !== val.staff_id
      )
      this.$refs.multipleTable.toggleRowSelection(val, false)
    },
    handleSelect(selection, row) {
      console.log(selection, row, 'selectionselectionselection')
      // 如果selection是空数组说明当前行是取消勾选,总数据中需要对比row进行移除
      if (selection.length === 0) {
        this.selectList = this.selectList.filter(
          (item) => item.staff_id !== row.staff_id
        )
      } else {
        // 判断row.staff_id是否在selection里面如果不在说明是取消进行移除
        const isSelect = selection.some(
          (item) => item.staff_id === row.staff_id
        )
        if (!isSelect) {
          this.selectList = this.selectList.filter(
            (item) => item.staff_id !== row.staff_id
          )
        } else {
          // isSelect ：true 新增勾选
          this.selectList.push(row)
        }
      }
    },
    getMobileCode() {
      console.log('getMobileCode')
      let userInfo = JSON.parse(sessionStorage.getItem('login_user'))
      console.log(userInfo, 'userInfouserInfo')
      const params = {
        scene: `${this.activityId}_${userInfo.staff_id}_${userInfo.staff_name}_${this.xueBaCardConfig.acct_type_code}`,
        page: 'pages/webview/active/index',
        env_version: process.env.NODE_ENV === 'production' ? 'release' : 'trial'
      }
      getMobileQrcode(params).then((res) => {
        this.qrUrl = res ? `data:image/png;base64,${res}` : ''
      })
    },
    /* eslint-disable */
    handleCopyImg() {
      location.origin.includes(`https://`) ||
        this.$message.error(`图片复制功能需要在https://协议下使用`)
      let base64Data = this.qrUrl.split(';base64,')
      let type = base64Data[0].split('data:')[1]
      base64Data = base64Data[1]
      // 将base64转为Blob类型
      let bytes = atob(base64Data),
        ab = new ArrayBuffer(bytes.length),
        ua = new Uint8Array(ab)
      ;[...Array(bytes.length)].forEach((v, i) => (ua[i] = bytes.charCodeAt(i)))
      let blob = new Blob([ab], { type })
      // “navigator.clipboard.write”该方法的确只能在本地localhost 、127.0.0.1 或者 https 协议下使用，否则navigator没有clipboard方法。
      navigator.clipboard.write([new ClipboardItem({ [type]: blob })])
      this.$message.success('复制成功')
    },
    doCopy() {
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      input.value = this.urlText
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      this.$message.success(
        this.$langue('Mooc_Common_Alert_CopySucessed', {
          defaultText: '复制成功'
        })
      )
      // 复制后移除输入框
      input.remove()
    },
    async getActiveStaffs(first) {
      // const dept_full_name_last =
      //   this.$store.state.userDepInfo?.dept_full_name.split('/') || ''
      // const dept_id = this.$store.state.userDepInfo?.dept_id || ''
      // const full_name = this.$store.state.userDepInfo?.dept_full_name || ''
      // if (!this.hasData.orgName && first) {
      //   const initial = [{ UnitName: dept_full_name_last[dept_full_name_last.length - 1] , UnitID: dept_id, UnitFullName: full_name }]
      //   console.log(initial, 'initial')
      //   this.$nextTick(() => {
      //     this.$refs.unitSelector1.setSelected(initial)
      //   })
      //   console.log(dept_full_name_last, 'dept_full_name_lastdept_full_name_last')
      //   this.hasData.orgName = dept_full_name_last[dept_full_name_last.length - 1]
      //   console.log(this.hasData.orgName, 'this.hasData.orgName')
      // }
      const { staffName, orgName } = this.hasData
      let params = {
        current: this.pagination.current,
        size: this.pagination.size,
        activityId: this.activityId,
        staffName: staffName || '',
        orgName: orgName || ''
      }
      const res = await getActiveStaffs(params)
      this.tableData = res?.records || []
      this.$nextTick(() => {
        this.tableData.forEach((item) => {
          if (this.selectList.some((s) => s.staff_id === item.staff_id)) {
            console.log('111')
            this.$refs.multipleTable.toggleRowSelection(item, true)
          }
        })
      })
      this.pagination.total = res?.total || 0
      console.log(res, 'dadasdasda')
    },
    handlerOpen() {
      this.pagination.current = 1
      this.hasData.staffName = ''
      setTimeout(() => {
        this.$refs.staffSelect.clearSelected()
      }, 800)
      // this.getActiveStaffs('first')
    },
    handleSelectionChange(val) {},
    onSubmit() {
      if (!this.hasData.staffName && !this.hasData.orgName) {
        this.$message('请输入条件进行查询')
        return
      }
      this.pagination.current = 1
      this.getActiveStaffs()
    },
    reset() {
      this.$refs.staffSelect.clearSelected()
      this.$refs.unitSelector1.clearSelected()
      this.unitValue = ''
      this.pagination.current = 1
      this.hasData = Object.assign({}, defaultData)
      this.getActiveStaffs()
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.getActiveStaffs()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getActiveStaffs()
    },
    handelrGiveXueBaCard() {
      //  选中的要小于剩余的余额
      if (this.selectList.length > Number(this.consumePoint)) {
        let messageText =
          this.giveType === 'account'
            ? `超出可分享数量，当前剩余可分享 ${this.consumePoint} 次`
            : `超出可送出学霸卡数量，当前剩余可送出 ${this.consumePoint} 张学霸卡`
        this.$confirm(messageText, '提示', {
          confirmButtonText: '确定',
          showCancelButton: false
        })
      } else {
        const giveNumbers =
          this.selectList.filter(
            (item) => item.can_get_reward && item.can_present
          ) || []
        let RewardsNum = 0
        if (giveNumbers.length <= 3 - this.numberOfRewards) {
          RewardsNum = giveNumbers.length
        } else {
          RewardsNum = 3 - this.numberOfRewards
        }
        let names = this.selectList.map((item) => item.staff_name).join(';')
        let messagetext =
          this.giveType === 'account'
            ? `是否确认邀请 ${names}， ${this.selectList.length} 人分享学习`
            : `是否确认送出 ${this.selectList.length} 张学霸卡，送出后将获得劝学奖励 ${RewardsNum} 张「${this.xueBaCardConfig.card_name}」专用卡？`
        this.$confirm(messagetext, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          const userInfo = JSON.parse(sessionStorage.getItem('login_user'))
          let params = {
            from: userInfo.staff_id,
            from_name: userInfo.staff_name,
            acct_type_code: this.xueBaCardConfig.acct_type_code,
            to_batch: this.selectList.map((item) => item.staff_id),
            object_id: this.xueBaCardConfig.activity_id,
            object_name: this.xueBaCardConfig.activity_name,
            object_type: 'XuebaActivity',
            object_type_name: '活动',
            trans_amt: '1',
            notify_type: 1 // 手动赠送1 ，用户进入页面自动领的 0
          }
          activityPresent(params).then((res) => {
            let message = `成功送出学霸卡 ${res.success_count} 张，获得劝学奖励 ${res.reward_count} 张「${this.xueBaCardConfig.card_name}」专用卡；`
            let accountMsg = `邀请成功 ${res.success_count} 人,邀请信息将通过到企微Tips和机器人消息推送至受邀人；`
            if (Number(res.fail_count) > 0) {
              message = `${message}\n送出失败${res.fail_count} 张，失败人员：${
                res.fail_names || '-'
              }`
              accountMsg = `${accountMsg}\n邀请失败${
                res.fail_count
              }人，失败人员：${res.fail_names || '-'}`
            }
            this.$message({
              type: res.success_count ? 'success' : 'warning',
              dangerouslyUseHTMLString: true,
              message: this.giveType === 'account' ? accountMsg : message
            })
            this.$emit('update:isShow', false)
            this.$emit('handlerGiveXuebaka')
            this.handelrCloseName('all') // 清空全部选中
            this.getActiveStaffs()
          })
        })
      }
    },
    checkSelectable(row) {
      return row.can_present
    },
    staffChange(val) {
      this.hasData.staffName = val.StaffName
      console.log(val, 'renyuan')
    },
    unitChange(val) {
      const { UnitFullName } = val
      this.hasData.orgName = UnitFullName
      console.log(val, 'unitChange')
    }
  }
}
</script>
<style lang="less">
.content-xueba {
  padding: 0 32px;
  .content-xueba-title {
    color: #000000e6;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    padding: 16px 0;
  }
  .el-button--default {
    background-color: #fff;
    color: #0052d9;
    border: 1px solid #dcdcdc;
  }
  .el-button--default:hover {
    background-color: #fff;
    color: #0052d9;
    border: 1px solid #0052d9;
  }
}
</style>
<style lang="less" scoped>
.give-course-dialog {
  font-family: 'PingFang SC';
  .share-box {
    display: flex;
    border-bottom: 1px solid #e7e7e7;
    padding: 0 32px 24px;
    .share-rq-code {
      width: 88px;
      height: 88px;
      background: #f5f5f5;
      border-radius: 6px;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 72px;
        height: 72px;
        border-radius: 6px;
      }
    }
    .share-url-box {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      margin-left: 16px;
      flex: 1;
      .url-text {
        display: flex;
      }
    }
    .copy-btn {
      color: #0052d9;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 32px;
      cursor: pointer;
      width: 180px;
    }
  }
  .share-box_account {
    border-bottom: none;
  }
  /deep/.el-dialog {
    border-radius: 8px;
  }
  /deep/.el-dialog__body {
    padding: 16px 0 24px 0;
  }
  /deep/.el-dialog__header {
    padding: 24px 32px 16px;
    border-bottom: unset;
  }
  /deep/.el-dialog__headerbtn {
    top: 26px;
  }
  /deep/.el-dialog__footer {
    padding: 0 32px 32px;
  }
  .title-tips {
    font-size: 14px;
    color: #********;
    margin-left: 20px;
  }
  .tips_b {
    color: #0052d9;
  }
}
.url_link {
  color: #0052d9;
}
.custom-table-header {
  border: 1px solid #f5f5f5;
  border-bottom: 0px;
  border-radius: 4px;
}
.has-form-inline {
  background: #f8f8f8;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  /deep/.el-form-item {
    margin-bottom: 0px;
  }
  /deep/.sdc-staff-selector {
    width: 240px;
  }
  /deep/.sdc-unit-selector {
    width: 240px;
  }
}
.pagination-coures-dialog {
  position: relative;
  /deep/.el-input__inner {
    height: 32px;
    border: 1px solid var(--Gray-Gray4-, #dcdcdc);
  }
  /deep/ .el-pagination__sizes input {
    height: 32px !important;
    line-height: 32px;
  }
  /deep/.el-input__icon {
    line-height: 20px !important;
  }
  /deep/.el-pagination__sizes {
    height: 32px !important;
    /deep/.el-input {
      width: 96px !important;
      height: 32px !important;
    }

    /deep/.btn-prev {
      border: none;
    }
  }
  /deep/.el-pager {
    .number,
    .more {
      width: 32px;
      height: 32px !important;
      border: 1px solid var(--Gray-Gray4-, #dcdcdc);
      background: var(--Gray-White, #fff);
      text-align: center;
      line-height: 32px;
    }
  }
  /deep/.el-pagination.is-background .btn-next,
  /deep/.el-pagination.is-background .btn-prev,
  /deep/ .el-pagination.is-background .el-pager li {
    margin: 0 8px;
  }
  /deep/.el-pagination__jump {
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
    background: #f3f3f3;
    border-radius: 3px;
  }
}
/deep/.el-pagination .el-pagination__total {
  height: 32px !important;
  line-height: 32px !important;
  margin-right: 136px !important;
  color: #******** !important;
  position: absolute;
  left: 0;
}
/deep/.el-pagination .el-pager .number.active {
  background: #0052d9 !important;
  color: #fff !important;
  border-color: #0052d9 !important;
}
/deep/.el-pagination.is-background .btn-next,
/deep/.el-pagination.is-background .btn-prev,
/deep/.el-pagination.is-background .el-icon-more,
/deep/.el-pagination.is-background .el-icon-d-arrow-right {
  background-color: #fff !important;
  border: none;
}
/deep/.el-pagination .el-pagination__jump input {
  height: 28px !important;
}
.select-tips-content {
  display: flex;
  flex-wrap: wrap;
  margin: 10px 0;

  .select-tips-item {
    background-color: #f2f2f2;
    border-color: #eee;
    color: #666;
    cursor: pointer;
    font-size: 14px;
    height: 28px;
    line-height: 28px;
    padding: 0 8px;
    border-radius: 4px;
    margin: 0 8px 8px 0;
  }
}
</style>
