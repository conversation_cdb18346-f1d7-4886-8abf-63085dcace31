<template>
  <div class="sort-dialog">
    <el-dialog 
      title="" 
      :visible.sync="visible"
       width="420px" 
      :close-on-click-modal="false"
      :before-close="closeDialog"
      >
        <div class="dialog-body">
          <el-form ref="form" :model="form" :rules="rules" label-width="90px">
            <el-form-item label="排列至第" prop="order_no">
              <el-input-number v-model="form.order_no" controls-position="right" :min="1" ></el-input-number>
              <span style="margin-left: 10px;">位</span>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="handleSave">确定</el-button>
        </div>
      </el-dialog>
  </div>
</template>
<script>
import { bannerSortingApi } from '@/config/mooc.api.conf'
export default {
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    
  },
  data() {
    return {
      form: {
        id: '',
        order_no: null
      },
      rules: {
        order_no: [{ required: true, message: '请输入排序', trigger: 'blur' }]
      }
    }
  },
  mounted() {
  },
  methods: {
    initData(data) {
      this.form.id = data.id
      this.form.order_no = Number(data.order_no)
    },
    // 保存
    handleSave() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          bannerSortingApi({ ...this.form }).then(res => {
            this.$message.success('操作成功')
            this.closeDialog()
            this.$emit('onSearch')
          })
        } else {
          return false
        }
      })
    },
    closeDialog() {
      this.$refs.form.clearValidate()
      this.form = {
        type: '',
        typeVal: '',
        target: []
      }
      this.$emit('update:visible', false)
      this.$emit('close')
    }
  }
}
</script>
<style lang="less" scoped>
.sort-dialog {
  :deep(.el-dialog) {
    .el-dialog__header {
      border-bottom: 0;
    }
    .el-dialog__body {
      padding: 0;
      .el-dialog__footer {
        padding: 8px 0 24px;
      }
    }
  }
  .dialog-body {
    padding: 10px 32px 0;
  }
}
</style>
