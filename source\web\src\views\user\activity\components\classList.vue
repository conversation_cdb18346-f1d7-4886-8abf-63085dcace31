<template>
  <div class="class-list">
    <div class="date-select" :class="isMoreDate ? 'date-select-more' : 'date-select-less' ">
      <div :class="['date-item', {'date-item-current': dateCurrent === item.formatDate }]" v-for="(item, index) in partClassDateSelect" :key="index" @click="handlerClickItem(item)"> {{item.formatDate}} </div>
      <div class="date-item moer-date-btn" @click="moreDate" v-if="uniqueList > widthSize">{{ isMoreDate ? '...更多日期' : '收起更多日期' }} </div>
    </div>
    <div :class="['class-content', { 'class-content-moer': showMoerList }]" v-if="dateListFilter.length">
      <div class="class-content-item" v-for="(item, index) in dateListFilter" :key="index" :dt-eid="dtTableList('eid', item)" :dt-areaid="dtTableList('area', item)" :dt-remark="dtTableList('remark', item)">
        <div class="item-title">{{item.class_name}}</div>
        <div class="item-tips">
          <span v-if="formate1(item.start_time)">夜校班</span>
          <span>{{item.teaching_type === 1 ? '线下授课' : '在线授课'}}</span>
          <span v-if="item.need_appovel">需上级审批</span>
        </div>
        <div class="item-class-info">
          <div class="item-class-info_left">
            <div class="info-lable"><i class="icon icon-time"></i> {{item.start_end_time}}</div>
            <div class="info-lable"><i class="icon icon-name"></i> {{item.inner_teacher_names}}</div>
            <div class="info-lable"><i class="icon icon-city"></i> {{teachingType(item)}} </div>
          </div>
          <div class="item-class-info_right">
            <!-- 可报名 -->
            <el-tooltip class="item" effect="dark" content="已有报名班级" placement="top" v-if="statusBtn(item) === 1 && surplus_count(item) !== 0" :disabled="!registered">
              <div :class="['status-button', 'status1', {'registered-btn': registered }]" @click="handlerApply(item)" :dt-eid="dtTable('eid', '报名', item)" :dt-areaid="dtTable('area', '报名', item)" :dt-remark="dtTable('remark', '报名', item)">
                <span class="button-text">报名</span>
                <span class="button-info">剩余名额 {{surplus_count(item)}}</span>
              </div>
            </el-tooltip>
            <!-- 加入等待列表 -->
            <el-tooltip class="item" effect="dark" content="已有报名班级" placement="top" v-else-if="statusBtn(item) === 2 ||  (statusBtn(item) === 1 && surplus_count(item) === 0) " :disabled="!registered">
              <div :class="['status-button', 'status1', {'registered-btn': registered }]" @click="handlerApply(item)">
                <span class="button-text">加入等待列表</span>
                <span class="button-info">排队人数 {{wait_queen_count(item)}}</span>
              </div>
            </el-tooltip>
            <!-- 已加入等待列表 -->
            <el-tooltip v-else-if="statusBtn(item) === 3" class="item" effect="dark" content="点击取消报名" placement="top">
              <div class="status-button status2" @click="handelrOpenLogoOut(item)">
                <span class="button-text">已加入等待列表</span>
                <span class="button-info">排队人数 {{wait_queen_count(item)}}</span>
              </div>
            </el-tooltip>
            <!-- 截止报名  -->
            <el-tooltip v-else-if="statusBtn(item) === 4" class="item" effect="dark" content="取消开班提醒" placement="top" :disabled="courseData.is_subscribed === 0">
              <div class="status-button status2" @click="handlerSetSubscribe">
                <span class="button-text">{{courseData.is_subscribed === 1 ? '已订阅开班提醒' : '开班提醒'}} </span>
                <span class="button-info" v-if="statusBtn(item) === 4">已截止报名</span>
              </div>
            </el-tooltip>
            <!-- 已报名取消报名 -->
            <div class="status-button status2" v-else-if="statusBtn(item) === 5" @click="handelrOpenLogoOut(item)" :dt-eid="dtTable('eid', '取消报名', item)" :dt-areaid="dtTable('area', '取消报名', item)" :dt-remark="dtTable('remark', '取消报名', item)">
              <span class="button-text">已报名</span>
              <span class="button-info">点击可取消报名</span>
            </div>
            <!-- 已报名不可取消报名 -->
            <div class="status-button status2" v-else-if="statusBtn(item) === 6">
              <span class="button-text">已报名</span>
              <span class="button-info">已超过注销截止时间</span>
            </div>
            <!-- 已报名审核中 -->
            <div class="status-button status2" v-else-if="statusBtn(item) === 7">
              <span class="button-text">已报名</span>
              <span class="button-info">待上级审批</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <Empty v-else :emptyShow="emptyShow" emptyTips="暂无排班"  />
    <div class="show-more-brn" v-show="dateListFilter.length> 2" @click="clickMoer">{{!showMoerList ? '展开查看更多...' : '收起' }}</div>
    <!-- 报名弹窗 -->
    <el-dialog title="Q-Learning课程报名" :visible.sync="dialogVisibleApply" width="660px" class="logo-out-dialog face-dialog">
      <div class="apply-dialog face-dialog-flex">
        <div class="left">
          <i class="el-icon-warning"></i>
        </div>
        <div class="right">
          <div v-if="!is_need_idp_score">培训公约：</div>
          <div v-else>
            <span>为保证学习效果，建议每季度报名IDP课程不超过<strong style="color:red">5</strong>门</span>
            <span v-show="currentClassItem.idp_courses_count">，您现在已报名<strong style='color:red'>{{currentClassItem.idp_courses_count}}</strong>门。</span>
          </div>
          <div>
            <ol>
              <li v-if="showCity">
                此开课地点为<span class="red-fw">【{{this.currentClassItem.city}}】</span>
                <span v-show="currentClassItem.need_appovel">，报名需直接上司的审批;</span>
              </li>
              <li v-else-if="!showCity && this.currentClassItem.need_appovel">报名需直接上司的审批;</li>
              <li>报名成功后请准时、全程参加培训，珍惜培训资源;</li>
              <li>若未准时、全程参加培训达4次，将影响下季度报名公司级课程的权限;</li>
              <li v-if="is_need_idp_score" class="red-fw">报名所需积分：<span>{{pointsRange}}</span> </li>
              <li v-if="currentClassItem.surplus_count === 0">本班报名已超过名额，你报名后将进入等待列表，你的前面还有{{wait_queen_countCur}}人在排队，后续等待邮件通知</li>
            </ol>
          </div>
          <!--积分相关提示  -->
          <div class="apply-type" v-if="is_need_idp_score">
            <p class="p0" v-if="!curr_class.isEnough"><span style="line-height:18px;padding-left:2px;"><i style="color:#e6a23c;font-size:14px;" class="el-icon-warning"></i> 积分不足</span></p>
            <p class="p1">当前可用积分：<strong>{{curr_class.surplus}}</strong></p>
            <p class="p1">第{{curr_class.quater}}季度专项课程报名积分为 <strong>{{curr_class.idp_point}}</strong>
              <!-- 分数足够抵扣 -->
              <span v-if="curr_class.isEnough">，本次报名将<strong class="red-fw">扣除{{pointsRange}}</strong><strong class="red-fw" v-html="curr_class.idp_point < curr_class.amt?'通用积分':'专项课程报名积分'"></strong></span>
            </p>
            <p class="p1">
              <span class="details-sapn"><a href="//portal.learn.woa.com/point/user/index" target="_blank">积分明细 ></a></span>
              <span class="details-sapn">
                <a :href="is_need_idp_score ? 'https://km.tencent.com/openkm/url/tn4yw3' : 'https://km.tencent.com/openkm/url/ksaldg' " target="_blank">积分消费说明></a>
              </span>
              <span><a href="//portal.learn.woa.com/point/user/rule" target="_blank">如何获取积分?</a></span>
            </p>
          </div>
          <div class="comfir-tips">
            请确认是否继续提交报名？
          </div>
          <!-- 上级审核 -->
          <div class="manage" v-if="currentClassItem.need_appovel">
            <p class="title">课程：{{courseData.course_name}}</p>
            <div class="managerName"><i style="color: red;margin-right:3px">* </i> 直接上级：
              <sdc-staff-selector ref="selectorStaff" v-model="staffId" size="medium" selectClass="selectClass" modalClass="modalClass" showFullTag @change="handlerChangeStaff"></sdc-staff-selector>
            </div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisibleApply = false" size="small">取 消</el-button>
        <el-button size="small" type="primary" :disabled="!curr_class.isEnough && is_need_idp_score" @click="handelrConfirmApply">确 定</el-button>
      </span>
    </el-dialog>
    <!-- 注销 -->
    <el-dialog title="Q-Learning课程注销" :visible.sync="dialogVisibleLogoOut" width="520px" class="logo-out-dialog face-dialog">
      <div class="face-dialog-flex">
        <div class="left">
          <i class="el-icon-warning"></i>
        </div>
        <div class="right">
          <div> 注销课程信息及注销原因将邮件知会到您的直属领导，请珍惜培训资源。</div>
          <div class="Reason-sel">请选择/输入注销原因</div>
          <div v-for="item in logoOutReason" :key="item.id" class="redio-content">
            <el-radio v-model="reasonRadio" :label="item.id">{{item.reason}}</el-radio>
          </div>
          <el-input v-show="[0, 3].includes(reasonRadio)" type="textarea" :rows="4" placeholder="请输入注销原因" v-model="logoOutReason[reasonRadio].other">
          </el-input>
          <template v-if="is_need_idp_score">
            <div class="label_form" style="color:#FF8200">
              <i class="el-icon-warning"></i>注销成功后，报名课程消耗的积分将退还至个人积分账户
            </div>
          </template>
        </div>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisibleLogoOut = false">取 消</el-button>
        <el-button size="small" type="primary" @click="handelrLogoOut">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// import elementResizeDetectorMaker from 'element-resize-detector'
import {
  getClassStatusItem,
  logoOutFace,
  getFaceCancelAcct,
  faceClassRegistApply
} from 'config/api.conf'
import Empty from '@/views/user/components/empty.vue'
import { mapState } from 'vuex'
export default {
  name: 'classList',
  props: ['actClassListArr', 'currentCity', 'courseData', 'registered'],
  data() {
    return {
      widthSize: 4,
      currentClassItem: {},
      dateCurrent: '全部',
      dateListFilter: [],
      isMoreDate: true,
      dialogVisibleLogoOut: false,
      dialogVisibleApply: false,
      showMoerList: false,
      reasonRadio: 0,
      partClassDateSelect: [],

      parentStaffInfo: {
        StaffID: '',
        StaffName: ''
      },
      staffId: '',
      logoOutReason: [
        {
          id: 0,
          reason: '与重要工作冲突(请输入此工作具体信息)',
          other: ''
        },
        {
          id: 1,
          reason: '临时有出差',
          other: ''
        },
        {
          id: 2,
          reason: '休假,不在公司',
          other: ''
        },
        {
          id: 3,
          reason: '其他',
          other: ''
        }
      ],
      // 当前班级相关信息
      curr_class: {
        quater: 0, // 所属季度
        amt: 0, // 报名所需分数
        idp_point: 0, // 所属季度idp分数
        isEnough: true, // 分数是否足够抵扣
        surplus: 0, // 所属季度可用积分
        is_ldi_course: false, // 是否是ldi课程
        class_level: 0 // 课程级别 1=>公司级,2=>部门级,3=>bg级
      },
      // IDP账号积分数据
      grantAccountData: []
    }
  },
  components: {
    Empty
  },
  watch: {
    // 监听宽度变化显示时间个数
    widthSize(val) {
      let dateList = this.uniqueDateList(this.classDateSelect)
      this.partClassDateSelect = dateList.slice(0, val)
    },
    classDateSelect(val) {
      if (val) {
        console.log(
          val,
          'classDateSelectclassDateSelectclassDateSelectclassDateSelect'
        )
        // 去重
        let dateList = this.uniqueDateList(val)
        console.log(dateList, 'dateListdateList')
        if (val.length > 4) {
          this.partClassDateSelect = dateList.slice(0, this.widthSize)
          this.isMoreDate = true
        } else {
          this.partClassDateSelect = dateList
        }
      }
    },
    actClassListArr(val) {
      this.dateListFilter = val
    },
    currentCity(val) {
      this.dateCurrent = '全部'
      // if (val) {
      //   if (val.includes('全部')) {
      //     this.dateListFilter = this.actClassListArr
      //   } else {
      //     this.dateListFilter = this.actClassListArr.filter((item) => item.city === val)
      //   }
      // }
    },
    dateCurrent(val) {
      if (val) {
        if (val.includes('全部')) {
          this.dateListFilter = this.actClassListArr
        } else {
          this.dateListFilter = this.actClassListArr.filter(
            (item) => item.formatDate === val
          )
        }
      }
    }
  },
  created() {
    this.dateListFilter = this.actClassListArr
  },
  mounted() {
    this.widthSize = document.body.clientWidth <= 1660 ? 4 : 5
    this.getFaceCancelAcct()
    // 监听显示更多按钮
    // 添加窗口大小改变监听器
    window.addEventListener('resize', () => {
      console.log(document.body.clientWidth, 'document.body.clientWidth')
      this.widthSize = document.body.clientWidth <= 1660 ? 4 : 5
    })
  },
  computed: {
    ...mapState({
      userInfo: (state) => state.userInfo
    }),
    emptyShow() {
      return !this.dateListFilter.length
    },
    teachingType() {
      return (val) => {
        const { teaching_type, location, city } = val
        // 1 线下面试 2 在线授课 3 网络研讨会',
        const typeObj = {
          1: location || city,
          2: '腾讯会议在线授课，报名成功后自动推送授课链接',
          3: '腾讯会议在线授课，报名成功后自动推送授课链接'
        }
        return typeObj[teaching_type]
      }
    },
    // 按钮状态 1-可报名 2-已报满-等待列表 3-已加入等待列表 4-截至报名 5-已报名-可注销 6-已报名-不可注销
    statusBtn() {
      return (val) => {
        const { status } = val.regist_type
        return status || 1
      }
    },
    // 剩余名额
    surplus_count() {
      return (val) => {
        const { surplus_count } = val.regist_type
        return surplus_count || 0
      }
    },
    //  等待人数
    wait_queen_count() {
      return (val) => {
        const { wait_queen_count } = val.regist_type
        return wait_queen_count || 0
      }
    },
    // 当前项等待人数
    wait_queen_countCur() {
      let wait_queen_count = 0
      if (this.currentClassItem.regist_type) {
        wait_queen_count = this.currentClassItem.regist_type.wait_queen_count
      }
      return wait_queen_count || 0
    },
    // 报名需要分数
    pointsRange() {
      let est_dur = 200
      if (this.currentClassItem.est_dur) {
        est_dur = this.currentClassItem.est_dur / 60 <= 3.5 ? 200 : 400
      }
      return est_dur
    },
    classDateSelect() {
      if (!this.actClassListArr.length) {
        return []
      }
      return [{ date: '全部', formatDate: '全部' }, ...this.actClassListArr]
    },
    uniqueList() {
      let uniqueNumber = this.uniqueDateList(this.classDateSelect)
      return uniqueNumber.length
    },
    formate1() {
      return (val) => {
        val = new Date(val)
        let h = val.getHours()
        let ms = val.getMinutes()
        // 判断时间大于18:45
        if (h > 18 || (h === 18 && ms >= 45)) {
          return true
        } else {
          return false
        }
      }
    },
    is_need_idp_score() {
      return this.currentClassItem.is_need_idp_score
    },
    showCity() {
      const { city, work_city, teaching_type } = this.currentClassItem
      return city !== work_city && teaching_type === 1
    },
    dtTableList() {
      return (type, item) => {
        if (type === 'eid') {
          return `element_${this.course_id}_${item.class_id}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '面授课详情页-新版',
            container: '',
            click_type: 'data',
            content_type: '班级',
            content_id: item.class_id,
            content_name: item.class_name,
            terminal: 'PC'
          })
        } else {
          return `area_${this.course_id}_${item.class_id}`
        }
      }
    }
  },
  methods: {
    // 去重时间筛选
    uniqueDateList(arr) {
      let res = arr.reduce((result, obj) => {
        if (
          !result.some(
            (item) =>
              JSON.stringify(item.formatDate) === JSON.stringify(obj.formatDate)
          )
        ) {
          result.push(obj)
        }
        return result
      }, [])
      return res
    },
    moreDate() {
      let resList = this.uniqueDateList(this.classDateSelect)
      if (this.isMoreDate) {
        this.partClassDateSelect = resList
      } else {
        this.partClassDateSelect = resList.slice(0, this.widthSize)
      }
      this.isMoreDate = !this.isMoreDate
    },
    handlerClickItem(val) {
      this.dateCurrent = val.formatDate
    },
    formatDateTime(date) {
      const dateTime = new Date(date)
      const formattedDate = this.$moment(dateTime).format('YYYY/MM/DD')
      const getDay = dateTime.getDay()
      const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'] // 获取星期信息
      return `${formattedDate}（${weekday[getDay]}）` // 组合日期和星期信息并返回
    },
    handelrOpenLogoOut(val) {
      this.dialogVisibleLogoOut = true
      this.currentClassItem = val
    },
    clickMoer() {
      this.showMoerList = !this.showMoerList
    },
    async getFaceCancelAcct() {
      try {
        this.grantAccountData = await getFaceCancelAcct()
      } catch (err) {
        this.$message.error(err)
      }
    },
    handlerChangeStaff(val) {
      const { StaffID, StaffName } = val
      this.parentStaffInfo = { StaffID, StaffName }
      console.log(val, '改变人员信息')
    },
    // 初始化当前班级信息
    currClassInfo(data) {
      let date_saf = data.start_time
      date_saf = date_saf.replace(/-/g, '/')
      let date = new Date(date_saf)

      let year = date.getFullYear(date)
      let m = date.getMonth(date) + 1
      let quater = Math.ceil(parseInt(m) / 3)
      let curr_quater = year + 'S' + quater
      let surplus = 0
      let amt = data.est_dur / 60 <= 3.5 ? 200 : 400

      // 可用积分 = 当前季度idp专项积分>0 ?当季idp积分+通用积分:通用积分
      const idpAccount = this.grantAccountData.filter(
        (item) => item.type === 'idp'
      )[0] // 当前季度idp专项积分
      console.log(idpAccount, 'idpAccountidpAccountidpAccountidpAccount')
      const commonAccount = this.grantAccountData.filter(
        (item) => item.type === 'common'
      )[0] // 通用积分
      console.log(commonAccount, 'commonAccountcommonAccountcommonAccount')
      surplus = commonAccount.point_total
      if (
        idpAccount?.point_season &&
        idpAccount?.point_season[curr_quater] > 0
      ) {
        surplus =
          idpAccount.point_season[curr_quater] + commonAccount.point_total
      }
      this.curr_class.quater = quater
      this.curr_class.amt = amt
      this.curr_class.idp_point = idpAccount?.point_season[curr_quater] || 0
      this.curr_class.isEnough = surplus >= amt
      this.curr_class.surplus = surplus
      this.curr_class.is_ldi_course =
        data.pdi_sub_level === 2 && data.class_level === 1
      this.curr_class.class_level = data.class_level
    },
    // 处理报名逻辑
    handlerApply(val) {
      if (this.registered) return
      this.currentClassItem = val
      this.dialogVisibleApply = true
      const {
        is_need_idp_score,
        idp_courses_count,
        black_list,
        need_appovel,
        parent_name,
        parent_staff_id
      } = val
      if (is_need_idp_score) {
        // 提示考勤异常累积达4次
        if (Number(black_list) === 1) {
          let nowDate = new Date().toLocaleDateString()
          this.$confirm(
            `1、为了更有效地利用培训资源，员工培训考勤异常累积达4次，下季度将限制报名公司级课程；<br />2、截至 ${nowDate},您培训考勤异常次数已达4次，本季度不能报名公司级课程，下季度恢复报名权限。<br />如有疑问请咨询v_shannwang(王阳) !`,
            `提示`,
            {
              confirmButtonText: '确定',
              customClass: 'confirm-center-face',
              showCancelButton: false,
              dangerouslyUseHTMLString: true,
              type: 'warning',
              center: true
            }
          ).then(() => {})
          return false
        } else {
          if (idp_courses_count >= 5) {
            this.$confirm(
              `为保证学习效果，建议每季度报名IDP课程不超过<strong style="color:red">5</strong>门，您现在已报名<strong style="color:red">5</strong>门，请下季度再报名其他课程！`,
              `提示`,
              {
                confirmButtonText: '确定',
                customClass: 'confirm-center-face',
                showCancelButton: false,
                dangerouslyUseHTMLString: true,
                type: 'warning',
                center: true
              }
            ).then(() => {})
            return false
          }
        }
      }
      // 如果需要审批
      if (need_appovel) {
        this.parentStaffInfo.StaffID = parent_staff_id
        this.parentStaffInfo.StaffName = parent_name
        if (parent_staff_id) {
          setTimeout(() => {
            this.$refs.selectorStaff.setSelected(this.parentStaffInfo)
          }, 400)
        }
      }
      this.currClassInfo(val)
    },
    handelrConfirmApply() {
      const { need_appovel, class_id } = this.currentClassItem
      let params = {
        class_id: class_id
      }
      // 需要审核
      if (need_appovel) {
        if (!this.staffId) {
          this.$message.warning('请输入直接上级!')
          return false
        } else if (this.staffId + '' === this.userInfo.staff_id + '') {
          this.$message.warning('直接上级不能为自己!')
          return false
        }
        params.leader = {
          user_name: this.parentStaffInfo.StaffName,
          user_id: this.parentStaffInfo.StaffID
        }
      }
      faceClassRegistApply(params).then((res) => {
        console.log(res, '成功报名')
        let message = this.currentClassItem.need_appovel
          ? '提交成功,该记录正等待直接上司的审批, 请留意邮件提醒'
          : '报名成功'
        if (this.currentClassItem.regist_type.status === 2) {
          this.$message.success('成功报名加入等待列表')
        } else {
          this.$message.success(message)
        }
        this.$parent.actClassList()
        this.dialogVisibleApply = false
        // this.refurbishStatus()
      })
    },
    // 注销
    handelrLogoOut() {
      if (
        [0, 3].includes(this.reasonRadio) &&
        !this.logoOutReason[this.reasonRadio].other.trim()
      ) {
        this.$message('请输入注销原因')
        return false
      }
      let parmas = {
        class_id: this.currentClassItem.class_id,
        reason: `${this.logoOutReason[this.reasonRadio].reason}${
          this.logoOutReason[this.reasonRadio].other
        }`
      }
      logoOutFace(parmas).then((res) => {
        this.dialogVisibleLogoOut = false
        // 需要积分打开积分弹出提示
        if (this.is_need_idp_score) {
          let htmlMsg = `<p>报名课程消耗的${this.pointsRange}积分已退还个人积分账户</p> <a href="//portal.learn.woa.com/point/user/index" target="_blank" style="color:#0052d9;cursor:pointer" >查看积分余额</a> `
          this.$confirm(htmlMsg, '注销成功', {
            confirmButtonText: '确定',
            dangerouslyUseHTMLString: true,
            showCancelButton: false,
            type: 'success'
          })
        } else {
          this.$message.success('注销成功')
          // this.refurbishStatus()
        }
        this.$parent.actClassList()
      })
    },
    // 刷新状态
    refurbishStatus() {
      getClassStatusItem({ class_id: this.currentClassItem.class_id }).then(
        (res) => {
          this.dateListFilter.forEach((item) => {
            if (item.class_id === this.currentClassItem.class_id) {
              item.regist_type = res
            }
          })
          console.log('获取class的状态', res)
        }
      )
    },
    handlerSetSubscribe() {
      this.$parent.handlerSetSubscribe()
    },
    dtTable() {
      return (type, name, item) => {
        if (type === 'eid') {
          return `element_${item ? item.class_id : ''}_${name}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: this.classInfo.course_name,
            page_type: '面授课详情页-新版',
            container: '',
            click_type: 'button',
            content_type: '班级',
            content_id: item ? item.class_id : '',
            content_name: item ? item.class_name : '',
            button_name: name,
            terminal: 'PC'
          })
        } else {
          return `area_${item ? item.class_id : ''}_${name}`
        }
      }
    }
  }
}
</script>
<style lang="less">
.confirm-center-face {
  .el-message-box__header {
    padding-top: 10px;
  }
  .el-message-box__content {
    padding: 16px 0;
    text-align: left;
  }
}
.face-dialog {
  .el-dialog {
    border-radius: 8px;
  }
  .el-dialog__header {
    border-bottom: none;
    padding: 32px 32px 0;
  }
  .el-dialog__headerbtn {
    top: 30px;
    right: 30px;
  }
  .el-dialog__body {
    padding: 16px 32px;
  }
  .el-dialog__footer {
    padding: 0 32px 32px;
  }
}
</style>
<style lang="less" scoped>
.class-list {
  background-color: #fff;
  padding: 16px 24px 15px;
  border-radius: 0 0 8px 8px;
  .date-select {
    display: flex;
    flex-wrap: wrap;
    position: relative;
    .date-item {
      background: #F5F9FF;
      color: #333333;
      padding: 0 12px;
      margin: 0 14px 14px 0;
      border-radius: 4px;
      height: 28px;
      line-height: 28px;
      display: -webkit-box;
      display: -ms-flexbox;
      display: flex;
      -webkit-box-align: center;
      -ms-flex-align: center;
      align-items: center;
      cursor: pointer;
    }
    .date-item:hover {
      background: #ECF2FE;
      color: #0052D9;
    }
    .date-item-current {
      background: #ECF2FE;
      color: #0052D9;
    }
    .moer-date-btn {
      // position: absolute;
      // right: 0;
    }
  }
  .date-select-more {
    // width: 712px;
    // height: 28px;
    // overflow: hidden;
  }
  .class-content {
    max-height: 429px;
    overflow: hidden;
    &-item {
      padding: 20px 0;
      border-bottom: 1px solid #eee;
      .item-title {
        color: #333333;
        font-family: 'PingFang SC';
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: 24px;
      }
      .item-tips {
        margin: 10px 0;
        span {
          padding: 2px 6px;
          font-size: 12px;
          line-height: 18px;
          color: #777;
          margin-right: 8px;
          border-radius: 4px;
          background: #f5f7fa;
        }
      }

      .item-class-info {
        padding: 10px 18px 10px 12px;
        border-radius: 8px;
        background: #f8f8f8;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &_left {
          .info-lable {
            display: flex;
            align-items: center;
            line-height: 22px;
            font-size: 14px;
            margin-bottom: 10px;
            .icon {
              display: block;
              width: 24px;
              height: 24px;
              margin-right: 12px;
            }
            .icon-time {
              background: url('~@/assets/img/time-class.png') no-repeat center /
                cover;
            }
            .icon-name {
              background: url('~@/assets/img/teacher-class.png') no-repeat
                center / cover;
            }
            .icon-city {
              background: url('~@/assets/img/local-class.png') no-repeat center /
                cover;
            }
          }
          .info-lable:last-child {
            margin-bottom: 0;
          }
        }
        &_right {
          .status-button {
            width: 140px;
            height: 72px;
            border-radius: 4px;
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            cursor: pointer;
            .button-text {
              font-size: 14px;
              font-style: normal;
              font-weight: 500;
              line-height: 22px;
              margin-bottom: 4px;
            }
            .button-info {
              font-family: 'PingFang SC';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 16px;
            }
          }
          .status1 {
            background-color: #0052d9;
            color: #fff;
          }
          .status2 {
            color: #333333;
            background-color: #fff;
            border-color: #fff;
            .button-info {
              color: #777777;
            }
          }
          .status3 {
            color: #bcbec2;
            background-color: #c8c9cc;
            border-color: #c8c9cc;
            cursor: not-allowed;
          }
          .status4 {
            color: #fff;
            background-color: #f56c6c;
          }
          .registered-btn {
            color: #fff;
            background-color: #c8c9cc;
            border-color: #c8c9cc;
            cursor: not-allowed;
          }
        }
      }
      .item-class-info:hover {
        background: linear-gradient(90deg, #f9f9f9 0%, #f1f5fd 97.29%);
      }
    }
    &-item:last-child {
      // border-bottom: none;
    }
  }
  .show-more-brn {
    text-align: center;
    color: #0052d9;
    line-height: 22px;
    margin-top: 15px;
    cursor: pointer;
  }
  .class-content-moer {
    max-height: unset;
  }
}

.logo-out-dialog {
  .red-fw {
    color: #fe3733;
    font-weight: 600;
  }
  .face-dialog-flex {
    display: flex;
    .el-icon-warning {
      color: #e6a23c;
      font-size: 24px;
      margin-right: 16px;
      margin-top: 3px;
    }
  }
  .apply-dialog {
    line-height: 28px;
    font-size: 16px;
    color: #00000099;
    .right {
      flex: 1;
    }
    ol {
      list-style: decimal;
      padding-left: 20px;
      li {
        list-style: decimal;
      }
    }
    .manage {
      width: 100%;
      border-top: 1px solid #eee;
      .title {
        font-size: 16px;
        font-weight: 500;
        color: #000;
        padding: 10px 0;
      }
    }
    .comfir-tips {
      color: #000000e6;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 500;
      line-height: 28px;
      padding: 10px 0;
    }
    .managerName {
      display: flex;
      align-items: center;
      margin-bottom: 14px;
      /deep/.sdc-staff-selector {
        width: 450px;
      }
    }
    .apply-type {
      padding: 16px;
      background: #f5f7f9;
      padding-top: 8px;
      text-align: left;
      margin-top: 10px;
      color: #000000e6;
    }
    .p0 {
      color: #ff8200;
      display: block;
      cursor: pointer;
    }
    .p1 {
      display: block;
      cursor: pointer;
      .details-sapn {
        margin-right: 10px;
      }
      span {
        a {
          color: #3464e0;
        }
      }
    }
  }
  .Reason-sel {
    margin: 10px 0;
    // font-size: 16px;
    // font-weight: 500;
  }
  .redio-content {
    margin: 10px 0;
  }
}
</style>
