<template>
  <div class="page-wrap">
    <div class="page-content">
      <AudienceSelector
        audience
        btnText="添加学员"
        :showTab="['unitStaff', 'unit', 'group', 'import']"
        multiple
        v-model="target_list" 
        ref="selector"
        appCode="qlearning"
        :env="audienceEnv" 
        importNumber='1000'
        :isShowCount="true"
        class="add-study-btn"
        :createStudentID="true"
        @changeRule="handleStudentTarget"
        @closeModal="closeModal"
        title="添加学员"
        style="max-height: initial;"
      />
    </div>
  </div>
</template>
<script>
import { AudienceSelector } from '@tencent/sdc-audience'
// import { handAddStudent } from '@/config/mooc.api.conf.js'
export default {
  name: 'memberManagement',
  components: {
    AudienceSelector
  },
  data () {
    return {
      audienceEnv: process.env.NODE_ENV,
      openMode: 'auto', // auto: 自动打开， self: 手动打开
      target_list: ''
    }
  },
  mounted () {
    this.listenMessage()
  },
  methods: {
    listenMessage () {
      window.addEventListener('message', this.handleMessage, false)
    },

    handleMessage (e) {
      const { data } = e
      if (data.vendor !== 'memberPage') return
      
      if (data.events === 'dialogVisible' && data.params.show) {
        this.$refs.selector.$refs.modal.showModal()
        this.target_list = data.params.targetIds
      }
    },

    // 添加目标学员
    handleStudentTarget(val) {
      window.parent && window.parent.postMessage({ 
        events: 'addMemberSuccess',
        vendor: 'memberPage',
        params: val
      }, '*')
    },
    closeModal() {
      console.log('关闭执行了')
      window.parent && window.parent.postMessage({ 
        vendor: 'memberPage',
        events: 'dialogClose',
        params: {
          show: false 
        }
      }, '*')
    }
  },
  destroyed () {
    window.removeEventListener('message', this.handleMessage, false) 
  }
}
</script>

<style lang="less" scoped>
.page-wrap {
  height: 100%;
  background: #fff;
  position: relative;
  .page-content {
    position: absolute;
    top: 50%;
    left: 50%;
  }
}
</style>
