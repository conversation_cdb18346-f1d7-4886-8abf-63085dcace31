<template>
  <div class="manage-page">
    <Breadcrumb />
    <div class="content-top">
      <el-input
        placeholder="请输入图文名称"
        class="w-280 mr-16"
        prefix-icon="el-icon-search"
        v-model="graphic.graphic_name"
      />
      <!-- <el-select
        v-model="graphic.classify_id"
        @change="getList"
        placeholder="图文分类"
        class="w-280 mr-16">
        <el-option
          v-for="item in classifyList"
          :key="item.id"
          :label="item.title"
          :value="item.id"
        ></el-option>
      </el-select> -->
      <!-- <el-cascader
        class="w-280 mr-16"
        placeholder="请选择图文分类"
        v-model="graphic.classify_id"
        :options="classifyList"
        :props="classifyProps"
        @change="classifyChange"
        collapse-tags
        clearable
      >
      </el-cascader> -->
      <sdc-search-label
        class="w-280 mr-16"
        v-model="graphic.classify_id"
        @getSelectedLabelList="getSelectedLabelList"
        :labelNodeEnv="labelNodeEnv"
      >
      </sdc-search-label>
      <el-select
        v-model="graphic.content_type"
        @change="getList"
        placeholder="图文类型"
        class="w-280 mr-16"
      >
        <el-option
          v-for="(item, index) in contentTypeList"
          :key="index"
          :label="item.label"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <el-input
        placeholder="请输入作者名称"
        class="w-280 mr-16"
        prefix-icon="el-icon-search"
        v-model="graphic.author"
      />
      <el-date-picker
        type="datetimerange"
        v-model="subTimeArr"
        @change="handleTimeChange"
        value-format="yyyy-MM-dd HH:mm:ss"
        :default-time="['00:00:00', '23:59:59']"
        :picker-options="pickerOptions"
        range-separator="至"
        start-placeholder="请选择开始时间"
        end-placeholder="请选择结束时间"
        class="time-range mr-16"
      >
      </el-date-picker>
      <el-select
        v-model="graphic.excellent_status"
        @change="getList"
        placeholder="是否加精"
        class="w-280 mr-16"
      >
        <el-option
          v-for="(item, index) in addexcellentlist"
          :key="index"
          :label="item.label"
          :value="item.id"
        >
        </el-option>
      </el-select>
      <el-select v-model="graphic.graphic_status" placeholder="请选择内容状态" @change="getList">
        <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
    </div>
    <div class="content-main">
      <div class="table-top">
        <div class="left">
          <button class="el-button el-button--primary" @click="handleLinkUser">
            <i class="el-icon-circle-plus-outline"></i><span>创建图文</span>
          </button>
          <el-button class="el-button--primary" @click="exportList"
            >导出</el-button
          >
        </div>
        <div class="right">
          <el-radio-group v-model="graphic.order_by" @change="getList">
            <el-radio-button :label="0">最新</el-radio-button>
            <el-radio-button :label="1">字数</el-radio-button>
            <el-radio-button :label="2">浏览量</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <el-table
        ref="table"
        :data="tableParams.list"
        header-row-class-name="table-header-style"
        row-class-name="table-row-style"
      >
        <el-table-column
          v-for="item of tableCols"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :width="item.width ? item.width : 'auto'"
          style="overflow: hidden"
          show-overflow-tooltip
          align="left"
        >
          <template slot-scope="{ row }">
            <span
              v-if="item.prop === 'graphic_name'"
              @click="handleLink(row)"
              class="graphic-name"
            >
              {{ row.graphic_name }}
              <span class="jing" v-if="row.excellent_status === 1">精</span>
            </span>
            <span v-else-if="item.prop === 'info_sec_status'">
              {{ safeInfo[row.info_sec_status] }}
            </span>
            <span v-else-if="item.prop === 'graphic_status'">
              {{ status_map[row.graphic_status] }}
            </span>
            <span v-else-if="item.prop === 'content_type'">
              {{
                row.content_type === 0 || row.content_type === 3
                  ? '原创'
                  : row.content_type === 1
                  ? '转载'
                  : row.content_type === 2
                  ? '活动推广' 
                  : row.content_type === 4
                  ? 'AI创建'
                  : row.content_type === 5
                  ? '人工运营'
                  : '-'
              }}
            </span>
            <span v-else>{{
              row.graphic_status === 4
                ? row[item.prop] || row[item.prop] != 4
                  ? row[item.prop]
                  : '-'
                : row[item.prop]
            }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="260">
          <template slot-scope="{ row }">
            <el-button type="text" @click="handleLinkUser(row)">编辑</el-button>
            <el-button type="text" :disabled="![2, 3].includes(row.graphic_status)" @click="handleOn(row)">上架</el-button>
            <el-button type="text" :disabled="![1].includes(row.graphic_status)" @click="handleUp(row)">下架</el-button>
            <el-button type="text" @click="getbrilliant(row)">{{
              row.excellent_status === 0 ? '加精' : '取消加精'
            }}</el-button>
            <el-button type="text" @click="handleDelete(row)" class="mr-16"
              >删除</el-button
            >
            <el-dropdown @command="handleCommand(row, $event)">
              <el-button type="text">更多</el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="share">分享</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
      <div class="table-pagination">
        <el-pagination
          :hide-on-single-page="tableParams.list.length === 0"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="tableParams.page_no"
          :page-sizes="[10, 20, 30, 50, 100]"
          :page-size="tableParams.page_size"
          layout="total,  prev, pager, next, sizes, jumper"
          :total="tableParams.total"
        >
        </el-pagination>
      </div>
    </div>
    <QrCodeDialog
      v-if="copyShow.show"
      :visible.sync="copyShow.show"
      :url="copyShow.url"
    />
  </div>
</template>
<script>
import axios from 'axios'
import QrCodeDialog from '@/views/components/qrCodeDialog'
import {
  getGraphicList,
  // getNetCourseClassify,
  delGraphic,
  add_excellent,
  startCommon,
  stopCommon
} from 'config/api.conf'
import env from 'config/env.conf.js'
import Breadcrumb from '../../components/breadcrumb'
import pager from 'mixins/pager'
const safeInfo = {
  'null': '审核通过',
  '0': '待审核',
  '1': '审核通过',
  '2': '审核不通过'
}
export default {
  mixins: [pager],
  components: {
    Breadcrumb,
    QrCodeDialog
  },
  data () {
    return {
      status_map: {
        1: '在用',
        3: '下架',
        4: '草稿',
        6: '待审核'
      },
      statusOptions: [
        { label: '全部', value: '' },
        { label: '在用', value: '1' },
        { label: '下架', value: '3' },
        { label: '草稿', value: '4' },
        { label: '待审核', value: '6' }
      ],
      labelNodeEnv:
        process.env.NODE_ENV === 'production' ? 'production' : 'test',
      safeInfo,
      tableCols: [
        { label: '图文名称', prop: 'graphic_name' },
        { label: '作者', prop: 'authors' },
        { label: '信安审核', prop: 'info_sec_status' },
        { label: '内容状态', prop: 'graphic_status' },
        { label: '类型', prop: 'content_type' },
        { label: '发布时间', prop: 'first_submit_time' },
        { label: '字数', prop: 'graphic_number' },
        { label: '浏览量', prop: 'view_count' }
      ],
      graphic: {
        graphic_name: '',
        classify_id: [],
        content_type: '',
        author: '',
        excellent_status: '',
        order_by: 0, //  0最新 1字数 2浏览量
        graphic_status: '' // 内容状态
      },
      subTimeArr: ['', ''],
      tableParams: {
        page_no: 1,
        page_size: 10,
        total: 0,
        list: []
      },
      // classifyList: [],
      // classifyProps: {
      //   multiple: true,
      //   label: 'item_name',
      //   value: 'item_id',
      //   children: 'child',
      //   checkStrictly: true,
      //   emitPath: false
      // },
      contentTypeList: [
        { label: '全部', id: '' },
        { label: '原创', id: 0 },
        { label: '转载', id: 1 },
        { label: '活动推广', id: 2 },
        { label: 'AI创建', id: 4 },
        { label: '人工运营', id: 5 }
      ],
      addexcellentlist: [
        { label: '全部', id: '' },
        { label: '非精品', id: 0 },
        { label: '精品', id: 1 }
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: '最近一周',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近一个月',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            }
          },
          {
            text: '最近三个月',
            onClick (picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            }
          }
        ]
      },
      copyShow: {
        show: false,
        url: '',
        qrUrl: location.href
      }
    }
  },
  watch: {
    'graphic.graphic_name': function () {
      setTimeout(() => {
        this.tableParams.page_no = 1
        this.getList()
      }, 500)
    },
    'graphic.author': function () {
      setTimeout(() => {
        this.tableParams.page_no = 1
        this.getList()
      }, 500)
    }
  },
  mounted () {
    // this.$nextTick(e => {
    //   const classifSelect = document.querySelector('.el-cascader')
    //   classifSelect.onclick = function () {
    //     const $el = document.querySelectorAll(
    //       '.el-cascader-panel .el-cascader-node[aria-owns]'
    //     )
    //     Array.from($el).map(item => item.removeAttribute('aria-owns'))
    //   }
    // })
    // this.getList()
    this.getList()

    // this.getClassifyList()
  },
  methods: {
    getSelectedLabelList (val) {
      this.graphic.classify_id = val
      // this.graphic.classify_id = val
      this.getList()
    },
    async getList () {
      const { page_no, page_size } = this.tableParams
      let classify_id = ''
      if (this.graphic.classify_id.length > 0) {
        classify_id = this.graphic.classify_id.map(item => item.label_id).join(';')
      }
      let params = {
        graphic_status: this.graphic.graphic_status,
        graphic_name: this.graphic.graphic_name,
        label_ids: classify_id,
        content_type: this.graphic.content_type,
        author: this.graphic.author,
        excellent_status: this.graphic.excellent_status,
        order_by: this.graphic.order_by,
        submit_start_time: this.subTimeArr ? this.subTimeArr[0] || '' : '',
        submit_end_time: this.subTimeArr ? this.subTimeArr[1] || '' : '',
        page_no: page_no,
        page_size
      }
      await getGraphicList(params).then(res => {
        this.tableParams.list = res.records || []
        this.tableParams.total = res.total
      })
    },
    // getClassifyList () {
    //   getNetCourseClassify().then(res => {
    //     this.classifyList = res
    //   })
    // },
    // classifyChange (val) {
    //   this.graphic.classify_id = val.join()
    //   this.getList()
    // },
    handleTimeChange () {
      this.tableParams.page_no = 1
      this.getList()
    },
    handleLink (row) {
      let hrefObj = ''
      if (row.graphic_status === 1) {
        hrefObj = this.$router.resolve({
          name: 'preview',
          query: { graphic_id: row.graphic_id }
        })
      } else {
        hrefObj = this.$router.resolve({
          name: 'create',
          query: { graphic_id: row.graphic_id }
        })
      }
      window.open(`https://${location.host + hrefObj.href}`)
    },
    handleLinkUser (row) {
      let hrefObj = ''
      if (row) {
        hrefObj = this.$router.resolve({
          name: 'create',
          query: { graphic_id: row.graphic_id, from: 'graphic-list' }
        })
      } else {
        hrefObj = this.$router.resolve({ name: 'create' })
      }
      window.open(`https://${location.host + hrefObj.href}`)
    },
    // 上架
    handleOn(item) {
      startCommon({ id: item.graphic_id, act_type: 18 }).then(res => {
        this.$message.success('启用成功')
        this.getList()
      })
    },
    // 下架
    handleUp(item) {
      let that = this
      this.$messageBox
        .prompt(
          '点击确认弹窗下架后文章将无法浏览，是否确认下架？',
          '下架',
          {
            closeOnClickModal: false,
            customClass: 'confirm-input-message-box',
            iconClass: 'el-icon-warning-outline',
            cancelButtonClass: 'el-button--default',
            confirmButtonClass: 'el-button--danger',
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            inputPlaceholder: '请输入“下架”确认此次操作',
            inputErrorMessage: '请输入“下架”',
            inputValidator: function (val) {
              return !!val && val.trim() === '下架'
            }
          }
        )
        .then(({ value }) => {
          if (value && value.trim() === '下架') {
            stopCommon({ id: item.graphic_id, act_type: 18 }).then((res) => {
              that.getList()
              this.$message.success('下架成功')
            })
          }
        })
    },
    getbrilliant (item) {
      let str = ''
      if (item.excellent_status === 0) {
        str =
          '<p style="font-size:14px">确定要给此内容设为精品吗?</p>' +
          '<p style="font-size:14px">确认后奖励作者30通用积分(仅集团正式员工)<p/>'
      } else {
        str =
          '<p style="font-size:14px">确定要给此内容取消加精吗?</p>' +
          '<p style="font-size:14px">注:加精积分奖励不退还<p/>'
      }
      this.$messageBox
        .confirm(str, '提示', {
          dangerouslyUseHTMLString: true,
          closeOnClickModal: false,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          iconClass: 'el-icon-warning-outline',
          center: true
        })
        .then(() => {
          let excellent_status = 0
          if (item.excellent_status === 0) {
            excellent_status = 1
          }
          let params = {
            data_id: item.graphic_id,
            operating_reason: '',
            excellent_status: excellent_status
          }
          add_excellent(params).then(res => {
            this.getList()
          })
        })
        .catch(() => {})
    },
    exportList () {
      const { page_no, page_size } = this.tableParams
      let classify_id = ''
      if (this.graphic.classify_id.length > 0) {
        classify_id = this.graphic.classify_id.map(item => item.label_id).join(';')
      }
      let params = {
        graphic_name: this.graphic.graphic_name,
        label_ids: classify_id,
        content_type: this.graphic.content_type,
        author: this.graphic.author,
        excellent_status: this.graphic.excellent_status,
        order_by: this.graphic.order_by,
        submit_start_time: this.subTimeArr[0] || '',
        submit_end_time: this.subTimeArr[1] || '',
        page_no,
        page_size
      }
      axios({
        url: `${
          env[process.env.NODE_ENV].trainingPath
        }api/graphic/manage/graphic/export_graphic`,
        method: 'POST',
        data: params,
        responseType: 'blob'
      }).then(res => {
        let _this = this
        if (res.data.type === 'application/json') {
          const reader = new FileReader() // 创建一个FileReader实例
          reader.readAsText(res.data, 'utf-8') // 读取文件
          reader.onload = function () {
            // 文件读取成功进行信息处理
            const data = JSON.parse(reader.result) // 获取到后端返回的json信息
            if (data.code === 403) {
              _this.$router.replace({
                name: '401'
              })
            } else if (data.code !== 200) {
              _this.$message.success('导出失败') 
            }
          }
        } else {
          const url = window.URL.createObjectURL(new Blob([res.data]))
          const link = document.createElement('a')
          link.href = url
          link.setAttribute('download', '图文列表.xlsx')
          document.body.appendChild(link)
          link.click()
          _this.$message.success('导出成功')
        }
      })
    },
    toLink () {},
    handleDelete (item) {
      let that = this
      let str =
        '<p style="color:#333;font-size:16px;">正在删除"' +
        item.graphic_name +
        '"</p>' +
        '<p style="text-align: left;">删除图文后相关数据将被清除且不可恢复，请谨慎操作<p/>'
      this.$messageBox
        .prompt(str, '', {
          dangerouslyUseHTMLString: true,
          closeOnClickModal: false,
          customClass: 'graphic_del',
          confirmButtonText: '删除',
          cancelButtonText: '取消',
          type: 'warning',
          center: true,
          inputPlaceholder: '请输入"确认删除"来确认此次操作',
          inputValidator: function (val) {
            if (val && val.trim() === '确认删除') {
              return true
            } else {
              return '请输入确认删除'
            }
          }
        })
        .then(({ value }) => {
          if (value && value.trim() === '确认删除') {
            delGraphic(item.graphic_id).then(res => {
              that.getList()
              that.$message.success('删除成功')
            })
          }
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          })
        })
    },
    handleCommand (item, evt) {
      if (evt === 'share') {
        let userInfo = JSON.parse(sessionStorage.getItem('login_user'))
        const href = `https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=${item.graphic_id}&from_act_id=${item.graphic_id}&share_staff_id=${userInfo.staff_id}&share_staff_name=${userInfo.staff_name}`
        this.copyShow.url = href
        this.copyShow.show = true
      }
    },
    handleSizeChange (size) {
      this.tableParams.page_size = size
      this.getList()
    },
    handleCurrentChange (current) {
      this.tableParams.page_no = current
      this.getList()
    }
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/graphic-manage.less';
.table-top {
  display: flex;
  justify-content: space-between;
}
.graphic-name:hover {
  color: #0052d9;
  cursor: pointer;
}
.el-button--text:hover {
  background: unset !important;
}
.jing {
  color: #fff;
  background-color: #d9001b;
  display: inline-block;
  border-radius: 5px;
  width: 20px;
  text-align: center;
  margin: 0 5px 0 5px;
  position: absolute;
  right: 0;
  z-index: 1;
}
</style>
