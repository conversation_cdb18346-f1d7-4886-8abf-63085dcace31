<template>
  <div :class="[{'edit-manage-page': !editDisabled}, 'apply-manage-page']">
    <div class="header-top">
      <span class="title">报名管理</span>
      <el-button :disabled="isApprove" v-show="editDisabled" type="primary" size="small" @click="editDisabled=false">编辑</el-button>
    </div>
    <div class="apply-content">
      <div class="title-box">
        <img src="@/assets/mooc-img/title-icon.png" />
        <span class="bassinfo-class-title">报名管理</span>
      </div>
      <div class="apply-item">
        <span class="label">自主报名</span>
        <el-switch
          :disabled="editDisabled"
          v-model="enabled_registration"
          >
        </el-switch>
        <span class="switch-tips">开启后，允许学员自主报名参与项目进行学习</span>
      </div>
      <div class="apply-item" v-show="enabled_registration">
        <span class="label">开放范围</span>
        <div class="apply-radio-box">
          <el-radio-group v-model="applyRadio" :disabled="editDisabled" @change="handleApply">
            <el-radio :label="0">集团正式员工可报名</el-radio>
            <el-radio :label="2">仅对集团正式员工、毕业生、实习生开放</el-radio>
            <el-radio :label="1">自定义可报名范围</el-radio>
          </el-radio-group>
        </div>
        <AudienceSelector
        v-if="applyRadio === 1"
        audience
        :showTab="['unitStaff', 'unit', 'group', 'import']"
        multiple
        v-model="target_list"
        ref="selector"
        appCode="qlearning"
        :env="audienceEnv"
        importNumber='1000'
        :isShowCount="false"
        :createStudentID="true"
        size="medium"
        :disabled="editDisabled"
        />
      </div>
      <div class="apply-item" v-show="enabled_registration && projectManageInfo.resource_from === null">
        <span class="label">报名确认</span>
        <el-switch
          :disabled="editDisabled"
          v-model="register_confirm"
          :active-value="1"
          :inactive-value="0"
          >
        </el-switch>
        <span class="switch-tips">关闭后，开放范围内的学员进入项目详情页以及任务详情页时自动完成报名并加入项目，无需额外报名确认</span>
      </div>
      <div class="apply-item" v-show="enabled_registration">
        <span class="label">报名期限</span>
        <el-switch
          :disabled="true"
          v-model="apply">
        </el-switch>
      </div>
      <div class="apply-item" v-show="enabled_registration">
        <span class="label">重复报名</span>
        <el-switch
          :disabled="true"
          v-model="enabled_repeat_reg">
        </el-switch>
      </div>
      <div class="apply-item" v-show="enabled_registration">
        <span class="label">名额限制</span>
        <el-switch
          :disabled="true"
          v-model="enabled_limit_students"
          active-color="#0052D9"
          inactive-color="#dcdfe6">
        </el-switch>
      </div>
      <div class="apply-item" v-show="enabled_registration">
        <span class="label">报名审批</span>
        <el-switch
          :disabled="true"
          v-model="need_reg_approval"
          active-color="#0052D9"
          inactive-color="#dcdfe6">
        </el-switch>
      </div>
    </div>
    <bottomFiexd v-show="!editDisabled" @cancel="cancel" @save="handleSave"></bottomFiexd>
  </div>
</template>

<script>
import { AudienceSelector } from '@tencent/sdc-audience'
import { saveApplyManage, applyManageInfo } from '@/config/mooc.api.conf.js'
import bottomFiexd from '@/views/components/botttomFixed.vue'
import { mapState } from 'vuex'
const allTarget = 2015587
const studentTarget = '2030108'
export default {
  components: {
    AudienceSelector,
    bottomFiexd
  },
  computed: {
    ...mapState(['projectManageInfo']),
    isApprove() {
      // 审批-审批中
      return this.$route.query.approve === '1' || this.projectManageInfo.course_status === 6
    }
  },
  data() {
    return {
      audienceEnv: process.env.NODE_ENV,
      enabled_registration: false,
      enabled_repeat_reg: true,
      register_confirm: 1, // 报名确认 0:关闭 1:开启
      applyRadio: 0,
      target_list: '',
      need_reg_approval: false,
      enabled_limit_students: false,
      editDisabled: true,
      apply: false
    }
  },
  mounted() {
    this.getInfo()
  },
  methods: {
    getInfo() {
      const { mooc_course_id } = this.$route.query
      applyManageInfo(mooc_course_id).then((res) => {
        this.register_confirm = res.register_confirm === 0 ? 0 : 1
        this.enabled_registration = res.enabled_registration
        this.target_list = res.target_list
        if (this.target_list * 1 === allTarget) {
          this.applyRadio = 0
        } else if (this.target_list === studentTarget) {
          this.applyRadio = 2
        } else {
          this.applyRadio = 1
        }
      })
    },
    handleSave() {
      if (this.applyRadio === 1 && !this.target_list) {
        this.$message.warning('请选择目标人员')
        return
      }
      // 自主报名关闭清空数据
      if (!this.enabled_registration) {
        this.target_list = allTarget
        this.applyRadio = 0
      }
      const { mooc_course_id } = this.$route.query
      const params = {
        mooc_course_id,
        target_list: this.target_list,
        register_confirm: this.register_confirm,
        enabled_registration: this.enabled_registration
        // enabled_repeat_reg: this.enabled_repeat_reg,
        // need_reg_approval: this.need_reg_approval,
        // enabled_limit_students: this.enabled_limit_students
      }
      saveApplyManage(params).then((res) => {
        this.$message.success('保存成功')
        this.editDisabled = true
        this.getInfo()
      })
    },
    handleApply() {
      if (this.applyRadio === 0) {
        this.target_list = allTarget
      } else if (this.applyRadio === 2) {
        this.target_list = studentTarget
      } else {
        this.target_list = ''
      }
    },
    cancel() {
      this.editDisabled = true
      this.getInfo()
    }
  }
}
</script>
<style lang="less" scoped>
.apply-manage-page {
  background-color: #fff;
  height: 100%;
  .header-top {
    // height: 56px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(243, 243, 243, 1);
    padding: 16px 20px;
    .title {
      font-size: 16px;
      height: 24px;
      line-height: 24px;
      font-weight: bold;
      color: rgba(0, 0, 0, 0.9)
    }
  }
  .title-box {
    display: flex;
    align-items: center;
    margin-bottom: 16px;

    img {
      width: 16px;
      height: 16px;
      margin-right: 8px;
    }

    .bassinfo-class-title {
      color: #000000;
      font-weight: bold;
      line-height: 22px;
      display: inline-block;
    }
  }
  .apply-content {
    padding: 16px 20px;
    .apply-item {
      display: flex;
      align-items: center;
      margin-bottom: 29px;
      padding-left: 24px;
      .label {
        margin-right: 30px;
        color: rgba(0, 0, 0, 0.6)
      }
      .switch-tips {
        margin-left: 20px;
        color: rgba(0, 0, 0, 0.4)
      }
      .apply-radio-box {
        margin-right: 20px;
      }
    }
  }
}
.edit-manage-page {
  height: calc(100% - 70px);
}
</style>
