<template>
  <div class="list-dialog">
    <el-dialog
      width="900px"
      :visible="visible"
      title="添加问卷"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <div class="course-body">
        <div class="course-body-title">
          <div class="check-label-warning">
            <i class="el-icon-warning-outline"></i>
            <span>仅支持添加本人创建/负责的问卷，若无问卷创建权限。请联系v_huiahuang</span>
          </div>

          <el-button type="primary" size="small" @click="openQuestionnair('add')">创建问卷</el-button>
        </div>

        <el-form ref="form" :model="form" inline>
          <el-form-item>
            <el-select v-model="form.modalType" placeholder="按模板分组查找" clearable>
              <el-option
                v-for="item in form.modalTypeList"
                :key="item.module_value"
                :label="item.module_name"
                :value="item.module_value"
              ></el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-input
              clearable
              v-model="form.modalName"
              placeholder="请输入模板标题"
              suffix-icon="el-icon-search"
            ></el-input>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" size="small" @click="onSearch(1)">搜索</el-button>
            <el-button size="small" plain @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table
          ref="mainTable"
          :data="tableData.records"
          max-height="551px"
          @selection-change="selectionChange"
        >
          <el-table-column prop="item_id" label="模板id" align="center" width="90"></el-table-column>
          <el-table-column prop="item_title" label="模板标题" align="center" width="200"></el-table-column>
          <el-table-column prop="item_gounp" label="模板分组" align="center"></el-table-column>
          <el-table-column prop="item_create_time" label="创建时间" align="center"></el-table-column>
        </el-table>

         <el-pagination
          v-if="tableData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="current"
          :page-sizes="[5, 10, 20, 30, 50, 100]"
          :page-size="size"
          layout="total,  prev, pager, next, sizes, jumper"
          :total="tableData.total"
          >
        </el-pagination>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="cancel">取 消</el-button>
        <el-button size='small' @click="submit" type="primary">使用模板创建问卷</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        modalType: '',
        modalTypeList: [
          {
            module_value: '',
            module_name: ''
          }
        ],
        modalName: ''
      },
      tableData: {
        records: [
          // {
          //   item_id: '11111',
          //   item_title: '测试1',
          //   item_gounp: '测试分组1',
          //   item_create_time: '2021-07-30 16:58:49'
          // }
        ],
        total: 10
      },
      current: 1,
      size: 5
    }
  },
  methods: {
    cancel() {
      this.$emit('update:visible', false)
    },
    handleReset() {
      this.form = {
        modalType: '',
        modalTypeList: [],
        modalName: ''
      }
    },
    selectionChange () {},
    submit() {},
    onSearch(page_no = 1) {},
    handleSizeChange() {},
    handleCurrentChange() {},
    openQuestionnair(type) {
      this.$emit('openQuestionnair', type)
    }
  }
}
</script>

<style lang="less" scoped>
.list-dialog {
  .content-url {
    color: #0052d9;
    cursor: pointer;
  }
  .course-body-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .check-label-warning {
    color: #ff7548;
    background-color: rgba(253, 246, 236, 1);
    padding: 10px 16px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    i {
      margin-right: 10px;
      font-size: 20px;
    }
  }
}
</style>
