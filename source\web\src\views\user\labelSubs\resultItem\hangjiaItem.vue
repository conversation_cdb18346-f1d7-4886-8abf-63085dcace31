<template>
    <div class="labelCardItem">
        <div class="courseImage">
            <img :src="require('@/assets/img/label/hangjiabg.png')" @click.stop="toPage(info)"
                :dt-eid=dtEidCard(info)
                :dt-areaid="dataModuleAreaid(info)" 
                :dt-remark="dtRgihtModuleRemark(info)" 
            >
            <div class="hangjiaInfo">
                <div class="avatar">
                    <div class="good_hj" v-if="info.origin_data && info.origin_data.is_excellent_hangjia">
                        <img class="good_hj_img" :src="`//r.hrc.${host ? 'woa' : 'oa'}.com/photo/150/${info.origin_data.expertName?info.origin_data.expertName.substr(0,info.origin_data.expertName.indexOf('(')):''}.png`" onerror='this.src="./static/xt.png"'>
                        <img class="good_hj_text" :src="require('@/assets/img/label/good_hj_text.png')" alt="">
                    </div>
                    <img class="good_hj_img" v-else :src="`//r.hrc.${host ? 'woa' : 'oa'}.com/photo/150/${info.origin_data.expertName?info.origin_data.expertName.substr(0,info.origin_data.expertName.indexOf('(')):''}.png`" onerror='this.src="./static/xt.png"'>
                </div>
                <div class="rightInfo">
                    <p class="expertName" :title="info.origin_data.expertName">{{ info.origin_data.expertName }}</p>
                    <p class="tag" :title="info.origin_data.tag">{{ info.origin_data.tag }}</p>
                    <div class="consult">
                        <span class="active">{{info.origin_data.meetNum||0}}</span>单咨询<span class="split">|</span>
                        <!-- <span v-if="info.origin_data.count && info.origin_data.count !== '0'"><span class="active">{{ info.origin_data.count === 100 ? info.origin_data.count : (Math.floor(info.origin_data.count * 100) / 100 ).toFixed(2)}}</span>分</span>
                        <span v-else>暂无评分</span> -->
                    </div>
                </div>
                <span class="group" v-if="info.origin_data.least_group_sharing_count">{{ info.origin_data.least_group_sharing_count }}人可预约成团</span>
            </div>
        </div>
        <div  class="courseInfo">
            <moduleTitle :info="info"/>
            <div class="courseView flex align-center">
                <img :src="require('@/assets/img/label/jf.png')">
                <span class="realScroe">{{info.origin_data.real_price ? info.origin_data.real_price + '积分/次' : '免费咨询'}}</span>
                <span v-if="info.origin_data.origin_price && info.origin_data.origin_price>info.origin_data.real_price">{{info.origin_data.origin_price}}积分/次</span>
                <!-- <div>
                    <img :src="`//r.hrc.${host ? 'woa' : 'oa'}.com/photo/150/${info.origin_data.expertName?info.origin_data.expertName.substr(0,info.origin_data.expertName.indexOf('(')):''}.png`" style="width: 32px;height:32px;margin-right: 8px;border-radius:50%;" alt="">
                </div>
                <div class="hjDesc" style=" text-overflow:ellipsis;overflow:hidden;white-space: nowrap;">
                    <p style="line-height: 16px;">{{ info.origin_data.expertName }}</p>
                    <p style="line-height: 16px; text-overflow:ellipsis;overflow:hidden;white-space: nowrap;" :title="info.origin_data.tag">{{info.origin_data.tag}}</p>
                </div> -->
            </div>
            <relatedContent :info="info" :curModuleId="curModuleId" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :subsType="subsType" :contentType="contentType"></relatedContent>
        </div>
    </div>
</template>
<script>
import moduleTitle from './child/moduleTitle.vue'
import relatedContent from './child/relatedContent.vue'
export default {
  name: 'hangjiaItem',
  props: ['info', 'curModuleId', 'curLabelId', 'sortSubsIds', 'subsType', 'contentType'],
  data() {
    return {
      host: location.host
    }
  },
  components: {
    moduleTitle,
    relatedContent
  },
  computed: {
    dtEidCard() {
      return (val) => {
        return `element_${val.item_id}_${val.module_id}`
      }
    },
    dataModuleAreaid() {
      return (val) => {
        return `area_${val.item_id}_${val.module_id}`
      }
    },
    dtRgihtModuleRemark() {
      return (val, container) => {
        return JSON.stringify({ 
          page: '标签订阅内容', 
          page_type: '标签订阅内容',
          container: '已上架内容',
          click_type: 'data',
          content_name: val.title,
          content_id: val.item_id,
          content_type: val.module_name,
          terminal: 'PC'
        })
      }
    }
  }
}
</script>
<style lang="less" scoped>
@import '../style/item.less';
.hangjiaInfo{
    position: absolute;
    width: 100%;
    height: 100%;
    padding: 12px;
    padding-top: 71px;
    display: flex;
    align-items: center;
    .group{
        position: absolute;
        right: 0;
        top: 0;
        color: #bd6600;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: 18px;
        border-radius: 0 4px;
        padding: 2px 6px;
        background: #FFEABB;
    }
    .avatar{
        margin-right: 9px;
        .good_hj{
            width: 85px;
            height: 68px;
            background: url('../../../../assets/img/label/good_hj.png');
            background-size: 100% 100%;
            text-align: center;
            position: relative;
            padding-top: 5px;
            padding-left: 1px;
        }
        .good_hj_text{
            position: absolute;
            bottom: 0px;
            height: 20px;
            width: 60px;
            right: 11px;
        }
        .good_hj_img{
            width: 52px;
            height: 52px;
            border-radius: 50%;
        }
    }
    .rightInfo{
        flex: 1;
        .expertName{
            color: #333333;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            word-break: break-all;
        }
        .tag{
            color: #777777;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            margin-top: 2px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            word-break: break-all;
        }
        .consult{
            margin-top: 4px;
            color: #a3a3a3;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            align-items: center;
            .split{
                margin: 0 6px;
            }
            .active{
                color: #0052d9;
                // margin: 0 1px;
            }
        }
    }
}
.courseView{
    .realScroe{
        color: #ff7200 !important;
        & + span{
            text-decoration: line-through;
        }
    }
}
</style>
