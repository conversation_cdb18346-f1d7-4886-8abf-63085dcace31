<template>
  <div class="preview-page" id="previewPage">
    <div class="encourage" v-show="openencourage">
      <div class="border"></div>
      <span class="closeencourage" @click="closeencourage">×</span>
      <div class="encourage_top">
        <span class="first_share"></span>
        <p class="encourage_p1">首次发表文章奖励</p>
        <p class="encourage_p2">额外激励200通用积分</p>
      </div>
      <div class="encourage_center">
        2023年4月1日起，首次发表大于1000字的文字
      </div>
      <div class="encourage_bottom">
        <el-button type="text" @click="godoing">去完成</el-button
        ><el-button type="text" @click="noprompt">不再提示</el-button>
      </div>
    </div>
    <div class="contain-main">
      <div class="left">
        <!-- <div
          id="graphic-preview"
          :class="[
            formData.is_open_catalogue == 0 ? 'have-catalogue' : '',
            'left-contain'
          ]"
        > -->
        <div
          id="graphic-preview"
          :class="[
            editorConfig.catalogue ? 'have-catalogue' : '',
            'left-contain'
          ]"
        >
          <sdc-mce-preview
            ref="editor"
            :urlConfig="editorConfig.urlConfig"
            :catalogue.sync="editorConfig.catalogue"
            :content="formData.graphic_text"
            :desc="graphicDesc"
            :fileInRow="fileInRow"
          >
            <slot>
              <div class="editor-header">
                <div class="name">
                  <span class="tag word">{{
                    $langue('Mooc_Common_ResourceType_Article', {
                      defaultText: '文章'
                    })
                  }}</span>
                  <i
                    :class="[
                      formData.content_type == 0 || formData.content_type == 3
                        ? 'label_originate'
                        : formData.content_type == 1
                        ? 'label_reprint'
                        : formData.content_type == 2
                        ? 'label_activity'
                        : '',
                      'label-icon'
                    ]"
                  >
                    {{
                      formData.content_type == 0 || formData.content_type == 3
                        ? $langue('Article_Original', { defaultText: '原创' })
                        : formData.content_type == 1
                        ? $langue('Article_Transport', { defaultText: '转载' })
                        : formData.content_type == 2
                        ? $langue('Article_Activity', { defaultText: '活动' })
                        : ''
                    }}
                  </i>
                  <span
                    >{{ formData.graphic_name }}
                    <img
                      v-if="formData.excellent_status === 1"
                      class="Refinement"
                      src="../../../assets/img/Refinement.png"
                      alt=""
                    />
                  </span>
                </div>
                <div class="info info1">
                  <div class="info-left">
                    <span class="time">{{ formData.creator_at }}</span>
                    <el-tooltip effect="dark" placement="bottom">
                      <div slot="content">
                        <span class="create">{{authorsNames}}</span>
                      </div>
                      <p class="create">
                        <span>{{ authorsNames }}</span>
                      </p>
                    </el-tooltip>
                  </div>
                  <div class="info-right">
                    <span
                      ><i class="icon-view"></i>({{
                        formData.view_count | countFilter
                      }})</span
                    >
                    <span
                      @click="handleLikeOrFav(1)"
                      :class="[zanAndcollect.isZan ? 'icon-zan-active' : '']"
                      :dt-eid="dtdianzan('eid')"
                      :dt-remark="dtdianzan('remark')"
                      ><i class="icon-zan"></i>({{
                        formData.praise_count | countFilter
                      }})</span
                    >
                    <span
                      ><i class="icon-comment"></i>({{
                        formData.comment_count | countFilter
                      }})</span
                    >
                    <span
                      @click="handleLikeOrFav(2)"
                      :class="[
                        zanAndcollect.isCollect ? 'icon-collect-active' : ''
                      ]"
                      :dt-eid='dtCollect("eid")'
                      :dt-remark="dtCollect('remark')"
                      ><i class="icon-collect"></i> ({{
                        formData.favorite_count | countFilter
                      }})</span
                    >
                    <span
                      v-if="appreciateshow"
                      @click="handleLikeOrFav(3)"
                      class="icon_reward-active"
                    >
                      <i class="icon_reward"></i> ({{ appreciatenumb }})
                    </span>
                    <span
                      v-if="!scene && can_edit"
                      class="editor"
                      @click="handleEdit"
                      ><i class="icon-edit"></i
                      >{{
                        $langue('Article_Edit', { defaultText: '编辑' })
                      }}</span
                    >
                    <sdc-point-reward
                      :dialogVisible.sync="doalpgVosoble"
                      :params="paramslist"
                      @close="appreciateclose"
                    ></sdc-point-reward>
                    <!-- <span v-else @click="goBack()" class="goback">返回上一页</span> -->
                    <div class="jf-tip" v-if="isShowJfTip">
                      <i class="jf-icon"></i
                      >{{
                        $langue('Mooc_Common_Alert_CommonPoint', {
                          point: +1,
                          defaultText: `通用积分+1`
                        })
                      }}
                    </div>
                  </div>
                </div>
                <div class="info info2 flex-center">
                  <div class="info-left">
                    <div class="info-classify">
                      <!-- <span class="label"
                        >{{
                          $langue('Article_Category', { defaultText: '分类' })
                        }}：</span
                      > -->
                      <!-- <el-tooltip effect="dark" placement="bottom">
                        <div slot="content">
                          <span
                            v-for="(
                              classifItem, index
                            ) in formData.classify_full_name"
                            :key="index"
                          >
                            <i
                              v-for="(item2, index2) in classifItem"
                              :key="index2"
                              @click="searchGo(item2, 1)"
                              >{{ item2
                              }}{{
                                index2 === classifItem.length - 1 ? '' : '/'
                              }}</i
                            >
                            <i
                              v-if="
                                index !==
                                formData.classify_full_name?.length - 1
                              "
                              class="fh"
                              >;
                            </i>
                          </span>
                        </div>
                        <p>
                          <span
                            v-for="(
                              classifItem, index
                            ) in formData.classify_full_name"
                            :key="index"
                          >
                            <i
                              v-for="(item2, index2) in classifItem"
                              :key="index2"
                              @click="searchGo(item2, 1)"
                              >{{ item2
                              }}{{
                                index2 === classifItem.length - 1 ? '' : '/'
                              }}</i
                            >
                            <i
                              v-if="
                                index !==
                                formData.classify_full_name?.length - 1
                              "
                              class="fh"
                              >;
                            </i>
                          </span>
                        </p>
                      </el-tooltip> -->
                    </div>
                    <div class="info-label">
                      <span class="label label-center"
                        >{{
                          $langue('Article_Lable', { defaultText: '标签：' })
                        }}</span
                      >
                   <div class="tag-list-box">
                      <sdc-label-show v-if="scene" ref="labelShow" :labelNodeEnv="labelNodeEnv" :actType="18" :courseId="graphic_id" :isPreview="true" :previewLbael="previewLbael"></sdc-label-show>
                      <sdc-label-show v-else ref="labelShow" :labelNodeEnv="labelNodeEnv" :actType="18" :courseId="graphic_id" :courseInfo="courseInfo"></sdc-label-show>
                      <!-- <div class="tag-list" v-for="(item, index) in labelList" :key="index">
                        <el-tooltip effect="dark" :content="item.category_full_name" placement="top-start">
                          <span class="tag-value" @click="searchGo(item.label_name, 2)">{{ item.label_name }}</span>
                        </el-tooltip>
                    </div> -->
                   </div>
                      <!-- <el-tooltip effect="dark" placement="bottom">
                        <div slot="content">
                          <span
                            v-for="(labelItem, idx) in labelList"
                            :key="idx"
                            @click="searchGo(labelItem, 2)"
                          >
                            {{ labelItem.category_full_name
                            }}<i v-if="idx !== labelList.length - 1" class="fh"
                              >;</i
                            >
                          </span>
                        </div>
                        <p>
                          <span
                            v-for="(labelItem, idx) in labelList"
                            :key="idx"
                            class="tag-value"
                            @click="searchGo(labelItem, 2)"
                          >
                            {{ labelItem.label_name
                            }}<i v-if="idx !== labelList.length - 1" class="fh"
                              >;</i
                            >
                          </span>
                        </p>
                      </el-tooltip> -->
                    </div>
                  </div>
                  <div class="info-right right-icons">
                    <span @click="handleShow(1)"
                      :dt-eid="dtAddCourse('eid')"
                      :dt-remark="dtAddCourse('remark')"
                      ><i class="icon-add"></i>
                      {{
                        $langue('Article_AddCourseList', {
                          defaultText: '添加到课单'
                        })
                      }}</span
                    >
                    <span @click="handleShow(2)" v-if="!isFormMooc"
                      :dt-eid="dtShareCourse('eid')"
                      :dt-remark="dtShareCourse('remark')"
                      ><i class="icon-share"></i
                      >{{
                        $langue('Article_Share', { defaultText: '分享' })
                      }}</span
                    >
                    <!-- <span><i class="icon-mobile"></i>移动端查看</span> -->
                  </div>
                </div>
                <p
                  class="original-text-link"
                  v-if="
                    formData.content_type == 1 || formData.content_type == 3
                  "
                >
                  <span v-if="formData.content_type == 1"
                    >{{
                      $langue('Article_OriginalLink', {
                        defaultText: '原文链接'
                      })
                    }}：</span
                  >
                  <i class="icon-note" v-else>笔记</i>
                  <span v-if="formData.content_type != 1"
                    >{{
                      $langue('Article_RelevantContent', {
                        defaultText: '相关内容'
                      })
                    }}：</span
                  >
                  <el-link
                    type="primary"
                    :underline="false"
                    @click="fromUrlGo"
                    >{{
                      formData.content_type == 1
                        ? formData.from_url
                        : formData.relation_content.content_name
                    }}</el-link
                  >
                </p>
              </div>
            </slot>
          </sdc-mce-preview>
          <img :src="imgCodeSrc" style="width: 200px" />
          <Watermark
            ref="watermark"
            v-if="watermark.textContent"
            :targetId="watermark.targetId"
            :text="watermark.textContent"
            :canvasUserOptions="watermark.canvasUserOpt"
            :wmUserOptions="watermark.wmUserOpt"
            :isManualInit="false"
          />
          <div class="comment-box">
            <div v-if="!this.scene && loadComment" id="commentBox">
              <sdc-comment
                :params="commentParams"
                @setCommentCount="setCommentCount"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="right" v-if="!isFormMooc">
        <ContentList
          title="课单内容列表"
          v-if="$route.query.jump_from == 'CourseList' && courseList.length > 0"
          :list="courseList"
          :paramsData.sync="paramsData"
          scene="course"
          class="mbt-20"
        />
        <ContentList
          :title="$langue('NetCourse_Extended', { defaultText: '延伸学习' })"
          v-if="extandList.length > 0"
          :list="extandList"
          :paramsData.sync="paramsData"
          scene="extand"
          class="mbt-20"
        />
        <ContentList
          :title="$langue('NetCourse_Recommended', { defaultText: '内容推荐' })"
          v-if="!this.scene && recommendList.length > 0"
          :list="recommendList"
          :paramsData.sync="paramsData"
          scene="recommend"
          class="mbt-20"
        />
        
        <sdc-learn-ai-bot class="aibot card" :env="aiBotEnv"></sdc-learn-ai-bot>
        <div class="right-fix-btns" v-if="!this.scene">
          <el-tooltip
            effect="dark"
            :content="
              $langue('Mooc_ProjectDetail_BasicInfo_Prais', {
                defaultText: '点赞'
              })
            "
            popper-class="zanAndCollectTooltip"
            placement="left-start"
          >
            <div
              :class="[zanAndcollect.isZan ? 'do-zan-active' : 'do-zan']"
              @click="handleLikeOrFav(1)"
            ></div>
          </el-tooltip>
          <el-tooltip
            effect="dark"
            :content="
              $langue('Mooc_ProjectDetail_BasicInfo_collect', {
                defaultText: '收藏'
              })
            "
            popper-class="zanAndCollectTooltip"
            placement="left-start"
          >
            <div
              :class="[
                zanAndcollect.isCollect ? 'do-collect-active' : 'do-collect'
              ]"
              @click="handleLikeOrFav(2)"
            ></div>
          </el-tooltip>
          <el-tooltip
            v-if="appreciateshow"
            effect="dark"
            content="赞赏"
            popper-class="zanAndCollectTooltip"
            placement="left-start"
          >
            <div class="do-admire-active" @click="handleLikeOrFav(3)"></div>
          </el-tooltip>
        </div>
      </div>
    </div>

    <AddCourseDialog
      :visible.sync="addCourseDialogShow"
      :itemData.sync="addCourseDialogData"
    />
    <!-- <shareDialog :isShow.sync="shareDialog" ref="shareDialog"></shareDialog> -->
    <QrCodeDialog
      v-if="copyShow.show"
      :visible.sync="copyShow.show"
      :url="copyShow.url"
      :copyTitle="formData.graphic_name"
    />
  </div>
</template>

<script>
import { AddCourseDialog } from '@/components/index'
import ContentList from '@/views/components/list'
import Watermark from '@/components/watermark.vue'
import QrCodeDialog from '@/views/components/qrCodeDialog'
import {
  getViewGraphic,
  viewGraphicRecord,
  getGraphicDetails,
  getContentsList,
  getExtanContentList,
  getRecommendList, // TODO:暂时屏蔽,后端500
  checkPraised,
  addPraise,
  deletePraise,
  checkFavorited,
  addFavorite,
  deleteFavorite,
  close_popup,
  get_popup_msg, // TODO:
  is_rewarded,
  reward_count,
  isFormalStaff,
  statususer,
  display_control,
  realtimeDocParse
} from 'config/api.conf'
import MoocJs from 'sdc-moocjs-integrator'
import translate from 'mixins/translate.vue'
import { pageExposure } from '@/utils/tools.js'
export default {
  mixins: [translate],
  components: {
    AddCourseDialog,
    ContentList,
    // QrCode,
    QrCodeDialog,
    Watermark
  },
  data() {
    return {
      editorConfig: {
        catalogue: false,
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/{contentId}`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview`
        }
      },
      commentParams: {},
      formData: {
        classify_full_name: [],
        content_type: '',
        graphic_text: '',
        comment_count: 0,
        favorite_count: 0
      },
      courseList: [],
      extandList: [],
      recommendList: [],
      labelList: [],
      addCourseDialogData: {
        module_id: 8,
        module_name: '图文'
      },
      addCourseDialogShow: false,
      copyShow: {
        show: false,
        url: '',
        qrUrl: location.href
      },
      shareDialog: false,
      viewTimer: null,
      fileInRow: 4,
      zanAndcollect: {
        isZan: false,
        isCollect: false
      },
      can_edit: false,
      loadComment: false,
      isShowJfTip: false,
      openencourage: false,
      is_appreciate: false,
      appreciatenumb: 0,
      appreciateshow: false,
      doalpgVosoble: false,
      paramslist: {},
      creator_id: 0,
      creatorser_id: '',
      reportFlag: false,
      staff_id: 0,
      allAuthorsHaveResigned: false, // 所有作者是否离职
      showdisplay: false,
      watermark: {
        targetId: 'graphic-preview', // 水印目标元素id
        textContent: '',
        canvasUserOpt: {
          width: 200,
          height: 160,
          fillStyle: 'rgba(153, 153, 153, 0.1)',
          font: '20px Microsoft Yahei',
          rotateDegree: 39,
          translate: {
            x: 0,
            y: 0
          }
        },
        wmUserOpt: {
          'z-index': 99999
        }
      },
      imgCodeSrc: '',
      labelNodeEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test'
      // graphic_id: null
    }
  },
  created() {
    sessionStorage.setItem('graphic_access_record_id', '')
    this.handleViewGraphicRecord()
    this.opens()
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          let pingShareUrl =
            process.env.NODE_ENV === 'production'
              ? `https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=${this.graphic_id}&from_act_id=${this.graphic_id}&share_staff_id=${val.staff_id}&share_staff_name=${val.staff_name}`
              : `http://s.test.yunassess.com/s/edLs1e?scheme_type=graphic&graphic_id=${this.graphic_id}&from_act_id=${this.graphic_id}&share_staff_id=${val.staff_id}&share_staff_name=${val.staff_name}`
          this.copyShow.url = this.scene
            ? `${pingShareUrl}&scene=${this.scene}`
            : pingShareUrl
          this.watermark.textContent = val.staff_name
          const hostUrl = location.hostname.endsWith('.woa.com')
            ? process.env.VUE_APP_PORTAL_HOST_WOA
            : process.env.VUE_APP_PORTAL_HOST
          if (!this.scene) {
            this.commentParams = {
              userName: val.staff_name,
              actId: this.graphic_id,
              appId: 'A9BiosXihR0h46ThNsAX',
              scrollTarget: '.graphic-user-page',
              urlConfig: {
                getComments: `${hostUrl}/training/api/graphic/user/graphic-comment/get_comments`,
                addComment: `${hostUrl}/training/api/graphic/user/graphic-comment/add`,
                deleteComment: `${hostUrl}/training/api/graphic/user/graphic-comment/delete/`,
                like: `${hostUrl}/training/api/graphic/user/graphic-comment/like`,
                sticky: `${hostUrl}/training/api/graphic/user/graphic-comment/sticky`,
                show: `${hostUrl}/training/api/graphic/user/graphic-comment/show`
              }
            }
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    courseInfo() {
      return {
        mooc_course_id: this.graphic_id,
        page: this.formData.graphic_name,
        page_type: '文章详情页'
      }
    },
    aiBotEnv() {
      return process.env.NODE_ENV === 'production' ? 'production' : 'test'
    },
    previewLbael() {
      const sessionFormData = JSON.parse(
        sessionStorage.getItem('graphic_preview')
      )
      return this.formData.label_list || sessionFormData.label_list || []
    },
    graphic_id() {
      return (
        this.$route.query.graphic_id ||
        sessionStorage.getItem('graphic_id') ||
        this.formData.graphic_id
      )
    },
    graphic_access_record_id() {
      return (
        this.formData.graphic_access_record_id ||
        sessionStorage.getItem('graphic_access_record_id') ||
        ''
      )
    },
    scene() {
      // 浏览页不带scene，预览带
      return this.$route.query.scene && parseInt(this.$route.query.scene) === 1
    },
    isFormMooc() {
      return this.$route.query.from === 'mooc'
    },
    isFormSpoc() {
      return this.$route.query.from === 'spoc'
    },
    // 埋点
    paramsData () {
      return { course_name: this.formData.graphic_name }
    },
    dtdianzan() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.formData.graphic_name,
            page_type: '文章详情页', 
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '点赞',
            terminal: 'PC'
          })
        } else {
          let { mooc_course_id, task_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_${task_id}_like`
        }
      }
    },
    dtCollect() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.formData.graphic_name,
            page_type: '文章详情页', 
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '收藏',
            terminal: 'PC'
          })
        } else {
          let { mooc_course_id, task_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_${task_id}_collect`
        }
      }
    },
    dtAddCourse() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.formData.graphic_name,
            page_type: '文章详情页', 
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '添加到课单',
            terminal: 'PC'
          })
        } else {
          let { mooc_course_id, task_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_${task_id}_addCourseList`
        }
      }
    },
    dtShareCourse() {
      return (type) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.formData.graphic_name,
            page_type: '文章详情页', 
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '分享',
            terminal: 'PC'
          })
        } else {
          let { mooc_course_id, task_id } = this.getRouterQuery()
          return `element_${mooc_course_id}_${task_id}_share`
        }
      }
    },
    authorsNames() {
      return this.formData.authors_list?.join('; ')
    },
    graphicDesc() {
      return this.formData.graphic_desc ? this.formData.graphic_desc.replace(/<[^>]*>/g, '') : ''
    }
  },
  mounted() {
    this.realtimeDocParse()
    if (this.scene) {
      this.$message({
        showClose: true,
        message: '当前正在访问预览页',
        type: 'warning',
        duration: 0,
        customClass: 'previewWarnOnce'
      })
    }
    const sessionFormData = JSON.parse(
      sessionStorage.getItem('graphic_preview')
    )
    this.graphic_id = this.$route.query.graphic_id ||
    sessionStorage.getItem('graphic_id') ||
    this.formData.graphic_id
    if (this.scene && sessionFormData && sessionFormData.graphic_status === 1) {
      this.loadComment = true
      // 发布状态预览从存储拿数据
      this.formData = sessionFormData
      let reg = /<image id="cdnImg".*?src="(.*?)"\s*\/>/
      if (this.formData.graphic_text.match(reg)) {
        let obj = this.fixText(this.formData.graphic_text, reg)
        this.formData.graphic_text = obj.newText
        this.imgCodeSrc = obj.imgsrc
      }
      this.editorConfig.catalogue =
        this.formData.is_open_catalogue === 1 ? Boolean(false) : Boolean(true)
      this.formData.classify_full_name = this.formData.classify_full_name?.map(
        (item) => {
          let e = item.split('->')
          return e
        }
      )
      this.labelList = this.formData.label_list
      this.extandList =
        JSON.parse(sessionStorage.getItem('extend_contents')) || []
    } else {
      this.getViewGraphicInfo().then((res) => {
        this.loadComment = true
        if (!this.scene) {
          // 浏览页开启时长记录
          this.creatViewTimer()
          document.addEventListener(
            'visibilitychange',
            this.handleViewGraphicRecord
          )
          // 通知mooc开启学习上报
          if (this.isFormMooc || this.isFormSpoc) {
            MoocJs.play()
          }
          if (
            this.$route.query.jump_from === 'CourseList' &&
            this.$route.query.area_id
          ) {
            this.getCourseList(this.$route.query.area_id)
          }
          this.getConRecommendList()
          if (this.graphic_id) {
            this.getExtandList()
            this.getZanAndCollectStatus()
          }
        } else {
          this.extandList =
            JSON.parse(sessionStorage.getItem('extend_contents')) || []
        }
      })
    }
    if (document.body.clientWidth > 1440) {
      this.fileInRow = 4
    } else {
      this.fileInRow = 3
    }
    const _this = this
    window.addEventListener('resize', function () {
      return (() => {
        if (document.body.clientWidth > 1440) {
          _this.fileInRow = 4
        } else {
          _this.fileInRow = 3
        }
      })()
    })

    document.addEventListener('copy', function (event) {
      let clipboardData = event.clipboardData
      clipboardData.setData('text/plain', document.getSelection().toString())
      event.preventDefault()
    })
    MoocJs.messageListener((res) => {
      if (res.events === 'tencent-mooc-lang') {
        localStorage.setItem('sdc-sys-def-lang', res.params)
        this.getLangJS()
      }
    })
  },
  methods: {
    // 解析课程
    realtimeDocParse() {
      let params = {
        bizType: 'GRAPHIC',
        bizId: this.graphic_id,
        sessionId: '',
        model: 1
      }
      realtimeDocParse(params).then(res => {

      })
    },
    getRouterQuery() {
      let { mooc_course_id, task_id } = this.$route.query
      return {
        mooc_course_id: mooc_course_id || '',
        task_id: task_id || ''
      }
    },
    fixText(text, reg) {
      let imgsrc, newText
      if (text.match(reg)) {
        imgsrc = text.match(reg)[1] || ''
        newText = text.replace(reg, '') || ''
      }
      return { newText, imgsrc }
    },
    creatViewTimer() {
      let _this = this
      this.viewTimer = setInterval(() => {
        _this.handleViewGraphicRecord() // 浏览器时长需每15秒记录一次
      }, 15000) // 15000
    },
    clearViewTimer() {
      clearInterval(this.viewTimer)
    },
    handleViewGraphicRecord() {
      if (document.visibilityState === 'hidden') {
        return
      }
      if (this.reportFlag) {
        this.clearViewTimer()
        return
      }
      const recordParam = {
        graphic_id: this.graphic_id,
        sharer_id: this.$route.query.share_staff_id || '',
        sharer_name: this.$route.query.share_staff_name || '',
        from_type: 'graphic',
        record_id: this.graphic_access_record_id
      }
      viewGraphicRecord(recordParam).then((data) => {
        if (data) {
          this.formData.graphic_access_record_id = data
          sessionStorage.setItem('graphic_access_record_id', data)
        } else if (!data) {
          this.reportFlag = true
        }
      })
    },
    getViewGraphicInfo() {
      // 浏览getViewGraphic 预览getGraphicDetails
      const viewParams = {
        sharer_id: this.$route.query.share_staff_id || '',
        sharer_name: this.$route.query.share_staff_name || '',
        from_type: 'graphic',
        id: this.graphic_access_record_id
      }
      const params = {
        graphic_id: this.graphic_id
      }
      const commonInfoAPI = this.scene ? getGraphicDetails : getViewGraphic
      let comParam = {}
      if (!this.scene) {
        comParam = {
          ...params,
          ...viewParams
        }
      } else {
        comParam = params
      }
      const reFun = commonInfoAPI(comParam)
        .then((data) => {
          this.can_edit = data.can_edit
          this.formData = data

          let reg = /<image id="cdnImg".*?src="(.*?)"\s*\/>/
          if (this.formData.graphic_text.match(reg)) {
            let obj = this.fixText(this.formData.graphic_text, reg)
            this.formData.graphic_text = obj.newText
            this.imgCodeSrc = obj.imgsrc
          }
          
          this.editorConfig.catalogue =
            data.is_open_catalogue === 1 ? Boolean(false) : Boolean(true)
          const { graphic_name, cover_image_id, graphic_desc, graphic_id } =
            data
          const addCourseParam = {
            content_name: graphic_name,
            cover_img_url: cover_image_id,
            description: graphic_desc,
            href: `https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=${graphic_id}`,
            item_id: graphic_id,
            origin: location.origin
          }
          this.addCourseDialogData = {
            ...this.addCourseDialogData,
            ...addCourseParam
          }
          if (data.classify_full_name) {
            this.formData.classify_full_name = data.classify_full_name?.map(
              (item) => {
                let e = item.split('->')
                return e
              }
            )
          }
          this.labelList = this.formData.label_list || []
          JSON.parse(data.authors)
          let authors = JSON.parse(data.authors)
          let authorsIdList = authors.map((v) => v.staffid)
          let authorsNameList = authors.map((v) => v.staffname)

          this.paramslist = {
            env: process.env.NODE_ENV,
            to_name:
              authorsNameList.length === 1
                ? authorsNameList[0]
                : authorsNameList,
            to: authorsIdList.length === 1 ? authorsIdList[0] : authorsIdList,
            object_id: data.graphic_id,
            object_type: 'Graphic',
            object_type_name: '文章',
            object_name: data.graphic_name
          }
          this.creatorser_id = data.creator_id
          this.getsaffuserid()
          this.getzanshang()
          this.getappreciate()
          this.isFormalStaffpeople(data.authors) // TODO:

          // 详情页曝光上报
          pageExposure({
            page_type: '文章详情页',
            content_type: '文章',
            act_type: '18',
            content_name: graphic_name,
            content_id: data.graphic_id
          })
        })
        .catch((err) => {
          if (err.code === 403 || err.code === 500) {
            sessionStorage.setItem('401Msg', err.message)
            this.$router.replace({
              name: '401'
            })
            if (this.isFormMooc || this.isFormSpoc) {
              MoocJs.sendErrorInfo(err.message)
            }
          }
          // if ([403, 404, 500].includes(err.code)) {
          //   MoocJs.sendErrorInfo(err.message)
          // }
        })
      return reFun
    },
    getCourseList(cl_id) {
      getContentsList(cl_id).then((data) => {
        this.courseList = data
      })
    },
    getExtandList() {
      const params = {
        act_id: this.graphic_id,
        act_type: 18
      }
      getExtanContentList(params).then((data) => {
        this.extandList = data
      })
    },
    getConRecommendList() {
      // 当天创建的数据凌晨12点之前，不请求数据
      let curDateLastTime = new Date().setHours(0, 0, 0, 0)
      let createTimeS = this.formData?.creator_at
        ? `${this.formData.creator_at.split(' ')[0]} 23:59:59`
        : ''
      const createTime = new Date(createTimeS).getTime()
      if (curDateLastTime > createTime) {
        const params = { // TODO:暂时屏蔽,后端500
          module_id: 8,
          item_id: this.graphic_id
        }
        getRecommendList(params).then((data) => {
          this.recommendList = data || []
          if (this.recommendList.length > 5) {
            this.recommendList = this.recommendList.splice(0, 5)
          }
        })
      }
    },
    getZanAndCollectStatus() {
      const params = { graphic_id: this.graphic_id }
      checkPraised(params).then((res) => {
        this.zanAndcollect.isZan = res
      })
      checkFavorited(params).then((res) => {
        this.zanAndcollect.isCollect = res
      })
    },
    handleLikeOrFav(scene) {
      if (this.scene) return
      const params = { graphic_id: this.graphic_id }
      if (scene === 1) {
        checkPraised(params).then((res) => {
          const PAndFCommonAPI = res ? deletePraise : addPraise
          const tip = res
            ? this.$langue('Mooc_Common_Alert_CancelPraiseSucessed', {
              defaultText: '取消点赞成功'
            })
            : this.$langue('Mooc_Common_Alert_PraiseSucessed', {
              defaultText: '点赞成功'
            })
          this.formData.praise_count = res
            ? this.formData.praise_count === null ||
              this.formData.praise_count === 0
              ? 0
              : this.formData.praise_count - 1
            : this.formData.praise_count + 1
          this.handlerCommonInt(PAndFCommonAPI, params, tip)
        })
      } else if (scene === 2) {
        checkFavorited(params).then((res) => {
          const PAndFCommonAPI = res ? deleteFavorite : addFavorite
          const tip = res
            ? this.$langue('Mooc_Common_Alert_CancelCollectSucessed', {
              defaultText: '取消收藏成功'
            })
            : this.$langue('Mooc_Common_Alert_CollectSucessed', {
              defaultText: '收藏成功'
            })
          this.formData.favorite_count = res
            ? this.formData.favorite_count === null ||
              this.formData.favorite_count === 0
              ? 0
              : this.formData.favorite_count - 1
            : this.formData.favorite_count + 1
          this.handlerCommonInt(PAndFCommonAPI, params, tip)
        })
      } else if (scene === 3) {
        if (this.is_appreciate) {
          this.$message({
            type: 'warning',
            message: '您已经赞赏过了,不能重复赞赏',
            duration: 2000
          })
        } else {
          if (this.creatorser_id === this.$store.state.userInfo.staff_id) {
            this.$message({
              type: 'warning',
              message: '不能赞赏自己的文章~',
              duration: 2000
            })
          } else if (this.allAuthorsHaveResigned) {
            this.$message({
              type: 'warning',
              message: '作者已离职，无法赞赏',
              duration: 2000
            })
            this.rewaring = false
          } else {
            this.doalpgVosoble = true
          }
        }
      }
    },
    getsaffuserid() {
      let authors = JSON.parse(this.formData.authors)
      let promiseAll = authors.map((v) => statususer(v.staffid))
      Promise.all(promiseAll).then((res) => {
        if (res.some((v) => v !== 2)) {
          this.allAuthorsHaveResigned = false
        } else {
          this.allAuthorsHaveResigned = true
        }
      })
    },
    handlerCommonInt(PAndFCommonAPI, params, tip) {
      if (PAndFCommonAPI === addFavorite) {
        params.graphic_name = this.formData.graphic_name
      }
      PAndFCommonAPI(params).then((data) => {
        if (PAndFCommonAPI === addPraise || PAndFCommonAPI === deletePraise) {
          this.zanAndcollect.isZan =
            PAndFCommonAPI === addPraise ? Boolean(true) : Boolean(false)
        }
        if (
          PAndFCommonAPI === addFavorite ||
          PAndFCommonAPI === deleteFavorite
        ) {
          this.zanAndcollect.isCollect =
            PAndFCommonAPI === addFavorite ? Boolean(true) : Boolean(false)
        }
        if (PAndFCommonAPI === addPraise || PAndFCommonAPI === addFavorite) {
          if (data.credit && data.credit !== '0') {
            this.isShowJfTip = true
            setTimeout(() => {
              this.isShowJfTip = false
            }, 6000)
            this.$message.success(
              `${tip}， ${this.$langue('Mooc_Common_Alert_CommonPoint', {
                point: +data.credit,
                defaultText: `通用积分+${+data.credit}`
              })}`
            )
          } else this.$message.success(tip)
        } else this.$message.success(tip)
      })
    },
    handleEdit() {
      this.$router.push({
        name: 'create',
        query: {
          graphic_id: this.formData.graphic_id,
          from: 'preview'
        }
      })
    },
    searchGo(item, scene) {
      if (this.scene) return
      let href = location.hostname.endsWith('.woa.com')
        ? process.env.VUE_APP_V8_HOST_WOA
        : process.env.VUE_APP_V8_HOST
      href = `${href}/mat/user/search?keywords=${item}&from_page=ql新首页`
      if (scene === 2) href = `${href}&type=label`
      window.open(href)
    },
    fromUrlGo() {
      const openUrl =
        this.formData.content_type === 1
          ? this.formData.from_url
          : this.formData.relation_content.href
      if (openUrl) window.open(openUrl)
    },
    handleShow(scene) {
      if (scene === 1) {
        this.addCourseDialogShow = this.scene ? Boolean(false) : Boolean(true)
      } else {
        this.copyShow.show = this.scene ? Boolean(false) : Boolean(true)
      }
    },
    opens() {
      display_control().then((res) => {
        let ress = res // TODO:
        this.showdisplay = res
        let params = { act_type: 18 } // TODO:
        get_popup_msg(params).then((res) => {
          if (res.is_formal_staff && ress) {
            this.openencourage = !res.is_close_popup
          } else {
            this.openencourage = false
          }
        })
      })
      if (this.isFormMooc) {
        this.openencourage = false
      }
    },
    closeencourage() {
      this.openencourage = false
    },
    noprompt() {
      this.openencourage = false
      let params = { act_type: 18 }
      close_popup(params).then((res) => {})
    },
    godoing() {
      this.$router.push('/graphic/user/create')
    },
    setCommentCount() {},
    getzanshang() {
      let params = {
        to: this.formData.creator_id,
        object_id: this.formData.graphic_id,
        object_type: 'Graphic',
        object_name: this.formData.graphic_name
      }
      is_rewarded(params).then((res) => {
        this.is_appreciate = res
      })
    },
    getappreciate() {
      let params = {
        object_id: this.formData.graphic_id,
        object_type: 'Graphic'
      }
      reward_count(params).then((res) => {
        this.appreciatenumb = res
      })
    },
    isFormalStaffpeople(authors) {
      let params = JSON.parse(sessionStorage.getItem('login_user'))
      isFormalStaff(params.staff_id).then((res) => {
        if (res) {
          let authorsList = JSON.parse(authors)
          let promiseAll = []
          authorsList.forEach((v) => {
            promiseAll.push(isFormalStaff(v.staffid))
          })
          Promise.all(promiseAll).then((pRes) => {
            if (pRes.some((v) => v)) {
              this.appreciateshow = true
            } else {
              this.appreciateshow = false
            }
          })
        } else {
          this.appreciateshow = false
        }
      })
    },
    appreciateclose(e) {
      if (e) {
        this.is_appreciate = true
        ++this.appreciatenumb
      }
    }
  },
  beforeDestroy() {
    clearInterval(this.viewTimer)
    document.removeEventListener(
      'visibilitychange',
      this.handleViewGraphicRecord
    )
    if (sessionStorage.getItem('graphic_access_record_id')) {
      sessionStorage.removeItem('graphic_access_record_id')
    }
  },
  filters: {
    countFilter(val) {
      let str = ''
      str =
        val > 0 ? (val >= 10000 ? `${(val / 10000).toFixed(1)}w` : val) : '0'
      return str
    }
  }
}
</script>

<style lang="less" scoped>
.preview-page {
  .encourage {
    .el-button {
      font-size: 12px;
    }
    z-index: 9999;
    top: 62px;
    right: 18%;
    position: absolute;
    background-color: #fff;
    width: 315px;
    height: 86px;
    .border {
      width: 0;
      height: 0;
      /*在三角形底边设置一个边界颜色/
            border-top: 20px solid red;
            /*其它3边设置相同颜色，*/
      border-bottom: 9px solid #fff;
      border-left: 9px solid transparent;
      border-right: 9px solid transparent;
      position: absolute;
      top: -9px;
      left: 84px;
    }
    .closeencourage {
      top: 11px;
      position: absolute;
      right: 13px;
      color: #a0a1a3;
      font-size: 21px;
      cursor: pointer;
    }
    .encourage_top {
      .first_share {
        margin-right: 5px;
        width: 20px;
        height: 20px;
        display: inline-block;
        background: url('~@/assets/img/first_share.png') no-repeat center /
          cover;
        background-size: 18px 18px;
      }
      .encourage_p1 {
        font-family: 'PingFang SC';
        line-height: 22px;
        margin-right: 10px;
        font-weight: 400;
        color: #ed7b2fff;
        font-size: 14px;
      }
      .encourage_p2 {
        color: #00000066;
        font-size: 12px;
        font-weight: 400;
        font-family: 'PingFang SC';
        line-height: 22px;
      }
      padding: 15px 10px 2px 10px;
      display: flex;
      font-size: 15px;
    }
    .encourage_center {
      margin-left: 40px;
      text-align: left;
      font-size: 12px;
      color: #00000066;
      font-weight: 400;
      font-family: 'PingFang SC';
      line-height: 20px;
    }
    .encourage_bottom {
      .el-button {
        font-weight: 600;
      }
      text-align: end;
      margin-top: 2px;
      margin-right: 20px;
    }
  }
  .contain-main {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    padding-top: 10px;
    .left {
      height: 100%;
      //   width: 1148px;
      .left-contain {
        position: relative;
        // background-color: #fff;
        .comment-box #commentBox {
          position: relative;
          padding: 0 24px 24px 24px;
          background-color: #fff;
          &::before {
            position: absolute;
            content: '';
            left: 0;
            top: -16px;
            background-color: #fff;
            width: 100%;
            height: 16px;
            z-index: 999;
          }
        }
      }
      .editor-header {
        padding: 24px 24px 0 24px;
        background-color: #fff;
        .name {
          display: flex;
          align-items: center;
          color: #000000;
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 22px;
          word-break: break-word;
          span:last-child {
            flex: 1;
          }
        }
        .Refinement {
          width: 26px;
          height: 26px;
          margin-left: 10px;
          margin-bottom: 2px;
          object-fit: contain;
        }
        .tag {
          padding: 0 6px;
          border-radius: 2px;
          margin-right: 10px;
        }
        .word {
          color: rgba(255, 139, 108, 1);
          border: 1px solid rgba(255, 139, 108, 1);
          background: rgba(255, 139, 108, 0.2);
        }
        .label-icon {
          display: inline-block;
          width: 42px;
          height: 20px;
          line-height: 20px;
          margin-right: 10px;
          font-style: normal;
          color: #fff;
          font-size: 14px;
          text-align: center;
          &.label_activity {
            background: url('~@/assets/img/label_activity.png') no-repeat center /
              cover;
          }
          &.label_originate {
            background: url('~@/assets/img/label_originate.png') no-repeat
              center / cover;
          }
          &.label_reprint {
            background: url('~@/assets/img/label_reprint.png') no-repeat center /
              cover;
          }
        }
        .info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 16px;
          .info-left,
          .info-right {
            display: flex;
          }
          .info-left {
            .create,
            .time {
              text-overflow: ellipsis;
              overflow: hidden;
              word-break: break-all;
              white-space: nowrap;
              color: rgba(0, 0, 0, 0.6);
            }
            .time {
              width: 140px;
            }
            .create {
              margin-left: 16px;
            }
            .info-classify,
            .info-label {
              display: flex;
              .label {
                white-space: nowrap;
                color: rgba(0, 0, 0, 0.4) !important;
              }
              p {
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                white-space: nowrap;
                span {
                  cursor: pointer;
                  color: #3464e0;
                  i {
                    font-style: normal;
                  }
                }
              }
              .tag-value {
                background-color: rgba(235, 239, 252, 1);
                height: 20px;
                font-size: 12px;
                color: rgba(0, 82, 217, 1);
                padding: 4px;
                border-radius: 2px;
                display: inline-block;
                margin-right: 12px;
                line-height: 10px;
                cursor: pointer
              }
              .label-center {
                line-height: 25px;
              }
             .tag-list-box {
                display: flex;
                flex-wrap: wrap;
                margin-left: 8px;
              }

              // .tag-value {
              //   background-color: rgba(235, 239, 252, 1);
              //   height: 20px;
              //   font-size: 12px;
              //   color: rgba(0, 82, 217, 1);
              //   padding: 4px;
              //   border-radius: 2px;
              //   display: inline-block;
              //   margin-right: 12px;
              //   line-height: 10px;
              //   cursor: pointer
              // }
            }
            .info-label {
              line-height: 20px;
              
            }
            .fh {
              color: rgba(0, 0, 0, 0.4);
            }
          }
          .info-right {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.4);
            span {
              display: flex;
              align-items: center;
              justify-content: center;
              width: fit-content;
              margin-right: 18px;
            }
            span:nth-child(2),
            span:nth-child(4),
            span:nth-child(5),
            .editor {
              cursor: pointer;
            }
            .goback {
              cursor: pointer;
              color: #3464e0;
            }
            i {
              display: inline-block;
              width: 14px;
              height: 14px;
            }
            .icon-view {
              background: url('~@/assets/img/watch.png') no-repeat center /
                cover;
            }
            .icon-zan {
              background: url('~@/assets/img/zan1.png') no-repeat center / cover;
            }
            .icon-zan-active {
              color: #3464e0;
              i {
                background: url('~@/assets/img/zan1-active.png') no-repeat
                  center / cover;
              }
            }
            .icon-comment {
              background: url('~@/assets/img/comment.png') no-repeat center /
                cover;
            }
            .icon-collect {
              background: url('~@/assets/img/fav2.png') no-repeat center / cover;
            }
            .icon_reward {
              background: url('~@/assets/img/icon_reward.png') no-repeat center /
                cover;
            }
            .icon-collect-active {
              color: #3464e0;
              i {
                background: url('~@/assets/img/fav2-active.png') no-repeat
                  center / cover;
              }
            }
            .icon_reward-active {
              color: #3464e0;
              i {
                background: url('~@/assets/img/icon_reward_active.png')
                  no-repeat center / cover;
              }
            }
            .icon-edit {
              background: url('~@/assets/img/edit.png') no-repeat center / cover;
            }
            .icon-add {
              background: url('~@/assets/img/add.png') no-repeat center / cover;
            }
            .icon-share {
              background: url('~@/assets/img/share.png') no-repeat center /
                cover;
            }
            .icon-mobile {
              background: url('~@/assets/img/mobile.png') no-repeat center /
                cover;
            }
            .jf-icon {
              background: url('~@/assets/img/integral-icon.png') no-repeat
                center / cover;
              display: block;
              width: 20px;
              height: 20px;
              margin-right: 4px;
            }
            .jf-tip {
              color: #ff7548;
              position: absolute;
              right: 20px;
              top: -22px;
              display: flex;
              align-items: center;
            }
          }
          .right-icons {
            color: rgba(0, 0, 0, 0.6);
            span {
              white-space: nowrap;
              margin-right: 16px;
              cursor: pointer;
            }
          }
        }
        .info1 .info-right {
          position: relative;
        }
        .info2 {
          border-bottom: solid 1px #eeeeee;
          padding-bottom: 8px;
          margin-bottom: 20px;
          display: flex;
          align-items: flex-start;
          justify-content: space-between;
        }
        .flex-center {
          display: flex;
          align-items: center;
        }
      }
      .original-text-link {
        margin-bottom: 20px;
        display: flex;
        color: rgba(0, 0, 0, 0.6);
        span {
          width: 70px;
        }
        .icon-note {
          display: inline-block;
          width: 42px;
          height: 20px;
          border-radius: 2px;
          border: 1px solid#0052D9;
          color: #0052d9;
          background: rgba(52, 100, 224, 0.1);
          margin-right: 12px;
          font-style: normal;
          font-weight: bold;
          line-height: 18px;
          text-align: center;
        }
        .el-link {
          flex: 1;
          overflow: hidden; //溢出内容隐藏
          text-overflow: ellipsis; //文本溢出部分用省略号表示
          display: -webkit-box; //特别显示模式
          -webkit-line-clamp: 1; //行数
          line-clamp: 1;
          -webkit-box-orient: vertical; //盒子中内容竖直排列
        }
      }
      :deep(.sdc-editor-preview) {
        width: 100%;
        .content-wrapper {
          width: 100%;
        }
        .file-count {
          padding: 0 0 8px 24px;
        }
        .editor-file-list {
          margin: 0 24px 36px 24px;
        }
        .desc,
        .editor-content {
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          word-break: break-word;
          overflow-wrap: break-word;
          ol,
          ul {
            padding: revert;
            li {
              list-style: inherit;
            }
          }
          ul {
            list-style-type: disc;
          }
          ol {
            list-style-type: decimal;
          }
        }
        .desc {
          margin: 0 24px 0 24px;
        }
        .editor-content {
          padding: 20px 24px 32px 24px;
        }
      }
      :deep(.sdc-comment) {
        padding-top: 32px;
        border-top: solid 1px #eeeeee;
      }
    }
    .right {
      width: 272px;
      height: 100%;
      margin-left: 20px;
      .mbt-20 {
        margin-bottom: 20px;
      }
    }
    .right-fix-btns {
      position: fixed;
      bottom: 80px;
      // right: 34px;
      div {
        width: 50px;
        height: 50px;
        box-shadow: 0 0 4px 0 rgba(102, 102, 102, 0.3);
        border-radius: 50%;
        cursor: pointer;
        margin-bottom: 20px;
      }
      div:last-child {
        margin: 0;
      }
      .do-zan {
        background: url('~@/assets/img/do-zan.png') no-repeat center / cover;
      }
      .do-zan:hover,
      .do-zan-active:hover {
        background: url('~@/assets/img/do-zan-hover.png') no-repeat center /
          cover;
      }
      .do-zan-active {
        background: url('~@/assets/img/do-zan-active.png') no-repeat center /
          cover;
      }
      .do-collect {
        background: url('~@/assets/img/do-fav.png') no-repeat center / cover;
      }
      .do-collect:hover,
      .do-collect-active:hover {
        background: url('~@/assets/img/do-fav-hover.png') no-repeat center /
          cover;
      }
      .do-collect-active {
        background: url('~@/assets/img/do-fav-active.png') no-repeat center /
          cover;
      }
      .do-admire-active {
        background: url('~@/assets/img/do-admire-active.png') no-repeat center /
          cover;
      }
      .do-admire-active:hover {
        background: url('~@/assets/img/do-admire.png') no-repeat center / cover;
      }
    }
    .have-catalogue {
      .comment-box #commentBox {
        margin-left: 220px;
      }
    }
  }
}
@media screen and (max-width: 1660px) {
  .have-catalogue {
    .editor-header,
    .comment-box #commentBox {
      width: 654px;
    }
    .info {
      .info-left {
        .info-classify p,
        .info-label p {
          max-width: 160px !important;
        }
        .create {
          max-width: 90px !important;
        }
      }
    }
  }
  .contain-main .left {
    width: 866px !important;
    .info {
      .info-left {
        .info-classify p,
        .info-label p {
          max-width: 200px;
        }
        .create {
          max-width: 270px;
        }
      }
    }
  }
  .create {
    max-width: 420px;
  }
}
@media screen and (min-width: 1661px) {
  .have-catalogue {
    .editor-header,
    .comment-box #commentBox {
      width: 928px;
    }
    .info {
      .info-left {
        .info-classify p,
        .info-label p {
          max-width: 240px !important;
        }
        .create {
          max-width: 350px !important;
        }
      }
    }
  }

  .contain-main .left {
    width: 1148px !important;
    .info {
      .info-left {
        .info-classify p,
        .info-label p {
          max-width: 360px;
        }
        .create {
          max-width: 570px;
        }
      }
    }
  }
  .create {
    max-width: 690px;
  }
}
</style>
