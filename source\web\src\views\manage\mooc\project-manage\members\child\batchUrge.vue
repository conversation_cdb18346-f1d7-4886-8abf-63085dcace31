<template>
  <el-dialog 
  :title="title" 
  :visible.sync="visible"
  width="430px"
  custom-class="none-border-dialog batchUrge-dialog"
  :close-on-click-modal="false"
  :before-close="closeDialog"
  >
    <div class="batchUrge-body">
      <p class="urge-tips">{{ tips }}</p>
      <p class="push-tips" v-show="targetShow">是否立即向成功加入项目的学员推送学习提醒?</p>
      <div class="info-template-box">
        <div class="item-contenet">
          <el-checkbox v-model="form.mailChceck" @change="changeTemplate($event, 'mail')">邮件</el-checkbox>
          <span @click="getInfoTemplate('mail')" class='item-check-template'>查看消息模板</span>
        </div>
        <div class="item-contenet">
          <el-tooltip effect="dark" :disabled="!form.botCheck" content="企微Tips和机器人只能2选1" placement="top">
            <el-checkbox :disabled="form.botCheck" v-model="form.msgCheck" @change="changeTemplate($event, 'tips')">企微Tips</el-checkbox>
          </el-tooltip>
          <span @click="getInfoTemplate('tips')" class='item-check-template'>查看消息模板</span>
        </div>
        <div class="item-contenet">
          <el-tooltip 
            effect="dark" 
            :disabled="botMessage.disabled"
            :content="botMessage.msg" 
            placement="top"
            >
            <el-checkbox :disabled="!projectManageInfo.enable_bot || form.msgCheck" v-model="form.botCheck" @change="changeTemplate($event, 'bot')">企微机器人</el-checkbox>
          </el-tooltip>
          <span @click="getInfoTemplate('bot')" class='item-check-template'>查看消息模板</span>
        </div>
        <div class="item-contenet" v-if="title !== '添加学员结果'">
          <el-tooltip effect="dark" :disabled="projectManageInfo.enable_myoa" content="请先联系超级管理员启用MyOA通知功能" placement="top">
            <el-checkbox :disabled="!projectManageInfo.enable_myoa" v-model="form.moaCheck" @change="changeTemplate($event, 'myoa')">MyOA</el-checkbox>
          </el-tooltip>
          <span @click="getInfoTemplate('myoa', 'fasterMyOa')" class='item-check-template'>查看消息模板</span>
        </div>
        <div class="item-contenet">
          <el-tooltip effect="dark" :disabled="projectManageInfo.enable_hr_assistant" content="请先联系超级管理员启用HR助手通知功能" placement="top">
            <el-checkbox :disabled="!projectManageInfo.enable_hr_assistant" v-model="form.hrCheck" @change="changeTemplate($event, 'hrAssistant')">HR助手</el-checkbox>
          </el-tooltip>
          <span @click="getInfoTemplate('hrAssistant', 'fasterWxAssistant')" class='item-check-template'>查看消息模板</span>
        </div>
      </div>
      <CustomTips
      lineHeight="30px" 
      :title="customTipsTitle" 
      IconName="el-icon-warning" 
      backgroundColor="#fdf6ec" 
      color="#FF7548"
      style="margin-top: 20px"
      >
    </CustomTips>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog" size="small">{{ targetShow ? '暂时不用' : '取 消' }}</el-button>
      <el-button type="primary" @click="handleSave" size="small">{{ targetShow ? '推送提醒' : '确 定' }}</el-button>
    </div>
    <commonInfoTemplate v-if="commonInfoTemplateDialog" :visible.sync="commonInfoTemplateDialog" ref="commonInfoTemplateRef" :previewType="previewType"></commonInfoTemplate>
  </el-dialog>
</template>
<script>
import CustomTips from '@/components/tips.vue'
import { infoTemplate, urgeAPI } from '@/config/mooc.api.conf'
import commonInfoTemplate from '@/views/manage/mooc/components/commonInfoTemplate.vue'
import { mapState } from 'vuex'
export default {
  components: {
    CustomTips,
    commonInfoTemplate
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isTipsInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        mailChceck: false,
        msgCheck: false,
        botCheck: false,
        moaCheck: false,
        hrCheck: false
      },
      previewType: '',
      commonInfoTemplateDialog: false,
      commTemplateList: [],
      multipleList: [],
      tips: '',
      msg_value_list: [],
      pushStaffId: []
    }
  },
  computed: {
    ...mapState(['projectManageInfo', 'userLimitInfo']),
    targetShow() {
      return this.isTipsInfo.isTips === 'target'
    },
    title() {
      const { isTips } = this.isTipsInfo
      return isTips === 'batch' ? '批量催办' : isTips === 'single' ? '催办' : '添加学员结果'
    },
    customTipsTitle() {
      return this.isTipsInfo.isTips === 'batch' ? '自动过滤“已完成”和“已逾期”状态的学员；企微TIPS和机器人2选1，最多可选3个催办渠道' : '企微TIPS和机器人2选1，最多可选3个催办渠道'
    },
    botMessage () {
      if (!this.projectManageInfo.enable_bot) {
        return {
          disabled: false,
          msg: '请先联系超级管理员启用企微机器人功能'
        }
      } else if (this.form.msgCheck) {
        return {
          disabled: false,
          msg: '企微Tips和机器人只能2选1'
        }
      }
      return {
        disabled: true,
        msg: ''
      }
    }
  },
  mounted() {
  },
  methods: {
    initData(data) {
      const { isTips } = this.isTipsInfo
      if (['batch', 'single'].includes(isTips)) { // 催办
        this.tips = data?.length === 1 ? `确定给学员${data[0].staff_name}发送催办消息？` : `已选中${data?.length}位学员，确定发送催办消息？`
        this.multipleList = data
        return
      }
      // 添加学员---推送提醒
      const { exist_count, succeed_count, staff_ids } = data
      this.pushStaffId = staff_ids
      this.tips = `${succeed_count}位学员已成功加入培养项目，${exist_count}位学员已存在，无法重复添加`
    },
    // 催办
    handleSave() {
      const { mooc_course_id } = this.$route.query
      const staffId = this.multipleList?.length === 1 ? [this.multipleList[0].staff_id] : this.multipleList.map((e) => e.staff_id)
      const { isTips } = this.isTipsInfo
      const send_type = isTips === 'target' ? 'notify' : 'remind'
      let list = this.msg_value_list.map((e) => e.type)
      if (!list?.length) {
        this.$message.warning('请选择消息模板')
        return
      }
      if (this.msg_value_list?.length > 3) {
        this.$message.warning('最多可选3个催办渠道！')
        return
      }
      const type = list.join(';')
      const params = {
        mooc_course_id,
        staff_ids: isTips === 'target' ? this.pushStaffId : staffId,
        type,
        send_type
      }
      urgeAPI(params).then((res) => {
        const msg = isTips === 'target' ? '推送成功' : this.multipleList?.length > 1 ? '批量催办成功' : '催办成功'
        this.$message.success(msg)
        this.closeDialog()
        this.$emit('onSearch')
      })
    },
    // 模板勾选
    changeTemplate(val, type) {
      if (!val) {
        this.msg_value_list = this.msg_value_list.filter((e) => e.type !== type)
        return
      }
      if (type === 'myoa') {
        this.$confirm('当选择多个催办时间时，为避免生成多条MyOA待办给学员造成困扰，针对符合催办条件的学员仅会生成一条MyOA待办。', '确定选中MyOA渠道催办吗？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
        }).catch(() => {
          const i = this.msg_value_list.findIndex((e) => e.type === type)
          this.msg_value_list.splice(i, 1)
          this.form.moaCheck = false
        })
      }
      if (this.msg_value_list?.length > 3) {
        this.$message.warning('最多可选3个催办渠道！')
        return
      }
      const { mooc_course_id } = this.$route.query
      infoTemplate({ module_name: 'remind', mooc_course_id, template_type: type }).then((res) => {
        this.msg_value_list.push({
          template_id: res.template_id,
          mooc_course_id: mooc_course_id,
          type
        })
      })
    },
    closeDialog() {
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    getInfoTemplate(type, previewType) {
      this.previewType = previewType || ''
      this.commonInfoTemplateDialog = true
      const { mooc_course_id } = this.$route.query
      const { isTips } = this.isTipsInfo
      const module_name = isTips === 'target' ? 'notify' : 'remind'
      infoTemplate({ module_name, mooc_course_id, template_type: type }).then((res) => {
        this.$nextTick(() => {
          this.$refs.commonInfoTemplateRef.initData(res)
        })
      })
    }
  }
}
</script>
<style lang="less" scoped>
.batchUrge-dialog {
  .batchUrge-body {
    .push-tips {
      margin-top: 20px;
    }
    .urge-tips {
      color: rgba(0, 0, 0, 0.6)
    }
  }
  .info-template-box {
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    // margin-bottom: 20px;
    .item-check-template {
      color: #0052D9;
      cursor: pointer;
    }
    .item-contenet {
      display: flex;
      justify-content: space-between;
    }
    .item-contenet + .item-contenet {
      margin-top: 20px;
    }
  }
}
</style>
