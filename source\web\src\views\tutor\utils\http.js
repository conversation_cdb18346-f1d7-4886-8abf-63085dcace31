import { DataHttp } from '@tencent/sdc-core'
import { app } from './index'
import Vue from 'vue'
import router from '../router'
let headers = {}
export default DataHttp.getInstance({
  // 当前应用上下文
  app,
  // 开启应用上下文
  ctx: true,
  // 当前请求实例(可使用DataHttp构建，也可配置详细参数，参考axios配置)
  axios: DataHttp.create({
    withCredentials: true,
    headers: {
      retry: 0,
      ...headers
    },
    // 请求拦截器
    async request(request) {
      try {
        if (window.$informationReview) {
          const res = await window.$informationReview.reviewRouteIntercept(request.url, request.method, request.data)
          if (!res.success) return Promise.reject({ code: 500, message: res.error_msg, ...res })

          if (res.status_code === 1) {
            return Promise.reject({ code: 500, message: res.error_msg, ...res })
          } else if (res.status_code === -1) {
            request.data = res.data
          }
        }
        return request
      } catch (error) {
        return Promise.reject(error)
      }
    },
    response(res) {
      if (res.data.code === 0) {
        res.data.code = 200
      }

      if ((res.data.code === 200 && res.data.success) && window.$informationReview) {
        window.$informationReview.reviewResults && window.$informationReview.reviewResults(res.config.url, res.config.method, res.config.data)
      }
      return res
    },
    // 错误处理
    reject(err) {
      return err
    }
  }),
  // 数据响应映射(当返回数据不满足{status,result,message}格式时，对返回数据字段映射)
  map: {
    status: 'code',
    result: 'data',
    message: 'message',
    value: 200
  },
  // 全局错误处理函数(err是包含title，message的对象，app为应用上下文)
  error(err) {
    // 导师认证页面没权限则页面提示无权限 不弹提示
    if (err?.title && err?.title.indexOf('api/tutor/user/status') !== -1) {
      console.log('err: ', err)
      return
    } 
    if (err.code === 403) {
      router.replace({
        name: '401'
      })
      sessionStorage.setItem('401Msg', err.message)
    } else if (err.code === 404) {
      router.replace({
        name: 'not_exist'
      })
    } else {
      if (err.code === -50006 || err.code === -50005) return
      let message = ''
      if (err.code === undefined && err.response?.data?.code) {
        const data = err.response?.data
        message = data?.code !== 200 ? data.message : '网络异常，请稍后重试！'
      } else {
        message = err.code && err.code !== 200 ? (err.message || err.data) : '网络异常，请稍后重试！'
      }
      if (message && JSON.stringify(message) !== '{}') {
        Vue.prototype.$message.error({
          message,
          duration: 2000
        })
      }
    } 
  }
})
