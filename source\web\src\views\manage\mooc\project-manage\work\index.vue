<template>
  <!-- 添加作业 -->
  <div class="work-container">
    <p class="title">{{workType === 'edit' ? '编辑' : '添加'}}作业</p>
    <el-form class="form" :model="form" :rules="rules" ref="form" label-width="140px">
      <el-form-item label="作业名称：" prop="title" class="input-style">
        <el-input 
          class="course-name-input" 
          v-model="form.title" 
          placeholder="请输入作业名称"
          clearable>
        </el-input>
        <span class="custom-el-input-count">{{handleValidor(form.title, 50, '1')}}/50</span>
      </el-form-item>
      <el-form-item label="作业要求：" prop="desc" class="project-detail-tincy">
        <el-radio-group v-model="form.requirement_type">
          <el-radio :label="0">管理员统一要求</el-radio>
          <el-radio :label="1" v-if="fromSystem === 'spoc'">指定角色定制要求</el-radio>
        </el-radio-group>
        <sdc-mce-editor 
          v-show="form.requirement_type === 0"
          ref="workEditor" 
          selector="course_make" 
          :env="editorEnv" 
          :content="form.desc"
          :catalogue.sync="editorConfig.catalogue"
          :urlConfig="editorConfig.urlConfig"
          :options="editorConfig.options"
          @getWordCount="getWordCount"
          :insertItems="insertItems"
        />
        <div v-show="form.requirement_type === 1 && fromSystem === 'spoc'"> 
          <span class="relation-selected" v-if="selecHomeworkItem">{{ selecHomeworkItem.jobRequirementName }}</span>
          <el-button plain size="small" class="relation-btn" @click="selectHomeworkReq">{{ selecHomeworkItem ? '重新选择' : '+ 选择关联的定制作业要求' }}</el-button>
        </div>
      </el-form-item>
      <el-form-item label="参考资料：">
        <el-upload
          action
          :auto-upload="true"
          :file-list="fileList"
          :accept="accepts"
          :show-file-list="false"
          :limit="10"
          :multiple="false"
          :http-request="onUpload" 
          :before-upload="beforeAvatarUpload"
          >
          <el-button plain icon="el-icon-upload2" size="small" id="work-upload-btn">点击上传</el-button>
          <p class="work-request" slot="tip">
            <el-tooltip effect="dark" placement="top-start">
              <div slot="content">
                <p>1. 视频格式：支持wmv、mp4、flv、avi、rmvb、mpg、mkv、mov，单个视频小于2G</p>
                <p>2. 音频格式：支持w4v、m4a、wma、wav、mp3、amr，单个音频小于500M</p>
                <p>3. 文档格式：支持doc、docx、ppt、pptx、xls、xlsx、pdf，单个文档小于100M</p>
                <p>4. 图片格式：支持jpg、jpeg、gif、png、bmp、ico、svg，单个图片小于10M</p>
                <p>5. 压缩包格式：支持zip、rar，单个压缩包小于1G</p>
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
            最多支持上传10个附件，学员写作业时可以查看或下载资料，支持类型：视频/音频/文档/图片/压缩包
          </p>
        </el-upload>
        <div class="upload-progress-status">
          <div class="upload-item-box" v-for="(item, index) in fileList" :key="index">
            <span class="progress-left-content" @mouseover="titleOver($event, index)">
              <el-tooltip effect="dark" :disabled="!item.isOverflow" :content="item.file_name" placement="top-start">
                <span class="progress-title">{{ item.file_name }}</span>
              </el-tooltip>
              <span class="progress-loading">
                <!-- 加载中 -->
                <i class="el-icon-loading" v-if="item.status === 1"></i>
                <!-- 加载成功 -->
                <i class="el-icon-success" v-if="item.status === 2"></i>
                <!-- 加载失败 -->
                <i class="el-icon-error" v-if="item.status === 0"></i>
                {{ 
                  item.status === 1 ? `${item.processValue}%` : 
                  item.status === 2 ? '上传成功' :
                  item.status === 0 ? '文件格式有误' : ''
                }}
              </span>
            </span>
            <span class="progress-right-content">
              <span v-if="item.file_size">文件大小 {{ changeSize(item.file_size) }}</span>
              <span v-if ="item.created_at" class="progress-time">上传日期： {{ item.created_at }}</span>
              <el-button v-if="item.status === 2" type="text" @click="resetUpload(item)">重新上传</el-button>
              <el-button type="text" @click="deleteUpload(item)">删除</el-button>
            </span>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="允许提交类型：" prop="subTypeList">
        <div class="sub_type_list">
          <el-checkbox class="check-all" v-model="subTypeAll" @change="changeSubTypeALl">全选</el-checkbox>
          <el-checkbox-group v-model="form.subTypeList" @change="changeSubmitType">
            <el-checkbox label="Text">文本</el-checkbox>
            <el-checkbox label="Image">图片</el-checkbox>
            <el-checkbox label="Audio">音频</el-checkbox>
            <el-checkbox label="Video">视频</el-checkbox>
            <el-checkbox label="Doc">文档</el-checkbox>
            <el-checkbox label="Zip">压缩包</el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form-item>
      <el-form-item label="提交时间：">
        <el-date-picker
          v-model="form.start_time"
          placeholder="选择提交开始日期"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="setRangeTime"
          >
        </el-date-picker>
        <span class="line">-</span>
        <el-date-picker
          v-model="form.end_time"
          placeholder="选择提交截止日期"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          @change="setRangeTime"
          >
        </el-date-picker>
        <p class="tips">过了提交截止时间后，学生无法提交作业，未提交作业的学员无法参与作业互评</p>
      </el-form-item>
      <el-form-item label="提交截止时间：" class="end-time" v-if="form.end_time">
        <el-switch v-model="form.enabled_deadline_remind"></el-switch>
        <div class="input-time-box" v-if="form.enabled_deadline_remind">
          <span class="input-time-title">提交截止前</span>
          <el-input-number 
          v-model="form.deadline_days" 
          controls-position="right" 
          :min="1"
          style="width: 100px" 
          size="small"
          ></el-input-number>
          <span class="input-unit">天</span>
        </div>
        <el-time-picker
          v-if="form.enabled_deadline_remind"
          style="width: 140px"
          placeholder="请选择时间"
          v-model="form.deadline_time"
          :picker-options="{
            selectableRange: '00:00:00 - 23:59:59'
          }"
          value-format="HH:mm:ss"
        >
        </el-time-picker>
        <span class="input-time-tips" v-if="fromSystem === 'spoc' && semesterInfoSpoc.trainingTypeId === 14">通过邮件自动提醒<span class="red-tips">未达到指定互评份数</span>的学员和</span>
        <span class="input-time-tips" v-else>通过企业微信机器人自动提醒作业<span class="red-tips">未提交</span>和<span class="red-tips">已退回</span>的学员</span>
      </el-form-item>
      <el-form-item label="共享查看：" class="share-check-box">
        <div class="people-switch">
          <el-switch v-model="form.enabled_share"></el-switch>
          <span class="people-switch-tips">开启后，允许指定人员查看学员提交的作业内容</span>
        </div>
        <div class="share-people" v-show="form.enabled_share">
          <span class="people-title">指定人员</span>
          <el-checkbox-group v-model="shareList">
            <!-- <el-checkbox label="1">其他学员</el-checkbox>
            <el-checkbox label="2">学员直接上级</el-checkbox>
            <el-checkbox label="3">学员所属组织BP</el-checkbox> -->
            <el-checkbox :label="role.shared_type" v-for="role in shareRoleList" :key="role.shared_type">{{ role.shared_name }}</el-checkbox>
          </el-checkbox-group>
        </div>
      </el-form-item>
      <el-form-item label="作业批阅：" class="work-check-box">
        <div class="people-switch">
          <el-switch :disabled="disabledEdit" v-model="form.enable_mark" @change="handleMarkType"></el-switch>
          <span class="people-switch-tips">开启后，可以指定老师批阅或者学员互评</span>
        </div>
        <div class="total-score-box" v-show="form.enable_mark">
          <div class="left-score">
            <span class="total-title">总分</span>
            <el-input-number 
            v-model="form.total_score" 
            controls-position="right" 
            size="small" 
            :min="1" 
            style="width: 100px"
            :precision="0"
            :disabled="disabledEdit"
            ></el-input-number>
          </div>
          <div class="right-score">
            <span class="total-title">合格分</span>
            <el-input-number 
            v-model="form.pass_score" 
            controls-position="right" 
            size="small" 
            :min="1" 
            style="width: 100px"
            :precision="0"
            :disabled="disabledEdit"
            ></el-input-number>
          </div>
        </div>
        <CustomTips
          v-show="form.enable_mark"
          class="read-over-tips"
          title="请注意，项目发布后，已保存的作业任务不支持修改总分和合格分" 
          IconName="el-icon-warning-outline" 
          backgroundColor="#fdf6ec" 
          color="#FF7548"
          lineHeight="40px"
          >
        </CustomTips>
      </el-form-item>
      <el-form-item v-if="form.enable_mark" :label="fromSystem !== 'spoc' ? '评分方式：' : '批阅角色：'" prop="markTypeList">
        <div class="score-type-box">
          <el-checkbox-group v-model="form.markTypeList" :disabled="disabledEdit" @change="handleMarkType">
            <el-checkbox :label="role.mark_type" v-for="role in markRoleList" :key="role.mark_type">{{ role.mark_name }}</el-checkbox>
            <!-- <el-checkbox label="1">老师批阅</el-checkbox>
            <el-checkbox label="2">学员互评</el-checkbox> -->
          </el-checkbox-group>
          <!-- <span class="tips">(学员可以对其他同学提交的作业进行批阅)</span> -->
        </div>
        <CustomTips
          class="score-type-tips"
          title="请注意，项目发布后，已保存的作业任务不支持评分方式及评分权重" 
          IconName="el-icon-warning-outline" 
          backgroundColor="#fdf6ec" 
          color="#FF7548"
          lineHeight="40px"
          >
        </CustomTips>
      </el-form-item>
      <!-- <el-form-item label="评分权重：" v-if="markType(1) && markType(2) && fromSystem !== 'spoc' && form.enable_mark" prop="scoreWeight">
        <div class="score-weight-box">
          <div class="left-weight">
            <span class="teacher-title">老师分数</span>
            <el-input-number 
            v-model="form.teacher_mark_weight" 
            controls-position="right" 
            size="small" 
            :min="0" 
            style="width: 100px"
            :disabled="disabledEdit"
            ></el-input-number>&nbsp;%
          </div>
          <div class="right-weight">
            <span class="teacher-title">互评分数</span>
            <el-input-number 
            v-model="form.student_mark_weight" 
            controls-position="right" 
            size="small" 
            :min="0" 
            style="width: 100px"
            :disabled="disabledEdit"
            ></el-input-number>&nbsp;%
          </div>
        </div>
        <p class="red-tips">某个分数为空时，先按照另一个分数100%权重计算总成绩</p>
      </el-form-item> -->
      <el-form-item label="评分权重：" v-if="roleWeightList.length > 0 && form.enable_mark" prop="scoreWeightSpoc">
        <div class="score-weight-box">
          <div class="left-weight" v-for="item in roleWeightList" :key="item.roleId">
            <span class="teacher-title">{{item.mark_name}}分数</span>
            <el-input-number 
            v-model="item.mark_weight" 
            controls-position="right" 
            size="small" 
            :min="0" 
            style="width: 100px"
            :disabled="disabledEdit"
            ></el-input-number>&nbsp;%
          </div>
        </div>
        <p class="red-tips">{{this.fromSystem !== 'spoc' ? '某个分数为空时，先按照另一个分数100%权重计算总成绩' : '若有角色没有评分会影响学员作业合格率'}}</p>
      </el-form-item>
      <!-- 勾选老师批阅 -->
      <el-form-item label="指定批阅老师：" v-if="markType(1) && form.enable_mark" prop="mark_teachers">
        <sdc-staff-selector
          multiple 
          ref="teacherSelectorRef" 
          v-model="form.mark_teachers"
          size="small" 
          :props="adminProps" 
          placeholder="请指定批阅作业的老师" 
          @change="changeStaff"
          />
      </el-form-item>
      <el-form-item label="互评设置：" v-if="markType(2) && form.enable_mark">
        <div class="comment-set">
          <div class="comment-box item-comment">
            <span class="comment-label comment-unit">互评份数：</span>
            <div>
              每位学员至少完成
              <el-input-number 
              v-model="form.student_mark_count" 
              controls-position="right" 
              size="small" 
              :min="1" 
              style="width: 100px"
              class="comment-input-num"
              :disabled="disabledEdit"
              ></el-input-number>&nbsp;份作业互评
              <span class="red-tips right-tips">互评份数一定要小于学员人数</span>
            </div>
          </div>
          <div class="comment-time item-comment">
            <span class="comment-label" :class="fromSystem === 'spoc' ? 'comment-unit' : ''">互评时间：</span>
            <el-date-picker
              size="small"
              style="width:380px"
              v-model="studentMarkTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00','23:59:59']"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="setRangeTime">
            </el-date-picker>
            <span class="red-tips right-tips">互评截止时间要晚于作业提交的截止时间，截止后学员无法参与互评</span>
          </div>
          <div class="comment-end item-comment" v-if="studentMarkTime?.length">
            <span class="comment-label">提醒截止：</span>
            <div class="switch-end-time">
              <el-switch v-model="form.enabled_student_mark_remind"></el-switch>
              <span class="switch-title">互评截止前</span>
              <div v-if="form.enabled_student_mark_remind">
                <el-input-number 
                v-model="form.student_mark_deadline_days" 
                controls-position="right" 
                size="small" 
                :min="1" 
                style="width: 100px"
                ></el-input-number>
                <span class="switch-title">天</span>
              <el-time-picker
                style="width: 140px"
                placeholder="请选择时间"
                v-model="form.student_mark_deadline_time"
                :picker-options="{
                  selectableRange: '00:00:00 - 23:59:59'
                }"
                value-format="HH:mm:ss"
              >
              </el-time-picker>
              <span class="end-time-tips right-tips">通过企业微信机器人自动提醒<span class="red-tips">未达到指定互评份数</span>的学员</span>
              </div>
            </div>
          </div>
          <div class="comment-name item-comment">
            <span class="comment-label">双向匿名：</span>
            <div class="switch-end-time">
              <el-switch v-model="form.enabled_student_mark_anonymous" :disabled="disabledEdit"></el-switch>
              <span class="switch-title">开启后，学员将匿名批阅其他学员的作业，同时也无法查看被批阅人信息</span>
            </div>
          </div>
        </div>
        <CustomTips
          class="comment-set-tips"
          title="请注意，项目发布后，已保存的作业任务无法修改互评份数、匿名开关" 
          IconName="el-icon-warning-outline" 
          backgroundColor="#fdf6ec" 
          color="#FF7548"
          lineHeight="40px"
          >
        </CustomTips>
      </el-form-item>
      <!-- spoc新增 评分维度&分值显示&批阅说明 配置项 -->
      <el-form-item label="评分维度：" class="share-check-box" v-if="form.enable_mark && fromSystem === 'spoc'">
        <div class="people-switch">
          <el-switch v-model="form.enable_score_dimension" @change="changeDimension"></el-switch>
          <span class="people-switch-tips">开启后，需对每个维度进行打分，最多添加6个维度，维度最高分总和必须等于配置的总分</span>
        </div>
        <div class="dimensions-item" v-if="form.enable_score_dimension">
          <el-row
            v-for="(item, ind) in form.score_dimensions"
            class="row-style-scores"
            :key="ind"
          >
            <el-col :span="12">
              <el-form-item
                :label="`选项名称${ind + 1}`"
                label-width="88px"
                :prop="`score_dimensions.` + ind + '.score_dimension_name'"
                :rules="{
                  required: true, message: '选项名称不能为空', trigger: ['change', 'blur']
                }"
                class="required-icon"
              >
                <el-input v-model="item.score_dimension_name" maxlength="20"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="分值"
                label-width="72px"
                class="required-icon"
              >
                <el-form-item
                  label=""
                  :prop="`score_dimensions.` + ind + '.min_score'"
                  :rules="[
                    {required: true, message: '请输入最小值', trigger: ['change', 'blur']},
                    { validator: (rule, value, callback, item) => {
                      validateRangeCount(rule, value, callback, form[`score_dimensions`][ind])
                    }, trigger: 'blur' }
                  ]"
                  class="row-item-style"
                >
                  <el-input-number v-model="item.min_score" :min="0" :controls="false" class="end-input-style"></el-input-number>
                </el-form-item>
                ~
                <el-form-item
                  label=""
                  :prop="`score_dimensions.` + ind + '.max_score'"
                  :rules="[
                    {required: true, message: '请输入最大值', trigger: ['change', 'blur']},
                    { validator: (rule, value, callback, item) => {
                      validateRangeCount(rule, value, callback, form[`score_dimensions`][ind])
                    }, trigger: 'blur' }
                  ]"
                  class="row-item-style"
                >
                  <el-input-number v-model="item.max_score" :min="0" :controls="false" class="end-input-style"></el-input-number>
                </el-form-item>
                <span class="icon-plus" v-if="ind === 0" @click="addScoreItem('score_dimensions')">
                  <i class="el-icon-plus"></i>
                </span>
                <span class="icon-remove" v-if="ind > 0" @click="removeScoreItem('score_dimensions', ind)">
                  <i class="el-icon-minus"></i>
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form-item>
      <el-form-item label="分值显示：" class="share-check-box" v-if="form.enable_mark && fromSystem === 'spoc'">
        <div class="people-switch">
          <el-switch v-model="form.enable_score_display" @change="changeDisplay"></el-switch>
          <span class="people-switch-tips">开启后，可对分值区间进行替换显示，最多添加6个分值区间；如满分100，合格60，分值区间就是0到100，且只能在0到59，60到100之间再进行分段；若90~100显示优秀或S，学员属于分值区间则展示优秀或S。</span>
        </div>
        <div class="dimensions-item" v-if="form.enable_score_display">
          <el-row
            v-for="(item, ind) in form.score_displays"
            class="row-style-scores"
            :key="ind"
          >
            <el-col :span="12">
              <el-form-item
                :label="`分值区间${ind + 1}`"
                label-width="88px"
                class="required-icon"
              > 
                <el-form-item
                  label=""
                  :prop="`score_displays.` + ind + '.min_score'"
                  :rules="[
                    {required: true, message: '请输入最小值', trigger: ['change', 'blur']},
                    { validator: (rule, value, callback, item) => {
                      validateRangeCount(rule, value, callback, form[`score_displays`][ind])
                    }, trigger: 'blur' }
                  ]"
                  class="row-item-style"
                >
                  <el-input-number v-model="item.min_score" :min="0" :controls="false" class="end-input-style"></el-input-number>
                </el-form-item>
                ~
                <el-form-item
                  label=""
                  :prop="`score_displays.` + ind + '.max_score'"
                  :rules="[
                    {required: true, message: '请输入最大值', trigger: ['change', 'blur']},
                    { validator: (rule, value, callback, item) => {
                      validateRangeCount(rule, value, callback, form[`score_displays`][ind])
                    }, trigger: 'blur' }
                  ]"
                  class="row-item-style"
                >
                  <el-input-number v-model="item.max_score" :min="0" :controls="false" class="end-input-style"></el-input-number>
                </el-form-item>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="显示"
                label-width="70px"
                :prop="`score_displays.` + ind + '.alias'"
                :rules="{
                  required: true, message: '显示不能为空', trigger: ['change', 'blur']
                }"
                class="required-icon"
              >
                <el-input v-model="item.alias" maxlength="5" style="width: 80%;"></el-input>
                <span class="icon-plus" v-if="ind === 0" @click="addScoreItem('score_displays')">
                  <i class="el-icon-plus"></i>
                </span>
                <span class="icon-remove" v-if="ind > 0" @click="removeScoreItem('score_displays', ind)">
                  <i class="el-icon-minus"></i>
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form-item>
      <el-form-item label="管理员确认结果：" class="work-check-box" v-if="form.enable_mark">
        <div class="people-switch">
          <el-switch :disabled="disabledEdit" v-model="form.admin_confirm_result"></el-switch>
          <span class="people-switch-tips">学期管理员需要对指定作业状态的学员的批阅结果进行确认，完成确认后学员才可见作业成绩</span>
        </div>
        <div class="total-score-box" v-show="form.admin_confirm_result">
          <el-checkbox-group v-model="form.confirm_desc">
            <el-checkbox label="1">待批阅</el-checkbox>
            <el-checkbox label="2">合格</el-checkbox>
            <el-checkbox label="3">不合格</el-checkbox>
          </el-checkbox-group>
        </div>
        <CustomTips
          class="manager-set-tips"
          title="请注意，项目发布后，不支持调整"
          IconName="el-icon-warning-outline" 
          backgroundColor="#fdf6ec" 
          color="#FF7548"
          lineHeight="40px"
          >
        </CustomTips>
      </el-form-item>
      <el-form-item label="批阅说明：" prop="mark_desc" class="project-detail-tincy" v-show="form.enable_mark && fromSystem === 'spoc'">
        <sdc-mce-editor 
          ref="Editor" 
          selector="mark_desc" 
          :env="editorEnv" 
          :content="form.mark_desc"
          :catalogue.sync="editorConfigDesc.catalogue"
          :urlConfig="editorConfigDesc.urlConfig"
          :options="editorConfigDesc.options"
          :insertItems="insertItems"
          />
      </el-form-item>
      <!--  -->
      <el-form-item label="完成条件：" prop="completeType">
        <div class="score-type-box">
          <el-checkbox :disabled="disabledEdit" v-model="submitWork" @change="changeSubmitWork($event, '3')">提交作业</el-checkbox>
          <el-checkbox :disabled="disabledEdit" v-model="readOverWork" v-if="form.enable_mark" @change="changeSubmitWork($event, '4')">批阅合格</el-checkbox>
          <el-checkbox :disabled="disabledEdit" v-model="completeWork" v-if="form.markTypeList.includes(2) && form.enable_mark" @change="changeSubmitWork($event, '5')">完成互评</el-checkbox>
        </div>
        <CustomTips
          class="score-type-tips"
          title="请注意，项目发布后，已保存的作业任务不支持修改完成条件" 
          IconName="el-icon-warning-outline" 
          backgroundColor="#fdf6ec" 
          color="#FF7548"
          lineHeight="40px"
          >
        </CustomTips>
      </el-form-item>
      <el-form-item label="任务性质：">
        <el-radio-group v-model="form.required">
          <el-radio :label="true">应学</el-radio>
          <el-radio :label="false">选学</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="任务状态：" v-if="fromSystem === 'mooc'">
        <el-radio-group v-model="form.lock_status">
          <el-radio label="1">解锁任务</el-radio>
          <el-radio label="2">锁定任务</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="任务起止时间：" prop="rangeTime" v-else>
        <el-date-picker
          v-model="form.rangeTime"
          type="datetimerange"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00', '23:59:59']"
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          disabled
        >
        </el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="自动解锁时间：">
        <el-date-picker
          v-model="form.start_time"
          type="date"
          placeholder="请选择时间">
        </el-date-picker>
        <CustomTips
          class="unlock-type-tips"
          title="如果任务被管理员手动锁定，到自动解锁时间后学员仍然无法进行任务学习" 
          IconName="el-icon-warning-outline" 
          backgroundColor="#fdf6ec" 
          color="#FF7548"
          lineHeight="40px"
          >
        </CustomTips>
      </el-form-item> -->
    </el-form>
    <!-- 底部按钮区域 -->
    <bottomFiexd @cancel="onCancel" @save="onDebounceSubmit" submitText="确定"></bottomFiexd>
    <!-- 添加定制作业选择弹框 -->
    <addHomeworkreqDialog v-if="addHomeworkReqShow" ref="addHomeworkreqDialog" :visible.sync="addHomeworkReqShow"  :semesterId="semesterId" @changeSingleData="changeSingleData"></addHomeworkreqDialog>
    <!-- 添加重复 -->
    <addErrorDialog :visible.sync="addErrorDialogShow" :taskNameList="taskNameList"></addErrorDialog>
  </div>
</template>
<script>
import CustomTips from '@/components/tips.vue'
import bottomFiexd from '@/views/components/botttomFixed.vue'
import addHomeworkreqDialog from '../../components/add-homeworkreq-dialog'
import { saveWork, updateWork, getRoleList, getFinishedCondition, getSemesterInfo, getQueryHomeworkRequirements } from '@/config/mooc.api.conf.js'
import addErrorDialog from '.././task-list/component/add-error-dialog.vue'
import { handlerDateFormat, debounce } from '@/utils/tools.js'
export default {
  components: {
    CustomTips,
    bottomFiexd,
    addErrorDialog,
    addHomeworkreqDialog
  },
  data() {
    const scoreValid = (rule, value, callback) => {
      const { teacher_mark_weight, student_mark_weight } = this.form
      if (Number(teacher_mark_weight) && Number(student_mark_weight)) {
        if (Number(teacher_mark_weight) + Number(student_mark_weight) !== 100) {
          callback(new Error('权重之和需等于100'))
        } else {
          callback()
        }
      } else {
        if (!Number(teacher_mark_weight) && !Number(student_mark_weight)) {
          callback(new Error('请输入评分权重'))
        } else {
          callback()
        }
        callback()
      }
    }
    const weightValid = (rule, value, callback) => {
      if (this.roleWeightList.length > 0) {
        const total = this.roleWeightList.reduce((prev, next) => (prev || 0) + (next.mark_weight || 0), 0)
        if (!total || total === 0) {
          callback(new Error('请输入评分权重'))
        } else if (total === 100) {
          callback()
        } else {
          callback(new Error('权重之和需等于100'))
        }
      } else {
        callback()
      }
    }

    const validateRangeCount = (rule, value, callback, row) => {
      let pointRegExp = /^\d+$/ // 小数位数 为非负整数
      if (!pointRegExp.test(value) || row.max_score <= row.min_score) {
        row.max_score <= row.min_score ? callback(new Error('最大值应大于最小值')) : callback(new Error('请输入大于等于0的整数'))
      } else {
        callback()
      }
    }
    return {
      validateRangeCount: validateRangeCount,
      form: {
        title: '',
        desc: '',
        required_id: '',
        start_time: '', // 提交开始时间
        end_time: '', // 提交结束时间
        enabled_deadline_remind: true,
        deadline_days: '',
        deadline_time: '09:00:00',
        enabled_share: false,
        enable_mark: false,
        mark_type: '',
        total_score: '',
        pass_score: '',
        teacher_mark_weight: 50,
        student_mark_weight: 50,
        student_mark_count: '',
        student_mark_start_time: '',
        student_mark_end_time: '',
        enabled_student_mark_remind: false,
        student_mark_deadline_days: '',
        student_mark_deadline_time: '09:00:00',
        enabled_student_mark_anonymous: true,
        mark_teachers: [],
        markTypeList: [], // 评分方式
        completeType: [], // 完成条件
        required: true,
        lock_status: '1',
        subTypeList: [], // 提交类型
        scoreWeight: '',
        enable_score_dimension: false, // 评分维度开关
        enable_score_display: false, // 分值显示开关
        score_dimensions: [{ score_dimension_name: '', max_score: '', min_score: '' }], // 评分维度
        score_displays: [{ min_score: '', max_score: '', alias: '' }], // 分值显示
        mark_desc: '', // 批阅说明
        rangeTime: [], // 任务起止时间
        requirement_type: 0, // 作业要求类型
        admin_confirm_result: false, // 是否管理员确认结果
        confirm_desc: [] // 管理员确认结果
      },
      selecHomeworkItem: null, // 选择的作业要求
      roleWeightList: [], // 勾选角色权重配置
      spoc_finished_condition: {},
      subTypeAll: false,
      shareList: [], // 指定人员
      shareRoleList: [], // 共享角色列表
      markRoleList: [], // 批阅角色列表
      studentMarkTime: '', // 互评时间
      fileList: [],
      submitWork: false, // 提交作业
      readOverWork: false, // 批阅合格
      completeWork: false, // 完成互评
      addErrorDialogShow: false,
      addHomeworkReqShow: false, // 作业要求列表选择弹框
      taskNameList: [],
      adminProps: {
        staffID: 'staff_id',
        staffName: 'staff_name'
      },
      curFile: {},
      getTincyWordCount: '',
      resetIndex: -1, // 重新上传下标
      rules: {
        title: [
          { required: true, message: '请输入作业名称', trigger: 'blur' }
        ],
        desc: [
          { required: true, message: '请输入作业要求', trigger: 'blur' }
        ],
        subTypeList: [
          { required: true, message: '请选择提交类型', trigger: 'change' }
        ],
        mark_teachers: [
          { required: true, message: '请选择指定老师', trigger: 'blur' }
        ],
        markTypeList: [
          { required: true, message: '请选择评分方式', trigger: 'change' }
        ],
        scoreWeight: [
          { required: true, validator: scoreValid, trigger: 'blur' }
        ],
        completeType: [
          { required: true, message: '请选择完成条件', trigger: 'change' }
        ],
        rangeTime: [{ required: true, message: '请选择起止时间', trigger: 'blur', type: 'array' }],
        scoreWeightSpoc: [{ required: true, validator: weightValid, trigger: 'change', type: 'array' }]
      },
      insertItems: [
        'link',
        'table',
        'image',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorConfig: {
        catalogue: false,
        options: { // 非必传字段
          selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false, 
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
              formatselect fontsizeselect lineheight |
              bold italic underline strikethrough |
              forecolor backcolor |
              dent align bullist numlist |
              insert codesample |
              fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      },
      editorEnv: process.env.NODE_ENV,
      selected_require: false, // 是否已选择有定制作业要求
      fileTypeArr: [
        {
          suffix: ['wmv', 'mp4', 'flv', 'avi', 'rmvb', 'mpg', 'mkv', 'mov', 'mpeg', 'x-matroska', 'quicktime'],
          file_type: 1,
          update_type: 1,
          file_type_name: '视频',
          size: 2147483648,
          size_name: '2GB',
          update_type_name: 'Video'
        },
        {
          suffix: ['w4v', 'm4a', 'wma', 'wav', 'mp3', 'amr', 'mpeg'],
          file_type: 2,
          update_type: 2,
          file_type_name: '音频',
          size: 524288000,
          size_name: '500MB',
          update_type_name: 'Audio'
        },
        {
          suffix: ['jpg', 'jpeg', 'gif', 'png', 'bmp', 'ico', 'svg'],
          file_type: 3,
          update_type: 0,
          file_type_name: '图片',
          size: 10485760,
          size_name: '10MB',
          update_type_name: 'Image'
        },
        {
          suffix: ['zip', 'rar'],
          file_type: 4,
          update_type: 3,
          file_type_name: '压缩包',
          size: 1073741824,
          size_name: '1GB',
          update_type_name: 'Zip'
        },
        {
          suffix: ['doc', 'docx', 'ppt', 'pptx', 'xls', 'xlsx', 'pdf', 'msword', 'vnd.ms-excel', 'vnd.ms-powerpoint'],
          file_type: 5,
          update_type: 3,
          file_type_name: '文档',
          size: 104857600,
          size_name: '100MB',
          update_type_name: 'Doc'
        }
        
      ],
      accepts: '.wmv,.mp4,.flv,.avi,.rmvb,.mpg,.mkv,.mov,.w4v,.m4a,.wma,.wav,.mp3,.amr,.doc,.docx,.ppt,.pptx,.xls,.xlsx,.pdf,.jpg,.jpeg,.gif,.png,.bmp,.ico,.svg,.zip,.rar',
      // spoc学期信息
      semesterInfoSpoc: {},
      editorConfigDesc: {
        catalogue: false,
        options: { // 非必传字段
          selector: '#mark_desc',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
              formatselect fontsizeselect lineheight |
              bold italic underline strikethrough |
              forecolor backcolor |
              dent align bullist numlist |
              insert codesample |
              fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      }
    }
  },
  computed: {
    // 禁止编辑
    disabledEdit() {
      // course_status 0-未发布，1-已下架，2-已结束，3-已下架
      if (this.fromSystem !== 'spoc') {
        return this.form?.course_status ? this.form.course_status !== 0 : false
      } else {
        return new Date() > new Date(this.form.rangeTime[0]) && this.workType === 'edit' ? new Date() > new Date(this.form.rangeTime[0]) : false
      }
    },
    changeSize() {
      return (size) => {
        return Math.ceil((size / 1048576) * 10) / 10 + 'M'
      }
    },
    workType() {
      return this.$route.query.workType
    },
    semesterId() {
      return this.$route.query.mooc_course_id
    },
    fromSystem() {
      return this.$route.query.from || 'mooc'
    },
    markType() {
      return (val) => {
        return this.form.markTypeList?.length && this.form.markTypeList.includes(val)
      }
    }
  },
  mounted() {
    let param = {
      scheme_type: this.fromSystem === 'spoc' ? 'spoc' : 'mooc',
      semester_id: this.$route.query.mooc_course_id
    }
    getRoleList(param).then((res) => {
      this.markRoleList = res.mark_list || []
      this.shareRoleList = res.share_list || []
    })
    // 来源为spoc加上学期id
    if (this.fromSystem === 'spoc') {
      const { workType, task_id, mooc_course_id } = this.$route.query
      const params = { 
        mooc_course_id,
        task_id: task_id || '',
        scheme_type: this.fromSystem === 'spoc' ? 'spoc' : 'mooc'
      }
      getSemesterInfo({ semesterId: mooc_course_id }).then((res) => {
        this.semesterInfoSpoc = res
      })
      if (workType === 'edit') {
        getFinishedCondition(params).then((res) => {
          this.spoc_finished_condition = res
          this.queryData()
        })
      }
    } else {
      this.queryData()
    }
  },
  methods: {
    // 点击选择定制作业要求按钮
    selectHomeworkReq() {
      this.addHomeworkReqShow = true
      if (this.selecHomeworkItem?.id) {
        this.$nextTick(() => {
          this.$refs.addHomeworkreqDialog && this.$refs.addHomeworkreqDialog.setRadioId(this.selecHomeworkItem?.id)
        })
      }
    },
    // 选择定制作业要求拿到的数据
    changeSingleData(data) {
      this.selecHomeworkItem = data
    },
    // 编辑获取数据
    queryData() {
      const { homework_id, task_id, mooc_course_id } = this.$route.query
      if (!homework_id || !mooc_course_id) return
      const params = { 
        mooc_course_id,
        task_id: task_id || '',
        scheme_type: this.fromSystem === 'spoc' ? 'spoc' : 'mooc'
      }
      updateWork(params, homework_id).then((res) => {
        const { 
          mark_type, 
          work_types, 
          shared_type, 
          teachers, 
          student_mark_start_time, 
          student_mark_end_time,
          mooc_task,
          attachment_list,
          enable_mark,
          mark_weight,
          confirm_desc,
          desc,
          requirement_type
        } = res
        const markTypeList = mark_type.split(';').map(item => Number(item)) // 评分方式
        const subTypeList = work_types.split(';') // 提交类型
        const confirmDesc = confirm_desc && confirm_desc.split(';') // 管理员确认结果
        // 提交类型全选
        this.subTypeAll = subTypeList.length === 6
        let info = ''
        let lockStatus = ''
        let requiredVal = ''
        let rangeTime = []
        if (!task_id) { // 未保存组织树再次编辑
          const cacheData = JSON.parse(localStorage.getItem(`work_info_data_${homework_id}`))
          const { finished_condition, lock_status, required } = cacheData
          info = finished_condition
          lockStatus = lock_status
          requiredVal = required
          rangeTime = cacheData.rangeTime || []
        } else { // 编辑  需要获取spoc的任务完成条件
          if (this.fromSystem !== 'spoc') {
            info = JSON.parse(mooc_task.finished_condition.replace(/'/g, '"'))
            lockStatus = mooc_task.lock_status + ''
            requiredVal = mooc_task.required
            this.roleWeightList = JSON.parse(mark_weight) || []
          } else {
            const { finished_condition, required, task_start_time, task_end_time } = this.spoc_finished_condition
            info = finished_condition
            lockStatus = this.spoc_finished_condition.lock_status + ''
            requiredVal = required
            this.roleWeightList = JSON.parse(mark_weight) || []
            rangeTime = [task_start_time, task_end_time]
          }
        }
        // 完成条件
        const completeType = info.type.split(';')
        const completeInfo = [
          { value: 3, label: 'submitWork' },
          { value: 4, label: 'readOverWork' },
          { value: 5, label: 'completeWork' }
        ]
        completeType.forEach((e) => {
          completeInfo.forEach((v, index) => {
            if (Number(e) === v.value) {
              // 'submitWork', 'readOverWork', 'completeWork'
              this[v.label] = true
            }
          })
        })
        let descStr = desc
        if (requirement_type === 1 && desc && desc.indexOf(';') !== -1) {
          let required_id_arr = desc.split(';')
          descStr = required_id_arr[0]
        }
        this.form = {
          ...res,
          markTypeList,
          subTypeList,
          desc: descStr,
          confirm_desc: confirmDesc,
          mark_teachers: teachers,
          lock_status: lockStatus,
          required: requiredVal,
          completeType,
          rangeTime
        }
        // 共享范围
        this.shareList = shared_type.split(';').map(item => Number(item))
        // 指定老师
        if (teachers?.length && this.markType(1) && enable_mark) {
          this.$nextTick(() => {
            this.$refs.teacherSelectorRef.setSelected(this.form.mark_teachers)
          })
        }
        // 互评时间
        this.studentMarkTime = student_mark_start_time ? [student_mark_start_time, student_mark_end_time] : ''
        // 参考资料数据回显
        this.fileList = attachment_list.map((e) => {
          return {
            ...e,
            name: e.file_name,
            status: 2,
            processValue: 100
          }
        })
        // 作业要求指定角色回显
        if (this.form.requirement_type === 1) {
          this.form.required_id = this.form.desc
          getQueryHomeworkRequirements({ id: this.form.desc }).then((result) => {
            this.selecHomeworkItem = result
            if (this.selecHomeworkItem) {
              this.selected_require = true
            }
          })
        }
      })
    },
    // 添加防抖，防止重复提交作业
    onDebounceSubmit: debounce(function () {
      this.onSubmit()
    }, 300),
    onSubmit() {
      const { id, workType } = this.$route.query
      // 简介--需要校验
      if (this.getTincyWordCount) {
        this.form.desc = this.$refs['workEditor'].getContent()
      }
      if (!this.form.enabled_share) { // 共享查看关闭
        this.shareList = []
      }
      if (!this.form.enable_mark) { // 批阅关闭
        this.form = {
          ...this.form,
          total_score: '',
          pass_score: '',
          markTypeList: [],
          teacher_mark_weight: 50,
          student_mark_weight: 50,
          mark_teachers: [],
          student_mark_count: '',
          enabled_student_mark_remind: false,
          student_mark_deadline_days: 1,
          student_mark_deadline_time: '09:00:00',
          enabled_student_mark_anonymous: true,
          admin_confirm_result: false
        }
        this.studentMarkTime = ''
        this.readOverWork = false
        this.completeWork = false
      }
      if (!this.form.admin_confirm_result) { // 管理员确认结果未开启
        this.form.confirm_desc = []
      }
      // 未勾选老师批阅
      if (this.form.enable_mark && !this.markType(1)) {
        this.form.mark_teachers = []
      }
      // 提交截止时间关闭
      if (!this.form.enabled_deadline_remind) {
        this.form.deadline_days = 1
        this.form.deadline_time = '09:00:00'
      }
      // 旧数据student_mark_weight,teacher_mark_weight设置
      if (this.fromSystem !== 'spoc' && this.roleWeightList.length > 0) {
        const student_weight = this.roleWeightList.find(item => +item.mark_type === 2) && this.roleWeightList.find(item => +item.mark_type === 2).mark_weight
        const teacher_weight = this.roleWeightList.find(item => +item.mark_type === 1) && this.roleWeightList.find(item => +item.mark_type === 1).mark_weight
        this.form.student_mark_weight = student_weight || 0
        this.form.teacher_mark_weight = teacher_weight || 0
      }
      if (this.fromSystem === 'spoc') {
        this.form.mark_desc = this.$refs['Editor'].getContent()
      }

      if (this.form.requirement_type === 1) {
        // this.form.desc = this.selecHomeworkItem ? this.selecHomeworkItem?.id + '' : ''
        if (this.selected_require && this.selecHomeworkItem && this.form.required_id + '' === this.selecHomeworkItem?.id + '') {
          // 已选作业要求但是没换
          this.form.desc = this.selecHomeworkItem?.id + ''
        } else if (this.selected_require && this.selecHomeworkItem && this.form.required_id + '' !== this.selecHomeworkItem?.id + '') {
          // 已选作业要求且换了
          this.form.desc = this.selecHomeworkItem?.id
        } else if (!this.selected_require && this.selecHomeworkItem) {
          // 原来没选 但是现在第一次选了
          this.form.desc = this.selecHomeworkItem?.id + ''
        } else if (!this.selecHomeworkItem) {
          this.form.desc = ''
        }
      }
      const {
        title,
        desc,
        start_time, 
        end_time, 
        enabled_deadline_remind,
        deadline_days,
        deadline_time,
        enabled_share,
        enable_mark,
        total_score,
        pass_score,
        student_mark_weight,
        teacher_mark_weight,
        student_mark_count,
        enabled_student_mark_remind,
        student_mark_deadline_days,
        student_mark_deadline_time,
        enabled_student_mark_anonymous,
        mark_teachers,
        markTypeList,
        completeType,
        required,
        lock_status,
        rangeTime,
        enable_score_dimension,
        score_dimensions,
        mark_desc,
        requirement_type,
        admin_confirm_result,
        confirm_desc
      } = this.form
      // 互评时间
      const student_mark_start_time = this.studentMarkTime?.length ? this.studentMarkTime[0] : ''
      const student_mark_end_time = this.studentMarkTime?.length ? this.studentMarkTime[1] : ''
     
      // 共享查看开启
      if (enabled_share && !this.shareList?.length) {
        this.$message.warning('请选择指定人员')
        return
      }
      if (enable_mark && (!total_score || !pass_score)) {
        this.$message.warning('请输入的总分或合格分')
        return
      }
      if (enable_mark && total_score < pass_score) {
        this.$message.warning('输入总分不能小于合格分')
        return
      }
      if (new Date(end_time).getTime() < new Date(start_time).getTime()) {
        this.$message.warning('作业提交开始时间需早于作业提交截止时间')
        return
      }
      // 来源spoc校验提交时间是否在学期周期内
      if (this.fromSystem === 'spoc') {
        // 判断目标时间段是否在原始时间段内
        const startTimestamp = new Date(start_time).getTime()
        const endTimestamp = new Date(end_time).getTime()
        const targetStartTimestamp = new Date(this.semesterInfoSpoc.startTime).getTime()
        const targetEndTimestamp = new Date(this.semesterInfoSpoc.endTime).getTime()
        if (!(targetStartTimestamp <= startTimestamp && targetEndTimestamp >= endTimestamp)) {
          this.$message.warning('作业提交时间需在学期周期时间内')
          return
        }
      }
      if (markTypeList.includes(2) && this.studentMarkTime?.length && end_time && new Date(student_mark_end_time).getTime() < new Date(end_time).getTime()) {
        this.$message.warning('互评截止时间需晚于作业提交截止时间')
        return
      }
      // 来源spoc校验互评时间是否在学期周期内
      if (this.fromSystem === 'spoc' && markTypeList.includes(2)) {
        if (this.studentMarkTime?.length === 0) {
          this.$message.warning('请选择互评时间')
          return
        }
        const startTimestamp = new Date(student_mark_start_time).getTime()
        const endTimestamp = new Date(student_mark_end_time).getTime()
        const targetStartTimestamp = new Date(this.semesterInfoSpoc.startTime).getTime()
        const targetEndTimestamp = new Date(this.semesterInfoSpoc.endTime).getTime()
        if (!(targetStartTimestamp <= startTimestamp && targetEndTimestamp >= endTimestamp)) { 
          this.$message.warning('作业互评时间需在学期周期时间内')
          return
        }
      }
      // 评分方式
      if (!student_mark_count && markTypeList.includes(2)) {
        this.$message.warning('请输入互评份数')
        return
      }
      // spoc校验互评份数不多于学期人数
      if (this.fromSystem === 'spoc' && student_mark_count > this.semesterInfoSpoc.semesterTraineeCount) {
        this.$message.warning('互评份数不能多于学期人数')
        return
      }
      if (end_time && enabled_deadline_remind && (!deadline_days || !deadline_time)) {
        this.$message.warning('请输入提交截止天数和时间')
        return
      }
      if (enabled_student_mark_remind && (!student_mark_deadline_days || !student_mark_deadline_time)) {
        this.$message.warning('请输入互评截止天数和时间')
        return
      }
      // spoc校验评分维度
      if (this.fromSystem === 'spoc' && enable_score_dimension) {
        const sum = score_dimensions.reduce((prev, cur) => {
          return cur.max_score + prev
        }, 0)
        if (sum > total_score) {
          this.$message.warning('维度最高分值总和不能大于总分')
          return
        }
      }
      // 作业提交类型
      let work_types = ''
      this.form.subTypeList.forEach((e) => {
        work_types += `${e};`
      })
      work_types = work_types.slice(0, -1)
      // 指定人员
      let shared_type = ''
      this.shareList.forEach((e) => {
        shared_type += `${e};`
      })
      shared_type = shared_type.slice(0, -1)
      // 评分方式
      let mark_type = ''
      markTypeList.forEach((e) => {
        mark_type += `${e};`
      })
      mark_type = mark_type.slice(0, -1)
      
      // 完成条件
      const type = completeType.join(';')

      const params = {
        ...this.form,
        title,
        desc,
        work_types, // Text：文本Image：图片Audio：音频Video：视频Doc：文档Zip：压缩包
        start_time, // 提交开始时间
        end_time, // 提交结束时间
        enabled_deadline_remind, // 是否开启截止提醒
        deadline_days, // 截止前几天
        deadline_time, // 截止提醒时间
        enabled_share, // 是否开始共享查看
        shared_type, // 共享范围类型 1其他学员2学员直接上级3学员所属组织BP
        enable_mark, // 是否开启批阅
        mark_type, // 批阅类型1-老师，2学员互评
        total_score, // 总分
        pass_score, // 及格分
        teacher_mark_weight: student_mark_weight ? (teacher_mark_weight || 0) : 100, // 老师互评权重
        student_mark_weight: teacher_mark_weight ? (student_mark_weight || 0) : 100, // 学生互评权重
        student_mark_count, // 互评份数
        student_mark_start_time, // 互评开始时间
        student_mark_end_time, // 互评结束时间
        enabled_student_mark_remind, // 是否开启互评提醒
        student_mark_deadline_days,
        student_mark_deadline_time,
        enabled_student_mark_anonymous, // 是否开启匿名互评
        mark_teachers, // 批阅老师
        finished_condition: { // 完成条件
          type,
          condition: null
        },
        required,
        lock_status,
        attachment_list: this.fileList, // 附件
        mark_roles: this.roleWeightList, // 批阅角色
        scheme_type: this.fromSystem === 'spoc' ? 'spoc' : 'mooc',
        mark_desc,
        requirement_type,
        admin_confirm_result,
        confirm_desc: confirm_desc.join(';')
      }
      // 学期类型为岗前学期 添加student_mark_remind_channel
      if (this.fromSystem === 'spoc' && this.semesterInfoSpoc.trainingTypeId === 14) {
        params.student_mark_remind_channel = 'mail'
      }
      // 来源spoc 传递批阅角色mark_roles
      let data = {
        ...params,
        task_name: title,
        act_name: title,
        act_type: 22,
        resource_type: 'HomeWork',
        resource_type_name: '作业',
        scheme_type: this.fromSystem === 'spoc' ? 'spoc' : 'mooc',
        mark_roles: this.roleWeightList,
        taskStartTime: rangeTime[0],
        taskEndTime: rangeTime[1]
      }
      this.$refs['form'].validate((valid) => {
        if (valid) {
          saveWork(params).then((res) => {  
            data = {
              ...data,
              act_id: res,
              id,
              workType
            }
            if (res) {
              localStorage.setItem(`work_info_data_${res}`, JSON.stringify(data))
              window.opener.workReConnect(data)
              this.onCancel()
            }
          })
        } else {
          return false
        }
      })
    },
    // 全选
    changeSubTypeALl(val) {
      if (val) {
        this.$refs.form.clearValidate('subTypeList')
        this.form.subTypeList = ['Text', 'Image', 'Audio', 'Video', 'Doc', 'Zip']
      } else {
        this.form.subTypeList = []
      }
    },
    changeSubmitType(val) {
      this.form.subTypeList = val
      if (val.length < 6) {
        this.subTypeAll = false
      } else { // 全部都选中
        this.$refs.form.clearValidate('subTypeList')
        this.subTypeAll = true
      }
    },
    // 完成条件
    compeleteCondition(val) {
      if (val?.length === 2) {
        this.submitWork = true
      } else if (this.submitWork && val?.length === 2) {
        this.submitWork = false
        // 当只勾选学员互评时，完成条件勾选批阅合格
      } else if (val.includes('2') && (this.form.markTypeList.includes(2) && this.form.markTypeList?.length === 1)) {
        const text = 
        '当前作业评分方式仅勾选“学员互评”，如果任务完成条件选择了“批阅合格”,' +
        '可能会出现某个学员作业没有其他学员评价的情况，进而不满足完成条件导致任务无法完成'
        this.$confirm(`${text}`, '确定选中批阅合格吗？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          this.$message({
            type: 'success',
            message: '已选中批阅合格'
          })
        })
      }
    },
    // 完成条件勾选
    changeSubmitWork(ev, type) {
      const { completeType, markTypeList } = this.form
      const completeInfo = [
        { value: 3, label: 'submitWork' },
        { value: 4, label: 'readOverWork' },
        { value: 5, label: 'completeWork' }
      ]
      if (ev) {
        completeType.push(type)
        // markTypeList.includes('2') 学员互评
        // 勾选了“批阅合格”或者“完成互评”，必须勾选提交作业
        if ((completeType.includes('4') || completeType.includes('5')) && !completeType.includes('3')) {
          this.submitWork = true
          completeType.push('3')
        }
        // 当只勾选学员互评时，完成条件勾选批阅合格需要弹窗提示
        if (type !== '5' && completeType.includes('4') && markTypeList.includes(2) && markTypeList?.length === 1) {
          const text = 
          '当前作业评分方式仅勾选“学员互评”，如果任务完成条件选择了“批阅合格”,' +
          '可能会出现某个学员作业没有其他学员评价的情况，进而不满足完成条件导致任务无法完成'
          this.$confirm(`${text}`, '确定选中批阅合格吗？', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(() => {
            // this.$message({
            //   type: 'success',
            //   message: '已选中批阅合格'
            // })
          }).catch(() => {
            const completeIndex = completeType.findIndex((e) => e === type)
            completeType.splice(completeIndex, 1)
            completeInfo.forEach((e) => {
              if (Number(type) === e.value) {
                // 'submitWork', 'readOverWork', 'completeWork'
                this[e.label] = false
              }
            })
          })
        }
      } else {
        const completeIndex = completeType.findIndex((e) => e === type)
        completeType.splice(completeIndex, 1)
        completeInfo.forEach((e) => {
          if (Number(type) === e.value) {
            // 'submitWork', 'readOverWork', 'completeWork'
            this[e.label] = false
          }
        })
      }
    },
    onCancel() {
      setTimeout(() => {
        window.close()
      }, 1000)
    },
    // 授权人员
    changeStaff(data) {
      this.form.mark_teachers = data
    },
    handleMarkType(val) {
      // 指定老师
      if (this.form.mark_teachers?.length) {
        this.$nextTick(() => {
          this.$refs.teacherSelectorRef && this.$refs.teacherSelectorRef.setSelected(this.form.mark_teachers)
        })
      }  
      // spoc勾选角色设置互评权重
      if (Array.isArray(val)) {
        this.roleWeightList = this.markRoleList.filter((item) => val.indexOf(item.mark_type) > -1)
        // mooc设置初始权重
        if (this.fromSystem !== 'spoc') {
          this.roleWeightList = this.roleWeightList.map((item) => {
            if (this.roleWeightList.length === 1) {
              return {
                ...item,
                mark_weight: 100
              }
            } else {
              return {
                ...item,
                mark_weight: 50
              }
            }
          }) 
        }
      }
      // spoc 如配置互评任务起止时间取提交时间及互评结束时间
      if (Array.isArray(val) && this.fromSystem === 'spoc' && val.includes(2)) {
        this.form.rangeTime = [this.form.start_time, (this.studentMarkTime.length ? this.studentMarkTime[1] : '')]
      } else {
        this.form.rangeTime = [this.form.start_time, this.form.end_time]
      }
    },
    beforeAvatarUpload(file) {
      this.curFile = {}
      let typeObj = JSON.parse(JSON.stringify(this.fileTypeArr))
      let arrL = typeObj.length
      let isSize = false
      let fileType = file.type ? file.type : file.name.substring(file.name.lastIndexOf('.') + 1)
      for (let i = 0; i < arrL; i++) {
        let suffixL = typeObj[i].suffix.length
        for (let v = 0; v < suffixL; v++) {
          let suffix = typeObj[i].suffix
          let reg = RegExp(suffix[v])
          if (reg.exec(fileType)) {
            let size = typeObj[i].size
            isSize = file.size < size
            if (isSize) {
              this.curFile = typeObj[i]
              this.curFile.size = file.size
              break
            } else {
              this.$message.error(`上传${typeObj[i].file_type_name}大小不能超过 ${typeObj[i].size_name}!`)
              return false
            }
          }
        }
      }
      return isSize
    },
    // 取消上传
    deleteUpload(item) {
      this.fileList = this.fileList.filter((e) => e.content_id !== item.content_id)
    },
    // 重新上传
    resetUpload(item) {
      this.resetIndex = this.fileList.findIndex((e) => e.content_id === item.content_id)
      document.getElementById('work-upload-btn').click()
    },
    // 上传
    onUpload({ file }) {
      // this.$sdc.loading(`${this.curFile.file_type_name}上传中`)
      let _this = this
      /* eslint-disable*/
      let authUrl = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_PORTAL_HOST_WOA : process.env.VUE_APP_PORTAL_HOST
      new contentCenter.uploadFile({
        file: file,
        type: _this.curFile.update_type, // 0表示上传图片，1视频 2音频 3文档
        appId: 'QLearningService',
        operateAuthUrl:`${authUrl}/training-portal-common/api/v1/portal/user/common/uploadOperateSignature`,
        onSuccess(res) {
          // _this.$message.success(`${_this.curFile.file_type_name}上传成功`);
          _this.fileList = _this.fileList.map((e) => {
            if(e.file_name === file.name) {
              return {
                ...res[0],
                ...e,
                file_size:  _this.curFile.size,
                file_type:  _this.curFile.update_type_name,
                created_at: handlerDateFormat(new Date(), '-'),
                status: 2 // 上传成功
              }
            }
            return e
          })
        },
        onProgress(info) {
          const percent = parseInt(info.percent * 10000) / 100
          if (_this.fileList?.length) {
            let index = 0
            if(_this.resetIndex >= 0) { // 重新上传
              index = _this.resetIndex
              _this.resetIndex = -1 // 重置避免影响
            } else { // 正常上传
              index = _this.fileList.findIndex((e) => e.file_name === file.name) 
            }
            if(index >= 0) {
              _this.fileList[index] = {
                status: 1,
                processValue: percent,
                file_name: file.name,
              }
            } else {
              _this.fileList.push({
                processValue: percent,
                file_name: file.name,
                status: 1 // 上传中
              })
            }
          } else {
            _this.fileList.push({
              processValue: percent,
              file_name: file.name,
              status: 1 // 上传中
            })
          }
          // 更新数据
          _this.fileList = _this.fileList.slice()
        },
        onError(err) {
          _this.$message.error('上传失败');
          if (_this.fileList?.length) {
            _this.fileList.forEach((e) => {
              if(e.file_name === file.name) {
                e.status = 0
                e.file_name = file.name
              } else {
                _this.fileList.push({
                  file_name: file.name,
                  status: 0 // 上传失败
                })
              }
            })
          } else {
            _this.fileList.push({
              file_name: file.name,
              status: 0 // 上传失败
            })
          }
        }
      })
      /* eslint-disable*/
    },
    getWordCount(val) {
      // 防止只上传图片
      const textVal = this.$refs['workEditor'].getContent()
      let imgVal = null
      textVal.replace(/<img.*?(?:>|\/>)/gi, (match, capture) => {
        imgVal = capture
      })
      this.getTincyWordCount = val || imgVal
      if (this.getTincyWordCount) {
        this.$refs.form.clearValidate('desc')
      } else {
        this.form.desc = ''
      }
    },
    handleValidor(value, num) {
      if (value) {
        const china = value.match(/[\u4e00-\u9fa5]/g)
        const zhCount = china && china.join('').length
        const enCount = Math.ceil((value.length - zhCount) / 2)
        const total = zhCount + enCount
        if (total > num) {
          this.form.title = value.slice(0, -1)
        }
        return zhCount + enCount 
      }
      return 0
    },
    titleOver(e, index) {
      const target = e.target
      this.$set(
        this.fileList[index],
        'isOverflow',
        target.scrollWidth > target.clientWidth
      )
    },
    setRangeTime(val) {
      if (this.fromSystem !== 'spoc' ) return
      if (this.markType(2)) {
        if (Array.isArray(val) ) {
          this.form.rangeTime = [this.form.start_time, Array.isArray(this.studentMarkTime) ? this.studentMarkTime[1] : val[1]]
        }
      } else {
        this.form.rangeTime = [this.form.start_time, this.form.end_time]
      }
    },
    // 添加周期/分值项
    addScoreItem(key) {
      const item = key === 'score_dimensions' ? { score_dimension_name: '', max_score: '', min_score: '' } : { min_score: '', max_score: '', alias: '' }
      if (this.form[key].length > 5) return 
      this.form[key].push(item)
    },
    // 删除周期/分值项
    removeScoreItem(key, id) {
      if (this.form[key].length <= 1) return
      this.form[key].splice(id, 1)
    },
    changeDimension(value) {
      if(!this.form.score_dimensions) this.form.score_dimensions = []
      if (value && this.form.score_dimensions.length === 0) {
        this.form.score_dimensions.push({ score_dimension_name: '', max_score: '', min_score: '' })
      }
    },
    changeDisplay(value) {
      if(!this.form.score_displays) this.form.score_displays = []
      if (value && this.form.score_displays.length === 0) {
        this.form.score_displays.push({ min_score: '', max_score: '', alias: '' })
      }
    }
  }
}
</script>
<style>
  @import '~@/assets/css/center.less';
  #app {
    overflow: auto;
  }
</style>
<style lang="less" scoped>
.work-container {
  background: #fff;
  margin: 20px 24px;
  margin-bottom: 90px;
  padding-bottom: 36px;
  :deep(.buttom) {
    .inner {
      width: 100%
    }
  }
  .title {
    color: #000000e6;
    font-size: 16px;
    font-weight: 600;
    padding: 16px 20px;
    border: 1px solid #f3f3f3ff;
  }
  .red-tips {
    color: #E34D59;
    font-size: 12px;
    line-height: 20px;
    margin-top: 8px;
  }
  :deep(.el-checkbox) {
    margin-right: 24px;
    .el-checkbox__label {
      padding-left: 8px;
      color: #000000e6;
    }
  }
  :deep(.el-checkbox:last-of-type) {
    margin-right: 0px;
  }
  :deep(.project-detail-tincy) {
    width: 1200px;

    .tox.tox-tinymce {
      border: 1px solid #ccc !important;
      height: 450px;

      .tox-sidebar-wrap .tox-edit-area {
        min-height: 450px !important;
      }
    }
    .req-select-btn {
      margin-bottom: 18px;
      width: 210px;
      height: 32px;
      line-height: 30px;
      padding: 0 16px;
      border-radius: 3px;
      border: 1px solid #0052D9;
      background: #FFF;
      color: #0052d9;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-weight: 400;
      cursor: pointer;
    }
  }
  :deep(.sdc-staff-selector) {
    width: 496px;
  }
  .input-style {
    position: relative;
    :deep(.el-form-item__content) {
      width: 496px;
    }
    :deep(.el-input) {
      .el-input__inner {
        padding-right: 70px;
      }
      .el-input__suffix {
        position: absolute;
        right: 43px;
      }
    }
  }
  .custom-el-input-count {
    color: #ACACAC;
    background: #FFF;
    position: absolute;
    font-size: 12px;
    bottom: 6px;
    right: 6px;
    line-height: 20px;
  }
  .form {
    margin-top: 20px;
    .el-form-item {
      margin-bottom: 24px;
    }
    .line {
      margin-right: 10px;
      margin-left: 10px;
    }
    .read-over-tips {
      width: 470px;
      margin-top: 22px;
    }
    .score-type-tips {
      width: 480px;
      margin-top: 10px;
    }
    .unlock-type-tips {
      width: 520px;
      margin-top: 22px;
    }
    .work-request, .tips {
      color: #00000066;
      font-size: 12px;
      line-height: 20px;
      display: flex;
      align-items: center;
      margin-top: 8px;
      i {
        margin-right: 4px;
        font-size: 20px;
      }
    }
    .upload-progress-status {
      background: #fafafa;
      width: 800px;
      margin-top: 16px;
      font-size: 14px;
      .upload-item-box {
        line-height: 22px;
        padding: 16px;
        display: flex;
        justify-content: space-between;
        .progress-left-content {
          display: flex;
          align-items: center;
          .progress-title {
            overflow: hidden;
            word-break: break-all;
            text-overflow: ellipsis;
            max-width: 220px;
            display: inline-block;
            white-space: nowrap;
          }
          .progress-loading {
            margin-left: 8px;
            color: #0052d9;
            i {
              margin-right: 5px
            }
            .el-icon-success {
              color: #2BA471;
            }
            .el-icon-error {
              color: #D54941;
            }
          }
        }
        .progress-right-content {
          color: #00000066;
          font-size: 12px;
          .progress-time {
            margin-left: 16px;
            margin-right: 40px;
          }
          .el-button {
            padding: unset;
          }
          .el-button+.el-button {
            margin-left: 20px;
          }
        }
      }
    }
    .end-time {
      :deep(.el-form-item__content) {
        display: flex;
        align-items: center
      }
      .input-time-box {
        margin-left: 8px;
      }
      .input-time-title {
        margin-right: 8px;
      }
      .input-unit {
        margin-left: 8px;
        margin-right: 12px
      }
      .input-time-tips {
        margin-left: 8px;
        color: #000000e6;
        font-size: 14px;
      }
      
    }
    .share-check-box,.work-check-box {
      .people-switch {
        display: flex;
        align-items: center;
      }
      .people-switch-tips {
        margin-left: 8px;
        font-size: 12px;
        color: #00000066;
      }
      .manager-set-tips {
        margin-top: 12px;
        width: 272px;
      }
      .share-people,.total-score-box {
        display: flex;
        border-radius: 4px;
        background: #f7f7f7ff;
        padding: 13px 16px;
        line-height: 22px;
        width: fit-content;
        margin-top: 10px;
        .people-title,.total-title {
          color: #00000099;
          font-size: 14px;
          margin-right: 10px;
          width: 70px;
        }
      }
      .dimensions-item {
        border-radius: 4px;
        background: #f7f7f7ff;
        padding: 13px 16px;
        line-height: 22px;
        width: fit-content;
        margin-top: 10px;
        .row-item-style{
          display: inline-block;
        }
        .end-input-style{
          width: 70px;
        }
        .el-input-number{
          line-height: 32px;
        }
        .icon-plus,.icon-remove{
          margin: 0 8px;
        } 
        ::v-deep .el-form-item.required-icon{
          .el-form-item__label:before {
            content: '*';
            color: #F81D22;
            margin-right: 4px;
          }
        }
      }
    }
    .work-check-box {
      .total-score-box {
        padding: 8px 16px;
        align-items: center;
        width: 350px;
        .right-score {
          margin-left: 24px
        }
      }
    }
    .score-type-box {
      display: flex;
      .tips {
        margin-left: 8px;
      } 
      // .submit-work-check {
      //   margin-right: 24px;
      // }
    }
    .score-weight-box {
      display: flex;
      .teacher-title {
        color: #000000e6;
        font-size: 14px;
        margin-right: 10px;
      }
      .left-weight{ 
        margin-right:12px;
      }
      .right-weight {
        margin-left: 24px;
      }
    }
    .comment-set-tips {
      width: 490px;
      margin-top: 22px;
    }
    .comment-set {
      width: 880px;
      // height: 190px;
      border-radius: 4px;
      // opacity: 1;
      background: #f7f7f7ff;
      padding: 12px 16px;
      .comment-label {
        margin-right: 12px;
      }
      .comment-input-num {
        margin-right: 8px;
        margin-left: 8px;
      }
      .switch-end-time {
        display: flex;
        align-items: center
      }
      .switch-title {
        margin-right: 8px;
        margin-left: 8px;
      }
      .comment-unit::before {
        content: '*';
        color: red;
        margin-right: 2px;
      }
      .comment-box,
      .comment-time,
      .comment-end,
      .comment-name {
        display: flex;
        .end-time-tips {
          color: #000000e6;
          font-size: 12px;
        }
        // .red-tips {
        //   color: #E34D59;
        //   font-size: 12px;
        // }
        .right-tips {
          margin-left: 8px;
          margin-top: unset;
          line-height: 32px;
        }
      }
      .item-comment + .item-comment {
        margin-top: 16px;
      }
    }
    .sub_type_list {
      display: flex;
      .check-all {
        margin-right: 24px;
      }
    }
    :deep(.el-upload--text) {
      text-align: left
    }
    :deep(.el-upload-list--text) {
      width: 250px;
      .el-upload-list__item-name {
        .el-icon-document {
          display: none
        }
      }
    }
  }
  .relation-selected{
    border-radius: 4px;
    background:#ECF2FE;  
    padding: 6px 16px;
    color: #000000e6;
    font-size: 14px;
    margin-right: 14px;
  }
  .relation-btn{
    border-radius: 3px;
    border: 1px solid #0052D9;
    color: #0052D9;
  }
}
</style>
