.avatar-uploader {
  display: flex;
  align-items: center;
}
.avatar-uploader {
  :deep(.el-upload) {
    width: 200px;
    height: 125px;
    background-color: #FBFBF9;
    border: 1px dashed #DEDEDE;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
}
.width270 {
  :deep(.el-upload){
    width: 270px;
  }
}
.avatar-uploader :deep(.el-upload:hover) {
  border-color: #0052D9;
}
.el-icon-upload{
  font-size: 40px;
}
.el-upload__tip{
  margin-top: 8px;
  font-size: 12px;
  line-height: 17px;
  color:#999999;
  p:nth-child(2) {
    margin-top: 4px;
  }
  p:last-child {
    font-size: 14px;
    margin-top: 10px;
    font-weight: bold;
    display: inline-block;
  }
}
.upload-img-content {
  display: flex;
  position: relative;
  border-radius: 6px;
  border: 1px dashed #d9d9d9;
  box-sizing: content-box;
  width: 200px;
  height: 133px;
  box-sizing: border-box;
  img {
    border-radius: 6px;
    width: 100%;
    height: 100%;
  }
  .delete-bg {
    height: calc(100% + 3px);
    width: calc(100% + 3px);
    opacity: 0;
    position: absolute;
    top:50%;
    left: 50%;
    transform: translate(-50%,-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 6px;
    span{
      cursor: pointer;
    }
    span+span {
      margin-left: 15px;
    }
    i {
      font-size: 24px;
      color:#fff;
    }
  }
}
.upload-img-content:hover {
  .delete-bg {
    background-color: rgba(0,0,0,.4);
    opacity: 1;
    transition: opacity .3s;
  }
}