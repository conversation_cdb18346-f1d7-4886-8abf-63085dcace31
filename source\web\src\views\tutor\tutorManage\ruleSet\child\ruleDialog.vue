<template>
  <el-Dialog 
  :visible="visible" 
  width="600px"
  :title="dialogTitle"
  :close-on-click-modal="false"
  @close="cancel" 
  custom-class="custom-rule-dialog" 
  >
    <CustomTips
    :title="tipsTitle" 
    IconName="el-icon-warning-outline" 
    backgroundColor="#fdf6ec" 
    color="#FF7548"
    :class="['rule-tips', { 'isCompanyTips': !isCompany }]"
    >
    </CustomTips>
    <div class="use-box" v-if="isCompany">
      <span class="label">启用个性化规则：</span>
      <el-switch
        v-model="switchValue"
        active-text="开"
        inactive-text="关"
      >
      </el-switch>
    </div>
    <el-form v-show="switchValue" ref="form" :model="form" :rules="rule" label-width="100px" label-position="left">
      <el-form-item label="专业职级" prop="level_requirement">
        <el-input style="width:96px" v-model="form.level_requirement"></el-input>
        <span class="label">级及以上</span>
        <span v-if="isCompany" class="y-tips">公司规则：<span>{{companyRuleData.level_requirement}}</span>级及以上</span>
      </el-form-item>
      <el-form-item label="司龄" prop="working_years_requirement">
        <el-input-number size="small" style="width:96px" :min="0" v-model="form.working_years_requirement" controls-position="right"></el-input-number>
        <span class="label">年及以上</span>
        <span v-if="isCompany" class="y-tips">公司规则：<span>{{companyRuleData.working_years_requirement}}</span>年及以上</span>
      </el-form-item>
      <el-form-item label="绩效" prop="performance_requirement">
        <span class="label performance-label">最近</span>
        <el-input style="width:96px" v-model="form.performance_requirement"></el-input>
        <span class="label">次绩效GOOD及以上</span>
        <span v-if="isCompany" class="y-tips">公司规则：近<span>{{companyRuleData.performance_requirement}}</span>次</span>
      </el-form-item>
      <el-form-item label="BG工作年限" prop="bg_work_requirement">
        <el-input style="width:96px" v-model="form.bg_work_requirement"></el-input>
        <span class="label">年及以上</span>
        <span v-if="isCompany" class="y-tips">
          公司规则：
          <span v-if=companyRuleData.bg_work_requirement>{{ companyRuleData.bg_work_requirement }}年及以上</span>
          <span v-else>-</span>
        </span>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel" size="small">取消</el-button>
      <el-button @click="confirm" type="primary" size="small">保存</el-button>
    </span>
  </el-Dialog>
</template>
<script>
import CustomTips from '@/components/tips.vue'
import { 
  saveRule
} from '../../../api/tutor.api.conf'
export default {
  props: {
    visible: Boolean,
    companyRuleData: Object
  },
  data() {
    return {
      switchValue: false,
      form: {
        level_requirement: '',
        working_years_requirement: undefined,
        performance_requirement: '',
        bg_work_requirement: ''
      },
      rule: {
        level_requirement: [
          { required: true, message: '请输入专业职级', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: '请输入正整数', trigger: 'blur' }
        ],
        working_years_requirement: [
          { required: true, message: '请输入司龄', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: '请输入正整数', trigger: 'blur' }
        ],
        performance_requirement: [
          { required: true, message: '请输入绩效', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: '请输入正整数', trigger: 'blur' }
        ],
        bg_work_requirement: [
          { pattern: /^[1-9]\d*$/, message: '请输入正整数', trigger: 'blur' }
        ]
      }
    }
  },
  components: {
    CustomTips
  },
  computed: {
    dialogTitle() {
      const tips = this.form.bg_id === 0 ? `${this.form.bg_name}基础规则` : `${this.form.bg_name}个性化规则`
      return `修改门槛资格-${tips}`
    },
    isCompany() {
      return this.form.bg_id !== 0
    },
    tipsTitle() {
      return !this.isCompany ? '完成修改后规则实时生效，可能会同步调整BG的个性化规则配置，请谨慎操作' : '完成修改后规则实时生效，BG配置的个性化规则需高于公司基础要求，请谨慎操作'
    }
  },
  methods: {
    initData(data) {
      const res = JSON.parse(JSON.stringify(data))
      this.switchValue = res.enabled
      this.form = res
    },
    cancel() {
      this.$emit('update:visible', false)
    },
    confirm() {
      const params = {
        ...this.form,
        enabled: this.switchValue
      }
      this.$refs['form'].validate((valid) => {
        if (valid) {
          saveRule(params).then((res) => {
            console.log('保存', res)
            this.$emit('updateRule')
            this.$message.success('保存成功')
            this.cancel()
          })
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
:deep(.custom-rule-dialog) {
  .el-dialog__header{
    padding: 24px 32px 0px;
    font-size: 16px;
    font-weight: bolder;
    border-bottom: unset
  }
  .el-dialog__body {
    padding: 24px 32px 10px;
  }
  .rule-tips {
    display: flex;
    align-items: flex-start;
    height: 52px;
    border-radius: 3px;
    margin-bottom: 16px;
    padding-top: 6px;
    line-height: 20px;
    i {
      margin-top: 3px;
    }
  }
  .isCompanyTips {
    line-height: 40px;
    display: flex;
    align-items: center;
    padding-top: unset;
  }
  .el-form {
    .performance-label {
      margin-right: 8px;
    }
    .el-input, .el-input-number {
      margin-right: 8px;
    }
    .y-tips {
      margin-left: 16px;
      color: #e37318;
    }
    .el-form-item {
      margin-bottom: 20px;
    }
    .el-form-item__label, .label {
      color: #00000099;
    }
  }
  .use-box {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
    .label {
      color: #00000099;
    }
  }
  .el-switch {
    position: relative;
    .el-switch__label {
      position: absolute;
      margin-left: unset;
      margin-right: unset;
      color: #fff;
      font-size: 10px;
      transform: scale(0.8);
      z-index: 99;
      top: 1px;
    }
    .el-switch__label--right {
      left: 7px;
      display: none;
    }
    .el-switch__label--left {
      right: 7px;
      display: none;
    }
    .el-switch__label.is-active {
      display: block;
    }
    .el-switch__core::after  {
      width: 15px;
      height: 15px;
      position: absolute;
      top: 1.6px;
      left: 2px;
    }
  }
  .is-checked {
    .el-switch__core::after {
      left: 100%;
    }
  }
  :deep(.el-dialog__footer) {
    padding: 0 32px 24px;
  }
}
</style>
