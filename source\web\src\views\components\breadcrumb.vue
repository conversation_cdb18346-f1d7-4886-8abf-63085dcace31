<template>
  <el-breadcrumb separator-class="el-icon-arrow-right" class="breadcrumbCon">
    <el-breadcrumb-item v-for="(item,index) in breadcrumb" :key="index">{{item}}</el-breadcrumb-item>
  </el-breadcrumb>
</template>

<script>
export default {
  name: 'manageBreadCrumb',
  props: {
    title: {
      type: String,
      default: ''
    },
    breadcrumbs: {
      type: Array
    }
  },
  data() {
    return {
      breadcrumb: []
    }
  },
  mounted() {
    this.breadcrumb = this.breadcrumbs ? this.breadcrumbs : this.$route.meta.breadcrumb
  },
  methods: {
    handleBreadCrumb() {
    }
  }
}
</script>
<style lang="less">
.breadcrumbCon{
   margin-bottom: 20px; 
}
</style>
