<template>
  <div class="work-upload-img-warp">
    <!-- 查看照片 -->
    <ImageViewer 
      v-if="viewerVisible"
      :urlList="fileList"
      :viewFile="viewFile"
      :on-close="closeViewer"
      @down="download"
    ></ImageViewer>
    <el-dialog custom-class="media-dialog"  :visible.sync="dialogVisible" width="1024px" @close="handleDialogClose">
      <div v-if="dialogVisible">
        <template v-if="viewFile.type === 'Video'">
          <div id="view-video" ></div>
        </template>
        <template v-if="viewFile.type === 'Audio'">
          <div id="view-audio" ></div>
        </template>
        <template v-if="viewFile.type === 'Doc'">
          <div id="view-file" >
            <iframe class="view-iframe" id="view-iframe" :src="viewFile.url" width="100%" height="640" :allowfullscreen="true"></iframe>
          </div>
        </template>
      </div>
      <div class="media-info">
        <p class="media-title">{{ viewFile.name }}</p>
        <span class="media-item">{{ $langue('Mooc_ProjectDetail_Documents_FileSize', { defaultText: '大小' }) }}: <span class="media-desc">{{ viewFile.file_size }}</span></span>
        <span class="media-item media-margin">{{ $langue('Mooc_ProjectDetail_Documents_CreateTime', { defaultText: '上传时间' }) }}: <span class="media-desc">{{ viewFile.created_at }}</span></span>
        <span class="media-item"> <el-button size="mini" round :class="[!viewFile.allow_download? 'disable-view' : '','media-down']" @click="urlForDownload(viewFile)">{{ $langue('Mooc_ProjectDetail_Documents_DownLoad', { defaultText: '下载' }) }}</el-button></span>
      </div>
      <div class="footer-close">
        <img class="footer-img" src="@/assets/img/close-circle.png"  @click="dialogVisible = false" alt="">
      </div>
    </el-dialog>
  </div>
</template>

<script>
import env from 'config/env.conf.js'
import { getSingleData } from '@/config/mooc.api.conf'
import { getContentInfo, operatesignature } from 'config/api.conf'
import { workSourceInfo } from 'config/mooc.api.conf'
import ImageViewer from './image-viewer'
const envName = env[process.env.NODE_ENV]
export default {
  components: {
    ImageViewer
  },
  props: {
    slotTip: {}
  },
  data() {
    return {
      dialogVisible: false,
      viewFile: {
        name: '',
        type: '',
        url: '',
        created_at: '',
        file_size: '',
        allow_download: false
      },
      fileList: [],
      viewerVisible: false
    }
  },
  watch: {},
  methods: {
    proportion(width, height) {
      let limit = width / height
      if (Number.isInteger(limit)) {
        return `${limit} : 1`
      } else {
        return `${Math.round(limit * 10)} : 10`
      }
    },
    /* eslint-disable*/
    handleViewFile(obj) {
      const item = JSON.parse(JSON.stringify(obj))
      const { content_id, resource_type, resource_name, created_at, file_size, mooc_course_id,allow_download } = item
      this.viewFile.type = resource_type
      this.viewFile.name = resource_name
      this.viewFile.content_id = content_id
      this.viewFile.created_at = created_at
      this.viewFile.file_size = file_size
      this.viewFile.act_id = mooc_course_id
      this.viewFile.allow_download = allow_download
      getSingleData({ content_id: content_id, act_id: mooc_course_id })
      switch (this.viewFile.type) {
        case 'Video':
          this.handleFilePreview(item, '#view-video')
          break;
        case 'Audio':
          this.handleFilePreview(item, '#view-audio')
          break;      
        case 'Image':
          this.handleImg(item)
          break;
        case 'Zip':
          this.urlForDownload(item)
          break;     
        case 'Doc' :
          this.handleFilePreview(item, '#view-file')
          break;               
      }
    },
    handleFilePreview(obj, el) {
      this.dialogVisible = true
      if(this.viewFile.type == 'Doc' ){
         operatesignature({
          app_id: 'QLearningService',
          content_id: obj.content_id,
          corp_name: 'tencent',
          operate: 'visit'
        }).then((signature) => {
          if (signature) {
            getContentInfo(obj.content_id, {
              app_id: 'QLearningService',
              signature: signature
            }).then((res) => {
              this.$nextTick(() => {
                this.viewFile.url = res.file_info  && res.file_info.doc_url
              })
            })
          }
        })
      }else{
        this.$nextTick(() => {
          new contentCenter.filePreview({
            el,
            contentId: obj.content_id,
            appId: 'QLearningService',       
            contentInfoUrl: `${envName.trainingPath}api/businessCommon/common/content/previewInfo?app_id=QLearningService&corp_name=tencent&content_id=${obj.content_id}`,
            width: '1024px',
            height: '576px'
          })
        })
      }
    },
    // 图片拼接
    handleImg(data) {
      if (data) {
        getSingleData({ content_id: data.content_id, act_id: data.mooc_course_id })
        this.viewerVisible = true
        this.$nextTick(() => {
          this.viewFile = {
            ...data,
            name: data.resource_name
          }
          this.fileList = [`${envName.contentcenter}content-center/api/v1/content/imgage/${data.content_id}/preview`]
        })
      }
    },
    // 下载
    download() {
      this.urlForDownload(this.viewFile)
    },
    // 获取下载文件源地址
    urlForDownload(obj) {
      const signatureParams = {
        content_id: obj.content_id,
        operate: 'download'
      }
      operatesignature(signatureParams).then((signature) => {
        workSourceInfo(obj.content_id, {
          app_id: 'QLearningService',
          signature: signature
        }).then((res) => {
          this.getBlob(res).then((bolb) => {
            this.saveAs(bolb, obj.name)
          })
        })
      })
    },
    // 获取 blob 格式文件
    getBlob(url) {
      return new Promise(resolve => {
        const xhr = new XMLHttpRequest()
        xhr.open('GET', url, true)
        xhr.responseType = 'blob'
        xhr.onload = () => {
          if (xhr.status === 200) {
            resolve(xhr.response)
          }
        }
        xhr.send()
      })
    },
    // 文件下载
    saveAs(blob, filename) {
      let link = document.createElement('a')
      link.href = window.URL.createObjectURL(blob)
      link.download = filename
      link.click()
    },
    // 预览弹窗关闭
    handleDialogClose() {
      switch (this.viewFile.type) {
        case 'Video':
          let video_id = document.getElementsByClassName('vjs-tech')[0]?.playerId
          let myVideo = document.getElementById(`${video_id}_html5_api`)
          if (myVideo && myVideo.play()) {
            myVideo.pause()
          }
          break
        case 'Audio':
          let audio_id = document.getElementsByClassName('vjs-tech')[0]?.playerId
          let myAudio = document.getElementById(`${audio_id}_html5_api`)
          if (myAudio && myAudio.play()) {
            myAudio.pause()
          }
          break
        default:
          break
      }
    },
    closeViewer() {
      this.viewerVisible = false
    },
  }
};
</script>
<style lang="less" scoped>
.upload-img {
  display: flex;
  align-items: flex-start;
}
.file-view .view-iframe {
  border: medium none;
}
.el-dialog__wrapper {
  background: #00000080;
}
:deep .media-dialog{
  box-shadow: unset;
  border-radius: 8px;
  background: unset;
   .el-dialog__header{
    display: none !important;
  }
   .el-dialog__body{
    padding: 0 !important;
    background: #F1F3F4;
    border-radius: 8px 8px 0 0;
    .view-iframe {
      border: none;
      border-bottom: 1px solid #f1f3f4;
    }
  }
  .tcplayer-contaner,.vjs-error-display,.vjs-tech {
    border-radius: 8px 8px 0 0;
  }
  .media-info{
    font-family: "PingFang SC";
    background-color: #fff;
    padding: 20px 20px 16px;
    border-radius: 0 0 8px 8px;
    background: #fff;
    .media-title{
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 8px;
      color: #000000e6;
    }
    .media-item{
      color:#00000066;;
      font-size: 14px;
    }
    .media-margin{
      margin: 0 24px;
    }
    .media-desc{
      color: #00000099;
    }
    .media-down{
      background: #ecf2feff;
      color: #0052d9ff;
      font-size: 12px;
    }
    .disable-view{
      color: #00000042;
    }
    .disable-view:hover{
      color: #00000042;
      cursor: not-allowed;
    }
  }
}
.footer-close{
  line-height: 56px;
  text-align: center;
  padding-top: 8px;
  background: #f1f3f4;
  .footer-img{
    width: 48px;
    cursor: pointer;
  }
}

</style>
