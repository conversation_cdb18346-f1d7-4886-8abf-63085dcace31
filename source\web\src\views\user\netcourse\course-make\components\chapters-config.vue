<template>
  <div class="chapters-config">
    <!-- 视频回显 -->
    <video-chapters 
    ref="videoChapters" 
    :videoInfo="videoInfo" 
    :estDur="estDur" 
    :playTime="currentTime" 
    @getImageUrl="getImageUrl" 
    :approveStatus="approveStatus" 
    :autoPlay="false" 
    :volumePanel="false"
    >
    </video-chapters>
    <div class="ai-chapters-tips" v-if="(fromType === 'material' && courseInfo.status === 13) || fromType === 'netcourse'">
      <div v-if="useSwitch || fromType === 'material'" class="open-chapters">
        <i class="el-icon-success" style="color: #2BA471"></i>
        <span class="tips">已智能识别内容并划分章节， 
          <el-link :disabled="(!$route.query.net_course_id || approveStatus) && fromType === 'netcourse'" @click="aiVisible=true" type="primary">点此查看、{{fromType === 'netcourse' ? '导入或停用' : '导入至人工配置'}}</el-link>
        </span>
      </div>
      <div v-else class="close-chapters">
        <i class="el-icon-error" style="color: #D54941"></i>
        <span class="tips">章节智能识别功能已关闭，
          <el-link :disabled="!$route.query.net_course_id || approveStatus" @click="aiVisible=true" type="primary">点此查看、导入或停用</el-link>
        </span>
      </div>
    </div>
    <p class="pseudo-class-title" v-if="fromType==='material'">人工分段章节</p>
    <el-form
      ref="refChapters"
      :model="chaptersForm"
      label-width="68px"
      class="chapter-form-style"
      size="small"
      label-position="left"
    >
      <div v-for="(item, inds) in chaptersForm.chaptersList" :key="inds">
        <el-row
          class="row-time-style"
        >
          <el-col class="col-time-range">
            <el-form-item
              :label="`章节-${inds + 1}：`"
              class="time-range"
            >
              <el-col style="width: auto;">
                <span class="minute-text">开始于</span>
                <el-form-item
                  label=""
                  :prop="'chaptersList.' + inds + '.minutes'"
                  class="minute-input-item"
                  :rules="[
                    { validator: validateNumber, trigger: 'blur' }
                  ]"
                >
                  <!-- <el-input v-model.number="item.minutes" @blur="blurSetProgress(item, 'minutes', inds)" :disabled="inds === 0 || isDisabled" clearable class="minute-input-style"></el-input> -->
                  <el-input v-model.number="item.minutes" @blur="blurSetProgress(item, 'minutes', inds)" :disabled="isDisabled" clearable class="minute-input-style"></el-input>
                </el-form-item>
              </el-col>
              <el-col class="col-text">
                <span>分</span>
              </el-col>
              <el-col style="width: auto;">
                <el-form-item
                  label=""
                  :prop="'chaptersList.' + inds + '.seconds'"
                  class="second-input-item"
                  :rules="[
                    { validator: validateNumber, trigger: 'blur' }
                  ]"
                >
                  <!-- <el-input v-model.number="item.seconds" @blur="blurSetProgress(item, 'seconds', inds)" :disabled="inds === 0 || isDisabled" clearable class="second-input-style"></el-input> -->
                  <el-input v-model.number="item.seconds" @blur="blurSetProgress(item, 'seconds', inds)" :disabled="isDisabled" clearable class="second-input-style"></el-input>
                </el-form-item>
              </el-col>
              <el-col class="col-text">
                <span>秒</span>
              </el-col>
            </el-form-item>
          </el-col>
          <el-col class="col-err-msg" v-if="item.errorMsg">
            <span class="err-icon"></span>
            <span class="err-text">{{ item.errorMsg }}</span>
          </el-col>
          <el-col class="col-btn">
            <!-- <span class="icon-remove" :class="{'icon-disabled': (!duration || isDisabled)}" v-if="inds > 0" @click="deleteItem(item)"> -->
            <span class="icon-remove" :class="{'icon-disabled': isDisabled}" v-if="inds > 0" @click="deleteItem(item)">
              <span class="icon icon-status"></span>
            </span>
            <!-- 只有先获取到视频时长，且转码成功才能操作章节 -->
            <!-- <el-button size="small" icon="el-icon-plus" :class="{'add-disabled': (!duration || isDisabled)}" :disabled="!duration || isDisabled" v-if="videoInfo.status === 13" class="icon-plus" @click.stop="addItem">添加章节 -->
            <el-button size="small" icon="el-icon-plus" :class="{'add-disabled': isDisabled}" :disabled="isDisabled" v-if="videoInfo.status === 13" class="icon-plus" @click.stop="addItem">添加章节
            </el-button>
          </el-col>
        </el-row>
        <!--  -->
        <el-row v-if="!isFirstAdd">
          <el-form-item class="cut-img-upload" label="" :prop="'chaptersList.' + inds + '.chapter_cover_url'">
            <el-row class="upload-row">
              <el-col class="upload-col-label">
                <span>封面</span>
              </el-col>
              <el-col class="show-img-col">
                <img v-if="item.chapter_cover_url" width="160px" height="90px" style="object-fit: cover" :src="item.chapter_cover_url"
                class="avatar" />
                <div v-else class="upload-icon">
                  <p>未填写开始时间暂无章节封面</p>
                </div>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="" class="required-icon" :prop="'chaptersList.' + inds + '.chapter_title'"
            :rules="[
              {required: true, message: '请填写章节标题', trigger: 'blur'}
            ]"
          >
            <el-input v-model="item.chapter_title" class="title-input" placeholder="请填写章节标题，不超过16个中文字符（32个英文字符）" :disabled="isDisabled" clearable></el-input>
            <span class="custom-el-input-count">{{ handleValidor(item.chapter_title, 16, inds) }}/16</span>
          </el-form-item>
          <el-form-item label="" :prop="'chaptersList.' + inds + '.chapter_content'">
            <el-input v-model="item.chapter_content" class="title-input desc-input" type="textarea" :rows="4" placeholder="请输入章节概述，不超过500个中文字符" :disabled="isDisabled" clearable></el-input>
            <span class="custom-el-input-count">{{ handleValidor(item.chapter_content, 500, inds) }}/500</span>
          </el-form-item>
        </el-row>
      </div>
      <div class="buttom-btn" :class="{'materialStyle': fromType === 'material'}" v-if="!isDisabled">
        <div class="inner">
          <el-button class="cancel-btn" @click="cancel">取消</el-button>
          <el-button class="confirm-btn" type="primary" @click="onSubmit">保存</el-button>
        </div>
      </div>
    </el-form>
    <!-- 弹窗 -->
    <el-dialog
      title="智能章节识别"
      :visible.sync="aiVisible"
      width="960px"
      :before-close="handleClose"
      custom-class="ai-chapters-dialog none-border-dialog" 
    >
      <div class="ai-chapters-body">
        <div class="ai-switch-item" v-if="fromType !== 'material'">
          <span class="label">功能开关</span>
          <el-switch
            v-model="useSwitch"
            class="ai-switch"
            active-color="#0052D9"
            :width="26"
            @change="handleUseSwitch"
            >
          </el-switch>
          <span class="label-tips">若启用智能章节识别，且内容未人工配置章节划分数据，则将对用户显示智能章节识别结果</span>
        </div>
        <div class="config-item">
          <span class="label">人工配置</span>
          <el-button :disabled="!isDisabledConfig" @click="getAiChapters('exportAi')" type="primary" size="small">导入并修改</el-button>
          <span class="label-tips">点击即可导入智能章节切分数据，并可进行修改</span>
        </div>
        <div class="chapters-content" v-if="isDisabledConfig">
          <div class="chapters-list-box" v-for="(item, inds) in aiChapterRecord" :key="inds">
            <span class="form-title">{{ `章节-${inds + 1}：` }}</span>
            <div class="chapter-dialog-form">
              <div class="form-time-item dialog-item">
                <span class="form-label">开始于</span>
                <el-input 
                  v-model.number="item.minutes" 
                  :disabled="true" 
                  class="minute-input-style"
                  >
                </el-input>
                <span>分</span>
                <el-input 
                  v-model.number="item.seconds" 
                  :disabled="true" 
                  class="second-input-style"
                  >
                </el-input>
                <span>秒</span>
              </div>
              <div class="form-coverImg-item dialog-item">
                <span class="form-label cover-label">封面</span>
                <img 
                v-if="item.imgUrl" 
                width="160px" 
                height="90px" 
                style="object-fit: cover" 
                :src="item.imgUrl"
                class="coverImg" 
                />
                <div v-else class="upload-icon">
                  <p>未填写开始时间暂无章节封面</p>
                </div>
              </div>
              <div class="form-title-item dialog-item">
                <span class="red-start">*</span>
                <el-input v-model="item.chapter_title" :disabled="true"></el-input>
              </div>
              <div class="form-desc-item dialog-item">
                <span class="placehodler"></span>
                <el-input v-model="item.chapter_content" type="textarea" :rows="4" :disabled="true"></el-input>
              </div>
            </div>
          </div>
        </div>
        <div class="empty" v-if='aiChapterRecord.length === 0'>
          <span class="empty-img"></span>
          <div class="empty-text">{{!useSwitch && fromType === 'netcourse' ? '未启用智能章节识别' : '暂无数据'}}</div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import videoChapters from './video-chapters.vue'
import { saveNetCourseChapters, getAiChaptersApi, updateAiChapterStatus, switchStatus, getMaterialAiChapters, saveMaterialChapters } from 'config/api.conf'
import env from 'config/env.conf.js'
export default {
  components: { videoChapters },
  props: {
    approveStatus: {
      type: Boolean,
      default: false
    },
    activeName: {
      type: String,
      default: ''
    },
    chaptersList: {
      type: Array,
      default: () => []
    },
    videoInfo: {
      type: Object,
      default: () => ({})
    },
    estDur: [Number, String],
    courseInfo: {
      type: Object,
      default: () => ({})
    },
    fromType: {
      type: String,
      default: 'netcourse'
    }
  },
  data() {
    const validateNumber = (rule, value, callback) => {
      // console.log(rule, value, 'rule, value-------')
      let regExp = /^\d+$/
      if (value === '') {
        let errMsg = rule.fullField.indexOf('minutes') !== -1 ? '请输入分' : '请输入秒'
        callback(new Error(errMsg))
      } else if (!regExp.test(value)) {
        callback(new Error(' '))
        // if (rule.fullField && rule.fullField.split('.').length > 2) {
        //   let arr = rule.fullField.split('.')
        //   this.chaptersForm[arr[0]][arr[1]][arr[2]] = ''
        //   this.chaptersForm[arr[0]][arr[1]].errorMsg = '请输入整数'
        // }
      } else if (rule.fullField && rule.fullField.indexOf('minutes') !== -1 && rule.fullField.split('.').length > 2) {
        let arr = rule.fullField.split('.')
        let maxList = [] 
        let minList = []
        this.chaptersForm.chaptersList.forEach((item, index) => {
          if (index < arr[1] * 1) {
            if (index !== 0 && (item.minutes === '' || item.seconds === '')) {
              // this.chaptersForm[arr[0]][arr[1]][arr[2]] = ''
              // this.chaptersForm[arr[0]][arr[1]].errorMsg = '请先输入前序章节的时间点'
              callback(new Error(' '))
            } else {
              maxList.push(item.minutes * 60 + item.seconds * 1)
              callback()
            }
          } else if (index === arr[1] * 1) {
            let max = Math.max(...maxList)
            if (item.seconds === '' && (value * 60 < max)) {
              // item.errorMsg = '请勿填写早于前序章节开始时间的时间点'
              // this.chaptersForm[arr[0]][arr[1]][arr[2]] = ''
              callback(new Error(' '))
            } else if (item.seconds === '' && (value * 60 > this.fileTime)) {
              callback(new Error(' '))
            } else if (item.seconds !== '' && (item.minutes * 60 + item.seconds * 1) > this.fileTime) {
              callback(new Error(' '))
            } else if (item.seconds !== '' && (item.minutes * 60 + item.seconds * 1) < max) {
              callback(new Error(' '))
            } else {
              callback()
            }
          }
        })
        if (arr[1] * 1 < (this.chaptersForm.chaptersList.length - 1)) {
          for (let i = this.chaptersForm.chaptersList.length - 1; i > arr[1] * 1; i--) {
            let charpterItem = this.chaptersForm.chaptersList[i]
            if (charpterItem.minutes !== '' && charpterItem.seconds === '') {
              minList.push(charpterItem.minutes * 60)
            } else if (charpterItem.minutes !== '' && charpterItem.seconds !== '') {
              minList.push(charpterItem.minutes * 60 + charpterItem.seconds * 1)
            }
          }
          let min = Math.min(...minList)
          if (this.chaptersForm[arr[0]][arr[1]].seconds === '' && (value * 60 > min)) {
            // this.chaptersForm[arr[0]][arr[1]][arr[2]] = ''
            // this.chaptersForm[arr[0]][arr[1]].errorMsg = '请勿填写晚于后序章节开始时间的时间点'
            callback(new Error(' '))
          } else if (this.chaptersForm[arr[0]][arr[1]].seconds !== '' && (value * 60 + this.chaptersForm[arr[0]][arr[1]].seconds > min)) {
            // this.chaptersForm[arr[0]][arr[1]][arr[2]] = ''
            // this.chaptersForm[arr[0]][arr[1]].errorMsg = '请勿填写晚于后序章节开始时间的时间点'
            callback(new Error(' '))
          } else {
            callback()
          }
        }
      } else if (rule.fullField && rule.fullField.indexOf('seconds') !== -1 && rule.fullField.split('.').length > 2) {
        let arr = rule.fullField.split('.')
        if (this.chaptersForm.chaptersList[arr[1]].minutes === '') {
          // this.chaptersForm[arr[0]][arr[1]][arr[2]] = ''
          // this.chaptersForm.chaptersList[arr[1] * 1].errorMsg = '请先输入当前章节的分钟数'
          callback(new Error(' '))
        }
        if (value >= 60) {
          callback(new Error(' '))
          // this.chaptersForm[arr[0]][arr[1]][arr[2]] = ''
          // this.chaptersForm[arr[0]][arr[1]].errorMsg = '请输入小于60的秒数'
        }
        let maxList = []
        let minList = []
        this.chaptersForm.chaptersList.forEach((item, index) => {
          if (index < arr[1] * 1) {
            if (index !== 0 && (item.minutes === '' || item.seconds === '')) {
              // this.chaptersForm[arr[0]][arr[1]][arr[2]] = ''
              callback(new Error(' '))
            } else {
              maxList.push(item.minutes * 60 + item.seconds * 1)
              // callback()
            }
          } else if (index === arr[1] * 1) {
            let max = Math.max(...maxList)
            // console.log(max, 'max----------')
            if (this.fileTime < (item.minutes * 60 + value)) {
              // item.errorMsg = '请勿填写超出视频时长的时间点'
              // this.chaptersForm[arr[0]][arr[1]][arr[2]] = ''
              callback(new Error(' '))
            } else if ((item.minutes * 60 + value) < max) {
              // item.errorMsg = '请勿填写早于前序章节开始时间的时间点'
              // this.chaptersForm[arr[0]][arr[1]][arr[2]] = ''
              callback(new Error(' '))
            } else {
              callback()
            }
          }
        })
        if (arr[1] * 1 < (this.chaptersForm.chaptersList.length - 1)) {
          for (let i = this.chaptersForm.chaptersList.length - 1; i > arr[1] * 1; i--) {
            let charpterItem = this.chaptersForm.chaptersList[i]
            if (charpterItem.minutes !== '' && charpterItem.seconds !== '') {
              minList.push(charpterItem.minutes * 60 + charpterItem.seconds * 1)
            }
          }
          let min = Math.min(...minList)
          if (this.chaptersForm[arr[0]][arr[1]].minutes * 60 + value > min) {
            // this.chaptersForm[arr[0]][arr[1]][arr[2]] = ''
            // this.chaptersForm[arr[0]][arr[1]].errorMsg = '请勿填写晚于后序章节开始时间的时间点'
            callback(new Error(' '))
          } else {
            callback()
          }
        }
      } else {
        callback()
      }
      this.$forceUpdate()
    }
    return {
      validateNumber: validateNumber,
      chaptersForm: {
        chaptersList: [
          { minutes: 0, seconds: 0, chapter_cover_url: '', chapter_title: '', chapter_content: '', chapter_status: 1 }
        ]
      },
      aiVisible: false,
      useSwitch: false,
      isFirstAdd: true,
      uploadStatus: false,
      duration: '', // 视频时长
      currentTime: -1, // 当前播放时间节点
      imgInfo: {},
      index: 0, // 当前编辑的时间点位置
      isExportAi: false,
      aiChapterRecord: [],
      isInit: true
    }
  },
  computed: {
    isDisabled() {
      return this.approveStatus || !((this.videoInfo.src || this.videoInfo.content_id) && this.videoInfo.status === 13 && this.fileTime)
    },
    // isChapterFrom() {
    //   return this.courseInfo.ai_chapter_from
    // },
    isUndeline() {
      return ![1, 3].includes(Number(this.courseInfo.status))
    },
    isDisabledConfig() {
      return !!this.aiChapterRecord.length && (this.useSwitch || this.fromType === 'material')
    },
    fileTime() { // 素材时长，有duration用duration，没有的话用estDur
      let value = 0
      try {
        value = this.videoInfo.duration || (this.estDur * 60) || 0
      } catch (error) {
        console.log('error~~~~~~~~~~~~: ', error)
        value = 0
      }
      return value
    }
  },
  watch: {
    activeName: {
      immediate: true,
      handler(val) {
        this.$nextTick(() => {
          this.$refs['refChapters'].clearValidate()
        })
      }
    },
    videoInfo: {
      deep: true,
      immediate: true,
      handler(val) {
        if (val.content_id) {
          // console.log(val, 'courseInfo.content_info-----------------')
          this.uploadStatus = true // 视频已加载
          this.duration = val.duration
          // this.videoData = {
          //   content_id: val.content_id,
          //   duration: val.duration
          // }
        }
      }
    },
    chaptersList: {
      deep: true,
      immediate: true,
      handler(val) {
        if (val.length === 0) {
          this.resetForm()
          return
        }
        let update = val.some(v => v.id !== undefined)
        if (update && this.currentTime === -1) {
          // 如果是更新当前时间, 则初始化 默认重置为0
          this.currentTime = 0
          this.index = 0
        }
        // 判断是否是第一次添加章节
        this.isFirstAdd = !update
        this.chaptersForm.chaptersList = val.map(item => {
          // 秒 转换为 分 和 秒
          let { minutes, seconds } = this.secondsToMinutes(item.chapter_time_point)
          const envName = env[process.env.NODE_ENV]
          const url = item.chapter_cover_content_id ? `${envName.contentcenter}content-center/api/v1/content/imgage/${item.chapter_cover_content_id}/preview` : item.chapter_cover_url
          let chapter_title = ''
          let chapter_content = ''
          if (item.chapter_title) {
            const total = this.forMatTitle(item.chapter_title)
            if (total > 16) {
              chapter_title = item.chapter_title.slice(0, 16) // 最多裁剪这么多
            } else {
              chapter_title = item.chapter_title.slice() // 全部裁剪
            }
          }
          if (item.chapter_content) {
            const total = this.forMatTitle(item.chapter_content)
            if (total > 500) {
              chapter_content = item.chapter_content.slice(0, 500)
            } else {
              chapter_content = item.chapter_content.slice()
            }
          }
          return {
            id: item.id,
            minutes,
            seconds,
            chapter_cover_url: url, // 章节封面地址
            chapter_title,
            chapter_content,
            chapter_status: item.chapter_status // 转码状态
          }
        })
        if (this.isInit) {
          this.isInit = false
          this.$emit('updateChapterList', JSON.stringify(this.chaptersForm.chaptersList))
        }
      }
    },
    aiVisible(val) {
      if (val && this.useSwitch) {
        this.getAiChapters()
      }
    }
  },
  mounted() {
    this.getSwitchStatus()
    if (this.fromType === 'material' && this.courseInfo.file_id) {
      this.getAiChapters()
    }
  },
  methods: {
    getSwitchStatus() {
      const id = this.$route.query?.net_course_id || ''
      if (!id) return
      switchStatus({ course_id: this.$route.query?.net_course_id || '' }).then((e) => {
        this.useSwitch = !!e
      })
    },
    // 智能章节开关
    handleUseSwitch(val) {
      this.useSwitch = val
      const params = {
        course_id: this.courseInfo?.net_course_id || '',
        ai_chapter_from: this.useSwitch ? (this.courseInfo.ai_chapter_from || 'tencentMeetingAi') : ''
      }
      updateAiChapterStatus(params).then((res) => {
        const msg = this.useSwitch ? '已开启智能章节' : '已关闭智能章节'
        this.$message.success(msg)
        if (val) { // 开启状态有数据
          this.getAiChapters()
          return
        }
        this.aiChapterRecord = []
      })
    },
    // 获取智能章节
    getAiChapters(type = '') {
      let api = this.fromType === 'material' ? getMaterialAiChapters : getAiChaptersApi
      let params = {}
      if (this.fromType === 'material') {
        params = {
          fileId: this.courseInfo.file_id
        }
      } else {
        params = {
          content_id: this.courseInfo?.content_info?.content_id || '', // 课程文件id
          course_id: this.courseInfo?.net_course_id || ''
        }
      }
      api(params).then((e) => {
        this.aiChapterRecord = e.map((v) => {
          let { minutes, seconds } = this.secondsToMinutes(v.chapter_time_point)
          const envName = env[process.env.NODE_ENV]
          const url = v.chapter_cover_content_id ? `${envName.contentcenter}content-center/api/v1/content/imgage/${v.chapter_cover_content_id}/preview` : v.chapter_cover_url
          return {
            ...v,
            minutes,
            seconds,
            imgUrl: url
          }
        })
        this.isExportAi = false
        // 导入并修改
        if (type === 'exportAi') {
          this.isExportAi = true
          this.$emit('exportAiData', { chaptersRecord: this.aiChapterRecord })
          this.handleClose()
        }
      })
    },
    handleClose() {
      this.aiVisible = false
    },
    pause() {
      this.$refs.videoChapters && this.$refs.videoChapters.pause()
    },
    getImageUrl(imageUrl) {
      this.$set(this.chaptersForm.chaptersList[this.index], 'chapter_cover_url', imageUrl)
      console.log(this.chaptersForm, 'this.chaptersForm----888')
      this.$forceUpdate()
    },
    // 校验 分 和 秒的 输入框
    checkInput(item, type, inds) {
      if (this.$refs.refChapters && this.$refs.refChapters[inds]) {
        this.$refs.refChapters[inds].clearValidate()
      }
      let errMsg = ''
      let regExp = /^\d+$/
      // if (item.minutes && item.seconds === '') {
      //   errMsg = '请完整填写章节开始时间'
      //   item[type] = ''
      // } else if (item.minutes === '' && item.seconds) {
      //   errMsg = '请先输入当前章节的分钟数'
      //   item[type] = ''
      // } else 
      if (!regExp.test(item[type])) {
        errMsg = '请输入整数'
        item[type] = ''
        return errMsg
      } else if (type === 'minutes') {
        let maxList = []
        let maxSecondList = []
        let minList = []
        let minSecondList = []
        for (let index = 0; index < this.chaptersForm.chaptersList.length; index++) {
          let chapt = this.chaptersForm.chaptersList[index]
          if (index < inds) {
            if (index !== 0 && (chapt.minutes === '' || chapt.seconds === '')) {
              item[type] = ''
              errMsg = '请先输入前序章节的时间点'
              return errMsg
            } else {
              maxList.push(chapt.minutes)
              maxSecondList.push(chapt.minutes * 60 + chapt.seconds * 1)
            }
          } else if (index === inds) {
            let maxMinute = Math.max(...maxList)
            let maxSecond = Math.max(...maxSecondList)
            if (item.seconds === '' && (item[type] < maxMinute)) {
              errMsg = '请勿填写早于前序章节开始时间的时间点'
              item[type] = ''
              return errMsg
            } else if (item.seconds === '' && (item[type] * 60 > this.fileTime)) {
              errMsg = '请勿填写超出视频时长的时间点'
              item[type] = ''
              return errMsg
            } else if (item.seconds !== '' && (item[type] * 60 + item.seconds * 1) > this.fileTime) {
              errMsg = '请勿填写超出视频时长的时间点'
              item[type] = ''
              return errMsg
            } else if (item.seconds !== '' && (item[type] * 60 + item.seconds * 1) < maxSecond) {
              errMsg = '请勿填写早于前序章节开始时间的时间点'
              item[type] = ''
              return errMsg
            } else if (item.seconds !== '' && (item[type] * 60 + item.seconds * 1) === maxSecond) {
              errMsg = '请勿填写与前序章节开始时间重复的时间点'
              item[type] = ''
              return errMsg
            } else if (item.seconds === '') {
              errMsg = '请完整填写章节开始时间'
              return errMsg
            }
          }
        }
        // 如果输入的不是最后一个章节
        if (inds < (this.chaptersForm.chaptersList.length - 1)) {
          for (let i = this.chaptersForm.chaptersList.length - 1; i > inds; i--) {
            let charpterItem = this.chaptersForm.chaptersList[i]
            if (charpterItem.minutes !== '' && charpterItem.seconds === '') {
              // 收集和比较分钟
              minList.push(charpterItem.minutes)
              minSecondList.push(charpterItem.minutes * 60)
            } else if (charpterItem.minutes !== '' && charpterItem.seconds !== '') {
              minList.push(charpterItem.minutes)
              minSecondList.push(charpterItem.minutes * 60 + charpterItem.seconds * 1)
            }
          }
          let minMinutes = Math.min(...minList)
          let minSeconds = Math.min(...minSecondList)
          if (item.seconds === '' && (item[type] > minMinutes)) {
            errMsg = '请勿填写晚于后序章节开始时间的时间点'
            item[type] = ''
            return errMsg
          } else if (item.seconds !== '' && (item[type] * 60 + item.seconds * 1 > minSeconds)) {
            errMsg = '请勿填写晚于后序章节开始时间的时间点'
            item[type] = ''
            return errMsg
          } else if (item.seconds !== '' && (item[type] * 60 + item.seconds * 1 === minSeconds)) {
            errMsg = '请勿填写与后序章节开始时间重复的时间点'
            item[type] = ''
            return errMsg
          }
        }
      } else if (type === 'seconds') {
        if (item.minutes === '') {
          errMsg = '请先输入当前章节的分钟数'
          item[type] = ''
          return errMsg
          // console.log(item.minutes, 'item.minutes-----------------')
        }
        if (item[type] >= 60) {
          errMsg = '请输入小于60的秒数'
          item[type] = ''
          return errMsg
        }
        let maxList = []
        let minList = []
        for (let index = 0; index < this.chaptersForm.chaptersList.length; index++) {
          let chapt = this.chaptersForm.chaptersList[index]
          if (index < inds) {
            if (index !== 0 && (chapt.minutes === '' || chapt.seconds === '')) {
              item[type] = ''
              errMsg = '请先输入前序章节的时间点'
              return errMsg
            } else {
              maxList.push(chapt.minutes * 60 + chapt.seconds * 1)
            }
          } else if (index === inds) {
            let max = Math.max(...maxList)
            // console.log(max, 'max----------')
            if (this.fileTime < (item.minutes * 60 + item[type])) {
              errMsg = '请勿填写超出视频时长的时间点'
              item[type] = ''
              return errMsg
            } else if ((item.minutes * 60 + item[type]) < max) {
              errMsg = '请勿填写早于前序章节开始时间的时间点'
              item[type] = ''
              return errMsg
            } else if ((item.minutes * 60 + item[type]) === max) {
              errMsg = '请勿填写与前序章节开始时间重复的时间点'
              item[type] = ''
              return errMsg
            }
          }
        }
        // if (errMsg) return errMsg
        // 输入的不是最后一个章节
        if (inds < (this.chaptersForm.chaptersList.length - 1)) {
          for (let i = this.chaptersForm.chaptersList.length - 1; i > inds; i--) {
            let charpterItem = this.chaptersForm.chaptersList[i]
            if (charpterItem.minutes !== '' && charpterItem.seconds !== '') {
              minList.push(charpterItem.minutes * 60 + charpterItem.seconds * 1)
            }
          }
          let min = Math.min(...minList)
          if (item.minutes * 60 + item[type] * 1 > min) {
            errMsg = '请勿填写晚于后序章节开始时间的时间点'
            item[type] = ''
            return errMsg
          } else if (item.minutes * 60 + item[type] * 1 === min) {
            errMsg = '请勿填写与后序章节开始时间重复的时间点'
            item[type] = ''
            return errMsg
          }
        }
      }
      return errMsg
    },
    // 拿到当前输入节点的 分 和 秒 并计算时长
    blurSetProgress(item, type, index) {
      let msg = ''
      msg = this.checkInput(item, type, index)
      if (msg) {
        // this.$set(item, 'errorMsg', msg)
        item.errorMsg = msg
        this.chaptersForm.chaptersList.splice(index, 1, item)
        this.$forceUpdate()
        return
      } else {
        item.errorMsg = ''
      }
      this.index = index
      let currentTime = item.minutes * 60 + item.seconds * 1
      // 因为加载速度的原因，实际截屏位置慢3秒，所以补上
      this.currentTime = currentTime
      this.$forceUpdate()
      // console.log('当前时长是------', this.currentTime)
    },
    secondsToMinutes(seconds) {
      let minutes = Math.floor(seconds / 60) // 计算分钟
      let remainingSeconds = seconds % 60 // 计算剩余秒数
      return {
        minutes: minutes,
        seconds: remainingSeconds
      }
    },
    forMatTitle(value) {
      const china = value.match(/[\u4e00-\u9fa5]/g)
      const zhCount = china && china.join('').length
      const enCount = Math.ceil((value.length - zhCount) / 2)
      const total = zhCount + enCount
      return total
    },
    // 校验输入框字符数
    handleValidor(value, num, index) {
      if (value) {
        const china = value.match(/[\u4e00-\u9fa5]/g)
        const zhCount = china && china.join('').length
        const enCount = Math.ceil((value.length - zhCount) / 2)
        const total = zhCount + enCount
        if (total > num) {
          if (num === 16) {
            this.$set(this.chaptersForm.chaptersList[index], 'chapter_title', value.slice(0, -1))
          } else {
            this.$set(this.chaptersForm.chaptersList[index], 'chapter_content', value.slice(0, -1))
          }
        }
        return zhCount + enCount
      }
      return 0
    },
    // 重置
    resetForm() {
      this.isFirstAdd = true
      this.chaptersForm.chaptersList = [{ minutes: 0, seconds: 0, chapter_cover_url: '', chapter_title: '', chapter_content: '', chapter_status: 1 }]
      this.$nextTick(() => {
        this.$refs['refChapters'].clearValidate()
      })
    },
    // 添加章节
    addItem() {
      if (this.currentTime === -1 && this.isFirstAdd) {
        this.currentTime = 0
        this.index = 0
      }
      this.isFirstAdd = false
      this.chaptersForm.chaptersList.push({ minutes: '', seconds: '', chapter_cover_url: '', chapter_title: '', chapter_content: '', chapter_status: 1, key: Date.now() })
    },
    // 删除当前章节
    deleteItem(item) {
      // if (this.approveStatus || !this.duration) return
      if (this.isDisabled) return
      let index = this.chaptersForm.chaptersList.indexOf(item)
      if (index !== -1) {
        this.chaptersForm.chaptersList.splice(index, 1)
        this.$message.success('已删除')
      }
    },
    // 保存
    onSubmit() {
      // console.log(this.$refs['refChapters'], '调了保存--------')
      this.$refs['refChapters'].validate((valid) => {
        // 判断如果是更换了视频素材，视频时长与配置章节时间节点对不上，当配置节点超出时，给提示
        let isExceed = this.chaptersForm.chaptersList.some(chapter => {
          return (chapter.minutes * 60 + chapter.seconds) > this.fileTime
        })
        if (isExceed) {
          this.$message.error('请勿填写超过视频时长的时间点')
        }
        if (valid) {
          let chaptersList = this.chaptersForm.chaptersList.map(item => {
            return {
              id: this.isExportAi ? '' : item.id,
              chapter_time_point: (item.minutes * 60 + item.seconds),
              chapter_title: item.chapter_title,
              chapter_content: item.chapter_content,
              chapter_cover_url: item.chapter_cover_url,
              chapter_status: item.chapter_status
            }
          })
          let api = this.fromType === 'material' ? saveMaterialChapters : saveNetCourseChapters
          let params = {}
          if (this.fromType === 'material') {
            params = {
              file_id: this.courseInfo.file_id,
              pub_file_chapter_info_list: chaptersList || []
            }
          } else {
            params = {
              net_course_id: this.$route.query.net_course_id,
              chapters_setting_detail_in_dto_list: chaptersList || []
            }
          }
          // 未填写标题时间为空的时候章节传[]
          const flagValue = this.chaptersForm.chaptersList.find((e) => !e.minutes && !e.seconds)
          if (flagValue && this.chaptersForm.chaptersList?.length === 1) {
            params.chapters_setting_detail_in_dto_list = []
          }
          api(params).then(res => {
            this.$message.success(this.fromType !== 'material' ? '分段章节配置保存成功' : '章节信息保存成功')
            // 刷新列表
            this.$emit('refreshChapterList')
          })
        }
      })
    },
    cancel() {
      if (this.fromType !== 'material') {
        this.$router.push({ name: 'courseList' })
      } else {
        this.$emit('refreshChapterList')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.chapters-config {
  .pseudo-class-title {
      position: relative;
      margin-top: 24px;
      padding-left: 16px;
      font-size: 16px;
      font-weight: bold;
      color: rgba(0,0,0,0.8);
      font-family: "PingFang SC";
      margin-bottom: 10px;
      display: inline-block;
    }
    .pseudo-class-title::before {
      position: absolute; 
      top: 50%; 
      transform: translatey(-50%); 
      left: 0; 
      content: ''; 
      width: 4px;
      height: 18px;
      background-color: #0052D9; 
    } 
  .upload-icon {
    width: 160px;
    height: 90px;
    border-radius: 4px;
    background: #F5F5F5;
    display: flex;
    justify-content: center;
    align-items: center;
    p {
      width: 84px;
      height: 40px;
      color: #00000099;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 12px;
      font-weight: 400;
      line-height: 20px;
    }
  }
  .chapter-form-style {
    padding: 28px 0 20px;
    ::v-deep .el-form-item.required-icon.el-form-item--small {
      .el-form-item__content:before {
        display: inline-block;
        content: '*';
        color: #F81D22;
        width: auto;
        margin-right: 4px;
      }
      .el-form-item__error {
        margin-left: 12px;
      }
    }
    .row-time-style {
      display: flex;
      width: auto;
      .col-time-range {
        width: 320px;
      }
      .col-err-msg {
        margin-left: 28px;
        width: auto;
        height: 32px;
        line-height: 32px;
        .err-icon {
          display: inline-block;
          width: 16px;
          height: 16px;
          margin-right: 8px;
          vertical-align: middle;
          background: url('~@/assets/mooc-img/error-circle-filled.png') no-repeat center / cover;
        }
        .err-text {
          color: #e34d59;
          text-align: center;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
        }
      }
      .col-btn {
        margin-left: 52px;
        width: auto;
        text-align: right;
        display: flex;
        align-self: flex-start;
      }
    }
    .minute-text {
      margin-left: 10px;
      margin-right: 8px;
      color: #00000099;
    }
    ::v-deep .el-form-item {
      margin-bottom: 24px;
      .el-form-item__label {
        padding-right: 0px;
      }
    }
    ::v-deep .el-form-item.second-input-item.el-form-item--small, ::v-deep .el-form-item.minute-input-item.el-form-item--small {
      width: auto;
      display: inline-block;
      margin-bottom: 0px !important;
      .el-form-item__label {
        width: 0px !important;
      }
      .el-form-item__error {
        margin-left: 0px;
        width: 160px;
      }
      .el-form-item__content {
        margin-left: 0 !important;
      }
      .el-input__inner {
        width: 56px;
        padding: 5px;
      }
      .second-input-style {
        display: inline-block;
      }
    }
    .col-text {
      padding: 0 8px;
      width: auto;
      color: #00000099;
    }
    ::v-deep .icon-plus {
      cursor: pointer;
      width: 108px;
      height: 32px;
      padding: 5px 16px;
      border-radius: 3px;
      border: 1px solid var(---Brand7-Hover, #266FE8);
      background: #F3F3F3;
      color: #0052d9;
      font-family: "PingFang SC";
      .el-icon-plus {
        width: 16px;
        height: 16px;
        line-height: 16px;
        color: #0052d9;
      }
      &:active, &:visited {
        border: 1px solid var(---Brand9-Click, #0034B5);
        background: #E7E7E7;
        color: #0034b5;
        .el-icon-plus {
          color: #0034b5;
        }
      }
    }
    .add-disabled {
      background: #EEE;
      border: 1px solid #DCDCDC;
      color: #00000042;
      .el-icon-plus {
        color: #00000042;
      }
    }
    .icon-remove {
      display: inline-block;
      width: 32px;
      height: 32px;
      line-height: 32px;
      text-align: center;
      padding: 1px 2px;
      margin-right: 16px;
      border-radius: 3px;
      border: 1px solid var(---Brand7-Hover, #266FE8);
      cursor: pointer;
      pointer-events: auto;
      .icon {
        display: inline-block;
        width: 16px;
        height: 16px;
      }
      .icon-status {
        background: url('~@/assets/mooc-img/delete-hover.png') no-repeat center / cover;
      }
      &:active, &:visited {
        border: 1px solid var(---Brand9-Click, #0034B5);
        .icon-status {
          background: url('~@/assets/mooc-img/delete-active.png') no-repeat center / cover;
        }
      }
    }
    .icon-remove.icon-disabled {
      cursor: not-allowed;
      pointer-events: none;
      background: #EEE;
      border: 1px solid #DCDCDC;
      color: #00000042;
      .icon-status {
        background: url('~@/assets/mooc-img/delete-disabled.png') no-repeat center / cover;
      }
    }
    ::v-deep .title-input {
      &.desc-input {
        margin-left: 12px;
      }
      width: 824px;
      .el-input__inner, .el-textarea__inner {
        width: 824px;
      }
    }
    .custom-el-input-count {
      color: #ACACAC;
      background: #FFF;
      position: absolute;
      font-size: 12px;
      bottom: 6px;
      line-height: 20px;
    }
    .cut-img-upload {
      .upload-row {
        display: flex;
        justify-content: flex-start;
      }
      .upload-col-label {
        width: 30px;
        margin-right: 8px;
        margin-left: 10px;
        color: #00000099;
      }
      .show-img-col {
        img {
          border-radius: 4px;
        }
      }
    }
  }
  .buttom-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 74px;
    line-height: 74px;
    background-color: #fff;
    text-align: center;
    z-index: 99;
    &.materialStyle{
      .inner{
        padding: 0;
        text-align: right;
      }
    }
    .inner {
      display: inline-block;
      width: 1440px;
      padding-left: 188px;
      text-align: left;
    }
    .confirm-btn, .cancel-btn {
      width: 104px;
      height: 40px;
      border-radius: 4px;
    }
    .cancel-btn {
      margin-right: 20px;
    }
    .confirm-btn {
      background: #0052D9;
    }
  }
  .ai-chapters-tips {
    margin-top: 20px;
    .tips {
      margin-left: 9px;
      color: #000000;
      font-size: 14px;
      line-height: 22px;
    }
  }
  :deep(.ai-chapters-dialog) {
    .el-dialog__body{
      padding-right: 16px;
    }
  }
  .ai-chapters-dialog {
    .label-tips {
      color: #00000066;
      font-size: 14px;
      line-height: 22px;
      margin-left: 12px;
    }
   
    .ai-chapters-body {
      :deep(.ai-switch){
        .el-switch__core {
          height: 16px;
        }
        .el-switch__core:after {
          width: 12px;
          height: 12px;
        }
      }
      :deep(.is-checked) {
        .el-switch__core::after {
          margin-left: -13px;
        }
      }
      .label {
        color: #000000;
        margin-right: 12px;
      }
      .ai-switch-item {
        margin-bottom: 21px;
      }
      .chapters-content {
        height: 550px;
        overflow-y: auto;
        padding-right: 16px;
        .chapters-list-box {
          display: flex;
          align-items: baseline;
          color: #00000099;
          margin-top: 24px;
          .form-title {
            margin-right: 12px;
            height: 22px;
            line-height: 22px;
            font-weight: bold;
          }
  
          .chapter-dialog-form {
            flex: 1;
            .dialog-item {
              margin-bottom: 24px;
              display: flex;
            }
            .dialog-item:last-of-type {
              margin-bottom: unset;
            }
            .placehodler {
              margin-right: 13px;
              display: inline-block;
            }
            .form-time-item {
              display: flex;
              align-items: center;
              .minute-input-style, 
              .second-input-style {
                width: 56px;
                margin-left: 8px;
                margin-right: 8px;
              }
              :deep(.is-disabled) {
                .el-input__inner {
                  padding-right: 5px;
                }
              }
            }
            .form-coverImg-item {
              display: flex;
              .cover-label {
                margin-right: 12px;
              }
            }
            .form-title-item {
              display: flex;
              .red-start {
                color: red;
                margin-right: 7px;
              }
            }
          }
        }
      }
      .empty {
        text-align: center;

        .empty-text {
          margin-top: 17px;
          color: #999999;
          font-size: 14px;
          margin-bottom: 24px;
        }

        .empty-img {
          display: inline-block;
          width: 160px;
          height: 160px;
          background: url(~@/assets/img/empty.png) no-repeat center/contain;
        }
      }
    }
  }
}
@media screen and (max-width: 1660px) {
  .custom-el-input-count {
    bottom: 6px;
    right: 14px;
  }
}
@media screen and (min-width: 1661px) {
  .custom-el-input-count {
    bottom: 6px;
    right: 296px;
  }
}
</style>
