<template>
  <div class="preview">
    <div class="preview-content">
      <div class="appendix">
        <appendix :isPreview="true" :fileData="info.fileData"></appendix>
      </div>
      <div class="p-cur">
        <sdc-mce-preview ref="preview" :urlConfig="editorConfig.urlConfig" :catalogue.sync="editorConfig.catalogue" :content="info.content">
        </sdc-mce-preview>
      </div>
    </div>
  </div>
</template>

<script>
import appendix from '@/views/components/appendix'
export default {
  components: {
    appendix
  },
  data() {
    return {
      info: {},
      editorConfig: {
        catalogue: false,
        options: {
          // 非必传字段
          selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
                    formatselect fontsizeselect lineheight |
                    bold italic underline strikethrough |
                    forecolor backcolor |
                    dent align bullist numlist |
                    insert codesample |
                    fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      }
    }
  },
  created() {
    this.info = JSON.parse(sessionStorage.getItem('work_preview'))
  }
}
</script>

<style lang="less" scoped>
.preview {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  padding-top: 10px;
}
.preview-content {
  width: 1000px;
  padding: 24px;
  background-color: #fff;
  margin-bottom: 20px;
  .p-cur {
    padding-top: 32px;
  }
}
</style>
