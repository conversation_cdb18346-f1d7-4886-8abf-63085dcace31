<template>
  <van-popup class="copy-pop" v-model="show" closeable position="bottom" @confirm="doCopy">
    <div class="share-content">
      <div class="share-content-title">分享至 <span><i class="red"> * </i> 注意此活动仅限腾讯员工参与</span></div>
      <div class="tips">
        {{isGraphic ? '邀请同事共读好文，同时为其解锁价值1200元的“哈佛精品文库”' : '分享给同事，邀请TA解锁价值1200元的“哈佛精品文库”'}}
      </div>
      <div class="share-box">
        <!-- <div class="url-text">
          <el-input size="small" v-model="urlText" type="text" disabled></el-input>
        </div> -->
        <div class="share-wx share-item" @click="handlerWxShear">
          <img :src="require('@/assets/img/mobile/geekBang/wx-shear.png')" alt="">
          <span class="text">微信</span>
        </div>
        <div class="share-url share-item" @click="doCopy">
          <img :src="require('@/assets/img/mobile/geekBang/url-shear.png')" alt="">
          <span class="text">复制链接</span>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script>
import { mapState } from 'vuex'
import { Toast } from 'vant'
export default {
  props: {
    isShowGive: {
      type: Boolean,
      default: false
    },
    xueBaCardConfig: {
      type: Object,
      default: () => {}
    },
    giveType: {
      type: String,
      default: 'account'
    },
    course_id: {
      type: [String, Number],
      default: ''
    },
    isGraphic: {
      type: Boolean,
      default: false
    },
    course_title: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      qrUrl: ''
    }
  },
  mounted() {},
  computed: {
    ...mapState(['userInfo']),
    show: {
      set(val) {
        this.$emit('update:isShowGive', val)
      },
      get() {
        return this.isShowGive
      }
    },
    activityId() {
      return this.xueBaCardConfig.activity_id || this.$route.query.activityId
    },
    urlText() {
      let url = this.isGraphic
        ? `https://sdc.qq.com/s/9bd9km?scheme_type=outsourced&resource_type=graphic&course_id=${this.course_id}`
        : `https://sdc.qq.com/s/PTUrrm?scheme_type=harvard&activityId=${this.activityId}`
      return `${url}&staff_name=${this.userInfo.staff_name}&staff_id=${this.userInfo.staff_id}`
    },
    // 埋点
    dtButton() {
      return (type, name) => {
        const { activity_id, audience_id, audience_name, card_name } =
          this.xueBaCardConfig
        if (type === 'area') {
          return `area_${audience_id}_${activity_id}`
        } else if (type === 'eid') {
          return `element_${audience_id}_${activity_id}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${card_name}活动首页`,
            page_type: `${card_name}活动首页`,
            container: audience_name,
            container_id: '',
            click_type: 'button',
            content_name: name,
            terminal: 'H5'
          })
        }
      }
    }
  },
  methods: {
    handlerWxShear() {
      // this.$emit('update:isShowFixedShear', true)
      this.$parent.isShowFixedShear = true
    },
    doCopy() {
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      let str = this.isGraphic
        ? `【腾讯学堂】${this.userInfo.staff_name}向你推荐哈佛好文《${this.course_title}》，点击即可解锁阅读，并领取价值1200元的【哈佛精品文库】[${this.urlText}]`
        : `【腾讯学堂】${this.userInfo.staff_name}向你赠送了价值1200元的【哈佛精品文库】阅读权限，点击链接即可领取500+篇优质文章的阅读权限[${this.urlText}]`
      input.value = str
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      Toast({
        message: '已复制链接，去粘贴分享吧',
        icon: 'passed'
      })
      // 复制后移除输入框
      input.remove()
    }
  }
}
</script>

<style lang="less" scoped>
.copy-pop {
  border-radius: 12px 12px 0 0;
}
.red {
  color: red;
}
.share-content {
  padding: 12px 16px;
  &-title {
    color: #000000e6;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    opacity: 0.9;
    margin-top: 6px;
    span {
      color: #00000080;
      font-family: 'PingFang SC';
      font-size: 10px;
      font-style: normal;
      font-weight: 400;
      line-height: 12px;
      margin-left: 12px;
    }
  }
  .tips {
    color: #00000080;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding: 12px 0;
  }
  .share-box {
    display: flex;
    // border-bottom: 1px solid #e7e7e7;
    padding-bottom: 12px;
    .url-text {
      display: none;
    }
    .share-item {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      text-align: center;
      margin-right: 28px;
      img {
        width: 48px;
        height: 48px;
      }
      .text {
        color: #000000e6;
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        margin-top: 8px;
      }
    }
  }
}
</style>
