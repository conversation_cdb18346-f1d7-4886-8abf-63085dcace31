<template>
  <div class="list-dialog">
    <el-dialog
      width="900px"
      :visible="visible"
      title="添加课程素材"
      :close-on-click-modal="false"
      :before-close="cancel"
    >
      <div class="course-body">
        <div class="course-body-title">
          <div class="check-label-warning">
            <i class="el-icon-warning-outline"></i>
            <span>仅支持使用本人上传或有使用权限的课程素材</span>
          </div>
          <div class="title-right">
            <span>完成素材上传后请刷新列表</span>
            <el-button size="small" plain class="default-button" @click="refreshList">刷新列表</el-button>
            <el-dropdown @command="handleCommand" trigger="click">
              <el-button type="primary" size="small">上传素材<i class="el-icon-arrow-down el-icon--right"></i></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="Video">视频</el-dropdown-item>
                <el-dropdown-item command="Audio">音频</el-dropdown-item>
                <el-dropdown-item command="Article">文章</el-dropdown-item>
                <el-dropdown-item command="Doc">文档</el-dropdown-item>
                <!-- <el-dropdown-item command="Scorm">scorm</el-dropdown-item>
                <el-dropdown-item command="Flash">压缩包</el-dropdown-item> -->
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <el-form ref="form" :model="form" inline>
          <el-form-item>
            <el-select
              v-model="form.module_value"
              placeholder="请选择素材类型"
              clearable
            >
              <el-option
                v-for="item in contentModuleTypes"
                :key="item.module_value"
                :label="item.module_name"
                :value="item.module_value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-input
              clearable
              v-model="form.material_name"
              placeholder="请输入素材名称"
              suffix-icon="el-icon-search"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-input
              clearable
              v-model="form.creator_name"
              placeholder="请输入上传人名称"
              suffix-icon="el-icon-search"
            ></el-input>
          </el-form-item>
          <el-form-item>

            <el-button type="primary" size="small" @click="refreshList()">搜索</el-button>
            <el-button size="small" plain @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table
          ref="mainTable"
          :data="tableData.records"
          max-height="551px"
          @selection-change="selectionChange"
          @sort-change="sortChange"
        >
          <!-- 单选 -->
          <el-table-column width="55" v-if="entryType === 'change'" :key="1"> 
            <template slot-scope="scope">
              <el-radio :disabled="materialStatus(scope.row) !== 'success'" @change="handleRowChange(scope.row)" v-model="tableRadio" :label="scope.row.file_id">&nbsp;</el-radio>
            </template>
          </el-table-column>
          <!-- 多选 -->
          <el-table-column width="55" type="selection" :selectable="handleDisable" v-else :key="2"></el-table-column>
          <el-table-column prop="file_id" label="素材id" width="100"></el-table-column>
          <el-table-column prop="file_show_name" label="素材名称" show-overflow-tooltip>
            <template v-slot="prop">
              <el-tooltip v-if="materialStatus(prop.row) === 'ing'" effect="dark" content="素材转码中，暂时无法使用，请耐心等待" placement="bottom">
                <img class="status-icon" style="width: 20px; height: 20px;" src="@/assets/mooc-img/warn-circle.png" alt="">
              </el-tooltip>
              <el-tooltip v-if="materialStatus(prop.row) === 'fail'" effect="dark" placement="bottom" popper-class="transcode-error">
                <div slot="content">素材转码失败，<span class="click-again" @click="clickReTranscode(prop.row)">点击此处重新转码</span></div>
                <img class="status-icon" style="width: 18px; height: 18px;" src="@/assets/mooc-img/close-circle.png" alt="">
              </el-tooltip>
              <span v-if="materialStatus(prop.row) !== 'success'">{{ prop.row.file_show_name }}</span>
              <a v-else class="content-url" target="_blank" :href="content_url + prop.row.file_id">{{ prop.row.file_show_name }}</a> 
            </template>
          </el-table-column>
          <el-table-column prop="" label="类型" width="100">
            <template slot-scope="scope">
              <span>{{ filterResourceName(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="creator_name" width="150" label="上传人"></el-table-column>
          <el-table-column prop="created_at" label="上传时间" width="180" sortable="custom" :sort-orders="['descending', 'ascending', null]"></el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button :disabled="scope.row.read_only" type="text" @click="handleEdit(scope.row)">编辑</el-button>
              <el-button :disabled="scope.row.read_only" class="checkout" type="text" @click="handleDelete(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          v-if="tableData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="current"
          :page-sizes="[5, 10, 20, 30, 50, 100]"
          :page-size="size"
          layout="total,  prev, pager, next, sizes, jumper"
          :total="tableData.total"
          >
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="cancel">取 消</el-button>
        <el-button size='small' @click="submit" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <addErrorDialog :visible.sync="addErrorDialogShow" :taskNameList="taskNameList"></addErrorDialog>
    <el-dialog
      class="quote-detail"
      :close-on-click-modal="false"
      title="引用详情"
      :visible.sync="quoteDetailVisible"
      :before-close="quoteDetailClose">
      <div class="quote-list">
        <div v-for="(item, index) in quoteList" :key="index" class="quote-item">
          <p>
            <span class="label" :class="item.resource_type === '培养项目' ? 'label-mooc' : 'label-net'">{{ item.resource_type }}</span>
            <span @click="goProjectDetail(item)" class="title">{{item.course_name}}</span>
            
          </p>
          <p class="info">
            <span>{{ item.creator_name }} 引用于 {{ item.created_at }}</span>
            <span></span>
          </p>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="quoteDetailVisible = false">知道了</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import pager from '@/mixins/pager'
import { getMaterialUserList, reTransCode, deleteMateria, materialQuoteList } from 'config/mooc.api.conf'
import addErrorDialog from './add-error-dialog.vue'
const file_type_info = {
  'Video': '1',
  'Audio': '2',
  'Scorm': '3',
  'Flash': '4',
  'Doc': '5'
}
export default {
  mixins: [pager],
  components: {
    addErrorDialog
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentNode: {
      type: Object
    },
    treeNode: {
      type: Array,
      default: () => ([])
    },
    entryType: { // 更换课程--添加课程区分
      type: String
    }
  },
  data() {
    return {
      tableRadio: '',
      form: {
        material_name: '',
        module_value: '',
        creator_name: ''
      },
      classifiyOptions: [],
      classifyProps: {
        multiple: false,
        label: 'classify_name', 
        value: 'classify_id',
        children: 'child',
        checkStrictly: true, 
        emitPath: false
      },
      contentModuleTypes: [
        { module_name: '视频', module_id: 1, course_type: ['Video', 'Video-2d', 'Video-3d', 'Video-ppt'], module_value: 0 },
        { module_name: '音频', module_id: 1, course_type: ['Audio'], module_value: 1 },
        { module_name: '文章', module_id: 8, course_type: ['Article'], module_value: 2 },
        { module_name: '文档', module_id: 16, course_type: ['Doc'], module_value: 3 },
        { module_name: 'scorm', module_id: 1, course_type: ['Scorm'], module_value: 4 },
        { module_name: '压缩包', module_id: 1, course_type: ['Flash'], module_value: 5 }
      ],
      tableData: {
        records: [],
        total: 0
      },
      selectionList: [],
      addErrorDialogShow: false,
      taskNameList: [],
      size: 5,
      sortOrder: '',
      sort_by: '',
      content_url: window.location.origin + '/training/material/play?material_id=',
      quoteDetailVisible: false,
      quoteList: []
    }
  },
  computed: {
    filterResourceName() {
      return ({ file_type, module_id }) => {
        let name = ''
        if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(file_type)) {
          name = '视频'
        } else if (file_type === 'Audio') {
          name = '音频'
        } else if (file_type === 'Article' || module_id === 8) {
          name = '文章'
        } else if (file_type === 'Doc' || module_id === 16) {
          name = '文档'
        } else if (file_type === 'Scorm') {
          name = 'Scorm'
        } else if (file_type === 'Flash') {
          name = '压缩包'
        }
        return name
      }
    },
    materialStatus() {
      return (item) => {
        if ((item.status === 13 && item.content_id) || (item.is_trans_sucessed === 1 && item.is_distribute_sucessed === 1)) {
          return 'success'
        } else if ([1, 2, 4, 5, 7, 8, 10, 11, 14, 15].includes(item.status)) {
          return 'ing'
        } else if ([3, 6, 9, 12, 16].includes(item.status)) {
          return 'fail'
        }
      }
    }
  },
  created() {
    this.onSearch()
    // 添加完素材刷新页面
    window.workReConnect = () => {
      this.onSearch()
    }
  },
  methods: {
    onSearch(page_no = 1) {
      const { material_name, module_value, creator_name } = this.form
      const course_type_info = this.contentModuleTypes.find((v) => v.module_value === module_value)
      const data = {
        fileName: material_name,
        moduleId: course_type_info?.module_id,
        fileType: course_type_info?.course_type,
        current: page_no,
        size: this.size,
        sort_order: this.sortOrder,
        sort_by: this.sort_by,
        creatorName: creator_name
      }
      // 优化参数，有值就传
      let params = {}
      for (let key in data) {
        let temp = data[key]
        if ((temp || temp === 0) && ((Array.prototype.isPrototypeOf(temp) && temp.length > 0) || !Array.prototype.isPrototypeOf(temp))) {
          params[key] = data[key]
        }
      }

      getMaterialUserList(params).then(res => {
        this.tableData.records = res.records.map((v) => {
          // 以防首字母小写处理
          const file_type = v.file_type ? v.file_type.slice(0, 1).toUpperCase() + v.file_type.slice(1).toLowerCase() : ''
          return {
            ...v,
            file_type
          }
        })
        this.tableData.total = res.total
      })
    },
    refreshList() {
      this.current = 1
      this.onSearch()
    },
    handleCommand(val) {
      const { href } = this.$router.resolve({
        name: 'materialUpload',
        query: {
          uploadType: val
        }
      })
      window.open(href, '_blank')
    },
    handleDisable(row) {
      if (this.materialStatus(row) === 'success') {
        return true
      } else {
        return false
      }
    },
    clickReTranscode(row) {
      let id = row.content_id ? row.content_id : row.file_id
      reTransCode(id).then(() => {
        this.onSearch(this.current)
      })
    },
    handleEdit(row) {
      if (row.content_id) {
        const { href } = this.$router.resolve({
          name: 'materialUpload',
          query: { 
            uploadType: row.file_type,
            file_id: row.file_id,
            file_type: file_type_info[row.file_type]
          }
        })
        window.open(href, '_blank')
        return
      }
      const url = process.env.NODE_ENV === 'production' ? `https://learn.woa.com/manage/upload/edit/${row.file_id}` : `https://test-learn.woa.com/manage/upload/edit/${row.file_id}`
      window.open(url, '_blank')
    },
    handleDelete(row) {
      this.$messageBox.confirm(row.file_show_name, '确定删除素材吗？', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        if (row.refer_count > 0) {
          this.$messageBox.confirm('', '当前素材已被其他资源引用，无法删除', {
            confirmButtonText: '查看引用详情',
            cancelButtonText: '关闭'
          }).then(() => {
            this.quoteDetailVisible = true
            this.getQuoteList(row.file_id)
          })
        } else {
          deleteMateria(row.file_id).then(res => {
            this.$message.success('删除成功')
            this.onSearch(this.current)
          })
        }
      })
    },
    getQuoteList(id) {
      materialQuoteList(id).then(res => {
        this.quoteList = res
      })
    },
    // 跳转至引用项目详情页
    goProjectDetail(item) {
      if (item.resource_type === '培养项目') {
        const { href } = this.$router.resolve({
          name: 'projectDetail',
          query: { mooc_course_id: item.course_id }
        })
        window.open(href, '_blank')
      } else if (item.resource_type === '网络课') {
        const { href } = this.$router.resolve({
          name: 'play',
          query: { course_id: item.course_id }
        })
        window.open(href, '_blank')
      }
    },
    quoteDetailClose() {
      this.quoteDetailVisible = false
    },
    viewQuoteDetail(row) {
    },
    // 单选
    handleRowChange(val) {
      this.selectionList = [val]
    },
    // 多选
    selectionChange(val) {
      this.selectionList = val
    },
    sortChange({ column, prop, order }) {
      this.sortOrder = order === 'ascending' ? 'asc' : order === 'descending' ? 'desc' : ''
      this.sort_by = this.sortOrder ? 'created_at' : ''
      this.current = 1
      this.onSearch()
    },
    submit() {
      if (this.selectionList.length === 0) {
        this.$message.error('请选择一个课程')
        return
      }
      // this.taskNameList = []
      // let newTaskList = []
      // if (this.currentNode?.id) { // 当前阶段
      //   newTaskList = (this.currentNode?.sub_tasks || []).filter((e) => e.task_type === 'task')
      // } else { // 当前任务树
      //   newTaskList = JSON.parse(JSON.stringify(this.treeNode)) 
      // }
      // newTaskList.forEach((v) => {
      //   this.selectionList.forEach((a) => {
      //     if (v.task_name === a.file_show_name) {
      //       this.taskNameList.push({
      //         name: v.task_name
      //       })
      //     }
      //   })
      // })
      // if (this.taskNameList?.length) {
      //   this.addErrorDialogShow = true
      //   return
      // }
      this.selectionList = this.selectionList.map((v) => {
        let resource_type = ''
        let resource_type_name = ''
        if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(v.file_type)) {
          resource_type = 'Video'
          resource_type_name = '视频'
        } else if (v.file_type === 'Flash') {
          resource_type = 'Zip'
          resource_type_name = '压缩包'
        } else if (v.file_type === 'Audio') {
          resource_type = 'Audio'
          resource_type_name = '音频'
        } else if (v.file_type === 'Scorm') {
          resource_type = 'Scorm'
          resource_type_name = 'Scorm'
        } else if (v.file_type === 'Article') { // 文章
          resource_type = 'Article'
          resource_type_name = '文章'
        } else if (v.file_type === 'Doc') { // 文档
          resource_type = 'Doc'
          resource_type_name = '文档'
        }
        return {
          ...v,
          id: this.currentNode?.id || '',
          required: true,
          act_id: v.file_id + '',
          resource_type,
          resource_type_name,
          task_name: v.file_show_name,
          act_name: v.file_show_name,
          radio: resource_type === 'Scorm' ? 1 : 2,
          isWordNum: '',
          word_number: v.word_num,
          act_type: '21', // 素材act_type为21
          resource_content_type: v.content_type || '',
          resource_url: v.file_url
        }
      })
      this.$emit('update:visible', false)
      this.$emit('handleShowSetDialog', this.selectionList)
      if (this.entryType === 'change') {
        this.$emit('changeSingleData', ...this.selectionList)
      }
    },
    handleReset() {
      this.form = {
        material_name: '',
        module_value: ''
      }
      this.current = 1
      this.size = 5
      this.sortOrder = ''
      this.sort_by = ''
      this.$refs.mainTable.clearSort() 
      this.onSearch()
    },
    cancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.list-dialog {
  .content-url {
    color:#0052d9;
    cursor: pointer;
  }
  .course-body-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .check-label-warning {
    color: #ff7548;
    background-color: rgba(253, 246, 236, 1);
    padding: 10px 16px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    i {
      margin-right: 10px;
      font-size: 20px;
    }
  }
  .title-right{
    color: #00000066;
    .default-button{
      margin: 0 16px;
      background: #fff;
      border: 1px solid #dcdfe6;
      color: #606266;
    }
    .default-button:hover, .default-button:focus {
      background: #fff;
      border-color: #0052D9;
      color: #0052D9;
    }

    .default-button:active {
      background: #fff;
      border-color: #0052D9;
      color: #0052D9;
    }
  }
  .checkout {
    color: #e34d59 !important;
  }
  .status-icon{
    position: relative;
    top: -2px;
    margin-right: 8px;
  }
  .quote-detail{
    :deep(.el-dialog__body){
      max-height: 560px;
      overflow: auto; 
    }
    .quote-list{
      .quote-item{
        border-bottom: 1px solid #eeeeeeff;
        margin-bottom: 16px;
        .label{
          height: 18px;
          padding: 0 4px;
          border: 1px solid #dcdcdc;
          font-size: 12px;
          border-radius: 2px;
        }
        .label-mooc{
          background: #FEF3E6;
          color: #ED7B2F;
          border-color: #FEF3E6;
        }
        .label-net{
          background: #ECF2FE;
          color: #0052D9;
          border-color: #ECF2FE;
        }
        .title{
          color: #0052D9;
          cursor: pointer;
          margin-left: 12px;
          line-height: 22px;
        }
        .info{
          color: #00000099;
          margin: 12px 0 16px;
        }
      }
    }
  }
}

</style>
<style lang="less">
.transcode-error{
  .click-again{
    text-decoration: underline;
    cursor: pointer;
  }
}
</style>
