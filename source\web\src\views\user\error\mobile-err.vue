<template>
  <div class="error-page-container">
    <img class="author-img" src="@/assets/img/error-author.png">
    <p class="tips">该页面暂不支持使用当前浏览器{{ isMooc ? '参与培训' : '' }}，推荐使用Chrome/Safari/QQ浏览器</p>
    <el-button type="primary" size="small" @click="handleCopy">{{isMooc ? '复制培养项目链接' : '复制链接'}}</el-button>
    <div v-if="qrUrl && isMooc" class="mobile-code-main">
      <img :src="qrUrl" />
      <p>支持微信/企业微信扫码参与培养项目</p>
    </div>
  </div>
</template>

<script>
import { getMobileQrcode } from '@/config/mooc.api.conf.js'
export default {
  name: 'mobile-err',
  components: {
  },
  data() {
    return {
      qrUrl: ''
    }
  },
  computed: {
    msg() {
      return sessionStorage.getItem('mobile-err-href')
    },
    isMooc() {
      return this.$route.query.label === 'mooc-error'
    }
  },
  mounted() {
    this.getMobileCode()
  },
  methods: {
    // 获取二维码
    getMobileCode() {
      const { page, mooc_course_id, task_id } = this.$route.query
      console.log(this.$route.query)
      if (!page) return // 不是mooc页面不用二维码
      const params = {
        scene: page === 'taskDetail' ? `${mooc_course_id}_${task_id}_mooc` : mooc_course_id,
        page: page === 'taskDetail' ? 'pages/webview/mooc/taskDetail' : 'pages/mooc/projectDetails/index',
        env_version: process.env.NODE_ENV === 'production' ? 'release' : 'trial'
      }
      getMobileQrcode(params).then((res) => {
        this.qrUrl = res ? `data:image/png;base64,${res}` : ''
      })
    },
    handleCopy() {
      let tempInput = document.createElement('input')
      tempInput.value = this.msg
      tempInput.id = 'creatDom'
      document.body.appendChild(tempInput)
      tempInput.select()
      if (document.execCommand('copy')) {
        document.execCommand('copy')
        this.$message.success('链接复制成功')
        let creatDom = document.getElementById('creatDom')
        creatDom.parentNode.removeChild(creatDom)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.error-page-container {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  text-align: center;
  padding: 0 20px;
  .author-img {
    width: 274px;
    height: 134px;
  }
  .tips {
    margin-top: 20px;
    margin-bottom: 20px;
    color: #000000cc;
    font-size: 16px;
    font-weight: 600;
  }
  .mobile-code-main {
    margin-top: 60px;
    p {
      color: #000000cc;
      font-size: 14px;
      line-height: 22px;
      margin-top: 20px;
    }
    img {
      width: 120px;
      height: 120px;
    }
  }
}
</style>
