<template>
  <div class="custom-tag-container">
    <el-button class="diy-tag" v-if="addTags.length >= 5" @click="addCustomTag" type="text">点击此处自定义标签</el-button>
    <el-popover v-else placement="bottom" width="300" v-model="visible" trigger="click">
      <div class="popver-box">
        <el-input v-model.trim="label_name" maxlength="10" show-word-limit placeholder="请输入标签名称"></el-input>
        <el-button size="small" @click="cancel">取 消</el-button>
        <el-button size="small" @click="confirm" type="primary">确 定</el-button>
      </div>
      <el-button class="diy-tag" slot="reference" type="text">点击此处自定义标签</el-button>
    </el-popover>
  </div>
</template>
<script>
import { customeTag } from '@/config/mooc.api.conf'
export default {
  props: {
    // visible: {
    //   type: <PERSON><PERSON><PERSON>,
    //   default: false
    // }
    addTags: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      label_name: '',
      visible: false
    }
  },
  methods: {
    addCustomTag() {
      if (this.addTags?.length >= 5) return this.$message.warning('项目标签数量已达上限，无法继续添加')
    },
    cancel() {
      this.visible = false
      this.label_name = ''
      // this.$emit('update:visible', false)
    },
    confirm() {
      customeTag({ label_name: this.label_name }).then((res) => {
        this.$emit('tagConfirm', {
          label_name: this.label_name,
          label_id: res,
          label_type: '2'
        })
        this.cancel()
      })
    }
  }
}
</script>
<style lang="less">
.el-popper {
  margin-top: 5px !important;

  .popver-box {

    text-align: center;

    .el-input {
      margin-bottom: 20px;
    }
  }
}
</style>
<style lang="less" scoped>
.custom-tag-container {
  .diy-tag {
    line-height: unset;
    padding: unset;
  }
}
</style>
