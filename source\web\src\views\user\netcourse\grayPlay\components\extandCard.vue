<template>
  <div class="course-main">
    <div class="course-title-row flex-box">
      <div class="couse-title">{{ title === '延伸学习' ? $langue('NetCourse_Extended', { defaultText: '延伸学习' }) : title }}</div>
      <div class="course-tabs flex-box" v-if="tabs.length">
        <div class="tabs-item" :class="{ 'active-item': curTabs === index }" v-for="(e, index) in tabs" :key="e.value" @click="switchTabs(e, index)">{{ e.label }}</div>
      </div>
    </div>
    <div class="course-swiper-box">
      <div 
      v-for="(e, index) in commonList" 
      :key="index"
      class='item-course-swiper'
      :class="{ 'item-course-active': e.currentCourse }"
      @click="toLink(e)"
      :dt-eid="dtEextendList('eid', e)"
      :dt-remark="dtEextendList('remark', e)"
      :dt-areaid="dtEextendList('areaid', e)" 
      >
        <div class="flex-box">
          <div class="item-icon" v-if="e.currentCourse">
            <img src="@/assets/img/icon-live.png" alt="" srcset="">
          </div>
          <div class="item-top overflow-l1">
            <span class="live-content">{{ e.content_name }}</span>
          </div>
        </div>
        <div class="item-bottom">
          <span>{{ showModuleName(e) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { actTypes } from '@/utils/moduleMap.js'
export default {
  props: {
    commonList: {
      type: Array,
      default: () => ([])
    },
    courseData: {
      type: Object,
      default: () => ({})
    },
    dtPageType: {
      type: String,
      default: '网课详情页-新版'
    },
    title: {
      type: String,
      default: '延伸学习'
    },
    tabs: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      curTabs: 0,
      courseDetail: {
        content_basic_list: [],
        name: '',
        view_count: 0
      },
      actTypes
    }
  },
  computed: {
    course_id() {
      return this.courseData.course_id || parseInt(this.$route.query.course_id)
    },
    showModuleName() {
      return (e) => {
        let content_module_id = e.content_module_id || e.module_id
        let act_type_name = e.act_type_name || e.module_name
        let tips = ''
        let row = this.actTypes.find((v) => v.module_id === content_module_id)
        if ([1].includes(content_module_id)) { // 网络课
          tips = `·${e.duration || 0}` + this.$langue('Mooc_ProjectDetail_TaskList_Minute', { defaultText: '分钟' })
        } else if ([7, 8].includes(content_module_id)) { // 案例-文章
          tips = `·${e.word_num || 0}` + this.$langue('Mooc_ProjectDetail_TaskList_Words', { defaultText: '字' })
        } else if ([10].includes(content_module_id)) { // 培养项目
          tips = `·${e.sub_count || 0}` + this.$langue('Mooc_ProjectDetail_BasicInfo_Tasks', { defaultText: '项任务' })
        } else if ([15].includes(content_module_id)) { // 课单
          tips = `·${e.sub_count || 0}` + this.$langue('NetCourse_Contents', { defaultText: '个内容' })
        }
        return `${this.$langue(row?.langKey, { defaultText: act_type_name })}` + tips
      }
    },
    dtEextendList() {
      return (type, row) => {
        const { area_id } = this.$route.query
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: this.dtPageType, 
            container: this.title,
            click_type: 'data',
            content_type: row.content_module_name || row.module_name,
            content_id: row.id,
            content_name: row.content_name,
            act_type: row.act_type || '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${area_id}_${row.id}`
        } else {
          return `area_${this.course_id}_${area_id}_${row.id}`
        }
      }
    }
  },
  mounted() {
  },
  methods: {
    switchTabs(v, i) {
      this.curTabs = i
      this.$emit('switchTabs', v)
    },
    // 跳转
    async toLink(v) {
      console.log(v)
      let href = v.href || v.content_url
      if (!href) return
      window.open(href)
    }
  }
}
</script>
<style lang="less" scoped>
.course-main {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;

  .course-title-row {
    justify-content: space-between;
    margin-bottom: 16px;
  }
  .couse-title {
    // max-width: 696px;
    color: #333333;
    line-height: 22px;
    font-size: 16px;
    font-weight: bold;
    display: inline-block;
    margin-right: 20px;
    flex-shrink: 0;
  }
  .course-tabs {
    flex: 1;
    .tabs-item {
      min-width: 48px;
      line-height: 22px;
      padding: 0 6px;
      border-radius: 4px;
      background: #EFEFEF;
      color: #333333;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      text-align: center;
      cursor: pointer;
      & + .tabs-item {
        margin-left: 8px;
      }
    }
    .active-item {
      background-color: #0052D9;
      color: #fff;
    }
  }
  .course-swiper-box {
    max-height: 155px;
    overflow: auto;
    padding-right: 6px;

    .item-course-swiper {
      height: 22px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: space-between;

      i {
        width: 16px;
        height: 16px;
        display: inline-block;
        margin-right: 4px;
      }

      .item-icon {
        width: 16px;
        height: 16px;
        margin-right: 4px;
        & > img {
          width: 16px;
          height: 16px;
        }
      }

      .item-top {
        width: 232px;
        .live-icon {
          background: url("~@/assets/img/icon-live.png") no-repeat center / cover;
          flex-shrink: 0;
          position: relative;
          top: 2px;
        }

        .live-content {
          line-height: 22px;
          color: #333333;
          word-break: break-all;
        }
      }

      .item-bottom {
        border-radius: 4px;
        background: #F5F7FA;
        font-size: 12px;
        color: #777777;
        flex-shrink: 0;
        height: 22px;
        padding: 0 6px;
        line-height: 22px;

        .circle {
          font-size: 12px;
          margin-left: 4px;
          margin-right: 4px;
          display: inline-block;
        }

        .timer {
          display: inline-block;
        }
      }
    }
    .item-course-swiper + .item-course-swiper {
      margin-top: 10px;
    }
    .item-course-active {
      .item-top .live-content {
        color: #0052D9;
      }
      .item-bottom {
        color: #333333;
      }
    }
  }
  .flex-box {
    display: flex;
    align-items: center;
  }
}
</style>
