<template>
  <div class="video-by-ppt course-make-style">
    <div class="tab-box">
      <!-- 1 -->
      <div class="header">{{title}}</div>
      <ul class="ul-tabs-chapter">
          <li 
          v-for="item in tabList" 
          :key="item.value"
          :class="[{'active': activeName === item.value}, 'li-tab-item']"
          @click.stop="changeTab(item.value)"
          >
            {{ item.label }}
          </li>
      </ul>
      <!-- ai做、课 -->
      <div class="content-main" v-show="activeName === 'basic'">
        <!-- 左侧步骤 -->
        <div class="step-left">
          <!-- 头部文字 -->
          <steps-header>
            <template v-slot:desc
              >提供授课PPT、讲稿（填写在PPT备注中），即可快捷生成包括PPT画面、授课语音的在线课程</template
            ></steps-header
          >
          <!-- 步骤区域 -->
          <div class="content">
            <el-steps direction="vertical" :active="activeLeft">
              <el-step title="上传PPT" id="pptUpload">
                <template slot="description">
                  <ppt-upload
                    @onPPTChange="onPPTChange"
                    :virtualInfo="virtualInfo"
                  />
                </template>
              </el-step>
              <el-step title="选择音色" id="voiceSelect">
                <template slot="description">
                  <voice-select
                    @onVoiceChange="onVoiceChange"
                    :virtualInfo="virtualInfo"
                    :disabledVoice="demoGenerat"
                  />
                </template>
              </el-step>
              <el-step title="效果预览" id="demoPreview">
                <template slot="title">
                  <span>效果预览</span>
                  <el-button
                    type="text"
                    class="sumbmit-btn"
                    v-show="!demoGenerat"
                    @click="onSubmit('demo')"
                    :disabled="demoDisabled"
                    >提交以上配置，生成Demo</el-button
                  >
                  <span class="demo-generat" v-show="demoGenerat">DEMO生成中</span>
                </template>
                <template slot="description">
                  <demo-preview ref="demoPreview" :demoData="demoData" />
                </template>
              </el-step>
              <el-step title="填写在线课程信息" id="courseInfo">
                <template slot="description">
                  <course-info
                    ref="courseInfo"
                    :courseInfo="courseInfo"
                    :pptName="pptName"
                    :pptImg="pptImg"
                    videoStatus="add"
                    @safeChange="safeChange"
                    @vaildAfterCode="vaildAfterCode"
                  />
                </template>
              </el-step>
            </el-steps>
          </div>
        </div>
        <!-- 右侧步骤条 -->
        <right-steps
          :stepList="stepList"
          :statusList="statusList"
          @isClickStep="isClickStep"
        />
      </div>
      <!-- 分段章节 -->
      <div class="chapters-content" v-show="hasPermission && (activeName === 'chapters')">
        <span class="pseudo-class-title">分段章节</span>
        <!-- 章节配置 -->
        <chapters-config 
          :approveStatus="false" 
          :activeName="activeName" 
          :estDur="courseInfo.est_dur" 
          :videoInfo="courseInfo.content_info || {}" 
          :chaptersList="chaptersList"
          :courseInfo.sync="courseInfo"
          @refreshChapterList="refreshChapterList" 
          @exportAiData="exportAiData"
          ref="chaptersConfig"
        >
        </chapters-config>
      </div>
      <!-- ai文章 -->
      <aiArticle 
      v-if="activeName === 'aiArticle'" 
      :courseData.sync="courseInfo"
      @getCourse="getCourse"
      >
      </aiArticle>
    </div>
    <!-- 底部按钮区域 -->
    <div class="buttom" v-if="activeName === 'basic'" style="height: auto;">
      <div class="inner">
        <convention-confirm v-model="isChooseConvention" style="line-height: initial;margin: 16px 0 0 0;" />
        <el-button @click="onSave">存草稿</el-button>
        <el-tooltip :disabled="!demoGenerat" effect="dark" content="Demo视频生成中，请稍后尝试" placement="top">
          <span><el-button :disabled="demoGenerat || !isChooseConvention" type="primary" @click="approveSafe">提交合成</el-button></span>
        </el-tooltip>
        <!-- <span class="tips" v-show="process_duration > 0"
          >预计转码发布需要约{{
            transforTime(process_duration)
          }}，发布成功后将通过邮件/企业微信通知您</span
        > -->
        <span class="tips" v-show="updated_at"
          >已保存草稿：{{ updated_at }}</span
        >
      </div>
    </div>
    <!-- 信息审核再次编辑异步变化弹窗 -->
    <informationSafetyDialog :isShow.sync="informationSafetyShow" @safeConfirm="onSubmit()"/>
  </div>
</template>

<script>
import stepsHeader from '../components/steps-header.vue'
import pptUpload from '../components/PPT-upload.vue'
import voiceSelect from '../components/voice-select.vue'
import demoPreview from '../components/demo-preview.vue'
import rightSteps from '../components/right-steps.vue'
import courseInfo from '../components/course-info.vue'
import chaptersConfig from '../components/chapters-config.vue'
import aiArticle from '../components/aiArticle.vue'
import informationSafetyDialog from '@/components/information-safety-dialog'
import {
  composeVideo,
  getVideoResult,
  updateCourse,
  addCourseDraft,
  getCourseInfo,
  checkTarget,
  getNetCourseChapters
} from 'config/api.conf'
import { transforTime } from 'utils/tools'
import moment from 'moment'
import conventionConfirm from '@/views/components/convention-confirm.vue'
// 定时器
let timer = null
let flag = true

export default {
  name: 'videoByPPT',
  components: {
    stepsHeader,
    pptUpload,
    voiceSelect,
    demoPreview,
    rightSteps,
    courseInfo,
    chaptersConfig,
    aiArticle,
    conventionConfirm,
    informationSafetyDialog
  },
  data() {
    return {
      activeName: 'basic',
      hasPermission: false, // 是否有权限访问章节配置
      tabList: [
        { label: '基础配置', value: 'basic' },
        { label: '分段章节', value: 'chapters' },
        { label: 'AI文章', value: 'aiArticle' }
      ],
      chaptersList: [], // 分段章节配置数据
      stepList: [
        {
          label: '上传PPT',
          id: 'pptUpload',
          process: true
        },
        {
          label: '选择音色',
          id: 'voiceSelect',
          process: false
        },
        {
          label: '效果预览',
          id: 'demoPreview',
          process: false
        },
        {
          label: '完善信息',
          id: 'courseInfo',
          process: false
        },
        {
          label: '提交审核',
          id: '',
          process: false
        },
        {
          label: '合成剪辑',
          id: '',
          process: false
        },
        {
          label: '确认发布',
          id: '',
          process: false
        }
      ],
      statusList: ['wait', 'wait', 'wait', 'wait'],
      activeLeft: 0,
      activeRight: 0,
      pptData: {},
      pptImg: '',
      pptName: '',
      voiceData: {},
      demoData: {
        video_duration: 0,
        content_id: '',
        request_id: '',
        src: '',
        msg: ''
      },
      // process_duration: 0,
      courseInfo: {},
      virtualInfo: {},
      demoGenerat: false,
      isCompose: false,
      updated_at: '',
      clickStepFlag: false,
      transforTime,
      isChooseConvention: false,
      safeInfoChange: false,
      informationSafetyShow: false
    }
  },
  computed: {
    title() {
      const title = ['1', '3'].includes(this.courseInfo.status) ? '编辑在线课程' : '上传在线课程'
      document.title = `${title}_Q-Learning`
      return title
    },
    demoDisabled() {
      return this.statusList.slice(0, 2).includes('wait') || this.isCompose
    },
    // ppt发生改变
    isPptChange() {
      return this.pptData.ppt_id && this.courseInfo.virtual_info?.ppt_id !== this.pptData.ppt_id
    },
    isSafeInfoChange() {
      const id = this.$route.query.net_course_id
      return id && (this.safeInfoChange || this.isPptChange)
    }
    // showDraftTime() {
    //   return this.process_duration === 0 && this.updated_at
    // }
  },
  created() {
    const id = this.$route.query.net_course_id
    if (id) {
      this.getCourse(id)
    }
    checkTarget().then((res) => {
      this.hasPermission = res || false
      if (id && this.hasPermission) {
        this.getChaptersInfo(id)
      }
    })
  },
  mounted() {
    // 左侧滚动，显示到右侧进度区
    document.getElementById('app').addEventListener('scroll', () => {
      if (flag && !this.clickStepFlag) {
        flag = false
        const arr = ['pptUpload', 'voiceSelect', 'demoPreview', 'courseInfo']
        let isSet = false
        arr.forEach((item, index) => {
          this.stepList[index].process = false
          const el = document.getElementById(item)
          const position = el?.getBoundingClientRect()
          if (position?.top >= 0 && !isSet) {
            this.stepList[index].process = true
            isSet = true
          }
        })
        if (!isSet) {
          this.stepList[3].process = true
        }
        setTimeout(() => {
          flag = true
        }, 50)
      }
    })
  },
  beforeDestroy() {
    timer && clearInterval(timer)
  },
  methods: {
    safeChange(value) {
      this.safeInfoChange = value
    },
    changeTab(tab) {
      // 分段章节导入并修改
      this.activeName = tab
      if (this.activeName === 'chapters') {
        this.$refs.demoPreview && this.$refs.demoPreview.pause()
      // 查询分段章节配置
      } else {
        this.$refs.chaptersConfig && this.$refs.chaptersConfig.pause()
      }
    },
    // 刷新章节配置
    refreshChapterList() {
      let id = this.$route.query.net_course_id
      if (id) {
        this.getChaptersInfo(id)
      }
    },
    // 获取章节设置
    getChaptersInfo(id) {
      const params = { 
        course_id: id 
      }
      getNetCourseChapters(params).then(res => {
        // console.log(res, '获取章节配置返回的res-------')
        this.chaptersList = res || []
      })
    },
    exportAiData(data) {
      const { chaptersRecord } = data
      this.chaptersList = chaptersRecord
    },
    // 滚动开关
    isClickStep(val) {
      this.clickStepFlag = val
    },
    // 获取课程详情
    getCourse() {
      const id = this.$route.query.net_course_id
      getCourseInfo(id).then((res) => {
        if (res) {
          // 视频正在处理中
          if (res.status === '5') {
            this.$router.replace({
              name: 'videoError'
            })
          }
          this.courseInfo = res
          this.updated_at = moment(this.courseInfo.updated_at).format(
            'YYYY年MM月DD日 HH时mm分ss秒'
          )
          this.virtualInfo = res.virtual_info || {}
          const { ppt_request, timbre_id, preview_id } = this.virtualInfo
          this.$set(this.demoData, 'src', preview_id || '')
          this.$set(this.statusList, 0, ppt_request ? 'success' : 'wait')
          this.$set(this.statusList, 1, timbre_id ? 'success' : 'wait')
          this.$set(this.statusList, 2, preview_id ? 'success' : 'wait')
        } else {
          // 视频正在转码
          this.$router.replace({
            name: 'videoError'
          })
        }
      })
    },
    // 当ppt文件改变
    onPPTChange(data) {
      this.pptData = data
      this.pptImg = data.pptImg
      this.pptName = data.file_name
      this.$set(this.statusList, 0, data.request_id ? 'success' : 'wait')
      this.clearTimer()
    },
    // 当音色选择改变
    onVoiceChange(data) {
      console.log('ppt', data)
      this.$set(this.statusList, 1, data.timbre_id ? 'success' : 'wait')
      this.voiceData = data
      this.clearTimer()
    },
    // 清除demo生成请求定时器
    clearTimer() {
      timer && clearInterval(timer)
      this.demoGenerat = false
      this.isCompose = false
    },
    // 点击进度滚动到对应进度区域
    scrollStep(index, id) {
      this.stepList.forEach((i, j) => {
        i.process = index === j
      })
      const anchorElement = document.querySelector(`#${id}`)
      const scrollConfig = {
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      }
      anchorElement && anchorElement.scrollIntoView(scrollConfig)
    },
    // 存草稿
    onSave() {
      const courseInfo = this.$refs.courseInfo.getParams()
      this.saveCourseInfo(courseInfo, 'save')
    },
    // 信息安全审核
    approveSafe() {
      if (this.isSafeInfoChange) {
        this.informationSafetyShow = true
        return
      }
      this.onSubmit()
    },
    // 提交，type为demo时生成demo，否则为生成
    onSubmit(type) {
      // 必填提示，并滚动到对应的步骤
      for (let k = 0; k < 2; k++) {
        if (this.statusList[k] === 'wait') {
          const msg = ['请上传PPT并解析', '请选择音色']
          this.$message.error(msg[k])
          this.scrollStep(k, this.stepList[k].id)
          return
        }
      }
      const { timbre_id, tts_speed, tts_source } = this.voiceData
      const params = {
        app_id: 'QLearningService',
        request_id: this.pptData.request_id,
        vcn: timbre_id,
        tts_speed: tts_speed,
        tts_source,
        only_audio: true
      }
      if (this.pptData.file) {
        params.ppt_file = this.pptData.file
      } else if (this.pptData.ppt_id) {
        params.ppt_content_id = this.pptData.ppt_id
      }
      let courseInfo = null
      if (type === 'demo') {
        this.$set(this.statusList, 2, 'wait')
        this.$set(this.demoData, 'msg', '')
        this.demoGenerat = true
        this.isCompose = true
        params.is_preview = true
      } else {
        // 表单必填校验，并滚动到对应的步骤
        courseInfo = this.$refs.courseInfo.vaildCourseInfo()
        if (!courseInfo) {
          this.$message.error('请完善课程信息')
          this.scrollStep(5, 'courseInfo')
          return
        }
        params.is_preview = false
      }
      if (type !== 'demo') {
        // this.saveCourseInfo(courseInfo, 'submit')
        this.$refs.courseInfo && (this.$refs.courseInfo.showSpecialDialogFn(courseInfo, 'submit'))
        return
      }
      // 先合成视频
      composeVideo(params)
        .then((res) => {
          console.log('合成哈哈哈', res)
          if (res && res.request_id) {
            if (type === 'demo') {
              this.demoData = {
                ...res,
                src: '',
                msg: ''
              }
              timer && clearInterval(timer)
              timer = setInterval(() => {
                this.getStatus()
              }, 3000)
            }
            //  else {
            //   this.process_duration = res.process_duration
            //   // 再保存课程信息
            //   this.saveCourseInfo(courseInfo, 'submit', res)
            // }
          } else {
            this.demoGenerat = false
            this.isCompose = false
            if (type === 'demo') {
              this.$set(
                this.demoData,
                'msg',
                '视频生成失败，请联系系统管理员协助处理'
              )
            } else {
              this.$message.error('视频生成失败，请联系系统管理员协助处理')
            }
          }
        })
        .catch((err) => {
          const msg = err.message || '视频生成失败，请联系系统管理员协助处理'
          if (type === 'demo') {
            this.$set(this.demoData, 'msg', msg)
          }
          this.demoGenerat = false
          this.isCompose = false
        })
    },
    vaildAfterCode({ pageData, value }) {
      this.saveCourseInfo(pageData, value)
    },
    // 保存课程信息
    saveCourseInfo(courseInfo, type) {
      const { timbre_id, timbre_type, tts_speed, tts_source } = this.voiceData
      const params = {
        course_type: 'Video-ppt',
        virtual_info: {
          request_id: this.pptData.request_id,
          ppt_id: this.pptData.ppt_id,
          ppt_text: this.pptData.ppt_text,
          file_name: this.pptData.file_name,
          file_size: this.pptData.file_size,
          upload_time: this.pptData.upload_time,
          timbre_id,
          timbre_type,
          tts_speed,
          tts_source,
          preview_id: this.demoData.src
        },
        ...courseInfo
      }
      const course_id = this.$route.query.net_course_id
      if (course_id) {
        params.net_course_id = course_id
      }
      let api = type === 'submit' ? updateCourse : addCourseDraft
      // if (type === 'submit') {
      //   api = updateCourse
      //   params.content_info = {
      //     file_name: '',
      //     content_id: videoData && videoData.content_id,
      //     file_size: ''
      //   }
      // } else {
      //   api = addCourseDraft
      //   params.content_info = {}
      // }

      // 如果是提交上架
      if (type === 'submit') {
        if (course_id) { // 编辑
          if (this.courseInfo.status && this.courseInfo.status !== '4') { // 非草稿进来
            params.status = this.courseInfo.status || ''
          } else { // 草稿
            params.status = '6'
          }
        } else { // 新增
          params.status = '6'
        }
      } else {
        delete params.status
      }
      api(params).then((net_course_id) => {
        this.clearTimer()
        this.$message.success(
          type === 'save'
            ? '已保存草稿'
            : '保存成功'
            // `提交成功, 预计转码发布需要约${transforTime(
            //     this.process_duration
            //   )}，发布成功后将通过邮件/企业微信通知您`
        )
        setTimeout(() => {
          if (type === 'save') {
            this.updated_at = moment().format('YYYY年MM月DD日 HH时mm分ss秒')
            this.$router.replace({
              name: this.$route.name,
              query: {
                net_course_id
              }
            })
            // 数据重新回显，并把状态改为草稿状态
            this.courseInfo = {
              ...params,
              status: '4',
              net_course_id
            }
          } else {
            this.$router.push({ name: 'courseList' })
          }
        }, 1000)
      })
    },
    // 获取demo生成状态，暂时10秒调用一次
    getStatus() {
      getVideoResult({
        app_id: 'QLearningService',
        request_id: this.demoData.request_id
      })
        .then((res) => {
          const status = [0, 200, 1003, 1006, 1007, 1008, 1009]
          if (res) {
            if (status.includes(res.status)) {
              if (res.status === 200) {
                this.$set(
                  this.demoData,
                  'src',
                  res.cos_prefix + res.final_vh_path
                )
                this.$set(this.statusList, 2, 'success')
                this.$set(this.demoData, 'msg', '')
                timer && clearInterval(timer)
                this.demoGenerat = false
              } else {
                this.clearTimer()
                this.$set(
                  this.demoData,
                  'msg',
                  'DEMO生成失败，请联系v_huiahuang(黄辉煌)'
                )
              }
            } else {
              this.$set(this.demoData, 'msg', '')
            }
          }
        })
        .catch(() => {
          this.clearTimer()
          this.$set(this.demoData, 'msg', '网络异常，请稍后重试！')
        })
    }
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/course-make.less';
</style>
