<template>
  <div class="des-main-container">
    <div class="des-main">
      <div class="title-box">
        <span class="title-left">
          <span class="tag">{{ titleTagName(courseData.course_type) }}</span>
          <!-- <img class="label-icon" v-if="showOutcourseType" src="../../../../assets/img/mobile/geekBang/label.png" /> -->
          <img class="label-icon" v-if="showOutcourseType" :src="typeImg" />
          <span class="title">{{ courseData.course_title }}</span>
        </span>
        <span
          :class="[{ 'active-expand-des': showDesExpand }, 'expand-des-icon']"
          @click="showDesContent"
        ></span>
      </div>
      <div class="title-icon">
        <span class="item-icon">
          <span class="play-i"></span>{{ summaryData.view_count || 0 }}
        </span>
        <span class="item-name">{{ courseData.authorInfo }}</span>
        <span class="created-time">{{ courseData.created_at }}</span>
      </div>
      <div v-if="showDesExpand">
        <!-- 简介 -->
        <div v-if="showDes" class="des-content" @click="showDes = !showDes" v-html="courseData.course_intro"></div>
        <div v-else class="all-des-content" @click="showDes = !showDes" v-html="courseData.course_intro" ></div>
        <!-- 标签列表 -->
        <div :class="['tag-list-box', 'clearfix', {'tag-height': showExpand}]">
          <span
            v-for="item in tagList"
            :key="item.label_id"
            :class="[{ 'hot-tag': item.label_hot_enable === 1 }, { 'official-tag': item.label_type === 1 }, 'item-tag']"
          >
            <span :class="[{ 'hot-tag-i': item.label_hot_enable === 1 }, { 'official-tag-i': item.label_type === 1 }]"></span>
            <span class="tag-label">{{ item.label_name }}</span>
          </span>
        </div>
        <div class="expand-and-open" v-show="isOverflow" @click="expandTag">
          {{ showExpand ? $langue('NetCourse_Expand', { defaultText: '展开' }) : $langue('NetCourse_Retract', { defaultText: '收起' }) }}
          <span :class="[{ 'packUp-icon': !showExpand }, 'expand-icon']"></span>
        </div>
      </div>
      <!-- 其它功能 -->
      <div class="person-info-box" v-if="courseData.independent_course === 1">
        <div class="item-info" @click="handleLike">
          <span
            :class="[activePrise ? 'active-like' : 'like-i-name', 'icon']"
          ></span>
          <div :class="[{ 'active-label': activePrise }, 'label']">
            {{ summaryData.praise_count || 0 }}
          </div>
        </div>
        <div class="item-info" @click="handleFavorited">
          <span
            :class="[activefav ? 'active-start' : 'start-i-name', 'icon']"
          ></span>
          <div :class="[{ 'active-label': activefav }, 'label']">
            {{ summaryData.fav_count || 0 }}
          </div>
        </div>
        <div class="item-info" @click="toComment">
          <span class="chart-i-name icon"></span>
          <div class="label">{{ summaryData.recomm_count || 0 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { 
  getSummaryData,
  checkPraised,
  addPraise,
  deletePraise,
  checkFavorited,
  addFavorited,
  deleteFavorite
} from 'config/mooc.api.conf.js'
import { Toast } from 'vant'
export default {
  components: {
  },
  props: {
    courseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      act_type: '102', // 课程类型 102-极客时间
      summaryData: {}, // 课程互动统计
      showDes: true, // 简介显示
      showDesExpand: true, // 简介展开/关闭
      tagList: [], // 标签列表
      showExpand: false, // 标签展开/关闭
      isOverflow: false, // 标签显示按钮
      activePrise: false, // 点赞
      activefav: false // 收藏
    }
  },
  computed: {
    course_id() {
      return this.$route.query.course_id || ''
    },
    titleTagName() {
      return (val) => {
        if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(val)) {
          return this.$langue('Mooc_Common_ResourceType_Video', { defaultText: '视频' })
        } else if (val === 'Audio') {
          return this.$langue('Mooc_Common_ResourceType_Audio', { defaultText: '音频' })
        } else if (val === 'Article') {
          return this.$langue('Mooc_Common_ResourceType_Article', { defaultText: '文章' })
        } else if (val === 'Doc') {
          return this.$langue('Mooc_Common_ResourceType_Doc', { defaultText: '文档' })
        } else if (val === 'Scorm') {
          return 'scorm'
        } else if (val === 'Flash') {
          return this.$langue('Mooc_Common_ResourceType_Zip', { defaultText: '压缩包' })
        }
      }
    },
    showOutcourseType() {
      return ['geekBang', 'imooc', 'hundun'].includes(this.courseData.recourse_from)
    },
    typeImg() {
      let urlMap = {
        geekBang: require('../../../../assets/img/mobile/geekBang/label.png'),
        imooc: require('../../../../assets/img/mobile/geekBang/mukewang.png'),
        hundun: require('../../../../assets/img/mobile/geekBang/hundun.png')
      }
      return urlMap[this.courseData.recourse_from] || urlMap['geekBang']
    }
  },
  watch: {
    courseData() {
      this.getSummaryInfo()
      this.$nextTick(() => {
        if (this.courseData.labels?.length) {
          this.tagList = this.courseData.labels
          const tag = document.querySelector('.item-tag')
          if (!tag) return
          const el = document.querySelector('.tag-list-box')
          if (el.clientHeight > tag.clientHeight * 2) {
            this.isOverflow = true
            this.showExpand = true
          }
        }
      })
    }
  },
  mounted() {
  },
  methods: {
    // 获取统计数据
    getSummaryInfo() {
      getSummaryData({
        act_type: this.act_type,
        course_id: this.courseData.outsourced_course_id
      }).then(res => {
        this.summaryData = res
        this.getPraisedStatus()
        this.getFavoritedStatus()
      })
    },
    // 标签展开
    expandTag() {
      this.showExpand = !this.showExpand
    },
    // 简介展开
    showDesContent() {
      this.showDesExpand = !this.showDesExpand
    },
    // 获取点赞状态
    getPraisedStatus() {
      checkPraised({
        act_type: this.act_type,
        course_id: this.courseData.outsourced_course_id
      }).then(res => {
        this.activePrise = res
      })
    },
    // 点赞/取消点赞
    handleLike() {
      let praiseCommonAPI = null
      let tip = null
      if (this.activePrise) {
        praiseCommonAPI = deletePraise
        this.summaryData.praise_count--
        tip = this.$langue('Mooc_Common_Alert_CancelPraiseSucessed', { defaultText: '取消点赞成功' })
      } else {
        praiseCommonAPI = addPraise
        this.summaryData.praise_count++
        tip = this.$langue('Mooc_Common_Alert_PraiseSucessed', { defaultText: '点赞成功' })
      }
      praiseCommonAPI({
        act_type: this.act_type,
        course_id: this.courseData.outsourced_course_id
      }).then((res) => {
        if (res.data) {
          Toast(`${tip}`)
          this.activePrise = !this.activePrise
        }
        this.getPraisedStatus()
      })
    },
    // 获取收藏状态
    getFavoritedStatus() {
      checkFavorited({
        act_type: this.act_type,
        course_id: this.courseData.outsourced_course_id
      }).then(res => {
        this.activefav = res
      })
    },
    // 收藏/取消收藏
    handleFavorited() {
      let praiseCommonAPI = null
      let tip = null
      if (this.activefav) {
        praiseCommonAPI = deleteFavorite
        this.summaryData.fav_count--
        tip = this.$langue('Mooc_Common_Alert_CancelCollectSucessed', { defaultText: '取消收藏成功' })
      } else {
        praiseCommonAPI = addFavorited
        this.summaryData.fav_count++
        tip = this.$langue('Mooc_Common_Alert_CollectSucessed', { defaultText: '收藏成功' })
      }
      praiseCommonAPI({
        act_type: this.act_type,
        course_id: this.courseData.outsourced_course_id
      }).then((res) => {
        if (res.data) {
          Toast(`${tip}`)
          this.activefav = !this.activefav
        }
        this.getFavoritedStatus()
      })
    },
    // 去评论
    toComment() {
      this.$emit('toComment')
    }
  }
}
</script>
<style lang="less" scoped>
.des-main {
  background-color: #fff;
  padding: 16px;
  font-size: 12px;
  //清除浮动
  .clearfix:after {
    content: '';
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  .title-box {
    display: flex;
    align-items: flex-start;
    .title-left {
      display: inline-block;
      width: 310px;
      .label-icon {
        height: 20px;
        margin-right: 8px;
        position: relative;
        top: -2px;
        z-index: 0;
      }
    }
    .tag {
      display: inline-block;
      text-align: center;
      font-size: 12px;
      width: 50px;
      line-height: 16px;
      border-radius: 2px;
      border: 1px solid #0052d9ff;
      background: #ecf2feff;
      color: #0052d9;
      margin-right: 10px;
    }
    .title {
      font-size: 16px;
      font-weight: bold;
      line-height: 24px;
      word-break: break-all;
    }
    .expand-des-icon {
      background: url('~@/assets/img/mobile/arrow-down.png') no-repeat
        center/cover;
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-left: 16px;
      margin-top: 3px;
      transition: all 0.1s;
      transform: rotate(0deg);
    }
    .active-expand-des {
      transform: rotate(180deg);
    }
  }
  .title-icon {
    display: flex;
    align-items: center;
    line-height: 20px;
    margin-top: 8px;
    color: #00000066;
    .item-icon {
      display: flex;
      align-items: center;
      min-width: 30px;
      margin-right: 16px;
    }
    .item-name {
      margin-right: 16px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    .created-time {
      min-width: 110px;
    }
    .play-i {
      background: url('~@/assets/img/mobile/play.png') no-repeat center/cover;
      display: inline-block;
      width: 12px;
      height: 12px;
      margin-right: 4px;
    }
  }
  .des-content {
    max-height: 44px;
    line-height: 20px;
    color: #00000066;
    margin-top: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    word-break: break-all;
    /*! autoprefixer: ignore next */
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    /* autoprefixer: on */
  }
  .all-des-content {
    line-height: 20px;
    color: #00000066;
    margin-top: 8px;
  }
  .tag-list-box {
    margin-top: 12px;
    .official-tag {
      font-size: 14px;
    }
    .item-tag {
      height: 24px;
      line-height: 24px;
      text-align: center;
      border-radius: 22px;
      opacity: 1;
      font-size: 10px;
      padding: 0px 12px;
      color: #00000099;
      background: #f7f8faff;
      float: left;
      margin-right: 8px;
      margin-bottom: 8px;
      .tag-label {
        line-height: 16px;
        height: 16px;
        max-width: 132px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
      }
    }
    .official-tag {
      background: #ecf2feff;
      color: #0052d9ff;
      &-i {
        background: url('~@/assets/img/mobile/official-tag.png') no-repeat;
        background-size: 14px;
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
        position: relative;
        top: 3px;
      }
    }
    .hot-tag {
      background: #fbf2e8;
      color: #ff5923ff;
      &-i {
        background: url('~@/assets/img/mobile/hot-tag.png') no-repeat;
        background-size: 14px;
        width: 14px;
        height: 14px;
        display: inline-block;
        margin-right: 4px;
        position: relative;
        top: 3px;
      }
    }
  }
  .expand-and-open {
    text-align: center;
    color: #00000099;
    font-size: 12px;
    line-height: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    // margin-top: 8px;
    .expand-icon {
      background: url('~@/assets/img/mobile/arrow-down.png') no-repeat
        center/cover;
      height: 16px;
      width: 16px;
      display: inline-block;
      transform: rotate(0deg);
      transition: all 0.3s;
      margin-top: 2px;
    }
    .packUp-icon {
      transform: rotate(180deg);
    }
  }
  .tag-height {
    height: 32px;
    overflow: hidden;
  }
  .person-info-box {
    display: flex;
    justify-content: left-content;
    align-items: center;
    margin-top: 20px;
    font-size: 10px;
    .item-info {
      width: 25%;
      text-align: center;
    }
    .icon {
      display: inline-block;
      width: 24px;
      height: 24px;
    }
    .label {
      line-height: 16px;
      margin-top: 2px;
    }
    .active-label {
      color: #0052d9ff;
    }
    .like-i-name {
      background: url('~@/assets/img/mobile/like.png') no-repeat center/cover;
    }
    .active-like {
      background: url('~@/assets/img/mobile/like-active.png') no-repeat
        center/cover;
    }
    .start-i-name {
      background: url('~@/assets/img/mobile/start.png') no-repeat center/cover;
    }
    .active-start {
      background: url('~@/assets/img/mobile/start-active.png') no-repeat
        center/cover;
    }
    .chart-i-name {
      background: url('~@/assets/img/mobile/chart.png') no-repeat center/cover;
    }
    .add-i-name {
      background: url('~@/assets/img/mobile/add.png') no-repeat center/cover;
    }
    .share-i-name {
      background: url('~@/assets/img/mobile/share.png') no-repeat center/cover;
    }
  }
}
</style>
