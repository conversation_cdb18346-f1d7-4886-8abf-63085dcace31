.activity-common {

  // 公共弹窗样式
  :deep(.el-dialog) {
    border-radius: 9px;
    .el-dialog__header {
      border-bottom-color: #eeeeee;
    }
    .el-dialog__body {
      padding: 24px 32px 14px;
    }
    .el-dialog__headerbtn {
      top: 27px;
      .el-dialog__close {
        color: #00000099;
        &:hover {
          color: #007aff;
        }
      }
    }
    .el-dialog__footer {
      padding: 10px 32px 24px;
    }
    .el-form .el-form-item {
      margin-bottom: 14px;
    }
  }
  .dialog-header {
    padding: 4px 0;
    line-height: 24px;
  }
  // 弹窗底部按钮样式
  .cancel-btn {
    min-width: 60px;
    background-color: #e7e7e7;
    border-color: #e7e7e7;
    &:hover {
      opacity: 0.8;
    }
  }
  .confirm-btn {
    min-width: 60px;
    background-color: #0052d9;
    border-color: #0052d9;
    &:hover {
      opacity: 0.8;
    }
  }
  
  // 自定义表单样式
  .form-item {
    display: flex;
    align-items: flex-start;
    line-height: 22px;
    margin-bottom: 23px;
    &:not(:last-child) {
      margin-bottom: 16px;
    }
    .form-item-label {
      width: 56px;
      text-align: right;
      color: #00000099;
      font-size: 14px;
      line-height: 22px;
      margin-right: 12px;
      flex-shrink: 0;
    }
    .form-item-content {
      margin-right: 24px;
      flex: 1;
      :deep(.el-checkbox-group) {
        height: fit-content;
      }
      :deep(.el-checkbox) {
        margin-right: 20px;
        .el-checkbox__label {
          line-height: 22px;
          padding-left: 8px;
        }
        .el-checkbox__input.is-checked .el-checkbox__inner {
          border-radius: 4px;
        }
      }
    }
  }

  // 自定义表格样式
  .content-table {
    border-radius: 4px;
    opacity: 1;
    border-top: 1px solid #eeeeeeff;
    border-left: 1px solid #eeeeeeff;
    border-right: 1px solid #eeeeeeff;
    .content-url {
      color: #0052D9;
    }
  }

  :deep(.el-table) {
    .table-header-style th {
      color: #00000099;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 14px;
      background-color: #F5F5F5;
    }
    .el-table__body-wrapper table,
    .el-table__header-wrapper table {
      width: max-content !important;
    }
  }
  :deep(.el-pagination) {
    margin-top: 13px;
  }

  :deep(.el-button--text) {
    &:hover, &:focus {
      background-color: transparent;
    }
  }
}

// 单行文本溢出显示省略号
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// 多行文本溢出显示省略号
.text-ellipsis-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.flex-box {
  display: flex;
  align-items: center;
}
.flex-column {
  flex-direction: column;
}
.flex-between {
  justify-content: space-between;
}
.flex-1 {
  flex: 1;
}
.flex-j-c {
  justify-content: center;
}
.flex-j-s {
  justify-content: flex-start;
}
.flex-j-e {
  justify-content: flex-end;
}
.flex-a-c {
  align-items: center;
}
.flex-a-s {
  align-items: flex-start;
}
.flex-a-e {
  align-items: flex-end;
}

// 颜色
.color-primary {
  color: #0052D9;
}
.color-success {
  color: #00A870;
}
.color-warning {
  color: #ED7B2F;
}
.color-danger {
  color: #E34D59;
}
.color-fail {
  color: #00000099;
}
.color-orange {
  color: #E37318;
}

.for(@i) when(@i <= 160) { //@i
  .mr-@{i} {
    margin-right: @i * 1px;
  }
  .ml-@{i} {
    margin-left: @i * 1px;
  }
  .mt-@{i} {
    margin-top: @i * 1px;
  }
  .mb-@{i} {
    margin-bottom: @i * 1px;
  }
  .pr-@{i} {
    padding-right: @i * 1px;
  }
  .pl-@{i} {
    padding-left: @i * 1px;
  }
  .pt-@{i} {
    padding-top: @i * 1px;
  }
  .pb-@{i} {
    padding-bottom: @i * 1px;
  }
  .for(@i + 2); //@i + 1 相当于"++i"
}
.for(2);

.m-0 {
  margin: 0;
}
