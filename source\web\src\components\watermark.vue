/* eslint-disable no-prototype-builtins */
<template>
  <div class="component-wrapper">
  </div>
</template>

<script>
export default {
  props: {
    // 被覆盖水印的目标元素的id
    targetId: {
      required: false
    },
    // 被覆盖水印的目标元素的class
    targetClass: {
      required: false
    },
    // 水印文字
    text: {
      type: String
    },
    // 水印图案的用户配置项
    canvasUserOptions: Object,
    // 水印元素的用户配置项
    wmUserOptions: Object,
    // 是否手动初始化
    // 因为水印元素依赖于目标元素，如果目标元素没有渲染完成，那么水印元素不能正常工作，所以用户可根据实际情况进行手动初始化
    isManualInit: {
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      // 水印元素的id
      watermarkId: '',
      // 水印图案，由canvas生成的url
      url: '',
      // 被覆盖水印的目标元素
      $target: undefined,
      // 水印元素
      $wm: undefined,
      // 水印图案的配置项
      canvasOptions: {
        width: 200,
        height: 160,
        fillStyle: 'rgba(12, 12, 12, 0.1)',
        font: '24px Microsoft Yahei',
        translate: {
          x: 20,
          y: 20
        },
        rotateDegree: 39
      },
      // 水印元素的配置项
      wmOptions: {
        'z-index': 99999
      },
      modifyCallback: null,
      // 是否关闭监控告警
      isCloseModifyAlert: false,
      destroyed: false
    }
  },
  methods: {
    /**
     * [init 进行一些初始化操作]
     * @return {[type]} [description]
     */
    init () {
      if (this.targetId) {
        // 生成水印元素的id，用于监控该元素是否被删除
        // 后缀是为了增强id随机性
        this.watermarkId = this.targetId + '_watermark_xx512'
        // 获取到目标元素
        this.$target = document.getElementById(this.targetId)
      } else if (this.targetClass) {
        this.watermarkId = this.targetClass + '_watermark_xx512'
        this.$target = document.getElementsByClassName(this.targetClass)[0]
      }
      // 生成水印图案的配置项
      this.createCanvasOption()
      // 生成水印元素的配置项
      this.createWmOption()
      // 生成水印图案的url
      this.url = this.createCanvasDataUrl()
    },

    /**
     * [addWatermark 添加水印]
     */
    addWatermark () {
      if (this.$target) {
        this.addWatermarkToTarget()
        this.observeWaterMark()
      }
    },

    /**
     * [createCanvasOption 根据用户传入的参数，生成水印图案的配置项]
     * @return {[type]} [description]
     */
    createCanvasOption () {
      for (const key in this.canvasUserOptions) {
        // eslint-disable-next-line no-prototype-builtins
        if (this.canvasOptions.hasOwnProperty(key)) {
          this.canvasOptions[key] = this.canvasUserOptions[key]
        }
      }
    },

    /**
     * [createWmOption 根据用户传入的参数，生成水印元素的配置项]
     * @return {[type]} [description]
     */
    createWmOption () {
      for (const key in this.wmUserOptions) {
        // eslint-disable-next-line no-prototype-builtins
        if (this.wmOptions.hasOwnProperty(key)) {
          this.wmOptions[key] = this.wmUserOptions[key]
        }
      }
    },

    /**
     * [createCanvasDataUrl 生成水印图案的url]
     * @return {[type]} [description]
     */
    createCanvasDataUrl () {
      // 创建canvas
      const canvas = document.createElement('canvas')
      canvas.width = this.canvasOptions.width
      canvas.height = this.canvasOptions.height
      const ctx = canvas.getContext('2d')
      ctx.fillStyle = this.canvasOptions.fillStyle
      ctx.font = this.canvasOptions.font
      ctx.translate(this.canvasOptions.translate.x, this.canvasOptions.translate.y)
      ctx.rotate(this.canvasOptions.rotateDegree * Math.PI / 180)
      ctx.fillText(this.text, 20, 20)
      return canvas.toDataURL('image/png')
    },

    /**
     * [addWatermarkToTarget 在目标元素上面添加水印层，这种方式没有直接修改目标元素的background，这样可以单独操纵水印元素]
     */
    addWatermarkToTarget () {
      // 创建水印覆盖目标元素
      const $wm = document.createElement('div')
      $wm.setAttribute('id', this.watermarkId)
      // $wm.style.width = '100%'
      $wm.style.width = getComputedStyle(this.$target).width
      // 注意：此处不能使用$wm.height('100%');那样只会渲染一屏
      $wm.style.height = '100%'
      // $wm.style.height = getComputedStyle(this.$target).height
      $wm.style.position = 'absolute'
      $wm.style.top = '0px'
      $wm.style.left = '0px'
      $wm.style['pointer-events'] = 'none'
      for (const key in this.wmOptions) {
        $wm.style[key] = this.wmOptions[key]
      }
      // this.url = this.createCanvasDataUrl();
      $wm.style.background = 'url(' + this.url + ') repeat top left'
      this.$wm = $wm
      this.$target.append($wm)
    },

    /**
     * [observeWaterMark 监控水印元素，从两方面防止被修改：1.属性被修改，2.元素被删除]
     * @param  {[type]} $wm      [水印元素]
     * @param  {[type]} selector [被添加水印的目标元素的选择器]
     * @param  {[type]} url      [水印的url]
     * @return {[type]}          [description]
     */
    observeWaterMark () {
      const obConfig = {
        attributes: true,
        characterData: true
      }
      // 增加监控，防止水印被修改
      const observer = new MutationObserver((mutations, observer) => {
        for (const m of mutations) {
          // 先取消监听，避免死循环
          observer.disconnect()
          // 此处用了一点小技巧：直接删除$wm元素，删除动作会引发下面的监控，进而重新生成元素
          this.$wm.parentNode.removeChild(this.$wm)
          // 如果用户删除或者修改了id，那么下面n.id==this.watermarkId将会为flase，那么不能重新渲染水印元素，所以此处要对这一情况单独处理
          if (m.attributeName === 'id') {
            this.addWatermarkToTarget()
            this.observeWaterMark()
          }
        }
      })
      observer.observe(this.$wm, obConfig)

      // 进一步加强监控，防止元素被删除
      // 因为
      const pObserver = new MutationObserver((mutations, observer) => {
        for (const m of mutations) {
          if (m.type === 'childList' && m.removedNodes.length > 0) {
            for (const n of m.removedNodes) {
              if (n.id === this.watermarkId || n.id === 'watermark-warpper') {
                pObserver.disconnect()
                // 如果是代码内删除的水印则不重新生成水印
                if (!this.destroyed) {
                  this.addWatermarkToTarget()
                  this.observeWaterMark()
                }
                if (!this.isCloseModifyAlert) {
                  // 如果是删除了父元素
                  if (n.id === 'watermark-warpper') {
                    setTimeout(() => {
                      location.reload()
                    }, 1000)
                  }
                } else {
                  this.isCloseModifyAlert = false
                }
              }
            }
          }
        }
      })
      const pObConfig = {
        childList: true,
        subtree: true
      }
      const videoBox = document.getElementById('videoBox')
      if (videoBox) {
        pObserver.observe(videoBox, pObConfig)
      } else {
        pObserver.observe(this.$target, pObConfig)
      }
    },
    createWatermark () {
      this.destroyed = false
      this.init()
      this.addWatermark()
    },
    refreshWatermark () {
      if (this.$wm) {
        this.$wm.parentNode.removeChild(this.$wm)
      } else {
        this.init()
        this.addWatermark()
      }
    },
    destroyWatermark () {
      this.destroyed = true
      this.$wm.parentNode.removeChild(this.$wm)
    }
  },
  mounted () {
    if (!this.isManualInit) {
      this.init()
      this.addWatermark()
    }
  }
}
</script>
