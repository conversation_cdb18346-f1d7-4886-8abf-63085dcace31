<template>
  <div class="directly-upload-container" :class="{ 'chapters-config-container': activeName === 'chapters' }">
    <div v-if="!approveStatus" class="header">{{title}}</div>
    <div class="tab-box">
      <ul class="ul-tabs-chapter">
          <li 
          v-for="item in tabList" 
          :key="item.value"
          :class="[{'active': activeName === item.value}, 'li-tab-item']"
          @click.stop="changeTab(item.value)"
          >
            {{ item.label }}
          </li>
      </ul>
      <div class="main-content" v-show="activeName === 'basic'">
        <div class="vedio-upload">
          <span class="pseudo-class-title">视频素材<span class="tips" v-if="$route.query.net_course_id">完整课程视频如下，点击播放器即可预览</span></span>
          <videoUpload 
          ref="uploadVideo" 
          :approveStatus="approveStatus" 
          :videoInfo="courseInfo.content_info" 
          :courseInfo="courseInfo" 
          @onVideoChange="onVideoChange" 
          @onVideoChangeType="onVideoChangeType"
          >
          </videoUpload>
        </div>
        <div class="subttile-box">
          <span class="pseudo-class-title">外挂字幕</span>
          <div class="subttile-set-main">
            <el-radio-group :disabled="approveStatus" v-model="subttileRadio" @change="handleOpenSubttile">
              <el-radio :label="0">关闭</el-radio>
              <el-radio :label="1">开启</el-radio>
            </el-radio-group>
            <el-upload
            v-show="subttileRadio === 1 && !subttileSetInfo.caption_id"
            action=""
            :with-credentials="true"
            :http-request="onUpload"
            :file-list="subttileSetInfo.fileList"
            :show-file-list="false"
            >
              <div id="srt-directly-upload" class="upload-btn">
                <i class="el-icon-upload2"></i>
                <span>上传字幕文件（支持srt文件）</span>
              </div>
            </el-upload>
            <span class="subttile-tips">如使用AI做课功能，建议关闭此功能</span>
          </div>
          <!-- 字幕已上传 -->
          <subttile-upload
          v-show="subttileRadio === 1 && subttileSetInfo.caption_id"
          ref="subttileUpload"
          @confirmOnUpload="confirmOnUpload" 
          @againUpload="againUpload" 
          :directlyUpload="false"
          />
        </div>
        <div class="course-info-box" v-if="showLive && isSuperAdmin">
          <span class="pseudo-class-title">直播相关信息</span>
          <el-form :model="form" :rules="rules" ref="form1" label-width="140px" inline>
            <el-row>
              <el-col :span="12">
                <el-form-item class="show_in_ql_home" label="是否被推荐到首页" prop="show_in_ql_home">
                  <el-radio-group v-model="form.show_in_ql_home" :disabled="approveStatus">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="0">否</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="直播观看次数">
                  {{courseInfo.live_pv || '-'}}
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="12">
                <el-form-item label="预计开始时间">
                  {{ courseInfo.live_start_time || '-' }}
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="预计结束时间">
                  {{ courseInfo.live_end_time || '-' }}
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
        <div class="course-info-box">
          <span class="pseudo-class-title">课程信息</span>
          <course-info
          :approveStatus="approveStatus"
          ref="courseInfo" 
          :courseInfo="courseInfo" 
          :directlyUpload="false"
          videoStatus="add"
          :pptName="videoInfo.file_name"
          @filesChanged="filesChanged"
          @safeChange="safeChange"
          @vaildAfterCode="vaildAfterCode"
          />
        </div>
      </div>
      <div class="chapters-content" v-show="hasPermission && (activeName === 'chapters')">
        <span class="pseudo-class-title">分段章节</span>
        <!-- 章节配置 -->
        <chapters-config 
          :approveStatus="approveStatus" 
          :activeName="activeName" 
          :estDur="courseInfo.est_dur" 
          :videoInfo="courseInfo.content_info" 
          :chaptersList="chaptersList"
          :courseInfo.sync="courseInfo"
          @refreshChapterList="refreshChapterList" 
          @exportAiData="exportAiData"
          ref="chaptersConfig"
        >
        </chapters-config>
      </div>
      <!-- ai文章 -->
      <aiArticle 
      v-if="activeName === 'aiArticle'" 
      :courseData.sync="courseInfo"
      @getCourse="getCourse"
      :approveStatus="approveStatus"
      >
      </aiArticle>
    </div>
    <!-- 底部按钮区域 -->  
    <div class="buttom" v-if="activeName === 'basic'" style="height: initial;line-height: initial;">
      <div class="directly-inner" v-if="approveStatus">
        <el-button :loading="approveLoading" @click="handleRefuseShow" :disabled="courseInfo.status !== '6'">拒绝</el-button>
        <el-button :loading="approveLoading" type="primary" @click="handleApprove(1)" :disabled="courseInfo.status !== '6'">审核通过</el-button>
      </div>
      <div class="inner" v-else style="width: 1450px;padding: 15px 0 15px 0">
        <convention-confirm v-model="isChooseConvention" v-if="[2].includes(courseInfo.info_sec_status) || [1, '1', 3, 4, '3', '4', null, undefined, ''].includes(courseInfo.status)" style="line-height: 22px; margin: 0 0 16px 0;" />
        <el-button @click="cancel">取消</el-button>
        <el-button @click="onSaveDraft" :disabled="btnDisabled">存草稿</el-button>
        <!-- 3为停用; 4为草稿; null, undefined, ''为新建 -->
        <template v-if="['3', '4', null, undefined, ''].includes(courseInfo.status)">
          <!-- <el-tooltip class="item" effect="dark" content="请先存草稿~" placement="right-start" v-if="courseInfo.status === '3'">
            <el-button class="btn-long" disabled type="primary" @click="onSubmit('1')">提交上架审核</el-button>
          </el-tooltip> -->
          <el-button class="btn-long" type="primary" @click="upApprove('1')" :disabled="!isChooseConvention">提交上架审核</el-button>
        </template>
        <!-- 1为在用 -->
        <el-button class="btn-long" v-else-if="[2].includes(courseInfo.info_sec_status) || (courseInfo.status === '1' && isFilesChange)" type="primary" @click="approveSafe('1')" :disabled="!isChooseConvention">再次提交审核</el-button>
        <el-button v-else type="primary" @click="approveSafe('2')" :disabled="!isChooseConvention || [0].includes(courseInfo.info_sec_status)">发布上架</el-button>
        <el-button type="primary" @click="previewEvent">预览</el-button>
        <span class="tips" v-show="draftTime">已保存草稿：{{ draftTime }}</span>
      </div>
    </div>
    <!-- 审核拒绝 -->
    <refuseDialog :refuseShow.sync="refuseShow" @refuseConfirm="handleApprove"></refuseDialog>
    <!-- 预览分享的弹窗 -->
    <ShareDialog ref="shareDialog" :isShow.sync="sharedialogShow" />
    <!-- 信息审核再次编辑异步变化弹窗 -->
    <informationSafetyDialog :isShow.sync="informationSafetyShow" @safeConfirm="onSubmit(informationSafetyValue)"/>
  </div>
</template>

<script>
import courseInfo from '../components/course-info.vue'
import chaptersConfig from '../components/chapters-config.vue'
import videoUpload from '../components/video-upload.vue'
import subttileUpload from '../components/subttile-upload.vue'
import aiArticle from '../components/aiArticle.vue'
import refuseDialog from '../components/refuseDialog.vue'
import ShareDialog from '@/views/components/shareDialog'
import informationSafetyDialog from '@/components/information-safety-dialog'
import { transforTime } from 'utils/tools'
import moment from 'moment'
import { mapState } from 'vuex'
import {
  getCourseInfo,
  getApproveDetailApi,
  addCourseDraft,
  addCourseSave,
  approveStatus,
  getNetCourseChapters,
  checkTarget,
  netSavePreviewApi
} from 'config/api.conf'
import conventionConfirm from '@/views/components/convention-confirm.vue'
export default {
  components: {
    courseInfo,
    videoUpload,
    subttileUpload,
    refuseDialog,
    chaptersConfig,
    ShareDialog,
    aiArticle,
    conventionConfirm,
    informationSafetyDialog
  },
  props: {
    approveStatus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      previewId: '', // 预览的id
      sharedialogShow: false, // 显示预览分享的弹窗
      tabList: [
        { label: '基础配置', value: 'basic' },
        { label: '分段章节', value: 'chapters' },
        { label: 'AI文章', value: 'aiArticle' }
      ],
      activeName: 'basic',
      courseInfo: {
        content_info: {
          file_name: '',
          content_id: '',
          file_size: ''
        },
        status: '',
        live_id: '', // 直播转网课的id 
        live_pv: 0, // 直播观看数
        live_start_time: null, // 预计开始时间
        live_end_time: null // 预计结束时间
      },
      videoInfo: {
        file_name: '',
        content_id: '',
        file_size: ''
      },
      chaptersList: [], // 分段章节配置数据
      subttileSetInfo: { // 字幕设置传递数据
        caption_name: '',
        caption_id: '',
        caption_size: '',
        fileList: []
      },
      subttileRadio: 0,
      process_duration: 0,
      transforTime,
      draftTime: '',
      videoId: '', // 课程视频Id
      approveLoading: false,
      refuseShow: false,
      hasPermission: false, // 是否有权限访问章节配置
      form: {
        show_in_ql_home: 1 // 是否被推荐到首页
      },
      rules: {
        show_in_ql_home: [{ required: true, message: '请选择是否被推荐到首页', trigger: 'change' }]
      },
      otherPropChanged: false, // 其他属性是否发生编辑改变 标题、封面、课程简介、标签
      safePropChange: false,
      isChooseConvention: false,
      informationSafetyShow: false,
      informationSafetyValue: ''
    }
  },
  computed: {
    ...mapState(['userLimitInfo']),
    btnDisabled() {
      // 在用，停用禁止点击草稿
      // return Boolean(this.courseInfo.status === '1' || this.courseInfo.status === '3')
      return Boolean(this.courseInfo.status === '1')
    },
    title() {
      // const title = this.btnDisabled ? '编辑在线课程' : '上传在线课程'
      const title = this.btnDisabled || this.courseInfo.status === '3' ? '编辑在线课程' : '上传在线课程'
      document.title = `${title}_Q-Learning`
      return title
    },
    showLive() { // 是否显示直播模块
      return this.courseInfo.live_id && this.courseInfo.live_id !== '0' && this.courseInfo.live_id !== 0
    },
    // 是否是超管
    isSuperAdmin() {
      let { supper_admin } = this.userLimitInfo
      return supper_admin
    },
    // 视频素材发生改变
    isVideoChange() {
      return this.videoId && this.videoInfo?.content_id !== this.videoId
    },
    // 标题、视频课件、封面、课程简介、标签 任何一个字段是否发生改变
    isFilesChange() {
      const id = this.$route.query.net_course_id
      return id && (this.otherPropChanged || this.isVideoChange)
    },
    isSafeChange() {
      const id = this.$route.query.net_course_id
      return id && (this.safePropChange || this.isVideoChange)
    }
  },
  created() {
    this.getCourse()
    const id = this.$route.query.net_course_id
    checkTarget().then((res) => {
      this.hasPermission = res || false
      if (id && this.hasPermission) {
        this.getChaptersInfo(id)
      }
    })
  },
  methods: {
    // 预览
    previewEvent() {
      const pageData = this.$refs.courseInfo.getParams()
      if (!pageData.course_name) {
        this.$message.warning('请填写课程名称')
        return
      }
      this.saveCourseInfo(pageData, 'preview')
    },
    previewRequest(data) {
      netSavePreviewApi(data).then(res => {
        this.previewId = res || ''
        this.sharedialogShow = true
        const url = process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/b5GaSG' : 'http://s.test.yunassess.com/s/hoo9Gg'
        let userInfo = JSON.parse(sessionStorage.getItem('login_user'))
        let params = {
          url: `${url}?course_id=${this.previewId}&scheme_type=netcourse&share_staff_id=${userInfo.staff_id}&share_staff_name=${userInfo.staff_name}&is_preview=true`,
          scene: `${this.previewId}_${userInfo.staff_id}_isPreview=true`,
          page: 'pages/networkCourse/video/videoDetail'
        }
        if (process.env.NODE_ENV !== 'production') {
          params.url = `https://test-portal-learn.woa.com/training/netcourse/grayPlay?course_id=${this.previewId}&is_preview=true`
        }
        this.$nextTick(() => {
          this.$refs.shareDialog.initCode(params)
        })
      }).catch(err => {
        console.log('err: ', err)
      })
    },
    exportAiData(data) {
      const { chaptersRecord } = data
      this.chaptersList = chaptersRecord
    },
    filesChanged(isChanged) {
      this.otherPropChanged = isChanged
    },
    safeChange(val) {
      this.safePropChange = val
    },
    changeTab(tab) {
      // 分段章节导入并修改
      this.activeName = tab
      if (this.activeName === 'chapters') {
        this.$refs.uploadVideo && this.$refs.uploadVideo.pause()
        // 查询分段章节配置
      } else {
        this.$refs.chaptersConfig && this.$refs.chaptersConfig.pause()
      }
    },
    // 编辑页面 获取课程详情
    getCourse() {
      const id = this.$route.query.net_course_id
      if (!id) return
      let api = this.approveStatus ? getApproveDetailApi : getCourseInfo
      let params = this.approveStatus ? { course_id: id, act_type: 2 } : id
      api(params).then((res) => {
        if (res?.net_course_id) {
          this.courseInfo = res
          this.videoId = res.content_info.content_id
          this.videoInfo = res.content_info
          if (this.courseInfo.status !== '6' && this.approveStatus) {
            this.$message.warning('该课程已审批')
          }
          // 字幕设置
          this.subttileSetInfo = {
            caption_name: res.captions[0]?.file_name,
            caption_id: res.captions[0]?.content_id,
            caption_size: res.captions[0]?.size
          }
          this.subttileRadio = res.show_caption
          this.draftTime = res.status === '4' && moment(res.updated_at).format('YYYY年MM月DD日 HH时mm分ss秒')
          this.$nextTick(() => {
            this.$refs.subttileUpload.initData(this.subttileSetInfo)
          })
        } else {
          // 视频正在转码
          this.$router.replace({
            name: 'videoError'
          })
        }
      })
    },
    // 刷新章节配置
    refreshChapterList() {
      let id = this.$route.query.net_course_id
      if (id) {
        this.getChaptersInfo(id)
      }
    },
    // 获取章节设置
    getChaptersInfo(id) {
      const params = { 
        course_id: id 
      }
      getNetCourseChapters(params).then(res => {
        // console.log(res, '获取章节配置返回的res-------')
        this.chaptersList = res || []
      })
    },
    // 存草稿
    onSaveDraft() {
      const pageData = this.$refs.courseInfo.getParams()
      if (!pageData.course_name) {
        this.$message.warning('请填写课程名称')
        return
      }
      this.saveCourseInfo(pageData, 'draft')
    },
    // 审批被拒绝后，点击"提交上架审核"按钮
    upApprove(value) {
      if (value === '1' && this.courseInfo.approve_status === 8) {
        this.$messageBox.confirm('是否确认提交审核?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.onSubmit(value)
        }).catch(() => {})
      } else {
        this.onSubmit(value)
      }
    },
    // 信息安全审核
    approveSafe(value) {
      this.informationSafetyValue = value
      if (this.isSafeChange) {
        this.informationSafetyShow = true
        return
      }
      this.onSubmit(value)
    },
    onSubmit(value) {
      // 视频--字幕必填
      if (!this.videoInfo?.content_id && !this.courseInfo?.content_info?.content_id) {
        this.$message.error('请上传视频素材')
        return
      }
      
      // 外挂字幕上传
      if (this.subttileRadio === 1 && !this.subttileSetInfo.caption_id) {
        this.$message.error('请上传外挂字幕文件')
        return
      }

      // 是否被推荐到首页--必填
      if (this.showLive && !this.form.show_in_ql_home) {
        this.$refs['form1'].validate()
        this.$message.error('请选择是否被推荐到首页')
        return
      }
      // 表单必填校验
      const pageData = this.$refs.courseInfo.vaildCourseInfo()
      console.log('pageData------------------: ', pageData)
      if (!pageData) {
        this.$message.error('请完善课程信息')
        return
      }
      this.$refs.courseInfo && (this.$refs.courseInfo.showSpecialDialogFn(pageData, value))
    },
    // 表单校验后的代码
    vaildAfterCode({ pageData, value }) {
      const type = value === '1' ? 'publish' : 'submit'
      this.saveCourseInfo(pageData, type)
    },
    // 保存课程信息
    saveCourseInfo(pageData, type) {
      const id = this.$route.query.net_course_id
      const captions = [{
        content_id: this.subttileSetInfo?.caption_id,
        file_name: this.subttileSetInfo.caption_name,
        size: this.subttileSetInfo.caption_size
      }]
      // 在用状态编辑防止 course_type 写死，在用状态编辑用后端返回
      const course_type = this.courseInfo?.course_type ? this.courseInfo.course_type : 'Video'
      const video_info = this.videoInfo && this.videoInfo.content_id ? this.videoInfo : this.courseInfo.content_info
      const params = {
        show_in_ql_home: this.form.show_in_ql_home,
        course_type,
        virtual_info: {},
        net_course_id: id,
        ...pageData,
        content_info: video_info, // 视频数据,
        show_caption: this.subttileRadio,
        captions: this.subttileSetInfo.caption_id ? captions : null
      }
      // params.status = type === 'publish' ? '6' : pageData.status

      // 按钮状态判断传参
      switch (type) {
        case 'publish':
          params.status = '6'
          break
        case 'draft':
          params.status = '4'
          break
        case 'preview':
          // params.status = '4'
          break
        default:
          params.status = '1'
          break
      }
      // 发布---课程视频更换后状态统一待审核
      if ((this.videoId && params.content_info?.content_id !== this.videoId) || type === 'publish') {
        params.status = '6'
      }
      
      // TODO:开发环境和测试环境调试时使用
      try {
        if (process.env.NODE_ENV !== 'production' && this.$route.query.Debugger) {
          return
        }
      } catch (error) {
        console.log('error: ', error)
      }

      // 预览特殊处理
      if (type === 'preview') {
        this.previewRequest(params)
        return
      }

      const commonAPI = type === 'draft' ? addCourseDraft : addCourseSave
      commonAPI(params).then((res) => {
        this.$message.success(type === 'draft' ? '已保存草稿' : type === 'publish' ? '发布成功' : '保存成功')
        if (type === 'draft') {
          this.$router.push({ 
            name: 'couserUpload',
            query: {
              net_course_id: res
            } 
          })
          this.draftTime = moment().format('YYYY年MM月DD日 HH时mm分ss秒')
          // 数据重新回显，并把状态改为草稿状态
          this.courseInfo = {
            ...params,
            status: '4',
            net_course_id: res
          }
        } else {
          setTimeout(() => {
            this.$router.push({ name: 'courseList' })
          }, 1000)
        }
      })
    },
    cancel() {
      this.$router.push({ name: 'courseList' })
    },
    // 视频上传回传数据
    onVideoChange (data) {
      console.log('视频上传数据', data)
      // 重新上传
      if (data?.content_id) {
        this.videoInfo = {
          file_name: data.fileName,
          content_id: data.content_id,
          file_size: data.fileSize
        }
        this.$refs.uploadVideo.changeFileData(this.videoInfo)
        return
      }
      // 视频删除
      this.videoInfo = {}
      this.$refs.uploadVideo.changeFileData(this.videoInfo)
    },
    // 视频上传改变course_type
    onVideoChangeType(type) {
      this.courseInfo.course_type = type
    },
    // 字幕上传
    onUpload(options) {
      this.$refs.subttileUpload.onUpload(options)
    },
    handleOpenSubttile(val) {
      if (val === 0) { // 字幕清空
        this.subttileSetInfo = {}
      }
    },
    // 字幕上传回传数据
    confirmOnUpload(data) {
      this.subttileSetInfo = JSON.parse(JSON.stringify(data))
    },
    // 重新上传
    againUpload() {
      document.getElementById('srt-directly-upload').click()
    },
    // 管理后台审核
    handleApprove(approve_result, review_failed_reason) {
      this.approveLoading = true
      const { net_course_id } = this.$route.query
      const params = {
        act_type: 2,
        course_id: net_course_id,
        approve_result,
        is_mobile: 1
      }
      if (approve_result === 2) {
        params.review_failed_reason = review_failed_reason
      }
      approveStatus(params).then((res) => {
        this.approveLoading = false
        const msg = approve_result === 2 ? '审核拒绝' : '审核通过'
        this.courseInfo.status = approve_result === 2 ? '8' : '7'
        this.$message.success(msg)
        try {
          window.opener.workReConnect()
        } catch (error) {
          console.error('error: ', error)
        }
        setTimeout(() => {
          window.close()
        }, 200)
      })
    },
    handleRefuseShow() {
      this.refuseShow = true
    }
  }
}
</script>
<style lang="less" scoped>
@import '~assets/css/directly-upload.less';
.show_in_ql_home {
  .el-form-item__content {
    min-width: 150px;
  }
}
.directly-upload-container {
  .btn-long {
    min-width: 120px;
  }
}
</style>
