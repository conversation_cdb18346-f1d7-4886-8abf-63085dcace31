const path = require('path')
const resolve = dir => path.join(__dirname, dir)
const svgPath = resolve('./src/assets')
const {
  title
} = require('./package')
const UglifyJsPlugin = require('uglifyjs-webpack-plugin')
const CompressionPlugin = require('compression-webpack-plugin')
// 压缩成 .gz 文件
const plugins = [
  new CompressionPlugin({
    filename: '[path][base].gz',
    algorithm: 'gzip',
    test: /\.js$|\.css$|\.html$/,
    threshold: 10240,
    minRatio: 0.8
  })
]
if (process.env.NODE_ENV !== 'development') {
  plugins.push(
    new UglifyJsPlugin({
      uglifyOptions: {
        // 删除注释
        output: {
          comments: false
        },
        // 删除console debugger 删除警告
        compress: {
          warnings: false,
          drop_console: true, // console
          drop_debugger: false,
          pure_funcs: ['console.log'] // 移除console
        }
      }
    })
  )
}
let publicPath = '/training/'

if (process.env.NODE_ENV === 'test') {
  publicPath = 'https://cdnyewutest.yunassess.com/knowledgeservice/'
} else if (process.env.NODE_ENV === 'production') {
  publicPath = 'https://qlportal.ihr.tencent-cloud.com/knowledgeservice/'
}
module.exports = {
  outputDir: 'build',
  publicPath,
  pages: {
    // h5页面
    mobile: {
      entry: ['./node_modules/@babel/polyfill', 'src/views/mobile/main.js'],
      template: 'src/views/mobile/mobile.html',
      filename: 'mobile.html',
      chunks: ['element-ui', 'vant', 'vue', 'vue-router', '@tencent', 'chunk-vendors', 'chunk-common', 'mobile']
    },
    // pc页面
    index: {
      entry: ['./node_modules/@babel/polyfill', 'src/main.js'],
      template: 'public/index.html',
      filename: 'index.html',
      chunks: ['element-ui', 'cos-js-sdk-v5', 'vue', 'vue-router', '@tencent', 'chunk-vendors', 'chunk-common', 'index']
    },
    tutor: {
      entry: ['./node_modules/@babel/polyfill', 'src/views/tutor/main.js'],
      template: 'src/views/tutor/tutor.html',
      filename: 'tutor.html',
      chunks: ['element-ui', 'cos-js-sdk-v5', 'vue', 'vue-router', '@tencent', 'chunk-vendors', 'chunk-common', 'tutor']
    }
  },
  configureWebpack: {
    name: title,
    optimization: {
      splitChunks: false
    }
  },
  // 添加对svg的自定义解析
  chainWebpack: (config) => {
    // 别名
    config.resolve.alias
      .set('@', resolve('./src'))
      .set('assets', resolve('./src/assets'))
      .set('components', resolve('./src/components'))
      .set('config', resolve('./src/config'))
      .set('mixins', resolve('./src/mixins'))
      .set('plugins', resolve('./src/plugins'))
      .set('utils', resolve('./src/utils'))
      .set('views', resolve('./src/views'))
      .set('router', resolve('./src/router'))
      .set('sdc-core', resolve('./node_modules/@tencent/sdc-core'))
      .set('sdc-vue', resolve('./node_modules/@tencent/sdc-vue'))
      .set('sdc-webui', resolve('./node_modules/@tencent/sdc-webui'))
      .set('sdc-theme', resolve('./node_modules/@tencent/sdc-theme'))
      .set('sdc-comment', resolve('./node_modules/@tencent/sdc-comment'))
      .set('sdc-img-cover', resolve('./node_modules/@tencent/sdc-img-cover'))
      .set('sdc-ui-rte', resolve('./node_modules/@tencent/sdc-ui-rte'))
      .set('sdc-moocjs', resolve('./node_modules/@tencent/sdc-moocjs'))
      .set('sdc-moocjs-integrator', resolve('./node_modules/@tencent/sdc-moocjs-integrator'))
      .set('sdc-comment-mob', resolve('./node_modules/@tencent/sdc-comment-mob'))
    // 拆包，目前拆的不够好，还需要搞搞
    config.optimization.splitChunks({
      maxInitialRequests: Infinity,
      cacheGroups: {
        'element-ui': {
          name: 'element-ui',
          priority: 10,
          test: /[\\/]node_modules[\\/]_?element-ui(.*)/,
          chunks: 'initial'
        },
        'vant': {
          name: 'vant',
          priority: 10,
          test: /[\\/]node_modules[\\/]_?vant(.*)/
        },
        'cos-js-sdk-v5': {
          name: 'cos-js-sdk-v5',
          test: /[\\/]node_modules[\\/]cos-js-sdk-v5[\\/]/,
          priority: -10
        },
        'vue': {
          name: 'chunk-vue',
          test: /[\\/]node_modules[\\/]vue[\\/]/,
          priority: -10
        },
        'vue-router': {
          name: 'vue-router',
          test: /[\\/]node_modules[\\/]vue-router[\\/]/,
          priority: -10
        },
        '@tencent': {
          name: 'chunk-tencent',
          test: /[\\/]node_modules[\\/]@tencent[\\/]/,
          priority: -10
        },
        vendors: {
          name: 'chunk-vendors',
          test: /[\\/]node_modules[\\/]/,
          priority: -20,
          chunks: 'initial'
        },
        common: {
          name: 'chunk-common',
          minChunks: 2,
          priority: -30,
          chunks: 'initial',
          reuseExistingChunk: true
        }
      }
    })
    config.module.rule('svg').exclude.add(svgPath)
    config.module.rule('icon').test(/\.svg$/)
      .include.add(svgPath).end() // 回退上下文
      .use('svg-sprite-loader').loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      }).end()
  },
  // 指定Runtime + Compile来编译组件
  runtimeCompiler: true,
  lintOnSave: true, // 这里禁止使用eslint-loader
  assetsDir: '.',
  pluginOptions: {
    'style-resources-loader': {
      preProcessor: 'less',
      patterns: [
        path.resolve(__dirname, './src/assets/css/vars.less')
      ]
    }
  },
  devServer: {
    // host: 'lo.learn.woa.com', // 设置访问本地端口
    // host: 'test.woa.com', // 设置访问本地端口
    port: 8088, // 设置访问本地端口
    hot: true, // 自动保存
    disableHostCheck: true,
    historyApiFallback: { // 本地多页面配置路由转到对应的html文件
      rewrites: [
        {
          from: /^\/training\/mobile(\/.*)?$/,
          to: '/training/mobile.html'
        },
        {
          from: /^\/training\/tutor(\/.*)?$/,
          to: '/training/tutor.html'
        },
        {
          from: /^\/training(\/.*)?$/,
          to: '/training/index.html'
        }
      ]
    },
    overlay: {
      warnings: true,
      errors: true
    },
    proxy: {
      // 连接测试环境服务器
      '/development': {
        target: 'http://test-portal-learn.woa.com',
        changOrigin: true,
        pathRewrite: {
          '^/development': ''
        }
      },
      '/api': {
        target: 'http://dev-ntsgw.woa.com',
        changOrigin: true
      }
    }
  }
}
