<template>
  <div class="courselist-dialog">
    <el-dialog
      :title="mode === 'edit' ? '内容管理' : '推送情况'"
      :visible.sync="visible"
      width="1200px"
      :close-on-click-modal="false"
      :before-close="closeDialog"
      z-index="1000"
    >
      <div class="log-body">
        <el-form ref="form" :model="form" inline label-width="100px">

          <div class="log-title">
            <span>栏目：</span>
            <span>{{ rec_name }}</span>
          </div>

          <div class="log-body-form">
            <el-form-item label="内容类型：">
              <el-select v-model="form.module_id" placeholder="请选择" style="width: 200px">
                <el-option
                  v-for="item in moduleInfo"
                  :key="item.module_id"
                  :label="item.module_name"
                  :value="item.module_id">
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="上传时间：">
              <el-date-picker
                style="width: 360px"
                v-model="form.upload_time"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="-"
                start-placeholder="开始日期时间"
                end-placeholder="结束日期时间">
                </el-date-picker>
            </el-form-item>

            <el-form-item label="内容标题：">
              <el-input
                style="width: 200px"
                type="text"
                placeholder="请输入内容标题"
                v-model="form.course_name"
              >
              </el-input>
            </el-form-item>

            <div class="search-btn">
              <el-form-item label="认证等级：">
                <el-select v-model="form.course_level" placeholder="请选择" style="width: 200px">
                  <el-option
                    v-for="item in courseLevelLabel"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
              <el-button @click="handleReset" class="s-reset" icon="el-icon-refresh" >重置</el-button>
              <el-button style="width: 80px;" type="primary" @click="onSearch(1)">搜索</el-button>
            </div>
          </div>

        </el-form>

        <div class="search-body">
          <div class="search-fun" v-if="mode === 'edit'">
            <el-button type="primary" @click="addContent">添加</el-button>
            <el-button @click="deleteList" class="delete-some">批量删除</el-button>
            <span >已选择<i>{{ selectedData.length }}</i>条内容</span>
          </div>
          <el-table
            ref="multipleTable"
            v-loading="tableLoading"
            :data="courseListData"
            :header-cell-style="{background:'#F5F5F5'}"
            class="content-table qlcontent-table"
            @select="qlsearchSelectionChange"
            @select-all="qlsearchSelectionAllChange"
            height="280px"
            >
            <el-table-column
              type="selection"
              width="55"
              key="1">
            </el-table-column>
            <el-table-column
              label="序号"
              type="index"
              width="88"
              key="2">
            </el-table-column>
            <el-table-column align="left"  label="内容标题" prop="course_name" show-overflow-tooltip width="230px" key="3"></el-table-column>
            <el-table-column align="left" label="认证等级" prop="course_level" width="120">
              <template v-slot="prop">
                <span>{{ prop.row.course_level ? showCourseLabel(prop.row.course_level) : '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column align="left"  label="内容类型" prop="recommend_module_id" width="140px" key="4">
              <template v-slot="prop">
                {{ getModuleName(prop.row.recommend_module_id * 1) }}
              </template>
            </el-table-column>
            <el-table-column align="left"  label="上传时间" prop="course_publish_time" width="150px" v-if="mode === 'check'" key="5">
              <template v-slot="prop">
                {{ prop.row.course_publish_time ? prop.row.course_publish_time.split(' ')[0] : '-' }}
              </template>
            </el-table-column>
            <el-table-column align="left"  label="链接" prop="course_url" show-overflow-tooltip width="350px" key="6">
              <template v-slot="prop">
                <a class="content-url" target="_blank" :href="prop.row.course_url">{{ prop.row.course_url }}</a>
              </template>
            </el-table-column>
            <el-table-column align="left" label="推送人数" prop="send_count" v-if="mode === 'check' " width="120px" key="7">
              <template v-slot="prop">
                <span style="color: #0052D9;">{{ prop.row.send_count ? prop.row.send_count : '0' }}人</span>
              </template>
            </el-table-column>

            <el-table-column align="left" label="操作" width="190" v-if="mode === 'edit'" key="8">
              <template slot-scope="{ row }">
                <el-button type="text" @click="handleDelete(row)" class="red-color">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div class="table-pagination">
            <el-pagination
              style="margin-left: auto"
              :hide-on-single-page="courseListData.length === 0"
              @current-change="onSearch"
              :current-page="searchData.page_no"
              :page-size="searchData.page_size"
              layout="total, prev, pager, next, jumper"
              :total="searchData.total"
            >
            </el-pagination>
          </div>

        </div>
        
      </div>
    </el-dialog>

    <addCourse ref="addConfigDialogRef" :visible.sync="addDialogVisible" @onSearch="onSearch" @added="addCourseList" :simpleTableData="simpleTableData"></addCourse>
  </div>
</template>

<script>
import addCourse from './addCourse.vue'
// import { getRecommendCouseList, addRecommendCourse, delRecommendCourse } from '@/config/mooc.api.conf'
import { getRecommendCouseList, addRecommendCourse, delRecommendCourse } from '@/config/mooc.api.conf'
import { actTypes } from '@/utils/map.js'
export default {
  components: { addCourse },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    moduleInfo: {
      type: Array,
      default: () => []
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.onSearch(1)
      }
    }
  },
  data() {
    return {
      form: {
        upload_time: '',
        module_id: '',
        course_name: '',
        course_level: ''
      },
      courseLevelLabel: [
        {
          label: '全部',
          value: ''
        },
        {
          label: '公司级',
          value: 1
        },
        {
          label: 'BG级',
          value: 2
        },
        {
          label: '部门级',
          value: 3
        },
        {
          label: '个人分享',
          value: 4
        }
      ],
      searchData: {
        startTime: '',
        endTime: '',
        page_no: 1,
        page_size: 9999,
        total: 0
      },
      recommend_id: '', // 推广栏目的id
      mode: 'check', // edit： 编辑  check： 查看
      rec_name: '',
      courseListData: [],
      simpleTableData: [], // 简单表格数据
      selectedData: [],
      tableLoading: false,
      addDialogVisible: false
    }
  },
  methods: {
    showCourseLabel(level) {
      return this.courseLevelLabel.find(item => item.value === level * 1)?.label || '-'
    },
    init(info) {
      const { mode = 'edit', id = '', rec_name = '' } = info
      this.mode = mode
      this.recommend_id = id
      this.rec_name = rec_name
    },
    sendAddedCourseList(list) {
      this.$refs.addConfigDialogRef.getAddedCourse(list)
    },
    getModuleName(id) {
      return actTypes.find(item => item.module_id === id)?.act_type_name || '未知'
    },
    handleReset() {
      this.form = {
        upload_time: '',
        module_id: '',
        course_name: '',
        course_level: ''
      }
      this.onSearch(1)
    },
    deleteList() {
      if (this.selectedData.length === 0) return this.$message.warning('请至少选择一项')
      this.delCourse(this.selectedData, true)
    },
    handleDelete(row) {
      this.delCourse(row, false)
    },
    delCourse(item, batch = false) {
      let names = ''
      let ids = ''
      const h = this.$createElement
      let html = [
        h('span', { style: 'color: #00000099;' }, '请确认是否删除： ')
      ]
      if (batch) {
        item.forEach((v, i) => {
          ids += v.id + ','
          if (i < 6) {
            names += `${v.course_name}、`
          }
        })
        names = names.substring(0, names.length - 1)
        if (item.length > 6) names += '...'
        ids = ids.substring(0, ids.length - 1)

        html.push(h('span', { style: 'color: #000000e6;' }, names))
        html.push(h('span', { style: 'color: #000000e6; display: inline; margin-left: 4px;' }, '共'))
        html.push(h('i', { style: 'color: #0052d9; font-weight: 500; font: icon; display: inline; margin: 0 4px;' }, item.length))
        html.push(h('span', { style: 'color: #000000e6;' }, '项课程？'))
      } else {
        ids = item.id
        names = item.course_name
        html.push(h('span', { style: 'color: #000000e6;' }, names))
      }
      let that = this

      this.$msgbox({
        title: '提示',
        closeOnClickModal: false,
        message: h('p', null, html),
        showCancelButton: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        customClass: 'messageBox_course_1121997',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '执行中...'
            delRecommendCourse(ids).then((res) => {
              this.selectedData = []
              instance.confirmButtonLoading = false
              that.$message.success('删除成功')
              that.onSearch()
              done()
            })
          } else {
            done()
          }
        }
      })
    },
    closeDialog() {
      this.courseListData = []
      this.selectedData = []
      this.form = {
        upload_time: '',
        module_id: '',
        course_name: '',
        course_level: ''
      }
      this.$emit('update:visible', false)
    },
    qlsearchSelectionChange(e) {
      this.selectedData = e
    },
    qlsearchSelectionAllChange(e) {
      this.selectedData = e
    },
    onSearch(num) {
      if (num) {
        this.searchData.page_no = num
      }
      this.selectedData = []
      const { course_name = '', module_id = '', course_level = '' } = this.form
      let start_time = this.form.upload_time ? this.form.upload_time[0] : ''
      let end_time = this.form.upload_time ? this.form.upload_time[1] : ''
      let data = {
        recommend_id: this.recommend_id,
        module_id,
        course_name,
        start_time,
        end_time,
        page_no: this.searchData.page_no,
        page_size: this.searchData.page_size,
        course_level
      }
      this.tableLoading = true
      getRecommendCouseList(data).then(res => {
        this.courseListData = res.records
        this.tableLoading = false
        this.searchData.total = res.total
        this.simpleTableData = this.courseListData.map(item => {
          return {
            recommend_item_id: item.recommend_item_id,
            recommend_module_id: item.recommend_module_id
          }
        })
        this.$nextTick(() => {
          this.$refs.multipleTable.doLayout()
        })
      }).catch(() => {
        this.tableLoading = false
      })
    },
    addContent() {
      this.addDialogVisible = true
    },
    addCourseList(list) {
      console.log(list, '777777')
      let params = {
        course_list: list.map(item => {
          return {
            recommend_item_id: item.item_id,
            recommend_module_id: item.module_id,
            course_name: item.content_name,
            recommend_act_type: item.act_type || '',
            course_url: item.content_url,
            course_publish_time: item.created_at,
            course_level: item.course_level ? item.course_level : ''
          }
        }),
        recommend_id: this.recommend_id
      }
      addRecommendCourse(params).then(res => {
        this.onSearch()
        this.$message.success('添加成功')
        this.$refs.addConfigDialogRef.banSelectCourse('normal')
      })
    }
  }
}
</script>
<style lang="less" scoped>
.table-pagination {
  display: flex;
  .e-page {
    margin-left: auto;
  }
}
.courselist-dialog {
  :deep(.el-dialog) {
    .el-dialog__header {
      border-bottom: 0;
    }
    .el-dialog__body {
      padding: 0;
      .el-dialog__footer {
        padding: 8px 0 24px;
      }
    }
  }
  :deep(.el-button) {
    &.red-color {
      span {
        color: red;
      }
    }
  }

  & /deep/ .el-table {
      border: 1px solid #eee;
      .el-table__empty-block {
        width: 100% !important;
      }
      .table-header-style th {
        padding: 6px 0;
        color: #00000099;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 14px;
      }
      .el-table__header {
        tr {
          th {
            &:nth-child(1) {
              .cell {
                padding-left: 14px;
              }
            }
          }
        }
      }
    }

  .log-body {
    padding: 10px 32px 20px 32px;
    .log-title {
      margin: 0 0 20px 0;
      span {
        &:first-child {
          color: #00000099;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
        &:nth-child(2) {
          color: #000000e6;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
      }
    }
    .log-body-form {
      background: #FAFAFA;
      padding: 16px 0;
      :deep(.el-form-item__label) {
        line-height: 40px;
      }
      .search-btn {
        display: flex;
        align-items: center;
        margin-right: 16px;
        .s-reset {
          margin-left: auto;
          color: #0052D9;
          border: 1px solid #0052D9;
          width: 80px;
          display: flex;
          justify-content: center;
        }
      }
    }
    .search-body {
      margin: 20px 0 0 0;

      .search-fun {
        margin: 0 0 20px 0;
        .delete-some {
          color: #0052D9;
          border: 1px solid #0052D9;
          background: #fff;
        }
        span {
          margin: 0 0 0 16px;
          color: #666666;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          line-height: 22px;
          i {
            margin: 0 8px;
             color: #0052d9;
            font-style: normal;
            font-size: 16px;
          }
        }
      }
    }
  }
}
</style>
