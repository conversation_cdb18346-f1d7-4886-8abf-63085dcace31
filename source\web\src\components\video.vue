<template>
  <div :class="['video-component', {'vertical-style': vertical}]" id="video-component">
    <link
      href="https://web.sdk.qcloud.com/player/tcplayer/release/v4.8.0/tcplayer.min.css"
      rel="stylesheet"
    />
    <video
      :id="videoDomId"
      ref="video"
      playsinline
      webkit-playsinline
      x5-playsinline
      crossorigin="anonymous"
    >
    </video>
    <Watermark
      ref="watermark"
      v-if="watermark.textContent"
      :targetId="watermark.targetId"
      :text="watermark.textContent"
      :canvasUserOptions="watermark.canvasUserOpt"
      :wmUserOptions="watermark.wmUserOpt"
      :isManualInit="false"
    />
  </div>
</template>
<script>
import axios from 'axios'
import { operatesignature, getContentInfo, getMobileContentInfo } from 'config/api.conf'
import Watermark from '@/components/watermark.vue'
import { throttle } from '@/utils/tools.js'
import poster from '@/assets/img/audioPlay.png'
export default {
  components: {
    Watermark
  },
  props: {
    app_id: {
      type: String,
      default: 'QLearningService'
    },
    content_id: {
      type: String,
      default: ''
    },
    // 视频播放url
    source_src: {
      type: String,
      default: ''
    },
    show_caption: {
      type: Number,
      default: 0
    },
    corp_name: {
      type: String,
      default: 'tencent'
    },
    // 设置播放时间
    playTime: {
      type: Number,
      default: 0
    },
    // 自动播放
    autoPlay: {
      type: Boolean,
      default: true
    },
    // 捕获当前帧
    needCapture: {
      type: Boolean,
      default: false
    },
    // 控制音量
    volumePanel: {
      type: Boolean,
      default: true
    },
    // 播放器控制栏
    controls: {
      type: Boolean,
      default: true
    },
    // 播放器进度条
    progressControl: {
      type: Boolean,
      default: true
    },
    playbackRateMenuButton: {
      type: Boolean,
      default: true
    },
    // 全屏按钮
    fullscreenToggle: {
      type: Boolean,
      default: true
    },
    // 是否横屏
    vertical: {
      type: Boolean,
      default: false
    },
    // 播放按钮是否显示
    playToggle: {
      type: Boolean,
      default: true
    },
    // 是否静音播放
    muted: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      poster: '', // 封面
      videoDomId: 'video-box-' + Math.random().toString().slice(-6),
      vedioPlayer: null,
      jsLoadCount: 0,
      videoData: {},
      captionTypeData: [
        { lang: '中文', captionData: [], subTrack: null },
        { lang: '英文', captionData: [], subTrack: null }
      ],
      curCaptionLang: '中文',
      watermark: {
        targetId: 'video-component', // 水印目标元素id
        textContent: '',
        canvasUserOpt: {
          width: 200,
          height: 160,
          fillStyle: 'rgba(153, 153, 153, 0.1)',
          font: '20px Microsoft Yahei',
          rotateDegree: 39,
          translate: {
            x: 0,
            y: 0
          }
        },
        wmUserOpt: {
          'z-index': 99999
        }
      },
      isVertical: false,
      isCapture: false, // 是否准备截图
      copyWatermark: null
    }
  },
  watch: {
    content_id(newV) {
      if (newV && this.jsLoadCount === 4) {
        this.getOperatesignature()
      }
    },
    jsLoadCount(newV) {
      if (newV === 4) {
        if (this.content_id) {
          this.getOperatesignature()
        } else if (this.source_src) {
          this.initVideo()
        }
      }
    },
    source_src(newV) {
      if (newV && this.jsLoadCount === 4 && !this.content_id) {
        this.initVideo()
      }
    },
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          this.watermark.textContent = val.staff_name
        }
      },
      immediate: true
    },
    playTime(newV) {
      this.vedioPlayer && this.vedioPlayer.currentTime(newV)
      if (this.needCapture) {
        this.isCapture = true
      }
    },
    'videoData.content_type': {
      immediate: true,
      handler(newV) {
        if (newV === 'audio') {
          const videoRef = this.$refs.video
          if (videoRef) {
            videoRef.poster = poster
          }
        }
      }
    }
  },
  created() {
    this.isVertical = this.$route.query.vertical || false
    const scrArr = [
      {
        src: '//web.sdk.qcloud.com/player/tcplayer/release/v4.8.0/libs/hls.min.1.1.6.js',
        loadF: true
      },
      {
        src: '//web.sdk.qcloud.com/player/tcplayer/release/v4.8.0/libs/flv.min.1.6.3.js',
        loadF: true
      },
      {
        src: '//web.sdk.qcloud.com/player/tcplayer/release/v4.6.0/libs/dash.all.min.4.4.1.js',
        loadF: true
      },
      {
        src: '//web.sdk.qcloud.com/player/tcplayer/release/v4.8.0/tcplayer.v4.8.0.min.js',
        loadF: true
      }
    ]
    this.handleLoadJs(scrArr)
  },
  methods: {
    handleLoadJs(scrs) {
      let item = scrs.shift()
      if (item) {
        let _this = this
        let scriptCon = document.createElement('script')
        scriptCon.type = 'text/javascript'
        scriptCon.src = item.src
        document.getElementsByTagName('head')[0].appendChild(scriptCon)
        if (item.loadF) {
          scriptCon.onload = scriptCon.onreadystatechange = () => {
            _this.jsLoadCount++
            _this.handleLoadJs(scrs)
          }
        }
      }
    },
    getOperatesignature() {
      const signatureParams = {
        app_id: this.app_id,
        content_id: this.content_id,
        corp_name: this.corp_name,
        operate: 'visit'
      }
      operatesignature(signatureParams).then((signature) => {
        if (signature) this.getVideoFileInfo(signature)
      })
    },
    getVideoFileInfo(signature) {
      const params = {
        signature,
        app_id: this.app_id,
        show_water_word: false
      }
      const api = window.location.pathname.includes('mobile') ? getMobileContentInfo : getContentInfo
      api(this.content_id, params).then((data) => {
        this.$emit('getVideoInfo', data)
        this.videoData = data
        if (data.is_successed === false) {
          this.$message.error(data.status)
        }
        if (data.file_info) {
          this.initVideo()
          this.readCaptionFile()
        }
      })
    },
    // 视频截屏
    captureImage(scale = 1) {
      let canvas = document.createElement('canvas')
      canvas.style.cssText = `position:fixed;left:-9999px`
      const video = this.vedioPlayer.el_
      canvas.width = video.clientWidth * scale
      canvas.height = video.clientHeight * scale
      const videoDom = this.vedioPlayer.el_.children[1]
      // console.log(canvas.width, canvas.height, videoDom, '调用截屏-----videoDom')

      document.body.appendChild(canvas)
      const ctx = canvas.getContext('2d')
      ctx.drawImage(videoDom, 0, 0, canvas.width, canvas.height)
      // let img = document.createElement('img')
      // img.src = canvas.toDataURL('image/png')
      // img.onload = function() {
      //   ctx.drawImage(videoDom, 0, 0, canvas.width, canvas.height)
      // }
      // outputDom.appendChild(img)
      // const image = canvas.toDataURL('image/png')
      let url = canvas.toDataURL()

      document.body.removeChild(canvas)
      canvas = null

      // let a = document.createElement('a')
      // // let event = new MouseEvent('click')
      // a.download = `${this.playTime}.png`
      // a.href = url
      // a.click()
      // a.dispatchEvent(event)

      // 将base64转换为文件对象
      let arr = url.split(',')
      let mime = arr[0].match(/:(.*?);/)[1] // 文件类型
      let bstr = atob(arr[1]) // 此处将base64解码
      let n = bstr.length
      let u8arr = new Uint8Array(n)
      while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
      }
      // 通过以下方式将以上变量生成文件对象，三个参数分别为文件内容、文件名、文件类型
      let file = new File([u8arr], `${this.playTime}.png`, { type: mime })
      // console.log(file, 'file--------')
      this.$emit('getCaptureFile', file)
    },
    initVideo() {
      let _this = this
      const params = {
        autoplay: this.autoPlay, // 自动播放
        fakeFullscreen: true,
        licenseUrl: 'https://test-portal-learn.woa.com',
        controls: this.controls,
        muted: this.muted,
        plugins: {
          ContextMenu: {
            levelSwitch: {
              open: true
            }
          }
        },
        controlBar: {
          progressControl: this.progressControl,
          fullscreenToggle: this.fullscreenToggle, // !this.vertical &&
          volumePanel: this.volumePanel,
          playbackRateMenuButton: this.playbackRateMenuButton, // 禁掉倍数
          playToggle: this.playToggle
        }
      }
      if (this.content_id) {
        params.fileID = _this.videoData.file_info.vod_id
        params.appID = _this.videoData.file_info.vod_app_id
        params.psign = _this.videoData.file_info.token
      } else if (this.source_src) {
        params.sources = [{ src: this.source_src, type: 'video/mp4' }]
      }
      /* eslint-disable*/
      this.vedioPlayer = TCPlayer(this.videoDomId, params)
      this.vedioPlayer.on('ready', function () {
        _this.$emit('loadVideoSucess', { videoDomId: _this.videoDomId})
        if (
          _this.show_caption &&
          _this.videoData?.file_attachments.length > 0
        ) {
          _this.videoData.file_attachments.forEach((item) => {
            if (
              item.attachement_type === 'Caption' &&
              (item.title === '中文' || item.title === '英文')
            ) {
              const curCaptionIdx = _this.captionTypeData.findIndex(
                (c) => c.lang === item.title
              )
              _this.captionTypeData[curCaptionIdx].subTrack =
                _this.vedioPlayer.addRemoteTextTrack(
                  {
                    src: item.url,
                    kind: 'subtitles',
                    srclang: item.title === '中文' ? 'zh-cn' : 'en',
                    label: item.title === '中文' ? '中文' : '英文',
                    default: item.title === '中文' ? 'true' : 'false'
                  },
                  true
                )
            }
          })
        }
      })
      this.vedioPlayer.on('loadedmetadata', function () {
        // 监听视频加载完成
        const param = {
          evt: 'loadedmetadata',
          duration: _this.vedioPlayer.duration()
        }
        _this.$emit('handleRecord', param)
      })
      this.vedioPlayer.on('playing', function (res) {
        // 监听视频真正播放（因缓冲暂停会恢复播放）
        const param = {
          evt: 'play',
          duration: _this.vedioPlayer.duration(),
          curTime: _this.vedioPlayer.currentTime()
        }
        _this.$emit('handleRecord', param)
        _this.$emit('dtClickVideo', 'play') // 埋点
        _this.$store.commit('net/setVideoPlay', true)
      })
      this.vedioPlayer.on('play', function (res) {
        // 监听播放
        _this.$emit('onPlay', true)
      })
      this.vedioPlayer.on('pause', function (res) {
        // 暂停关掉定时器, 播放完也会调用此方法
        const param = {
          evt: 'pause',
          duration: _this.vedioPlayer.duration(),
          curTime: _this.vedioPlayer.currentTime()
        }
        _this.$emit('handleRecord', param)
        _this.$emit('dtClickVideo', 'pause') // 埋点
        _this.$emit('onPause')
        _this.$store.commit('net/setVideoPlay', false)
      })
      // 播放停止，下一帧内容不可用时触发
      this.vedioPlayer.on('waiting', function (res) {
        const param = {
          evt: 'pause',
          duration: _this.vedioPlayer.duration(),
          curTime: _this.vedioPlayer.currentTime()
        }
        _this.$emit('handleRecord', param)
      })
      this.vedioPlayer.on('ended', function (res) {
        // 播放完关掉定时器
        const param = {
          evt: 'ended',
          duration: _this.vedioPlayer.duration(),
          curTime: _this.vedioPlayer.currentTime()
        }
        _this.$emit('onEnded', true)
        _this.$emit('handleRecord', param)
      })
      this.vedioPlayer.on('fullscreenchange', function () {
        // 是横屏且是全屏时
        // if (_this.vertical && _this.vedioPlayer.isFullscreen()) {
          // let fullscreenDom = document.querySelector('.vjs-fullscreen-control.vjs-control.vjs-button .vjs-icon-placeholder:before')
          // fullscreenDom.style.display = 'none'
        // }
        _this.$emit('dtClickVideo', 'fullscreen') // 埋点
        _this.$emit('fullscreenchange', _this.vedioPlayer.isFullscreen())
      })
      // 监听播放位置有变更时触发
      this.vedioPlayer.on('timeupdate', throttle(function(){
        if (_this.needCapture && _this.isCapture) {
          _this.isCapture = false
          // 截屏
          _this.captureImage()
        }
        _this.$emit('getCurrentTime', _this.vedioPlayer.currentTime())
      }, 500))
      // 播放速率获取
      this.vedioPlayer.on('ratechange', function (res) {
        const rate = _this.vedioPlayer.playbackRate()
        _this.$emit('dtClickVideo', 'rate') // 埋点
        _this.$store.commit('net/setVidePlayRate', rate)
      })
      // 播放音量获取
      this.vedioPlayer.on("volumechange", function () {
        const volume = _this.vedioPlayer.muted() ? 0 : _this.vedioPlayer.volume()
        _this.$store.commit('net/setVideoVolume', volume)
        _this.$emit('dtClickVideo', 'volume') // 埋点
      })
      this.$refs.watermark.init()
      if (this.vedioPlayer) {
        this.$nextTick(() => {
          let targetDom = document.getElementById(
            _this.watermark.targetId + '_watermark_xx512'
          )
          let copyDomeId = _this.watermark.targetId + '_watermark_xx513'
          if (_this.copyWatermark == null && targetDom) {
            _this.copyWatermark = targetDom.cloneNode(true)
            _this.copyWatermark.id = copyDomeId
            _this.copyWatermark.style.position = 'absolute'
            _this.copyWatermark.style.width = '100%'
            _this.copyWatermark.style.height = '100%'
            _this.copyWatermark.style.top = '0'
            let videoBox = document.querySelector(`#${this.videoDomId} video`)
            videoBox.parentNode.insertBefore(_this.copyWatermark, videoBox)
          }
          if (
            document
              .querySelector(`#${this.videoDomId} video`)
              .getAttribute('id')
              .indexOf('html5_api') === -1
          ) {
            document.getElementById(copyDomeId).style.display = 'none'
          }
        })
      }
    },
    readCaptionFile() {
      if (!this.videoData.file_attachments) return
      this.videoData.file_attachments.forEach((item) => {
        if (
          item.attachement_type === 'Caption' &&
          (item.title === '中文' || item.title === '英文')
        ) {
          axios({
            url: item.url,
            method: 'GET'
          }).then((response) => {
            if (response.status === 200 && response.data) {
              let data = response.data?.split('\n\n')
              const captionArr = data?.map((str) => {
                let obj = {}
                const captionItemArr = str.split(/[(\r\n)\r\n]+/).filter((v) => v)
                captionItemArr.map((e, idx) => {
                  if (idx === 1) {
                    const time = JSON.parse(JSON.stringify(e))
                    obj.startTime = e.split('-->')[0]
                    obj.endTime = e.split('-->')[1]
                    const endTimeCopy = JSON.parse(
                      JSON.stringify(time.split('-->')[1])
                    )
                    const startTimeCopy = JSON.parse(
                      JSON.stringify(time.split('-->')[0])
                    )
                    obj.IntStartTime = startTimeCopy
                      ? this.timeToSec(startTimeCopy)
                      : 0
                    obj.IntEndTime = endTimeCopy
                      ? this.timeToSec(endTimeCopy)
                      : 0
                  }
                  if (idx === 2) obj.caption = e
                })
                return obj
              })
              const curCaptionIdx = this.captionTypeData.findIndex(
                (c) => c.lang === item.title
              )
              this.captionTypeData[curCaptionIdx].captionData = captionArr
              if (this.curCaptionLang === item.title)
                this.$emit('getCurCaption', captionArr) // 字幕语言切换后返回对应的字幕后续扩展，目前只支持一种
            }
          })
        }
      })
    },
    timeToSec(timeStr) {
      let time = timeStr.split(',')[0]
      let s = ''
      const hour = time.split(':')[0]
      const min = time.split(':')[1]
      const sec = time.split(':')[2]
      let misecond = timeStr.split(',')[1].substr(0, 2)
      misecond = Number(parseInt(misecond) / 100)
      s = Number(hour * 3600) + Number(min * 60) + Number(sec)
      return (s * 100 + misecond * 100) / 100
    },
  },
}
</script>
<style lang="less" scoped>
.video-component {
  position: relative;
  border-radius: 3px;
  border: 1px solid #ececec;
}

.vertical-style {
  width: 100vh !important;
  height: 100vw !important;
  transform-origin: 0 0;
  transform: translateX(100vw) rotate(90deg);
  z-index: 999;
}
/*
  .vjs-icon-placeholder {
    display: none;
  }
*/

:deep(.tcplayer) {
  width: 100% !important;
  height: 100% !important;
  video {
    object-fit: cover;
  }
}
:deep(.vjs-poster){
  // background: url("~@/assets/img/audioPlay.png") no-repeat;
  background-size: cover;
}
</style>
