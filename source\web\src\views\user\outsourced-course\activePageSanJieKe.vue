<template>
  <div class="active-page">
    <div class="banner-card">
      <div class="banner-content" @click="goTo">
        <!-- <template>
          <img class="cloud-icon" src="@/assets/outsourcedCourse/cloud.png" />
          <img class="card-icon" src="@/assets/outsourcedCourse/card.png" />
        </template> -->
        <img class="before_bg" src="@/assets/outsourcedCourse/before_bg_sanjieke.png" alt="">
        <div class="title">
          <img class="img" src="@/assets/outsourcedCourse/titleSanjieke.png">
        </div>
        <div class="toolbar">
          <div class="left">
            <div class="info">我的学霸卡：<span class="wide">{{ cardInfo.balance || 0 }}</span> 张</div>
            <!-- <div class="link" @click.stop="toActiveDetail">点击查看此次赠课活动详情</div> -->
          </div>
          <div class="right">
            <el-button class="common" type="primary" plain @click.stop="toMooc">已兑课程</el-button>
            <el-button class="common" type="primary" plain @click.stop="openRewardDetails">学霸卡领用详情</el-button>
            <el-button class="common" type="primary" plain @click.stop="toRulesDetail">活动规则</el-button>
          </div>
        </div>
      </div>
    </div>

    <div class="contain-main">
      <!-- <div class="coupon-bar">
        <div class="coupon-card">
          <div class="card coupon-init">
            <div class="title">学霸卡</div>
            <div class="subtitle">
              <span>可用于免费兑换外部付费好课</span>
            </div>
            <div class="lower-part">
              <p>可领 <span class="important">{{ cardInfo.can_get_geek_num || 0 }}</span> 张，库存 <span class="important">{{ cardInfo.quantity || 0 }}</span> 张</p>
              <p>领取后7个自然日内有效</p>
              <el-button v-if="cardInfo.can_get_geek_num > 0" class="receive-init" type="primary" @click="throttleFn(1, true)" plain>点击领取</el-button>
              <el-button v-else class="receive-init disabled" type="primary" @click="throttleFn(1, false)" plain>点击领取</el-button>
            </div>
          </div>
          <div class="introduce" v-html="configInfo?.top_info?.desc || ''"></div>
        </div>
        <div class="coupon-card">
          <div class="card coupon-qualify">
            <div class="title">学霸卡 · 学习达标奖励</div>
            <div class="subtitle">
              <span>可用于免费兑换外部付费好课</span>
            </div>
            <div class="lower-part">
              <p>可领 <span class="important">{{ cardInfo.can_get_reward_num || 0 }}</span> 张，库存 <span class="important">{{ cardInfo.quantity || 0 }}</span> 张</p>
              <p>领取后7个自然日内有效</p>
              <el-button v-if="cardInfo.can_get_reward_num > 0 && cardInfo.can_get_geek" class="receive-qualify" type="primary" @click="throttleFn(2, true)" plain>点击领取</el-button>
              <el-button v-else class="receive-qualify disabled" type="primary" @click="throttleFn(2, false)" plain>点击领取</el-button>
            </div>
          </div>
          <div class="introduce" v-html="configInfo?.my_ticket_info?.desc || ''"></div>
        </div>
      </div> -->

      <div class="coupon-bar-new">
        <span class="title">学霸卡</span>
        <div class="card-list-content">
          <div class="card-init" :class="getClass1()" :style="bgiStyle1()">
            <div class="sub-title" :class="{'init-disable-class1': isDisableStyle1()}">学霸卡<img style="width:42px;height:16px;" :src="require('@/assets/outsourcedCourse/sanjieke-font.png')" alt=""></div>
            <div class="title-1" :class="{'init-disable-class2': isDisableStyle1()}">可用于兑换三节课课程</div>
            <span class="title-warm">领取后7个自然日内有效</span>
            <div class="botton-box">
              <el-button v-if="cardInfo.can_get_geek_num > 0" class="button-customer" type="primary" @click="throttleFn(1, true)" plain>点击领取</el-button>
              <el-button v-else-if="getNums > 0 && cardsOrder[0] && [0, 'consume', 'manage_deduct', undefined].includes(cardsOrder[0].verb_id)" class="button-customer disabled" type="primary" @click="throttleFn(1, false)" plain>已领取</el-button>
               <!-- 点击领取、已领取、抢光了 -->
               <el-button v-else class="button-customer disabled-gray" type="primary" @click="throttleFn(1, false)" plain>{{ showText(0) }}</el-button>
            </div>
          </div>
          <div class="card-stand" :class="getClass(item)" :style="bgiStyle(item)" v-for="item in 4" :key="item">
            <div class="sub-title" :class="{'stand-disable-class1': isDisableStyle(item)}">学霸卡<img style="width:42px;height:16px;" :src="require('@/assets/outsourcedCourse/sanjieke-font.png')" alt=""></div>
            <div class="title-1" :class="{'stand-disable-class2': isDisableStyle(item)}">可用于兑换三节课课程</div>
            <span class="title-warm">领取后7个自然日内有效</span>
            <div class="botton-box">
              <el-button v-if="getNums >= 1 && item >= getNums && cardInfo.can_get_reward_num && cardInfo.can_get_geek" class="button-customer" type="primary" @click="throttleFn(2, true)" plain>点击领取</el-button>
              <!-- <el-button v-if="getNums >= 1 && item >= getNums" class="button-customer" type="primary" @click="throttleFn(2, true)" plain>点击领取</el-button> -->
              <el-button v-else-if="getNums >= 1 && item < getNums && cardsOrder[item] && [0, 'consume', 'manage_deduct', undefined].includes(cardsOrder[item].verb_id)" class="button-customer disabled" type="primary" @click="throttleFn(2, false)" plain>已领取</el-button>
              <!-- 点击领取、已领取、抢光了 -->
              <el-button v-else class="button-customer disabled-gray" type="primary" @click="throttleFn(2, false)" plain>{{ showText(item) }}</el-button>
            </div>
          </div>
        </div>
        <div class="text-box">
          <div><span class="text-title">领取说明：</span>每次可领取<span class="text-attention">1张</span>卡，完成解锁条件后可额外领取<span class="text-attention">1张</span>，活动期间每人最多领取<span class="text-attention">5张</span>卡，先到先得，领完即止；
          </div>
          <div>
            <span class="text-title">解锁条件：</span>每张学霸卡可兑换<span class="text-attention">1门</span>课，学习该课程中任意<span class="text-attention">5个任务</span>，每个任务学习时长不低于<span class="text-attention">3分钟</span>，即可解锁下一张学霸卡。
          </div>
          <!-- <div class="warm">
            无需使用学霸卡兑换的【三节课】课程不在本次活动范围内
          </div> -->
        </div>
      </div>

      <div class="course-list" 
        v-if="(configInfo.course_recommend_list || []).length"
        :dt-areaid="dtListBodyList('area')"
        :dt-remark="dtListBodyList('remark')"
      >
        <div class="title-col">
          <span class="title">立即兑换课程</span>
          <span class="link" @click="toActiveDetail" 
            :dt-areaid="dtMoerCourses('area', '1')"
            :dt-eid="dtMoerCourses('eid', '1')"
            :dt-remark="dtMoerCourses('remark', '1')"
          >>>>  查看更多可兑换好课</span>
        </div>
        <div class="list">
          <div class="course-item" 
            v-for="(item, index) in (configInfo.course_recommend_list || [])" 
            :key="item.course_id" 
            @click="toCourseDetail(item.course_url)"
            :dt-areaid="dtListBody(item, 'area', index)"
            :dt-eid="dtListBody(item , 'eid', index)"
            :dt-remark="dtListBody(item , 'remark', index)"
          >
            <el-image class="cover" :src="getCourseCoverUrl(item.course_pic_id)">
              <div class="image-slot" slot="placeholder">
                <i class="el-icon-loading"></i>
              </div>
              <div class="error-cover" slot="error">
                <img class="image-box" :src="require('@/assets/outsourcedCourse/movie.png')" alt="" />
              </div>
            </el-image>
            <div class="time" v-if="item.course_length">{{item.course_length}}分钟</div>
            <div class="title">
             <img :src="require('@/assets/outsourcedCourse/title-tips.png')" alt=""> {{ item.course_name }}
            </div>
          </div>
        </div>
        <div class="link">
          <span @click="toActiveDetail"
            :dt-areaid="dtMoerCourses('area', '2')"
            :dt-eid="dtMoerCourses('eid', '2')"
            :dt-remark="dtMoerCourses('remark', '2')"> &gt;&gt;&gt;  查看更多可兑换好课 &lt;&lt;&lt; </span>
        </div>
      </div>
    </div>

    <el-dialog  custom-class="coupon-dialog" :visible.sync="dialogVisible" width="711px" :show-close="false">
      <div class="usage-details">
        <div class="head">
          <span>学霸卡领取和使用详情</span>
          <img class="close" src="@/assets/mooc-img/close.png" @click="dialogVisible = false" />
        </div>
        <div class="body">
          <el-table
            :data="tableData"
            style="width: 100%; min-height: 400px;"
            header-row-class-name="table-header-style"
            row-class-name="table-row-style"
            class="table-content"
          > 
            <el-table-column prop="index" label="序号" width="60">
              <template slot-scope="scope">
                <span>{{ (pagination.current-1)*pagination.size + scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="receive_time" label="领取时间" width="160"></el-table-column>
            <el-table-column prop="verb_id" label="卡券状态" width="90">
              <template slot-scope="scope">
                <span :class="['status-label', getCardInfo(scope.row.verb_id).class]">{{ getCardInfo(scope.row.verb_id).name }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="说明" show-overflow-tooltip>
              <template slot-scope="scope">
                {{ resolveDescData(scope.row) }}
              </template>
            </el-table-column>
            <el-table-column label="" width="100">
              <template slot-scope="scope">
                <div class="operat-btn-box" v-if="!scope.row.verb_id">
                  <el-link @click="toManagePage()" type="primary" :underline="false">去兑换课程</el-link>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pagination">
            <div :class="['box btn prev', { 'disable': pagination.current <= 1 }]" @click="toPrev">
              <img class="img-16 icon-grey" src="@/assets/mooc-img/bottom-arrow.png" />
              <img class="img-16 icon-active" src="@/assets/mooc-img/<EMAIL>" />上一页
            </div>
            <div class="box num">
              <span class="current">{{ pagination.current }}</span>
              <span class="">/</span>
              <span class="total">{{ pagination.pageNum }}</span>
            </div>
            <div :class="['box btn next', { 'disable': pagination.current >= pagination.pageNum} ]" @click="toNext">
              下一页
              <img class="img-16 icon-grey" src="@/assets/mooc-img/bottom-arrow.png" />
              <img class="img-16 icon-active" src="@/assets/mooc-img/<EMAIL>" />
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import env from 'config/env.conf.js'
import { 
  getHomePageConfig,
  getHomePageInfo,
  claimPoints,
  queryGeekRecord
} from '@/config/mooc.api.conf.js'
import { throttle } from '@/utils/tools.js'
const HTTPS_REG = /^https:\/\//
export default {
  components: {
  },
  data() {
    return {
      userInfo: {},
      bannerImage: '',
      configInfo: {}, // 详情配置信息
      cardInfo: {
        can_get_geek: false,
        balance: 0,
        quantity: 0,
        can_get_geek_num: 0,
        can_get_reward_num: 0
      }, // 是否可领券、库存信息
      dialogVisible: false,
      tableData: [],
      // 分页
      pagination: {
        pageNum: 1,
        current: 1,
        size: 5,
        total: 0
      }
    }
  },
  watch: {
  },
  computed: {
    getCardInfo() {
      return (res) => {
        let statusMap = {
          0: '待兑换',
          consume: '已兑换',
          manage_deduct: '已兑换',
          deduct_expire: '已失效'
          // manage_deduct: '已使用',
        }
        let statusStyleMap = {
          0: 'status-waite-use',
          consume: 'status-oready-used',
          manage_deduct: 'status-oready-used',
          deduct_expire: 'status-no-effict'
        }
        return {
          name: statusMap[res || 0],
          class: statusStyleMap[res || 0]
        }
      }
    },
    // 已经领取的数量
    getNums() {
      return this.tableData.length || 0
    },
    // 领取顺序
    cardsOrder() {
      return JSON.parse(JSON.stringify(this.tableData)).reverse()
    },
    dtListBodyList() {
      return (type) => {
        let { audience_id, audience_name } = this.configInfo
        if (type === 'area') {
          return `area_${audience_id}`
        } else if (type === 'eid') {
          return `element_${audience_id}`
        } else if (type === 'remark') {
          return JSON.stringify({ 
            page: '三节课活动首页',
            page_type: '三节课活动首页',
            container: audience_name,
            container_id: '',
            content_name: '课程推荐列表',
            terminal: 'PC'
          })
        }
      }
    },
    dtMoerCourses() {
      return (type, index) => {
        let { audience_id, audience_name } = this.configInfo
        if (type === 'area') {
          return `area_${audience_id}_more_${index}`
        } else if (type === 'eid') {
          return `element_${audience_id}_more_${index}`
        } else if (type === 'remark') {
          return JSON.stringify({ 
            page: '三节课活动首页',
            page_type: '三节课活动首页',
            container: audience_name,
            container_id: '',
            click_type: 'button',
            content_name: '查看更多可兑换好课' + index,
            terminal: 'PC'
          })
        }
      }    
    },
    // 埋点
    dtListBody() {
      return (item, type, index) => {
        let dt_id = `${item.course_from}_${item.course_id}`
        if (type === 'area') {
          return `area_${dt_id}`
        } else if (type === 'eid') {
          return `element_${dt_id}`
        } else if (type === 'remark') {
          return JSON.stringify({ 
            page: '三节课活动首页',
            page_type: '三节课活动首页',
            container: index <= 7 ? `${this.configInfo.audience_name}_8` : `${this.configInfo.audience_name}_16`,
            container_id: '',
            click_type: 'data',
            content_type: '培养项目',
            act_type: '11',
            content_id: item.course_id,
            content_name: item.course_name,
            terminal: 'PC'
          })
        }
      }
    }
  },
  created() {
    this.getHomePageData()
  },
  mounted() {
    setTimeout(() => {
      this.getHomePageInfoFn()
      this.getRewardDetails()
    }, 500)
  },
  methods: {
    // 置灰的背景下按钮应该显示的文字
    showText(item) {
      if (this.cardsOrder[item] && this.cardsOrder[item].verb_id === 'deduct_expire') {
        return '已领取'
      } else if (!this.cardInfo.quantity) {
        return '抢光了'
      } else {
        return '点击领取'
      }
    },
    // 达标卡 右上角图片
    getClass(item) {
      if (this.getNums >= 1 && item >= this.getNums && this.cardInfo.can_get_reward_num && this.cardInfo.can_get_geek) {
        return `` // 待领取
      } else if (this.getNums >= 1 && item < this.getNums && this.cardsOrder[item] && [0, 'consume', 'manage_deduct', undefined].includes(this.cardsOrder[item].verb_id)) {
        if ([0, undefined].includes(this.cardsOrder[item].verb_id)) {
          return `card-stand-1` // 待兑换
        } else {
          return `card-stand-2` // 已兑换
        }
      } else {
        if (this.cardsOrder[item] && this.cardsOrder[item].verb_id === 'deduct_expire') {
          return `card-stand-4` // 已失效
        } else {
          return `card-stand-3` // 未解锁
        }
      }
    },
    // 初始卡 右上角图片
    getClass1() {
      if (this.cardInfo.can_get_geek_num > 0) {
        return `` // 待领取
      } else if (this.getNums > 0 && this.cardsOrder[0] && [0, 'consume', 'manage_deduct', undefined].includes(this.cardsOrder[0].verb_id)) {
        if ([0, undefined].includes(this.cardsOrder[0].verb_id)) {
          return `card-stand-1` // 待兑换
        } else {
          return `card-stand-2` // 已兑换
        }
      } else {
        return `card-stand-4` // 已失效
      }
    },
    // 达标卡 背景图
    bgiStyle(item) {
      if (this.getNums >= 1 && item >= this.getNums && this.cardInfo.can_get_reward_num && this.cardInfo.can_get_geek) {
        return `background-image: url(${require('@/assets/outsourcedCourse/card-stand-new-sanjieke.png')});`
      } else if (this.getNums >= 1 && item < this.getNums && this.cardsOrder[item] && [0, 'consume', 'manage_deduct', undefined].includes(this.cardsOrder[item].verb_id)) {
        return `background-image: url(${require('@/assets/outsourcedCourse/card-stand-new-sanjieke.png')});`
      } else {
        return `background-image: url(${require('@/assets/outsourcedCourse/card-stand-no-sanjieke.png')});`
      }
    },
    // 初始卡 背景图
    bgiStyle1() {
      if (this.cardInfo.can_get_geek_num > 0) {
        return `background-image: url(${require('@/assets/outsourcedCourse/card-init-new-sanjieke.png')});`
      } else if (this.getNums > 0 && this.cardsOrder[0] && [0, 'consume', 'manage_deduct', undefined].includes(this.cardsOrder[0].verb_id)) {
        return `background-image: url(${require('@/assets/outsourcedCourse/card-init-new-sanjieke.png')});`
      } else {
        return `background-image: url(${require('@/assets/outsourcedCourse/card-init-no-effict-sanjieke.png')});`
      }
    },
    // 达标卡 判断是否置灰 “学霸卡”和“可用于兑换三节课课程”文字置灰
    isDisableStyle(item) {
      if (this.getNums >= 1 && item >= this.getNums && this.cardInfo.can_get_reward_num && this.cardInfo.can_get_geek) {
        return false
      } else if (this.getNums >= 1 && item < this.getNums && this.cardsOrder[item] && [0, 'consume', 'manage_deduct', undefined].includes(this.cardsOrder[item].verb_id)) {
        return false
      } else {
        return true
      }
    },
    // 初始卡 判断是否置灰 “学霸卡”和“可用于兑换三节课课程”文字置灰
    isDisableStyle1() {
      if (this.cardInfo.can_get_geek_num > 0) {
        return false
      } else if (this.getNums > 0 && this.cardsOrder[0] && [0, 'consume', 'manage_deduct', undefined].includes(this.cardsOrder[0].verb_id)) {
        return false
      } else {
        return true
      }
    },
    init() {
      this.getHomePageData()
      this.getHomePageInfoFn()
      this.getRewardDetails()
    },
    // 获取活动详情数据
    async getHomePageData() {
      try {
        let res = await getHomePageConfig({ homePageKey: 'sanjiekeHomePageConfig' })
        let data = res ? JSON.parse(res) : {}
        if (data.top_info && data.top_info.desc) {
          data.top_info.desc = data.top_info.desc.replace('7个自然日', `<span style='color:#ed7b2f;font-weight:500;'>7个自然日</span>`)
        }
        if (data.my_ticket_info && data.my_ticket_info.desc) {
          const updataFrontStyleArray = ['任意5个学习任务', '7个自然日', '最多领取4张', '任意5个章节']
          updataFrontStyleArray.forEach(item => {
            data.my_ticket_info.desc = data.my_ticket_info.desc.replace(item, `<span style='color:#ed7b2f;font-weight:500;'>${item}</span>`)
          })
        }
        this.configInfo = data
        console.log(this.configInfo, 'configInfoconfigInfoconfigInfoconfigInfoconfigInfo')
        this.getImgUrl()
      } catch (error) {
        console.log('获取活动详情数据-error: ', error)
      }
    },
    // 积分授予-查询是否可以领取sanjieke
    async getHomePageInfoFn() {
      try {
        this.userInfo = JSON.parse(sessionStorage.getItem('login_user'))
        let res = await getHomePageInfo({ staff_id: this.userInfo.staff_id, acct_type_code: 'sanjieke' })
        this.cardInfo = res
      } catch (error) {
        if (error.code === 0 && error.data) {
          this.cardInfo = error.data
        }
      }
    },
    // 点击领取卡券 节流方法
    throttleFn: throttle(function (type, status) {
      this.getCoupon(type, status)
    }, 500),
    // 点击领取卡券
    getCoupon(type, status) {
      switch (type) {
        case 1:
          // 初始卡
          if (status) {
            this.claimPointsFn('sanjieke')
          } else {
            // hasGetGeek-是否已经领取，canGetGeekNum-可领取的兑换券的数量，formalStaff-是否正式员工，totalBalance-已领取数量，limit-最大限制数量
            const { has_get_geek: hasGetGeek, can_get_geek_num: canGetGeekNum, formal_staff: formalStaff, total_balance: totalBalance, limit } = this.cardInfo
            if (!formalStaff) {
              this.$message.warning('非集团本部正式员工不可领取')
            } else if (totalBalance >= limit) {
              this.$message.warning('已达到领取数量上限')
            } else if (!hasGetGeek && canGetGeekNum <= 0) {
              this.$message.warning('本轮学霸卡已发放完毕，暂时无法领取，敬请期待后续轮次的发放活动')
            } else if (hasGetGeek) {
              this.$message.warning('你本轮次已领取过学霸卡，每人限领1次，敬请期待后续轮次的发放活动')
            }
          }
          break
        case 2:
          // 达标卡
          if (status) {
            this.claimPointsFn('reward')
          } else {
            // hasGetGeekReward-是否已经领取，canGetRewardNum-可领取的兑换券的数量，canGetGeek-是否可以领取，formalStaff-是否正式员工，totalBalance-已领取数量，limit-最大限制数量
            const { has_get_geek_reward: hasGetGeekReward, can_get_reward_num: canGetRewardNum, can_get_geek: canGetGeek, formal_staff: formalStaff, total_balance: totalBalance, limit } = this.cardInfo
            let canBeClaimed = canGetRewardNum > 0 && canGetGeek
            if (!formalStaff) {
              this.$message.warning('非集团本部正式员工不可领取')
            } else if (totalBalance >= limit) {
              this.$message.warning('已达到领取数量上限')
            } else if (!canBeClaimed) {
              this.$message.warning('暂无领取资格，请查看活动规则并完成学习条件后再点击领取')
            } else if (!hasGetGeekReward && canGetRewardNum <= 0) {
              this.$message.warning('本轮学霸卡已发放完毕，暂时无法领取，敬请期待后续轮次的发放活动')
            } else if (hasGetGeekReward) {
              this.$message.warning('你本轮次已领取过学霸卡，每人限领1次，敬请期待后续轮次的发放活动')
            }
          }
          break
        default:
          break
      }
    },
    // 发请求领取积分卡
    async claimPointsFn(type) {
      try {
        let payload = {
          staff_id: this.userInfo.staff_id,
          staff_name: this.userInfo.staff_name,
          acct_type_code: 'sanjieke',
          grant_amt: '1',
          grant_type: type
        }
        await claimPoints(payload)
        this.$message.success('学霸卡领取成功')
        this.init()
      } catch (error) {
        console.log('领取兑换券ERR：', error)
        this.init()
      }
    },
    // 发请求获取领用详情
    getRewardDetails() {
      queryGeekRecord({ 
        staff_id: this.userInfo.staff_id,
        acct_type_code: 'sanjieke',
        current: this.pagination.current,
        size: this.pagination.size
      }).then(res => {
        this.tableData = res.records
        console.log('tableData: ', this.tableData)
        // 分页数据处理
        this.pagination.total = res.total
        const { size } = this.pagination
        let pages = Math.floor(res.total / size)
        let pageNum = pages > 0 ? pages : 1
        let remainder = res.total > size ? res.total % size : 0
        this.pagination.pageNum = remainder > 0 ? pageNum + 1 : pageNum
      })
    },
    // 学霸卡领用详情
    openRewardDetails() {
      this.dialogVisible = true
    },
    // 处理列表的"说明"字段
    resolveDescData(item) {
      if (['consume', 'manage_deduct'].includes(item.verb_id)) {
        return `使用时间：${item.deduct_time}`
      } else if (item.verb_id === 'deduct_expire') {
        return `失效时间：${item.expire_time}`
      } else {
        return `领取7天后，卡券失效，请及时使用`
      }
    },
    // 获取内容中心图片
    getCourseCoverUrl(data) {
      if (HTTPS_REG.test(data)) {
        return data
      }
      if (data) {
        const envName = env[process.env.NODE_ENV]
        return `${envName.contentcenter}content-center/api/v1/content/imgage/${data}/preview`
      }
      return 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
    },
    // 领取和使用详情 - 去兑换课程
    toManagePage() {
      let herf = process.env.NODE_ENV === 'production' ? 'https://portal.learn.woa.com' : 'https://test-portal-learn.woa.com'
      window.open(herf + '/training/mooc/home')
    },
    // 点击顶部图片跳转
    goTo() {
      let url = this.configInfo.banner?.banner_pic_go_url || ''
      console.log(url, 'bannerGo')
      if (url) window.open(url)
    },
    // 点击查看此次赠课活动详情
    toActiveDetail() {
      // 外部好课专区
      window.open('https://sdc.qq.com/s/sYkoua?scheme_type=homepage&page_id=503')
      // window.open('https://sdc.qq.com/s/1ZyHTE?scheme_type=moduledetail&config_id=4785&name=%E5%A4%96%E9%83%A8%E6%8A%80%E6%9C%AF%E5%A5%BD%E8%AF%BE&page_id=107&lang=zh')
    },
    // 已兑课程
    toMooc() {
      let herf = process.env.NODE_ENV === 'production' ? 'https://portal.learn.woa.com' : 'https://test-portal-learn.woa.com'
      window.open(herf + '/training/mooc/home?to=myItem')
    },
    // 活动规则
    toRulesDetail() {
      window.open('https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=38680&from_act_id=38680&share_staff_id=73758&share_staff_name=circlechai')
    },
    // 跳转到课程详情
    toCourseDetail(href) {
      window.open(href)
    },
    getImgUrl() {
      const envName = env[process.env.NODE_ENV]
      this.bannerImage = this.configInfo?.banner?.banner_pic_url || `${envName.contentcenter}content-center/api/v1/content/imgage/${this.configInfo?.banner?.banner_pic_id}/preview`
    },
    // 分页 上一页
    toPrev() {
      if (this.pagination.current <= 1) return
      this.pagination.current--
      this.getRewardDetails()
    },
    // 分页 下一页
    toNext() {
      if (this.pagination.current >= this.pagination.pageNum) return
      this.pagination.current++
      this.getRewardDetails()
    }
  },
  beforeDestroy() {
  },
  filters: {
  }
}
</script>

<style lang="less">
.coupon-dialog {
  border-radius: 8px;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
  .el-table .table-header-style {
    height: 46px;
  }
  .el-table .table-header-style th{
    background: #F5F5F5;
  }
  .el-table th>.cell {
    color: #00000099;
    font-size: 14px;
    font-weight: 400;
    line-height: 22px;
  }
}
</style>
<style lang="less" scoped>
.active-page {
  width: 100%;
//   height: 100vh;
//   overflow: auto;
  .banner-card {
    width: 100%;
    min-width: max-content;
    height: 206px;
    // background: linear-gradient(99deg, #2475F9 5.9%, #0052D9 100.57%);
    background: url('../../../assets/outsourcedCourse/after_bg_sanjieke.png') no-repeat 0 0;
    background-position: 50%;
    background-size: cover;
    background-repeat: no-repeat;
    .banner-content {
      position: relative;
      width: 1100px;
      min-width: 1100px;
      height: 100%;
      padding-top: 50px;
      margin: 0 auto;
      position: relative;
      cursor: pointer;
      .before_bg {
        position: absolute;
        width: 388px;
        height: 121px;
        bottom: 53px;
        right: 0;
      }
      .banner-image {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        z-index: 1;
        cursor: pointer;
      }
      .cloud-icon {
        width: 131px;
        position: absolute;
        left: 4px;
        bottom: 19.55px;
        z-index: 1;
      }
      .card-icon {
        width: 179px;
        position: absolute;
        right: -7px;
        bottom: 11.7px;
        z-index: 1;
      }
      .title {
        width: 378px;
        position: relative;
        z-index: 2;
        .img {
          width: 100%;
        }
      }
      .toolbar {
        display: flex;
        align-items: center;
        padding-top: 24px;
        position: relative;
        z-index: 2;
        .left {
          display: flex;
          line-height: 32px;
          color: #ffffff;
          font-size: 14px;
          font-weight: 500;
          padding-right: 48px;
          .info {
            padding: 8px 12px;
            font-size: 20px;
            font-weight: 500;
            color: #fff;
            line-height: 22px;
            border-radius: 8px;
            // background: #ffffff80;
            background: #1854B5;
          }
          .link {
            text-decoration-line: underline;
            text-underline-offset:2px;
            cursor: pointer;
          }
          .wide {
            line-height: 24px;
            color: #f4f8ff;
          }
        }
        .right {
          .common {
            height: 32px;
            padding: 5px 20px;
            border-radius: 64px;
            color: #0052d9;
            font-size: 14px;
            font-weight: 600;
            background: #E8F4FF;
            border-color: #E8F4FF;
            &.is-plain:hover {
              background: #fff;
              border-color: #fff;
              color: #2f74e1;
            }
            &+.el-button {
              margin-left: 12px;
            }
          }
        }
      }
    }
  }
  .contain-main {
    width: 1100px;
    min-width: 1100px;
    margin: -42px auto 0;
    position: relative;
    z-index: 2;
    .coupon-bar {
      width: 100%;
      display: flex;
      justify-content: space-between;
      .coupon-card {
        width: 540px;
        padding: 20px 28px;
        background: #fff;
        border-radius: 8px;
        box-shadow: 0 0 8px 0 #eeeeee99;
        .card {
          width: 484px;
          height: 127px;
          padding: 16px 24px;
          .title {
            font-size: 18px;
            font-weight: 700;
            line-height: 22px;
            margin-bottom: 2px;
          }
          .subtitle {
            font-size: 12px;
            line-height: 20px;
            &>span {
              border-bottom: 1px dotted;
              padding-bottom: 6px;
            }
          }
          .lower-part {
            padding-top: 10px;
            position: relative;
            p {
              font-size: 12px;
              line-height: 16px;
              &+p {
                margin-top: 4px;
              }
            }
            .el-button  {
              width: 100px;
              height: 28px;
              border-radius: 64px;
              font-size: 12px;
              font-weight: 500;
              padding: 0;
              color: #ffffff;
              position: absolute;
              right: 0;
              bottom: 0;
            }
          }
        }
        .coupon-init {
          background: url('~@/assets/outsourcedCourse/card-init.png')  no-repeat center / cover;
          .title {
            color: #0c3102;
          }
          .subtitle {
            color: #3e653d;
          }
          .lower-part {
            p {
              color: #6e794f;
              &+p {
                color: #31571999;
              }
            }
          }
          .receive-init {
            background: #2A648E;
            border-color: #2A648E;
            &.is-plain:hover {
              background: #2A648E;
              opacity: 0.8;
            }
            &.disabled {
              background: #C2DBEC;
              border-color: #C2DBEC;
              &:hover {
                background: #C2DBEC;
                opacity: 1;
              }
            }
          }
          
        }
        .coupon-qualify {
          background: url('~@/assets/outsourcedCourse/card-qualify.png')  no-repeat center / cover;
          .title {
            background-image:-webkit-linear-gradient(top,#805524,#573A18);
            -webkit-background-clip:text;
            -webkit-text-fill-color:transparent;
          }
          .subtitle {
            color: #6e491f;
          }
          .lower-part {
            p {
              color: #6e491fe6;
              &+p {
                color: #6e491f99;
              }
            }
          }
          .receive-qualify {
            background: linear-gradient(276deg, #8B5300 14.56%, #AE864A 95.9%);
            border: 0;
            &.is-plain:hover {
              background: linear-gradient(276deg, #8B5300 14.56%, #AE864A 95.9%);
              opacity: 0.8;
            }
            &.disabled {
              background: linear-gradient(276deg, #EAC896 14.56%, #EEDCB9 95.9%);
              &:hover {
                background: linear-gradient(276deg, #EAC896 14.56%, #EEDCB9 95.9%);
                opacity: 1;
              }
            }
          }
        }
        .introduce {
          padding-top: 12px;
          color: #5c3d00;
          font-size: 14px;
          font-weight: 400;
          line-height: 22px;
        }
      }
    }
    .coupon-bar-new {
      display: flex;
      flex-direction: column;
      padding: 16px 28px 20px;
      height: 270px;
      border-radius: 8px;
      background-color: #fff;
      filter: drop-shadow(0 0 8px #eeeeee99);
      .title {
        color: #000000e6;
        font-size: 18px;
        font-weight: 600;
        line-height: 22px;
      }
      .card-list-content {
        display: flex;
        margin: 18px 0;
        .card-init {
          position: relative;
          width: 248px;
          height: 127px;
          margin-right: 12px;
          padding: 12px 16px 12px 20px;
          background: url('~@/assets/outsourcedCourse/card-init-new.png') no-repeat 0 0;
          background-size: 100%;
          &::after { // 待领取
            position: absolute;
            content: '';
            right: 0;
            top: 0;
            width: 40px;
            height: 40px;
            background: url('~@/assets/outsourcedCourse/wait-get.png') no-repeat 0 0;
            background-size: 100%;
            z-index: 1;
          }
          .init-disable-class1 {
            color: #00000099;
          }
          .init-disable-class2 {
            color: #00000066;
          }
        }
        .card-stand {
          position: relative;
          width: 187px;
          height: 127px;
          margin-right: 12px;
          padding: 12px 16px 12px 20px;
          background: url('~@/assets/outsourcedCourse/card-stand-new-sanjieke.png') no-repeat 0 0;
          background-size: 100%;
          &:last-child {
            margin-right: 0;
          }
          &::after { // 待领取
            position: absolute;
            content: '';
            right: 0;
            top: 0;
            width: 40px;
            height: 40px;
            background: url('~@/assets/outsourcedCourse/wait-get.png') no-repeat 0 0;
            background-size: 100%;
            z-index: 1;
          }
          .stand-disable-class1 {
            color: #00000099;
          }
          .stand-disable-class2 {
            color: #00000066;
          }
        }
        .card-stand-1::after { // 待兑换
          background-image: url('~@/assets/outsourcedCourse/card-stand-1.png');
        }
        .card-stand-2::after { // 已兑换
          background-image: url('~@/assets/outsourcedCourse/card-stand-2.png');
        }
        .card-stand-3::after { // 未解锁
          background-image: url('~@/assets/outsourcedCourse/card-stand-3.png');
        }
        .card-stand-4::after { // 已失效
          background-image: url('~@/assets/outsourcedCourse/card-stand-4.png');
        }
        .sub-title {
          color: #0c3102;
          font-size: 16px;
          font-weight: 700;
          line-height: 22px;
          display: flex;
          align-items: center;
          font-family: "Source Han Sans CN";
          img {
            margin-left: 8px;
          }
        }
        .title-1 {
          margin-top: 2px;
          padding-bottom: 5px;
          margin-bottom: 5px;
          color: #6e491f;
          font-size: 12px;
          line-height: 20px;
        }
        .title-warm {
          padding-top: 5px;
          color: #ff6600;
          font-size: 12px;
          line-height: 16px;
          border-top: 1px dotted #486646;
        }
        .botton-box {
          margin-top: 11px;
          display: flex;
          justify-content: flex-end;
        }
        .el-button  {
          width: 72px;
          height: 22px;
          border-radius: 64px;
          font-size: 12px;
          font-weight: 500;
          padding: 0;
          color: #ffffff;
        }
        .button-customer {
          background: linear-gradient(276deg, #8B5300 14.56%, #AE864A 95.9%);
          border-color: #e92a0040;
          &.is-plain:hover {
            opacity: 0.8;
          }
          &.disabled {
            background: linear-gradient(276deg, #EAC896 14.56%, #EEDCB9 95.9%);
            border-color: #eed9b3;
          }
          &.disabled-gray {
            background: linear-gradient(276deg, #D4D4D4 14.56%, #D8D8D8 95.9%);
            border-color: #d6d5d5;
          }
        }
      }
      .text-box {
        color: #00000099;
        font-size: 14px;
        line-height: 22px;
        .text-title {
          color: #00000099;
          font-size: 14px;
          font-weight: 500;
          line-height: 22px;
        }
        .warm {
          color: #ed7b2f;
          font-size: 14px;
          line-height: 22px;
        }
        .text-attention {
          color: #ed7b2f;
          font-weight: 500;
        }
      }
    }
    .course-list {
      padding: 21px 28px;
      background: #fff;
      margin-top: 20px;
      border-radius: 8px;
      box-shadow: 0 0 8px 0 #eeeeee99;
      .title-col {
        display: flex;
        // justify-content: space-between;
        align-items: center;
        .title {
          color: #000000e6;
          font-size: 18px;
          font-weight: 600;
          line-height: 22px;
          margin-right: 20px;
        }
        .link {
          color: #ff6600;
          font-size: 16px;
          font-weight: 500;
          line-height: 22px;
          text-decoration-line: underline;
          text-underline-offset:2px;
          cursor: pointer;
        }
      }
      .list {
        margin: 20px -28px 0 0;
        display: flex;
        flex-wrap: wrap;
        .course-item {
          width: 240px;
          margin: 0 28px 24px 0;
          cursor: pointer;
          .cover {
            width: 100%;
            height: 160px;
            margin-bottom: 12px;
          }
          .title {
            color: #000000e6;
            font-size: 14px;
            line-height: 22px;
            display: -webkit-box;
            overflow: hidden;
            text-overflow: ellipsis;
            word-break: break-all;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            &>img {
                display: inline-block;
                width: 58px;
                height: 20px;
                vertical-align: bottom;
            }
            &>span {
              height: 20px;
              padding: 0 6px;
              margin-right: 6px;
              justify-content: center;
              align-items: center;
              gap: 10px;
              border-radius: 2px;
              border: 1px solid #0052D9;
              background: #ECF2FE;
              color: #0052D9;
              font-weight: 600;

            }
          }
        }
      }
      &>.link {
        text-align: center;
        span {
          cursor: pointer;
          color: #ff6600;
          font-size: 16px;
          font-weight: 500;
          line-height: 24px;
          text-decoration-line: underline;
        }
      }
    }
  }
  .usage-details {
    .head {
      padding: 16px 32px;
      color: #000000e6;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      border-bottom: 1px solid #E7E7E7;
      position: relative;
      .close {
        width: 16px;
        height: 16px;
        position: absolute;
        top: 20px;
        right: 34px;
        cursor: pointer;
      }
    }
    .body {
      padding: 24px 32px;
      .pagination {
        padding-top: 16px;
        display: flex;
        justify-content: flex-end;
        .box {
          display: flex;
          padding: 6px 16px 6px 8px;
          justify-content: center;
          align-items: center;
          gap: 4px;
          border-radius: 6px;
          border: 0.5px solid #E7E7E7;
          background: #FFF;
          color: #00000099;
          font-size: 12px;
          line-height: 20px;
          user-select: none;
          &+.box {
            margin-left: 16px;
          }
        }
        .btn {
          cursor: pointer;
          &:hover {
            color: #0052D9;
            background: #F3F7FF;
            .icon-grey {
              display: none;
            }
            .icon-active {
              display: block;
            }
          }
          & > img {
            margin: 0 !important;
          }
        }
        .prev {
          .icon-grey {
            transform: rotate(90deg);
          }
          .icon-active {
            transform: rotate(180deg);
            display: none;
          }
        }
        .next {
          padding: 6px 8px 6px 16px;
          .icon-grey {
            transform: rotate(-90deg);
          }
          .icon-active {
            display: none;
          }
        }
        .num {
          padding: 6px 16px;
          .current {
            color: #0052D9;
          }
          .total {
            color: #000000e6;
          }
        }
        .disable {
          background: #F5F5F5;
          cursor: no-drop;
          &:hover {
            background: #F5F5F5;
            color: #00000099;
            .icon-grey {
              display: block;
            }
            .icon-active {
              display: none;
            }
          }
        }
      }
    }
  }
  .status-label {
    font-size: 12px;
    line-height: 20px;
    padding: 1px 8px;
    border-radius: 4px;
  }
  .status-no-effict {
    color: #ed7b2f;
    border: 1px solid var(---Warning5-Normal, #ED7B2F);
    background: var(---Warning1-Light, #FEF3E6);
  }
  .status-waite-use {
    color: #0052d9;
    border: 1px solid var(---Brand8-Normal, #0052D9);
    background: var(---Brand1-Light, #ECF2FE);
  }
  .status-oready-used {
    color: #00a870;
    border: 1px solid var(---Success5-Normal, #00A870);
    background: var(---Success1-Light, #E8F8F2);
  }
}
.error-cover {
  width: 100%;
  height: 100%;
}
.image-box {
  width: 100%;
  height: 100%;
}
.img-16 {
  width: 16px;
  height: 16px;
}
.important {
  color: #ED7B2F !important;
  font-weight: normal;
}
</style>
