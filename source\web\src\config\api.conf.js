import http from 'utils/http'
import env from 'config/env.conf.js'

const envName = env[process.env.NODE_ENV]
// 标签接口区分woa oa
// let tagUrl
// if (window.location.hostname.endsWith('.woa.com')) {
//   tagUrl = envName.mLearn
// } else {
//   tagUrl = envName.mLearn
// }
// 图文用户端
// 获取登陆用户信息
export const getLoginUser = () => http.get(envName.commonPath + 'training-portal-common/api/v1/portal/user/common/loginuser')
// 获取用户所属组织
export const getUserDep = (params) => http.get(envName.trainingPath + 'api/common/user/userinfo/baseinfo', { params })
// 获取网络课分类
export const getNetCourseClassify = () => http.get(envName.trainingPath + 'api/graphic/user/content_classify/get_net_course_classify', { loading: false })
// 获取标签分类
export const getTagList = (params) => http.get(`${envName.trainingPath}api/graphic/user/lable/auto_com_labels`, { params })
// 获取推荐标签
export const getRecommendLabels = (params) => http.post(`${envName.trainingPath}api/businessCommon/manage/label/get_recommend_labels`, { params })
// 保存用户标签
// export const saveLabel = (params) => http.get(envName.trainingPath + '/api/graphic/user/lable/savelabel', { params })
// 图文保存草稿
export const saveDraft = (params) => http.post(envName.trainingPath + 'api/graphic/user/graphic/save_draft', { params, loading: false })
// 图文保存发布
export const addGraphic = (params) => http.post(envName.trainingPath + 'api/graphic/user/graphic/add_graphic', { params, loading: false })
// 图文编辑
export const modifyGraphic = (params) => http.post(envName.trainingPath + 'api/graphic/user/graphic/modify', { params, loading: false })
// 获取图文详情/图文预览
export const getGraphicDetails = (params) => http.get(envName.trainingPath + 'api/graphic/user/graphic/get_graphic_details', { params, loading: false })
// 获取用户的课单（课程添加到多个课单时的课单列表）
export const getUserCourseList = (params) => http.get(`${envName.courseManagePath}user/courselist/get_user_cl`, { params, loading: true })
// 添加到课单
export const addClassList = (params) => http.post(envName.courseManagePath + 'user/courselist/add_multiple_content', { params, loading: true })
// 获取课单数量
export const getContentAddedCount = (params) => http.get(`${envName.courseManagePath}user/courselist/get_content_added_count`, { params, loading: true })
// 本站内容列表
export const getSearchsite = (params) => http.post(`${envName.trainingPath}api/businessCommon/common/content/mooc-search`, { params, loading: true })
// 文章本站内容列表
export const getGraphicSearchsite = (params) => http.post(`${envName.trainingPath}api/businessCommon/common/content/search`, { params, loading: true })
// 添加自定义内容
export const addExtendContents = (params) => http.post(envName.trainingPath + 'api/graphic/user/graphic/add_extend_contents', { params, loading: true })
// 获取延伸学习分页列表
export const queryExtendContentPage = (params) => http.get(envName.trainingPath + `api/businessCommon/common/fileProdConn/query_extend_content_page`, { params, loading: false })
// 获取图文所有的延伸课程
export const getExtanContentList = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/fileProdConn/get_extend_content_list`, { params, loading: true })
// 删除延伸学习
export const delExtendContent = (id) => http.get(envName.trainingPath + `api/graphic/user/graphic/del_extend_content/${id}`, { loading: true })
// 延伸学习置顶操作
export const updateTopedStatus = (id) => http.get(envName.trainingPath + `api/graphic/user/graphic/update_toped_status/${id}`, { loading: true })
// 图文浏览
export const getViewGraphic = (params) => http.get(envName.trainingPath + 'api/graphic/user/graphic/view_graphic', { params, loading: false })
// 图文浏览记录上报
export const viewGraphicRecord = (params) => http.get(envName.trainingPath + 'api/graphic/user/graphic/view_graphic/record', { params, loading: false })
// 课单内容列表
export const getContentsList = (id) => http.get(`${envName.courseManagePath}user/courselist/get_contents/${id}`, { loading: true })
// 获取内容推荐
export const getRecommendList = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/recommend/get-similar-item`, { params, loading: true })
// 是否是系统高峰期
export const isSysBusy = (params) => http.get(`${envName.trainingPath}api/businessCommon/manage/dict/is_sys_busy`, { params, loading: false })
// 判断是否点赞
export const checkPraised = (params) => http.get(`${envName.trainingPath}api/graphic/user/praise/check-praised`, { params, loading: false })
// 点赞
export const addPraise = (params) => http.get(`${envName.trainingPath}api/graphic/user/praise/add-praise`, { params, loading: false })
// 取消点赞
export const deletePraise = (params) => http.get(`${envName.trainingPath}api/graphic/user/praise/delete-praise`, { params, loading: false })
// 判断是否收藏
export const checkFavorited = (params) => http.get(`${envName.trainingPath}api/graphic/user/favorite/check-favorited`, { params, loading: false })
// 收藏
export const addFavorite = (params) => http.get(`${envName.trainingPath}api/graphic/user/favorite/add-favorite`, { params, loading: false })
// 取消收藏
export const deleteFavorite = (params) => http.get(`${envName.trainingPath}api/graphic/user/favorite/delete-favorite`, { params, loading: false })
// 判断当前用户是否在白名单
export const visitorCheck = (params) => http.get(`${envName.trainingPath}api/graphic/user/graphic/graphic_visitor_check`, { params, loading: false })

// 文档详情
// 获取文档详情
export const getWordDetail = (params) => http.get(`${envName.v8MobileHost}api/user/file-prod/file-prod/detail`, { params, loading: false })

// 获取文档地址
export const getWordUrl = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/content/previewInfo`, { params, loading: false })
// 获取文档个性推荐
export const getPersonalRecList = (params) => http.get(`${envName.v8MobileHost}api/user/recommend/user-recommends/get-personal-rec-list`, { params, loading: false })
// 获取文档延伸学习
export const getRecommendLearning = (params) => http.get(`${envName.v8MobileHost}api/user/recommend/user-recommends/get-recommend-learning`, { params, loading: false })
// 文档点赞
export const wordPraise = (params) => http.post(`${envName.v8MobileHost}api/user/file-prod/file-prod/eval`, { params, loading: false })
// 文档收藏
export const wordCellect = (params) => http.post(`${envName.v8MobileHost}api/user/file-prod/file-prod/fav`, { params, loading: false })
// 文档上报学习记录
export const wordAddRecord = (params) => http.post(`${envName.v8MobileHost}api/user/file-prod/file-prod/add-study-record`, { params, loading: false })

// 图文管理端
export const getGraphicList = (params) => http.post(`${envName.trainingPath}api/graphic/manage/graphic/query_graphic`, { params, loading: true })
// 删除图文
export const delGraphic = (graphic_id) => http.get(`${envName.trainingPath}api/graphic/manage/graphic/del_graphic/${graphic_id}`, { loading: true })
// 图文加精
export const add_excellent = (params) => http.post(`${envName.trainingPath}api/graphic/manage/graphic/add_excellent`, { params, loading: true })
// 创作中心--我的图文列表
export const myGraphicPageList = (params) => http.post(`${envName.trainingPath}api/graphic/user/my/my_graphic_page`, { params, loading: true })

export const graphicPageListDel = (graphic_id) => http.get(`${envName.trainingPath}api/graphic/user/graphic/delete_graphic?graphic_id=${graphic_id}`, { loading: true })
// 灰度账户有没有权限
export const isAuthority = (params) => http.get(`${envName.trainingPath}api/netcourse/user/courseinfo/check-vh-gray`, { params, loading: false })
// 合成语音
export const composeVioce = (params) => http.get(`${envName.contentcenter}content-center/api/v1/content/vh/tts-preview`, { params, loading: false })
// 解析ppt
export const analysisPPT = (params) => http.post(`${envName.contentcenter}content-center/api/v1/content/vh/ppt-preview`, {
  params,
  loading: false,
  timeout: 300000,
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})
// 视频生成
export const composeVideo = (params, loading) => http.post(`${envName.contentcenter}content-center/api/v1/content/vh/vh-generate-by-contentid`, {
  params,
  loading,
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})
// 获取生成的视频
export const getVideoResult = (params) => http.get(`${envName.contentcenter}content-center/api/v1/content/vh/vh-result`, { params, loading: false })
// 获取音色
export const getVoice = () => http.get(`${envName.contentcenter}content-center/api/v1/content/vh/tts-voice-dict`, { loading: false })

// 在线课程
// 在线课程列表
export const getOnlineResult = (params) => http.get(`${envName.trainingPath}api/netcourse/user/courseinfo/get-course-list`, { params, loading: true })
// 在线课程统计
export const getOnlineStatistics = (params) => http.get(`${envName.trainingPath}api/netcourse/user/courseinfo/course-statistics`, { params, loading: true })
// 停用
// export const stopCourse = (params) => http.post(`${envName.trainingPath}api/netcourse/user/courseinfo/course-destroy/${params}`, { loading: true })
export const stopCourse = (params) => http.post(`${envName.trainingPath}api/businessCommon/manage/courseReview/deactivate/${params}?act_type=${2}`, { loading: true })
// 删除
export const deleteCourse = (params) => http.post(`${envName.trainingPath}api/netcourse/user/courseinfo/delete-course`, { params, loading: true })
// 启用
// export const startCourse = (params) => http.post(`${envName.trainingPath}api/netcourse/user/courseinfo/save-course-status`, { params, loading: true })
export const startCourse = (params) => http.post(`${envName.trainingPath}api/businessCommon/manage/courseReview/activate/${params.net_course_id}?act_type=${2}`, { loading: true })
// 课程信息表单
// 分类
export const getClassify = (params) => http.get(`${envName.trainingPath}api/businessCommon/manage/course-classify/get-course-classify`, { params, loading: true })
// 考试分类
export const getExamClassify = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/exam/categories`, { params, loading: true })
// 推荐标签
export const getTagRecommendList = (params) => http.post(`${envName.trainingPath}api/businessCommon/manage/label/get_recommend_labels`, { params, loading: false })
// 标签列表
export const getAiTagList = (params) => http.get(`${envName.trainingPath}api/businessCommon/manage/label/auto_com_labels`, { params, loading: false })
// 网课合成
export const updateCourse = (params) => http.post(`${envName.trainingPath}api/netcourse/user/courseinfo/submit-course-synthesis`, { params, loading: false })
// 网课存草稿
export const addCourseDraft = (params) => http.post(`${envName.trainingPath}api/netcourse/user/courseinfo/save-course-draft`, { params, loading: false })
// 网课发布
export const addCoursePublish = (params) => http.post(`${envName.trainingPath}api/netcourse/user/courseinfo/course-publish`, { params, loading: true })
// 直接上传保存
export const addCourseSave = (params) => http.post(`${envName.trainingPath}api/netcourse/user/courseinfo/save-course-info`, { params, loading: false })
// 获取网课详细信息
export const getCourseInfo = (id) => http.get(`${envName.trainingPath}api/netcourse/manage/courseinfo/${id}`, { loading: true })
// UGC网课发布
export const coursePublish = (id, params) => http.post(`${envName.trainingPath}api/netcourse/user/courseinfo/course-list-publish/${id}`, { params, loading: true })
// 获取内容中心签名
export const operatesignature = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/content/operatesignature`, { params, loading: true })
// 获取内容中心文件信息
export const getContentInfo = (id, params) => http.get(`${envName.contentcenter}content-center/api/v1/content/${id}`, { params, loading: true })
// 移动端获取内容中心文件信息
export const getMobileContentInfo = (id, params) => http.get(`${envName.contentcenter}content-center/api/v1/content/${id}`, { params, loading: true })
// 获取视频信息
export const getVideoInfo = (params) => http.post(`${envName.contentcenter}content-center/api/v1/media/video-info`, {
  params,
  loading: true,
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})
// 获取章节配置权限
export const checkTarget = () => http.get(`${envName.trainingPath}api/ql/manage/netCourseChapters/checkTarget
`, { loading: true })
// 获取章节配置列表
export const getNetCourseChapters = (params) => http.get(`${envName.trainingPath}api/ql/manage/netCourseChapters/info`, { params, loading: true })
// 保存章节配置
export const saveNetCourseChapters = (params) => http.post(`${envName.trainingPath}/api/ql/manage/netCourseChapters/save`, { params, loading: true })

// 网课播放页详情
export const getNetCourseInfo = (params) => http.get(`${envName.trainingPath}api/netcourse/user/courseinfo/get-course-info`, { params, loading: true })
// 网络课延伸学习
export const getNetExtendContentList = (params) => http.get(`${envName.trainingPath}api/netcourse/user/courseinfo/get-extend-courses`, { params, loading: true })
// 网络课判断是否点赞
export const netCheckPraised = (params) => http.get(`${envName.trainingPath}api/netcourse/user/course-praise/check-praised`, { params, loading: false })
// 网络课点赞
export const netAddPraise = (params) => http.get(`${envName.trainingPath}api/netcourse/user/course-praise/add-praise`, { params, loading: false })
// 网络课取消点赞
export const netDeletePraise = (params) => http.get(`${envName.trainingPath}api/netcourse/user/course-praise/delete-praise`, { params, loading: false })
// 网络课判断是否收藏
export const netCheckFavorited = (params) => http.get(`${envName.trainingPath}api/netcourse/user/course-favorite/check-favorited`, { params, loading: false })
// 网络课收藏
export const netAddFavorite = (params) => http.get(`${envName.trainingPath}api/netcourse/user/course-favorite/add-favorite`, { params, loading: false })
// 网络课取消收藏
export const netDeleteFavorite = (params) => http.get(`${envName.trainingPath}api/netcourse/user/course-favorite/delete-favorite`, { params, loading: false })
// 获取笔记心得
export const getRelationGraphic = (params) => http.get(`${envName.trainingPath}api/graphic/user/graphic/get_relation_graphic_by_actType`, { params, loading: false })
// 获取笔记 - h5页面
export const getRelationGraphicH5 = (params) => http.get(`${envName.trainingPath}api/ext/graphic/get_relation_graphic`, { params, loading: false })
// 网络课学习记录上报
export const netViewRecord = (params) => http.post(`${envName.trainingPath}api/netcourse/user/courseinfo/add-study-record`, { params, loading: false })
// 获取网络课章节列表
export const getNetCourseChapterList = (netCourseId) => http.get(`${envName.trainingPath}/api/netcourse/user/netCourseChapters/${netCourseId}/chapter-list`, { loading: true })
// 文章抽次分享弹窗
export const close_popup = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/tip/close_popup`, { params, loading: true })
export const get_popup_msg = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/tip/get_popup_msg`, { params, loading: true })
// 是否赞赏
export const is_rewarded = (params) => http.post(`${envName.courseWoaHost}point/api/v1/user/acct/is-rewarded`, { params, loading: false })
// 赞赏次数
export const reward_count = (params) => http.post(`${envName.courseWoaHost}point/api/v1/user/acct/reward-count`, { params, loading: false })
// 是否是正式员工
export const isFormalStaff = (params) => http.get(`${envName.courseWoaHost}point/api/v1/user/isFormalStaff/${params}`, { loading: false })
// 是否离职
export const statususer = (params) => http.get(`${envName.courseWoaHost}point/api/v1/user/status/${params}`, { loading: false })
export const display_control = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/tip/display_control`, { loading: false })
// export const close_popup = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/tip/close_popup`, { params, loading: true })
// export const get_popup_msg = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/tip/get_popup_msg`, { params, loading: true })

// 管理后台
export const approveManageList = (params) => http.get(`${envName.trainingPath}api/netcourse/manage/courseinfo/get-approve-course-list`, { params, loading: false })
export const courseReviewList = (params) => http.get(`${envName.trainingPath}api/businessCommon/manage/courseReview/courseList`, { params, loading: true })
// 审核接口
// export const approveStatus = (params) => http.post(`${envName.trainingPath}api/netcourse/manage/courseinfo/approve`, { params, loading: true })
export const approveStatus = (params) => http.post(`${envName.trainingPath}api/businessCommon/manage/courseReview/approve`, { params, loading: true })
// ai审核权限详情
// export const approveAiDetail = (id) => http.get(`${envName.trainingPath}api/netcourse/manage/courseinfo/get-approve-course-info?net_course_id=${id}`, { loading: true })
export const approveAiDetail = (id, actType) => http.get(`${envName.trainingPath}/api/businessCommon/manage/courseReview/courseInfo?course_id=${id}&act_type=${actType}`, { loading: true })
// 获取审核直接上传视频签名
export const approveOperatesignature = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/content/operatesignature`, { params, loading: false })
// 获取审核页内容中心文件信息
export const getApproveContentInfo = (id, params) => http.get(`${envName.contentcenter}content-center/api/v1/content/${id}/url`, { params, loading: true })
// 标签管理列表
export const find_label_page = (params) => http.post(envName.trainingPath + 'api/label/manage/labelinfo/find_label_page', { params, loading: true })
// 标签关联课程
export const find_labelCourse_page = (params) => http.post(envName.trainingPath + 'api/label/manage/labelinfo/find_labelCourse_page', { params, loading: true })
// 导出标签关联课程
export const export_labelCourse_page = (params) => http.post(envName.trainingPath + 'api/label/manage/labelinfo/export_labelCourse_page', { params, loading: true, responseType: 'blob' })
// 设置官方/取消官方
export const update_label_type = (params) => http.post(envName.trainingPath + 'api/label/manage/labelinfo/update_label_type', { params, loading: true })
// 导出标签列表数据
export const export_label_data = (params) => http.post(envName.trainingPath + 'api/label/manage/labelinfo/export_label_data', { params, loading: true, responseType: 'blob' })
// 删除标签
export const del_label = (params) => http.get(`${envName.trainingPath}api/label/manage/labelinfo/del_label/${params}`, { loading: false })
// 标签分类树
export const category_tree = (params) => http.get(`${envName.trainingPath}api/v1/manage/category/category_tree`, { loading: false })
// 导出标签列表数据
export const add_or_update_label = (params) => http.post(envName.trainingPath + 'api/label/manage/labelinfo/add_or_update_label', { params, loading: true })
// 分类-导出明细
export const export_label_dataAPI = () => http.post(envName.trainingPath + 'api/v1/manage/category/export_label_data', { loading: true, responseType: 'blob' })
// 分类-列表
export const category_treeAPI = () => http.get(envName.trainingPath + 'api/v1/manage/category/category_tree', { loading: true })
// 分类保存
export const saveAPI = (params) => http.post(envName.trainingPath + 'api/v1/manage/category/save', { params, loading: true })
// 标签列表展示
export const labelsAPI = (params) => http.get(envName.trainingPath + 'api/v1/manage/category/labels', { params, loading: true })
// 设为热门
export const update_hot_label = (params) => http.post(envName.trainingPath + 'api/label/manage/labelinfo/update_hot_label', { params, loading: true })
// 作业说明
export const homework_detail = (params) => http.get(envName.trainingPath + 'api/mooc/user/homework/get-homework-detail', { params, loading: true })
// 作业互评列表
export const studentReviewHomework = (params) => http.get(envName.trainingPath + 'api/mooc/user/homework/get-student-review-homework-list', { params, loading: false })
export const studentHomework = (params) => http.post(envName.trainingPath + 'api/mooc/user/homework/submit-homework-record', { params, loading: true })
// 作业记录
export const getHomeworkRecord = (params) => http.get(envName.trainingPath + `api/mooc/user/homework/get-homework-record`, { params, loading: true })
// 撤回作业
export const revocationHomeworkRecord = (params) => http.get(envName.trainingPath + 'api/mooc/user/homework/revocation-homework-record', { params, loading: true })
// 保存草稿
export const draftStudentHomework = (params) => http.post(envName.trainingPath + 'api/mooc/user/homework/submit-draft-homework-record', { params })
// 作业列表
export const workListHome = (params) => http.get(envName.trainingPath + 'api/mooc/user/homework/get-student-homework-list', { params, loading: false })
// 获取学生互评
export const getStudentMark = (params) => http.get(envName.trainingPath + 'api/mooc/user/homework/get-my-mark-record', { params, loading: false })
// 获取老师批阅评价
export const getTeacherMark = (params) => http.get(envName.trainingPath + 'api/mooc/manage/homework/get-my-mark-record', { params, loading: false })
// 保存修改互评
export const saveAndEditMark = (type, params) => http.post(envName.trainingPath + `api/mooc/${type}/homework/save-mark`, { params, loading: true })
// 评分详情
export const getHomeworkMarkDetail = (type, params) => http.get(envName.trainingPath + `api/mooc/${type}/homework/get-homework-mark-detail`, { params, loading: true })
// 互评详情列表老师批阅列表
export const getDetailsMutualEvaluation = (type, params) => http.get(envName.trainingPath + `api/mooc/${type}/homework/get-homework-mark-list`, { params, loading: true })
// 老师批阅记录最后一次
export const getTeacherReviewRecord = (type, params) => http.get(envName.trainingPath + `api/mooc/${type}/homework/get-last-teacher-mark`, { params, loading: true })
// 老师批阅记录
export const getTeacherInfoList = (type, params) => http.get(envName.trainingPath + `api/mooc/${type}/homework/get-teacher-mark-record`, { params, loading: true })

// 获取用户的抽奖次数
export const getObtainingUserLotteryTimesAPI = () => http.get(`${envName.lotteryHost}api/v1/user/lottery/lottery-acct`, { loading: false })
// 获取当前课程是否以获取抽奖机会
export const getCourseGotLotteryAPI = (params) => http.get(`${envName.lotteryHost}api/v1/user/lottery/got-lottery`, { params, loading: false })
// 获取课程学习记录
export const getNetCourseLearnRecordAPI = (netCourseId) => http.get(`${envName.trainingPath}api/ql/user/net-course/study_time/${netCourseId}`, { loading: false })
// 获取抽奖机会
export const getLotteryChanceAPI = (params) => http.post(`${envName.lotteryHost}api/v1/user/lottery/lottery-chance`, { params, loading: false })

// 分发页-列表分页
export const getForwardPageApi = (params) => http.get(`${envName.trainingPath}api/businessCommon/manage/forward/page`, { params, loading: true })
// 分发页-启用/停用状态修改
// export const getForwardStatusApi = (params) => http.put(`${envName.trainingPath}api/businessCommon/manage/forward/status`, { params, loading: true })
export const getForwardStatusApi = (params) => http.put(`${envName.trainingPath}api/businessCommon/manage/forward/status?forward_id=${params.forward_id}&status=${params.status}`, { loading: true })
// 分发页-列表删除
export const delForwardApi = (forward_id) => http.del(`${envName.trainingPath}api/businessCommon/manage/forward/del/${forward_id}`, { loading: true })
// 分发页-获取用户跳转地址
export const getUserSkipUrl = (forward_id) => http.get(envName.trainingPath + `api/businessCommon/user/forward/${forward_id}`, { loading: false })
// 分发页-详情
export const getForwardInfo = (forward_id) => http.get(envName.trainingPath + `api/businessCommon/manage/forward/info/${forward_id}`, { loading: true })
// 分发页-保存
export const saveForward = (params) => http.post(envName.trainingPath + `api/businessCommon/manage/forward/save`, { loading: true, params })

// 获取运营分级下的分级项目
export const getOperationApi = (pid) => http.get(envName.trainingPath + `api/businessCommon/manage/dict/get-child-dict-items?key=operations_level&pid=${pid}`, { loading: false })
// 章节智能
export const getAiChaptersApi = (params) => http.get(envName.trainingPath + 'api/ql/manage/netCourseChapters/getAiChapters', { params })
// 智能章节状态
export const updateAiChapterStatus = (params) => http.post(envName.trainingPath + `api/ql/manage/netCourseChapters/updateAiContentFrom`, { loading: true, params })
// 智能章节开关开启状态
export const switchStatus = (params) => http.get(envName.trainingPath + `api/ql/manage/netCourseChapters/getChapterFrom`, { loading: true, params })
// 获取课单详情
export const getCourseDetailAPI = (area_id, params) => http.get(envName.tagPath + `user/courselist/cl_detail/${area_id}`, { loading: false, params })
// 收藏课单
export const favCourse = (params) => http.get(envName.tagPath + `common/favorite/add-favorite`, { loading: true, params })
// 课单观看次数
export const getCourseViewCount = (id) => http.get(envName.tagPath + `user/courselist/add_view/${id}`, { loading: false })
// 猜你喜欢
export const guessLikeAPI = (params) => http.get(envName.mLearn + `portal-qlearning-adapter/api/area/user/page/recommend/discover`, { loading: false, params })
// 专区
export const specialAreaAPI = (id, params) => http.get(envName.portalArea + `area/user/page/${id}/contents`, { loading: false, params })
// 专区详情信息
export const specialDetail = (id, params) => http.get(envName.portalArea + `area/user/page/detail/${id}`, { loading: false, params })
// 评分
export const scoreAPI = (params) => http.post(envName.trainingPath + `api/netcourse/user/course-score/add-score`, { loading: true, params })
// 包含此内容
export const includesContent = (params) => http.get(envName.trainingPath + `api/businessCommon/common/recommend/get-container-contents`, { loading: true, params })
// 详情AB页面
export const targetAB = (params) => http.get(envName.trainingPath + `api/businessCommon/common/checkGrayRelease`, { loading: true, params })
// ai文章获取
export const getAiGraphic = (params) => http.get(envName.trainingPath + `api/graphic/user/graphic/netCourse/AiGraphic`, { loading: true, params })
// 上架-通用接口
export const startCommon = (params) => http.post(`${envName.trainingPath}api/businessCommon/manage/courseReview/activate/${params.id}?act_type=${params.act_type}`, { loading: true })
// 下架-通用接口
export const stopCommon = (params) => http.post(`${envName.trainingPath}api/businessCommon/manage/courseReview/deactivate/${params.id}?act_type=${params.act_type}`, { loading: true })

// 获取审核页面的课程详情
export const getApproveDetailApi = ({ course_id, act_type }) => http.get(`${envName.trainingPath}api/businessCommon/manage/courseReview/courseInfo?course_id=${course_id}&act_type=${act_type}`, { loading: true })

// 网络课预览
export const netSavePreviewApi = (params) => http.post(`${envName.trainingPath}api/netcourse/manage/courseinfo/cacheCoursePreview`, { params, loading: true })
// 获取网课预览信息
export const getCoursePreviewInfo = (id) => http.get(`${envName.trainingPath}api/netcourse/manage/courseinfo/${id}?preview=true`, { loading: true })

// 智能文章开关开启
export const aiArticleApi = (params) => http.post(envName.trainingPath + `api/ql/manage/netCourseAiGraphic/enableAiGraphic`, { loading: true, params })
// 保存文章
export const saveAiArticleApi = (params, id) => http.post(envName.trainingPath + `api/ql/manage/netCourseAiGraphic/${id}/graphic/add`, { loading: true, params })
// 删除ai文章
export const deleteAIArticle = (id) => http.post(envName.trainingPath + `api/ql/manage/netCourseAiGraphic/${id}/graphic/del`, { loading: true })
// 推广
export const popularizeApi = (params) => http.get(envName.trainingPath + `api/business-common/user/banner/get-last-update-course`, { params, loading: false })
// 移动端纪要按钮开关
export const setSummaryApi = (id, params) => http.post(envName.trainingPath + `api/netcourse/user/courseinfo/${id}/set-brief-minutes-button`, { params, loading: true })
// 获取纪要开关状态
export const getSummaryApi = (id) => http.get(envName.trainingPath + `api/netcourse/user/courseinfo/${id}/get-brief-minutes-button`, { loading: true })

/* 
活动 
*/
// 活动详情
export const getActivityInfo = (params) => http.get(`${envName.trainingPath}api/activity/user/activityInfo`, { params, loading: true })
// 活动报名
export const setMemberStatus = (params) => http.post(`${envName.trainingPath}api/activity/user/set-member-status`, { params, loading: true })
// 活动转态
export const getActivityStatus = (params) => http.get(`${envName.trainingPath}api/activity/user/activity-status`, { params, loading: true })
// 查询是否收藏活动
export const activityCheckFavorite = (params) => http.get(`${envName.trainingPath}api/activity/user/check-favorited`, { params, loading: true })
export const activityAddFavorite = (params) => http.get(`${envName.trainingPath}api/activity/user/add-favorite`, { params, loading: true })
export const activityDeleteFavorite = (params) => http.get(`${envName.trainingPath}api/activity/user/delete-favorite`, { params, loading: true })
// 获取是否是跳转到活动页面的灰度成员
export const getGrayTarget = (params) => http.get(`${envName.trainingPath}api/businessCommon/common/checkTarget`, { params, loading: false })

// 面授课
// 面授课详情
export const getFaceDetails = (params) => http.get(`${envName.trainingPath}/api/faceClass/user/actCourseInfo`, { params, loading: true })
// 课程开班列表
export const actClassList = (params) => http.post(envName.trainingPath + `api/faceClass/user/actClassList`, { loading: true, params })
// 报名状态
export const getClassStatusItem = (params) => http.get(`${envName.trainingPath}/api/faceClass/user/faceClassRegistType`, { params, loading: true })
//  撤销报名
export const logoOutFace = (params) => http.get(`${envName.trainingPath}/api/faceClass/user/faceCancelRegist`, { params, loading: true })
// 报名
export const faceClassRegistApply = (params) => http.post(`${envName.trainingPath}/api/faceClass/user/faceClassRegistApply`, { params, loading: true })
//  获取积分
export const getFaceCancelAcct = (params) => http.get(`${envName.trainingPath}/api/faceClass/user/faceCancelAcct`, { params, loading: true })
//  开班提醒
export const setSubscribe = (params) => http.get(`${envName.trainingPath}/api/faceClass/user/subscribe`, { params, loading: true })
// 面授课是否收藏
export const faceCheckFavorited = (params) => http.get(`${envName.trainingPath}api/faceClass/user/check-favorited`, { params, loading: true })
// 添加收藏
export const faceAddFavorite = (params) => http.get(`${envName.trainingPath}api/faceClass/user/add-favorite`, { params, loading: true })
// 取消收藏
export const faceDeleteFavorite = (params) => http.get(`${envName.trainingPath}api/faceClass/user/delete-favorite`, { params, loading: true })
// 听音频页面 小程序/h5 同步推广列表已经点击的数据(查询)
export const getPopularizeVisiList = () => http.get(`${envName.trainingPath}api/netcourse/user/courseinfo/get-click-record`, { loading: false })
// 听音频页面 小程序/h5 同步推广列表已经点击的数据(保存)
export const setPopularizeVisiList = (params) => http.post(`${envName.trainingPath}api/netcourse/user/courseinfo/set-click-record`, { params, loading: false })
// 搜索人员信息
export const getBaseEmpInfo = (params) => http.get(`${envName.trainingPath}api/faceClass/user/base-emp-info`, { params, loading: true })
// 聚合报名页
export const getPolymerInfo = (params) => http.get(`${envName.trainingPath}api/polymer/user/polymer/detail`, { params, loading: true })
// 报名列表
export const getPolymerInList = (params) => http.get(`${envName.trainingPath}api/activity/user/polymer/polymer-class-list`, { params, loading: true })
// 聚合报名页提交班级活动
export const polymerRegist = (params) => http.post(`${envName.trainingPath}api/polymer/user/polymer/regist`, { params, loading: true })

// 榜单广场设置
// 添加内容
export const addContent = (params) => http.post(envName.portalAdapter + `area/manage/page/rank/year-rank/add-course`, { loading: true, params })
// 配置列表
export const rankQuery = (params) => http.get(`${envName.portalAdapter}area/manage/page/rank/year-rank/get-rank-list`, { params, loading: true })
// 删除
export const deleteRank = (params) => http.post(`${envName.portalAdapter}area/manage/page/rank/year-rank/del-course`, { params, loading: true })
// 排序
export const sortRank = (params) => http.post(`${envName.portalAdapter}area/manage/page/rank/year-rank/update-order-no`, { params, loading: true })
// 编辑
export const editRank = (params) => http.post(`${envName.portalAdapter}area/manage/page/rank/year-rank/update-rank`, { params, loading: true })

// 文章解析
export const realtimeDocParse = (params) => http.get(`${envName.commonPath}training-portal-common/api/user/ai-bot/realtime-doc-parse`, { params, loading: false })

// 导师
// 获取员工状态信息
export const getTutorStatus = () => http.get(`${envName.trainingPath}api/tutor/user/status`, { loading: true })
// 认证要求检查
export const certificationRiskCheck = () => http.get(`${envName.trainingPath}api/tutor/user/certificationRiskCheck`, { loading: true })
// 查询当前用户部门和BG管理员信息
export const currAdmins = () => http.get(`${envName.trainingPath}api/tutor/user/currAdmins`, { loading: true })

// 素材智能章节
export const getMaterialAiChapters = (params) => http.get(envName.trainingPath + 'api/businessCommon/manage/pub-file/getAiChapters', { params })
// 素材人工章节
export const getMaterialChapters = (params) => http.get(envName.trainingPath + 'api/businessCommon/manage/pub-file/getChapters', { params })
// 保存素材人工章节
export const saveMaterialChapters = (params) => http.post(envName.trainingPath + 'api/businessCommon/manage/pub-file/saveChapters', { params })
// 素材人工文章
export const getMaterialGraphic = (params) => http.get(envName.trainingPath + 'api/businessCommon/manage/pub-file/getGraphic', { params })
// 保存素材人工文章
export const saveMaterialGraphic = (params, file_id) => http.post(envName.trainingPath + 'api/businessCommon/manage/pub-file/saveGraphic?file_id=' + file_id, { params })
// 搜索
export const searchCourse = (params) => http.post(`${envName.commonPath}training-portal-area/api/area/manage/content/searchsite`, { params, loading: true })
// 获取搜索的课程id
export const getSearchCourseId = (params) => http.get(`${envName.commonPath}training-portal-area/api/area/content-classify/module-info`, { params, loading: true })
