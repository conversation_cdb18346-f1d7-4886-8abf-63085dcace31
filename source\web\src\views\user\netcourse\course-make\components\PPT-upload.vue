<template>
  <div class="ppt-upload">
    <el-upload
      v-show="ppt_id === '' && !uploading && !approveStatus"
      class="upload-box"
      action=""
      accept=".pptx"
      :drag="true"
      :with-credentials="true"
      :http-request="onUpload"
      :file-list="fileList"
      :show-file-list="false"
    >
      <el-button type="text" id="ppt-upload-btn">点击上传</el-button>
      <span class="split-line">/</span>
      <span class="upload-text">拖拽到此区域上传文件</span>
    </el-upload>
    <div class="on-upload" v-show="uploading">
      <div class="progress">
        <div class="inner">
          <span class="status">{{
            percent === 100 ? '上传完成' : '上传中'
          }}</span>
          <span class="percent" v-show="percent < 100">{{ percent }}%</span>
          <i class="el-icon-check icon" v-show="percent === 100"></i>
        </div>
      </div>
      <el-progress
        :percentage="percent"
        :stroke-width="4"
        color="#0052d9"
        :show-text="false"
      ></el-progress>
    </div>
    <div class="has-upload" v-show="(ppt_id !== '' && !uploading) || approveStatus">
      <div class="ppt-name">
        <span>{{ fileData.file_name || '-' }}</span>
        <i class="el-icon-check icon" v-if="ppt_id !== ''"></i>
      </div>
      <div class="ppt-size">
        <span>文件大小: </span>
        <span>{{ fileData.file_size || '-' }}</span>
      </div>
      <div class="upload-date">
        <span>上传日期: </span>
        <span>{{ fileData.upload_time || '-' }}</span>
      </div>
      <div class="btn-box" v-if="!approveStatus">
        <el-button type="text" @click="triggerUpload">重新上传</el-button>
        <el-button type="text" @click="deletePPT">删除</el-button>
      </div>
    </div>
    <div class="upload-tips">
      <p class="title">PPT上传指引</p>
      <p>- 仅支持不大于<span class="warn-text">100MB</span>，<span class="warn-text">后缀为.pptx</span>的文件。</p>
      <p>- PPT的备注内容即为语音合成时所使用的文本，<span class="warn-text">请确保PPT每页均有备注，无备注页在视频中将被快速跳过。</span></p>
      <p>- 备注文本避免使用特殊字符，<span class="warn-text">英文单词全部字母大写，系统会读出每个字母，若单词首字母大写，系统则以单词形式读出，</span>请根据需求提前编写。</p>
      <p>- <span class="warn-text">不支持PPT动画效果，动画效果请分多页展示，备注亦需逐页填写。</span></p>
    </div>
    <el-button
      type="primary"
      size="small"
      :disabled="ppt === null"
      class="analysis-ppt"
      :loading="loading"
      @click="onAnalysis"
      >解析PPT和备注文本</el-button
    >
    <div class="result-text">解析结果</div>
    <div class="no-data" v-show="ppt_text.ppt_imgs.length === 0">暂无内容</div>
    <div
      class="result-data"
      id="ppt-area"
      @scroll="onWordScroll"
      v-show="ppt_text.ppt_imgs.length > 0"
    >
      <div class="word-scroll-box">
        <div class="word-mask"></div>
        <div
          class="ppt-list"
          v-for="(item, index) in ppt_text.ppt_imgs"
          :key="item.PageNo"
        >
          <span class="ppt-index">{{ item.PageNo }}</span>
          <el-image 
            style="width: 100px; height: 100px"
            :src="item.TgtUri" 
            :preview-src-list="pptImgList">
          </el-image>
          <span class="ppt-text">{{ ppt_text.ppt_notes[index] }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { analysisPPT } from 'config/api.conf'

export default {
  name: 'voice-select',
  props: {
    virtualInfo: {
      type: Object,
      default: () => {}
    },
    approveStatus: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      ppt: null,
      ppt_id: '',
      percent: 0,
      uploading: false,
      loading: false,
      fileData: {
        file_name: '',
        file_size: '',
        upload_time: ''
      },
      ppt_text: {
        ppt_imgs: [],
        ppt_notes: [],
        cos_prefix: '',
        request_id: ''
      }
    }
  },
  watch: {
    virtualInfo(val) {
      if (val.ppt_text) {
        this.ppt_text = JSON.parse(val.ppt_text)
      }
      if (val.ppt_id) {
        this.ppt_id = val.ppt_id
        this.fileData = {
          file_name: val.file_name,
          file_size: val.file_size,
          upload_time: val.upload_time
        }
      }
      if (val.request_id) {
        this.$emit('onPPTChange', {
          file: null,
          ...this.fileData,
          ppt_id: val.ppt_id,
          request_id: val.request_id,
          ppt_text: val.ppt_text,
          pptImg: this.ppt_text.ppt_imgs[0].TgtUri
        })
      }
    },
    ppt_text: {
      handler() {
        // 初始化遮罩层位置
        this.$nextTick(() => {
          this.onWordScroll()
        })
      },
      immediate: true
    }
  },
  computed: {
    pptImgList() {
      return this.ppt_text.ppt_imgs.map((v) => v.TgtUri)
    }
  },
  methods: {
    onUpload(options) {
      const that = this
      const file_name = options.file.name
      const arr = file_name.split('.')
      const fileType = arr[arr.length - 1]
      if (!['pptx'].includes(fileType)) {
        this.$message.error('请选择.pptx文件')
        return
      }
      const size = options.file.size / 1024 / 1024 > 100
      if (size) {
        this.$message.error('文件大小超过100M，请重新上传')
        return
      }
      this.uploading = true
      const authUrl = location.hostname.endsWith('.woa.com')
        ? process.env.VUE_APP_PORTAL_HOST_WOA
        : process.env.VUE_APP_PORTAL_HOST
      /* eslint-disable*/
      new contentCenter.uploadFile({
        file: options.file,
        type: 3, // 0表示上传图片，1视频 2音频 3文档
        appId: 'QLearningService',
        operateAuthUrl: `${authUrl}/training-portal-common/api/v1/portal/user/common/uploadOperateSignature`,
        onSuccess(res) {
          that.ppt = options.file
          that.ppt_text = {
            ppt_imgs: [],
            ppt_notes: [],
            cos_prefix: '',
            request_id: ''
          }
          that.ppt_id = res[0] && res[0].content_id
          that.fileData = {
            file_name,
            file_size: Math.ceil((options.file.size / 1048576) * 10) / 10 + 'M', // 保留1位小数
            upload_time: that.getDate()
          }
          that.$emit('onPPTChange', {
            file: that.ppt,
            ...that.fileData,
            ppt_id: that.ppt_id,
            request_id: '',
            ppt_text: JSON.stringify(that.ppt_text),
            pptImg: ''
          })
        },
        onError(err) {
          this.uploading = false
          that.$message.error(err)
        },
        onProgress(info) {
          that.percent = parseInt(info.percent * 10000) / 100
          if (that.percent === 100) {
            setTimeout(() => {
              that.percent = 0
              that.uploading = false
            }, 200)
          }
        }
      })
    },
    onAnalysis() {
      this.loading = true
      // 部署流程定义（点击按钮，上传bpmn文件，上传成功后部署，然后重新加载列表）
      analysisPPT({
        app_id: 'QLearningService',
        file: this.ppt
      })
        .then((res) => {
          this.ppt_text = res
          this.$emit('onPPTChange', {
            file: this.ppt,
            ...this.fileData,
            ppt_id: this.ppt_id,
            request_id: res.request_id,
            ppt_text: JSON.stringify(res),
            pptImg: res.ppt_imgs[0].TgtUri
          })
          this.loading = false
        })
        .catch(() => {
          this.loading = false
        })
    },
    getDate() {
      const now = new Date()
      const year = now.getFullYear()
      const month = now.getMonth() + 1
      const date = now.getDate()
      let h = now.getHours()
      let m = now.getMinutes()
      let s = now.getSeconds()
      h = h < 10 ? '0' + h : h
      m = m < 10 ? '0' + m : m
      s = s < 10 ? '0' + s : s
      return year + '/' + month + '/' + date + ' ' + h + ':' + m + ':' + s
    },
    triggerUpload() {
      document.getElementById('ppt-upload-btn').click()
    },
    deletePPT() {
      this.ppt = null
      this.ppt_id = ''
      this.fileList = []
      this.ppt_text = {
        ppt_imgs: [],
        ppt_notes: [],
        cos_prefix: '',
        request_id: ''
      }
      this.$emit('onPPTChange', {})
    },
    // 滚动的时候固定遮罩
    onWordScroll() {
      const el = document.getElementById('ppt-area')
      if (el) {
        document.querySelector('.word-mask').style.top =
          el.scrollTop + (el.clientHeight - 112) + 'px'
      }
    }
  }
}
</script>

<style lang="less" scoped>
.ppt-upload {
  font-size: 14px;
  :deep(.upload-box) {
    .el-upload {
      width: 100%;
      height: 100%;
      .el-upload-dragger {
        width: 100%;
        height: 100%;
        line-height: 144px;
        border: unset;
      }
      .split-line {
        margin: 0 8px;
        color: #333;
      }
      .upload-text {
        color: #666;
      }
    }
  }
  .upload-box,
  .on-upload,
  .has-upload {
    height: 144px;
    box-sizing: border-box;
    border-radius: 3px;
    border: 1px dashed #dcdcdc;
  }
  .on-upload {
    text-align: center;
    .progress {
      margin-top: 45px;
      line-height: 22px;
      .inner {
        display: inline-block;
        width: 246px;
        text-align: left;
        .status {
          margin-right: 8px;
          color: #666;
        }
        .icon {
          border-radius: 50%;
          border: unset;
          background-color: #00a870;
          color: #fff;
        }
      }

      .percent {
        color: #0052d9;
      }
    }
    :deep(.el-progress) {
      display: inline-block;
      width: 246px;
      .el-progress-bar {
        .el-progress-bar__outer {
          background-color: #f2f2f2;
        }
      }
    }
  }
  .has-upload {
    padding: 16px;
    .ppt-name {
      margin-bottom: 8px;
      color: #333;
      line-height: 22px;
      .icon {
        font-size: 12px;
        padding: 2px;
        margin-left: 17px;
        border-radius: 50%;
        border: unset;
        background-color: #00a870;
        color: #fff;
        transform: scale(0.88);
      }
    }
    .ppt-size,
    .upload-date {
      line-height: 20px;
      font-size: 12px;
      color: #999;
    }
    .btn-box {
      margin-top: 20px;
      .el-button {
        margin-right: 6px;
      }
    }
  }
  .upload-tips {
    margin: 8px 0 16px 0;
    line-height: 22px;
    font-size: 12px;
    color: #999;
    .title {
      color: #333;
      font-weight: bold;
    }
    .warn-text{
      color: #d63535;
    }
  }
  .analysis-ppt {
    font-size: 14px;
  }
  .result-text {
    margin: 24px 0 10px 0;
    font-weight: bold;
    line-height: 20px;
    color: #333;
  }
  .result-data {
    padding: 12px 20px 12px 12px;
    max-height: 306px;
    overflow-y: auto;
    border-radius: 3px;
    border: 1px solid #eee;
    font-size: 12px;
    .word-scroll-box {
      position: relative;
    }
    .ppt-list {
      margin-bottom: 20px;

      display: flex;
      &:last-of-type {
        margin-bottom: 0;
      }
      .ppt-index {
        color: #999;
      }
      img {
        margin: 0 16px 0 8px;
        width: 110px;
        height: 60px;
        box-shadow: 0 0 4px 0 #eee;
      }
      .ppt-text {
        flex: 1;
        color: rgba(0, 0, 0, 0.6);
      }
    }
    .word-mask {
      position: absolute;
      width: 100%;
      height: 100px;
      top: 0;
      left: 0;
      background: linear-gradient(
        180deg,
        rgba(217, 217, 217, 0) 0%,
        rgba(249, 249, 249, 0.4) 100%
      );
    }
  }
  .no-data {
    font-size: 12px;
    color: #999;
  }
  :deep(.el-image-viewer__wrapper) {
    .el-image-viewer__close {
      color: #fff;
      opacity: 0.6;
    }
  }
}
</style>
