<template>
  <div class="left-menu-container">
    <div class="avatar-box">
      <el-avatar size="large" class="user-avatar" :src="avatar">
        <img :src="defaultAvatar" />
      </el-avatar>
      <p>{{$store.state.userInfo.staff_name}}</p>
    </div>
    <div class="share-btn-box">
      <div class="share-btn">
        <span class="text-left">
          <span class="share-icon"></span>
          <span>我要分享</span>
        </span>
        <div class="i-right">
          <i class="el-icon-caret-bottom"></i>
          <div class="share-card-content">
            <div class="triangle"></div>
            <div class="item-card">
              <ul>
                <li
                  v-for="item in shareList"
                  :key="item.icon"
                  class="item-li"
                  @click="handleShare(item)"
                >
                  <span :class="['li-icon',  `${item.icon}`]"></span>
                  {{item.label}}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="menu-box">
      <ul class="menu-ul">
        <div class="menu-f">
          <span class="menu-f-icon"></span>内容管理
        </div>
        <li
          :class="{'active-li': $route.name === item.routerName}"
          v-for="item in courseList"
          :key="item.name"
          @click="handleRouterPath(item)"
          appKey="learn-credit-service"
          :x-operation-code="item.permissionCode"
          x-operation-model="disable"
        >
          {{ item.name }}
          <br />
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { getAvatar } from 'utils/tools'
export default {
  props: {
    shareList: {
      type: Array,
      default: () => [
        {
          icon: 'ai-icon',
          label: 'AI快捷做课',
          testUrl:
            '//test-portal-learn.woa.com/training/creator-center/course-share',
          prdUrl: '//portal.learn.woa.com/training/creator-center/course-share'
        },
        {
          icon: 'img-icon',
          label: '发表图文',
          testUrl: '//test-portal-learn.woa.com/training/graphic/user/create',
          prdUrl: '//portal.learn.woa.com/training/graphic/user/create'
        },
        {
          icon: 'ask-icon',
          label: '行家轻咨询',
          testUrl:
            '//hangjia.woa.com/blank-main/hangj_main-page/expert-step-one',
          prdUrl: '//hangjia.woa.com/blank-main/hangj_main-page/expert-step-one'
        },
        {
          icon: 'send-icon',
          label: '发表案例',
          testUrl: '//anli.woa.com',
          prdUrl: '//anli.woa.com'
        },
        {
          icon: 'teach-icon',
          label: '认证讲师/课程',
          testUrl: '//lec.learn.oa.com/#/staff/authentication',
          prdUrl: '//lec.learn.oa.com/#/staff/authentication'
        }
      ]
    },
    courseList: {
      type: Array,
      default: () => [
        {
          id: 1,
          name: '我的活动',
          routerName: 'activityList',
          testUrl: '',
          prdUrl: ''
          // permissionCode: 'QLV8_Activity'
        },
        {
          id: 4,
          name: '我的文章',
          routerName: 'graphic-page',
          testUrl: '',
          prdUrl: ''
          // permissionCode: 'Graphic_Mgt'
        },
        {
          id: 3,
          name: '我的在线课程',
          routerName: '',
          testUrl: 'https://test-portal-learn.woa.com/training/creator-center/course-list',
          prdUrl: 'https://portal.learn.woa.com/training/creator-center/course-list'
          // permissionCode: 'QLV8_MFFC'
        },
        {
          id: 5,
          name: '我的案例',
          routerName: '',
          testUrl: 'https://anli.woa.com/case/main/csgl_case-front/case-personal',
          prdUrl: 'https://anli.woa.com/case/main/csgl_case-front/case-personal'
          // permissionCode: 'Case'
        },
        {
          id: 7,
          name: '行家工作台',
          routerName: '',
          testUrl: 'https://hangjia.woa.com/blank-main/hangj_hangj-my/my-menu?hangjNotice=true',
          prdUrl: 'https://hangjia.woa.com/blank-main/hangj_hangj-my/my-menu?hangjNotice=true'
          // permissionCode: 'Hangjia'
        }
        // {
        //   id: 8,
        //   name: '讲师工作台',
        //   routerName: '',
        //   testUrl: 'https://lec.learn.woa.com/#/staff/apply',
        //   prdUrl: 'https://lec.learn.woa.com/#/staff/apply',
        //   permissionCode: 'M_Inst'
        // },
        // {
        //   id: 2,
        //   name: '班级管理',
        //   routerName: '',
        //   testUrl: '//test-learn.woa.com/manage/class',
        //   prdUrl: '//learn.woa.com/manage/class',
        //   permissionCode: 'QLV8_CLASS_MGT'
        // },
        // {
        //   id: 6,
        //   name: '网络课管理',
        //   routerName: 'courseList',
        //   testUrl: '',
        //   prdUrl: ''
        //   // permissionCode: 'Net_Course_Mgt'
        // }
      ]
    }
  },
  data() {
    return {
      avatar: require('@/assets/img/avatar.png'),
      defaultAvatar: require('@/assets/img/avatar.png')
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          this.avatar = getAvatar(this.$store.state.userInfo.staff_name)
        }
      },
      immediate: true
    }
  },
  methods: {
    handleRouterPath(row) {
      let { routerName, testUrl, prdUrl } = row
      let query = {}
      if (routerName) {
        if (this.$route.query.page_id) {
          query = {
            page_id: this.$route.query.page_id
          }
        }
        this.$router.push({
          name: routerName,
          query
        })
      } else if (testUrl || prdUrl) {
        let url = process.env.NODE_ENV === 'production' ? prdUrl : testUrl
        if (this.$route.query.page_id) {
          url += '?page_id=' + this.$route.query.page_id
        }
        window.open(url)
      } else {
        this.$message.warning('暂无跳转链接')
      }
    },
    handleShare({ testUrl, prdUrl }) {
      const url = process.env.NODE_ENV === 'production' ? prdUrl : testUrl
      window.open(url)
    }
  }
}
</script>

<style scoped lang="less">
.left-menu-container {
  position: sticky;
  top: 78px;
  max-height: 80vh;
  min-height: 40vh;
  width: 180px;
  background-color: #fff;
  z-index: 99;
  padding: 16px 0 0 0;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  .avatar-box {
    color: rgba(0, 0, 0, 0.8);
    text-align: center;
    margin-bottom: 12px;
    :deep(.el-avatar) {
      width: 56px;
      height: 56px;
      border-radius: 50%;
      margin-bottom: 10px;
    }
  }
  .share-btn-box {
    width: 156px;
    line-height: 36px;
    position: relative;
    margin: 0 auto;
    padding-bottom: 16px;
    padding-top: 16px;
    border-bottom: 1px solid #eeeeee;
    cursor: pointer;
    .share-btn {
      border-radius: 28px;
      color: #fff;
      text-align: center;
      line-height: 36px;
      font-weight: 500;
      display: flex;
    }
    .text-left {
      width: 110px;
      border-radius: 28px 0 0 28px;
      background-color: #0052d9;
      position: relative;
      display: flex;
      align-items: center;
    }
    .text-left:hover,
    .i-right:hover {
      background-color: #0d5fe7;
    }
    .i-right {
      display: inline-block;
      background-color: #0052d9;
      width: 46px;
      border-radius: 0 28px 28px 0;
      i {
        float: right;
        margin-right: 20px;
        margin-top: 10px;
      }
    }
    .i-right:hover {
      i {
        transform: rotate(-180deg);
        transition: transform 0.3s;
      }
      .share-card-content {
        visibility: visible;
        opacity: 1;
      }
    }
    .share-icon {
      display: inline-block;
      width: 16px;
      height: 16px;
      margin-right: 8px;
      margin-left: 20px;
      background: url('~@/assets/img/share-icon.png') no-repeat center/cover;
    }
    .text-left::after {
      content: '';
      position: absolute;
      top: 12px;
      width: 1px;
      height: 12px;
      background-color: #6198fc;
      right: -0.5px;
      opacity: 1;
    }
    .el-icon-caret-bottom {
      font-size: 16px;
      color: #fff;
    }
    .share-card-content {
      position: absolute;
      right: -180px;
      top: 16px;
      width: 164px;
      height: 210px;
      box-shadow: 0 2px 12px 0 #0000001a;
      padding: 8px 6px;
      border-radius: 8px;
      background-color: #fff;
      visibility: hidden; // 隐藏
      opacity: 0; // 隐藏
      transition: all 0.6s ease-in-out;
      .triangle {
        // 三角型
        width: 0;
        height: 0;
        border-top: 15px solid transparent;
        border-right: 20px solid #fff;
        border-bottom: 15px solid transparent;
        left: -10px;
        top: 5px;
        position: absolute;
        z-index: -1;
      }
      li + li {
        margin-top: 4px;
      }
      .item-li {
        display: flex;
        align-items: center;
        color: #000000;
        height: 36px;
        padding: 0 12px;
        cursor: pointer;
      }
      .item-li:hover {
        background-color: rgba(243, 245, 247, 1);
        color: #0052d9;
      }
      .li-icon {
        width: 20px;
        height: 20px;
        margin-right: 8px;
      }
      .ai-icon {
        background: url('~@/assets/img/li-ai-icon.png') no-repeat center/cover;
      }
      .img-icon {
        background: url('~@/assets/img/li-img-icon.png') no-repeat center/cover;
      }
      .ask-icon {
        background: url('~@/assets/img/li-ask-icon.png') no-repeat center/cover;
      }
      .send-icon {
        background: url('~@/assets/img/li-send-icon.png') no-repeat center/cover;
      }
      .teach-icon {
        background: url('~@/assets/img/li-teach-icon.png') no-repeat
          center/cover;
      }
    }
  }
  .share-btn:hover {
    .text-left::after {
      opacity: 0;
    }
  }
  .menu-box {
    margin-top: 16px;
    flex: 1;
    overflow: hidden;
    .menu-ul {
      overflow-y: auto;
      height: 100%;
      padding-bottom: 30px;
      .menu-f-icon {
        display: inline-block;
        width: 20px;
        height: 20px;
        background: url('~@/assets/img/menu-icon.png') no-repeat center/cover;
        margin-right: 4px;
      }
      .menu-f {
        display: flex;
        align-items: center;
        padding-left: 20px;
        height: 36px;
        font-weight: bold;
      }
      li + li {
        margin-top: 4px;
      }
      li {
        padding-left: 32px;
        cursor: pointer;
        height: 36px;
        line-height: 36px;
        width: 156px;
        margin: auto;
        color: rgba(0, 0, 0, 0.6);
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      li:hover {
        background-color: #f5f7f9;
        color: #0052d9;
      }
      .active-li {
        background-color: #f5f7f9;
        color: #0052d9;
      }
    }
  }
}
</style>
