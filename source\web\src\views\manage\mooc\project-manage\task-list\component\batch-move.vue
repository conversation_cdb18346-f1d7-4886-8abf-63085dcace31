<template>
  <div class="batch-move-dialog">
    <el-dialog 
    width="430px" 
    :visible="visible" 
    :title="diaglogTitle" 
    :close-on-click-modal="false" 
    :before-close="cancel"
    >
      <div class="move-body">
        <p class="num">{{ taskTips }}</p>
        <div class="radio-box">
          <span class="label">移动至</span>
          <el-radio-group v-model="statusType" @change="initStage">
            <el-radio v-for="item in newGroupList" :label="item.label" :key="item.label">{{ item.labelName }}</el-radio>
          </el-radio-group>
        </div>
        <el-select
          v-if="statusType !== 3"
          v-model="stage" 
          placeholder="请选择" 
          class="stage"
          style="width:100%"
          >
          <el-option
            v-for="item in stageList"
            :key="item.id"
            :label="item.task_name"
            :value="item.id"
            >
          </el-option>
        </el-select>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取 消</el-button>
        <el-button @click="submit" size="small" type="primary">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    treeNode: {
      type: Array,
      default: () => ({})
    },
    moveInfo: {
      type: Object
    }
  },
  data() {
    return {
      statusType: 1,
      stage: '',
      stageList: [],
      groupList: [
        { label: 1, labelName: '阶段' },
        { label: 2, labelName: '任务组' },
        { label: 3, labelName: '根节点' }
      ]
    }
  },
  computed: {
    newGroupList() {
      const { type } = this.moveInfo
      const list = [
        { label: 1, labelName: '阶段' },
        { label: 3, labelName: '根节点' }
      ]
      return type === 'group' ? list : this.groupList
    },
    diaglogTitle() {
      const { type } = this.moveInfo
      return type === 'group' ? '移动任务组' : type === 'task' ? '移动任务' : '批量移动任务'
    },
    taskTips() {
      const { type, taskName, num } = this.moveInfo
      return ['group', 'task'].includes(type) ? taskName : `已选择${num || 0}个任务`
    }
  },
  mounted() {
    this.initStage()
  },
  methods: {
    initStage() {
      this.stage = ''
      if (this.statusType === 1) { // 阶段
        this.stageList = this.treeNode.filter((e) => e.task_type === 'stage')
      } else if (this.statusType === 2) { // 任务组
        let list = []
        const formatGroup = (data = []) => {
          data.forEach((e) => {
            if (e.task_type === 'group') {
              list.push(e)
            } else {
              if (e.sub_tasks?.length) {
                formatGroup(e.sub_tasks)
              }
            }
          })
          return list
        }
        this.stageList = formatGroup(this.treeNode)
      }
    },
    submit() {
      if (this.statusType !== 3 && !this.stage) {
        this.$message.warning('请选择阶段或任务组')
        return
      }
      this.$emit('handleBatchMove', this.statusType, this.stage)
      this.cancel()
    },
    cancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.batch-move-dialog {
  .move-body {
    .num {
      color: rgba(0, 0, 0, 0.6);
    }
    .label {
      color: rgba(0, 0, 0, 0.6);
      margin-right: 12px;
    }
    .radio-box {
      margin-top: 24px;
      margin-bottom: 24px;
    }
  }
}
</style>
