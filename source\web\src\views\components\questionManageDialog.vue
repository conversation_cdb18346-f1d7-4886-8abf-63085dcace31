<template>
  <div>
    <el-dialog
      custom-class="question-m-dialog"
      :class="{ 'check-data-mode': otherOption.mode === 'checkData' }"
      :visible.sync="visible"
      :show-close="true"
      :close-on-click-modal="false"
      :before-close="close"
    >
      <template slot="title">
        <div class="header">
          <p v-if="otherOption.mode !== 'checkData'">{{ type === 'font' ? '课前问卷编辑' : '课后问卷编辑' }}</p>
          <p v-else>{{ type === 'font' ? '查看课前统计数据详情' : '查看课后统计数据详情' }}</p>
          <p>活动名称：{{ displayActivityName }}</p>
        </div>
      </template>
      <iframe :src="questionSrc" class="questionIframe" frameborder="0" id="questionIframe"></iframe>
      <template slot="footer">
        <div class="footer" v-if="otherOption.mode !== 'checkData'">
          <div class="footer-tips">
            <i class="icon"></i>
            如需收集统计学员对内容、讲师、组织的评价，
            <a
              href="https://doc.weixin.qq.com/doc/w3_AP0ATAbdAFw8RhX8I0qQqS9hZ65Cs?scode=AJEAIQdfAAoUxvdpkzAZsAdQYpAIQ"
              target="_blank"
            >点此查看配置指引</a>。
          </div>
          <div class="footer-btn">
            <el-button type size="small" @click="close" class="cancel-btn">取消</el-button>
            <el-button
              type="primary"
              size="small"
              @click="confirm"
              class="save-btn"
              :disabled="submitDisabled"
            >{{ questionType === 'add' ? '保存并关联问卷' : '保存修改'}}</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
    <remind
      v-if="isShowRemind"
      :visible.sync="isShowRemind"
      @getRemindInfo="getRemindInfo"
      :isShowStartMode="true"
      :remindInfo="remindInfo"
    ></remind>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import remind from '@/views/manage/classroom/activity/edit/components/remind.vue'
export default {
  components: {
    remind
  },
  computed: {
    ...mapState({
      activityInfo: state => state.activity.activityInfo
    }),
    questionIframeDom() {
      return document.getElementById('questionIframe')
    },
    displayActivityName() {
      return (
        this.activityName ||
        (this.activityInfo && this.activityInfo.activity_name) ||
        ''
      )
    }
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    questionType: {
      type: String,
      default: 'add' // add or edit or showList
    },
    sid: {
      type: [String, Number],
      default: ''
    },
    type: {
      type: String,
      default: 'font' // font 课前问卷 after 课后问卷 checkData 查看统计数据
    },
    activityName: {
      type: String,
      default: ''
    },
    otherOption: {
      type: Object,
      default: () => ({
        mode: 'normal' // normal 正常模式 checkData 查看统计数据
      })
    }
  },
  watch: {
    visible: {
      handler(newVal) {
        let type = this.questionType
        let { mode, customUrl, isCustomCss = false } = this.otherOption
        if (customUrl) {
          this.questionSrc = customUrl
          return
        }
        // 查看统计数据
        if (mode === 'checkData') {
          this.questionSrc = `${
            this.questionAddress[process.env.NODE_ENV]
          }/stat/1/recycle?sid=${this.sid}&mode=qlearningCheckData`
          return
        }

        if (type === 'edit' || type === 'editFor') {
          this.questionSrc = `${
            this.questionAddress[process.env.NODE_ENV]
          }/edit/v2.html?org=60000000002&scene=1&sid=${this.sid}`
        } else if (type === 'showList') {
          this.questionSrc = `${
            this.questionAddress[process.env.NODE_ENV]
          }/workspace.html?org=60000000002&position=recent&folderId=0`
        } else if (type === 'add') {
          if (this.otherOption.customAdd) {
            this.$emit('createQuestion')
          } else {
            this.questionSrc = `${
              this.questionAddress[process.env.NODE_ENV]
            }/edit/v2.html?org=60000000002&scene=1`
          }
        }

        if (isCustomCss) {
          this.questionSrc = this.questionSrc + '&mode=qlearningCheckData'
        }
      },
      immediate: true
    }
  },
  data() {
    return {
      questionSrc: '',
      messageHandler: null,
      submitDisabled: true,
      questionAddress: {
        test: '//test-wj-learn.woa.com',
        production: '//wj-learn.woa.com'
      },
      isShowRemind: false,
      remindInfo: {
        enabled_remaind: false,
        message_type: '',
        reminder_begin_time: '',
        reminder_end_time: '',
        reminder_fixed_send_times: '',
        reminder_fixed_week_time: '',
        reminder_gap_time: '',
        reminder_send_times: '',
        reminder_task_id: '',
        act_id: ''
      },
      startMode: 1
    }
  },
  mounted() {
    this.messageHandler = this.handleMessage.bind(this)
    window.addEventListener('message', this.messageHandler)
    this.$nextTick(() => {
      this.questionIframeDom.onload = () => {
        this.submitDisabled = false
      }
    })
  },
  destroyed() {
    // 使用保存的引用正确移除事件监听器
    window.removeEventListener('message', this.messageHandler)
  },
  methods: {
    handleMessage(e) {
      const { data } = e
      if (data.events === 'getData' && data.vendor === 'questionnaire') {
        const { title = '', id = '', respondent_url = '' } = data.data
        let questionData = {
          act_name: title,
          resource_url: respondent_url,
          resource_url_mobile: respondent_url,
          act_id: id
        }
        let info = {
          ...questionData,
          ...data.data
        }
        this.getData(info)
      }
      if (data.events === 'error' && data.vendor === 'questionnaire') {
        this.$message.error('问卷数据获取失败！')
      }
    },
    close() {
      this.otherOption.mode = 'normal'
      this.$emit('update:visible', false)
    },
    getData(data) {
      let newData = {
        ...data,
        remindSetting: this.remindInfo,
        startMode: this.startMode,
        openMode: this.questionType
      }
      this.$emit('getQuestionData', newData)
      this.close()
    },
    confirm() {
      if (this.questionType === 'add' && this.type === 'after') {
        this.isShowRemind = true
        // 给催办设置默认值 每一天早上九点催办
        this.remindInfo.enabled_remaind = true
        this.remindInfo.reminder_gap_time = 1
        this.remindInfo.reminder_send_times = '09:00:00'
        this.remindInfo.message_type = 'mail'
      } else {
        this.save()
      }
    },
    getRemindInfo(data) {
      const { remindData, startMode } = data
      this.remindInfo = remindData
      this.startMode = startMode
      if (!this.remindInfo.enabled_remaind) {
        this.remindInfo = {
          message_type: '',
          reminder_begin_time: '',
          reminder_end_time: '',
          reminder_fixed_send_times: '',
          reminder_fixed_week_time: '',
          reminder_gap_time: '',
          reminder_send_times: '',
          reminder_task_id: ''
        }
      }
      this.save()
    },
    save() {
      this.questionIframeDom.contentWindow.postMessage(
        {
          type: 'questionnaire',
          events: 'getData'
        },
        '*'
      )
    }
  }
}
</script>

<style lang="less" scoped>
:deep(.question-m-dialog) {
  height: 900px;
  border-radius: 9px;
  overflow: hidden;
  .el-dialog {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  .el-dialog__body {
    padding: 0;
    height: calc(100% - 130px);
  }
  &.check-data-mode .el-dialog__body {
    height: calc(100% - 71px);
  }
  .el-dialog__footer {
    padding: 0;
  }
  .el-dialog__header {
    height: 64px;
    padding: 0 0 0 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .header {
    display: flex;
    align-items: center;
    p {
      &:first-child {
        font-size: 16px;
        color: #333;
        margin-right: 20px;
        font-weight: 600;
      }
      &:last-child {
        font-size: 14px;
        color: #666;
      }
    }
  }
  .questionIframe {
    width: 100%;
    flex: 1;
    height: 100%;
  }
  .footer {
    height: 60px;
    display: flex;
    align-items: center;
    padding: 0 20px 0 0;

    .footer-tips {
      color: #3464e0;
      margin-left: auto;
      display: flex;
      align-items: center;
      .icon {
        width: 16px;
        height: 16px;
        background: url('~@/assets/img/tips-icon.png') no-repeat center/cover;
        margin-right: 4px;
      }
      a {
        color: #3464e0;
        text-decoration: underline;
      }
    }
    .footer-btn {
      .cancel-btn {
        padding: 5px 26px;
        margin: 0 16px;
        font-size: 14px;
        line-height: 22px;
      }
      .save-btn {
        padding: 5px 16px;
        font-size: 14px;
        line-height: 22px;
      }
    }
  }
}

@media screen and (max-width: 1660px) {
  :deep(.question-m-dialog) {
    width: 1228px;
  }
}

@media screen and (min-width: 1661px) {
  :deep(.question-m-dialog) {
    width: 1420px;
  }
}
</style>
