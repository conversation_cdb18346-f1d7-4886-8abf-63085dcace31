import {
  backtop,
  Button,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Icon,
  RadioGroup,
  RadioButton,
  Checkbox,
  CheckboxGroup,
  Input,
  Loading,
  Menu,
  MenuItem,
  Submenu,
  Select,
  Option,
  Tooltip,
  MessageBox,
  Notification,
  Message,
  Form,
  FormItem,
  Table,
  TableColumn,
  Col,
  Autocomplete,
  Row,
  Dialog,
  datePicker,
  timePicker,
  pagination,
  Avatar,
  Carousel,
  CarouselItem,
  Link,
  Upload,
  Tag,
  Radio,
  Image,
  Tabs,
  TabPane,
  InputNumber,
  Popover,
  Badge,
  InfiniteScroll,
  Cascader,
  Tree,
  Breadcrumb,
  BreadcrumbItem,
  steps,
  step,
  slider,
  progress,
  Switch,
  TimeSelect,
  Rate
} from 'element-ui'
import locale from 'element-ui/lib/locale'
import zh from 'element-ui/lib/locale/lang/zh-CN'
import en from 'element-ui/lib/locale/lang/en'

export default {
  components: [
    backtop,
    Button,
    Dropdown,
    DropdownMenu,
    DropdownItem,
    Icon,
    RadioGroup,
    RadioButton,
    Checkbox,
    CheckboxGroup,
    Input,
    Loading,
    Menu,
    MenuItem,
    Submenu,
    Select,
    Option,
    Tooltip,
    Form,
    FormItem,
    Table,
    TableColumn,
    Col,
    Row,
    Autocomplete,
    Dialog,
    datePicker,
    timePicker,
    pagination,
    Avatar,
    Carousel,
    CarouselItem,
    Link,
    Upload,
    Tag,
    Radio,
    Image,
    Tabs,
    TabPane,
    InputNumber,
    Popover,
    Badge,
    InfiniteScroll,
    Cascader,
    Tree,
    Breadcrumb,
    BreadcrumbItem,
    steps,
    step,
    slider,
    progress,
    Switch,
    TimeSelect,
    Rate
  ],
  install(Vue) {
    Vue.prototype.$messageBox = MessageBox
    Vue.prototype.$notify = Notification
    Vue.prototype.$message = Message
    Vue.prototype.$prompt = Vue.prototype.$messageBox.prompt
    Vue.prototype.$confirm = Vue.prototype.$messageBox.confirm
  },
  locale,
  langs: {
    zh,
    en
  }
}
