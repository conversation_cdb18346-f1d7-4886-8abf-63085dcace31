<!DOCTYPE html>
<html>
<head>
  <title><%= webpackConfig.name %></title>
  <meta charset="utf-8" />
  <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
  <meta http-equiv="Pragma" content="no-cache" />
  <meta http-equiv="Expires" content="0" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1, maximum-scale=1, minimum-scale=1, user-scalable=no, viewport-fit=cover" />
  <link href="<%= BASE_URL %>favicon.ico" rel="icon" />
  <% if (VUE_APP_BUILD_ENV === 'prd') { %>
    <!-- 仅在生产环境中加载外部CSS -->
    <link rel="stylesheet" href="https://portal.learn.woa.com/commonHeaderStyle.css">
    <script src="https://qlportal.ihr.tencent-cloud.com/knowledgeservice/reLogin/reLogin.js?ts=<%= new Date().getTime() %>"></script>
  <% } else { %>
    <!-- 开发环境中可以加载本地的CSS -->
    <link rel="stylesheet" href="//test-portal-learn.woa.com/commonHeaderStyle.css">
    <script src="https://cdnyewutest.yunassess.com/knowledgeservice/reLogin/reLogin.js?ts=<%= new Date().getTime() %>"></script>
  <% } %>
  <script>window.SDC_BUILD_ENV = '<%= VUE_APP_BUILD_ENV %>'</script>
  <!-- <script src="https://cdn-go.woa.com/TEditor/TEditor/latest/index.min.js"></script> -->
  <!-- <link rel="stylesheet" href="https://cdn-go.woa.com/TEditor/TEditor/latest/index.min.css"></link> -->
</head>

<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>