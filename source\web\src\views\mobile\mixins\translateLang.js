
export default {
  methods: {
    getMobileLangJS() {
      let lang = localStorage.getItem('sdc-sys-def-lang')
      // 加载中英文js
      let langScript = document.createElement('script')
      let langDom = document.getElementById('mobileLangScript')
      if (langDom) {
        let parent = langDom.parentNode
        parent.removeChild(langDom)
      }
      langScript.type = 'text/javascript'
      langScript.value = 'translate'
      langScript.id = 'mobileLangScript'
      if (process.env.NODE_ENV === 'production') {
        langScript.src = lang === 'zh-cn' ? '//cdn.multilingualres.hr.tencent.com/MOOC/All_zh-cn.js' : '//cdn.multilingualres.hr.tencent.com/MOOC/All_en-us.js'
      } else {
        langScript.src = lang === 'zh-cn' ? '//cdntestmultilingualres.yunassess.com/MOOC/All_zh-cn.js' : '//cdntestmultilingualres.yunassess.com/MOOC/All_en-us.js'
      }
      document.getElementsByTagName('head')[0].appendChild(langScript)
      langScript.onload = langScript.onreadystatechange = (res) => {
        this.$i18n.locale = lang
        this.$i18n.setLocaleMessage(lang, window.mooc_All)
      }
    }
  }
}
