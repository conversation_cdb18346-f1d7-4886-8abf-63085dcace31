<template>
  <div class="questionPage">
    <iframe :src="questionSrc" class="questionIframe" frameborder="0" id="questionIframe"></iframe>
    <bottomFiexd @cancel="onCancel" @save="onSubmit" submitText="保存并关联问卷"></bottomFiexd>
  </div>
</template>

<script>
import bottomFiexd from '@/views/components/botttomFixed.vue'
export default {
  components: {
    bottomFiexd
  },
  data () {
    return {
      questionSrc: 'https://wj-learn.woa.com/edit/v2.html?org=60000000002&scene=1',
      questionType: 'add', // add or edit or showList
      sid: '',
      questionData: '',
      dataId: '',
      mode: ''
    }
  },
  mounted () {
    this.questionType = this.$route.query.type
    this.dataId = this.$route.query.dataId
    if (this.questionType === 'edit' || this.questionType === 'editFor') {
      this.sid = this.$route.query.sid
      this.questionSrc = `https://wj-learn.woa.com/edit/v2.html?org=60000000002&scene=1&sid=${this.sid}&mode=${this.questionType === 'edit' ? 'normal' : 'qlearningCheckData'}`
    }
    if (this.questionType === 'showList') {
      this.questionSrc = `https://wj-learn.woa.com/workspace.html?org=60000000002&position=recent&folderId=0`
    }
    window.addEventListener('message', (e) => {
      const { data } = e
      if (data.events === 'getData' && data.vendor === 'questionnaire') {
        this.saveInfo(data.data)
      }
      if (data.events === 'error' && data.vendor === 'questionnaire') {
        this.$message.error('问卷数据获取失败，请确认当前是否在问卷页内！')
      }
    })
  },
  computed: {
    questionIframeDom () {
      return document.getElementById('questionIframe')
    }
  },
  methods: {
    onCancel() {
      setTimeout(() => {
        window.close()
      }, 200)
    },
    saveInfo(info) {
      console.log(info, '保存时的info')
      let taskName = this.$route.query.taskName || ''
      let cutmonId = this.$route.query.cutmonId || ''
      const { title = '', id = '', respondent_url = '' } = info

      let questionData = {
        act_name: title, // 问卷的名称 到时候由问卷返回
        resource_url: respondent_url, // 到时候由问卷返回
        resource_url_mobile: respondent_url, // 到时候由问卷返回
        act_id: id // 到时候由问卷返回(可能)
      }
      let moocTaskInfo = {
        resource_type: 'Survey',
        resource_type_name: '问卷',
        module_name: '问卷',
        act_type: '32',
        task_name: this.questionType === 'edit' ? taskName : title, // mooc任务的名称
        questionType: this.questionType,
        required: false,
        sid: id,
        id: cutmonId, // 前端自己生成的任务id
        dataId: this.dataId,
        ...questionData
      }
      if (this.questionType === 'edit') {
        setTimeout(() => {
          window.opener.questionReEdit(moocTaskInfo)
          this.onCancel()
        }, 100)
        return
      }
      setTimeout(() => {
        window.opener.questionReConnect(moocTaskInfo)
        this.onCancel()
      }, 100)
    },
    onSubmit() {
      this.questionIframeDom.contentWindow.postMessage({
        type: 'questionnaire',
        events: 'getData'
      }, '*')
    }
  }
}
</script>

<style lang="less" scoped>
.questionPage {
  height: 100%;
  width: 100%;
  .questionIframe {
    height: calc(100% - 70px);
    width: 100%;
  }
}
</style>
