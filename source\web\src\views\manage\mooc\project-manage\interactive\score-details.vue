<template>
  <div class="score-details">
    <div class="score-block row-1">
      <h2 class="score-head">评分统计</h2>
      <div class="score-statistic">
        <div class="summary">
          <div class="item">
            <span>课程评分</span>
            <p>{{ (statistics && statistics.score.toFixed(1)) || 0 }}</p>
          </div>
          <div class="item">
            <span>评分人数</span>
            <p>{{ (statistics && statistics.user_count) || 0 }}</p>
          </div>
        </div>
        <div class="chart">
          <div ref="pie-chart" class="pie">
            <div class="shadow"></div>
          </div>
          <div class="pie-data">
            <p v-for="(item, index) in grade" :key="index">
              <span class="title">{{ item.title }}</span>
              <span class="ratio">{{ (item.ratio * 100).toFixed(2) }}%</span>
              <span class="num">{{ item.num }}人</span>
            </p>
          </div>
        </div>
      </div>
    </div>
    <div class="score-block row-2">
      <h2 class="score-head">评分明细</h2>
      <el-form ref="form" :model="form" inline>
        <el-form-item label="名称">
          <el-input v-model="form.staffName" placeholder="请输入学员名称" size="small" clearable></el-input>
        </el-form-item>
        <el-form-item label="评分">
          <el-select v-model="form.scoreType" placeholder="请选择评分" size="small">
            <el-option
              v-for="item in scoreOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" size="small" @click="onSearch(1)">搜索</el-button>
          <el-button size="small" @click="reset">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="tableData.records" header-row-class-name="table-header-style">
        <el-table-column prop="staff_name" label="学员名称"></el-table-column>
        <el-table-column label="评分">
          <template slot-scope="scope">
            {{ scope.row.score }}星
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="评分时间"></el-table-column>
        <el-table-column prop="status_name" label="培训状态"></el-table-column>
      </el-table>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="current"
        :page-sizes="[10, 20, 30, 50, 100]"
        :page-size="size"
        layout="total,  prev, pager, next, sizes, jumper"
        :total="tableData.total">
      </el-pagination>
    </div>
  </div>
</template>

<script>
import pagination from '@/mixins/pager'
import { getInteractScoreStatisticsAPI, getInteractScoreListAPI } from '@/config/mooc.api.conf.js'

export default {
  mixins: [pagination],
  data () {
    return {
      statistics: null,
      grade: [
        // { ratio: 0.1358, title: 'red 13.58%', color: 'red' },
        // { ratio: 0.1244, title: 'green 12.44%', color: 'green' },
        // { ratio: 0.1066, title: 'blue 10.66%', color: 'blue' },
        // { ratio: 0.0829, title: 'yellow 8.29%', color: 'yellow' },
        // { ratio: 0.0568, title: 'orange 5.68%', color: 'orange' },
        // { ratio: 0.0474, title: 'black 4.74%', color: 'black' },
        // { ratio: 0.0209, title: 'purple 2.09%', color: 'purple' },
        // { ratio: 0.4252, title: 'pink 42.52%', color: 'pink' }
      ],
      form: {
        staffName: '',
        createTime: '',
        scoreType: '',
        taskProperty: ''
      },
      scoreOptions: [
        { label: '全部', value: '' },
        { label: '1星', value: 1 },
        { label: '2星', value: 2 },
        { label: '3星', value: 3 },
        { label: '4星', value: 4 },
        { label: '5星', value: 5 }
      ],
      tableData: {
        records: [],
        total: 0
      }
    }
  },
  created () {
    this.getStatistics()
    this.onSearch(1)
  },
  mounted() {
    this.$nextTick(() => {
      this.getTableHeight()
    })
  },
  methods: {
    // 评分统计
    getStatistics () {
      getInteractScoreStatisticsAPI({
        act_id: this.$route.query.mooc_course_id
      }).then(res => {
        this.statistics = res
        this.grade = [
          { title: '5星', num: res.level_five, ratio: res.user_count && this.handleProzentsatz(res.level_five / res.user_count), color: '#2BA3FF' },
          { title: '4星', num: res.level_four, ratio: res.user_count && this.handleProzentsatz(res.level_four / res.user_count), color: '#39CDCF' },
          { title: '3星', num: res.level_three, ratio: res.user_count && this.handleProzentsatz(res.level_three / res.user_count), color: '#44CD6F' },
          { title: '2星', num: res.level_two, ratio: res.user_count && this.handleProzentsatz(res.level_two / res.user_count), color: '#FFA41D' },
          { title: '1星', num: res.level_one, ratio: res.user_count && this.handleProzentsatz(res.level_one / res.user_count), color: '#F9CF36' }
        ]
        this.buildPie(this.$refs['pie-chart'], this.grade)
      })
    },
    // 处理评价数据
    handleProzentsatz(d) {
      return d ? Number(d.toFixed(2)) : 0
    },
    execPoint (cx, cy, r, ratio) {
      // 计算弧度，一个整圆弧度是 2π
      const rad = ratio * 2 * Math.PI
      return {
        x: cx + Math.sin(rad) * r,
        y: cy - Math.cos(rad) * r
      }
    },
    /**
     * 构建裁剪路径
     * @param data 数据
     * @param width 元素宽度，宽高相同
     */
    buildSectorPaths (data, width) {
      // 偏转量
      let offset = 0
      // 圆心坐标
      const cx = width / 2
      const cy = width / 2
      // 半径
      const r = width / 2
      const result = []
      for (const datum of data) {
        let path = `M ${cx},${cy}`
        // 圆弧起点
        const start = this.execPoint(cx, cy, r, offset)
        path += ` L ${start.x},${start.y}`
        // 圆弧终点
        offset += datum.ratio
        const end = this.execPoint(cx, cy, r, offset)
        // 圆弧大关圆画大圆，否则画小圆
        const angle = datum.ratio * 2 * Math.PI
        path += ` A ${r},${r} 0,${angle > Math.PI ? 1 : 0},1 ${end.x},${end.y}`
        path += ' Z'
        result.push(path)
      }
      return result
    },
    buildPie (containerEl, data) {
      containerEl.classList.add('pie')
      const paths = this.buildSectorPaths(data, 162)
      // console.log(paths)
      for (let i = 0; i < paths.length; i++) {
        // const path = paths[i]
        const sector = document.createElement('div')
        sector.classList.add('sector')
        sector.style.clipPath = `path('${paths[i]}')`
        // 给个随机背景色
        // const r = Math.floor(Math.random() * 255)
        // const g = Math.floor(Math.random() * 255)
        // const b = Math.floor(Math.random() * 255)
        // sector.style.backgroundColor = `rgb(${r},${g},${b})`
        sector.style.backgroundColor = data[i].color
        containerEl.appendChild(sector)
        // 新增加的标题
        const title = document.createElement('div')
        title.classList.add('title')
        title.innerHTML = `${data[i].title}: ${data[i].num}人 （${(data[i].ratio * 100).toFixed(2)}%）`
        // title.style.left = paths[i].tipPosition[0] + 'px'
        // title.style.top = paths[i].tipPosition[1] + 'px'
        containerEl.appendChild(title)
      }
    },
    onSearch (page_no = 1) {
      this.current = page_no
      getInteractScoreListAPI({
        act_id: this.$route.query.mooc_course_id,
        staff_name: this.form.staffName,
        score: this.form.scoreType,
        page_no,
        page_size: this.size
      }).then(res => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    reset () {
      this.form.staffName = ''
      this.form.scoreType = ''
      this.onSearch(1)
    },
    getTableHeight () {
      let wrapHeight = document.getElementsByClassName('el-tabs__content')[0].offsetHeight
      const rowHeight_1 = document.getElementsByClassName('row-1')[0].offsetHeight + 50
      const rowHeight_2 = document.getElementsByClassName('row-2')[0].getElementsByClassName('score-head')[0].offsetHeight + 20
      const searchHeight = document.getElementsByClassName('el-form')[0].offsetHeight
      const paginationHeight = document.getElementsByClassName('el-pagination')[0]?.offsetHeight
      this.tableHeight = wrapHeight - 40 - rowHeight_1 - rowHeight_2 - searchHeight - paginationHeight - 10
    }
  }
}
</script>

<style lang="less" scoped>
.score-details {
  .score-block {
    .score-head {
      color: rgba(0, 0, 0, 0.8);
      font-size: 16px;
      font-weight: bold;
      margin: 0 0 20px;
      position: relative;
      display: flex;
    }
    .score-head::before {
      content: '';
      width: 16px;
      height: 16px;
      background: url('~@/assets/mooc-img/title-icon.png') no-repeat center / 100% 100%;
      margin-right: 8px;
    }
    .el-form {
      .el-form-item {
        margin-right: 30px;
      }
    }
    .score-statistic {
      display: flex;
      align-items: center;
      margin-bottom: 30px;
      .summary {
        width: 388px;
        height: 212px;
        border-radius: 4px;
        border: 1px solid #eee;
        display: flex;
        justify-content: space-around;
        align-items: center;
        .item {
          color: #666;
          text-align: center;
          margin-right: 30px;
          & > p {
            color: #333;
            font-size: 28px;
            font-weight: bold;
            margin-top: 12px;
          }
        }
      }
      .chart {
        flex: 1;
        height: 212px;
        border-radius: 4px;
        border: 1px solid #eee;
        margin-left: 20px;
        padding-left: 80px;
        display: flex;
        align-items: center;
        /deep/.pie {
          width: 162px;
          height: 162px;
          position: relative;
          .sector {
            width: 100%;
            height: 100%;
            position: absolute;
            transition: all .1s ease-in-out;
            cursor: pointer;
          }
          .sector:hover {
            transform: scale(1.1);
          }
          .title {
            width: 60%;
            height: 60%;
            border-radius: 50%;
            text-align: center;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            margin: auto;
            z-index: 2;
            display: none;
          }
          .sector:hover + .title {
            display: flex;
          }
          .shadow {
            width: 60%;
            height: 60%;
            background: #fff;
            border-radius: 50%;
            position: absolute;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            margin: auto;
            z-index: 1;
          }
        }
        .pie-data {
          margin-left: 90px;
          & > p {
            display: flex;
            margin: 10px 0;
            .title {
              color: #666;
              display: flex;
              align-items: center;
              &::before {
                content: '';
                width: 6px;
                height: 6px;
                background: #0052d9;
                border-radius: 50%;
                display: inline-block;
                margin-right: 8px;
              }
            }
            .ratio {
              width: 100px;
              border-left: 1px solid #eee;
              color: #999;
              padding-left: 15px;
              margin-left: 15px;
            }
            .num {
              color: #333;
            }
          }
          & > p:nth-of-type(1) .title::before {
            background: #2ba3ff;
          }
          & > p:nth-of-type(2) .title::before {
            background: #39cdcf;
          }
          & > p:nth-of-type(3) .title::before {
            background: #44cd6f;
          }
          & > p:nth-of-type(4) .title::before {
            background: #ffa41d;
          }
          & > p:nth-of-type(5) .title::before {
            background: #f9cf36;
          }
        }
      }
    }
  }
}
</style>
