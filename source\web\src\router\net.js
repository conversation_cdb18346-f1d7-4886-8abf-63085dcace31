const netcourse = [
  {
    path: '/netcourse',
    name: 'netcourse',
    component: () => import('views/user/netcourse/index'),
    meta: {
      title: 'ai虚拟人做课工具'
    },
    children: [
      {
        path: 'online-course',
        name: 'onlineCourse',
        component: () => import('views/user/netcourse/online-course/index'),
        meta: {
          title: '在线课程'
        }
      },
      {
        path: 'make-share',
        name: 'makeShare',
        component: () => import('views/user/netcourse/make-share/index'),
        meta: {
          title: '在线课程制作和分享'
        }
      },
      {
        path: 'quick-class',
        name: 'quickClass',
        component: () => import('views/user/netcourse/quick-class/index'),
        meta: {
          title: 'AI快捷做课'
        }
      },
      // {
      //   path: 'course',
      //   name: 'course',
      //   component: () => import('views/user/netcourse/course/index'),
      //   meta: {
      //     title: '课单'
      //   }
      // },
      {
        path: 'ai/video-ppt',
        name: 'videoByPPT',
        component: () => import('views/user/netcourse/course-make/video-PPT/index'),
        meta: {
          title: 'AI快捷做课_Q-Learning'
        }
      },
      {
        path: 'ai/video-2d',
        name: 'videoBy2D',
        component: () => import('views/user/netcourse/course-make/video-2D/index'),
        meta: {
          title: 'AI快捷做课_Q-Learning'
        }
      },
      {
        path: 'ai/publish',
        name: 'coursePublish',
        component: () => import('views/user/netcourse/course-make/publish/index'),
        meta: {
          title: '发布在线课程_Q-Learning'
        }
      },
      {
        path: 'ai/directly-upload',
        name: 'directlyUpload',
        component: () => import('views/user/netcourse/course-make/directly-upload/index'),
        meta: {
          title: '上传在线课程_Q-Learning'
        }
      }
    ]
  },
  {
    path: '/netcourse/play',
    name: 'play',
    component: () => import('views/user/netcourse/net/index'),
    meta: {
      title: '网络课'
    }        
  },
  // 灰度页面
  {
    path: '/netcourse/grayPlay',
    name: 'grayPlay',
    component: () => import('views/user/netcourse/grayPlay/index'),
    meta: {
      title: '网课详情页-新版'
    }
  },
  // 极客视频
  {
    path: '/outsourcedCourse/video/play',
    name: 'videoPlay',
    component: () => import('views/user/outsourced-course/video'),
    meta: {
      title: '网络课'
    }        
  }
]

export default netcourse
