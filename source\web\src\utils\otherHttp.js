import axios from 'axios'

import { Message } from 'element-ui'

// const baseURL = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_V8_HOST_WOA : process.env.VUE_APP_V8_HOST
const service = axios.create({
  // baseURL,
  timeout: 15000,
  withCredentials: true
})

service.interceptors.request.use(config => {
  return config
}, error => {
  return Promise.reject(error)
})

service.interceptors.response.use(response => {
  return response
}, error => {
  Message({
    message: error.message,
    type: 'error',
    duration: 5 * 1000
  })
  return Promise.reject(error)
})

export default service
