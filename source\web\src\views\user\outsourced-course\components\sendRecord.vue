<template>
  <el-dialog class="has-course-dialog" title="送出记录" :visible.sync="dialogVisible" width="856px" @open="handlerOpen">
    <div class="content-xueba">
      <el-table :data="tableData" style="width: 100%;" class="custom-table-header" :header-cell-style="headerCellStyle">
        <el-table-column label="赠送人" width="349" show-overflow-tooltip>
          <template slot-scope="scope">
            <span >{{scope.row.staff_name}}</span>
          </template>
        </el-table-column>
        <el-table-column label="送出时间" width="">
          <template slot-scope="scope"> {{scope.row.created_at}} </template>
        </el-table-column>
        <el-table-column label="赠送状态" width="">
          <template slot-scope="scope"> <span :style="`color:${reward_amt(scope.row.reward_amt).color}`">{{reward_amt(scope.row.reward_amt).name}}</span></template>
        </el-table-column>
      </el-table>
      <el-pagination class="pagination-coures-dialog" :pager-count="5" background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pagination.current" :page-sizes="[10, 30, 50, 100]" :page-size="pagination.size" layout="total, sizes, prev, pager, next, jumper" :total="pagination.total">
      </el-pagination>
    </div>
    <span slot="footer" class="dialog-footer">
      <!-- <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="dialogVisible = false">确 定</el-button> -->
    </span>
  </el-dialog>
</template>

<script>
import { getPresentRecord } from '@/config/mooc.api.conf.js'
export default {
  props: ['isShow'],
  data() {
    return {
      pagination: {
        current: 1,
        size: 10,
        total: 100
      },
      optionsAct: [{ label: '培养项目', value: 11 }],
      tableData: []
    }
  },
  computed: {
    dialogVisible: {
      set(val) {
        this.$emit('update:isShow', val)
      },
      get() {
        return this.isShow
      }
    },
    headerCellStyle() {
      return {
        background: '#F5F5F5',
        color: '#00000099',
        fontSize: '14px',
        fontWeight: '400'
      }
    },
    reward_amt() {
      return (amt) => {
        return amt
          ? { name: '已送出有奖励', color: '#00A870' }
          : { name: '已送出无奖励', color: '#0052D9' }
      }
    }
  },
  mounted() {},
  methods: {
    handlerOpen() {
      this.current = 1
      this.getActiveOrders()
    },
    // async getActiveOrders() {
    //   const { sourceFrom, actType } = this.hasData
    //   let params = {
    //     current: this.pagination.current,
    //     size: this.pagination.size,
    //     sourceFrom: sourceFrom,
    //     actType: actType
    //   }
    //   const res = await getActiveOrders(params)
    //   this.pagination.total = res.total
    //   this.tableData = res.records || []
    //   console.log(res, '已兑换课程列表')
    // },
    // 赠送的
    getActiveOrders() {
      let params = {
        activityId: this.$route.query.activityId,
        current: this.pagination.current,
        size: this.pagination.size
      }
      getPresentRecord(params).then((res) => {
        console.log(res, '赠送')
        this.pagination.total = res.total
        this.tableData = res.records || []
      })
      //   获得奖励的有多少张
      //   let presentFilterList = this.presentRecordList.filter(
      //     (item) => item.reward_amt
      //   )
      //   this.numberOfRewards = presentFilterList.length
    },
    handlerOpenUrlCurse(val) {
      window.open(val.course_url, '_blank')
    },
    onSubmit() {
      this.getActiveOrders()
    },
    reset() {
      this.getActiveOrders()
    },
    handleSizeChange(size) {
      this.pagination.size = size
      this.getActiveOrders()
    },
    handleCurrentChange(val) {
      this.pagination.current = val
      this.getActiveOrders()
    }
  }
}
</script>
<style lang="less">
.content-xueba {
  .el-button--default {
    background-color: #fff;
    color: #0052d9;
    border: 1px solid #0052d9;
  }
  .el-button--default:hover {
    background-color: #fff;
    color: #0052d9;
    border: 1px solid #0052d9;
  }
}
</style>
<style lang="less" scoped>
.content-xueba {
  padding: 0 12px;
}
.url_link {
  color: #0052d9;
  cursor: pointer;
}
.has-course-dialog {
  /deep/.el-dialog {
    border-radius: 8px;
  }
  /deep/.el-dialog__footer {
    padding: 0;
  }
  /deep/.el-dialog__header {
    border-bottom: 1px solid #e7e7e7;
  }
  /deep/.el-dialog__body {
    max-height: 830px;
  }
}
.custom-table-header {
  border: 1px solid #f5f5f5;
  border-bottom: 0px;
  border-radius: 4px;
}
.has-form-inline {
  background: #f8f8f8;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  /deep/.el-form-item {
    margin-bottom: 0px;
  }
}
.pagination-coures-dialog {
  position: relative;
  /deep/.el-input__inner {
    height: 32px;
    border: 1px solid var(--Gray-Gray4-, #dcdcdc);
  }
  /deep/ .el-pagination__sizes input {
    height: 32px !important;
    line-height: 32px;
  }
  /deep/.el-input__icon {
    line-height: 20px !important;
  }
  /deep/.el-pagination__sizes {
    height: 32px !important;
    /deep/.el-input {
      width: 96px !important;
      height: 32px !important;
    }

    /deep/.btn-prev {
      border: none;
    }
  }
  /deep/.el-pager {
    .number,
    .more {
      width: 32px;
      height: 32px !important;
      border: 1px solid var(--Gray-Gray4-, #dcdcdc);
      background: var(--Gray-White, #fff);
      text-align: center;
      line-height: 32px;
    }
  }
  /deep/.el-pagination.is-background .btn-next,
  /deep/.el-pagination.is-background .btn-prev,
  /deep/ .el-pagination.is-background .el-pager li {
    margin: 0 8px;
  }
  /deep/.el-pagination__jump {
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
    background: #f3f3f3;
    border-radius: 3px;
  }
}
/deep/.el-pagination .el-pagination__total {
  height: 32px !important;
  line-height: 32px !important;
  margin-right: 136px !important;
  color: #00000099 !important;
  position: absolute;
  left: 0;
}
/deep/.el-pagination .el-pager .number.active {
  background: #0052d9 !important;
  color: #fff !important;
  border-color: #0052d9 !important;
}
/deep/.el-pagination.is-background .btn-next,
/deep/.el-pagination.is-background .btn-prev,
/deep/.el-pagination.is-background .el-icon-more,
/deep/.el-pagination.is-background .el-icon-d-arrow-right {
  background-color: #fff !important;
  border: none;
}
/deep/.el-pagination .el-pagination__jump input {
  height: 28px !important;
}
</style>
