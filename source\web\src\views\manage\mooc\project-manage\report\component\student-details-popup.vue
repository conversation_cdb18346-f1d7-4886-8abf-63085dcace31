<template>
  <div>
    <el-dialog
      :visible.sync="isShowDetailsPopup"
      custom-class="student-details-popup"
      title="学员详情"
      width="960px"
      :lock-scroll="false"
      :close-on-click-modal="false"
      :before-close="closeDialog">
      <div :class="['icon-status', 'status-'+baseInfo.status]"></div>
      <div class="l-r">
        <p><span>学员名称：</span>{{ baseInfo.staff_name || '-' }}</p>
        <p><span>所属组织：</span>{{ baseInfo.dept_name || '-' }}</p>
      </div>
      <div class="l-r">
        <p><span>开始培训时间：</span>{{ baseInfo.study_start_time || '-' }}</p>
        <p><span>完成培训时间：</span>{{ baseInfo.finished_time || '-' }}</p>
      </div>
      <ul class="t-b">
        <li>
          <p class="t">{{ baseInfo.required_count }}/{{ baseInfo.required_total }}</p>
          <p class="b">应学进度</p>
        </li>
        <li>
          <p class="t">{{ baseInfo.optional_count }}/{{ baseInfo.optional_total }}</p>
          <p class="b">选学进度</p>
        </li>
        <li>
          <p class="t">{{ baseInfo.finished_count }}/{{ baseInfo.task_count }}</p>
          <p class="b">总进度</p>
        </li>
        <li class="cursor" @click="handleOpenCertificateDetail(baseInfo.certificate_count)">
          <p class="t">{{ baseInfo.certificate_count || 0 }}</p>
          <p class="b">证书奖励</p>
        </li>
        <li>
          <p class="t">{{ baseInfo.total_study_time }}</p>
          <p class="b">学习时长（分钟）</p>
        </li>
      </ul>
      <el-table :data="tableData.records" header-row-class-name="table-header-style">
        <el-table-column prop="task_name" label="任务名称" min-width="450">
          <template slot-scope="scope">
            <div class="pub-flex">
              <span style="margin-right:24px;">{{ scope.row.task_name }}</span>
              <el-tag v-if="scope.row.required" type="warning" size="small">应学</el-tag>
              <el-tag v-else type="success" size="mini">选学</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="resource_type" label="任务类型" min-width="120">
          <template slot-scope="scope">
            {{ handlerActType(scope.row.resource_type) }}
          </template>
        </el-table-column>
        <el-table-column prop="is_finished" label="任务状态" min-width="120">
          <template slot-scope="scope">
            {{ handlerTaskStatus(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="开始任务时间" min-width="160">
          <template slot-scope="scope">
            {{ handlerDateFormat(scope.row.start_time) || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="end_time" label="完成任务时间" min-width="160">
          <template slot-scope="scope">
            {{ scope.row.finished_time || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="total_study_time" label="累计学习时长" min-width="160">
          <template slot-scope="scope">
            {{ handleTime(scope.row.total_study_time) || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="" label="操作" min-width="120" fixed="right">
          <template slot-scope="scope">
            <el-popover
              v-if="['Exam', 'Practice', 'ThirdParty'].includes(scope.row.resource_type)"
              placement="bottom"
              trigger="hover">
                <div class="tip-card">
                  <div class="title">其他信息</div>
                  <!-- 考试--练习 -->
                  <ul class="list" v-if="['Exam', 'Practice'].includes(scope.row.resource_type)">
                    <li v-if="scope.row.resource_type === 'Exam'">
                      <span class="label">通过状态：</span><span>{{ handleIsFinished(scope.row) }}</span>
                    </li>
                    <li>
                      <span class="label">最高成绩：</span><span>{{ scope.row.score === null ? '-' : scope.row.score }}</span>
                    </li>
                    <li v-if="scope.row.resource_type === 'Exam'">
                      <span class="label">是否作弊：</span><span>{{ scope.row.is_finished === null ? '-' : scope.row.is_cheat ? '是' : '否' }}</span>
                    </li>
                  </ul>
                  <!-- 第三方 -->
                  <ul v-if="scope.row.resource_type === 'ThirdParty'"></ul>
                  <!-- 作业 -->
                  <ul v-if="scope.row.resource_type === 'HomeWork'">
                    <li>
                      <span class="label">作业状态：</span><span>-</span>
                      <span class="label">提交时间：</span><span>-</span>
                      <span class="label">批阅人：</span><span>-</span>
                      <span class="label">老师分数：</span><span>-</span>
                      <span class="label">互评分数：</span><span>-</span>
                      <span class="label">总成绩：</span><span>-</span>
                      <span class="label">互评批阅份数：</span><span>-</span>
                    </li>
                  </ul>
                </div>
              <el-button type="text" slot="reference">查看更多</el-button>
            </el-popover>
            <el-button v-else type="text" :disabled="true">查看更多</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        v-if="tableData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="current"
        :page-sizes="[10, 20, 30, 50, 100]"
        :page-size="size"
        layout="total,  prev, pager, next, sizes, jumper"
        :total="tableData.total">
      </el-pagination>
    </el-dialog>
    <!-- 证书列表弹窗 -->
    <certificate-list-popup ref="certificateListPopup" moduleType="manage" :isShowCertificatePopup.sync="isShowCertificatePopup"></certificate-list-popup>
  </div>
</template>

<script>
import pagination from '@/mixins/pager'
import CertificateListPopup from '@/views/components/certificate-list-popup.vue'
import { handlerDateFormat, transforTime } from '@/utils/tools.js'
import { getStudentDetailTaskListAPI } from '@/config/mooc.api.conf.js'

const resourceType = [
  { name: '视频', id: 'Video' },
  { name: '音频', id: 'Audio' },
  { name: '图片', id: 'Image' },
  { name: '压缩文档', id: 'Zip' },
  { name: '文档', id: 'Doc' },
  { name: '考试', id: 'Exam' },
  { name: '练习', id: 'Practice' },
  { name: '图文', id: 'Article' },
  { name: 'Scorm', id: 'Scorm' },
  { name: '外部链接', id: 'Other' }
]

export default {
  mixins: [pagination],
  components: {
    CertificateListPopup
  },
  props: {
    isShowDetailsPopup: {
      type: Boolean,
      default: false
    },
    detailsInfo: {
      type: Object,
      default: null
    }
  },
  data () {
    return {
      baseInfo: {
        staff_name: '',
        dept_name: '',
        study_start_time: '',
        finished_time: '',
        required_count: 0,
        required_total: 0,
        optional_count: 0,
        optional_total: 0,
        finished_count: 0,
        task_count: 0,
        certificate_count: 0,
        total_study_time: 0
      },
      tableData: {
        records: [],
        total: 0
      },
      isShowCertificatePopup: false
    }
  },
  created () {
    this.onSearch(1)
  },
  methods: {
    onSearch (page_no = 1) {
      this.current = page_no
      getStudentDetailTaskListAPI({
        mooc_course_id: this.$route.query.mooc_course_id,
        staff_id: this.detailsInfo.staff_id,
        page_no,
        page_size: this.size
      }).then(res => {
        if (res.total_study_time > 0) {
          res.total_study_time = (res.total_study_time / 60).toFixed(2)
        }
        this.baseInfo = res
        this.tableData.records = res.task_list
        this.tableData.total = res.total
      })
    },
    // 关闭弹窗
    closeDialog () {
      this.$emit('update:isShowDetailsPopup', false)
    },
    handleIsFinished(row) {
      if (row.act_type === '20') {
        if (row.exam_status === 2) {
          return '待批阅'
        }
        if (row.is_cheat) {
          return '未通过'
        } else {
          if (row.is_finished === null) return '-'
          if (row.is_finished) return '已通过'
          if (!row.is_finished) return '未通过'
        }
      }
      return '-'
    },
    handleOpenCertificateDetail(num) {
      if (num <= 0) {
        this.$message.warning('您还没有可查看的证书')
        return
      }
      this.isShowCertificatePopup = true
      this.$refs.certificateListPopup.onSearch(this.detailsInfo.staff_id)
    },
    handlerTaskStatus (row) {
      if (row.is_finished) {
        return '已完成'
      }
      if (row.start_time) {
        let timeVal = new Date() - new Date(this.handlerDateFormat(row.start_time))
        if (timeVal > 0) {
          return '进行中'
        } else {
          return '未开始'
        }
      } else {
        return '未开始'
      }
    },
    handlerDateFormat(v) {
      if (v) {
        return handlerDateFormat(v, '-')
      }
      return '-'
    },
    handlerActType (t) {
      const data = resourceType.find(v => v.id === t)
      return data?.name || ''
    },
    handleTime (e) {
      return transforTime(e)
    }
  }
}
</script>

<style lang="less" scoped>
:deep(.student-details-popup) {
 .el-dialog__body {
    padding: 20px 30px;
    position: relative;
  }
  .icon-status {
    width: 100px;
    height: 100px;
    position: absolute;
    right: 32px;
    top: 24px;
    &.status-null {
      background: url('~@/assets/mooc-img/status-wks.png') no-repeat center / 100% 100%;
    }
    &.status-0 {
      background: url('~@/assets/mooc-img/status-jxz.png') no-repeat center / 100% 100%;
    }
    &.status-1 {
      background: url('~@/assets/mooc-img/status-ywc.png') no-repeat center / 100% 100%;
    }
    &.status-3 {
      background: url('~@/assets/mooc-img/status-yyq.png') no-repeat center / 100% 100%;
    }
  }
  .l-r {
    display: flex;
    align-items: center;
    margin: 0 0 20px;
    & > p {
      color: #333;
      margin-right: 60px;
      width: 290px;
      & > span {
        color: #666;
      }
    }
  }
  .t-b {
    height: 80px;
    background: #f8f8f8;
    text-align: center;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 40px;
    margin-bottom: 40px;
    border-radius: 4px;
    .t {
      color: #333;
      font-size: 20px;
      font-weight: bold;
    }
    .b {
      color: #666;
      margin-top: 10px;
    }
  }
  .el-form .el-form-item .el-input__inner {
    width: 240px;
  }
  .el-button--text:focus, .el-button--text:hover {
    background-color: transparent;
  }
  .el-table .el-table__body-wrapper table {
    width: max-content !important;
  }
  .el-table .el-table__header-wrapper table, .el-table .el-table__body-wrapper table {
    width: max-content !important;
  }
}
.cursor {
  cursor: pointer;
}
:deep(.el-tag) {
  width: 32px;
  height: 18px;
  line-height: 18px;
  font-size: 12px;
  font-weight: 400;
  padding: 0 4px;
  text-align: center;
  border-radius: 2px;
}
:deep(.el-tag.el-tag--warning) {
  background: rgb(253, 246, 236);
  color: rgba(255, 117, 72, 1);
}
:deep(.el-tag.el-tag--success) {
  background: rgb(204, 242, 226);
  color: rgb(0, 179, 104);
}
.tip-card {
  .title {
    font-size: 16px;
    font-weight: 600;
    color: #000;
    margin-bottom: 12px;
  }
  .list {
    & > li {
      margin-bottom: 12px;
      &:last-child {
        margin: 0;
      }
    }
    .label {
      font-size: 14px;
      color: #000;
    }
  }
}
</style>
