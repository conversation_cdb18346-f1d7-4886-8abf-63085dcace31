<template>
  <div class="material-container">
    <div class="header-box">
      <p class="title">素材库</p>
      <el-button type="primary" plain size="small" @click="handleIntranet">内网素材上传页面</el-button>
    </div>
    <el-dropdown @command="addMaterial" trigger="click" size="small">
      <el-button type="primary" size="small"><i class="el-icon-plus"></i>上传素材<i class="el-icon-arrow-down"></i></el-button>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="Video">视频</el-dropdown-item>
        <el-dropdown-item command="Audio">音频</el-dropdown-item>
        <el-dropdown-item command="Article">文章</el-dropdown-item>
        <el-dropdown-item command="Doc">文档</el-dropdown-item>
        <!-- <el-dropdown-item command="Scorm">scorm</el-dropdown-item> -->
        <el-dropdown-item command="Flash">压缩包</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <el-form :model="form" ref="searchForm" :inline="true" class="search-form-style">
      <el-form-item label="素材名称：" prop="material_name">
        <el-input v-model="form.material_name" placeholder="请输入素材名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="类型：">
        <el-select v-model="form.module_value" placeholder="请选择素材类型" clearable>
          <el-option 
          v-for="item in contentModuleTypes" 
          :key="item.module_value" 
          :label="item.module_name" 
          :value="item.module_value"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="转码状态：">
        <el-select v-model="form.status_value" placeholder="请选择转码状态" clearable>
          <el-option v-for="(item, index) in transCodeOptions" :key="index" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="存放位置：">
        <el-select v-model="form.storageLocation" placeholder="请选择素材存放位置" clearable>
          <el-option v-for="item in depositOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="来源：">
        <el-select v-model="form.contentType" placeholder="请选择素材来源" clearable>
          <el-option v-for="item in originOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="上传时间：">
        <el-date-picker
          size="small"
          style="width:380px"
          v-model="createTime"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00','23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="上传人：" prop="creator_name">
        <el-input v-model="form.creator_name" placeholder="请输入创建人名称" clearable></el-input>
      </el-form-item>
      <el-form-item label="信安审核：">
        <el-select clearable v-model="form.info_sec_status" placeholder="请选择" collapse-tags>
          <el-option v-for="item in safeOptions" :key="item.value" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSearch(1)" size="small">搜索</el-button>
        <el-button @click="handleReset" size="small">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table 
    :data="tableData.records" 
    style="width: 100%"
    header-row-class-name="table-header-style"
    row-class-name="table-row-style"
    >
      <el-table-column prop="file_id" label="素材id" width="120"></el-table-column>
      <el-table-column prop="file_show_name" label="名称" show-overflow-tooltip >
        <template v-slot="prop">
          <span v-if="prop.row.status !== 13">{{ prop.row.file_show_name }}</span>
          <a v-else class="content-url" target="_blank" :href="content_url + prop.row.file_id">{{ prop.row.file_show_name }}</a> 
        </template>
      </el-table-column>
      <el-table-column prop="material_name" label="类型" show-overflow-tooltip width="180">
        <template slot-scope="scope">
          <span>{{ filterResourceName(scope.row) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="信安审核" width="180">
        <template slot-scope="scope">
          <span>{{ safeInfo[scope.row.info_sec_status] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="转码状态" width="180">
        <template slot-scope="scope">
          <span>{{ statusLabel(scope.row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="storage_server" label="存放位置">
        <template slot-scope="scope">
          <span>{{ scope.row.content_id ? '云资源' : '内网资源' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="content_type" label="来源" width="180">
        <template slot-scope="scope">
          <span>{{ origin_info[scope.row.content_type] }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="refer_count" label="引用次数" width="180">
        <template slot-scope="scope">
          <span v-if='scope.row.refer_count' @click="toDetail(scope.row)" class="refer-count">{{ scope.row.refer_count }}</span>
          <span v-else>{{ scope.row.refer_count || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="上传时间" width="280"></el-table-column>
      <el-table-column prop="creator_name" label="上传人" ></el-table-column>
      <el-table-column label="操作" width="280">
        <template slot-scope="scope">
          <el-link type="primary" :disabled="scope.row.info_sec_status === 0" @click="edit(scope.row)" :underline="false">编辑</el-link>
          <el-link v-if='scope.row.status === 13' type="primary" @click="toDetail(scope.row)" :underline="false">详情</el-link>
          <el-link v-if="scope.row.status === 3" type="primary" @click="handleCode(scope.row)" :underline="false">重新转码</el-link>
          <el-link type="danger" @click="handleDelete(scope.row)" :underline="false">删除</el-link>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination 
    v-if="tableData.total" 
    @size-change="handleSizeChange" 
    @current-change="handleCurrentChange"
    :current-page="current" 
    :page-sizes="[5, 10, 20, 30, 50, 100]"
    :page-size="size"
    layout="total,  prev, pager, next, sizes, jumper" 
    :total="tableData.total"
    >
    </el-pagination>
    <el-dialog
      custom-class="quoteVisible-dialog none-border-dialog"
      title="当前素材已被其他资源引用，无法删除"
      :visible.sync="quoteInfo.quoteVisible"
      :show-close="false"
      width="30%"
      >
      <span class="label" @click="toDetail(quoteInfo)">查看引用详情</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="quoteInfo.quoteVisible = false" size="small">知道了</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { getMaterialList, deleteMaterial, transcoding } from 'config/mooc.api.conf'
import pager from '@/mixins/pager.vue'
import { mapState } from 'vuex'
// const status_info = {
//   2: '转码中',
//   3: '转码失败',
//   13: '转码成功'
// }
const file_type_info = {
  'Video': '1',
  'Audio': '2',
  'Scorm': '3',
  'Flash': '4',
  'Doc': '5'
}
const origin_info = {
  'UGC': '个人分享',
  'PGC': '组织/项目'
}
const safeInfo = {
  null: '审核通过',
  0: '待审核',
  1: '审核通过',
  2: '审核不通过'
}
export default {
  mixins: [pager],
  data() {
    return {
      content_url: window.location.origin + '/training/manage/material-play?material_id=',
      form: {
        material_name: '',
        creator_name: '',
        admin_name: '',
        storageLocation: '', // 存放位置
        contentType: '',
        status_value: [],
        module_value: '',
        info_sec_status: ''
      },
      createTime: [],
      tableData: {
        total: 0,
        records: []
      },
      origin_info,
      quoteInfo: {
        quoteVisible: false,
        file_id: ''
      },
      safeInfo,
      safeOptions: [
        { value: 0, label: '待审核' },
        { value: 1, label: '审核通过' },
        { value: 2, label: '审核不通过' }
      ],
      contentModuleTypes: [
        { module_name: '视频', module_id: 1, course_type: ['Video', 'Video-2d', 'Video-3d', 'Video-ppt'], module_value: 0 },
        { module_name: '音频', module_id: 1, course_type: ['Audio'], module_value: 1 },
        { module_name: '文章', module_id: 8, course_type: ['Article'], module_value: 2 },
        { module_name: '文档', module_id: 16, course_type: ['Doc'], module_value: 3 },
        { module_name: 'scorm', module_id: 1, course_type: ['Scorm'], module_value: 4 },
        { module_name: '压缩包', module_id: 1, course_type: ['Flash'], module_value: 5 }
      ],
      transCodeOptions: [
        { value: [1], label: '待转码' },
        { value: [13], label: '转码成功' },
        { value: [3, 6, 9, 12, 16], label: '转码失败' },
        { value: [2, 4, 5, 7, 8, 10, 11, 14, 15], label: '转码中' }
      ],
      depositOptions: [
        { value: 1, label: '云资源' },
        { value: 2, label: '内网资源' }
      ],
      originOptions: [
        { value: 'UGC', label: '个人分享' },
        { value: 'PGC', label: '组织/项目' }
      ]
    }
  },
  computed: {
    ...mapState(['userLimitInfo']),
    filterResourceName() {
      return ({ file_type, module_id }) => {
        let name = ''
        if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(file_type)) {
          name = '视频'
        } else if (file_type === 'Audio') {
          name = '音频'
        } else if (file_type === 'Article' || module_id === 8) {
          name = '文章'
        } else if (file_type === 'Doc' || module_id === 16) {
          name = '文档'
        } else if (file_type === 'Scorm') {
          name = 'Scorm'
        } else if (file_type === 'Flash') {
          name = '压缩包'
        }
        return name
      }
    },
    statusLabel() {
      return (val) => {
        const failStatusList = [3, 6, 9, 12, 16]
        const goingStatusList = [2, 4, 5, 7, 8, 10, 11, 14, 15]
        return failStatusList.includes(val) ? '转码失败' : 
          goingStatusList.includes(val) ? '转码中' : 
            val === 13 ? '转码成功' : val === 1 ? '待转码' : '' 
      }
    }
  },
  created() {
    // 添加完素材刷新页面
    window.workReConnect = () => {
      this.onSearch()
    }
  },
  mounted() {
    this.onSearch()
  },
  methods: {
    onSearch(page_no = 1) {
      const { material_name, module_value, creator_name, status_value, storageLocation, contentType, info_sec_status } = this.form
      const course_type_info = this.contentModuleTypes.find((v) => v.module_value === module_value)
      console.log('类型', this.form)
      const startToCloudTime = this.createTime?.length ? this.createTime[0] : ''
      const endToCloudTime = this.createTime?.length ? this.createTime[1] : ''
      const data = {
        fileName: material_name,
        fileType: course_type_info?.course_type,
        current: page_no,
        size: this.size,
        sort_order: 'desc',
        sort_by: 'created_at',
        creatorName: creator_name,
        startToCloudTime,
        endToCloudTime,
        status: status_value,
        storageLocation,
        contentType,
        info_sec_status
      }
      // 优化参数，有值就传
      let params = {}
      for (let key in data) {
        let temp = data[key]
        if ((temp || temp === 0) && ((Array.prototype.isPrototypeOf(temp) && temp.length > 0) || !Array.prototype.isPrototypeOf(temp))) {
          params[key] = data[key]
        }
      }
      getMaterialList(params).then((res) => {
        console.log('素材返回数据', res)
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    // 重新转码
    handleCode({ file_id }) {
      transcoding(file_id).then((res) => {
        this.$message.success('重新转码成功')
      })
    },
    // 上传素材
    addMaterial(val) {
      const { href } = this.$router.resolve({
        name: 'materialUpload',
        query: {
          uploadType: val,
          file_type: file_type_info[val]
        }
      })
      window.open(href, '_blank')
    },
    // 详情
    toDetail({ file_id }) {
      const { href } = this.$router.resolve({
        name: 'materialDetail',
        query: { file_id }
      })
      window.open(href, '_blank')
    },
    // 编辑
    edit({ file_type, file_id, content_id }) {
      if (content_id) {
        const { href } = this.$router.resolve({
          name: 'materialUpload',
          query: { 
            uploadType: file_type, 
            file_id, 
            file_type: file_type_info[file_type] 
          }
        })
        window.open(href, '_blank')
        return
      }
      const url = process.env.NODE_ENV === 'production' ? `https://learn.woa.com/manage/upload/edit/${file_id}` : `https://test-learn.woa.com/manage/upload/edit/${file_id}`
      window.open(url, '_blank')
    },
    handleIntranet() {
      const url = process.env.NODE_ENV === 'production' ? 'https://learn.woa.com/manage/upload/add' : 'https://test-learn.woa.com/manage/upload/add'
      window.open(url, '_blank')
    },
    // 删除
    handleDelete(row) {
      if (row.refer_count) { // 素材已被引用
        this.quoteInfo.quoteVisible = true
        this.quoteInfo.file_id = row.file_id
      } else {
        this.$confirm(`${row.file_show_name}`, '确定删除素材吗?', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
          // type: 'warning'
        }).then(() => {
          deleteMaterial(row.file_id).then((res) => {
            console.log('删除', res)
            this.$message({
              type: 'success',
              message: '删除成功!'
            })
            const index = this.tableData.records.findIndex((e) => e.file_id === row.file_id)
            this.tableData.records.splice(index, 1)
          })
        })
      }
    },
    handleReset() {
      this.form = {
        material_name: '',
        creator_name: '',
        admin_name: '',
        storageLocation: '',
        contentType: '',
        status_value: [],
        module_value: ''
      }
      this.createTime = []
      this.size = 10
      this.current = 1
      this.onSearch()
    }
  }
}
</script>
<style lang="less" scoped>
.material-container {
  padding: 20px;
  .el-table__row {
    .el-link + .el-link {
      margin-left: 10px;
    }
  }
  .content-url, .refer-count {
    color:#0052d9;
    cursor: pointer;
  }
  .header-box {
    padding-bottom: 16px;
    border-bottom: 1px solid #f3f3f3ff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .title {
      font-size: 18px;
      font-weight: bold;
    }

  }
  .el-dropdown {
    margin-top: 18px;
    .el-icon-plus{
      margin-right: 4px;
    }
    .el-icon-arrow-down {
      margin-left: 4px;
    }
  }
  .search-form-style {
    margin-top: 16px;
    background: #f9f9f9ff;
    padding-top: 16px;
    padding-left: 16px;
    margin-bottom: 20px;
    .el-form-item {
      margin-bottom: 16px;
    }
  }
}
.quoteVisible-dialog {
  .label {
    color:#0052d9;
    cursor: pointer;
  }
}
</style>
