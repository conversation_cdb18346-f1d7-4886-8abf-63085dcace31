<template>
  <el-Dialog 
  :visible="visible" 
  :title="dialogTitle" 
  width="810px" 
  :close-on-click-modal="false" 
  @close="cancel"
  class="add-tutor-dialog"
  >
    <div class="add-body">
      <div class="name-form-item common-form">
        <div class="form-label">员工名称：</div>
        <sdc-staff-selector 
        :range="range" 
        v-model="form.tutor_staff_id" 
        showFullTag 
        @change="handleChange" 
        >
      </sdc-staff-selector>
        <div class="ye-tips">仅支持添加权限范围内组织下的正式员工及顾问</div>
      </div>
      <!-- -3已经存在且免认证， -2已经存在， -1禁用， 0认证中，1合格， 2免认证， 3风险 -->
      <!-- 新增提示 -->
      <div class="ye-tips tips-b" v-if="!showIdTips && [-2, -3, -1].includes(riskInfo.check_result)">当前员工已在导师库中，无法重复添加</div>
      <div class="ye-tips tips-b" v-if="!showIdTips && riskInfo.check_result === 2">当前员工符合免认证规则，无需校验门槛资格及认证要求</div>
      <div class="ye-tips tips-b" v-if="showIdTips && riskInfo.check_result === -1">当前员工处于“禁用/认证中”状态，暂时无法添加为免认证导师，员工完成重新认证后可添加为免认证导师</div>
      <!-- 免认证提示 -->
      <div class="ye-tips tips-b" v-if="showIdTips && [-3].includes(riskInfo.check_result)">当前员工已是免认证导师，无法重复添加</div>
      <!-- null初始化的值 -->
      <!-- showIdTips-免认证 -->
      <div class="check-box" v-if="![null, -3, -1, -2, 2].includes(riskInfo.check_result) && !showIdTips  || (![null, -3, -1].includes(riskInfo.check_result) && showIdTips)">
        <div class="c-title">
          <span class="f-label">导师资质校验：</span>
          <span>根据公司+BG配置门槛及认证要求进行校验</span>
          <span>，职级和绩效数据存在T+1延迟</span>
        </div>
        <!-- 门槛资格 -->
        <div class="qualification-box common-require">
          <span class="q-label">门槛资格</span>
          <div 
          v-for="(item, index) in riskInfo.threshold_risk_detail" 
          :key="index" 
          :class="[item.meet_requirements ? 'r-bg' : 'err-bg', 'common-bg']"
          >
            <i :class="[item.meet_requirements ? 'el-icon-circle-check' : 'el-icon-circle-close']"></i>
            <span class="q-content">{{ item.requirements_content }}</span>
          </div>
        </div>
        <!-- 认证要求 -->
        <div class="require-box common-require">
          <span class="q-label">认证要求</span>
          <div 
          v-for="(item, index) in riskInfo.cert_risk_detail" 
          :key="index" 
          :class="[item.meet_requirements ? 'r-bg' : 'err-bg', 'common-bg', 'require-bg']"
          @click="toDetail(item)"
          >
            <i :class="[item.meet_requirements ? 'el-icon-circle-check' : 'el-icon-circle-close']"></i>
            <span class="q-content">{{ item.requirements_content }}</span>
          </div>
          <span v-if="isDissatisfy" class="ye-tips tips-c">不满足认证要求，确定添加后将触发“认证跟催”流程</span>
        </div>
      </div>
      <div class="reason-form-item common-form">
        <div class="form-label reason-label"><span v-if="showIdTips || (!showIdTips && riskInfo.check_result !== 2 && isAddErr)" class="red-star">*</span>录入原因：</div>
        <sdc-mce-editor 
        ref="addTutorEditRef" 
        :env="editorEnv" 
        :content="form.entry_reason"
        :catalogue.sync="editorConfig.catalogue"
        :urlConfig="editorConfig.urlConfig"
        :options="editorConfig.options"
        :insertItems="insertItems"
        @getWordCount="getWordCount" 
        />
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <CustomTips
      v-if="showIdTips"
      title="免认证录入后，该导师将跳过任何门槛资格及认证要求的校验环节，请谨慎操作" 
      IconName="el-icon-warning-outline" 
      backgroundColor="#fdf6ec" 
      color="#FF7548"
      lineHeight="32px"
      class="c-tips"
      >
      </CustomTips>
      <span class="r-btn">
        <el-button @click="cancel" size="small">取消</el-button>
        <el-button :disabled="disabledConfirm" type="primary" @click="confirm" size="small">确定</el-button>
      </span>
    </span>
  </el-Dialog>
</template>
<script>
import { tutorAdd, getUserRisk, getAuthentication } from '../../../api/tutor.api.conf'
import CustomTips from '@/components/tips.vue'
import { mapState } from 'vuex'
export default {
  props: {
    visible: Boolean
  },
  components: {
    CustomTips
  },
  data() {
    return {
      form: {
        tutor_staff_id: '',
        entry_reason: ''
      },
      dialogTitle: '新增导师',
      showIdTips: false,
      mceChangeVal: 0,
      riskInfo: {
        threshold_risk_detail: [],
        check_result: null,
        cert_risk_detail: []
      },
      insertItems: [
        'link',
        'table',
        'image',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorConfig: {
        catalogue: false,
        options: { // 非必传字段
          selector: '#tutor_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo |
              formatselect fontsizeselect lineheight |
              bold italic underline strikethrough |
              dent align numlist`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      },
      editorEnv: process.env.NODE_ENV
    }
  },
  computed: {
    ...mapState({
      userRole: state => state.userRole
    }),
    disabledConfirm() {
      let flag = false
      if (this.showIdTips) { // 免认证
        flag = [-3, -1].includes(this.riskInfo.check_result) || this.mceChangeVal === 0
      } else { // 新增
        // 新增-免认证不用禁止按钮
        if (this.riskInfo.check_result === 2) {
          flag = false
        } else {
          flag = [-2, -3].includes(this.riskInfo.check_result) || (this.isAddErr && this.mceChangeVal === 0)
        }
      }
      return flag
    },
    range() {
      let unitID = []
      const { bg_admin, dept_admin, bg_ids, dept_ids } = this.userRole
      if (bg_admin) { // bg
        unitID = bg_ids
      } else if (dept_admin) { // 部门
        unitID = dept_ids
      }
      return {
        unitID: !unitID || !unitID.length ? null : unitID, // 根组织ID
        // unitID, // 根组织ID
        isContainSubStaff: true, // 展示下级组织及员工
        manageUnitIdList: [10101],
        staffTypeIdList: [2, 6]
      }
    },
    // 新增-门槛资格校验
    isAddErr() {
      return (this.riskInfo.threshold_risk_detail || []).some((e) => !e.meet_requirements)
    },
    // 不满足
    isDissatisfy() {
      return !this.showIdTips && (this.riskInfo.cert_risk_detail || []).some((e) => !e.meet_requirements)
    }
  },
  methods: {
    initData(val) {
      if (val === '2') {
        this.dialogTitle = '免认证录入'
        this.showIdTips = true
        return
      }
      this.dialogTitle = '新增导师'
    },
    toDetail(v) {
      if (!v.course_url) return
      window.open(v.course_url, '_blank')
    },
    handleChange(val) {
      if (!val) {
        this.riskInfo = {
          threshold_risk_detail: [],
          check_result: null,
          cert_risk_detail: []
        }
        return
      }
      getUserRisk(val.StaffID).then((res) => {
        this.riskInfo = res
      })
    },
    cancel() {
      this.form = {
        tutor_staff_id: '',
        entry_reason: ''
      }
      this.$emit('update:visible', false)
    },
    confirm() {
      const entry_reason = this.$refs['addTutorEditRef'].getContent()
      const param = {
        tutor_staff_id: this.form.tutor_staff_id,
        entry_reason: this.mceChangeVal ? entry_reason : ''
      }
      const commonAPI = this.showIdTips ? getAuthentication : tutorAdd
      commonAPI(param).then(() => {
        this.$message.success('添加成功')
        this.$emit('onSearch')
        this.cancel()
      })
    },
    getWordCount(val) {
      this.mceChangeVal = val
    }
  }
}
</script>
<style lang="less">
  .reason-form-item {
    width: 736px;
  
    .tox.tox-tinymce {
      border: 1px solid #DCDCDC !important;
      height: 200px;
      border-radius: 3px;
  
      .tox-sidebar-wrap .tox-edit-area {
        min-height: 160px !important;
      }
    }
    .tox .tox-editor-header {
      text-align: left;
    }
  }
</style>
<style lang="less" scoped>
.add-tutor-dialog {
  :deep(.el-dialog__header) {
    padding: 24px 32px 20px;
  }
  :deep(.el-dialog__body) {
    padding: 24px 32px;
  }
  :deep(.el-dialog__footer) {
    padding: 0 32px 24px;
  }
  .common-form {
    color: #00000099;
    font-size: 14px;
    line-height: 22px;
    .form-label {
      margin-right: 8px;
    }
  }
  .ye-tips {
    color:#ED7B2F;
    font-size: 14px;
    line-height: 22px;
    margin-left: 20px;
  }
  .tips-b {
    margin-top: 8px;
    margin-left: 78px;
  }
  .name-form-item {
    display: flex;
    align-items: center;
    :deep(.sdc-selector) {
      width: 280px;
    }
    :deep(.el-form-item__content){
      display: flex;
      align-items: center;
    }
  }
  .reason-form-item {
    margin-top: 24px;
    .red-star {
      color: red;
      margin-right: 2px;
    }
    .reason-label {
      margin-bottom: 8px;
    }
  }
  .check-box {
    border-radius: 4px;
    background: #F9F9F9;
    padding: 12px 16px 0px;
    margin-top: 16px;
    .c-title {
      color: #00000066;
      font-size: 14px;
      line-height: 22px;
      margin-bottom: 12px;
      .f-label {
        color: #00000099;
        font-weight: bold;
      }
    }
    .common-require {
      display: flex;
      flex-wrap: wrap;
      font-size: 12px;
      line-height: 20px;
      .q-label {
        margin-right: 12px;
        display: inline-block;
      }
      .common-bg {
        margin-bottom: 12px;
        padding: 2px 8px;
        border-radius: 3px;
        margin-right: 12px;
        i {
          font-size: 14px;
          margin-right: 4px;
        }
      }
      .r-bg {
        background-color: #E3F9E9;
        color: #2BA471;
      }
      .err-bg {
        background-color: #FFF0ED;
        color: #D54941;
      }
    }
    .require-box {
      .require-bg {
        cursor: pointer;
        text-decoration: underline;
      }
      .tips-c  {
        margin-left: unset;
        margin-bottom: 12px;
      }
    }
  }
  .dialog-footer {
    display: flex;
    justify-content: space-between;
    .c-tips {
      padding: 0 8px;
      border-radius: 3px;
      flex-shrink: 0;
    }
    .r-btn {
      width: 100%;
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
