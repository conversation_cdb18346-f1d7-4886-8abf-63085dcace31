<template>
  <div class="view-operation-logs-popup activity-common">
    <el-dialog :visible.sync="visible" width="800px" :close-on-click-modal="false" :before-close="handlerClose">
      <div slot="title" class="dialog-header">操作日志</div>
      <div class="dialog-content">
        <div class="table-card">
          <el-table :data="tableData.records" header-row-class-name="table-header-style" row-class-name="table-row-style" class="content-table" height="500px">
            <el-table-column type="index" label="序号" width="60">
              <template slot-scope="scope">
                {{ (current - 1) * size + scope.$index + 1 }}
              </template>
            </el-table-column>
            <el-table-column prop="operate_name" label="步骤" width="156"></el-table-column>
            <el-table-column prop="creator_name" label="操作人" width="112"></el-table-column>
            <el-table-column prop="created_at" label="操作时间" width="178"></el-table-column>
            <el-table-column prop="remark" label="备注">
              <template slot-scope="scope">
                <el-tooltip class="item" effect="dark" :content="scope.row.remark" placement="top">
                  <span class="text-ellipsis-2">{{ scope.row.remark }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            v-if="tableData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="current"
            :page-sizes="[5, 10, 20, 30, 50, 100]"
            :page-size="size"
            layout="total,  prev, pager, next, sizes, jumper"
            :total="tableData.total"
          >
          </el-pagination>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import pager from '@/mixins/pager'
import { getActivityStudentLogApi } from '@/config/classroom.api.conf.js'

export default {
  name: 'viewOperationLogsPopup',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    attId: {
      type: [Number, String],
      default: ''
    }
  },
  mixins: [pager],
  data() {
    return {}
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.getActivityStudentLog()
        }
      },
      immediate: true
    }
  },
  computed: {},
  created() { },
  mounted() { },
  beforeDestroy() { },
  methods: {
    onSearch() {
      this.getActivityStudentLog()
    },
    getActivityStudentLog() {
      getActivityStudentLogApi({
        att_id: this.attId,
        current: this.current,
        size: this.size
      }).then(res => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    handlerClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="less" scoped>
  @import '~assets/css/activity-common.less';
  
  .view-operation-logs-popup {
    :deep(.el-dialog) {
      .el-dialog__body {
        padding: 24px 32px 24px;
      }
    }
    .dialog-content {
      max-height: 740px;
      overflow-y: auto;
    }
  }
</style>
