<template>
  <sdc-layout :header-layout="['logo', 'links']" :sidebar-menus="sidebarMenusData">
    <div class="mooc-web-container">
      <div slot="header-logo">
        <span class="app-name1">文章管理</span>
        <span class="app-name2">管理后台</span>
      </div>
      <div slot="header-links">
        <el-avatar size="small" class="user-avatar" :src="avatar">
          <img :src="defaultAvatar" />
        </el-avatar>
        <span class="staff-name">{{ staffName }}</span>
        <sdc-link @click="exit" text="退出" />
      </div>
    </div>
  </sdc-layout>
</template>

<script>
import { refresh, resize } from 'sdc-vue'
import { getAvatar } from 'utils/tools'

export default {
  name: 'manage',
  mixins: [refresh, resize],
  data() {
    return {
      staffName: '',
      avatar: require('@/assets/img/avatar.png'),
      defaultAvatar: require('@/assets/img/avatar.png'),
      sidebarMenus: {
        active: '1.1',
        map: {
          key: 'id',
          text: 'name',
          url: 'link',
          pid: 'parentId',
          root: '0'
        },
        data: []
      },
      blacklist: ['/mooc/manage/project-list', '/mooc/manage/project-create', '/mooc/manage/banner-list']
    }
  },
  watch: {
    '$store.state.userInfo'() {
      this.avatar = getAvatar(this.$store.state.userInfo.staff_name)
      this.staffName = this.$store.state.userInfo.staff_name
    },
    $route: {
      immediate: true,
      handler() {
        if (this.blacklist.includes(this.$route.path)) {
          window.$qlCommonHeader && window.$qlCommonHeader.destroy()
        }
      }
    }
  },
  computed: {
    sidebarMenusData() {
      let menus = this.sidebarMenus
      const { supper_admin, staff_id } = this.$store.state.userLimitInfo
      const superAdminList = this.$store.state.superAdminList
      if (staff_id && !supper_admin && this.$route.fullPath.substr(0, 5) === '/mooc') {
        let path = {
          'project-list': '1.1',
          'banner-list': '1.2'
        }
        superAdminList.forEach(v => {
          let index = -1
          menus.data.map(s => {
            index = s.children.findIndex(d => d.path === v.path)
            if (index !== -1) {
              s.children.splice(index, 1)
              delete path[v.path]
            }
          })
        })
        const arr = window.location.pathname.split('/')
        menus.active = path[arr[arr.length - 1] || 'project-list']
        this.handleCurRoute()
      }
      return menus
    }
  },
  created() {
    this.handerMenuData()
  },
  methods: {
    handerMenuData() {
      if (this.$route.fullPath.substr(0, 5) !== '/mooc') {
        this.sidebarMenus.data = [
          {
            id: '1',
            name: '文章管理',
            icon: 'el-icon-document',
            parentId: '0',
            level: 1,
            children: [
              {
                id: '1.1',
                name: '文章列表',
                parentId: '1',
                level: 2,
                path: 'graphic-list',
                click: () => {
                  this.menuChange('graphic-list')
                }
              }
            ]
          }
        ]
        const path = {
          'graphic-list': '1.1'
        }
        const arr = window.location.pathname.split('/')
        this.sidebarMenus.active = path[arr[arr.length - 1] || 'graphic-list']
      } else {
        this.sidebarMenus.data = [
          {
            id: '1',
            name: '培养项目管理',
            icon: 'el-icon-document',
            parentId: '0',
            level: 1,
            children: [
              {
                id: '1.1',
                name: '培养项目管理',
                parentId: '1',
                level: 2,
                path: 'project-list',
                click: () => {
                  this.menuChange('project-list')
                }
              },
              {
                id: '1.2',
                name: '轮播图管理',
                parentId: '1',
                level: 2,
                path: 'banner-list',
                click: () => {
                  this.menuChange('banner-list')
                }
              }
            ]
          }
        ]
        let path = {
          'project-list': '1.1',
          'banner-list': '1.2'
        }
        const arr = window.location.pathname.split('/')
        this.sidebarMenus.active = path[arr[arr.length - 1] || 'project-list']
      }
    },
    // 菜单切换
    menuChange(name) {
      this.$router.push({ name })
    },
    exit() {
      if (process.env.NODE_ENV === 'development') {
        // 测试环境
        window.location.href = `//passtest.oa.com/modules/passport/signout.ashx?url=${location.href}`
      } else {
        // 生产环境
        window.location.href = `${location.origin}/_logout/?url=${location.href}`
      }
    },
    handleCurRoute() {
      const { supper_admin, staff_id } = this.$store.state.userLimitInfo
      const superAdminList = this.$store.state.superAdminList
      const len = superAdminList.length
      if (staff_id && !supper_admin) {
        for (let i = 0; i < len; i++) {
          superAdminList[i].path === this.$route.name && this.menuChange('401')
        }
      }
    }
  }
}
</script>

<style lang="less">
@import '~assets/css/el-style.less';
@import '~assets/css/center.less';
@import '~assets/css/common.less';

.mooc-web-container {
  :deep(.header-inner) {
    .header-left {
      width: auto;
      padding-left: 20px;

      .logo {
        span:first-child {
          margin-right: 18px;
          font-size: 28px;
          font-weight: bold;
        }

        span:last-child {
          font-size: 16px;
        }
      }
    }

    .header-right {
      .header-right-inner>div {
        display: flex;
        align-items: center;
      }

      .staff-name {
        margin: 0 8px 0 8px;
      }
    }
  }
}
</style>
