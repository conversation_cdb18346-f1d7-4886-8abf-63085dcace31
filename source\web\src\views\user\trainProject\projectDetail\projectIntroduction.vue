<template>
  <div class="project-introduction-content" :class="courseType === 'sanjieke' ? 'project-introduction-content-sanjieke' : '' ">
    <div class="tag-content item-box">
      <div class="title-box">
        <img src="@/assets/mooc-img/title-icon.png" />
        <span class="bassinfo-class-title">{{ $langue('Mooc_ProjectDetail_Desc_Label', { defaultText: '项目标签' }) }}</span>
      </div>
      <div class="item-box-value flex-tag-box">
        <dynamicsTag v-if="rankTag" :tagData="rankTag" :dtData="dtData"></dynamicsTag>
        <sdc-label-show ref="labelShow" :labelNodeEnv="labelNodeEnv" :actType="11" :courseId="mooc_course_id" from_page="mooc"></sdc-label-show>
      </div>
      <div class="item-box-value flex-tag-box">
        <active-jump class="mgt-16" :dtArg="dtArg"></active-jump>
      </div>
      <!-- <div class="item-box-value flex-tag-box" v-if=detailData?.course_labels?.length>
        <span 
        v-for="item in detailData.course_labels" 
        :key="item.label_id" 
        class="tag-box"
        @click="toPage(item.label_name)"
        >
          <el-tooltip effect="dark" :content="item.category_full_name" placement="top-start">
            <span>{{ item.label_name }}</span>
          </el-tooltip>
        </span>
      </div>
      <div class="item-box-value" v-else>{{ $langue('Mooc_ProjectDetail_Desc_NoLable', { defaultText: '暂无项目标签' }) }}</div> -->
    </div>
    <div class="project-des-content item-box">
      <div class="title-box">
        <img src="@/assets/mooc-img/title-icon.png" />
        <span class="bassinfo-class-title">{{ $langue('Mooc_ProjectDetail_Desc_Desc', { defaultText: '项目简介' }) }}</span>
      </div>
      <!-- <div class="item-box-value">{{ detailData?.course_desc || $langue('Mooc_ProjectDetail_Desc_NoDesc', { defaultText: '暂无项目简介' }) }}</div> -->
      <div class="item-box-value">
        <sdc-mce-preview
          ref="editor"
          :urlConfig="editorConfig.urlConfig"
          :catalogue.sync="editorConfig.catalogue"
          :content="detailData?.course_desc || $langue('Mooc_ProjectDetail_Desc_NoDesc', { defaultText: '暂无项目简介' })"
        >
        </sdc-mce-preview>
      </div>
    </div>
    <div class="project-detail-content item-box">
      <div class="title-box">
        <img src="@/assets/mooc-img/title-icon.png" />
        <span class="bassinfo-class-title">{{ $langue('Mooc_ProjectDetail_Desc_Detail', { defaultText: '项目详情' }) }}</span>
      </div>
      <sdc-mce-preview
        v-if="tincyValue"
        ref="editor"
        :urlConfig="editorConfig.urlConfig"
        :catalogue.sync="editorConfig.catalogue"
        :content="detailData.course_desc_detail ? detailData.course_desc_detail.replace(/\n/g, '<br/>') : ''"
      >
      </sdc-mce-preview>
      <!-- <div v-if="detailData?.course_desc_detail" class="item-box-value" v-html="detailData?.course_desc_detail"></div> -->
      <div v-else class="no-desDetail-img">
        <img src="@/assets/img/empty.png" alt="">
        <div class="empty-text">{{ $langue('Mooc_ProjectDetail_Desc_NoDetail', { defaultText: '暂无项目详情' }) }}</div>
      </div>
    </div>
    <div class="admin-content item-box">
      <div class="title-box">
        <img src="@/assets/mooc-img/title-icon.png" />
        <span class="bassinfo-class-title">{{ $langue('Mooc_ProjectDetail_Desc_Admins', { defaultText: '项目管理员' }) }}</span>
      </div>
      <div :class="[{ 'register-avator-content': register }, 'item-box-value', 'avator-content', 'clearfix']" >
        <span 
        :class="[{'last-avator-box': index % 5 === 4 && !register }, 'avator-box']" 
        v-for="(item, index) in adminList" 
        :key="item.admin_id"
        @mouseover="titleOver($event, index)"
        >
          <el-image lazy fit="fill" :src="item.url"  class="avator">
            <div class="image-slot" slot="placeholder">
              <i class="el-icon-loading"></i>
            </div>
            <div class="error-cover" slot="error">
              <img :src="errorAvatorImg" alt="" />
            </div>
          </el-image>
          <el-tooltip effect="dark" :disabled="!item.isOverflow" :content="item.admin_name" placement="top-start">
            <span class="avator-name">{{ item.admin_name }}</span>
          </el-tooltip>
        </span>
        <span v-if="!adminList.length">{{ $langue('Mooc_ProjectDetail_Desc_NoAdmins', { defaultText: '项目暂未配置管理员' }) }}</span>
      </div>
    </div>
  </div>
</template>
<script>
import dynamicsTag from '@/components/dynamicsTag'
import ActiveJump from '@/views/components/activeJump.vue'
import { getDynamicsTag } from 'config/mooc.api.conf.js'
import { mapState } from 'vuex'
import moment from 'moment'
export default {
  components: {
    dynamicsTag,
    ActiveJump
  },
  props: {
    courseType: {
      type: String
    },
    detailData: {
      type: Object,
      default: () => ({})
    },
    adminList: {
      type: Array,
      default: () => ([])
    },
    register: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      rankTag: null,
      errorAvatorImg: require('@/assets/mooc-img/user-avatar.png'),
      editorConfig: {
        catalogue: false,
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/{contentId}`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview`
        }
      },
      labelNodeEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test'
    }
  },
  computed: {
    ...mapState(['isBusy']),
    courseInfo() {
      return {
        mooc_course_id: this.mooc_course_id,
        page: this.detailData?.course_title || '',
        page_type: 'mooc介绍页'
      }
    },
    dtArg() {
      return {
        page: this.detailData?.course_title || '',
        page_type: 'mooc介绍页',
        container: '项目介绍',
        content_name: '订阅抽奖入口',
        course_id: this.mooc_course_id
      }
    },
    tincyValue() {
      return this.detailData?.course_desc_detail ? this.detailData?.course_desc_detail.replace(/(<([^>]+)>)/ig, '') || this.detailData?.course_desc_detail.includes('data-content') : false
    },
    mooc_course_id () {
      let { mooc_course_id } = this.getRouterQuery()
      return mooc_course_id
    },
    dtData() {
      return {
        page: this.detailData.course_title,
        page_type: '项目详情页', 
        container: '项目介绍',
        click_type: 'data',
        content_type: '培养项目',
        content_id: this.mooc_course_id,
        container_id: '',
        content_name: '',
        act_type: '11',
        terminal: 'PC',
        id: this.mooc_course_id
      }
    }
  },
  mounted() {
    this.$store.dispatch('getIsBusy').then(() => {
      if (this.isBusy !== '1') {
        this.getDynamicsTag()
      }
    })
  },
  methods: {
    getDynamicsTag() {
      const dt = moment().subtract(2, 'days').format('YYYYMMDD')
      let { mooc_course_id } = this.$route.query
      const param = {
        act_type: '11',
        course_id: mooc_course_id,
        p_dt: dt
      }
      getDynamicsTag(param).then((res) => {
        this.rankTag = res
      })
    },
    getRouterQuery() {
      let { mooc_course_id } = this.$route.query
      return {
        mooc_course_id: mooc_course_id || ''
      }
    },
    toPage(val) {
      const obj = {
        production: {
          woa: 'https://learn.woa.com/mat/user/search',
          oa: 'http://v8.learn.oa.com/mat/user/search'

        },
        test: {
          woa: 'https://test-learn.woa.com/mat/user/search',
          oa: 'http://test.v8.learn.oa.com/mat/user/search'
        }
      }
      const key = location.hostname.endsWith('.woa.com') ? 'woa' : 'oa'
      const url = process.env.NODE_ENV === 'production' ? obj['production'][key] : obj['test'][key]
      const commonUrl = url + `?keywords=${val}&from_page=mooc&type=label`
      window.open(commonUrl)
    },
    titleOver(e, index) {
      const target = e.target
      this.$set(
        this.adminList[index],
        'isOverflow',
        target.scrollWidth > target.clientWidth
      )
    }
  }
}
</script>
<style lang="less" scoped>
  @font-color: #2C2C2E;
  @line-height: 28px;
  @main-color: #273BC1;

  .project-introduction-content {
    padding: 20px;

    .mgt-16 {
      margin-top: 16px;
    }

    :deep(.sdc-editor-preview) {
      .editor-content {
        h1 {
          font-size: 24px;
        }
        h2 {

        }
        img {
          margin: 15px 0;
        }
      }
    }

    .title-box {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      img {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }

      .bassinfo-class-title {
        color: #000000;
        font-weight: bold;
        line-height: 22px;
        display: inline-block;
      }
    }
    .avator-content {
      width: 950px;
    }
    .register-avator-content  {
      width: 800px;
    }
    .admin-content {
      margin-bottom: 8px !important;
    }
    .item-box {
      margin-bottom: 28px;
      .mgl-24 {
        margin-left: 24px;
      }
      .flex-tag-box {
        display: flex;
        flex-direction: row;
        flex-wrap: wrap;
        .label-show-page {
          width: auto;
        }
        .tag-box {
          line-height: 20px;
          margin-bottom: 5px;
          display: flex;
          align-items: center;
        }
      }
      .item-box-value {
        margin-left: 24px;
        color: #000000e6;
        font-size: 14px;
        line-height: 22px;
        .avator-box {
          height: 32px;
          line-height: 32px;
          display: flex;
          align-items: center;
          float: left;
          width: 160px;
          :deep(img) {
            width: 32px;
            height: 32px;
            border-radius: 80px;
            border: 2px solid #ffffffff;
          }
        }
        .avator-name {
          overflow: hidden;
          display: inline-block;
          width: 120px;
          text-overflow: ellipsis;
          white-space: nowrap;
          margin-left: 8px;
        }
        .avator-box {
          margin-right: 36px;
          margin-bottom: 20px;
        }
        .last-avator-box {
          margin-right: unset;
        }
      }
    }
    .tag-content {

      .tag-box {
        height: 20px;
        border-radius: 2px;
        opacity: 1;
        background: #ebeffcff;
        color: #0052d9ff;
        font-size: 12px;
        padding: 4px;
        margin-right: 12px;
        cursor: pointer;
      }
    }
    .project-detail-content {
      .sdc-editor-preview {
        margin-left: 24px;
      }
      .no-desDetail-img {
        text-align: center;
        color: #000000;
        font-size: 16px;
        img {
          width: 178px;
          margin-bottom: 20px;
        }
      }
    }
  }
  .project-introduction-content-sanjieke {
    :deep(.sdc-editor-preview) {
      .editor-content {
        h1 {
          font-size: 24px;
        }
        h2 {

        }
        img {
          margin: 15px 0;
        }

        h1 {
          font-weight: 600;
          font-size: 24px;
          line-height: @line-height;
          color: @font-color;
          margin-bottom: 28px;
        }

        h2 {
          font-weight: 600;
          font-size: 19px;
          line-height: @line-height;
          color: @font-color;
          border-left: 4px solid @main-color;
          padding-left: 10px!important;
          margin-bottom: 28px;
        }

        h3 {
          font-weight: 600;
          font-size: 17px;
          line-height: @line-height;
          color: @font-color;
          margin-bottom: 28px;
        }

        p {
          font-weight: 400;
          font-size: 16px;
          line-height: @line-height;
          color: @font-color;
          margin-bottom: 18px;
          text-indent: 0!important;
        }

        p:last-child {
          margin-bottom: 0;
        }

        img {
          display: block;
          margin-bottom: 28px;
        }

        blockquote {
          font-weight: 400;
          font-size: 16px;
          line-height: @line-height;
          color: #5A6A80;
          border-left: 4px solid #CBD3DE;
          padding-left: 10px!important;
          margin-left: 20px;
        }

        pre {
          white-space: pre-wrap;
          white-space: -moz-pre-wrap;
          white-space: -pre-wrap;
          white-space: -o-pre-wrap;
          word-wrap: break-word;
          padding: 10px;
          margin-bottom: 16px;
          border-radius: 3px;
          line-height: 2;
          background: #f4f2f0;
          overflow-x: auto;
        }

        code {
          background: #f4f2f0;
          padding: 2px 4px;
          color: #474334;
        }

        a {
          font-weight: 400;
          font-size: 16px;
          line-height: @line-height;
          color: @main-color;
          text-decoration: none;
        }

        a:hover {
          color: #3E5AE5;
        }

        ul, ol {
          padding-left: 24px;
          font-weight: 400;
          font-size: 14px;
          line-height: @line-height;
          color: @font-color;
          li {
            margin-bottom: 9px;
            list-style: inherit;
            p {
              font-size: 14px;
            }
          }
        }

        ul {
          list-style-type: disc;
        }
        ol {
          list-style-type: decimal;
        }

        table {
          border-collapse: collapse;
          td {
            padding: 8px 16px;
          }

          thead {
            td {
              font-weight: 500;
              font-size: 16px;
              line-height: @line-height;
              color: @font-color;
            }
          }

          tbody {
            td {
              font-weight: 400;
              font-size: 16px;
              line-height: @line-height;
              color: @font-color;
            }
            tr:nth-child(odd) {
              background: #F0F2F5;
            }
          }
        }

      }
    }
  }
</style>
