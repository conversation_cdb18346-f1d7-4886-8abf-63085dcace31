<template>
  <div class="give-tab-box">
    <!-- <div class="tab-container">
      <div class="tab" :class="activeTab === 'left' ? 'active' : '' " @click="changeTab('left')">我送出的功学卡</div>
      <div class="tab" :class="activeTab === 'right' ? 'active' : '' " @click="changeTab('right')">我收到的功学卡（{{listObj.presentPassiveRecordList.length}}）</div>
    </div> -->
    <div class="tab-content">
      <div class="tab-content-tips" v-if="activeTab === 'left' && (presentRecordNumber || consumePoint > 0)">
        当前已送出 <span class="color_b">{{presentRecordNumber}}</span> 张学霸卡（共可送出 10 张），获得奖励 <span class="color_b">{{numberOfRewards}}</span> 张「{{Config.card_name}}」专用卡，请在本页面上方【学霸卡领用详情】模块查看。
      </div>
      <div class="tab-content-tips" v-if="activeTab === 'right'">
        当前已收到 <span class="color_b">{{listObj.presentPassiveRecordList.length}}</span> 张学霸卡（最多可受赠 3 张），对应获得 {{listObj.presentPassiveRecordList.length}} 张「{{Config.card_name}}」专用卡
      </div>
      <div class="tab-content-list">
        <!-- <el-tooltip :content="`剩余可送出：${consumePoint} 张`" placement="top" effect="light">
          <span>
            <div class="tab-content-list_left" :style="isDisabled ? 'opacity: 0.2;' : '' " @click="openGive" v-if="activeTab === 'left' &&  (consumePoint > 0 || presentRecordNumber)">
              <img :src="require('@/assets/outsourcedCourse/give-pc.png')" alt="">
              <span>送出学霸卡</span>
            </div>
          </span>
        </el-tooltip> -->
        <div class="tab-content-list_left"  @click="openGive" v-if="activeTab === 'left' &&  (isQuantity || presentRecordNumber)">
          <img :src="require('@/assets/outsourcedCourse/give-pc.png')" alt="">
          <span>送出学霸卡</span>
        </div>
        <div class="tab-content-list_right" v-if="tabList.length">
          <swiper class="mySwiper" :style="tabList.length > 3 ? 'margin: 0 36px;' : '' " :options="swiperOption" :space-between="50">
            <div class="swiper-button-prev" slot="button-prev" :style="tabList.length > 3 ? '' : 'display:none;' ">
            </div>
            <!-- @click="next" -->
            <div class="swiper-button-next" slot="button-next" :style="tabList.length > 3 ? '' : 'display:none;' "></div>
            <swiper-slide v-for="(item, index) in tabList" :key="index">
              <div class="tab-content_item">
                <div class="tab-content_item-left" ><img :src="userImgUrl(item.staff_name)" alt=""></div>
                <div class="tab-content_item-right">
                  <div class="name">{{activeTab === 'left' ? '向' : '来自'}}：{{item.staff_name}}</div>
                  <div class="number1">送{{activeTab === 'left' ? '出' : '给你'}} 1 张学霸卡</div>
                  <div class="number2">奖励：<span v-if="activeTab === 'left'">{{item.reward_amt || 0}}</span> <span v-else>1</span> 张「{{Config.card_name}}」专用卡</div>
                </div>
              </div>
            </swiper-slide>
          </swiper>
        </div>
      </div>
      <div class="tab-content_null">
        <div class="tab-content_null-left" v-show="activeTab === 'left' && (consumePoint === 0 && presentRecordNumber === 0)">
          <img :src="require('@/assets/outsourcedCourse/null-data-left.png')" alt="">
          <div class="tab-content_null-footer">
            暂未解锁发送学霸卡功能
          </div>
          <!-- <div class="tab-content_tips">
            抢到「学霸卡-{{Config.card_name}}专用卡」，或拜托同事向你送出学霸卡，并兑换任意一门「{{Config.card_name}}」课程
            即可参与劝学有礼-发出学霸卡活动
          </div> -->
        </div>
        <div class="tab-content_null-right" v-show="activeTab === 'right' && !tabList.length">
          <img :src="require('@/assets/outsourcedCourse/null-data-right.png')" alt="">
          <div class="tab-content_null-footer">
            快联系小伙伴 <br />
            让他们向你送出学霸卡吧！
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ['Config', 'listObj', 'consumePoint', 'numberOfRewards', 'isQuantity'],
  data() {
    return {
      activeTab: 'left',
      swiperOption: {
        loop: false, // 是否循环轮播
        speed: 1000, // 切换速度
        observer: true, // 修改swiper自己或子元素时，自动初始化swiper
        observeParents: true, // 修改swiper的父元素时，自动初始化swiper
        centeredSlides: false,
        spaceBetween: 12,
        // 设置slider容器能够同时显示的slides数量
        slidesPerView: 3,
        // 左右切换
        navigation: {
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        }
      }
    }
  },
  created() {},
  watch: {
  },
  computed: {
    presentRecordNumber() {
      return this.listObj.presentRecordList.length
    },
    tabList() {
      return this.activeTab === 'left'
        ? this.listObj.presentRecordList
        : this.listObj.presentPassiveRecordList
    },
    userImgUrl() {
      return (val) => {
        if (!val) return ''
        let staffname = val.split(',')[0].split('(')[0]
        return `https://learn.woa.com/rhrc/photo/150/${staffname}.png`
      }
    },
    isDisabled() {
      return this.consumePoint === 0
    }
  },
  methods: {
    changeTab(val) {
      this.activeTab = val
    },
    openGive() {
      // if (this.isDisabled) return
      this.$emit('openGive')
    }
  }
}
</script>

<style lang="less" scoped>
.color_b {
  color: #0052d9;
}
.give-tab-box {
  .tab-content {
    margin-top: 16px;
    &-tips {
      padding: 6px 12px;
      border-radius: 4px;
      background: #f9f9f9;
      color: #00000099;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    &-list {
      //   height: 84px;
      width: 100%;
      display: flex;
      margin-top: 18px;
      &_left {
        width: 152px;
        display: flex;
        height: 84px;
        padding: 6px 24px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 8px;
        flex-shrink: 0;
        align-self: stretch;
        border-radius: 8px;
        border: 0.5px solid #eee;
        background: #ffe6c1;
        margin-right: 12px;
        cursor: pointer;
        img {
          width: 16px;
          height: 16px;
        }
        span {
          color: #cb5500;
          font-family: 'PingFang SC';
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: 22px;
        }
      }
      &_right {
        flex: 1;
        overflow: hidden;
        position: relative;
        .mySwiper {
          position: unset;
        }
        .swiper-slide {
          //   margin-right: 12px;
          display: flex;
          height: 84px;
          //   padding: 8px 12px;
          flex-direction: column;
          align-items: flex-start;
          border-radius: 8px;
          border: 0.5px solid #eee;
          background: linear-gradient(
            0deg,
            #fdffff 36.9%,
            #fbfeff 77.92%,
            #f9fffe 100%
          );
        }
        // .swiper-slide:last-child {
        //   margin-right: 0;
        // }
        .swiper-button-prev,
        .swiper-button-next {
          height: 100%;
          width: 28px;
          border: 1px solid #eeeeee;
          background: #ffffff;
          border-radius: 4px;
          top: 26%;
          cursor: pointer;
        }
        .swiper-button-prev {
          border-radius: 4px 0 0 4px;
          left: 0;
        }
        .swiper-button-next {
          border-radius: 0 4px 4px 0;
          right: 0;
        }
        .swiper-button-prev:after,
        .swiper-button-next:after {
          font-size: 14px;
          color: #666666;
          font-weight: 600;
        }
        .tab-content_item {
          display: flex;
          margin-top: 12px;
          padding: 0 12px;
          &-left {
            margin-right: 6px;
            img {
              width: 40px;
              height: 40px;
              border-radius: 50%;
            }
          }
          &-right {
            line-height: 20px;
            font-size: 12px;
            .name {
              align-self: stretch;
              color: #333333ff;
              font-family: 'PingFang SC';
              font-size: 14px;
              font-style: normal;
              font-weight: 500;
              line-height: 22px;
              display: -webkit-box;
              overflow: hidden;
              text-overflow: ellipsis;
              word-break: break-all;
              -webkit-box-orient: vertical;
              -webkit-line-clamp: 1;
            }
            .number1 {
              align-self: stretch;
              color: #666666ff;
              font-family: 'PingFang SC';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
            }
            .number2 {
              color: #999999ff;
              font-family: 'PingFang SC';
              font-size: 12px;
              font-style: normal;
              font-weight: 400;
              line-height: 20px;
            }
          }
        }
      }
    }
    &_null {
      &-right {
        img {
          position: relative;
          left: 50%;
          transform: translateX(-50%);
          width: 128px;
          height: 128px;
          margin: 12px 0 8px;
        }
      }
      &-footer {
        color: #666666ff;
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
      .tab-content_tips {
        margin: 24px 0 16px;
        line-height: 32px;
        background-color: #f9f9f9;
        font-size: 12px;
        padding: 0 12px;
        color: #777777;
      }
      &-left {
        img {
          position: relative;
          left: 50%;
          transform: translateX(-50%);
          width: 128px;
          height: 128px;
          margin: 8px 0;
        }
      }
    }
  }
}
.tab-container {
  display: flex;
  width: fit-content;
}
.tab {
  padding: 10px 20px;
  cursor: pointer;
  border: 1px solid #dcdcdc;
  color: #777777;
  transition: color 0.3s, border-color 0.3s;
}
.tab:first-child {
  border-top-left-radius: 4px;
}
.tab:last-child {
  border-top-right-radius: 4px;
}
.tab.active {
  color: #0052d9;
  border-color: #0052d9;
  font-weight: bold;
}
</style>
