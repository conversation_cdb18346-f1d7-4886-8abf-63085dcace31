<template>
  <div class="active-page">
    <div class="img-content">
      <van-image class="top-img" :src="topImgUrl">
        <template v-slot:error>
          <img class="top-img" src="../../../../assets/img/mobile/geekBang/active-top.png" />
        </template>
      </van-image>
    </div>
    <div class="content-main">
      <div class="active-rule">
        <div class="active-rule-text">
          参与活动，即可获得《哈佛精品文库》500+篇文章的阅读
          权限！<span style="color:#0052d9ff" @click="toRulesDetail" >点击查看完整活动规则>></span>
        </div>
        <!-- <div class="active-rule-btn" @click="toRulesDetail">活动规则</div> -->
      </div>
      <!-- <div class="my-authority">
        <div class="auth-text">我的权限</div>
        <div class="auth-show">
          <div :style="cardInfo.balance ? '' : 'background: #FBFBFB; color: #00000066;' " :class="['auth-show-left', { 'auth-show-after1' : cardInfo.balance}]">
            <img class="show-img" :style="cardInfo.balance ? '' : 'opacity: 0.8' " :src="require('@/assets/img/mobile/geekBang/learn.png')" alt="">
            学习权限
          </div>
          <div :style="shareAuth ? '' : 'background: #FBFBFB; color: #00000066;' " :class="['auth-show-right', { 'auth-show-after2' : shareAuth}]">
            <img class="show-img" :style="shareAuth ? '' : 'opacity: 0.8' " :src="require('@/assets/img/mobile/geekBang/share.png')" alt="">
            分享权限
          </div>
        </div>
        <div class="request-auth" v-if="!cardInfo.balance" @click="applyperm">
          {{record ? '已提交需求' : '暂未开启学习权限，可点此提交需求'}}
        </div>
      </div> -->
    </div>
    <div class="give-box" v-if="shareAuth">
      <div class="give-title">
        限时分享：邀请同事参与学习，为其免费开通价值<span style="color:#FF0000;">¥1200</span>的《哈佛精品文库》阅读权限
      </div>
      <!-- <div class="give-tips">
        除了自己学习，您还可以通过点击下方“邀请同事”按钮，邀请您的同事一起参与学习。<br />
        对方接受邀请后，将获得与您同等的阅读和分享权限。<br />
        数量有限，先到先得
      </div> -->
      <div class="tab-content">
        <div class="tab-content_tips">
          当前已成功邀请 <span class="color_b">{{total}}</span>名同事 <span style="color:#0052d9ff;" @click="openRecord">点击查看已邀请同事</span>
        </div>
        <div class="tab-content_btn" :style="!isQuantity ? 'opacity: 0.5;' : '' " @click="openGive">
          <img :src="require('../../../../assets/img/mobile/geekBang/bulk-user-add-m.png')" alt="">
          <span>邀请同事</span>
        </div>
        <!-- <div class="tab-content_item" v-for="(item, index) in presentRecordList" :key="index">
          <div class="tab-content_item-left"><img :src="userImgUrl(item.staff_name)" alt=""></div>
          <div class="tab-content_item-right">
            <div class="name">邀请对象：{{item.staff_name}}</div>
            <div class="number1">邀请时间：{{createTime(item.created_at)}}</div>
          </div>
        </div>
        <div class="loading-moer" @click="handlerLoadingMore" v-if="total > presentRecordList.length">加载更多</div> -->
      </div>
    </div>
    <div class="good-course-list" v-if="(courseList || []).length">
      <div class="top">
        <span class="title">精选好文</span>
        <span class="link" @click="toActiveDetail" :dt-areaid="dtMoerCourses('area', '1')" :dt-eid="dtMoerCourses('eid', '1')" :dt-remark="dtMoerCourses('remark', '1')">查看更多</span>
      </div>
      <!-- <div class="label-tab">
        <div @click="handelrClicekActive({ key: '0' })" :class="['label-item-tab', { 'item-active': active === '0'}]">全部好文</div>
        <div @click="handelrClicekActive(item)" :class="['label-item-tab', { 'item-active': active === item.key}]" v-for="(item, index) in lableTabs" :key="index">{{item.value}}</div>
      </div> -->
      <div class="list-content">
        <div class="item" v-for="item in courseList || []" :key="item.course_id" @click="toCourseDetail(item.course_url)">
          <div class="cover">
            <!-- 走内容中心 -->
            <van-image class="cover-img" :src="getCourseCoverUrl(item.course_pic_id)">
              <template v-slot:error>
                <img class="cover-img" src="../../../../assets/img/mobile/geekBang/err-cover-img.png" />
              </template>
            </van-image>
            <div class="time" v-if="item.course_length">
              {{ item.course_length }}分钟
            </div>
          </div>
          <div class="text-main">
            <div class="two-line">
              <span class="course-type">{{item.course_from_name}}</span>
              <span class="title">{{ item.course_name }}</span>
            </div>
            <div class="desc two-line">{{ processString(item.course_desc || '') }}</div>
            <!-- <div class="desc two-line" v-html="item.course_desc"></div> -->
          </div>
        </div>
      </div>
      <div class="link">
        <span @click="toActiveDetail" :dt-areaid="dtMoerCourses('area', '2')" :dt-eid="dtMoerCourses('eid', '2')" :dt-remark="dtMoerCourses('remark', '2')">点击查看更多</span>
      </div>
    </div>
    <!-- 复制链接 -->
    <harvardShare :xueBaCardConfig="xueBaCardConfig" :isShowGive.sync="isShowGive" giveType="account"></harvardShare>
    <pick-up v-model="showPick" :isQuantity="isQuantity" @handlerReceive="handlerReceive"></pick-up>
    <!-- 送出劝学卡 -->
    <div class="share-model" v-show="isShowHint && isShowFixedShear">
      <div class="model-content">
        <img src="../../../../assets/img/mobile/geekBang/share-right.png" alt="">
        <div class="model-footer">
          <span class="model-btn" @click="coloseShaerHint">知道了</span>
        </div>
      </div>
    </div>
    <!-- 送出记录 -->
    <invitation-record @handlerLoadingMore="handlerLoadingMore" :presentRecordList="presentRecordList" :total="total" :isShow.sync="isShowRecord"></invitation-record>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import moment from 'moment'
import env from 'config/env.conf.js'
import {
  getAcctinfos,
  getUserActiveInfo,
  getHomePageInfo,
  getPresentRecord,
  checkShareAuth,
  activityPresent,
  getRequestRecord,
  messageSubscribe,
  getSubscribeStatus
} from '@/config/mooc.api.conf.js'
// import giveCardMobile from './giveCardMobile.vue'
import harvardShare from './harvardShareMobile.vue'
import InvitationRecord from './InvitationRecord.vue'
import { Toast, Dialog } from 'vant'
import PickUp from './pickUp.vue'
const HTTPS_REG = /^https:\/\//
export default {
  components: {
    // giveCardMobile,
    harvardShare,
    PickUp,
    InvitationRecord
  },
  data() {
    return {
      showPick: false,
      isShowRecord: false,
      record: false,
      isShowGive: false,
      shareAuth: false,
      active: '0',
      current: 1,
      total: 0,
      presentRecordList: [],
      auth: false,
      xueBaCardConfig: {},
      cardInfo: {},
      consumePoint: 0,
      numberOfRewards: 0,
      courseList: [],
      lableTabs: [],
      recommendMap: {},
      isShowFixedShear: false,
      isFirst: false
    }
  },
  async created() {
    // 首次进入显示分享提示
    // if (localStorage.getItem('graphic_share_mini') !== 'true') {
    //   this.isShowFixedShear = true
    // }
    console.log(
      this.$route.query,
      'this.$route.querythis.$route.querythis.$route.query'
    )
    await this.getUserActiveInfo()
  },
  computed: {
    ...mapState(['userInfo']),
    isQuantity() {
      const { quantity = 0 } = this.xueBaCardConfig
      return quantity > 0
    },
    cardName() {
      return this.xueBaCardConfig.card_name || ''
    },
    activityId() {
      return this.$route.query.activityId || 1
    },
    isShowHint() {
      return this.shareAuth && this.isQuantity
    },
    topImgUrl() {
      // 顶部图片
      const { banner_img_top } = this.xueBaCardConfig
      if (!banner_img_top) return
      return banner_img_top.banner_mobile
    },
    userImgUrl() {
      return (val) => {
        if (!val) return ''
        let staffname = val.split(',')[0].split('(')[0]
        return `https://learn.woa.com/rhrc/photo/150/${staffname}.png`
      }
    },
    createTime() {
      return (date) => {
        return moment(date).format('YYYY-MM-DD')
      }
    },
    dtMoerCourses() {
      return (type, index) => {
        let { audience_id, audience_name } = this.xueBaCardConfig
        if (type === 'area') {
          return `area_${audience_id}_more_${index}`
        } else if (type === 'eid') {
          return `element_${audience_id}_more_${index}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${this.cardName}活动首页`,
            page_type: `${this.cardName}活动首页`,
            container: audience_name,
            container_id: '',
            click_type: 'button',
            content_name: '查看更多可兑换好课' + index,
            terminal: 'H5'
          })
        }
      }
    },
    // 埋点
    dtListBody() {
      return (item, type, index) => {
        let dt_id = `${item.course_from}_${item.course_id}`
        if (type === 'area') {
          return `area_${dt_id}`
        } else if (type === 'eid') {
          return `element_${dt_id}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${this.cardName}活动首页`,
            page_type: `${this.cardName}活动首页`,
            container:
              index <= 7
                ? `${this.xueBaCardConfig.audience_name}_8`
                : `${this.xueBaCardConfig.audience_name}_16`,
            container_id: '',
            click_type: 'data',
            content_type: '培养项目',
            act_type: '11',
            content_id: item.course_id,
            content_name: item.course_name,
            terminal: 'H5'
          })
        }
      }
    }
  },
  methods: {
    // 获取订阅转态
    async getSubscribeStatus() {
      // "first_" + acctTypeCode + "_"+ activityId
      let res = await getSubscribeStatus(`first_${this.xueBaCardConfig.acct_type_code}_${this.xueBaCardConfig.activity_id}`)
      console.log(res, '订阅状态')
      this.isFirst = !res
    },
    messageSubscribe() {
      messageSubscribe({}, `first_${this.xueBaCardConfig.acct_type_code}_${this.xueBaCardConfig.activity_id}`).then(
        (res) => {}
      )
    },
    // 接受邀请
    handlerReceive() {
      // 没有订阅说明是第一次进入需要自动订阅
      // if (this.isFirst && this.cardInfo.balance) {
      //   this.messageSubscribe()
      //   Toast({
      //     message: '成功解锁权限',
      //     icon: 'success'
      //   })
      //   this.showPick = false
      //   setTimeout(() => {
      //     this.getSubscribeStatus()
      //   }, 1000)
      //   return
      // }
      this.activityPresent()
    },
    // 获取他人赠送的卡券
    activityPresent() {
      let params = {
        from: this.$route.query.staff_id || 0,
        from_name: this.$route.query.staff_name || '腾讯学堂',
        acct_type_code: this.xueBaCardConfig.acct_type_code || '',
        to_batch: [this.userInfo.staff_id],
        object_id: this.xueBaCardConfig.activity_id,
        object_name: this.xueBaCardConfig.activity_name,
        object_type: 'XuebaActivity',
        object_type_name: '活动',
        trans_amt: '1',
        notify_type: 0 // 手动赠送1 ，用户进入页面自动领的 0
      }
      activityPresent(params)
        .then((res) => {
          if (res.success_count) {
            Toast({
              message: '成功解锁权限',
              icon: 'success'
            })
            this.showPick = false
            this.isFirst = false
            this.messageSubscribe()
          } else {
            Toast({
              message: '活动太火爆了，请稍后再试！',
              icon: 'error'
            })
          }
          console.log(res, '赠送接口')
          this.getAcctinfos()
          this.getHomePageInfoFn(true)
          this.checkShareAuth()
        })
        .catch(() => {
          Toast({
            message: '活动太火爆了，请稍后再试！',
            icon: 'error'
          })    
        })
    },
    // 提交需求
    applyperm() {
      if (this.record) return
      let params = {
        recourse_from: this.xueBaCardConfig.acct_type_code,
        course_id: ''
      }
      getRequestRecord(params).then((res) => {
        Toast({
          message: '提交成功',
          icon: 'passed'
        })
        this.record = true
      })
    },
    coloseShaerHint() {
      this.isShowFixedShear = false
      localStorage.setItem('graphic_share_mini', true)
    },
    // 分享权限
    async checkShareAuth() {
      const result = await checkShareAuth({
        type: this.xueBaCardConfig.acct_type_code
      })
      this.shareAuth = result
    },
    async getAcctinfos() {
      let params = {
        acct_type_codes: this.xueBaCardConfig.acct_type_code + 'Trans'
      }
      const result = await getAcctinfos(params)
      //   查看有没有余额有余额解锁赠送劝学卡
      this.consumePoint = Number(result.consume_point)
    },
    // 获取学霸卡基础信息
    async getUserActiveInfo() {
      try {
        let res = await getUserActiveInfo({ activity_id: this.activityId })
        console.log(res, '获取学霸卡基础信息')
        this.xueBaCardConfig = res
        this.recommendMap = res.recommend_map
        this.recommendMap['0'] = res.course_list
        this.courseList = this.recommendMap['0']
        this.lableTabs = Object.entries(res.audience_map).map(
          ([key, value]) => ({ key, value })
        )
        let SubscribeRes = await getSubscribeStatus(`first_${this.xueBaCardConfig.acct_type_code}_${this.xueBaCardConfig.activity_id}`)
        this.isFirst = !SubscribeRes
        this.getAcctinfos()
        this.getHomePageInfoFn()
        this.getPresentRecord(1)
        this.checkShareAuth()
      } catch (err) {
        console.log('获取基础信息: ', err)
        if (err.code) {
          let type = 0
          if (err.code === 403) {
            type = 3
            if (err.message.includes('权限')) {
              type = 6
            }
          } else if (err.code === 500) {
            type = 6
          }
          this.$router.replace({
            name: 'mobileError',
            params: {
              message: err.message
            },
            query: {
              type
            }
          })
        }
      }
    },
    // 积分授予-查询是否可以领取geek
    async getHomePageInfoFn() {
      try {
        let res = await getHomePageInfo({
          staff_id: this.userInfo.staff_id,
          acct_type_code: this.xueBaCardConfig.acct_type_code,
          activity_id: this.activityId
        })
        this.cardInfo = res
        // 无权限直接打开领取弹窗
        if (!this.cardInfo.balance) {
          this.showPick = true
        } else if (this.cardInfo.balance && this.isFirst) { // isFirst: true 首次进入需要调用弹窗
          this.openPermissionTips()
        }
      } catch (error) {
        console.error('积分授予-查询是否可以领取geek-error: ', error)
      }
    },
    // 权限弹窗
    openPermissionTips() {
      let message = this.cardInfo.balance
        ? `恭喜您已经成功订阅，获得哈佛精品文库的阅读权限！`
        : `很遗憾您暂时没有办法参与本次活动，您可以通过身边已有全权限的小伙伴的邀请获得权限，敬请留意哦。\n如有疑问请联系：minnaluan或v_xxyhe`
      Dialog.alert({
        title: '温馨提示',
        messageAlign: 'left',
        message: message,
        confirmButtonColor: '#0052d9',
        confirmButtonText: '确认',
        overlayClass: 'permission_tips_dialog' 
      }).then(() => {
        this.isFirst = false
        this.messageSubscribe()
        // localStorage.setItem('permission_tips', true)
      })
    },
    // 赠送的
    async getPresentRecord(current) {
      console.log(current, 'current')
      let params = {
        activityId: this.xueBaCardConfig.activity_id || this.activity_id,
        current: current,
        size: 10
      }
      const res = await getPresentRecord(params)
      console.log(res, '赠送')
      let records = res.records || []
      this.total = res.total
      if (current === 1) {
        this.presentRecordList = records
      } else {
        this.presentRecordList = this.presentRecordList.concat(records)
      }
    },
    handlerLoadingMore() {
      this.current = this.current + 1
      this.getPresentRecord(this.current)
    },
    openGive() {
      if (!this.isQuantity) return
      this.isShowGive = true
    },
    toActiveDetail() {
      let encodeUrl = encodeURIComponent(this.xueBaCardConfig.course_more_link)
      let url = '/pages/webview/index?href=' + encodeUrl
      window.wx.miniProgram.navigateTo({ url })
    },
    toCourseDetail(href) {
      let encodeUrl = encodeURIComponent(href)
      let url = '/pages/webview/index?href=' + encodeUrl
      window.wx.miniProgram.navigateTo({ url })
    },
    // 课程封面 内容中心逻辑
    getCourseCoverUrl(course_pic_id) {
      if (HTTPS_REG.test(course_pic_id)) {
        return course_pic_id
      }
      const envName = env[process.env.NODE_ENV]
      return `${envName.contentcenter}content-center/api/v1/content/imgage/${course_pic_id}/preview`
    },
    /**
     * 字符串去除标签类型的字符等
     * @param {string} str
     * @returns {string}
     */
    processString(str) {
      if (!str || typeof str !== 'string') {
        return ''
      }
      // 去除HTML标签
      str = str.replace(/<[^>]*>/g, '')
      // 去除特殊字符
      str = str.replace(/&nbsp;/g, ' ')
      str = str.replace(/&amp;/g, '&')
      str = str.replace(/&lt;/g, '<')
      str = str.replace(/&gt;/g, '>')
      // 可以继续添加其他特殊字符的替换规则
      return str
    },
    handelrClicekActive(val) {
      this.active = val.key
      this.courseList = this.recommendMap[val.key]
    },
    // 赠送劝学卡后刷新赠送列表
    handlerGiveXuebaka() {
      this.getPresentRecord(1)
      this.getAcctinfos()
    },
    // 活动规则
    toRulesDetail() {
      let encodeUrl = encodeURIComponent(
        this.xueBaCardConfig.activity_detail_link
      )
      let url = '/pages/webview/index?href=' + encodeUrl
      window.wx.miniProgram.navigateTo({ url })
    },
    openRecord() {
      this.isShowRecord = true
    }
  }
}
</script>
<style lang="less">
</style>
<style lang="less" scoped>
* {
  box-sizing: border-box;
}
.active-page {
  overflow-y: auto;
  height: 100%;
  background: #f4faff;
  padding-bottom: 21px;
  /deep/.van-overlay {
    // z-index: 1999 !important;
  }
  .top-img {
    width: 100%;
  }
  .content-main {
    position: relative;
    padding: 0 16px;
    margin: -44px auto 0;
    .active-rule {
      padding: 12px;
      background-color: #fff;
      border-radius: 16px;
      box-shadow: 0 0 8px 0 #b5d0e30a;
      &-text {
        color: #000000e6;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
      .active-rule-btn {
        border-radius: 6px;
        background: var(---Brand1-Light, #ecf2fe);
        width: 100%;
        height: 32px;
        line-height: 32px;
        text-align: center;
        color: #0052d9;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        margin-top: 12px;
      }
    }
    .my-authority {
      padding: 16px 20px;
      background-color: #fff;
      box-shadow: 0 0 8px 0 #b5d0e30a;
      border-radius: 16px;
      margin-top: 12px;
      .auth-text {
        align-self: stretch;
        color: #000000e6;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 600;
        line-height: 22px;
      }
      .auth-show {
        display: flex;
        justify-content: space-between;
        margin-top: 16px;
        .show-img {
          width: 24px;
          height: 24px;
          margin-right: 8px;
        }
        &-left {
          position: relative;
          width: 146px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          border-radius: 6px;
          background: #e8f8f2;
        }
        &-right {
          position: relative;
          width: 146px;
          height: 40px;
          line-height: 40px;
          text-align: center;
          border-radius: 6px;
          background: #e5eeff;
        }
        &-after1::after {
          content: '';
          position: absolute;
          left: 0;
          width: 16px;
          height: 16px;
          border-radius: 6px;
          background: url('../../../../assets/img/mobile/geekBang/left-auth.png')
            no-repeat;
          background-size: 100% 100%;
        }
        &-after2::after {
          content: '';
          position: absolute;
          left: 0;
          width: 16px;
          height: 16px;
          border-radius: 6px;
          background: url('../../../../assets/img/mobile/geekBang/right-auth.png')
            no-repeat;
          background-size: 100% 100%;
        }
      }
      .request-auth {
        color: #ffffff;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 44px;
        width: 100%;
        height: 44px;
        text-align: center;
        background-color: #0052d9;
        border-radius: 8px;
        margin-top: 12px;
      }
    }
  }
  .color_b {
    color: #0052d9ff;
    font-weight: 600;
  }
  .give-box {
    margin: 12px 16px 0;
    padding: 14px 12px;
    background-color: #fff;
    border-radius: 16px;
    .give-title {
      color: #000000e6;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
      margin-bottom: 12px;
    }
    .give-tips {
      border-radius: 4px;
      background: #f8f8f8;
      padding: 8px 12px;
      color: #00000099;
      font-size: 14px;
      line-height: 22px;
    }
    .tab-content {
      background-color: #fff;
      &_tips {
        color: #000000e6;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
      &_btn {
        display: flex;
        height: 48px;
        // line-height: 48px;
        padding: 6px 12px;
        justify-content: center;
        align-items: center;
        gap: 8px;
        align-self: stretch;
        border-radius: 8px;
        background: #eff4ffff;
        color: #0052d9ff;
        font-size: 16px;
        font-weight: 600;
        margin-top: 12px;
        img {
          width: 20px;
          height: 20px;
        }
      }
      &_btn_disabled {
        background: #ffe6c1ff;
        color: #cb5500ff;
      }
      &_item {
        display: flex;
        padding: 20px 12px;
        border-radius: 8px;
        border: 0.5px solid #eee;
        margin-top: 12px;
        background: linear-gradient(
          0deg,
          #fdffff 36.9%,
          #fbfeff 77.92%,
          #f9fffe 100%
        );
        &-left {
          margin-right: 6px;
          img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
          }
        }
        &-right {
          margin-left: 16px;
          line-height: 20px;
          font-size: 12px;
          overflow: hidden;
          .name {
            display: block;
            color: #333333ff;
            font-family: 'PingFang SC';
            font-size: 14px;
            font-weight: 500;
            line-height: 22px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .number1 {
            align-self: stretch;
            color: #666666ff;
            font-family: 'PingFang SC';
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
          }
          .number2 {
            color: #999999ff;
            font-family: 'PingFang SC';
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
          }
        }
      }
      &_null {
        &-right {
          img {
            position: relative;
            left: 50%;
            transform: translateX(-50%);
            width: 128px;
            height: 128px;
            margin: 12px 0 8px;
          }
        }
        &-footer {
          color: #666666ff;
          text-align: center;
          font-family: 'PingFang SC';
          font-size: 12px;
          font-style: normal;
          font-weight: 400;
          line-height: 20px;
        }
        &-left {
          img {
            position: relative;
            left: 50%;
            transform: translateX(-50%);
            width: 128px;
            height: 128px;
            margin: 8px 0;
          }
        }
      }
      .loading-moer {
        padding-top: 10px;
        text-align: center;
        color: #0052d9ff;
      }
    }
  }
  .good-course-list {
    margin: 12px 16px;
    padding: 20px;
    border-radius: 16px;
    background: #fff;
    box-shadow: 0 0 8px 0 #b5d0e30a;
    .top {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .title {
        color: #000000e6;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
      }
      .link {
        margin-left: 20px;
        color: #0052d9;
        font-size: 14px;
        line-height: 22px;
      }
    }
    .label-tab {
      display: flex;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch; /* 在移动设备上优化滚动 */
      margin: 16px 0 4px;
      .label-item-tab {
        flex-shrink: 0;
        padding: 0 8px;
        height: 24px;
        border-radius: 4px;
        background: #f5f7fa;
        line-height: 24px;
        margin-right: 8px;
      }
      .item-active {
        color: #fff;
        background-color: #0052d9ff;
      }
    }
    /* 隐藏滚动条样式 */
    .label-tab::-webkit-scrollbar {
      display: none; /* 对于Webkit浏览器 */
    }
    .list-content {
      .two-line {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2; /*显示两行*/
      }
      .item {
        display: flex;
        border-bottom: 0.5px solid #eee;
        padding: 12px 0;
        .cover {
          position: relative;
          margin-right: 12px;
          border-radius: 3px;
        }
        .cover-img {
          width: 129px;
          height: 86px;
          border-radius: 3px;
          img {
            border-radius: 3px;
          }
        }
        /deep/.van-image__error,
        /deep/ .van-image__img,
        /deep/.van-image__loading {
          border-radius: 3px;
        }
        .time {
          position: absolute;
          right: 8px;
          bottom: 8px;
          padding: 2px 12px;
          height: 20px;
          line-height: 20px;
          color: #ffffff;
          font-size: 12px;
          border-radius: 12px;
          background: #00000066;
        }
        .text-main {
          .course-type {
            margin-right: 5px;
            padding: 3px 4px;
            color: #0052d9;
            font-size: 12px;
            line-height: 12px;
            border-radius: 2px;
            background: #ebeffc;
          }
          .title {
            color: #000000e6;
            font-size: 14px;
            line-height: 22px;
          }
          .desc {
            margin-top: 2px;
            height: 40px;
            color: #00000099;
            font-size: 12px;
            line-height: 20px;
          }
        }
      }
      .item:last-child {
        border-bottom: none;
      }
    }
    & > .link {
      text-align: center;
      span {
        cursor: pointer;
        color: #0052d9;
        font-size: 14px;
        line-height: 22px;
        text-decoration-line: underline;
      }
    }
  }
  .share-model {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* 半透明遮罩层 */
    z-index: 3000; /* 确保遮罩层在其他内容之上 */
    .model-content {
      width: 227px;
      position: absolute;
      right: 5.3%;
      img {
        width: 100%;
        height: 100%;
      }
      .model-footer {
        padding-top: 20px;
        display: flex;
        justify-content: center;
        .model-btn {
          width: 86px;
          height: 34px;
          border-radius: 32px;
          border: 1px solid #ffffffcc;
          background: #474747;
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          color: #fff;
          line-height: 34px;
          text-align: center;
        }
      }
    }
  }
}
</style>
