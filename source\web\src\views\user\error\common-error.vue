<template>
  <div class="exception-page">
    <sdc-exception-page :code="code" home="/courselist/home">
      <p class="text"><slot></slot></p>
    </sdc-exception-page>
  </div>
</template>

<script>
export default {
  name: 'common-error',
  props: {
    code: {
      type: String,
      default: '401'
    }
  }
}
</script>

<style lang="less" scoped>
.exception-page {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow: hidden;
  :deep(.sdc-exception-page) {
    .title,
    .links,
    .info {
      display: none;
    }
    .text {
      color: #333;
      font-weight: 700;
      font-size: 18px;
    }
    .link,a {
      color: #0052D9;
    }
  }
}
</style>
