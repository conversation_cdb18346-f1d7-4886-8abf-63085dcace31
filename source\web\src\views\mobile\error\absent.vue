<template>
  <div class="not-tencent-employee-wrapper">
    <div class="header">
      <div class="title">出错啦</div>
    </div>
    <div class="sep"></div>
    <div class="body">
      <div class="info-1">{{ info1 }}</div>
      <div class="info-2">{{ info2 }}</div>
    </div>
  </div>
</template>

<script>

export default {
  data() {
    return {
      info1: 'something is wrong',
      info2: '若有疑问，请咨询小T（连线HR）'
    }
  },
  methods: {
    setErrorType (errorType) {
      switch (errorType) {
        // NOT_TENCENT_EMPLOYEE
        case 1:
          this.info1 = '仅针对腾讯员工开放哦'
          this.info2 = '如果您是腾讯员工，请确保微信号填写正确'
          break
        // NOT_SUPPORT_MOBILE
        case 2:
          this.info1 = '该内容暂不支持移动端'
          break
        // CONTENT_DO_NOT_EXIST
        case 3:
          this.info1 = '内容不存在或者已删除'
          break
        // PAGE_DO_NOT_EXIST
        case 4:
          this.info1 = '页面不存在'
          break
        case 5:
          this.info1 = '无权限'
          this.info2 = '暂无此课程的访问权限，请联系项目管理员进行调整'
          break
        default:
          break
      }
    }
  },
  mounted () {
    this.setErrorType(parseInt(this.$route.query.type))
  }
}
</script>

<style lang='less'>
// @import '../../common/style/common.less';
.not-tencent-employee-wrapper {
  width: 100%;
  height: 100%;
  background: #347ab6;
  display: flex;
  flex-direction: column;
  .header {
    background: #eee;
    width: 100%;
    height: 25%;
    display: flex;
    align-items: center;
    justify-content: center;
    .title {
      color: #CC3333;
      font-weight: bold;
      font-size: 40px;
      letter-spacing: 10px;
    }
  }
  .sep {
    width: 0;
    height: 0;
    border-right: 750px solid #eee;
    border-bottom: 40px solid transparent;
    background: #347ab6;
  }
  .body {
    width: 100%;
    flex-grow: 1;
    background: #347ab6;
    display: flex;
    flex-direction: column;
    align-items: center;
    color: #eee;
    padding-top: 25%;
    .info-1 {
      font-size: 24px;
      margin-bottom: 12px;
    }
    .info-3 {
      display: flex;
      align-items: center;
      .join-href {
        color: #33CC66;
        font-style: italic;
      }
    }
  }
  .footer {
    background: #347ab6;
    color: #eee;
    width: 100%;
    height: 30px;
    line-height: 30px;
    font-size: 12px;
    color: #eee;
    text-align: center;
  }
}
</style>
