<template>
  <van-popup
    class="add-course-popup"
    v-model="visible"
    round
    :overlay="true"
    position="bottom"
    get-container="body"
    :safe-area-inset-bottom="true"
    :close-on-click-overlay="true"
    @close="cancel"
  >
    <div class="main">
      <div class="popup-body">
        <div class="switch-btn" @click="cancel"><span class="btn"></span></div>
        <span 
          class="btn-box" 
          :dt-remark="dtBtn('remark')" 
          :dt-areaid="dtBtn('areaid')"
          :dt-eid="dtBtn('eid')"
        >
          <span class="icon-box">
            <span class="icon"></span>
            <span>{{ activefav ? '已添加到收藏夹' : '添加到收藏夹' }}</span>
          </span>
          <van-checkbox v-model="activefav"/>
        </span>
        <div class="title">{{ $langue('Article_AddCourseList', { defaultText: '添加到课单' }) }}</div>
        <van-list
          v-if="tableData.length"
          v-model="loading"
          :finished="finished"
          :finished-text="$langue('Mooc_Home_NoMore', { defaultText: '没有更多了' })"
          @load="onLoad"
          ref="pullRefreshList"
        >
          <van-checkbox-group v-model="checkGroup">
            <div
              class='item-list'
              v-for="(item, index) in tableData"
              :key="index"
              :dt-remark="dtList('remark', item)" 
              :dt-areaid="dtList('areaid', item)"
              :dt-eid="dtList('eid', item)"
            >
              <div  
              :class="[
                // { 'disabled-check-box': item.have_content },
                'check-box'
              ]">
                <span>{{ item.name }}</span>
                <van-checkbox
                  :name="item.cl_id"
                  ref="checkboxes"
                />
              </div>
              <div class="course-tips">{{item.content_count}}门课程 · {{ item.is_open ? '公开' : '私密' }}</div>
            </div>
          </van-checkbox-group>
        </van-list>
        <div v-if="!tableData.length && !refreshing" class="empty-list-box">
          <img :src="require('@/assets/img/mobile/empty-note.png')" />
          <div>{{ $langue('Mooc_Common_NoData', { defaultText: '暂无数据' }) }}</div>
        </div>
      </div>
      <div class="confirm-btn" @click="confirm" :dt-remark="dtBtn('remark', 'confirm')" :dt-areaid="dtBtn('areaid', 'confirm')" :dt-eid="dtBtn('eid', 'confirm')">{{ isBtnType ? '完成' : '取消'}}</div>
    </div>
  </van-popup>
</template>
<script>
import { 
  getUserCourseList, 
  addClassList, 
  netCheckFavorited, 
  netDeleteFavorite, 
  netAddFavorite,
  faceCheckFavorited,
  faceDeleteFavorite,
  faceAddFavorite,
  activityCheckFavorite,
  activityDeleteFavorite,
  activityAddFavorite
} from 'config/api.conf'
import { Toast } from 'vant'
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    courseData: {
      type: Object,
      default: () => ({})
    },
    courseType: {
      type: String,
      default: 'net'
    }
  },
  data() {
    return {
      size: 5,
      current: 1,
      checkGroup: [],
      tableData: [],
      loading: false,
      finished: false,
      refreshing: false,
      visible: false,
      activefav: false,
      oldActiveFav: false
    }
  },
  watch: {
    show(val) {
      this.visible = val
      if (val) {
        this.current = 1
        this.tableData = []
        // 滚动条回到顶部
        this.$nextTick(() => {
          if (this.$refs.pullRefreshList) {
            this.$refs.pullRefreshList.$el.scrollTop = 0
            // this.checkGroup = []
          }
        })
        this.onLoad()
        this.getCheckFav()
      }
    }
  },
  computed: {
    module() {
      let idObj = {
        face: { module_id: 2, module_name: '面授课' },
        net: { module_id: 1, module_name: '网络课' },
        activity: { module_id: 4, module_name: '活动' }
      }
      return idObj[this.courseType]
    },
    course_id() {
      return this.courseData.course_id || this.$route.query.course_id || this.$route.query.activity_id
    },
    isBtnType() {
      const oldList = this.tableData.filter((v) => v.have_content).map(v => v.cl_id)
      const istrue = !this.checkGroup.every(item => oldList.includes(item))
      const isFav = this.activefav !== this.oldActiveFav // 初始值
      return isFav || this.checkGroup.length !== oldList.length || istrue
    },
    dtBtn() {
      return (type, val) => {
        let name = ''
        if (val) {
          name = this.isBtnType ? '完成' : '取消'
        } else {
          name = this.activefav ? '添加到收藏夹' : '已添加到收藏夹'
        }
        const data = {
          page: this.courseData.course_name,
          page_type: this.module.module_name + '详情页面-移动新版', 
          container: '底部导航-添加课单',
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: name,
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'H5'
        }
        if (type === 'remark') {
          return JSON.stringify(data)
        } else if (type === 'eid') {
          return `element_${this.course_id}_${name}`
        } else {
          return `area_${this.course_id}_${name}`
        }
      }
    },
    dtList() {
      return (type, row) => {
        const data = {
          page: this.courseData.course_name,
          page_type: this.module.module_name + '详情页面-移动新版', 
          container: '底部导航-添加课单',
          click_type: 'data',
          content_type: '',
          content_id: '',
          content_name: row.name,
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'H5'
        }
        if (type === 'remark') {
          return JSON.stringify(data)
        } else if (type === 'eid') {
          return `element_${this.course_id}_${row.cl_id}`
        } else {
          return `area_${this.course_id}_${row.cl_id}`
        }
      }
    }
  },
  methods: {
    getCheckFav() {
      let objCheckFav = {
        'net': { http: netCheckFavorited, params: { 'net_course_id': this.course_id } },
        'face': { http: faceCheckFavorited, params: { 'course_id': this.course_id } },
        'activity': { http: activityCheckFavorite, params: { 'activity_id': this.course_id } }
      }
      let params = objCheckFav[this.courseType].params
      objCheckFav[this.courseType].http(params).then(res => {
        this.activefav = res
        this.oldActiveFav = res
      })
    },
    // 收藏
    handleFavorited() {
      let objFavorited = {
        'net': { 'addHttp': netAddFavorite, 'deleteHttp': netDeleteFavorite, 'params': { 'net_course_id': this.course_id } },
        'face': { 'addHttp': faceAddFavorite, 'deleteHttp': faceDeleteFavorite, 'params': { 'course_id': this.course_id } },
        'activity': { 'addHttp': activityAddFavorite, 'deleteHttp': activityDeleteFavorite, 'params': { 'activity_id': this.course_id } }
      }
      let params = objFavorited[this.courseType].params
      let praiseCommonAPI = null
      // let tip = null
      if (!this.activefav) {
        // praiseCommonAPI = this.courseType === 'face' ? faceDeleteFavorite : netDeleteFavorite
        praiseCommonAPI = objFavorited[this.courseType].deleteHttp
        this.courseData.fav_count--
        // tip = this.$langue('Mooc_Common_Alert_CancelCollectSucessed', { defaultText: '取消收藏成功' })
      } else {
        // praiseCommonAPI = this.courseType === 'face' ? faceAddFavorite : netAddFavorite
        praiseCommonAPI = objFavorited[this.courseType].addHttp
        this.courseData.fav_count++
        // tip = this.$langue('Mooc_Common_Alert_CollectSucessed', { defaultText: '收藏成功' })
      }
      this.$emit('update: courseData', this.courseData)
      praiseCommonAPI(params).then((data) => {
        this.getCheckFav()
        // if (data.credit && data.credit !== '0') {
        //   Toast(`${tip}, ${this.$langue('Mooc_Common_Alert_CommonPoint', { point: +data.credit, defaultText: `通用积分+${+data.credit}` })}`)
        // } else {
        //   Toast(`${tip}`)
        // }
      })
    },
    onLoad() {
      this.loading = true
      let params = {
        page_no: this.current++,
        page_size: this.size,
        module_id: this.module.module_id,
        item_id: this.course_id || ''
      }
      getUserCourseList(params).then((res) => {
        if (this.refreshing) {
          this.tableData = []
          this.refreshing = false
        }
        this.tableData.push(...res.records)
        // 复选框回显
        this.checkGroup = this.tableData.filter((v) => v.have_content).map(v => v.cl_id)
        this.loading = false
        if (this.tableData.length >= res.total) {
          this.finished = true
        }
      })
    },
    // 确认
    confirm() {
      // 取消
      if (!this.isBtnType) {
        this.cancel()
        return
      }
      this.handleFavorited()
      // if (!this.checkGroup?.length) {
      //   Toast(this.$langue('Mooc_TaskDetail_AddOneRecord', { defaultText: '请至少添加一条数据' }))
      //   return
      // }
      const { course_name, course_desc } = this.courseData
      const href = `https://ihr.tencent.com/5FD89y/${this.course_id}`
      const params = {
        is_mobile: true,
        cl_ids: this.checkGroup,
        content_details: [
          {
            content_name: course_name,
            description: course_desc,
            href,
            module_id: this.module.module_id,
            module_name: this.module.module_name,
            item_id: this.course_id
          }
        ]
      }
      addClassList(params).then((res) => {
        this.cancel()
        Toast('已完成')
      })
    },
    cancel() {
      this.visible = false
      this.$emit('update:show', false)
    },
    onRefresh() {
      // 清空列表数据
      this.finished = false
      // 重新加载数据
      this.tableData = []
      this.current = 1
      this.refreshing = true
      this.onLoad()
    },
    toggle(index) {
      this.$refs.checkboxes[index].toggle()
    }
  }
}
</script>
<style lang="less" scoped>
.add-course-popup {
  overflow-y: hidden;
  height: calc(100% - 180px);
  .main {
    position: relative;
    height: 100%;
  }
  .popup-body {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #F6F7F9;
    padding: 0 16px;
    .switch-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding-top: 8px;
      padding-bottom: 8px;
      .btn {
        width: 30px;
        height: 4px;
        border-radius: 12px;
        background: #D9D9D9;
      }
    }
    .btn-box {
      border-radius: 4px;
      background: #fff;
      font-size: 14px;
      line-height: 22px;
      color: #333333;
      padding: 10px 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .icon-box {
        display: flex;
        align-items: center;
      }
      .icon {
        display: inline-block;
        width: 16px;
        height: 16px;
        background: url('~@/assets/img/mobile/fav-data.png') no-repeat center / cover;
        margin-right: 8px;
      }
      // .active-like-btn {
      //   background: #FCF5E8;
      //   color: #FF8000;
      //   .icon {
      //     background: url('~@/assets/img/mobile/liked-icon.png') no-repeat center / cover;
      //   }
      // }
    }
    .van-list {
      width: 100%;
      height: 100%;
      overflow: auto;
      padding-bottom: 74px;
      background: #fff;
    }
    .empty-list-box {
      text-align: center;
      color: #000000e6;
      font-size: 14px;
      margin-top: 10px;
      img {
        width: 160px;
        height: 160px;
      }
    }
  }
  .item-list {
    border-bottom: 1px solid #efefef;
    padding: 13px 16px;
    .course-tips {
      color: #999999;
      font-size: 14px;
      font-weight: 400;
      line-height: 24px;
    }
    .check-box {
      margin-bottom: 4px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      color: #333333;
      font-size: 14px;
      line-height: 22px;
      :deep(.van-checkbox) {
        flex-shrink: 0;
      }
      span {
        max-width: 310px;
      }
    }
    .disabled-check-box {
      color: #00000042;
      :deep(.van-checkbox__icon--disabled) {
        .van-icon {
          background-color: #ebedf0;
          border-color: unset;
          color: #fff;
        }
      }
    }
  }
  .title {
    color: #333333;
    font-size: 14px;
    font-weight: bold;
    margin-top: 20px;
    margin-bottom: 12px;
    line-height: 22px;
  }
  .confirm-btn {
    position: fixed;
    bottom: 0px;
    width: 100%;
    height: 74px;
    line-height: 40px;
    text-align: center;
    color: #000000e6;
    font-size: 14px;
    border-top: 1px solid #ebedf0;
    background-color: #fff;
  }
}
</style>
