<template>
  <el-dialog title="添加项目标签" :visible.sync="visible" custom-class="dialog-center add-course-tag-container" width="762px"
    top="0" :close-on-click-modal="false" :before-close="cancel">
    <div class="item-add-tag-tips">
      <span>请从以下列表中选择标签，如果没有您想要的标签，</span>
      <custom-tag :addTags="addTags" @tagConfirm="handleSelect"></custom-tag>
    </div>
    <div class="chioce-tag">
      <div class="right-tips"><span>已选标签</span><span>一个项目最多支持5个标签</span></div>
      <el-tag class="default-el-tag custom-dark-tag" v-for="(tag, index) in addTags" :key="index" effect="plain" closable
        @close="handleClose(tag.label_name)">
        {{ tag.label_name }}
      </el-tag>
    </div>
    <div class="lately-use" v-if="latelyTagList.length > 0">
      <div class="right-tips">最近使用</div>
      <div>
        <ul>
          <el-tag :class="[{ 'custom-dark-tag': handleChecked(e) }, 'default-el-tag']" v-for="e in latelyTagList"
            :key="e.label_id" effect="plain" @click="handleSelect(e)">
            {{ e.label_name }}
          </el-tag>
        </ul>
      </div>
    </div>
    <div class="search-main">
      <el-form :model="form" inline>
        <el-form-item>
          <el-input clearable style="width: 230px" v-model.trim="form.label_name" @input="querySearch"
            placeholder="请输入标签名称"></el-input>
        </el-form-item>
        <el-form-item class="cascader-form-item">
          <el-cascader :props="{ checkStrictly: true }" clearable style="width: 230px" v-model="form.tagType"
            :options="tagTypeOptions" placeholder="请选择标签分类" @change="querySearch"></el-cascader>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain icon="el-icon-refresh" size="small" @click="handleReset">重置</el-button>
          <!-- <el-button type="primary" @click="querySearch">搜索</el-button> -->
        </el-form-item>
      </el-form>
      <div class="search-tag-list">
        <ul>
          <el-tag :class="[{ 'custom-dark-tag': handleChecked(e) }, 'default-el-tag']" v-for="(e) in searchTagList"
            :key="e.label_id" effect="plain" @click="handleSelect(e)">
            {{ e.label_name }}
          </el-tag>
        </ul>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel" size="small">取消</el-button>
      <el-button type="primary" @click="confirm" size="small">确定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { searchTag, latelyTenTagManage, getClassifyTree } from '@/config/mooc.api.conf'
// import editCourseId from '@/mixins/editCourseId.vue'
import CustomTag from './children/custom-tag.vue'
export default {
  // mixins: [editCourseId],
  components: {
    CustomTag
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      addTags: [],
      addCustomTagShow: false,
      loading: false,
      form: {
        label_name: '',
        tagType: []
      },
      latelyTagList: [],
      searchTagList: [],
      tagTypeOptions: []
    }
  },
  mounted() {
    this.querySearch()
    this.getTenTag()
    this.getTagType()
  },
  methods: {
    initData(list = []) {
      this.addTags = [].concat(list)
    },
    cancel() {
      this.$emit('update:visible', false)
    },
    handleClose(tag) {
      this.addTags.splice(this.addTags.findIndex((e) => e.label_name === tag), 1)
    },
    querySearch() {
      const { label_name, tagType } = this.form
      this.loading = true
      const params = {
        label_name,
        count: 500,
        act_type: 15,
        classify_full_path: tagType?.length ? tagType.join(',') : ''
      }
      searchTag(params).then((res) => {
        this.loading = false
        this.searchTagList = res.map((e) => ({
          label_name: e.name,
          label_id: e.label_id
        }))
      })
    },
    // 获取最近十个标签
    getTenTag() {
      // const commonTenTag = this.course_type_entry === 2 ? latelyTenTagManage : latelyTenTag
      latelyTenTagManage().then((res) => {
        this.latelyTagList = res
      })
    },
    // 标签分类
    getTagType() {
      getClassifyTree({ act_type: 15 }).then((res) => {
        this.tagTypeOptions = res.map((e) => {
          return {
            value: e.item_id,
            label: e.item_name,
            children: e.child.map((v) => {
              return {
                value: v.item_id,
                label: v.item_name
              }
            })
          }
        })
      })
    },
    // 重置
    handleReset() {
      this.form = {
        label_name: '',
        tagType: []
      }
      this.querySearch()
    },
    handleChecked(item) {
      return this.addTags.some((e) => e.label_id === item.label_id)
    },
    handleSelect(item) {
      // 选中自己删除
      const index = this.addTags.findIndex((e) => e.label_id === item.label_id)
      if (index > -1) {
        this.addTags.splice(index, 1)
        return
      }
      if (this.addTags?.length >= 5) {
        this.$message.warning('项目标签数量已达上限，无法继续添加')
        return
      }
      this.addTags.push(item)
    },
    showCheck(v) {
      return this.addTags.some((e) => e.label_id === v.label_id)
    },
    confirm() {
      this.cancel()
      this.$emit('confirmTagList', this.addTags)
    }
  }
}
</script>
<style lang="less">
.default-el-tag {
  border-radius: 12px;
  height: 24px;
  padding: 0 8px;
  line-height: 21px;
  color: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(220, 220, 220, 1);
  font-size: 12px;
  background-color: #fff;
}

.custom-dark-tag {
  color: #FFF;
  background-color: #0052D9;
  border-color: #0052D9;

  .el-tag__close {
    font-size: 12px;
    color: #FFF;
    border-radius: 50%;
    transform: scale(.8);
    position: relative;
    top: 0px;
    right: -2px;
  }
}
</style>
<style lang="less" scoped>
.add-course-tag-container {
  .el-tag {
    margin-right: 14px;
    margin-bottom: 16px;
    cursor: pointer;
  }

  .el-form {
    border-bottom: 1px solid #DCDCDC;

    .el-form-item {
      margin-bottom: 16px;
    }
  }

  .item-add-tag-tips {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    color: #000000
  }

  .right-tips {
    margin-bottom: 16px;
    color: #000000;

    span:last-of-type {
      color: #999999;
      margin-left: 16px;
    }
  }

  .chioce-tag {
    border-bottom: 1px solid #DCDCDC;
    margin-bottom: 16px;
  }

  .lately-use {
    ul {
      margin-top: 10px;
      margin-bottom: 10px;

      .el-tag {
        margin-bottom: 16px;
      }
    }
  }

  .search-main {

    // :deep(.el-button) {
    //   height: 32px;
    //   width: 80px;
    //   // padding: 8px 20px;
    // }
    .search-tag-list {
      margin-top: 12px;
      height: 200px;
      box-sizing: border-box;
      overflow: auto;
    }

    .last-tag-right {
      margin-right: unset;
    }

    .el-form-item {
      margin-right: unset
    }

    .cascader-form-item {
      margin-left: 32px;
      margin-right: 26px;
    }
  }
}
</style>
