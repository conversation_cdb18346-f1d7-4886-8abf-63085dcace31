<template>
  <div class="advanced-settings-popup">
    <el-dialog title="" :visible.sync="visible" width="564px" :close-on-click-modal="false" :before-close="handlerClose">
      <div slot="title" class="dialog-header">
        进阶设置
      </div>
      <div class="dialog-content">
        <el-form :model="form" ref="form" label-width="120px">
          <el-form-item label="参会人员设置 :">
            <el-radio-group v-model="form.refuse_join_type">
              <el-radio v-for="item in refuseJoinTypes" :label="item.value" :key="item.value">{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="会议录制和回看 :">
            <el-switch v-model="form.enable_meeting_record" :width="36" active-color="#0052D9"></el-switch>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="handlerClose" size="small">取 消</el-button>
        <el-button class="confirm-btn" type="primary" @click="handlerConfirm" size="small">保 存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'advancedSettingsPopup',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      refuseJoinTypes: [{ label: '仅限内部人员', value: 1 }, { label: '仅限目标学员', value: 2 }, { label: '仅限报名学员', value: 3 }],
      form: {
        refuse_join_type: 1,
        enable_meeting_record: true
      }
    }
  },
  watch: {
    formData: {
      handler(val) {
        this.form = { ...val }
      },
      immediate: true
    }
  },
  computed: {},
  created() { },
  mounted() { },
  beforeDestroy() { },
  methods: {
    handlerClose() {
      this.$refs.form.resetFields()
      this.$emit('update:visible', false)
    },
    handlerConfirm() {
      this.$emit('confirm', this.form)
      this.handlerClose()
    }
  }
}
</script>

<style lang="less" scoped>
  .advanced-settings-popup {
    :deep(.el-dialog) {
      border-radius: 9px;
      .el-dialog__header {
        border-bottom-color: #EEEEEE;
      }
      .el-dialog__body {
        padding: 20px 32px;
      }
      .el-dialog__headerbtn .el-dialog__close {
        color: #00000099;
        &:hover {
          color: #007aff;
        }
      }
      .el-dialog__footer {
        padding: 10px 32px 24px;
      }
      .el-form .el-form-item {
        margin-bottom: 14px;
      }
    }
    :deep(.el-radio) {
      font-weight: 400;
    }
    .cancel-btn {
      background-color: #E7E7E7;
      border-color: #E7E7E7;
      &:hover {
        opacity: 0.8;
      }
    }
    .confirm-btn {
      background-color: #0052D9;
      border-color: #0052D9;
      &:hover {
        opacity: 0.8;
      }
    }
  }
</style>
