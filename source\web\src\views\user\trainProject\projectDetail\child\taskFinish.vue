<template>
  <div class="task-finish">
    <div class="content">
      <img
        :src="require('@/assets/mooc-img/task-finish.png')"
        alt=""
        class="finish-icon"
      />
      <p class="tips1">当前任务已完成，可继续项目学习</p>
      <p class="back-project" @click="postMessageToParent('back')">回到项目任务列表</p>
      <div class="btn-box">
        <el-button plain @click="postMessageToParent('prevTask')" v-show="prevTaskBtnShow" :style="nextTaskBtnShow ? 'margin-right:14px' : 'margin-right:0' ">上一个任务</el-button>
        <el-button type="primary" @click="postMessageToParent('nextTask')" v-show="nextTaskBtnShow">下一个任务</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import MoocJs from 'sdc-moocjs-integrator'
export default {
  mounted () {
    MoocJs.postMessage('questionTaskDone')
    MoocJs.complete()
    MoocJs.messageListener((res) => {
      let { params } = res
      this.nextTaskBtnShow = params.nextTaskBtnShow
      this.prevTaskBtnShow = params.prevTaskBtnShow
    })
  },
  data () {
    return {
      nextTaskBtnShow: true,
      prevTaskBtnShow: true
    }
  },
  methods: {
    postMessageToParent(type = '') {
      let data = {
        methodsType: type,
        from: 'questionnaire'
      }
      // window.parent && window.parent.postMessage(data, '*')
      MoocJs.postMessage('questionPageClick', data)
    }
  }
}
</script>

<style lang="less">
.task-finish {
  padding: 16px 0 0 0;
  .content {
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 794px;
    width: 1000px;
    background: #fff;
    border-radius: 4px;
    .finish-icon {
      width: 160px;
    }
    .tips1 {
      margin-top: 24px;
      color: #000000;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
    }
    .back-project {
      margin-top: 24px;
      color: #0052d9;
      text-align: right;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 14px;
      cursor: pointer;
    }
    .btn-box {
      display: flex;
      margin-top: 24px;
    }
  }
}
</style>
