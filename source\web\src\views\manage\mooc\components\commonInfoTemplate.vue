<template>
  <div class="commonInfoTemplate-dialog">
    <el-dialog
      :visible.sync="visible"
      width="950px"
      :close-on-click-modal="false"
      :before-close="closeDialog"
      :modal-append-to-body="false"
    >
      <!-- 自定义标题 -->
      <div slot="title" class="dialog-title">
        <span>{{ dialogInfo.dialogTitle }}</span>
        <el-popover
          placement="bottom-start"
          trigger="hover"
          :width="previewImg[previewType]?.type == 'short' ? 300 : 900"
          popper-class="popper-example-box"
        >
          <img
            :src="previewImg[previewType]?.imgSrc"
            alt=""
          />
          <span
            v-show="['myoa', 'hrAssistant'].includes(form.template_type)"
            slot="reference"
            class="check-example"
            >查看示例</span
          >
        </el-popover>
      </div>
      <!-- 变量说明 -->
      <div class="top-tips" v-if="form.template_type !== 'hrAssistant'">
        <div class="tips-title">
          <span class="label">变量说明：</span
          >{{ dialogInfo.tips ? dialogInfo.tips : '变量可在标题和正文中使用' }}
        </div>
        <div class="top-content">
          <div class="card" v-for="item in form.list" :key="item.param_title">
            <div class="item-value">
              {{ item.param_title }}：<span>{{ item.param_name }}</span>
            </div>
          </div>
        </div>
      </div>
      <CustomTips
        v-if="form.template_type === 'hrAssistant'"
        lineHeight="30px"
        title="受限于微信消息规范无法随便变更模板，以下为消息模板示例"
        IconName="el-icon-warning"
        backgroundColor="#fdf6ec"
        color="#FF7548"
        style="margin-bottom: 20px"
      >
      </CustomTips>
      <!-- 修改模板-查看模板 -->
      <el-form
        :model="form"
        :rules="rules"
        ref="form"
        label-position="left"
        label-width="90px"
      >
        <el-form-item
          :label="dialogInfo.title"
          prop="every_day"
          class="input-style"
        >
          <div v-if="checkStatus">
            <el-input
              v-model="form.title"
              style="width: 646px"
              placeholder="请输入标题"
              clearable
            ></el-input>
            <span class="custom-el-input-count"
              >{{ handleValidor(form.title, 50, '1') }}/50</span
            >
          </div>
          <div v-else>
            <span>{{ form.title }}</span>
          </div>
        </el-form-item>
        <el-form-item
          v-if="form.template_type === 'myoa'"
          class="basic-show-box"
        >
          <span class="basic-label">基础信息显示字段：</span>
          <el-checkbox-group v-model="basicList" :disabled="!checkStatus">
            <el-checkbox label="title">项目名称</el-checkbox>
            <el-checkbox label="progress">应学进度</el-checkbox>
            <el-checkbox label="joinTime">加入时间</el-checkbox>
            <el-checkbox label="projectTime">项目学习时间</el-checkbox>
            <el-checkbox label="projectUrl">培训链接</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <!-- hr通知正文 -->
        <el-form-item
          :label="dialogInfo.contentLabel"
          v-if="form.template_type === 'hrAssistant'"
        >
          <p v-html="form.list" style="line-height: 16px; margin-top: 8px"></p>
        </el-form-item>
        <!-- 其他模板 -->
        <el-form-item
          :label="dialogInfo.contentLabel"
          class="template-tincy"
          v-else
        >
          <sdc-mce-editor
            v-if="
              checkStatus &&
              ['mail', 'leader', 'deptBp'].includes(form.template_type)
            "
            ref="editor"
            :env="editorEnv"
            :content="form.content"
            :catalogue.sync="editorConfig.catalogue"
            :urlConfig="editorConfig.urlConfig"
            :options="editorConfig.options"
            :insertItems="insertItems"
          />
          <div v-else-if="checkStatus">
            <el-input
              class="texTarea-input"
              type="textarea"
              :rows="5"
              placeholder="请输入项目简介"
              v-model="form.content"
            >
            </el-input>
            <span class="custom-el-input-count"
              >{{ handleValidor(form.content, wordNum, '2') }}/{{
                wordNum
              }}</span
            >
          </div>
          <p v-else v-html="form.content"></p>
        </el-form-item>
      </el-form>
      <div slot="footer" v-if="checkStatus" class="dialog-footer">
        <convention-confirm v-model="isChooseConvention" />
        <el-button style="margin-left: auto;" @click="closeDialog">取 消</el-button>
        <el-button type="primary" :disabled="!isChooseConvention" @click="handleSave">保存模板</el-button>
      </div>
      <div slot="footer" v-else class="dialog-footer">
        <el-button style="margin-left: auto;" @click="closeDialog" size="small">关 闭</el-button>
        <el-button
          type="primary"
          :disabled="form.template_type === 'hrAssistant' "
          size="small"
          @click="editTemplate"
          >修改模板</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { templateEdit } from '@/config/mooc.api.conf'
import CustomTips from '@/components/tips.vue'
import conventionConfirm from '@/views/components/convention-confirm.vue'
export default {
  components: {
    CustomTips,
    conventionConfirm
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    donCloseAfterSaving: {
      type: Boolean,
      default: false
    },
    templateType: {
      type: String,
      default: ''
    },
    previewType: {
      type: String,
      default: 'remindWxAssistant'
    }
  },
  watch: {
    visible(val) {
      if (!val) {
        this.isChooseConvention = false
      }
    }
  },
  data() {
    return {
      form: {
        list: []
      },
      checkStatus: true,
      rules: {},
      basicList: [],
      insertItems: [
        'link',
        'table',
        'image',
        'profile',
        'embed',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorConfig: {
        catalogue: false,
        options: {
          // 非必传字段
          selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
            formatselect fontsizeselect lineheight |
            bold italic underline strikethrough |
            forecolor backcolor |
            dent align bullist numlist |
            insert codesample |
            fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      },
      editorEnv: process.env.NODE_ENV,
      previewImg: {
        // 加入项目提醒 - 微信hr助手
        remindWxAssistant: {
          imgSrc: 'https://xue.m.tencent.com/mooc/hr-assistant-join-demo.png',
          type: 'short'
        },
        // 催办 - MyOa
        fasterMyOa: {
          imgSrc: 'https://xue.m.tencent.com/mooc/myoa-demo.png',
          type: 'long'
        },
        // 催办 - 微信hr助手
        fasterWxAssistant: {
          imgSrc: 'https://xue.m.tencent.com/mooc/hr-assistant-demo.jpg',
          type: 'short'
        }
      },
      isChooseConvention: false
    }
  },
  computed: {
    dialogInfo() {
      const { template_type } = this.form
      let info = {}
      if (template_type === 'mail') {
        info = {
          dialogTitle:
            this.templateType === 'atuoAdd'
              ? '加入项目提醒消息模版-邮件'
              : '催办消息模板-邮件',
          title: '邮件标题：',
          contentLabel: '邮件正文：'
        }
      } else if (template_type === 'tips') {
        info = {
          dialogTitle:
            this.templateType === 'atuoAdd'
              ? '加入项目提醒消息模版-企微Tips'
              : '催办消息模板-企微Tips',
          title: 'Tips标题：',
          contentLabel: 'Tips正文：'
        }
      } else if (template_type === 'bot') {
        info = {
          dialogTitle:
            this.templateType === 'atuoAdd'
              ? '加入项目提醒消息模版-企微机器人'
              : '催办消息模板-企微机器人',
          title: 'Tips标题：',
          contentLabel: 'Tips正文：'
        }
      } else if (template_type === 'hrAssistant') {
        info = {
          dialogTitle:
            this.templateType === 'atuoAdd'
              ? '加入项目提醒消息模板-HR助手'
              : '催办消息模板-HR助手',
          title: '通知标题：',
          contentLabel: '通知正文：'
        }
      } else if (template_type === 'myoa') {
        info = {
          dialogTitle: `催办消息模板-MyOA`,
          title: '标题：',
          contentLabel: '说明：'
        }
      } else if (template_type === 'leader') {
        info = {
          dialogTitle: '催办同步提醒邮件-直接上级',
          title: '邮件标题：',
          contentLabel: '邮件正文：',
          tips: '未完成学员列表变仅支持在正文中使用，其他变量在标题和正文均可使用'
        }
      } else {
        // bp
        info = {
          dialogTitle: '催办同步提醒邮件-组织BP',
          title: '邮件标题：',
          contentLabel: '邮件正文：',
          tips: '未完成学员列表变仅支持在正文中使用，其他变量在标题和正文均可使用'
        }
      }
      return info
    },
    wordNum() {
      return this.form.template_type === 'bot' ? 1000 : 500
    }
  },
  methods: {
    // 初始化数据
    initData(data) {
      this.checkStatus = false
      this.form = data
      this.form.list =
        this.form.template_type === 'hrAssistant'
          ? data.description.replace(/\n/g, '</br>')
          : JSON.parse(data.description)
      this.basicList =
        (data.choose_basic_infos?.length &&
          data.choose_basic_infos.split(',')) ||
        []
    },
    // 修改模板
    editTemplate() {
      this.checkStatus = true
    },
    // 保存模板
    handleSave() {
      const { mooc_course_id } = this.$route.query
      const { content, title, id, template_id, template_type, module_name } =
        this.form
      let contentEdit = ''
      if (
        this.checkStatus &&
        ['mail', 'leader', 'deptBp'].includes(this.form.template_type)
      ) {
        contentEdit = this.$refs.editor.getContent()
      } else {
        contentEdit = content
      }
      const params = {
        mooc_course_id,
        id: id || '',
        template_id,
        title,
        content: contentEdit,
        template_type,
        module_name,
        description: JSON.stringify(this.form.list)
      }
      if (this.form.template_type === 'myoa') {
        params.choose_basic_infos = this.basicList.join(',')
      }
      templateEdit(params).then((res) => {
        this.isChooseConvention = false
        this.checkStatus = false
        this.$message.success('保存成功')
        if (this.donCloseAfterSaving) return
        this.closeDialog()
      })
    },
    closeDialog() {
      this.$emit('update:visible', false)
    },
    handleValidor(value, num, type) {
      if (!value) {
        return 0
      }
      const china = value.match(/[\u4e00-\u9fa5]/g)
      const zhCount = china && china.join('').length
      const enCount = Math.ceil((value.length - zhCount) / 2)
      const total = zhCount + enCount
      if (total > num) {
        if (type === '1') {
          this.form.title = value.slice(0, -1)
        } else {
          this.form.content = value.slice(0, -1)
        }
      }
      return zhCount + enCount
    }
  }
}
</script>
<style lang="less">
.template-tincy {
  width: 786px;
  .tox.tox-tinymce {
    border: 1px solid #ccc !important;
    height: 450px;
    .tox-sidebar-wrap .tox-edit-area {
      min-height: 300px !important;
    }
  }
  .texTarea-input {
    .el-textarea__inner {
      height: 300px !important;
    }
  }
}
.popper-example-box {
  img {
    width: 100%;
  }
}
</style>
<style lang="less" scoped>
.commonInfoTemplate-dialog {
  .dialog-title {
    .check-example {
      color: #0052d9;
      font-size: 14px;
      margin-left: 20px;
      cursor: pointer;
    }
  }
  .top-tips {
    font-size: 16px;
    background-color: #f2f6fc;
    padding: 16px 20px;
    margin-bottom: 20px;
    .tips-title {
      margin-bottom: 16px;
      .label {
        color: black;
        font-weight: bold;
      }
    }
    .top-content {
      margin-bottom: 16px;
      display: flex;
      width: 900px;
      flex-wrap: wrap;
      .card {
        width: 300px;
      }
      .card:nth-child(n + 4) {
        margin-top: 16px;
      }
    }
  }
  .custom-el-input-count {
    color: #acacac;
    background: #fff;
    position: absolute;
    font-size: 12px;
    bottom: 6px;
    right: 6px;
    line-height: 20px;
  }
  .input-style {
    position: relative;
    :deep(.el-form-item__content) {
      width: 646px;
    }
    :deep(.el-input) {
      .el-input__inner {
        padding-right: 70px;
      }
      .el-input__suffix {
        position: absolute;
        right: 43px;
      }
    }
  }
  .basic-show-box {
    .basic-label {
      color: rgba(0, 0, 0, 0.6);
      margin-left: -90px;
    }
    .el-checkbox-group {
      margin-left: -90px;
    }
  }
  .dialog-footer {
    display: flex;
    align-items: center;
  }
}
</style>
