<template>
  <div class="material-container">
    <docPreview 
      v-if="materialData.file_type =='Doc'" 
      class="article-container" 
      :courseData="materialData"></docPreview>
      <articlePreview 
      v-else-if="materialData.file_type =='Article'" 
      class="article-container" 
      :courseData="materialData"></articlePreview>
    <div v-else class="other-container">
      <div class="main-fixed">
        <Scorm 
          v-if="['Flash', 'Scorm', 'Doc'].includes(materialData.file_type)"
          class="scorm-box"
          :courseData="materialData"
          @handleScormRecord="handleScormRecord"
          />
        <Video 
          class="video-box"
          ref="videoRef"
          v-else-if="['Video', 'Video-2d', 'Video-3d', 'Video-ppt', 'Audio'].includes(materialData.file_type)"
          :content_id.sync="materialData.content_id"
          @getCurrentTime="getCurrentTime"
          @getCurCaption="getCurCaption" 
          @handleRecord="handleRecord"
          @fullscreenchange="fullscreenchange"
          :vertical="isVertical"
          :playTime="playTime"
          :volumePanel="false"
          :autoPlay="false"
          :source_src="materialData.file_url"
          >
        </Video>
        <div v-else class="video-box">
          <van-image
            lazy
            fit="fill"
            :src="materialData.photo_url ? materialData.photo_url : require('@/assets/img/default_bg_img.png')"
          >
          </van-image>
        </div>
        <div class="tab-nav" v-if="materialData.file_type !=='Scorm'">
          <span @click="changeTab(index)" class="tab-pane" :class="{'active-pane': index === active}" v-for="(item, index) in tabList" :key="index">{{ $langue(item.text, { defaultText: item.title }) }}</span>
        </div>
      </div>
      <div :class="[ materialData.file_type === 'Scorm' ? 'no-has-tabs' : 'has-tabs', 'main-content']" :style="isFullscreen ? 'display: none' : '' ">
        <!-- 简介 -->
        <div v-show="active === 0" class="des-main">
          <div class="title-box" v-if="!isFormMooc">
            <span class="title-left">
              <span class="tag">{{filterResourceName}}</span>
              <span class="title">{{ materialData.file_show_name }}</span>
            </span>
          </div>
          <div class="title-info" v-if="!isFormMooc">
            <!-- <span class="item-icon"><span class="play-i"></span>{{ materialData.view_count }}</span> -->
            <span class="item-name" v-if="materialData.creator_name">{{ materialData.creator_name }}</span>
            <span>{{ materialData.created_at || '创建时间: --' }}</span>
          </div>
          <!-- 简介 -->
          <!-- <div v-if="showDes" class="des-content" @click="showDes = !showDes">{{ materialData.file_desc_text}}</div>
          <div v-else class="all-des-content" @click="showDes = !showDes" v-html="materialData.file_desc"></div> -->
          <div v-if="materialData.file_desc" class="des-content" v-html="materialData.file_desc"></div>
          <div class="des-content" v-else>暂无简介</div>
        </div>
        <!-- 文稿 -->
        <textDraft 
          v-show="active === 1 && captionData && captionData.length" 
          ref="textDraftRef" 
          :captionData.sync="captionData"
          @toCaption="toCaption"
        >
        </textDraft>
      </div>
    </div>
  </div>
</template>
<script>
import { Video, Scorm } from '@/components/index'
import { getMateriaDetail } from 'config/mooc.api.conf'
import textDraft from './textDraft.vue'
import docPreview from './docPreview.vue'
import articlePreview from './articlePreview.vue'
import axios from 'axios'
import MoocJs from 'sdc-moocjs-integrator'
import { pageExposure } from '@/utils/tools.js'

export default {
  components: {
    Video,
    Scorm,
    textDraft,
    docPreview,
    articlePreview
  },
  data() {
    return {
      active: 0,
      materialData: {},
      captionData: [],
      countTimer: null,
      viewTimer: null,
      playTime: 0,
      extandList: [],
      tabList: [
        { title: '简介', key: 'des', text: 'Mooc_TaskDetail_Audio_Description' }
      ],
      loading: false,
      showDes: true,
      isVertical: false, // 是否横屏
      isFullscreen: false
    }
  },
  computed: {
    material_id() {
      return this.$route.query.material_id
    },
    isFormMooc() {
      return this.$route.query.from === 'mooc' || this.$route.query.from === 'spoc'
    },
    filterResourceName() {
      let { file_type } = this.materialData
      let name = ''
      if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(file_type)) {
        name = '视频'
      } else if (file_type === 'Audio') {
        name = '音频'
      } else if (file_type === 'Article') {
        name = '文章'
      } else if (file_type === 'Doc') {
        name = '文档'
      } else if (file_type === 'Scorm') {
        name = 'Scorm'
      } else if (file_type === 'Flash') {
        name = '压缩包'
      }
      return name
    }
  },
  mounted() {
    this.getCourseDetail()

    if (this.isFormMooc) {
      MoocJs.setPause(() => {
        this.$refs.videoRef.vedioPlayer.pause()
      })
      MoocJs.setPlay(() => {
        this.$refs.videoRef.vedioPlayer.play()
      })
    }
  },
  beforeDestroy() {
    MoocJs.removeEvent()
  },
  methods: {
    fullscreenchange(data) {
      this.isFullscreen = data
      this.isVertical = this.isFullscreen
      window.parent && window.parent.postMessage({ isVertical: data, from: 'material' }, '*')
    },
    getCurCaption(data) {
      if (!this.captionData.length > 0) this.captionData = data
      let flag = this.tabList.map(item => item.key).includes('text')
      if (this.captionData && this.captionData.length && !flag) {
        this.tabList.push({ title: '文稿', key: 'text', text: 'NetCourse_TextContent' })
        this.active = 1
      }
    },
    handleRecord(param) {
      if (param.evt === 'play' && this.isFormMooc) {
        MoocJs.play()
      } 
      if (param.evt === 'pause' && this.isFormMooc) {
        MoocJs.pause()
      }
      if (param.evt === 'ended' && this.isFormMooc) {
        MoocJs.complete()
      }
    },
    handleScormRecord() {

    },
    // 监听实时播放
    getCurrentTime(time) {
      // 文稿滚动内容
      this.$nextTick(() => {
        if (this.$refs.textDraftRef) {
          this.$refs.textDraftRef.scrollTopContent(time)
        }
      })
    },
    getCourseDetail() {
      // const { cl_id } = this.$route.query
      getMateriaDetail(this.material_id).then((data) => {
        document.title = data.file_show_name
        this.materialData = data
        this.materialData.file_type = data.file_type ? data.file_type.slice(0, 1).toUpperCase() + data.file_type.slice(1).toLowerCase() : ''
        // const src = process.env.NODE_ENV === 'production' ? `https://learn.woa.com/mobilenet/net?act_id=${this.course_id}` : `https://test-learn.woa.com/mobilenet/net?act_id=${this.course_id}`
        // // 兼容处理，如果content_id没有跳转到v8
        // if (!data?.content_id && data?.cl_id) { // 网课
        //   const url = `${src}&jump_from=CourseList&project=0&source=ql&from=CourseList&area_id=${cl_id}`
        //   window.location.href = url
        // } else if (!data?.content_id) { // 网络课移动化
        //   window.location.href = src
        // }
        if (data.captions?.length > 0) this.readCaptionFile(data.captions)

        // 详情页曝光上报
        pageExposure({
          page_type: '移动端素材播放页',
          content_type: '素材',
          act_type: '21',
          content_name: data.file_show_name,
          content_id: this.material_id,
          terminal: 'H5'
        })
      }).catch(err => {
        if (this.isFormMooc && err.code === 500) {
          MoocJs.sendErrorInfo(err.message)
        }
      })
    },
    // 跳转至字幕
    toCaption(data) {
      const { IntStartTime } = data
      this.playTime = IntStartTime
      if (this.$refs?.videoRef) {
        this.$refs.videoRef.play()
      }
    },
    readCaptionFile(captions) {
      captions.forEach(item => {
        if (item.attachement_type === 'Caption') {
          axios({
            url: item.url,
            method: 'GET',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
            }
          }).then((response) => {
            if (response.status === 200 && response.data) {
              try {
                let data = response.data?.split('\n\n')
                const captionArr = data?.map(str => {
                  let obj = {}
                  const captionItemArr = str.split(/[(\r\n)\r\n]+/)
                  captionItemArr.map((e, idx) => {
                    if (idx === 1) {
                      const time = JSON.parse(JSON.stringify(e))
                      obj.startTime = e.split('-->')[0]
                      obj.endTime = e.split('-->')[1]
                      const endTimeCopy = JSON.parse(JSON.stringify(time.split('-->')[1])) 
                      const startTimeCopy = JSON.parse(JSON.stringify(time.split('-->')[0]))
                      obj.IntStartTime = startTimeCopy ? this.timeToSec(startTimeCopy) : 0
                      obj.IntEndTime = endTimeCopy ? this.timeToSec(endTimeCopy) : 0
                    }
                    if (idx === 2) obj.caption = e
                  })
                  return obj
                })
                this.captionData = captionArr         
                let flag = this.tabList.map(item => item.key).includes('text')
                if (this.captionData && this.captionData.length && !flag) {
                  this.tabList.push({ title: '文稿', key: 'text', text: 'NetCourse_TextContent' })
                  this.active = 1
                }       
              } catch (error) {}
            }
          })
        }
      }) 
    },
    changeTab(index) {
      this.active = index
    }
  }
}
</script>

<style lang="less" scoped>
.material-container {
  height: 100%;
  background: #fff;
  .article-container{
    height: 100%;
    background: #fff;
  }
  .other-container{
    overflow: hidden;
    height: 100%;
  }
  .main-fixed {
    width: 100%;
    :deep(.video-component) {
      border: unset;
      border-radius: unset;
      background-color: black;
    }
  }
  .no-has-tabs.main-content{
    height: calc(100vh - 210px);
  }
  .has-tabs.main-content{
    height: calc(100vh - 248px);
  }
  .main-content {
    overflow: auto;
  }
  .video-box,.scorm-box {
    height: 210px;
  }
  .van-image{
    width: 100%;
    height: 100%;
  }
  .tab-nav{
    padding: 0 20px;
    height: 38px;
    line-height: 38px;
    background: #fff;
    border-bottom: 3px solid #f8f8f8;
    .tab-pane{
      display: inline-block;
      margin-right: 24px;
      border-bottom: 3px solid transparent;
    }
    .active-pane{
      border-bottom: 3px solid #0052D9;
      color: #0052D9;
      transition: border 1s;
    }
  }
  .des-main {
    background-color: #fff;
    padding: 20px 16px;
    font-size: 12px;
    //清除浮动
    .clearfix:after {
      content: "";
      display: block;
      height: 0;
      clear: both;
      visibility: hidden;
    }
    .title-box {
      display: flex;
      align-items: flex-start;
      .title-left {
        display: flex;
        align-items: center;
        width: 310px
      }
      .tag {
        display: inline-block;
        text-align: center;
        font-size: 12px;
        width: 50px;
        line-height: 18px;
        height: 18px;
        border-radius: 2px;
        border: 1px solid #0052d9ff;
        background: #ecf2feff;
        color: #0052D9;
        margin-right: 10px;
      }
      .title {
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        word-break: break-all;
      }
      .expand-des-icon {
        background: url('~@/assets/img/mobile/arrow-down.png') no-repeat center/cover;
        display: inline-block;
        width: 16px;
        height: 16px;
        margin-left: 16px;
        margin-top: 3px;
        transition: all 0.1s;
        transform: rotate(0deg);
      }
      .active-expand-des {
        transform: rotate(180deg);
      }
    }
    .title-info {
      display: flex;
      align-items: center;
      line-height: 20px;
      margin-top: 8px;
      color: #00000066;
      .item-icon {
        display: flex;
        align-items: center;
      }
      .item-name {
        margin-right: 16px;
      }
      .play-i {
        background: url('~@/assets/img/mobile/play.png') no-repeat center/cover;
        display: inline-block;
        width: 12px;
        height: 12px;
        margin-right: 4px;
      }

    }
    .des-content {
      line-height: 20px;
      color: #00000066;
      margin-top: 8px;
      // overflow: hidden;
      // text-overflow: ellipsis;
      // display: -webkit-box;
      // word-break: break-all;
      // /*! autoprefixer: ignore next */
      // -webkit-line-clamp: 3;
      // -webkit-box-orient: vertical;
      // /* autoprefixer: on */
    }
    .all-des-content {
      line-height: 20px;
      color: #00000066;
      margin-top: 8px;
    }
    .tag-list-box {
      margin-top: 12px;
      .item-tag {
        height: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 22px;
        opacity: 1;
        font-size: 10px;
        padding: 0px 12px;
        color: #00000099;
        background: #f7f8faff;
        float: left;
        margin-right: 8px;
        margin-bottom: 8px;
        .tag-label {
          line-height: 16px;
          height: 16px;
          max-width: 132px;
          text-overflow:ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
      .hot-tag {
        background: #fbf2e8ff;
        color: #ff5923ff;
        &-i {
          background: url('~@/assets/img/mobile/hot-tag.png') no-repeat center/cover;
          width: 12px;
          height: 12px;
          display: inline-block;
          margin-right: 4px;
        }
      }
      .official-tag {
        background: #ecf2feff;
        color: #0052d9ff;
        &-i {
          background: url('~@/assets/img/mobile/official-tag.png') no-repeat center/cover;
          width: 12px;
          height: 12px;
          display: inline-block;
          margin-right: 4px;
        }
      }
    }
    .expand-and-open {
      text-align: center;
      color: #00000099;
      font-size: 12px;
      line-height: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      // margin-top: 8px;
      .expand-icon {
        background: url('~@/assets/img/mobile/arrow-down.png') no-repeat center/cover;
        height: 16px;
        width: 16px;
        display: inline-block;
        transform: rotate(0deg);
        transition: all 0.3s;
        margin-top: 2px
      }
      .packUp-icon {
        transform: rotate(180deg);
      }
    }
    .person-info-box {
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-top: 20px;
      font-size: 10px;
      .item-info {
        text-align: center;
      }
      .item-info + .item-info {
        margin-left: 32px;
      }
      .icon {
        display: inline-block;
        width: 24px;
        height: 24px;
      }
      .label {
        line-height: 16px;
        margin-top: 2px;
      }
      .active-label {
        color: #0052d9ff;
      }
      .like-i-name {
        background: url('~@/assets/img/mobile/like.png') no-repeat center/cover;
      }
      .active-like {
        background: url('~@/assets/img/mobile/like-active.png') no-repeat center/cover;
      }
      .start-i-name {
        background: url('~@/assets/img/mobile/start.png') no-repeat center/cover;
      }
      .active-start {
        background: url('~@/assets/img/mobile/start-active.png') no-repeat center/cover;
      }
      .chart-i-name {
        background: url('~@/assets/img/mobile/chart.png') no-repeat center/cover;
      }
      .add-i-name {
        background: url('~@/assets/img/mobile/add.png') no-repeat center/cover;
      }
      .share-i-name {
        background: url('~@/assets/img/mobile/share.png') no-repeat center/cover;
      }
    }
  }
}
</style>
