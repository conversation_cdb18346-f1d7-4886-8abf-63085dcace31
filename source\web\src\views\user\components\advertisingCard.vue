<template>
  <div class="advertising-card" v-if="advertisingList && advertisingList.length && isShowRecommend === 1" :style="styles">
    <div class="tips-label">推荐</div>
    <div class="advertising-content">
      <swiper :options="swiperOption" ref="mySwiper" class="advertising-swiper-box">
        <swiper-slide v-for="(item, index) in advertisingList" :key="index" :data-id="item.id"  >
          <div 
            class="advertising-swiper-item"
            @click="handerClick(item)">
            <div class="preview-box">
              <img class="preview-img" :src="item.image_url" alt="" srcset="">
              <div class="time-box" v-if="item.duration">{{ item.duration }}分钟</div>
            </div>
            <div class="item-content">
              <div class="course-title">
                <span class="type" v-if="item.moduleName">{{ item.moduleName }}</span>
                {{ item.banner_name }}
              </div>
              <div class="description" :class="{ 'single-row': item.banner_type === 2 }">{{ item.description }}</div>
              <div class="labels">
                <span v-for="(e, j) in item.labels" :key="j" >{{ e }}</span>
              </div>
            </div>
          </div>
        </swiper-slide>
        <div class="swiper-pagination" slot="pagination"></div>
      </swiper>
    </div>
  </div>
</template>
<script>
import { getActTypeByModuleId, getActTypeNameByModuleId } from '@/utils/map.js'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]

export default {
  props: {
    isShowRecommend: {
      type: [String, Number],
      default: 0
    },
    courseData: {
      type: Object,
      default: () => ({})
    },
    commonTitle: {
      type: String,
      default: ''
    },
    actType: {
      type: [String, Number],
      default: 2
    },
    count: {
      type: [String, Number],
      default: 10
    },
    styles: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      advertisingRecord: {},
      advertisingList: [],
      advertisingEscalationList: []
    }
  },
  watch: {
    isShowRecommend: {
      handler(newV) {
        if (newV === 1) this.getSysBusyStatus()
      },
      deep: true,
      immediate: true
    }
  },
  computed: {
    swiperOption() {
      return {
        // loop: true,
        autoplay: {
          delay: 3000,
          stopOnLastSlide: false,
          disableOnInteraction: false
        },
        direction: 'horizontal',
        slidesPerView: 'auto',
        freeMode: false,
        // 显示分页
        pagination: {
          el: '.swiper-pagination',
          type: 'bullets',
          // dynamicBullets: true
          clickable: true // 允许分页点击跳转
        }
      }
    }
  },
  created() {
    let storage = this.getStorage()
    if (storage && typeof storage === 'object') {
      this.advertisingRecord = storage
    }
  },
  mounted() {},
  methods: {
    getSysBusyStatus() {
      fetch(envName.trainingPath + `api/businessCommon/manage/dict/is_sys_busy`, {
        credentials: 'include'
      }).then(response => {
        response.json().then(res => {
          if (res.code === 200 && res.success && res.data !== '1') {
            this.getAdvertisingList()
          }
        })
      })
    },
    getAdvertisingList() {
      fetch(envName.trainingPath + `api/business-common/user/banner/get-last-update-course?act_type=${this.actType}&count=${this.count}`, {
        credentials: 'include'
      }).then(response => {
        response.json().then(res => {
          if (res.code === 200 && res.success) {
            let keyList = Object.keys(this.advertisingRecord)
            let list = []
            const resList = res.data || []
            resList.map(v => {
              v.moduleName = ''
              v.dteid = this.dtCardData('eid', v)
              v.dtremark = this.dtCardData('remark', v)
              v.dtareaid = this.dtCardData('areaid', v)
              if (v.banner_type === 2) {
                v.moduleName = v.recommend_module_id ? getActTypeNameByModuleId(v.recommend_module_id * 1) : ''
              }
              if (!v.duration) v.duration = 0
              if (!v.labels) v.labels = []
              // 判断当前推广是否显示以及是否过期
              if (keyList.includes(String(v.id))) {
                let endTime = this.advertisingRecord[v.id]
                let time = endTime.replace(/-/g, '/')
                if (new Date(time) < new Date()) {
                  delete this.advertisingRecord[v.id]
                  list.push(v)
                }
              } else {
                list.push(v)
              }
            })
            this.setStorage(this.advertisingRecord)
            this.advertisingList = list
            this.$nextTick(() => {
              this.advertisingEscalationList.push(0)
              window.BeaconReport('at_show_area', { areaid: list[0].dteid, remark: list[0].dtremark })
              this.handerSwiper()
            })
          }
        })
      })
    },
    handerSwiper() {
      let that = this
      this.$refs.mySwiper.$swiper.on('slideChangeTransitionEnd', function () {
        if (that.advertisingEscalationList.indexOf(this.activeIndex) === -1) {
          let { dtareaid, dtremark } = that.advertisingList[this.activeIndex]
          that.advertisingEscalationList.push(this.activeIndex)
          window.BeaconReport('at_show_area', { areaid: dtareaid, remark: dtremark })
        }
      })
    },
    handerClick(row) {
      window.BeaconReport('at_click', { eid: row.dteid, remark: row.dtremark })
      if (row.link_url) {
        this.advertisingRecord[row.id] = row.end_time
        this.setStorage(this.advertisingRecord, row)
        window.open(row.link_url)
      } else {
        this.$message.wranning('跳转链接为空')
      }
    },
    getStorage() {
      return JSON.parse(localStorage.getItem('advertising_record'))
    },
    setStorage(val, current) {
      if (current && current.banner_type === 1) return
      let data = JSON.stringify(val)
      localStorage.setItem('advertising_record', data)
    },
    dtCardData(type, row) {
      const { course_id } = this.$route.query
      if (type === 'remark') {
        return JSON.stringify({
          page: document.title || this.courseData.course_name,
          page_type: '灰度网络课详情页', 
          container: '推广',
          click_type: 'data',
          content_type: getActTypeNameByModuleId(row.recommend_module_id * 1),
          content_id: row.recommend_item_id,
          content_name: row.banner_name,
          act_type: getActTypeByModuleId(row.recommend_module_id * 1),
          page_id: '',
          container_id: '',
          terminal: 'PC'
        })
      } else if (type === 'eid') {
        return `element_${course_id}${'_' + row.recommend_item_id}`
      } else {
        return `area_${course_id}${'_' + row.recommend_item_id}`
      }
    }
  }
}
</script>
<style lang="less" scoped>
  .advertising-card {
    background: #fff;
    padding: 20px 20px 12px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    position: relative;
    .tips-label {
      height: 24px;
      line-height: 24px;
      padding: 0 10px;
      border-radius: 8px 0;
      background: #FF843F;
      color: #ffffff;
      font-size: 14px;
      font-weight: 500;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 10;
    }
    .advertising-content {
      overflow: auto;
      .advertising-swiper-item {
        cursor: pointer;
        display: flex;
        align-items: flex-start;
        padding-bottom: 7px;
        .preview-box {
          width: 128px;
          height: 84px;
          background-color: #ccc;
          border-radius: 8px;
          margin-right: 16px;
          flex-shrink: 0;
          position: relative;
          .preview-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: inline-block;
            border-radius: 8px;
          }
          .time-box {
            height: 22px;
            line-height: 22px;
            padding: 0 6px;
            border-radius: 5px;
            background: #00000080;
            color: #ffffff;
            font-size: 12px;
            font-weight: 500;
            position: absolute;
            bottom: 4px;
            right: 4px;
          }
        }
        .item-content {
          flex: 1;
          overflow: hidden;
          .course-title {
            height: 44px;
            line-height: 22px;
            color: #333333;
            font-size: 14px;
            font-weight: 600;
            overflow: hidden;
            display: -webkit-box;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            margin-bottom: 4px;
            .type {
              display: inline-block;
              height: 18px;
              line-height: 18px;
              padding: 0 4px;
              border-radius: 2px;
              background:#F5F5F7;
              color: #777777;
              font-size: 11px;
              font-weight: 500;
              margin-right: 4px;
            }
          }
          .description {
            max-height: 40px;
            line-height: 20px;
            color: #666666;
            font-size: 12px;
            overflow: hidden;
            display: -webkit-box;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2;
            margin-top: -4px;
          }
          .single-row {
            -webkit-line-clamp: 1;
            margin-bottom: 8px;
          }
          .labels {
            width: 100%;
            height: 22px;
            overflow: hidden;
            margin-bottom: 12px;
            &>span {
              display: inline-block;
              height: 22px;
              line-height: 22px;
              padding: 0px 6px;
              border-radius: 4px;
              background: #F5F7FA;
              color: #777777;
              font-size: 12px;
              max-width: 100%;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              &:not(:last-child) {
                margin-right: 8px;
              }
            }
          }
        }
      }
      .swiper-pagination {
        bottom: -2px;
        ::v-deep .swiper-pagination-bullet {
          width: 6px;
          height: 6px;
          background: #EBEBEB;
          opacity: 1;
        }
        ::v-deep .swiper-pagination-bullet.swiper-pagination-bullet-active {
          width: 22px;
          border-radius: 8px;
          background: #006FFF;
        }
      }
    }
  }
</style>
