<template>
  <div>
    <!-- 视频回显 -->
    <video-chapters
      ref="videoChapters"
      :videoInfo="videoInfo"
      :estDur="estDur"
      :playTime="currentTime"
      :approveStatus="false"
      :autoPlay="false"
      :volumePanel="false"
    >
    </video-chapters>
    <div class="ai-chapters-tips" v-if="articleInfo.status === 13">
      <div class="open-chapters">
        <i class="el-icon-success" style="color: #2ba471"></i>
        <span class="tips"
          >已智能识转写文章，
          <el-link @click="getDialogArticle()" :disabled="!articleInfo.ai_graphic_id" type="primary"
            >点此查看、导入至人工配置</el-link
          >
        </span>
      </div>
    </div>
    <div class="article-content">
        <div class="content-top">
          <span class="pseudo-class-title">人工转写文章</span>
          <div v-if="showArticleEdit">
            <el-button type="primary" v-if="articleInfo.file_id" size="mini" @click="showArticleEdit=false">编辑</el-button>
            <el-button v-if='pageArticleList.length' :disabled="approveStatus" type="danger" plain size="mini" @click="handleDelte">删除</el-button>
          </div>
        </div>
        <div class="content-main" v-if="showArticleEdit">
            <div
                :class="['article-list-box']"
                v-for="(item, index) in pageArticleList" 
                :key="index"
            >
                <div class="tp-title">
                    <span class="time"><span>{{ transforNcTime(item.time) }}</span></span>
                    <span class="title" v-html="item.title"></span>
                </div>
                <div class="article-card-content">
                    <p class="item-other-title" v-for="(e, i) in item.titleList" :key="delTitle(e, i)">
                    <span v-html="delTitle(e, i)"></span>
                    </p>
                    <p class="item-other-title" v-for="(c, j) in item.content" :key="j"><span v-html="delTitle(c, j)"></span></p>
                    <img class="item-img" v-for="v in item.imglist" :key="v" :src="v" />
                </div>
            </div>
        </div>
        <sdc-mce-editor
            v-else
            ref="editor" 
            selector="ai_article" 
            :env="editorEnv" 
            :content="pageArticleData"
            :catalogue.sync="editorConfig.catalogue" 
            :urlConfig="editorConfig.urlConfig" 
            :options="editorConfig.options"
            :insertItems="insertItems"
        />
    </div>
    <div class="empty" v-if='pageArticleList.length === 0 && showArticleEdit'>
        <span class="empty-img"></span>
        <div class="empty-text">暂无数据</div>
    </div>
    <div class="buttom-btn" v-if="!showArticleEdit">
        <div class="inner">
          <el-button class="cancel-btn" @click="getCustomArticle('')">取消</el-button>
          <!-- <el-button class="confirm-btn" v-if="showAiGraphic" type="primary" @click="onPreview">预览</el-button> -->
          <el-button class="confirm-btn" type="primary" @click="onSubmit">保存</el-button>
        </div>
    </div>
    <el-dialog
      title="AI文章识别"
      :visible.sync="aiArticleVisible"
      width="960px"
      :before-close="handleClose"
      custom-class="ai-article-dialog none-border-dialog" 
    >
      <div class="ai-article-body">
        <div class="config-item">
          <span class="label">人工配置</span>
          <el-button :disabled="dialogArticleList?.length<1" @click="exportData()" type="primary" size="small">导入并修改</el-button>
          <span class="label-tips">点击即可导入智能转写文章数据，并可进行修改</span>
        </div>
        <div class="article-body-main">
          <div
            :class="['article-list-box']"
            v-for="(item, index) in dialogArticleList" 
            :key="index"
          >
            <div class="tp-title">
              <span class="time"><span>{{ transforNcTime(item.time) }}</span></span>
              <span class="title" v-html="item.title"></span>
            </div>
            <div class="article-card-content">
              <p class="item-other-title" v-for="(e, i) in item.titleList" :key="delTitle(e, i)"><span v-html="delTitle(e, i)"></span></p>
              <p class="item-other-title" v-for="(c, j) in item.content" :key="j"><span v-html="delTitle(c, j)"></span></p>
              <img class="item-img" v-for="v in item.imglist" :key="v" :src="v" />
            </div>
          </div>
          <div class="empty" v-if='dialogArticleList.length === 0'>
            <span class="empty-img"></span>
            <div class="empty-text">暂无数据</div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import videoChapters from '@/views/user/netcourse/course-make/components/video-chapters.vue'
import {
  getMaterialGraphic,
  saveMaterialGraphic
} from 'config/api.conf'
import { transforNcTime } from 'utils/tools'
import otherHttp from 'utils/otherHttp.js'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]
export default {
  name: 'transliterateArticle',
  props: {
    videoInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    articleInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    estDur: {
      type: Number,
      default: 0
    },
    currentTime: {
      type: Number,
      default: 0
    },
    status: {
      type: Number,
      default: 0
    }
  },
  components: {
    videoChapters
  },
  data() {
    return {
      transforNcTime,
      showArticleEdit: true,
      pageArticleList: [],
      pageArticleData: null,
      dialogArticleData: null,
      editorEnv: process.env.NODE_ENV,
      insertItems: [
        'link',
        'table',
        'image',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorConfig: {
        catalogue: false,
        options: { // 非必传字段
          selector: '#course_desc',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector: 'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
            formatselect fontsizeselect lineheight |
            bold italic underline strikethrough |
            forecolor backcolor |
            dent align bullist numlist |
            insert codesample |
            fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      },
      aiArticleVisible: false,
      dialogArticleList: []
    }
  },
  computed: {
    delTitle() {
      return (row, i) => {
        return (row && row[`t${i}`]) || ''
      }
    }
  },
  watch: {
    articleInfo: {
      handler(val) {
        if (val.artificial_graphic_id) {
          this.getCustomArticle()
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 获取用户的文章数据
    getCustomArticle(artificial_graphic_id = '') {
      const id = artificial_graphic_id || this.articleInfo.artificial_graphic_id
      if (!id) return
      getMaterialGraphic({ graphic_id: id }).then((res) => {
        this.pageArticleData = res ? this.delStr(res) : ''
        this.pageArticleList = this.forMatList(res)
        this.$emit('updateArticle', this.pageArticleData)
        this.showArticleEdit = true
      })
    },
    // 获取智能文章数据
    getDialogArticle() {
      if (!this.articleInfo.ai_graphic_id) return
      getMaterialGraphic({ graphic_id: this.articleInfo.ai_graphic_id }).then((res) => {
        this.dialogArticleData = res
        this.dialogArticleList = this.forMatList(res)
      })
      this.aiArticleVisible = true
    },
    forMatList(res) {
      res = res ? res.replace(/\n/g, '<br/>') : ''
      let container = document.createElement('div')
      container.innerHTML = res
      let articleList = []
      let currentTime = null
      let currentImgList = []
      let currentTitleList = []
      let currentContent = []
      function createEntry(time, imgList, titleList, content) {
        const otherTitleList = titleList?.length ? titleList.slice(1) : []
        const formattitle = titleList?.length ? titleList[0] : ''
        return {
          time: parseInt(time),
          imglist: imgList,
          title: formattitle,
          titleList: otherTitleList.map((v, i) => { 
            return {
              [`t${i}`]: v 
            }
          }),
          content: content.map((e, j) => {
            return {
              [`t${j}`]: e
            }
          })
        }
      }
      Array.from(container.children).forEach(e => {
        if (e.classList.contains('chaptersTime')) {
          if (currentTime !== null) { // 当前时间
            articleList.push(createEntry(currentTime, currentImgList, currentTitleList, currentContent))
            currentImgList = []
            currentTitleList = []
            currentContent = []
          }
          currentTime = e.getAttribute('id')
        } else if (e.tagName === 'H1' || e.tagName === 'H2' || e.tagName === 'H3') { // 标题
          currentTitleList.push(e.innerHTML)
        } else if (e.tagName === 'P' && e.querySelector('img')) { // 图片
          let img = e.querySelector('img')
          currentImgList.push(
            `${envName.contentcenter}content-center/api/v1/content/imgage/${img.getAttribute('data-content')}/preview`
          )
          // 规避图片中也有文本
          let reg = /<img[^>]*>/g
          currentContent.push(e.innerHTML.replace(reg, ''))
        } else { // 内容
          currentContent.push(e.innerHTML)
        }
      })
      if (currentTime !== null) {
        articleList.push(createEntry(currentTime, currentImgList, currentTitleList, currentContent))
      }
      return articleList
    },
    // 数据导入
    exportData() {
      this.$nextTick(() => {
        this.showArticleEdit = false
      })
      this.pageArticleData = JSON.parse(JSON.stringify(this.delStr(this.dialogArticleData)))
      this.pageArticleList = JSON.parse(JSON.stringify(this.dialogArticleList))
      this.$message.success('数据导入成功')
      this.handleClose()
    },
    handleClose() {
      this.aiArticleVisible = false
    },
    // 删除
    handleDelte() {
      this.$messageBox.confirm('确定删除吗？', '删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(async () => {
        otherHttp.delete(envName.trainingPath + 'api/businessCommon/manage/pub-file/delGraphic?file_id=' + this.articleInfo.file_id).then((res) => {
          console.log(res)
          // this.$emit('getCourse')
          this.pageArticleList = []
          this.pageArticleData = null
          this.$emit('updateArticle', this.pageArticleData)
        })
      })
    },
    delStr(res) {
      return res = res.replace(/<p class="chaptersTime" id="(\d+)"><\/p>/g, (match, id) => {
        const hours = String(Math.floor(id / 3600)).padStart(2, '0')
        const minutes = String(Math.floor((id % 3600) / 60)).padStart(2, '0')
        const seconds = String(id % 60).padStart(2, '0')
        return `<p>章节开始时间-${hours}:${minutes}:${seconds}</p>`
      }).replace(/<(h[1-3])(?:\s+id="[^"]*")?>(.*?)<\/\1>/g, '<$1>章节标题-$2</$1>')
    },
    // 保存文章
    onSubmit() {
      let str = this.$refs['editor'].getContent()
      const regTime = /<p>章节开始时间-(\d{2}):(\d{2}):(\d{2})<\/p>/g
      if (!regTime.test(str)) {
        this.$message.warning('请输入正确的时间格式（章节开始时间-00:00:00）')
        return
      }
      const regTitle = /<(h[1-3])>章节标题-(.*?)<\/\1>/g
      if (!regTitle.test(str)) {
        this.$message.warning('请输入正确的标题格式（章节标题-标题内容）')
        return
      }
      str = str.replace(/<p>章节开始时间-(\d{2}):(\d{2}):(\d{2})<\/p>/g, (match, h, m, s) => {
        const seconds = parseInt(h) * 3600 + parseInt(m) * 60 + parseInt(s)
        return `<p class="chaptersTime" id="${seconds}"></p>`
      }).replace(/<(h[1-3])>章节标题-(.*?)<\/\1>/g, '<$1>$2</$1>')
      saveMaterialGraphic({ graphic_content: str }, this.articleInfo.file_id).then((res) => {
        this.showArticleEdit = true
        // this.getCustomArticle(res)
        this.$message.success('文章信息保存成功')
        this.$emit('saved', res)
      })
    }
  }
}
</script>
<style lang="less" scoped>
.ai-chapters-tips {
    margin-top: 20px;
    .tips {
        margin-left: 9px;
        color: #000000;
        font-size: 14px;
        line-height: 22px;
    }
}
.article-content {
    background:#fff;
    padding: 20px 0;
    margin-top: 12px;
    .content-top {
        display: flex;
        align-items: center;
        margin-bottom: 24px;
        .pseudo-class-title {
            margin-right: 20px;
            position: relative;
            padding-left: 16px;
            font-size: 16px;
            font-weight: bold;
            color: rgba(0,0,0,0.8);
            font-family: "PingFang SC";
            display: inline-block;
        }
        .pseudo-class-title::before {
            position: absolute; 
            top: 50%; 
            transform: translatey(-50%); 
            left: 0; 
            content: ''; 
            width: 4px;
            height: 18px;
            background-color: #0052D9; 
        }
    }
    .content-main {
        margin-top: 12px;
    }
}
.empty {
    text-align: center;
    background:#fff;
    padding-bottom: 20px;

    .empty-text {
      margin-top: 17px;
      color: #999999;
      font-size: 14px;
      margin-bottom: 24px;
    }

    .empty-img {
      display: inline-block;
      width: 160px;
      height: 160px;
      background: url(~@/assets/img/empty.png) no-repeat center/contain;
    }
}
.buttom-btn {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 74px;
    line-height: 74px;
    background-color: #fff;
    text-align: right; 
    z-index: 99;
    .inner {
      display: inline-block;
      text-align: right;
      margin-right: 24px;
    }
    .confirm-btn, .cancel-btn {
      width: 104px;
      height: 40px;
      border-radius: 4px;
    }
    .el-button + .el-button {
      margin-left: 20px;
    }
    .confirm-btn {
      background: #0052D9;
    }
}
.article-list-box {
    cursor: pointer;
    margin-bottom: 12px;
    .tp-title {
        margin-bottom: 4px;
        line-height: 22px;
        display: flex;
        .time {
            border-radius: 4px;
            background: #EEE;
            padding: 0 8px;
            height: 24px;
            color: #333333;
            margin-right: 16px;
            display: flex;
            align-items: center;
            span {
            height: 22px;
            line-height: 22px;
            display: inline-block;
            }
        }
        .title {
            font-weight: bold;
            color: #333333;
        }
        }
        .article-card-content {
        .item-other-title {
            line-height: 22px;
            letter-spacing: 0.28px;
            margin-top: 4px;
        }
        .item-img {
            margin-top: 4px;
            width: 315px;
            height: 177px;
            border-radius: 4px;
        }
    }
}
.ai-article-dialog {
    .article-body-main {
      height: 500px;
      overflow: auto;
      margin-top: 20px;
    }
    .label-tips {
      color: #00000066;
      font-size: 14px;
      line-height: 22px;
      margin-left: 12px;
    }
      
    .ai-article-body {
      :deep(.ai-switch){
        .el-switch__core {
          height: 16px;
        }
        .el-switch__core:after {
          width: 12px;
          height: 12px;
        }
      }
      :deep(.is-checked) {
        .el-switch__core::after {
          margin-left: -13px;
        }
      }
      .label {
        color: #000000;
        margin-right: 12px;
      }
      .ai-switch-item {
        margin-bottom: 21px;
      }
      .chapters-content {
        height: 550px;
        overflow-y: auto;
        padding-right: 16px;
        .chapters-list-box {
          display: flex;
          align-items: baseline;
          color: #00000099;
          margin-top: 24px;
          .form-title {
            margin-right: 12px;
            height: 22px;
            line-height: 22px;
            font-weight: bold;
          }
  
          .chapter-dialog-form {
            flex: 1;
            .dialog-item {
              margin-bottom: 24px;
              display: flex;
            }
            .dialog-item:last-of-type {
              margin-bottom: unset;
            }
            .placehodler {
              margin-right: 13px;
              display: inline-block;
            }
            .form-time-item {
              display: flex;
              align-items: center;
              .minute-input-style, 
              .second-input-style {
                width: 56px;
                margin-left: 8px;
                margin-right: 8px;
              }
              :deep(.is-disabled) {
                .el-input__inner {
                  padding-right: 5px;
                }
              }
            }
            .form-coverImg-item {
              display: flex;
              .cover-label {
                margin-right: 12px;
              }
            }
            .form-title-item {
              display: flex;
              .red-start {
                color: red;
                margin-right: 7px;
              }
            }
          }
        }
      }
    }
  }
</style>
