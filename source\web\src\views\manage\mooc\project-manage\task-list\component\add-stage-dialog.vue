<template>
  <el-dialog :visible.sync="visible" :width="width" :title="form.title" :show-close="false" :close-on-click-modal="false"
    custom-class="tasks-operate-dialog none-border-dialog" :before-close="cancel">
    <div class="input-style">
      <el-input v-model.trim="form.task_name" :placeholder="placeholder" clearable></el-input>
      <span class="custom-el-input-count">{{ handleValidor(form.task_name, 50, '1') }}/50</span>
    </div>
    <el-select v-if="taskInfo.task_type === 'group'" v-model="form.parent_task_name" placeholder="请选择归属阶段" class="stage"
      :disabled="taskInfo.currentGroup" clearable>
      <el-option v-for="item in stageList" :key="item.parent_Id_Diy" :label="item.task_name" :value="item.parent_Id_Diy">
      </el-option>
    </el-select>
    <span slot="footer" class="dialog-footer">
      <el-button size="mini" @click="cancel">取 消</el-button>
      <el-button type="primary" size="mini" @click="comfirm">{{ this.taskInfo?.type === 'edit' ? '保 存' : '确 定'
      }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    taskInfo: {
      type: Object,
      default: () => ({})
    },
    currentNode: {
      type: Object
    },
    width: {
      type: String,
      default: '430px'
    },
    treeNode: {
      type: Array,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        title: '',
        task_name: '',
        parent_task_id: '',
        parent_task_name: ''
      },
      placeholder: ''
    }
  },
  computed: {
    stageList() {
      return this.treeNode.filter((e) => e.task_type === 'stage')
    }
  },
  mounted() {
    const { type } = this.taskInfo
    if (type === 'edit') { // 编辑数据回显
      this.form.task_name = this.currentNode?.task_name
    }
    (this.stageList || []).forEach((e) => { // 编辑-新增都显示任务组名称
      if (e.parent_Id_Diy === this.currentNode.parent_Id_Diy) {
        this.form.parent_task_name = this.currentNode.parent_Id_Diy
      }
    })
    this.initTitle()
  },
  methods: {
    initTitle() {
      const { type, task_type } = this.taskInfo
      if (task_type === 'group') {
        this.form.title = `${type === 'add' ? '新增' : '编辑'}任务组`
        this.placeholder = '请输入任务组名称'
      } else if (task_type === 'stage') {
        this.form.title = `${type === 'add' ? '新增' : '编辑'}阶段`
        this.placeholder = '请输入阶段名称'
      }
    },
    cancel() {
      this.$emit('update:visible', false)
    },
    comfirm() {
      const { task_type, type } = this.taskInfo
      const { parent_task_name, task_name } = this.form
      let param = {}
      if (!task_name) {
        this.$message.warning(task_type === 'stage' ? '请输入阶段名称' : '请输入任务组名称')
        return
      }
      // 阶段名任务组名不能重复
      let flagList = []
      let newTaskList = []
      let node = {}
      if (task_type === 'stage') { // 阶段
        node = JSON.parse(JSON.stringify(this.currentNode))
        newTaskList = JSON.parse(JSON.stringify(this.treeNode))
      } else { // 任务组获取最新的父节点---避免编辑更换了父节点
        node = this.treeNode.find((e) => e.parent_Id_Diy === this.form.parent_task_name)
        if (node?.id) {
          newTaskList = (node?.sub_tasks || []).filter((e) => e.task_type === task_type)
        } else {
          newTaskList = JSON.parse(JSON.stringify(this.treeNode))
        }
      }
      newTaskList.forEach((v) => {
        if (v.task_name === task_name && this.currentNode?.id !== v.id) {
          flagList.push(v)
        }
      })
      if (flagList?.length) {
        this.$message.warning('名称已存在，请重新输入')
        return
      }
      if (type === 'add') { // 新增
        param = {
          id: parent_task_name || '',
          task_name
        }
        this.$emit('updateStageOrGroup', param, type, task_type)
        this.$emit('update:visible', false)
      } else { // 编辑
        let curObj = JSON.parse(JSON.stringify(this.currentNode))
        param = {
          ...curObj,
          task_name,
          id: this.currentNode.id || '', // 还未保存编辑阶段所需
          eidtId: parent_task_name // 编辑任务组所需
        }
        this.$emit('updateStageOrGroup', param, type, task_type)
        this.$emit('update:visible', false)
      }
    },
    handleValidor(value, num, type) {
      if (!value) return 0
      const china = value.match(/[\u4e00-\u9fa5]/g)
      const zhCount = china && china.join('').length
      const enCount = Math.ceil((value.length - zhCount) / 2)
      const total = zhCount + enCount
      if (total > num) {
        if (type === '1') {
          this.form.task_name = value.slice(0, -1)
        }
      }
      return zhCount + enCount
    }
  }
}
</script>
<style lang="less" scoped>
.tasks-operate-dialog {
  .stage {
    margin-top: 20px;
    width: 366px;
  }

  .input-style {
    position: relative;
    height: 32px;

    :deep(.el-input) {
      .el-input__inner {
        padding-right: 70px;
      }

      .el-input__suffix {
        position: absolute;
        right: 43px;
      }
    }

    .custom-el-input-count {
      position: absolute;
      top: 8px;
      right: 0px;
      width: 40px;
    }
  }
}
</style>
