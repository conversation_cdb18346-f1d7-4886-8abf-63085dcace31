<template>
  <sdc-layout :header-layout="['logo', 'links']" :sidebar-menus="sidebarMenus">
    <div slot="header-logo">
      <img class="logos" src="../../assets/img/logo-white.png" alt="" />
      <span class="app-name2">标签管理</span>
    </div>
    <div slot="header-links">
      <el-avatar size="small" class="user-avatar" :src="avatar">
        <img :src="defaultAvatar" />
      </el-avatar>
      <span class="staff-name">{{ staffName }}</span>
      <sdc-link @click="exit" text="退出" />
    </div>
  </sdc-layout>
</template>

<script>
import { refresh, resize } from 'sdc-vue'
import { getAvatar } from 'utils/tools'

export default {
  name: 'manage',
  mixins: [refresh, resize],
  data() {
    return {
      staffName: '',
      avatar: require('@/assets/img/avatar.png'),
      defaultAvatar: require('@/assets/img/avatar.png'),
      sidebarMenus: {
        active: '1.1',
        map: {
          key: 'id',
          text: 'name',
          url: 'link',
          pid: 'parentId',
          root: '0'
        },
        data: [
          {
            name: '课程标签管理',
            parentId: '0',
            level: 1,
            icon: 'none'
          }
        ]
      }
    }
  },
  watch: {
    '$store.state.userInfo'() {
      this.avatar = getAvatar(this.$store.state.userInfo.staff_name)
      this.staffName = this.$store.state.userInfo.staff_name
    }
  },
  created() {
    const path = {
      'graphic-list': '1.1'
    }
    const arr = window.location.pathname.split('/')
    this.sidebarMenus.active = path[arr[arr.length - 1] || 'graphic-list']
  },
  methods: {
    // 菜单切换
    menuChange(name) {
      this.$router.push({ name })
    },
    exit() {
      if (process.env.NODE_ENV === 'development') {
        // 测试环境
        window.location.href = `//passtest.oa.com/modules/passport/signout.ashx?url=${location.href}`
      } else {
        // 生产环境
        window.location.href = `${location.origin}/_logout/?url=${location.href}`
      }
    }
  }
}
</script>

<style lang="less" scoped>
.logos {
  width: 162px;
}
:deep(.header-inner) {
  .header-left {
    width: auto;
    padding-left: 20px;
    .logo {
      span:first-child {
        margin-right: 18px;
        font-size: 28px;
        font-weight: bold;
      }
      span:last-child {
        font-size: 16px;
      }
    }
  }
  .header-right {
    .header-right-inner > div {
      display: flex;
      align-items: center;
    }
    .staff-name {
      margin: 0 8px 0 8px;
    }
  }
}
</style>
