<template>
  <div class="course-publish course-make-style">
    <!-- 左侧具体步骤内容 -->
    <div class="step-left">
      <steps-header>
        <template v-slot:title>{{
          courseInfo.course_type === 'Video-2d' ? '+人像' : ''
        }}</template>
        <template v-slot:desc>{{
          courseInfo.course_type === 'Video-2d'
            ? '提供授课PPT、讲稿（填写在PPT备注中）和10秒人像录像，即可快捷生成包括PPT画面、授课语音和人像的在线课程'
            : '提供授课PPT、讲稿（填写在PPT备注中），即可快捷生成包括PPT画面、授课语音的在线课程'
        }}</template>
      </steps-header>
      <div class="content">
        <div class="tips">
          <span class="label">提示：</span>
          <div class="text">
            <div>您提交的课程制作信息已通过审核并完成了视频合成</div>
            <div>
              请在此页面确认视频合成的最终效果和相关信息，确认后即可发布课程；如需修改PPT和语音文本，请点击页面底部的“重新制作‘按钮
            </div>
          </div>
        </div>
        <!-- 发布页步骤 -->
        <el-steps direction="vertical">
          <el-step
            v-show="false"
            v-for="(item, index) in hideStepList"
            :key="index"
          >
          </el-step>
          <el-step title="合成剪辑" id="resultPreview">
            <template slot="title">
              <span>合成剪辑</span>
              <span class="result-tips"
                >完整课程视频如下，点击播放器即可预览</span
              >
            </template>
            <template slot="description">
              <result-preview
                :videoData="videoData"
                :captionList="captionList"
              />
            </template>
          </el-step>
          <el-step title="确认发布" id="courseInfo">
            <template slot="title">
              <span>确认发布</span>
              <span class="cofirm-text">检查确认在线课程信息</span>
            </template>
            <template slot="description">
              <course-info
                ref="courseInfo"
                :courseInfo="courseInfo"
                videoStatus="publish"
              />
            </template>
          </el-step>
        </el-steps>
      </div>
    </div>
    <!-- 右侧步骤条 -->
    <right-steps
      :stepList="stepList"
      :statusList="statusList"
      videoStatus="publish"
      @isClickStep="isClickStep"
    />
    <!-- 底部按钮区域 -->
    <div class="buttom">
      <div class="inner">
        <el-button @click="onRemake" :disabled="isPublish">重新制作</el-button>
        <el-button type="primary" @click="onPublish" :disabled="isPublish"
          >正式发布</el-button
        >
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import stepsHeader from '../components/steps-header.vue'
import resultPreview from '../components/result-preview.vue'
import rightSteps from '../components/right-steps.vue'
import courseInfo from '../components/course-info.vue'
import {
  getCourseInfo,
  addCoursePublish,
  getContentInfo,
  operatesignature
} from 'config/api.conf'

let flag = true

export default {
  name: 'coursePublish',
  components: {
    stepsHeader,
    resultPreview,
    rightSteps,
    courseInfo
  },
  data() {
    return {
      stepList: [
        {
          label: '上传PPT',
          id: '',
          process: false
        },
        {
          label: '选择音色',
          id: '',
          process: false
        },
        {
          label: '上传人像',
          id: '',
          process: false
        },
        {
          label: '画面配置',
          id: '',
          process: false
        },
        {
          label: '效果预览',
          id: '',
          process: false
        },
        {
          label: '完善信息',
          id: '',
          process: false
        },
        {
          label: '提交审核',
          id: '',
          process: false
        },
        {
          label: '合成剪辑',
          id: 'resultPreview',
          process: true
        },
        {
          label: '确认发布',
          id: 'courseInfo',
          process: false
        }
      ],
      statusList: [
        'success',
        'success',
        'success',
        'success',
        'success',
        'success',
        'success',
        'success',
        'wait'
      ],
      hideStepList: [
        'pptUpload',
        'voiceSelect',
        'portraitUpload',
        'layoutConfig',
        'demoPreview',
        'courseInfo',
        'submit'
      ],
      courseInfo: {},
      videoData: {
        content_id: '',
        duration: 0,
        file_size: 0
      },
      captionList: [],
      clickStepFlag: false,
      isPublish: false
    }
  },
  created() {
    const net_course_id = this.$route.query.net_course_id
    if (net_course_id) {
      this.getCourse(net_course_id)
    }
  },
  mounted() {
    // 左侧滚动，显示到右侧进度区
    document.getElementById('app').addEventListener('scroll', () => {
      if (flag && !this.clickStepFlag) {
        flag = false
        const arr = ['resultPreview', 'courseInfo']
        let isSet = false
        arr.forEach((item, index) => {
          const i = this.stepList.length - 2 + index
          this.stepList[i].process = false
          const el = document.getElementById(item)
          const position = el?.getBoundingClientRect()
          if (position?.top >= 0 && !isSet) {
            this.stepList[i].process = true
            isSet = true
          }
        })
        if (!isSet) {
          this.stepList[this.stepList.length - 1].process = true
        }
        setTimeout(() => {
          flag = true
        }, 50)
      }
    })
  },
  methods: {
    // 滚动开关
    isClickStep(val) {
      this.clickStepFlag = val
    },
    // 获取课程详情
    getCourse(net_course_id) {
      // 获取课程信息
      getCourseInfo(net_course_id).then((res) => {
        const { course_type, content_info, captions, est_dur, status } = res
        if (status === '1') {
          this.isPublish = true
          this.$message.warning(
            '课程已发布，您无需再次发布，您可以直接观看该课程'
          )
        }
        const content_id = content_info && content_info.content_id
        this.videoData.duration = (est_dur || 0) * 60
        this.videoData.file_size =
          Math.ceil(((content_info.file_size || 0) / 1048576) * 10) / 10
        if (course_type === 'Video-ppt') {
          this.stepList.splice(2, 2)
          this.statusList.splice(2, 2)
          this.hideStepList.splice(2, 2)
        }
        if (content_id) {
          this.videoData.content_id = content_id
          this.courseInfo = res
          this.getVideoData(content_id, captions)
        }
      })
    },
    // 获取内容中心视频信息
    getVideoData(content_id, captions) {
      operatesignature({
        operate: 'visit',
        content_id
      }).then((signature) => {
        getContentInfo(content_id, {
          app_id: 'QLearningService',
          signature
        }).then((res) => {
          const { file_attachments } = res
          if (captions && captions.length > 0) {
            captions[0].attachement_type = 'Caption'
            captions[0].title = '中文'
            this.readCaptionFile(captions)
          } else {
            this.readCaptionFile(file_attachments)
          }
        })
      })
    },
    // 通过url获取字幕
    readCaptionFile(file_attachments) {
      if (!file_attachments) return
      file_attachments.forEach((item) => {
        if (item.attachement_type === 'Caption' && item.title === '中文') {
          this.getCaption(item.url)
        }
      })
    },
    // 通过content_id获取字幕
    getCaption(url) {
      if (!url) return
      axios({
        url,
        method: 'GET'
      }).then((response) => {
        if (response.status === 200 && response.data) {
          const data = response.data?.split('\n\n')
          const captionList = []
          data.forEach((str) => {
            let obj = {}
            const arr = str.split(/[(\r\n)\r\n]+/)
            if (arr[0]) {
              arr.forEach((e, idx) => {
                if (idx === 1) obj.time = e?.split(',')[0]
                if (idx === 2) obj.caption = e
              })
              captionList.push(obj)
            }
          })
          this.captionList = captionList
        }
      })
    },
    // 重新制作
    onRemake() {
      this.$messageBox
        .prompt(
          '警告：重新制作将放弃当前已合成的视频素材，是否确认？',
          '重新制作',
          {
            closeOnClickModal: false,
            customClass: 'confirm-input-message-box',
            iconClass: 'el-icon-warning-outline',
            cancelButtonClass: 'el-button--default',
            confirmButtonClass: 'el-button--danger',
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            inputPlaceholder: '请输入“重新制作”确认此次操作',
            inputErrorMessage: '请输入“重新制作”',
            inputValidator: function (val) {
              return !!val && val.trim() === '重新制作'
            }
          }
        )
        .then(({ value }) => {
          if (value && value.trim() === '重新制作') {
            const { net_course_id } = this.$route.query
            this.$router.replace({
              name:
                this.courseInfo.course_type === 'Video-ppt'
                  ? 'pptCourse'
                  : '2DCourse',
              query: {
                net_course_id
              }
            })
          }
        })
    },
    // 发布
    onPublish() {
      const courseInfo = this.$refs.courseInfo.vaildCourseInfo()
      if (!courseInfo) {
        this.$message.error('请完善课程信息')
        this.scrollStep(this.stepList.length - 1, 'courseInfo')
        return
      }
      const { net_course_id } = this.$route.query
      const { course_type, content_info } = this.courseInfo
      const params = {
        course_type,
        content_info: {
          file_name: '',
          content_id: content_info && content_info.content_id,
          file_size: ''
        },
        virtualInfo: {},
        ...courseInfo
      }
      if (net_course_id) {
        params.net_course_id = net_course_id
      }
      addCoursePublish(params).then(() => {
        this.$message.success('发布成功')
        setTimeout(() => {
          this.$router.push({ name: 'courseList' })
        }, 1000)
      })
    },
    // 点击进度滚动到对应进度区域
    scrollStep(index, id) {
      this.stepList.forEach((i, j) => {
        i.process = index === j
      })
      const anchorElement = document.querySelector(`#${id}`)
      const scrollConfig = {
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      }
      anchorElement && anchorElement.scrollIntoView(scrollConfig)
    }
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/course-make.less';
.course-publish {
  .el-steps {
    padding-top: 0 !important;
  }
}
</style>
