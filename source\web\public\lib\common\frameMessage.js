window.frameMessage = (function() {
  function sendToParentMessage(data, events = 'getData', vendor = 'questionnaire') {
    window.parent.postMessage({
      events,
      data,
      vendor
    }, "*");
  }

  function _receiveMessage() {
    console.log('执行了')
    window.addEventListener("message", async function(e){
      console.log(e, 'js接收到的消息')
      try {
        const  { data } = e
        if (data.type === 'questionnaire' && data.events === 'getData') {
          let res = await window.urUtils.getCurrentSurveyInfo()
          sendToParentMessage(res, 'getData')
        }
      } catch (error) {
        sendToParentMessage(error, 'error')
      }
    }, false)
  }

  // 通知学堂任务完成
  function notifyTaskDone(options) {
    return new Promise((resolve, reject) => {
      let { params = {} } = options
      let env = 'test'
      let requestUrl = {
        production: 'https://portal.learn.woa.com/training/api/user/wenjuan/wj-finished',
        test: 'https://test-portal-learn.woa.com/training/api/user/wenjuan/wj-finished'
      }
      let url = requestUrl[env]

      if (!params.user_id || !params.from || !params.course_id) {
        resolve()
        return
      }

      // 不在iframe中
      if (window === window.top) {
        fetch(url, 
          { 
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(params),
            credentials: 'include' 
          },
        ).then(res => {
          resolve(res)
        }).catch(err => {
          reject(err)
        })
      } else {
        // 在iframe中
        window.parent.postMessage({
          event: 'completeStatusUpdata',
          type: 'questionnaire'
        }, '*')

        resolve()
      }
    })
  }

  _receiveMessage()

  return {
    notifyTaskDone
  }
})()