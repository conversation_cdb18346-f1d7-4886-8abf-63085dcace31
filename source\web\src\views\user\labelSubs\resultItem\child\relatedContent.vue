<template>
    <div class="tab-box">
      <dynamicsTag v-if="info.origin_data.rank_tag && [1, 10].includes(info.module_id)" :tagData="info.origin_data.rank_tag" :dtData="dtData"></dynamicsTag>
      <div class="relatedLabels" v-if="(!curLabelId && relatedInfo.type === 1) || contentType===1 || contentType===2">
        <span v-for="(item, index) in relatedInfo.detail" :class="{'active':index === 0 && contentType!==2}" :key="index" @click.stop="labelCli(item)">{{item.label_name}}</span>
      </div>
      <div class="zone" v-if="(!curLabelId && relatedInfo.type === 2)">
        来自专区<span class="specailName" @click.stop="specailJump(sortSubsIds[1][relatedInfo.detail.num].label_jump_url, sortSubsIds[1][relatedInfo.detail.num].label_id)">「{{relatedInfo.detail.page_name}}」</span>相关内容
      </div>
    </div>
</template>
<script>
import specailJump from '../../specailJump'
import dynamicsTag from '@/components/dynamicsTag'
export default {
  name: 'relatedContent',
  props: ['info', 'curModuleId', 'curLabelId', 'sortSubsIds', 'subsType', 'contentType'],
  components: {
    dynamicsTag
  },
  data() {
    return {
      labelArr: [],
      relatedInfo: null
    }
  },
  mixins: [specailJump],
  created() {
    this.labelData()
    this.relatedSubs()
  },
  computed: {
    dtData() {
      return {
        page: '标签订阅内容', 
        page_type: '标签订阅内容',
        container: '订阅标签相关内容',
        click_type: 'data',
        content_name: this.info.title,
        content_id: this.info.item_id,
        content_type: this.info.module_name,
        terminal: 'PC',
        id: this.info.item_id
      }
    }
  },
  methods: {
    getUrl2() {
      let pas = window.location.hostname.endsWith('.woa.com')
      if (pas) {
        return process.env.NODE_ENV === 'production' ? 'https://learn.woa.com' : 'https://test-learn.woa.com'
      } else {
        return process.env.NODE_ENV === 'production' ? 'https://learn.woa.com' : 'http://test.v8.learn.oa.com'
      }
    },
    labelCli(item) {
      // 非官方标签跳转到搜索页
      if (item.is_official) {
        window.open(`${this.getUrl()}/training/label-subs?isLabelGatherPage=true&label_id=${item.label_id}&label_name=${item.label_name}&source=bqdy&from_page=标签订阅`)
      } else {
        let query = this.$route.query
        let from_name = 'LabelSubscribe'
        if (query?.isLabelGatherPage && (query?.isLabelGatherPage === 'true' || query?.isLabelGatherPage === true)) {
          from_name = 'LabelGather'
        }
        window.open(`${this.getUrl2()}/mat/user/search?keywords=${item.label_name}&source=bqdy&from_page=标签订阅&from=${from_name}`)
      }
    },
    labelData() {
      let arr = []
      let arr2 = []
      if (this.info.label_system?.length > 0) {
        this.info.label_system.forEach((element, index) => {
          let obj = {}
          obj.label_name = element
          obj.label_id = this.info.origin_data.label_system_ids[index]
          obj.is_official = (this.info.origin_data.label_first[index] === '其他' && this.info.origin_data.label_second[index] === '自定义') ? 0 : 1
          if (!arr2.includes(this.info.origin_data.label_system_ids[index])) {
            arr.push(obj)
            arr2.push(this.info.origin_data.label_system_ids[index])
          }
        })
      }
      this.labelArr = arr
    },
    relatedSubs() {
      console.log('123123123')
      if (!this.contentType) {
        let labelTime = new Date('1970-01-01').getTime()
        let specailTime = new Date('1970-01-01').getTime()
        let filterSpecailData = {}
        let firstLabelIndex = -1
        let { page_list, sort_time } = this.info.origin_data
        let labels = [...this.labelArr]
        let specailIds = this.sortSubsIds[1].map(item => item.label_id + '')
        let labelData = this.sortSubsIds[0].sort((a, b) => new Date(a.subscribe_time).getTime() - new Date(b.subscribe_time).getTime())
        // 遍历专区列表，与订阅列表交集，找出最近一个的专区
        if (page_list && page_list.length > 0) {
          page_list.forEach((item, index) => {
            if (specailIds.includes(item.page_id)) {
              let time = new Date(item.add_time).getTime()
              if (time > specailTime) {
                specailTime = time
                filterSpecailData = item
                filterSpecailData.num = specailIds.indexOf(item.page_id)
              }
            }
          })
        }
        // 遍历标签列表，找出与订阅列表交集的标签
        if (labels && labels.length > 0) {
          for (let i = labelData.length - 1; i > -1; i--) {
            let item = labelData[i]
            for (let j = 0; j < labels.length; j++) {
              let val = labels[j]
              if ((item.label_id + '') === (val.label_id + '')) {
                labelTime = new Date(sort_time).getTime()
                firstLabelIndex = j
                break
              }
            }
            if (firstLabelIndex > -1) break
          }
        }
        if (!(this.subsType * 1)) {
          // console.log('type0000000000000000000')
          // 内容同时订阅了标签与专区
          if (firstLabelIndex > -1 && (page_list && page_list.length > 0)) {
            // 标签时间大于专区时间，展示标签信息
            if (labelTime > specailTime) {
              let removedItem = labels.splice(firstLabelIndex, 1)[0]
              labels.unshift(removedItem)
              this.$parent.$parent.type = 1
              this.relatedInfo = {
                type: 1,
                detail: labels
              } 
            } else {
              this.$parent.$parent.type = 2
              this.$parent.$parent.page_id = filterSpecailData.page_id
              this.relatedInfo = {
                type: 2,
                detail: filterSpecailData
              } 
            }
          } else if (firstLabelIndex > -1) {
            let removedItem = labels.splice(firstLabelIndex, 1)[0]
            labels.unshift(removedItem)
            this.$parent.$parent.type = 1
            this.relatedInfo = {
              type: 1,
              detail: labels
            } 
          } else if (page_list && page_list.length > 0) {
            this.$parent.$parent.type = 2
            this.$parent.$parent.page_id = filterSpecailData.page_id
            this.relatedInfo = {
              type: 2,
              detail: filterSpecailData
            } 
          }
        } else if ((this.subsType * 1) === 1) {
          let removedItem = labels.splice(firstLabelIndex, 1)[0]
          labels.unshift(removedItem)
          this.$parent.$parent.type = 1
          this.relatedInfo = {
            type: 1,
            detail: labels
          }
        } else if ((this.subsType * 1) === 2) {
          this.$parent.$parent.type = 2
          this.$parent.$parent.page_id = filterSpecailData.page_id
          this.relatedInfo = {
            type: 2,
            detail: filterSpecailData
          }
        }
      } else if (this.contentType === 1) {
        let labels = this.labelArr
        let number = 0
        for (let i = 0; i < labels.length; i++) {
          let item = labels[i]
          if (String(item.label_id) === String(this.curLabelId)) {
            number = i
            break
          }
        }
        let removedItem = labels.splice(number, 1)[0]
        labels.unshift(removedItem)
        this.$parent.$parent.type = 1
        this.relatedInfo = {
          type: 1,
          detail: labels
        } 
      } else if (this.contentType === 2) {
        this.$parent.$parent.type = 2
        this.$parent.$parent.page_id = ''
        this.relatedInfo = {
          type: 1,
          detail: this.labelArr
        } 
      } else {
        this.$parent.$parent.type = ''
        this.relatedInfo = {
          type: '',
          detail: []
        }
      }
    }
  }
}
</script>
<style lang="less" scoped>
.tab-box {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  overflow: hidden;
  height: 22px;
  :deep(.rankTag-main) {
    margin-right: 8px;
  }
}
.zone{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    align-self: stretch;
    overflow: hidden;
    color: #00000066;
    text-overflow: ellipsis;
    font-family: "PingFang SC";
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 16px;
    .specailName{
      color: #000000E6;
    }
    span:hover{
      color: #0052d9;
    }
}
.relatedLabels{
    height: 22px;
    overflow: hidden;
    span{
        display: inline-block;
        padding: 2px 6px;
        margin-right: 8px;
        color: #777777;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 18px;
        background: #F5F7FA;
        &.active{
            color: #0052D9;
            background: #F4F9FE;
        }
    }
}
</style>
