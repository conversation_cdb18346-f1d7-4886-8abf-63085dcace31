<template>
  <div class="net-page">
    <div v-if="['Video', 'Video-2d', 'Video-3d', 'Video-ppt', 'Audio'].includes(courseData.course_type)" class="video-main">
      <Video
        class="video-box" 
        :content_id.sync="courseData.content_id" 
        @getCurrentTime="getCurrentTime"
        @getCurCaption="getCurCaption" 
        @handleRecord="handleRecord" 
        :source_src="courseData.file_url"
        :playTime="playTime"
        ref="vidioDOM" 
      ></Video>
      <div class="current-time-tips" v-if="isCurrentTimeShow">
        <i class="el-icon-close" @click="isCurrentTimeShow = false"></i>
        <!-- <span>上次播放至{{this.playTime}}秒</span> -->
        <span>{{$langue('NetCourse_PlayAt', {seconds: this.playTime, defaultText: `上次播放至${this.playTime}秒` })}}</span>
        <span class="tips-btn" @click="toCurrentTime">点击跳转</span>
      </div>
    </div>
    <Scorm v-else-if="['Flash', 'Scorm', 'Doc'].includes(courseData.course_type)"
    class="scorm-box" :courseData="courseData"
    @handleScormRecord="handleScormRecord" />
    <div v-else class="video-box">
      <el-image lazy fit="fill"
        :src="courseData.photo_url ? courseData.photo_url : require('@/assets/img/default_bg_img.png')"
        class="item-image">
        <div slot="error" class="image-slot">
          <i class="default-icon-picture"></i>
        </div>
      </el-image>
    </div>
  </div>
</template>

<script>
// import axios from 'axios'
import { Video, Scorm } from '@/components/index'
import { getNetCourseInfo, netViewRecord } from 'config/api.conf'
import MoocJs from 'sdc-moocjs-integrator'
export default {
  components: {
    Video,
    Scorm
  },
  data() {
    return {
      courseData: {
        content_type: '',
        comment_count: 0,
        fav_count: 0
      },
      viewTimer: null,
      countTimer: null,
      captionData: [],
      captionCurTime: null,
      studyRecordQuery: {
        act_id: this.$route.query.course_id,
        area_id: this.$route.query.area_id || '',
        from: this.$route.query.jump_from || this.$route.query.from || '',
        learn_record_id: 0,
        is_finish: 0,
        is_review: 0,
        total_study_time: 0,
        my_study_progress: ''
        // 下面是 scorm 课件时才需要
        // data_model: '',
        // scorm_item_id: 1,
      },
      finishStatus: 0,
      playTime: 0,
      isCurrentTimeShow: false,
      curTimeShouTimer: null
    }
  },
  watch: {
    playTime(val) {
      if (val) {
        this.isCurrentTimeShow = true
        this.curTimeShouTimer = setTimeout(() => {
          this.isCurrentTimeShow = false
          this.curTimeShouTimer = null
        }, 15000)
      }
    }
  },
  computed: {
    isFormMooc() {
      return this.$route.query.from === 'mooc' || this.$route.query.from === 'spoc'
    },
    
    course_id() {
      return this.$route.query.course_id ? parseInt(this.$route.query.course_id) : ''
    },
    filterResourceName() {
      let { course_type } = this.courseData
      let name = ''
      if (['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(course_type)) {
        name = '视频'
      } else if (course_type === 'Audio') {
        name = '音频'
      } else if (course_type === 'Article') {
        name = '文章'
      } else if (course_type === 'Doc') {
        name = '文档'
      } else if (course_type === 'Scorm') {
        name = 'Scorm'
      } else if (course_type === 'Flash') {
        name = '压缩包'
      }
      return name
    }
  },
  mounted() {
    this.getCourseDetail()
    if (this.isFormMooc) {
      MoocJs.setPause(() => {
        this.$refs.vidioDOM.vedioPlayer.pause()
      })
      MoocJs.setPlay(() => {
        this.$refs.vidioDOM.vedioPlayer.play()
      })
    }
  },
  methods: {
    getCurCaption(data) {
      if (!this.captionData.length > 0) this.captionData = data
    },
    handleRecord(param) {
      if (param.evt === 'play') {
        // 重新学习，重置数据
        if (this.finishStatus === 1) {
          this.finishStatus = 0
          this.studyRecordQuery.is_review = 1 // 重播
          this.courseData.net_learn_record_id = ''
        }
        this.creatViewTimer(param)
        if (this.isFormMooc) {
          MoocJs.play()
        }
      }

      if (param.evt === 'pause' || param.evt === 'ended') {
        if (param.evt === 'ended') { // 学习完
          this.finishStatus = 1
        }
        this.viewRecord(param)
        clearInterval(this.viewTimer)
        clearInterval(this.countTimer)

        if (param.evt === 'pause' && this.isFormMooc) {
          MoocJs.pause()
        } else if (param.evt === 'ended' && this.isFormMooc) {
          MoocJs.complete()
        }
      }
    },
    handleScormRecord(scormdata) {
      this.viewRecord(scormdata, 'scorm')
    },
    getCurrentTime(curTime) {
      const captionBox = this.$el.querySelector('.caption-content')
      this.captionCurTime = Number(curTime.toFixed(2))
      let curDom = document.getElementsByClassName('caption-item-active')[0]?.previousElementSibling
      if (curDom) {
        captionBox.scrollTop = curDom.offsetTop - 18
      }
      this.studyRecordQuery.my_study_progress = Math.floor(this.captionCurTime) // 视频当前播放时长
    },
    creatViewTimer(param) {
      let _this = this
      this.countTimer = setInterval(function () {
        _this.studyRecordQuery.total_study_time++
      }, 1000)
      this.viewTimer = setInterval(() => {
        _this.viewRecord(param) // 浏览器时长需每15秒记录一次
      }, 15000) // 15000
    },
    viewRecord(videoParam, recordType) {
      const { net_learn_record_id } = this.courseData
      this.studyRecordQuery.is_finish = this.finishStatus
      this.studyRecordQuery.learn_record_id = net_learn_record_id
      let recordParam = this.studyRecordQuery
      if (recordType && recordType === 'scorm') {
        recordParam = videoParam
        recordParam.act_id = this.course_id
        recordParam.learn_record_id = net_learn_record_id || 0
      }
      netViewRecord(recordParam).then((data) => {
        if (data) {
          if (recordParam.is_finish) {
            this.courseData.net_learn_record_id = 0
          } else {
            this.courseData.net_learn_record_id = data
          }
        }
      })
    },
    getCourseDetail() {
      const { share_staff_id, share_staff_name } = this.$route.query
      getNetCourseInfo({ act_id: this.course_id, share_staff_id: share_staff_id || '', share_staff_name: share_staff_name || '' }).then((data) => {
        if (data.status === 3) {
          sessionStorage.setItem('401Msg', '该课程已下架！')
          this.$router.replace({
            name: '401'
          })
          return
        }
        document.title = `${data.course_name}_Q-Learning`
        this.courseData = data
        this.studyRecordQuery.learn_record_id = data.learn_record_id
        this.playTime = data.my_study_progress
        if (data.course_type === 'CaseStudy') {
          this.$messageBox('暂不支持该类型资源查看！', '提示', {
            distinguishCancelAndClose: true,
            confirmButtonText: '好的',
            cancelButtonText: '取消'
          })
        }
      }).catch((err) => {
        if (this.isFormMooc && (err.code === 403 || err.code === 500)) {
          MoocJs.sendErrorInfo(err.message)
        }
      })
    },
    // 跳转时间
    toCurrentTime() {
      this.isCurrentTimeShow = false
      this.$refs.vidioDOM.vedioPlayer.currentTime(this.playTime)
    },
    timeToSec(timeStr) {
      let time = timeStr.split(',')[0]
      let s = ''
      const hour = time.split(':')[0]
      const min = time.split(':')[1]
      const sec = time.split(':')[2]
      let misecond = timeStr.split(',')[1].substr(0, 2)
      misecond = Number(parseInt(misecond) / 100)
      s = Number(hour * 3600) + Number(min * 60) + Number(sec)
      return (s * 100 + misecond * 100) / 100
    }
  },
  beforeDestroy() {
    clearInterval(this.viewTimer)
    clearTimeout(this.curTimeShouTimer)
    MoocJs.removeEvent()
  }
}
</script>

<style lang="less" scoped>
.net-page {
  width: 100%;
  height: 100%;
  .video-box,
  .scorm-box {
    width: 100%;
    height: 100%;
  }
  :deep(.vjs-poster){
    background-size: contain;
  }
}
.video-main {
  width: 100%;
  height: 100%;
  position: relative;
  .current-time-tips {
    position: absolute;
    left: 10px;
    bottom: 50px;
    background: #0F1010;
    color: #fff;
    height: 26px;
    line-height: 26px;
    font-size: 14px;
    display:flex;
    align-items:center;
    padding: 0 5px;
    border-radius: 2px;
    .el-icon-close {
      margin-right: 5px;
      cursor: pointer;
      font-size: 16px
    }
    .tips-btn {
      cursor: pointer;
      color:#3464e0;
      margin-left: 5px;
    }
  }
}
</style>
