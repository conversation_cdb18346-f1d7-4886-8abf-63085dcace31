<template>
  <div class="enterPage-container">
    <convention-banner style="margin: 0 auto 16px;" class="convention-banner-box" />
    <div class="enterPage-container-content">
      <left-menu></left-menu>
      <div class="enterPage-container-content-right">
        <router-view></router-view>
      </div>
    </div>
  </div>
</template>

<script>
import conventionBanner from '@/views/components/convention-banner.vue'
import leftMenu from '@/views/components/leftMenu.vue'
import { loadJS } from '@/utils/tools.js'

export default {
  components: {
    conventionBanner,
    leftMenu
  },
  data() {
    return {
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name && !window.$qlCommonHeader) {
          this.linkHref = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeaderStyle.css` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeaderStyle.css`  
          const commonHeaderJsUrl = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeader.js` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeader.js`
          loadJS(commonHeaderJsUrl, function() {
            if (window.$qlCommonHeader && document.getElementsByClassName('common-header').length < 1) {
              window.$qlCommonHeader.create({
                staff_name: val.staff_name,
                dev: !(process.env.NODE_ENV === 'production')
                // from: 'creationCenter'
              })
              
              setTimeout(() => {
                let el = document.getElementsByClassName('common-header-occupy')
                if (el.length > 0) {
                  console.log('El', el[0].offsetHeight)
                  document.getElementById('app').style.height = `calc(100% - ${el[0].offsetHeight}px)`
                }
              }, 500)
            }
          })
        }
      },
      immediate: true
    }
  },
  methods: {
    handleRouterPath(name) {
      this.$router.push({ name })
    }
  }
}
</script>
<style lang="less" scoped>
body {
  background-color: #f5f5f5;

}
.enterPage-container {
  width: 100%;
  padding-top: 12px;
  background-color: #F6F7F9;
  .enterPage-container-content {
    min-height: 60vh;
    margin: 0 auto;
    display: flex;
    .enterPage-container-content-right {
      flex: 1;
      margin-left: 12px;
      overflow-x: hidden;
    }
  }
}

@media screen and (max-width: 1660px) {
  .enterPage-container-content,
  .convention-banner-box {
    width: 1180px;
  }
  
}
@media screen and (min-width: 1661px) {
  .enterPage-container-content,
  .convention-banner-box {
    width: 1420px;
  }
}
</style>
