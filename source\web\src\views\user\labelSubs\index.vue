<template>
  <div class='labelPage'>
    <aside class='mySubscribe' v-if="!isLabelGatherPage">
        <header class="subsTitle flex justify-between">
          <div>我的订阅</div>
          <span @click="dialogVisible = true, radioType=1" :dt-eid="dtEid('管理')" :dt-remark="dtRemark('管理')">管理</span>
        </header>
        <div class="subsType">
          <span :class="{'active': !subsType}" @click="changeSubsType('')">不限({{ isSearch ? allFilterCount.all : allSubsCount }})</span>
          <span :class="{'active': subsType == 1}" @click="changeSubsType(1)">标签({{ isSearch ? allFilterCount.labels : labels.length }})</span>
          <span :class="{'active': subsType == 2}" @click="changeSubsType(2)">专区({{ isSearch ? allFilterCount.specials : specials.length }})</span>
        </div>
        <section class="subsData">
          <div class="subsDataTop" v-if="allSubsCount">
            <div class="subsAllsItem flex align-center justify-between" :style="{'right':right2,'z-index':z_index}">
                <span class="flex-1 align-center" :class="{'active':labelIndex === ''}"  @click="changeLabel('')">
                  <img v-if="labelIndex === ''" :src="require('@/assets/img/wallet.png')" style="margin-top:-2px;margin-right:10px;" alt="">
                  <img v-else :src="require('@/assets/img/prefixIcon.png')" style="margin-top:-1px;margin-right:10px;"  alt="">
                  <span>全部</span>
                </span>
                <img :src="require('@/assets/img/search.png')" style="cursor: pointer;"  @click="isSearch=!isSearch" alt="搜索">
            </div>
            <div class="searchMySubs flex align-center justify-between relative" :style="{'left':left}">
                <el-input
                  class="flex-1"
                  v-model="keywords"
                  placeholder="请输入关键词"
                  size="small">
                </el-input>
                <span class="clearWord" v-if="keywords" @click.stop="keywords = ''"><i class="el-icon-error"></i></span>
                <img :src="require('@/assets/img/search.png')" style="width: 16px;position: absolute;left: 10px;"  @click="isSearch=!isSearch" alt="搜索">
                <span class="cancel" @click="isSearch=!isSearch;keywords = '' ">取消</span>
            </div>
          </div>
          <section class="center NoSubsData" v-if="isSearch && newLabels?.length<1 && keywords">
              <img :src="require('@/assets/img/empty_2x.png')">
              <p>{{!subsType? '暂无已订阅的标签和专区' : subsType == 1 ? "暂无已订阅的标签" : "暂无已订阅的专区"}}</p>
              <el-button 
                v-show="subsType != 2"
                type="primary" 
                round 
                size="small" 
                @click="dialogVisible = true, radioType=1"
                :dt-eid="dtEid('订阅更多标签')" 
                :dt-remark="dtRemark('订阅更多标签')"
              >订阅更多标签</el-button>
              <el-button 
                v-show="subsType != 1"
                type="primary" 
                round 
                size="small" 
                @click="dialogVisible = true, radioType=3"
                :dt-eid="dtEid('订阅更多专区')" 
                :dt-remark="dtRemark('订阅更多专区')"
              >订阅更多专区</el-button>
          </section>
          <ul class="subsList" v-else>
            <li
              class="flex align-center" 
              v-for="(item,index) in newLabels" 
              :key="index" 
              :class="{'active':index === labelIndex}"
              @click="changeLabel(item, index)"
            >
              <div class="label-items-label flex align-center">
                <img   
                  :dt-eid="dtEid(item, '我订阅的标签')"
                  :dt-remark="dtRemark(item, '我订阅的标签')"
                  :dt-areaid="dataAreaid(item, '我订阅的标签')"
                  v-if="index === labelIndex && item.subscribe_type === 1"
                  :src="require('@/assets/img/addlabel2.png')"
                >
                <img 
                  v-else-if="index !== labelIndex && item.subscribe_type === 1"
                  :src="require('@/assets/img/addlabel.png')"
                  :dt-eid="dtEid(item, '我订阅的标签')" 
                  :dt-remark="dtRemark(item, '我订阅的标签')"
                  :dt-areaid="dataAreaid(item, '我订阅的标签')"  
                  alt=""
                >
                <img 
                  v-else-if="index === labelIndex && item.subscribe_type === 2"
                  :src="require('@/assets/img/label/specails_active.png')"
                  :dt-eid="dtEid(item, '我订阅的标签')" 
                  :dt-remark="dtRemark(item, '我订阅的标签')"
                  :dt-areaid="dataAreaid(item, '我订阅的标签')"  
                  alt=""
                >
                <img 
                  v-else-if="index !== labelIndex && item.subscribe_type === 2"
                  :src="require('@/assets/img/label/specails.png')"
                  :dt-eid="dtEid(item, '我订阅的标签')" 
                  :dt-remark="dtRemark(item, '我订阅的标签')"
                  :dt-areaid="dataAreaid(item, '我订阅的标签')"  
                  alt=""
                >
                <span 
                  class="span_text"
                  :title="item.label_name"
                  v-html="item.new_key"
                  :dt-eid="dtEid(item, '我订阅的标签')" 
                  :dt-remark="dtRemark(item, '我订阅的标签')"
                ></span>
              </div>
            </li>
          </ul>
        </section>
    </aside>
    <template v-if="isLabelGatherPage">
      <labelGather :labels="newLabels" :labelIds="[...label_ids, ...special_ids]"/>
    </template>
    <main class='labelContent'>
      <detailTop :labelId="searchForm.label_id" :labelName="labelName" :subsType="subsType" :isLabelGatherPage="isLabelGatherPage" :labelDetail="labelDetail"></detailTop>
      <div class="moduleData flex">
        <img :src="require('@/assets/img/label/left.png')" class="prev" v-show="showModulePrev" @click="prevCli" alt="">
        <ul class='moduleList flex' ref="modules">
          <li v-for="item in labelModules" :key="item.module_id + 'c'" class="moduleItem" :class="{'active':searchForm.module_id===item.module_id}" @click="changeModule(item)">
            <span>{{ item.name }}</span>
            <span v-if="isSubsLen">({{ item.count> 999 ? '999+' : item.count }})</span>
          </li>
        </ul>
        <img :src="require('@/assets/img/label/right.png')" class="next" v-show="showModuleNext" @click="nextCli" alt="">
      </div>
      <section class='contentFilter flex align-center justify-between' v-show="sortSubsIds.length > 0 || isLabelGatherPage">
        <div class="flex flex-1 align-center">
          <section class="filterSection flex">
            <div class="flex align-center">上新时间：
              <el-tooltip class="item" effect="dark" popper-class='timePopper' content="指课程发布/开始时间或关联至专区的时间" placement="bottom-start">
                <img :src="require('@/assets/img/label/<EMAIL>')" style="width: 16px;cursor: pointer;margin: 3px 12px 3px 0;">
              </el-tooltip>
            </div>
            <div>
              <span
                class="filterItem"
                :class="{'active':!curTime}" 
                key='all'  
                @click="changeTime(null)" 
                :dt-eid="dtEid('全部', '上新时间')" 
                :dt-remark="dtRemark('全部', '上新时间')"
              >全部</span>
              <span
                class="filterItem"
                :class="{'active':curTime === item.value}" 
                @click="changeTime(item.value)" 
                v-for='(item, index) in time_s' 
                :key='index + "d"'
                :dt-eid="dtEid(item.name, '上新时间')" 
                :dt-remark="dtRemark(item.name, '上新时间')" 
              >{{item.name}}</span>
            </div>
          </section>
          <section class="filterSection filterSort flex" v-if='!searchForm.module_id || labelModuleSort[searchForm.module_id]?.sorts.length>0'>
            <div>排序方式：</div>
            <div v-if="!searchForm.module_id">
              <span
                class="filterItem"
                :class="{'active':searchForm.sortBy === item.field}"
                v-for='(item, index) in labelModuleSort.all.sorts'
                :key='index + "a"'
                @click='changeSort(item)'
                :dt-eid="dtEid(item.title, '排序方式')" 
                :dt-remark="dtRemark(item.title, '排序方式')" 
              >{{ item.title }}</span>
            </div>
            <div v-else>
              <span
                class="filterItem"
                :class="{'active':searchForm.sortBy === item.field}"
                v-for='(item, index) in labelModuleSort[searchForm.module_id].sorts'
                :key='index + "b"'
                @click='changeSort(item)'
                :dt-eid="dtEid(item.title, '排序方式')" 
                :dt-remark="dtRemark(item.title, '排序方式')" 
                >{{ item.title }}</span>
            </div>
          </section>
          <div class="excellentCheckbox" v-if='labelModuleSort[searchForm.module_id]?.isExce'>
            <div class="mask" v-if="this.excellentData"></div>
            <el-checkbox class="excellentChecked" v-model="checked" @change="changeExcellent">只看精品</el-checkbox>
          </div>
        </div>
        <span class="contentCount justify-end">仅显示有权限的内容，共<a class="linkKey"><span style="font-weight:600;">{{ total }}</span></a>个</span>
      </section>
      <section v-if="isShowData" id="labelContentList1" class="subsCardListBox" @scroll="handleScroll">
        <div class="content-wraper" id="content-wraper1">
          <template v-if="!searchForm.module_id">
            <h4 class="contentTitle" v-if="contentCounts.futCount>0 && contentCounts.preCount>0">即将开始</h4>
            <div class="subsCardList flex flex-wrap">
              <resultItem  v-for="(item,index) in futContents" :key="item.item_id + index + 'fut'" :info="item" :isLabelGatherPage="isLabelGatherPage" :subsType="subsType" :contentType="labelDetail.subscribe_type" :curModuleId="searchForm.module_id" :curLabelId="searchForm.label_id" :sortSubsIds="[labels, specials]"></resultItem>
            </div>
            <h4 class="contentTitle" style="margin-top: 24px" v-if="contentCounts.futCount>0 && contentCounts.preCount>0">立即学习</h4>
            <div class="subsCardList flex flex-wrap" v-if="isAll">
              <resultItem  v-for="(item,index) in preContents" :key="item.item_id + index + 'pre'" :info="item" :isLabelGatherPage="isLabelGatherPage" :subsType="subsType" :contentType="labelDetail.subscribe_type" :curModuleId="searchForm.module_id" :curLabelId="searchForm.label_id" :sortSubsIds="[labels, specials]"></resultItem>
            </div>
          </template>
          <template v-if="searchForm.module_id">
            <div class="subsCardList flex flex-wrap" style="min-height:auto">
              <resultItem  v-for="(item,index) in moduleContents" :key="item.item_id + index + 'mod'" :info="item" :isLabelGatherPage="isLabelGatherPage" :subsType="subsType" :contentType="labelDetail.subscribe_type" :curModuleId="searchForm.module_id" :curLabelId="searchForm.label_id" :sortSubsIds="[labels, specials]"></resultItem>
            </div>
          </template>
          <p v-if="!loading && !noMore" class="center bottm-center">下拉加载更多</p>
          <p v-if="loading" class="center bottm-center">加载中...</p>
          <template v-if="noMore">
            <p v-if="!isLabelGatherPage" class="center bottm-center" style="padding:25px 0 31px; color: #00000099; font-size: 16px;line-height:24px;">没有更多内容了，试试<a class="linkKey" @click="dialogVisible = true, radioType=1">订阅更多标签和专区</a>吧~</p>
            <p v-if="isLabelGatherPage" class="center bottm-center"  style="padding:25px 0 31px; color: #00000099; font-size: 16px;line-height:24px;">没有更多内容了，看看<a class="linkKey" href="https://portal.learn.woa.com/training/label-subs" target="_blank">更多的订阅内容</a>吧~</p>
          </template>
        </div>
      </section>
      <section class="noMoreSubs center" v-else>
        <div v-if="is_loaded">
          <img :src="require('@/assets/img/loading.png')">
          <p>数据加载中...</p>
        </div>
        <div v-if="!is_loaded">
          <img :src="require('@/assets/img/empty_2x.png')">
          <template v-if="isLabelGatherPage">
            <p>暂无相关内容，看看已订阅的内容吧~</p>
            <el-button 
              type="primary" 
              round size="small" 
              @click="toContentPage"
              :dt-remark="dtRemark('查看更多订阅内容')"
            >查看更多订阅内容</el-button>
          </template>
          <template v-else>
            <p>
              <span v-if="!Number(subsType) && allSubsCount<1">您还没有订阅任何标签和专区，无法展示相关内容~</span>
              <span v-else-if="subsType == 1 && labels.length < 1">您还没有订阅任何标签，无法展示相关内容~</span>
              <span v-else-if="subsType == 2 && specials.length < 1">您还没有订阅任何专区，无法展示相关内容~</span>
              <span v-else-if="contentLen<1 && allSubsCount>0">暂无相关内容，试试订阅更多标签和专区吧~</span>
            </p>
            <template v-if="!Number(subsType) && allSubsCount<1">
              <el-button 
                type="primary" 
                round size="small" 
                @click="dialogVisible = true, radioType=1"
                :dt-remark="dtRemark('开始订阅标签')"
              >开始订阅标签</el-button>
              <el-button 
                type="primary" 
                round size="small" 
                @click="dialogVisible = true, radioType=3"
                :dt-remark="dtRemark('开始订阅专区')"
              >开始订阅专区</el-button>
            </template>
            <el-button 
              v-else-if="(subsType == 1 && labels.length < 1) || (!subsType && allSubsCount<1)"
              type="primary" 
              round size="small" 
              @click="dialogVisible = true, radioType=1"
              :dt-remark="dtRemark('开始订阅标签')"
            >开始订阅标签</el-button>
            <el-button 
              v-else-if="subsType == 2 && specials.length < 1 || (!subsType && allSubsCount<1)"
              type="primary" 
              round size="small" 
              @click="dialogVisible = true, radioType=3"
              :dt-remark="dtRemark('开始订阅专区')"
            >开始订阅专区</el-button>
            <template v-else-if="allSubsCount > 0 && contentLen<1">
              <el-button 
                type="primary" 
                round size="small" 
                @click="dialogVisible = true, radioType=1"
                :dt-remark="dtRemark('订阅更多标签')"
              >订阅更多标签</el-button>
              <el-button 
                type="primary" 
                round size="small" 
                @click="dialogVisible = true, radioType=3"
                :dt-remark="dtRemark('订阅更多专区')"
              >订阅更多专区</el-button>
            </template>
          </template>
        </div>
      </section>
    </main>
    <!-- <labelSubsProps v-if="dialogVisible" :radioType="radioType" @input="subsLabelClose"></labelSubsProps> -->
    <sdc-sub-label-manage
      id="123"
      class="subLabelDialog" 
      v-if="dialogVisible"
      :radioType="radioType"
      :labelse="dialogVisible" 
      :labelNodeEnv="labelNodeEnv" 
      :dtArg="dtArg"
      @input="subsLabelClose"
      >
    </sdc-sub-label-manage>
  </div>
</template>
<script>
import { getSubsLabels, getLabelDetail, getLabelContents, subscribeLabel, getSpecialList, getLabelSubscribeDetailInfo } from '@/config/mooc.api.conf.js'
// import labelSubsProps from './src/index.vue'
import resultItem from './resultItem/resultItem.vue'
import detailTop from './resultItem/child/detailTop.vue'
import labelGather from './resultItem/child/labelGather.vue'
// import { pcCoverLogo } from '@/utils/outsourcedCourseMap.js'
function getDateFn(time, t, b) {
  let date = ''
  if (t === '-') {
    date = new Date(new Date().getTime() - time)
  } else {
    date = new Date(new Date().getTime() + time)
  }
  let y = date.getFullYear()
  let m = (date.getMonth() + 1) < 10 ? ('0' + (date.getMonth() + 1)) : (date.getMonth() + 1)
  let d = date.getDate() < 10 ? ('0' + date.getDate()) : date.getDate()
  if (b === 's') {
    return y + '-' + m + '-' + d + ' 00:00:00'
  } else {
    return y + '-' + m + '-' + d + ' 23:59:59'
  }
}
export default {
  name: 'labelContent',
  data() {
    return {
      labelIndex: '',
      subsType: 0, // 订阅类型
      keywords: '', // 订阅搜索关键词
      isSearch: false, // 是否输入搜索关键词
      labelName: '',
      host: window.location.host.endsWith('.woa.com'),
      labelNodeEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test',
      checked: false, // 是否选中精品
      labels: [], // 订阅标签
      specials: [], // 订阅专区
      label_ids: [], // 所有label_ids
      special_ids: [], // 专区ids
      labelDetail: {
        subscribe_type: ''
      }, // 标签详情
      curTime: '',
      time_s: [
        {
          name: '最近一周',
          value: ['[]', getDateFn((86400000 * 7), '-', 's'), getDateFn((86400000 * 7), '+', 'e')]
        },
        {
          name: '最近一月',
          value: ['[]', getDateFn((86400000 * 30), '-', 's'), getDateFn((86400000 * 30), '+', 'e')]
        }
      ],
      // 排序字段
      labelModuleSort: {
        1: {
          // 网络课
          sorts: [
            // { title: '个性化', field: 'personal,origin_data.sort_time' },
            { title: '最新', field: 'origin_data.sort_time' },
            { title: '热门', field: 'view_count,origin_data.sort_time' },
            { title: '评分最高', field: 'origin_data.avg_score,origin_data.sort_time' }
          ],
          isExce: false
        },
        10: {
          // 培养项目-
          sorts: [
            // { title: '个性化', field: 'personal,origin_data.sort_time' },
            { title: '最新发布', field: 'origin_data.sort_time' },
            { title: '最多参与', field: 'origin_data.member_count,origin_data.sort_time' },
            { title: '评分最高', field: 'origin_data.score,origin_data.sort_time' },
            { title: '点赞最多', field: 'origin_data.praise_count,origin_data.sort_time' }
          ],
          isExce: true
        },
        8: {
          // 文章-
          sorts: [
            // { title: '个性化', field: 'personal,origin_data.sort_time' },
            { title: '最新', field: 'origin_data.sort_time' },
            { title: '热门', field: 'view_count,origin_data.sort_time' }
          ],
          isExce: true
        },
        7: {
          // 案例-
          sorts: [
            // { title: '个性化', field: 'personal,origin_data.sort_time' },
            { title: '最新', field: 'origin_data.sort_time' },
            { title: '热门', field: 'view_count,origin_data.sort_time' }
          ],
          isExce: true
        },
        15: {
          // 课单-
          sorts: [
            // { title: '个性化', field: 'personal,origin_data.sort_time' },
            { title: '最新课单', field: 'origin_data.sort_time' },
            { title: '观看最多', field: 'view_count,origin_data.sort_time' },
            { title: '点赞最多', field: 'origin_data.praise_count,origin_data.sort_time' },
            { title: '收藏最多', field: 'origin_data.favorite_count,origin_data.sort_time' }
          ],
          isExce: true
        },
        6: {
          // 行家-
          sorts: [
            // { title: '个性化', field: 'personal,origin_data.sort_time' },
            { title: '最新', field: 'origin_data.sort_time' },
            { title: '热门', field: 'origin_data.meetNum,origin_data.sort_time' },
            { title: '评分最高', field: 'origin_data.count,origin_data.sort_time' }
          ],
          isExce: false
        },
        3: {
          // 直播-
          sorts: [],
          isExce: false
        },
        2: {
          // 面授课-
          sorts: [
            // { title: '个性化', field: 'personal,origin_data.sort_time' },
            { title: '最新', field: 'origin_data.sort_time' },
            { title: '热门', field: 'view_count,origin_data.sort_time' },
            { title: '评分最高', field: 'origin_data.avg_score,origin_data.sort_time' }
          ],
          isExce: false
        },
        4: {
          // 活动-
          sorts: [
            // { title: '个性化', field: 'personal,origin_data.sort_time' },
            { title: '最新', field: 'origin_data.sort_time' },
            { title: '热门', field: 'view_count,origin_data.sort_time' }
          ],
          isExce: false
        },
        16: {
          // 文档-
          sorts: [
            // { title: '个性化', field: 'personal' },
            { title: '最新', field: 'origin_data.sort_time' },
            { title: '热门', field: 'view_count,origin_data.sort_time' }
          ],
          isExce: false
        },
        20: {
          // k吧-
          sorts: [
            // { title: '个性化', field: 'personal' },
            { title: '最新', field: 'origin_data.sort_time' },
            { title: '热门', field: 'view_count,origin_data.sort_time' }
          ],
          isExce: false
        },
        99: {
          // 外链-
          sorts: [
            // { title: '个性化', field: 'personal' },
            { title: '最新', field: 'origin_data.sort_time' }
          ],
          isExce: false
        },
        'all': {
          // 综合-
          sorts: [
            // { title: '个性化', field: 'personal' },
            { title: '最新', field: 'origin_data.sort_time' }
          ],
          isExce: false
        }
      },
      // 标签模块
      labelModules: [
        {
          name: '综合',
          module_id: '',
          count: 0
        },
        {
          name: '网络课',
          module_id: 1,
          count: 0
        },
        {
          name: '培养项目',
          module_id: 10,
          count: 0
        },
        {
          name: '文章',
          module_id: 8,
          count: 0
        },
        {
          name: '案例',
          module_id: 7,
          count: 0
        },
        {
          name: '文档',
          module_id: 16,
          count: 0
        },
        {
          name: '课单',
          module_id: 15,
          count: 0
        },
        {
          name: '行家',
          module_id: 6,
          count: 0
        },
        {
          name: '直播',
          module_id: 3,
          count: 0
        },
        {
          name: '面授课',
          module_id: 2,
          count: 0
        },
        {
          name: '活动',
          module_id: 4,
          count: 0
        },
        {
          name: 'k吧文章',
          module_id: 20,
          count: 0
        },
        {
          name: '外链课程',
          module_id: 99,
          count: 0
        }
      ],
      // 即将开始的课程
      futContents: [],
      // 已上新的内容
      preContents: [],
      // 单个模块内容
      moduleContents: [],
      // 查询参数
      searchForm: {
        label_id: '',
        module_id: '',
        groupFields: ['module_id'],
        filters: {},
        sortBy: 'origin_data.sort_time',
        sortOrder: 'desc',
        pageFrom: 1,
        pageSize: 12
      },
      // 课程页码
      futPageFrom: 1,
      // 已上新页码
      prePageFrom: 1,
      // 单个模块页码
      modulePageFrom: 1,
      // 是否已经获取完即将开始的课程
      isAll: false,
      total: 0, // 内容数量
      dialogVisible: false, // 是否显示订阅标签弹窗
      radioType: 1, // 弹窗tab
      isLabelGatherPage: false, // 是否是标签聚合页
      left: '-300px',
      right2: '0',
      z_index: '0',
      loading: false, // 内容加载中
      is_loaded: true, // 
      noMore: false, // 没有更多数据了
      that: this,
      isPop: false, // 未订阅时，是否已弹出弹窗
      isSubsCl: false, // 是否执行过聚合页标签订阅方法
      excellentData: false,
      showModulePrev: false,
      showModuleNext: false,
      isGetFutData: false,
      isGetPreData: false,
      isGetModuleData: false
    }
  },
  components: {
    // labelSubsProps,
    resultItem,
    detailTop,
    labelGather
  },
  computed: {
    dtArg() {
      let { label_id, label_name } = this.$route.query
      return {
        page: label_name || document.title,
        page_type: `${document.title}页`,
        container: '订阅弹窗',
        content_name: '订阅抽奖入口',
        course_id: label_id || Math.random().toFixed(3) * 1000
      }
    },
    // 过滤订阅标签
    newLabels() {
      let subsList = []
      if (!this.subsType) {
        subsList = [...this.labels, ...this.specials]
      } else if (this.subsType === 1) {
        subsList = [...this.labels]
      } else {
        subsList = [...this.specials]
      }
      subsList.forEach(item => {
        item.new_key = item.label_name
      })
      subsList.sort((a, b) => new Date(a.subscribe_time).getTime() - new Date(b.subscribe_time).getTime())
      const regex = new RegExp(this.keywords, 'gi')
      let data = (this.isSearch && this.keywords) ? subsList.filter(item => {
        item.new_key = item.label_name
        if (item.label_name) {
          if (item.label_name.match(regex)) {
            // 不区分大小写
            // let index = item.label_name.toLowerCase().indexOf(this.keywords.toLowerCase())
            // let name0 = item.label_name.substr(0, index)
            // let name1 = `<span style="color:#0052D9;">${item.label_name.substr(index, this.keywords.length)}</span>`
            // let name2 = item.label_name.substr(index + this.keywords.length)
            // let newKeys = name0 + name1 + name2
            // item.new_key = newKeys
            let word = item.label_name.match(regex)
            let newKeys = item.label_name.replace(regex, `<span style="color:#0052D9;">${word[0]}</span>`)
            item.new_key = newKeys
          }
          return item.label_name.match(regex)
        }
      }) : subsList
      return data
    },
    sortSubsIds() {
      return [...this.labels, ...this.specials].sort((a, b) => new Date(b.subscribe_time).getTime() - new Date(a.subscribe_time).getTime())
    },
    contentLen() {
      return [...this.futContents, ...this.preContents, ...this.moduleContents].length
    },
    // 是否有订阅标签或专区
    isSubsLen() {
      if (this.isLabelGatherPage) {
        return true
      }
      if (this.label_ids.length > 0 && (this.subsType * 1) === 1) {
        return true
      }
      if (this.special_ids.length > 0 && (this.subsType * 1) === 2) {
        return true
      }
      if (!(this.subsType * 1) && [...this.special_ids, ...this.label_ids].length > 0) {
        return true
      }
      return false
    },
    disabled() {
      return this.noMore
    },
    isShowData() {
      if (!this.searchForm.module_id) {
        if ([...this.preContents, ...this.futContents].length > 0) {
          return true
        } else {
          return false
        }
      } else {
        if (this.searchForm.module_id && this.moduleContents.length > 0) {
          return true
        } else {
          return false
        }
      }
    },
    // 订阅总数量
    allSubsCount() {
      return this.labels.length + this.specials.length
    },
    contentCounts() {
      let count1 = 0
      let count2 = 0
      this.labelModules.forEach((item, index) => {
        if (index > 0) {
          if ([2, 3, 4].includes(item.module_id)) {
            count1 += item.count * 1
          } else {
            count2 += item.count * 1
          }
        }
      })
      return {
        preCount: count2,
        futCount: count1
      }
    },
    allFilterCount() {
      let subsList = [...this.labels, ...this.specials]
      const regex = new RegExp(this.keywords, 'gi')
      let data = subsList.filter(item => item.label_name.match(regex))
      const countObj = {
        all: data.length,
        labels: data.filter(item => item.subscribe_type === 1).length,
        specials: data.filter(item => item.subscribe_type === 2).length
      }
      return countObj
    },
    dtEid() {
      return (val, container) => {
        let label = container ? `_${container}` : ''
        if (typeof val === 'string') {
          return `element_${val}`
        }
        return `element_${val.label_id}${label}`
      }
    },
    dataAreaid() {
      return (val, container) => {
        let label = container ? `_${container}` : '' 
        if (typeof val === 'string') {
          return `area_${val}`
        }
        return `area_${val.label_id}${label}`
      }
    },
    dtRemark() {
      return (val, container) => {
        if (typeof val === 'string') {
          return JSON.stringify({ 
            page: '标签订阅内容', 
            page_type: '标签订阅内容',
            container: container || '',
            click_type: 'button',
            content_name: val,
            terminal: 'PC'
          })
        }
        return JSON.stringify({ 
          page: '标签订阅内容', 
          page_type: '标签订阅内容',
          container,
          click_type: 'data',
          content_name: val.label_name,
          content_id: val.label_id,
          terminal: 'PC',
          content_type: '标签'
        })
      }
    }
  },
  watch: {
    isSearch() {
      if (this.isSearch) {
        this.left = '0'
        this.right2 = '-300px'
        this.z_index = '-10'
      } else {
        this.left = '-300px'
        this.right2 = '0'
        this.z_index = '0'
      }
    },
    labelModules: {
      handler(val) {
        this.showBtn()
      },
      deep: true,
      immediate: true
    }
  },
  beforeCreate() {
    let isLabelGatherPage = this.$route.query.isLabelGatherPage || false
    if (isLabelGatherPage) {
      document.title = '标签关联内容'
    }
  },
  created() {
    let type = this.$route.query.subsType || 0
    this.subsType = type * 1
    if (this.subsType !== 1 && this.subsType !== 2) {
      this.subsType = 0
    }
  },
  mounted() {
    window.onresize = function() {
      setTimeout(() => {
        const labelContentList1 = document.getElementById('labelContentList1')
        if (!labelContentList1) return
        const wrapper1 = document.getElementById('content-wraper1')
        if (labelContentList1.offsetHeight > wrapper1.offsetHeight && !this.noMore) {
          wrapper1.style.height = document.getElementsByClassName('labelContent')[0].clientHeight - 174 + 'px'
          labelContentList1.style.height = wrapper1.offsetHeight - 5 + 'px'
        }
      }, 400)
    }
    // 判断是否是聚合页
    this.isLabelGatherPage = this.$route.query.isLabelGatherPage || false
    if (this.isLabelGatherPage) {
      this.labelName = this.$route.query.label_name
      let label_id = this.$route.query.label_id
      if (!label_id) {
        this.is_loaded = false
        return
      } 
      this.searchForm.label_id = label_id
      this.getLabels(1)
    } else {
      // 从邮件跳转过来带参数
      let { stime, dialog } = this.$route.query
      // 上新时间默认选中一周
      if (stime && stime === '1') {
        this.searchForm.filters['sort_time'] = this.time_s[0].value
        this.searchForm.filters['page_list.add_time'] = this.time_s[0].value
        this.curTime = this.time_s[0].value
      } else if (stime && stime === '2') {
        this.searchForm.filters['sort_time'] = this.time_s[1].value
        this.searchForm.filters['page_list.add_time'] = this.time_s[1].value
        this.curTime = this.time_s[1].value
      }
      // 打开提醒弹窗
      if (dialog) {
        this.radioType = dialog
        this.dialogVisible = true
        this.isPop = true
      }
      let { label_id, special_id } = this.$route.query
      if (label_id) {
        this.getLabels(1, Number(label_id))
        delete this.searchForm.filters['page_list.add_time']
      } else if (special_id) {
        this.getLabels(1, null, Number(special_id))
        delete this.searchForm.filters['sort_time']
      } else {
        this.getLabels(1)
      }
    }
  },
  methods: {
    toContentPage() {
      window.open(location.origin + location.pathname)
    },
    // 获取列表高度设置高度
    setLabelContentLIstHeight() {
      this.$nextTick(() => {
        const labelContentList1 = document.getElementById('labelContentList1')
        if (!labelContentList1) return
        const wrapper1 = document.getElementById('content-wraper1')
        if (labelContentList1.offsetHeight > wrapper1.offsetHeight && !this.noMore) {
          wrapper1.style.height = document.getElementsByClassName('labelContent')[0].clientHeight - 174 + 'px'
          labelContentList1.style.height = wrapper1.offsetHeight - 5 + 'px'
        }
      })
    },
    showBtn() {
      this.$nextTick(() => {
        let ele = this.$refs.modules
        if (ele.clientWidth >= ele.scrollWidth) {
          this.showModulePrev = false
          this.showModuleNext = false
          return
        }
        if (ele.clientWidth + ele.scrollLeft >= ele.scrollWidth) {
          this.showModulePrev = true
          this.showModuleNext = false
        } else {
          this.showModulePrev = false
          this.showModuleNext = true
        }
      })
    },
    prevCli() {
      this.$refs.modules.scrollLeft = 0
      this.showBtn()
    },
    nextCli() {
      this.$refs.modules.scrollLeft = this.$refs.modules.scrollWidth - this.$refs.modules.clientWidth
      this.showBtn()
    },
    // 获取专区详情
    getSpecialDetail(ids) {
      let obj = {
        filters: {
          'module_id': ['=', 12],
          'item_id': ['in', ...ids]
        },
        newFlag: true,
        pageFrom: 1,
        pageSize: ids.length
      }
      return getSpecialList(obj).then((res) => {
        if (res.content) {
          let data = res.content
          this.specials.forEach((item, index) => {
            data.forEach((val, i) => {
              if (String(item.label_id) === val.item_id) {
                item.title = val.title
                item.brief = val.brief
                item.href = val.href
                item.label_name = val.title
              }
            })
          })
        }
      })
    },
    // 切换订阅类型
    async changeSubsType(type) {
      this.labelIndex = ''
      this.subsType = type
      this.resetData()
      delete this.searchForm.filters['sort_time']
      delete this.searchForm.filters['page_list.add_time']
      delete this.searchForm.filters['page_list.page_id']
      delete this.searchForm.filters['label_system_ids']
      this.labelDetail = {
        subscribe_type: ''
      }
      this.searchForm.label_id = ''
      this.setModuleCount({})
      if (!(this.subsType * 1)) {
        if (this.label_ids.length > 0) {
          this.searchForm.filters['label_system_ids'] = ['in', ...this.label_ids]
          if (this.curTime) this.searchForm.filters['sort_time'] = this.curTime
        }
        if (this.special_ids.length > 0) {
          this.searchForm.filters['page_list.page_id'] = ['in', ...this.special_ids]
          if (this.curTime) this.searchForm.filters['page_list.add_time'] = this.curTime
        }
      }
      if (this.labels.length > 0 && (this.subsType * 1) === 1) {
        this.searchForm.filters['label_system_ids'] = ['in', ...this.label_ids]
        if (this.curTime) this.searchForm.filters['sort_time'] = this.curTime
      }
      if (this.specials.length > 0 && (this.subsType * 1) === 2) {
        this.searchForm.filters['page_list.page_id'] = ['in', ...this.special_ids]
        if (this.curTime) this.searchForm.filters['page_list.add_time'] = this.curTime
      }
      if (this.isSubsLen) {
        if (this.searchForm.module_id) {
          await this.getModuleContent()
          this.getCounts()
        } else {
          await this.getAllContents()
        }
      } else {
        this.is_loaded = false
      }
    },
    getLabelSubscribeDetailInfo(id, type) {
      return getLabelSubscribeDetailInfo({
        label_id: id,
        subscribe_type: type
      }).then(res => {
        return res
      }).catch(() => {
        return null
      })
    },
    // 获取订阅标签
    getLabels(type = null, label_id = null, special_id = null) {
      if (!this.isLabelGatherPage) {
        this.moduleContents = this.futContents = this.preContents = []
        this.searchForm.pageFrom = this.prePageFrom = this.futPageFrom = this.modulePageFrom = 1
        this.isAll = false
        this.noMore = false
      }
      let subsType = this.$route.query.subsType * 1 || 0
      if (subsType !== 1 && subsType !== 2) {
        subsType = 0
      }
      this.setModuleCount({})
      return getSubsLabels({
        subscribeType: type
      }).then(async (res) => {
        this.is_loaded = false
        if (res && res.length > 0) {
          res.forEach(item => {
            item.isShow = false
          })
        }
        // 标签订阅列表
        if (type === 1) {
          this.labels = res || []
          let ids = res.map(item => item.label_id)
          this.label_ids = ids
          // 聚合页面，直接获取内容
          if (this.isLabelGatherPage) {
            if (!this.isSubsCl) {
              if (this.$route.query.label_id) this.searchForm.filters['label_system_ids'] = ['=', this.$route.query.label_id]
              this.subsType = 1
              if (!this.labelName) {
                let d = await this.getLabelSubscribeDetailInfo(this.$route.query.label_id, 1)
                this.labelName = d.label_name
              }
              this.labelDetail.subscribe_type = 1
              this.getAllContents()
            }
            return res
          }
          if (ids?.length > 0) {
            // 如果url带的label_id参数在标签列表中，直接选中标签且获取标签详情，赋值标签参数
            if (label_id && ids.includes(label_id)) {
              this.searchForm.label_id = label_id
              this.labelName = this.labels[ids.indexOf(label_id)].label_name
              this.getLabelDetail({
                subscribe_type: 1,
                label_id
              })
              this.searchForm.filters['label_system_ids'] = ['=', label_id]
            } else if (label_id) {
              // 如果不在列表中，判断标签是否失效，如果失效选中全部，否则跳转至标签聚合页
              let labelInfo = await this.getLabelSubscribeDetailInfo(label_id, 1)
              if (labelInfo) {
                window.location.href = process.env.NODE_ENV === 'production' ? `https://portal.learn.woa.com/training/label-subs?isLabelGatherPage=true&label_id=${label_id}` : `https://test-portal-learn.woa.com/training/label-subs?isLabelGatherPage=true&label_id=${label_id}`
                return
              } else {
                this.searchForm.filters['label_system_ids'] = ['in', ...ids]
              }
            } else {
              this.searchForm.filters['label_system_ids'] = ['in', ...ids]
            }
          }
          // 获取专列订阅列表
          this.getLabels(2, null, special_id)
          return res
        } else { // 专区订阅列表
          this.specials = res || []
          let ids = res.map(item => item.label_id)
          this.special_ids = ids
          // 如果专区列表有内容并且没有选中任何标签
          if (ids?.length > 0 && (this.searchForm.filters?.label_system_ids?.[0] === 'in' || !this.searchForm.filters?.label_system_ids)) {
            // 如果url带的special_id参数在专区列表中
            if (special_id && ids.includes(special_id)) {
              delete this.searchForm.filters['label_system_ids']
              this.searchForm.label_id = special_id
              this.searchForm.filters['page_list.page_id'] = ['=', special_id]
              this.labelDetail = this.specials.filter(item => item.label_id === special_id)[0] || {}
              this.labelName = this.labelDetail.label_name
              this.labelDetail.subscribe_type = 2
              for (let i = 0; i < this.newLabels.length; i++) {
                let item = this.newLabels[i]
                if (item.label_id === this.searchForm.label_id && item.subscribe_type === 2) {
                  this.labelIndex = i
                  break
                }
              }
            } else if (special_id) {
              // 如果专区id不在列表中，判断是否失效，失效选中全部，否则跳转至专区详情页
              let specialInfo = await this.getLabelSubscribeDetailInfo(special_id, 2)
              if (specialInfo) {
                window.location.href = process.env.NODE_ENV === 'production' ? `https://portal.learn.woa.com/user/special?page_id=${special_id}` : `https://test-portal-learn.woa.com/user/special?page_id=${special_id}`
                return
              } else {
                this.searchForm.filters['page_list.page_id'] = ['in', ...ids]
              }
            } else {
              this.searchForm.filters['page_list.page_id'] = ['in', ...ids]
            }
          } else {
            if (this.searchForm.filters?.label_system_ids?.[0] === '=' && this.searchForm.filters?.label_system_ids?.[1]) {
              for (let i = 0; i < this.newLabels.length; i++) {
                let item = this.newLabels[i]
                if (item.label_id === this.searchForm.label_id && item.subscribe_type === 1) {
                  this.labelIndex = i
                  break
                }
              }
            }
          }
          if (this.isSubsLen) {
            this.getAllContents()
          }
          // 没有订阅对应的类型数据时，弹出订阅管理弹窗
          if (!this.isPop) {
            let t = 0
            if ([...this.labels, ...this.specials].length < 1 && subsType * 1 === 0) t = 1
            else if (this.labels.length < 1 && subsType * 1 === 1) t = 1
            else if (this.specials.length < 1 && subsType * 1 === 2) t = 3
            if (!t) return
            this.radioType = t
            this.dialogVisible = true
            this.isPop = true
          }
        }
        return res
      }).catch(err => {
        this.is_loaded = false
        console.log(err)
      })
    },
    async getAllContents(flag = true) {
      if (flag) {
        this.getCounts()
      }
      await this.getFutureContent()
      this.setLabelContentLIstHeight()
    },
    // 赋值module数量
    setModuleCount(obj) {
      let count = 0
      this.labelModules.forEach(item => {
        if (obj[item.module_id]) {
          item.count = obj[item.module_id]
          count += item.count
        } else {
          item.count = 0
        }
      })
      if (this.searchForm.module_id) {
        this.total = obj[this.searchForm.module_id] || 0
      } else {
        this.total = count
      }
      this.labelModules[0].count = count
    },
    // 获取标签详情
    getLabelDetail(item, index = null, flag = false, event = null) {
      if (item.subscribe_type === 1) {
        getLabelDetail({ label_id: item.label_id }).then(res => {
          this.labelDetail = res || {}
          this.labelDetail.subscribe_type = 1
        })
      } else {
        this.labelDetail = item || {}
        this.labelDetail.subscribe_type = 2
      }
    },
    // 深拷贝
    deepClone(obj) {
      if (obj === null || typeof obj !== 'object') {
        return obj
      }
      if (obj instanceof Date) {
        return new Date(obj.getTime())
      }
      if (obj instanceof Array) {
        return obj.reduce((arr, item, i) => {
          arr[i] = this.deepClone(item)
          return arr
        }, [])
      }
      if (obj instanceof Object) {
        return Object.keys(obj).reduce((newObj, key) => {
          newObj[key] = this.deepClone(obj[key])
          return newObj
        }, {})
      }
    },
    searchParams(obj) {
      if (this.subsType === 2) {
        delete obj.filters['label_system_ids']
        delete obj.filters['sort_time']
      } else if (this.subsType === 1) {
        delete obj.filters['page_list.page_id']
        delete obj.filters['page_list.add_time']
      }
      return obj
    },
    // 获取订阅标签内容
    // 获取已上新内容
    getPreviousContent() {
      if (!this.isSubsLen) {
        this.is_loaded = false
        return
      }
      if (this.isGetPreData) return
      this.isGetPreData = true
      let params = this.deepClone(this.searchForm)
      params.filters.module_id = ['in', 1, 6, 7, 8, 10, 15, 16, 20, 99]
      params.sortOrder = 'desc'
      params = this.searchParams(params)
      return getLabelContents(params).then((res) => {
        this.loading = false
        if (res && res.content) {
          this.preContents = [...this.preContents, ...res.content]
          let count = this.preContents.length
          const latelyList = this.preContents.slice(0, 3)
          localStorage.setItem('homePage_list', JSON.stringify(latelyList))
          if (this.isAll && (!res.content || res.content.length < 12 || (count >= res.total))) {
            this.noMore = true
          }
        } else {
          this.noMore = true
        }
        this.isGetPreData = false
        this.$nextTick(() => {
          this.is_loaded = false
        })
        return res
      }).catch((err) => {
        this.isGetPreData = false
        this.prePageFrom -= 1
        this.loading = false
        this.is_loaded = false
        console.log(err)
      })
    },
    // 获取即将开始的课程
    getFutureContent() {
      if (!this.isSubsLen) {
        this.is_loaded = false
        return
      }
      if (this.isGetFutData) return
      this.isGetFutData = true
      let params = this.deepClone(this.searchForm)
      params.filters.module_id = ['in', 2, 3, 4]
      if (this.searchForm.sortBy === 'origin_data.sort_time') {
        params.sortOrder = 'asc'
      } else {
        params.sortOrder = 'desc'
      }
      params = this.searchParams(params)
      return getLabelContents(params).then((res) => {
        this.loading = false
        if (res && res.content) {
          this.futContents = [...this.futContents, ...res.content]
          if ((!res.content || (res.content.length < 12))) {
            if ((this.searchForm.pageSize - res.content.length) > 0) {
              this.isAll = true
              this.searchForm.pageFrom = this.prePageFrom = 1
              this.getPreviousContent()
            }
          }
        } else {
          this.isAll = true
          this.searchForm.pageFrom = this.prePageFrom = 1
          this.getPreviousContent()
        }
        this.isGetFutData = false
        this.$nextTick(() => {
          this.is_loaded = false
        })
        return res
      }).catch((err) => {
        this.isGetFutData = false
        this.futPageFrom -= 1
        this.loading = false
        this.is_loaded = false
        console.log(err)
      })
    },
    // 获取单个模块内容
    getModuleContent() {
      if (!this.isSubsLen) {
        this.is_loaded = false
        return
      }
      if (this.isGetModuleData) return
      this.isGetModuleData = true
      let params = this.deepClone(this.searchForm)
      if (!params.sortBy) {
        delete params.sortBy
        delete params.sortOrder
      }
      params = this.searchParams(params)
      getLabelContents(params).then(res => {
        this.loading = false
        this.total = res.total 
        this.moduleContents = [...this.moduleContents, ...res.content]
        if (!res.content || res.content.length < 12 || this.moduleContents.length >= res.total) {
          this.noMore = true
        }
        this.isGetModuleData = false
        this.$nextTick(() => {
          this.is_loaded = false
        })
        this.setLabelContentLIstHeight()
        return res
      }).catch(err => {
        console.log(err)
        this.isGetModuleData = false
        this.modulePageFrom -= 1
        this.loading = false
        this.is_loaded = false
      })
    },
    // 重置请求数据
    resetData() {
      this.isAll = false
      this.noMore = false
      this.is_loaded = true
      // 重置页码
      this.searchForm.pageFrom = this.prePageFrom = this.futPageFrom = this.modulePageFrom = 1
      // 清空原有数据内容
      this.preContents = this.futContents = this.moduleContents = []
    },
    // 切换标签
    async changeLabel(item, index) {
      this.searchForm.label_id = !item ? '' : item.label_id
      this.labelName = !item ? '' : item.label_name
      this.labelIndex = !item ? '' : index
      this.resetData()
      delete this.searchForm.filters['sort_time']
      delete this.searchForm.filters['page_list.add_time']
      delete this.searchForm.filters['page_list.page_id']
      delete this.searchForm.filters['label_system_ids']
      this.setModuleCount({})
      if (item) {
        if (item.subscribe_type === 1) {
          this.searchForm.filters['label_system_ids'] = ['=', item.label_id]
          if (this.curTime) this.searchForm.filters['sort_time'] = this.curTime
        } else {
          this.searchForm.filters['page_list.page_id'] = ['=', item.label_id]
          if (this.curTime) this.searchForm.filters['page_list.add_time'] = this.curTime
        }
      } else {
        if (this.label_ids.length > 0 && (this.subsType * 1) !== 2) {
          this.searchForm.filters['label_system_ids'] = ['in', ...this.label_ids]
          if (this.curTime) this.searchForm.filters['sort_time'] = this.curTime
        }
        if (this.special_ids.length > 0 && (this.subsType * 1) !== 1) {
          this.searchForm.filters['page_list.page_id'] = ['in', ...this.special_ids]
          if (this.curTime) this.searchForm.filters['page_list.add_time'] = this.curTime
        }
      }
      // 对应类型如果没有数据时不请求数据
      if (!this.isSubsLen) {
        this.is_loaded = false
        return
      }
      if (this.searchForm.module_id) {
        await this.getModuleContent()
        this.getCounts()
      } else {
        this.getAllContents()
      }
      if (!item) {
        this.labelDetail = {
          subscribe_type: ''
        }
        return
      }
      this.getLabelDetail(item, index, false, null)
    },
    // 切换模块
    changeModule(item) {
      if (this.searchForm.module_id === item.module_id) {
        return
      }
      delete this.searchForm.filters['excellent_status']
      this.checked = false
      this.resetData()
      this.searchForm.module_id = item.module_id
      this.searchForm.sortBy = 'origin_data.sort_time'
      this.searchForm.sortOrder = 'desc'
      this.searchForm.filters.module_id = ['=', item.module_id]
      if ([4, 20, 99].includes(item.module_id)) {
        this.nextCli()
      } else {
        this.prevCli()
      }
      // 对应类型如果没有数据时不请求数据
      if (!this.isSubsLen) {
        this.is_loaded = false
        return
      }
      this.loading = true
      // 切到综合，没有module_id时查所有，否则查单个模块内容
      if (!item.module_id) {
        if (this.isLabelGatherPage) this.searchForm.filters['label_system_ids'] = ['=', this.$route.query.label_id]
        this.getAllContents()
        return
      }
      // 面授课、活动、直播按正序排序
      if ([2, 3, 4].includes(item.module_id)) {
        this.searchForm.sortOrder = 'asc'
      }
      this.getModuleContent()
    },
    // 切换上新时间
    async changeTime(value) {
      if (this.searchForm.filters['sort_time'] === value) {
        return
      }
      this.resetData()
      this.curTime = ''
      delete this.searchForm.filters['sort_time']
      delete this.searchForm.filters['page_list.add_time']
      if (value) {
        if (this.labelDetail.subscribe_type !== 1 && this.special_ids.length > 0) {
          this.searchForm.filters['page_list.add_time'] = value
        }
        if (this.labelDetail.subscribe_type !== 2 && this.label_ids.length > 0) {
          this.searchForm.filters['sort_time'] = value
        }
        this.curTime = value
      }
      // 对应类型如果没有数据时不请求数据
      if (!this.isSubsLen) {
        this.is_loaded = false
        return
      }
      if (this.searchForm.module_id) {
        await this.getModuleContent()
        this.getCounts()
      } else {
        await this.getAllContents()
      }
    },
    async getCounts() {
      let data = await this.getmoduleCount1()
      let data2 = await this.getmoduleCount2()
      if (!data) {
        data = {
          category: {}
        }
      }
      if (!data2) {
        data2 = {
          category: {}
        }
      }
      let obj = {
        ...data.category.module_id,
        ...data2.category.module_id
      }
      this.setModuleCount(obj)
    },
    getmoduleCount1() {
      if (!this.isSubsLen) {
        this.is_loaded = false
        return
      }
      let params = this.deepClone(this.searchForm)
      let obj = {
        filters: {},
        groupFields: ['module_id'],
        pageFrom: 1,
        pageSize: 1
      }
      obj.filters.module_id = ['in', 1, 6, 7, 8, 10, 15, 16, 20, 99]
      if (params.filters['sort_time']) {
        obj.filters.sort_time = [...params.filters['sort_time']]
      }
      if (params.filters['page_list.add_time']) {
        obj.filters['page_list.add_time'] = [...params.filters['page_list.add_time']]
      }
      if (params.filters['label_system_ids']?.length > 1) obj.filters.label_system_ids = [...params.filters['label_system_ids']]
      if (params.filters['page_list.page_id']?.length > 1) obj.filters['page_list.page_id'] = [...params.filters['page_list.page_id']]
      params = this.searchParams(obj)
      return getLabelContents(params).then((res) => {
        return res
      })
    },
    getmoduleCount2() {
      if (!this.isSubsLen) {
        this.is_loaded = false
        return
      }
      let params = this.deepClone(this.searchForm)
      let obj = {
        filters: {},
        groupFields: ['module_id'],
        pageFrom: 1,
        pageSize: 1
      }
      obj.filters.module_id = ['in', 2, 3, 4]
      if (params.filters['sort_time']) {
        obj.filters.sort_time = [...params.filters['sort_time']]
      }
      if (params.filters['page_list.add_time']) {
        obj.filters['page_list.add_time'] = [...params.filters['page_list.add_time']]
      }
      if (params.filters['label_system_ids']?.length > 1) obj.filters.label_system_ids = [...params.filters['label_system_ids']]
      if (params.filters['page_list.page_id']?.length > 1) obj.filters['page_list.page_id'] = [...params.filters['page_list.page_id']]
      params = this.searchParams(obj)
      return getLabelContents(params).then((res) => {
        return res
      })
    },
    // 切换排序
    changeSort(item) {
      if (this.searchForm.sortBy === item.field) {
        return
      }
      this.resetData()
      let module_id = this.searchForm.module_id
      this.searchForm.sortBy = item.field
      // 面授课、活动按最新排序正序，其他倒序
      if (module_id) {
        if ((module_id === 2 || module_id === 4)) {
          if (this.searchForm.sortBy === 'origin_data.sort_time') {
            this.searchForm.sortOrder = 'asc'
          } else {
            this.searchForm.sortOrder = 'desc,asc'
          }
        } else {
          if (this.searchForm.sortBy === 'origin_data.sort_time') {
            this.searchForm.sortOrder = 'desc'
          } else {
            this.searchForm.sortOrder = 'desc,desc'
          }
        }
      }
      // 对应类型如果没有数据时不请求数据
      if (!this.isSubsLen) {
        this.is_loaded = false
        return
      }
      if (!module_id) {
        this.getAllContents(false)
      } else {
        this.getModuleContent()
      }
    },
    // 只看精品
    async changeExcellent(value) {
      this.resetData()
      if (value) {
        this.searchForm.filters['excellent_status'] = ['=', 1]
      } else {
        delete this.searchForm.filters['excellent_status']
      }
      // 对应类型如果没有数据时不请求数据
      if (!this.isSubsLen) {
        this.is_loaded = false
        return
      }
      await this.getModuleContent()
      this.excellentData = false
    },
    // 监听订阅标签弹窗关闭时，重新获取标签列表
    subsLabelClose() {
      this.dialogVisible = false
      if (!this.isSubsCl) this.isSubsCl = true
      this.getSubsList()
    },
    getSubsList() {
      let arr = [this.getSubsItem(1), this.getSubsItem(2)]
      Promise.all(arr).then(res => {
        console.log(res)
        // 重新赋值标签
        let label_ids = res[0].map(item => item.label_id)
        this.labels = res[0]
        this.label_ids = label_ids
        if (this.isLabelGatherPage) return
        // 重新赋值专区
        let special_ids = res[1].map(item => item.label_id)
        this.specials = res[1]
        this.special_ids = special_ids
        if (this.labelDetail.subscribe_type === 1) {
          if (!label_ids.includes(this.labelDetail.label_id)) {
            this.changeLabel('')
          }
        } else if (this.labelDetail.subscribe_type === 2) {
          if (!special_ids.includes(this.labelDetail.label_id)) {
            this.changeLabel('')
          }
        } else {
          this.changeLabel('')
        }
      })
    },
    getSubsItem(type) {
      return getSubsLabels({
        subscribeType: type
      }).then(res => {
        return res
      }).catch(() => {
        return []
      })
    },
    // 取消订阅
    cancelSubs(item, type = null, index = null) {
      subscribeLabel({ label_id: item.label_id, opt_type: type || 2, subscribe_type: item.subscribe_type }).then(res => {
        if (res === true) {
          if (!type) {
            this.$message({
              customClass: 'label--el-message',
              message: `已取消订阅${item.subscribe_type === 1 ? '标签' : '专区'}「${item.label_name}」`,
              iconClass: 'el-icon-warning'
            })
          } else {
            this.$message.success({
              customClass: 'label--el-message',
              message: `成功订阅${item.subscribe_type === 1 ? '标签' : '专区'}「${item.label_name}」`,
              type: 'success'
            })
          }
          if (item.label_id === this.searchForm.label_id && !this.isLabelGatherPage) {
            this.searchForm.label_id = ''
          }
          if (this.isLabelGatherPage && !this.isSubsCl) this.isSubsCl = true
          this.labelIndex = ''
          this.getLabels(1)
        } else {
          this.$message({
            customClass: 'label--el-message label--el-message2',
            message: `系统繁忙，请稍后重试！`,
            type: 'error'
          })
        }
      }).catch(err => {
        console.log('订阅失效~~~~~~~~~~~', err)
        let message = err.message
        if (err.code === -50006) {
          message = '标签已失效'
        }
        if (err.code === -50005) {
          message = '专区已失效'
        }
        this.$message({
          customClass: 'label--el-message label--el-message2',
          message: message,
          type: 'error'
        })
        this.labelIndex = ''
        this.getLabels(1)
      })
    },
    handleScroll(e) {
      const { scrollTop, clientHeight, scrollHeight } = e.target
      if (this.noMore) return
      if (Math.ceil(scrollTop) + clientHeight >= scrollHeight - 10 && !this.loading) {
        if (!this.searchForm.module_id) {
          if (!this.isAll) {
            if (!this.isGetFutData) {
              this.loading = true
              this.futPageFrom += 1
              this.searchForm.pageFrom = this.futPageFrom
              this.getFutureContent()
            }
          } else {
            if (!this.isGetPreData) {
              this.loading = true
              this.prePageFrom += 1
              this.searchForm.pageFrom = this.prePageFrom
              this.getPreviousContent()
            }
          }
        } else {
          if (!this.isGetModuleData) {
            this.loading = true
            this.modulePageFrom += 1
            this.searchForm.pageFrom = this.modulePageFrom
            this.getModuleContent()
          }
        }
      }
    }
  }
}
</script>
<style scoped lang='less'>
@import './style/init.less';
.labelPage {
  display: flex;
  margin: 0 auto;
  background: #fff;
  position: relative;
  width: 1384px;
  min-width: 1384px;
  height: calc(100vh - 80px);
  overflow: hidden;
  margin-top: 17px;
  font-family: "PingFang SC";
  .mySubscribe {
    position: relative;
    width: 248px;
    height: 100%;
    line-height: 24px;
    padding:16px 0 0 24px;
    border-right: 1px solid var(--gray-gray-3, #E7E7E7);;
  }
  .mySubscribe{
    header.subsTitle{
      display: flex;
      align-items: flex-end;
      padding-right: 24px;
      div{
        font-size: 16px;
        font-weight: 600;
      }
      span{
        display: inline-block;
        color: #00000099;
        cursor: pointer;
        font-size: 12px;
        font-weight: 400;
        line-height: 20px;
      }
    }
    .subsType{
      margin: 10px 0;
      color: #777777;
      font-family: "PingFang SC";
      span{
        display: inline-block;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        margin-right: 16px;
        cursor: pointer;
      }
      span.active{
        color: #0052D9;
      }
    }
    .subsData{
      font-size: 16px;
      height: calc(100% - 43px);
      .subsDataTop{
        position: relative;
        min-height: 40px;
        .subsAllsItem{
          position: relative;
          transition: right .4s,z-index .3s;
          padding-right: 24px;
          img{
            width: 20px;
            height: 20px;
          }
        }
        .subsAllsItem>span{
          margin-right: 8px;
        }
        .searchMySubs{
          top: 0;
          left: -300px;
          position: absolute;
          margin-bottom: 18px;
          transition: left .4s;
          padding-right: 24px;
          /deep/ .el-input{
              input{
                  padding-left: 28px;
              }
          }
          span.clearWord {
            cursor: pointer;
            position: absolute;
            right: 72px;
            i {
              color: #999999;
            }
          }
          >span.cancel{
              color: #00000099;
              font-size: 12px;
              line-height: 20px;
              margin-left: 16px;
              cursor: pointer;
          }
        }
      }
      .subsDataTop .subsAllsItem>span,.subsList>li{
          height: 36px;
          line-height: 36px;
          padding-left: 18px;
          cursor: pointer;
          font-size: 14px;
          color: #00000099;
          .labelDetailBox::before{
            left: 45px;
          }
          .label-items-label{
            text-overflow:ellipsis;
            white-space: nowrap;
            overflow: hidden;
            >img{
              width: 20px;
              height: 20px;
              margin-right: 8px;
            }
            >span{
              font-weight: 400;
              line-height: 22px;
              text-overflow:ellipsis;
              white-space: nowrap;
              overflow: hidden;
            }
          }
      }
      .subsDataTop .subsAllsItem>span:hover,.active{
          color: #0052D9 !important;
          background: var(--brand-brand-1-light, #F2F3FF);
      }
      .subsList{
        height: calc(100% - 68px);
        overflow: auto;
        margin-right: 4px;
        padding-right: 12px;
      }
      .subsList>li:hover{
        background: var(--gray-gray-1, #F3F3F3);
      }
      .NoSubsData{
        padding-right: 24px;
        img{
          width: 120px;
          height: auto;
          margin-top:33px;
        }
        p{
          margin: 20px 0 40px;
          color: #00000099;
          font-size: 16px;
        }
        button{
          font-size:16px;
          line-height:24px;
          padding:8px 22px;
          border: none;
          margin: 0;
          font-weight: 400;
          margin-bottom: 16px;
          background: #0052D9;
        }
      }
    }
  }
  .labelContent {
    height: 100%;
    flex: 1;
    overflow: hidden;
    .moduleData{
      height: 56px;
      overflow: hidden;
      padding: 0 24px;
      border-bottom: 1px solid var(--gray-gray-3, #E7E7E7);
      flex-wrap: nowrap;
      flex: 1;
      align-items: center;
      img{
        width: 18px;
        height: 18px;
        cursor: pointer;
      }
      .prev{
        margin-right: 10px;
      }
      .next{
        margin-left: 10px;
      }
    }
    .moduleList{
      width: calc(100% - 48px);
      overflow: hidden;
      flex-wrap: nowrap;
      flex: 1;
      // scroll-behavior: smooth;
      li{
        margin: 12px 0;
        padding: 5px 8px;
        color: #000000e6;
        cursor: pointer;
        margin-right: 5px;
        white-space: nowrap;
        span{
          display: inline-block;
          line-height: 22px;
          font-size: 14px;
        }
      }
      li:hover,.active{
        color: #0052D9;
        background: var(--brand-brand-1-light, #F2F3FF);
        border-radius: 3px;
      }
    }
    .contentFilter{
      padding: 20px 24px;
      .filterSection{
        margin-right: 20px;
        line-height: 22px;
        &.filterSort {
          .filterItem{
            margin-right: 12px;
          }
        }
        .filterItem{
          display: inline-block;
          margin-right: 20px;
          line-height: 22px;
          cursor: pointer;
        }
        .filterItem:hover,.active{
          color: #0052d9;
        }
      }
      .excellentCheckbox{
        position: relative;
        .excellentCheckbox{
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 100%;
          z-index: 10;
        }
        .excellentChecked{
          /deep/ .el-checkbox__input.is-focus .el-checkbox__inner{
            border-color: #dcdfe6;
          }
        }
        /deep/ .el-checkbox__label{
          font-weight:400;
          color: #000000e6;
        }
      }
      .contentCount{
        color: #000000e6;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        span {
          font-weight: 600;
          color: #0052d9;
        }
      }
      // >.contentFilter>span{
      //   text-align: right;
      // }
    }
    .infinite-list-wrapper {
      height: calc(100% - 145px);
      // overflow: auto;
    }
    .subsCardListBox{
      height: calc(100% - 175px);
      overflow: auto;
      .content-wraper{
        width: 1131px;
      }
      .contentTitle{
        color: #000000;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        margin-left: 24px;
        margin-bottom: 16px;
      }
      .subsCardList{
        padding: 0 19px 0 24px;
        >div{
          width: 250px;
          margin-right: 29px;
          margin-bottom: 16px;
          border-radius: 8px;
          border: 1px solid #F6F6F6;
          &:nth-child(4n + 0) {
              margin-right: 0;
          }
        }
      }
      .bottm-center {
        padding: 31px 0 100px;
      }
      >p{
        margin: 40px 0 170px;
      }
    }
    .noMoreSubs{
      height: calc(100% - 145px);
      padding-top: 107px;
      overflow: auto;
      p{
        margin: 20px 0 40px;
        color: #00000099;
        font-size: 16px;
      }
      img{
        width: 160px;
        height: auto;
      }
      button{
        border: none;
        font-size:16px;
        font-weight: 400;
        line-height:24px;
        padding:8px 22px;
        & + button{
          margin-left: 40px;
        }
        &.el-button--primary{
          background: #0052D9;
        }
      }
    }
  }
}
/deep/ .el-popover{
    width: 200px !important;
    height: 128px !important;
    border-radius: 6px !important;
}
/deep/ .el-popover--plain{
    padding: 12px !important;
}

</style>
<style>
.el-tooltip__popper.timePopper{
  color: #ffffff;
  font-family: "PingFang SC";
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 22px;
  background: rgba(0, 0, 0, 0.9);
  padding: 4px 8px;
}
</style>
