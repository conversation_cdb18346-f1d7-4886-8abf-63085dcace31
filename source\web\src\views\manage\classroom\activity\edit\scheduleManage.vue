<!-- eslint-disable eqeqeq -->
<template>
  <div class="schedule-manage-card">
    <div class="activity-title">活动名称：<span>{{ form.activity_name }}</span>
      <p></p>
    </div>

    <div class="form-card">
      <el-form :model="form" ref="form" label-width="82px">
        <el-form-item label="通知功能">
          <div class="w-550 mb-10">
            <el-checkbox-group @change="noticeChange($event, 'notice')" v-model="noticeChecks">
              <el-checkbox v-for="item in noticeTypes" :key="item.value" :label="item.value">{{ item.label
              }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
        <el-form-item label="将至提醒">
          <div class="w-550 mb-10">
            <el-checkbox-group @change="noticeChange($event, 'upcoming')" v-model="upcomingReminder">
              <el-checkbox v-for="item in upcomingTypes" :key="item.value" :label="item.value">{{ item.label
              }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </el-form-item>
        <el-form-item label="日程管理">
          <div class="flex-row mb-16">
            <el-button @click="getActivityInfo(1)" type="primary" class="min-btn" size="small">取消日程邀请</el-button>
            <span class="tip ml-12">取消所有此前已发出的日程邀请</span>
          </div>
          <div class="flex-row mb-16">
            <el-button @click="getActivityInfo(2)" type="primary" class="min-btn" size="small">手动发送日程</el-button>
            <span class="tip ml-12">向所有已报名学员，讲师发送/更新日程</span>
          </div>
        </el-form-item>
        <el-form-item label="企微群聊">
          <div v-if="!chatId" class="flex-row">
            <el-button style="padding:8px  26px " @click="getActivityInfo(3)" type="primary" class="min-btn"
              size="small">&nbsp;发起群聊&nbsp;</el-button>
            <span class="tip ml-12">基于目前所有已报名学员名单，发起企微群聊</span>
          </div>
          <div v-else class="group-chat-info">
            <div class="qrcode">
              <!-- :logoScale="0.2" :logoSrc="logoSrc" -->
              <vue-qr v-if="linkUrl" ref="qrCode" :text="linkUrl" :size="92" :margin="0">
              </vue-qr>
            </div>
            <div class="group-info">
              <div class="tip">已创建企微群，点击链接/企微扫码即可进入群聊</div>
              <el-tooltip class="item" effect="dark" :content="'活动名称：' + form.activity_name" placement="top-start">
                <div class="title">活动名称：{{ form.activity_name }}</div>
              </el-tooltip>
              <div class="link-row flex-row">
                <el-link class="a-link" :href="linkUrl" type="primary" target="_blank">{{ linkUrl }}</el-link>
                <el-button class="ml-12 ml_copy" size="mini" @click="handleCopyLink()">复制</el-button>
              </div>
            </div>
          </div>
          <div class="invitation-options flex-row">
            <span class="label">自动邀请学员加入企微群:</span>
            <el-radio-group @change="noticeChange($event, 'radio')" v-model="radio">
              <el-radio :label="true">是</el-radio>
              <el-radio :label="false">否</el-radio>
            </el-radio-group>
            <span class="tip">开启此功能后，将自动邀请报名成功的学员加入群聊</span>
          </div>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import { cancelCalendarInvitationApi, sendManualScheduleApi, createGroupChatApi, seveScheduleApi } from '@/config/classroom.api.conf.js'
import { mapState } from 'vuex'
export default {
  name: 'schedule-manage',
  components: {},
  data() {
    return {
      noticeTypes: [
        { label: '自动发送日程邀约：向报名成功的学员，自动发送日历邀约', value: 1 }
      ],
      upcomingTypes: [
        { label: '邮件提醒', value: 'mail' },
        { label: 'Tips提醒', value: 'tips' },
        { label: '短信提醒', value: 'sms' }
      ],
      activity_name: '',
      noticeChecks: [], // 通知功能
      upcomingReminder: [], // 将至提醒
      logoSrc: require('@/assets/mooc-img/tencent-study.png'),
      linkUrl: '',
      radio: 1, // 自动邀请
      form: {},
      chatId: '', // 群聊ID
      group_welcome_words: '' // 欢迎词
    }
  },
  created() { },
  mounted() {
    this.initData()
  },
  beforeDestroy() { },
  computed: {
    ...mapState({
      $activityInfo: state => state.activity.activityInfo
    }),
    activityId() {
      return this.$route.query.activity_id
    }
  },
  methods: {
    noticeChange(e, type) {
      if (type === 'notice') {
        this.seve('auto_send_calendar', Number(this.noticeChecks.join()))
      } else if (type === 'upcoming') {
        this.seve('notify_channels', this.upcomingReminder.join(';'))
      } else {
        this.seve('auto_join_group', this.radio)
      }
    },
    seve(params, value) {
      seveScheduleApi({ [params]: value, activity_id: this.activityId, group_welcome_words: this.group_welcome_words }).then(res => {
        this.$message.success('保存成功')
        this.$store.commit('activity/SET_ACTIVITY_INFO', {
          [params]: value
        })
      })
    },
    initData() {
      const { notify_channels, auto_send_calendar, auto_join_group, wechat_group_id } = this.$activityInfo
      this.form = this.$activityInfo
      this.upcomingReminder = notify_channels.split(';')
      if (Boolean(auto_send_calendar) === true) {
        this.noticeChecks = [1]
      } else {
        this.noticeChecks = []
      }
      this.radio = auto_join_group
      if (wechat_group_id) this.linkUrl = 'https://nops.woa.com/pigeon/v1/tools/add_chat?chatId=' + wechat_group_id
      this.chatId = wechat_group_id
    },
    getActivityInfo(type) {
      if (!this.activityId) return
      // 1:取消日历邀请  2:手动发送日程 3:发起群聊
      if (type === 1) {
        cancelCalendarInvitationApi({ activity_id: this.activityId }).then(res => {
          this.$message.success('保存成功')
        })
      } else if (type === 2) {
        sendManualScheduleApi({ activity_id: this.activityId }).then(res => {
          this.$message.success('保存成功')
        })
      } else if (type === 3) {
        createGroupChatApi({ activity_id: this.activityId }).then(res => {
          this.$message.success('保存成功')
          this.$store.commit('activity/SET_ACTIVITY_INFO', {
            wechat_group_id: res
          })
          this.linkUrl = 'https://nops.woa.com/pigeon/v1/tools/add_chat?chatId=' + res
          this.chatId = res
        })
      }
    },
    handleCopyLink() {
      var tempInput = document.createElement('input')
      tempInput.value = this.linkUrl
      tempInput.id = 'creatDom'
      document.body.appendChild(tempInput)
      tempInput.select()

      if (document.execCommand('copy')) {
        document.execCommand('copy')
        this.$message.success(this.$langue('Mooc_Common_Alert_CopySucessed', { defaultText: '复制成功' }))
        let creatDom = document.getElementById('creatDom')
        creatDom.parentNode.removeChild(creatDom)
      } else {
        console.error('复制失败')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.schedule-manage-card {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow-y: auto;
  padding-bottom: 20px;
  .activity-title {
    padding: 32px 15px 20px 28px;
    color: #00000099;
    font-size: 14px;
    line-height: 22px;
    border-bottom: 1px solid #eee;

    span {
      color: #000000e6;
    }
  }

  .form-card {
    padding-top: 24px;

    .flex-row>.tip {
      color: #00000099;
    }

    .el-button--primary {
      color: #0052D9;
      background-color: #ECF2FE;
      border: none;
      padding: 8px 16px;
      font-size: 14px;
      font-family: "PingFang SC";
    }
    :deep(.el-form-item__label) {
      font-weight: 500;
    }
  }

  .group-chat-info {
    width: 669px;
    display: flex;
    padding: 12px;
    border-radius: 4px;
    background-color: #F9F9F9;

    .qrcode {
      width: 108px;
      height: 108px;
      margin-right: 12px;
      background: #fff;
      border-radius: 6px;
      padding: 8px;
      flex-shrink: 0;
    }

    .group-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;

      .tip {
        color: #00000099;
        font-size: 14px;
        line-height: 22px;
      }

      .title {
        width: 95%;
        color: #000;
        font-size: 14px;
        line-height: 22px;
        font-weight: bold;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .link-row {
        position: relative;
        font-size: 14px;
        margin-top: auto;

        .a-link {
          min-width: 50%;
          max-width: 80%;
          line-height: 1;

          // text-decoration: revert;
          // color: rgb(64, 158, 255);

        }
      }
    }
  }

  .a-link /deep/ .el-link--inner {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-decoration: underline;
    padding-bottom: 4px;
  }

  .invitation-options {
    line-height: 32px;
    margin-top: 10px;

    .label {
      margin-right: 20px;
    }

    .tip {
      margin-left: 20px;
    }
  }

  .el-form {
    padding-left: 14px;
  }

  .flex-row {
    display: flex;
    align-items: center;
  }

  .min-btn {
    min-width: 108px;
  }

  .w-550 {
    width: 550px;
  }

  .ml_copy {
    position: absolute;
    right: 0px;
    bottom: 0px;
    padding: 9px 16px;
    font-size: 14px;
  }

  .ml-12 {
    margin-left: 12px;
  }

  .mr-12 {
    margin-right: 12px;
  }

  .mb-10 {
    margin-bottom: 10px;
  }

  .mb-16 {
    margin-bottom: 16px;
  }

  .el-form-item {
    margin-bottom: 0px;

    .el-form-item__label {
      font-weight: 500;
    }
  }
}
</style>
