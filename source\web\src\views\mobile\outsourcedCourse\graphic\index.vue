<template>
  <div class="article-detail" :dt-areaid="dtOutCoursePage('area')" :dt-remark="dtOutCoursePage('remark')">
    <!-- 文章内容区域 -->
    <div class="article-head">
      <div class="title overflow-l2">
        <span class="tag" v-if="articleInfo.recourse_from !== 'sanjieke'">文章</span>
        <!-- <img class="label-icon" src="../../../../assets/img/mobile/geekBang/label.png" /> -->
        <img class="label-icon" :src="typeImg" />
        {{ articleInfo.course_title }}
      </div>
      <div class="user-date">
        <span class="user">{{ articleInfo.authorInfo }}</span>
        <span class="date">{{ articleInfo.created_at }}</span>
        <!-- <div class="view">
          <img class="icon" src="../../../../assets/img/mobile/geekBang/icon-watch.png" />
          <span class="num">{{ articleInfo.view_count || 0 }}</span>
        </div> -->
      </div>
      <!-- 功能栏 -->
      <div class="count" v-if="isIndependentCourse">
        <!-- 阅读量 -->
        <div class="praise">
          <img class="icon" src="../../../../assets/img/mobile/geekBang/icon-watch.png" />
          <span class="num">({{ summaryData.view_count || 0 }})</span>
        </div>
        <!-- 点赞 -->
        <div class="praise" :class="[isLike ? 'active-item' : '' ]" @click="handleLike">
          <img v-if="isLike" class="icon" src="../../../../assets/img/mobile/geekBang/praise-active.png" />
          <img v-else class="icon" src="../../../../assets/img/mobile/geekBang/praise.png" />
          <span class="num">({{ summaryData.praise_count || 0 }})</span>
        </div>
        <!-- 收藏 -->
        <div class="praise" :class="[ isCollect ? 'active-item' : '' ]" @click="handleFavorited">
          <img v-if="isCollect" class="icon" src="../../../../assets/img/mobile/geekBang/cellect-active.png" />
          <img v-else class="icon" src="../../../../assets/img/mobile/geekBang/cellect.png" />
          <span class="num">({{ summaryData.fav_count || 0 }})</span>
        </div>
      </div>
      <!-- 标签 -->
      <div class="label-list" v-if="articleInfo.labels.length > 0">
        <label-component :label-list="articleInfo.labels" max-line="1"></label-component>
      </div>
    </div>

    <!-- 文章内容 v-if="isShowIframe" - 嵌入的h5页面 -->
    <iframe v-if="isShowIframe" class="article-info" id="articleDetail" :src="iframeSrc" frameborder="0" scrolling="no" allowfullscreen></iframe>

    <!-- 暂无权限 -->
    <div class="trial-study-pop" v-if="isHarvard && !isPurchased">
      <div class="warm">提示:暂无权限浏览当前内容</div>
      <div class="title">如兑换解锁“哈佛精品文库”账号后，即可学习500+门更多好文</div>
      <van-button class="btn" @click="openActivity">暂未开启学习权限，点此去领取权限</van-button>
      <!-- <div class="coupon-content">
        <span class="use-num">需使用<span style="font-weight:500;">1</span> 张学霸卡-哈佛管理导师专用卡</span>
      </div>
      <div class="my-coupon">我的兑换券：0张</div>
      <div class="link" @click="linkTo">
        <img class="icon" src="../../../../assets/img/mobile/geekBang/help_circle.png" alt="" />
        <span>想要赢取更多券？点击查看指引</span>
      </div> -->
    </div>
    <!-- 阅读完毕分享 -->
    <div class="trial-study-pop" v-if="shareAuth && isQuantity">
      <div class="warm">文章不错，我要分享!</div>
      <div class="title">分享给同事，即可帮他开通与您同等的权限，一起学习，共同探讨。</div>
      <van-button class="btn" @click="handlerShareFixed">复制链接，转发分享</van-button>
      <van-button class="btn btn-more" @click="openActivity">更多好文</van-button>
    </div>
    <!-- 评论 -->
    <div class="comment-wrap" v-if="isShowIframe">
      <sdc-comment-mob v-if="isIndependentCourse && commentParams" :params="commentParams"></sdc-comment-mob>
    </div>
    <div class="share-fixed more-fixed" @click="openActivity" v-if="shareAuth && isQuantity">
      <img src="@/assets/outsourcedCourse/more-icon.png" alt="">
      <span class="share-text">更多 <br /> 好文</span>
    </div>
    <div class="share-fixed" v-if="shareAuth && isQuantity" @click="handlerShareFixed">
      <img src="@/assets/outsourcedCourse/share-hover.png" alt="">
      <span class="share-text">分享 <br /> 阅读</span>
      <div class="share-f-tips" :style="isShowFixed ? 'display: block' : 'display: none' ">
        邀请同事共读好文，为TA解锁价值1200元的“哈佛精品文库”
        <span class="tips-close" @click.stop="closeShareFixed"><i class="el-icon-close"></i></span>
      </div>
    </div>
    <div class="share-model" v-show="isShowHint && isShowFixedShear">
      <div class="model-content">
        <img src="@/assets/img/mobile/geekBang/share-right.png" alt="">
        <div class="model-footer">
          <span class="model-btn" @click="coloseShaerHint">知道了</span>
        </div>
      </div>
    </div>
    <!-- 复制链接 -->
    <harvardShare :xueBaCardConfig="harvardInfo" :isShowGive.sync="isShowGive" :course_title="harvardShareTitle" giveType="account" :course_id="courseId" :isGraphic="true"></harvardShare>
    <pick-up v-model="showPick" :isQuantity="isQuantity" @handlerReceive="handlerReceive" :isGraphic="true"></pick-up>
  </div>
</template>
<script>
import { throttle, pageExposure } from '@/utils/tools.js'
import labelComponent from './components/labelComponent'
// import giveCardMobile from '../activePage/giveCardMobile.vue'
import harvardShare from '../activePage/harvardShareMobile.vue'
import pickUp from '../activePage/pickUp.vue'

import {
  getGeekCourseDetail,
  geekStudyRecord,
  getSummaryData,
  checkPraised,
  addPraise,
  deletePraise,
  checkFavorited,
  addFavorited,
  deleteFavorite,
  getCoursePurchaseInfo,
  getSubscription,
  insertSubscription,
  moocEnroll,
  checkShareAuth,
  getAcctinfos,
  getHarvardActivityInfo,
  getOutsourceLinkConfig,
  activityPresent
  // getRequestRecord
} from 'config/mooc.api.conf.js'
import MoocJs from 'sdc-moocjs-integrator'
import { Toast } from 'vant'
import { detailLogo } from '@/utils/outsourcedCourseMap.js'

export default {
  name: 'articleDetails',
  components: {
    labelComponent,
    // giveCardMobile,
    harvardShare,
    pickUp
  },
  data() {
    return {
      record: false,
      consumePoint: 0,
      shareAuth: false,
      harvardInfo: {},
      isShowFixed: false,
      isShowGive: false,
      showPick: false,
      popTextState: 1, // 兑换时弹窗的状态 0：提示：当前章节试学完毕 1：兑换课程 2：兑换名额不足 3：兑换券不足 4：试学额度已用完
      showRedemptionPopup: false, // 是否显示兑换弹窗
      guideHref: '', // 指引链接跳转
      act_type: 102,
      articleInfo: {
        course_title: '',
        authorInfo: '',
        creator_at: '',
        labels: []
      },
      iframeEl: null,
      iframeSrc: '',
      iframeHeight: 0,
      viewTimer: null, // 上报定时器
      recordId: null, // 上报记录id
      isPageHidden: false, // 是否熄屏
      summaryData: {},
      commentParams: null, // 评论组件配置
      isLike: false, // 是否点赞
      isCollect: false, // 是否收藏
      isArticleLoading: true, // 文章加载中
      coursePurchaseInfo: {}, // 用户课程购买信息 (极客)
      subscriptionInfo: {}, // 订阅补货通知状态
      errNum: 0, // 上报错误次数
      cardList: [],
      linkConfig: {},
      isShowFixedShear: false
    }
  },
  computed: {
    // 是否哈商
    isHarvard() {
      return ['harvard'].includes(this.articleInfo?.recourse_from)
    },
    isShowHint() {
      return this.shareAuth && this.isQuantity
    },
    // 是否购买
    isPurchased() {
      return this.articleInfo?.purchased
    },
    urlText() {
      let userInfo = this.$store.state.userInfo
      let url =
        process.env.NODE_ENV === 'production'
          ? 'https://portal.learn.woa.com/training/outsourcedCourse/graphic/play'
          : `https://test-portal-learn.woa.com/training/outsourcedCourse/graphic/play`
      return `${url}?course_id=${this.courseId}&staffName=${userInfo.staff_name}`
    },
    harvardShareTitle() {
      const { course_title = '' } = this.articleInfo
      if (course_title.length > 12) {
        return course_title.slice(0, 12) + '...'
      }
      return course_title
    },
    courseId() {
      // 0WLyFSwH
      return this.$route.query?.course_id || ''
    },
    mooc_course_id() {
      return this.articleInfo.recourse_parent_mooc_id || '-1'
    },
    shareStaffId() {
      return this.$route.query.share_staff_id || ''
    },
    shareStaffName() {
      return this.$route.query.share_staff_name || ''
    },
    isIndependentCourse() {
      return this.articleInfo.independent_course === 1
    },
    isQuantity() {
      const { quantity = 0 } = this.harvardInfo
      return quantity > 0
    },
    // 是否显示
    isShowIframe() {
      return !this.isHarvard || (this.isHarvard && this.isPurchased)
    },
    isPreview() {
      // 是否是试读模式
      const { preview = false } = this.$route.query
      try {
        if (preview) {
          return preview && JSON.parse(preview)
        }
        return false
      } catch (error) {
        return false
      }
    },
    isSubscription() {
      // 是否已经订阅补货通知
      return this.subscriptionInfo && this.subscriptionInfo.sub_status === 1
    },
    popTextMap() {
      // 兑换弹窗情况汇总
      return {
        0: {
          warm: '提示：当前章节试学完毕',
          title: `如果学习课程中更多章节，请“兑换课程”后继续学习`,
          btn: '兑换课程',
          code: 0
        },
        1: {
          warm: '兑换课程',
          title: `当前课程可任选${this.coursePurchaseInfo.allow_preview_num}个章节试学，你已试学章节数量：${this.coursePurchaseInfo.previewed_num}个，如需学习课程所有章节内容，请兑换课程后学习`,
          btn: '确认兑换',
          code: 1
        },
        2: {
          warm: '兑换名额不足',
          title: `当前课程的可兑换名额为0，暂不支持兑换，运营团队将会定期补充兑换库存，敬请关注`,
          btn: this.isSubscription ? '已订阅补货通知' : '订阅补货通知',
          code: 2
        },
        3: {
          warm: '兑换券不足',
          title: `抱歉，你的兑换券数量不足，无法兑换此课程`,
          btn: '如何赢取更多券？点击查看指引',
          code: 3
        },
        4: {
          warm: '提示：试学额度已用完',
          title: `当前课程可任选${this.coursePurchaseInfo.allow_preview_num}个章节试学，你已试学章节数量：${this.coursePurchaseInfo.previewed_num}个，如需学习课程所有章节内容，请兑换课程后学习`,
          btn: '兑换课程',
          code: 4
        }
      }
    },
    currentPopInfo() {
      return this.popTextMap[this.popTextState]
    },
    typeImg() {
      return (
        detailLogo[this.articleInfo.recourse_from] || detailLogo['geekBang']
      )
    },
    windowHeight() {
      return window.innerHeight + 46
    },
    // 页面曝光
    dtOutCoursePage() {
      return (type) => {
        const { course_title } = this.articleInfo
        if (type === 'area') {
          return `area_${this.courseId}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: course_title,
            page_type: '外部课程详情页',
            container: course_title,
            act_type: '102',
            content_type: '文章',
            terminal: 'H5'
          })
        } else {
          return ``
        }
      }
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          const hostUrl =
            process.env.NODE_ENV === 'development'
              ? process.env.VUE_APP_PORTAL_HOST_WOA
              : window.origin
          this.commentParams = {
            userName: val.staff_name,
            actId: this.courseId,
            appId: 'A9BiosXihR0h46ThNsAX',
            orderType: 102,
            urlConfig: {
              getComments: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/${this.mooc_course_id}/comment-page`,
              addComment: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/add-comment`,
              deleteComment: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/delete-comment/`,
              like: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/praised-comment`,
              sticky: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/sticky-comment`,
              show: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/show-comment`
            }
          }
        }
      },
      immediate: true
    }
  },
  created() {
    // 首次进入显示分享文字
    if (localStorage.getItem('graphic_share_tips') !== 'true') {
      this.isShowFixed = true
    }
    MoocJs.messageListener((res) => {
      if (res.events === 'tencent-mooc-lang') {
        localStorage.setItem('sdc-sys-def-lang', res.params)
        this.getLangJS()
      }
    })

    // 添加message事件监听
    window.addEventListener('visibilitychange', this.visibilitychange, false)
    window.addEventListener('beforeunload', this.beforeunloadEvent, false)
    this.getGeekCourseInfo()
  },
  mounted() {
    // 监听页面滚动
    let iframeDom = document.querySelector('#articleDetail')
    let titleDom = document.querySelector('.article-head')
    let height = titleDom ? titleDom.clientHeight : 0
    let instance = 22 // 误差容错
    window &&
      window.addEventListener(
        'scroll',
        throttle(() => {
          let scrollTop = window.pageYOffset - height - instance
          iframeDom &&
            iframeDom.contentWindow.postMessage(`scroll:top:${scrollTop}`, '*')
        }, 0)
      )
  },
  beforeDestroy() {
    window.removeEventListener('visibilitychange', this.visibilitychange)
    this.clearViewTimer()
  },
  methods: {
    openActivity() {
      const { activity_id } = this.harvardInfo
      // let url = process.env.NODE_ENV === 'production' ? `https://sdc.qq.com/s/PTUrrm?scheme_type=harvard&activityId=${activity_id}` : `https://test-learn.woa.com/training/mobile/activePage/xueba/harvard?activityId=${activity_id}&scheme_type=harvard`
      let url = `/pages/webview/active/index?scheme_type=harvard&activityId=${activity_id}`
      window.wx.miniProgram.navigateTo({ url })
    },
    getLink() {
      getOutsourceLinkConfig({
        resourceConfig: this.articleInfo.recourse_from
      }).then((res) => {
        this.linkConfig = res
      })
    },
    // 哈佛商学院图片封面高度计算
    calcHarvardHeight() {
      this.$nextTick(() => {
        let iframeDom = document.querySelector('#articleDetail')
        let articleHeadDom = document.querySelector('.article-head')
        let screenHeight = window.innerHeight || 0
        if (iframeDom && screenHeight && articleHeadDom) {
          let coverHeight = screenHeight - articleHeadDom.offsetHeight
          iframeDom.contentWindow.postMessage({ message: { 'cover:height': coverHeight } }, '*')
        }
      })
    },
    doCopy() {
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      input.value = this.urlText
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      Toast({
        message: '复制成功',
        icon: 'passed'
      })
      // 复制后移除输入框
      input.remove()
    },
    // 分享权限
    async checkShareAuth() {
      const result = await checkShareAuth({
        type: this.articleInfo.recourse_from
      })
      this.shareAuth = result
      // 给小程序发送分享信息
      const loginUser = JSON.parse(sessionStorage.getItem('login_user'))
      console.log(loginUser, 'loginUser')
      // 如果有权限增加分享人的stff
      let path = this.shareAuth ? `pages/webview/outsourced/iframe/index?resource_type=graphic&course_id=${this.courseId}&staff_id=${loginUser.staff_id}&staff_name=${loginUser.staff_name}` : `pages/webview/outsourced/iframe/index?resource_type=graphic&course_id=${this.courseId}`
      window.wx.miniProgram.postMessage({
        data: {
          type: 'share',
          config: {
            title: this.articleInfo.course_title,
            imageUrl: this.articleInfo.course_cover,
            path: path
          }
        }
      })
      console.log(path, this.articleInfo.course_cover, '分享人pathpathpath')
    },
    // 提交需求
    applyperm() {
      // if (this.record) return
      // let params = {
      //   recourse_from: this.articleInfo.recourse_from,
      //   course_id: this.courseId
      // }
      // getRequestRecord(params).then((res) => {
      //   Toast({
      //     message: '提交成功',
      //     icon: 'passed'
      //   })
      //   this.record = true
      // })
    },
    // 课程报名
    joinCourse() {
      const params = {
        mooc_course_id: this.mooc_course_id,
        join_type: '3'
      }
      moocEnroll(params).then((res) => {
        this.showRedemptionPopup = false
        Toast(`兑换课程成功`)
        // 刷新页面 重新获取数据
        this.getGeekCourseInfo()
      })
    },
    // 订阅补货通知
    subscribeNotice() {
      if (this.isSubscription) {
        // 已经订阅补货通知的话 点击按钮 return
        return
      }
      // 发请求通知逻辑
      insertSubscription({
        recourse_from: this.articleInfo.recourse_from
      }).then((res) => {
        this.showRedemptionPopup = false
        Toast(`订阅补货成功`)
        getSubscription({ recourse_from: this.articleInfo.recourse_from }).then(
          (res) => {
            this.subscriptionInfo = res || {}
          }
        )
      })
    },
    // 指引链接跳转
    linkTo(code) {
      window.open(this.linkConfig.rule_page_link)
    },
    async getAcctinfos() {
      let params = {
        acct_type_codes: this.articleInfo.recourse_from + 'Trans'
      }
      const result = await getAcctinfos(params)
      console.log(result, '查看有没有余额有余额解锁赠送劝学卡')
      //   查看有没有余额有余额解锁赠送劝学卡
      this.consumePoint = Number(result.consume_point)
    },
    handlerReceive() {
      this.activityPresent()
    },
    // 哈商活动信息
    getHarvardActivityInfo() {
      getHarvardActivityInfo().then((res) => {
        console.log(res, '哈商活动信息')
        this.harvardInfo = res
        // 有赠送人并且课程没有购买的时候调用领券 this.$route.query.staff_id && this.$route.query.staff_name && 
        if (!this.isPurchased) {
          this.showPick = true
        }
      })
    },
    // 获取他人赠送的卡券
    activityPresent() {
      let params = {
        from: this.$route.query.staff_id || 0,
        from_name: this.$route.query.staff_name || '腾讯学堂',
        acct_type_code: this.harvardInfo.acct_type_code,
        to_batch: [this.$store.state.userInfo.staff_id],
        object_id: this.harvardInfo.activity_id,
        object_name: this.harvardInfo.activity_name,
        object_type: 'XuebaActivity',
        object_type_name: '活动',
        trans_amt: '1',
        notify_type: 0 // 手动赠送1 ，用户进入页面自动领的 0
      }
      console.log(params, '领卡参数')
      activityPresent(params).then((res) => {
        if (res.success_count) {
          Toast({
            message: '成功解锁权限',
            icon: 'passed'
          })
          this.showPick = false
        } else {
          Toast({
            message: '活动太火爆了，请稍后再试！',
            icon: 'error'
          })
        }
        this.checkShareAuth()
        // 延时调用，避免Toast不显示
        setTimeout(() => {
          this.getGeekCourseInfo()
        }, 800)
        console.log(res, '赠送接口')
      }).catch(() => {
        Toast({
          message: '活动太火爆了，请稍后再试！'
        })
      })
    },
    // 获取课程详情
    getGeekCourseInfo() {
      if (window.top === window.self) {
        Toast.loading({ duration: 0, forbidClick: true, message: '加载中...' })
      }
      window.parent && window.parent.postMessage({ page: 'iframe', loading: true, geekSourceLoading: true }, '*')
      getGeekCourseDetail(this.courseId).then((res) => {
        document.title = res.course_title
        let intro = this.computedStr(res.author_intro)
          ? `(${this.computedStr(res.author_intro)})`
          : ''
        res.authorInfo = `${res.author}${intro}`
        this.articleInfo = res
        if (['harvard'].includes(res.recourse_from) && !res.purchased) {
          Toast.clear()
        }
        this.articleInfo.labels = res.labels || []
        if (this.isIndependentCourse) {
          this.getSummaryInfo()
        }
        // if (this.isPreview) { // 试读时获取其他信息(需要修改)
        //   this.getOtherInfo()
        // }
        // 哈商的调用卡券信息
        if (this.isHarvard) {
          this.getAcctinfos()
          this.getHarvardActivityInfo()
          // 判断有没有权限有权限增加分享信息
          this.checkShareAuth()
          this.getLink()
        }
        let src = (res.recourse_iframe_url || '') + `&pm=parent&scrolltop=true`
        if (this.isPreview) {
          src += '&preview=true'
        }
        this.iframeSrc = src
        // 极客时间 文章加载完成后开始计时
        window.addEventListener('message', this.communication)

        // 详情页曝光上报
        pageExposure({
          page_type: '移动端外购课文章详情页',
          content_type: '网络课',
          act_type: '2',
          content_name: res.course_title,
          content_id: this.courseId,
          terminal: 'H5'
        })
      }).catch((err) => {
        Toast.clear()
        window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
        if ([403, 404, 500].includes(err.code)) {
          MoocJs.sendErrorInfo(err.message)
        }
      })
    },
    // 试读时获取其他信息
    async getOtherInfo() {
      let coursePurchaseInfoApi = getCoursePurchaseInfo(this.mooc_course_id)
      let subscriptionApi = getSubscription({
        recourse_from: this.articleInfo.recourse_from
      })
      let res = await Promise.all([coursePurchaseInfoApi, subscriptionApi])
      this.coursePurchaseInfo = res[0]
      this.subscriptionInfo = res[1] || {}
    },
    communication(event) {
      console.log('communication', event)
      let vendorArray = Object.keys(detailLogo) || []
      let { action, vendor, params, height } = event.data
      vendor === 'sanjieke' && console.log('三节课--课程: ', event.data)
      if ([...vendorArray, 'geekbang'].includes(vendor) && action === 'article:mounted') {
        Toast.clear()
        window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
        let iframeDom = document.querySelector('#articleDetail')
        iframeDom && iframeDom.contentWindow.postMessage(`window:height:${this.windowHeight}`, '*')
        // 浏览页开启时长记录
        this.creatViewTimer()
        // 通知mooc开启学习上报
        MoocJs.play()

        // 哈佛商学院适配封面高度
        this.calcHarvardHeight()
      }
      if (this.isPreview && vendor === 'geekbang' && action === 'article:preview') {
      }
      if ([...vendorArray, 'geekbang'].includes(vendor) && action === 'article:loaded') {
        // 如果有高度 设置iframe高度
        if (params.height) {
          this.iframeHeight = params.height
          this.iframeAutoFit(params.height + 0 + 'px')
          this.isArticleLoading = false
        }
      }
      if ([...vendorArray, 'geekbang'].includes(vendor) && this.iframeHeight > 0) {
        let hVal = params.height ? params.height : height
        if (hVal > this.iframeHeight) {
          this.iframeHeight = hVal
          this.iframeAutoFit(hVal + 0 + 'px')
        }
      }
    },
    // iframe高度自适应
    iframeAutoFit(height) {
      console.log(height, 'heightheight')
      this.$nextTick(() => {
        let iframeObj = document.querySelector('#articleDetail')
        if (iframeObj) {
          iframeObj.style.height = height
        }
      })
    },
    // 初始化监听器
    creatViewTimer() {
      let _this = this

      let durtation = 0
      let totalTime = (this.articleInfo.total_time || 15) * 2.5
      this.viewTimer = setInterval(() => {
        ++durtation
        if (durtation % 15 === 0) {
          _this.handleViewGraphicRecord() // 浏览器时长需每15秒记录一次
        }
        if (durtation >= totalTime) {
          this.clearViewTimer()
        }
      }, 1000)
    },
    // 访问记录上报
    handleViewGraphicRecord() {
      const recordParam = {
        area_id: this.$route.query.area_id || '',
        from: this.$route.query.from || '',
        course_id: this.courseId,
        sharer_id: this.shareStaffId || '',
        sharer_name: this.shareStaffName || '',
        // from_type: 'geekBang',
        from_type: this.articleInfo.recourse_from || 'geekBang',
        record_id: this.recordId || ''
      }
      console.log('文章上报参数recordParam: ', recordParam)
      geekStudyRecord(recordParam)
        .then((data) => {
          // 只统计连续上报的错误次数
          this.errNum = 0
          if (data) {
            this.recordId = data
          }
        })
        .catch(() => {
          // 统计连续上报错误次数
          this.errNum++
          if (this.errNum >= 3) {
            this.clearViewTimer()
          }
        })
    },
    // 清除15s计时器
    clearViewTimer() {
      if (this.viewTimer !== null) {
        clearInterval(this.viewTimer)
        this.recordId = ''
      }
    },
    // 监听熄屏/显示
    visibilitychange() {
      if (document.hidden) {
        this.clearViewTimer()
        this.handleViewGraphicRecord()
        this.isPageHidden = true
      } else {
        this.isPageHidden = false
        if (this.articleInfo.recourse_iframe_url) {
          this.creatViewTimer()
        }
      }
    },
    // 销毁前处理
    beforeunloadEvent() {
      this.handleViewGraphicRecord()
      this.clearViewTimer()
    },
    // 获取点赞，收藏……等统计数据
    getSummaryInfo() {
      getSummaryData({
        act_type: this.act_type,
        course_id: this.courseId
      }).then((res) => {
        this.summaryData = res
        this.getPraisedStatus()
        this.getFavoritedStatus()
      }).catch(() => {
        Toast.clear()
        window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
      })
    },
    // 获取点赞状态
    getPraisedStatus() {
      checkPraised({
        act_type: this.act_type,
        course_id: this.courseId
      }).then((res) => {
        this.isLike = res
      })
    },
    // 点赞/取消点赞
    handleLike() {
      let praiseCommonAPI = null
      let tip = null
      if (this.isLike) {
        praiseCommonAPI = deletePraise
        this.summaryData.praise_count--
        tip = this.$langue('Mooc_Common_Alert_CancelPraiseSucessed', {
          defaultText: '取消点赞成功'
        })
      } else {
        praiseCommonAPI = addPraise
        this.summaryData.praise_count++
        tip = this.$langue('Mooc_Common_Alert_PraiseSucessed', {
          defaultText: '点赞成功'
        })
      }
      praiseCommonAPI({
        act_type: this.act_type,
        course_id: this.courseId
      }).then((res) => {
        if (res.data) {
          Toast(`${tip}`)
          this.isLike = !this.isLike
        }
        this.getPraisedStatus()
      })
    },
    // 获取收藏状态
    getFavoritedStatus() {
      checkFavorited({
        act_type: this.act_type,
        course_id: this.courseId
      }).then((res) => {
        this.isCollect = res
      })
    },
    // 收藏/取消收藏
    handleFavorited() {
      let praiseCommonAPI = null
      let tip = null
      if (this.isCollect) {
        praiseCommonAPI = deleteFavorite
        this.summaryData.fav_count--
        tip = this.$langue('Mooc_Common_Alert_CancelCollectSucessed', {
          defaultText: '取消收藏成功'
        })
      } else {
        praiseCommonAPI = addFavorited
        this.summaryData.fav_count++
        tip = this.$langue('Mooc_Common_Alert_CollectSucessed', {
          defaultText: '收藏成功'
        })
      }
      praiseCommonAPI({
        act_type: this.act_type,
        course_id: this.courseId
      }).then((res) => {
        if (res.data) {
          Toast(`${tip}`)
          this.isCollect = !this.isCollect
        }
        this.getFavoritedStatus()
      })
    },
    computedStr(str) {
      if (!str) return ''
      let len = 0
      let index = 0
      if (str.length <= 6) return str
      for (let i = 0; i < str.length; i++) {
        let c = str.charCodeAt(i)
        // 单字节加1
        if ((c >= 0x0001 && c <= 0x007e) || (c >= 0xff60 && c <= 0xff9f)) {
          len++
        } else {
          len += 2
        }
        if (len >= 12) {
          index = i
          break
        }
      }
      if (str.length > index + 2) {
        return (
          str.substring(0, index + 1) + '...' + str.substring(str.length - 2)
        )
      } else {
        return str
      }
    },
    // 分享阅读
    handlerShareFixed() {
      this.isShowGive = true
    },
    closeShareFixed() {
      this.isShowFixed = false
      localStorage.setItem('graphic_share_tips', true)
    },
    coloseShaerHint() {
      this.isShowFixedShear = false
    },
    // 赠送劝学卡后刷新赠送数量
    handlerGiveXuebaka() {
      this.getAcctinfos()
    }
  }
}
</script>
<style lang="less" scoped>
.article-detail {
  height: 100%;
  background: #fff;
  // display: flex;
  // flex-direction: column;
  .article-head {
    padding: 12px 16px 8px;
    background-color: #fff;
    .title {
      line-height: 24px;
      color: #000000ff;
      font-size: 16px;
      font-weight: 600;
      .tag {
        margin-right: 10px;
        display: inline-block;
        width: 42px;
        height: 20px;
        line-height: 20px;
        border-radius: 2px;
        border: 1px solid #ff8b6cff;
        background: #ff8b6c33;
        color: #ff8b6cff;
        font-size: 14px;
        font-weight: 600;
        text-align: center;
      }
      .label-icon {
        height: 20px;
        margin-right: 8px;
        position: relative;
        top: -2px;
        z-index: 0;
      }
    }
    .user-date {
      margin-top: 6px;
      min-height: 20px;
      line-height: 20px;
      color: #00000099;
      font-size: 12px;
      display: flex;
      align-items: baseline;
      justify-content: space-between;
      .date {
        text-align: right;
        flex-shrink: 0;
        margin-left: 36rpx;
      }
      // &>span {
      //   margin-right: 34px;
      // }
      // .view {
      //   display: flex;
      //   align-items: center;
      //   .icon {
      //     width: 12px;
      //     height: 12px;
      //     float: left;
      //     margin-right: 4px;
      //   }
      // }
    }
    .count {
      height: 16px;
      line-height: 16px;
      font-size: 12px;
      margin-top: 16px;
      .admire,
      .praise,
      .cellect {
        float: left;
        color: #00000066;
      }
      .admire,
      .praise {
        margin-right: 24px;
      }
      .active-item {
        color: #0052d9;
      }
      .admire,
      .praise,
      .cellect {
        .icon {
          display: inline-block;
          width: 16px;
          height: 16px;
          float: left;
          margin-right: 2px;
        }
      }
    }
    .label-list {
      margin-top: 8px;
    }
  }
  .article-info {
    width: 100%;
    height: 0;
    position: relative;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    overflow: hidden;
  }
  // .article-info {
  //   width: 100%;
  //   flex: 1;
  // }
  .comment-wrap {
    background-color: #fff;
  }
  // 文字超出两行省略号
  .overflow-l2 {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden; //溢出内容隐藏
    text-overflow: ellipsis; //文本溢出部分用省略号表示
    display: -webkit-box; //特别显示模式
    -webkit-line-clamp: 2; //行数
    line-clamp: 2;
    -webkit-box-orient: vertical; //盒子中内容竖直排列
  }
  .trial-study-pop {
    margin: 16px;
    // width: 343px;
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-radius: 8px;
    background: #f8fbff;
    display: flex;
    flex-direction: column;
    align-items: center;
    .warm {
      color: #0052d9;
      font-size: 20px;
      font-weight: 600;
      line-height: 28px;
      margin-bottom: 8px;
    }
    .title {
      width: 279px;
      color: #000000e6;
      font-size: 16px;
      line-height: 24px;
      margin-bottom: 16px;
      text-align: center;
    }
    .btn {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 304px;
      height: 48px;
      color: #ffffff;
      font-size: 16px;
      font-weight: 600;
      border-radius: 24px;
      background: linear-gradient(90deg, #488dff 0%, #1766ff 100%);
    }
    .btn-more {
      color: #006dff;
      background: #fff;
      margin-top: 16px;
      border: 1px solid #006dff;
    }
    .coupon-content {
      margin-top: 16px;
      font-size: 14px;
      color: #0052d9;
    }
    .points {
      color: #00000066;
      line-height: 22px;
      text-decoration-line: line-through;
      margin-right: 12px;
    }
    .use-num {
      line-height: 22px;
      margin-right: 20px;
    }
    .extra-num {
      line-height: 22px;
      span {
        font-weight: 500;
      }
    }
    .my-coupon {
      margin-top: 8px;
      color: #0052d9;
      font-size: 14px;
      line-height: 22px;
    }
    .link {
      cursor: pointer;
      margin-top: 16px;
      .icon {
        width: 14px;
        height: 14px;
        margin-right: 5px;
      }
      span {
        color: #0052d9;
        font-size: 14px;
        line-height: 22px;
        text-decoration-line: underline;
      }
    }
  }
  .share-fixed {
    position: fixed;
    z-index: 1000;
    top: 40%;
    right: -1px;
    width: 68px;
    height: 40px;
    border-radius: 24px 0 0 24px;
    background-color: #fff;
    text-align: center;
    display: flex;
    align-items: center;
    border: 0.5px solid #dcdcdc;
    cursor: pointer;
    z-index: 1000;
    img {
      width: 16px;
      height: 16px;
      margin: 0 8px 0 12px;
    }
    .share-text {
      color: #006dff;
      font-family: 'PingFang SC';
      font-size: 10px;
      font-style: normal;
      font-weight: 400;
      line-height: 12px;
    }
    .share-f-tips {
      position: absolute;
      bottom: -80px;
      right: 20px;
      color: #ffffff;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      width: 266px;
      height: 66px;
      padding: 12px 10px;
      text-align: left;
      flex-shrink: 0;
      border-radius: 8px;
      background: var(---Brand7-Hover, #266fe8);
      .tips-close {
        position: absolute;
        right: 8px;
        top: 8px;
        .el-icon-close {
          color: #fff;
          font-weight: 600;
        }
      }
    }
    .share-f-tips::after {
      position: absolute;
      top: -18px;
      right: 15px;
      content: '';
      display: inline-block;
      width: 0;
      height: 0;
      border-top: 10px solid transparent;
      border-right: 10px solid transparent;
      border-left: 10px solid transparent;
      border-bottom: 10px solid #266fe8;
    }
  }
  .more-fixed {
    top: 33%;
  }
  /deep/.van-overlay {
    z-index: 1999 !important;
  }
  .share-model {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* 半透明遮罩层 */
    z-index: 2000; /* 确保遮罩层在其他内容之上 */
    .model-content {
      width: 227px;
      position: absolute;
      right: 5.3%;
      img {
        width: 100%;
        height: 100%;
      }
      .model-footer {
        padding-top: 20px;
        display: flex;
        justify-content: center;
        .model-btn {
          width: 86px;
          height: 34px;
          border-radius: 32px;
          border: 1px solid #ffffffcc;
          background: #474747;
          font-size: 14px;
          font-style: normal;
          font-weight: 500;
          color: #fff;
          line-height: 34px;
          text-align: center;
        }
      }
    }
  }
}
</style>
