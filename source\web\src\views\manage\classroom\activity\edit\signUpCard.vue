<template>
  <div class="sign-up-card activity-common">
    <div class="activity-title">活动名称：<span>{{ $activityInfo.activity_name || '-' }}</span></div>
    <div class="activity-content">
      <div class="qrcode-card card">
        <div class="qr-title">二维码</div>
        <div class="qrcode">
          <vue-qr
            v-if="qrCodeUrl"
            ref="qrCode" 
            :text="qrCodeUrl" 
            :size="84"
            :logoScale="0.2"
            :margin="0" 
          >
          </vue-qr>
        </div>
        <div class="tip-box">企微/微信扫码，即可转发至群聊</div>
      </div>
      <div class="info-card">
        <div class="link-input mb-20">
          <el-input class="w-600" v-model="linkUrl" placeholder="" readonly></el-input>
        </div>
        <div class="toolbar">
          <el-button class="btn-box" type="primary" size="small" @click="handleCopyLink()">复制链接</el-button>
          <el-button class="btn-box default-btn" size="small" @click="handleOpenLink()">打开</el-button>
          <el-button class="btn-box default-btn" size="small" @click="handleClick()" v-if="false">
            <span class="icon-box">
              <img class="btn-icon" src="@/assets/classroomImg/poster.png" alt="" />
            </span>
            二维码海报
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { copyToClipboard } from '@/utils/tools.js'

export default {
  name: 'sign-up-card',
  components: {},
  data() {
    return {
      linkUrl: '',
      qrCodeUrl: ''
    }
  },
  watch: {},
  computed: {
    ...mapState({
      $activityInfo: state => state.activity.activityInfo
    }),
    activityId () {
      return this.$route.query.activity_id
    }
  },
  created() { },
  mounted() {
    this.linkUrl = `https://sdc.qq.com/s/JZH8Eg?scheme_type=activityDetail&activity_id=${this.activityId}&jump_from=mp_qrcode&project=0&source=ql`
    this.qrCodeUrl = `https://sdc.qq.com/s/JZH8Eg?scheme_type=activityDetail&activity_id=${this.activityId}&project=0&source=ql`
  },
  beforeDestroy() { },
  methods: {
    handleCopyLink() {
      copyToClipboard(this.linkUrl)
    },
    handleOpenLink() {
      window.open(this.linkUrl)
    }
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/activity-common.less';

  .sign-up-card {
    width: 100%;
    height: 100%;
    // padding: 0 15px;
    background-color: #fff;
    .activity-title {
      padding: 32px 28px 20px;
      color: #00000099;
      font-size: 14px;
      line-height: 22px;
      border-bottom: 1px solid #EEEEEE;
      & > span {
        color: #000000e6;
      }
    }
    .activity-content {
      padding: 28px 62px 28px 28px;
      display: flex;
      .qrcode-card {
        width: 250px;
        padding: 20px;
        border-radius: 4px;
        background: #F9F9F9;
        flex-shrink: 0;
        margin-right: 28px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        .qr-title {
          width: 80px;
          height: 32px;
          line-height: 30px;
          text-align: center;
          color: #000000e6;
          text-align: center;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          border-radius: 3px;
          border: 1px solid #DCDCDC;
          background: #FFF;
          margin: 0 auto 12px;
          user-select: none;
        }
        .qrcode {
          width: 100px;
          height: 100px;
          padding: 8px;
          margin: 0 auto;
          margin-bottom: 12px;
          border-radius: 4px;
          background: #fff;
          & > img {
            background: #EFEFEF;
          }
        }
        .tip-box {
          color: #0052d9;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
        }
      }
      .info-card {
        flex: 1;
        .toolbar {
          margin-bottom: 16px;
          .btn-box {
            min-width: 80px;
            font-size: 14px;
          }
          .default-btn {
            color: #000000e6;
          }
          .icon-box {
            width: 20px;
            height: 10px;
            margin-right: 6px;
            display: inline-block;
          }
          .btn-icon {
            width: 24px;
            height: 24px;
            position: relative;
            left: -4px;
            top: -7px;
            float: left;
          }
        }
      }
    }

    .w-600 {
      width: 600px;
    }
  }
</style>
