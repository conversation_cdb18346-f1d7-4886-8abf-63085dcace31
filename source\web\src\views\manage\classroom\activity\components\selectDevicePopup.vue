<template>
  <div>
    <div class="select-device-input">
      <el-tag size="small" closable @close="handleClose(tag)" v-for="tag in frameList" :key="tag.meeting_room_id">{{ tag | nameFilter }}</el-tag>
    </div>
    <el-dialog
      :visible.sync="visible"
      custom-class="digital-collection-popup"
      title="关联会议室Rooms设备 (多选)"
      width="850px"
      :lock-scroll="false"
      :close-on-click-modal="false"
      :before-close="closeDialog"
    >
      <div> 
        <div class="search-row">
          <div class="search-input">
            <el-input v-model="searchValue" placeholder="关键词搜索" class="search-input-box"></el-input>
          </div>
          <el-button type="primary" size="small" class="create-btn" @click="onSearch(1)">搜索</el-button>
        </div>
        <div class="content-card">
          <div class="list card">
            <div class="li" :class="['li', { 'active': activeList.includes(item.meeting_room_id)}]" v-for="item in roomsList" :key="item.meeting_room_id" @click="handleClick(item)">{{ item | nameFilter }}</div>
          </div>
          <div class="selected-list card">
            <div class="selected-li" v-for="item in selectedList" :key="item.meeting_room_id">
              <span>{{ item | nameFilter }}</span>
              <span class="other-icon-box" @click="handleDelete(item)"> <span class="right-icon del"></span></span>
            </div>
          </div>
        </div>
        <el-pagination
          v-if="tableData.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="tableData.current"
          :page-sizes="[5, 10, 20, 30, 50, 100]"
          :page-size="size"
          layout="total,  prev, pager, next, sizes, jumper"
          :total="tableData.total"
        >
        </el-pagination>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="closeDialog" size="small">取消</el-button>
        <el-button type="primary" @click="resetting" size="small">重置</el-button>
        <el-button type="primary" @click="complete" size="small">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getRoomsListApi } from '@/config/classroom.api.conf.js'
import pager from '@/mixins/pager'

export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectItems: {
      type: Array,
      default: () => []
    }
  },
  mixins: [pager],
  data() {
    return {
      searchValue: '',
      roomsList: [],
      selectedList: [],
      frameList: [],
      selectedId: ''
    }
  },
  watch: {
    visible(val) {
      if (val) {
        if (this.frameList.length) this.selectedList = [...this.frameList]
        this.onSearch(1)
      }
    },
    selectItems: {
      handler(val) {
        if (Array.isArray(val) && val.length) {
          this.selectedList = [...val]
          this.frameList = [...val]
        }
      },
      immediate: true
    }
  },
  computed: {
    activeList() {
      return this.selectedList.map(v => v.meeting_room_id)
    }
  },
  filters: {
    nameFilter(e) {
      return `${e.meeting_room_name}（${e.meeting_room_location || ''}${e.meeting_room_status === 500 ? '-未放开预定' : ''}）`
    }
  },
  mounted() {
    this.size = 20
  },
  methods: {
    onSearch(current = 1) {
      this.current = current
      this.getRoomsList()
    },
    getRoomsList() {
      const params = {
        page_no: this.current,
        page_size: this.size,
        meeting_room_name: this.searchValue
      }
      getRoomsListApi(params).then(res => {
        this.roomsList = res.device_info_list
        this.tableData.total = res.total_count
      })
    },
    handleClick(e) {
      let curIndex = this.selectedList.findIndex(v => v.meeting_room_id === e.meeting_room_id)
      if (curIndex === -1) {
        this.selectedList.push(e)
      } else {
        this.$message.warning('已选择该选项')
      }
    },
    resetting() {
      this.searchValue = ''
      this.selectedList = []
      this.current = 1
      this.getRoomsList(1)
    },
    handleDelete(e) {
      this.deleteArray('selectedList', 'meeting_room_id', e.meeting_room_id)
    },
    complete() {
      this.frameList = [...this.selectedList]
      this.closeDialog()
      this.$emit('deviceChange', this.frameList)
    },
    // 关闭弹窗
    closeDialog() {
      this.selectedList = []
      this.$emit('update:visible', false)
    },
    async handleClose(e) {
      await this.deleteArray('frameList', 'meeting_room_id', e.meeting_room_id)
      await this.$emit('deviceChange', this.frameList)
    },
    deleteArray(arrName, key, value) {
      if (arrName && Array.isArray(this[arrName]) && key && value) {
        let index = this[arrName].findIndex(v => v[key] === value)
        this[arrName].splice(index, 1)
      }
    }
  }
}
</script>

<style lang="less" scoped>
:deep(.select-device-input) {
  position: relative;
  box-sizing: border-box;
  width: 526px;
  height: 100%;
  line-height: 28px;
  padding: 3px 3px 0 3px;
  min-height: 56px;
  max-height: 200px;
  border: 1px solid #dcdcdc;
  border-radius: 4px;
  overflow-y: auto;
  overflow-x: hidden;
  text-align: left;
  .el-tag {
    margin-right: 5px;
  }
}
:deep(.digital-collection-popup) {
  .el-dialog__body {
    padding: 24px 32px;
  }
  .search-row {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 24px;
    .search-input {
      width: 240px;
      margin-right: 12px;
    }
    .create-btn {
      width: 80px;
    }
  }
  .content-card {
    width: 100%;
    height: 400px;
    display: flex;
    justify-content: space-between;
    .card {
      width: 380px;
      padding: 16px 8px;
      overflow-y: auto;
      border: 1px solid #e5e5e5;
      border-radius: 4px;
      box-sizing: border-box;
      .li {
        padding: 4px 8px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
        user-select: none;
        margin-bottom: 2px;
        &:hover {
          background-color: #006fff0d;
        }
      }
      .active {
        background-color: #006fff0d;
      }
      .selected-li {
        display: flex;
        justify-content: space-between;
        align-items: center;
        line-height: 30px;
        &:hover {
          color: #409eff;
        }
        .other-icon-box {
          flex-shrink: 0;
          width: 24px;
          height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          cursor: pointer;
          &:hover {
            background: rgba(245, 247, 249, 1);
            .del {
              background: url('~@/assets/img/del-active.png') no-repeat center/cover;
            }
          }
          
          .del {
            display: inline-block;
            width: 16px;
            height: 16px;
            background: url('~@/assets/img/del.png') no-repeat center/cover;
          }
        }
      }
    }
  }
  .el-pagination .el-pager {
    height: 24px;
  }
  .el-pagination__sizes .el-select .el-input .el-select__caret {
    line-height: 24px;
  }
}
</style>
