<template>
  <div
    id="amplify-btn-content"
    class="amplify-btn-content"
    :draggable="true"
    @dragstart="dragstart"
    @dragend="dragend"
    @mousedown="mousedown"
    v-if="isFullscreen"
  >
    <div
      class="amplify-btn-draggle"
      :class="{ 'amplify-btn-draggle-after-hidden': hiddenTip }"
    >
      可拖拽移动位置
      <i class="el-icon-close close-btn" @click="hiddenTip = true"></i>
    </div>
    <div
      class="amplify-btn"
      @click="handerAmplify"
      @mouseenter="mouseenter"
      @mouseleave="mouseleave"
    >
      <span class="icon"></span>
      <span>退出全屏</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Drag',
  props: {
    isFullscreen: {
      // 是否全屏
      type: Boolean,
      default: false
    }
  },
  data () {
    return {
      hiddenTip: false, // 是否显示tip提示 鼠标悬停在"退出全屏"按钮3s后隐藏
      timeId: null,
      isInner: false // 点击的是中间的全屏按钮还是外边框 true为中间的全屏按钮 false为外边框
    }
  },
  methods: {
    handerAmplify () {
      this.$emit('handerAmplify')
    },
    mousedown (event) {
      if (event.target.classList.contains('amplify-btn-content')) {
        this.isInner = false
      } else {
        this.isInner = true
      }
    },
    mouseenter () {
      let _this = this
      if (!this.timeId) {
        this.timeId = setTimeout(() => {
          _this.hiddenTip = true
        }, 3000)
      }
    },
    mouseleave () {
      if (this.timeId) {
        clearTimeout(this.timeId)
        this.timeId = null
      }
    },
    dragstart (ev) {
      ev.dataTransfer.setData('id', ev.target.id)
    },
    dragend (event) {
      event.preventDefault()
      let button = document.querySelector('.amplify-btn-content')
      let newX = event.pageX - 20
      let newY = event.pageY - 20
      if (this.isInner) {
        newX = event.pageX - 6
        newY = event.pageY
      }
      if (this.hiddenTip) {
        if (newX < 59) {
          newX = 59
        }
      } else {
        if (newX < 195) {
          newX = 195
        }
      }
      if (newY < 25) {
        newY = 25
      }
      if (newX > window.innerWidth - 55) {
        newX = window.innerWidth - 55
      }
      if (newY > window.innerHeight - 21) {
        newY = window.innerHeight - 21
      }
      button.style.left = newX + 'px'
      button.style.top = newY + 'px'
      button.classList.add('move')
    }
  }
}
</script>

<style scoped lang="less">
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}
.move {
  transform: translate(-50%, -50%);
}
.amplify-btn-content {
  cursor: pointer;
  position: fixed;
  right: 24px;
  bottom: 22px;
  width: 128px;
  height: 55px;
  padding: 5px;
  border-radius: 50px;
  &:hover {
    cursor: move;
  }
  .amplify-btn-draggle {
    position: absolute;
    top: 12px;
    left: -128px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 30px;
    color: #fff;
    padding: 0 5px;
    border-radius: 6px;
    background: var(--text-icon-font-gy-190-primary, #000000e6);
    cursor: default;
    &::after {
      content: '';
      position: absolute;
      right: -10px;
      top: 50%;
      border-left: 5px solid var(--text-icon-font-gy-190-primary, #000000e6);
      border-right: 5px solid transparent;
      border-bottom: 5px solid transparent;
      border-top: 5px solid transparent;
      transform: translateY(-50%);
    }
  }
  .close-btn {
    color: #fff;
    margin-left: 3px;
    cursor: pointer;
  }
  .amplify-btn-draggle-after-hidden {
    display: none;
  }
}
.amplify-btn {
  width: 112px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  color: #000000e6;
  border: 0.5px solid #dcdcdc;
  border-radius: 24px;
  background: var(---White, #fff);
  box-shadow: 0 8px 10px -5px #00000014, 0 16px 24px 2px #0000000a,
    0 6px 30px 5px #0000000d;
  &:hover {
    cursor: default;
    color: #0052d9;
    .icon {
      background-image: url('../../../../assets/outsourcedCourse/no_fullscreen.png');
    }
  }
  .icon {
    width: 20px;
    height: 20px;
    background-image: url('../../../../assets/outsourcedCourse/no_fullscreen_normal.png');
    background-size: contain;
    display: inline-block;
    margin-right: 4px;
  }
}
</style>
