<template>
  <div class="bottom-text" v-show="emptyShow">
    <img class="empty-img" :src="empty" alt="" />
    <div class="empty-text">{{ emptyTips || $langue('Mooc_Common_NoData', { defaultText: '暂无数据' }) }}</div>
    <slot></slot>
  </div>
</template>
<script>
export default {
  props: {
    emptyShow: {
      type: Boolean,
      default: false
    },
    emptyTips: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      empty: require('@/assets/img/empty.png')
    }
  }
}
</script>
<style lang="less" scoped>
.bottom-text {
  padding-top: 20px;
  color: #999;
  text-align: center;
  background-color: #fff;
  padding-bottom: 30px;
  .empty-img {
    margin-bottom: 20px;
    width: 178px;
    height: 130px;
  }
}
</style>
