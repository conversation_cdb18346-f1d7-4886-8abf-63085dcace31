<template>
  <el-dialog 
  class="out-project-dialog dialog-center none-border-dialog" 
  :title="$langue('Mooc_ProjectDetail_BasicInfo_ExitProject', { defaultText: '退出项目' })" 
  :visible.sync="visible" 
  width="430px"
  top="50px"
  :show-close="false"
  :close-on-click-modal="false"
  >
    <div class="project-body">
      <span class="tips">{{ $langue("Mooc_ProjectDetail_ExitReason", { defaultText: '请认真填写退出原因' }) }}</span>
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item prop="reason">
          <el-input
            type="textarea"
            :placeholder="$langue('Mooc_ProjectDetail_ExitReasonRequired', { defaultText: '请输入退出项目的原因，此项必填' })"
            v-model="form.reason"
            maxlength="200"
            show-word-limit
            :autosize="{ minRows: 5, maxRows: 6 }"
          >
          </el-input>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel" size="small">{{ $langue('Mooc_ProjectDetail_Cancel', { defaultText: '取消' }) }}</el-button>
      <el-button type="danger" @click="handleConfirm" size="small">{{ $langue('Mooc_ProjectDetail_SureExit', { defaultText: '确定退出' }) }}</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { 
  cacelEnroll
} from '@/config/mooc.api.conf.js'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      score: 3,
      form: {},
      rules: {
        reason: [{ required: true, message: this.$langue('Mooc_ProjectDetail_EnterExitReason', { defaultText: '请输入退出项目的原因' }), trigger: 'blur' }]
      }
    }
  },
  methods: {
    cancel() {
      this.$emit('update:visible', false)
    },
    handleConfirm() {
      const { mooc_course_id } = this.$route.query
      this.$refs['form'].validate((valid) => {
        if (valid) {
          cacelEnroll({ mooc_course_id, reason: this.form.reason }).then((res) => {
            this.cancel()
            this.$message.success(this.$langue('Mooc_TaskDetail_ExitProjSucessed', { defaultText: '退出项目操作成功' }))
            this.$emit('outProject')
          })
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.out-project-dialog {
  .tips {
    margin-bottom: 8px;
    display: inline-block;
    color: #e34d59ff;
  }
}
</style>
