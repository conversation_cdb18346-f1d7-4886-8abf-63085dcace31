<template>
  <!-- 作业详情 -->
    <div class="work-detail-container">
      <div class="left-box">
        <workList 
          :total='total' 
          :workListLoading.sync="workListLoading"
          :studentList="studentList"
          :activeStudent="activeStudent"
          :detailData="detailData"
          :currentInfo="currentInfo"
          @getStudentList="getStudentList"
          @handleCurrentActive="handleCurrentActive"
          :statisticsNumber="statisticsNumber"
          ref='workListRef'
         ></workList>
      </div>
      <div class="mid-box">
        <div class="header">
          <div class="header-top">
            <span class="title">{{detailData.title}}</span>
            <span class="check-work" @click="dialogVisibleWorkRequire = true">查看作业要求</span>
          </div>
          <div class="header-bottom">
            <span v-if="statusLabel" :class="statusLabelColor" class="common-status-label">
              <span>{{ statusLabel }}</span>
            </span>
            <span class="sub-time">提交人：<span class="value">{{currentInfo.staff_name || '-'}}</span></span>
            <span class="sub-time">提交时间：<span class="value"> {{currentInfo.submit_time || '-'}}</span></span>
          </div>
        </div>
        <!-- 作业内容下载作业 -->
        <div class="work-content" v-if="studentList.length">
          <span class="work-content-title">
            作业内容 
            <el-button type="primary" @click="downloadHomework" :loading="downLoading">下载作业</el-button>
          </span>
          <div class="appendix-c">
            <appendix size="mini" :isPreview="true" :fileData="fileData"></appendix>
          </div>
          <div class="p-cur">
            <sdc-mce-preview 
            ref="preview" 
            :urlConfig="editorConfig.urlConfig" 
            :catalogue.sync="editorConfig.catalogue" 
            :content="currentInfo.content"
            >
            </sdc-mce-preview>
          </div>
        </div>
        <div class="none-work-content" v-else>暂无数据~</div>
      </div>
      <!-- 互评详情 -->
      <scoreDetails 
      ref="scoreDetails" 
      :detailData="detailData"
      :currentInfo="currentInfo"
      detailType="manage"
      :isReadOver="isReadOver"
      ></scoreDetails>
      <div class="my-work-footer">
        <div class="footer-content-work">
          <el-button size="small" @click="handlepart('p')" v-if="disabledPart">上一份作业</el-button>
          <el-button size="small" @click="handlepart('n')" v-if="disabledNext">下一份作业</el-button>
          <!-- 只勾选学员互评 -->
          <el-tooltip
          v-if="showGoodStatusBack"
          :disabled="currentInfo.status !== 2 && submitDate"  
          effect="dark" 
          :content="tipsContent" 
          placement="top"
          >
            <span><el-button :disabled="currentInfo.status === 2 || !submitDate" size="small" type="danger" @click="backWork()"> 退回作业</el-button></span>
          </el-tooltip>
          <!-- 开通了老师批阅 -->
          <el-tooltip
          v-else-if="readTeacher"
          :disabled="currentInfo.status !== 2 && submitDate"  
          effect="dark" 
          :content="tipsContent" 
          placement="top"
          >
            <span><el-button :disabled="currentInfo.status === 2 || !submitDate" size="small" type="danger" @click="backWork()"> 退回作业</el-button></span>
          </el-tooltip>
          <!-- 有批阅权限---退回状态不显示 -->
          <!-- 未勾选老师批阅不显示 -->
        <el-button 
          v-if="showBtn && !isReadOver"  
          size="small" 
          type="primary" 
          @click="submitWork"
          >
          确认提交
        </el-button>
        <el-button 
          v-if="showBtn && isReadOver"  
          size="small" 
          type="primary" 
          @click="isReadOver = false"
          >
          修改评分
        </el-button>
        </div>
    </div>
    <!-- 退回作业 -->
    <el-dialog :close-on-click-modal="false" class="details-back" title="退回作业" :visible.sync="dialogVisibleBackWork" width="30%">
      <div>
        <p class="red" v-if="currentInfo.teacher_mark_record === null">作业退回后学员需重新提交，此操作无法撤销。退回原因将会反馈给学员，请认真填写。</p>
        <p class="red" v-else>作业退回后学员需重新提交，同时会清除作业的老师分数及评语，此操作无法撤销。退回原因将会反馈给学员，请认真填写。</p>
        <el-input type="textarea" :autosize="{ minRows: 14, maxRows: 16}" placeholder="请输入退回作业的原因，此项必填" v-model="backWorkInput">
        </el-input>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogVisibleBackWork = false">取消</el-button>
        <el-button size="small" type="danger" @click="handleReturn">确认退回</el-button>
      </span>
    </el-dialog>
    <!-- 作业要求 -->
    <el-dialog :close-on-click-modal="false" class="details-back" :title="detailData.title" :visible.sync="dialogVisibleWorkRequire" width="30%">
      <div>
        <sdc-mce-preview ref="preview" :urlConfig="editorConfig.urlConfig" :catalogue.sync="editorConfig.catalogue" :content="detailData.desc">
        </sdc-mce-preview>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" size="small" @click="dialogVisibleWorkRequire = false">知道了</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import {
  getManagerWorkListDetails,
  getRefundHomework,
  workTaskDetail,
  getManagerWorkList,
  getStatisticsNum
} from '@/config/mooc.api.conf.js'
import { saveAndEditMark } from 'config/api.conf'
import scoreDetails from '@/views/components/scoreDetails.vue'
import appendix from '@/views/components/appendix'
import workList from '@/views/components/workList'
import axios from 'axios'
import env from 'config/env.conf.js'
const status_info = {
  1: '待批阅',
  2: '已退回',
  3: '合格',
  4: '不合格'
}
const no_read_work_info = {
  1: '已提交',
  2: '已退回'
}
export default {
  components: {
    scoreDetails,
    appendix,
    workList
  },
  data() {
    return {
      workListLoading: false,
      dialogVisibleBackWork: false,
      dialogVisibleWorkRequire: false,
      backWorkInput: '',
      fileData: [],
      total: 0,
      workList: [],
      detailData: {},
      work: {
        content: ''
      },
      statisticsNumber: {},
      currentInfo: {},
      status_info,
      no_read_work_info,
      studentList: [],
      everyStudentInfo: {},
      scoreData: {},
      isReadOver: false,
      downLoading: false,
      editorConfig: {
        catalogue: false,
        options: {
          // 非必传字段
          selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
                    formatselect fontsizeselect lineheight |
                    bold italic underline strikethrough |
                    forecolor backcolor |
                    dent align bullist numlist |
                    insert codesample |
                    fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      }
    }
  },
  created() {
    this.queryData()
    this.getStudentList()
    this.getStatistics()
  },
  mounted() {

  },
  computed: {
    statusLabel() {
      return this.detailData.enable_mark ? status_info[[this.currentInfo.status]] : no_read_work_info[this.currentInfo.status]
    },
    statusLabelColor() {
      let colorClass = ''
      switch (this.currentInfo.status) {
        case 1:
          colorClass = 'wait-status'
          break
        case 2:
          colorClass = 'back-status'
          break
        case 3:
          colorClass = 'good-status'
          break
        case 4:
          colorClass = 'bad-status'
          break
      }
      return colorClass
    },
    disabledPart() {
      return this.activeStudent !== this.studentList[0]?.staff_id && this.studentList.length > 1
    },
    disabledNext() {
      return (this.activeStudent !== this.studentList[this.studentList.length - 1]?.staff_id) && this.studentList.length > 1
    },
    submitDate() {
      let flagStart = false
      let flagEnd = false
      // 获取当前时间
      const timeStamp = Date.parse(new Date())
      // 开始
      const startDate = Date.parse(this.detailData.start_time)
      // 结束
      const endDate = Date.parse(this.detailData.end_time)
      // 避免没有作业提交时间
      flagStart = startDate ? timeStamp > startDate : true
      flagEnd = endDate ? timeStamp < endDate : true
      return flagStart && flagEnd
    },
    tipsContent() {
      let tips = ''
      if (!this.submitDate) {
        tips = '已过作业提交截止时间，无法退回学员作业'
      } else if (this.currentInfo.status === 2) {
        tips = '该学员作业已退回'
      }
      return tips
    },
    // 确认提交按钮显示与否
    showBtn() {
      //  没有批阅权限---退回状态不显示
      // 未勾选老师批阅不显示
      const markTypeList = (this.detailData?.mark_type && this.detailData?.mark_type.split(';')) || []
      return this.currentInfo.has_mark && markTypeList.includes('1') && this.currentInfo.status !== 2
    },
    record_id() {
      return this.everyStudentInfo?.record_id
    },
    activeStudent() {
      return this.everyStudentInfo?.staff_id || 0
    },
    // 完成条件不只是提交作业
    finished_condition() {
      return this.detailData?.finished_condition?.type !== '3'
    },
    // 完成条件勾选老师批阅--可以同时勾选了学员互评
    readTeacher() {
      const teacherType = this.detailData?.finished_condition?.type.split(';')
      return teacherType?.length ? teacherType.includes('4') && this.currentInfo.has_mark && this.finished_condition : ''
    },
    // 只勾选学员互评，合格不合格状态管理员需要退回操作
    showGoodStatusBack() {
      return this.detailData?.mark_type === '2' && [3, 4].includes(this.currentInfo.status) && this.detailData.approval_permission
    }
  },
  methods: {
    downloadHomework() {
      this.downLoading = true
      const { act_id } = this.$route.query
      let url = `${env[process.env.NODE_ENV].trainingPath}api/mooc/manage/homework/download-homework-record?record_id=${this.record_id}&act_id=${act_id}`
      axios({
        url,
        method: 'get',
        responseType: 'blob'
      }).then((res) => {
        if (res.status === 200 && res.data) {
          this.downLoading = false
          const a = document.createElement('a')
          const href = window.URL.createObjectURL(new Blob([res.data], { type: 'application/zip' })) // 创建下载的链接
          a.href = href
          a.download = this.currentInfo.staff_name
          document.body.appendChild(a)
          a.click() // 点击下载
          document.body.removeChild(a) // 下载完成移除元素
          window.URL.revokeObjectURL(href) // 释放掉blob对象
        }
      }).catch((e) => {
        this.downLoading = false
      })
    },
    // 作业总详情
    queryData() {
      const { task_id, act_id, homework_id } = this.$route.query
      if (!task_id) return
      workTaskDetail({ task_id, mooc_course_id: act_id }, homework_id).then((res) => {
        this.detailData = res
        this.detailData.finished_condition = JSON.parse(this.detailData?.finished_condition) 
      })
    },
    // 上一份--下一份
    handlepart(val) {
      let pindex = 0
      const { homework_id } = this.$route.query
      if (!this.studentList.length) return
      this.studentList.forEach((item, index) => {
        if (item.staff_id === this.activeStudent) {
          pindex = index + (val === 'n' ? 1 : -1)
        }
      })
      this.everyStudentInfo = this.studentList[pindex] // 当前学员
      this.getDetails()
      // 评分详情
      this.$refs['scoreDetails'].initScoreData({ record_id: this.record_id, homework_id })
    },
    // 选中某个学员
    handleCurrentActive(val) {
      this.everyStudentInfo = val // 当前学员信息
      this.studentList.forEach((item) => {
        if (item.staff_id === val.staff_id) {
          this.getDetails()
        }
      })
    },
    // 每个学员的作业详情
    getDetails() {
      let { act_id, homework_id } = this.$route.query
      const { staff_id } = this.everyStudentInfo
      let params = {
        homework_id,
        act_id,
        record_id: this.record_id,
        staff_id: staff_id || ''
      }
      getManagerWorkListDetails(params).then((res) => {
        this.currentInfo = res
        this.fileData = res.homework_attachments.map((e) => {
          return {
            ...e,
            content_type: e.attachment_type
          }
        })
        // 是否批阅
        this.isReadOver = !!res?.teacher_mark_record?.score
        // 评分详情
        this.$refs['scoreDetails'].initScoreData({ record_id: this.record_id, homework_id })
      })
    },
    // 退回作业
    backWork() {
      this.backWorkInput = ''
      this.dialogVisibleBackWork = true
    },
    submitWork() {
      // 修改评价
      this.scoreData = this.$refs['scoreDetails'].form
      if (this.scoreData?.score > this.detailData.total_score) {
        this.$message.warning('请输入小于总分的分数')
        return
      }
      const { homework_id, task_id } = this.$route.query
      let params = {
        record_id: this.record_id,
        homework_id,
        mark_type: '1', // 1-老师批阅，2-学生获评，3-老师退回
        content: this.scoreData?.content || '',
        score: this.scoreData?.score || '',
        task_id,
        mark_role_id: 1,
        mark_role_name: '老师批阅'
      }
      saveAndEditMark('manage', params).then((res) => {
        this.$message.success('提交成功')
        this.getDetails()
        this.getStudentList()
        this.getStatistics()
      })
    },
    handleReturn() {
      if (!this.backWorkInput.trim()) {
        this.$message.error('请输入退回作业的原因')
        return
      }
      const { task_id, act_id } = this.$route.query
      let params = {
        record_id: this.record_id,
        reason: this.backWorkInput,
        mark_role_id: 1,
        mark_role_name: '老师批阅',
        task_id,
        mooc_course_id: act_id
      }
      getRefundHomework(params).then((res) => {
        this.$message.success('退回成功')
        this.dialogVisibleBackWork = false
        this.getDetails()
        this.getStatistics()
        this.getStudentList()
      })
    },
    // 学员列表
    getStudentList(form, type) {
      const { homework_id, act_id, recordId } = this.$route.query
      let parmas = {
        homework_id,
        act_id,
        staff_name: form?.staff_name || '',
        page_no: form?.page_no || 1,
        page_size: 20,
        status: form?.status || ''
      }
      getManagerWorkList(parmas).then((res) => {
        this.workListLoading = false
        this.total = res.total
        this.studentList = type === 'load' ? this.studentList.concat(res.records) : res.records
        if (type !== 'load') {
          // 查询不到就默认第一条
          const index = this.studentList.findIndex((e) => e.record_id === Number(recordId))
          this.everyStudentInfo = index >= 0 ? this.studentList[index] : this.studentList[0]
          this.getDetails()
        }
      }).catch(() => {
        this.workListLoading = false
      })
    },
    // 更新学员状态
    getStatistics() {
      const { act_id, homework_id } = this.$route.query
      let parmas = {
        homework_id,
        act_id
      }
      getStatisticsNum(parmas).then((res) => {
        this.statisticsNumber = res
      })
    }
  }
}
</script>
<style lang="less" scoped>
.work-detail-container {
  width: 1440px;
  margin: auto;
  // background-color: #f6f7f9;
  display: flex;
  // height: 100%;
  padding: 20px 0 70px;
  position: relative;
  :deep(.work-list) {
    padding-top: 0;
    background: #fff;
    .list-text {
      color: #000000e6;
    }
  }
  .left-box {
    position: static;
    // top: 20px;
    // left: 200px;
    padding: 20px;
    background-color: #fff;
    margin-right: 20px;
  }
  .mid-box {
    background-color: #fff;
    padding: 16px 24px;
    width: 834px;
    .header {
      border-bottom: 1px solid #eeeeee;
      padding-bottom: 16px;
      .title {
        color: #000000e6;
        font-size: 16px;
        font-weight: bold;
      }
      .check-work {
        color: #0052d9;
        font-size: 14px;
        margin-left: 16px;
        cursor: pointer;
      }
      .header-bottom {
        margin-top: 16px;
        color: #00000099;
        display: flex;
        align-items: center;
        line-height: 22px;
        .common-status-label {
          opacity: 1;
          font-size: 12px;
          border-radius: 2px;
          padding: 3px 4px;
          height: 18px;
          display: flex;
          align-items: center;
          margin-right: 40px;
        }
        .good-status {
          background: #ccf2e2;
          color: #00B368;
        }
        .bad-status {
          background: #fdecee;
          color: #E34D59
        }
        .back-status {
          background: #fdf6ec;
          color:#FF7548
        }
        .wait-status {
          background: #d4e3fc;
          color: #0052D9
        }
        .value {
          color: #000000e6;
        }
        .sub-time {
          margin-right: 40px;
        }
      }
    }
    .work-content {
      margin-top: 18px;
      .work-content-title {
        color: #000000e6;
        font-size: 14px;
        font-weight: 600;
        height: 22px;
        line-height: 22px;
        .el-button {
          margin-left: 20px;
          padding: 2px 8px;
          font-size: 12px;
          line-height: 20px;
        }
      }
      .p-cur {
        padding: 20px 0;
      }
    }
    .none-work-content {
      padding-top: 20px;
      color: #999;
      text-align: left;
    }
  }
  // .right-box {
    // width: 320px;
    // background-color: #f6f7f9;
    .comment-detail {
      display: flex;
      justify-content: space-between;
      .comment-title {
        font-size: 16px;
        font-weight: bold;
        color: #000000e6;
      }
    }
    .score-tag {
      height: 18px;
      border-radius: 2px;
      background: #fef3e6;
      color: #ed7b2f;
      font-size: 12px;
      line-height: 18px;
      text-align: center;
      padding: 0 4px;
    }
    .memeber-tips {
      width: 275px;
      margin-top: 16px;
    }
    .member-comment-box {
      margin-top: 16px;
      .item-member-comment {
        .comment-info {
          font-size: 12px;
          display: flex;
          justify-content: space-between;
          line-height: 20px;
          .score-tag {
            margin-left: 8px;
          }
          .comment-time {
            color: #00000066;
          }
        }
        .comment-content {
          line-height: 22px;
          margin-top: 4px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 3;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      .item-member-comment + .item-member-comment {
        margin-top: 12px;
      }
    }
  // }
  .my-work-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 70px;
    line-height: 70px;
    background-color: #fff;
    z-index: 99;
    .footer-content-work {
      margin: auto;
      text-align: right;
    }
    @media screen and (max-width: 1660px) {
      .footer-content-work { 
        width: 1158px;
      }
    }
    @media screen and (min-width: 1661px) {
      .footer-content-work { 
        width: 1440px;
      }
    }
    .el-button {
      margin: 0 20px 0 0;
      width: 104px;
    }
  }
}
.details-back {
  .red {
    color: #e34d59;
    margin-bottom: 20px;
  }
}
</style>
