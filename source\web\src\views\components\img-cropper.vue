<template>
  <div class="cropper_model">
    <el-dialog
      title="图片剪裁"
      width="600px"
      custom-class="dialog-center cropper_model_dlg"
      :visible.sync="dialogVisible"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @open="open"
      @close="close"
      v-loading='loading_dialog'
    >
      <div class="content-main">
        <p style="margin:0px 0px 20px 16px">当前图片比例为{{ proportion }}</p>
        <div class="cropper_content">
          <div class="cropper" style="text-align: center;">
            <vueCropper
              ref="cropper"
              :img="options.img"
              :outputSize="options.outputSize"
              :outputType="options.outputType"
              :info="options.info"
              :canScale="options.canScale"
              :autoCrop="options.autoCrop"
              :autoCropWidth="options.autoCropWidth"
              :autoCropHeight="options.autoCropHeight"
              :fixed="options.fixed"
              :fixedBox="options.fixedBox"
              :fixedNumber="options.fixedNumber"
              :enlarge="options.enlarge"
              :centerBox="options.centerBox"
              :mode=options.mode
              :infoTrue="options.infoTrue"
              :height="options.height"
              :full="options.full"
              @realTime="previewImg"
            >
            </vueCropper>
            <div class="cropper_btns">
              <div>
                <el-button type="primary" @click="goUpload" size="mini">
                  重新选择
                </el-button>
                <el-button
                  @click="rotateLeft"
                  icon="el-icon-refresh-left"
                  size="mini"
                  title="左旋转"
                >
                </el-button>
                <el-button
                  @click="rotateRight"
                  icon="el-icon-refresh-right"
                  size="mini"
                  title="右旋转"
                >
                </el-button>
                <el-button @click="changeScale(1)" size="mini" title="放大">
                  +
                </el-button>
                <el-button @click="changeScale(-1)" size="mini" title="缩小">
                  -
                </el-button>
              </div>
              <el-button type="primary" @click="uploadImg" size="mini" :loading="loading">
                确定
              </el-button>
            </div>
          </div>
        </div>
        </div>
    </el-dialog>
  </div>
</template>

<script>
import { VueCropper } from 'vue-cropper'

export default {
  components: { VueCropper },
  props: {
    imgInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      options: {
        img: '', // 裁剪图片的地址
        outputSize: 1, // 裁剪生成图片的质量
        outputType: 'png', // 裁剪生成图片的格式
        info: true, // 裁剪框的大小信息
        canScale: true, // 图片是否允许滚动缩放
        autoCrop: true, // 是否默认生成截图狂
        autoCropWidth: 240, // 默认生成截图框宽度
        autoCropHeight: 360, // 默认生成截图框高度
        fixed: true, // 是否开启截图框宽高固定比例
        fixedNumber: [2, 3], // 截图框的宽高比例
        full: true, // 是否输出原图比例的截图
        fixedBox: false, // 固定截图框大小 不允许改变
        canMove: true, // 上传图片是否可以移动
        canMoveBox: true, // 截图框能否拖动
        original: true, // 上传图片按照原始比例渲染
        centerBox: true, // 截图框是否被限制在图片里面
        height: false, // 是否按照设备的dpr输出等比例图片
        infoTrue: true, // true为展示真实输出图片宽高false展示看到的截图框宽高
        maximgSize: 3000, // 限制图片最大宽度和高度
        enlarge: 1, // 图片根据截图框输出比例倍数
        mode: 'contain' // 图片默认渲染方式(contain, cover, 100px, 100% auto)
      },
      preview: {},
      name: '',
      proportion: '2 : 3',
      loading_dialog: false
    }
  },
  watch: {
    imgInfo: {
      deep: true,
      immediate: true,
      handler(newV, oldV) {
        const { width } = newV
        this.options.autoCropWidth = width
        this.options.autoCropHeight = Math.round((width * 3) / 2)
        this.options.centerBox = true
      }
    }
  },
  methods: {
    open(data) {
      this.loading_dialog = true
      if (data && data.name) {
        this.name = data.name
      }
      if (data) {
        this.options.img = window.URL.createObjectURL(data)
        this.dialogVisible = true
        if (this.options.img) {
          this.$nextTick(() => {
            setTimeout(() => {
              this.loading_dialog = false
            }, 1500)
          })
        } else {
          this.$message.error('请重新选择')
          this.close()
        }
      }
    },
    close() {
      this.dialogVisible = false
    },
    // base64转图片文件
    dataURLtoFile(dataurl, filename) {
      let arr = dataurl.split(',')
      let mime = arr[0].match(/:(.*?);/)[1]
      let bstr = atob(arr[1])
      let len = bstr.length
      let u8arr = new Uint8Array(len)
      while (len--) {
        u8arr[len] = bstr.charCodeAt(len)
      }
      return new File([u8arr], filename, { type: mime })
    },
    // 左旋转
    rotateLeft() {
      this.$refs.cropper.rotateLeft()
    },
    // 右旋转
    rotateRight() {
      this.$refs.cropper.rotateRight()
    },
    // 放大缩小
    changeScale(num) {
      num = num || 1
      this.options.centerBox = false
      this.$nextTick(() => {
        this.$refs.cropper.changeScale(num)
      })
    },
    // 实时预览
    previewImg(data) {
      this.preview = data
    },
    goUpload() {
      this.$emit('upAgain')
    },
    // 上传图片
    uploadImg() {
      let self = this
      self.loading = true
      this.$refs.cropper.getCropData(data => {
        let file = this.dataURLtoFile(data, self.name)
        // 生成文件类型
        self.loading = false
        this.$emit('getFile', { file: file })
      })
    }
  }
}
</script>

<style lang="less" scoped>
.cropper_model_dlg {
  .cropper_content {
    margin: 0 auto;
    display: flex;
    justify-content: center;
  }
  .cropper {
    width: 500px;
    height: 400px;
    background: yellow;
  }
  .cropper_right {
    width: 300px;
    text-align: center;
  }
  .cropper_preview {
    margin: 0 auto;
    display: inline-block;
    border: 1px solid #ddd;
  }
  .cropper_btns {
    margin-top: 30px;
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
