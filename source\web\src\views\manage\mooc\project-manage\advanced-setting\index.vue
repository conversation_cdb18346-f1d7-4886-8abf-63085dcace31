<template>
  <div class="data-count">
    <el-tabs v-model="currentTab">
      <el-tab-pane label="消息设置" name="msgSetting">
        <msg-setting v-if="currentTab === 'msgSetting'"></msg-setting>
      </el-tab-pane>
      <el-tab-pane label="资料管理" name="dataManage">
        <data-manage v-if="currentTab === 'dataManage'"></data-manage>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import MsgSetting from './msg-setting.vue'
import DataManage from './data-manage.vue'

export default {
  components: {
    MsgSetting,
    DataManage
  },
  data () {
    return {
      currentTab: 'msgSetting'
    }
  }
}
</script>

<style lang="less" scoped>
  .data-count {
    height: 100%;
    background: #fff;
  }
  :deep(.el-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;
    .el-tabs__header {
      padding: 20px 20px 0;
      margin: 0;
      border-bottom: 1px solid #f3f3f3ff;
    }
    .el-tabs__content {
      flex: 1;
      padding: 20px;
      overflow: auto;
    }
  }
</style>
