<template>
  <div class="user">
    <link rel="stylesheet" :href="linkHref">
    
    <!-- 内容区域 -->
    <div id="content">
      <router-view :key="$route.fullPath" ref="container" />
    </div>
  </div>
</template>
  
<script>

export default {
  name: 'user',
  components: {
  },
  data() {
    return {
      linkHref: ''
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        let root = document.documentElement
        if (val.staff_name && !document.getElementById('graphic-common-head')) {
          root.style.setProperty('--app-height', 'calc(100% - 62px)')
          this.headerIsLoad = true
          this.linkHref = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeaderStyle.css` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeaderStyle.css`  
          const commonHeaderJsUrl = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeader.js` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeader.js`
          this.loadHeadJS(commonHeaderJsUrl, function() {
            if (window.$qlCommonHeader) {
              window.$qlCommonHeader.create({
                staff_name: val.staff_name,
                dev: !(process.env.NODE_ENV === 'production')
              })

              setTimeout(() => {
                let el = document.getElementsByClassName('common-header-occupy')
                if (el.length > 0) {
                  console.log('El', el[0].offsetHeight)
                  document.getElementById('app').style.height = `calc(100% - ${el[0].offsetHeight}px)`
                }
              }, 500)
            }
          })
        } else {
          root.style.setProperty('--app-height', '100%')
        }
      },
      immediate: true
    }
  },
  computed: {
  },
  mounted() {
  },
  methods: {
    // 获取登陆用户信息
    loadHeadJS (url, callback) {
      var script = document.createElement('script')
      var fn = callback || function() {}
      script.type = 'text/javascript'
      script.id = 'graphic-common-head'
      // IE
      if (script.readyState) {
        script.onreadystatechange = function() {
          if (script.readyState === 'loaded' || script.readyState === 'complete') {
            script.onreadystatechange = null
            fn()
          }
        }
      } else {
        // 其他浏览器
        script.onload = function() {
          fn()
        }
      }
      script.src = url
      document.getElementsByTagName('head')[0].appendChild(script)
    }
  }
}
</script>
<style lang="less">
@import '~assets/css/graphic-common.less';
@import '~assets/css/common.less';
@import '~assets/css/center.less';
#app {
  height: var(--app-height);
}
@import '~assets/css/el-style.less';
.mooc-autocomplete{
  width: 300px !important;
  .el-scrollbar{
    .search-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .search-label {
      display: block;
      width: 180px;
      font-size: 13px;
      color: #434343;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .search-time {
      float: right;
      font-size: 13px;
      color: #999;
    }
  }
}
</style>
<style lang="less" scoped>
.user {
  background-color: #f0f4fa;
  height: 100%;
  min-width: 1440px;
  position: relative;
  display: flex;
  flex-direction: column;
  #content {
    width: 100%;
    height: 100%;
    background-color: #f0f4fa;
    flex-grow: 1;
  }
}
</style>
