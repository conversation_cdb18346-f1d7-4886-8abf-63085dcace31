<template>
  <div class="video-container" :dt-areaid="dtOutCoursePage('area')" :dt-remark="dtOutCoursePage('remark')">
    <div
      id="main-area"
      class="main-fixed"
    >
      <div class="play-video" v-if="['Video', 'Video-2d', 'Video-3d', 'Video-ppt', 'Audio'].includes(courseData.course_type)">
         <!-- 文章内容 - 嵌入的h5页面 -->
        <iframe
          class="video-card"
          id="videoCard"
          allowfullscreen
          :src="iframeSrc"
          frameborder="0"
        ></iframe>
      </div>
      <div class="video-box" v-else>
        <van-image
          lazy
          fit="fill"
          :src="
            courseData.photo_url
              ? courseData.photo_url
              : require('@/assets/img/default_bg_img.png')
          "
        >
        </van-image>
      </div>
      <div class="tabs-card">
        <van-tabs
          v-model="activeKey"
          color="#0052D9"
          title-active-color="#0052D9"
          @click="changeTabs"
        >
          <van-tab
            v-for="item in tabList"
            :title="$langue(item.title, { defaultText: item.text })"
            :key="item.key"
            :name="item.key"
          ></van-tab>
        </van-tabs>
        <div class="conceal" v-if="tabListLen < 4" :style="{ width: `calc(100vw - 78px * ${tabListLen})` }"></div>
      </div>
    </div>
    <div class="main-content">
      <!-- 简介 -->
      <desContent
        v-show="activeKey === 'des'"
        :courseData.sync="courseData"
        @toComment="activeKey = 'comment'"
      ></desContent>
      <!-- 评论 -->
      <sdc-comment-mob
        v-if="courseData.independent_course === 1 && activeKey === 'comment' && commentParams"
        :params="commentParams"
      ></sdc-comment-mob>
    </div>
    <!-- 双语按钮 -->
    <!-- 双语暂时注释 -->
    <div id="drag-lang" class="drag-lang" v-if="isGeekbang" >
      <span :class="[moocLang === 'en-us' ? 'el-icon-zh' : 'el-icon-en', 'icon']" ></span>
      <span class="text">{{ moocLang === 'en-us' ? '中文' : 'Eng' }}</span>
    </div>
  </div>
</template>
<script>
import { 
  getGeekCourseDetail,
  geekStudyRecord
} from 'config/mooc.api.conf.js'
import desContent from './desContent.vue'
import MoocJs from 'sdc-moocjs-integrator'
// import { Toast } from 'vant'
import { mapState } from 'vuex'
import translateLang from '@/views/mobile/mixins/translateLang.js'
import { detailLogo } from '@/utils/outsourcedCourseMap.js'
import { pageExposure } from '@/utils/tools.js'

export default {
  mixins: [translateLang],
  components: {
    desContent
  },
  data() {
    return {
      act_type: '102', // 课程类型 102-极客时间
      activeKey: 'des',
      iframeSrc: '',
      courseData: {}, // 课程信息
      commentParams: null, // 评论组件配置
      viewTimer: null, // 上报定时器
      playTime: 0,
      isPageHidden: false, // 是否熄屏
      tabList: [
        { title: 'Mooc_TaskDetail_Audio_Description', key: 'des', text: '简介' }
      ],
      iframeDom: null,
      studyRecordQuery: { // video学习记录
        area_id: this.$route.query.area_id || '',
        from: this.$route.query.from || '',
        act_id: this.$route.query.course_id,
        learn_record_id: 0,
        is_finish: 0,
        is_review: 0,
        total_study_time: 0,
        errNum: 0, // 上报错误次数
        clear3sTimeId: null
        // my_study_progress: ''
      }
    }
  },
  computed: {
    ...mapState(['moocLang']),
    course_id() {
      return this.$route.query.course_id || ''
    },
    mooc_course_id() {
      return this.courseData.recourse_parent_mooc_id || '-1'
    },
    shareStaffId() {
      return this.$route.query.share_staff_id || ''
    },
    shareStaffName() {
      return this.$route.query.share_staff_name || ''
    },
    isGeekbang() {
      return this.$route.query.from === 'geekbang'
    },
    tabListLen() {
      return this.tabList.filter(v => v.title).length
    },
    // 页面曝光
    dtOutCoursePage() {
      return (type) => {
        const { course_title } = this.courseData
        if (type === 'area') {
          return `area_${this.course_id}`
        } else if (type === 'remark') {
          return JSON.stringify({ 
            page: course_title,
            page_type: '外部课程详情页',
            container: course_title,
            act_type: '102',
            content_type: '视频',
            terminal: 'H5'
          })
        } else {
          return ``
        }
      }
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          const hostUrl =
            process.env.NODE_ENV === 'development'
              ? process.env.VUE_APP_PORTAL_HOST_WOA
              : window.origin
          this.commentParams = {
            userName: val.staff_name,
            actId: this.course_id,
            appId: 'A9BiosXihR0h46ThNsAX',
            orderType: 102,
            urlConfig: {
              getComments: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/${this.mooc_course_id}/comment-page`,
              addComment: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/add-comment`,
              deleteComment: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/delete-comment/`,
              like: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/praised-comment`,
              sticky: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/sticky-comment`,
              show: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/show-comment`
            }
          }
        }
      },
      immediate: true
    }
  },
  created() {
    // 添加message事件监听
    window.addEventListener('visibilitychange', this.visibilitychange, false)
    window.addEventListener('beforeunload', this.beforeunloadEvent, false)
  },
  mounted() {
    this.iframeDom = document.querySelector('#videoCard')
    MoocJs.setPause(() => {
      // 暂停
      this.iframeDom && this.iframeDom.contentWindow.postMessage('video:pause', '*')
    })
    MoocJs.setPlay(() => {
      // 播放
      this.iframeDom && this.iframeDom.contentWindow.postMessage('video:play', '*')
    })
    MoocJs.messageListener((res) => {
      if (res.events === 'tencent-mooc-lang') {
        this.$store.commit('setMoocLang', res.params)
        this.getMobileLangJS()
      }
    })
    this.$nextTick(() => {
      this.initLang()
      // 先初始化双语，再弹窗，避免双语没有加载出来
      this.getCourseDetail()
    })
  },
  beforeDestroy() {
    clearTimeout(this.clear3sTimeId)
    this.clear3sTimeId = null
    this.clearViewTimer()
    MoocJs.removeEvent()
    // 移除监听
    window.removeEventListener('visibilitychange', this.visibilitychange)
    window.removeEventListener('message', this.communication)
  },
  methods: {
    // 获取课程详情
    getCourseDetail() {
      window.parent && window.parent.postMessage({ page: 'iframe', loading: true, geekSourceLoading: true }, '*')
      getGeekCourseDetail(this.course_id, { loading: false }).then(res => {
        if (res.independent_course === 1) {
          this.tabList.push({ title: 'NetCourse_Comment', key: 'comment', text: '讨论' })
          this.tabList.push({ title: '', key: 'index1', text: '' })
          this.tabList.push({ title: '', key: 'index2', text: '' })
          this.tabList.push({ title: '', key: 'index3', text: '' })
        } else {
          this.tabList.push({ title: '', key: 'index1', text: '' })
          this.tabList.push({ title: '', key: 'index2', text: '' })
          this.tabList.push({ title: '', key: 'index3', text: '' })
          this.tabList.push({ title: '', key: 'index4', text: '' })
        }
        document.title = res.course_title
        res.authorInfo = `${res.author}(${res.author_intro})`
        res.course_type = 'Video'
        res.act_type = '102'
        this.courseData = res
        this.iframeSrc = res.recourse_iframe_url + '&pm=parent'
        // 监听播放等事件
        window.addEventListener('message', this.communication)
        this.clear3sTimeId = setTimeout(() => {
          window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
        }, 3000)

        // 详情页曝光上报
        pageExposure({
          page_type: '移动端外部课程视频详情页',
          content_type: '网络课',
          act_type: '2',
          content_name: res.course_title,
          content_id: this.course_id,
          terminal: 'H5'
        })
      }).catch((err) => {
        window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
        if (err.code) {
          let type = 0
          if (err.code === 403) {
            type = 3
          } else if (err.code === 500) {
            type = 5
          }
          this.$router.replace({
            name: 'mobileError',
            query: {
              type
            }
          })
          MoocJs.sendErrorInfo(err.message)
        }
      })
    },
    communication(event) {
      let { action, vendor } = event.data
      let vendorArray = Object.keys(detailLogo) || []
      if ([...vendorArray, 'geekbang'].includes(vendor)) {
        if (action === 'video:mounted') {
          window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
        }
        this.handleRecord({ evt: action })
      }
    },
    // 初始化监听器
    creatViewTimer() {
      let _this = this
      this.clearViewTimer()
      let durtation = 0
      let totalTime = (this.courseData.video_time || 15) * 2.5
      this.viewTimer = setInterval(() => {
        durtation++
        _this.studyRecordQuery.total_study_time++
        if (durtation % 15 === 0) {
          _this.handleViewGraphicRecord() // 浏览器时长需每15秒记录一次
        }
        if (durtation >= totalTime) {
          _this.clearViewTimer()
        }
      }, 1000)
    },
    // 清除15s计时器
    clearViewTimer () {
      if (this.viewTimer !== null) {
        clearInterval(this.viewTimer)
        this.record_id = ''
      }
    },
    // 访问记录上报
    handleViewGraphicRecord() {
      if (!this.studyRecordQuery.total_study_time) return
      const recordParam = {
        area_id: this.$route.query.area_id || '',
        from: this.$route.query.from || '',
        course_id: this.course_id,
        sharer_id: this.shareStaffId || '',
        sharer_name: this.shareStaffName || '',
        // from_type: 'geekBang',
        from_type: this.courseData.recourse_from || 'geekBang',
        record_id: this.record_id || ''
      }
      console.log('视频上报参数recordParam: ', recordParam)
      geekStudyRecord(recordParam).then((data) => {
        // 只统计连续上报的错误次数
        this.errNum = 0
        if (data) {
          this.recordId = data
        }
      }).catch(() => {
        // 统计连续上报错误次数
        this.errNum++
        if (this.errNum >= 3) {
          this.clearViewTimer()
        }
      })
    },
    // TODO: 需要使用postMessage监听iframe里面的播放器事件 暂停或者播放 然后同步学习时间的显示 
    // iframe里面的播放器的事件
    handleRecord(param) {
      if (param.evt === 'video:play') {
        // 重新学习，重置数据
        if (this.studyRecordQuery.is_finish === 1) {
          this.studyRecordQuery.is_finish = 0
          this.studyRecordQuery.is_review = 1 // 重播
          this.studyRecordQuery.total_study_time = 0
          this.learnRecordId = 0
        }
        if (!this.viewTimer) {
          this.creatViewTimer()
        }
        MoocJs.play()
      }

      if (param.evt === 'video:pause' || param.evt === 'video:ended') {
        if (param.evt === 'video:ended') { // 学习完
          this.studyRecordQuery.is_finish = 1
        }
        
        // 延迟1秒调用方法，（视频播放完成会先触发pause，再触发ended），解决同时触发pause、ended情况
        setTimeout(() => {
          this.handleViewGraphicRecord()
        }, 1000)
        this.clearViewTimer()

        if (param.evt === 'video:pause') {
          MoocJs.pause()
        } else if (param.evt === 'video:ended') {
          MoocJs.complete()
        }
      }
    },
    // 视频播放器实时播放时间
    // getCurrentTime(curTime) {
    //   this.captionCurTime = Number(curTime.toFixed(2))
    //   this.studyRecordQuery.my_study_progress = Math.floor(this.captionCurTime) // 视频当前播放时长
    // },
    // tabs 切换
    changeTabs(val) {
      this.activeKey = val
    },
    // 语言按钮
    initLang() {
      let that = this
      const btnEl = document.getElementById('drag-lang')
      if (btnEl) {
        let disY
        let height
        btnEl.addEventListener('touchstart', function (e) {
          e.preventDefault()
          height = e.view.innerHeight - 40
          disY = e.touches[0].clientY - this.offsetTop
        })
        btnEl.addEventListener('touchmove', function (e) {
          that.isMoved = true
          let top = e.touches[0].clientY - disY
          if (top >= 0 && top <= height) {
            btnEl.style.top = top + 'px'
          }
        })
        btnEl.addEventListener('touchend', function (e) {
          // 判断是否有拖动，有则初始化为false，无则为点击
          if (that.isMoved) {
            that.isMoved = false
          } else {
            // 双语切换
            let lang = that.moocLang === 'en-us' ? 'zh-cn' : 'en-us'
            that.$store.commit('setMoocLang', lang)
            that.getMobileLangJS()
            window.parent &&
              window.parent.postMessage({
                lang,
                type: 'changeLang'
              })
          }
        })
      }
    },
    // 插入浏览器历史
    // pushHistory(title = 'title', url = '#') {
    //   let state = {
    //     title,
    //     url
    //   }
    //   window.history.pushState(state, state.title, state.url)
    // },
    // 销毁时
    beforeunloadEvent() {
      if (this.studyRecordQuery.total_study_time) {
        this.handleViewGraphicRecord()
        // let param = {
        //   ...this.studyRecordQuery,
        //   is_archive: true
        // }
        // let blob = new Blob([JSON.stringify(param)], { type: 'application/json; charset=UTF-8' })
        // navigator.sendBeacon('/training/api/netcourse/user/courseinfo/add-study-record', blob) // TODO: 上报接口 需要更换
      }
    },
    // 如果之前熄屏了，则回到熄屏之前的播放时间
    visibilitychange() {
      if (document.hidden) {
        this.handleViewGraphicRecord()
        this.iframeDom && this.iframeDom.contentWindow.postMessage('video:pause', '*')
        MoocJs.pause()
        this.isPageHidden = true
      } else {
        this.isPageHidden = false
      }
    }
  }
}
</script>

<style lang="less" scoped>
.video-container {
  overflow: hidden;
  .main-fixed {
    position: fixed;
    z-index: 99;
    width: 100%;
  }
  .main-content {
    position: relative;
    z-index: 9;
    margin-top: 255px;
    height: calc(100vh - 255px);
    background-color: #fff;
    .comment {
      background: #fff;
      padding-top: 10px;
    }
  }
  .fixed-main-content {
    position: fixed;
    width: 100%;
    top: 255px;
    margin-top: unset;
    height: calc(100% - 255px);
  }
  .play-video {
    height: 210px;
    position: relative;
    .video-card {
      width: 100vw;
      background-color: #f6f7f9;
    }
  }
  .video-box,
  .video-card {
    height: 210px;
  }
  .van-image {
    width: 100%;
    height: 100%;
  }
  .tabs-card {
    position: relative;
    box-shadow: 0 2px 12px 0 rgb(0 0 0 / 10%);
    .conceal {
      width: 0;
      height: 38px;
      background: #fff;
      position: absolute;
      top: 0;
      right: 0;
      z-index: 1;
    }
  }
  :deep(.van-tabs--line) {
    .van-tabs__wrap {
      height: 38px;
    }
    .van-tab--active {
      font-weight: bold;
    }
    .van-tabs__line {
      width: 28px;
    }
  }
  .loading-text {
    height: 80px;
    line-height: 80px;
    text-align: center;
    color: #00000066;
  }
  .video-learn-empty {
    padding-top: 20px;
    text-align: center;
    .empty-text {
      margin: 16px 0 20px;
    }
  }
  .drag-lang {
    width: 60px;
    height: 40px;
    padding-left: 8px;
    background: #fff;
    border: 1px solid #dcdcdc;
    border-radius: 20px 0 0 20px;
    border-right-color: transparent;
    box-shadow: 0 0 24px 0 #dcdcdc99;
    position: fixed;
    top: 53%;
    right: 0;
    z-index: 99;
    font-size: 10px;
    display: flex;
    align-items: center;
    user-select: none;
    .el-icon-en {
      background: url('~@/assets/img/english.png') no-repeat center / cover;
    }
    .el-icon-zh {
      background: url('~@/assets/img/china.png') no-repeat center / cover;
    }
    .icon {
      width: 20px;
      height: 20px;
      margin-right: 4px;
      display: inline-block;
    }
  }
  .lang-icon-vertical {
    top: 16px;
    right: 196px;
    z-index: 9999;
    padding: 0;
    width: 50px;
    height: 50px;
    flex-shrink: 0;
    border-radius: 36px;
    background: #fff;
    box-shadow: 0 0 12px 0 #99999999;
    text-align: center;
    .icon {
      margin: 0;
      width: 24px;
      height: 23px;
      transform: translateX(12px) rotate(90deg);
    }
    .text {
      display: none;
    }
  }
}
</style>
