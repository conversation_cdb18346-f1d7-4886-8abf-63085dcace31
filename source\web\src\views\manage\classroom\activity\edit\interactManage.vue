<template>
  <div class="interactive-manage-card">
    <div class="activity-title">活动名称：<span>{{ activityInfo.activity_name || '-' }}</span></div>
    
    <div class="extand-box mt-28">
      <div class="extand-title">
        <span class="title">课前学习</span>
        <p class="tip">此处关联的内容将在详情页中显示</p> 
        <span class="addExtandItem" @click="addNewCourse('front')">+ 新增内容</span>
      </div>

      <div class="extand-table">
        <el-table :data="frontRelationContents" max-height="216px" style="width: 100%">
          <el-table-column prop="relation_content_name" label="内容标题" width="706" show-overflow-tooltip>
            <template slot-scope="scope" v-if="scope">
              <span :class="['tags', getModuleClass(scope.row).className]">{{ getModuleClass(scope.row).typeName }}</span><span>{{ scope.row.relation_content_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="right">
            <template slot-scope="scope">
              <div class="icon-btns">
                <i class="icon-up" @click="handleUp(scope.row, scope.$index, 'front')"></i>
                <i class="icon-delete" @click="handleDelete(scope.row, scope.$index, 'front')"></i>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <div class="extand-box mt-24">
      <div class="extand-title">
        <span class="title">课后学习</span>
        <p class="tip">此处关联的内容将在详情页中显示</p> 
        <span class="addExtandItem" @click="addNewCourse('after')">+ 新增内容</span>
      </div>

      <div class="extand-table">
        <el-table :data="backRelationContents" max-height="216px" style="width: 100%">
          <el-table-column prop="relation_content_name" label="内容标题" width="706" show-overflow-tooltip>
            <template slot-scope="scope" v-if="scope">
              <span :class="['tags', getModuleClass(scope.row).className]">{{ getModuleClass(scope.row).typeName }}</span><span>{{ scope.row.relation_content_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="right">
            <template slot-scope="scope">
              <div class="icon-btns">
                <i class="icon-up" @click="handleUp(scope.row, scope.$index, 'back')"></i>
                <i class="icon-delete" @click="handleDelete(scope.row, scope.$index, 'back')"></i>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 添加课程 -->
     <!-- 添加课程 -->
    <addActivityCourseDialog
      v-if="addLineCourseShow"
      ref="addLineCourse"
      :visible.sync="addLineCourseShow"
      :extendOptions="extendOptions"
      :addNewCourseType="addNewCourseType"
      @handleShowSetDialog="handleShowSetDialog"
    />
    <!-- <AddCourseDialog
      v-if="addLineCourseShow"
      ref="addLineCourse"
      :visible.sync="addLineCourseShow"
      :extendOptions="extendOptions"
      @handleShowSetDialog="handleShowSetDialog"
    /> -->
  </div>
</template>

<script>
import { qlearningModuleTypes } from 'utils/constant'
// import {
//   AddCourseDialog
// } from '@/views/manage/mooc/project-manage/task-list/component/index.js'
import addActivityCourseDialog from '../components/addActivityCourseDialog.vue'
import { addCourseApi, deleteCourseApi, getCourseListApi } from '@/config/classroom.api.conf.js'
import { mapState, mapMutations } from 'vuex'
import { actTypes } from 'utils/map'
export default {
  name: 'interactive-manage',
  components: {
    // AddCourseDialog
    addActivityCourseDialog
  },
  computed: {
    ...mapState({
      activityInfo: state => state.activity.activityInfo
    })
  },
  data() {
    return {
      addLineCourseShow: false,
      addNewCourseType: '', // 1课前 2课后
      frontRelationContents: [], // 课前关联内容
      backRelationContents: [], // 课后关联内容
      extendOptions: {
        showAddOutLink: true,
        banSameCourse: true,
        sameCourseList: []
      }
    }
  },
  watch: {
    activityInfo: {
      handler(newVal) {
        if (newVal) {
          this.frontRelationContents = newVal.front_relation_contents || []
          this.backRelationContents = newVal.back_relation_contents || []
          
          // 当 activityInfo 变化时更新禁止列表
          this.$nextTick(() => {
            this.updateSameCourseList()
          })
        }
      },
      immediate: true,
      deep: true
    }
  },
  created() { },
  mounted() {
    // 初始化禁止选择的课程列表
    this.updateSameCourseList()
  },
  beforeDestroy() { },
  methods: {
    ...mapMutations({
      setActivityInfo: 'activity/SET_ACTIVITY_INFO'
    }),
    getCourseList() {
      getCourseListApi({
        activity_id: this.activityInfo.activity_id
      }).then(res => {
        // 更新 Vuex 中的数据
        const frontContents = res.filter(item => item.relation_usage_type === 1)
        const backContents = res.filter(item => item.relation_usage_type === 2)
        
        this.setActivityInfo({
          ...this.activityInfo,
          front_relation_contents: frontContents,
          back_relation_contents: backContents
        })
        
        // 更新本地数据
        this.frontRelationContents = frontContents
        this.backRelationContents = backContents
        
        // 更新禁止选择的课程列表
        this.updateSameCourseList()
      })
    },
    addNewCourse(type) {
      this.addNewCourseType = type
      
      // 在打开对话框前更新禁止选择的课程列表
      this.updateSameCourseList()
      
      this.addLineCourseShow = true
    },
    getModuleClass(data) {
      const { relation_module_id = '', relation_act_type = '' } = data
      const name = actTypes.find(item => item.act_type === relation_act_type)?.act_type_name || ''
      if (relation_module_id === 99) {
        return {
          className: 'tag-link',
          typeName: name
        }
      }
      if (!relation_module_id) {
        return {
          className: 'tag-net',
          typeName: '综合'
        }
      }
      let cardType = qlearningModuleTypes.find((item) => relation_module_id === item.module_id)
      if (cardType) {
        return { 
          className: cardType.moduleClassName,
          typeName: name
        }
      } else {
        return {
          className: 'tag-net',
          typeName: name
        }
      }
    },
    // 置顶
    handleUp(row, index, type) {
      if (type === 'front') {
        this.frontRelationContents.unshift(this.frontRelationContents.splice(index, 1)[0])
        // 更新后端数据
        this.updateCourseOrder('1')
      } else {
        this.backRelationContents.unshift(this.backRelationContents.splice(index, 1)[0])
        // 更新后端数据
        this.updateCourseOrder('2')
      }
    },
    // 更新课程顺序
    updateCourseOrder(type) {
      let contents = type === '1' ? this.frontRelationContents : this.backRelationContents
      
      let params = {
        activity_id: this.activityInfo.activity_id,
        relation_usage_type: type,
        relation_contents: contents
      }
      
      addCourseApi(params).then(res => {
        this.$message.success('顺序更新成功')
        
        // 更新 Vuex 中的数据
        this.setActivityInfo({
          ...this.activityInfo,
          front_relation_contents: [...this.frontRelationContents],
          back_relation_contents: [...this.backRelationContents]
        })
      })
    },
    // 删除
    handleDelete(row, index, type) {
      this.$confirm('确定要删除该内容吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteCourse({
          id: row.id
        }, type, index)
      }).catch(() => {})
    },
    handleShowSetDialog(data) {
      let newData = []
      newData = data.map(item => {
        return {
          relation_act_type: item.act_type || '',
          relation_module_id: item.module_id || '',
          relation_act_id: item.act_id || '',
          relation_content_url: item.resource_url || '',
          relation_content_name: item.content_name || '',
          relation_cover_img_url: item.photo_url || '',
          relation_view_count: item.view_count_total || '',
          relation_avg_score: item.avg_score || '',
          relation_created_time: item.created_at || '',
          relation_content_module_name: item.module_name || '',
          relation_content_resource_type: item.relation_content_resource_type || '',
          wechat_mini_appid: item.wechat_mini_appid || ''
        }
      })
      
      if (this.addNewCourseType === 'front') {
        this.addCourse(newData, '1')
      } else {
        this.addCourse(newData, '2')
      }
    },
    addCourse(data, type) {
      // 1 课前 2 课后
      // 合并现有课程和新课程
      let allContents = []
      if (type === '1') {
        // 合并课前学习内容
        allContents = [...this.frontRelationContents, ...data]
      } else {
        // 合并课后学习内容
        allContents = [...this.backRelationContents, ...data]
      }
      
      let params = {
        activity_id: this.activityInfo.activity_id,
        relation_usage_type: type,
        relation_contents: allContents
      }
      
      addCourseApi(params).then(res => {
        this.$message.success('添加成功')
        
        // 更新本地数据
        if (type === '1') {
          this.frontRelationContents = [...this.frontRelationContents, ...data]
        } else {
          this.backRelationContents = [...this.backRelationContents, ...data]
        }
        
        // 更新 Vuex 中的数据
        this.setActivityInfo({
          ...this.activityInfo,
          front_relation_contents: [...this.frontRelationContents],
          back_relation_contents: [...this.backRelationContents]
        })
        
        // 更新禁止选择的课程列表
        this.updateSameCourseList()
      })
    },
    // 更新禁止选择的课程列表
    updateSameCourseList() {
      this.extendOptions.sameCourseList = [...this.frontRelationContents, ...this.backRelationContents].map(v => {
        return {
          item_id: String(v.relation_act_id),
          act_type: String(v.relation_act_type)
        }
      })
      
      // 打印检查格式是否正确
      console.log('更新禁止选择列表:', this.extendOptions.sameCourseList)
    },
    deleteCourse(data, type, index) {
      deleteCourseApi(data).then(res => {
        this.$message.success('删除成功')
        // 从本地数组中删除
        if (type === 'front') {
          this.frontRelationContents.splice(index, 1)
        } else {
          this.backRelationContents.splice(index, 1)
        }
        // 更新 Vuex 中的数据
        this.setActivityInfo({
          ...this.activityInfo,
          front_relation_contents: [...this.frontRelationContents],
          back_relation_contents: [...this.backRelationContents]
        })
      })
    }
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/activity-common.less';

.interactive-manage-card {
  width: 100%;
  height: 100%;
  background-color: #fff;
  overflow-y: auto;
  padding-bottom: 20px;
  .activity-title {
    padding: 32px 28px 20px;
    color: #00000099;
    font-size: 14px;
    line-height: 22px;
    border-bottom: 1px solid #EEEEEE;
    & > span {
      color: #000000e6;
    }
  }
  .extand-box {
    padding: 0 28px;
    .extand-title {
      display: flex;
      align-items: center;
      padding-right: 16px;
      margin-bottom: 8px;

      .title {
      }

      .tip {
        color: #00000099;
        font-size: 14px;
        margin-left: 12px;
      }

      .addExtandItem {
        color: #0052D9;
        font-size: 14px;
        cursor: pointer;
        margin: 0 0 0 auto;
      }
    }
  }
  .extand-table {
    padding: 0 0 0 58px;
    margin: 16px 0 0 0;
    .operat-btn-box {
      i {
        & + i {
          margin-left: 20px;
        }
        font-size: 16px;
        width: 16px;
        height: 16px;
        display: inline-block;
        font-size: 16px;
        cursor: pointer;
        margin: 0 8px;
      }
      .view-icon {
        color: #989898;
        &:hover {
          color: #0052d9;
        }
      }

      .edit-icon {
        background: url('~@/assets/img/edit-allow.png') no-repeat center/cover;
        &:hover {
          background: url('~@/assets/img/edit-icon-hover.png') no-repeat center/cover;
        }
      }

      .icon-delete {
        background: url('~@/assets/img/icon-delete.png') no-repeat center / cover;
        &:hover {
          background: url('~@/assets/img/del-active.png') no-repeat center / cover;
        }
      }
      .ban {
        cursor: not-allowed;
        pointer-events: none;
        &.edit-icon {
          background: url('~@/assets/img/edit.png') no-repeat center/cover !important;
        }
        &.icon-delete {
          background:  url('~@/assets/img/del-1.png') no-repeat center/cover !important;
        }
      }
    }
    .tags {
      margin: 0 12px 0 10px;
      display: inline-block;
      height: 18px;
      padding: 0 7px;
      font-size: 12px;
      font-weight: 400;
      line-height: 18px;
      border: 1px solid;
      border-radius: 4px;
      &.tag-net {
          color:#0b8bff;
      }
      &.tag-music {
          color:#04aef6;
      }
      &.tag-live {
          color:#fe5d34;
      }
      &.tag-article {
          color:#42c55b;
      }
      &.tag-note {
          color:#40c19d;
      }
      &.tag-exam {
          color:#ff6600;
      }
      &.tag-hangjia {
          color:#ff8900;
      }
      &.tag-face {
          color:#ffa31a;
      }
      &.tag-activity {
          color:#ffbc03;
      }
      &.tag-marker {
          color:#00a99d;
      }
      &.tag-word{
          color: #0052D9;
      }
      &.tag-link {
          color:#a65ad4;
      }
    }
    :deep(.el-table) {
      border-left: 1px solid #EBEEF5;
      border-right: 1px solid #EBEEF5;
      th {
        background: rgba(245, 245, 245, 1);
        font-weight: 500;
        border-bottom: solid 1px #eeeeee;
      }
      .el-table__header {
        background: #EFEFEF;
        height: 52px;
        .cell {
          padding: 0 16px;
        }
      }

      th,
      td {
        height: 36px;
        padding: unset;
        color: #000000e6;

      }

      td {
        color: #000000e6;
      }
    }

    .icon-btns {
      i {
        font-size: 16px;
        width: 16px;
        height: 16px;
        display: inline-block;
        font-size: 16px;
        cursor: pointer;
      }

      i:first-child {
        margin-right: 8px;
      }

      .icon-up {
        background: url('~@/assets/img/icon-up.png') no-repeat center / cover;
      }

      .icon-delete {
        background: url('~@/assets/img/icon-delete.png') no-repeat center / cover;
      }
    }

    :deep(.el-table--scrollable-x .el-table__body-wrapper) {
      overflow-x: hidden;
    }
  }
}
</style>
