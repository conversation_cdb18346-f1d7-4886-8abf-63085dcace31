<template>
  <div class="task-detail-container" v-if="showTaskContainer">
    <div class="task-navbar" v-if="showNavBar">
      <div class="task-navbar-inner">
        <div class="left-content">
          <div class="left-item course-item">
            <span
              class="name course-name"
              :title="courseProcess.mooc_course_name"
              >{{ courseProcess.mooc_course_name || geekCouresInfo.course_title || ''}}</span
            >
            <span class="back-text" @click="backProjectDetail">{{
              $langue('Mooc_TaskDetail_Navigation_ReturnProjectHome', {
                defaultText: '返回项目首页'
              })
            }}</span>
          </div>
          <div class="line"></div>
          <div class="left-item task-item">
            <span class="task-name">
              <span
                v-if="projectInfo.task_name"
                class="label"
                :class="[projectInfo.required ? 'require' : 'norequire']"
              >
                {{
                  projectInfo.required
                    ? $langue('Mooc_ProjectDetail_TaskList_RequiredTask', {
                        defaultText: '应学'
                      })
                    : $langue('Mooc_ProjectDetail_TaskList_ElectiveTask', {
                        defaultText: '选学'
                      })
                }}
              </span>
              <span class="name" :title="projectInfo.task_name">{{
                projectInfo.task_name
              }}</span>
              <el-popover
                class="mobile-popove"
                popper-class="mobile-popove-box"
                placement="bottom"
                trigger="hover"
                @show="getMobileUrl"
              >
                <p class="task-name">{{ projectInfo.task_name }}</p>
                <img v-if="qrUrl" :src="qrUrl" />
                <p>
                  {{
                    $langue('Mooc_Common_Authority_ViewByWechat', {
                      defaultText: '请使用微信/企业微信扫码查看任务'
                    })
                  }}
                </p>
                <el-input
                  v-model="urlMobile"
                  type="text"
                  disabled
                  class="copy-btn"
                >
                  <template slot="append">
                    <el-button @click="doCopy()">{{
                      $langue('Mooc_ProjectDetail_Copy', {
                        defaultText: '复制'
                      })
                    }}</el-button>
                  </template>
                </el-input>
                <span slot="reference" class="mobile-box"
                  ><span class="mobile-icon"></span
                ></span>
              </el-popover>
            </span>
            <span class="finished-status-time">
              <span v-if="!['22', '23'].includes(projectInfo.act_type)">
                <span class="task-status" v-if="taskFinishedState.is_finished">
                  <span class="done-icon"></span>
                  <span class="done-text">{{
                    $langue('Mooc_ProjectDetail_TaskList_TaskFinished', {
                      defaultText: '任务已完成'
                    })
                  }}</span>
                  <span
                    >{{
                      $langue(
                        'Mooc_ProjectDetail_TrainingProgress_FinishedTime',
                        { defaultText: '完成时间：' }
                      )
                    }}{{ taskFinishedState.finished_time || '-' }}</span
                  >
                </span>
                <span class="task-status" v-else>
                  <span>{{
                    $langue('Mooc_TaskDetail_Navigation_TaskFinishCondition', {
                      defaultText: '任务完成条件：'
                    })
                  }}</span>
                  <span class="finished-condition">{{
                    projectInfo.conditionText
                  }}</span>
                  <span
                    class="learning-time"
                    v-show="projectInfo.conditionType * 1 === 2"
                  >
                    <span>{{
                      $langue('Mooc_TaskDetail_AlreadyLearned', {
                        defaultText: '已学习：'
                      })
                    }}</span>
                    <span class="learning-time-linehight">{{
                      learningTime
                    }}</span>
                  </span>
                </span>
              </span>
              <span class="isPreview-box" v-if="isPreview">
                <i class="el-icon-warning-outline"></i>
                <span class="tips">项目预览中，不会实际变更任务状态</span>
              </span>
            </span>
          </div>
        </div>
        <div class="right-btn">
          <el-button
            size="mini"
            v-if="projectInfo.task_desc"
            @click="viewTaskDesc"
            >{{
              $langue('Mooc_TaskDetail_Navigation_ViewTaskDesc', {
                defaultText: '查看任务简介'
              })
            }}</el-button
          >
          <el-button size="mini" @click="handleTaskList">{{
            $langue('Mooc_TaskDetail_Navigation_TaskList', {
              defaultText: '任务列表'
            })
          }}</el-button>
          <el-button size="mini" @click="previous" v-if="prevTaskBtnShow">{{
            $langue('Mooc_TaskDetail_Navigation_PreTask', {
              defaultText: '上个任务'
            })
          }}</el-button>
          <el-button
            size="mini"
            type="primary"
            @click="next"
            v-if="nextTaskBtnShow"
            >{{
              $langue('Mooc_TaskDetail_Navigation_NextTask', {
                defaultText: '下个任务'
              })
            }}</el-button
          >
          <!-- 双语暂时注释 -->
          <el-button size="mini" @click="handleChangelang" plain>
            <span class="more-lang-btn">
              <i
                :class="[
                  moocLang === 'en-us' ? 'el-icon-zh' : 'el-icon-en',
                  'icon'
                ]"
              ></i>
              {{ moocLang === 'en-us' ? '简体中文' : 'English' }}
              <i class="el-icon-sort"></i>
            </span>
          </el-button>
        </div>
      </div>
    </div>
    <div class="task-wrapper">
      <taskContent v-show="showIframe" :currentTask="projectInfo" ref="taskContent" @loading="onContentLoading" @receiveMessage="receiveMessage" :btnOptions="{prevTaskBtnShow, nextTaskBtnShow}"></taskContent>
      <taskList
        ref="taskList"
        :showTaskPanel.sync="isShowTaskPanel"
        :courseProcess="courseProcess"
        :geekInfo="taskGeekInfo"
        @getTaskList="getTaskList"
        @handleStudyTips="handleStudyTips"
        @isPageError="isPageError"
        @changeSourceTaskListShow="changeSourceTaskListShow"
        @getPerRes="getPerRes"
      ></taskList>
    </div>
    <el-dialog
      top="0"
      class="task-desc dialog-center-common"
      :title="
        $langue('Mooc_TaskDetail_Navigation_TaskDesc', {
          defaultText: '任务简介'
        })
      "
      :visible.sync="taskDescVisible"
    >
      <p>{{ projectInfo.task_name }}</p>
      <div>{{ projectInfo.task_desc }}</div>
      <span slot="footer" class="dialog-footer">
        <el-button
          type="primary"
          @click="taskDescVisible = false"
          size="small"
          >{{
            $langue('Mooc_ProjectDetail_Notice_IKnow', {
              defaultText: '知道了'
            })
          }}</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      top="0"
      center
      class="finish-project dialog-center-common"
      :visible.sync="finishProject.visible"
    >
      <img class="img" src="@/assets/mooc-img/done-fill.png" alt="" />
      <p style="margin-top: 12px; font-size: 16px">
        {{
          $langue('Mooc_ProjectDetail_Congratulations', {
            defaultText: '恭喜您完成培养项目！'
          })
        }}！
      </p>
      <div v-if="finishProject.enable_certificate" class="certificate-info">
        <div class="certificate-tips">
          {{
            $langue('Mooc_ProjectDetail_GetOneCertificate', {
              defaultText: '获得一张证书奖励'
            })
          }}
        </div>
        <div>{{ finishProject.certificate_name }}</div>
      </div>
      <span slot="footer" class="dialog-footer">
        <div v-if="finishProject.enable_certificate">
          <el-button @click="finishProject.visible = false" size="small">{{
            $langue('Mooc_ProjectDetail_Notice_IKnow', {
              defaultText: '知道了'
            })
          }}</el-button>
          <el-button type="primary" @click="viewCertificate" size="small">{{
            $langue('Mooc_ProjectDetail_ViewCertificate', {
              defaultText: '查看证书'
            })
          }}</el-button>
        </div>
        <el-button
          v-else
          type="primary"
          @click="finishProject.visible = false"
          size="small"
          >{{
            $langue('Mooc_ProjectDetail_Notice_IKnow', {
              defaultText: '知道了'
            })
          }}</el-button
        >
      </span>
    </el-dialog>

    <div v-if="finishedToast && moocLang !== 'en-us'" class="finished-Toast">
      <img src="@/assets/img/close.png" alt="" @click="closeTaskTips">
      <div class="done-tips">任务已完成</div>
      <div
        v-if="nextTaskBtnShow"
        class="next-task"
        @click="next"
        >点击前往下一任务</div
      >
    </div>

    <div v-if="finishedToast && moocLang === 'en-us'" class="finished-Toast">
      <img src="@/assets/img/close.png" alt="" @click="closeTaskTips">
      <div class="done-tips">Task completed</div>
      <div
        v-if="nextTaskBtnShow"
        class="next-task"
        @click="next"
        >Go to next one</div
      >
    </div>
    <!-- 极客 - 课程兑换弹窗 -->
    <redeemCoursesPopup v-if="isGeek && redeemCoursesShow" :visible.sync="redeemCoursesShow" :courseType="currentTask.mooc_resource_from" :purchaseData="geekPurchaseInfo" :status="redeemPopupStatus" :consume_appid="consume_appid" @handleRegistered="initPage" @handleClose="redeemPopupStatus = -1" @redeemCourses="purchaseCourses"></redeemCoursesPopup>
  </div>
  <!--页面状态处理 -->
  <taskErrorPage v-else :pageError="pageError" :autoSignUpParams="autoSignUpParams" @handleJoin="handleEnroll" />
</template>

<script>
// import { SDKTimer, SDKMultiTask } from './SDK'
import { SDKTimer, SDKMultiTask } from 'sdc-moocjs'
import {
  getCourseProcess,
  getTaskDetail,
  saveLearnRecord,
  getMobileQrcode,
  getGeekCourseDetail,
  getCoursePurchaseInfo,
  getTaskFinishedCondition,
  moocEnroll,
  getOutsourceLinkConfig,
  purchaseSourceFromConfig
} from '@/config/mooc.api.conf.js'
import taskContent from './taskContent.vue'
import taskList from './taskList.vue'
import taskErrorPage from '@/views/components/taskErrorPage.vue'
import redeemCoursesPopup from '../../outsourced-course/components/redeemCoursesPopup.vue'
import { certificateView } from '@/utils/tools.js'
import translate from '@/mixins/translate.vue'
import { mapState } from 'vuex'
export default {
  name: 'taskDetail',
  mixins: [translate],
  components: {
    taskList,
    taskContent,
    taskErrorPage,
    redeemCoursesPopup
  },
  data() {
    return {
      pageError: {
        noPermission: false,
        inaccessible: false,
        tips: '',
        isConcatAdmin: 0, // 0 隐藏管理员
        concatAdmins: [],
        timeText: '',
        register: false // 未报名
      },
      showNavBar: true,
      seconds: 0, // 当前任务本次学习时长
      totalStudyTime: 0, // 当前任务本次学习时间+已学习时长
      timer: null,
      hangUpTimer: null,
      courseProcess: {
        mooc_course_name: '',
        task_sum: 0,
        requiredProcess: '0/0',
        noRequireProcess: '0/0',
        learn_status: 0,
        unlocked_by_step: false, // 项目解锁模式：true 按顺序解锁/false自由模式
        enable_study_record_sync: true // true表示开启记录同步
      },
      taskTreeData: [], // 任务树（包括阶段、任务组）
      taskData: [], // 所有任务数据
      taskIndex: 0, // 当前任务在任务列表的位置
      prevTaskBtnShow: false, // 上个任务按钮是否显示
      nextTaskBtnShow: false, // 下个任务按钮是否显示
      taskRequireData: [], // 应学且未锁定的任务数据
      currentTask: {},
      learnRecordID: 0,
      taskFinishedState: {
        is_finished: false,
        finished_time: ''
      },
      taskDescVisible: false,
      isMultitasking: false, // 是否触发多任务同时学习提醒
      finishProject: {
        visible: false,
        enable_certificate: false, // false表示未开启证书，true表示开启证书
        certificate_id: '', // 证书编号
        certificate_name: '' // 证书名称
      },
      alreadyShowCertificate: false,
      logoSrc: require('@/assets/mooc-img/tencent-study.png'),
      qrUrl: '',
      interactiveDialog: false, // 视频互动弹窗
      langFlag: false,
      errNum: 0,
      finishedToast: false,
      showIframe: false, // 是否显示iframe内容 解决fix:跳转到课程详情页面时会先闪一下错误图片的问题
      contentLoading: false,
      geekCouresInfo: {}, // 极客信息
      geekPurchaseInfo: {}, // 极客课程购买信息
      redeemCoursesShow: false,
      redeemPopupStatus: -1,
      isPassTime: false, // 是否逾期
      isStarTime: false,
      sourceFromConfig: [],
      sourceTaskListShow: false, // 外链，任务列表单独隐藏
      consume_appid: '',
      autoSignUpParams: {
        register: false,
        resource_from: null,
        register_confirm: 1
      } // 自动报名参数
    }
  },
  watch: {
    showTaskContainer(val) {
      if (!val) {
        this.$sdc.loading.hide()
      }
    },
    isFrom(val) {
      if (val) {
        // this.getGeekBangDetail()
      }
    }
  },
  computed: {
    ...mapState(['moocLang', 'showTaskPanel']),
    isPreview() {
      return this.$route.query.previewType === 'preview'
    },
    showTaskContainer() {
      if (this.isPassTime) return false
      if (this.isStarTime) return false
      return !this.pageError.noPermission && !this.pageError.inaccessible
    },
    learningTime() {
      let time = (this.timer &&
        this.timer.formatSeconds(this.totalStudyTime)) || [0, 0, 0] // 传入用户输入的数据
      return time[0] * 1
        ? this.$langue('Mooc_ProjectDetail_TaskList_ViewTime1', {
          hour: time[0],
          minute: time[1],
          second: time[2],
          defaultText: `${time[0]}时${time[1]}分${time[2]}秒`
        })
        : this.$langue('Mooc_ProjectDetail_TaskList_ViewTime2', {
          minute: time[1],
          second: time[2],
          defaultText: `${time[1]}分${time[2]}秒`
        })
    },
    mooc_course_id() {
      return this.$route.query?.mooc_course_id || ''
    },
    task_id() {
      return this.$route.query?.task_id || ''
    },
    urlMobile() {
      // 小程序短码地址
      const url =
        process.env.NODE_ENV === 'production'
          ? 'https://sdc.qq.com/s/TZELHU'
          : 'http://s.test.yunassess.com/s/urrd9E'
      return `${url}?scheme_type=mooc&mooc_course_id=${this.mooc_course_id}&task_id=${this.task_id}&from=mooc`
    },
    projectInfo() {
      const info = {}
      if (this.currentTask.act_type === '20') {
        if (this.currentTask.resource_type === 'Exam') {
          info.conditionText = this.$langue(
            'Mooc_TaskDetail_Navigation_ExamPass',
            { defaultText: '通过考试' }
          )
        } else if (this.currentTask.resource_type === 'Practice') {
          info.conditionText = this.$langue(
            'Mooc_TaskDetail_Navigation_PracticePass',
            { defaultText: '完成练习' }
          )
        }
      } else if (this.currentTask.act_type === '32') {
        info.conditionText = this.$langue(
          'Mooc_TaskDetail_Navigation_FinishByCourse332',
          { defaultText: '完成问卷填写并提交' }
        )
      } else if (this.currentTask.conditionType * 1 === 1) {
        info.conditionText = this.$langue(
          'Mooc_TaskDetail_Navigation_FinishByCourse',
          { defaultText: '由课程完成条件决定' }
        )
      } else if (this.currentTask.conditionType * 1 === 2) {
        info.conditionText = this.$langue(
          'Mooc_TaskDetail_Navigation_MinStudyTime',
          {
            minute: this.currentTask.finished_condition?.condition || 0,
            defaultText: `至少学习${
              this.currentTask.finished_condition?.condition || 0
            }分钟`
          }
        )
      } else {
        info.conditionText = '-'
      }
      return {
        ...this.currentTask,
        ...info
      }
    },
    // 是否是外部课程
    isGeek() {
      return this.sourceFromConfig.includes(this.currentTask.mooc_resource_from)
    },
    // from是不是外部课程
    isFrom() {
      return this.$route.query?.from && this.sourceFromConfig.includes(this.$route.query?.from)
    },
    taskGeekInfo() {
      return {
        isGeek: this.isGeek,
        canPreview: this.geekCouresInfo.can_preview,
        courseAcquisitionType: this.geekCouresInfo.course_acquisition_type,
        purchased: this.geekCouresInfo.purchased,
        total: this.geekPurchaseInfo.allow_preview_num || 0,
        study: this.geekPurchaseInfo.previewed_num || 0,
        previewRecords: this.geekPurchaseInfo.preview_records || [],
        courseFrom: this.currentTask.mooc_resource_from
      }
    },
    isShowTaskPanel() {
      if (this.currentTask.act_type === '99') {
        return this.sourceTaskListShow
      } else {
        return this.showTaskPanel
      }
    }
  },
  created() {
    // this.purchaseSourceFromConfig()
    if (['geekBang', 'sanjieke'].includes(this.$route.query.from)) this.getGeekBangDetail()
  },
  mounted() {
    const lang = ['en-US', 'en-us'].includes(this.$route.query?.lang)
      ? 'en-us'
      : localStorage.getItem('sdc-sys-def-lang') || 'zh-cn'
    this.$store.commit('setMoocLang', lang || 'zh-cn')
    this.getLangJS()
    this.$sdc.loading()
    this.initTimer()
    this.initMultitasking()
  },
  beforeRouteUpdate(to, from, next) {
    // 离开当前页面重置任务列表筛选字段
    this.$store.commit('setTaskListFilter', {
      required: false,
      unFinished: false
    })
    let iframeDOM =
      this.$refs.taskContent && this.$refs.taskContent.getIframeDom()
    if (iframeDOM) iframeDOM.src = ''
    next()
  },
  beforeRouteLeave(to, from, next) {
    let iframeDOM =
      this.$refs.taskContent && this.$refs.taskContent.getIframeDom()
    if (iframeDOM) iframeDOM.src = ''
    next()
  },
  beforeDestroy() {
    this.timer.removeEvent()
    this.timer = null
    SDKMultiTask.removeStorageListener()
  },
  methods: {
    changeSourceTaskListShow(val) {
      this.sourceTaskListShow = val
    },
    isPageError(simpleRes, perRes, val) {
      this.isPassTime = false
      this.isStarTime = false
      let { have_auth_to_view, have_released, course_status, admin_list } = simpleRes
      let { start_time, register } = perRes
      // 管理员
      const adminList = (admin_list || []).map((e) => {
        const name = e.admin_name.split('(')[0]
        return {
          ...e,
          staff_name: e.admin_name,
          url: `//learn.woa.com/rhrc/photo/150/${name}.png`
        }
      })
      let tips = ''
      if (val === '已结束') {
        this.isPassTime = true // 已逾期
        if (register) {
          tips = this.$langue('Mooc_Common_Authority_NotStudyByDelay', { defaultText: '已逾期，无法进行任务学习。' })
          this.pageError = {
            inaccessible: true,
            noPermission: false,
            tips,
            isConcatAdmin: adminList.length ? 2 : 0, // 0-隐藏管理员
            concatAdmins: adminList,
            register,
            isShowBack: false
          }
        } else {
          tips = this.$langue('Mooc_ProjectDetail_BasicInfo_ProjectEnded', { defaultText: '培养项目已结束，无法访问。' })
          this.pageError = {
            inaccessible: true,
            noPermission: false,
            tips,
            isConcatAdmin: adminList.length ? 1 : 0, // 0-隐藏管理员
            concatAdmins: adminList,
            register,
            isShowBack: false
          }
        }
        return
      }
      if (val === '未开始') {
        this.isStarTime = true
        tips = this.$langue('Mooc_Common_Authority_NotStudyByTime', { defaultText: '项目未开始，无法进行任务学习' })
        this.pageError = {
          inaccessible: true,
          noPermission: false,
          tips,
          isConcatAdmin: 0, // 0-隐藏管理员
          concatAdmins: [],
          register,
          isShowBack: true,
          timeText: start_time
            ? `${this.$langue('Mooc_Common_Authority_StartTime', {
              defaultText: '开始时间'
            })}：${start_time}`
            : ''
        }
        return
      }
      if (simpleRes.course_status === 2) {
        tips = this.$langue('Mooc_ProjectDetail_BasicInfo_ProjectEnded', { defaultText: '培养项目已结束，无法访问。' })
      } else if (simpleRes.course_status === 3) {
        tips = this.$langue('Mooc_Common_Alert_CourseDestoryTips', { defaultText: '培养项目已下架，无法访问。' })
      } else {
        tips = !have_released ? this.$langue('Mooc_ProjectDetail_BasicInfo_ProjectUnPublished', { defaultText: '培养项目暂未发布，无法访问。' }) : !have_auth_to_view ? this.$langue('Mooc_ProjectDetail_BasicInfo_ProjectNoArthority', { defaultText: '您暂时没有访问该项目的权限！' }) : ''
      }
      this.pageError = {
        inaccessible: !have_released || course_status === 2,
        noPermission: !have_auth_to_view,
        tips,
        isConcatAdmin: adminList.length ? 1 : 0, // 0-隐藏管理员
        concatAdmins: adminList,
        register
      }
    },
    getPerRes(perRes) {
      console.log(perRes, 'perRes--')
      const { register, resource_from, register_confirm } = perRes
      this.autoSignUpParams = {
        register,
        resource_from,
        register_confirm
      }
    },
    async purchaseSourceFromConfig() {
      try {
        const res = await purchaseSourceFromConfig()
        this.sourceFromConfig = res || []
        this.getTaskDetail()
      } catch (error) {
        this.getTaskDetail()
      }
    },
    receiveMessage (data) {
      const { params } = data
      if (!params.methodsType) return
      switch (params.methodsType) {
        case 'back':
          this.backProjectDetail()
          break
        case 'nextTask':
          this.next()
          break
        case 'prevTask':
          this.previous()
          break
        default:
          this.$message.error('未知错误')
          break
      }
    },
    // 特殊课程链接
    async getOutsourceLinkConfig() {
      const res = await getOutsourceLinkConfig({ resourceConfig: this.currentTask.mooc_resource_from })
      // activity_page_link 活动落地页 rule_page_link 活动规则 ；special_page_link 外部好课专区
      this.outsourceLink = res
    },
    // 报名
    handleEnroll() {
      this.$messageBox.confirm(this.$langue('Mooc_ProjectDetail_BasicInfo_SureRegist', { defaultText: '报名后将加入项目进行学习，请留意项目的学习起止时间，是否确定报名？' }), this.$langue('Mooc_ProjectDetail_BasicInfo_RegistProj', { defaultText: '报名加入项目' }), {
        confirmButtonText: this.$langue('Mooc_Common_Sure', { defaultText: '确定' }),
        cancelButtonText: this.$langue('Mooc_Common_Alert_Cancel', { defaultText: '取消' })
      }).then(() => {
        const params = {
          mooc_course_id: this.mooc_course_id,
          join_type: '3'
        }
        moocEnroll(params).then((res) => {
          this.$message.success(this.$langue('Mooc_ProjectDetail_BasicInfo_RegistSucessed', { defaultText: '报名成功' }))
          window.location.reload()
        }).catch(res => {
          if (res?.title) {
            const message = res.code && res.code !== 200 ? (res.message || res.data) : '网络异常，请稍后重试！'
            this.$message.error(message)
          }
        })
      })
    },
    closeTaskTips () {
      this.finishedToast = false
    },
    initTimer() {
      let that = this
      const events = {
        // 学习时长回调函数
        getDurationSeconds: (seconds, durtation) => {
          // console.log(seconds, durtation, durtation % 15, durtation % 15 === 0, 'seconds')
          // 本次学习时长
          this.seconds = seconds
          this.totalStudyTime =
            this.seconds + (this.currentTask.total_study_time || 0)
          let leastLearnMinute =
            this.currentTask?.finished_condition?.condition || 0

          // 已完成任务不会再更新完成时间
          // 先判断当前任务是否已经完成，再判断完成条件是至少学习多少分钟，然后判断当前学习时间大于完成条件
          // finished_condition.type: 1 由课程完成条件决定、2 至少学习XX分钟，类型：字符串（兼容number类型）
          let leastLearnCondition =
            this.currentTask.finished_condition?.type * 1 === 2
          if (
            !this.taskFinishedState.is_finished &&
            leastLearnCondition &&
            this.totalStudyTime >= leastLearnMinute * 60
          ) {
            this.taskFinishedState = {
              is_finished: true,
              finished_time: this.$moment().format('YYYY-MM-DD HH:mm:ss')
            }
            this.handleSaveLearnRecord(true, {}, true)
          } else if (durtation % 15 === 0) {
            // is_auto_report为true表示定时上报
            this.handleSaveLearnRecord(false, { is_auto_report: true })
          }

          // durtation 本轮定时器持续时间，定时器有暂停重新开启，则会清空（用于定时上报学习记录）
        },
        // 防挂机回调函数
        hangUpTimerCalback(e) {
          // 通知第三方暂停视频播放(音视频不触发防挂机，不需要调暂停播放方法)
          // that.$refs.taskContent.setVideoPause()
          that.$messageBox
            .confirm(
              that.$langue('Mooc_Common_Alert_TriggerAntiHangUp', {
                defaultText:
                  '您已触发防挂机验证，已自动停止当前任务的学习。如需继续学习，请点击“继续学习”按钮重新进入任务页面。'
              }),
              that.$langue('Mooc_Common_Alert_AntiHangUp', {
                defaultText: '防挂机学习提醒'
              }),
              {
                confirmButtonText: that.$langue(
                  'Mooc_Common_Alert_ContinueStudy',
                  { defaultText: '继续学习' }
                ),
                cancelButtonText: that.$langue(
                  'Mooc_Common_Alert_ReturnProjectDetail',
                  { defaultText: '返回项目详情页' }
                )
              }
            )
            .then(() => {
              // 音视频不触发防挂机，不需要调播放方法
              // that.$refs.taskContent.setVideoPlay()
              console.log(1, '计时')
              that.startTime()
            })
            .catch(() => {
              that.backProjectDetail()
            })
        }
      }
      this.timer = new SDKTimer({
        // limtHangUpTime: 120, // 防挂机2分钟
        events: events
      })
    },
    handleSaveLearnRecord(flag, data, firstDone = false) {
      const { previewType } = this.$route.query
      // 预览不做上报
      if (previewType === 'preview') {
        return
      }
      // flag: true表示上报以后需要更新列表数据
      let params = {
        id: this.learnRecordID,
        task_id: this.task_id,
        mooc_course_id: this.mooc_course_id,
        is_finished: flag,
        act_id: this.currentTask.act_id,
        act_type: this.currentTask.act_type,
        elapsed_seconds: this.seconds,
        source: 'MOOC',
        is_auto_report: false
      }
      params = Object.assign(params, data)
      saveLearnRecord(params)
        .then((res) => {
          if (flag) {
            this.getCourseProcess() // 学习完，更新应学进度
            this.learnRecordID = 0
            // 更新任务列表、任务详情状态
            this.$refs.taskList.getTaskData().then(() => {
              // 在任务详情列表获取到列表数据之后调用筛选过滤方法
              this.$refs.taskList.setFilterRef()
            })
            // this.$message.success({
            //   message: this.$langue(
            //     'Mooc_ProjectDetail_TaskList_TaskFinished',
            //     { defaultText: '任务已完成' }
            //   ),
            //   customClass: 'finished-tips'
            // })
          } else {
            this.learnRecordID = res.record_id || 0
          }
          if (
            res.all_finished &&
            !this.alreadyShowCertificate &&
            this.currentTask.required
          ) {
            // 表示完成培养项目（最后一个应学任务完成）
            this.finishProject = {
              visible: true,
              certificate_name: res.certificate_name,
              certificate_id: res.certificate_id,
              enable_certificate: res.enable_grant_certificate
            }
            this.alreadyShowCertificate = true
          } else {
            // 没有全部完成，才显示任务完成提醒
            if (flag && firstDone) {
              // 应学任务，并且不是文章类型才提示任务完成条件
              if (this.currentTask.required && this.currentTask.resource_type !== 'Article') {
                this.finishedToast = true
              }
              let finishedToastTimer = setTimeout(() => {
                this.finishedToast = false
              }, 600 * 1000)
              this.$once('hook:beforeDestory', () => {
                clearTimeout(finishedToastTimer)
              })
            }
          }
          this.errNum = 0
        })
        .catch((err) => {
          // if (err.message.indexOf('Network Error') > -1) {
          //   this.errNum++
          //   if (this.errNum >= 4) {
          //     this.pauseTime()
          //     // 通知第三方视频暂停
          //     this.$refs.taskContent.setVideoPause()
          //     this.$messageBox.confirm('当前服务异常，无法正常上报学习记录，请稍后刷新页面重试。如要继续学习，可能会丢失学习进度。', '服务异常提醒', {
          //       confirmButtonText: this.$t('Mooc_Common_Alert_RefreshPage'),
          //       cancelButtonText: this.$t('Mooc_ProjectDetail_TrainingProgress_ContinueStudy')
          //     }).then(() => {
          //       location.reload()
          //     }).catch(() => {
          //       if (!this.interactiveDialog) {
          //         this.$refs.taskContent.setVideoPlay()
          //         this.startTime()
          //       }
          //       this.errNum = 0
          //     })
          //   }
          // }
          if (
            err.message.indexOf('timeout') > -1 ||
            err.message.indexOf('Network Error') > -1
          ) {
            this.errNum++
            if (this.errNum >= 6) {
              this.pauseTime()
              // 通知第三方视频暂停
              this.$refs.taskContent.setVideoPause()
              this.$messageBox
                .confirm(
                  this.$langue('Mooc_Common_Alert_NetworkErrorWarm', {
                    defaultText:
                      '检测到网络异常，无法上报学习记录，请检查设备的网络连接状况，并在网络恢复正常稳定后刷新页面。如要继续学习，可能会丢失学习进度。'
                  }),
                  this.$langue('Mooc_Common_Alert_NetworkError', {
                    defaultText: '网络异常提醒'
                  }),
                  {
                    confirmButtonText: this.$t('Mooc_Common_Alert_RefreshPage'),
                    cancelButtonText: this.$t(
                      'Mooc_ProjectDetail_TrainingProgress_ContinueStudy'
                    )
                  }
                )
                .then(() => {
                  location.reload()
                })
                .catch(() => {
                  if (!this.interactiveDialog) {
                    this.$refs.taskContent.setVideoPlay()
                    console.log(2, '计时')
                    this.startTime()
                  }
                  this.errNum = 0
                })
            }
          }
          if (err.code === -14001 || err.code === -14002) {
            this.pauseTime()
            this.isMultitasking = true
            // 通知第三方视频暂停
            this.$refs.taskContent.setVideoPause()
            this.$messageBox
              .confirm(
                this.$langue('Mooc_Common_Alert_NotSupportMultipleTasks', {
                  defaultText:
                    '培养项目不支持多任务同时学习，系统检测到您正在学习同一项目下的其他任务，已自动停止当前任务的学习。如需继续学习，请点击“继续学习”按钮重新进入任务页面。'
                }),
                this.$langue('Mooc_Common_Alert_MultipleTasksRemind', {
                  defaultText: '多任务同时学习提醒'
                }),
                {
                  confirmButtonText: this.$langue(
                    'Mooc_ProjectDetail_TrainingProgress_ContinueStudy',
                    { defaultText: '继续学习' }
                  ),
                  cancelButtonText: this.$langue(
                    'Mooc_Common_Alert_ReturnProjectDetail',
                    { defaultText: '返回项目详情页' }
                  )
                }
              )
              .then(() => {
                this.initMultitasking()
                if (!this.interactiveDialog) {
                  this.$refs.taskContent.setVideoPlay()
                  console.log(3, '计时')
                  this.startTime()
                }
              })
              .catch(() => {
                this.backProjectDetail()
              })
          }
        })
    },
    previous() {
      if (this.taskIndex === -1) return
      let index = this.taskIndex - 1
      let item = this.taskData[index]

      // const { mooc_resource_from, task_learn_status } = this.currentTask
      const { task_learn_status } = this.currentTask
      // let isRecourseFrom = mooc_resource_from === 'geekBang' || mooc_resource_from === 'sanjieke'
      if (this.isGeek && this.geekCouresInfo?.can_preview && task_learn_status === -2) {
        this.handleGeekTaskChange(item)
      } else {
        if (item.lock_status === 2) {
          if (item.unlock_time) {
            this.$message.warning(
              `${this.$langue('Api_Mooc_Project_TaskUnLocked', {
                defaultText: '任务暂未解锁，解锁时间'
              })}：${item.unlock_time}`
            )
          } else {
            this.$message.warning(
              this.$langue('Mooc_Common_Authority_NotStudyByAdminLocked', {
                defaultText: '任务已被管理员锁定，无法学习'
              })
            )
          }
        } else {
          this.$router.replace({
            name: 'taskDetail',
            query: {
              mooc_course_id: this.mooc_course_id,
              task_id: item.task_id
            }
          })
        }
      }
    },
    next() {
      if (this.taskIndex === -1) return
      let index = this.taskIndex + 1
      let item = this.taskData[index]

      // const { mooc_resource_from, task_learn_status } = this.currentTask
      const { task_learn_status } = this.currentTask
      if (this.isGeek && this.geekCouresInfo?.can_preview && task_learn_status === -2) {
        this.handleGeekTaskChange(item)
      } else {
        if (
          this.courseProcess.unlocked_by_step &&
          item.preReqTaskFinished === false
        ) {
          this.$message.warning(
            this.$langue('Mooc_ProjectDetail_TaskList_UnlockByPreTask', {
              defaultText: '完成上一个应学任务后解锁'
            })
          )
          return
        }
        if (item.lock_status === 2) {
          if (item.unlock_time) {
            this.$message.warning(
              `${this.$langue('Api_Mooc_Project_TaskUnLocked', {
                defaultText: '任务暂未解锁，解锁时间'
              })}：${item.unlock_time}`
            )
          } else {
            this.$message.warning(
              this.$langue('Mooc_Common_Authority_NotStudyByAdminLocked', {
                defaultText: '任务已被管理员锁定，无法学习'
              })
            )
          }
          return
        }
        const { previewType } = this.$route.query
        this.$router.replace({
          name: 'taskDetail',
          query: {
            mooc_course_id: this.mooc_course_id,
            task_id: item.task_id,
            previewType
          }
        })
      }
    },
    getCourseProcess() {
      getCourseProcess({ mooc_course_id: this.mooc_course_id })
        .then(async (res) => {
          // learn_status: 0 项目未开始时间，1 学员未开始状态 2 学员进行中状态 3 学员已完成状态 4 学员已逾期状态 5 项目已结束
          this.courseProcess = {
            mooc_course_name: res.mooc_course_name,
            task_sum: res.task_sum || 0,
            requiredProcess: `${res.required_task_finish_count || 0}/${
              res.required_task_count || 0
            }`,
            noRequireProcess: `${res.non_required_task_finish_count || 0}/${
              res.non_required_task_count || 0
            }`,
            learn_status: res.learn_status,
            unlocked_by_step: res.unlocked_by_step,
            enable_study_record_sync: res.enable_study_record_sync,
            course_status: res.course_status
          }
          await this.purchaseSourceFromConfig()
          // await this.getTaskDetail()
        })
        .catch(() => {
          this.$sdc.loading.hide()
        })
    },
    async getTaskList(data) {
      this.taskData = data
      // 获取当前task在项目所有task中的位置
      this.taskIndex = data.findIndex(
        (item) => item.task_id === this.task_id * 1
      )
      if (
        this.taskIndex !== 0 &&
        (this.taskData[this.taskIndex - 1] ||
          JSON.stringify(this.taskData[this.taskIndex - 1]) !== '{}')
      ) {
        this.prevTaskBtnShow = true
      } else {
        this.prevTaskBtnShow = false
      }
      if (
        this.taskIndex !== this.taskData.length - 1 &&
        (this.taskData[this.taskIndex + 1] ||
          JSON.stringify(this.taskData[this.taskIndex + 1]) !== '{}')
      ) {
        this.nextTaskBtnShow = true
      } else {
        this.nextTaskBtnShow = false
      }
      this.getCourseProcess()
    },
    getTaskDetail() {
      let isFromQuestionTask = this.$refs.taskContent?.isFromQuestionTask
      console.log(this.currentTask, 'task')
      if (isFromQuestionTask || (this.currentTask && [23, 99].includes(this.currentTask.act_type * 1))) {
        this.showIframe = true
      } else {
        this.showIframe = false
      }
      let _this = this
      getTaskDetail({
        mooc_course_id: this.mooc_course_id,
        task_id: this.task_id,
        preview: this.isPreview ? 1 : ''
      })
        .then((res) => {
          res = res || {}
          // 无权限、无法访问页面场景
          if (this.handlePageError(res)) return
          // 处理任务完成条件
          res.finished_condition =
            typeof res.finished_condition === 'string'
              ? JSON.parse(res.finished_condition)
              : res.finished_condition
          res.conditionType = res.finished_condition?.type || ''
          // if (res.act_type === '20') {
          //   if (res.resource_type === 'Exam') {
          //     res.conditionText = this.$langue('Mooc_TaskDetail_Navigation_ExamPass', { defaultText: '通过考试' }) // 通过考试
          //   } else if (res.resource_type === 'Practice') {
          //     res.conditionText = this.$langue('Mooc_TaskDetail_Navigation_PracticePass', { defaultText: '完成练习' }) // 完成练习
          //   }
          // } else if (res.finished_condition?.type * 1 === 1) {
          //   res.conditionText = this.$langue('Mooc_TaskDetail_Navigation_FinishByCourse', { defaultText: '由课程完成条件决定' })
          // } else if (res.finished_condition?.type * 1 === 2) {
          //   res.conditionText = `${this.$langue('Mooc_TaskDetail_Navigation_MinStudyTime', { minute: res.finished_condition?.condition || 0, defaultText: `至少学习${res.finished_condition?.condition || 0}分钟` }) )}`
          // } else {
          //   res.conditionText = '-'
          // }
          this.currentTask = res
          this.totalStudyTime = this.currentTask.total_study_time
          this.showIframe = true
          if (this.currentTask.mooc_resource_from) {
            this.getOutsourceLinkConfig()
          }
          // is_finished: null 未开始，false 进行中，true已完成
          if (res.is_finished) {
            this.taskFinishedState = {
              is_finished: true,
              finished_time: res.finished_time
            }
            // 2.22 第三方，外链也需要完成时，查询项目全部任务是否完成
            if (res.act_type === '23' || res.act_type === '99') {
              getTaskFinishedCondition({ mooc_course_id: this.mooc_course_id }).then(Taskres => {
                let certificateInfo = JSON.parse(localStorage.getItem('certificateInfo')) || []
                let showedCertificate = certificateInfo.some(item => {
                  return item.mooc_course_id !== this.mooc_course_id
                })
                if (
                  Taskres.all_finished &&
                  !_this.alreadyShowCertificate &&
                  _this.currentTask.required &&
                  !showedCertificate
                ) {
                  // 表示完成培养项目（最后一个应学任务完成）
                  _this.finishProject = {
                    visible: true,
                    certificate_name: Taskres.certificate_name,
                    certificate_id: Taskres.certificate_id,
                    enable_certificate: Taskres.enable_grant_certificate
                  }
                  localStorage.setItem('certificateInfo', JSON.stringify([...certificateInfo, this.mooc_course_id]))
                }
              })
            }
          }

          if (this.currentTask.act_type === '99') {
            this.sourceTaskListShow = false
          }
        })
        .catch(() => {
          this.$sdc.loading.hide()
          this.showIframe = true
        })
    },
    handlePageError(currentTaskData) {
      let flag = false
      // 外部课程 && 支持试读 && 未加入项目
      let resourceFrom = this.sourceFromConfig.includes(currentTaskData.mooc_resource_from)
      console.log(this.geekCouresInfo?.can_preview, 'can_preview')
      if (resourceFrom && this.geekCouresInfo?.can_preview && currentTaskData.task_learn_status === -2) {
        return flag
      }
      let config = Object.assign({}, this.pageError)
      // task_learn_status 任务状态（空/null任务正常 -2未加入项目 -1暂无访问权限 0项目未开始 1项目已结束  2项目已逾期 3被管理员锁定 4任务未解锁 5不支持PC端，6不支持移动端 7未发布）
      if (currentTaskData.task_learn_status === -2) {
        config = {
          inaccessible: true,
          tips: this.$langue(
            'Mooc_ProjectDetail_BasicInfo_CannotStudyNotInProj',
            { defaultText: '暂未加入培养项目，无法进行学习' }
          ),
          isShowJoinBtn: true,
          isShowBack: true
        }
        flag = true
      }
      // 两个按钮都不显示
      if (currentTaskData.task_learn_status === -1) {
        config = {
          noPermission: true,
          tips: this.$langue('Mooc_Common_Authority_NoArthority', {
            defaultText: '暂无此课程的访问权限，请联系项目管理员进行调整'
          }),
          isConcatAdmin: 1,
          concatAdmins: currentTaskData.mooc_course_admins_list,
          isShowJoinBtn: false,
          isShowBack: false
        }
        flag = true
      }

      // 只显示回到项目首页按钮
      if (currentTaskData.task_learn_status === 0) {
        config = {
          inaccessible: true,
          tips: this.$langue('Mooc_Common_Authority_NotStudyByTime', {
            defaultText: '项目未开始，无法进行任务学习'
          }),
          timeText: currentTaskData.start_time
            ? `${this.$langue('Mooc_Common_Authority_StartTime', {
              defaultText: '开始时间'
            })}：${currentTaskData.start_time}`
            : '',
          isShowJoinBtn: false,
          isShowBack: true
        }
        flag = true
      }

      // 只显示返回项目首页按钮
      if (currentTaskData.task_learn_status === 1) {
        config = {
          inaccessible: true,
          tips: this.$langue('Mooc_Common_Authority_NotStudyByProjectEnd', {
            defaultText: '项目已结束，无法进行任务学习'
          }),
          isShowJoinBtn: false,
          isShowBack: true
        }
        flag = true
      }

      // 只显示返回项目首页按钮
      if (currentTaskData.task_learn_status === 2) {
        config = {
          inaccessible: true,
          tips: this.$langue('Mooc_Common_Authority_NotStudyByDelay', {
            defaultText: '已逾期，无法进行任务学习'
          }),
          isConcatAdmin: 2,
          concatAdmins: currentTaskData.mooc_course_admins_list,
          isShowJoinBtn: false,
          isShowBack: true
        }
        flag = true
      }

      // 任务状态：task_status 0 草稿、1 已发布、2 已失效 ，只显示返回项目首页按钮
      if (currentTaskData.task_status === 2) {
        config = {
          inaccessible: true,
          tips: this.$langue('Mooc_Common_Authority_TaskDIsabled', {
            defaultText: '任务已失效，无法学习，请联系管理员调整'
          }),
          isConcatAdmin: 3,
          concatAdmins: currentTaskData.mooc_course_admins_list,
          isShowJoinBtn: false,
          isShowBack: true
        }
        flag = true
      }

      if (currentTaskData.task_learn_status === 5) {
        this.getMobileUrl().then(() => {
          config.inaccessible = true
          config.mobileViewUrl = this.qrUrl
          config.tips = this.$langue(
            'Mooc_Common_Authority_NotSupportPC',
            { defaultText: '该任务暂不支持PC端查看，请在移动端查看' }
          )
        })
        flag = true
      }

      if (currentTaskData.act_type === '99' && !currentTaskData.resource_url) {
        this.getMobileUrl().then(() => {
          config.inaccessible = true
          config.mobileViewUrl = this.qrUrl
          config.tips = this.$langue(
            'Mooc_Common_Authority_NotSupportPC',
            { defaultText: '该任务暂不支持PC端查看，请在移动端查看' }
          )
        })
        flag = true
      }

      // 两个按钮都不显示
      if (currentTaskData.task_learn_status === 7) {
        config = {
          inaccessible: true,
          tips: this.$langue(
            'Mooc_ProjectDetail_BasicInfo_ProjectUnPublished',
            { defaultText: '培养项目暂未发布，无法访问。' }
          ),
          isConcatAdmin: 1,
          concatAdmins: currentTaskData.mooc_course_admins_list,
          isShowJoinBtn: false,
          isShowBack: false
        }
        flag = true
      }

      // 锁定状态（1 解锁任务  2 锁定任务)
      if (currentTaskData.lock_status === 2) {
        config.inaccessible = true
        if (currentTaskData.unlock_time) {
          config.timeText = `${this.$langue(
            'Mooc_Common_Authority_UnlockTime',
            { defaultText: '解锁时间：' }
          )}${currentTaskData.unlock_time}`
          config.tips = this.$langue(
            'Mooc_Common_Authority_NotStudyByTaskLocked',
            { defaultText: '任务暂未解锁，无法学习' }
          )
          config.isShowJoinBtn = false
          config.isShowBack = true
        } else {
          config.tips = this.$langue(
            'Mooc_Common_Authority_NotStudyByAdminLocked',
            { defaultText: '任务已被管理员锁定，无法学习' }
          )
          config.isShowJoinBtn = false
          config.isShowBack = true
        }
        flag = true
      }
      let currentTaskListData = this.taskData[this.taskIndex]
      if (
        this.courseProcess.unlocked_by_step &&
        currentTaskListData?.preReqTaskFinished === false
      ) {
        config.inaccessible = true
        config.tips = this.$langue(
          'Mooc_Common_Authority_LockByFinishPreTask',
          { defaultText: '完成上一个应学任务后即可解锁此任务' }
        )
        config.isShowJoinBtn = false
        config.isShowBack = true
        flag = true
      }
      // config.course_status = this.courseProcess.course_status
      this.pageError = config
      // 未加入项目
      this.pageError.register = !(currentTaskData.task_learn_status === -2)
      return flag
    },
    initMultitasking() {
      this.isMultitasking = false
      let params = {
        key: 'mooc-multi-task',
        value: JSON.stringify({
          task_id: this.task_id,
          time: Date.now()
        })
      }
      SDKMultiTask.handleMultitasking(params, (e) => {
        let value = JSON.parse(e.newValue)
        let flag = this.taskData.some(
          (item) => item.task_id === value.task_id * 1
        )
        if (flag && !this.isMultitasking) {
          this.pauseTime()
          this.isMultitasking = true
          // 通知第三方视频暂停
          this.$refs.taskContent.setVideoPause()
          this.$messageBox
            .confirm(
              this.$langue('Mooc_Common_Alert_NotSupportMultipleTasks', {
                defaultText:
                  '培养项目不支持多任务同时学习，系统检测到您正在学习同一项目下的其他任务，已自动停止当前任务的学习。如需继续学习，请点击“继续学习”按钮重新进入任务页面。'
              }),
              this.$langue('Mooc_Common_Alert_MultipleTasksRemind', {
                defaultText: '多任务同时学习提醒'
              }),
              {
                confirmButtonText: this.$langue(
                  'Mooc_ProjectDetail_TrainingProgress_ContinueStudy',
                  { defaultText: '继续学习' }
                ),
                cancelButtonText: this.$langue(
                  'Mooc_Common_Alert_ReturnProjectDetail',
                  { defaultText: '返回项目详情页' }
                )
              }
            )
            .then(() => {
              this.initMultitasking()
              if (!this.interactiveDialog) {
                this.$refs.taskContent.setVideoPlay()
                console.log(4, '计时')
                this.startTime()
              }
            })
            .catch(() => {
              this.backProjectDetail()
            })
        }
      })
    },
    backProjectDetail() {
      const { previewType } = this.$route.query
      this.$router.push({
        path: '/mooc/projectDetail',
        query: {
          mooc_course_id: this.mooc_course_id,
          previewType
        }
      })
    },
    // taskContent子组件在视频开始播放时，调用
    startTime() {
      // 多任务同时学习时，不允许视频播放
      if (this.isMultitasking) return
      this.timer.start()
      // this.timer.getDurationSeconds = this.getDurationSeconds
    },
    pauseTime() {
      // taskContent子组件在视频暂停播放时，调用
      this.timer.pause()
    },
    // taskContent子组件在视频完成播放时，调用
    completeLearn(isInit) {
      if (
        !this.taskFinishedState.is_finished &&
        this.currentTask.finished_condition?.type * 1 === 1
      ) {
        if (isInit && this.courseProcess.enable_study_record_sync) {
          this.$messageBox.confirm(
            this.$langue('Mooc_Common_Alert_EnableSyncCourse', {
              defaultText:
                '培养项目已开启进度同步功能，您已通过其他途径完成当前任务关联的课程。'
            }),
            this.$langue('Mooc_Common_Alert_FinishTaskRemind', {
              defaultText: '任务完成提醒'
            }),
            {
              confirmButtonText: this.$langue(
                'Mooc_ProjectDetail_Notice_IKnow',
                { defaultText: '知道了' }
              ),
              showCancelButton: false
            }
          )
        }
        this.taskFinishedState = {
          is_finished: true,
          finished_time: this.$moment().format('YYYY-MM-DD HH:mm:ss')
        }
        this.handleSaveLearnRecord(true, {}, true)
      } else {
        this.handleSaveLearnRecord(false)
      }
    },
    // 第三方页面通知当前页面，课程无权限
    handleErrorInfo(param) {
      this.pageError = {
        noPermission: true,
        tips: `${
          param ||
          this.$langue('Mooc_ProjectDetail_TaskList_CourseError', {
            defaultText: '课程异常！'
          })
        } ${this.$langue('Mooc_ProjectDetail_TaskList_ConnectAdminChang', {
          defaultText: '请联系项目管理员进行调整'
        })}`,
        isConcatAdmin: 1,
        concatAdmins: this.currentTask.mooc_course_admins_list
      }
    },
    onUpdateStatus(param) {
      // 作业内部：完成作业、撤回作业（变成未完成）,完成互评等状态变化通知到父页面更新任务列表和进度接口
      if (this.currentTask.act_type === '22') {
        if (
          param.all_finished &&
          !this.alreadyShowCertificate &&
          this.currentTask.required
        ) {
          // 表示完成培养项目（最后一个应学任务完成）
          this.finishProject = {
            visible: true,
            certificate_name: param.certificate_name,
            certificate_id: param.certificate_id,
            enable_certificate: param.enable_grant_certificate
          }
          this.alreadyShowCertificate = true
        }
        this.getCourseProcess()
        this.$refs.taskList.getTaskData().then(() => {
          // 在任务详情列表获取到列表数据之后调用筛选过滤方法
          this.$refs.taskList.setFilterRef()
        })
      }
      if (this.currentTask.act_type === '23' || this.currentTask.act_type === '32') {
        this.getCourseProcess()
      }
    },
    setInteractiveStatus(flag) {
      this.interactiveDialog = flag
    },
    handleTaskList() {
      if (this.currentTask.act_type === '99') {
        this.sourceTaskListShow = !this.sourceTaskListShow
        this.$store.commit('setTaskPanel', this.sourceTaskListShow)
      } else {
        this.$store.commit('setTaskPanel', !this.showTaskPanel)
      }
    },
    jumpAnswerPage(flag) {
      this.showNavBar = false
      // this.showTaskPanel = false
      this.$store.commit('setTaskPanel', false)
      if (flag) {
        this.timer.start()
      }
    },
    endAnswer(params) {
      let firstDone // 此方法，每次进入考试就会执行，导致任务完成的提示弹窗会多次弹出，所以需要判断是否是第一次完成
      // init 字段表示是否是第一次完成
      if (!params.init && params.is_finished) {
        firstDone = true
      }
      this.showNavBar = true
      this.timer.pause()
      if (params.is_finished) {
        if (
          params.init &&
          this.courseProcess.enable_study_record_sync &&
          !this.taskFinishedState.is_finished
        ) {
          firstDone = true
          const msg = ['Exam', 'Practice'].includes(
            this.currentTask.resource_type
          )
            ? this.$langue('Mooc_Common_Alert_EnableSyncExam', {
              defaultText:
                  '培养项目已开启进度同步功能，您已通过其他途径完成当前任务关联的考试。'
            })
            : this.$langue('Mooc_Common_Alert_EnableSyncCourse', {
              defaultText:
                  '培养项目已开启进度同步功能，您已通过其他途径完成当前任务关联的课程。'
            })
          this.$messageBox.confirm(
            msg,
            this.$langue('Mooc_Common_Alert_FinishTaskRemind', {
              defaultText: '任务完成提醒'
            }),
            {
              confirmButtonText: this.$langue(
                'Mooc_ProjectDetail_Notice_IKnow',
                { defaultText: '知道了' }
              ),
              showCancelButton: false
            }
          )
        }
        params.score = params.highScore // 考试通过后同步最高分，解决考试通过，分数未达到及格分显示分数问题
        this.taskFinishedState = {
          is_finished: true,
          finished_time: this.$moment().format('YYYY-MM-DD HH:mm:ss')
        }
      }
      // 先判断是否待批阅，再判断是否已通过
      const exam_status = params.waitView ? 2 : params.is_finished ? 1 : 0
      let data = {
        elapsed_seconds: params.elapsed_seconds || 0,
        is_cheat: params.is_cheat || false,
        is_finished: params.is_finished || false,
        score: params.score || 0,
        exam_status // 0-未通过, 1-通过,  2-待批阅
      }
      this.handleSaveLearnRecord(data.is_finished, data, firstDone)
    },
    viewTaskDesc() {
      this.taskDescVisible = true
    },
    viewCertificate() {
      this.finishProject.visible = false
      certificateView(this.finishProject.certificate_id)
    },
    // 切换双语
    handleChangelang() {
      const lang = this.moocLang === 'en-us' ? 'zh-cn' : 'en-us'
      this.$store.commit('setMoocLang', lang)
      this.$refs.taskContent.changeMoocLang()
    },
    getMobileUrl() {
      // 该二维码跳转到小程序 pages/webview/mooc/taskDetail
      const params = {
        scene: `${this.mooc_course_id}_${this.task_id}_mooc_${this.moocLang}`,
        page: 'pages/webview/mooc/taskDetail',
        env_version: process.env.NODE_ENV === 'production' ? 'release' : 'trial'
      }
      return getMobileQrcode(params).then((res) => {
        this.qrUrl = `data:image/png;base64,${res}`
      })
    },
    doCopy() {
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      input.value = this.urlMobile
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      this.$message.success(
        this.$langue('Mooc_Common_Alert_CopySucessed', {
          defaultText: '复制成功'
        })
      )
      // 复制后移除输入框
      input.remove()
    },
    onContentLoading() {
      if (!this.contentLoading && this.isFrom) {
        this.contentLoading = true
        // 极客 && 未购买课程 && 支持试看
        // let isRecourseFrom = this.geekCouresInfo.recourse_from === 'geekBang' || this.geekCouresInfo.recourse_from === 'sanjieke'
        if (this.isGeek && !this.geekCouresInfo.purchased && this.geekCouresInfo.can_preview) {
          this.getGeekCoursePurchaseInfo()
        }
      }
    },
    // 极客课程信息
    getGeekBangDetail() {
      getGeekCourseDetail(this.mooc_course_id).then(res => {
        this.geekCouresInfo = res
        console.log('极客课程信息', res)
      })
    },
    // 极客课程购买信息
    getGeekCoursePurchaseInfo() {
      getCoursePurchaseInfo(this.mooc_course_id).then(res => {
        if (res.course_acquisition_type === 2) {
          res.course_val = res.course_val ? res.course_val : 1
        }
        console.log('极客课程购买信息', res)
        this.geekPurchaseInfo = res
      })
    },
    // 弹出试学额度已用完弹窗
    handleStudyTips(status, consume_appid) {
      this.consume_appid = consume_appid
      this.redeemPopupStatus = status
      this.redeemCoursesShow = true
    },
    // 直接兑换极客课程
    purchaseCourses() {
      const { user_account_num, course_stock_total, course_val } = this.geekPurchaseInfo
      // 课程名额 >= 1 && 用户兑换券 >= 课程价值（兑换券）=== 兑换课程
      if (course_stock_total > 0 && user_account_num >= course_val) {
        this.redeemPopupStatus = 0
      } else if (course_stock_total === 0) {
        // 兑换名额不足
        this.redeemPopupStatus = 1
      } else if (course_val > user_account_num) {
        // 兑换券不足
        this.redeemPopupStatus = 2
      }
      if (this.redeemPopupStatus > -1) {
        this.redeemCoursesShow = true
      }
    },
    handleGeekTaskChange(item) {
      const { preview_records, allow_preview_num, previewed_num } = this.geekPurchaseInfo
      // 已试学完可试学章数
      if (previewed_num >= allow_preview_num) {
        let index = preview_records.findIndex(e => e.outsourced_course_id === item.act_id)
        console.log(index)
        console.log(preview_records)
        console.log(item.act_id)
        if (index === -1) {
          this.redeemPopupStatus = 3
          this.redeemCoursesShow = true
          return
        }
      }
      this.$router.replace({
        name: 'taskDetail',
        query: {
          mooc_course_id: this.mooc_course_id,
          task_id: item.task_id,
          from: this.currentTask.mooc_resource_from
        }
      })
    },
    async initPage() {
      await this.$sdc.loading()
      await this.getGeekBangDetail()
      await this.$refs.taskList.getTaskData().then(() => {
        // 在任务详情列表获取到列表数据之后调用筛选过滤方法
        this.$refs.taskList.setFilterRef()
      })
      await this.getCourseProcess()
      await this.$sdc.loading.hide()
    }
  }
}
</script>
<style lang="less" scoped>
.task-detail-container {
  // min-width: 1200px;
  background: #f6f7f9;
  height: 100%;
  overflow-y: hidden;
  overflow-x: auto;
  display: flex;
  flex-direction: column;
  .task-navbar {
    background: #fff;
    box-shadow: 0 4px 12px 0 #eeeeeeff;
  }
  .task-navbar-inner {
    margin: 0 auto;
    width: 1440px;
    padding: 0 40px 0 30px;
    height: 74px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-content {
      display: flex;
      align-items: center;
      color: #00000066;
      font-size: 12px;
      flex: 1;

      .line {
        width: 1px;
        height: 32px;
        background: #eeeeee;
        margin: 0 15px;
      }

      .course-item {
        width: 208px;
      }
      .task-item {
        flex: 1;
        width: 0;
      }

      .course-name,
      .task-name {
        height: 22px;
        line-height: 22px;
      }

      .task-name {
        display: flex;
        overflow: hidden;
        align-items: center;
      }

      .left-item {
        height: 50px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .name {
          cursor: default;
          color: #000000e6;
          font-size: 14px;
          font-weight: 600;
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
          -o-text-overflow: ellipsis;
        }

        .label {
          padding: 0 4px;
          border-radius: 2px;
          margin-right: 8px;
          height: 18px;
          line-height: 18px;
          flex-shrink: 0;
        }

        .require {
          background: #fdf6ecff;
          color: #ff7548;
        }

        .norequire {
          background: #ccf2e2;
          color: #00b368;
        }

        .learning-time {
          background: #ebeffc;
          border-radius: 2px;
          padding: 0 10px;
          height: 20px;
          line-height: 20px;
          display: inline-block;
          margin-left: 12px;
          color: #000;

          &-linehight {
            color: #0052d9;
          }
        }

        .mobile-popove {
          // position: relative;
        }

        .mobile-box {
          display: flex;
          align-items: center;
          cursor: pointer;
          margin-left: 8px;
        }

        .mobile-icon {
          background: url('~@/assets/mooc-img/mobile.png') no-repeat
            center/cover;
          width: 16px;
          height: 16px;
          display: inline-block;
        }

        .done-icon {
          width: 20px;
          height: 20px;
          background: url('~@/assets/mooc-img/done-fill.png') no-repeat center /
            cover;
        }

        .done-text {
          margin: 0 12px 0 4px;
          color: #0ad0b6;
        }

        .back-text {
          position: relative;
          padding-left: 24px;
          height: 20px;
          line-height: 20px;
          cursor: pointer;

          &::before {
            content: '';
            width: 16px;
            height: 16px;
            background: url('~@/assets/mooc-img/back.png') no-repeat center /
              cover;
            position: absolute;
            top: 2px;
            left: 0;
          }
        }
        .finished-status-time {
          display: flex;
          align-items: center;
        }
        .isPreview-box {
          color: #ff7548;
          .el-icon-warning-outline {
            font-size: 16px;
          }
          .tips {
            font-size: 14px;
            margin-left: 4px;
          }
        }
        .task-status {
          height: 20px;
          line-height: 20px;
          display: flex;
          align-items: center;
          white-space: break-spaces;
          margin-right: 12px;
          .finished-condition {
            color: #000;
          }
        }
      }
    }
  }

  .right-btn {
    flex-shrink: 0;
    margin-left: 30px;
    display: flex;
    align-items: center;
    :deep(.el-button + .el-button) {
      margin-left: 24px;
    }
    .el-button:last-of-type {
      padding: 5px 8px;
    }
    .more-lang-btn {
      display: flex;
      align-items: center;
      .icon {
        width: 16px;
        height: 16px;
        margin-right: 4px;
      }
      .el-icon-en {
        background: url('~@/assets/img/english.png') no-repeat center / cover;
      }
      .el-icon-zh {
        background: url('~@/assets/img/china.png') no-repeat center / cover;
      }
      :deep(.el-icon-sort) {
        transform: rotate(90deg);
        margin-left: 2px;
      }
    }
  }

  .task-wrapper {
    position: relative;
    flex: 1;
  }

  :deep(.dialog-center-common) {
    .el-dialog {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .el-dialog__header {
      border-bottom: none;
      padding-bottom: 0;
    }

    p {
      color: #000;
      font-weight: 600;
      margin-bottom: 12px;
    }
  }

  :deep(.task-desc) {
    .el-dialog {
      width: 644px;
    }
  }

  :deep(.finish-project) {
    .el-dialog {
      width: 430px;
    }

    .el-dialog__body {
      text-align: center;
    }

    .certificate-info {
      margin-top: 24px;
      color: #ed7b2f;
    }

    .certificate-tips {
      margin-bottom: 8px;
      display: inline-block;
      margin-left: 24px;
      position: relative;

      &::before {
        content: '';
        width: 20px;
        height: 20px;
        background: url('~@/assets/mooc-img/warning-icon.png') no-repeat center /
          cover;
        position: absolute;
        top: -2px;
        left: -24px;
      }
    }
  }
}

.finished-Toast {
  text-align: center;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 14px;
  line-height: 20px;
  background-color: #FFF;
  width: fit-content;
  width: 174px;
  border-radius: 6px;
  padding: 16px 24px;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  border: 0.5px solid #DCDCDC;
  box-shadow: 0 8px 10px -5px #00000014, 0 16px 24px 2px #0000000a, 0 6px 30px 5px #0000000d;
  > img {
    position: absolute;
    top: 7px;
    right: 7px;
    width: 20px;
    height: 20px;
  }
  .done-tips {
     line-height: 22px;
  }
  .next-task {
    margin: 12px 0 0 0;
    color: #ed7b2f;
    line-height: 22px;
    text-decoration: underline;
  }
}
</style>

<style lang="less">
.mobile-popove-box {
  width: 358px;
  padding: 24px 32px;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;

  .task-name {
    font-size: 16px;
    font-weight: 600;
    color: #000;
    text-align: center;
  }
  .qr-code {
    margin: 20px;
  }
  .copy-btn {
    margin-top: 20px;
  }
  .el-input-group__append {
    color: #000000e6;
  }
  img {
    width: 200px;
    height: 200px;
  }
}
.el-message.finished-tips {
  top: 50% !important;
}
</style>
