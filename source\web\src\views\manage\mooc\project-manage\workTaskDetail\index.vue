<template>
  <!-- 作业任务详情 -->
  <div class="work-task-detail-container">
    <div class="header">
      <span class="lf-title">任务详情</span>
      <div class="right-btn-box">
        <span class="btn" @click="editWork">编辑作业</span>
        <span v-if='detailData.enabled_share' class="btn" @click="showShare">分享查看链接</span>
        <span v-if='detailData.enable_mark' class="btn" @click="showShare('read')">分享批阅链接</span>
      </div>
    </div>
    <div class="project-detail-box">
      <div class="item-terms">作业名称：<span class="value">{{ detailData.title || '-' }}</span></div>
      <div class="item-terms">作业提交时间：
        <span class="value">
          {{ !detailData.start_time && !detailData.end_time ? '不限制' : `${detailData.start_time || '不限制'} 至 ${detailData.end_time || '不限制'}` }}
        </span>
      </div>
      <div class="item-terms">共享查看：<span class="value">{{ detailData.enabled_share ? shared_name : "未开启" }}</span></div>
      <div class="item-terms">作业批阅：
        <span class="value" v-if="detailData.enable_mark">
          总分{{ detailData.total_score || 0 }}；
          合格分{{ detailData.pass_score || 0 }}
        </span>
        <span class="value" v-else>未开启</span>
      </div>
      <div v-if="condition !== '提交作业'" style="margin-top: 16px;">
        <div class="item-terms">评分方式：
          <span class="value" v-if="detailData.enable_mark">
            <span v-if="markTypeTwo('1')">
              老师批阅
              {{ markTypeTwo('1') && markTypeTwo('2') ? `（${detailData.teacher_mark_weight}%）；` : '' }}
            </span>
            <span v-if="markTypeTwo('2')">
              学员互评
              {{ markTypeTwo('1') && markTypeTwo('2') ? `（${detailData.student_mark_weight}%）` : '' }}
            </span>
          </span>
          <span class="value" v-else>未开启作业批阅</span>
        </div>
        <div class="item-terms">批阅老师：
          <span class="value" v-if="markTypeTwo('1')">{{ detailData.teachers }}</span>
          <span class="value" v-else>-</span>
        </div>
        <div class="item-terms" v-if="detailData.enable_mark">互评时间：
          <span class="value" v-if="markTypeTwo('2')"> 
            {{ detailData.student_mark_start_time ? `${detailData.student_mark_start_time} 至 ${detailData.student_mark_end_time}` : '不限制' }}
          </span>
          <span class="value" v-else>未开启学员互评</span>
        </div>
        <div class="item-terms">互评份数：
          <span v-if="markTypeTwo('2')" class="value">{{ detailData.student_mark_count || 0 }}</span>
          <span class="value" v-else>未开启学员互评</span>
        </div>
      </div>
      <div class="item-terms">完成条件：<span class="value">{{ condition }}</span></div>
      <div class="item-terms">
        <span class="bottom-type first-type">类型：<span class="value">作业</span></span>
        <span class="bottom-type">性质：<span class="value">{{ detailData.lock_status == 1 ? '应学' : '选学' }}</span></span>
        <!-- <span class="bottom-type">解锁时间：<span class="value">{{ detailData.desc }}</span></span> -->
        <span class="bottom-status finsh-status">已完成：<span class="value">{{ detailData.finished_count || 0
        }}</span></span>
        <span class="bottom-status going-status">进行中：<span class="value">{{ detailData.process_count || 0 }}</span></span>
        <span class="bottom-status no-start-status">未开始：<span class="value">{{ detailData.not_started_count || 0
        }}</span></span>
      </div>
    </div>
    <div class="main-content">
      <div class="top-btn-box">
        <div class="btn-down">
          <el-button size="small" @click="handleExport">导出作业明细</el-button>
          <!-- <el-button size="small" @click="handleDownWork">打包下载作业</el-button> -->
        </div>
        <el-input style="width: 240px" v-model="form.staff_name" placeholder="请输入学员名称" clearable
          suffix-icon="el-icon-search">
        </el-input>
      </div>
      <div class="form">
        <div class="form-input">
          <span>
            <span class="label">任务状态：</span>
            <el-select v-model="form.task_status" placeholder="请选择任务状态" clearable style="width: 280px">
              <el-option v-for="item in taskOptions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </span>
          <span class="item-form-staus">
            <span class="label">作业状态：</span>
            <el-select v-model="form.status" placeholder="请选择作业状态" clearable style="width: 280px">
              <el-option v-for="item in workStatusOpions" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </span>
        </div>
        <div class="form-btn">
          <el-button size="small" @click="handerRest">重置</el-button>
          <el-button type="primary" size="small" @click="onSearch(1)">查询</el-button>
        </div>
      </div>
      <el-table :data="tableData.records" style="width: 100%" header-row-class-name="table-header-style"
        row-class-name="table-row-style">
        <el-table-column prop="staff_name" label="学员名称" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.staff_name || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="task_status" label="任务状态" width="90">
          <template slot-scope="scope">
            <span>{{ scope.row.task_status === null ? '未开始' : scope.row.task_status ? '已完成' : '进行中' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="开始任务时间" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.start_time || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="finished_time" label="完成任务时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="status" label="作业状态" width="90">
          <template slot-scope="scope">
            <span>{{ isReadWorkInfo[scope.row.status] }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="submit_time" label="提交时间" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.submit_time || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="teacher_name" label="批阅老师" v-if="detailData.enable_mark && markTypeTwo('1')">
          <template slot-scope="scope">
            <span>{{ scope.row.teacher_name || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="teacher_score" label="老师分数" v-if="detailData.enable_mark && markTypeTwo('1')">
          <template slot-scope="scope">
            <span>{{ scope.row.teacher_score || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="student_score" label="互评分数" v-if="detailData.enable_mark && markTypeTwo('2')">
          <template slot-scope="scope">
            <span>{{ scope.row.student_score || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="总成绩" width="70" v-if="condition !== '提交作业'">
          <template slot-scope="scope">
            <span>{{ scope.row.score || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="review_count" label="互评份数" width="80" v-if="detailData.enable_mark && markTypeTwo('2')"></el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template slot-scope="scope">
            <el-link v-if="scope.row.status === 1 && markTypeTwo('1')" type="primary" @click="handleCheck(scope.row)" :underline="false">批阅</el-link>
            <el-link v-else-if="scope.row.status" type="primary" @click="handleCheck(scope.row)" :underline="false">查看</el-link>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination 
      v-if="tableData.total" 
      @size-change="handleSizeChange" 
      @current-change="handleCurrentChange"
      :current-page="current" 
      :page-sizes="[5, 10, 20, 30, 50, 100]" 
      :page-size="size"
      layout="total,  prev, pager, next, sizes, jumper" 
      :total="tableData.total"
      >
      </el-pagination>
    </div>
    <!-- 分享弹窗 -->
    <shareDialog ref="shareDialog" :title="shareTitle" :isShow.sync="sharedialogShow" />
  </div>
</template>
<script>
import pager from '@/mixins/pager.vue'
import { workTaskDetail, workTaskSearch } from '@/config/mooc.api.conf.js'
import axios from 'axios'
import env from 'config/env.conf.js'
import shareDialog from '@/views/components/shareDialog.vue'
const task_staus_info = {
  null: '未开始',
  0: '进行中',
  1: '已完成'
}
// 作业状态(0 草稿 1 已提交/待批阅  2 驳回  3 合格 4 不合格
const no_read_work_info = {
  null: '未提交',
  0: '未提交',
  1: '已提交',
  2: '已退回'
}
const read_work_info = {
  null: '未提交',
  0: '未提交',
  1: '待批阅',
  2: '已退回',
  3: '合格',
  4: '不合格'
}
export default {
  mixins: [pager],
  components: {
    shareDialog
  },
  data() {
    return {
      form: {
        task_status: '',
        status: '',
        staff_name: ''
      },
      contentModuleTypes: [],
      tableData: {
        records: [],
        total: 0
      },
      detailData: {
        title: '',
        desc: '',
        shared_type: '',
        finished_condition: {
          type: '',
          condition: null
        },
        enable_mark: false
      },
      task_staus_info,
      no_read_work_info,
      read_work_info,
      sharedialogShow: false,
      shareTitle: '',
      taskOptions: [
        { value: 0, label: '未开始' },
        { value: 1, label: '进行中' },
        { value: 2, label: '已完成' }
      ],
      readWorkOPtions: [
        { value: 0, label: '未提交' },
        { value: 1, label: '待批阅' },
        { value: 2, label: '已退回' },
        { value: 3, label: '合格' },
        { value: 4, label: '不合格' }
      ],
      noReadWorkOPtions: [
        { value: 0, label: '未提交' },
        { value: 1, label: '已提交' },
        { value: 2, label: '已退回' }
      ]
    }
  },
  computed: {
    shared_name() {
      const { shared_type } = this.detailData
      const shareArr = shared_type?.split(';')
      if (!shareArr?.length) return
      const nameArr = [
        { value: '1', label: '其他学员' },
        { value: '2', label: '学员直接上级' },
        { value: '3', label: '学员所属组织BP' }
      ]
      let name = ''
      nameArr.forEach((v) => {
        if (shareArr.includes(v.value)) {
          name += `${v.label}; `
        }
      })
      name = name.slice(0, -2)
      return name
    },
    condition() {
      const { finished_condition, act_id } = this.detailData
      if (!act_id) return
      const row = JSON.parse(finished_condition)
      const typeArr = row?.type.split(';')
      const nameArr = [
        { value: '3', label: '提交作业' },
        { value: '4', label: '批阅合格' },
        { value: '5', label: '完成互评' }
      ]
      let name = ''
      nameArr.forEach((v) => {
        if (typeArr.includes(v.value)) {
          name += `${v.label}; `
        }
      })
      name = name.slice(0, -2)
      return name
    },
    workStatusOpions() {
      const { enable_mark } = this.detailData
      return enable_mark ? this.readWorkOPtions : this.noReadWorkOPtions
    },
    isReadWorkInfo() {
      const { enable_mark } = this.detailData
      return enable_mark ? this.read_work_info : this.no_read_work_info
    },
    // 学员批阅 2  老师批阅 1
    markTypeTwo() {
      return (type) => {
        const markType = this.detailData?.mark_type
        if (markType?.length === 1) {
          return markType.includes(type)
        }
        return markType?.split(';').includes(type)
      }
    }
  },
  mounted() {
    this.queryData()
    this.onSearch()
  },
  methods: {
    // 编辑作业
    editWork() {
      // const { mooc_course_id, homework_id, task_id, id } = this.$route.query
      // const { href } = this.$router.resolve({
      //   name: 'work',
      //   query: {
      //     homework_id,
      //     task_id,
      //     mooc_course_id,
      //     id
      //   }
      // })
      // window.open(href, '_blank')
      this.$router.go(-1)
    },
    // 查看--批阅
    handleCheck(row) {
      const { mooc_course_id, homework_id, task_id } = this.$route.query
      this.$router.push({
        name: 'work-mark',
        query: {
          act_id: mooc_course_id,
          homework_id,
          task_id,
          recordId: row.record_id
        }
      })
    },
    // 列表查询
    onSearch(page_no = 1) {
      const { mooc_course_id, homework_id } = this.$route.query
      const params = {
        page_no,
        page_size: this.size,
        course_id: mooc_course_id,
        homework_id,
        ...this.form
      }
      workTaskSearch(params).then((res) => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    // 详情
    queryData() {
      const { task_id, mooc_course_id, homework_id } = this.$route.query
      if (!task_id) return
      workTaskDetail({ task_id, mooc_course_id }, homework_id).then((res) => {
        this.detailData = res
      })
    },
    // 导出
    handleExport() {
      const { mooc_course_id, homework_id, task_id } = this.$route.query
      const params = {
        page_size: this.size,
        course_id: mooc_course_id,
        task_id,
        homework_id,
        ...this.form
      }
      let url = `${env[process.env.NODE_ENV].trainingPath}api/mooc/manage/task/export-task-detail-student-list?page_no=${this.current}`
      for (let pre in params) {
        url += `&${pre}=${params[pre]}`
      }
      axios({
        url,
        method: 'get',
        responseType: 'blob'
      }).then((response) => {
        if (response.status === 200 && response.data) {
          const url = window.URL.createObjectURL(new Blob([response.data]))
          const link = document.createElement('a')
          link.href = url
          link.setAttribute('download', '作业明细.xlsx')
          document.body.appendChild(link)
          link.click()
          this.$message.success('导出成功')
        } else {
          this.$message.error('导出失败')
        }
      })
    },
    showShare(type) {
      const { mooc_course_id, homework_id, task_id } = this.$route.query
      this.sharedialogShow = true
      this.shareTitle = type === 'read' ? '分享批阅链接' : '分享查看链接'
      const { title } = this.detailData
      const url = process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/3hdLPs' : 'https://sdc.qq.com/s/g38Tem'
      this.$nextTick(() => {
        this.$refs.shareDialog.initCode({
          mooc_course_id,
          url: `${url}?scheme_type=mooc_mark&act_id=${mooc_course_id}&task_id=${task_id}&homework_id=${homework_id}`,
          taskTitle: title,
          scene: `${mooc_course_id}_${task_id}_${homework_id}`,
          page: 'pages/mooc/manageWorkList/index'
        })
      })
    },
    // 重置
    handerRest() {
      for (let prop in this.form) {
        this.form[prop] = ''
      }
      this.current = 1
      this.onSearch()
    }
  }
}
</script>
<style lang="less" scoped>
.work-task-detail-container {
  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #f3f3f3;

    .lf-title {
      color: #000000e6;
      font-size: 16px;
      font-weight: 600;
    }

    .right-btn-box {
      .btn+.btn {
        margin-left: 32px;
      }

      .btn {
        cursor: pointer;
        color: #0052D9;
      }
    }
  }

  .project-detail-box {
    padding: 20px;
    line-height: 22px;
    border-bottom: 1px solid #f3f3f3;

    .value {
      color: #000000e6;
    }

    .item-terms+.item-terms {
      margin-top: 16px;
    }

    .item-terms:last-of-type {
      margin-bottom: 16px;
    }

    .item-terms {
      color: #00000099;

      .bottom-type {
        color: #00000099;
      }

      .bottom-status {
        color: #000000e6;
      }

      .bottom-type+.bottom-type {
        margin-left: 36px;
      }

      .bottom-status+.bottom-status {
        margin-left: 46px;
      }

      .finsh-status {
        margin-left: 62px;
      }

      .first-type {
        margin-left: 27px;
      }

      .bottom-status {
        position: relative;
      }

      .bottom-status::before {
        content: '';
        width: 6px;
        height: 6px;
        position: absolute;
        margin-top: 9px;
        margin-left: -15px;
        border-radius: 50%;
      }

      .finsh-status::before {
        background: #48c79c;
      }

      .going-status::before {
        background: #266fe8;
      }

      .no-start-status::before {
        background: #ff9649;
      }
    }
  }

  .main-content {
    padding: 20px;

    .top-btn-box {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .form {
      background: #f9f9f9;
      padding: 20px;
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;

      .label {
        color: #00000099;
      }

      .item-form-staus {
        margin-left: 38px;
      }
    }

    .el-link+.el-link {
      margin-left: 10px;
    }
  }
}</style>
