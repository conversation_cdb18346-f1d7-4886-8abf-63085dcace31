<template>
  <div>
    <div class="course-card-container" v-if="commonInfo.list.length">
      <div class="course-title-box">
        <span class="title-num">{{ title }}</span>
        <span 
        class="right-icon" 
        @click="toPath" 
        :dt-areaid="dtAll('areaid', '查看全部')"
        :dt-remark="dtAll('remark', '查看全部')"
        :dt-eid="dtAll('eid', '查看全部')"
        >
        </span>
      </div>
      <swiper :options="swiperOption" ref="swiperCard">
        <swiper-slide 
        v-for="(e, index) in commonInfo.list" 
        :key="index" 
        class="item-course-swiper" 
        :data-id="e.item_id"
        :dt-areaid="dtCourse('areaid', e)"
        :dt-remark="dtCourse('remark', e)"
        >
          <div 
          :class="[
          'chapter-item-title',
          { 'active-item-swiper': e.item_id == course_id }
          ]" 
          :data-id="e.item_id"
          >
            <div class="img-box" :data-id="e.item_id">
              <span class="module-icon">{{ e.module_name }}</span>
              <van-image class="item-img" lazy fit="fill" :src="e.photo_url" :data-id="e.item_id">
                <template v-slot:error>
                  <img :src="formatModuleMap(e.module_id)" />
                </template>
              </van-image>
              <div class="play-time" v-if="showModuleTips(e)">{{ showModuleTips(e) }}</div>
            </div>
            <span class="title overflow-l2">{{ e.content_name }}</span>
          </div>
        </swiper-slide>
      </swiper>
    </div>
    <!-- 课单详情内容 -->
    <van-popup class="course-popup-container" get-container="body" v-model="popupShow" :overlay="false" position="bottom">
      <div class="body">
        <div class="header">
          <span class="placeholder"></span>
          <span class="title overflow-l1">课单·{{ commonInfo.name }}</span>
          <span class="close-icon" @click="handleClose"></span>
        </div>
        <div class="couse-list-box">
          <detailCard 
          :courseData="courseData"
          :cardData="e" 
          v-for="(e, index) in commonInfo.list" 
          :key="index" 
          :isMinute="true"
          entry="courseType"
          :commonInfo="commonInfo"

          >
        </detailCard>
          <div class="no-more">没有更多内容了~</div>
        </div>
      </div>
    </van-popup>
  </div>
</template>
<script>
import {
  getCourseDetailAPI,
  getCourseViewCount,
  specialAreaAPI,
  specialDetail
} from 'config/api.conf'
import detailCard from './detailCard.vue'
import { transformUnitW, formatModuleMap, transforNcTime } from 'utils/tools'
import env from 'config/env.conf.js'
import { actTypes } from 'utils/moduleMap.js'
export default {
  components: {
    detailCard
  },
  props: {
    fromType: {
      type: String,
      default: ''
    },
    courseData: {
      type: Object,
      default: () => ({})
    },
    // 默认网络课
    courseType: {
      type: String,
      default: 'net'
    }
  },
  data() {
    return {
      commonInfo: {
        name: '',
        list: []
      },
      transformUnitW,
      formatModuleMap,
      transforNcTime,
      popupShow: false,
      actTypes
    }
  },
  computed: {
    course_id() {
      return this.courseData.course_id || this.$route.query.course_id || this.$route.query.activity_id
    },
    pageTypeName() {
      let obj = {
        'net': '网课',
        'face': '面授课',
        'activity': '活动'
      }
      return obj[this.courseType]
    },
    title() {
      const val = this.fromType === 'isSpecial' ? '专区' : '课单'
      return `${val}·${this.commonInfo.name}`
    },
    showModuleTips() {
      return ({ module_id, duration, word_num, task_count }) => {
        let tips = ''
        if ([1, 2, 4].includes(module_id)) { // 网络课, 面授课，活动
          tips = transforNcTime(duration * 60)
        } else if ([7, 8].includes(module_id)) { // 案例，文章
          tips = `${word_num || 0}` + this.$langue('Mooc_ProjectDetail_TaskList_Words', { defaultText: '字' })
        } else if ([10].includes(module_id)) { // 培养项目
          tips = `${task_count || 0}` + this.$langue('Mooc_ProjectDetail_BasicInfo_Tasks', { defaultText: '项任务' })
        } else if ([15].includes(module_id)) { // 课单
          tips = `·${task_count || 0}` + this.$langue('NetCourse_Contents', { defaultText: '个内容' })
        }
        return tips
      }
    },
    dtAll() {
      return (type, val) => {
        const container = this.fromType === 'isSpecial' ? `专区` : `课单`
        const act_type = this.fromType === 'isSpecial' ? '13' : '15'
        const data = {
          page: this.courseData.course_name,
          page_type: this.pageTypeName + '详情页面-移动新版', 
          container,
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: '查看全部',
          act_type,
          container_id: '',
          page_id: '',
          terminal: 'H5'
        }
        if (type === 'remark') {
          return JSON.stringify(data)
        } else if (type === 'eid') {
          return `element_${this.course_id}_${this.commonInfo.page_id}_${val}`
        } else {
          return `area_${this.course_id}_${this.commonInfo.page_id}_${val}`
        }
      }
    },
    swiperOption() {
      return {
        loop: false,
        autoplay: false,
        direction: 'horizontal',
        slidesPerView: 'auto',
        freeMode: false,
        lazy: {
          loadPrevNext: true
        },
        navigation: { // 设置点击箭头
          nextEl: '.swiper-button-next',
          prevEl: '.swiper-button-prev'
        },
        on: {
          tap: (e) => {
            const aId = e.target.parentElement.getAttribute('data-id')
            if (aId) {
              const project = this.commonInfo.list.find((e) => e.item_id === aId)
              this.toLink(project)
            }
          }
        }
      }
    }
    
  },
  watch: {
    fromType: {
      immediate: true,
      handler(val) {
        if (val === 'isSpecial') {
          this.getSpcialList()
          return
        }
        this.getCourseList()
      }
    },
    'commonInfo.list': {
      immediate: true,
      handler(newVal) {
        const fieldMap = {
          content_url: ['href'],
          content_id: ['item_id']
        }
        newVal.forEach((item) => {
          for (let targetKey in fieldMap) {
            if (!item.hasOwnProperty(targetKey)) {
              for (let sourceKey of fieldMap[targetKey]) {
                item[targetKey] = item[sourceKey]
              }
            }
          }
          if (this.fromType === 'isSpecial') {
            item.task_count = item.origin_data.task_count
          }
        })
      }
    }
  },
  methods: {
    handleClose() {
      this.popupShow = false
    },
    toPath() {
      if (this.fromType === 'isSpecial') {
        if (this.commonInfo.custom_link_url) {
          window.open(this.commonInfo.custom_link_url)
        } else {
          const url = `//sdc.qq.com/s/Shd3Jk?scheme_type=homepage&page_id=${this.commonInfo.page_id}`
          window.location.href = url
        }
        return
      }
      // 课单跳转
      this.popupShow = true
    },
    async getSpcialList() {
      const { area_id } = this.$route.query
      const param = {
        app_id: 'A9BiosXihR0h46ThNsAX',
        terminal_type: 1,
        condition: {
          orderby: [{ column: 'view_count_total', type: 'desc' }]
        },
        current: 1,
        size: 10
      }
      const detailApi = await specialDetail(area_id, { app_id: 'A9BiosXihR0h46ThNsAX' })
      const listApi = await specialAreaAPI(area_id, param)
      Promise.all([detailApi, listApi]).then((res) => {
        const name = res[0].name
        const page_id = res[0].page_id
        const custom_link_url = res[0].custom_link_url
        const list = res[1].records || []
        this.commonInfo = {
          name,
          list,
          page_id,
          custom_link_url
        }
      })
    },
    // 单个详情跳转
    async toLink(item) {
      const { module_id, content_id, content_url } = item
      window.BeaconReport('at_click', {
        eid: this.dtCourse('eid', item),
        remark: this.dtCourse('remark', item)
      })
      let url = this.fromType === 'isSpecial' ? `${content_url}&from=SpecialArea&page_id=${this.commonInfo.page_id}` : content_url
      if (this.fromType !== 'isSpecial') { // 课单调用
        await getCourseViewCount(this.$route.query.area_id)
      }
      // 码客、行家、图文、外链暂不支持跳转
      const moduleIds = [5, 6, 9]
      if (moduleIds.includes(module_id) || (module_id === 99 && !this.checkWebviewHost(url))) {
        this.$router.push({
          name: 'mobileError',
          query: {
            type: 2,
            href: encodeURIComponent(url)
          }
        })
      } else if (module_id === 1) { // 网络课
        // this.$router.push({
        //   name: 'grayPlay',
        //   query: {
        //     course_id: content_id || ''
        //   }
        // })
        window.location.href = url
      } else if (module_id === 2) { // 面授课
        window.location.href = `https://sdc.qq.com/s/Eom5bg?scheme_type=faceClassDetail&course_id=${content_id}`
      } else if (module_id === 8) {
        if (window.__wxjs_environment === 'miniprogram') {
          // 小程序内直接跳转
          window.wx.miniProgram.navigateTo({
            url: `/pages/networkCourse/article/index?graphic_id=${content_id}`
          })
        } else {
          // 打开二合一地址唤起小程序
          window.location.href = url
        }
      } else if (url) {
        window.location.href = url
      }
    },
    // 检查域名是否在webview的白名单内
    checkWebviewHost(url) {
      const host = [
        'https://auth-mgate.woa.com',
        'https://csig.lexiangla.com',
        'https://ihr.tencent.com',
        'https://iwiki.woa.com',
        'https://km.tencent.com',
        'https://km.woa.com',
        'https://lexiangla.com',
        'https://m-learn.woa.com',
        'https://mybucket-1258938271.cos.ap-chengdu.myqcloud.com',
        'https://ntsgw.woa.com',
        'https://oa.m.tencent.com',
        'https://panshi.tencent.com',
        'https://portal.learn.woa.com',
        'https://test-portal-learn.woa.com',
        'https://test-learn.woa.com',
        'https://learn.woa.com',
        'https://video-learn.woa.com',
        'https://exam.woa.com',
        'https://sdc.qq.com',
        'https://hangjia.woa.com',
        'https://qianlong.woa.com',
        'https://ntsapps.woa.com',
        'https://cos.learn.woa.com',
        'https://policy.woa.com',
        'https://s.test.yunassess.com',
        'https://static.taishan.qq.com',
        'https://test-cos.learn.woa.com'
      ]
      return host.findIndex(i => url.startsWith(i)) > -1
    },
    getCourseList() {
      const { area_id, from } = this.$route.query
      const param = {
        from,
        share_staff_name: '',
        share_staff_id: ''
      }
      getCourseDetailAPI(area_id, param).then((res) => {
        const list = res.content_basic_list || []
        const envName = env[process.env.NODE_ENV]
        list.forEach((v) => {
          const url = v.cover_img_url || `${envName.contentcenter}content-center/api/v1/content/imgage/${v.cover_img_file_id}/preview`
          v.photo_url = url
          v.labels = (v.label_list || []).map((v) => v.label_name)
        })
        this.commonInfo = {
          name: res.name,
          list,
          page_id: res.cl_id
        }
      })
    },
    dtCourse(type, row) {
      const container = this.fromType === 'isSpecial' ? `专区` : `课单`
      let actRow = this.actTypes.find((e) => row.module_id === e.module_id)
      const data = {
        page: this.courseData.course_name,
        page_type: this.pageTypeName + '详情页面-移动新版', 
        container: container,
        click_type: 'data',
        content_type: row.module_name,
        content_id: row.content_id,
        content_name: row.content_name,
        act_type: actRow.act_type,
        container_id: '',
        page_id: '',
        terminal: 'H5'
      }
      if (type === 'remark') {
        return JSON.stringify(data)
      } else if (type === 'eid') {
        return `element_${this.course_id}_${this.commonInfo.page_id}_${row.content_id}`
      } else {
        return `area_${this.course_id}_${this.commonInfo.page_id}_${row.content_id}`
      }
    }
  }
}
</script>
<style lang="less" scoped>
.course-card-container {
  .course-title-box {
    line-height: 16px;
    font-size: 12px;
    display: flex;
    justify-content: space-between;
    margin-top: 16px;
    margin-bottom: 8px;

    .title-num {
      color: #000000e6;
      font-weight: bold;
      width: 300px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap
    }

    .right-icon {
      background: url('~@/assets/img/mobile/right-arrow.png') no-repeat;
      background-size: 16px;
      width: 16px;
      height: 16px;
      display: inline-block
    }
  }

  .item-course-swiper {
    width: 109px;

    .title {
      color: #999999;
      line-height: 16px;
      font-size: 11px;
    }

    .img-box {
      position: relative;
      margin-bottom: 6px;
      width: 109px;
      height: 64px;

      .module-icon {
        position: absolute;
        top: 4px;
        left: 4px;
        background-color: #0052D9;
        color: #fff;
        border-radius: 2px;
        height: 16px;
        line-height: 16px;
        padding: 0 8px;
        font-size: 10px;
        z-index: 99;
      }

      .play-time {
        position: absolute;
        right: 4px;
        bottom: 4px;
        font-size: 10px;
        border-radius: 5px;
        background: #00000099;
        color: #fff;
        height: 20px;
        line-height: 20px;
        padding: 0 6px;
      }
    }

    :deep(.item-img) {
      width: 109px;
      height: 64px;
      text-align: center;
      line-height: 91px;

      img,
      .van-image__loading {
        width: 109px;
        height: 64px;
        border-radius: 4px;
      }
    }
  }

  .active-item-swiper {
    .title {
      color: #0052D9;
    }
  }

  .item-course-swiper+.item-course-swiper {
    margin-left: 8px;
  }
}
.course-popup-container {
  height: calc(100% - 210px);
  :deep(.body ){
    height: 100%;
    overflow: hidden;
    .couse-list-box {
      height: calc(100% - 40px);
      overflow: auto;
      padding: 16px 16px 24px;
      .no-more {
        color: #00000066;
        text-align: center;
        margin-top: 24px;
        font-size: 14px;
      }
    }
    .header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 16px;
      border-bottom: 1px solid #EDEDED;
      .title {
        font-size: 16px;
        font-weight: bold;
        line-height: 24px;
        color: #000000e6;
        width: 190px;
        text-align: center;
      }
      .close-icon {
        display: inline-block;
        width: 24px;
        height: 24px;
        background: url('~@/assets/img/close.png') no-repeat center / cover;
      }
    }
  }
}
</style>
