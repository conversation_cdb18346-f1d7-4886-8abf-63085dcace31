<template>
  <div class="choose-participation-method-popup activity-common">
    <!-- Mobile -->
    <div class="mobile-template" v-if="isMobile">
      <van-dialog className="van-dialog-customer" overlayClass="overlay-customer" v-model="visible" :showConfirmButton="false">
        <div class="van-dialog-header">选择参加方式</div>
        <div class="van-dialog-content">
          <div class="tool-row" v-if="isNeedAppovel">
            <div class="label">需审批</div>
          </div>
          <el-radio v-model="form.join_type" :label="1" :disabled="disableRadio" >线下参加{{ OfflineText }} <span class="radio-text ml-22">{{ locationText }}</span></el-radio>
          <el-radio v-model="form.join_type" :label="2" >线上参加（不限名额 ）：<span class="radio-text ml-22">腾讯会议，报名成功后自动推送授课链接</span></el-radio>
        </div>
        <div class="btn-colume mt-36">
          <div class="btn-box cancel-button" @click="handlerCancel">取消</div>
          <div class="btn-box confirm-button" @click="handlerConfirm">确认</div>
        </div>
      </van-dialog>
    </div>

    <!-- PC -->
    <div class="pc-template" v-else>
      <el-dialog title="" :visible.sync="visible" width="564px" :close-on-click-modal="false" :before-close="handlerCancel">
        <div slot="title" class="dialog-header">选择参加方式</div>
        <div class="dialog-content">
          <div class="tool-row" v-if="isNeedAppovel">
            <div class="label">需审批</div>
          </div>
          <el-form :model="form" ref="form" label-width="0">
            <el-form-item label="">
              <el-radio v-model="form.join_type" :label="1" :disabled="disableRadio" >线下参加{{ OfflineText }}</el-radio>
              <el-radio v-model="form.join_type" :label="2" >线上参加（不限名额 ）：腾讯会议，报名成功后自动推送授课链接</el-radio>
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button class="cancel-btn" @click="handlerCancel" size="small">取消</el-button>
          <el-button class="confirm-btn" type="primary" @click="handlerConfirm" size="small">确认</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
export default {
  name: 'chooseParticipationMethodPopup',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    regStatus: {
      type: Number,
      default: 0
    },
    courseData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      form: {
        join_type: 1
      }
    }
  },
  computed: {
    isMobile () {
      var ua = navigator.userAgent.toLowerCase()
      return /iphone|ipad|ipod|android|blackberry|mini|windows\sce|phone|mobile/.test(ua) || /micromessenger/.test(ua)
    },
    locationText () {
      const { city, location } = this.courseData
      return `${city}-${location}`
    },
    OfflineText () {
      // regStatus: 1-可报名 2-已报满-等待列表 3-已加入等待列表 4-截至报名 5-已报名-可注销 6-已报名-不可注销

      // 线下参加（剩余名额 999 ）：深圳-腾讯滨海大厦-S2024
      // 线下参加（已报满，加入等待列表）：深圳-腾讯滨海大厦-S2024
      // 线下参加（已报满，无法报名）：深圳-腾讯滨海大厦-S2024  // 禁用
      // 线下参加（不限名额）：深圳-腾讯滨海大厦-S2024

      // is_limit_student_count ：是否限制人数
      // max_student_count : 最大人数限制
      // allow_waiting_list : 是否允许加入等待列表
      // regist_count : 已报名人数
      const { max_student_count, is_limit_student_count, regist_count, allow_waiting_list } = this.courseData
      let text = ''
      if ([1, 2].includes(this.regStatus)) {
        // 是否限制人数
        if (is_limit_student_count) {
          // 是否超过限制人数
          let count = max_student_count - regist_count
          if (count > 0) {
            text = `剩余名额 ${count}`
          } else {
            // 超过限制人数，是否允许加入等待列表
            text = `已报满，${allow_waiting_list ? '加入等待列表' : '无法报名'}`
          }
        } else {
          text = '不限名额'
        }
        if (this.isMobile) {
          return `（${text}）：`
        }
        return `（${text}）：${this.locationText}`
      }
      return ''
    },
    // 线下参加无法报名，禁用选项
    disableRadio () {
      const { max_student_count, is_limit_student_count, regist_count, allow_waiting_list } = this.courseData
      let count = max_student_count - regist_count
      if ([1, 2].includes(this.regStatus) && is_limit_student_count && count < 1 && !allow_waiting_list) {
        return true
      }
      return false
    },
    isNeedAppovel() {
      return this.courseData.need_appovel
    }
  },
  watch: {
    disableRadio: {
      handler (val) {
        if (val) {
          this.form.join_type = 2
        }
      },
      immediate: true
    }
  },
  created() { },
  mounted() { },
  beforeDestroy() { },
  methods: {
    handlerConfirm() {
      this.$emit('confirm', this.form)
      this.handlerCancel()
    },
    handlerCancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style lang="less" scoped>
@import '~assets/css/activity-common.less';
  .choose-participation-method-popup {
    .mobile-template {
      :deep(.overlay-customer) {
        background: #00000066;
      }
      :deep(.van-dialog-customer) {
        padding: 32px 24px 24px;
        width: 332px;
        border-radius: 12px;
        background: #FFF;
      }

      .van-dialog-header {
        color: #000000e6;
        text-align: center;
        font-family: "PingFang SC";
        font-size: 18px;
        font-style: normal;
        font-weight: 600;
        line-height: 26px;
        margin-bottom: 20px;
      }

      .van-dialog-content {
        .radio-text {
          color: #000000e6;
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px;
          display: inline-block;
        }
        :deep(.el-radio) {
          margin-right: 0px;
          &:not(:last-child) {
            margin-bottom: 16px;
          }
          .el-radio__input.is-checked .el-radio__inner {
            background-color: #fff;
            border-color: #0052D9;
          }
          .el-radio__input.is-checked .el-radio__inner:after {
            width: 6px;
            height: 6px;
          }
          .el-radio__label {
            color: #000000e6;
            padding-left: 8px;
            line-height: 22px;
            white-space: break-spaces;
          }
        }
      }

      .btn-colume {
        margin-top: 36px;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .btn-box {
          width: 136px;
          line-height: 24px;
          padding: 8px 16px;
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 6px;
        }
        .cancel-button {
          background: #F2F3FF;
          color: #0052d9;
        }
        .confirm-button {
          color: #ffffff;
          background: #0052D9;
        }
      }

      .tool-row {
        padding-top: 0;
      }
    }

    .pc-template {
      .dialog-content {
        height: 120px;
        :deep(.el-radio) {
          display: flex;
          margin-bottom: 16px;
          .el-radio__label {
            display: inline-block;
            white-space: pre-wrap;
            color: #000000e6;
          }
          .el-radio__input.is-checked+.el-radio__label {
            color: #000000e6;
          }
        }
      }
    }

    .tool-row {
      padding: 4px 0 16px;
      display: flex;
      flex-wrap: wrap;
      .label {
        padding: 0 8px;
        height: 20px;
        line-height: 20px;
        text-align: center;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        border-radius: 2px;
        background: #ECF2FE;
        user-select: none;
      }
    }
  }
</style>
