<template>
  <div>
    <el-dialog
      :visible.sync="visible"
      v-if="itemData.label_type === 1"
      :before-close="handleClose"
      :close-on-click-modal="click_modal"
      :append-to-body="true"
      custom-class="subsDialog"
      width="480px">
      <header slot="title">
        <img :src="require('@/assets/img/warn.png')" alt="">
        取消官方标签
      </header>
      <div class="text">取消官方标签后，该标签将不再支持用户订阅，同时会为已订阅该标签的用户推送标签失效提醒并自动取消订阅，确定取消吗？</div>
      <p class="subsCount">当前订阅人数：<a>{{itemData.subscribe_count}}</a></p>
      <span slot="footer" class="dialog-footer">
        <el-button size="small"  @click="out">取消</el-button>
        <el-button size="small" type="primary" @click="dialogVisible()">确定取消</el-button>
      </span>
    </el-dialog>
    <el-dialog
      v-else
      title="消息"
      :visible.sync="visible"
      width="20%"
      :before-close="handleClose"
      :close-on-click-modal="click_modal"
    >
      <!-- <p class="title_p" v-if="itemData.label_type === 1">
        确定要给此官方标签进行取消官方操作吗?
      </p> -->
      <div class="block" v-if="itemData.label_type !== 1">
        <span class="demonstration">标签分类:</span>
        <el-cascader
          size="small"
          :options="optionslist"
          v-model="classification"
          :props="{ emitPath: false }"
          clearable
        ></el-cascader>
      </div>
      <p class="title" v-if="itemData.label_type !== 1">
        注：设为官方后将奖励标签作者3通用积分
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="out">取 消</el-button>
        <el-button type="primary" @click="dialogVisible()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { update_label_type } from 'config/api.conf'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    itemData: {},
    optionslist: []
  },
  data() {
    return {
      classification: '',
      click_modal: false
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    dialogVisible() {
      if (this.classification === '' && this.itemData.label_type !== 1) {
        this.$message({
          type: 'error',
          message: '标签分类不能为空'
        })
      } else {
        let params = {
          label_id: this.itemData.label_id,
          category_id: this.classification,
          label_type: this.itemData.label_type === 1 ? 2 : 1
        }
        update_label_type(params).then((res) => {
          this.$message({
            type: 'success',
            message:
              this.itemData.label_type === 1 ? '已取消官方' : '已设为官方'
          })
          this.classification = ''
          this.$parent.getlist()
        })
      }
      this.$emit('update:visible', false)
    },
    out() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.block {
  margin-bottom: 10px;
  span {
    margin-right: 20px;
  }
}
.title_p {
  font-size: 16px;
  margin-bottom: 10px;
  line-height: 26px;
}
.title {
  color: #ff7c51;
}
/deep/.el-dialog.subsDialog {
  font-family: "PingFang SC";
  border-radius: 9px;
  .el-dialog__header{
    padding: 32px 32px 16px;
    border: none;
    header {
      display: flex;
      align-items: center;
      height: 24px;
      color: #000000e6;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      img{
        width: 24px;
        margin-right: 8px;
      }
    }
    .el-dialog__headerbtn{
      top: 36px;
      right: 34px;
    }
  }
  .el-dialog__body {
    padding: 0 32px;
    .text {
      color: #00000099;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .subsCount {
      margin-top: 10px;
      color: #000000e6;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      a{
        color: #0052d9;
      }
    }
  }
  .el-dialog__footer{
    padding: 0 32px 32px;
    margin-top: 24px;
    .el-button--default{
      width: 60px;
      color: #000000e6;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      border: none;
      background: var(--Gray-Gray3-, #E7E7E7);
    }
    .el-button--primary{
      width: 88px;
      color: #ffffff;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      border: none;
      background: var(--Brand-Brand7-Normal, #0052D9);
    }
    .el-button + .el-button {
      margin-left: 8px;
    }
  }
}
</style>
