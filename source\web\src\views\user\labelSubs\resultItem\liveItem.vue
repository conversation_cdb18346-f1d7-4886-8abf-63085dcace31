<template>
    <div class="labelCardItem">
        <div class="courseImage">
            <defaultImage :info="info"/>
            <span class="liveIcon" v-if="liveStatus === 1"><img :src="require('@/assets/img/label/living.png')" alt=""></span>
            <span class="duration" v-if="liveStatus === 0">未开始</span>
        </div>
        <div  class="courseInfo">
            <moduleTitle :info="info" :name="!liveStatus? '直播预约':'直播'" />
            <div class="courseView flex align-center liveTime" v-if="info.origin_data?.start_time">
              <img :src="require('@/assets/img/label/time2x.png')">
              <span class="timeRang">{{info.origin_data.start_time?.substring(0,info.origin_data.start_time.lastIndexOf(':'))}}~{{info.origin_data.end_time?.substring(0,info.origin_data.end_time.lastIndexOf(':'))}}</span>
            </div>
            <relatedContent :info="info" :curModuleId="curModuleId" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :subsType="subsType" :contentType="contentType"></relatedContent>
        </div>
    </div>
</template>
<script>
import defaultImage from './child/defaultImage.vue'
import moduleTitle from './child/moduleTitle.vue'
import relatedContent from './child/relatedContent.vue'
export default {
  name: 'liveItem',
  props: ['info', 'curModuleId', 'curLabelId', 'sortSubsIds', 'subsType', 'contentType'],
  data() { 
    return {
      nowTime: new Date().getTime()
    }
  },
  components: {
    defaultImage,
    moduleTitle,
    relatedContent
  },
  computed: {
    liveStatus() {
      const { start_time, end_time } = this.info.origin_data
      if (!start_time || !end_time) return 3
      if (new Date(start_time).getTime() > this.nowTime) return 0
      else if (new Date(start_time).getTime() < this.nowTime && new Date(end_time).getTime() > this.nowTime) return 1
      // else if (new Date(end_time).getTime() < this.nowTime) return 2
      else return 3
    }
  }
}
</script>
<style lang="less" scoped>
@import '../style/item.less';
</style>
