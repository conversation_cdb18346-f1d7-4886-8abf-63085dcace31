<template>
  <div class="project-details" v-if="detailsData">
    <el-row class="list-con">
      <div class="list-card">
        <div class="head">
          <span class="title">学员总数</span>
          <span class="btn-details" @click="openUrl('members')">详情</span>
        </div>
        <div class="num">{{ detailsData.student_count }}</div>
      </div>
      <div class="list-card">
        <div class="head">
          <span class="title">自主报名人数</span>
        </div>
        <div class="num">{{ detailsData.registe_count }}</div>
      </div>
      <div class="list-card">
        <div class="head">
          <span class="title">完成培训人数</span>
        </div>
        <div class="num">{{ detailsData.finished_count }}</div>
      </div>
      <div class="list-card">
        <div class="head">
          <span class="title">培训完成率</span>
          <el-tooltip class="btn-tips" effect="dark" content="计算公式为“完成培训人数/学员总数”" placement="top-start">
            <img class="info-circle" src="@/assets/mooc-img/info-circle.png" alt="" srcset="">
          </el-tooltip>
        </div>
        <div class="num">{{ detailsData.finished_rate }}%</div>
      </div>
    </el-row>
    <el-row class="list-con">
      <div class="list-card">
        <div class="head">
          <span class="title">应学任务学习进度</span>
          <el-tooltip class="btn-tips" effect="dark" content="计算公式为“所有学员已完成应学任务总数/所有学员应学任务总数”" placement="top-start">
            <img class="info-circle" src="@/assets/mooc-img/info-circle.png" alt="" srcset="">
          </el-tooltip>
        </div>
        <div class="num">{{ detailsData.required_progress }}%</div>
        <div class="foot">
          <span>应学任务数量 {{ detailsData.required_task_count }}</span>
        </div>
      </div>
      <div class="list-card">
        <div class="head">
          <span class="title">选学任务学习进度</span>
          <el-tooltip class="btn-tips" effect="dark" content="计算公式为“所有学员已完成选学任务总数/所有学员选学任务总数”" placement="top-start">
            <img class="info-circle" src="@/assets/mooc-img/info-circle.png" alt="" srcset="">
          </el-tooltip>
        </div>
        <div class="num">{{ detailsData.non_required_progress }}%</div>
        <div class="foot">
          <span>选学任务数量 {{ detailsData.non_required_task_count }}</span>
        </div>
      </div>
      <div class="list-card">
        <div class="head">
          <span class="title">任务总学习进度</span>
          <el-tooltip class="btn-tips" effect="dark" content="计算公式为“所有学员已完成任务总数/所有学员任务总数”" placement="top-start">
            <img class="info-circle" src="@/assets/mooc-img/info-circle.png" alt="" srcset="">
          </el-tooltip>
        </div>
        <div class="num">{{ detailsData.task_progress }}%</div>
        <div class="foot">
          <span>总任务数量 {{ detailsData.total_task_count }}</span>
        </div>
      </div>
      <div class="list-card">
        <div class="head">
          <span class="title">线上学习人均时长（小时）</span>
        </div>
        <div class="num">{{ detailsData.study_time_on_line }}</div>
        <div class="foot">
          <span>PC端 {{ studyTimePc || 0 }}%</span>
          <span>移动端 {{ studyTimeMobile || 0 }}%</span>
        </div>
      </div>
    </el-row>
    <el-row class="list-con">
      <div class="list-card">
        <div class="head">
          <span class="title">课程评分</span>
          <span class="btn-details" @click="openUrl('interactive', 'scoreDetails')">详情</span>
        </div>
        <div class="num">{{ detailsData.score.toFixed(1) }}</div>
        <div class="foot">
          <span>评分人数 {{ detailsData.score_student_count }}</span>
        </div>
      </div>
      <div class="list-card">
        <div class="head">
          <span class="title">评论量</span>
          <span class="btn-details" @click="openUrl('interactive', 'commentManage')">详情</span>
        </div>
        <div class="num">{{ detailsData.comment_count }}</div>
        <div class="foot">
          <span>评论人数 {{ detailsData.comment_user_count }}</span>
        </div>
      </div>
    </el-row>
  </div>
</template>

<script>
import { getSummaryProjectSummaryAPI } from '@/config/mooc.api.conf.js'

export default {
  data () {
    return {
      detailsData: null
    }
  },
  computed: {
    studyTimePc() {
      let total = this.detailsData.study_time_on_line_pc + this.detailsData.study_time_on_line_mobile
      return (this.detailsData.study_time_on_line_pc / total * 100)
    },
    studyTimeMobile() {
      let total = this.detailsData.study_time_on_line_pc + this.detailsData.study_time_on_line_mobile
      return (this.detailsData.study_time_on_line_mobile / total * 100)
    }
  },
  created () {
    this.getDetails()
  },
  methods: {
    getDetails () {
      const id = this.$route.query.mooc_course_id
      getSummaryProjectSummaryAPI(id).then(res => {
        this.detailsData = res
      })
    },
    // 详情跳转
    openUrl (routeName, tab) {
      const urlSrc = `${location.origin}/training/mooc/manage/${routeName}${location.search}${tab ? '&tab=' + tab : ''}`
      window.open(urlSrc)
    }
  }
}
</script>

<style lang="less" scoped>
  .project-details {
    margin-top: 4px;
    background: #fff;
    .list-con {
      display: flex;
      margin-bottom: 16px;
      .list-card {
        width: 292px;
        border: 1px solid #eee;
        border-radius: 4px;
        padding: 24px 32px;
        margin-right: 16px;
        .head {
          color: #666;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .btn-details {
            color: #3464e0;
            cursor: pointer;
          }
          .info-circle {
            width: 16px;
            height: 16px;
            cursor: pointer;
          }
        }
        .num {
          color: #000;
          font-size: 28px;
          margin: 10px 0 16px;
        }
        .foot {
          border-top: 1px solid #dcdcdc;
          color: #666;
          padding: 10px 0 0;
          & > span {
            margin-right: 20px;
          }
        }
      }
    }
  }
</style>
