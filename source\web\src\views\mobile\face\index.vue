<template>
  <div class="face-page" id="face-scroll">
    <div class="top-info">
      <img :src="courseData.photo_url ? courseData.photo_url : require('@/assets/img/activity-default.png')" alt="">
      <span class="top-header-l-tips">课程</span>
      <div class="right-info">
        <div class="face-title">{{courseData.course_name}} </div>
        <div class="face-base">
          <span class="top-iconInfo">
            <i class="icon-view"></i>
            <span>{{ courseData.view_count || 0 }} </span>
            <i class="icon-scores"></i>
            <span> {{courseData.avg_score || 0}}分</span>
            <span class="time">{{ courseData.created_at ||  ':--' }}</span>
          </span>
        </div>
      </div>
    </div>
    <div class="line-8"></div>
    <!-- tab栏 -->
    <div class="tabs-card">
      <div class="tabs">
        <div :class="['item-tab', {'active-item-tab': activeKey === item.key}]" v-for="item in tabList" :key="item.key" @click="changeTabs(item.key)">
          <span>{{ item.text }}</span>
          <span v-show="activeKey === item.key" class="active-line"></span>
        </div>
      </div>
      <div class="face-list-content" v-if="activeKey === 'list'">
        <div class="date-select">
          <div :class="['city date-item']" @click="selectCity"> <i class="icon-Local"></i>{{currentCity}}</div>
          <div :class="['date-item', {'date-item-current': dateCurrent === item.formatDate }]" v-for="(item, index) in classDateSelect" :key="index" @click="handlerClickItem(item)"> {{item.formatDate}} </div>
        </div>
      </div>
    </div>
    <!-- 开班列表 -->
    <classList ref="classList" v-show="activeKey === 'list'" :classLists="classLists" :registered="registered" :courseData="courseData"></classList>
    <!-- 介绍 -->
    <desContent v-if="activeKey === 'des'" :courseData="courseData" courseType="face"></desContent>
    <!-- 笔记 -->
    <notes v-if="activeKey === 'note'" :courseData="courseData" module_id="2" courseType="face"></notes>
    <!-- 延伸学习 -->
    <extendLearning v-if="activeKey === 'extand'" :extandList="extandList" :courseData="courseData"  courseType="face"></extendLearning>
    <!-- 专区推广内容 -->
    <courseExtend v-if="activeKey === 'list' || activeKey === 'des'" :courseData="courseData" courseType="face"></courseExtend>
    <!-- 底部操作按钮 -->
    <bottomNav @handleBtChoice="handleBtChoice" ref="bottomNavRef" :courseData="courseData" scrollID="window" courseType="face"></bottomNav>
    <!-- 评论弹窗 -->
    <commentPopup :commentShow.sync="commentShow" :courseData.sync="courseData" :isShowZan="false" courseType="face"></commentPopup>
    <!-- 添加课单 -->
    <addCousePopup :show.sync="addCourseShow" :courseData.sync="courseData" courseType="face"></addCousePopup>
    <!-- 分享提示框 -->
    <shareTipDialog v-model="showShareDialog" :portraitScreen="true"></shareTipDialog>
    <!-- 城市选择 -->
    <van-popup v-model="showPicker" position="bottom">
      <van-picker show-toolbar :columns="cityArr" @cancel="showPicker = false" @confirm="onConfirm" />
    </van-popup>
  </div>
</template>

<script>
import {
  getFaceDetails,
  actClassList,
  getExtanContentList,
  setSubscribe
} from 'config/api.conf'
import bottomNav from '@/views/mobile/videoDetailGray/child/bottomNav.vue'
import classList from './components/classListMobile.vue'
import notes from '@/views/mobile/videoDetailGray/notes.vue'
import extendLearning from '@/views/mobile/videoDetailGray/extendLearning.vue'
import courseExtend from './components/courseExtend.vue'
import shareTipDialog from '@/views/mobile/videoDetailGray/child/shareTipDialog.vue'
import addCousePopup from '@/views/mobile/videoDetailGray/child/addCousePopup.vue'
import commentPopup from '@/views/mobile/videoDetailGray/child/commentPopup.vue'
import desContent from './components/desContent.vue'
import MoocJs from 'sdc-moocjs-integrator'
import { Toast } from 'vant'
export default {
  components: {
    bottomNav,
    classList,
    notes,
    extendLearning,
    courseExtend,
    shareTipDialog,
    addCousePopup,
    commentPopup,
    desContent
  },
  data() {
    return {
      currentCity: '全部城市',
      showPicker: false,
      isMoreDate: false,
      commentShow: false,
      showShareDialog: false,
      addCourseShow: false,
      activeKey: 'list',
      dateCurrent: '全部日期',
      courseData: {},
      extandList: [],
      addCourseDialogData: {},
      tabList: [
        { key: 'list', text: '开班列表' },
        { key: 'des', text: '介绍' },
        { key: 'note', text: '笔记' }
      ],
      classLists: null,
      actClassListArr: [], // 开班列表
      cityArr: [], // 城市列表
      partClassDateSelect: [], // 时间循环数据
      registered: false
    }
  },
  watch: {
    dateCurrent(val) {
      if (val) {
        if (val.includes('全部')) {
          this.classLists = this.actClassListArr
        } else {
          this.classLists = this.actClassListArr.filter(
            (item) => item.formatDate === val
          )
        }
      }
    }
  },
  computed: {
    course_id() {
      return this.$route.query.course_id || ''
    },
    isFormMooc() {
      return this.$route.query.from === 'mooc'
    },
    classDateSelect() {
      if (!this.actClassListArr.length) {
        return []
      }
      let resList = this.removDuplication(this.actClassListArr, 'formatDate')
      return [{ date: '全部日期', formatDate: '全部日期' }, ...resList]
    }
  },
  mounted() {
    this.getCourseDetail()
    this.getExtandList()
  },
  methods: {
    async getCourseDetail() {
      try {
        const { share_staff_id, share_staff_name } = this.$route.query
        let params = {
          course_id: this.course_id,
          share_staff_id: share_staff_id || '',
          share_staff_name: share_staff_name || ''
        }
        const data = await getFaceDetails(params)
        document.title = `${data.course_name}_Q-Learning`
        this.courseData = data
        this.courseData.photo_url = data.ext_info.photo_url
        this.courseData.course_desc = data.ext_info.course_desc
        this.courseData.sub_class_count = data.ext_info.sub_class_count
        const { course_name, photo_url, course_desc, course_id, content_id } =
          this.courseData
        const net_url = location.hostname.endsWith('.woa.com')
          ? `${process.env.VUE_APP_PORTAL_HOST_WOA}/training/face?course_id=${course_id}`
          : `${process.env.VUE_APP_PORTAL_HOST}/training/face?course_id=${course_id}`
        this.addCourseDialogData = {
          ...this.addCourseDialogData,
          content_name: course_name,
          cover_img_url: photo_url,
          description: course_desc,
          href: net_url,
          item_id: course_id,
          origin: location.origin
        }
        this.courseData.content_id = content_id
        this.actClassList()
      } catch (err) {
        if (err.code) {
          if (this.isFormMooc && (err.code === 403 || err.code === 500)) {
            MoocJs.sendErrorInfo(err.message)
            return
          }
          let type = 0
          if (err.code === 403) {
            type = 3
            if (err.message.includes('权限')) {
              type = 5
            }
          } else if (err.code === 500) {
            type = 5
          }
          this.$router.replace({
            name: 'mobileError',
            query: {
              type
            }
          })
        }
      }
    },
    // 开班列表
    async actClassList(city) {
      let params = {
        course_id: this.course_id
      }
      // start_time: '',
      // city: ''
      let data = await actClassList(params)
      data.forEach((item) => {
        if (item.teaching_type !== 1) {
          item.city = '在线授课'
        }
      })
      let cityArr = JSON.parse(JSON.stringify(data))
      this.cityArr = [
        { city: '全部城市' },
        ...this.removDuplication(cityArr, 'city')
      ]
      this.cityArr.forEach((item) => {
        item.text = item.city
      })
      const { sub_class_count } = this.courseData
      if (sub_class_count && Number(sub_class_count) > 1) {
        let numReg = data.filter((item) =>
          [3, 5, 6, 7].includes(item.regist_type.status)
        )
        this.registered = numReg.length >= sub_class_count
      } else {
        this.registered = data.some((item) =>
          [3, 5, 6, 7].includes(item.regist_type.status)
        )
      }
      // 没有开班列表定位到介绍
      if (!data.length) {
        this.activeKey = 'des'
      }
      this.actClassListArr = data
      this.actClassListArr.forEach((item) => {
        item.formatDate = this.formatDateTime(item.start_time)
        item.start_end_time = this.formatDateTimeStartEnd(
          item.start_time,
          item.end_time
        )
      })
      // 筛选城市
      if (city) {
        this.actClassListArr = this.actClassListArr.filter(
          (item) => item.city === city
        )
      }
      this.classLists = this.actClassListArr
    },
    // 开班提醒 1 订阅，0退订
    handlerSetSubscribe() {
      const { is_subscribed } = this.courseData
      let params = {
        course_id: this.course_id,
        is_subscribed: Number(is_subscribed) === 1 ? 0 : 1
      }
      setSubscribe(params).then((res) => {
        Toast.success(
          Number(is_subscribed) === 1 ? '已退订开班提醒' : '成功订阅开班提醒'
        )
        this.getCourseDetail()
      })
    },
    getExtandList() {
      const params = {
        act_id: this.course_id,
        act_type: 1
      }
      this.loading = true
      getExtanContentList(params)
        .then((data) => {
          this.loading = false
          this.extandList = (data || []).map((item) => {
            return {
              ...item,
              module_name: item.content_module_name, // 类型名称
              module_id: item.content_module_id, // 类型id
              content_name: item.content_name, // 内容名称
              content_url: item.href,
              item_id: item.content_item_id,
              description: item.course_desc, // 简介
              play_total_count: item.view_count, // 查看次数
              word_num: item.word_num, // 图文/笔记 - 字数
              praise_count: '', // 图文/笔记/案例/码客 - 点赞数
              avg_score: item.avg_score, // 得分
              created_time: item.content_created_time, // 时间
              task_count: item.sub_count,
              labels: (item.labels || []).map((v) => v.label_name),
              photo_url: item.photo_url || '',
              origin_data: {
                expert_name: '', // 行家-人员名称
                meet_num: 0, // 咨询量
                avg_score: '', // 评分
                start_time: '', // 活动开始时间
                end_time: '' // 活动结束时间
              }
            }
          })
          if (this.extandList.length) {
            this.tabList.splice(2, 0, { key: 'extand', text: '延伸学习' })
          }
        })
        .catch(() => {
          this.loading = false
        })
    },
    formatDateTimeStartEnd(start, end) {
      const dateTimeStart = new Date(start)
      const dateTimeEnd = new Date(end)
      const formattedDateStart =
        this.$moment(dateTimeStart).format('YYYY/MM/DD')
      const formattedDateEnd = this.$moment(dateTimeEnd).format('YYYY/MM/DD')
      const ltStart = this.$moment(dateTimeStart).format('HH:mm')
      const ltEnd = this.$moment(dateTimeEnd).format('HH:mm')
      const weekArr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekdayStart = weekArr[dateTimeStart.getDay()] // 获取星期信息
      const weekdayEnd = weekArr[dateTimeEnd.getDay()] // 获取星期信息
      const startDateJoin = `${formattedDateStart}(${weekdayStart}) ${ltStart}`
      const endDateJoin = `${formattedDateEnd}(${weekdayEnd}) ${ltEnd}`
      return `${startDateJoin} - ${endDateJoin}` // 组合日期和星期信息并返回
    },
    formatDateTime(date) {
      const dateTime = new Date(date)
      const formattedDate = this.$moment(dateTime).format('YYYY/MM/DD')
      const getDay = dateTime.getDay()
      const weekday = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'] // 获取星期信息
      return `${formattedDate}（${weekday[getDay]}）` // 组合日期和星期信息并返回
    },
    // 去重
    removDuplication(arr, type) {
      let resArr = arr.reduce((result, obj) => {
        if (
          !result.some(
            (item) => JSON.stringify(item[type]) === JSON.stringify(obj[type])
          )
        ) {
          obj[type] === '在线授课' ? result.unshift(obj) : result.push(obj)
        }
        return result
      }, [])
      return resArr
    },
    changeTabs(val) {
      this.activeKey = val
      // 初始化底部导航状态
      this.$nextTick(() => {
        this.$refs.bottomNavRef.initShow()
      })
    },
    handlerClickItem(val) {
      this.dateCurrent = val.formatDate
    },
    selectCity() {
      this.showPicker = true
    },
    onConfirm(val) {
      const { city } = val
      this.actClassList(city.includes('全部城市') ? '' : city)
      this.showPicker = false
      this.currentCity = city
      this.dateCurrent = '全部日期'
      console.log(val, '确认城市')
    },
    // 底部按钮显示
    handleBtChoice(val) {
      console.log('底部按钮', val)
      if (val.icon === 'home') {
        const url = `//sdc.qq.com/s/Shd3Jk?scheme_type=homepage`
        window.location.href = url
      } else if (val.icon === 'comment') {
        this.commentShow = true
      } else if (val.icon === 'share') {
        this.showShareDialog = true
      } else if (val.icon === 'add') {
        this.addCourseShow = true
      }
    }
  }
}
</script>

<style lang="less" scoped>
.face-page {
  background-color: #fff;
  .line-8 {
    height: 8px;
    width: 100%;
    background-color: #f3f5f7;
  }
  .top-info {
    // position: fixed;
    display: flex;
    background-color: #fff;
    padding: 12px;
    margin-bottom: 10px;
    position: relative;
    img {
      width: 92px;
      height: 62px;
      border-radius: 2px;
      margin-right: 10px;
    }
    .top-header-l-tips {
      position: absolute;
      left: 16px;
      top: 16px;
      font-size: 12px;
      border-radius: 2px;
      display: inline-block;
      width: 32px;
      height: 18px;
      line-height: 18px;
      text-align: center;
      color: #fff;
      background-color: #0052d9;
    }
    .right-info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .face-title {
        font-size: 14px;
        font-weight: 600;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        text-overflow: ellipsis;
        white-space: normal;
      }
    }
    .face-base {
      display: flex;
      align-items: center;
      color: #777777;
      // margin-bottom: 10px;
      font-size: 10px;
      i {
        display: inline-block;
        margin-right: 4px;
        width: 16px;
        height: 16px;
      }
      .top-iconInfo {
        margin-right: 40px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        .icon-view {
          background: url('~@/assets/img/view.png') no-repeat center/cover;
        }
        .icon-scores {
          background: url('~@/assets/img/icon-scores.png') no-repeat
            center/cover;
          margin-left: 16px;
        }
        .time {
          margin-left: 16px;
        }
      }
    }
  }
  .tabs-card {
    position: sticky;
    z-index: 1000;
    top: 0;
    width: 100%;
    box-shadow: 0 4px 4px 0 #eeeeee40;
    // display: flex;
    // justify-content: space-between;
    background-color: #fff;
    .tabs {
      display: flex;
      align-items: center;
      padding-left: 20px;
      .item-tab {
        color: #00000099;
        font-size: 12px;
        line-height: 40px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
      }
      .item-tab + .item-tab {
        margin-left: 26px;
      }
      .active-item-tab {
        font-size: 14px;
        font-weight: bold;
        color: #0052d9;
      }
      .active-line {
        position: absolute;
        bottom: 0px;
        display: inline-block;
        width: 28px;
        height: 2px;
        background-color: #0052d9;
      }
    }
    .back-play {
      margin-right: 12px;
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #333333;
      .switch-content {
        height: 20px;
        margin-left: 4px;
        color: #ffffffe6;
      }
    }
    .face-list-content {
      padding: 10px;
      background-color: #fff;
      box-shadow: 0 4px 4px 0 #eeeeee40;
      border-top: 1px solid #eee;
      .city {
        display: flex;
        align-items: center;
        .icon-Local {
          background: url('~@/assets/img/mobile/city-icon.png') no-repeat center /
            cover;
          display: block;
          width: 16px;
          height: 16px;
          margin-right: 4px;
        }
      }
      /* 隐藏滚动条样式 */
      .date-select::-webkit-scrollbar {
        display: none; /* 对于Webkit浏览器 */
      }
      .date-select {
        display: flex;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch; /* 在移动设备上优化滚动 */
        .date-item {
          flex-shrink: 0;
          font-size: 12px;
          background: #f4f9fe;
          color: #777;
          padding: 0px 12px;
          height: 28px;
          line-height: 28px;
          margin-right: 14px;
          border-radius: 4px;
          cursor: pointer;
        }

        .date-item:hover {
          background: #f4f9fe;
          color: #0052d9;
        }
        .date-item-current {
          background: #f4f9fe;
          color: #0052d9;
        }
        .moer-date-btn {
          // position: absolute;
          // right: 0;
        }
      }
    }
  }
}
</style>
