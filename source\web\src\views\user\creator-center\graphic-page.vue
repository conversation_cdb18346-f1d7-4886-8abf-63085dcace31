<template>
  <div class="graphic-page-container">
    <p class="page-title">我的文章</p>
    <div class="search-top">
      <div class="tab-box">
        <ul>
          <li :class="{ 'active-tab': tableParams.graphic_status === item.status }" v-for="item in tabList"
            :key="item.label" @click="handleSort(item, 1)">
            <span>{{ item.label }}</span>
          </li>
        </ul>
      </div>
      <el-input class="creator-center-kwsearch" v-model="tableParams.graphic_name" placeholder="标题关键词搜索"
        style="width:240px"></el-input>
    </div>
    <div class="main-table">
      <div class="sort-box">
        <span>排序：</span>
        <ul>
          <li :class="{ 'active-sort': tableParams.order_by === e.order_by }" v-for="e in sortList" :key="e.label"
            @click="handleSort(e, 2)">{{ e.label }}</li>
        </ul>
      </div>
      <div class="card-list" v-if="tableParams.list.length > 0">
        <div class="item-card-box">
          <GraphicItemCard v-for="(item, index) in tableParams.list" :item="item" :key="index"
            @handleEvent="handleEvent" />
        </div>
        <el-pagination :hide-on-single-page="tableParams.list.length === 0" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" :current-page="tableParams.page_no" :page-sizes="[5, 10, 20, 30, 50, 100]"
          :page-size="tableParams.page_size" layout="total, prev, pager, next, sizes, jumper" :total="tableParams.total">
        </el-pagination>
      </div>
      <div class="empty" v-else>
        <span class="empty-img"></span>
        <div class="empty-text">暂无信息(内容)</div>
      </div>
    </div>
    <AddCourseDialog :visible.sync="addCourseDialogShow" :itemData.sync="addCourseDialogData" />
    <QrCodeDialog v-if="copyShow.show" :visible.sync="copyShow.show" :url="copyShow.url" :copyTitle="copyTitle" />
  </div>
</template>
<script>
import { AddCourseDialog } from '@/components/index'
import GraphicItemCard from '@/views/components/graphic-item-card'
import { myGraphicPageList, graphicPageListDel, startCommon, stopCommon } from 'config/api.conf'
import QrCodeDialog from '@/views/components/qrCodeDialog'
import { throttle } from 'utils/tools'
export default {
  components: {
    AddCourseDialog,
    GraphicItemCard,
    QrCodeDialog
  },
  data() {
    return {
      tableParams: {
        graphic_name: '',
        page_no: 1,
        page_size: 10,
        order_by: 0,
        graphic_status: '',
        list: [],
        total: 0
      },
      tabList: [
        { label: '全部', status: '' },
        // { label: '已发布', status: '1' },
        // { label: '草稿', status: '0' },
        { label: '在用', status: '1' },
        { label: '下架', status: '3' },
        { label: '草稿', status: '4' },
        { label: '待审核', status: '6' }
      ],
      sortList: [
        { label: '最近发布', order_by: 0 },
        { label: '浏览量', order_by: 1 },
        { label: '点赞量', order_by: 2 },
        { label: '收藏量', order_by: 3 },
        { label: '内容字数', order_by: 4 }
      ],
      copyShow: {
        show: false,
        url: '',
        qrUrl: location.href
      },
      addCourseDialogShow: false,
      addCourseDialogData: {
        module_id: 8,
        module_name: '图文'
      },
      copyTitle: ''
    }
  },
  watch: {
    'tableParams.graphic_name': {
      handler(newV) {
        this.searchInput()
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    async getList() {
      const { graphic_name, page_no, page_size, order_by, graphic_status } = this.tableParams
      let params = {
        graphic_name,
        page_no,
        page_size,
        order_by,
        graphic_status
      }
      await myGraphicPageList(params).then(res => {
        this.tableParams.list = res.records || []
        this.tableParams.total = res.total
      })
    },
    searchInput: throttle(function (e) {
      this.tableParams.page_no = 1
      this.getList()
    }, 1000),
    handleSort(item, scene) {
      this.tableParams.page_no = 1
      if (scene === 1) this.tableParams.graphic_status = item.status
      else this.tableParams.order_by = item.order_by
      this.getList()
    },
    handleSizeChange(size) {
      this.tableParams.page_size = size
      this.getList()
    },
    handleCurrentChange(current) {
      this.tableParams.page_no = current
      this.getList()
    },
    handleEdit(item) {
      const { href } = this.$router.resolve({
        name: 'create',
        query: {
          graphic_id: item.graphic_id,
          from: 'graphic-page'
        }
      })
      window.open(`https://${location.host + href}`)
    },
    handleEvent(data) {
      if (data.evt === 'addCourse') this.handleShow(data.data, 2)
      else if (data.evt === 'edit') this.handleEdit(data.data)
      else if (data.evt === 'share') this.handleShow(data.data, 1)
      else if (data.evt === 'del') this.handleDelete(data.data)
      else if (data.evt === 'go') this.handleLink(data.data)
      else if (data.evt === 'on') this.handleOn(data.data)
      else if (data.evt === 'up') this.handleUp(data.data)
    },
    // 上架
    handleOn(item) {
      startCommon({ id: item.graphic_id, act_type: 18 }).then(res => {
        this.$message.success('启用成功')
        this.getList()
      })
    },
    // 下架
    handleUp(item) {
      let that = this
      this.$messageBox
        .prompt(
          '点击确认弹窗下架后文章将无法浏览，是否确认下架？',
          '下架',
          {
            closeOnClickModal: false,
            customClass: 'confirm-input-message-box',
            iconClass: 'el-icon-warning-outline',
            cancelButtonClass: 'el-button--default',
            confirmButtonClass: 'el-button--danger',
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            inputPlaceholder: '请输入“下架”确认此次操作',
            inputErrorMessage: '请输入“下架”',
            inputValidator: function (val) {
              return !!val && val.trim() === '下架'
            }
          }
        )
        .then(({ value }) => {
          if (value && value.trim() === '下架') {
            stopCommon({ id: item.graphic_id, act_type: 18 }).then((res) => {
              that.getList()
              this.$message.success('下架成功')
            })
          }
        })
    },
    handleShow(item, scene) {
      if (scene === 1) {
        this.copyTitle = item.graphic_name
        let pingShareUrl = process.env.NODE_ENV === 'production'
          ? `https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=${item.graphic_id}&from_act_id=${item.graphic_id}&share_staff_id=${this.$store.state.userInfo.staff_id}&share_staff_name=${this.$store.state.userInfo.staff_name}`
          : `http://s.test.yunassess.com/s/edLs1e?scheme_type=graphic&graphic_id=${item.graphic_id}&from_act_id=${item.graphic_id}&share_staff_id=${this.$store.state.userInfo.staff_id}&share_staff_name=${this.$store.state.userInfo.staff_name}`
        this.copyShow.url = item.graphic_status === 0 ? `${pingShareUrl}&scene=${scene}` : pingShareUrl
        this.copyShow.show = true
      } else {
        const { graphic_name, cover_image_id, graphic_desc, graphic_id } = item
        const addCourseParam = {
          content_name: graphic_name,
          cover_img_url: cover_image_id,
          description: graphic_desc,
          href: `https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=${graphic_id}`,
          item_id: graphic_id,
          origin: location.origin
        }
        this.addCourseDialogData = {
          ...this.addCourseDialogData,
          ...addCourseParam
        }
        this.addCourseDialogShow = true
      }
    },
    handleDelete(item) {
      let that = this
      let str = '<p style="color:#333;font-size:16px;">正在删除"' + item.graphic_name + '"</p>'
        + '<p style="text-align: left;">删除图文后相关数据将被清除且不可恢复，请谨慎操作<p/>'
      this.$messageBox.prompt(str, '', {
        dangerouslyUseHTMLString: true,
        closeOnClickModal: false,
        customClass: 'graphic_del',
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
        inputPlaceholder: '请输入"确认删除"来确认此次操作',
        inputValidator: function (val) {
          if (val && val.trim() === '确认删除') {
            return true
          } else {
            return '请输入确认删除'
          }
        }
      }).then(({ value }) => {
        if (value && value.trim() === '确认删除') {
          graphicPageListDel(item.graphic_id).then((res) => {
            that.getList()
            that.$message.success('删除成功')
          })
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '已取消删除'
        })
      })
    },
    handleLink(row) {
      let hrefObj = ''
      if (row.graphic_status === 1) {
        hrefObj = this.$router.resolve({ name: 'preview', query: { graphic_id: row.graphic_id } })
      } else {
        hrefObj = this.$router.resolve({ name: 'create', query: { graphic_id: row.graphic_id } })
      }
      window.open(`https://${location.host + hrefObj.href}`)
    }
  }
}
</script>
<style lang="less" scoped>
.graphic-page-container {
  background-color: #F6F7F9;
  font-size: 14px;

  .page-title {
    color: rgba(0, 0, 0, 0.8);
    font-size: 16px;
    font-weight: 700;
    height: 24px;
    line-height: 24px;
  }

  .search-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 20px;
    margin-bottom: 12px;

    .tab-box {
      ul {
        display: flex;

        li {
          width: 100px;
          height: 32px;
          border: 1px solid rgba(238, 238, 238, 1);
          background: rgba(255, 255, 255, 1);
          border-radius: 4px;
          line-height: 32px;
          text-align: center;
          color: rgba(0, 0, 0, 0.6);
          font-size: 14px;
          cursor: pointer;
        }

        li+li {
          margin-left: 8px;
        }

        .active-tab {
          background: rgba(0, 82, 217, 1);
          color: #fff
        }
      }
    }
  }

  .main-table {
    background-color: #fff;
    border-radius: 4px;
    padding: 16px 28px;

    .sort-box {
      display: flex;
      align-items: center;
      padding-bottom: 15px;
      border-bottom: 1px solid rgba(238, 238, 238, 1);

      span:first-of-type {
        color: rgba(0, 0, 0, 0.4);
      }

      ul {
        display: flex;
        margin-left: 8px;

        li {
          color: rgba(0, 0, 0, 0.6);
          font-size: 14px;
          height: 22px;
          line-height: 22px;
          cursor: pointer;
        }

        li+li {
          margin-left: 24px;
        }

        .active-sort {
          color: #0052D9
        }
      }
    }

    .card-list {
      .item-card-box {
        max-height: 940px;
        overflow-y: auto;
      }
    }
  }

  .empty {
    text-align: center;
    margin-top: 140px;

    .empty-text {
      margin-top: 17px;
      color: rgba(0, 0, 0, 1);
      font-size: 16px;
      font-weight: 500;
    }

    .empty-img {
      display: inline-block;
      width: 160px;
      height: 160px;
      background: url(~@/assets/img/empty.png) no-repeat center/cover;
    }
  }

  .opreate-dropdown {
    padding: 4px;

    li+li {
      margin-top: 4px;
    }

    .el-dropdown-menu__item {
      padding: 0 12px
    }

    .el-dropdown-menu__item:focus,
    .el-dropdown-menu__item:not(.is-disabled):hover {
      color: rgba(0, 82, 217, 1);
      font-size: 14px;
      background: rgba(245, 247, 249, 1);
    }
  }

  :deep(.el-pagination) {
    margin-top: 20px;
    text-align: end;
  }
}</style>
