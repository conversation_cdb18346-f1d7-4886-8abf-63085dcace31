<!-- 聚合报名创建 -->
<template>
  <div class="agg-create">
    <div class="agg-content">
      <!-- <div class="agg-header">聚合报名{{ polymer_id ? '编辑' : '创建' }}</div> -->
      <div class="item-label">
        基础设置
      </div>
      <div class="tiem-value">
        <el-form :rules="rules" ref="form" :model="form" label-width="110px">
          <el-form-item prop="polymer_name" label="页面名称" class="is-required">
            <el-input class="suffix-title" size="small" style="width:785px" placeholder="请输入页面名称" show-word-limit
              v-model="form.polymer_name" clearable @input="handleInput"></el-input>
            <span class="title-length">{{ titleLength }}/50</span>
          </el-form-item>
          <el-form-item required label="页面状态">
            <el-radio v-model="form.status" :label="1">在用</el-radio>
            <el-radio v-model="form.status" :label="2">停用</el-radio>
          </el-form-item>
          <el-form-item required label="启用时间">
            <el-radio v-model="timeType" label="0">不限制</el-radio>
            <el-radio v-model="timeType" label="1">设置起止时间</el-radio>
            <template v-show="timeType == '1'">
              <el-date-picker format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" v-model="form.start_time" type="datetime" placeholder="开始时间"></el-date-picker>
              <span class="zhi">至</span>
              <el-date-picker format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" v-model="form.end_time" type="datetime" placeholder="结束时间"></el-date-picker>
            </template>
          </el-form-item>
          <el-form-item required label="可见人员范围">
            <el-radio v-model="staffStatus" label="1" @change="handlePersonnelScopeChange">全体员工</el-radio>
            <el-radio v-model="staffStatus" label="2" @change="handlePersonnelScopeChange">部分员工</el-radio>
            <AudienceSelector v-if="staffStatus === '2'" class="audience_selector" ref="selector" v-model="form.target_list" :showTab="['unitStaff', 'unit', 'group', 'import']" appCode="qlearning" :env="audienceEnv" />
          </el-form-item>
          <el-form-item required label="页面布局">
            <el-radio v-model="form.style_type" :label="1">列表布局</el-radio>
            <el-radio v-model="form.style_type" :label="2">卡片布局</el-radio>
            <span @click="sketchMapShow = true" class="filled">
              <img src="@/assets/img/help-circle-filled.png">
              <span>点击查看布局效果示意图</span>
            </span>
          </el-form-item>
          <el-form-item class="width785" prop="admin_list" label="页面负责人">
            <sdc-staff-selector multiple ref="page_person_charge" v-model="form.admin_list" size="small" placeholder="请选择页面负责人" @change="changeCourseAuth" />
          </el-form-item>
          <el-form-item label="页面介绍" style="padding-top: 4px;">
            <sdc-mce-editor 
              class="work-edit" 
              ref="workEditor" 
              :content="form.brief"
              :env="audienceEnv"
              :catalogue.sync="editorConfig.catalogue"
              :urlParams="editorConfig.urlParams"
              :urlConfig="editorConfig.urlConfig"
              :options="editorConfig.options" />
            <!-- <OteamRichText :textValue="testValue" ref="oteamRichTextRef2" mode="edit" /> -->
          </el-form-item>
          <el-form-item label="页面背景图">
            <div class="pageBackground">
              <div>
                <cut-img-upload ref="upload" size="5" :fixedNumber="[3300, 600]" @handleSuccess="handlePcSuccessImage"
                  :dialogImageUrl="pcUploadObj.cover_image" :autoImgUrl="form.pc_bg_photo_url"
                  @handleClearImg="handleClearImg('pc')" :cover_imgage_storage_type="pcUploadObj.cover_imgage_storage_type">
                </cut-img-upload>
                <div class="PCmobileBackground">PC端背景图</div>
              </div>
              <div>
                <cut-img-upload ref="uploads" size="5" :fixedNumber="[3300, 600]" @handleSuccess="handleMobileSuccessImage"
                  :dialogImageUrl="mobileUploadObj.cover_image" :autoImgUrl="form.mobile_bg_photo_url"
                  @handleClearImg="handleClearImg('mobile')" :cover_imgage_storage_type="mobileUploadObj.cover_imgage_storage_type">
                </cut-img-upload>
                <div class="PCmobileBackground">移动端背景图</div>
              </div>
              <div class="suggestion">
                PC端：建议尺寸900*506px<br />
                移动端：建议尺寸900*506px<br />
                图片小于4M，支持jpg、png、jpeg、bmp格式<br />
              </div>
            </div>

          </el-form-item>
        </el-form>
      </div>

      <div class="item-label">
        <span>
          关联内容
          <span @click="signUpVisible = true" class="item-label_tips">查看报名情况</span>
          <span class="item-label_tips_red">注意：如班级/活动关联了课前问卷，用户通过聚合报名页提交报名时，将跳过课前问卷填写环节</span>
        </span>
        <div class="lable-right">
          <el-button type="primary" size="small" @click="openCatalogue"><img src="@/assets/img/bulletpoint.png">目录管理</el-button>
          <el-button type="primary" size="small" icon="el-icon-circle-plus-outline" @click="openFaceList">新增内容</el-button>
        </div>
      </div>
      <div class="select-rel">
        <el-table header-row-class-name="table-header-style" row-class-name="table-row-style" :data="selectTableData"
          style="width: 100%" class="agg-table">
          <el-table-column prop="category_name" label="所属目录">
          </el-table-column>
          <el-table-column prop="content_name" label="标题">
          </el-table-column>
          <el-table-column prop="module_name" label="类型" width="80"></el-table-column>
          <el-table-column label="状态" width="120">
            <template slot-scope="scope">
              <span>{{ aggStatus(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="开班时间" width="330px">
            <template slot-scope="scope">
              <span class="table-course-title">{{ classTime(scope.row.origin_data) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="授课形式">
            <template slot-scope="scope">
              <!-- <span class="table-course-title">{{ scope.row.teaching_type === 1 ? '线下授课' : '在线授课' }}</span> -->
              <span class="table-course-title">{{ scope.row.teaching_type || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人" width="180px">
            <template slot-scope="scope">
              <span class="table-course-title">{{ scope.row.creator_name || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <div class="operat-btn-box">
                <el-link type="primary" :underline="false" @click="handleAssociatedEdit(scope.row)">编辑</el-link>
                <el-link type="primary" :underline="false" @click="handleNotAssociated(scope.row)" :disabled="cancellingAssociation">取消关联</el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- <div class="item-label question-label">
        <span>问卷关联设置 </span>
        <div class="lable-right">
          <el-button type="primary" :disabled="!!questionTabData.length" size="small" icon="el-icon-circle-plus-outline"
            @click="handlerOpenQuestion">关联问卷</el-button>
        </div>
      </div>
      <div class="question-box">
        <div class="question-item">
          <div class="row-box">
            <div class="row-left">
              <img class="row-icon" src="~@/assets/mooc-img/questionnaire-icon.png" alt="" />
              <div class="row-title">{{ scope.row.name }}</div>
            </div>
            <div class="row-right">
              <div class="text-btn">查看统计数据</div>
            </div>
          </div>
          <div class="row-box">
            <div class="row-left">
              <div class="span-coll">
                <span class="span-box">zhangsan(张三)</span>
                <span class="span-box">创建于 2024-04-21</span>
                <span class="span-box"><img class="icon-box" src="~@/assets/mooc-img/usergroup.png" alt /></span>
              </div>
            </div>
          </div>
        </div>
        <div class="empty" v-if="false">未关联问卷</div>
      </div> -->
      <!-- <el-table empty-text="未关联问卷" header-row-class-name="table-header-style" row-class-name="table-row-style"
        :data="questionTabData" style="width: 100%" class="agg-table">
        <el-table-column label="问卷名称">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template slot-scope="scope">
            <div class="operat-btn-box">
              <el-link type="primary" :underline="false" @click="openQuestionUrl(scope.row)">详情</el-link>
              <el-link type="primary" :underline="false" @click="editQuestion(scope.row)">编辑</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table> -->

      <!-- 底部按钮区域 -->
      <div class="buttom">
        <div class="inner">
          <el-button type="primary" size="small" @click="handleSaveDraft" :disabled="!!polymer_id || originData.status === 3">存草稿</el-button>
          <!-- <el-button type="primary" size="small" @click="handlerPreview">预 览</el-button> -->
          <el-button type="primary" size="small" @click="handelrPublish">{{ polymer_id ? '保 存' : '发 布' }}</el-button>
          <el-button size="small" @click="onCancel">取 消</el-button>
        </div>
      </div>

      <!-- 查看报名情况弹窗 -->
      <el-dialog :modal-append-to-body="false" title="查看报名情况" :visible.sync="signUpVisible" width="960px">
        <div class="face-activity">
          <el-table header-row-class-name="table-header-style" row-class-name="table-row-style" :data="signUpForm"
            style="width: 100%" height="438px" class="agg-table">
            <el-table-column prop="class_name" label="姓名">
            </el-table-column>
            <el-table-column label="报名时间">
              <template slot-scope="scope">
                <span class="table-course-title">{{ scope.row.start_time }}</span>
              </template>
            </el-table-column>
            <el-table-column label="组织架构">
              <template slot-scope="scope">
                <span class="table-course-title">{{ scope.row.creator_name || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="报名内容">
              <template slot-scope="scope">
                <span class="table-course-title">{{ scope.row.creator_name || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="报名状态">
              <template slot-scope="scope">
                <span>{{ Number(scope.row.act_type) === 4 ? '活动' : '班级' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="参加形式">
              <template slot-scope="scope">
                <span>{{ Number(scope.row.act_type) === 4 ? '活动' : '班级' }}</span>
              </template>
            </el-table-column>
          </el-table>
          <!-- <el-pagination v-show="signUpForm.length" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="current"
            :page-sizes="[5, 10, 20, 30, 50, 100]" :page-size="size" layout="total,  prev, pager, next, sizes, jumper"
            :total="total">
          </el-pagination> -->
        </div>
      </el-dialog>

      <!-- 布局效果示意图 -->
      <el-dialog custom-class="custom-dialog" :modal-append-to-body="false" title="布局效果示意" :visible.sync="sketchMapShow"
        width="540px">
        <div class="sketchMap">
          <div>
            <div class="sketchMapTitle">列表布局</div>
            <img src="@/assets/img/cardLayout.png">
          </div>
          <div>
            <div class="sketchMapTitle">卡片布局</div>
            <img src="@/assets/img/gridCards.png">
          </div>
        </div>
      </el-dialog>
      <!-- 问卷确认 -->
      <AddQuestionnairConfirmDialog :visible.sync="addQuestionnairConfirmDialogShow" :questionType="questionType"
        @continueCreateQuestionnaire="continueCreateQuestionnaire" v-if="addQuestionnairConfirmDialogShow" />

      <!-- 目录管理 -->
      <directoryManagement :dialogFace.sync="dialogDirectory" @updateCatalogList="updateCatalogList" ></directoryManagement>

      <!-- 添加课程 -->
      <addActivityCourseDialog :visible.sync="dialogContent" ref="addLineCourse" :moduleTypes="[3, 4, 23]" useQuery="getSearchsite" :catalogList.sync="catalogList" :extendOptions="extendOptions"
        @handleShowSetDialog="handleShowSetDialog" @handleEditCatalog="handleEditCatalog" />
    </div>
  </div>
</template>

<script>
import {
  addPolymer,
  saveDraftPolymer,
  getPolymerInfo,
  getPolymerCatalogList
} from '@/config/mooc.api.conf.js'
import { AudienceSelector } from '@tencent/sdc-audience'
import { CutImgUpload } from '@/components/index'
import addActivityCourseDialog from '@/views/manage/classroom/activity/components/addActivityCourseDialog.vue'
import directoryManagement from './components/directoryManagement.vue'
import AddQuestionnairConfirmDialog from '@/views/manage/mooc/project-manage/task-list/component/add-questionnairConfirmDialog.vue'
// import OteamRichText from '@/components/o-team-rich-text.vue'
import { handlerDateFormat } from '@/utils/tools.js'

const allTarget = ''

export default {
  name: 'createAggregate',
  components: {
    AudienceSelector,
    CutImgUpload,
    addActivityCourseDialog,
    directoryManagement,
    AddQuestionnairConfirmDialog
    // OteamRichText
  },
  data() {
    return {
      editorConfig: {
        catalogue: false,
        options: {
          // 非必传字段
          selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
            formatselect fontsizeselect lineheight |
            bold italic underline strikethrough |
            forecolor backcolor |
            dent align bullist numlist |
            insert codesample code |
            fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        urlParams: {
          is_public: true
        }
      },
      sketchMapShow: false, // 布局效果示意图
      total: '',
      testValue: JSON.stringify({
        version: 1,
        type: 'doc',
        content: [

        ]
      }),
      signUpVisible: false, // 报名情况弹窗隐显
      extendOptions: {
        showAddOutLink: true,
        banSameCourse: true,
        sameCourseList: [],
        showAddOutLink_createAggreagte: true,
        title: '新增内容',
        courseTitle: '课程名称',
        catalog: '',
        type: 'add'
      },
      frontRelationContents: [],
      dialogContent: false,
      dialogDirectory: false,
      addQuestionnairConfirmDialogShow: false,
      questionType: 'add',
      dialogFace: false,
      pcUploadObj: { // 一键封面上传
        cover_image: '',
        cover_image_id: '',
        cover_imgage_storage_type: ''
      },
      mobileUploadObj: { // 一键封面上传
        cover_image: '',
        cover_image_id: '',
        cover_imgage_storage_type: ''
      },
      questionTabData: [],
      selectTableData: [],
      uploadLoding: false,

      originData: {},
      staffStatus: '1', // 可见人员范围 1:全部人员 2:指定人员
      admin_list: [], // 页面负责人员
      form: {
        polymer_id: null, // 聚合id
        polymer_name: '', // 聚合名称
        status: 1,
        start_time: '',
        end_time: '',
        target_list: allTarget, // 目标人员
        style_type: 1, // 页面布局
        admin_list: [], // 页面负责人员
        brief: '',
        pc_bg_photo_url: '',
        pc_bg_color: '', // 背景色
        mobile_bg_photo_url: '',
        mobile_bg_color: '', // 移动端背景色
        content_list: [] // 关联内容
      },
      timeType: '1', // 是否设置起止时间
      contentId: '',
      currentEditQuestion: {},
      titleLength: 0,
      audienceEnv: process.env.NODE_ENV,
      rules: {
        polymer_name: [{ required: true, message: '请输入页面名称', trigger: 'blur' }],
        admin_list: [{ required: true, message: '请选择页面负责人', trigger: 'blur' }]
      },
      signUpForm: [],
      catalogList: [], // 目录列表
      cancellingAssociation: false // 取消关联中
    }
  },
  watch: {},
  computed: {
    polymer_id() {
      return this.$route?.query?.polymer_id || ''
    },
    aggStatus() {
      return (val) => {
        // regist_count 已报名人数 max_student_count 可报名人数
        const { regist_count, max_student_count, start_time } = val
        if (new Date(start_time) < new Date()) {
          return '已截止报名'
        } else if (regist_count >= max_student_count) {
          return '等待列表'
        } else {
          return '可报名'
        }
      }
    },
    // 开班时间
    classTime() {
      return (val) => {
        if (val?.start_time && val?.end_time) {
          return val.start_time + ' ~ ' + val.end_time
        }
        return '_'
      }
    },
    userPublicInfo() {
      return JSON.parse(sessionStorage.getItem('login_user_dep')) || {}
    }
  },
  async created() {
    try {
      await this.initPageData()
      
      // 保留 后续问卷功能
      // window.questionAggregate = (questionData) => {
      //   this.questionTabData = [questionData]
      // }
    } catch (error) {
      console.error('页面初始化失败:', error)
    }
  },
  methods: {
    async initPageData() {
      if (this.polymer_id) {
        await this.initEditMode()
      } else {
        await this.initCreateMode()
      }
    },
    async initEditMode() {
      this.questionType = 'edit'
      document.title = '聚合报名编辑'

      // 并行加载所有必要数据
      await Promise.all([
        this.getPolymerInfo(),
        this.getPolymerCatalogListFn()
      ])
    },
    async initCreateMode() {
      document.title = '新建聚合报名'
      
      const defaultCatalog = {
        id: 1,
        category_id: '',
        category_name: '默认目录',
        target_list: allTarget,
        show_name: true,
        order_no: 1,
        created_at: handlerDateFormat(new Date(), '-'),
        creator_name: this.userPublicInfo.emp_name_en
      }
      
      this.catalogList = [defaultCatalog]
    },
    // 目录列表
    getPolymerCatalogListFn() {
      const params = {
        polymer_id: this.polymer_id,
        current: 1,
        size: 1000
      }
      getPolymerCatalogList(params).then((res) => {
        this.catalogList = res.records
      })
    },
    handleShowSetDialog(data) {
      const newArr = []
      data.forEach(item => {
        // 判断是否添加同目录重复课程
        const isRepeat = this.selectTableData.some(item2 => item.act_id === item2.act_id && item.act_type === item2.act_type && item.category_name === item2.category_name)
        if (!isRepeat) {
          newArr.push(item)
        }
      })

      const { emp_name_en } = JSON.parse(sessionStorage.getItem('login_user_dep')) || {}
      const creator_name = emp_name_en ?? ''
      const newData = newArr.map(item => ({
        ...item,
        creator_name,
        start_time: item?.origin_data?.start_time
      }))

      this.selectTableData.push(...newData)
      
      // 当前功能不同目录可以重复添加同一课程，取消反显
      // this.extendOptions.sameCourseList = this.selectTableData.map(({ act_id, act_type }) => ({
      //   item_id: String(act_id),
      //   act_type: String(act_type)
      // }))
    },
    handleEditCatalog(categoryName) {
      // 参数校验
      if (!categoryName || typeof categoryName !== 'string') {
        this.$message.error('目录名称不能为空')
        return
      }

      try {
        const { act_id, act_type, catalog } = this.extendOptions
        const matchCategory = (item) => {
          return item.act_id === act_id && item.act_type === act_type && item.category_name === catalog
        }
        const index = this.selectTableData.findIndex(matchCategory)
        if (index === -1) {
          this.$message.error('未找到要修改的目录')
          return
        }

        const curData = {
          ...this.selectTableData[index],
          category_name: categoryName
        }

        this.selectTableData.splice(index, 1, curData)
        this.$message.success('目录修改成功')
      } catch (error) {
        console.error('修改目录失败:', error)
      }
    },
    // 可见人员范围改变
    handlePersonnelScopeChange(val) {
      this.form.target_list = ''
    },
    changeCourseAuth(val) {
      this.form.admin_list = [] // 每次操作都清空
      val.forEach(v => {
        this.form.admin_list.push({
          staff_id: v.StaffID,
          staff_name: v.StaffName
        })
      })
      this.admin_list = val
      // 手动触发校验
      this.$refs.form.validateField('admin_list')
    },
    // 关联内容 - 编辑
    handleAssociatedEdit(val) {
      this.dialogContent = true
      this.extendOptions = {
        showAddOutLink: true,
        banSameCourse: true,
        sameCourseList: [],
        showAddOutLink_createAggreagte: true,
        title: '编辑内容',
        type: 'edit',
        courseTitle: val.content_name,
        act_id: val.act_id,
        act_type: val.act_type,
        catalog: val.category_name
      }
    },
    // 关联内容 - 取消关联
    handleNotAssociated(row) {
      const index = this.selectTableData.findIndex(item => item.act_id === row.act_id && item.act_type === row.act_type && item.category_name === row.category_name)
      if (index === -1) {
        this.$message.error('未找到要删除的目录')
        return
      }
      this.cancellingAssociation = true
      this.$confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.selectTableData.splice(index, 1)
        this.$nextTick(() => {
          this.cancellingAssociation = false
        })
      }).catch(() => {
        this.cancellingAssociation = false
      })
    },
    // 预览
    handlerPreview() {
      this.form.content_list = this.selectTableData

      this.form.question = this.questionTabData[0]
      // this.form.status = 3
      let params = JSON.parse(JSON.stringify(this.form))
      sessionStorage.setItem('aggregate_preview', JSON.stringify(params))
      let linkUrl = process.env.NODE_ENV === 'production' ? 'https://portal.learn.woa.com/training' : 'https://test-portal-learn.woa.com/training'
      window.open(linkUrl + `/aggregate/detail?preview=1`)
    },
    // 编辑，获取聚合页详情
    async getPolymerInfo() {
      const data = await getPolymerInfo({ polymer_id: this.polymer_id })
      // 原始数据
      this.originData = { ...data }
      this.form = data

      this.handleInput(data.polymer_name)

      // 问卷
      // this.questionTabData = [data.question]

      const { target_list, start_time, end_time, admin_list, category_list } = data

      this.staffStatus = target_list ? '1' : '2'
      if (start_time && end_time) {
        this.timeType = '1'
      }
      if (admin_list && admin_list.length) {
        admin_list.forEach(item => {
          this.admin_list.push({
            StaffID: item.staff_id,
            StaffName: item.staff_name
          })
        })
        this.$refs.page_person_charge && this.$refs.page_person_charge.setSelected(this.admin_list)
        this.$nextTick(() => {
          this.$refs.form.validateField('admin_list')
        })
      }

      // 关联内容
      let contentList = []
      if (category_list && category_list.length) {
        category_list.forEach(item => {
          if (!item || !Array.isArray(item.content_list)) {
            return null
          }

          const courses = item.content_list.map(course => ({
            ...course,
            category_name: item.category_name || '',
            category_id: item.category_id || ''
          }))

          contentList = [...contentList, ...courses]
        })

        this.selectTableData = contentList
      }
    },
    // 发布
    handelrPublish() {
      this.form.brief = this.$refs['workEditor'].getContent()
      if (this.timeType === '0') {
        this.form.start_time = ''
        this.form.end_time = ''
      }
      
      this.$refs.form.validate(valid => {
        if (valid) {
          if (!this.checkForm()) return
          this.form.category_list = this.handleRelatedContentData(this.selectTableData)
          // 问卷 暂时不做 
          // this.form.question = this.questionTabData[0]

          this.handleSavePublish()
        }
      })
    },
    // 保存并发布
    handleSavePublish() {
      const { polymer_name, status, start_time, end_time, target_list, style_type, admin_list, brief, pc_bg_photo_url, pc_bg_color, mobile_bg_photo_url, mobile_bg_color, category_list } = this.form
      const params = {
        polymer_id: this.polymer_id || '',
        polymer_name: polymer_name.trim(),
        status,
        start_time,
        end_time,
        target_list,
        style_type,
        admin_list,
        brief,
        pc_bg_photo_url,
        pc_bg_color,
        mobile_bg_photo_url,
        mobile_bg_color,
        category_list
      }
      addPolymer(params).then((res) => {
        this.$message.success(this.polymer_id ? '保存成功' : '发布成功')
        this.$router.push({
          name: 'polymerList'
        })
      })
    },
    // 保存草稿
    handleSaveDraft() {
      this.$refs.form.validate(valid => {
        if (valid) {
          saveDraftPolymer(this.form).then(res => {
            this.$message.success('保存草稿成功')
            this.$router.push({
              name: 'polymerList'
            })
          })
        }
      })
    },
    // 校验
    checkForm() {
      const { status, start_time, end_time } = this.form
      if (status === 3) {
        this.$message.error('请选择页面状态')
        return false
      } else if (this.timeType === '1' && (!start_time || !end_time)) {
        this.$message.error('请选择启用时间')
        return false
      } else if (new Date(start_time) < new Date()) {
        this.$message.error('启用时间：开始时间不能早于当前时间')
        return false
      } else if (new Date(start_time) >= new Date(end_time)) {
        this.$message.error('启用时间：结束时间不能早于开始时间')
        return false
      } else if (!this.selectTableData.length) {
        this.$message.error('请至少关联一个班级&活动&直播')
        return false
      }
      // else if (!this.questionTabData.length) {
      //   this.$message.error('请关联问卷')
      //   return false
      // }
      return true
    },
    // 关联内容数据处理
    handleRelatedContentData(data) {
      if (Array.isArray(data) && data.length) {
        const categoryList = this.catalogList.map(item => ({
          polymer_id: item.polymer_id || this.polymer_id || '',
          category_id: item.category_id || '',
          category_name: item.category_name,
          target_list: item.target_list,
          show_name: item.show_name,
          order_no: item.order_no,
          content_list: []
        }))

        this.selectTableData.map(item => {
          const index = categoryList.findIndex(c => c.category_name === item.category_name)
          if (index !== -1) {
            let curData = categoryList[index]
            categoryList[index].content_list.push({
              id: item.id || '',
              polymer_id: curData.polymer_id || this.polymer_id || '',
              category_id: curData.category_id || '',
              act_id: item.act_id,
              act_type: item.act_type,
              enabled: 1
            })
          }
        })

        return categoryList
      } else {
        this.$message.error('请至少关联一个班级&活动&直播')
        return []
      }
    },
    handleInput(e) {
      const split = e.split('')
      // 计算已输入的长度
      const map = split.map(s => (s.charCodeAt(0) <= 128 ? 0.5 : 1))

      // 这里设置想要限制的长度
      const maxLength = 50
      let totalLength = 0
      let n = 0

      for (let i = 0; i < map.length; i++) {
        totalLength += map[i]
        if (totalLength >= maxLength) {
          n = i
          break
        }
      }

      this.titleLength = totalLength
      if (totalLength > maxLength) {
        this.form.polymer_name = split.slice(0, n).join('')
        this.titleLength = maxLength
      }
    },
    // 问卷弹窗确认
    continueCreateQuestionnaire() {
      this.addQuestionnairConfirmDialogShow = false
      this.handlerOpenQuestion()
    },
    // 图片编辑  
    // handleImgEdit() {
    //   this.$refs.sdcImgCoverRefExtand.outEdit({
    //     id: this.uploadObj.cover_image_id,
    //     stamp: this.$store.state.userInfo.staff_name, // 当前用户名
    //     type: 'outEdit'
    //   })
    // },
    // 内容中心图片回传 - Pc
    handlePcSuccessImage(url, file) {
      this.form.pc_bg_photo_url = url
      this.pcUploadObj.cover_image = url
      // 清空一键生成封面
      this.pcUploadObj.cover_image_id = ''
      this.pcUploadObj.cover_imgage_storage_type = 'contentcenter'
      this.getImgBgColor(URL.createObjectURL(file), 'pc')
    },
    // 内容中心图片回传 - Mobile
    handleMobileSuccessImage(url, file) {
      this.form.mobile_bg_photo_url = url
      this.mobileUploadObj.cover_image = url
      // 清空一键生成封面
      this.mobileUploadObj.cover_image_id = ''
      this.mobileUploadObj.cover_imgage_storage_type = 'contentcenter'
      this.getImgBgColor(URL.createObjectURL(file), 'mobile')
    },
    getImgBgColor(url, type) {
      let that = this
      let image = new Image()
      // 处理跨源污染
      // image.setAttribute('crossOrigin', 'anonymous') // anonymous/use-credentials
      image.onload = function () {
        const canvas = document.createElement('canvas')
        canvas.width = image.naturalWidth
        canvas.height = image.naturalHeight
        const context = canvas.getContext('2d')
        context.drawImage(image, 0, 0, image.naturalWidth, image.naturalHeight)
        /**
         * 获取左右各两个边缘像素点的色值
         * 左边两点
         * p1 [x = 10, y = 10]
         * p2 [x = 10, y = height - 10]
         * 右边两点
         * p3 [x = width - 10, y = 10]
         * p4 [x = width - 10, y = height - 10]
         */
        const data = context.getImageData(0, 0, image.width, image.height).data
        const p1 = data.slice(
          (image.width * 10 + 10) * 4,
          (image.width * 10 + 10) * 4 + 4
        )
        const p2 = data.slice(
          (image.width * (image.height - 10) + 10) * 4,
          (image.width * (image.height - 10) + 10) * 4 + 4
        )
        const p3 = data.slice(
          (image.width * 10 + (image.width - 10)) * 4,
          (image.width * 10 + (image.width - 10)) * 4 + 4
        )
        const p4 = data.slice(
          (image.width * (image.height - 10) + (image.width - 10)) * 4,
          (image.width * (image.height - 10) + (image.width - 10)) * 4 + 4
        )
        // 获取像素平均值
        const average = []
        for (let i in p1) {
          average[i] = Math.round((p1[i] + p2[i] + p3[i] + p4[i]) / 4)
        }
        const color = `rgba(${average.join(',')})`
        if (type === 'pc') {
          that.form.pc_bg_color = color
        } else if (type === 'mobile') {
          that.form.mobile_bg_color = color
        }
        URL.revokeObjectURL(url)
      }
      image.onerror = function (e) {
        URL.revokeObjectURL(url)
      }
      image.src = url
    },
    // 清空图片
    handleClearImg(type) {
      if (type === 'pc') {
        this.form.pc_bg_photo_url = ''
        this.form.pc_bg_color = ''
        this.pcUploadObj.cover_image = ''
        this.pcUploadObj.cover_image_id = ''
      } else if (type === 'mobile') {
        this.form.mobile_bg_photo_url = ''
        this.form.mobile_bg_color = ''
        this.mobileUploadObj.cover_image = ''
        this.mobileUploadObj.cover_image_id = ''
      }
    },
    openCatalogue() { // 打开目录管理
      this.dialogDirectory = true
    },
    updateCatalogList(val) {
      this.catalogList = val
      if (this.selectTableData.length) {
        const list = []
        this.catalogList.forEach((item) => {
          this.selectTableData.map((item2) => {
            // 编辑 - 修改目录
            if (this.polymer_id && item.category_id === item2.category_id) {
              item2.category_name = item.category_name
              list.push(item2)
            }
            // 新增 - 修改目录
            if (!this.polymer_id && item.old_category_name === item2.category_name) {
              item2.category_name = item.category_name
              list.push(item2)
            }
          })
        })
        if (list.length !== this.selectTableData.length) {
          this.selectTableData = [ ...list ]
        }
      }
    },
    openFaceList() { // 打开新增内容弹窗
      this.dialogContent = true
      this.extendOptions = {
        showAddOutLink: true,
        banSameCourse: true,
        sameCourseList: [],
        showAddOutLink_createAggreagte: true,
        title: '新增内容',
        courseTitle: '课程名称',
        catalog: '',
        type: 'add'
      }
    },
    onCancel() {
      this.$router.push({
        name: 'polymerList'
      })
    },
    handlerOpenQuestion() {
      const routeData = this.$router.resolve({
        name: 'questionAggregate', // 目标路由的名称
        query: {
          polymer_id: this.polymer_id || '',
          type: this.questionType,
          sid: this.currentEditQuestion.tx_quest_id || ''
        }
      })
      window.open(routeData.href, '_blank')
    },
    openQuestionUrl(val) {
      const { act_id } = val
      window.open(`https://wj-learn.woa.com/stat/1/overview?sid=${act_id}`)
    },
    editQuestion(val) {
      this.currentEditQuestion = val
      this.questionType = 'edit'
      this.addQuestionnairConfirmDialogShow = true
    }
  }
}
</script>
<style lang="less">
.work-edit {
  .tox.tox-tinymce {
    border: 1px solid #ccc !important;
    height: 450px;

    .tox-sidebar-wrap .tox-edit-area {
      min-height: 350px !important;
    }
    .tox-toolbar {
      width: 100%;
    }
  }
}
</style>
<style lang="less" scoped>
.pageBackground {
  justify-content: flex-start;
  display: flex;
  padding-bottom: 22px;

  .suggestion {
    font-weight: 400;
    font-size: 12px;
    color: #00000099;
    margin-left: 8px;
  }

  /deep/ .coverWrap {
    display: none;
  }

  >div {
    margin-right: 24px;
    display: flex;
    justify-content: space;
    flex-direction: column;
    align-items: center;

    .PCmobileBackground {
      margin-top: 16px;
      width: 104px;
      height: 30px;
      border-radius: 24px;
      font-weight: 400;
      font-size: 12px;
      line-height: 30px;
      text-align: center;
      vertical-align: middle;
      color: #000000E5;
      background-color: #F3F3F3;
    }
  }
}

.sketchMap {
  justify-content: space-around;
  display: flex;
  padding-bottom: 22px;

  img {
    width: 218px;
    height: 148px;
  }

  >div {
    display: flex;
    justify-content: space;
    flex-direction: column;
    align-items: center;
  }

  .sketchMapTitle {
    border-radius: 12px;
    width: 80px;
    height: 24px;
    line-height: 24px;
    margin-bottom: 24px;
    background-color: #EBEFFC;
    color: #0052D9;
    font-weight: 500;
    font-size: 14px;
    letter-spacing: 0px;
    vertical-align: middle;
    text-align: center;
  }
}

.zhi {
  margin: 0 16px;
  font-size: 14px;

}

.filled {
  color: #0052D9;

  span {
    vertical-align: middle;
    margin-left: 4px;
    cursor: pointer;
  }
}

.width785 {
  :deep(.el-form-item__content) {
    max-width: 785px;
  }

  /deep/.sdc-selector {
    display: flex !important;
  }
}

.agg-create {
  background-color: #F6F7F9;

  .agg-content {
    margin: auto;
    background-color: #fff;
    padding: 20px;
    padding-bottom: 34px;
  }

  .agg-header {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 20px;
  }

  .item-label {
    position: relative;
    color: #000000e6;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: normal;
    display: flex;
    justify-content: space-between;

    .item-label_tips {
      font-size: 14px;
      margin: 0 16px;
      color: #0052D9;
      cursor: pointer;
      font-weight: 400;
    }

    .item-label_tips_red {
      color: #E34D59;
      font-size: 14px;
      font-weight: 400;
    }
  }

  .item-label::after {
    content: '';
    width: 4px;
    height: 16px;
    position: absolute;
    left: -20px;
    top: 3px;
    background-color: #0052d9;
  }

  .tiem-value {
    padding: 12px;

    /deep/ .o-team-rich-text-container {
      min-width: 1000px;
      height: 358px;
    }

    /deep/ .el-date-editor.el-input {
      width: 240px;
    }

    /deep/ .el-input__icon {
      line-height: 32px;
    }

    /deep/.sdc-selector {
      display: inline-block;
    }

    /deep/.el-input__inner {
      height: 32px;
    }

    /deep/ .sdc-selector .el-input__inner {
      height: 28px;
    }

    .tips-img {
      color: #999999;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: normal;
      margin-left: 8px;
    }

    .agg-upload-img {
      margin-top: 10px;
    }
  }

  .question-box {
    margin-top: 24px;
    padding: 16px;
    gap: 12px;
    align-self: stretch;
    border-radius: 8px;
    background: #F9F9F9;
    display: flex;
    flex-direction: column;

    .question-item {
      width: 100%;

      .row-box {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 12px;

        .row-left {
          flex: 1;
          display: flex;
          align-items: center;

          .row-icon {
            flex-shrink: 0;
            width: 24px;
            height: 24px;
            margin-right: 8px;
          }

          .row-title {
            width: 558px;
            font-size: 14px;
            font-weight: 600;
            color: #000000e6;
            margin-right: 8px;
            // 单行省略
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .span-coll {
            padding-left: 32px;

            .span-box {
              display: inline-block;
              padding-right: 15px;
              margin-right: 15px;
              color: #00000099;
              font-family: "PingFang SC";
              font-size: 14px;
              font-style: normal;
              font-weight: 400;
              line-height: normal;
              border-right: 1px solid #CCCCCC;

              &:last-child {
                border-right: none;
              }
            }

            .icon-box {
              display: inline-block;
              width: 16px;
              height: 16px;
              vertical-align: middle;
            }
          }
        }

        .row-right {
          padding-left: 80px;
          flex-shrink: 0;
          display: flex;
          align-items: center;

          .text-btn {
            cursor: pointer;
            color: #0052d9;
            text-align: right;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
          }
        }
      }
    }

    .empty {
      width: 100%;
      height: 44px;
      line-height: 44px;
      text-align: center;
      color: #999999;
      font-size: 14px;
      font-weight: 400;
    }
  }

  .suffix-title {
    /deep/.el-input__inner {
      padding-right: 80px;
    }
  }

  .title-length {
    width: 50px;
    position: absolute;
    left: 704px;
    color: #909399;
    font-size: 12px;
    text-align: right;
  }

  .select-rel {
    margin-top: 24px;
  }

  .agg-table {
    border-radius: 4px;
    margin-top: 15px;
    opacity: 1;
    border-top: 1px solid #eeeeeeff;
    border-left: 1px solid #eeeeeeff;
    border-right: 1px solid #eeeeeeff;

    .operat-btn-box {
      .el-link+.el-link {
        margin-left: 10px;
      }
    }
  }

  .question-label {
    margin-top: 32px;
  }

  .buttom {
    background-color: #fff;
    text-align: right;
    margin-top: 40px;

    .inner {
      // @media screen and (max-width: 1660px) {
      //   width: 1158px;
      // }

      // @media screen and (min-width: 1661px) {
      //   width: 1440px;
      // }
      text-align: right;
      margin: 0 auto;
    }

    .el-button {
      margin: 0 20px 0 0;
      min-width: 104px;
    }
  }

  .lable-right {
    flex-shrink: 0;

    button {
      width: 112px;
      height: 32px;
      font-size: 14px;
      padding: 0;
    }

    img {
      width: 16px;
      margin-right: 5px;
      height: 16px;
      vertical-align: middle;
    }
  }

  .face-activity {
    padding: 0 20px;

    .agg-table {
      border-radius: 4px;
      margin-top: 15px;
      opacity: 1;
      border-top: 1px solid #eeeeeeff;
      border-left: 1px solid #eeeeeeff;
      border-right: 1px solid #eeeeeeff;
    }
  }

  :deep(.el-radio) {

    .el-radio__input.is-checked+.el-radio__label,
    .el-radio__label {
      color: #000000e6;
    }
  }

  :deep(.el-table) {
    border-radius: 8px !important;

    .table-header-style th {
      background: #F5F5F5;
      color: #00000099;
    }

    .cell {
      padding: 0 16px;
    }
  }

  :deep(.custom-dialog) {
    border-radius: 9px;
  }
}
</style>
