<template>
  <div class="des-container">
    <div class="des-main">
      <!-- 简介 -->
      <div
        id="file-desc"
        :class="{ 'overflow-l2': expandDesc }"
        v-html="wordInfo.file_desc"
        @click="showDesContent"
      ></div>
      <!-- <div :class="['tag-list-box', 'clearfix', {'tag-height': expandTag}]" v-if="tagList.length">
        <span
          v-for="item in tagList"
          :key="item.label_id"
          :class="[
            { 'hot-tag': item.label_hot_enable === 1 },
            { 'official-tag': item.label_type === 1 },
            'item-tag'
          ]"
        >
          <span
            :class="[
              { 'hot-tag-i': item.label_hot_enable === 1 },
              { 'official-tag-i': item.label_type === 1 }
            ]"
          ></span>
          <span class="tag-label">{{ item.name }}</span>
        </span>
      </div>
      <div
        class="expand-and-open"
        v-show="isOverflow"
        @click="onExpandTag"
      >
        {{ expandTag ? '展开' : '收起' }}
        <span :class="[{ 'packUp-icon': !expandTag }, 'expand-icon']"></span>
      </div> -->
    </div>
    <van-list
      v-model="loading"
      :finished="isFinished"
      finished-text="没有更多内容了～"
      @load="getRecList"
      class="card-list"
      >
      <CategoryCard
        class="content-list"
        v-for="item in recommendList"
        :detailsInfo="item"
        :key="item.content_id"
      />
    </van-list>
  </div>
</template>
<script>
import { getPersonalRecList } from 'config/api.conf'
import CategoryCard from '../components/CategoryCard.vue'
import axios from 'axios'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]

export default {
  components: {
    CategoryCard
  },
  props: {
    wordInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      recommendList: [],
      tagList: [],
      expandTag: false,
      expandDesc: true,
      word_id: '',
      page: 1,
      loading: false,
      isFinished: false,
      isOverflow: false
    }
  },
  watch: {
    wordInfo: {
      handler(val) {
        if (val.file_label?.length) {
          this.tagList = val.file_label
          this.$nextTick(() => {
            const tag = document.querySelector('.item-tag')
            if (!tag) return
            const el = document.querySelector('.tag-list-box')
            if (el.clientHeight > tag.clientHeight * 2) {
              this.isOverflow = true
              this.expandTag = true
            }
          })
        }
        this.wordInfo.file_desc = this.wordInfo.file_desc || '暂无简介'
      }
    },
    deep: true
  },
  created() {
    this.word_id = this.$route.query.word_id || ''
  },
  methods: {
    // 简介展开/收起
    showDesContent() {
      this.expandDesc = !this.expandDesc
    },
    onExpandTag() {
      this.expandTag = !this.expandTag
    },
    getRecList() {
      if (process.env.NODE_ENV !== 'production') {
        try {
          axios.get(`${envName.v8MobileHost}api/user/recommend/user-recommends/get-personal-rec-list`, {
            withCredentials: true,
            params: {
              limit: 10,
              page: this.page,
              isLoadMore: true
            }
          }).then(response => {
            let res = response?.data?.data || {}
            this.page++
            const result = (res.recList || []).map((e) => {
              return {
                ...e,
                content_name: e.title,
                content_id: e.item_id,
                description: e.brief,
                play_total_count: e.view_count,
                created_time: e.created_at.split(' ')[0],
                duration: e.origin_data.est_dur,
                photo_url: e.thumbnail_url
              }
            })
            this.recommendList = this.recommendList.concat(result)
            if (this.recommendList.length >= res.recNum) {
              this.isFinished = true
            }
            this.loading = false
          }).catch(() => {
            this.loading = false
            this.isFinished = true
          })
        } catch (error) {
          this.loading = false
          this.isFinished = true
        }
      } else {
        getPersonalRecList({
          limit: 10,
          page: this.page,
          isLoadMore: true
        })
          .then((res) => {
            this.page++
            const result = (res.recList || []).map((e) => {
              return {
                ...e,
                content_name: e.title,
                content_id: e.item_id,
                description: e.brief,
                play_total_count: e.view_count,
                created_time: e.created_at.split(' ')[0],
                duration: e.origin_data.est_dur,
                photo_url: e.thumbnail_url
              }
            })
            this.recommendList = this.recommendList.concat(result)
            if (this.recommendList.length >= res.recNum) {
              this.isFinished = true
            }
            this.loading = false
          })
          .catch(() => {
            this.loading = false
            this.isFinished = true
          })
      }
    },
    // 标签跳转
    tagToPath(val) {
      let href = location.hostname.endsWith('.woa.com')
        ? process.env.VUE_APP_V8_HOST_WOA
        : process.env.VUE_APP_V8_HOST
      href = `${href}/mat/user/search?keywords=${val}&from_page=ql新首页&type=label`
      window.open(href)
    }
  }
}
</script>
<style lang="less" scoped>
.des-container {
  background: #f8f8f8;
  .des-main {
    padding: 16px 16px 18px;
    background-color: #fff;
    //清除浮动
    .clearfix:after {
      content: '';
      display: block;
      height: 0;
      clear: both;
      visibility: hidden;
    }
    #file-desc {
      color: #00000066;
      font-size: 12px;
      line-height: 20px;
      word-break: break-all;
    }
    .tag-list-box {
      margin-top: 8px;
      .official-tag {
        font-size: 14px;
      }
      .item-tag {
        height: 24px;
        line-height: 24px;
        text-align: center;
        border-radius: 22px;
        opacity: 1;
        font-size: 10px;
        padding: 0px 12px;
        color: #00000099;
        background: #f7f8faff;
        float: left;
        margin-right: 8px;
        margin-bottom: 8px;
        .tag-label {
          line-height: 16px;
          height: 16px;
          max-width: 132px;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
      }
      .official-tag {
        background: #ecf2feff;
        color: #0052d9ff;
        &-i {
          background: url('~@/assets/img/mobile/official-tag.png') no-repeat;
          background-size: 14px;
          width: 14px;
          height: 14px;
          display: inline-block;
          margin-right: 4px;
          position: relative;
          top: 3px;
        }
      }
      .hot-tag {
        background: #fbf2e8;
        color: #ff5923ff;
        &-i {
          background: url('~@/assets/img/mobile/hot-tag.png') no-repeat;
          background-size: 14px;
          width: 14px;
          height: 14px;
          display: inline-block;
          margin-right: 4px;
          position: relative;
          top: 3px;
        }
      }
    }
    .tag-height {
      height: 32px;
      overflow: hidden;
    } 
    .expand-and-open {
      text-align: center;
      color: #00000099;
      font-size: 12px;
      line-height: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .expand-icon {
        background: url('~@/assets/img/mobile/arrow-down.png') no-repeat
          center/cover;
        height: 16px;
        width: 16px;
        display: inline-block;
        transform: rotate(0deg);
        transition: all 0.3s;
        margin-top: 2px;
      }
      .packUp-icon {
        margin-top: -2px;
        transform: rotate(180deg);
      }
    }
  }
  .card-list {
    margin-top: 8px;
    padding: 0px 16px;
    background: #fff;
    .content-list {
      border-bottom: 0.5px solid #e7e7e7;
    }
  }
}
</style>
