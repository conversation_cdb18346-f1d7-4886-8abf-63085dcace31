<template>
  <div>
    <el-dialog class="give-course-dialog" :visible.sync="dialogVisible" width="552px">
      <div slot="title" class="title">
        <span>分享学霸卡，邀请小伙伴兑换<span class="blue">价值百元</span>的{{cardName ?  `【${cardName}】` : '' }}精品好课。分享成功您还有机会获得更多学霸卡。</span>
      </div>
      <div class="harvard-shear-content">
        <div class="copy-content">
          <span class="url-text">{{urlText}}</span>
          <span class="copy-btn" v-if="shearType === 'home'" @click="doCopy" :dt-eid="dtBtnMain('eid','复制链接，转发分享')" :dt-areaid="dtBtnMain('area','复制链接，转发分享')" :dt-remark="dtBtnMain('remark','复制链接，转发分享')"><i class="el-icon-link"></i> 复制链接，转发分享</span>
          <span class="copy-btn" v-else @click="doCopy" :dt-areaid="dtBottonDetail('area', '复制链接，转发分享')" :dt-eid="dtBottonDetail('eid', '复制链接，转发分享')" :dt-remark="dtBottonDetail('remark', '复制链接，转发分享')"><i class="el-icon-link"></i> 复制链接，转发分享</span>
        </div>
        <div class="tips"><i>*</i> 注意：此活动仅限腾讯员工参与，外部人士无法接受邀请</div>
        <!-- <div class="shear-dialog-img">
          <img :src="require('@/assets/outsourcedCourse/shear-dialog.png')" alt="">
        </div> -->
      </div>
    </el-dialog>
  </div>

</template>

<script>
import { getDefaultActivityInfo } from '@/config/mooc.api.conf.js'
export default {
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    courseType: {
      type: String
    },
    xueBaCardConfig: {
      type: Object,
      default: () => {}
    },
    shearType: {
      type: String,
      default: 'home'
    },
    cardName: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      urlText: '',
      info: {}
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        console.log(val.staff_name)
        if (val.staff_name) {
        }
      },
      immediate: true
    },
    courseType: {
      handler(val) {
        if (val) {
          this.getInfo()
        }
      },
      immediate: true
    }
  },
  mounted() {},
  computed: {
    dialogVisible: {
      set(val) {
        this.$emit('update:isShow', val)
      },
      get() {
        return this.isShow
      }
    },
    activityId() {
      return this.$route.query.activityId
    },
    // dtBotton() {
    //   return (type, name) => {
    //     let { audience_id, audience_name, card_name } = this.xueBaCardConfig
    //     if (type === 'area') {
    //       return `area_${audience_id}_receive`
    //     } else if (type === 'eid') {
    //       return `element_${audience_id}_receive`
    //     } else if (type === 'remark') {
    //       return JSON.stringify({
    //         page: `${card_name}活动首页`,
    //         page_type: `${card_name}活动首页`,
    //         container: audience_name,
    //         container_id: '',
    //         click_type: 'button',
    //         content_name: name,
    //         terminal: 'PC'
    //       })
    //     }
    //   }
    // },
    dtBtnMain() {
      return (type, name) => {
        if (type === 'area') {
          return `area_${name}`
        } else if (type === 'eid') {
          return `element_${name}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${this.cardName}学霸卡活动落地页`,
            page_type: '学霸卡活动落地页',
            container: '',
            click_type: 'button',
            content_type: '',
            act_type: '',
            content_id: '',
            content_name: name,
            terminal: 'PC',
            page_id: '',
            container_id: ''
          })
        }
      }
    },
    dtBottonDetail() {
      return (type, name) => {
        if (type === 'area') {
          return `area_${name}_receive`
        } else if (type === 'eid') {
          return `element_${name}_receive`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `项目详情`,
            page_type: `项目详情`,
            container: '',
            container_id: '',
            click_type: 'button',
            content_name: name,
            terminal: 'PC'
          })
        }
      }
    }
    // urlText() {
    //   let userInfo = JSON.parse(sessionStorage.getItem('login_user')) || {}
    //   console.log(userInfo, 'userInfouserInfo')
    //   let url = `https://sdc.qq.com/s/PTUrrm?scheme_type=harvard&activityId=${this.activityId}`
    //   return `${url}&staff_name=${userInfo.staff_name}&staff_id=${userInfo.staff_id}`
    // }
  },
  methods: {
    getInfo() {
      getDefaultActivityInfo({ acctTypeCode: this.courseType }).then((res) => {
        console.log(res, '活动信息')
        console.log(this.$store.state, 'this.$store.state')
        const { userInfo } = this.$store.state
        this.info = res
        this.urlText = `https://sdc.qq.com/s/hxbgLe?scheme_type=xueba&type=${this.courseType}&activityId=${res.activity_id}&staff_name=${userInfo.staff_name}&staff_id=${userInfo.staff_id}`
      })
    },
    doCopy() {
      let userInfo = JSON.parse(sessionStorage.getItem('login_user')) || {}
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      let str = `关爱月，共成长！${userInfo.staff_name}向您赠送了一张价值百元的关爱月特别学霸卡，100门新课限时1个月免费兑换学习，点击链接即可领取兑换（${this.urlText}）`
      input.value = str
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      this.$message.success('已复制链接，去粘贴分享吧')
      // 复制后移除输入框
      input.remove()
    }
  }
}
</script>
<style lang="less">
.content-xueba {
  padding: 0 32px;
  .content-xueba-title {
    color: #000000e6;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
    padding: 16px 0;
  }
  .el-button--default {
    background-color: #fff;
    color: #0052d9;
    border: 1px solid #dcdcdc;
  }
  .el-button--default:hover {
    background-color: #fff;
    color: #0052d9;
    border: 1px solid #0052d9;
  }
}
</style>
<style lang="less" scoped>
.blue {
  color: #0052d9;
}
.give-course-dialog {
  font-family: 'PingFang SC';
  .title {
    color: #000000e6;
    font-family: 'PingFang SC';
    font-size: 16px;
    font-style: normal;
    font-weight: 600;
    line-height: 24px;
  }
  /deep/.el-dialog {
    background: linear-gradient(180deg, #e3f2ff 0%, #e3f2ff 0%, #eaf5ff 35.99%);
  }
  /deep/.el-dialog__body {
    padding: 0 24px 24px;
  }
  /deep/.el-dialog__header {
    padding: 24px 40px 20px 24px;
    border-bottom: unset;
  }
  /deep/.el-dialog__headerbtn {
    top: 20px;
    width: 20px;
    height: 20px;
    background-color: #fff;
    border-radius: 10px;
  }
  /deep/.el-dialog__close {
    color: #00000099;
    font-weight: 600;
  }
  /deep/.el-dialog__footer {
    padding: 0 32px 32px;
  }
  .harvard-shear-content {
    position: relative;
    padding: 16px;
    padding-bottom: 24px;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 14px 0 #f0f5ff40;
    .copy-content {
      display: flex;
      padding: 7px 8px;
      padding-left: 16px;
      border: 1px solid #d2e2f1;
      border-radius: 44px;
      .url-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-right: 20px;
        line-height: 32px;
      }
      .copy-btn {
        width: 170px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        background-color: #0052d9;
        color: #fff;
        border-radius: 53px;
        cursor: pointer;
      }
    }
    .tips {
      margin-top: 16px;
      color: #00000099;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      i {
        color: #e34d59;
      }
    }
    .shear-dialog-img {
      position: absolute;
      bottom: 8px;
      right: 16px;
      width: 56px;
      height: 56px;
      img {
        width: 56px;
        height: 56px;
      }
    }
  }
  .qr-code {
    display: flex;
    padding-top: 20px;
    margin-top: 20px;
    border-top: 1px solid #eee;
    .share-rq-code {
      width: 88px;
      height: 88px;
      background: #f5f5f5;
      border-radius: 6px;
      display: flex;
      justify-content: center;
      align-items: center;
      img {
        width: 88px;
        height: 88px;
        border-radius: 6px;
      }
    }
  }
}
</style>
