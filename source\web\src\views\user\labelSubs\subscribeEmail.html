<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Q-learning订阅课程</title>
    <style>
        div,p,table,tr,td,th,span,img,a,button,input,h1,h2,h3,h4,h5,h6{
            padding: 0;
            margin: 0;
        }
        .flex{
            display: flex;
        }
        .flex-1{
            flex: 1;
        }
        .align-center{
            align-items: center;
        }
        .justify-center{
            justify-content: center;
        }
        .justify-between{
            justify-content: space-between;
        }
        a{
            text-decoration: none;
        }
        body {
            font-family: "PingFang SC";
            padding:0;
            margin:0;
            background-image: linear-gradient(180deg, #ebf2ff 0%, #e4edff 21%, #f7faff 100%);
            background-repeat: no-repeat
        }
        table.layout {
            width: 1200px;
            margin: 0 auto;
            border-spacing: 0;
        }
        .email_page {
            width: 1200px;
            margin: 52px auto;
            position: relative;
            overflow: hidden;
        }
        .email_page .headImg{
            position: absolute;
            width: 100%;
            z-index: 0;
        }
        .email_page .email_content {
            position: relative;
            margin-top: 112px;
            padding: 24px 28px;
            background: linear-gradient(180deg, #FFF 0%, #E9F2FF 0%, #F7FAFF 0.01%, #FFF 100%);
            border-radius: 16px;
            z-index: 2;
        }
        .subs_tips {
            padding: 20px 28px;
            background: #EEF5FF;
            border-radius: 16px;
        }
        .subs_tips_text p{
            color: #000000e6;
            font-family: "PingFang SC";
            font-size: 20px;
            font-style: normal;
            font-weight: 600;
            line-height: 28px;
        }
        .subs_tips_text p.text_p2 {
            margin-top: 10px;
        }
        .subs_tips_text p a span {
            color: #0052d9;
            text-decoration: none;
        }
        .subs_tips_btn{
            width: 466px;
        }
        .subs_tips_btn a {
            display: inline-block;
            height: 44px;
            line-height: 44px;
            text-decoration: none;
            padding: 0 20px;
            margin-left: 16px;
            border-radius: 8px;
            color: #0052d9;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            background: #D9E6FF;
            border: none;
            cursor: pointer;
        }
        .subs_tips_btn a span {
            color: #0052d9;
        }
        .futureCourseData {
            overflow: hidden;
            margin-top: 23px;
        }
        .futureCourseData h4{
            color: #000000;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: 24px;
        }
        .more{
            padding: 4px 16px;
            background: #ECF2FE;
            border-radius: 4px;
            color: #0052d9;
            text-align: center;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 24px;
            cursor: pointer;
        }
        .more span {
            color: #0052d9;
        }
        .cardList {
            margin-top: 16px;
            display: inline-grid;
            grid-template-columns: 254px 254px 254px 254px;
            row-gap: 42px;
            column-gap: 42px;
        }
        .cardItem {
            /* width: 252px;
            height: 317px;
            margin-right: 42px; */
            display: block;
            border-radius: 8px;
            border: 1px solid #F6F6F6;
            background: #fff;
        }
        .cardItem:nth-child(4n + 0) {
            margin-right: 0;
        }
        .cardItem .courseImg {
            width: 252px;
            height: 167px;
            position: relative;
            overflow: hidden;
        }
        .cardItem .courseImg .defaultImg {
            width: 100%;
            max-width: 100%;
            max-height: 100%;
        }
        .cardItem .courseImg .notStart {
            position: absolute;
            bottom: 10px;
            right: 10px;
            display: inline-block;
            padding: 1px 6px;
            color: #ffffff;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            text-align: center;
            border-radius: 4px;
            background: #00000099;
        }
        .cardItem .courseImg .living {
            width: 58px;
            height: 20px;
            position: absolute;
            right: 10px;
            bottom: 10px;
        }
        .cardItem .courseImg .join {
            position: absolute;
            left: 10px;
            bottom: 10px;
            display: inline-block;
            padding: 1px 6px;
            color: #ffffff;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            text-align: center;
            border-radius: 4px;
            background: #00000099;
        }
        .cardItem .courseImg .updating{
            width: 64px;
            height: 26px;
            position: absolute;
            left: 0;
            top: 0;
        }
        .cardItem .courseImg .excellent{
            position: absolute;
            right: 10px;
            top: 10px;
            display: inline-block;
            border-radius: 4px;
            padding: 1px 6px;
            color: #ffffff;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            background: #FF3000;
        }
        .cardItem .courseImg .official{
            position: absolute;
            right: 10px;
            top: 10px;
            display: inline-block;
            border-radius: 4px;
            padding: 1px 6px;
            color: #ffffff;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            background: #0052D9;
        }
        .schedule{
            position: absolute;
            top: 10px;
            left: 0;
            width: 100%;
            text-align: right;
        }
        .cardItem .courseImg .schedule .excellent{
            margin-left: 8px;
            position: relative;
            top: 0;
        }
        .cardItem .courseImg .schedule .official{
            position: relative;
            top: 0;
        }
        .hangjiaInfo{
            position: absolute;
            width: 100%;
            height: 100%;
            bottom: 5px;
            padding: 12px;
            padding-top: 71px;
            padding-bottom: 7px;
            display: flex;
            align-items: center;
            box-sizing: border-box;
        }
        .hangjiaInfo .group{
            position: absolute;
            right: 0;
            top: 0;
            color: #bd6600;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            border-radius: 0 4px;
            padding: 2px 6px;
            background: #FFEABB;
        }
        .hangjiaInfo .avatar{
            margin-right: 20px;
        }
        .hangjiaInfo .avatar .good_hj{
            width: 85px;
            height: 68px;
            background: url('https://xue.m.tencent.com/mail/labelsub/good_hj.png');
            background-size: 100% 100%;
            text-align: center;
            position: relative;
            padding-top: 5px;
            padding-left: 1px;
        }
        .hangjiaInfo .avatar .good_hj_text{
            position: absolute;
            bottom: 0px;
            height: 20px;
            width: 60px;
            right: 11px;
        }
        .hangjiaInfo .avatar .good_hj_img{
            width: 52px;
            height: 52px;
            border-radius: 50%;
        }
        .hangjiaInfo .rightInfo{
            flex: 1;
        }
        .hangjiaInfo .rightInfo .expertName{
            color: #333333;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
        }
        .hangjiaInfo .rightInfo .tag{
            color: #777777;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            margin-top: 2px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            word-break: break-all;
        }
        .hangjiaInfo .rightInfo .consult{
            display: inline-block;
            margin-top: 4px;
            color: #a3a3a3;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            align-items: center;
        }
        .hangjiaInfo .rightInfo .consult .split{
            margin: 0 7px;
        }
        .hangjiaInfo .rightInfo .consult .active{
            color: #0052d9;
            margin: 0 1px;
        }
        .courseInfo {
            padding: 18px 5px 0 12px;
        }
        .ascription{
            padding: 0 12px 18px;
        }
        .courseTitle {
            height: 48px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            word-break: break-all;
        }
        .courseTitle .tag {
            display: inline-block;
            padding: 0 4px;
            border-radius: 2px;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            color: #777777;
            background: #F5F5F7
        }
        .courseTitle .text {
            color: #333333;
            text-align: justify;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            line-height: 24px;
        }
        .courseViews{
            display: flex;
            margin-top: 12px;
        }
        .courseViews img{
            width: 16px;
            height: 16px;
            margin-right: 2px;
        }
        .courseViews span{
            margin-right: 8px;
            color: #a3a3a3;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
        }
        .courseViews span:last-child{
            margin-right: 0;
        }
        .courseViews span.real_price {
            color: #FF7200;
        }
        .courseViews span.origin_price {
            color: #A3A3A3;
            text-decoration: line-through;
        }
        .courseTime {
            display: flex;
            margin-top: 12px;
            color: #a3a3a3;
            text-align: justify;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
        }
        .courseTime img {
            width: 16px;
            height: 16px;
            margin-right: 2px;
        }
        .courseLocation {
            margin-top: 4px;
        }
        .courseLocation  img{
            width: 16px;
            height: 16px;
            margin-right: 2px;
        }
        .courseLocation span {
            color: #a3a3a3;
            text-align: justify;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .courseRelatedLabelList {
            height: 22px;
            overflow: hidden;
            margin-top: 12px;
        }
        .courseRelatedLabelList a {
            display: inline-block;
            margin-right: 8px;
            padding: 2px 6px;
            border-radius: 4px;
            color: #777777;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
            background: #F5F7FA;
            cursor: pointer;
        }
        .courseRelatedLabelList a span{
            color: #777777;
        }
        .courseRelatedLabelList .active{
            color: #0052d9;
            background: #F4F9FE;
        }
        .courseRelatedLabelList .active span {
            color: #0052d9;
        }
        .courseRelatedSpecail{
            margin-top: 12px;
            color: #00000066;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
        }
        .courseRelatedSpecail a span{
            color: #000000e6;
            cursor: pointer;
        }
        .courseRelatedSpecail a span:hover{
            color: #0052D9;
        }
        .tableList {
            margin-top: 16px;
        }
        .tableItem{
            margin-bottom: 12px;
        }
        .tableItem .title{
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            overflow: hidden;
            color: #000000e6;
            text-overflow: ellipsis;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: 22px;
            cursor: pointer;
        }
        .tableItem .title .tag{
            display: inline-block;
            padding: 0 4px;
            border-radius: 2px;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: 18px;
            color: #777777;
            background: #F5F5F7
        }
        .tableItem .title span{
            color: #000000e6;
            margin-left: 6px;
        }
        .tableItem .rightInfo img{
            width: 16px;
            height: 16px;
            margin-right: 2px;
        }
        .tableItem .rightInfo .time{
            margin-left: 16px;
            color: #a3a3a3;
            text-align: justify;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
        }
        .tableItem .rightInfo .location{
            margin-left: 16px;
            color: #a3a3a3;
            text-align: justify;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 20px;
        }
        .tableItem .rightInfo .relatedSpecial {
            margin-left: 16px;
            color: #00000066;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
        }
        .tableItem .rightInfo .relatedSpecial a span{
            color: #000000e6;
            cursor: pointer;
        }
        .tableItem .rightInfo .relatedLabel {
            display: inline-flex;
            margin-left: 16px;
            padding: 2px 6px;
            background: #F4F9FE;
            cursor: pointer;
        }
        .tableItem .rightInfo .relatedLabel span {
            display: inline-block;
            color: #0052d9;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 18px;
            border-radius: 4px;
            margin-left: 2px;
        }
        .previousCourseData{
            overflow: hidden;
            margin-top: 20px;
        }
        .o-main .new-content {
            font-style: normal;
            position: relative;
            line-height: initial;
            font-weight: 400;
            padding: 16px 0 32px;
        }
        .o-main .new-content header {
            color: #000000;
            font-family: "PingFang SC";
            font-size: 16px;
            font-weight: 600;
            line-height: 24px;
            margin: 0 0 16px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .o-main .new-content header a {
            padding: 8px 32px;
            color: #0052d9;
            font-family: "PingFang SC";
            font-size: 16px;
            font-weight: 500;
            line-height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            background: #ECF2FE;
            text-decoration: none;
        }
        .o-main .new-content .card-block .card {
            width: 268px;
        }
        .o-main .new-content .card-block .card .card-header {
            width: 268px;
            height: 178px;
            border-radius: 8px;
            overflow: hidden;
            position: relative;
            margin-bottom: 18px;
        }
        .o-main .new-content .card-block .card .card-header .card-img {
            width: 100%;
            height: 100%;
        }
        .o-main .new-content .card-block .card .card-header .time {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.8);
            color: #FFFFFF;
            font-family: "PingFang SC";
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;
        }
        .o-main .new-content .card-block .card .card-bottom {
            padding: 0 12px 18px 12px;
            border-radius: 0 0 8px 8px;
        }
        .o-main .new-content .card-block .card .card-bottom .card-title {
            line-height: 24px;
            font-size: 16px;
            color: #333333;
            font-family: "PingFang SC";
            height: 48px;
            margin: 0 0 12px 0;
            text-decoration: none;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        .o-main .new-content .card-block .card .card-bottom .card-title .tag {
            display: inline-block;
            padding: 0 4px;
            border-radius: 2px;
            background-color: #F5F5F7;
            line-height: 18px;
            font-size: 12px;
            color: #777777;
            font-weight: 500;
            vertical-align: middle;
            margin: 0 8px 4px 0;
        }
        .o-main .new-content .card-block .card .card-bottom .detail {
            display: flex;
            align-items: center;
        }
        .o-main .new-content .card-block .card .card-bottom .detail .rank {
            display: flex;
            font-size: 12px;
            font-family: "PingFang SC";
            text-decoration: none;
        }
        .o-main .new-content .card-block .card .card-bottom .detail .rank .rank-type {
            height: 22px;
            padding: 2px 6px;
            border-radius: 4px 0 0 4px;
            background: linear-gradient(90deg, #FCF1DA 0%, #ECD09D 100%);
            color: #996b2d;
            white-space: nowrap;
            box-sizing: border-box;
            text-decoration: none;
        }
        .o-main .new-content .card-block .card .card-bottom .detail .rank .rank-detail {
            color: #c29344;
            padding: 2px 6px;
            background-color: #FDF4E0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-decoration: none;
        }
        .o-main .new-content .card-block .card .card-bottom .detail .view-count {
            color: #777777;
            font-family: "PingFang SC";
            font-size: 14px;
            line-height: 22px;
        }
        .o-main .new-content .card-block .card .card-bottom .labels {
            display: flex;
            flex-wrap: wrap;
            overflow: hidden;
            margin: 12px 0 0 0;
            height: 22px;
            word-break: break-all;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
        }
        .o-main .new-content .card-block .card .card-bottom .labels .label {
            padding: 2px 6px;
            border-radius: 4px;
            background-color: #F5F7FA;
            color: #777777;
            font-family: "PingFang SC";
            font-size: 12px;
            line-height: 18px;
            margin-right: 8px;
        }
        .o-main .new-content .card-block .m-header {
            display: flex;
            justify-content: space-between;
            margin: 0 0 20px 0;
        }
        .o-main .new-content .card-block .m-header .big-card {
            display: flex;
            border-radius: 12px;
            width: 562px;
            height: 210px;
            padding: 16px 22px 16px 16px;
            border: 1px solid #F7ECB6;
            background: linear-gradient(119deg, #FDEDCD 15.64%, #FFFCF5 61.73%, #FFFDF2 98.69%);
            box-sizing: border-box;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-left {
            width: 268px;
            height: 178px;
            flex-shrink: 0;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-left .card-header {
            position: relative;
            height: 100%;
            width: 100%;
            display: block;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-left .card-header img {
            width: 100%;
            height: 100%;
            border-radius: 4px;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-left .card-header .play-icon {
            width: 48px;
            height: 48px;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
        .o-main .new-content .card-block .m-header .big-card .bc-left .card-header .time {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.8);
            color: #FFFFFF;
            font-family: "PingFang SC";
            font-size: 12px;
            padding: 2px 6px;
            border-radius: 4px;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-right {
            display: flex;
            flex-direction: column;
            padding: 18px 22px 0 12px;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-right .card-title {
            line-height: 24px;
            font-size: 16px;
            color: #333333;
            font-family: "PingFang SC";
            height: 48px;
            margin: 0 0 12px 0;
            text-decoration: none;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-right .card-title .tag {
            display: inline-block;
            padding: 0 4px;
            border-radius: 2px;
            background-color: #F5F5F7;
            line-height: 18px;
            font-size: 12px;
            color: #777777;
            font-weight: 500;
            vertical-align: middle;
            margin: 0 8px 4px 0;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-right .detail .rank {
            display: flex;
            font-size: 12px;
            font-family: "PingFang SC";
            text-decoration: none;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-right .detail .rank .rank-type {
            padding: 2px 6px;
            border-radius: 4px 0 0 4px;
            background: linear-gradient(90deg, #FCF1DA 0%, #ECD09D 100%);
            color: #996b2d;
            box-sizing: border-box;
            line-height: 18px;
            white-space: nowrap;
            text-decoration: none;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-right .detail .rank .rank-detail {
            color: #c29344;
            padding: 2px 6px;
            background-color: #FDF4E0;
            box-sizing: border-box;
            line-height: 18px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            text-decoration: none;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-right .detail .view-count {
            margin: 8px 0 0 0;
            color: #777777;
            font-family: "PingFang SC";
            font-size: 14px;
            line-height: 22px;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-right .labels {
            display: flex;
            flex-wrap: wrap;
            overflow: hidden;
            margin: 12px 0 0 0;
            height: 22px;
            word-break: break-all;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-right .labels .label {
            padding: 2px 6px;
            border-radius: 4px;
            background-color: #F5F7FA;
            color: #777777;
            font-family: "PingFang SC";
            font-size: 12px;
            line-height: 18px;
            margin-right: 8px;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-right .labels-wrap {
            height: auto;
            -webkit-line-clamp: 2;
            height: 56px;
        }
        .o-main .new-content .card-block .m-header .big-card .bc-right .labels-wrap .label {
            margin-bottom: 6px;
        }
        .o-main .new-content .card-block .m-header .bule-color {
            border: 1px solid #E1EAFF;
            background: linear-gradient(119deg, #E3E9FF 15.64%, #FBFDFF 61.73%, #F2FDFF 98.69%);
        }
        .o-main .new-content .card-block .card-contain {
            margin: 20px 0 0 0;
            display: flex;
            flex-wrap: wrap;
            gap: 20px 24px;
        }
        .o-main .new-content .card-block .card-contain .card .card-header {
            border-radius: 4px 4px 0 0;
            display: block;
        }
        .o-main .new-content .card-block .card-contain .card .card-bottom {
            border: 1px solid #f2f3f2;
            border-top: transparent;
        }
        .o-main .new-content .footer-btn {
            display: flex;
            justify-content: space-between;
        }
        .o-main .new-content .footer-btn .check-new-course {
            margin: 20px auto 48px;
            width: 560px;
            height: 64px;
            text-align: center;
            line-height: 64px;
            border-radius: 4px;
            background-color: #ECF2FE;
            color: #0052d9;
            font-family: "PingFang SC";
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            text-decoration: none;
        }
        .o-main .new-content .banner {
            display: block;
            width: 100%;
            margin: 28px 0 0 0;
        }
        .o-main .new-content .banner img {
            width: 100%;
        }
        .o-main .new-content .go-look {
            display: block;
            margin: 20px auto 0;
            width: 380px;
            height: 64px;
            border-radius: 64px;
            text-align: center;
            line-height: 64px;
            background-color: #115BFF;
            color: #ffffff;
            font-family: "PingFang SC";
            font-size: 22px;
            font-weight: 600;
            position: relative;
            text-decoration: none;
        }
        .o-main .new-content .go-look img {
            position: absolute;
            bottom: -20px;
            right: -26px;
            width: 52px;
            height: 52px;
        }
    </style>
</head>
<body>
    <table class="layout">
        <tbody>
            <tr>
                <td>
                    <div class="email_page">
                        <img src="https://xue.m.tencent.com/mail/labelsub/head.png" class="headImg" alt="">
                        <div class="email_content">
                            <div class="subs_tips flex justify-between">
                                <div class="subs_tips_text">
                                    <p>Hi，${staffName}</p>
                                    <p class="text_p2">您订阅的<a href="https://portal.learn.woa.com/training/label-subs?jump_from=mail4&project=qlxtts&source=bqdy"><span>标签及专区</span></a>最近一周新增了以下内容，开始学习吧！</p>
                                </div>
                                <div class="subs_tips_btn">
                                    <a href="${moreLink}"><span>查看更多内容</span></a>
                                    <a href="https://portal.learn.woa.com/training/label-subs?dialog=1&jump_from=mail4&project=qlxtts&source=bqdy"><span>管理我的订阅</span></a>
                                    <a href="https://portal.learn.woa.com/training/label-subs?dialog=2&jump_from=mail4&project=qlxtts&source=bqdy"><span>修改订阅提醒</span></a>
                                </div>
                            </div>
                            <#if futureCardList?? && futureCardList?size gt 0>
                            <div class="futureCourseData">
                                <div class="flex align-center justify-between">
                                    <h4>即将开始</h4>
                                    <a class="more" href="https://portal.learn.woa.com/training/label-subs?jump_from=mail4&project=qlxtts&source=bqdy"><span>查看更多</span></a>
                                </div>
                                <div class="futureCardList cardList">
                                    <#list futureCardList as futureCard>
                                    <div class="cardItem">
                                        <a href="${futureCard.href}">
                                            <#if futureCard.module_id == 2>
                                            <div class="courseImg flex align-center justify-center">
                                                <img src="${futureCard.thumbnail_url}" class="defaultImg" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/default-photo-2.png'" alt="">
                                                <span class="notStart">${futureCard.origin_data.est_dur!0}分钟</span>
                                            </div>
                                            <div class="courseInfo">
                                                <div class="courseTitle">
                                                    <#if futureCard.origin_data.appointmentStatus == 1>
                                                    <span class="tag">面授课报名</span>
                                                    <#else>
                                                    <span class="tag">面授课</span>
                                                    </#if>
                                                    <span class="text" title="${futureCard.title}">${futureCard.title}</span>
                                                </div>
                                                <div class="courseViews flex align-center">
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                    <span>${futureCard.view_count!0}</span>
                                                    <#if futureCard.origin_data.avg_score ?? && (futureCard.origin_data.avg_score != '0') && (futureCard.origin_data.avg_score != '0.0')>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/flower2x.png" alt="">
                                                    <span>${futureCard.origin_data.avg_score}</span>
                                                    <#else>
                                                    <span>暂无评分</span>
                                                    </#if>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                    <span>${futureCard.origin_data.sort_time?substring(0,10)}</span>
                                                </div>
                                                <#if futureCard.origin_data.location?length gt 0>
                                                <div class="courseLocation flex align-center">
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/local2x.png" alt="">
                                                    <span title="${futureCard.origin_data.location}">${futureCard.origin_data.location}</span>
                                                </div>
                                                </#if>
                                            </div>
                                            </#if>
                                            <#if futureCard.module_id == 3>
                                            <div class="courseImg flex align-center justify-center">
                                                <img src="${futureCard.thumbnail_url}" class="defaultImg" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/default-photo-3.png'" alt="">
                                                <#if futureCard.origin_data.liveStatus == 1>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/living.png" class="living" alt="">
                                                </#if>
                                                <#if futureCard.origin_data.liveStatus == 2>
                                                <span class="notStart">未开始</span>
                                                </#if>
                                            </div>
                                            <div class="courseInfo">
                                                <div class="courseTitle">
                                                    <#if futureCard.origin_data.appointmentStatus == 1>
                                                    <span class="tag">直播预约</span>
                                                    <#else>
                                                    <span class="tag">直播</span>
                                                    </#if>
                                                    <span class="text" title="${futureCard.title}">${futureCard.title}</span>
                                                </div>
                                                <div class="courseTime flex align-center">
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                    <span>${futureCard.origin_data.start_time?substring(0,16)}-${futureCard.origin_data.end_time?substring(0,16)}</span>
                                                </div>
                                            </div>
                                            </#if>
                                            <#if futureCard.module_id == 4>
                                            <div class="courseImg flex align-center justify-center">
                                                <img src="${futureCard.thumbnail_url}" class="defaultImg" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/default-photo-4.png'" alt="">
                                                <span class="notStart">${futureCard.origin_data.est_dur!0}分钟</span>
                                            </div>
                                            <div class="courseInfo">
                                                <div class="courseTitle">
                                                    <#if futureCard.origin_data.appointmentStatus == 1>
                                                    <span class="tag">活动报名</span>
                                                    <#else>
                                                    <span class="tag">活动</span>
                                                    </#if>
                                                    <span class="text" title="${futureCard.title}">${futureCard.title}</span>
                                                </div>
                                                <div class="courseTime flex align-center">
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                    <span>${futureCard.origin_data.start_time?substring(0,16)} - ${futureCard.origin_data.end_time?substring(0,16)}</span>
                                                </div>
                                                <#if futureCard.origin_data.location?length gt 0>
                                                <div class="courseLocation flex align-center">
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/local2x.png" alt="">
                                                    <span title="${futureCard.origin_data.location}">${futureCard.origin_data.location}</span>
                                                </div>
                                                </#if>
                                            </div>
                                            </#if>
                                        </a>
                                        <div class="ascription">
                                            <#if futureCard.subscribeType == 1>
                                            <div class="courseRelatedLabelList">
                                                <a class="active" href="${futureCard.labelJumpUrl}"><span>${futureCard.relateLabelName}</span></a>
                                                <#list futureCard.labelList as labelItem>
                                                <a href="${labelItem.labelJumpUrl}"><span>${labelItem.labelName}</span></a>
                                                </#list>
                                            </div>
                                            </#if>
                                            <#if futureCard.subscribeType == 2>
                                            <div class="courseRelatedSpecail">
                                                来自专区<a href="${futureCard.labelJumpUrl}"><span>「${futureCard.relateLabelName}」</span></a>
                                            </div>
                                            </#if>
                                        </div>
                                    </div>
                                    </#list>
                                </div>
                                <#if futureTabList?? && futureTabList?size gt 0>
                                <div class="futureTableList tableList">
                                    <#list futureTabList as futureCard>
                                    <#if futureCard.module_id == 2>
                                    <div class="tableItem flex justify-between align-center">
                                        <a href="${futureCard.href}"></a>
                                            <div class="title flex align-center flex-1">
                                                <#if futureCard.origin_data.appointmentStatus == 1>
                                                <span class="tag">面授课预约</span>
                                                <#else>
                                                <span class="tag">面授课</span>
                                                </#if>
                                                <span>${futureCard.title}</span>
                                            </div>
                                        </a>
                                        <div class="rightInfo flex align-center">
                                            <#if futureCard.subscribeType == 1>
                                            <a class="relatedLabel flex align-center" href="${futureCard.labelJumpUrl}">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/tag.png" alt="">
                                                <span>${futureCard.relateLabelName}</span>
                                            </a>
                                            </#if>
                                            <#if futureCard.subscribeType == 2>
                                            <div class="relatedSpecial flex align-center">
                                                来自专区<a href="${futureCard.labelJumpUrl}"><span>「${futureCard.relateLabelName}」</span></a>
                                            </div>
                                            </#if>
                                            <#if futureCard.origin_data.location?length gt 0>
                                            <div class="location flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/local2x.png" alt="">
                                                <span>${futureCard.origin_data.location}</span>
                                            </div>
                                            </#if>
                                            <div class="time flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>${futureCard.origin_data.sort_time?substring(0,10)}</span>
                                            </div>
                                        </div>
                                    </div>
                                    </#if>
                                    <#if futureCard.module_id == 3>
                                    <div class="tableItem flex justify-between align-center">
                                        <a href="${futureCard.href}"></a>
                                            <div class="title flex align-center flex-1">
                                                <#if futureCard.origin_data.appointmentStatus == 1>
                                                <span class="tag">直播预约</span>
                                                <#else>
                                                <span class="tag">直播</span>
                                                </#if>
                                                <span>${futureCard.title}</span>
                                            </div>
                                        </a>
                                        <div class="rightInfo flex align-center">
                                            <#if futureCard.subscribeType == 1>
                                            <a class="relatedLabel flex align-center" href="${futureCard.labelJumpUrl}">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/tag.png" alt="">
                                                <span>${futureCard.relateLabelName}</span>
                                            </a>
                                            </#if>
                                            <#if futureCard.subscribeType == 2>
                                            <div class="relatedSpecial flex align-center">
                                                来自专区<a href="${futureCard.labelJumpUrl}"><span>「${futureCard.relateLabelName}」</span></a>
                                            </div>
                                            </#if>
                                            <div class="time flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>${futureCard.origin_data.start_time?substring(0,16)}-${futureCard.origin_data.end_time?substring(0,16)}</span>
                                            </div>
                                        </div>
                                    </div>
                                    </#if>
                                    <#if futureCard.module_id == 4>
                                    <div class="tableItem flex justify-between align-center">
                                        <a href="${futureCard.href}"></a>
                                            <div class="title flex align-center flex-1">
                                                <#if futureCard.origin_data.appointmentStatus == 1>
                                                <span class="tag">活动报名</span>
                                                <#else>
                                                <span class="tag">活动</span>
                                                </#if>
                                                <span>${futureCard.title}</span>
                                            </div>
                                        </a>
                                        <div class="rightInfo flex align-center">
                                            <#if futureCard.subscribeType == 1>
                                            <a class="relatedLabel flex align-center" href="${futureCard.labelJumpUrl}">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/tag.png" alt="">
                                                <span>${futureCard.relateLabelName}</span>
                                            </a>
                                            </#if>
                                            <#if futureCard.subscribeType == 2>
                                            <div class="relatedSpecial flex align-center">
                                                来自专区<a href="${futureCard.labelJumpUrl}"><span>「${futureCard.relateLabelName}」</span></a>
                                            </div>
                                            </#if>
                                            <#if futureCard.origin_data.location?length gt 0>
                                            <div class="location flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/local2x.png" alt="">
                                                <span>${futureCard.origin_data.location}</span>
                                            </div>
                                            </#if>
                                            <div class="time flex align-center">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                <span>${futureCard.origin_data.start_time?substring(0,16)} - ${futureCard.origin_data.end_time?substring(0,16)}</span>
                                            </div>
                                        </div>
                                    </div>
                                    </#if>
                                    </#list>
                                </div>
                                </#if>
                            </div>
                            </#if>
                            <#if previousCardList?? && previousCardList?size gt 0>
                            <div class="previousCourseData">
                                <div class="flex align-center justify-between">
                                    <h4>立即学习</h4>
                                    <a class="more" href="https://portal.learn.woa.com/training/label-subs?jump_from=mail4&project=qlxtts&source=bqdy"><span>查看更多</span></a>
                                </div>
                                <div class="previousCardList cardList">
                                    <#list previousCardList as futureCard>
                                    <div class="cardItem">
                                        <a href="${futureCard.href}">
                                            <#if futureCard.module_id == 1>
                                            <div class="courseImg flex align-center justify-center">
                                                <img src="${futureCard.thumbnail_url}" class="defaultImg" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/default-photo-1.png'" alt="">
                                                <span class="notStart">${futureCard.origin_data.est_dur!0}分钟</span>
                                            </div>
                                            <div class="courseInfo">
                                                <div class="courseTitle">
                                                    <span class="tag">网络课</span>
                                                    <span class="text" title="${futureCard.title}">${futureCard.title}</span>
                                                </div>
                                                <div class="courseViews flex align-center">
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                    <span>${futureCard.view_count!0}</span>
                                                    <#if futureCard.origin_data.avg_score ?? && (futureCard.origin_data.avg_score != '0') && (futureCard.origin_data.avg_score != '0.0')>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/flower2x.png" alt="">
                                                    <span>${futureCard.origin_data.avg_score}分</span>
                                                    <#else>
                                                    <span>暂无评分</span>
                                                    </#if>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                    <span>${futureCard.origin_data.sort_time?substring(0,10)}</span>
                                                </div>
                                            </div>
                                            </#if>
                                            <#if futureCard.module_id == 10>
                                            <div class="courseImg flex align-center justify-center">
                                                <img src="${futureCard.thumbnail_url}" class="defaultImg" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/default-photo-10.png'" alt="">
                                                <#if futureCard.origin_data.serial_type == 2>
                                                <img src="https://xue.m.tencent.com/mail/labelsub/updating.png" class="updating">
                                                </#if>
                                                <span class="notStart">${futureCard.origin_data.task_count!0}项任务</span>
                                                <#if futureCard.origin_data.join_status == 1>
                                                <span class="join">已参与</span>
                                                <#else>
                                                <span class="join">未参与</span>
                                                </#if>
                                                <#if futureCard.origin_data.excellent_status == 1>
                                                <span class="excellent">精品</span>
                                                </#if>
                                            </div>
                                            <div class="courseInfo">
                                                <div class="courseTitle">
                                                    <span class="tag">培养项目</span>
                                                    <span class="text" title="${futureCard.title}">${futureCard.title}</span>
                                                </div>
                                                <div class="courseViews flex align-center">
                                                    <#if futureCard.origin_data.score_count < 20>
                                                    <span>评分人数不足</span>
                                                    <#else>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/flower2x.png" alt="">
                                                    <span>${futureCard.origin_data.score!0}分</span>
                                                    </#if>
                                                    <#if futureCard.origin_data.show_join_count ?? && futureCard.origin_data.show_join_count == 1>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/people2x.png" alt="">
                                                    <span>${futureCard.origin_data.member_count!0}人参与</span>
                                                    </#if>
                                                </div>
                                            </div>
                                            </#if>
                                            <#if futureCard.module_id == 99>
                                            <div class="courseImg flex align-center justify-center">
                                                <img src="${futureCard.thumbnail_url}" class="defaultImg" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/link.png'" alt="">
                                            </div>
                                            <div class="courseInfo">
                                                <div class="courseTitle">
                                                    <span class="tag">外链课程</span>
                                                    <span class="text" title="${futureCard.title}">${futureCard.title}</span>
                                                </div>
                                                <div class="courseViews flex align-center">
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                    <span>${futureCard.origin_data.sort_time?substring(0,10)}</span>
                                                </div>
                                            </div>
                                            </#if>
                                            <#if futureCard.module_id == 16>
                                            <div class="courseImg flex align-center justify-center">
                                                <img src="${futureCard.thumbnail_url}" class="defaultImg" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/default-photo-16.png'" alt="">
                                            </div>
                                            <div class="courseInfo">
                                                <div class="courseTitle">
                                                    <span class="tag">文档</span>
                                                    <span class="text" title="${futureCard.title}">${futureCard.title}</span>
                                                </div>
                                                <div class="courseViews flex align-center">
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                    <span>${futureCard.view_count!0}</span>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/like2x.png" alt="">
                                                    <span>${futureCard.origin_data.likeNum!0}</span>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                    <span>${futureCard.origin_data.sort_time?substring(0,10)}</span>
                                                </div>
                                            </div>
                                            </#if>
                                            <#if futureCard.module_id == 8>
                                            <div class="courseImg flex align-center justify-center">
                                                <img src="${futureCard.thumbnail_url}" class="defaultImg" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/default-photo-8.png'" alt="">
                                                <span class="notStart">${futureCard.origin_data.wordNum!0}字</span>
                                                <#if futureCard.origin_data.excellent_status == 1>
                                                <span class="excellent">精品</span>
                                                </#if>
                                            </div>
                                            <div class="courseInfo">
                                                <div class="courseTitle">
                                                    <span class="tag">文章</span>
                                                    <span class="text" title="${futureCard.title}">${futureCard.title}</span>
                                                </div>
                                                <div class="courseViews flex align-center">
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                    <span>${futureCard.view_count!0}</span>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/like2x.png" alt="">
                                                    <span>${futureCard.origin_data.countLikes!0}</span>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                    <span>${futureCard.origin_data.sort_time?substring(0,10)}</span>
                                                </div>
                                            </div>
                                            </#if>
                                            <#if futureCard.module_id == 7>
                                            <div class="courseImg flex align-center justify-center">
                                                <img src="${futureCard.thumbnail_url}" class="defaultImg" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/default-photo-7.png'" alt="">
                                                <span class="notStart">${futureCard.origin_data.wordNum!0}字</span>
                                                <#if futureCard.origin_data.excellent_status == 1>
                                                <span class="excellent">精品</span>
                                                </#if>
                                            </div>
                                            <div class="courseInfo">
                                                <div class="courseTitle">
                                                    <span class="tag">案例</span>
                                                    <span class="text" title="${futureCard.title}">${futureCard.title}</span>
                                                </div>
                                                <div class="courseViews flex align-center">
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                    <span>${futureCard.view_count!0}</span>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/like2x.png" alt="">
                                                    <span>${futureCard.origin_data.topNum!0}</span>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                    <span>${futureCard.origin_data.sort_time?substring(0,10)}</span>
                                                </div>
                                            </div>
                                            </#if>
                                            <#if futureCard.module_id == 15>
                                            <div class="courseImg flex align-center justify-center">
                                                <img src="${futureCard.thumbnail_url}" class="defaultImg" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/default-photo-15.png'" alt="">
                                                <span class="notStart">${futureCard.origin_data.courses_count!0}门课</span>
                                                <div class="schedule">
                                                    <#if futureCard.origin_data.cl_type == 1>
                                                    <span class="official">官方</span>
                                                    </#if>
                                                    <#if futureCard.origin_data.excellent_status == 1>
                                                    <span class="excellent">精品</span>
                                                    </#if>
                                                </div>
                                            </div>
                                            <div class="courseInfo">
                                                <div class="courseTitle">
                                                    <span class="tag">课单</span>
                                                    <span class="text" title="${futureCard.title}">${futureCard.title}</span>
                                                </div>
                                                <div class="courseViews flex align-center">
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                    <span>${futureCard.view_count!0}</span>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/like2x.png" alt="">
                                                    <span>${futureCard.origin_data.praise_count!0}</span>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/star2x.png" alt="">
                                                    <span>${futureCard.origin_data.favorite_count!0}</span>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/comments2x.png" alt="">
                                                    <span>${futureCard.origin_data.comment_count!0}</span>
                                                </div>
                                            </div>
                                            </#if>
                                            <#if futureCard.module_id == 20>
                                            <div class="courseImg flex align-center justify-center">
                                                <img src="${futureCard.thumbnail_url}" class="defaultImg" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/default-photo-20.png'" alt="">
                                            </div>
                                            <div class="courseInfo">
                                                <div class="courseTitle">
                                                    <span class="tag">K吧文章</span>
                                                    <span class="text" title="${futureCard.title}">${futureCard.title}</span>
                                                </div>
                                                <div class="courseViews flex align-center">
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/eyes2x.png" alt="">
                                                    <span>${futureCard.view_count!0}</span>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/comments2x.png" alt="">
                                                    <span>${futureCard.origin_data.comment_count!0}</span>
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/time2x.png" alt="">
                                                    <span>${futureCard.created_at?substring(0,10)}</span>
                                                </div>
                                            </div>
                                            </#if>
                                            <#if futureCard.module_id == 6>
                                            <div class="courseImg flex align-center justify-center">
                                                <img src="${futureCard.thumbnail_url}" class="defaultImg" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/hangjiabg.png'" alt="">
                                                <div class="hangjiaInfo">
                                                    <div class="avatar">
                                                        <#if futureCard.origin_data.is_excellent_hangjia ?? && futureCard.origin_data.is_excellent_hangjia == 1>
                                                        <div class="good_hj">
                                                            <img class="good_hj_img" src="${futureCard.origin_data.expertImageUrl}" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/xt.png'">
                                                            <img class="good_hj_text" src="https://xue.m.tencent.com/mail/labelsub/good_hj_text.png" alt="">
                                                        </div>
                                                        <#else>
                                                        <img class="good_hj_img" src="${futureCard.origin_data.expertImageUrl}" onerror="this.src='https://xue.m.tencent.com/mail/labelsub/xt.png'">
                                                        </#if>
                                                    </div>
                                                    <div class="rightInfo">
                                                        <p class="expertName">${futureCard.origin_data.expertName}</p>
                                                        <p class="tag" title="${futureCard.origin_data.tag}">${futureCard.origin_data.tag}</p>
                                                        <div class="consult flex">
                                                            <span class="active">${futureCard.origin_data.meetNum!0}</span>单咨询
                                                            <span class="split">|</span>
                                                            <#if futureCard.origin_data.count??>
                                                            <span><span class="active">${futureCard.origin_data.count}</span>分</span>
                                                            <#else>
                                                            <span v-else>暂无评分</span>
                                                            </#if>
                                                        </div>
                                                    </div>
                                                    <#if futureCard.origin_data.least_group_sharing_count ??>
                                                    <span class="group">${futureCard.origin_data.least_group_sharing_count}人预约即可成团</span>
                                                    </#if>
                                                </div>
                                            </div>
                                            <div class="courseInfo">
                                                <div class="courseTitle">
                                                    <span class="tag">行家</span>
                                                    <span class="text" title="${futureCard.title}">${futureCard.title}</span>
                                                </div>
                                                <div class="courseViews flex align-center">
                                                    <img src="https://xue.m.tencent.com/mail/labelsub/jf.png" alt="">
                                                    <#if futureCard.origin_data.real_price ??>
                                                    <span class="real_price">${futureCard.origin_data.real_price} 积分/次</span>
                                                    <#else>
                                                    <span class="real_price">免费咨询</span>
                                                    </#if>
                                                    <#if futureCard.origin_data.origin_price ?? && (futureCard.origin_data.origin_price > futureCard.origin_data.real_price)>
                                                    <span class="origin_price">${futureCard.origin_data.origin_price}积分/次</span>
                                                    </#if>
                                                </div>
                                            </div>
                                            </#if>
                                        </a>
                                        <div class="ascription">
                                            <#if futureCard.subscribeType == 1>
                                            <div class="courseRelatedLabelList">
                                                <a class="active" href="${futureCard.labelJumpUrl}"><span>${futureCard.relateLabelName}</span></a>
                                                <#list futureCard.labelList as labelItem>
                                                <a href="${labelItem.labelJumpUrl}"><span>${labelItem.labelName}</span></a>
                                                </#list>
                                            </div>
                                            </#if>
                                            <#if futureCard.subscribeType == 2>
                                            <div class="courseRelatedSpecail">
                                                来自专区<a href="${futureCard.labelJumpUrl}"><span>「${futureCard.relateLabelName}」</span></a>
                                            </div>
                                            </#if>
                                        </div>
                                    </div>
                                    </#list>
                                </div>
                                <#if previousTabList?? && previousTabList?size gt 0>
                                <div class="previousTableList tableList">
                                    <#list previousTabList as futureCard> 
                                    <div class="tableItem flex justify-between align-center">
                                        <a href="${futureCard.href}">
                                            <div class="title flex align-center flex-1">
                                                <span class="tag">${futureCard.module_name}</span>
                                                <span>${futureCard.title}</span>
                                            </div>
                                        </a>
                                        <div class="rightInfo flex align-center">
                                            <#if futureCard.subscribeType == 1>
                                            <a class="relatedLabel flex align-center" href="${futureCard.labelJumpUrl}">
                                                <img src="https://xue.m.tencent.com/mail/labelsub/tag.png" alt="">
                                                <span>${futureCard.relateLabelName}</span>
                                            </a>
                                            </#if>
                                            <#if futureCard.subscribeType == 2>
                                            <div class="relatedSpecial flex align-center">
                                                来自专区<a href="${futureCard.labelJumpUrl}"><span>「${futureCard.relateLabelName}」</span></a>
                                            </div>
                                            </#if>
                                        </div>
                                    </div>
                                    </#list>
                                </div>
                                </#if>
                            </div>
                            </#if>

                            <!-- 新内容 -->
                            <div class="new-content" style="margin-top: 40px; font-family: PingFang SC;">
																		
                                <#if ai_recommend?? && ai_recommend?size gt 0>											  
                                <div style="height: 68px; color: #000000e6;font-size: 20px;font-weight: 600;line-height: 68px;padding-left: 28px;background: #EEF5FF;border-radius: 16px;margin-bottom: 32px;">
                                    小腾老师认为以下课程也非常适合您哦！
                                </div>
                                
                                <div class="card-block">
                                    <header style="height: 24px;">
                                        <span>AI智能推荐</span>
                                        <a href="https://sdc.qq.com/s/Shd3Jk?scheme_type=homepage&jump_from=mail4&project=qlxtts&source=aitj">更多你可能喜欢的好课</a>
                                    </header>

                                    <div class="card-contain">
                                        <#list ai_recommend as item>
                                       <div class="card">
                                           <a class="card-header" href="${item.contentUrl}">
                                               <img src="${item.photoUrl}" class="card-img" alt="">
                                               <#if item.duration && item.duration gt 0>
                                                    <div class="time">${item.duration}分钟</div>
                                                </#if>
                                           </a>
                               
                                           <div class="card-bottom">
                                               <a class="card-title" href="${item.contentUrl}">
                                                   <span class="tag">${item.moduleName}</span>
                                                   ${item.contentName}
                                               </a>
                                   
                                               <div class="detail">
                                                   <#if item.rankType ??>
                                                    <div class="rank">
                                                        <a href="${item.rankLink}" class="rank-type">${item.rankType}</a>
                                                        <a href="${item.rankLink}" class="rank-detail">${item.rankTitle}</a>
                                                    </div>
                                                   <#else>
                                                       <#if item.playTotalCount == 0>
                                                       <div class="view-count">
                                                           潜力新课
                                                       </div>
                                                       <#else>
                                                       <div class="view-count">
                                                           ${item.playTotalCount}人观看
                                                       </div>
                                                       </#if>
                                                   </#if>
                                               </div>
                                   
                                               <div class="labels">
                                                   <#list item.labels as tag>
                                                       <div class="label">${tag}</div>
                                                   </#list>
                                               </div>
                                           </div>
                                       </div>
                                       </#list>
                                   </div>
                                </div>
                                </#if>
                                
                                
                                <#if recommandType?? && (recommandType == "new" || recommandType == "hot")>
                                <div class="card-block" style="margin-top: 40px;">
                                    <header style="height: 24px;">
                                        <#if recommandType == "new">
                                        <span>Q-learning上榜新课</span>
                                        <a href="https://sdc.qq.com/s/Shd3Jk?scheme_type=homepage&configId=5076&configIdMobile=5100&componentName=新课榜&jump_from=mail4&project=qlxtts&source=xkb">查看更多新课</a>
                                        </#if>

                                        <#if recommandType == "hot">
                                        <span>Q-learning人气好课</span>
                                        <a href="https://sdc.qq.com/s/Shd3Jk?scheme_type=homepage&configId=5014&configIdMobile=5100&componentName=热课榜&jump_from=mail4&project=qlxtts&source=rxb">查看更多人气好课</a>
                                        </#if>
                                    </header>

                                    <#if top_courses?? && top_courses?size gt 0>
                                    <div class="m-header">
                                        <#list top_courses as item>
                                        <div class="big-card <#if item_index == 1>bule-color</#if>">
                                        
                                        <div class="bc-left">
                                            <a class="card-header" href="${item.contentUrl}">
                                                <img src="https://xue.m.tencent.com/mail/recommend/play.png" alt="" class="play-icon">
                                                <img src="${item.photoUrl}" class="card-img" alt="">
                                                <#if item.duration && item.duration gt 0>
                                                    <div class="time">${item.duration}分钟</div>
                                                </#if>
                                            </a>
                                        </div>
                                
                                        <div class="bc-right">
                                                <a class="card-title" href="${item.contentUrl}">
                                                    <span class="tag">${item.moduleName}</span>
                                                    ${item.contentName}
                                                </a>
                                
                                                <div class="detail">
                                                <#if item.rankType ??>
                                                    <div class="rank">
                                                        <a href="${item.rankLink}" class="rank-type">${item.rankType}</a>
                                                        <a href="${item.rankLink}" class="rank-detail">${item.rankTitle}</a>
                                                  </div>
                                                </#if>
                            
                                                <#if item.playTotalCount == 0>
                                                    <div class="view-count">
                                                    潜力新课
                                                    </div>
                                                <#else>
                                                    <div class="view-count">
                                                    ${item.playTotalCount}人观看
                                                    </div>
                                                </#if>
                                                </div>
                                
                                                <#if item.rankType ??>
                                                <div class="labels">
                                                    <#list item.labels as tag>
                                                    <div class="label">${tag}</div>
                                                    </#list>
                                                </div>
                                                <#else>
                                                <div class="labels labels-wrap">
                                                    <#list item.labels as tag>
                                                    <div class="label">${tag}</div>
                                                    </#list>
                                                </div>
                                                </#if>
                                            </div>
                                        </div>
                                        </#list>
                                    </div>
                                    </#if>
                                    
                                    <div class="card-contain">
                                        <#if position_courses?? && position_courses?size gt 0>
                                        <#list position_courses as item>
                                        <div class="card">
                                            <a class="card-header" href="${item.contentUrl}">
                                                <img src="${item.photoUrl}" class="card-img" alt="">
                                                <#if item.duration && item.duration gt 0>
                                                    <div class="time">${item.duration}分钟</div>
                                                </#if>
                                            </a>
                                
                                        <div class="card-bottom">
                                            <a class="card-title" href="${item.contentUrl}">
                                                <span class="tag">${item.moduleName}</span>
                                                ${item.contentName}
                                            </a>
                                
                                            <div class="detail">
                                                <#if item.rankType ??>
                                                <div class="rank">
                                                    <a href="${item.rankLink}" class="rank-type">${item.rankType}</a>
                                                    <a href="${item.rankLink}" class="rank-detail">${item.rankTitle}</a>
                                                  </div>
                                                <#else>
                                                    <#if item.playTotalCount == 0>
                                                    <div class="view-count">
                                                        潜力新课
                                                    </div>
                                                    <#else>
                                                    <div class="view-count">
                                                        ${item.playTotalCount}人观看
                                                    </div>
                                                    </#if>
                                                </#if>
                                            </div>
                                
                                            <div class="labels">
                                                <#list item.labels as tag>
                                                    <div class="label">${tag}</div>
                                                </#list>
                                            </div>
                                        </div>
                                        </div>
                                        </#list>
                                        </#if>

                                        <#if bg_courses?? && bg_courses?size gt 0>
                                        <#list bg_courses as item>
                                        <div class="card">
                                            <a class="card-header" href="${item.contentUrl}">
                                                <img src="${item.photoUrl}" class="card-img" alt="">
                                                <#if item.duration && item.duration gt 0>
                                                    <div class="time">${item.duration}分钟</div>
                                                </#if>
                                            </a>
                                
                                            <div class="card-bottom">
                                                <a class="card-title" href="${item.contentUrl}">
                                                    <span class="tag">${item.moduleName}</span>
                                                    ${item.contentName}
                                                </a>
                                    
                                                <div class="detail">
                                                    <#if item.rankType ??>
                                                    <div class="rank">
                                                        <a href="${item.rankLink}" class="rank-type">${item.rankType}</a>
                                                        <a href="${item.rankLink}" class="rank-detail">${item.rankTitle}</a>
                                                      </div>
                                                    <#else>
                                                        <#if item.playTotalCount == 0>
                                                        <div class="view-count">
                                                            潜力新课
                                                        </div>
                                                        <#else>
                                                        <div class="view-count">
                                                            ${item.playTotalCount}人观看
                                                        </div>
                                                        </#if>
                                                    </#if>
                                                </div>
                                    
                                                <div class="labels">
                                                    <#list item.labels as tag>
                                                        <div class="label">${tag}</div>
                                                    </#list>
                                                </div>
                                            </div>
                                        </div>
                                        </#list>
                                        </#if>
                                
                                    </div>
                                </div>
                                </#if>

                                <#if banner ??>
                                    <div class="banner">
                                        <a href="${banner.linkUrl}">
                                        <img src="${banner.imageUrl}" alt="">
                                        </a>
                                    </div>
                                    <#if banner.bannerText>
                                    <a class="go-look" href="${banner.linkUrl}">
                                        ${banner.bannerText}
                                        <img src="https://xue.m.tencent.com/mail/recommend/discoverPointer.png" alt="">
                                    </a>
                                    <#else>

                                    <a class="go-look" href="${banner.linkUrl}">
                                        前往查看
                                        <img src="https://xue.m.tencent.com/mail/recommend/discoverPointer.png" alt="">
                                    </a>
                                    </#if>
                                </#if>
                            </div>
                        </div>
                    </div>
                </td>
            </tr>
        </tbody>
    </table>
</body>
</html>