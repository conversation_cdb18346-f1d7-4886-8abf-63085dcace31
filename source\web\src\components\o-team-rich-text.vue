<template>
  <div class="o-team-rich-text">
    <slot name="container">
      <div class="o-team-rich-text-container" ref="defaultContainer"></div>
    </slot>
  </div>
</template>

<script>
/* eslint-disable */
export default {
  name: 'OTeamRichText',
  props: {
    container: {
      type: String,
      default: ''
    },
    textValue: {
      type: String,
      default: () => {
        return JSON.stringify({
          type: 'doc',
          content: [{ type: 'paragraph' }],
          version: 1
        })
      }
    },
    mode: {
      type: String,
      default: 'edit'
    }
  },
  data() {
    return {
      content: '',
      editor: null
    }
  },
  mounted() {
    this.init()
  },
  beforeDestroy() {
    // 组件销毁时清理编辑器实例
    if (this.editor && typeof this.editor.destroy === 'function') {
      this.editor.destroy()
    }
  },
  methods: {
    init() {
      // 检查 TrilobiteEditor 是否存在
      if (typeof TrilobiteEditor === 'undefined') {
        console.error('TrilobiteEditor 未定义，请确保已正确引入相关库')
        return
      }

      try {
        // 确定容器选择器
        const containerSelector = this.container || (this.$slots.container ? '.custom-container' : '.o-team-rich-text-container')
        
        this.editor = new TrilobiteEditor({
          model: this.mode, // edit 编辑模式，preview 预览模式
          container: containerSelector,
          value: this.textValue,
          plugins: [this.selfUpload()]
        })
      } catch (error) {
        console.error('初始化编辑器失败:', error)
      }
    },

    selfUpload() {
      // 检查 ImagePlugin 是否存在
      if (typeof ImagePlugin === 'undefined') {
        console.error('ImagePlugin 未定义，请确保已正确引入相关库')
        return null
      }

      return new ImagePlugin({
        upload: async (file, uploadTask) => {
          try {
            const res = await this.contentCenterHandle(file)
            const { file_url = '' } = res[0]
            return { url: file_url }
          } catch (err) {
            this.$message.error(err)
          }
        }
      })
    },

    async contentCenterHandle(file) {
      // 检查 contentCenter 是否存在
      if (typeof contentCenter === 'undefined' || !contentCenter.uploadFile) {
        throw new Error('contentCenter 未定义或不包含 uploadFile 方法')
      }

      return new Promise((resolve, reject) => {
        const authUrl = location.hostname.endsWith('.woa.com')
          ? process.env.VUE_APP_PORTAL_HOST_WOA
          : process.env.VUE_APP_PORTAL_HOST

        new contentCenter.uploadFile({
          file: file,
          type: 0, // 0表示上传图片，1视频 2音频 3文档
          appId: 'QLearningService',
          operateAuthUrl: `${authUrl}/training-portal-common/api/v1/portal/user/common/uploadOperateSignature`,
          isPublic: true,
          onSuccess(res) {
            resolve(res)
          },
          onError(err) {
            reject(err)
          }
        })
      })
    },

    getContent() {
      return new Promise((resolve, reject) => {
        this.editor.actions.getValue().then(res => {
          resolve(res)
        })
      })
    }
  }
}
</script>

<style lang="less">
.o-team-rich-text {
  .o-team-rich-text-container {
    width: 785px;
    height: 203px;
    border: 1px solid var(---Gray4-, #dcdcdc);
    .css-ts4092 {
      background: #F9F9F9;
      border-bottom: 1px solid var(---Gray4-, #dcdcdc);
      padding: 0;
    }
    .css-q9gimq {
      overflow-y: hidden;
    }
    .css-5t4fot {
      overflow-y: hidden;
    }
    .css-1q3xxv2 {
      overflow-y: scroll;
      .css-1nni6jk {

      }
    }
  }
}
</style>
