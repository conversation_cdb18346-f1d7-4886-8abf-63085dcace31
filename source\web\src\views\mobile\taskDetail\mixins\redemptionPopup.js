import { getSubscription, insertSubscription, moocEnroll } from '@/config/mooc.api.conf.js'
import { Toast } from 'vant'
export default {
  data() {
    return {
      act_type: 102, // 极客时间
      popTextState: 1, // 兑换时弹窗的状态 0：提示：当前任务试学完毕 1：兑换课程 2：兑换名额不足 3：学霸卡不足 4：试学额度已用完
      showRedemptionPopup: false, // 是否显示兑换弹窗
      guideHref: 'https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=37119&jump_from=hdym&project=hdyy&source=jksj' // 指引链接跳转
    }
  },
  computed: {
    isSubscription() { // 是否已经订阅补货通知
      return this.subscriptionInfo && this.subscriptionInfo.sub_status === 1
    },
    popTextMap() { // 兑换弹窗情况汇总
      return {
        0: {
          warm: '提示：当前任务试学完毕',
          title: `如果学习课程中更多任务，请“兑换课程”后继续学习`,
          btn: '兑换课程',
          code: 0
        },
        1: {
          warm: '兑换课程',
          title: `当前课程可任选 <span style="color: #ed7b2f">${this.coursePurchaseInfo.allow_preview_num}</span> 个任务试学<br/>你已试学任务数量：<span style="color: #ed7b2f">${this.coursePurchaseInfo.previewed_num}</span> 个<br />如需学习课程所有任务内容，请兑换课程后学习`,
          btn: '确认兑换',
          code: 1
        },
        2: {
          warm: '兑换名额不足',
          title: `当前课程的可兑换名额为 <span style="color: #e34d59">0</span>，暂不支持兑换，运营团队将会定期补充兑换库存，敬请关注`,
          btn: this.isSubscription ? '已订阅补货通知' : '订阅补货通知',
          code: 2
        },
        3: {
          warm: '学霸卡不足',
          title: `抱歉，你的学霸卡数量不足，无法兑换此课程`,
          btn: '如何获取更多学霸卡？点击查看指引',
          code: 3
        },
        4: {
          warm: '提示：试学额度已用完',
          title: `当前课程可任选 <span style="color: #ed7b2f">${this.coursePurchaseInfo.allow_preview_num}</span> 个任务试学<br/>你已试学任务数量：<span style="color: #ed7b2f">${this.coursePurchaseInfo.previewed_num}</span> 个<br />如需学习课程所有任务内容，请兑换课程后学习`,
          btn: '兑换课程',
          code: 4
        }
      }
    },
    currentPopInfo() {
      return this.popTextMap[this.popTextState]
    }
  },
  methods: {
    // 试学名额不足
    openPop(code) {
      this.popTextState = code
      this.showRedemptionPopup = true
    },
    // 打开兑换弹窗
    openRedemptionPopup() {
      // 判断该显示什么类型的弹窗后显示相应弹窗
      if (!this.coursePurchaseInfo.course_stock_total || this.coursePurchaseInfo.course_stock_total <= 0) {
        this.popTextState = 2
      } else if (!this.coursePurchaseInfo.user_account_num || this.coursePurchaseInfo.user_account_num < this.coursePurchaseInfo.course_val) {
        this.popTextState = 3
      } else {
        this.popTextState = 1
      }
      this.showRedemptionPopup = true
    },
    // 兑换弹窗提交事件 判断弹窗类型 执行相应事件
    userOperator() {
      switch (this.popTextState) {
        case 1:
          // 兑换课程
          this.joinCourse()
          break
        case 2:
          // 兑换名额不足 订阅补货通知
          this.subscribeNotice()
          break
        case 3:
          // 如何获取更多学霸卡？点击查看指引
          this.linkTo(3)
          break
        case 4:
          // 先判断有没有试学额度和学霸卡，弹响应的弹窗，如果没问题就直接兑换课程
          if (!this.coursePurchaseInfo.course_stock_total || this.coursePurchaseInfo.course_stock_total <= 0) {
            this.popTextState = 2 // 兑换额度不足
          } else if (!this.coursePurchaseInfo.user_account_num || this.coursePurchaseInfo.user_account_num < this.coursePurchaseInfo.course_val) {
            this.popTextState = 3 // 学霸卡不足
          } else {
            // 兑换课程
            this.joinCourse()
          }
          break
        default:
          break
      }
    },
    // 课程报名
    joinCourse() {
      const params = {
        mooc_course_id: this.mooc_course_id,
        join_type: '3'
      }
      moocEnroll(params).then((res) => {
        this.closeProp()
        Toast(`兑换课程成功`)
        // 刷新页面 重新获取数据
        this.initData()
        // window.location.reload()
      })
    },
    // 初始化数据
    closeProp() {
      this.showRedemptionPopup = false
      this.showTask = false
      this.showProject = false
    },
    // 订阅补货通知
    subscribeNotice() {
      if (this.isSubscription) { // 已经订阅补货通知的话 点击按钮 return
        return
      }
      // 发请求通知逻辑
      insertSubscription({
        recourse_from: this.courseDetailInfo.recourse_from
      }).then(res => {
        this.closeProp()
        Toast(`订阅补货成功`)
        getSubscription({
          recourse_from: this.courseDetailInfo.recourse_from
        }).then(res => {
          this.subscriptionInfo = res || {}
        })
      })
    },
    // 指引链接跳转
    linkTo(code) {
      let link = 'https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=38684&from_act_id=38684'
      this.courseDetailInfo.recourse_from === 'geekBang' ? link = 'https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=38684&from_act_id=38684' : link = 'https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=38680&from_act_id=38680&share_staff_id=73758&share_staff_name=circlechai'
      window.open(link)
    }
  }
}
