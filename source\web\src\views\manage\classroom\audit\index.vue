<template>
  <div class="agg-list">
    <div class="agg-header">班级报名审批列表</div>
    <el-form :inline="true" :model="form" class="demo-form-inline">
      <el-form-item label>
        <!-- <el-input
          style="width:208px"
          size="small"
          clearable
          v-model="form.student"
          placeholder="请输入学员名称"
        ></el-input>-->
        <sdc-staff-selector
          multiple
          ref="adminsActivityHeadrRef"
          v-model="form.student"
          size="small"
          placeholder="请输入学员名称"
          @change="changeMember"
          selectClass="act-member-selector"
        />
      </el-form-item>
      <el-form-item label>
        <el-input
          style="width:208px"
          size="small"
          clearable
          v-model="form.course_name"
          placeholder="请输入课程名称"
        ></el-input>
      </el-form-item>
      <el-form-item label>
        <el-select
          style="width:256px"
          size="small"
          v-model="form.status"
          placeholder="请选择状态"
          @change="search"
        >
          <el-option label="待审批记录" :value="0"></el-option>
          <el-option label="已审批记录" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" size="small" @click="search">查询</el-button>
      </el-form-item>
    </el-form>

    <div class="batch-operate">
      <el-button type="primary" size="small" @click="batchAgree" :disabled="!selectedRows.length || form.status === 1">批量同意</el-button>
    </div>

    <el-table
      header-row-class-name="table-header-style"
      row-class-name="table-row-style"
      :data="tableData.records"
      style="width: 100%"
      class="agg-table"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column type="index" label="序号" width="60"></el-table-column>
      <el-table-column prop="staff_name" label="姓名"></el-table-column>
      <el-table-column label="状态" width="100" prop="status_name"></el-table-column>
      <el-table-column prop="course_name" label="课程" width="200"></el-table-column>
      <el-table-column prop="reg_time" label="报名时间"></el-table-column>
      <el-table-column prop="start_time" label="开班时间"></el-table-column>
      <el-table-column prop="end_time" label="结束时间"></el-table-column>
      <el-table-column label="地点">
        <template slot-scope="scope">
          <span>{{ scope.row.location ? scope.row.location : '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" v-if="showOperateColumn">
        <template slot-scope="scope">
          <div class="operat-btn-box">
            <el-link type="primary" @click="agree(scope.row)">同意</el-link>
            <el-link type="danger" @click="disagree(scope.row)">拒绝</el-link>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="current"
      :page-sizes="[5, 10, 20, 30, 50, 100]"
      :page-size="size"
      layout="total,  prev, pager, next, sizes, jumper"
      :total="tableData.total"
    ></el-pagination>

    <!-- 拒绝确认对话框 -->
    <el-dialog
      title="拒绝培训申请"
      :visible.sync="rejectDialogVisible"
      width="800px"
      custom-class="reject-dialog"
      @close="handleDialogClose"
    >
      <el-form :model="rejectForm" label-width="100px">
        <el-form-item label="拒绝原因">
          <el-input
            type="textarea"
            v-model="rejectForm.reason"
            placeholder="请输入拒绝原因"
            :rows="4"
          ></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="rejectDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmReject">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getActivityAuditListApi, activityAuditApi } from '@/config/classroom.api.conf.js'
import pager from '@/mixins/pager'
const defaultForm = {
  name: '',
  type: '',
  status: 0
}
export default {
  name: '',
  mixins: [pager],
  data() {
    return {
      form: Object.assign({}, defaultForm),
      tableData: {
        total: 0,
        records: []
      },
      selectedRows: [],
      rejectDialogVisible: false,
      rejectForm: {
        reason: '',
        currentRow: null
      },
      showOperateColumn: true
    }
  },
  computed: {},
  created() {
    this.getApproveList(1)
  },
  methods: {
    changeMember(val) {
      // console.log(val, 'val')
    },
    search() {
      this.showOperateColumn = this.form.status === 0
      this.getApproveList(1)
    },
    // 分页
    onSearch(current) {
      this.getApproveList(current)
    },
    getParams() {
      return {
        status: this.form.status,
        student: this.form.student ? this.form.student.join(';') : '',
        course_name: this.form.course_name
      }
    },
    async getApproveList(current) {
      let searchParams = this.getParams()
      let params = {
        ...searchParams,
        size: this.size,
        current: current
      }
      const data = await getActivityAuditListApi(params)
      this.tableData.records = data.records
      this.tableData.total = data.total
    },
    handleSelectionChange(selection) {
      this.selectedRows = selection
    },
    async agree(row) {
      await this.handleApprove([row.approve_id], 1)
    },
    disagree(row) {
      this.rejectForm.currentRow = row
      this.rejectDialogVisible = true
    },
    async batchAgree() {
      if (this.selectedRows.length === 0) {
        this.$message.warning('请至少选择一条记录')
        return
      }
      const ids = this.selectedRows.map(row => row.approve_id)
      await this.handleApprove(ids, 1)
    },
    async handleApprove(ids, status) {
      if (!ids || ids.length === 0) {
        this.$message.warning('请选择要审批的记录')
        return
      }
      try {
        const params = {
          approve_id: ids.join(';'),
          status: status,
          reason: ''
        }
        if (status === 2 && this.rejectForm.reason) {
          params.reason = this.rejectForm.reason
        }
        await activityAuditApi(params)
        this.$message.success(status === 1 ? '审批通过成功' : '拒绝成功')
        this.getApproveList(this.current)
        if (status === 2) {
          this.rejectDialogVisible = false
          this.rejectForm.reason = ''
        }
      } catch (error) {
        // this.$message.error(status === 1 ? '审批失败' : '拒绝失败')
      }
    },
    async confirmReject() {
      if (!this.rejectForm.reason) {
        this.$message.warning('请输入拒绝原因')
        return
      }
      await this.handleApprove([this.rejectForm.currentRow.approve_id], 2)
    },
    handleDialogClose() {
      this.rejectForm.reason = ''
      this.rejectForm.currentRow = null
    }
  }
}
</script>
<style lang="less">
.act-member-selector {
  width: 208px;
  .selector-container {
    border-right: 1px solid #dcdcdc;
  }
  .suffix-open {
    display: none;
  }
}
</style>
<style lang="less" scoped>
.agg-list {
  padding: 20px;
  background-color: #fff;
  .agg-header {
    margin-bottom: 20px;
    font-size: 18px;
    font-weight: 600;
  }
  .batch-operate {
    margin-bottom: 15px;
  }
  .agg-table {
    border-radius: 4px;
    margin-top: 15px;
    opacity: 1;
    border-top: 1px solid #eeeeeeff;
    border-left: 1px solid #eeeeeeff;
    border-right: 1px solid #eeeeeeff;
    .operat-btn-box {
      .el-link + .el-link {
        margin-left: 10px;
      }
    }
  }
}

:deep(.reject-dialog) {
  border-radius: 3px;
  .el-dialog__header {
    padding: 20px;
    border-bottom: 1px solid #eee;
  }
  .el-dialog__body {
    padding: 30px 20px;
  }
  .el-dialog__footer {
    padding: 20px;
    border-top: 1px solid #eee;
  }
}
</style>
