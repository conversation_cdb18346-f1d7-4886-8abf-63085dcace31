// import SDKUtils from './utils'

// function throttle(func, wait) {
//   let previous = 0;
//   return function() {
//       const context = this;
//       const args = arguments;
//       const now = Date.now();
//       if (now - previous > wait) {
//           func.apply(context, args);
//           previous = now;
//       }
//   };
// }
export default class Timer {
  constructor(params) {
    // 是否开启防挂机功能（默认开启）
    this.antiHangUp = params.antiHangUp || true
    // 防挂机最长时间(s)，默认15分钟
    this.limtHangUpTime = params.limtHangUpTime || 900
    // 注册的回调函数
    this.events = params.events
    // 整个周期定时器持续时间（s）
    this.totalDuration = 0
    // 定时器是否正在计时，用于判断离开页面再次打开时是否自动启动定时器
    this.isTiming = false

    // 定时器
    this.timer = null
    // 防挂机延迟器
    this.hangUpTimer = null

    // this.handleVisibilityEvent = this.handleVisibilityEvent.bind(this)
    // document.addEventListener('visibilitychange', this.handleVisibilityEvent)

    // 点击iframe页面内容也会触发失焦、聚焦
    // window.onblur = () => {
    //   console.log('失去焦点')
    //   if (this.isTiming) {
    //     this.clear()
    //     console.log('失焦暂停 ')
    //   }
    // }
    // window.onfocus = () => {
    //   console.log('聚焦')
    //   if (this.isTiming) {
    //     console.log('聚焦继续开启')
    //     this.start()
    //   }
    // }

    // document.addEventListener('mousedown', this.handleHangUpTimer)
    // document.addEventListener('click', this.handleHangUpTimer)
    // document.addEventListener('scroll', this.handleHangUpTimer)
  }
  openAntiHangUp() {
    // console.log('防挂机开启')
    // 先执行一遍开启防挂机功能
    this.handleHangUpTimer()
    this.handleHangUpTimer = this.handleHangUpTimer.bind(this)
    // 当前页面的鼠标移动事件
    window.addEventListener('mousemove', this.handleHangUpTimer)

    // iframe监听鼠标移动事件通过postMessage再告知当前页面（iframe页面的鼠标移动事件没办法再父页面监听）
    // SDKUtils.mousemoveIframe(() => this.handleHangUpTimer())
  }
  closeAntiHangUp() {
    // console.log('防挂机关闭')
    window.removeEventListener('mousemove', this.handleHangUpTimer)
    clearTimeout(this.hangUpTimer)
    this.hangUpTimer = null
  }
  handleHangUpTimer() {
    // console.log('正在监听鼠标事件')
    if (this.hangUpTimer) clearTimeout(this.hangUpTimer)
    this.hangUpTimer = setTimeout(() => {
      // 暂停定时器
      this.pause()
      // 执行防挂机回调函数，通知页面到了设定时间
      this.events.hangUpTimerCalback(this.limtHangUpTime)
      // console.log('长时间未操作页面：' + this.limtHangUpTime)
    }, this.limtHangUpTime * 1000)
  }
  // handleVisibilityEvent() {
  //   switch (document.visibilityState) {
  //     case 'hidden':
  //       if (this.isTiming) {
  //         this.clear()
  //         // console.log('离开暂停 ')
  //       }
  //       break
  //     case 'visible':
  //       if (this.isTiming) {
  //         // console.log('回到页面继续开启')
  //         this.start()
  //       }
  //       break
  //   }
  // }
  start() {
    this.isTiming = true
    // 当前已开启定时器，避免开启多个
    if (this.timer) return
    if (this.antiHangUp) {
      this.openAntiHangUp()
    }
    // 本轮定时器持续时间，定时器有暂停重新开启，则会清空（用于定时上报学习记录）
    let durtation = 0
    this.timer = setInterval(() => {
      this.totalDuration++
      durtation++
      if (this.events.getDurationSeconds) {
        this.events.getDurationSeconds(this.totalDuration, durtation)
      }
    }, 1000)
  }

  pause() {
    // console.log('pause')
    this.isTiming = false
    this.clear()
  }

  clear() {
    if (!this.timer) return
    // console.log('clear')
    clearInterval(this.timer)
    this.timer = null
    if (this.antiHangUp) {
      this.closeAntiHangUp()
    }
  }
  formatSeconds(time) {
    // 转换为时分秒
    let h = parseInt(time / 60 / 60 % 24) || 0
    h = h < 10 ? '0' + h : h
    let m = parseInt(time / 60 % 60) || 0
    m = m < 10 ? '0' + m : m
    let s = parseInt(time % 60) || 0
    s = s < 10 ? '0' + s : s
    // 作为返回值返回
    return [h, m, s]
  }
  removeEvent() {
    // document.removeEventListener('visibilitychange', this.handleVisibilityEvent)
    this.clear()
  }
}
