const manage = [
  {
    path: '/user',
    name: 'user',
    component: () => import('views/user/index'),
    meta: {
      title: 'ai虚拟人做课工具'
    },
    children: [
      {
        path: '/user/uploadCoverImage',
        name: 'uploadCoverImage',
        component: () => import('views/user/uploadCoverImage/index.vue'),
        meta: {
          title: '封面图上传'
          // breadcrumb: ['推荐邮件', '活动列表']
        }
      },
      {
        path: '/user/memberManagement',
        name: 'memberManagement',
        component: () => import('views/user/memberManagement/index.vue'),
        meta: {
          title: '学员管理'
        }
      },
      {
        path: '/user/memberTest',
        name: 'memberManagement',
        component: () => import('views/user/memberManagement/memberTest/test.vue'),
        meta: {
          title: '学员管理测试页'
        }
      },
      {
        path: '/user/memberTest2',
        name: 'memberManagement',
        component: () => import('views/user/memberManagement/memberTest/test2.vue'),
        meta: {
          title: '学员管理测试页2'
        }
      }
    ]
  }
]

export default manage
