<template>
  <div class="lesson-manage">
    <div class="top-title">
      <span>配置列表</span>
    </div>
    <div class="content-top-wrap">
      <div class="content-top">
        <el-form ref="form" :model="searchData" inline label-width="80px">
          <div style="display: flex;">
            <el-form-item label="栏目名">
              <el-input
                placeholder="请输入栏目名"
                class="w-280 mr-16"
                v-model="searchData.rec_name"
                size="small"
              />
            </el-form-item>
            <el-form-item label="推广日期">
              <el-date-picker
                v-model="searchData.rec_time"
                type="date"
                value-format="yyyy-MM-dd"
                range-separator="至"
                placeholder="请选择推广日期"
                size="small"
                >
              </el-date-picker>
            </el-form-item>
            <div style="margin: 0 0 0 auto">
              <el-form-item>
                <el-button @click="handleReset" size="small" style="color: #0052D9;border: 1px solid #0052D9;">
                  <i class="el-icon-refresh"></i><span>重置</span>
                </el-button>
                <el-button type="primary" @click="onSearch()" size="small" style="width: 72px">搜索</el-button>
              </el-form-item>
            </div>
          </div>
        </el-form>
      </div>
    </div>

    <div class="content-main">

      <div class="table-top">
        <div class="left">
          <el-button type="primary" @click="handleAdd()" size="small">
            <span>添加</span>
          </el-button>
        </div>
      </div>

      <el-table
        ref="table"
        :data="tableParams.list"
        header-row-class-name="table-header-style"
        row-class-name="table-row-style"
        @sort-change="tableSortChange"
        @selection-change="handleSelectionChange"
        :default-sort = "{ prop: 'rec_time', order: 'descending' }"
      >
      <el-table-column
        label="序号"
        type="index"
        width="88">
      </el-table-column>
        <el-table-column
          v-for="item of tableCols"
          :key="item.prop"
          :prop="item.prop"
          :label="item.label"
          :width="item.width ? item.width : 'auto'"
          :sortable="item.sortable ? item.sortable : false"
          style="overflow: hidden"
          show-overflow-tooltip
          align="left"
        >
          <template slot-scope="{ row }">
            <span>{{ row[item.prop] ? row[item.prop] : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="推广日期"
          sortable="custom"
          prop="rec_time"
          width="402">
          <template slot-scope="{ row }">
            <span :style="row.isRecommend ? 'color: #00000066' : 'color: #000000'">{{ row.isRecommend ? `${row.rec_time}推送，已失效` : row.rec_time }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="250">
          <template slot-scope="{ row }">
            <el-button type="text" @click="handleConfigEdit(row)">编辑</el-button>
            <el-button type="text" @click="handleCourseManager(row)" v-if="!row.isRecommend">内容管理</el-button>
            <el-button type="text" @click="handleCheckData(row)" v-else>推送情况</el-button>
            <el-button type="text" @click="handleDelete(row)" class="color-red" >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="table-pagination">
        <el-pagination
          :hide-on-single-page="tableParams.list.length === 0"
          @current-change="pageNumChange"
          :current-page="searchData.pageNum"
          :page-size="searchData.pageSize"
          layout="total, prev, pager, next, jumper"
          :total="searchData.total"
        >
        </el-pagination>
      </div>
    </div>

    <!-- 添加或编辑推广位 -->
    <recommendDialog ref="recommendConfigDialogRef" :visible.sync="recommendDialogVisible" @onSearch="onSearch"></recommendDialog>

    <!-- 推广位的详情 -->
    <recommendDetail ref="courseListDialogRef" :visible.sync="recommendDetailVisible" :moduleInfo="moduleInfo"></recommendDetail>
  </div>
</template>

<script>
import recommendDialog from './components/recommendDialog.vue'
import recommendDetail from './components/courseList.vue'
import { getNetCoursePersonList, delNetCoursePerson, getModuleInfoApi } from '@/config/mooc.api.conf.js'
export default {
  components: { recommendDetail, recommendDialog },
  data () {
    return {
      searchData: {
        rec_name: '',
        rec_time: '',
        order_by: 'rec_time;desc', // updated_at - 更新时间，created_at  - 创建时间
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      tableCols: [
        // { label: '序号', prop: 'order_no', sortable: 'custom', width: '120' },
        { label: '栏目', prop: 'rec_name', width: '268' }
        // { label: '推广日期', prop: 'rec_time', width: '402', sortable: 'custom' }
        // { label: '创建时间', prop: 'created_at', sortable: 'custom' },
        // { label: '修改时间', prop: 'updated_at', sortable: 'custom' }
      ],
      multipleChoice: 0,
      tableParams: {
        list: []
      },
      recommendDialogVisible: false,
      recommendDetailVisible: false,
      moduleInfo: []
    }
  },
  created () {
    this.getList()
    this.getModuleInfo()
  },
  methods: {
    // 内容管理 ----
    handleCourseManager(row) {
      this.recommendDetailVisible = true
      this.$refs.courseListDialogRef.init({ ...row, mode: 'edit' })
    },
    pageNumChange(current) {
      this.onSearch(current)
    },
    handleCheckData(row) {
      this.recommendDetailVisible = true
      this.$refs.courseListDialogRef.init({ ...row, mode: 'check' })
    },
    // 获取编辑的选项
    getModuleInfo() {
      getModuleInfoApi().then(res => {
        let moduleArr = [] 
        let result = []
        // 返回的moduleInfo是个对象，需要转换成数组
        for (let key of Object.keys(res.moduleInfo)) {
          // 0是综合，综合不需要作为选项，过滤掉
          if (key === '0') continue
          moduleArr.push(res.moduleInfo[key])
        }
        for (let item of moduleArr) {
          if (item.module_id !== 99) {
            result.push(item)
          }
        }
        this.moduleInfo = result
      })
    },
    handleAdd () {
      this.recommendDialogVisible = true
      this.$refs.recommendConfigDialogRef.initData({ moduleName: 'add' })
    },
    onSearch(currentPage = 1) {
      this.searchData.pageNum = currentPage
      this.getList()
    },
    async getList () {
      const { pageNum, pageSize } = this.searchData
      let params = {
        rec_time: this.searchData.rec_time || '',
        rec_name: this.searchData.rec_name || '',
        order_by: this.searchData.order_by,
        page_no: pageNum,
        page_size: pageSize
      }
      await getNetCoursePersonList(params).then(res => {
        this.tableParams.list = res.records || []
        let now = new Date()
        this.tableParams.list.forEach(item => {
          let rec_time = new Date(item.rec_time)
          if (now.getTime() > rec_time.getTime()) {
            item.isRecommend = true
          } else {
            item.isRecommend = false
          }
        })
        this.searchData.total = res.total
      })
    },
    handleReset() {
      this.searchData = {
        creator_name: '',
        rec_name: '',
        rec_time: '',
        start_time: '',
        end_time: '',
        order_by: 'rec_time;desc',
        pageSize: 10,
        pageNum: 1
      }
      this.$refs.table.sort('rec_time', 'descending')
      // this.$refs.table.clearSort()
      this.onSearch()
    },
    tableSortChange({ prop, order }) {
      let sort = prop
      if (order === 'ascending') {
        sort += ';asc'
      } else if (order === 'descending') {
        sort += ';desc'
      } else {
        sort = ''
      }
      this.searchData.order_by = sort
      this.getList()
    },
    // 批量多选
    handleSelectionChange(val) {
      this.multipleChoice = val
    },
    // 批量删除
    handleBatchDelete() {
      this.handleDelete(this.multipleChoice, true)
    },
    handleDelete(item, batch = false) {
      let ids = ''
      // let names = ''
      let that = this
      const h = this.$createElement
      let html = [
        h('span', { style: 'color: #00000099;' }, '请确认是否删除此项？ ')
      ]

      if (batch) {
        item.forEach((v, i) => {
          ids += v.id + ','
          // if (i < 6) {
          //   names += `${v.banner_name}、`
          // }
        })
        // names = names.substring(0, names.length - 1)
        // if (item.length > 6) names += '...'
        ids = ids.substring(0, ids.length - 1)

        // html.push(h('span', { style: 'color: #000000e6;' }, names))
        html.push(h('span', { style: 'color: #000000e6;' }))
        html.push(h('span', { style: 'color: #000000e6; display: inline; margin-left: 4px;' }, '共'))
        html.push(h('i', { style: 'color: #0052d9; font-weight: 500; font: icon; display: inline; margin: 0 4px;' }, item.length))
        html.push(h('span', { style: 'color: #000000e6;' }, '项推广？'))
      } else {
        ids = item.id
        // names = item.banner_name
        // html.push(h('span', { style: 'color: #000000e6;' }, names))
        html.push(h('span', { style: 'color: #000000e6;' }))
      }
      console.log(ids, 'ids')
      
      this.$msgbox({
        title: '提示',
        closeOnClickModal: false,
        message: h('p', null, html),
        showCancelButton: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        beforeClose: (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '执行中...'
            delNetCoursePerson(ids).then((res) => {
              instance.confirmButtonLoading = false
              that.$message.success('删除成功')
              that.getList()
              done()
            })
          } else {
            done()
          }
        }
      })
    },
    // 编辑
    handleConfigEdit(row) {
      this.recommendDialogVisible = true
      this.$refs.recommendConfigDialogRef.initData({ moduleName: 'edit', ...row })
    },
    handleLink (row) {
      try {
        window.open(row.link_url)
      } catch (error) {
        this.$message.error('跳转地址错误')
      }
    }
  }
}
</script>

<style lang="less" scoped>
.color-red {
  color: #e34d59 !important;
}
.w-280 {
  width: 280px;
}
.mr-16 { 
  margin-right: 16px;
}
.lesson-manage {
  padding: 4px 0;
  height: 100%;
  background: #fff;
  border-radius: 4px;
  opacity: 1;
  box-shadow: 0 0 8px 0 #eeeeeeff;
  .top-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f3f3f3ff;
    padding: 16px 20px;
    span {
      color: #000000e6;
      font-size: 24px;
      font-weight: 600;
    }
  }
  .content-top-wrap {
    margin-bottom: 10px;
    padding: 20px 20px 0;
  }
  .content-top {
    border-radius: 4px;
    background: #FAFAFA;
    padding: 20px 20px 0 20px;
    display: flex;
    flex-direction: column;
    .j-end {
      display: flex;
      justify-content: flex-end;
      & /deep/ .el-button {
        min-width: 80px;
      }
    }
    .el-form /deep/ .el-form-item {
      margin-bottom: 16px;
    }
    .el-form--inline /deep/ .el-form-item__label {
      color: #00000099;
    }
  }
  .content-main {
    padding: 0 20px;
    .table-top {
      padding: 10px 10px 10px 0;
    }
    & /deep/ .el-table {
      border: 1px solid #eee;
      .el-table__empty-block {
        width: 100% !important;
      }
      .table-header-style th {
        padding: 6px 0;
        color: #00000099;
        font-family: "PingFang SC";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 14px;
      }
      .table-row-style {
        &:first-child {
          .cell {
            // padding: 0 0 0 20px;
          }
        }
      }
    }
    /deep/ .el-button.el-button--text {
      padding: 0;
    }
    /deep/ .el-button--text:hover, /deep/ .el-button--text:focus {
      color: #0052D9 !important;
    }
  }
}
.table-top {
  display: flex;
  justify-content: space-between;
  .left {
    display: flex;
    text-align: center;
    .select-num {
      margin-left: 16px;
      color: #666666;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      span {
        color: #0052d9;
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
      }
    }
  }
}
</style>
