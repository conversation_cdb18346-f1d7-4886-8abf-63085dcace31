<template>
  <div class="graphic-page">
    <convention-banner class="convention-banner" />
    <div class="platform" v-if="isSupperAdmin">
      <div class="platform-main">
        <el-form>
          <el-form-item label="文章类型" class="publish-platform-item is-required" style="margin-bottom: 0;">
            <el-radio-group v-model="formData.course_statement.publish_platform.type" :disabled="approveStatus">
              <el-radio label="QL">Q-Learning</el-radio>
              <el-radio label="KM" :disabled="true">KM</el-radio>
            </el-radio-group>
            <div class="magl-20" v-show="formData.course_statement.publish_platform.type === 'KM'">选择K吧：
              <el-select v-model="formData.course_statement.publish_platform.KMVal" :disabled="approveStatus" placeholder="请选择K吧" clearable>
                <el-option
                  v-for="item in KM_type_options"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="contain-main">
      <div
        :class="[
          editorConfig.is_open_catalogue ? '' : 'showEditCatalog',
          'left'
        ]"
      >
        <div class="editor-header">
          <input
            class="title"
            v-model="formData.graphic_name"
            maxlength="50"
            placeholder="请输入标题"
            :disabled="approveStatus"
          />
          <el-input
            class="desc"
            v-model="formData.graphic_desc"
            type="textarea"
            rows="2"
            maxlength="200"
            resize="none"
            placeholder="请输入简介/导语（可选填）"
            :disabled="approveStatus"
          ></el-input>
        </div>
        <div class="editor-body">
          <div v-show="approveStatus" class="sdc-preview">
            <sdc-mce-preview
              ref="preview"
              :urlConfig="editorConfig.urlConfig"
              :catalogue.sync="editorConfig.catalogue"
              :content="formData.graphic_text"
            >
            </sdc-mce-preview>
          </div>
          <sdc-mce-editor v-show="!approveStatus"
            ref="editor"
            :env="editorEnv"
            :content="formData.graphic_text"
            :key="count"
            :catalogue.sync="editorConfig.catalogue"
            :urlConfig="editorConfig.urlConfig"
            :options="editorConfig.options"
            @input="getRichTextLength"
            @catalogueVisible.self="catalogueVisible"
            :insertItems="insertItems"
          />
          <img :src="imgCodeSrc" alt="" style="width: 200px" />
        </div>
      </div>
      <div class="right">
        <PropertySet
          :barkInfo="barkInfo"
          :visitorCheck.sync="visitorCheckFlag"
          :formData.sync="formData"
          ref="propertySet"
          @handStafSelector="handStafSelector"
          @handleImg="handleImg"
          @filesChanged="filesChanged"
          @labelChanged="labelChanged"
          :approveStatus="approveStatus"
          @imgChange="imgChange"
          @vaildAfterCode="vaildAfterCode"
        />
      </div>
    </div>
    <div class="contain-footer">
      <div class="directly-inner" v-if="approveStatus">
        <el-button :loading="approveLoading" @click="handleRefuseShow" :disabled="formData.graphic_status !== 6">拒绝</el-button>
        <el-button :loading="approveLoading" type="primary" @click="handleApprove(1)" :disabled="formData.graphic_status !== 6">审核通过</el-button>
      </div>
      <div class="convention-footer" v-else>
        <convention-confirm v-model="isChooseConvention" style="text-align: right;margin-bottom: 16px;" v-if="[2].includes(formData.info_sec_status) || [1, 3, 4, null, undefined, ''].includes(formData.graphic_status)" />
        <div class="convention-footer-inner">
          <div class="left-tip">
            <span>当前字数：{{ formData.graphic_number }}</span>
            <span>预计所需阅读时间{{ forecastReadTime }}分钟</span>
            <span v-if="formData.graphic_status == 4 && !showSaveLoading"
              >每5分钟自动保存草稿，上次保存时间：{{
                formData.update_at || formData.creator_at || '--'
              }}</span
            >
            <span
              v-if="formData.graphic_status == 4 && showSaveLoading"
              class="save-loading"
              >自动保存中<i></i
            ></span>
          </div>
          <div class="btn-groups">
            <el-button @click="cancel">取消</el-button>
            <el-button type="primary" :disabled="btnDisabled" @click="handleEvent(1)">存草稿</el-button>
            <!-- 3为停用; 4为草稿; null, undefined, ''为新建 -->
            <template v-if="[3, 4, null, undefined, ''].includes(formData.graphic_status)">
              <el-tooltip effect="dark" content="请先存草稿~" placement="right-start" v-if="formData.graphic_status === 3">
                <el-button class="btn-long" disabled type="primary" @click="handleEvent(4)">提交上架审核</el-button>
              </el-tooltip>
              <el-button v-else class="btn-long" type="primary" @click="handleEvent(4)" :disabled="!isChooseConvention">提交上架审核</el-button>
            </template>
            <!-- 1为在用 -->
            <el-button class="btn-long" v-else-if="[2].includes(formData.info_sec_status) || (formData.graphic_status === 1 && isFilesChange)" :disabled="!isChooseConvention" type="primary" @click="approveSafe(4)">再次提交审核</el-button>
            <el-button v-else :disabled="formData.graphic_status === 6 || !isChooseConvention || [0].includes(formData.info_sec_status)" type="primary" @click="approveSafe(3)">发布上架</el-button>
            <!-- <el-button @click="handleEvent(3)" type="primary">提交</el-button> -->
            <el-button @click="handleEvent(2)" type="primary">预览</el-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 审核拒绝 -->
    <refuseDialog :refuseShow.sync="refuseShow" @refuseConfirm="handleApprove"></refuseDialog>
    <!-- 信息审核再次编辑异步变化弹窗 -->
    <informationSafetyDialog :isShow.sync="informationSafetyShow" @safeConfirm="handleEvent(informationSafetyValue)"/>
  </div>
</template>

<script>
import PropertySet from './propertySet'
import RefuseDialog from '../netcourse/course-make/components/refuseDialog.vue'
import conventionBanner from '@/views/components/convention-banner.vue'
import conventionConfirm from '@/views/components/convention-confirm.vue'
import {
  visitorCheck,
  saveDraft,
  addGraphic,
  modifyGraphic,
  addClassList,
  getGraphicDetails,
  getApproveDetailApi,
  approveStatus
} from 'config/api.conf'
import informationSafetyDialog from '@/components/information-safety-dialog'
import { qlearningModuleTypes } from 'utils/constant'
import { mapState } from 'vuex'
let allTarget = 2015587
export default {
  props: {
    approveStatus: { // 审批状态
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      refuseShow: false, // 显示审核不通过弹窗
      approveLoading: false,
      KM_type_options: [], // KM下拉选项
      insertItems: [
        'link',
        'table',
        'image',
        'profile',
        'embed',
        'codesample',
        'separator',
        'quote',
        'horizontal-rule'
      ],
      editorConfig: {
        catalogue: false,
        options: {
          // 非必传字段
          // selector: '#graphic__editor'
          selector: '#sdc__editor',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'catalogue',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat | 
            catalogueBtn formatselect fontsizeselect |
            bold italic underline strikethrough |
            forecolor backcolor |
            dent align bullist numlist |
            insert code fullScreenButton`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/contentinfo`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      },
      barkInfo: {}, // 备份的图文数据
      formData: {
        graphic_type: 1,
        graphic_name: '',
        graphic_desc: '',
        graphic_text: '',
        graphic_status: 4, // 4草稿，1发布(保存草稿状态时不校验) 6.审核 3.停用
        is_open_catalogue: 1, // 0开启 1不开启
        graphic_number: 0,
        content_type: 0, // 属性设置字段
        from_url: '',
        classify_id: [],
        classify_full_name: [],
        label_list: [],
        is_self_upload: 0, // 0：自己上传 1：智能生成
        cover_image_id: '', // 图片id
        is_open: 1, // allTarget全部 1部分
        target_list: allTarget, // 目标学员
        authors: [],
        authors_list: [],
        administrators: [],
        cl_id: '', // 选中的课单id
        extend_contents: [],
        relation_content: null,
        new_graphic_text: '',
        dept_id: '', // 内容管理组织id
        dept_name: '', // 内容管理组织名
        is_show_recommend: true, // 展示课程推荐
        ai_sync_flag: 0, // 是否同步给小Q同学 0-不同步 1-同步
        ai_expire_type: 1, // 数据有效时间 1-长期 2-自定义到期时间
        ai_expire_end_time: '', // 数据有效结束时间
        graphic_remark: '', // 备注
        course_statement: { // 改版新增字段
          operation_title: '', // 运营标题
          parent_content_act_type: null, // 父内容类型
          parent_content_id: '', // 父内容ID
          creation_source: 0, // 创作来源
          pgc_creation_org: [], // PGC创作组织
          pgc_joint_creation: [], // PGC联合创作组织
          pugc_creation_org: [], // PUGC创作组织
          pugc_joint_creation: [], // PUGC联合创作组织
          ogc_supplier_name: '', // 供应商名称
          ogc_purchase_org: [], // 采购组织
          ogc_out_teachers: '', // 外部讲师
          ogc_purchase_type: null, // 采购方式
          ogc_purchase_amount: undefined, // 采购成本
          human_cost: undefined, // 人力成本
          is_required: false, // 是否纳入应学
          certification_level: 0, // 认证等级
          operation_level: 3, // 运营分级
          operation_project_name: '', // 分级项目
          expert_score: null, // 内容专家评分
          user_score: 0, // 内容用户评分
          join_recommend: true, // 是否加入推荐流
          publish_platform: { // 发布平台
            type: 'QL', // 平台： QL、KM
            KMVal: '' // KM的值
          }
        }
      },
      isChooseConvention: false,
      copyParam: {
        target_list: '',
        authors: '',
        administrators: '',
        graphic_status: 4
      },
      forecastReadTime: 0,
      saveDraftTimer: null,
      count: 0,
      flag: true,
      showSaveLoading: false,
      visitorCheckFlag: null,
      editorEnv: process.env.NODE_ENV,
      imgCodeSrc: '',
      cdnImg: '',
      otherPropChanged: false, // 其他属性是否发生编辑改变 封面、标签
      informationSafetyValue: '',
      informationSafetyShow: false
    }
  },
  components: {
    PropertySet,
    RefuseDialog,
    conventionBanner,
    conventionConfirm,
    informationSafetyDialog
  },
  computed: {
    ...mapState(['userLimitInfo']),
    graphic_id() {
      return (
        this.formData.graphic_id ||
        this.$route.query.graphic_id ||
        sessionStorage.getItem('graphic_id')
      )
    },
    // 是否是 超管、公司级管理员、BG级、部门级、管理员
    isSupperAdmin() {
      let { supper_admin, mooc_company_admin, mooc_bgadmin, mooc_dept_admin, admin } = this.userLimitInfo
      return supper_admin || mooc_company_admin || mooc_bgadmin || mooc_dept_admin || admin
    },
    // 标题
    isTitleChanged() {
      return !!(this.formData.graphic_name !== this.barkInfo.graphic_name)
    },
    // 简介/导语
    isDescChanged() {
      return !!(this.formData.graphic_desc !== this.barkInfo.graphic_desc)
    },
    // 正文
    isTextChanged() {
      return !!(this.$refs['editor'].getContent() !== this.barkInfo.graphic_text)
    },
    // 标题、简介、标签、封面 任何一个字段是否发生改变
    isFilesChange() {
      const id = this.$route.query.graphic_id
      return !!(id && (this.otherPropChanged || this.isTitleChanged))
    },
    btnDisabled() {
      // 在用，停用禁止点击草稿
      return Boolean([1, 6].includes(this.formData.graphic_status))
    },
    isSafeInfoChange() {
      const id = this.$route.query.graphic_id
      return id && (this.isDescChanged || this.isTitleChanged || this.isTextChanged || this.safeInfoChange)
    }
  },
  provide() {
    return {
      handleCreateGraghic: this.handleSpecialCreateGraphic
    }
  },
  mounted() {
    if (!this.approveStatus && this.formData.graphic_status === 4) {
      this.createSaveDraftTimer()
    }
    const { from, type, module_id, id, name, from_type } = this.$route.query
    if (from === 'ql') {
      this.formData.content_type = type || 0
      const module_id2 = parseInt(module_id || 0)
      let moduleObj = qlearningModuleTypes.find(
        (item) => module_id2 === item.module_id
      )
      if (moduleObj) {
        if (!(module_id2 === 6 && from_type)) {
          this.formData.relation_content = {
            content_item_id: id || '',
            content_name: name,
            content_module_id: module_id2,
            content_module_name: moduleObj.module_name || '',
            href: `${moduleObj.urlPre + id}`
          }
          this.$refs.propertySet.initData(this.formData)
        }
      }
    }
    // 获取图文详情
    if (this.approveStatus) {
      this.getDetailInfo()
    }
  },
  methods: {
    imgChange(value) {
      this.safeInfoChange = value
    },
    handleRefuseShow() {
      this.refuseShow = true
    },
    // 管理后台审核
    handleApprove(approve_result, review_failed_reason) {
      this.approveLoading = true
      const { graphic_id } = this.$route.query
      const params = {
        act_type: 18,
        course_id: graphic_id,
        approve_result,
        is_mobile: 1
      }
      if (approve_result === 2) {
        params.review_failed_reason = review_failed_reason
      }
      approveStatus(params).then((res) => {
        this.approveLoading = false
        const msg = approve_result === 2 ? '审核拒绝' : '审核通过'
        this.formData.graphic_status = approve_result === 2 ? '8' : '7'
        this.$message.success(msg)
        this.refuseShow = false
        try {
          window.opener.workReConnect()
        } catch (error) {
          console.error('error: ', error)
        }
        setTimeout(() => {
          window.close()
        }, 200)
      })
    },
    // 封面图和标签发生了改变
    filesChanged(val) {
      this.otherPropChanged = val
    },
    fixText(text, reg) {
      let imgsrc, imgTag, newText
      if (text.match(reg)) {
        imgsrc = text.match(reg)[1] || ''
        imgTag = text.match(reg)[0] || ''
        newText = text.replace(reg, '') || ''
      }
      return { newText, imgsrc, imgTag }
    },
    getRichTextLength(content) {
      const textContent = content.replace(/<[^>]+>/g, '')

      // 替换&nbsp;为空格
      const decodedText = textContent.replace(/&nbsp;/g, ' ')

      // 删除空格
      const trimmedText = decodedText.replace(/\s+/g, '')

      // 计算字数
      const wordCount = trimmedText.length

      this.formData.graphic_number = wordCount
      this.forecastReadTime =
        wordCount > 0
          ? wordCount % 600 === 0
            ? wordCount / 600
            : (Math.ceil((wordCount * 10) / 600) / 10).toFixed(1)
          : 0
      this.formData.new_graphic_text = content
    },
    handleSpecialCreateGraphic() {
      // this.$refs.propertySet.$refs.extandTable.initData()
    },
    createSaveDraftTimer() {
      let _this = this
      this.saveDraftTimer = setInterval(() => {
        _this.createGraphic()
      }, 300000) // 300000
    },
    // 信息安全审核
    approveSafe(value) {
      // 4-再次提交审核
      // 3-发布上架
      this.informationSafetyValue = value
      if (this.isSafeInfoChange) {
        this.informationSafetyShow = true
        return
      }
      this.handleEvent(value)
    },
    createGraphic(data) {
      const { graphic_status } = this.copyParam
      const commonAPI = graphic_status === 4 ? saveDraft : this.$route.query.graphic_id || this.graphic_id ? modifyGraphic : addGraphic
      if (commonAPI === saveDraft) this.showSaveLoading = true

      let newParams = {}
      if (data) {
        // 需要进入弹窗判断
        newParams = data
      } else {
        let backArg = this.beforeCheckParams()
        newParams = backArg.newParams
        if (!backArg.isPass) return
      }
      newParams.graphic.is_open = newParams.graphic.is_open === 2 ? 2 : 1
      return new Promise((resolve, reject) => {
        commonAPI(newParams).then((data) => {
          console.log('接口成功', data)
          const returnGraphicId = commonAPI === saveDraft ? data : data.data
          this.formData.graphic_id = returnGraphicId
          sessionStorage.setItem('graphic_id', returnGraphicId)
          // 非草稿状态需手动调用添加到课单接口
          // if (graphic_status === 1 && this.formData?.cl_id?.length > 0) {
          if ([6, 1].includes(graphic_status) && this.formData?.cl_id?.length > 0) {
            this.doAddToClassList(returnGraphicId)
          }
          // if (
          //   commonAPI === addGraphic ||
          //   (graphic_status === 1 && commonAPI === modifyGraphic)
          // ) {
          if (commonAPI === addGraphic || commonAPI === modifyGraphic) {
            this.formData.graphic_status = graphic_status // 页面缓存发布状态提交成功按钮状态同步
            if (this.saveDraftTimer) {
              clearInterval(this.saveDraftTimer)
            }
            if (data.credit && data.credit !== '0') { this.$message.success(`提交成功，通用积分+${data.credit}`) } else this.$message.success('提交成功')
            if (graphic_status !== 6) {
              this.$router.push({
                name: 'preview',
                query: {
                  graphic_id: returnGraphicId
                }
              })
            }
            sessionStorage.clear()
          } else {
            this.showSaveLoading = false
            this.getGraphicViewInfo(returnGraphicId)
          }
          resolve(this.formData.graphic_id)
        }).catch((e) => {
          console.log('接口失败', e)
          if (!e.success) {
            // 直接点提交之后存草稿按钮不展示
            // this.copyParam.graphic_status =
            //   commonAPI !== saveDraft &&
            //   this.formData.graphic_status === 4 &&
            //   this.copyParam.graphic_status === 1
            //     ? 4
            //     : 1
            // 保存失败时（非草稿），copyParam.graphic_status的状态值还原
            if (commonAPI !== saveDraft && this.copyParam.graphic_status === 6) {
              if (this.formData.graphic_status === 4) {
                this.copyParam.graphic_status = 4
              } else if (this.formData.graphic_status === 1) {
                this.copyParam.graphic_status = 1
              }
            }
            reject(e)
          }
        })
      })
    },
    // 判断是否需要显示修改关键字段的弹窗 (创作来源、创作组织、联合创作组织、管理组织、采购组织、是否加入推荐池、是否必修)
    checkIsShowModifyKeyField(code) {
      let { newParams, isPass } = this.beforeCheckParams()
      if (!isPass) return
      this.$refs.propertySet && (this.$refs.propertySet.showSpecialDialogFn(newParams, code))
    },
    vaildAfterCode({ newParams, code }) {
      if (code === 3) {
        this.createGraphic(newParams)
      } else if (code === 4) {
        this.createGraphic(newParams).then((e) => {
          if (e.status_code === 1) return // 审核不通过
          if (e?.success === false) {
            this.$message.warning(e.message)
            return
          }
          setTimeout(() => {
            this.$router.push({ name: 'graphic-page' })
          }, 1000)
        })
      }
    },
    // 校验之前处理数据
    beforeCheckParams() {
      this.formData.graphic_text = this.$refs['editor'].getContent()
      const {
        graphic_name,
        graphic_desc,
        graphic_text,
        is_open_catalogue,
        graphic_number,
        content_type,
        from_url,
        is_self_upload,
        cover_image_id,
        is_open,
        cl_id,
        relation_content,
        classify_id,
        label_list,
        target_list,
        course_statement,
        dept_id,
        dept_name,
        is_show_recommend,
        graphic_remark,
        ai_sync_flag,
        ai_expire_type
      } = this.formData
      let { ai_expire_end_time } = this.formData
      const { graphic_status, authors, administrators } = this.copyParam
      // 未选择同步小Q同学 时间未空
      if (ai_sync_flag === 0 || ai_expire_type === 1) {
        ai_expire_end_time = ''
      }
      const params = {
        graphic_type: 1,
        graphic_name,
        graphic_desc,
        graphic_text,
        graphic_status,
        is_open_catalogue,
        graphic_number,
        content_type: parseInt(content_type),
        from_url,
        classify_id: classify_id && classify_id.length > 0 ? classify_id.join() : '',
        label_list: label_list || '',
        is_self_upload,
        cover_image_id,
        is_open,
        target_list: target_list,
        authors: authors,
        administrators: administrators,
        cl_id,
        dept_id,
        dept_name,
        is_show_recommend,
        graphic_remark,
        course_statement: JSON.parse(JSON.stringify(course_statement)),
        ai_sync_flag,
        ai_expire_type,
        ai_expire_end_time
      }
      if (this.graphic_id && this.graphic_id !== 'undefine') {
        params.graphic_id = parseInt(this.graphic_id)
      }

      // 处理新增字段 需要JSON.stringify处理的数据 回显时用
      // 发布平台字段
      if (params.course_statement.publish_platform) {
        params.course_statement.publish_platform = JSON.stringify(params.course_statement.publish_platform)
      }

      // 处理新增的组织字段
      const resolveArray = ['pgc_creation_org', 'pgc_joint_creation', 'pugc_creation_org', 'pugc_joint_creation', 'ogc_purchase_org']
      resolveArray.forEach(item => {
        if (params.course_statement[item] === null) {
          params.course_statement[item] = JSON.stringify([])
        } else {
          params.course_statement[item] = JSON.stringify(params.course_statement[item])
        }
      })
      // OGC-外部讲师
      if (params.course_statement['ogc_out_teachers']) {
        let strArray = params.course_statement['ogc_out_teachers'].split(';') ? params.course_statement['ogc_out_teachers'].split(';') : []
        strArray = strArray.map(item => {
          return { StaffName: item }
        })
        params.course_statement['ogc_out_teachers'] = JSON.stringify(strArray)
      } else {
        params.course_statement['ogc_out_teachers'] = JSON.stringify([])
      }
      // OGC-采购成本
      if ([undefined, '', 0].includes(params.course_statement.ogc_purchase_amount)) {
        params.course_statement.ogc_purchase_amount = null
      }
      // 人力成本
      if ([undefined, '', 0].includes(params.course_statement.human_cost)) {
        params.course_statement.human_cost = null
      }
      // 内容的专家评分
      params.course_statement.expert_score === '' && (params.course_statement.expert_score = null)
      if (typeof params.course_statement.expert_score === 'string') {
        params.course_statement.expert_score = +params.course_statement.expert_score
      }
      // 创作来源处理 UGC
      if (!this.$route.query.graphic_id && !this.isSupperAdmin) {
        params.course_statement.creation_source = 2
      }

      const newParams = {
        graphic: params,
        extend_contents:
          JSON.parse(sessionStorage.getItem('extend_contents')) || [],
        relation_content: parseInt(content_type) === 3 ? relation_content : null
      }
      const { module_id, from_type } = this.$route.query
      if (
        from_type &&
        from_type === 'hangjia' &&
        parseInt(module_id || 0) === 6
      ) {
        newParams.graphic.relation_content = null
        newParams.graphic.from_type = from_type
      }
      const isPass = this.checkSaveParams(newParams)
      if (!isPass) {
        // if (graphic_status === 1 && this.formData.graphic_status === 4) { this.copyParam.graphic_status = 4 } // 原来的逻辑
        if (graphic_status === 6) {
          if (this.formData.graphic_status === 4) {
            this.copyParam.graphic_status = 4
          } else if (this.formData.graphic_status === 1) {
            this.copyParam.graphic_status = 1
          }
        }
        return { isPass: false, newParams }
      } else {
        return { isPass: true, newParams }
      }
    },
    checkSaveParams(params) {
      const {
        graphic_name,
        graphic_status,
        content_type,
        // classify_id,
        label_list,
        cover_image_id,
        authors,
        from_url,
        is_open,
        target_list,
        administrators,
        course_statement
      } = params.graphic
      // 创建图文参数校验
      if (graphic_status !== 4) {
        if (course_statement.publish_platform.type === 'KM' && !course_statement.publish_platform.KMVal) {
          this.$message.warning('请选择K吧')
          return false
        }
        if (!(graphic_name && graphic_name.trim())) {
          this.$message.warning('请输入图文标题')
          return false
        }
        if (is_open === 1 && !target_list) {
          this.$message.warning('请选择内容访问权限')
          return false
        }
        if (content_type === '' || content_type === undefined || isNaN(content_type)) {
          this.$message.warning('请选择文章类型')
          return false
        } else {
          if (content_type === 1) {
            const reg = /(http|https):\/\/([\w.]+\/?)\S*/
            if (!from_url) {
              this.$message.error('请输入原文链接')
              return false
            } else if (!reg.test(from_url)) {
              this.$message.error(
                '原文链接请输入http://或https://开头的链接地址'
              )
              return false
            }
          }
          if (content_type === 3 && !params.relation_content) {
            this.$message.warning('请添加相关内容')
            return false
          }
        }
        // if (!classify_id) {
        //   this.$message.warning('请选择分类')
        //   return false
        // }
        if (!label_list || !label_list.length) {
          this.$message.warning('请添加内容标签')
          return false
        }
        if (!cover_image_id) {
          this.$message.warning(
            '请上传图片或者使用快速生成封面图功能生成封面图'
          )
          return false
        }
        if (!authors) {
          this.$message.warning('请选择内容创作者')
          return false
        }
        if (!administrators || !administrators.length) {
          this.$message.warning('请选择内容管理员')
          return false
        }
        if (!this.$refs.propertySet.viladeteForm()) {
          this.$message.warning('请完善文章信息')
          return false
        }
        return true
      } else {
        return true
      }
    },
    doAddToClassList(g_id) {
      const { graphic_name, graphic_desc, graphic_id, graphic_number } =
        this.formData
      let ids = this.formData.cl_id.split(',')
      ids = this.strToIntArr(ids)
      let params = {
        cl_ids: ids,
        content_details: [
          {
            content_name: graphic_name,
            description: graphic_desc,
            href: `https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=${g_id}`,
            module_id: 8,
            module_name: '文章',
            item_id: g_id || graphic_id,
            word_num: graphic_number
          }
        ]
      }
      addClassList(params).then(() => {})
    },
    getGraphicViewInfo(graphic_id) {
      let api = this.approveStatus ? getApproveDetailApi : getGraphicDetails
      let params = this.approveStatus ? { course_id: graphic_id || this.graphic_id, act_type: 18 } : { graphic_id: graphic_id || this.graphic_id }
      api(params).then((data) => {
        if (data.graphic_status !== 6 && this.approveStatus) {
          this.$message.warning('该文章已审批')
        }
        // 新增的人员和组织字段处理
        let course_statement = null
        try {
          course_statement = data.course_statement
        } catch (error) {
          course_statement = null
        }

        // 新增字段 有值数据处理
        if (course_statement) {
          // 内部员工和外部员工回显处理 兼容旧数据
          // 勾选状态回显
          course_statement.innerChecked = Boolean(course_statement.inner_teacher_names?.length)
          course_statement.outChecked = Boolean(course_statement.out_teacher_names?.length)
          // 外部员工
          course_statement.out_teacher_names = (course_statement.out_teacher_names || []).map((e) => e.teacher_name).join(';')

          const resolveArray = ['pgc_creation_org', 'pgc_joint_creation', 'pugc_creation_org', 'pugc_joint_creation', 'ogc_purchase_org']
          resolveArray.forEach(item => {
            try {
              if (!course_statement[item]) { // 兼容编辑时字段为null的情况
                course_statement[item] = []
              } else {
                course_statement[item] = JSON.parse(course_statement[item])
              }
            } catch (error) {
              course_statement[item] = []
            }
          })

          // 特殊处理OGC的外部讲师字段 ogc_out_teachers
          if (course_statement.ogc_out_teachers) {
            try {
              let ogc_out_teachers_array = JSON.parse(course_statement['ogc_out_teachers'])
              course_statement['ogc_out_teachers'] = ogc_out_teachers_array.map(item => item.StaffName).join(';')
            } catch (error) {
              course_statement['ogc_out_teachers'] = ''
            }
          }

          // OGC采购成本
          if ([null, '', 0, '0'].includes(course_statement.ogc_purchase_amount)) {
            course_statement.ogc_purchase_amount = undefined
          }
          // 人力成本
          if ([null, '', 0, '0'].includes(course_statement.human_cost)) {
            course_statement.human_cost = undefined
          }

          // 发布平台处理
          if (course_statement.publish_platform) {
            try {
              course_statement.publish_platform = JSON.parse(course_statement.publish_platform)
            } catch (error) {
              course_statement.publish_platform = { type: 'QL', KMVal: '' }
            }
          } else {
            course_statement.publish_platform = { type: 'QL', KMVal: '' }
          }
        } else {
          course_statement = {
            operation_title: '', // 运营标题
            parent_content_act_type: null, // 父内容类型
            parent_content_id: '', // 父内容ID
            creation_source: 0, // 创作来源
            pgc_creation_org: [], // PGC创作组织
            pgc_joint_creation: [], // PGC联合创作组织
            pugc_creation_org: [], // PUGC创作组织
            pugc_joint_creation: [], // PUGC联合创作组织
            ogc_supplier_name: '', // 供应商名称
            ogc_purchase_org: [], // 采购组织
            ogc_out_teachers: '', // 外部讲师
            ogc_purchase_type: null, // 采购方式
            ogc_purchase_amount: undefined, // 采购成本
            human_cost: undefined, // 人力成本
            is_required: false, // 是否纳入应学
            certification_level: 0, // 认证等级
            operation_level: 3, // 运营分级
            operation_project_name: '', // 分级项目
            expert_score: null, // 内容专家评分
            user_score: 0, // 内容用户评分
            join_recommend: null, // 是否加入推荐流
            publish_platform: { // 发布平台
              type: 'QL', // 平台： QL、KM
              KMVal: '' // KM的值
            }
          }
        }
        
        // 内容管理组织数据处理
        data.dept_id = data.dept_id || ''
        data.dept_name = data.dept_name || ''
        // 展示课程推荐、备注回显
        if (data.is_show_recommend !== undefined) {
          data.is_show_recommend = Boolean(data.is_show_recommend)
        } else {
          data.is_show_recommend = true
        }
        data.course_statement = course_statement

        this.formData = data
        this.formData.graphic_desc = this.formData.graphic_desc ? this.formData.graphic_desc.replace(/<[^>]*>/g, '') : ''
        // 备份数据 用来对比"标题、描述、标签、封面"的改动
        this.barkInfo = JSON.parse(JSON.stringify(data))

        if (this.formData.is_open === 1 && this.formData.target_list * 1 === allTarget) {
          this.formData.is_open = 0
        } else if (this.formData.is_open === 2) {
          this.formData.is_open = 2
        } else {
          this.formData.is_open = 1
        }
        let reg = /<image id="cdnImg".*?src="(.*?)"\s*\/>/
        if (this.formData.graphic_text.match(reg)) {
          let obj = this.fixText(this.formData.graphic_text, reg)
          this.newText = obj.newText
          this.imgCodeSrc = obj.imgsrc
          this.cdnImg = obj.imgTag
        }
        const { content_type, classify_id, label_list, cl_id } = data
        this.formData.content_type = content_type?.toString() || ''
        this.formData.classify_id = classify_id ? classify_id.split(',') : []
        if (this.formData.classify_id.length > 0) {
          this.formData.classify_id = this.formData.classify_id.map((e) => {
            return parseInt(e)
          })
        }
        if (label_list.length) {
          this.formData.label_list = label_list.filter(item => item.label_name)
        }
        this.formData.authors = data.authors
          ? this.keyNameToUpCase(JSON.parse(data.authors))
          : []
        this.formData.administrators = data.administrators
          ? this.keyNameToUpCase(JSON.parse(data.administrators))
          : []
        this.formData.cl_id = cl_id
        this.formData.extend_contents = []
        this.$refs.propertySet.initData(this.formData)
        this.editorConfig.catalogue =
          data.is_open_catalogue === 1 ? Boolean(false) : Boolean(true)
        this.copyParam.graphic_status = data.graphic_status
        if (data.graphic_status !== 4 && this.saveDraftTimer) {
          clearInterval(this.saveDraftTimer)
        }
      })
    },
    keyNameToUpCase(arr) {
      if (!(arr && arr.length > 0)) return ''
      let newArr = arr.map((e) => {
        return { StaffID: e.staffid, StaffName: e.staffname }
      })
      return newArr
    },
    evil(fn) {
      var Fn = Function
      return new Fn('return' + fn)()
    },
    // getWordCount(val) {
    //   this.formData.graphic_number = val
    //   // 以一分钟看600个字计算
    //   this.forecastReadTime = val > 0 ? (val % 600 === 0 ? val / 600 : (Math.ceil(val * 10 / 600) / 10).toFixed(1)) : 0
    // },
    catalogueVisible(val) {
      this.editorConfig.is_open_catalogue = val
      this.formData.is_open_catalogue = val ? 1 : 0
    },
    handStafSelector(obj) {
      if (obj.type === 1) {
        this.copyParam.authors = obj.val
        const authorsArr = obj.val ? JSON.parse(obj.val) : []
        this.formData.authors_list = authorsArr.map((e) => {
          return e.staffname
        })
        sessionStorage.setItem(
          'graphic_preview',
          JSON.stringify(this.formData)
        )
      } else if (obj.type === 2) this.copyParam.administrators = obj.val
      else this.copyParam.target_list = obj.val
    },
    // 标签改变时的预览功能缓存的数据同步
    labelChanged(list) {
      this.formData.label_list = list || []
      sessionStorage.setItem(
        'graphic_preview',
        JSON.stringify(this.formData)
      )
    },
    handleImg(cover_image_id) {
      this.formData.cover_image_id = cover_image_id
    },
    handleEvent(scene) {
      switch (scene) {
        case 1:
          this.copyParam.graphic_status = 4
          this.createGraphic()
          break
        case 2:
          if (this.formData.graphic_status !== 4) {
            // 发布状态点预览数据存本地,不修改已发布的数据
            this.formData.graphic_text = this.$refs['editor'].getContent()
            this.formData.graphic_text += this.cdnImg || ''
            sessionStorage.setItem(
              'graphic_preview',
              JSON.stringify(this.formData)
            )
            this.goGraphicPreview(this.graphic_id)
          } else {
            this.createGraphic().then((graphic_id) => {
              this.goGraphicPreview(graphic_id)
            })
          }
          break
        case 3:
          this.copyParam.graphic_status = 1
          // this.createGraphic()
          this.checkIsShowModifyKeyField(3)
          break
        case 4:
          this.copyParam.graphic_status = 6
          this.checkIsShowModifyKeyField(4)
          // this.createGraphic().then((e) => {
          //   console.log('接口不应该这里', e)
          //   if (e.status_code === 1) return // 审核不通过
          //   if (e?.success === false) {
          //     this.$message.warning(e.message)
          //     return
          //   }
          //   setTimeout(() => {
          //     this.$router.push({ name: 'graphic-page' })
          //   }, 1000)
          // })
          break
        default:
          break
      }
    },
    goGraphicPreview(graphic_id) {
      const { href } = this.$router.resolve({
        name: 'preview',
        query: {
          graphic_id: graphic_id,
          scene: 1
        }
      })
      window.open(href)
    },
    strToIntArr(ids) {
      if (!ids) return ''
      let a = ids.map((e) => {
        return parseInt(e)
      })
      return a
    },
    handleVisitorCheck() {
      // 图文白名单校验
      const handleResult = visitorCheck().then((data) => {
        this.visitorCheckFlag = data
        if (!data) {
          this.$router.push({ name: '401', query: { scene: 'vc' } })
        }
      })
      return handleResult
    },
    cancel() {
      let from = this.$route.query.from || 'graphic-page'
      if (['graphic-list', 'networkManage', 'graphic-page'].includes(from)) { // 返回后台列表和审批列表时头部导航会有问题
        window.$qlCommonHeader.hidden()
      }
      if (from === 'preview') { // 浏览页面跳转特殊处理 需要graphic_id参数
        let graphic_id = this.$route.query.graphic_id || this.formData.graphic_id
        this.$router.push({
          name: from,
          query: {
            graphic_id
          }
        })
        return
      }
      this.$router.push({ name: from })
    },
    getDetailInfo() {
      this.handleVisitorCheck().then((e) => {
        if (this.visitorCheckFlag) {
          if (this.graphic_id && this.graphic_id !== 'undefine') {
            this.formData.graphic_id = this.graphic_id
            this.getGraphicViewInfo()
          }
          if (this.flag) {
            this.flag = false
          } else {
            this.count++
          }
        }
      })
    }
  },
  activated() {
    this.getDetailInfo()
  }
}
</script>

<style lang="less" scoped>
.graphic-page {
  height: 100%;
  min-width: 1158px;
  min-height: 1247px;
  position: relative;

  .platform {
    padding-top: 10px;
    margin: 0 auto;
    .platform-main {
      background-color: #fff;
      padding: 10px 16px;
      .publish-platform-item :deep(.el-form-item__content){
        display: flex;
        align-items: center;
        .el-radio-group {
          display: flex;
          align-items: center;
          height: 32px;
        }
        .magl-20 {
          margin-left: 20px;
        }
      }
    }
  }
  .contain-main {
    padding-top: 10px;
    // height: calc(100% - 90px);
    display: flex;
    justify-content: center;

    .left {
      // height: 98%;
      margin-right: 10px;
      border-radius: 4px;

      .editor-header {
        // height: 12%;
        height: 200px;
        padding: 20px 16px;
        background-color: #fff;

        input {
          width: 100%;
          display: block;
          color: rgba(153, 153, 153, 1);
          border: none;
        }

        input::-webkit-input-placeholder {
          color: rgba(153, 153, 153, 1);
        }

        :-moz-placeholder {
          color: rgba(153, 153, 153, 1);
        }

        ::-moz-placeholder {
          color: rgba(153, 153, 153, 1);
        }

        input:-ms-input-placeholder {
          color: rgba(153, 153, 153, 1);
        }

        input::-ms-input-placeholder {
          color: rgba(153, 153, 153, 1);
        }

        .title {
          padding-bottom: 16px;
          font-size: 28px;
          font-weight: bold;
          border-bottom: solid 1px rgba(238, 238, 238, 1);
        }

        .desc {
          margin-top: 16px;
          font-size: 16px;
          font-weight: 400;

          :deep(.el-textarea__inner) {
            color: #999999;
            font-size: 16px;
            line-height: 20px;
            padding: unset;
            border: none;
          }
        }
      }

      .editor-body {
        // height: 88%;
        background: #fff;
        position: relative;
        top: -1px;

        :deep(.editor__area) {
          height: 100%;
          // 隐藏富文本图片添加链接功能
          .menu_item[title='添加链接'] {
            display: none;
          }
        }
        .sdc-preview {
          // max-height: 350px;
          overflow: auto;
          margin: 5px 0 0 0;
          padding: 16px;
          :deep(.editor-content) {
            word-break: break-all;
          }
        }
      }
    }

    .right {
      width: 420px;
      height: 100%;
      border-radius: 4px;
      background-color: #fff;

      :deep(.propertySet-content .extand-table) {
        .el-tooltip {
          width: 300px;
        }

        .tags {
          margin: 0 12px 0 0px;
        }
      }
    }

    .showEditCatalog {
      .editor-header {
        position: relative;
        left: 210px;
      }

      :deep(.editor__area) {
        .tox-sidebar-wrap {
          margin-left: -5px;

          .tox-sidebar {
            position: sticky !important;
            overflow-x: unset !important;
            padding-right: 100px;
            margin-top: -248px !important;
            width: 0 !important;
            padding-right: unset !important;

            .tox-sidebar__slider {
              position: sticky !important;
              top: 10px !important;
              overflow-x: unset !important;
            }
          }
        }
      }
    }
  }

  .contain-footer {
    margin-top: 20px;
    // height: 70px;
    display: flex;
    justify-content: center;
    position: sticky;
    z-index: 1;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    box-shadow: 0 -1px 4px -4px rgba(0, 0, 0, 0.12);
    .btn-long {
      min-width: 120px;
    }
    .directly-inner {
      margin: 0 auto;
      width: 250px;
    }
  }

  .contain-footer {
    .convention-footer {
      padding: 20px 0;
      .convention-footer-inner {
        display: flex;
        align-items: center;
        justify-content: space-between;
    
        :deep(.el-button) {
          width: 104px;
        }
    
        .left-tip {
          display: flex;
          align-items: center;
          color: rgba(0, 0, 0, 0.4);
    
          span:nth-child(2) {
            margin: 0 24px 0 14px;
          }
    
          .save-loading {
            display: flex;
            align-items: center;
    
            i {
              display: inline-block;
              margin-left: 14px;
              width: 24px;
              height: 24px;
              background: url('~@/assets/img/save-loading.gif') no-repeat center /
                cover;
            }
          }
        }
      }
    }
  }

  :deep(.tox-editor-header) {
    position: sticky !important;
    top: 0px;
    transform: unset;
    background: #fff;
  }

  .contain-main .propertySet-content,
  .contain-main .left {
    :deep(.el-input .el-input__inner) {
      padding: 0 15px;
    }
  }

  :deep(.sdc__editor .tox .tox-tbtn svg) {
    fill: #666;
  }
}

@media screen and (max-width: 1660px) {
  .convention-banner {
    margin: 10px auto 0;
    width: 1148px;
  }
  .platform {
    width: 1148px;
  }
  .contain-main {
    .left {
      width: 718px !important;
    }

    :deep(.sdc-preview) {
      min-height: 800px;
    }

    .showEditCatalog {
      .editor-header {
        width: 508px;
      }

      :deep(.editor__area) {
        .tox-sidebar {
          .tox-sidebar__slider {
            height: 300px;
          }
        }
      }

      :deep(.tox .tox-sidebar-wrap .tox-edit-area) {
        min-height: 888px !important;
      }
    }

    :deep(.tox .tox-sidebar-wrap .tox-edit-area) {
      min-height: 928px;
    }
  }

  .contain-footer > div {
    width: 1158px;
  }
}

@media screen and (min-width: 1661px) {
  .convention-banner {
    width: 1430px;
    margin: 10px auto 0;
  }
  .platform {
    width: 1430px;
  }
  .contain-main {
    .left {
      width: 1000px;
    }

    :deep(.sdc-preview) {
      min-height: 1000px;
    }

    .showEditCatalog {
      .editor-header {
        width: 790px;
      }

      :deep(.editor__area) {
        .tox-sidebar {
          .tox-sidebar__slider {
            height: 682px;
          }
        }
      }

      :deep(.tox .tox-sidebar-wrap .tox-edit-area) {
        min-height: 930px !important;
      }
    }

    :deep(.tox .tox-sidebar-wrap .tox-edit-area) {
      min-height: 966px;
    }
  }

  .contain-footer > div {
    width: 1440px;
  }
}
</style>
