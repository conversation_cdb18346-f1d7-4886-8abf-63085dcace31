<template>
    <div class="labelCardItem">
        <div class="courseImage">
            <defaultImage :info="info"/>
            <div class="kdident">
                <span class="official" v-if="info.origin_data.cl_type === 1">官方</span>
                <span class="boutique2" v-if="info.origin_data.excellent_status === 1">精品</span>
            </div>
            <span class="duration">共{{info.origin_data.courses_count||0}}门课</span>
        </div>
        <div  class="courseInfo">
            <moduleTitle :info="info"/>
            <div class="courseView kdInfo flex align-center">
                <div class="flex align-center">
                    <img :src="require('@/assets/img/label/eyes2x.png')">
                    <span>{{info.view_count||0}}</span>
                </div>
                <div class="flex align-center">
                    <img :src="require('@/assets/img/label/like2x.png')">
                    <span>{{info.origin_data.praise_count||0}}</span>
                </div>
                <div class="flex align-center">
                    <img :src="require('@/assets/img/label/star2x.png')">
                    <span>{{info.origin_data.favorite_count||0}}</span>
                </div>
                <div class="flex align-center">
                    <img :src="require('@/assets/img/label/comments2x.png')">
                    <span>{{info.origin_data.comment_count||0}}</span>
                </div>
            </div>
            <relatedContent :info="info" :curModuleId="curModuleId" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :subsType="subsType" :contentType="contentType"></relatedContent>
        </div>
    </div>
</template>
<script>
import defaultImage from './child/defaultImage.vue'
import moduleTitle from './child/moduleTitle.vue'
import relatedContent from './child/relatedContent.vue'
export default {
  name: 'classScheduleItem',
  props: ['info', 'curModuleId', 'curLabelId', 'sortSubsIds', 'subsType', 'contentType'],
  data() {
    return {}
  },
  components: {
    defaultImage,
    moduleTitle,
    relatedContent
  },
  computed: {}
}
</script>
<style lang="less" scoped>
@import '../style/item.less';
</style>
