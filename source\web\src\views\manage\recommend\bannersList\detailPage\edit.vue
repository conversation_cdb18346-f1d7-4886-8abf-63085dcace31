<template>
  <div class="add-banner-dialog">
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="visible"
      width="900px" 
      :top="form.banner_type === 2 && moduleName !== 'add' ? '15vh' : '8vh'"
      :close-on-click-modal="false"
      :before-close="closeDialog"
      >
        <div class="add-banner-body">
          <el-form ref="form" :model="form" :rules="rules" inline label-width="90px">
            <el-form-item label="选择终端" prop="terminal" >
              <el-checkbox-group v-model="form.terminal" class="mr-12">
                <el-checkbox v-for="(item, index) in pageType" :label="item.value" :key="index">{{ item.label }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
            <el-form-item label="展示人群" prop="target_ids" >
              <AudienceSelector
                v-if="visible"
                audience
                multiple
                v-model="form.target_ids"
                ref="selector"
                createStudentID
                appCode="qlearning"
                :env="audienceEnv"
              ></AudienceSelector>
            </el-form-item>
            <el-form-item label="排序" prop="order_no" >
              <el-input v-model.number="form.order_no" class="w-256 mr-12" maxLength="3" size="small" max-width="30" placeholder="请输入排序" ></el-input>
              <span class="color-gray">仅支持输入1-10的数字</span>
            </el-form-item>
            <el-form-item label="推广时间" prop="promotion_time" >
              <el-date-picker
                class="mr-12 w-360 date-box"
                v-model="form.promotion_time"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                start-placeholder="开始日期时间"
                end-placeholder="结束日期时间"
                size="small">
              </el-date-picker> 
              <span class="color-gray">同一推广周期内，最多可展示10门内容</span>
            </el-form-item>
          </el-form>

          <div class="search-box">
            <el-form inline label-width="90px">
              <el-form-item label="推荐形式" prop="banner_type">
                <el-radio-group v-model="form.banner_type" :disabled="moduleName === 'edit'">
                  <el-radio :label="1">活动</el-radio>
                  <el-radio :label="2" :disabled="disdContRadio">内容</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <template v-if="form.banner_type === 1">
              <el-form ref="form2" :model="form" :rules="rules" label-width="90px" >
                <el-form-item label="标题" prop="banner_name">
                  <el-input v-model="form.banner_name" placeholder="请输入标题" maxLength="200" size="small"></el-input>
                </el-form-item>
                <el-form-item label="活动描述" prop="description">
                  <el-input
                    class="width-inherit pub-p-r48"
                    type="textarea"
                    :rows="2"
                    maxLength="200"
                    placeholder="请输入活动描述"
                    v-model="form.description"
                    show-word-limit
                  >
                  </el-input>
                </el-form-item>
                <el-form-item label="跳转链接" prop="link_url">
                  <div class="j-end">
                    <el-input
                      class="width-inherit"
                      type="text"
                      placeholder="请输入http://或者https://开头的地址"
                      v-model="form.link_url"
                      size="small"
                    >
                    </el-input>
                    <el-button type="text" style="min-width: min-content;padding-left: 20px;" @click="toLink" size="small" :disabled="!form.link_url">跳转链接</el-button>
                  </div>
                </el-form-item>
                <el-form-item label="上传图片" prop="cover_image">
                  <cut-img-upload 
                  ref="upload"
                  accept=".jpg,.jpeg,.png"
                  :size="actType === '202'? '2' : '0.5'"
                  :fixedNumber="actType === '202' ? [1200, 160] : [262, 175]"
                  @handleSuccess="handleSuccessImage"
                  :dialogImageUrl="form.cover_image" 
                  :autoImgUrl="form.cover_image_id" 
                  @handleClearImg="handleClearImg"
                  >
                    <template v-slot:text>
                      <p>建议图片上传尺寸为{{actType === '202' ? '1200*160' : '262*175px'}}，图片小于{{actType === '202' ? '2000' : '500'}}kb，支持jpg/png/jpeg格式</p>
                    </template>
                  </cut-img-upload>
                </el-form-item>
                <el-form-item label="" ></el-form-item>
              </el-form>
            </template>
            <template v-else-if="form.banner_type === 2 && moduleName === 'add'">
              <searchBox v-model="searchData" @onSearch="onSearch(1)" @onReset="handleReset(true)"></searchBox>
            </template>
          </div>
          <div class="select-course" v-if="form.banner_type === 2 && moduleName === 'add'">
            <el-table
              ref="multipleTable"
              v-loading="tableLoading"
              :data="qlsearchContentTable"
              :header-cell-style="{background:'#eef1f6'}"
              class="content-table qlcontent-table"
              @selection-change="qlsearchSelectionChange"
              height="280px"
              >
              <el-table-column
                :selectable="(row) => row.dup != 1"
                type="selection"
                align="left"
                width="55">
              </el-table-column>
              <el-table-column align="left" label="内容标题" prop="content_name" show-overflow-tooltip></el-table-column>
              <el-table-column align="left" label="内容类型" prop="module_name" width="100px"></el-table-column>
              <el-table-column align="left" label="认证等级" prop="course_level" width="100px">
                <template v-slot="prop">
                  <span>{{ prop.row.course_level ? certificationLevel[prop.row.course_level] : '无' }}</span>
                </template>
              </el-table-column>
              <el-table-column align="left" label="标签" prop="labelsText" show-overflow-tooltip></el-table-column>
              <el-table-column align="left" label="内容链接" prop="content_url" show-overflow-tooltip>
                <template v-slot="prop">
                  <a class="content-url" target="_blank" :href="prop.row.content_url">{{ prop.row.content_url }}</a>
                </template>
              </el-table-column>
            </el-table>
            <div class="table-pagination">
              <el-pagination
                :hide-on-single-page="qlsearchContentTable.length === 0"
                @current-change="onSearch"
                :current-page="searchData.pageNum"
                :page-size="searchData.pageSize"
                layout="total, prev, pager, next, jumper"
                :total="searchData.total"
              >
              </el-pagination>
            </div>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="handleSave">提交</el-button>
        </div>
      </el-dialog>
  </div>
</template>
<script>
import { getImgBgColor } from '@/utils/tools.js'
import { AudienceSelector } from '@tencent/sdc-audience'
import { CutImgUpload } from '@/components/index'
import { searchsiteApi, addBannerApi, updateBannerApi } from '@/config/mooc.api.conf'
import searchBox from './contentSearchForm.vue'

export default {
  components: {
    AudienceSelector,
    CutImgUpload,
    searchBox
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogTitle() {
      return this.moduleName === 'add' ? '添加' : '编辑'
    },
    actType() {
      return this.$route.query.act_type
    },
    // 榜单---推荐形式禁用"内容"
    disdContRadio() {
      return ['202'].includes(this.actType)
    }
  },
  data() {
    const orderNoValid = (rule, value, callback) => {
      if (!value && value !== 0) {
        callback(new Error('请输入排序数'))
      } else {
        // if (value > 10 || value < 1) {
        //   callback(new Error('仅支持输入1-10的数字'))
        // }
        callback()
      }
    }
    // 跳转链接校验
    const validateUrl = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入'))
      } else {
        // 需要填写协议
        if (value.indexOf('http://') === 0 || value.indexOf('https://') === 0) {
          callback()
        } else {
          callback(new Error('格式错误，请输入http或https开头的链接地址'))
        }
      }
    }
    const validImg = (rule, value, callback) => {
      if (!value && !this.form.cover_image_id) {
        return callback(new Error('请选择运营图'))
      } else {
        callback()
      }
    }
    return {
      moduleName: 'add',
      audienceEnv: process.env.NODE_ENV,
      form: {
        act_type: '',
        id: null,
        terminal: ['pc', 'mobile'],
        order_no: null,
        promotion_time: [],
        banner_type: 2,
        target_ids: '', // 展示人群
        // 活动
        banner_name: '',
        description: '',
        link_url: '',
        cover_image: '',
        cover_image_id: '',
        bg_color: ''
      },
      rules: {
        order_no: [
          { pattern: /^[+]{0,1}[0-9](\d*)$/, message: '请输入正整数', trigger: 'change' },
          { required: true, type: 'number', validator: orderNoValid, trigger: ['change', 'blur'] }
        ],
        promotion_time: [ 
          { required: true, message: '请选择推广时间', trigger: ['change', 'blur'] }
        ],
        terminal: [
          { type: 'array', required: true, message: '请至少选择一个终端', trigger: ['change', 'blur'] }
        ],
        target_ids: [
          { required: true, message: '请选择展示人群', trigger: 'blur' }
        ],
        banner_name: [
          { required: true, message: '请输入标题', trigger: 'blur' }
        ],
        description: [
          { required: true, message: '请输入描述', trigger: 'blur' }
        ],
        link_url: [
          { required: true, message: '请输入跳转链接', trigger: 'blur' },
          { validator: validateUrl, trigger: ['change', 'blur'] }
        ],
        cover_image: [
          { required: true, validator: validImg, trigger: 'blur' }
        ]
      },
      pageType: [
        { label: 'PC', value: 'pc' },
        { label: '移动', value: 'mobile' }
      ],
      searchData: {
        keywords: '',
        startTime: '',
        endTime: '',
        labels: [],
        classify: [],
        sortBy: 'created_at',
        courseLevel: '',
        pageNum: 1,
        pageSize: 10,
        total: 0
      },
      certificationLevel: {
        '0': '-',
        '1': '公司级',
        '2': 'BG级',
        '3': '部门级',
        '4': '个人分享'
      },
      qlsearchContentTable: [],
      tableLoading: false,
      qlsearchContentSelect: [],
      editData: {}
    }
  },
  mounted() {
  },
  methods: {
    onSearch(num) {
      this.searchData.pageNum = num
      this.qlContentSearch()
    },
    // 本站内容搜索
    qlContentSearch (num) {
      let data = { ...this.searchData }
      if (data.moduleId === 0) {
        delete data.moduleId
      }
      delete data.total
      this.tableLoading = true
      searchsiteApi(data).then(res => {
        this.tableLoading = false
        res.list.map(v => {
          if (v.labels) {
            v.labelsText = v.labels.join(';')
          }
        })
        this.qlsearchContentTable = res.list
        this.searchData.total = res.total
      })
    },
    qlsearchSelectionChange(e) {
      if (e.length > 1) {
        this.$message.warning('请勾选单个内容')
        this.$refs.multipleTable.clearSelection()
      } else {
        this.qlsearchContentSelect = e
      }
    },
    initData(data) {
      this.editData = data
      const { id, terminal, order_no, start_time, end_time, banner_type, target_ids, banner_name, description, link_url, img_content_id, image_url, bg_color } = data
      this.moduleName = data.moduleName
      if (data.moduleName === 'edit') {
        this.form = {
          id,
          terminal: terminal.split(','),
          order_no,
          promotion_time: [start_time, end_time],
          banner_type,
          target_ids,
          banner_name,
          description,
          link_url,
          cover_image: image_url,
          cover_image_id: img_content_id,
          bg_color: bg_color
        }
      } else {
        if (['202'].includes(this.actType)) {
          this.form.banner_type = 1
        }
      }
    },
    toLink() {
      let URL = this.form.link_url
      if (URL && (URL.indexOf('http://') === 0 || URL.indexOf('https://') === 0)) {
        window.open(this.form.link_url)
      } else {
        this.$message.error('格式错误，请输入http或https开头的链接地址')
      }
    },
    handleClearImg(val) {
      this.form.cover_image_id = ''
      this.form.cover_image = ''
    },
    handleSuccessImage(val, file, id) {
      this.form.cover_image = val
      // 清空一键生成封面
      this.form.cover_image_id = ''
      this.form.img_content_id = id
      if (this.form.cover_image) {
        // this.$refs.form1.clearValidate('cover_image')
        this.$refs.form2.clearValidate('cover_image')
      }
      let that = this
      getImgBgColor(URL.createObjectURL(file), function (e) {
        that.form.bg_color = e
      })
    },
    // 保存
    handleSave() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const { promotion_time, terminal } = this.form
          let terminalStr = terminal.join()
          let start_time = promotion_time[0]
          let end_time = promotion_time[1]

          if (this.form.banner_type === 1) {
            this.$refs['form2'] && this.$refs['form2'].validate((valid2) => {
              if (valid2) {
                this.addActivityType({ ...this.form, terminalStr, start_time, end_time })
              } else {
                return false
              }
            })
          } else {
            this.addContentType({ ...this.form, terminalStr, start_time, end_time })
          }
        } else {
          return false
        }
      })
    },
    addActivityType(e) {
      const { terminalStr, start_time, end_time, order_no, target_ids, banner_type, banner_name, description, link_url, img_content_id, cover_image, bg_color } = e
      let params = {
        recommend_module_id: 99,
        act_type: this.$route.query.act_type,
        terminal: terminalStr,
        order_no,
        start_time,
        end_time,
        target_ids,
        banner_type,
        banner_name,
        description,
        link_url,
        img_content_id: img_content_id,
        image_url: cover_image,
        bg_color
      }
      if (this.moduleName === 'add') {
        this.addBanner(params)
      } else {
        params.id = this.form.id
        this.editBanner(params)
      }
    },
    addContentType(e) {
      const { terminalStr, order_no, start_time, end_time, banner_type, target_ids } = e
      let data = {}
      if (this.moduleName === 'add') {
        if (this.qlsearchContentSelect.length === 0) {
          this.$message.error('请搜索并勾选单个内容')
          return false
        }
        let selectData = this.qlsearchContentSelect[0]
        data = {
          act_type: this.$route.query.act_type,
          terminal: terminalStr,
          order_no,
          start_time,
          end_time,
          banner_type,
          target_ids,
          recommend_module_id: selectData.module_id,
          recommend_item_id: selectData.item_id,
          banner_name: selectData.content_name,
          link_url: selectData.content_url
        }
        this.addBanner(data)
      } else {
        data = {
          id: this.form.id,
          act_type: this.$route.query.act_type,
          terminal: terminalStr,
          order_no,
          start_time,
          end_time,
          banner_type,
          target_ids,
          recommend_module_id: this.editData.recommend_module_id,
          recommend_item_id: this.editData.recommend_item_id,
          banner_name: this.editData.banner_name,
          link_url: this.editData.link_url
        }
        this.editBanner(data)
      }
    },
    addBanner(params) {
      addBannerApi(params).then(res => {
        this.$message.success(this.dialogTitle + '成功')
        this.closeDialog()
        this.$emit('onSearch')
      })
    },
    editBanner(params) {
      updateBannerApi(params).then(res => {
        this.$message.success(this.dialogTitle + '成功')
        this.closeDialog()
        this.$emit('onSearch')
      })
    },
    closeDialog() {
      this.$refs.form.clearValidate()
      this.form = {
        id: null,
        terminal: ['pc', 'mobile'],
        order_no: null,
        promotion_time: [],
        banner_type: 2,
        target_ids: '',
        // 活动
        banner_name: '',
        description: '',
        link_url: '',
        cover_image: '',
        cover_image_id: '',
        bg_color: ''
      }
      this.handleReset()      
      this.$emit('update:visible', false)
      this.$emit('close')
    },
    handleReset(refresh = false) {
      this.searchData = {
        keywords: '',
        startTime: '',
        endTime: '',
        labels: [],
        classify: [],
        sortBy: 'created_at',
        courseLevel: '',
        pageNum: 1,
        pageSize: 10,
        total: 0
      }
      this.qlsearchContentTable = []
      refresh && this.onSearch(1)
    }
  }
}
</script>
<style lang="less" scoped>
.add-banner-dialog {
  :deep(.el-dialog) {
    .el-dialog__header {
      border-bottom: 0;
    }
    .el-dialog__body {
      padding: 0;
      .el-dialog__footer {
        padding: 8px 0 24px;
      }
    }
  }
  .add-banner-body {
    padding: 10px 32px 0;
    .search-box {
      border-radius: 4px;
      background: #FAFAFA;
      padding: 20px 16px 0 0;
    }
    .select-course {
      padding: 0 16px 16px;
      background: #FAFAFA;
      .content-url {
        color: #3464E0;
      }
      .el-table /deep/ th.is-leaf {
        background-color: #EEEEEE !important;
      }
      .table-pagination /deep/ .el-pagination {
        margin-top: 10px;
      }
    }
  }
  .reset-btn:hover {
    border-color: #3464E0;
    background-color: #F5F7F9;
    color: #3464E0;
  }
  .date-box {
    .el-date-editor /deep/ .el-range-input {
      width: 40%;
    }
    .el-date-editor /deep/ .el-range-separator {
      width: 8%;
    }
  }

  .w-256 {
    width: 256px;
  }
  .w-360 {
    width: 360px;
  }
  .mt-8 {
    margin-top: 8px;
  }
  .mr-12 {
    margin-right: 12px;
  }
  .color-gray {
    color: #00000099;
  }
}
.pub-p-r48 {
  :deep(.el-input__inner) {
    padding-right: 48px;
  }
}
</style>
