<template>
  <div class="graphic-user-page">
    <link rel="stylesheet" :href="linkHref">
    <keep-alive v-if="$route.meta.keepAlive">
      <router-view />
    </keep-alive>
    <router-view v-else :key="$route.path" />
  </div>
</template>

<script>
export default {
  name: 'app',
  data() {
    return {
      linkHref: '',
      blacklist: ['/user/memberManagement', '/user/memberTest']
    }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        let that = this
        let root = document.documentElement
        if (val.staff_name && !document.getElementById('graphic-common-head') && !['mooc', 'spoc'].includes(this.$route.query.from) && this.$route.query.comeFrom !== 'outCourse') {
          root.style.setProperty('--app-height', 'calc(100% - 62px)')
          this.headerIsLoad = true
          this.linkHref = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeaderStyle.css` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeaderStyle.css`  
          const commonHeaderJsUrl = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeader.js` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeader.js`
          this.loadHeadJS(commonHeaderJsUrl, function() {
            if (window.$qlCommonHeader && !that.blacklist.includes(that.$route.path)) {
              window.$qlCommonHeader.create({
                staff_name: val.staff_name,
                dev: !(process.env.NODE_ENV === 'production')
              })

              setTimeout(() => {
                let el = document.getElementsByClassName('common-header-occupy')
                if (el.length > 0) {
                  console.log('El', el[0].offsetHeight)
                  document.getElementById('app').style.height = `calc(100% - ${el[0].offsetHeight}px)`
                }
              }, 500)
            }
          })
        } else {
          root.style.setProperty('--app-height', '100%')
        }
      },
      immediate: true
    }
  },
  created() {
    
  },
  methods: {
    // 获取登陆用户信息
    loadHeadJS (url, callback) {
      var script = document.createElement('script')
      var fn = callback || function() {}
      script.type = 'text/javascript'
      script.id = 'graphic-common-head'
      // IE
      if (script.readyState) {
        script.onreadystatechange = function() {
          if (script.readyState === 'loaded' || script.readyState === 'complete') {
            script.onreadystatechange = null
            fn()
          }
        }
      } else {
        // 其他浏览器
        script.onload = function() {
          fn()
        }
      }
      script.src = url
      document.getElementsByTagName('head')[0].appendChild(script)
    }
  },
  mounted() {
    
  }
}
</script>

<style lang="less">
@import '~assets/css/graphic-common.less';
@import '~assets/css/ai-common.less';
@import '~assets/css/center.less';
#app {
  height: var(--app-height);
}
.graphic-user-page {
  min-width: 1200px;
  background: #F6F7F9;
  height: 100%;
  overflow-y: auto;
}
</style>
