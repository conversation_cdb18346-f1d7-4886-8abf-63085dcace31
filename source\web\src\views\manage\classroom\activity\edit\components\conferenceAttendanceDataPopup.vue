<template>
  <div class="conference-attendance-data-popup activity-common">
    <el-dialog :visible.sync="visible" width="800px" :close-on-click-modal="false" :before-close="handlerClose">
      <div slot="title" class="dialog-header">获取腾讯会议考勤数据</div>
      <div class="dialog-content">
        <div class="error-tips color-danger mb-20">将根据腾讯会议参会情况，刷新考勤状态。此次将更新 {{ tableData.records.length }} 人的考勤状态，明细如下</div>
        <div class="table-card">
          <el-table :data="tableData.records" header-row-class-name="table-header-style" row-class-name="table-row-style" class="content-table" height="392px">
            <el-table-column prop="staff_name" label="姓名" width="112"></el-table-column>
            <el-table-column prop="dept_full_name" label="组织架构" width="234">
              <template slot-scope="scope">
                <el-tooltip class="item" effect="dark" :content="scope.row.dept_full_name" placement="top">
                  <span class="text-box">{{ scope.row.dept_full_name }}</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="reg_status_name" label="报名状态" width="122">
              <template slot-scope="scope">
                <span :class="['text-box', regStatusColor(scope.row.reg_status) ]">{{ regStatusName(scope.row.reg_status) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="status_name" label="考勤状态" width="88">
              <template slot-scope="scope">
                <span :class="['text-box', attendanceStatusColor(scope.row.status)]">{{ attendanceStatusName(scope.row.status) }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="join_time" label="参会时间" width="178"></el-table-column>
          </el-table>
          <el-pagination
            v-if="tableData.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="current"
            :page-sizes="[5, 10, 20, 30, 50, 100]"
            :page-size="size"
            layout="total,  prev, pager, next, sizes, jumper"
            :total="tableData.total"
          >
          </el-pagination>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="handlerClose" size="small">取消</el-button>
        <el-button class="confirm-btn" type="primary" @click="handlerConfirm" size="small">确认获取</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import pager from '@/mixins/pager'
import { getMeetingAttendanceApi, saveMeetingAttendanceApi } from '@/config/classroom.api.conf.js'

export default {
  name: 'conferenceAttendanceDataPopup',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  mixins: [pager],
  data() {
    return {}
  },
  watch: {
    visible: {
      handler(val) {
        if (val) {
          this.onSearch()
        }
      },
      immediate: true
    }
  },
  computed: {
    activityId () {
      return this.$route.query.activity_id
    }
  },
  created() { },
  mounted() { },
  beforeDestroy() { },
  methods: {
    onSearch() {
      this.getConferenceAttendanceData()
    },
    getConferenceAttendanceData() {
      getMeetingAttendanceApi({
        activity_id: this.activityId,
        current: this.current,
        size: this.size
      }).then(res => {
        this.tableData.records = res.records || []
        this.tableData.total = res.total
      })
    },
    handlerConfirm() {
      if (this.tableData.records && this.tableData.records.length === 0) {
        this.$message.warning('暂无数据，请稍后再试')
        return
      }
      saveMeetingAttendanceApi({
        activity_id: this.activityId
      }).then(res => {
        this.$emit('saveMeetingAttendance')
        this.$message.success('获取成功')
        this.$nextTick(() => {
          this.handlerClose()
        })
      })
    },
    handlerClose() {
      this.$emit('update:visible', false)
    },
    regStatusColor(status) {
      switch (status) {
        case 0:
          return 'color-success'
        case 6:
          return 'color-warning'
        case 2:
          return 'color-primary'
        case 3:
          return 'color-primary'
        case 11:
          return 'color-fail'
        case 1:
          return 'color-danger'
        default:
          return ''
      }
    },
    regStatusName(status) {
      switch (status) {
        case 0:
          return '已报名'
        case 6:
          return '未报名霸课'
        case 2:
          return '排队候补'
        case 3:
          return '待上级审核'
        case 11:
          return '审核未通过'
        case 1:
          return '已注销报名'
        default:
          return ''
      }
    },
    attendanceStatusColor(status) {
      switch (status) {
        case 4:
          return 'color-success'
        case 5:
          return 'color-danger'
        case 18:
          return 'color-warning'
        case 19:
          return 'color-primary'
        default:
          return 'color-fail'
      }
    },
    attendanceStatusName(status) {
      switch (status) {
        case 4:
          return '全勤'
        case 5:
          return '缺勤'
        case 18:
          return '部分缺勤'
        case 19:
          return '临时取消'
        default:
          return '暂无数据'
      }
    }
  }
}
</script>

<style lang="less" scoped>
  @import '~assets/css/activity-common.less';

  .view-operation-logs-popup {
    :deep(.el-dialog) {
      .el-dialog__body {
        padding: 24px 32px 24px;
      }
    }
    .dialog-content {
      max-height: 740px;
      overflow-y: auto;
    }
  }
</style>
