<template>
  <div class="approve-video-component" id="approve-video-component">
    <link href="https://web.sdk.qcloud.com/player/tcplayer/release/v4.6.0/tcplayer.min.css" rel="stylesheet" />
    <video 
      :id="videoDomId" 
      playsinline 
      webkit-playsinline 
      x5-playsinline
      >
    </video>
    <Watermark 
      ref="watermark" 
      v-if="watermark.textContent" 
      :targetId="watermark.targetId" 
      :text="watermark.textContent"
      :canvasUserOptions="watermark.canvasUserOpt" 
      :wmUserOptions="watermark.wmUserOpt" 
      :isManualInit="false"
    />
  </div>
</template>
<script>
import axios from 'axios'
import { approveOperatesignature, getApproveContentInfo } from 'config/api.conf'
import Watermark from '@/components/watermark.vue'
export default {
  components: {
    Watermark
  },
  props: {
    app_id: {
      type: String,
      default: 'QLearningService'
    },
    content_id: {
      type: String,
      default: ''
    },
    // // 视频播放url
    // source_src: {
    //   type: String,
    //   default: ''
    // },
    show_caption: {
      type: Number,
      default: 0
    },
    corp_name: {
      type: String,
      default: 'tencent'
    },
    getCurPlayTime: {
      type: Boolean,
      default: false
    },
    // 设置播放时间
    playTime: {
      type: Number,
      default: 0
    },
    autoPlay: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      videoDomId: 'approve-video-box-' + Math.random().toString().slice(-6),
      vedioPlayer: null,
      jsLoadCount: 0,
      videoData: {},
      source_src: '',
      captionTypeData: [
        { lang: '中文', captionData: [], subTrack: null },
        { lang: '英文', captionData: [], subTrack: null }
      ],
      curCaptionLang: '中文',
      sendCurTimer: null,
      watermark: {
        targetId: 'approve-video-component', // 水印目标元素id
        textContent: '',
        canvasUserOpt: {
          width: 200,
          height: 160,
          fillStyle: 'rgba(153, 153, 153, 0.1)',
          font: '20px Microsoft Yahei',
          rotateDegree: 39,
          translate: {
            x: 0,
            y: 0
          }
        },
        wmUserOpt: {
          'z-index': 99999
        }
      },
      copyWatermark: null
    }
  },
  watch: {
    content_id(newV) {
      if (newV && this.jsLoadCount === 4) {
        this.getOperatesignature()
      }
    },
    jsLoadCount(newV) {
      if (newV === 4) {
        if (this.content_id) {
          console.log('id', this.content_id)
          this.getOperatesignature()
        } else if (this.source_src) {
          this.initVideo()
        }
      }
    },
    // source_src(newV) {
    //   if (newV) {
    //     this.initVideo()
    //   }
    // },
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          this.watermark.textContent = val.staff_name
        }
      },
      immediate: true
    },
    playTime(newV) {
      this.vedioPlayer.currentTime(newV)
    }
  },
  created() {
    const scrArr = [
      {
        src: '//web.sdk.qcloud.com/player/tcplayer/release/v4.6.0/libs/hls.min.1.1.5.js',
        loadF: true
      },
      {
        src: '//web.sdk.qcloud.com/player/tcplayer/release/v4.6.0/libs/flv.min.1.6.3.js',
        loadF: true
      },
      {
        src: '//web.sdk.qcloud.com/player/tcplayer/release/v4.6.0/libs/dash.all.min.4.4.1.js',
        loadF: true
      },
      {
        src: '//web.sdk.qcloud.com/player/tcplayer/release/v4.6.0/tcplayer.v4.6.0.min.js',
        loadF: true
      }
    ]
    this.handleLoadJs(scrArr)
  },
  methods: {
    pause() {
      this.vedioPlayer && this.vedioPlayer.pause()
    },
    handleLoadJs(scrs) {
      let item = scrs.shift()
      if (item) {
        let _this = this
        let scriptCon = document.createElement('script')
        scriptCon.type = 'text/javascript'
        scriptCon.src = item.src
        document.getElementsByTagName('head')[0].appendChild(scriptCon)
        if (item.loadF) {
          scriptCon.onload = scriptCon.onreadystatechange = () => {
            _this.jsLoadCount++
            _this.handleLoadJs(scrs)
          }
        }
      }
    },
    getOperatesignature() {
      const signatureParams = {
        content_id: this.content_id,
        operate: 'download'
      }
      approveOperatesignature(signatureParams).then((signature) => {
        if (signature) this.getVideoFileInfo(signature)
      })
    },
    getVideoFileInfo(signature) {
      const params = {
        signature,
        app_id: this.app_id,
        content_id: this.content_id
      }
      getApproveContentInfo(this.content_id, params).then((data) => {
        // this.videoData = data
        console.log('视频地址', data)
        this.source_src = data
        // if (data.file_info) {
        this.initVideo()
        this.readCaptionFile()
        // }
      })
    },
    initVideo() {
      let _this = this
      const params = {
        autoplay: this.autoPlay, // 自动播放
        plugins: {
          ContextMenu: {
            levelSwitch: {
              open: true
            }
          }
        }
      }
      if (this.source_src) {
        params.sources = [{ src: this.source_src, type: 'video/mp4' }]
      } else {
        params.fileID = _this.videoData.file_info.vod_id
        params.appID = _this.videoData.file_info.vod_app_id
        params.psign = _this.videoData.file_info.token
      }
      /* eslint-disable*/
      this.vedioPlayer = TCPlayer(this.videoDomId, params)
      this.vedioPlayer.on('ready', function () {
        if (
          _this.show_caption &&
          _this.videoData?.file_attachments.length > 0
        ) {
          _this.videoData.file_attachments.forEach((item) => {
            if (
              item.attachement_type === 'Caption' &&
              (item.title === '中文' || item.title === '英文')
            ) {
              const curCaptionIdx = _this.captionTypeData.findIndex(
                (c) => c.lang === item.title
              )
              _this.captionTypeData[curCaptionIdx].subTrack =
                _this.vedioPlayer.addRemoteTextTrack(
                  {
                    src: item.url,
                    kind: 'subtitles',
                    srclang: item.title === '中文' ? 'zh-cn' : 'en',
                    label: item.title === '中文' ? '中文' : '英文',
                    default: item.title === '中文' ? 'true' : 'false'
                  },
                  true
                )
            }
          })
        }
      })
      this.vedioPlayer.on('play', function (res) {
        // 打开定时器
        const param = {
          evt: 'play',
          duration: _this.vedioPlayer.duration(),
          curTime: _this.vedioPlayer.currentTime()
        }
        _this.$emit('handleRecord', param)
        if (_this.getCurPlayTime) _this.sendCurTime()
      })
      this.vedioPlayer.on('pause', function (res) {
        // 暂停关掉定时器, 播放完也会调用此方法
        const param = {
          evt: 'pause',
          duration: _this.vedioPlayer.duration(),
          curTime: _this.vedioPlayer.currentTime()
        }
        _this.$emit('handleRecord', param)
        clearInterval(_this.sendCurTimer)
      })
      this.vedioPlayer.on('ended', function (res) {
        // 播放完关掉定时器
        const param = {
          evt: 'ended',
          duration: _this.vedioPlayer.duration(),
          curTime: _this.vedioPlayer.currentTime()
        }
        _this.$emit('handleRecord', param)
        clearInterval(_this.sendCurTimer)
      })
      this.$refs.watermark.init()
      if (this.vedioPlayer) {
        this.$nextTick(() => {
          let targetDom = document.getElementById(
            _this.watermark.targetId + '_watermark_xx512'
          )
          let copyDomeId = _this.watermark.targetId + '_watermark_xx513'
          if (_this.copyWatermark == null) {
            _this.copyWatermark = targetDom.cloneNode(true)
            _this.copyWatermark.id = copyDomeId
            _this.copyWatermark.style.position = 'absolute'
            _this.copyWatermark.style.width = '100%'
            _this.copyWatermark.style.height = '100%'
            _this.copyWatermark.style.top = '0'
            let videoBox = document.querySelector(`#${this.videoDomId} video`)
            videoBox.parentNode.insertBefore(_this.copyWatermark, videoBox)
          }
          if (
            document
              .querySelector(`#${this.videoDomId} video`)
              .getAttribute('id')
              .indexOf('html5_api') === -1
          ) {
            document.getElementById(copyDomeId).style.display = 'none'
          }
        })
      }
    },
    readCaptionFile() {
      if (!this.videoData.file_attachments) return
      this.videoData.file_attachments.forEach((item) => {
        if (
          item.attachement_type === 'Caption' &&
          (item.title === '中文' || item.title === '英文')
        ) {
          axios({
            url: item.url,
            method: 'GET'
          }).then((response) => {
            if (response.status === 200 && response.data) {
              let data = response.data?.split('\n\n')
              const captionArr = data?.map((str) => {
                let obj = {}
                const captionItemArr = str.split(/[(\r\n)\r\n]+/)
                captionItemArr.map((e, idx) => {
                  if (idx === 1) {
                    const time = JSON.parse(JSON.stringify(e))
                    obj.startTime = e.split('-->')[0]
                    obj.endTime = e.split('-->')[1]
                    const endTimeCopy = JSON.parse(
                      JSON.stringify(time.split('-->')[1])
                    )
                    const startTimeCopy = JSON.parse(
                      JSON.stringify(time.split('-->')[0])
                    )
                    obj.IntStartTime = startTimeCopy
                      ? this.timeToSec(startTimeCopy)
                      : 0
                    obj.IntEndTime = endTimeCopy
                      ? this.timeToSec(endTimeCopy)
                      : 0
                  }
                  if (idx === 2) obj.caption = e
                })
                return obj
              })
              const curCaptionIdx = this.captionTypeData.findIndex(
                (c) => c.lang === item.title
              )
              this.captionTypeData[curCaptionIdx].captionData = captionArr
              if (this.curCaptionLang === item.title)
                this.$emit('getCurCaption', captionArr) // 字幕语言切换后返回对应的字幕后续扩展，目前只支持一种
            }
          })
        }
      })
    },
    timeToSec(timeStr) {
      let time = timeStr.split(',')[0]
      let s = ''
      const hour = time.split(':')[0]
      const min = time.split(':')[1]
      const sec = time.split(':')[2]
      let misecond = timeStr.split(',')[1].substr(0, 2)
      misecond = Number(parseInt(misecond) / 100)
      s = Number(hour * 3600) + Number(min * 60) + Number(sec)
      return (s * 100 + misecond * 100) / 100
    },
    sendCurTime() {
      let _this = this
      this.sendCurTimer = setInterval(function () {
        const param = {
          evt: 'sendCurTime',
          duration: _this.vedioPlayer.duration(),
          curTime: _this.vedioPlayer.currentTime()
        }
        _this.$emit('handleRecord', param)
      }, 50)
    }
  },
  beforeDestroy() {
    clearInterval(this.sendCurTimer)
  }
}
</script>
<style lang="less" scoped>
.approve-video-component {
  position: relative;
  border-radius: 3px;
  border: 1px solid #ececec;
  width: 325px;
  height: 180px;
  margin-bottom: 15px;
}

:deep(.tcplayer) {
  width: 100%;
  height: 100%;

  video {
    object-fit: contain;
  }
}

:deep(.vjs-poster) {
  background-size: cover;
}
</style>
  