<template>
  <div class="common-card">
    <span class="card-title">
      <span>{{ commonTitle }}</span>
      <span 
        class="hobby-ri" 
        v-if="type === 'NetCourse_GuessYouLike'" 
        @click="handleHobby"
        :dt-eid="dtLikeBtn('eid')"
        :dt-remark="dtLikeBtn('remark')"
      > 
        {{$langue('NetCourse_Preferences', { defaultText: '偏好设置' })}} 
        <span class="hobby-icon"></span>
      </span>
    </span>
    <div class="common-content">
      <div 
      class="card-list" 
      v-for="(e, index) in commonList" 
      :key="index" 
      @click="handleToPath(e)"
      :dt-eid="dtCardList('eid', e)"
      :dt-remark="dtCardList('remark', e)"
      :dt-areaid="dtCardList('areaid', e)" 
      >
        <div class="item-left-img">
          <el-image lazy fit="fill" :src="e.photo_url" class="extend-cover">
            <div class="image-slot" slot="placeholder">
              <i class="el-icon-loading"></i>
            </div>
            <div class="error-cover" slot="error">
              <img :src="formatModuleMap(e.module_id)" alt="" />
            </div>
          </el-image>
          <span class="content-type"><span>{{ showModuleName(e) }}</span></span>
          <span class="time" v-if="showModuleTips(e)">
            {{ showModuleTips(e) }}
          </span>
        </div>
        <div class="item-right-content">
          <div class="content-name overflow-l2">{{ e.content_name }}</div>
          <div class="mid-tag">
            <dynamicsTag v-if="e.rank_tag" :tagData="e.rank_tag" :dtData="dtData(e)" @isShowDynamicsTag="isShowDynamicsTag"></dynamicsTag>
            <div v-if="e.labels?.length" class="tag-box">
              <span class="tag overflow-l1" v-for="(item, i) in e.labels" :key="i">
                {{ item }}
              </span>
            </div>
          </div>
          <div class="bottom-icon" v-if="e.module_id === 3">
            <span class="view-span"><i class="card-time"></i>{{ forMatTime(e) }}</span>
          </div>
          <div class="bottom-icon" v-if="e.module_id !== 3 && !e.rank_tag && isShowTag">
            <span class="view-span">
              <i class="view-icon"></i>
              <span>{{ transformUnitW(e.view_count || 0) }}{{ $langue('NetCourse_Views', { defaultText: '次观看' }) }}</span>
            </span>
            <!-- <span class="view-span" v-if="showScore(e)">
              <i class="score-icon"></i>
              <span>{{ e.avg_score || 0 }}{{ $langue('Mooc_ProjectDetail_Score_Point', { defaultText: '分' }) }}</span>
            </span> -->
          </div>
        </div>
      </div>
      <div class='btn-plain' v-if="type !== 'NetCourse_Extended'">
        <span 
          class="btn" 
          @click="toPage"
          :dt-eid="dtPageBtn('eid')"
          :dt-remark="dtPageBtn('remark')"
        >
          {{ $langue('NetCourse_GoToHome', { defaultText: '去首页探索更多好课' }) }}
        </span>
      </div>
    </div>
    <sdc-sub-label-manage
      class="subLabelDialog" 
      v-if="labelDialogShow" 
      :labelse="labelDialogShow" 
      :labelNodeEnv="labelNodeEnv" 
      :dtArg="dtArg"
      @input="handlerCloseDialog"
      >
    </sdc-sub-label-manage>
  </div>
</template>
<script>
import { transformUnitW, formatModuleMap } from 'utils/tools'
import { actTypes } from '@/utils/moduleMap.js'
import dynamicsTag from '@/components/dynamicsTag'
import env from 'config/env.conf.js'
const envName = env[process.env.NODE_ENV]
export default {
  components: {
    dynamicsTag
  },
  props: {
    commonTitle: {
      type: String,
      default: ''
    },
    commonList: {
      type: Array,
      default: () => ([])
    },
    type: {
      type: String,
      default: ''
    },
    courseData: {
      type: Object,
      default: () => ({})
    },
    dtPageType: {
      type: String,
      default: '网课详情页-新版'
    } 
  },
  data() {
    return {
      transformUnitW,
      formatModuleMap,
      actTypes,
      labelDialogShow: false,
      isShowTag: true,
      labelNodeEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test'
    }
  },
  watch: {
    commonList: {
      immediate: true,
      handler(newVal) {
        const fieldMap = {
          photo_url: ['thumbnail_url'],
          content_name: ['title'],
          content_url: ['href'],
          module_name: ['content_module_name'],
          module_id: ['content_module_id'],
          view_count: ['view_count_uv', 'play_total_count']
          // avg_score: ['avg_score']
        }
        newVal.forEach((item) => {
          for (let targetKey in fieldMap) {
            if (!item.hasOwnProperty(targetKey)) {
              for (let sourceKey of fieldMap[targetKey]) {
                item[targetKey] = item[sourceKey]
              }
            }
          }
          if (this.type === 'NetCourse_RelatedRecommendations') { // 相关推荐
            item.avg_score = item.origin_data.avg_score
            item.duration = item.origin_data.est_dur
            item.rank_tag = item.origin_data.rank_tag
          }
        })
      }
    }
  },
  computed: {
    dtArg() {
      return {
        page: this.courseData.course_name || '',
        page_type: this.dtPageType || '',
        container: '订阅弹窗',
        content_name: '订阅抽奖入口',
        course_id: this.course_id
      }
    },
    course_id() {
      return this.courseData.course_id || parseInt(this.$route.query.course_id)
    },
    showScore() {
      return ({ module_id }) => {
        // 活动文章外链不显示
        // 延伸学习-相关推荐
        return !(['NetCourse_Extended', 'NetCourse_RelatedRecommendations'].includes(this.type) && [4, 8, 99].includes(module_id))
      }
    },
    showModuleName() {
      return (v) => {
        let row = this.actTypes.find((e) => v.module_id === e.module_id)
        return this.$langue(row.langKey, { defaultText: row.act_type_name })
      }
    },
    showModuleTips() {
      return ({ module_id, duration, word_num, origin_data }) => {
        let tips = ''
        if ([1, 2, 4].includes(module_id)) { // 网络课, 面授课，活动
          tips = `${duration || 0}` + this.$langue('Mooc_ProjectDetail_TaskList_Minute', { defaultText: '分钟' })
        } else if ([7, 8].includes(module_id)) { // 案例，文章
          tips = `${word_num || 0}` + this.$langue('Mooc_ProjectDetail_TaskList_Words', { defaultText: '字' })
        } else if ([10].includes(module_id)) { // 培养项目
          tips = `${origin_data?.task_count || 0}` + this.$langue('Mooc_ProjectDetail_BasicInfo_Tasks', { defaultText: '项任务' })
        }
        return tips
      }
    },
    forMatTime() {
      return ({ start_time, end_time }) => {
        const endTime = end_time ? end_time.split(' ')[1] : ''
        return `${start_time} - ${endTime}`
      }
    },
    dtCardList() {
      return (type, row) => {
        let actTypeInfo = this.actTypes.find((e) => row.module_id === e.module_id)
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: this.dtPageType, 
            container: this.commonTitle,
            click_type: 'data',
            content_type: row.module_name,
            content_id: row.item_id,
            content_name: row.content_name,
            act_type: actTypeInfo.act_type || '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${row.item_id}`
        } else {
          return `area_${this.course_id}_${row.item_id}`
        }
      }
    },
    dtLikeBtn() {
      return (type, row) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: this.dtPageType, 
            container: this.commonTitle,
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '偏好设置',
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_偏好设置`
        }
      }
    },
    dtPageBtn() {
      return (type, row) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: this.dtPageType, 
            container: this.commonTitle,
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: '去首页探索更多好课',
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_去首页探索更多好课`
        }
      }
    },
    dtData() {
      return (e) => {
        return {
          page: this.courseData.course_name,
          page_type: '网课详情页-新版', 
          container: this.commonTitle,
          click_type: 'data',
          content_type: '',
          content_id: '',
          content_name: '',
          act_type: '',
          container_id: '',
          page_id: '',
          terminal: 'PC',
          id: e.item_id
        }
      }
    }
  },
  methods: {
    isShowDynamicsTag(val) {
      this.isShowTag = val
    },
    // 偏好设置
    handleHobby() {
      this.labelDialogShow = true
    },
    handlerCloseDialog() {
      this.labelDialogShow = false
    },
    handleToPath(v) {
      if (!v.content_url) return
      window.open(v.content_url)
    },
    toPage() {
      const url = `${envName.courseWoaHost}user/home`
      window.open(url)
    }
  }
}
</script>
<style lang="less" scoped>
  .common-card {
    background: #fff;
    padding: 20px 5px 20px 20px;
    border-radius: 8px;
    // height: 362px;
    .common-content {
      overflow: auto;
      padding: 0px 15px 0 0;
    }
    .card-title {
      font-size: 16px;
      font-weight: bold;
      color: #333333;
      display: inline-block;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .hobby-ri {
        color: #777777;
        display: flex;
        align-items: center;
        margin-right: 15px;
        font-size: 14px;
        font-weight: 400;
        cursor: pointer;
        .hobby-icon {
          background: url("~@/assets/img/hobby-icon.png") no-repeat center /cover;
          width: 18px;
          height: 18px;
          display: inline-block;
          margin-left: 5px;
        }
      }
    }
    .card-list {
      display: flex;
      align-items: flex-start;
      margin-bottom: 16px;
      cursor: pointer;
      .item-left-img {
        position: relative;
        font-size: 12px;
        font-weight: 500;
        width: 128px;
        height: 84px;
        .extend-cover {
          width: 128px;
          height: 84px;
          border-radius: 4px;
          flex-shrink: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          .error-cover img {
            width: 128px;
            height: 84px;
          }
        }
        .content-type {
          position: absolute;
          top: 4px;
          left: 4px;
          background: #0052D9;
          color: #fff;
          padding: 0 4px;
          border-radius: 4px;
          height: 18px;
          display: flex;
          align-items: center;
        }
        .time {
          position: absolute;
          bottom: 4px;
          right: 4px;
          background: #00000099;
          color: #fff;
          padding: 0 4px;
          border-radius: 4px;
          height: 18px;
          display: flex;
          align-items: center;
        }
      }
      .item-right-content {
        margin-left: 16px;
        .content-name {
          color: #333333;
          font-weight: bold;
          line-height: 17px;
          opacity: 0.8;
        }
        .mid-tag {
          margin-top: 2px;
          width: 216px;
          .dynamics-container {
            margin-bottom: 4px;
          }
          .tag-box {
            overflow: hidden;
            height: 22px;
          }
          .tag {
            height: 22px;
            line-height: 22px;
            padding: 0px 6px;
            border-radius: 4px;
            background: #F5F7FA;
            color: #777777;
            margin-right: 8px;
            max-width: 82px;
            display: inline-block;
            flex-shrink: 0;
            font-size: 12px;
          }
        }
        .bottom-icon {
          margin-top: 4px;
          display: flex;
          color: #777777;
          .view-span {
            display: flex;
            align-items: center;
            line-height: 16px;
            font-size: 12px;
          }
          i {
            display: inline-block;
            width: 16px;
            height: 16px;
            margin-right: 4px;
          }
          .view-icon {
            background: url("~@/assets/img/watch.png") no-repeat center /cover;
          }
          .score-icon {
            background: url("~@/assets/img/score-line.png") no-repeat center /cover;
            margin-left: 20px;
          }
          .card-time {
            background: url("~@/assets/img/card-time.png") no-repeat center /cover;
          }
        }
      }
    }
    .btn-plain {
      text-align: center;
      margin-top: 4px;
      .btn {
        display: inline-block;
        height: 32px;
        line-height: 32px;
        border-radius: 4px;
        background: #F2F8FF;
        color: #0052D9;
        padding: 0 16px;
        cursor: pointer;
        font-size: 12px;
      }
    }
  }
  :deep(.subLabelDialog) {
    .el-form .el-form-item__content {
      line-height: 40px;
    }
  }
</style>
