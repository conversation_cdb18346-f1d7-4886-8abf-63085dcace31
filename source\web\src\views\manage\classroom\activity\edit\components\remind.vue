<template>
  <div class="remind">
    <el-dialog
      :visible.sync="visible"
      custom-class="c-dialog"
      :before-close="closeDialog"
      :close-on-click-modal="false"
      width="725px"
    >
      <template slot="title">
        <span class="title">{{ isShowStartMode ? '问卷启动催办设置' : '催办设置' }}</span>
      </template>
      <div class="remind-content">
        <start-set v-if="isShowStartMode" ref="startSet" :startMode="startMode" />

        <el-form :model="form" :rules="rules" ref="form">
          <el-form-item label="是否开启催办">
            <el-switch v-model="form.enabled_remaind"></el-switch>
          </el-form-item>

          <el-form-item label="催办时间 :" prop="enabled_remaind">
            <div class="reminder-time">
              <div class="cycle-time">
                <el-checkbox
                  v-model="checked1"
                  @change="checkBoxChange($event, 1)"
                  :disabled="!form.enabled_remaind"
                >按周期时间催办</el-checkbox>
                <div class="cycle-time-content" v-if="checked1">
                  <!-- <el-form-item label="起止催办时间 :" label-width="105" class="pub-m-b-16 start-end-time" prop="date">
                    <el-date-picker v-model="form.date" size="small" type="daterange" format="yyyy-MM-dd"
                      value-format="yyyy-MM-dd HH:mm:ss" :default-time="['00:00:00', '23:59:59']" range-separator="至"
                      start-placeholder="开始日期" end-placeholder="结束日期" :disabled="!form.enabled_remaind">
                    </el-date-picker>
                  </el-form-item>-->

                  <el-form-item label="催办频率 :" label-width="105">
                    <div class="pub-m-l-76">
                      <div class="row-box pub-m-b-16">
                        <el-radio
                          v-model="frequency"
                          label="1"
                          @change="frequencyChange($event, 1)"
                          :disabled="!form.enabled_remaind"
                        >每</el-radio>
                        <el-input-number
                          v-model="form.reminder_gap_time"
                          class="width-88"
                          size="small"
                          controls-position="right"
                          :min="frequency === '1' ? 1 : 0"
                          :max="9999"
                          :disabled="!form.enabled_remaind || frequency !== '1'"
                        ></el-input-number>
                        <span class="tips pub-m-l-12 pub-m-r-12">天</span>
                        <el-time-picker
                          v-model="form.time_1"
                          class="width-120"
                          size="small"
                          :picker-options="{
                            selectableRange: '9:00:00 - 23:59:59'
                          }"
                          value-format="HH:mm:ss"
                          placeholder="任意时间点"
                          :disabled="!form.enabled_remaind || frequency !== '1'"
                        ></el-time-picker>
                        <span class="tips pub-m-l-12">进行催办</span>
                      </div>

                      <div class="row-box">
                        <el-radio
                          v-model="frequency"
                          label="2"
                          @change="frequencyChange($event, 2)"
                          :disabled="!form.enabled_remaind"
                        >每周</el-radio>
                        <el-select
                          v-model="form.reminder_fixed_week_time"
                          class="width-114 pub-m-r-12"
                          size="small"
                          placeholder="请选择"
                          :disabled="!form.enabled_remaind || frequency !== '2'"
                        >
                          <el-option
                            v-for="item in weekList"
                            :key="item.val"
                            :label="item.label"
                            :value="item.val"
                          ></el-option>
                        </el-select>
                        <el-time-picker
                          v-model="form.time_2"
                          size="small"
                          class="width-120"
                          :picker-options="{
                        selectableRange: '9:00:00 - 23:59:59'
                      }"
                          value-format="HH:mm:ss"
                          placeholder="任意时间点"
                          :disabled="!form.enabled_remaind || frequency !== '2'"
                        ></el-time-picker>
                        <span class="tips pub-m-l-12">进行催办</span>
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </div>

              <div class="fixed-time">
                <el-checkbox
                  v-model="checked2"
                  @change="checkBoxChange($event, 2)"
                  :disabled="!form.enabled_remaind"
                >按固定时间催办</el-checkbox>
                <div class="fixed-time-content" v-if="checked2">
                  <el-row
                    class="pub-flex pub-m-b-12"
                    v-for="(item, index) in addWeekList"
                    :key="item.id"
                  >
                    <el-date-picker
                      v-model="item.value"
                      size="small"
                      format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      type="datetime"
                      placeholder="选择日期时间"
                      :disabled="!form.enabled_remaind"
                      :picker-options="dynamicFixTimeOptions"
                      :disabled-hours="disabledHours"
                      :disabled-minutes="disabledMinutes"
                      :disabled-seconds="disabledSeconds"
                    ></el-date-picker>
                    <div
                      class="btn-box add-btn pub-row-center pub-col-center"
                      v-if="index === addWeekList.length - 1"
                      @click="addWeekItem"
                      :class="{'disabled': !form.enabled_remaind}"
                    ></div>
                    <div
                      class="btn-box remove-btn pub-row-center pub-col-center"
                      v-if="addWeekList.length > 1"
                      @click="removeWeekItem(item.id)"
                      :class="{'disabled': !form.enabled_remaind}"
                    ></div>
                  </el-row>
                  <div style="color: #00000066;
                    font-family: 'PingFang SC';
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 22px;"
                  >最多配置5个催办时间</div>
                </div>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="催办渠道" prop="messageType">
            <el-checkbox-group v-model="form.message_type" :disabled="!form.enabled_remaind">
              <el-checkbox v-for="(item) in messageTypeList" :label="item.type" :key="item.id">
                <span>{{ item.label }}</span>
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="close" class="btn1">取消</el-button>
        <el-button type="primary" @click="submit" class="btn2">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import startSet from './startMode/startSet.vue'
export default {
  components: {
    startSet 
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    remindInfo: {
      type: Object,
      default: () => {}
    },
    isShowStartMode: {
      type: Boolean,
      default: false
    },
    startMode: {
      type: Number,
      default: 1
    },
    time: {
      type: Object,
      default: () => {
        return {
          start: '',
          end: ''
        }
      }
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(val) {
        this.setData(this.remindInfo)
      }
    }
  },
  data() {
    return {
      form: {
        enabled_remaind: false,
        reminder_gap_time: '', // 间隔天数
        reminder_send_times: '', // 间隔天数的时间
        reminder_fixed_week_time: '', // 每周的时间
        message_type: [],
        // date: [],
        time_1: '',
        time_2: '',
        reminder_task_id: ''
      },
      checked1: false,
      checked2: false,
      weekList: [
        { label: '周一', val: 1 },
        { label: '周二', val: 2 },
        { label: '周三', val: 3 },
        { label: '周四', val: 4 },
        { label: '周五', val: 5 },
        { label: '周六', val: 6 },
        { label: '周日', val: 7 }
      ],
      addWeekList: [
        {
          id: 0,
          value: ''
        }
      ],
      frequency: '0',
      rules: {
        message_type: [
          {
            validator: this.messageTypeValidator,
            trigger: 'change',
            required: true
          }
        ],
        enabled_remaind: [
          {
            validator: this.remindTimeValidator,
            trigger: 'change',
            required: true
          }
        ],
        // date: [
        //   { validator: this.dateValidator, trigger: 'change', required: true }
        // ],
        messageType: [
          {
            validator: this.messageTypeValidator,
            trigger: 'change',
            required: true
          }
        ]
      },
      messageTypeList: [
        { id: '1', label: '邮件', type: 'mail', moduleName: 'remind' },
        {
          id: '2',
          label: '企业微信TIPS',
          type: 'tips',
          moduleName: 'remind'
        },
        {
          id: '3',
          label: '企业微信机器人',
          type: 'bot',
          moduleName: 'remind'
        }
      ],
      operations: 1
    }
  },
  computed: {
    // 动态计算时间选择器的配置
    dynamicFixTimeOptions() {
      return {
        disabledDate: this.remindDisableRange
      }
    }
  },
  methods: {
    remindDisableRange(time) {
      // 如果没有传结束时间，不禁用
      if (!this.time.end) return false
      
      // 获取结束时间的日期部分（年月日）
      const endTime = new Date(this.time.end)
      const endDate = new Date(endTime.getFullYear(), endTime.getMonth(), endTime.getDate())
      
      // 获取当前选择时间的日期部分（年月日）
      const currentDate = new Date(time.getFullYear(), time.getMonth(), time.getDate())
      
      // 只禁用结束时间之前的日期，结束时间当天不禁用
      return currentDate.getTime() < endDate.getTime()
    },
    
    // 禁用小时的方法
    disabledHours() {
      if (!this.time.end) return []
      
      const endTime = new Date(this.time.end)
      const endHour = endTime.getHours()
      
      // 检查当前是否有选中的日期是结束时间当天
      for (let item of this.addWeekList) {
        if (item.value) {
          const selectedDate = new Date(item.value)
          const endDate = new Date(endTime.getFullYear(), endTime.getMonth(), endTime.getDate())
          const selectedDateOnly = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate())
          
          // 如果选中的是结束时间当天
          if (selectedDateOnly.getTime() === endDate.getTime()) {
            // 禁用结束时间之前的小时（不包括结束时间的小时）
            return Array.from({ length: endHour }, (_, i) => i)
          }
        }
      }
      
      return []
    },
    
    // 禁用分钟的方法
    disabledMinutes(hour) {
      if (!this.time.end) return []
      
      const endTime = new Date(this.time.end)
      const endHour = endTime.getHours()
      const endMinute = endTime.getMinutes()
      
      // 检查当前是否有选中的日期是结束时间当天
      for (let item of this.addWeekList) {
        if (item.value) {
          const selectedDate = new Date(item.value)
          const endDate = new Date(endTime.getFullYear(), endTime.getMonth(), endTime.getDate())
          const selectedDateOnly = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate())
          
          // 如果选中的是结束时间当天
          if (selectedDateOnly.getTime() === endDate.getTime()) {
            if (hour === endHour) {
              // 在结束时间的小时内，禁用结束时间之前的分钟（不包括结束时间的分钟）
              return Array.from({ length: endMinute }, (_, i) => i)
            } else if (hour < endHour) {
              // 在结束时间之前的小时，禁用所有分钟
              return Array.from({ length: 60 }, (_, i) => i)
            }
          }
        }
      }
      
      return []
    },
    
    // 禁用秒数的方法
    disabledSeconds(hour, minute) {
      if (!this.time.end) return []
      
      const endTime = new Date(this.time.end)
      const endHour = endTime.getHours()
      const endMinute = endTime.getMinutes()
      const endSecond = endTime.getSeconds()
      
      // 检查当前是否有选中的日期是结束时间当天
      for (let item of this.addWeekList) {
        if (item.value) {
          const selectedDate = new Date(item.value)
          const endDate = new Date(endTime.getFullYear(), endTime.getMonth(), endTime.getDate())
          const selectedDateOnly = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), selectedDate.getDate())
          
          // 如果选中的是结束时间当天
          if (selectedDateOnly.getTime() === endDate.getTime()) {
            if (hour === endHour && minute === endMinute) {
              // 在结束时间的小时和分钟内，禁用结束时间之前的秒数（不包括结束时间的秒数）
              return Array.from({ length: endSecond }, (_, i) => i)
            } else if (hour < endHour || (hour === endHour && minute < endMinute)) {
              // 在结束时间之前的时间，禁用所有秒数
              return Array.from({ length: 60 }, (_, i) => i)
            }
          }
        }
      }
      
      return []
    },
    closeDialog() {
      this.$emit('update:visible', false)
    },
    remindTimeValidator(rules, value, callback) {
      if (!this.form.enabled_remaind) {
        callback()
        return
      }

      if (this.form.enabled_remaind) {
        if (!this.checked1 && !this.checked2) {
          callback(new Error('请选择催办时间'))
        }

        if (this.checked1) {
          if (this.frequency === '1') {
            if (!this.form.reminder_gap_time || !this.form.time_1) {
              callback(new Error('请选择天数和时间'))
            }
          } else {
            if (!this.form.reminder_fixed_week_time || !this.form.time_2) {
              callback(new Error('请选择每周的时间和时间'))
            }
          }
        }

        if (this.checked2) {
          if (
            this.addWeekList.length === 0 ||
            this.addWeekList.some(e => !e.value)
          ) {
            callback(new Error('请添加固定时间'))
          }
          
          // 检查固定时间是否在活动结束时间之后
          if (this.time.end) {
            const endTime = new Date(this.time.end)
            for (let item of this.addWeekList) {
              if (item.value) {
                const selectedTime = new Date(item.value)
                if (selectedTime.getTime() < endTime.getTime()) {
                  callback(new Error('催办时间不能早于活动结束时间'))
                  return
                }
              }
            }
          }
        }
        callback()
      } else {
        callback()
      }
    },
    messageTypeValidator(rules, value, callback) {
      if (!this.form.enabled_remaind) {
        callback()
        return
      }

      if (this.form.enabled_remaind && this.form.message_type.length === 0) {
        callback(new Error('请选择催办渠道'))
      } else {
        callback()
      }
    },
    checkBoxChange(e, t) {
      if (e && t === 1) {
        this.frequency = '1'
        this.form.time_1 = '09:00:00'
      } else if (!e && t === 1) {
        // this.form.date = []
        this.form.reminder_gap_time = ''
        this.form.reminder_fixed_week_time = ''
        this.form.reminder_send_times = ''
        this.frequency = '0'
        this.form.time_1 = ''
        this.form.time_2 = ''
        // this.$nextTick(() => {
        //   this.$refs.form.clearValidate(['date'])
        // })
      } else if (!e && t === 2) {
        this.addWeekList = [{ id: 0, value: '' }]
        this.form.reminder_fixed_send_times = []
        this.$nextTick(() => {
          this.$refs.form.clearValidate(['enabled_remaind'])
        })
      }
      
      // 如果是选择固定时间催办，为第一个时间选择器设置默认值
      if (e && t === 2) {
        let defaultValue = ''
        if (this.time.end) {
          const endTime = new Date(this.time.end)
          // 设置为活动结束时间
          const defaultTime = new Date(endTime)
          
          // 格式化为 yyyy-MM-dd HH:mm:ss
          const year = defaultTime.getFullYear()
          const month = String(defaultTime.getMonth() + 1).padStart(2, '0')
          const day = String(defaultTime.getDate()).padStart(2, '0')
          const hour = String(defaultTime.getHours()).padStart(2, '0')
          const minute = String(defaultTime.getMinutes()).padStart(2, '0')
          const second = String(defaultTime.getSeconds()).padStart(2, '0')
          
          defaultValue = `${year}-${month}-${day} ${hour}:${minute}:${second}`
        }
        
        this.addWeekList = [{ id: 0, value: defaultValue }]
      }
      this.$nextTick(() => {
        this.$refs.form.validate()
      })
    },
    frequencyChange(e, t) {
      if (e && t === 1) {
        this.form.reminder_gap_time = 1
        this.form.reminder_fixed_week_time = ''
        if (!this.form.time_1) {
          this.form.time_1 = '09:00:00'
        }
        this.form.time_2 = null
      } else if (e && t === 2) {
        this.form.reminder_gap_time = null
        this.form.reminder_fixed_week_time = 1
        if (!this.form.time_2) {
          this.form.time_2 = '09:00:00'
        }
        this.form.time_1 = null
      }
    },
    // 添加周期项
    addWeekItem() {
      if (this.addWeekList.length >= 5) {
        this.$message.warning('最多只能添加5个催办时间')
        return
      }
      ++this.operations
      
      this.addWeekList.push({
        id: this.operations,
        value: ''
      })
    },
    // 删除周期项
    removeWeekItem(id) {
      if (this.addWeekList.length <= 1) return
      let index = this.addWeekList.findIndex(e => e.id === id)
      this.addWeekList.splice(index, 1)
    },
    close() {
      this.$emit('update:visible', false)
    },
    setData(data) {
      const {
        enabled_remaind = false,
        message_type,
        // reminder_begin_time,
        // reminder_end_time,
        reminder_send_times,
        reminder_fixed_week_time,
        reminder_gap_time,
        reminder_fixed_send_times,
        reminder_task_id
      } = data
      this.form.enabled_remaind = enabled_remaind
      this.form.message_type = message_type ? message_type.split(';') : []
      this.form.reminder_task_id = reminder_task_id
      // this.form.date = reminder_begin_time && reminder_end_time
      //   ? [reminder_begin_time, reminder_end_time]
      //   : []
      this.form.reminder_fixed_week_time = reminder_fixed_week_time
      this.form.reminder_gap_time = reminder_gap_time
      if (!!reminder_gap_time || !!reminder_fixed_week_time) {
        this.checked1 = true
        if (reminder_gap_time) {
          this.frequency = '1'
          this.form.time_1 = reminder_send_times
        } else if (reminder_fixed_week_time) {
          this.frequency = '2'
          this.form.time_2 = reminder_send_times
        }
      }
      if (reminder_fixed_send_times && reminder_fixed_send_times.length) {
        this.checked2 = true
        this.addWeekList = []
        reminder_fixed_send_times.split(';').forEach((v, i) => {
          this.addWeekList.push({
            id: i,
            value: v
          })
          this.operations = this.addWeekList[this.addWeekList.length - 1].id + 1
        })
        this.form.reminder_fixed_send_times = reminder_fixed_send_times.split(
          ';'
        )
      }
    },
    submit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          let emitData = {
            frequency: this.frequency, // 每周 or 每隔
            addWeekList: this.addWeekList,
            ...this.form
          }
          let dto = {
            remindData: emitData
          }
          if (this.isShowStartMode) {
            dto.startMode = this.$refs.startSet.getStartMode()
          }
          this.$emit('update:visible', false)
          this.$emit('getRemindInfo', dto)
        }
      })
    }
    // dateValidator(rule, value, callback) {
    //   if (!this.form.enabled_remaind) {
    //     callback()
    //     return
    //   }

    //   if (
    //     this.checked1 &&
    //     (!this.form.date ||
    //       (Array.isArray(this.form.date) && this.form.date.length === 0))
    //   ) {
    //     callback(new Error('请选择起止催办时间'))
    //   } else {
    //     callback()
    //   }
    // }
  }
}
</script>

<style lang="less" scoped>
.remind {
  :deep(.el-dialog) {
    border-radius: 9px;
    .el-dialog__header {
      padding: 24px 32px;
      display: flex;
      align-items: center;
    }
    .el-dialog__headerbtn {
      position: unset;
      margin-left: auto;
    }
    .el-dialog__footer {
      padding: 30px 32px 24px 0;
    }
  }
  .c-dialog {
    .title {
      display: flex;
      align-items: center;
      font-size: 20px;
      font-weight: 600;
      line-height: 24px;
      font-size: 16px;
    }
  }
  .remind-content {
    .reminder-time {
      .cycle-time {
        margin-bottom: 16px;
        .cycle-time-content {
          padding: 16px 24px 16px 16px;
          background: #fbfbfb;
          margin-top: 12px;
          .start-end-time {
            :deep(.el-date-editor) {
              width: 280px;
              .el-range-separator {
                width: unset;
              }
            }
          }
        }
      }

      .fixed-time {
        margin-left: 86px;
        .fixed-time-content {
          margin: 10px 0 0 26px;
          .btn-box {
            width: 32px;
            height: 32px;
            border-radius: 3px;
            opacity: 1;
            border: 1px dashed #dcdcdc;
            background: rgba(255, 255, 255, 1);
            font-weight: 700;
            font-size: 16px;
            cursor: pointer;
            margin-left: 8px;

            &.disabled {
              cursor: not-allowed;
              opacity: 0.5;
              pointer-events: none;
            }
          }

          .add-btn {
            position: relative;

            &::before {
              content: '';
              width: 9px;
              height: 1.7px;
              background: #000000e6;
              position: absolute;
              top: 16px;
              left: 11px;
            }

            &::after {
              content: '';
              width: 9px;
              height: 1.3px;
              background: #000000e6;
              position: absolute;
              top: 16px;
              left: 11px;
              transform: rotate(90deg);
            }
          }

          .remove-btn {
            position: relative;
            font-size: 1px;

            &::after {
              content: '';
              width: 9px;
              height: 1.3px;
              background: #000000e6;
              position: absolute;
              top: 16px;
              left: 11px;
            }
          }
        }
      }

      .tips {
        color: rgba(0, 0, 0, 0.4);
      }
    }
  }

  .dialog-footer {
    display: flex;
    align-items: center;
    height: 100%;
    width: 100%;
    padding: 0;
    .btn1,
    .btn2 {
      height: 32px;
      width: 60px;
      border-radius: 3px;
      text-align: center;
      line-height: 32px;
      padding: 0;
    }
    .btn1 {
      margin-left: auto;
    }
  }
  :deep(.el-dialog__body) {
    padding: 23px 38px 0 32px;
  }
  :deep(.el-form-item__error) {
    margin-left: 79px;
  }

  :deep(.el-checkbox.is-disabled),
  :deep(.el-radio.is-disabled),
  :deep(.el-input.is-disabled),
  :deep(.el-select.is-disabled),
  :deep(.el-date-editor.is-disabled),
  :deep(.el-time-picker.is-disabled) {
    opacity: 0.5;
    cursor: not-allowed;
  }
}
.pub-m-l-76 {
  margin-left: 76px;
}

.row-box {
  margin-bottom: 8px;
}

.width-114 {
  width: 114px;
}

.width-120 {
  width: 114px;
}

.pub-m-r-12 {
  margin-right: 12px;
}

.pub-m-b-16 {
  margin-bottom: 16px;
}

.pub-m-l-12 {
  margin-left: 12px;
}
.width-88 {
  width: 88px;
}
.pub-flex {
  display: flex;
}
.pub-m-b-12 {
  margin-bottom: 12px;
}

.pub-row-center {
  display: flex;
  justify-content: center;
}

.pub-row-between {
  display: flex;
  justify-content: space-between;
}

.pub-col-center {
  align-items: center;
}
</style>
