import {
  DataHttp
} from 'sdc-core'
import {
  app
} from './index'
import Vue from 'vue'
import router from '../router'
// import {
//   Message
// } from 'element-ui'

let headers = {}
let MessageAllVisiableList = ['api/activity/user/get-survey']
export default DataHttp.getInstance({
  // 当前应用上下文
  app,
  // 开启应用上下文
  ctx: true,
  // 当前请求实例(可使用DataHttp构建，也可配置详细参数，参考axios配置)
  axios: DataHttp.create({
    withCredentials: true,
    headers: {
      retry: 0,
      ...headers
    },
    // 请求拦截器
    async request(request) {
      let lang = localStorage.getItem('sdc-sys-def-lang')
      request.headers.lang = lang
      // return request
      
      try {
        if (window.$informationReview) {
          const res = await window.$informationReview.reviewRouteIntercept(request.url, request.method, request.data)
          if (!res.success) return Promise.reject({ code: 500, message: res.error_msg, ...res })

          if (res.status_code === 1) {
            return Promise.reject({ code: 500, message: res.error_msg, ...res })
          } else if (res.status_code === -1) {
            request.data = res.data
          }
        }
        return request
      } catch (error) {
        return Promise.reject(error)
      }
    },
    response(res) {
      if (res.config.url.indexOf('api/v1/content/vh/vh-result') === -1) {
        if (res.data.code === 200 && !res.data.success) {
          res.data.code = 500
        }
      }
      if (res.data.code === 0) {
        res.data.code = 200
      }

      if ((res.data.code === 200 && res.data.success) && window.$informationReview) {
        window.$informationReview.reviewResults && window.$informationReview.reviewResults(res.config.url, res.config.method, res.config.data)
      }
      return res
    },
    // 错误处理
    reject (err) {
      return err
    }
  }),
  // 数据响应映射(当返回数据不满足{status,result,message}格式时，对返回数据字段映射)
  map: {
    status: 'code',
    result: 'data',
    message: 'message',
    value: 200
  },
  // 全局错误处理函数(err是包含title，message的对象，app为应用上下文)
  error(err) {
    if (err?.title && err?.title.indexOf('task/get_course_progress') !== -1) {
      console.log(err, 'get_course_progress err')
    }
    // 导师认证页面没权限则页面提示无权限 不弹提示
    if (err?.title && err?.title.indexOf('api/tutor/user/status') !== -1) {
      console.log('err: ', err)
    } else if (err.code === 403) {
      window.$qlCommonHeader && window.$qlCommonHeader.destroy()
      let chArr = document.body.getElementsByClassName('common-header')
      if (chArr?.length > 0) {
        for (let i = 0; i < chArr.length; i++) {
          if (chArr[i] !== null) chArr[i].parentNode.removeChild(chArr[i])
        }
      }
      router.replace({
        name: '401'
      })
      sessionStorage.setItem('401Msg', err.message)
    } else if (err.code === 404) {
      router.replace({
        name: 'not_exist'
      })
    } else {
      if (err.code === -50006 || err.code === -50005) return

      if (MessageAllVisiableList.every(v => err.title.indexOf(v) !== -1) && err?.code === 500) {
        Vue.prototype.$message.error({
          message: err.message,
          duration: 0
        })
      }
      // 上报接口不拦截提示
      let records = ['graphic/view_graphic/record', 'student/save-learn-record', 'task/save_learn_record', 'courseinfo/add-study-record', 'info/study/record', 'mooc/user/courseinfo/add-register', 'api/area/user/page/detail', 'api/area/user/page/recommend/discover']
      if (err?.title && records.every(v => err.title.indexOf(v) === -1)) {
        let message = ''
        if (err.code === undefined && err.response?.data?.code) {
          const data = err.response?.data
          message = data?.code !== 200 ? data.message : '网络异常，请稍后重试！'
        } else {
          message = err.code && err.code !== 200 ? (err.message || err.data) : '网络异常，请稍后重试！'
        }
        if (message && JSON.stringify(message) !== '{}') {
          Vue.prototype.$message.error({
            message,
            duration: 2000
          })
        }
      }
    }
  }
})
