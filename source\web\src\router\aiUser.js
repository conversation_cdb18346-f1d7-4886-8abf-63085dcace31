import net from './net'
import face from './face'
const aiUser = [
  // 创作者中心
  {
    path: '/creator-center',
    name: 'creator-center',
    component: () => import('views/user/creator-center/index.vue'),
    meta: {
      title: '创作中心'
    },
    children: [
      {
        path: 'graphic-page',
        name: 'graphic-page',
        component: () => import('views/user/creator-center/graphic-page.vue'),
        meta: {
          title: '我的文章',
          isAuth: true
        }
      },
      {
        path: 'course-list',
        name: 'courseList',
        component: () => import('views/user/netcourse/online-course/index'),
        meta: {
          title: '我的在线课程_Q-Learning',
          isAuth: true
        }
      },
      {
        path: 'course-share',
        name: 'courseShare',
        component: () => import('views/user/netcourse/make-share/index'),
        meta: {
          title: '在线课程制作和分享'
        }
      },
      {
        path: 'course-quick',
        name: 'courseQuick',
        component: () => import('views/user/netcourse/quick-class/index'),
        meta: {
          title: 'AI快捷做课'
        }
      },
      {
        path: 'course-ppt',
        name: 'pptCourse',
        component: () => import('views/user/netcourse/course-make/video-PPT/index'),
        meta: {
          title: 'AI快捷做课_Q-Learning'
        }
      },
      {
        path: 'course-2d',
        name: '2DCourse',
        component: () => import('views/user/netcourse/course-make/video-2D/index'),
        meta: {
          title: 'AI快捷做课_Q-Learning'
        }
      },
      {
        path: 'course-publish',
        name: 'aiCoursePublish',
        component: () => import('views/user/netcourse/course-make/publish/index'),
        meta: {
          title: '发布在线课程_Q-Learning'
        }
      },
      {
        path: 'course-upload',
        name: 'couserUpload',
        component: () => import('views/user/netcourse/course-make/directly-upload/index'),
        meta: {
          title: '上传在线课程_Q-Learning'
        }
      }
    ]
  },
  // 文章用户端
  {
    path: '/graphic',
    name: 'graphic',
    component: () => import('views/user/index.vue'),
    meta: {
      title: '文章'
    },
    children: [
      {
        path: 'user/create',
        name: 'create',
        component: () => import('views/user/graphic/create.vue'),
        meta: {
          title: '文章',
          keepAlive: true
        }
      },
      {
        path: 'user/preview',
        name: 'preview',
        component: () => import('views/user/graphic/preview.vue'),
        meta: {
          title: '文章'
        }
      },
      // 极客文章
      {
        path: '/outsourcedCourse/graphic/play',
        name: 'graphicPlay',
        component: () => import('@/views/user/outsourced-course/graphic'),
        meta: {
          title: '文章'
        }
      },
      // 网课
      ...net,
      // 面授课
      ...face
    ]
  },
  {
    path: '/netcourse/show',
    name: 'netcourseShow',
    component: () => import('@/views/user/netcourse/show/index.vue'),
    meta: {
      title: '网络课'
    }
  }
]
export default aiUser
