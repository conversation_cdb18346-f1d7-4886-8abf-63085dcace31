import http from 'utils/http'
import env from 'config/env.conf.js'

const envName = env[process.env.NODE_ENV]
// 标签接口区分woa oa
let tagUrl
if (window.location.hostname.endsWith('.woa.com')) {
  tagUrl = envName.courseWoaHost
} else {
  tagUrl = envName.courseHost
}

// -----mooc项目后台页面接口--------
// 获取登陆用户信息
export const getLoginUser = () => http.get(`${envName.commonPath}training-portal-common/api/v1/portal/user/common/loginuser`)
// 用户部门权限
export const getUserLimits = () => http.get(`${envName.trainingPath}api/mooc/user/courseinfo/get-user-roles`)
// 导航数据
export const getNavData = (params) => http.get(`${envName.commonPath}training-portal-area/api/area/user/menu/A9BiosXihR0h46ThNsAX`, { params })
// 导航搜索
export const searchMooc = (params) => http.get(tagUrl + `portal-qlearning-adapter/api/user/search/getAssociateKeywordsNew`, { params })
// -----mooc项目管理页面接口--------
// 获取项目详情
export const getProjectDetail = (mooc_course_id) => http.get(`${envName.trainingPath}api/mooc/manage/courseinfo/get-course-simple-info/${mooc_course_id}`, { loading: true })
// 获取任务树
export const taskTreeList = (id) => http.get(`${envName.trainingPath}api/mooc/manage/task/task-tree/${id}`, { loading: true })
// 保存任务树
export const saveTreeData = (params, id) => http.post(`${envName.trainingPath}api/mooc/manage/task/task-tree/${id}`, { params, loading: true, origin: true })
// 保存单个任务
export const saveSingleData = (params) => http.post(`${envName.trainingPath}api/mooc/manage/task/task-info`, { params, loading: true })
// 获取单个任务
export const getSingleTaskInfo = (id) => http.get(`${envName.trainingPath}api/mooc/manage/task/task-info/${id}`, { loading: true })
// 课程分类
export const getCourseClassifyAPI = (params) => http.get(`${envName.trainingPath}api/mooc/manage/`, { params, loading: true })
// 考试列表
export const getExamList = (params) => http.post(`${envName.trainingPath}api/businessCommon/common/exam/exam-list`, { params, loading: true })
// 练习列表
export const getPracticeList = (params) => http.post(`${envName.trainingPath}api/businessCommon/common/exam/practice-list`, { params, loading: true })
// 设置学习模式
export const setLockByStep = (params) => http.post(`${envName.trainingPath}api/mooc/manage/courseinfo/set-unlocked-by-step`, { params, loading: false })
// 素材列表-管理端
export const getMaterialList = (params) => http.post(`${envName.trainingPath}api/businessCommon/manage/pub-file/get-file-list`, { params, loading: true })
// 素材列表-用户端
export const getMaterialUserList = (params) => http.post(`${envName.trainingPath}api/businessCommon/user/pub-file/get-file-list`, { params, loading: true })
// 素材观看页
export const getMateriaDetail = (id) => http.get(`${envName.trainingPath}api/businessCommon/manage/pub-file/file/${id}`, { loading: true })
// 素材观看页-用户端  2024-12-25 新增
export const getMateriaDetailUser = (id) => http.get(`${envName.trainingPath}api/businessCommon/user/pub-file/file/${id}`, { loading: true })
// 素材删除
export const deleteMateria = (id) => http.del(`${envName.trainingPath}api/businessCommon/manage/pub-file/file/${id}`, { loading: true })
// 素材重新转码
export const reTransCode = (id) => http.get(`${envName.trainingPath}api/businessCommon/user/pub-file/reTransCode/${id}`, { loading: true })
// 素材引用列表
export const materialQuoteList = (id) => http.get(`${envName.trainingPath}api/businessCommon/user/pub-file/pub-file-refer/${id}`, { loading: true })
// 加精
export const addGoods = (params) => http.post(`${envName.trainingPath}api/mooc/manage/courseinfo/save-excellent_status`, { params, loading: true })

// 素材章节
export const getChapterListApi = (id) => http.get(`${envName.trainingPath}api/businessCommon/user/pub-file/get-chapter-list/${id}`, { loading: true })

// ---报名管理---
export const saveApplyManage = (params) => http.post(`${envName.trainingPath}api/mooc/manage/courseinfo/save-register-info`, { params, loading: true })
export const applyManageInfo = (id) => http.get(`${envName.trainingPath}api/mooc/manage/courseinfo/get-register-info/${id}`, { loading: true })

// ---学员管理---
// 学院列表
export const studyManageList = (params) => http.get(`${envName.trainingPath}api/mooc/manage/student/get-student-list`, { params, loading: true })
// 添加学员
export const addStudent = (params) => http.post(`${envName.trainingPath}api/mooc/manage/student/add-student`, { params, loading: true })
// 自动加入学员
export const autoAddStudent = (params) => http.post(`${envName.trainingPath}api/mooc/manage/setting/student-auto-join`, { params, loading: true })
// 获取学员自动加入规则
export const getStudentAutoJoin = (params) => http.get(`${envName.trainingPath}api/mooc/manage/setting/get-student-auto-join`, { params, loading: true })
// 退出学员记录
export const outRecords = (params) => http.get(`${envName.trainingPath}api/mooc/manage/student/get-student-cancel-list`, { params, loading: true })
// 导出学员列表
export const exportStudentData = (params) => http.get(`${envName.trainingPath}api/mooc/manage/student/export-student-list`, { params, loading: true })
// 消息模板
export const infoTemplate = (params) => http.get(`${envName.trainingPath}api/mooc/manage/student/get-project-msg-template`, { params, loading: true })
// 模板修改
export const templateEdit = (params) => http.put(`${envName.trainingPath}api/mooc/manage/student/update-project-msg-template`, { params, loading: true })
// 批量延期学员
export const batchDelayStudent = (params) => http.post(`${envName.trainingPath}api/mooc/manage/student/delayed-student`, { params, loading: true })
// 批量移出
export const batchDelete = (params) => http.post(`${envName.trainingPath}api/mooc/manage/student/delete-student`, { params, loading: true })
// 手动添加
export const handAddStudent = (params) => http.post(`${envName.trainingPath}api/mooc/manage/student/target-add-student`, { params, loading: true })
// -----mooc项目列表-------
export const projectList = (params) => http.get(`${envName.trainingPath}api/mooc/manage/courseinfo/get-course-list`, { params, loading: true })
export const copyProjectList = (params) => http.post(envName.trainingPath + 'api/mooc/manage/courseinfo/copy-course-info ', { params, loading: false })
// 结束--发布项目 0:待发布  1:已发布   2:已下架
export const endProject = (params) => http.post(envName.trainingPath + 'api/mooc/manage/courseinfo/save-course-status', { params, loading: false })
// 上架
export const activateProject = (id) => http.post(`${envName.trainingPath}api/businessCommon/manage/courseReview/activate/${id}?act_type=11`, { loading: true })
// 下架
export const deactivateProject = (id) => http.post(`${envName.trainingPath}api/businessCommon/manage/courseReview/deactivate/${id}?act_type=11`, { loading: true })
// 删除项目
export const deleteProject = (id) => http.post(envName.trainingPath + `api/mooc/manage/courseinfo/delete-course-info/${id}`, { loading: false })
// 催办
export const urgeAPI = (params) => http.post(`${envName.trainingPath}api/mooc/manage/message/batch-send-message`, { params, loading: true })
// 项目级别
export const courseLevel = (params) => http.get(`${envName.trainingPath}api/mooc/manage/courseinfo/get-course-level`, { params, loading: true })
// -----mooc轮播图管理-------
// 轮播图列表
export const getBannerListApi = (params) => http.get(`${envName.trainingPath}api/business-common/manage/banner/get-banner-list`, { params, loading: true })
// 轮播图添加
export const addBannerApi = (params) => http.post(`${envName.trainingPath}api/business-common/manage/banner/add`, { params, loading: true })
// 轮播图编辑
export const updateBannerApi = (params) => http.put(`${envName.trainingPath}api/business-common/manage/banner/update`, { params, loading: true })
// 轮播图删除
export const deleteBannerApi = (banner_id) => http.del(`${envName.trainingPath}api/business-common/manage/banner/delete/${banner_id}`, { loading: true })
// 轮播图上/下架
export const bannerStatusApi = (params) => http.post(`${envName.trainingPath}api/business-common/manage/banner/bannerStatus/${params.id}/${params.status}`, { params, loading: true })
// 轮播图排序
export const bannerSortingApi = (params) => http.get(`${envName.trainingPath}api/business-common/manage/banner/order/${params.id}/${params.order_no}`, { loading: true })
// ------banner推广------
// 成长矩阵 - 搜索
export const searchsiteApi = (params) => http.post(`${envName.portalArea}area/manage/content/searchsite`, { params, loading: false })
export const getModuleInfoApi = () => http.get(`${envName.portalArea}area/content-classify/module-info`, { loading: false })
export const getNetClassifyApi = () => http.get(`${envName.portalArea}area/content-classify/net-classify`, { loading: false })
// 详情页运营位添加
export const addDetailPageBannerApi = (params) => http.post(`${envName.trainingPath}api/business-common/manage/banner/add-banners`, { params, loading: true })
// 详情页运营位排序
export const updateOrderNoApi = (params) => http.put(`${envName.trainingPath}api/business-common/manage/banner/update-order-no`, { params, loading: true })
// ------课单标签------
// 自定义标签
export const customeTag = (params) => http.get(tagUrl + 'courselist/api/cl/user/lable/savelabel', { params, loading: true })
// 首页获取标签分类树型结构
export const getClassifyTree = (params) => http.get(tagUrl + 'portal-qlearning-adapter/api/adapter/common/act-classify/get-classify-tree', { params })
// 首页获取分类下的标签
export const getTagList = (params) => http.get(tagUrl + 'portal-qlearning-adapter/api/adapter/common/label/getlabels', { params, loading: true })
export const latelyTenTagManage = (params) => http.get(tagUrl + 'courselist/api/cl/manage/courselist/get_used_label', { params, loading: true })
// 获取最近使用的十个标签
export const latelyTenTag = (params) => http.get(tagUrl + 'courselist/api/cl/user/courselist/get_used_label', { params, loading: true })
// 标签查询
export const searchTag = (params) => http.get(tagUrl + 'portal-qlearning-adapter/api/adapter/common/label/search-labels', { params })
// 项目创建
export const saveProject = (params) => http.post(envName.trainingPath + `api/mooc/manage/courseinfo/save-course-info`, { params, loading: false })
// 获取证书列表
export const getCertificateList = (params) => http.get(envName.trainingPath + `api/mooc/manage/certificate/get-certificate-list`, { params, loading: false })
// 获取培养信息
export const getTrainInfo = (id) => http.get(`${envName.trainingPath}api/mooc/manage/courseinfo/get-course-info/${id}`, { loading: true })

// ------互动管理---------
// 获取公告列表
export const getNoticeListAPI = (params) => http.get(`${envName.trainingPath}api/mooc/manage/interact/get-notice-list`, { params, loading: true })
// 新建、编辑公告
export const createEditNoticeAPI = (params) => http.post(`${envName.trainingPath}api/mooc/manage/interact/save-notice`, { params, loading: true })
// 修改公告状态
export const changeNoticeStatusAPI = (params) => http.post(`${envName.trainingPath}api/mooc/manage/interact/save-notice-status`, { params, loading: true })
// 删除公告
export const deleteNoticeAPI = (noticeId) => http.get(`${envName.trainingPath}api/mooc/manage/interact/delete-notice/${noticeId}`, { loading: true })
// 评论列表
export const commentTableListManage = (params) => http.get(envName.trainingPath + 'api/mooc/manage/course-comment/get_comments', { params, loading: true })
// 点赞
export const likeCommentManage = (params) => http.post(envName.trainingPath + 'api/mooc/manage/course-comment/praised', { params, loading: true })
// 置顶
export const topCommentManage = (params) => http.post(envName.trainingPath + 'api/mooc/manage/course-comment/sticky', { params, loading: true })
// 隐藏
export const hideCommentManage = (params) => http.post(envName.trainingPath + 'api/mooc/manage/course-comment/show', { params, loading: true })
// 回复
export const replyCommentManage = (params) => http.post(envName.trainingPath + 'api/mooc/manage/course-comment/add', { params, loading: true })
// 删除
export const deleteCommentManage = (id) => http.post(envName.trainingPath + `api/mooc/manage/course-comment/delete/${id}`, { loading: true })
// 评分统计
export const getInteractScoreStatisticsAPI = (params) => http.get(envName.trainingPath + `api/mooc/manage/interact/get-grade-group`, { params, loading: true })
// 评分明细列表
export const getInteractScoreListAPI = (params) => http.get(envName.trainingPath + `api/mooc/manage/interact/get-mooc-score`, { params, loading: true })

// -----数据统计-----
// 获取数据统计详情
export const getSummaryProjectSummaryAPI = (id, params) => http.get(`${envName.trainingPath}api/mooc/manage/summary/course-summary/${id}`, { params, loading: true })
// 任务详情列表
export const getSummaryTaskListAPI = (params) => http.get(`${envName.trainingPath}api/mooc/manage/summary/task-list`, { params, loading: true })
// 任务详情列表导出
export const getSummaryTaskListExportAPI = (params) => http.get(`${envName.trainingPath}api/mooc/manage/summary/task-list/export`, { params, loading: true, responseType: 'blob' })
// 任务的学习详情列表
export const getTaskStudyRecordsAPI = (params) => http.get(`${envName.trainingPath}api/mooc/manage/task/study-records`, { params, loading: true })
// 任务的学习详情列表导出
export const getTaskStudyRecordsExportAPI = (params) => http.get(`${envName.trainingPath}api/mooc/manage/task/study-records/export`, { params, loading: true, responseType: 'blob' })
// 学员详情列表
export const getStudentDetailListAPI = (params) => http.get(`${envName.trainingPath}api/mooc/manage/student/get-student-detail-list`, { params, loading: true })
// 学员详情列表导出
export const getStudentDetailListExportAPI = (params) => http.get(`${envName.trainingPath}api/mooc/manage/student/export-student-detail-list`, { params, loading: true, responseType: 'blob' })
// 学员详情下的任务
export const getStudentDetailTaskListAPI = (params) => http.get(`${envName.trainingPath}api/mooc/manage/student/get-student-detail`, { params, loading: true })
// 学员证书列表-管理端
export const getManageStudentCertificateListAPI = (params) => http.get(`${envName.trainingPath}api/mooc/manage/student/get-student-certificate-list`, { params, loading: true })
// 学员证书列表-用户端
export const getUserStudentCertificateListAPI = (params) => http.get(`${envName.trainingPath}api/mooc/user/student/get-student-certificate-list`, { params, loading: true })
// 学员证书详情
export const getStudentCertificateInfo = (id) => http.get(`${envName.trainingPath}api/mooc/manage/certificate/get-user-certificate-info/${id}`, { loading: true })
// 学员详情统计
export const getStudentStatisticsAPI = (params) => http.get(`${envName.trainingPath}api/mooc/manage/student/get-student-statistics`, { params, loading: true })
// 学员详情
export const studentDetailExportAPI = (params) => http.get(`${envName.trainingPath}api/mooc/manage/student/export-student-task-list`, { params, loading: true, responseType: 'blob' })
// -----高级设置-----
// 发送测试信息
export const sendTestMsg = (params) => http.post(envName.trainingPath + 'api/mooc/manage/message/send-test-message', { params, loading: true })
// 获取催办配置
export const getStudentExpedite = (params) => http.get(envName.trainingPath + 'api/mooc/manage/setting/get-student-expedite', { params, loading: true })
// 催办配置
export const studentExpedite = (params) => http.post(envName.trainingPath + 'api/mooc/manage/setting/student-expedite', { params, loading: true })
// 上传资料
export const saveResourceBatch = (params) => http.post(envName.trainingPath + 'api/mooc/manage/resource/save-resource-batch', { params, loading: true })
// 资料列表
export const getResourceList = (params) => http.get(envName.trainingPath + 'api/mooc/manage/resource/get-resource-list', { params, loading: true })
// 资料移除
export const resourceDelete = ({ id, mooc_course_id }) => http.del(`${envName.trainingPath}api/mooc/manage/resource/delete?id=${id}&mooc_course_id=${mooc_course_id}`, { loading: true })
// 资料是否允许下载设置
export const setAllowDownload = ({ id, allow_download }) => http.get(`${envName.trainingPath}api/mooc/manage/resource/allow-download/${id}/${allow_download}`, { loading: true })
// 资料列表排序设置
export const setResourceSort = ({ resource_id, target_no, mooc_course_id }) => http.get(`${envName.trainingPath}api/mooc/manage/resource/order/${mooc_course_id}/${resource_id}/${target_no}`, { loading: true })
// 资料文件查看或下载
export const resourceViewOrDownload = (params) => http.post(`${envName.trainingPath}api/mooc/manage/resource/view-or-download`, { params, loading: true })
// 获取内容的预览信息
export const getPreviewInfoApi = (content_id) => http.get(`${envName.trainingPath}api/businessCommon/common/content/previewInfo?app_id=QLearningService&corp_name=tencent&content_id=${content_id}`, { loading: true })
// 获取内容的下载地址
export const urlForDownloadApi = (content_id) => http.get(`${envName.trainingPath}api/businessCommon/common/content/urlForDownload?app_id=QLearningService&corp_name=tencent&content_id=${content_id}`, { loading: true })
// 上级催办模板
export const superiorUrgeTemplate = (params) => http.get(`${envName.trainingPath}api/mooc/manage/student/get-project-msg-template`, { params, loading: true })
// 素材管理
// 素材添加
export const saveMaterial = (params) => http.post(`${envName.trainingPath}api/businessCommon/manage/pub-file/addOrUpdatePubFile`, { params, loading: true })
// 素材删除
export const deleteMaterial = (fileId) => http.del(`${envName.trainingPath}api/businessCommon/manage/pub-file/file/${fileId}`, { loading: true })
// 素材管理引用详情
export const metaerialDetail = (fileId) => http.get(`${envName.trainingPath}api/businessCommon/manage/pub-file/file/${fileId}`, { loading: true })
// 素材引用查询
export const quoteSearch = (fileId) => http.get(`${envName.trainingPath}api/businessCommon/user/pub-file/pub-file-refer/${fileId}`, { loading: true })
export const transcoding = (id) => http.get(`${envName.trainingPath}api/businessCommon/user/pub-file/reTransCode/${id}`, { loading: true })
// 素材-文章-保存草稿
export const articlelDraftData = (params) => http.get(`${envName.trainingPath}api/businessCommon/manage/pub-file/get-draft`, { params, loading: true })
// 添加作业
export const saveWork = (params) => http.post(`${envName.trainingPath}api/mooc/manage/homework/addOrUpdateHomework`, { params, loading: true })
// 更新作业
export const updateWork = (params, homework_id) => http.get(`${envName.trainingPath}api/mooc/manage/homework/gethomework/${homework_id}`, { params, loading: true })
// 作业任务详情
export const workTaskDetail = (params, homework_id) => http.get(`${envName.trainingPath}api/mooc/manage/homework/getTaskHomework/${homework_id}`, { params, loading: true })
// 作业详情查询
export const workTaskSearch = (params) => http.get(`${envName.trainingPath}api/mooc/manage/task/get-task-detail-student-list`, { params, loading: true })

// 第三方任务
// 管理端任务保存
export const saveThirdParty = (params) => http.post(`${envName.trainingPath}api/mooc/manage/task/third-party/save-task`, { params, loading: true })
// 管理端任务详情-刷新第三方任务
export const refreshTaskManage = (params) => http.get(`${envName.trainingPath}api/mooc/manage/task/third-party/refresh-task`, { params, loading: true })
// 管理端任务详情
export const thirdPartyDetail = (params) => http.get(`${envName.trainingPath}api/mooc/manage/task/third-party/task-detail`, { params, loading: true })
// 管理端任务详情-更多
export const getThirdPartyExtensions = (params) => http.get(`${envName.trainingPath}api/mooc/manage/task/third-party/get-extensions`, { params, loading: true })
// 管理端任务详情-第三方任务导出接口
export const thirdPartyExport = (params) => http.get(`${envName.trainingPath}api/mooc/manage/task/third-party/export`, { params, loading: true, responseType: 'blob' })
// mooc用户端任务详情页-刷新第三方任务
export const refreshTaskUser = (params) => http.get(`${envName.trainingPath}api/mooc/user/courseinfo/third-party/refresh-task`, { params, loading: true })
// mooc用户端任务详情页-任务状态
export const taskStatus = (params) => http.get(`${envName.trainingPath}api/mooc/manage/task/task-status`, { params, loading: true })

// 用户界面
// 培养项目首页
// 分类
export const getTrainClassify = (params) => http.get(envName.trainingPath + 'api/mooc/user/courseinfo/get-course-classify', { params, loading: true })
export const getTrainClassifyTree = (params) => http.get(envName.trainingPath + 'api/label/user/category/category_tree', { params, loading: true })
export const getClassifyTag = (params) => http.get(envName.trainingPath + 'api/label/user/labelinfo/get_label_basicinfo', { params, loading: true })
// 标签
export const getTrainTag = (params) => http.get(envName.trainingPath + 'api/mooc/user/courseinfo/get-label-by-classify', { params, loading: true })
// 获取项目列表
export const getTrainList = (params) => http.get(envName.trainingPath + 'api/mooc/user/courseinfo/get-user-course', { params, loading: false })
// banner列表
export const getBannerList = (params) => http.get(envName.trainingPath + 'api/business-common/user/banner/get-banner-list', { params, loading: true })
// 培养项目
export const getMyTrainProject = (params) => http.get(envName.trainingPath + 'api/mooc/user/courseinfo/get-my-course', { params, loading: true })
// 首页项目统计
export const getStatistics = (params) => http.get(envName.trainingPath + 'api/mooc/user/courseinfo/get-my-course-summary', { params, loading: false })
// 项目详情
// 获取详情
export const projectDetailAPI = (moocCourseId) => http.get(envName.trainingPath + `api/mooc/user/courseinfo/get-course-info/${moocCourseId}`, { loading: true })
// 获取个人详情
export const personDetail = (params) => http.get(envName.trainingPath + `api/mooc/user/student/get-student-status`, { params, loading: true })
// 项目详情
export const cardDetail = (params, loading = false) => http.get(envName.trainingPath + `api/mooc/user/student/get-student-projInfo-simple`, { params, loading: loading })
// 详情点赞
export const moocPraise = (params) => http.get(envName.trainingPath + `api/mooc/user/course-praise/add-praise`, { params, loading: true })
// 取消详情点赞
export const deleteMoocPraise = (params) => http.get(envName.trainingPath + `api/mooc/user/course-praise/delete-praise`, { params, loading: true })
// 详情收藏
export const moocfav = (params) => http.get(envName.trainingPath + `api/mooc/user/course-favorite/add-favorite`, { params, loading: true })
// 取消收藏
export const deleteMoocfav = (params) => http.get(envName.trainingPath + `api/mooc/user/course-favorite/delete-favorite`, { params, loading: true })
// 报名
export const moocEnroll = (params, loading = true) => http.post(envName.trainingPath + `api/mooc/user/courseinfo/add-register`, { params, loading })
// 取消报名
export const cacelEnroll = (params) => http.get(envName.trainingPath + `api/mooc/user/courseinfo/cancel-register`, { params, loading: true })
// 添加评分
export const addScore = (params) => http.post(envName.trainingPath + `api/mooc/user/course-score/add-score`, { params, loading: true })
// 获取用户评分
export const getUerScore = (params) => http.get(envName.trainingPath + `api/mooc/user/course-score/check-scored`, { params, loading: true })
// 任务详情任务列表
export const getDetailTaskList = (params) => http.get(envName.trainingPath + `api/mooc/user/courseinfo/get-task-list`, { params, loading: true })
// 任务详情公告列表
export const getDetailNoticeList = (params) => http.get(envName.trainingPath + `api/mooc/user/interact/get-notice-list`, { params, loading: true })
// 公告未查看数据
export const noticeNoPreview = (params) => http.put(envName.trainingPath + `api/mooc/user/interact/update-notice-list-status`, { params, loading: true })
// 培养进度
export const getTrainProgress = (params) => http.get(envName.trainingPath + `api/mooc/user/student/get-course-progress`, { params, loading: true })
// 获取项目小程序二维码
export const getMobileQrcode = (params) => http.post(envName.trainingPath + `api/mooc/user/student/get-mobile-QRcode`, { params, loading: false })

// 学习资料
// 学习资料列表
export const getStudyDataList = (params) => http.get(envName.trainingPath + 'api/mooc/user/resource/get-resource-list', { params })
export const getSingleData = (params) => http.get(envName.trainingPath + 'api/mooc/user/resource/view', { params })
// 学习资料下载
export const sourceDownload = (params) => http.get(envName.trainingPath + 'api/mooc/user/resource/download', { params })
// 全部下载
export const sourceAllDownload = (params) => http.post(envName.trainingPath + 'api/mooc/user/resource/download-all-resources', { params })
// 批量下载
export const batchDownload = (params) => http.get(`${envName.trainingPath}}api/v1/content/doc/batch-download-async?compress_name=${params.compress_name}&encrypt=true`, { params, loading: false })
// 课程进度
export const getCourseProcess = (params) => http.get(envName.trainingPath + 'api/mooc/user/student/get-course-progress', { params })
// 任务列表
export const getTaskList = (params) => http.get(envName.trainingPath + 'api/mooc/user/courseinfo/get-task-list', { params })
// 学习记录上报
export const saveLearnRecord = (params) => http.post(envName.trainingPath + 'api/mooc/user/student/save-learn-record', { params })
// 任务详情
export const getTaskDetail = (params) => http.get(envName.trainingPath + 'api/mooc/user/courseinfo/get-task-detail', { params })
// 2.22新增作业，第三方任务查询是否全部完成接口
export const getTaskFinishedCondition = (params) => http.get(envName.trainingPath + '/api/mooc/user/student/check-user-all-finish-task', { params })
// 作业
// 管理用户列表
export const getManagerWorkList = (params) => http.get(envName.trainingPath + 'api/mooc/manage/homework/get-student-homework-list', { params, loading: true })
// 管理端获取作业记录
export const getManagerWorkListDetails = (params) => http.get(envName.trainingPath + 'api/mooc/manage/homework/get-homework-record', { params, loading: true })
// 作业退回
export const getRefundHomework = (params) => http.post(envName.trainingPath + 'api/mooc/manage/homework/refund-homework', { params, loading: true })
// 获取作业状态数量
export const getStatisticsNum = (params) => http.get(envName.trainingPath + 'api/mooc/manage/homework/get-homework-record-statistics', { params, loading: true })
// 管理端下载作业
export const downloadHomeworkRecord = (params) => http.get(envName.trainingPath + 'api/mooc/manage/homework/download-homework-record', { params, loading: true })
export const workSourceInfo = (content_id, params) => http.get(envName.contentcenter + `content-center/api/v1/content/${content_id}/url`, { params })

// spoc任务相关
export const getCourseProcessSpoc = (params) => http.get(envName.spocHost + 'task/get_course_progress', { params })
export const getTaskListSpoc = (params) => http.get(envName.spocHost + 'task/get_task_list', { params })
export const saveLearnRecordSpoc = (params, classId) => http.post(envName.spocHost + `task/save_learn_record?classId=${classId}`, { params })
export const getTaskDetailSpoc = (params) => http.get(envName.spocHost + 'task/get_task_detail', { params })
export const getRoleList = (params) => http.get(envName.trainingPath + 'api/mooc/manage/homework/get-mark-role-list', { params })
export const getFinishedCondition = (params) => http.get(envName.trainingPath + 'api/mooc/manage/homework/get-task-finishedCondition', { params })
const spocManageHost = envName.spocHost.replace('/mobile', '')
export const getSemesterInfo = (params) => http.get(spocManageHost + 'semester/select_semester_info', { params })
// 查询作业要求
export const queryHomeworkRequirements = (params) => http.get(spocManageHost + 'customized_job_requirements/customized_job_requirements_list', { params })
// 查询作业要求详情
export const getQueryHomeworkRequirements = (params) => http.get(spocManageHost + 'customized_job_requirements/get_customized_job_requirements_details', { params })
// spoc评审列表
export const getSpocCourseList = (params) => http.get(spocManageHost + 'semesterReview/courseList', { params })

// 互动能力
// 互动详情
export const getCourseInteraction = (params) => http.get(envName.trainingPath + 'api/courseInteraction/user/interaction/get-course-interaction', { params })
// 互动记录提交
export const saveInteractionRecord = (params) => http.post(envName.trainingPath + 'api/courseInteraction/user/interaction/save-interaction-record', { params })
// 互动配置分页
export const getCourseInteractionPage = (params) => http.get(envName.trainingPath + 'api/courseInteraction/manage/interaction/get-interactive-page', { params })
// 互动配置新建
export const getCourseInteractionNew = (params) => http.post(envName.trainingPath + 'api/courseInteraction/manage/interaction/add-course-interaction', { params })
// 互动配置修改
export const getCourseInteractionRevise = (params) => http.post(envName.trainingPath + 'api/courseInteraction/manage/interaction/update-course-interaction', { params })
// 互动配置删除
export const getCourseInteractionDelete = (id, params) => http.del(envName.trainingPath + `api/courseInteraction/manage/interaction/${id}/delete-course-interaction?interactive_id=${params}`, { params })
// 互动配置详情
export const getCourseInteractionDetails = (id, params) => http.get(envName.trainingPath + `api/courseInteraction/manage/interaction/${id}/get-course-interaction?interactive_id=${params}`)
// 互动记录导出
export const getCourseInteractionRecordExport = (params) => http.post(envName.trainingPath + 'api/courseInteraction/manage/interaction/export-interactive-record', { params })
// 互动记录分页列表
export const getCourseInteractionRecordPage = (params) => http.get(envName.trainingPath + 'api/courseInteraction/manage/interaction/get-interactive-record', { params })
// 互动记录详情
export const getInteractiveRecordInfo = (id) => http.get(envName.trainingPath + `api/courseInteraction/manage/interaction/get-interactive-record-info/${id}`, { loading: true })
// 课程互动配置信息
export const getCourseDetailInfo = (courseId) => http.get(envName.trainingPath + `api/netcourse/manage/courseinfo/get-course-interactive/${courseId}`, { loading: false })
// 互动配置-更新状态
export const updateInteractiveStatus = (params) => http.put(envName.trainingPath + `api/courseInteraction/manage/interaction/update-interactive-status?act_type=${params.act_type}&course_id=${params.course_id}&status=${params.status}`, { loading: false })
// 互动配置-进度条控制
export const updateProgressBar = (params) => http.put(envName.trainingPath + `api/courseInteraction/manage/interaction/update-progress-bar?course_id=${params.course_id}&status=${params.status}&act_type=${params.act_type}`, { loading: false })

// 极客时间 课程详情
export const getGeekCourseDetail = (courseId, params) => http.get(envName.trainingPath + `api/outsourcedCourse/user/info/${courseId}`, params)
// 极客时间 是否给课程点赞
export const checkPraised = ({ act_type, course_id }) => http.get(envName.trainingPath + `api/outsourcedCourse/user/interaction/${act_type}/check-praised?course_id=${course_id}`, { loading: false })
// 极客时间 课程点赞
export const addPraise = ({ act_type, course_id }) => http.get(envName.trainingPath + `api/outsourcedCourse/user/interaction/${act_type}/add-praise?course_id=${course_id}`, { loading: false })
// 极客时间 课程取消点赞
export const deletePraise = ({ act_type, course_id }) => http.get(envName.trainingPath + `api/outsourcedCourse/user/interaction/${act_type}/delete-praise?course_id=${course_id}`, { loading: false })
// 极客时间 课程是否收藏
export const checkFavorited = ({ act_type, course_id }) => http.get(envName.trainingPath + `api/outsourcedCourse/user/interaction/${act_type}/check-favorited?course_id=${course_id}`, { loading: false })
// 极客时间 收藏课程
export const addFavorited = ({ act_type, course_id }) => http.get(envName.trainingPath + `api/outsourcedCourse/user/interaction/${act_type}/add-favorite?course_id=${course_id}`, { loading: false })
// 极客时间 课程取消收藏
export const deleteFavorite = ({ act_type, course_id }) => http.get(envName.trainingPath + `api/outsourcedCourse/user/interaction/${act_type}/delete-favorite?course_id=${course_id}`, { loading: false })
// 极客时间 上报
export const geekStudyRecord = (params) => http.get(envName.trainingPath + `api/outsourcedCourse/user/info/study/record`, { params, loading: false })
// 极客时间 课程互动统计
export const getSummaryData = ({ act_type, course_id }) => http.get(envName.trainingPath + `api/outsourcedCourse/user/interaction/${act_type}/summary?course_id=${course_id}`, { loading: false })

// 判断是不是特殊人员
export const getlabelSpecialUsers = () => http.get(envName.trainingPath + `api/label/user/labelinfo/label-special-users`, { loading: false })
// 极客时间 获取用户账户信息和课程售卖信息
export const getCoursePurchaseInfo = (course_id) => http.get(envName.trainingPath + `api/outsourcedCourse/user/purchase/course-purchase-info?course_id=${course_id}`)
// 极客时间 补货通知-添加订阅
export const insertSubscription = (params) => http.post(envName.trainingPath + 'api/outsourcedCourse/user/subscription/insert-subscription', { params })
// 极客时间 补货通知-查询订阅
export const getSubscription = (params) => http.get(envName.trainingPath + `api/outsourcedCourse/user/subscription/get-subscription`, { params })

// 极客时间-管理端 外部课程列表
export const getOutsourcedCoursePageApi = (params) => http.post(envName.trainingPath + 'api/outsourcedCourse/manage/info/outsourced-course-page', { params })
// 极客时间-管理端 外部课程-删除
export const delOutsourcedApi = (course_id) => http.post(envName.trainingPath + `api/outsourcedCourse/manage/info/del-outsourced/${course_id}`)
// 极客时间-管理端 外部课程信息同步
export const infoSyncApi = (params) => http.get(envName.trainingPath + 'api/outsourcedCourse/manage/info/sync', { params, loading: true })
// 极客时间-管理端 补充库存
export const addGeekStockApi = (params) => http.post(envName.trainingPath + 'api/outsourcedCourse/manage/config/addGeekStock', { params, loading: true })
// 极客时间-管理端 库存规则配置
export const getStockDescApi = () => http.get(envName.trainingPath + 'api/outsourcedCourse/manage/config/getStockDesc')
// 极客时间-管理端 mooc关联外部课程信息
export const getMoocOutsourcedInfo = (mooc_id) => http.get(envName.trainingPath + `api/outsourcedCourse/manage/info/mooc-outsourced-info/${mooc_id}`)
// 极客时间-管理端 获取geek的库存信息
export const getStockApi = () => http.get(envName.trainingPath + `api/outsourcedCourse/manage/config/getStock`)
// 极客时间-管理端 获取外部课程类型列表
export const getTenantListApi = () => http.get(envName.trainingPath + `api/outsourcedCourse/manage/config/tenantList`)
// 极客时间-管理端 获取内容类型下拉
export const getTypeConditionApi = (params) => http.post(envName.trainingPath + `api/outsourcedCourse/manage/info/get-type-condition`, { params })

// 极客时间-活动页 获取活动页面数据
export const getHomePageConfig = (params) => http.get(envName.trainingPath + `api/outsourcedCourse/user/info/homePage/config`, { params })
// 极客时间-活动页 积分授予-查询是否可以领取geek
export const getHomePageInfo = (params) => http.get(envName.lotteryHost + `api/v1/pub/acct/grant/query_geek`, { params })
// 极客时间-活动页 积分授予-领取积分
export const claimPoints = (params) => http.post(envName.lotteryHost + `api/v1/pub/acct/grant`, { params })
// 极客时间-活动页 积分授予-查询领取兑换记录
export const queryGeekRecord = (params) => http.get(envName.lotteryHost + `api/v1/pub/acct/record/query_geek`, { params, loading: true })
// 学霸卡活动基础信息
export const getUserActiveInfo = (params) => http.get(envName.lotteryHost + `api/v1/user/activity/homepage`, { params, loading: true })
// 查询人员
export const getActiveStaffs = (params) => http.get(envName.lotteryHost + `api/v1/user/activity/staffs`, { params, loading: true })
// 已兑换课程
export const getActiveOrders = (params) => http.get(envName.lotteryHost + `api/v1/user/activity/orders`, { params, loading: true })
// 赠送劝学卡
export const activityPresent = (params) => http.post(envName.lotteryHost + `api/v1/user/activity/present`, { params, loading: true })
// 查询我的赠送记录
export const getPresentRecord = (params) => http.get(envName.lotteryHost + `api/v1/user/activity/present_record`, { params, loading: true })
// 查询我的接受赠送记录
export const getPresentPassiveRecord = (params) => http.get(envName.lotteryHost + `api/v1/user/activity/present_passive_record`, { params, loading: true })
// 消息订阅 {msgType}
export const messageSubscribe = (params, msgType) => http.get(envName.lotteryHost + `api/v1/user/subscribe/${msgType}`, { params, loading: true })
// 查询活动信息
export const getDefaultActivityInfo = (params) => http.get(envName.lotteryHost + `api/v1/user/activity/default-activity-info`, { params, loading: true })
// 取消订阅
export const messageUnsubscribe = (params, msgType) => http.get(envName.lotteryHost + `api/v1/user/unsubscribe/${msgType}`, { params, loading: true })
// 查询用户全部账户信息
export const getAcctinfos = (params) => http.get(envName.lotteryHost + `api/v1/user/acctinfos`, { params, loading: false })
// 获取活动规则、外部课程、落地页链接
export const getOutsourceLinkConfig = (params) => http.get(envName.trainingPath + `api/outsourcedCourse/user/info/outsourceLinkConfig`, { params, loading: false })
// 兑换弹窗卡券选项
export const getAcctinfosActivity = (params) => http.get(envName.lotteryHost + `api/v1/user/activity/acctinfos`, { params, loading: true })
//  订阅状态
export const getSubscribeStatus = (type, params) => http.get(envName.lotteryHost + `api/v1/user/subscribe-status/${type}`, { params, loading: true })
// 学霸卡内容来源
export const outsourceSourceFrom = (params) => http.get(envName.trainingPath + `api/outsourcedCourse/user/info/outsourceSourceFrom`, { params, loading: true })
//  查询目前活动中的外部课程
export const purchaseSourceFromConfig = (params) => http.get(envName.trainingPath + `api/outsourcedCourse/user/info/purchaseSourceFromConfig`, { params })
// 哈商判断是否有分享权限
export const checkShareAuth = (params) => http.get(envName.trainingPath + `api/outsourcedCourse/share/check-share-auth`, { params })
// 哈商接受邀请获取访问权限
export const acceptInvite = (params) => http.get(envName.trainingPath + `api/outsourcedCourse/user/info/harvard/accept-invite`, { params })
// 查询哈商活动信息
export const getHarvardActivityInfo = (params) => http.get(envName.lotteryHost + `/api/v1/user/activity/harvard-activity-info`, { params, loading: false })
// 权限申请
export const getRequestRecord = (params) => http.get(envName.trainingPath + `/api/outsourcedCourse/user/info/save/request-record`, { params, loading: true })

// 标签订阅
export const getSubsLabels = (params) => http.get(envName.trainingPath + 'api/label/subscribe/getSubscribeLabels', { params })
export const getLabelDetail = (params) => http.post(envName.trainingPath + 'api/label/subscribe/getLabelDetailInfo', { params })
export const getLabelContents = (params) => http.post(envName.trainingPath + 'api/label/subscribe/searchSubscribeContents', { params })
export const subscribeLabel = (params) => http.post(envName.trainingPath + 'api/label/subscribe/subscribeLabel', { params })
export const getLabelBasicinfo = (params) => http.get(envName.trainingPath + 'api/label/user/labelinfo/get_label_basicinfo', { params })
// 专区列表
export const getSpecialList = (params) => http.post(envName.commonPath + 'mat/api/user/search', { params })
export const getLabelSubscribeDetailInfo = (params) => http.post(envName.trainingPath + 'api/label/subscribe/getLabelSubscribeDetailInfo', { params })

// 获取运营分级下的分级项目
export const getOperationApi = (pid) => http.get(envName.trainingPath + `api/businessCommon/manage/dict/get-child-dict-items?key=operations_level&pid=${pid}`, { loading: false })
// 申请发布项目
export const applyApprove = (id) => http.post(envName.trainingPath + `api/mooc/manage/courseinfo/submit-course-approve/${id}`, { loading: false })

// 课找人 --- 列表查询
export const getNetCoursePersonList = (params) => http.get(envName.trainingPath + `api/recommend/manage/recommend-courses/search`, { loading: true, params })
// 课找人 --- 添加推广
export const addNetCoursePerson = (params) => http.post(envName.trainingPath + `api/recommend/manage/recommend-courses/add`, { params })
// 课找人 --- 删除推广
export const delNetCoursePerson = (ids) => http.del(envName.trainingPath + `api/recommend/manage/recommend-courses/delete/${ids}`)
// 课找人 --- 修改推广
export const updateNetCoursePerson = (params) => http.post(envName.trainingPath + `api/recommend/manage/recommend-courses/update`, { params })
// 课找人 --- 检查是否添加过推广的课程
export const checkRecommendCourse = (params) => http.post(envName.trainingPath + `api/recommend/manage/recommend-courses/check-course`, { params })
// 课找人 --- 查询一个推广的课程列表
export const getRecommendCouseList = (params) => http.get(envName.trainingPath + `api/recommend/manage/recommend-courses/get-course-list`, { params })
// 课找人 --- 新增某个推广栏目下的课程
export const addRecommendCourse = (params) => http.post(envName.trainingPath + `api/recommend/manage/recommend-courses/add-course-list`, { params })
// 课找人 --- 删除某个推广栏目下的课程
export const delRecommendCourse = (ids) => http.del(envName.trainingPath + `api/recommend/manage/recommend-courses/delete-course/${ids}`)
// 课找人 --- 获取栏目名称
export const getRecommendName = (params) => http.get(envName.trainingPath + `api/business-common/manage/push/get-columns`, { params })

// ddi测评浮窗提示显示
export const showDDIShare = (params) => http.get(`${envName.trainingPath}api/outsourcedCourse/user/info/${params}/show_share_tips`, { loading: false })
// ddi测评浮窗关闭
export const closeDDIShare = (params) => http.get(`${envName.trainingPath}api/outsourcedCourse/user/info/${params}/close-share-tips`, { loading: false })
/*
聚合报名管理端
*/
// 管理页列表
export const getPolymerList = (params) => http.post(envName.trainingPath + `api/polymer/manage/info/polymer-list`, { params, loading: true })
// 创建、编辑聚合页
export const addPolymer = (params) => http.post(envName.trainingPath + `api/polymer/manage/info/publish-polymer`, { params })
// 聚合页保存草稿
export const saveDraftPolymer = (params) => http.post(envName.trainingPath + `api/polymer/manage/info/draft-polymer`, { params })
// 关联班级活动列表
export const getActivityClass = (params) => http.post(envName.trainingPath + `api/activity/manage/polymer/class-list`, { params })
// 编辑详情
export const getPolymerInfo = (params) => http.get(envName.trainingPath + `api/polymer/manage/info/detail`, { params })
// 编辑关联班级列表
export const getPolymerInfoClassList = (params) => http.get(envName.trainingPath + `api/activity/manage/polymer/polymer-class-list`, { params })
// 删除聚合页
export const getDeletePolymerList = (params) => http.get(envName.trainingPath + `api/polymer/manage/info/delete-polymer`, { params })
// 聚合页上下架
export const setPolymerActivate = (params) => http.get(envName.trainingPath + `api/polymer/manage/info/activate-polymer`, { params })
// 聚合页目录列表
export const getPolymerCatalogList = (params) => http.get(envName.trainingPath + `api/polymer/manage/info/get-category-list`, { params })
// 聚合页新建目录
export const addPolymerCatalog = (params) => http.post(envName.trainingPath + `api/polymer/manage/info/create-category`, { params })
// 聚合页编辑目录
export const editPolymerCatalog = (params) => http.post(envName.trainingPath + `api/polymer/manage/info/update-category`, { params })
// 聚合页删除目录
export const delPolymerCatalog = (params) => http.post(envName.trainingPath + `api/polymer/manage/info/delete-category`, { params })

// 网课-mooc获取动态标签
export const getDynamicsTag = (params) => http.get(`${envName.portalAdapter}area/user/page/rank/rank-tag`, { params, loading: false })

export const postSSO = (params) => http.get(`${envName.trainingPath}api/ext/outsourceCourse/saml/sso?${params}`)
