// 信息审核
window.$informationReview = (function() {
  const _urlConfig = {
    audit_config_prod: `https://contentcenter-1252291750.cos.ap-guangzhou.myqcloud.com/public/academy/config/audit/prod/auditConfig.json?ts=${new Date().getTime()}`,
    audit_config_test: `https://contentcenter-1252291750.cos.ap-guangzhou.myqcloud.com/public/academy/config/audit/test/auditConfig.json?ts=${new Date().getTime()}`,
    mobile_prod: '//learn.woa.com/training/',
    mobile_test: '//test-learn.woa.com/training/',
    pc_prod: '//portal.learn.woa.com/training/',
    pc_test: '//test-portal-learn.woa.com/training/'
  }
  const _devUrl = [
    // test
    'test-portal-learn.woa.com',
    'test.portal.learn.oa.com',
    'test.v8.learn.oa.com',
    'test-learn.woa.com',
    'dev-ntsapps.woa.com',
    'dev-m-ntsapps.woa.com',
    // dev
    'test.woa.com',
    'local.oa.com'
  ]
  // 可直接使用的域名
  const feasibleUrl = [
    'portal.learn.woa.com',
    'learn.woa.com',
    'm-learn.woa.com',
    'test-portal-learn.woa.com',
    'test-learn.woa.com',
    'anli.woa.com'
  ]
  var _dev = true
  var _configJsonUrl = _urlConfig.audit_config_test
  var _baseURL = ''
  var _loadingMask = null
  const _fileTypes = [
    { name: '.png', type: 'image' },
    { name: '.jpg', type: 'image' },
    { name: '.bmp', type: 'image' },
    { name: '.jpeg', type: 'image' },
    { name: '.jpg', type: 'image' },
    { name: '.gif', type: 'image' },
    { name: '.webp', type: 'image' },
    { name: '.ico', type: 'image' },

    { name: '.mp4', type: 'video' },
    { name: '.mkv', type: 'video' },
    { name: '.avi', type: 'video' },
    { name: '.mpg', type: 'video' },
    { name: '.rmvb', type: 'video' },
    { name: '.mpv', type: 'video' },
    { name: '.flv', type: 'video' },
    { name: '.mov', type: 'video' },
    { name: '.ts', type: 'video' },
    { name: '.m4v', type: 'video' },
    { name: '.wmv', type: 'video' },

    { name: '.mp3', type: 'audio' },
    { name: '.aac', type: 'audio' },
    { name: '.wav', type: 'audio' },
    { name: '.wma', type: 'audio' },
    { name: '.flac', type: 'audio' },
    { name: '.cda', type: 'audio' },
    { name: '.m4a', type: 'audio' },
    { name: '.m4v', type: 'audio' },
    { name: '.amr', type: 'audio' },

    { name: '.ppt', type: 'document' },
    { name: '.pptx', type: 'document' },
    { name: '.xls', type: 'document' },
    { name: '.xlsx', type: 'document' },
    { name: '.doc', type: 'document' },
    { name: '.docx', type: 'document' },
    { name: '.pdf', type: 'document' },
    { name: '.xps', type: 'document' },
    { name: '.csv', type: 'document' }
  ]
  var _sourceDataType = 'object'
  var _isMobile = true
  var _userInit = false
  var _isDebugger = false
  // api拦截配置
  var _apiInterceptConfig = []
  // var _apiInterceptConfig = [
  //   {
  //     'api': '/training/api/mooc/manage/courseinfo/save-course-info', // 请求的api地址
  //     'method': 'post',
  //     "need_faild_alert": true, // 是否需要失败消息提示，true 使用sdk的审核失败提示，false 不需要sdk的失败提示，需要页面自己实现失败提示
  //     'draft_column_code':'draft_code',// 判断本次提交是否草稿操作的字段名称（如果草稿和正式保存的接口是同一个，是通过某个字段来标志的需要配置改字段）
  //     'draft_satus_value': '1',// 判断草稿状态的值（如果草稿和正式保存的接口是同一个，是通过某个字段来标志的需要配置改字段）
  //     'is_batch_import': false,// 非必填，是否批量导入课程，如果是批量导入的场景，传入的数据需要是一个数组，默认不传该字段
  //     'setting': {
  //       'act_type': '11', // 对应的业务类型
  //       'columns': [
  //         { 
  //           'column_code': 'course_title', // 请求接口的json对象的字段名，如果是文件类的字段上传该字段为文件地址的字段名
  //           'column_name': '项目名称', // 字段对应的页面字段名称
  //           'column_type': 'text', // 字段的类型，审核支持的类型枚举见下面的“column_type枚举”
  //           'call_type': ['sync'], // 调用类型，枚举为 sync(同步调用)、async(异步调用),如果同时都有需要先按类型分组，优先执行同步的接口” 
  //           'manual_review': false // 是否接入人工审核，一般情况下同步就没有人工审核
  //         },
  //         { 'column_code': 'course_statement.operation_title', 'column_name': '运营标题', 'column_type': 'text', 'call_type': ['sync'], 'manual_review': false },
  //         { 'column_code': 'cover_image_id', 'column_name': '项目封面', 'column_type': 'image', 'storage_type': 'contentcenter', 'call_type': ['async'], 'manual_review': true },
  //         { 'column_code': 'course_desc', 'column_name': '项目简介', 'column_type': 'text', 'call_type': ['sync'], 'manual_review': false },
  //         { 'column_code': 'course_desc_detail', 'column_name': '项目详情', 'column_type': 'richText', 'call_type': ['sync'], 'manual_review': false }

  //         // column_type枚举: text、richText、audio、video、image、document、file：多类型上传

  //         // test
  //         // ,{ 'column_code': 'course_labels.label_name', 'column_name': '项目标签', 'column_type': 'text', 'call_type': ['sync'], 'manual_review': false }
  //         // ,{ 'column_code': 'course_labels_list.label_name', 'column_name': '项目标签', 'column_type': 'text', 'call_type': ['sync'], 'code_type': 'json', 'manual_review': false }
  //         // ,{ 'column_code': 'course_attachment.file_url', 'column_name': '资料上传', 'column_type': 'file', 'call_type': ['async'], 'manual_review': false }
  //         // ,{ 'column_code': 'questionDTOS.question.questionName', 'column_name': '考试标题', 'column_type': 'text', 'call_type': ['sync'], 'manual_review': false },
  //       ]
  //     }
  //   },
  //   {
  //     'api': '/training/api/mooc/manage/resource/save-resource-batch', // 请求的api地址
  //     'method': 'post',
  //     "need_faild_alert": true,
  //     'draft_column_code':'',
  //     'draft_satus_value': '',
  //     'setting': {
  //       'act_type': '11', // 对应的业务类型
  //       'columns': [
  //         { 
  //           'column_code': 'resource_name', // 请求接口的json对象的字段名，如果是文件类的字段上传该字段为文件地址的字段名
  //           'column_name': '文件名称', // 字段对应的页面字段名称
  //           'column_type': 'text', // 字段的类型，审核支持的类型枚举见下面的“column_type枚举”
  //           'call_type': ['sync'], // 调用类型，枚举为 sync(同步调用)、async(异步调用),如果同时都有需要先按类型分组，优先执行同步的接口” 
  //           'manual_review': false // 是否接入人工审核，一般情况下同步就没有人工审核
  //         },
  //         { 'column_code': 'content_id', 'column_name': '文件', 'column_type': 'file', 'storage_type': 'contentcenter', 'call_type': ['async'], 'manual_review': true },
  //       ]
  //     }
  //   }
  // ]





  /**
   * 加载 拦截api配置列表
   */
  async function loadConfigJson() {
    try {
      const response = await fetch(_configJsonUrl)
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      const data = await response.json()
      return data
    } catch (error) {
      console.error('Error fetching data:', error)
    }
  }

  /**
   * 初始化
   */
  function create({
    dev = false
  }) {
    if (_isDebugger) console.log('verify.js - create', dev)
    if (_apiInterceptConfig.length && _dev === dev) return
    _dev = dev
    _userInit = true
    init(false)
  }

  async function init(automatic) {
    if (_isDebugger) console.log('verify.js - init', automatic, _userInit, _apiInterceptConfig)
    // 手动初始化后，不执行自动初始化
    if (automatic && _userInit) return

    // 自动初始化，获取环境变量
    if (automatic) {
      _dev = _devUrl.includes(location.hostname) ? true : false
      if (_isDebugger) console.log('verify.js - automatic', location.hostname, _dev)
    }

    // 获取拦截配置
    _configJsonUrl = _dev ? _urlConfig.audit_config_test : _urlConfig.audit_config_prod
    if (_isDebugger) console.log('verify.js - _configJsonUrl', _configJsonUrl)

    _isMobile = isMobile()
    if (_isDebugger) console.log('verify.js - _isMobile', _isMobile)

    // 获取审核接口域名
    if (feasibleUrl.includes(location.hostname)) {
      _baseURL = `//${location.hostname}/training/`
      if (_isDebugger) console.log('verify.js - feasibleUrl', _baseURL, location.hostname)
    } else {
      _baseURL = _isMobile
        ? _dev 
          ? _urlConfig.mobile_test
          : _urlConfig.mobile_prod
        : _dev
          ? _urlConfig.pc_test
          : _urlConfig.pc_prod
    }
    if (_isDebugger) console.log('verify.js - _baseURL', _baseURL, location.hostname)
    
    const result = await loadConfigJson()
    _apiInterceptConfig = result.apiList
  }
  (function () {
    setTimeout(() => {
      init(true)
    }, 300)
  })()

  /**
   * 是否移动端
   */
  function isMobile() {
    var ua = navigator.userAgent.toLowerCase()
    return /iphone|ipad|ipod|android|blackberry|mini|windows\sce|phone|mobile/.test(ua) || /micromessenger/.test(ua)
  }
  function isDebugger() {
    const params = new URLSearchParams(window.location.search);
    return params.has('debugger') && params.get('debugger') === 'true';
  }
  _isDebugger = isDebugger()

  /**
   * @param {object, string} str 
   * @returns 
   */
  function paramsProcessing(obj, type = 'obj') {
    if (typeof obj === 'object' && obj !== null) return obj

    if (typeof obj === 'string') {
      try {
        return JSON.parse(obj)
      } catch (e) {
        if (type === 'obj') {
          return {}
        } else {
          return obj
        }
      }
    }
    return {}
  }

  /**
   * 获取唯一id
   * @returns uid
   */
  function generateUniqueId() {
    return Date.now().toString(36) + Math.random().toString(36).substring(2, 10)
  }
  
  function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
      return obj;
    }
  
    if (obj instanceof Date) {
      return new Date(obj.getTime());
    }
  
    if (obj instanceof Array) {
      return obj.reduce((arr, item, i) => {
        arr[i] = deepClone(item);
        return arr;
      }, []);
    }
  
    if (obj instanceof Object) {
      return Object.keys(obj).reduce((newObj, key) => {
        newObj[key] = deepClone(obj[key]);
        return newObj;
      }, {});
    }
  }

  /**
   * 判断是否为保存草稿
   * @param {object} targetApi 
   * @param {object} data 
   * @returns 
   */
  function isDraftCodeMatch(targetApi, data) {
    if (targetApi === undefined) return false;
    if (!targetApi.draft_column_code) return false;
  
    const columnValue = String(data[targetApi.draft_column_code]);
    if (!columnValue) return false;
  
    return columnValue === targetApi.draft_satus_value;
  }

  /**
   * 获取指定key列表的value
   * @param {object} params 
   * @param {object} fields 
   * @param {object} setting 
   * @returns 
   */
  function getValues(data, fields, setting) {
    // TEST
    // params.course_attachment = [
    //   { file_name: "test.pptx", file_url: "https://contentcenter-1257413033.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/doc/2024/11/6735d3dc-8024-45e0-a771-6d8bd09ba0fd.pptx" },
    //   { file_name: "抽奖用户批量修改模板.pptx", file_url: "https://contentcenter-1257413033.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/doc/2024/11/88170f7e-ec2b-423a-a2fd-47f575d8bb4b.xls" },
    //   { file_name: "签到表(单列)-20230830.docx", file_url: "https://contentcenter-1257413033.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/doc/2024/11/8f7c5adc-1154-453d-b631-49f7fddc8b50.docx" },
    //   { file_name: "新建 Microsoft Excel 工作表.xlsx", file_url: "https://contentcenter-1257413033.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/doc/2024/11/6cc23561-4aec-4a80-9b28-382fcb23645f.xlsx" },
    //   { file_name: "新建 Microsoft Excel 工作表-01.xls", file_url: "https://contentcenter-1257413033.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/doc/2024/11/90d5953e-1163-4639-82cb-a60c6b8cb1b4.xls" },
    //   { file_name: "新建 Microsoft PowerPoint 演示文稿.pptx", file_url: "https://contentcenter-1257413033.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/doc/2024/11/5fc6765d-08ae-45fb-bbf5-3d16b5a5880b.pptx" },
    //   { file_name: "3bbd425f-7a10-46c5-8b98-52ba0a929541.png", file_url: "https://contentcenter-1257413033.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/image/2024/11/038530b9-662a-4c75-bbab-7f5291908c42.png" },
    //   { file_name: "企业微信截图_20230313100716.bmp", file_url: "https://contentcenter-1257413033.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/image/2024/11/b21e0d2a-103e-4a2b-be54-852077cef0a1.bmp" },
    //   { file_name: "2109250006343S5-0-lp.jpg", file_url: "https://contentcenter-1257413033.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/image/2024/11/9b2efb36-619c-467a-8602-540cd238f955.jpg" },
    //   { file_name: "mda-a.mov", file_url: "https://contentcenter-1257413033.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/video/2024/11/3f73a152-4b8d-493f-8661-858f9ad840bb/3f73a152-4b8d-493f-8661-858f9ad840bb.mov" },
    //   { file_name: "屏幕录制 2024-10-22 145525.mp4", file_url: "https://contentcenter-1257413033.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/video/2024/11/1970b9fe-5317-47ee-8401-3ac0a05ab8c2/1970b9fe-5317-47ee-8401-3ac0a05ab8c2.mp4" },
    //   { file_name: "屏幕录制 1111.mp4", file_url: "https://contentcenter-1257413033.cos.ap-guangzhou.myqcloud.com/intranet/public/QLearningService/video/2024/11/1970b9fe-5317-47ee-8401-3ac0a05ab8c2/1970b9fe-5317-47ee-8401-3ac0a05ab8c2.mp41111" },
    // ]
    // params.questionDTOS = [
    //   {
    //     question: {
    //       questionName: '题目一'
    //     }
    //   },
    //   {
    //     question: {
    //       questionName: '题目二'
    //     }
    //   }
    // ]

    let params = deepClone(data)
    let result = {
      act_type: Number(setting.act_type) || '',
      // request_id: generateUniqueId(),
      columns: []
    }

    fields.forEach(field => {
      const keys = field.column_code.split('.')
      let multiple = false
      let singleColumnsObj = {
        column_name: field.column_name,
        column_type: field.column_type,
        value: '',
        call_type: field.call_type,
        manual_review: field.manual_review
      }
      let LastKey = keys[keys.length - 1]

      const value = keys.reduce((obj, key) => {

        // 目标对象为json格式时，对格式处理
        if (obj && typeof obj === 'string') {
          obj = paramsProcessing(obj, 'string')
        }

        if (!obj) {
          console.error(`[verify.js]: The parent object of the '${key}' field does not exist in the request data，The traversed object is empty`)
        } else if (obj && !Array.isArray(obj) && !(key in obj)) {
          console.error(`[verify.js]: '${key}' does not exist in the request data`)
        }

        if (Array.isArray(obj)) {
          // 文本类型，转换成字符串返回
          if (field.column_type === 'text') {
            let textData = obj.map(item => item[key])
            return LastKey === key ? textData.join() : textData
          } else if (field.column_type === 'file'){
            // 多种文件类型，单独校验是否符合审核格式
            const { data, isMultiple } = batchFileProcessing(obj, key, multiple, field)
            multiple = isMultiple
            return data
          } else {
            // 其它类型，批量上传 columns 对象处理
            const { data, isMultiple } = batchOtherTypeProcessing(obj, key, multiple, field, LastKey)
            multiple = isMultiple
            return data
          }
        }
        
        if (obj && obj[key] !== undefined && field.column_type === 'file' && LastKey === key) {
          fileTypeProcessing(obj[key], singleColumnsObj, 2)
        }
        return obj && obj[key] !== undefined ? obj[key] : null
      }, params)

      if (value) {
        if (!multiple) {
          singleColumnsObj.value = singleColumnsObj.column_type === 'text' ? String(value) : value
          result.columns.push(singleColumnsObj)
        } else {
          result.columns = [...result.columns, ...value]
        }
      }
    })
    
    return result
  }

  /**
   * file 类型，批量数据处理
   * @param {object} obj 
   * @param {string} key 
   * @param {boolean} multiple 
   * @param {object} field 
   * @returns 
   */
  function batchFileProcessing(obj, key, multiple, field) {
    let arr = obj.map(item => {
      let columnsObj = {
        column_name: field.column_name,
        column_type: field.column_type,
        value: '',
        call_type: field.call_type,
        manual_review: field.manual_review
      }
      return fileTypeProcessing(item[key], columnsObj, 1)
    })
    arr = arr.filter(v => v !== null)
    arr.length && (multiple = true)
    if (!arr.length) {
      console.error(`[verify.js]: '${field.column_code}' does not exist in the request data`)
    }
    return {
      data: arr.length ? arr : null,
      isMultiple: multiple
    }
  }

  /**
   * 文件类型处理
   * @param {*} value value
   * @param {*} columnsObj 
   * @param {*} type 为1需要返回 columnsObj ，2：修改 columnsObj内 column_type 
   * @returns 
   */
  function fileTypeProcessing(value, columnsObj, type) {
    if (value.includes('http')) {
      let curfileType = _fileTypes.find(v => value.endsWith(v.name))
      if (curfileType !== undefined) {
        if (type === 1) {
          columnsObj.column_type = curfileType.type
          columnsObj.value = value
          return columnsObj
        } else {
          columnsObj.column_type = curfileType.type
        }
      }
      // 不属于可审核的文件类型
      return null
    }
    // 文件value为 content_id
    if (type === 1) {
      columnsObj.column_type = 'file'
      columnsObj.value = value
      return columnsObj
    } else {
      columnsObj.column_type = 'file'
    }
  }

  /**
   * 其它类型，批量数据处理
   * @param {object} obj 
   * @param {string} key 
   * @param {boolean} multiple 
   * @param {object} field 
   * @returns 
   */
  function batchOtherTypeProcessing(obj, key, multiple, field, LastKey) {
    let arr = obj.map(item => {
      if (item[key]) {
        if (key === LastKey) {
          let otherColumnsObj = {
            column_name: field.column_name,
            column_type: field.column_type,
            value: item[key],
            call_type: field.call_type,
            manual_review: field.manual_review
          }
          return otherColumnsObj
        }
        return item[key]
      }
      return null
    })
    arr = arr.filter(v => v !== null)
    if (key === LastKey) arr.length && (multiple = true)
    if (!arr.length) {
      console.error(`[verify.js]: '${field.column_code}' does not exist in the request data`)
    }
    return {
      data: arr.length ? arr : null,
      isMultiple: multiple
    }
  }

  /**
   * 发送表单数据进行审核
   * @param {obj} params 需审核的数据
   * @returns response
   */
  function sendVerifyData (params) {
    if (_isDebugger) console.log('verify.js - sendVerifyData', _baseURL)

    return fetch(`${_baseURL}api/security-audit/user/info/audit`, { 
      method: 'POST',
      headers: {
        // 'Content-Type':'application/x-www-form-urlencoded'
        'Content-Type': 'application/json' // 内容类型为JSON
      },
      // body: new URLSearchParams({ popId: pop_id }).toString(),
      body: JSON.stringify(params),
      credentials: 'include'
    }).then(response => {
      return response.json()
    }).catch(err => {
      console.log('err', err)
      return err
    })

    // 审核接口返回数据格式
    // {
    //   "success": true,
    //   "data": {
    //       "request_id": "xxxxxx",//请求业务的唯一id
    //       "status_code": 1,//审核状态，审核结果状态,-1 异步等待结果 1 不通过（恶意），2 通过
    //       "column_id": "xxxxxx_1",//审核的列的唯一id,status_code为0、1时有值，2和-1为空
    //       "column_name": "标题",//审核的字段名称,status_code为0、1的时候存在，2和-1为空
    //       "result_type": 10001,//审核结果恶意类型，正常100,广告10001,政治20001,色情20002,社会事件20004,暴力20011,低俗20012,违法犯罪20006,欺诈20008,版权20013,谣言20104,其他21000,谩骂20007,暴恐24001,血腥24002,20431红一，status_code为1，时有值，-1为空  
    //       "result_remark": ""//审核结果说明，status_code为0、1时可能有值，-1、2为空  
    //   },
    //   "message": null,
    //   "code": 200
    // }
  }

  /**
   * 请求内容审核拦截
   * @param {str} api 请求的url
   * @param {string} method 请求的方法，get、post、put、delete等
   * @param {object} data 为正常的业务保存数据
   */
  function reviewRouteIntercept(api, method, data) {
    /*
      1. 获取请求的路由
      2. 读取配置文件的配置
      3. 查询路由是否在配置文件中配置
      4. 有配置调用审核方法contentReview
        4.1 审核不通过终止执行，提示给用户端
        4.2 审核通过或异步审核，调用业务保存接口
      5. 无配置调用业务保存接口
    */
    if (_isDebugger) console.log('verify.js - reviewRouteIntercept')
    if (!api) throw new Error ('[verify.js]: The “api” parameter of the reviewRouteIntercept() method cannot be empty') 
    if (!method) throw new Error ('[verify.js]: The “method” parameter of the reviewRouteIntercept() method cannot be empty') 
    
    let targetApi = null
    _sourceDataType = typeof data === 'string' ? 'string' : 'object'
    data = paramsProcessing(data)

    return new Promise((resolve, reject) => {
      targetApi = _apiInterceptConfig.find(v => new RegExp(v.api).test(api) && v.method === method.toLowerCase())

      // 需要拦截的接口
      if (targetApi) {
        // 批量导入审核
        if (targetApi.is_batch_import) {
          // TEST 模拟批量导入数据
          // let arrObj =  [1,2,3,4,5,6,7,8].map(v => {
          //   let batchData = {...data}
          //   batchData.TraverseIndex = v
          //   batchData.draft_code = 1
          //   if (v % 2 === 0) {
          //     batchData.draft_code = 0
          //   }
          //   // if (v === 4) {
          //   //   batchData.course_title = '习大大'
          //   // }
          //   return batchData
          // })
          // console.log('arrObj', arrObj)

          if (!Array.isArray(data)) throw new Error (`[verify.js]: The request data for configuring “is_match_iport” must be of array type, now it is an “${typeof data}”`) 
          batchImportIntercept(data, targetApi, { api, method }).then(res => {
            if (_isDebugger) console.log('课程批量审核', res)
            if (res.success) {
              openTipBox(targetApi.need_faild_alert, res)
            }
            resolve(res)
          }).catch(err => {
            reject(err)
          })
        } else {
          // 单个内容审核
          // 判断是否为保存草稿
          let isDraft = isDraftCodeMatch(targetApi, data)
          if (!isDraft) {
            contentReview(data, targetApi.setting, targetApi.need_faild_alert, { api, method }).then(res => {
              resolve(res)
            }).catch(err => {
              reject(err)
            })
          } else {
            resolve({ success: true, status_code: 200, column_name: null, result_type: null, result_remark: null, data: {}, error_msg: '' })
          }
        }
      } else {
        // ------ 非拦截接口 ------
        resolve({ success: true, status_code: 200, column_name: null, result_type: null, result_remark: null, data: {}, error_msg: '' })
      }
    })
  }

  /**
   * 手动内容审核
   * @param {object} data 为正常的业务保存数据
   * @param {object} setting 参考上面配置文件中的参数，手动调用时传递，配置拦截无需此参数
   * @param {object} showPrompt 是否显示提示框,默认true(true:sdk会自动弹出提示框，false:sdk不自动弹窗，会返回提示数据，由接入方自己实现弹窗(避免弹窗的分格和业务系统不统一))
   */
  function contentReview(data, setting, showPrompt = true, config = { api: '', method: '' }) {  
    /*
    1. 根据setting的配置解析保存数据，组合审核参数，参考下面的“reviewParams示例”
    2. 调用后台的审核接口
    3. 组合审核结果给调用方，参考下面的“机审方法的返回字段示例”
    */
    dataVerification(data, setting)
    if (!_loadingMask) _loadingMask = createLoadingMask()

    return new Promise((resolve, reject) => {
      try {
        _loadingMask && _loadingMask.show()
        devBeforeRequestPrompt(config.api || '_', data, setting.columns, setting)
        const params = getValues(data, setting.columns, setting)

        sendVerifyData(params).then(res => {
          _loadingMask && _loadingMask.hide()
          const returnedData = returnedDataProcessing(res, data)
          devInfoPrompt(config.api || '_', config.method || '_', data, params, res)
          if (res.code === 200 && res.success) {
            openTipBox(showPrompt, res.data)
          }
          resolve(returnedData)
        }).catch((err) => {
          _loadingMask && _loadingMask.hide()
          devInfoPrompt(config.api || '_', config.method || '_', data, params, err)
          reject(err)
        })
      } catch (error) {
        _loadingMask && _loadingMask.hide()
        reject(error)
      }
    })
  }

  /**
   * 批量导入审核处理
   * @param {object} data  为正常的业务保存数据
   * @param {object} targetApi 配置文件的参数
   * @param {object} config 其它配置，用于打印
   * @returns 
   */
  function batchImportIntercept(data, targetApi, config) {
    return new Promise(async (resolve, reject) => {
      const totalData = []
      // 筛选非保存草稿数据
      let waitingReview = data.map(v => {
        let isDraft = isDraftCodeMatch(targetApi, v)
        if (isDraft) totalData.push(v)
        if (!isDraft) return () => contentReview(v, targetApi.setting, false, config)
      })
      waitingReview = waitingReview.filter(v => v !== undefined)

      let result = {
        success: true,
        error_msg: '',
        data: [],
        status_code: '',
        column_name: '',
        result_remark: ''
      }
      let len = waitingReview.length
      for (let i = 0; i < len; i++) {
        try {
          let res =  await waitingReview[i]()
          if (res.success) {
            if (res.status_code === 1) {
              resolve(res, i)
              break;
            }
            result.status_code = result.status_code !== -1 ? res.status_code : -1
            totalData.push(res.data)
          } else {
            resolve(res, i)
            break;
          }
          if (i === waitingReview.length - 1) {
            result.data = totalData
            resolve(result)
          }
        } catch (error) {
          reject(error, i)
          break;
        }
      }
    })
  }

  /**
   * 返回信息处理
   * @param {object} res 响应信息
   * @param {object} data 请求表单数据
   * @returns 
   */
  function returnedDataProcessing(res, data) {
    const returnedData = {
      success: true,// 是否成功调用
      error_msg: '',// 错误信息
      data: {},// status_code === -1时，加了request_id字段的待保存的业务数据对象
      status_code: '',// 审核状态，审核结果状态,-1 异步等待结果 1 不通过（恶意），2 通过，如果没有配置拦截或者没有需要调用信安审核的接口本字段为空
      column_name: '',// 对应字段名称，status_code为1，时有值，-1、0、2为空，如果没有配置拦截或者没有需要调用信安审核的接口本字段为空
      result_type: '',// 审核结果恶意类型，如果没有配置拦截或者没有需要调用信安审核的接口本字段为空
      result_remark: ''// 审核结果说明，status_code为0、1时可能有值，-1、0、2为空，如果没有配置拦截或者没有需要调用信安审核的接口本字段为空
    }
    if (res.code === 200 && res.success) {
      const { column_name, status_code, result_type, result_remark, request_id } = res.data
      returnedData.status_code = status_code || ''
      returnedData.column_name = column_name || ''
      returnedData.result_type = result_type || ''
      returnedData.result_remark = result_remark || ''
      returnedData.data = _sourceDataType === 'string' ? JSON.stringify(data) : data
      if (status_code === -1) {
        data.request_id = request_id
        returnedData.data = _sourceDataType === 'string' ? JSON.stringify(data) : data
      } else if (status_code === 1) {
        returnedData.data = null
      }
      return returnedData
    } else {
      returnedData.success = false
      returnedData.data = null
      returnedData.error_msg = res.message || ''
      return returnedData
    }
  }

  /**
   * 审核结果处理
   * @param {*} api 
   * @param {*} method 
   */
  function reviewResults(api, method, data) {
    data = paramsProcessing(data)
    targetApi = _apiInterceptConfig.find(v => new RegExp(v.api).test(api) && v.method === method.toLowerCase())

    let isDraft = isDraftCodeMatch(targetApi, data)
    if (targetApi !== undefined && !isDraft && targetApi.need_faild_alert) {
      let faildAlert = false
      if (Array.isArray(data)) {
        let index = data.findIndex(v => v.request_id)
        if (index !== -1) faildAlert = true
      } else {
        faildAlert = data.request_id ? true : false
      }
      if (_isDebugger)  console.log('reviewResults', faildAlert, data)
      if (faildAlert) {
        let msg = '内容已提交后台审核，请耐心等待，留意企微“小腾老师”机器人消息提醒。<br/>如有疑问，可联系graywu。'
        createMessageBox({
          title: "提示",
          content: msg,
          confirmText: '确认',
          onConfirm: () => {}
        })
      }
    }
  }

  /**
   * 打开提示弹窗
   * @param {*} show 是否显示
   * @param {*} data 审核接口返回数据
   */
  function openTipBox(show, data) {
    const { column_name, status_code } = data
    if (show && [1].includes(status_code)) {
      let msg = `填写${column_name || '***'}信息中包含敏感内容，无法保存，请仔细检查，修改后再提交。<br/>如有疑问，可联系graywu。`
      createMessageBox({
        title: "提示",
        content: msg,
        confirmText: '返回修改',
        onConfirm: () => {}
      })
    }
    // if (show && [-1, 1].includes(status_code)) {
    //   let msg = '内容已提交后台审核，请耐心等待，留意企微“小腾老师”机器人消息提醒。<br/>如有疑问，可联系graywu。'
    //   if (status_code === 1) {
    //     msg = `填写${column_name || '***'}信息中包含敏感内容，无法保存，请仔细检查并完成修改。<br/>如有疑问，可联系graywu。`
    //   }
    //   createMessageBox({
    //     title: "提示",
    //     content: msg,
    //     confirmText: status_code === 1 ? '返回修改' : '确认',
    //     onConfirm: () => {}
    //   })
    // }
  }

  function dataVerification(data, setting) {
    if (typeof data !== 'object' || data === null) throw new Error ('[verify.js]: The format of the “data” parameter is incorrect, it is not an object type') 
    if (typeof setting !== 'object' || setting === null) throw new Error ('[verify.js]: The format of the “setting” parameter is incorrect, it is not an object type') 
    if (!setting.act_type) throw new Error ('[verify.js]: The “setting.act_type” field in the configuration document cannot be empty')
    if (!setting.columns.length) throw new Error ('[verify.js]: The “setting.columns” configuration list in the configuration document cannot be empty')
  }

  /**
   * 开发环境请求前信息提示
   * @param {string} api 
   * @param {object} params 
   * @param {object} fields 
   * @param {object} setting 
   * @returns 
   */
  function devBeforeRequestPrompt(api, params, fields, setting) {
    if (!_dev) return
    console.groupCollapsed('[verify - request]', api)
    console.log('api - params', params)
    console.log('fields', fields)
    console.log('setting', setting)
    console.groupEnd()
  }

  /**
   * 开发环境审核结果信息提示
   * @param {string} api 
   * @param {string} method 
   * @param {object} data 
   * @param {object} params 
   * @param {object} response 
   * @param {number} verify 
   * @returns 
   */
  function devInfoPrompt(api, method, data, params, response) {
    if (!_dev) return
    console.groupCollapsed(`[verify - response]`, api)
    console.log('method', method)
    console.log('api - params', data)
    console.log('request - params', params)
    console.log('response - params', response)
    console.groupEnd()
  }

  /**
   * 创建提示弹窗
   * @param {*} param
   * @returns 
   */
  function createMessageBox({ title, content, confirmText = "确认", cancelText = '取消', onConfirm, onCancel }) {
    // Create the main elements
    const overlay = document.createElement("div");
    const messageBox = document.createElement("div");
    const header = document.createElement("div");
    const titleIcon = document.createElement("img")
    const titleSpan = document.createElement("span")
    const body = document.createElement("div");
    const footer = document.createElement("div");
    const confirmButton = document.createElement("button");
    let cancelButton;
    if (onCancel) {
      cancelButton = document.createElement("button");
    }

    // Overlay styles
    overlay.style.position = "fixed";
    overlay.style.top = "0";
    overlay.style.left = "0";
    overlay.style.width = "100%";
    overlay.style.height = "100%";
    overlay.style.backgroundColor = "rgba(0, 0, 0, 0.5)";
    overlay.style.opacity = "0";
    overlay.style.pointerEvents = "none";
    overlay.style.transition = "opacity 0.3s ease";
    overlay.style.zIndex = "9999";

    // MessageBox styles
    messageBox.style.position = "absolute";
    messageBox.style.left = "50%";
    messageBox.style.top = "40%";
    messageBox.style.width = !_isMobile ? "480px" : "311px";
    messageBox.style.backgroundColor = "#fff";
    messageBox.style.borderRadius = !_isMobile ? "8px" : "12px";
    messageBox.style.boxShadow = "0 4px 8px rgba(0, 0, 0, 0.2)";
    messageBox.style.transform = "translate(-50%, -50%) translateY(-20px)";
    messageBox.style.opacity = "0";
    messageBox.style.transition = "opacity 0.3s ease, transform 0.3s ease";
    messageBox.style.padding = !_isMobile ? "32px" : "24px 17px";

    // Header styles
    header.style.fontSize = !_isMobile ? "16px" : "18px";
    header.style.lineHeight = !_isMobile ? "24px" : "26px";
    header.style.fontWeight = "bold";
    header.style.marginBottom = "16px";
    header.style.display = "flex";
    header.style.alignItems = "center";
    if (_isMobile) header.style.justifyContent = "center";
    titleIcon.style.width = "24px"
    titleIcon.style.height = "24px"
    titleIcon.style.display = "block"
    titleIcon.style.marginRight = "8px"
    titleIcon.src="https://xue.m.tencent.com/ql/common/alert-icon.png"
    titleSpan.textContent = title;

    // Body styles
    body.style.fontSize = !_isMobile ? "14px" : "16px";
    body.style.lineHeight = !_isMobile ? "22px" : "24px";
    body.style.color = "#ED7B2F";
    body.style.marginBottom = "24px";
    if (_isMobile) body.style.textAlign = "center";
    body.innerHTML = content;

    // Footer styles
    if (!_isMobile) footer.style.textAlign = "right";
    if (_isMobile) {
      footer.style.padding = "0 8px";
      footer.style.display = "flex";
    }

    // Confirm button styles
    confirmButton.textContent = confirmText;
    confirmButton.style.background = "#0052D9";
    confirmButton.style.border = "none";
    confirmButton.style.borderRadius = !_isMobile ? "3px" : "6px";
    confirmButton.style.color = "#fff";
    confirmButton.style.padding = !_isMobile ? "5px 16px" : "8px 16px";
    confirmButton.style.cursor = "pointer";
    confirmButton.style.fontSize = !_isMobile ? "14px" : "16px";
    confirmButton.style.lineHeight = !_isMobile ? "22px" : "24px";
    if (_isMobile) {
      confirmButton.style.flex = "1";
      confirmButton.style.fontWeight = "600";
    }
    confirmButton.addEventListener("mouseenter", () => {
      confirmButton.style.background = "#0c6cd3";
    });
    confirmButton.addEventListener("mouseleave", () => {
      confirmButton.style.background = "#0052D9";
    });
    
    // Cancel button styles
    if (onCancel) {
      cancelButton.textContent = cancelText;
      cancelButton.style.background = !_isMobile ? "#E7E7E7" : "#F2F3FF";
      cancelButton.style.border = "none";
      cancelButton.style.borderRadius = !_isMobile ? "3px" : "6px";
      cancelButton.style.color = !_isMobile ? "#000000e6" : "0052D9";
      cancelButton.style.padding = !_isMobile ? "5px 16px" : "8px 16px";
      cancelButton.style.cursor = "pointer";
      cancelButton.style.fontSize = !_isMobile ? "14px" : "16px";
      cancelButton.style.lineHeight = !_isMobile ? "22px" : "24px";
      cancelButton.style.marginRight = !_isMobile ? "8px" : "12px";
      if (!_isMobile) cancelButton.style.border = "1px solid #E7E7E7";
      if (_isMobile) {
        cancelButton.style.flex = "1";
        cancelButton.style.fontWeight = "600";

      }
      cancelButton.addEventListener("mouseenter", () => {
        cancelButton.style.background = "#cecece";
        cancelButton.style.color = "#333";
      });
      cancelButton.addEventListener("mouseleave", () => {
        cancelButton.style.background = !_isMobile ? "#E7E7E7" : "#F2F3FF";
        cancelButton.style.color = !_isMobile ? "#000000e6" : "0052D9";
      });
    }

    // Append elements
    header.appendChild(titleIcon);
    header.appendChild(titleSpan);
    cancelButton && footer.appendChild(cancelButton);
    footer.appendChild(confirmButton);
    messageBox.appendChild(header);
    messageBox.appendChild(body);
    messageBox.appendChild(footer);
    overlay.appendChild(messageBox);
    document.body.appendChild(overlay);

    // Show animation
    setTimeout(() => {
      overlay.style.opacity = "1";
      overlay.style.pointerEvents = "auto";
      messageBox.style.opacity = "1";
      messageBox.style.transform = "translate(-50%, -50%) translateY(0)";
    }, 0);

    // Event listeners
    cancelButton && cancelButton.addEventListener("click", () => {
      hideMessageBox();
      if (onCancel) onCancel();
    });

    confirmButton.addEventListener("click", () => {
      hideMessageBox();
      if (onConfirm) onConfirm();
    });

    function hideMessageBox() {
      messageBox.style.opacity = "0";
      messageBox.style.transform = "translate(-50%, -50%) translateY(-20px)";
      overlay.style.opacity = "0";
      overlay.style.pointerEvents = "none";
      setTimeout(() => {
        document.body.removeChild(overlay);
      }, 300); // Wait for the animation to finish
    }

    return { hide: hideMessageBox };
  }

  /**
   * 创建加载遮罩层
   * @returns 
   */
  function createLoadingMask() {
    // 创建遮罩层
    const mask = document.createElement("div");
    mask.id = "loading-mask";
    mask.style.position = "fixed";
    mask.style.top = "0";
    mask.style.left = "0";
    mask.style.width = "100%";
    mask.style.height = "100%";
    mask.style.backgroundColor = "rgba(0, 0, 0, 0.5)"; // 半透明背景
    mask.style.zIndex = "9999"; // 确保在最上层
    mask.style.display = "none"; // 默认隐藏
    mask.style.justifyContent = "center";
    mask.style.alignItems = "center";
    mask.style.pointerEvents = "none"; // 阻止事件穿透
    mask.style.transition = "opacity 0.3s"; // 动画效果

    // 添加加载动画
    const loader = document.createElement("div");
    loader.style.width = "50px";
    loader.style.height = "50px";
    loader.style.border = "5px solid #f3f3f3"; // 外圈颜色
    loader.style.borderTop = "5px solid #3498db"; // 加载动画颜色
    loader.style.borderRadius = "50%";
    loader.style.animation = "verifySpin 1s linear infinite"; // 旋转动画

    mask.appendChild(loader);
    document.body.appendChild(mask);

    // 添加旋转动画的 CSS
    const style = document.createElement("style");
    style.innerHTML = `
        @keyframes verifySpin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);

    // 显示遮罩层
    function showLoadingMask() {
      mask.style.display = "flex";
      mask.style.opacity = "1";
      mask.style.pointerEvents = "auto"; // 阻止点击
    }

    // 隐藏遮罩层
    function hideLoadingMask() {
      mask.style.opacity = "0";
      mask.style.pointerEvents = "none";
      setTimeout(() => {
        mask.style.display = "none"; // 动画结束后隐藏
      }, 300); // 与 transition 的时间保持一致
    }

    return {
      show: showLoadingMask,
      hide: hideLoadingMask
    }
  }


  
  return {
    create,
    paramsProcessing,
    reviewRouteIntercept,
    contentReview,
    reviewResults,
    createMessageBox
  }
})()
