<template>
  <div class="give-tab-box">
    <div class="tab-content">
      <!-- <div class="tab-content_tips">
        当前已送出 <span class="color_b">{{presentRecordNumber}}</span> 张学霸卡（共可送出 10 张），获得奖励 <span class="color_b">{{numberOfRewards}}</span> 张「{{Config.card_name}}」专用卡，请在本页面上方【学霸卡领用详情】模块查看。
      </div> -->
      <!-- <div class="tab-content_null">
        <div class="tab-content_null-left" >
          <img :src="require('../../../../assets/img/mobile/geekBang/null-data-left.png')" alt="">
          <div class="tab-content_null-footer">
            暂未解锁发送学霸卡功能
          </div>
        </div>
      </div> -->
      <div class="tab-content_btn" @click="openGive" :style="!disabled ? 'opacity: 0.2;' : '' " :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', `送出学霸卡`)">
        <img :src="require('../../../../assets/img/mobile/geekBang/give.png')" alt="">
        <span >送出学霸卡</span>
      </div>
      <!-- <div class="tab-content_item" v-for="(item, index) in tabList" :key="index">
        <div class="tab-content_item-left"><img :src="userImgUrl(item.staff_name)" alt=""></div>
        <div class="tab-content_item-right">
          <div class="name">{{activeTab === 'left' ? '向' : '来自'}}：{{item.staff_name}}</div>
          <div class="number1">送{{activeTab === 'left' ? '出' : '给你'}} 1 张学霸卡</div>
          <div class="number2">奖励：<span v-if="activeTab === 'left'">{{item.reward_amt || 0}}</span> <span v-else>1</span> 张「{{Config.card_name}}」专用卡</div>
        </div>
      </div> -->

    </div>
    <van-popup class="copy-pop" v-model="show" closeable position="bottom" @confirm="doCopy">
      <div class="share-content">
        <div class="share-content-title">分享至 <span><i class="red"> * </i> 注意此活动仅限腾讯员工参与</span></div>
        <div class="tips">分享学霸卡，邀请小伙伴兑换价值百元的【{{cardName}}】精品好课。分享成功您还有机会获得更多学霸卡。
        </div>
        <div class="share-box">
          <div class="share-wx share-item" @click="handlerWxShear" :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', '微信')">
            <img :src="require('@/assets/img/mobile/geekBang/wx-shear.png')" alt="">
            <span class="text">微信</span>
          </div>
          <div class="share-url share-item" @click="doCopy" :dt-areaid="dtBotton('area')" :dt-eid="dtBotton('eid')" :dt-remark="dtBotton('remark', '复制链接')">
            <img :src="require('@/assets/img/mobile/geekBang/url-shear.png')" alt="">
            <span class="text">复制链接</span>
          </div>
        </div>
      </div>
    </van-popup>
  </div>

</template>

<script>
import { mapState } from 'vuex'
import { Toast } from 'vant'
export default {
  // props: ['Config', 'listObj', 'consumePoint', 'numberOfRewards'],
  // props: ['disabled', 'xueBaCardConfig'],
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    xueBaCardConfig: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      activeTab: 'left',
      show: false
    }
  },
  computed: {
    ...mapState(['userInfo']),
    // tabList() {
    //   return this.activeTab === 'left'
    //     ? this.listObj.presentRecordList
    //     : this.listObj.presentPassiveRecordList
    // },
    // userImgUrl() {
    //   return (val) => {
    //     if (!val) return ''
    //     let staffname = val.split(',')[0].split('(')[0]
    //     return `https://rhrc.woa.com/photo/150/${staffname}.png`
    //   }
    // },
    // presentRecordNumber() {
    //   return this.listObj.presentRecordList.length
    // },
    // isDisabled() {
    //   return this.consumePoint === 0
    // },
    activityId() {
      return this.$route.query.activityId
    },
    userImgUrl() {
      return (val) => {
        if (!val) return ''
        let staffname = val.split(',')[0].split('(')[0]
        return `https://learn.woa.com/rhrc/150/${staffname}.png`
      }
    },
    presentRecordNumber() {
      return this.listObj.presentRecordList.length
    },
    isDisabled() {
      return this.consumePoint === 0
    },
    dtBotton() {
      return (type, name) => {
        const { audience_id, audience_name, activity_name } = this.xueBaCardConfig
        if (type === 'area') {
          return `area_${audience_id}_receive`
        } else if (type === 'eid') {
          return `element_${audience_id}_receive`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: `${activity_name}活动落地页`,
            page_type: `学霸卡活动落地页`,
            container: audience_name,
            container_id: '',
            click_type: 'button',
            content_name: name,
            terminal: 'H5'
          })
        }
      }
    },
    cardName() {
      return this.xueBaCardConfig.card_name || ''
    }
  },
  methods: {
    changeTab(val) {
      this.activeTab = val
    },
    handlerWxShear() {
      // this.$emit('update:isShowFixedShear', true)
      this.$parent.isShowHint = true
    },
    openGive() {
      if (!this.disabled) return
      // this.$emit('openGive')
      this.show = true
    },
    // 获取复制链接文案
    getStr() {
      const urlText = `https://sdc.qq.com/s/hxbgLe?scheme_type=xueba&type=${this.xueBaCardConfig.acct_type_code}&activityId=${this.activityId}&staff_name=${this.userInfo.staff_name}&staff_id=${this.userInfo.staff_id}`
      const strMap = {
        'sanjieke': `新春到好礼到！好友送知识，邀您共成长！${this.userInfo.staff_name}向您赠送了一张价值百元的三节课新春学霸卡，400+精品付费课程免费兑换，点击链接即可领取兑换(${urlText})`,
        'guanaiyueXueba': `关爱月，共成长！${this.userInfo.staff_name}向您赠送了一张价值百元的关爱月特别学霸卡，100门新课限时1个月免费兑换学习，点击链接即可领取兑换(${urlText})`
      }
      const str = strMap[this.xueBaCardConfig.acct_type_code] || ''
      return {
        urlText,
        str
      }
    },
    // 复制链接
    doCopy() {
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      const { str } = this.getStr()
      input.value = str
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      Toast({
        message: '已复制链接，去粘贴分享吧',
        icon: 'passed'
      })
      // 复制后移除输入框
      input.remove()
    }
  }
}
</script>

<style lang="less" scoped>
.color_b {
  color: #0052d9ff;
}
.tab-container {
  display: flex;
  border-radius: 16px 16px 0 0;
  height: 46px;
  overflow: hidden;
  align-items: flex-end;
  width: 100%;
  position: relative;
  z-index: 1;
  // background-color: #F8F8F8;
}
.tab {
  cursor: pointer;
  width: 50%;
  color: #999;
  font-weight: bold;
  width: 100%;
  font-size: 12px;
  height: 40px;
  line-height: 40px;
  position: relative;
  text-align: center;
  background-color: #f8f8f8;
  sup {
    font-size: 22px;
    color: #d54941ff;
  }
}
.tab.active-left {
  height: 46px;
  line-height: 46px;
  font-size: 14px;
  font-weight: 600;
  color: #000000e6;
  //   background-color: #fff;
  border-radius: 0 16px 0 0;
  background: url('../../../../assets/img/mobile/geekBang/left-select.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.tab.active-right {
  height: 46px;
  line-height: 46px;
  font-size: 14px;
  font-weight: 600;
  color: #000000e6;
  border-radius: 0 16px 0 0;
  background: url('../../../../assets/img/mobile/geekBang/right-select.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.tab.active-left::after,
.tab.active-right::after {
  content: '';
  position: absolute;
  left: 50%;
  border-radius: 2px;
  transform: translateX(-50%);
  bottom: 0;
  width: 16px;
  height: 4px;
  background-color: #0052d9;
}
.tab-content {
  background-color: #fff;
  // padding: 20px;
  &_tips {
    color: #666666ff;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    background: #f9f9f9;
    padding: 6px 12px;
    border-radius: 4px;
  }
  &_btn {
    display: flex;
    height: 48px;
    // line-height: 48px;
    padding: 6px 12px;
    justify-content: center;
    align-items: center;
    gap: 8px;
    align-self: stretch;
    border-radius: 8px;
    background: #ffe6c1;
    color: #cb5500ff;
    font-size: 16px;
    font-weight: 600;
    margin-top: 12px;
    img {
      width: 16px;
      height: 16px;
    }
    span {
      height: 16px;
    }
  }
  &_btn_disabled {
    background: #ffe6c1ff;
    color: #cb5500ff;
  }
  &_item {
    display: flex;
    padding: 8px 12px;
    border-radius: 8px;
    border: 0.5px solid #eee;
    margin-top: 12px;
    background: linear-gradient(
      0deg,
      #fdffff 36.9%,
      #fbfeff 77.92%,
      #f9fffe 100%
    );
    &-left {
      margin-right: 6px;
      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
      }
    }
    &-right {
      line-height: 20px;
      font-size: 12px;
      .name {
        align-self: stretch;
        color: #000000e6;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: 22px;
      }
      .number1 {
        align-self: stretch;
        color: #666666ff;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
      .number2 {
        color: #999999ff;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
      }
    }
  }
  &_null {
    &-right {
      img {
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        width: 128px;
        height: 128px;
        margin: 12px 0 8px;
      }
    }
    &-footer {
      color: #666666ff;
      text-align: center;
      font-family: 'PingFang SC';
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
    }
    &-left {
      img {
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        width: 128px;
        height: 128px;
        margin: 8px 0;
      }
    }
  }
}
.back-c {
  position: absolute;
  height: 40px;
  width: 100%;
  z-index: -1;
  background-color: #f8f8f8;
}
.copy-pop {
  border-radius: 12px 12px 0 0;
}
.red {
  color: red;
}
.share-content {
  padding: 12px 16px;
  &-title {
    color: #000000e6;
    font-family: 'PingFang SC';
    font-size: 15px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    opacity: 0.9;
    margin-top: 6px;
    span {
      color: #00000080;
      font-family: 'PingFang SC';
      font-size: 10px;
      font-style: normal;
      font-weight: 400;
      line-height: 12px;
      margin-left: 12px;
    }
  }
  .tips {
    color: #00000080;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
    padding: 12px 0;
  }
  .share-box {
    display: flex;
    // border-bottom: 1px solid #e7e7e7;
    padding-bottom: 12px;
    .url-text {
      display: none;
    }
    .share-item {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      text-align: center;
      margin-right: 28px;
      img {
        width: 48px;
        height: 48px;
      }
      .text {
        color: #000000e6;
        text-align: center;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        margin-top: 8px;
      }
    }
  }
}
</style>
