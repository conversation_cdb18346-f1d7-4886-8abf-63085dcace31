<template>
  <div class="video-container">
    <div id="main-area" :class="['main-fixed',{ 'h100vh' : isFullscreen }, { 'flex-box': isCourse && lessonExpand }]">

      <Scorm
        v-if="['Flash', 'Scorm', 'Doc'].includes(courseData.course_type)"
        :class="[{'fullScreen-video-box': isFullscreen}, 'scorm-box']"
        :content_id="courseData.content_id"
        :courseData="courseData"
        @handleScormRecord="handleScormRecord"
      />

      <div v-else-if="['Video', 'Video-2d', 'Video-3d', 'Video-ppt', 'Audio'].includes(courseData.course_type)" :class="[{'play-video-video-isVertical': isFullscreen && videoIsVertical}, 'play-video']">
        <Video
          :class="['video-box', {'fullScreen-video-box': isFullscreen}]"
          ref="videoRef"
          :content_id.sync="courseData.content_id"
          @getCurCaption="getCurCaption"
          @handleRecord="handleRecord"
          @getCurrentTime="getCurrentTime"
          @onPlay="onPlay"
          @onEnded="onEnded"
          @onPause="onPause"
          @fullscreenchange="fullscreenchange"
          @getVideoInfo="getVideoInfo"
          @loadVideoSucess="loadVideoSucess"
          :playTime="playTime"
          :volumePanel="false"
          :autoPlay="false"
          :vertical="!isPad && isFullscreen && (isInteractive || !videoIsVertical)"
          :fullscreenToggle="!isInteractive"
          :progressControl="!courseData.limit_progress_bar"
          :source_src="courseData.file_url"
          :playbackRateMenuButton="!isInteractive"
        >
        </Video>

        <!-- 手动实现控制栏等拓展功能 -->
        <video-contral-component ref="videoContralComponent" v-if="isVideoLoade && videoTotalTime" :videoTotalTime="videoTotalTime" :isFullscreen="isFullscreen" :captionData="VideoCaptionData"
          :videoIsVertical="videoIsVertical" :courseData="courseData" :isInteractive="isInteractive" :isRequest="isRequest" :chapterSummaryList="chapterSummaryList" :isPad="isPad"
        />

        <!-- 跳转提示 -->
        <div v-show="isVideoLoade && isCurrentTimeShow && showTime > 0" :class="[
          'common-time-tips',
          {'current-time-tips': moocLang === 'zh-cn' && !isFullscreen},
          {'en-current-time-tips': moocLang === 'en-us' && !isFullscreen},
          {'bottom-42': !isFullscreen && courseData.limit_progress_bar},
          {'fullscreen-tips': isFullscreen && (isInteractive || !videoIsVertical) && moocLang === 'zh-cn'  },
          {'en-fullscreen-tips': isFullscreen && (isInteractive || !videoIsVertical) && moocLang === 'en-us' },
          {'fullscreen-Vertical-tips': isFullscreen && !isInteractive && videoIsVertical && moocLang === 'zh-cn'  },
          {'en-fullscreen-Vertical-tips': isFullscreen && !isInteractive && videoIsVertical && moocLang === 'en-us' },
          {'fullscreen-Vertical-tips-pad': isFullscreen && isPad && moocLang === 'zh-cn'  },
          {'en-fullscreen-Vertical-tips-pad': isFullscreen && isPad && moocLang === 'en-us' }
          ]">
          <span>{{ `已为您定位至${timeToString(showTime || 0)}` }}&nbsp;</span>
          <!-- <span>{{ $langue('NetCourse_PlayAt', {seconds: showTime, defaultText: `上次播放至${showTime || 0}秒` }) }}&nbsp;</span> -->
        </div>

      </div>
      <div v-else class="video-box">
        <van-image lazy fit="fill" :src="courseData.photo_url ? courseData.photo_url : require('@/assets/img/default_bg_img.png')">
        </van-image>
      </div>

    </div>
    <!-- tab栏 -->
    <div class="tabs-card" v-show="!lessonExpand && !isFullscreen">
      <div class="tabs">
        <div
        :class="['item-tab', {'active-item-tab': activeKey === item.key}]"
        v-for="item in tabList"
        :key="item.key"
        @click="changeTabs(item.key)"
        :dt-remark="dtCommon('remark', item.text)"
        :dt-areaid="dtCommon('areaid', item.text)"
        :dt-eid="dtCommon('eid', item.text)"
        >
          <span>{{ $langue(item.title, { defaultText: item.text }) }}</span>
          <span v-show="activeKey === item.key" class="active-line"></span>
        </div>
      </div>
      <div class="back-play" v-if="!isInteractive && !isRequest && false">
        后台播放
        <!-- <div class="switch-content" :class="{'switch-content-close': !backPlay}"> -->
        <div class="switch-content">
          <van-switch v-model="backPlay" @change="backPlayChange" size="18" active-color="#0052D9" inactive-color="#DCDCDC" />
        </div>
      </div>
    </div>

    <div class="fixed-main-content" id="scroll-main-content">
      <!-- 详情 -->
      <desContent
      ref="desContentRef"
      v-if="activeKey === 'des'"
      :isPreview="isPreview"
      :courseData.sync="courseData"
      :chapterSummaryList="chapterSummaryList"
      :studyRecordQuery="studyRecordQuery"
      @toComment="activeKey = 'comment'"
      @toChapterPosition="changePlaytime"
      >
      </desContent>

      <!-- 字幕 -->
      <textDraft
      v-if="activeKey === 'text' && captionData && captionData.length"
      ref="textDraftRef"
      :chapterSummaryList="chapterSummaryList"
      :captionData.sync="captionData"
      :captionCurTime="studyRecordQuery.my_study_progress"
      @toChapterPosition="changePlaytime"
      :courseData.sync="courseData"
      >
      </textDraft>

      <!-- 笔记 -->
      <notes v-if="activeKey === 'note'" :courseData="courseData"></notes>

      <!-- 延伸学习 -->
      <extendLearning v-if="activeKey === 'extand'" :courseData="courseData" :extandList="extandList"></extendLearning>

      <!-- 底部操作按钮 -->
      <bottomNav @handleBtChoice="handleBtChoice" ref="bottomNavRef" :courseData="courseData"></bottomNav>
    </div>
    <!-- 听音频 -->
    <div id="drag-service" v-show="audioUrl && !lessonExpand && !isFullscreen">
      <span class="icon"></span>
      <!-- <span>{{ $langue('NetCourse_ListenVideo', { defaultText: '听视频' }) }}</span> -->
      <span>{{ $langue('NetCourse_NetCourseListenAudio', { defaultText: '听音频' }) }}</span>
    </div>
    <div id="drag-service-ai-bot" v-if="($route.query.from !== 'mooc')">
      <div class="img_content">
        <div class="chat_img1">
          <!-- <img class="bot-icon2" :src="require('@/assets/img/bot-icon2.png')" alt=""> -->
          <img class="bot-icon" :src="require('@/assets/img/xiaoQ.png')" alt="">
        </div>
      </div>
    </div>
    <!-- 双语按钮 -->
    <!--
      <div v-if="!isFormMooc && !isFullscreen" id="drag-lang" :class="['drag-lang', { 'lang-icon-full': isFullscreen && (isInteractive || !videoIsVertical) }]">
    -->
    <!-- <div v-if="!isFormMooc && !isFullscreen" id="drag-lang" :class="['drag-lang']">
      <span :class="[moocLang === 'en-us' ? 'el-icon-zh' : 'el-icon-en', 'icon']"></span>
      <span class="text">{{ moocLang === 'en-us' ? '中文' : 'Eng' }}</span>
    </div> -->

    <!-- 横屏提示 -->
    <van-popup class="vertical-popup" v-model="verticalTips" :close-on-click-overlay="false">
      <div class="bg">
        <img class="vertical" src="@/assets/img/mobile/vertical.png" alt="" />
      </div>
      <div class="bottom">
        <div class="text">
          {{ $langue('NetCourse_SupportHorizontalScreen', { defaultText: '当前内容为互动课程，仅支持横屏播放' }) }}
        </div>
        <div class="text">{{ $langue('NetCourse_HorizontalPhone', { defaultText: '请横置手机观看' }) }}</div>
        <span class="confirm" @click="verticalClose">{{$langue('NetCourse_Ok', { defaultText: '好的' })}}</span>
      </div>
    </van-popup>

    <!-- 横屏互动 -->
    <van-popup v-if="showInteractive" class="interactive-popup" :class="{'interactive-popup-pad': isPad}" v-model="showInteractive" :close-on-click-overlay="false">
      <div class="content">
        <div class="title">{{ questionData?.title || '' }}</div>
        <div class="desc" v-if="questionData?.introduction" v-html="questionData?.introduction"></div>
        <div class="question" v-for="(item, index) in questionData?.select_content" :key="item.question_id">
          <van-radio-group v-if="item?.choose_type_config?.type === 'single' || (item?.active_type === 'vote' && item?.vote_type_config?.can_max_vote_nums === 1)" v-model="selected[index]" checked-color="#0052D9">
            <van-radio v-for="(option, index) in item.options" :key="index" :name="option.option_value">{{ option.option_text }}</van-radio>
          </van-radio-group>
          <van-checkbox-group v-else v-model="selected[index]">
            <van-checkbox v-for="(option, optionIndex) in item.options" :key="optionIndex" :name="option.option_value" shape="square">{{ option.option_text }}</van-checkbox>
          </van-checkbox-group>
        </div>
      </div>
      <div class="bottom">
        <span :class="['continue', { 'continue-disabled': continueDisabled }]" @click="onContinue">
          {{ $langue('Mooc_Common_Alert_ContinueStudy', { defaultText: '继续学习' }) }}
        </span>
      </div>
    </van-popup>
    <!-- 评论弹窗 -->
    <commentPopup :commentShow.sync="commentShow" :courseData.sync="courseData"></commentPopup>
    <!-- 添加课单 -->
    <addCousePopup :show.sync="addCourseShow" :courseData.sync="courseData"></addCousePopup>
    <!-- 分享提示框 -->
    <shareTipDialog v-model="showShareDialog" :portraitScreen="true"></shareTipDialog>
  </div>
</template>
<script>
import { Video, Scorm } from '@/components/index'
import {
  getNetCourseInfo,
  netViewRecord,
  getNetCourseChapterList,
  getCoursePreviewInfo,
  getExtanContentList,
  realtimeDocParse
} from 'config/api.conf'
import {
  urlForDownloadApi,
  saveInteractionRecord,
  getCourseInteraction
} from 'config/mooc.api.conf'
import desContent from './desContent.vue'
import textDraft from './textDraft.vue'
import notes from './notes.vue'
import axios from 'axios'
import MoocJs from 'sdc-moocjs-integrator'
import env from 'config/env.conf.js'
import { Toast } from 'vant'
import { mapState } from 'vuex'
import translateLang from '@/views/mobile/mixins/translateLang.js'
import videoContralComponent from './child/video-contral-component.vue'
import shareTipDialog from './child/shareTipDialog.vue'
import addCousePopup from './child/addCousePopup.vue'
import bottomNav from './child/bottomNav.vue'
import commentPopup from './child/commentPopup.vue'
import extendLearning from './extendLearning.vue'
import moment from 'moment'
const envName = env[process.env.NODE_ENV]
export default {
  mixins: [translateLang],
  components: {
    Video,
    Scorm,
    desContent,
    textDraft,
    notes,
    videoContralComponent,
    shareTipDialog,
    addCousePopup,
    bottomNav,
    commentPopup,
    extendLearning
  },
  data() {
    return {
      isPreview: false, // 是否是预览
      videoTotalTime: 0, // 视频总时长
      initTop: 0, // 听视频的当前top值
      initLangTop: 0, // 语言切换的top值
      activeKey: 'des',
      courseData: {
        content_type: '',
        comment_count: 0,
        fav_count: 0
      },
      chapterSummaryList: [], // 章节列表
      captionData: [],
      VideoCaptionData: [], // 字幕文件数据 用于视频上显示，需要切分长数据
      countTimer: null,
      viewTimer: null,
      studyRecordQuery: {
        act_id: this.$route.query.course_id,
        area_id: this.$route.query.area_id || '',
        from: this.$route.query.jump_from || this.$route.query.from || '',
        learn_record_id: 0,
        is_finish: 0,
        is_review: 0,
        total_study_time: 0,
        my_study_progress: '',
        course_duration: 0
      },
      duration: 0,
      playTime: 0,
      showTime: 0, // 用来显示的时间
      extandList: [],
      loading: false,
      lessonExpand: false,
      isInteractive: false, // 是否开启互动
      verticalTips: false,
      showInteractive: false,
      selected: [],
      mainAreaHeight: 304,
      interactionData: {},
      continueDisabled: true,
      questionData: {},
      audioUrl: null,
      interactionTime: -1, // 互动弹窗的时间，禁止跨过互动
      isPageHidden: false, // 是否熄屏
      lastTime: 0, // 上一次播放的时间，禁止快进快退
      lastHiddenTime: 0, // 上次熄屏的播放时间，禁止熄屏后快进快退
      isFullscreen: false,
      isCurrentTimeShow: false,
      chapterData: {
        chapter_content_list: [],
        chapter_ai_content_list: []
      },
      isVideoLoade: false, // 视频是否加载完成
      isPlaying: false, // 是否播放状态
      isVideoFinish: false, // 本轮视频播放是否结束
      speedArray: [0.5, 1, 1.25, 1.5, 2], // 倍速容器
      speedIndex: 1, // 当前播放速度
      hiddentralBox: false, // 播放时点击屏幕显示或者隐藏视频操作栏
      hiddenTimeId: null, // 播放时点击隐藏视频操作栏的计时器
      showCustomerCaption: true, // 是否显示字幕(手动实现的字幕)
      subtitlesText: '', // 当前时间所显示的字幕
      videoInfo: {}, // 视频信息
      isChapterFirstRequest: true, // 是否是第一次获取章节数据
      bottomIconInfo: [
        { icon: 'home', label: '首页' },
        { icon: 'comment', label: '评价' },
        { icon: 'add', label: '添加' },
        { icon: 'share', label: '分享' }
      ],
      backPlay: true, // 后台播放开关
      scrollEl: null,
      commentShow: false,
      isLike: false,
      showShareDialog: false,
      addCourseShow: false,
      documentHidden: false, // 是否切到手机桌面（后台播放时用于判断是否和小程序通讯)
      entryTime: '',
      isPad: false // 是否是pad端
    }
  },
  computed: {
    ...mapState(['moocLang']),
    tabList() {
      const list = [
        { title: '', key: 'des', text: '详情' }
        // { title: '', key: 'text', text: '字幕检索' },
        // { title: 'NetCourse_Note', key: 'note', text: '笔记' },
        // { title: 'NetCourse_Comment', key: 'comment', text: '讨论' }
        // { title: 'NetCourse_Extended', key: 'extand', text: '延伸学习' }
      ]
      if (this.captionData?.length && this.courseData.show_comments !== false) {
        list.splice(1, 0, {
          title: '',
          key: 'text',
          text: '字幕检索'
        })
      }
      list.splice(2, 0, {
        title: 'NetCourse_Note',
        key: 'note',
        text: '笔记'
      })
      if (this.extandList?.length) {
        list.splice(4, 0, {
          title: 'NetCourse_Extended',
          key: 'extand',
          text: '延伸学习'
        })
      }
      return list
    },
    course_id() {
      return this.$route.query.course_id || ''
    },
    shareStaffId() {
      return this.$route.query.share_staff_id || ''
    },
    isFormMooc() {
      return (this.$route.query.from === 'mooc' || this.$route.query.from === 'spoc')
    },
    isCourse() {
      return ((this.$route.query.jump_from === 'CourseList' || this.$route.query.from === 'CourseList') && this.$route.query.area_id)
    },
    mainContentStyle() {
      return {
        top: this.isCourse && this.activeKey === 'text' ? this.mainAreaHeight + 'px' : '',
        marginTop: this.isCourse && this.activeKey !== 'text' ? this.mainAreaHeight + 'px' : ''
      }
    },
    // 是否是纵向视频比例 高>宽
    videoIsVertical() {
      try {
        let { width, height } = this.videoInfo.file_attachments[0]
        width = parseInt(width)
        height = parseInt(height)
        return width < height
      } catch (error) {
        // console.log('error: ', error)
        return false
      }
    },
    dtCommon() {
      return (type, val) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页面-移动新版',
            container: 'tab导航',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: val,
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'H5'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${val}`
        } else {
          return `area_${this.course_id}_${val}`
        }
      }
    },
    // 是否是必修课
    isRequest() {
      return this.courseData?.course_statement?.is_required || false
    }
  },
  created() {
    try {
      this.isPreview = JSON.parse(this.$route.query.is_preview)
      if (this.isPreview) {
        this.tabList = [{ title: '', key: 'des', text: '详情' }]
      }
    } catch (error) {
      this.isPreview = false
    }
    // 添加message事件监听
    window.addEventListener('visibilitychange', this.visibilitychange, false)
    window.addEventListener('beforeunload', this.beforeunloadEvent, false)
  },
  mounted() {
    this.entryTime = new Date().getTime()
    if (this.isFormMooc) {
      MoocJs.setPause(() => {
        this.$refs.videoRef.vedioPlayer.pause()
      })
      MoocJs.setPlay(() => {
        this.$refs.videoRef.vedioPlayer.play()
      })
    }
    // 先初始化双语，再弹窗，避免双语没有加载出来
    this.$nextTick(() => {
      this.initDrag()
      this.initLang()
      this.initAibot()
    })

    if (this.isPreview) {
      this.getPreviewInfo()
      return
    }

    this.getCourseDetail().then((e) => {
      // 任务已完成时，开启任务同步弹窗
      console.log('任务已完成数据', this.courseData)
      if (this.courseData.is_finish * 1 === 1 && this.isFormMooc) {
        MoocJs.complete('init')
      }
    })
    MoocJs.messageListener((res) => {
      if (res.events === 'tencent-mooc-lang') {
        this.$store.commit('setMoocLang', res.params)
        this.getMobileLangJS()
      }
    })
    if (this.$route.query.mini && this.$route.query.history) {
      window.addEventListener('popstate', async () => {
        // 为了避免只调用一次，再次调用一次
        this.pushHistory(document.title, location.href)
        this.beforeunloadEvent()
        // 调用微信的返回事件
        window.parent &&
          window.parent.postMessage({
            data: 'navigateBack'
          })
      })
      this.pushHistory(document.title, location.href)
    }
    this.getExtandList()
    this.getChapterList()
    const { targetTime, lsTime } = this.$route.query
    if (targetTime > -1) {
      this.activeKey = 'chapter'
      this.playTime = targetTime * 1
      this.showTime = targetTime * 1
      // this.changePlaytime(this.playTime)
    }
    // 听视频小程序页面带过来的进度
    if (lsTime > -1) {
      this.playTime = lsTime * 1
      this.showTime = lsTime * 1
    }
    this.detectDevice()
    // 监听横竖屏变化
    window.addEventListener('resize', this.detectDevice)
    this.realtimeDocParse()
  },
  beforeDestroy() {
    const leaveTime = new Date().getTime()
    const time = Math.floor((leaveTime - this.entryTime) / 1000)
    const curTime = moment().format('YYYY年MM月DD日 HH时mm分ss秒')
    window.BeaconReport('at_show_area', {
      eid: `area_${this.course_id}_${curTime}`,
      remark: JSON.stringify({
        page: this.courseData.course_name,
        page_type: '网课详情页面-移动新版',
        container: '详情',
        click_type: 'data',
        content_type: '',
        content_id: '',
        content_name: '详情访问结束时间',
        act_type: 2,
        page_id: '',
        container_id: '',
        terminal: 'H5'
      })
    })
    window.BeaconReport('at_show_area', {
      eid: `area_${this.course_id}_${time}`,
      remark: JSON.stringify({
        page: this.courseData.course_name,
        page_type: '网课详情页面-移动新版',
        container: '详情',
        click_type: 'data',
        content_type: '',
        content_id: '',
        content_name: '详情页面停留时长',
        act_type: 2,
        page_id: '',
        container_id: '',
        terminal: 'H5'
      })
    })
    this.postDataToMessage('beforeDestroy')
    this.viewTimer = null
    this.countTimer = null
    clearInterval(this.viewTimer)
    clearInterval(this.countTimer)
    MoocJs.removeEvent()
    // 移除监听
    window.removeEventListener('visibilitychange', this.visibilitychange)
    window.removeEventListener('resize', this.detectDevice)
  },
  watch: {
    selected: {
      handler(val) {
        // 判断是否可以继续学习
        let res = false
        const data = this.questionData?.select_content
        for (let i = 0; i < val.length; i++) {
          // 单选
          if (typeof val[i] === 'string') {
            // 选了，并且是正确答案
            data[i].correct_answer = data[i].correct_answer || ''
            const answer = typeof data[i].correct_answer === 'string' ? data[i].correct_answer : data[i].correct_answer[0]
            // 选了，并且是正确答案
            if (val[i] === '' || (data[i].choose_type_config?.completion_conditions === 'correct' && answer !== val[i])) {
              res = true
              break
            }
            // 多选
          } else {
            // 选了，并且，如果是投票不超过最大数量限制
            if (val[i].length === 0 || (data[i].active_type === 'vote' && data[i]?.vote_type_config?.can_max_vote_nums < val[i].length)) {
              res = true
              break
              // 选了，并且是正确答案
            } else if (
              data[i].choose_type_config?.completion_conditions === 'correct'
            ) {
              const answer = JSON.parse(JSON.stringify(data[i].correct_answer))
              answer.sort((a, b) => a * 1 - b * 1)
              const select = JSON.parse(JSON.stringify(val[i]))
              select.sort((a, b) => a * 1 - b * 1)
              if (answer.join(',') !== select.join(',')) {
                res = true
                break
              }
            }
          }
        }
        this.continueDisabled = res
      },
      deep: true
    },
    backPlay() { // 背景播放开关改变 通知小程序
      this.postDataToMessage()
    },
    isPlaying(value) { // 视频播放状态发生改变
      if (!this.documentHidden) {
        this.postDataToMessage()
      }
    },
    isFullscreen(value) { // 全屏状态改变并且是pad，需要通知mooc
      if (this.isPad) {
        window.parent &&
          window.parent.postMessage({
            type: 'isPadFullscreenGray',
            isPadFullscreenGray: value
          })
      }
    }
  },
  methods: {
    isPadFn() {
      const ua = navigator.userAgent.toLowerCase()
      const isiPad = /ipad|macintosh/i.test(ua) && 'ontouchend' in document
      const isAndroidTablet = /android/i.test(ua) && !/mobile/i.test(ua)
      const isWindowsTablet = /windows/i.test(ua) && /touch/i.test(ua)
      return isiPad || isAndroidTablet || isWindowsTablet
    },
    isPadByScreen() {
      const { width, height } = window.screen
      const minSize = 768 // 常见平板最小宽度
      const maxSize = 1280 // 常见平板最大宽度（考虑横竖屏）
      const screenThreshold = Math.min(width, height) >= minSize && Math.max(width, height) <= maxSize
      const isHighDensity = window.devicePixelRatio >= 1.5 // 高精度检测（考虑设备像素比）
      return screenThreshold && isHighDensity
    },
    detectDevice() {
      const isPadUA = this.isPadFn()
      const isPadScreen = this.isPadByScreen()
      // 若UA或屏幕任一条件满足，则判定为Pad
      if (isPadUA || isPadScreen) {
        console.log('当前设备是平板')
        this.isPad = true
        return 'pad'
      } else {
        console.log('当前设备是手机或桌面')
        this.isPad = false
        return 'phone'
      }
    },
    // 解析课程
    realtimeDocParse() {
      let params = {
        bizType: 'NET_COURSE',
        bizId: this.course_id,
        sessionId: '',
        model: 1
      }
      realtimeDocParse(params).then(res => {

      })
    },
    // 底部按钮显示
    handleBtChoice(val) {
      console.log('底部按钮', val)
      if (val.icon === 'home') {
        const url = `//sdc.qq.com/s/Shd3Jk?scheme_type=homepage`
        window.location.href = url
      } else if (val.icon === 'comment') {
        this.commentShow = true
      } else if (val.icon === 'share') {
        this.showShareDialog = true
      } else if (val.icon === 'add') {
        this.addCourseShow = true
      }
    },
    // 背景播放开关
    backPlayChange(val) {
      this.$refs.videoContralComponent && (this.$refs.videoContralComponent.backPlay = val)
    },
    // 获取课程实时预览信息
    getPreviewInfo() {
      getCoursePreviewInfo(this.course_id).then(res => {
        document.title = res.course_name
        this.courseData = res
        this.courseData.labels = res.course_labels
      }).catch(err => {
        console.log('err: ', err)
      })
    },
    // 获取网络课章节列表
    getChapterList() {
      getNetCourseChapterList(this.course_id).then((res) => {
        this.isChapterFirstRequest = false
        const { chapter_ai_content_list, chapter_content_list } = res
        const chapterList = chapter_content_list?.length ? chapter_content_list : chapter_ai_content_list?.length ? chapter_ai_content_list : []
        this.chapterData = res
        this.chapterSummaryList = chapterList.map((item, i) => {
          let { minutes, seconds } = this.secondsToMinutes(item.chapter_time_point)
          let chapter_time = minutes + ':' + seconds
          const url = item.chapter_cover_content_id ? `${envName.contentcenter}content-center/api/v1/content/imgage/${item.chapter_cover_content_id}/preview` : item.chapter_cover_url
          const startTime = chapterList[i].chapter_time_point
          // 播放到最后一条数据时，结束时间放大2倍，让其留在最后一条数据中
          const endTime = chapterList.length === (i + 1) ? chapterList[i].chapter_time_point * 2 : chapterList[i + 1].chapter_time_point
          const totalMin = this.resolveMinTime(startTime, endTime)
          return {
            ...item,
            chapter_content: item.chapter_content ? item.chapter_content.replace(/(\\r\\n|\\n|\n|\r\n)+/g, '<br>') : '',
            chapter_time,
            imgUrl: url,
            totalMin,
            startTime,
            endTime
          }
        })
      })
    },
    resolveMinTime(startTime, endTime) {
      try {
        return ((endTime - startTime) / 60).toFixed(2)
      } catch (error) {
        return 0
      }
    },
    secondsToMinutes(seconds) {
      let minutes = Math.floor(seconds / 60) // 计算分钟
      let remainingSeconds = seconds % 60 // 计算剩余秒数
      remainingSeconds = remainingSeconds.toString().padStart(2, '0')
      return {
        minutes: minutes,
        seconds: remainingSeconds
      }
    },
    getRouterQuery() {
      let { mooc_course_id, taskId } = this.$route.query
      return {
        mooc_course_id: mooc_course_id || '',
        task_id: taskId || ''
      }
    },
    // 跳转至对应的播放时间
    toCurrentTime() {
      this.lastTime = this.playTime
      this.$nextTick(() => {
        this.$refs.videoContralComponent && this.$refs.videoContralComponent.toCurrentTime(this.playTime)
        this.$refs.videoRef.vedioPlayer.currentTime(this.playTime)
      })
    },
    // 章节改变当前时间节点
    changePlaytime(chapter_time) {
      this.$nextTick(() => {
        this.playTime = chapter_time
        this.$refs.videoRef.vedioPlayer.currentTime(this.playTime)
        this.$refs.videoRef.vedioPlayer.play()
      })
    },
    // 插入浏览器历史
    pushHistory(title = 'title', url = '#') {
      let state = { title, url }
      window.history.pushState(state, state.title, state.url)
    },
    beforeunloadEvent() {
      // 离开当前页面学习记录归档
      if (!this.studyRecordQuery.total_study_time) return
      const params = {
        ...this.studyRecordQuery,
        is_archive: true
      }
      let blob = new Blob([JSON.stringify(params)], {
        type: 'application/json; charset=UTF-8'
      })
      navigator.sendBeacon(
        '/training/api/netcourse/user/courseinfo/add-study-record',
        blob
      )
    },
    // h5给小程序传递参数
    postDataToMessage(action) {
      // 必修课，互动课不开启背景播放，无需发送消息给小程序---小程序逻辑是：如果没有收到消息中的openBackPlay参数，默认为false，不开启后台播放
      // if (this.isInteractive || this.isRequest) return
      // let params = {
      //   data: {
      //     page: 'net',
      //     openBackPlay: this.backPlay, // 打开了开关
      //     isPlaying: this.isPlaying, // 是否正在播放视频
      //     duration: this.$refs.videoRef.vedioPlayer.duration() || 0, // 视频时长
      //     action: action || ''
      //   }
      // }
      // if (action) {
      //   params.data.currentTime = this.$refs.videoRef.vedioPlayer.currentTime() // 当前视频播放进度
      // }
      // console.log('h5发送给小程序的参数------params: ', params)
      // window.wx && window.wx.miniProgram.postMessage(params)
    },
    // 如果之前熄屏了，则回到熄屏之前的播放时间
    visibilitychange() {
      // 熄屏上报一次
      if (document.hidden) {
        this.beforeunloadEvent()
        this.postDataToMessage('hidden')
        this.documentHidden = true
      } else {
        this.documentHidden = false
      }
      if (
        this.courseData.enable_interactive &&
        this.courseData.limit_progress_bar
      ) {
        if (document.hidden) {
          this.isPageHidden = true
          this.$refs.videoRef.vedioPlayer.pause()
        } else {
          this.$refs.videoRef.vedioPlayer.currentTime(this.lastHiddenTime)
          this.isPageHidden = false
        }
      }
    },
    getCurCaption(data) {
      if (this.isPreview) return
      if (!this.captionData.length > 0) {
        this.captionData = data
        this.VideoCaptionData = this.resolveCaptionData(data)
      }
    },
    handleRecord(param) {
      if (param.evt === 'loadedmetadata') {
        this.postDataToMessage() // 给h5传递时长字段
        // 视频加载完成后拿到播放时长
        if (param.duration) {
          this.duration = Math.floor(param.duration)
        }
        // 防止更换视频，过滤多余的章节时间节点
        if (this.duration) {
          let arr = this.chapterSummaryList.filter(its => {
            return its.chapter_time_point <= this.duration
          })
          if (arr.length) {
            arr[arr.length - 1].endTime = this.duration
            console.log('this.duration: ', this.duration)
            arr[arr.length - 1].totalMin = this.resolveMinTime(arr[arr.length - 1].startTime, arr[arr.length - 1].endTime)
          }
          this.chapterSummaryList = arr
        }
        if (!this.courseData.enable_interactive) { // 跳转到上次的学习记录那里 互动课另外处理，为了解决直接跳转时提示弹窗和互动同时出现的问题
          this.toCurrentTime()
        }
      }
      let status = ''
      if (param.evt === 'play') {
        this.studyRecordQuery.course_duration = Math.floor(param.duration)
        // 重新学习，重置数据
        if (this.studyRecordQuery.is_finish === 1) {
          this.studyRecordQuery.is_finish = 0
          this.studyRecordQuery.is_review = 1 // 重播
          this.studyRecordQuery.total_study_time = 0
          this.learnRecordId = 0
        }
        if (!this.countTimer) {
          this.creatViewTimer()
        }
        if (this.isFormMooc) {
          MoocJs.play()
        }
      }

      if (param.evt === 'pause' || param.evt === 'ended') {
        // 息屏状态不更新课程状态
        if (this.isPageHidden) return

        if (param.evt === 'ended') {
          // 学习完
          this.studyRecordQuery.is_finish = 1
          status = 'ended'
        }

        // 延迟1秒调用方法，（视频播放完成会先触发pause，再触发ended），解决同时触发pause、ended情况
        if (this.viewRecordTime) clearTimeout(this.viewRecordTime)
        this.viewRecordTime = setTimeout(() => {
          this.viewRecord(status)
        }, 1000)
        clearInterval(this.countTimer)
        this.countTimer = null

        if (param.evt === 'pause' && this.isFormMooc) {
          MoocJs.pause()
        } else if (param.evt === 'ended' && this.isFormMooc) {
          MoocJs.complete()
        }
      }
    },
    creatViewTimer() {
      let _this = this
      let durtation = 0
      let { course_duration } = this.studyRecordQuery
      this.countTimer = setInterval(function () {
        let { my_study_progress, is_finish } = _this.studyRecordQuery
        _this.studyRecordQuery.total_study_time++
        durtation++
        // if (durtation % 15 === 0) {
        //   _this.viewRecord() // 浏览器时长需每15秒记录一次
        // }
        if (durtation % 5 === 0) {
          _this.viewRecord() // 浏览器时长需每5秒记录一次
        }

        // // 比视频时长提前10秒触发完成上报
        // if (course_duration >= 60 && my_study_progress + 10 >= course_duration && !is_finish) {
        // 比视频时长提前5秒触发完成上报
        if (course_duration >= 60 && my_study_progress + 5 >= course_duration && !is_finish) {
          _this.studyRecordQuery.is_finish = 1
          _this.viewRecord()
          clearInterval(_this.countTimer)
          _this.countTimer = null
          if (_this.isFormMooc) {
            MoocJs.complete()
          }
        }
      }, 1000)
    },
    // 学习上报
    viewRecord(status) {
      const { moocPreview } = this.$route.query
      if (!this.studyRecordQuery.total_study_time || moocPreview * 1 === 1) return
      this.studyRecordQuery.learn_record_id = this.learnRecordId
      netViewRecord(this.studyRecordQuery).then((data) => {
        if (data) {
          if (status === 'ended') {
            this.learnRecordId = 0
          } else {
            this.learnRecordId = data
          }
        }
      })
    },
    getCourseDetail(isChangeTab) {
      const { cl_id } = this.$route.query
      const FUNC = getNetCourseInfo({
        act_id: this.course_id,
        share_staff_id: this.shareStaffId || ''
      })
        .then((data) => {
          if (data.support_mobile !== 1) {
            let link = process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/b5GaSG' : 'http://s.test.yunassess.com/s/hoo9Gg'
            this.$router.replace({
              name: 'mobileError',
              query: {
                type: 2,
                href: encodeURIComponent(`${link}?course_id=${this.course_id}`)
              }
            })
            if (this.isFormMooc) {
              MoocJs.sendErrorInfo(this.$langue('Mooc_TaskDetail_ContentNotSupportedPC1', { defaultText: '很抱歉，当前内容暂不支持移动端访问' }))
            }
            return
          }
          let cre = data.created_at.split(':')
          cre = [cre[0], cre[1]]
          cre = cre.join(':')
          data.created_at = cre
          document.title = data.course_name
          this.courseData = data
          this.postMessageToWebView() // 传递参数给webview
          let is_required = this.courseData?.course_statement?.is_required || false
          if (is_required || data.enable_interactive) {
            let message = ''
            if (is_required) {
              message = '当前课程为必修课，暂不支持后台播放哦~'
            } else {
              message = '当前课程为互动课，暂不支持后台播放哦~'
            }
            Toast({
              // className: 'interaction-toast',
              message,
              duration: 10000
            })
          }
          console.log('this.courseData: ', this.courseData)
          this.courseData_brief = JSON.parse(JSON.stringify(data))
          this.courseData_brief.created_at =
          this.courseData_brief.created_at.split(' ')[0]
          if (!this.playTime) this.playTime = data.my_study_progress
          if (!this.showTime) this.showTime = data.my_study_progress // 用于回显上次学习进度----提示
          // 是否开启互动
          if (data.enable_interactive) {
            window.parent &&
              window.parent.postMessage({
                data: 'vertical'
              })
            this.getInteraction()
            this.isInteractive = true
            // 互动开启全屏
            this.isFullscreen = true
            this.verticalTips = true
          } else {
            // 续播
            if (!isChangeTab) {
              const { targetTime } = this.$route.query
              // 上报过-学习过
              if (data.my_study_progress !== null || targetTime > -1) {
                this.isCurrentTimeShow = true
              } else {
                this.playTime = 0
              }
            }
          }

          const src = process.env.NODE_ENV === 'production' ? `https://learn.woa.com/mobilenet/net?act_id=${this.course_id}` : `https://test-learn.woa.com/mobilenet/net?act_id=${this.course_id}`
          // 兼容处理，如果content_id没有跳转到v8
          if (!data?.content_id && data?.cl_id) {
            // 网课
            const url = `${src}&jump_from=CourseList&project=0&source=ql&from=CourseList&area_id=${cl_id}`
            window.location.replace = url
          } else if (!data?.content_id) {
            // 网络课移动化
            window.location.replace = src
          }
          data.content_id && this.getOriginUrl(data.content_id)
          if (data.captions?.length > 0) this.readCaptionFile(data.captions)
          // 详情页曝光上报
          window.BeaconReport('at_show_area', {
            eid: `area_${this.course_id}`,
            remark: JSON.stringify({
              page: data.course_name,
              page_type: '网课详情页面-移动新版',
              container: '详情',
              click_type: 'data',
              content_type: '网络课',
              content_id: data.course_id,
              content_name: data.course_name,
              act_type: 2,
              page_id: '',
              container_id: '',
              terminal: 'H5'
            })
          })
          const time = moment().format('YYYY年MM月DD日 HH时mm分ss秒')
          window.BeaconReport('at_show_area', {
            eid: `area_${this.course_id}_${time}`,
            remark: JSON.stringify({
              page: this.courseData.course_name,
              page_type: '网课详情页面-移动新版',
              container: '详情',
              click_type: 'data',
              content_type: '',
              content_id: '',
              content_name: '详情访问开始时间',
              act_type: 2,
              page_id: '',
              container_id: '',
              terminal: 'H5'
            })
          })
        })
        .catch((err) => {
          if (err.code) {
            if (this.isFormMooc && (err.code === 403 || err.code === 500)) {
              MoocJs.sendErrorInfo(err.message)
              return
            }
            let type = 0
            if (err.code === 403) {
              type = 3
              if (err.message.includes('权限')) {
                type = 5
              }
            } else if (err.code === 500) {
              type = 5
            }
            this.$router.replace({
              name: 'mobileError',
              query: {
                type
              }
            })
          }
        })
      return FUNC
    },
    getInteraction() {
      getCourseInteraction({
        courseId: this.course_id,
        actType: 2
      }).then((res) => {
        res.configurations_of_select = res.configurations_of_select || []
        res.configurations_of_select.sort(
          (a, b) => a.active_time - b.active_time
        )
        this.interactionData = res
      })
    },
    getOriginUrl(contentId) {
      urlForDownloadApi(contentId).then((res) => {
        this.audioUrl = res
      })
    },
    changeTabs(val) {
      if (this.isPreview) return
      this.activeKey = val
      // 初始化底部导航状态
      this.$nextTick(() => {
        this.$refs.bottomNavRef.initShow()
      })
      if (this.activeKey === 'text') { // 没有开启播放的时候，初始化自动滚动
        this.$nextTick(() => {
          this.$refs.textDraftRef.scrollTopContent()
        })
      }
    },
    getExtandList() {
      const params = {
        act_id: this.course_id,
        act_type: 2
      }
      this.loading = true
      getExtanContentList(params)
        .then((data) => {
          this.loading = false
          this.extandList = (data || []).map((item) => {
            return {
              ...item,
              module_name: item.content_module_name, // 类型名称
              module_id: item.content_module_id, // 类型id
              content_name: item.content_name, // 内容名称
              content_url: item.href,
              item_id: item.content_item_id,
              description: item.course_desc, // 简介
              play_total_count: item.view_count, // 查看次数
              word_num: item.word_num, // 图文/笔记 - 字数
              praise_count: '', // 图文/笔记/案例/码客 - 点赞数
              avg_score: item.avg_score, // 得分
              created_time: item.content_created_time, // 时间
              task_count: item.sub_count,
              labels: (item.labels || []).map((v) => v.label_name),
              photo_url: item.photo_url || '',
              origin_data: {
                expert_name: '', // 行家-人员名称
                meet_num: 0, // 咨询量
                avg_score: '', // 评分
                start_time: '', // 活动开始时间
                end_time: '' // 活动结束时间
              }
            }
          })
        })
        .catch(() => {
          this.loading = false
        })
    },
    getItemImg({ photo_storage_type, photo_url, photo_id }) {
      if (photo_storage_type === 'contentcenter') {
        const envName = env[process.env.NODE_ENV]
        return `${envName.contentcenter}content-center/api/v1/content/imgage/${photo_url}/preview`
      }
      return photo_id
    },
    isNumeric(str) {
      return /^\d+$/.test(str)
    },
    readCaptionFile(captions) {
      console.log('captions: ', captions)
      captions.forEach((item) => {
        if (item.attachement_type === 'Caption') {
          axios({
            url: item.url,
            method: 'GET',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8'
            }
          }).then((response) => {
            if (response.status === 200 && response.data) {
              try {
                // let data = response.data?.split('\n\n')
                let data = response.data?.split(/\n\n|\r\n\r\n/)
                let newData = []
                data.forEach(value => {
                  // let valueItem = value.split(/[\r\n]+/)
                  let valueItem = value.split(/[(\r\n)\r\n|(\r\n\r\n)\r\n\r\n]+/)
                  if (this.isNumeric(valueItem[0]) && valueItem[1].includes('-->')) {
                    newData.push(value)
                  } else {
                    newData[newData.length - 1] += `\r\n${value}`
                  }
                })
                const captionArr = newData?.map((str) => {
                  let obj = {}
                  // const captionItemArr = str.split(/[(\r\n)\r\n]+/)
                  const captionItemArr = str.split(/[(\r\n)\r\n|(\r\n\r\n)\r\n\r\n]+/)

                  captionItemArr.map((e, idx) => {
                    if (idx === 1) {
                      const time = JSON.parse(JSON.stringify(e))
                      obj.startTime = e.split('-->')[0]
                      obj.endTime = e.split('-->')[1]
                      const endTimeCopy = JSON.parse(
                        JSON.stringify(time.split('-->')[1])
                      )
                      const startTimeCopy = JSON.parse(
                        JSON.stringify(time.split('-->')[0])
                      )
                      obj.IntStartTime = startTimeCopy
                        ? this.timeToSec(startTimeCopy)
                        : 0
                      obj.IntEndTime = endTimeCopy
                        ? this.timeToSec(endTimeCopy)
                        : 0
                    }
                    if (idx === 2) obj.caption = e
                    if (idx > 2) {
                      obj.caption += e
                    }
                  })
                  return obj
                })
                this.captionData = captionArr
                this.VideoCaptionData = this.resolveCaptionData(captionArr)
              } catch (error) {
                console.log('error: ', error)
              }
            }
          })
        }
      })
    },
    // 字幕数据切分
    resolveCaptionData(list) {
      let newArray = []
      list.forEach(item => {
        item.caption && (item.caption = item.caption.trim())
        if (item.caption && item.caption.length > 30) {
          let groupNum = Math.ceil(item.caption.length / 30) // 分成多少组
          let step = (item.IntEndTime - item.IntStartTime) / groupNum
          let currentStartTime = item.IntStartTime
          let currentEndTime = currentStartTime + step
          let currentStr = item.caption.substr(0, 30)
          for (let index = 0; index < groupNum; index++) {
            newArray.push({
              IntEndTime: currentEndTime,
              IntStartTime: currentStartTime,
              caption: currentStr
            })
            currentStartTime = currentEndTime + 0.01
            if (currentStartTime >= item.IntEndTime) {
              currentStartTime = item.IntEndTime
            }
            currentEndTime = currentStartTime + step
            if (currentEndTime >= item.IntEndTime) {
              currentEndTime = item.IntEndTime
            }
            currentStr = item.caption.substr((index + 1) * 30, 30)
          }
        } else {
          newArray.push(item)
        }
      })
      return newArray
    },
    expandSwitch(v) {
      this.lessonExpand = v
      this.$nextTick(() => {
        let el = document.getElementById('main-area')
        this.mainAreaHeight = el.offsetHeight + 7
      })
    },
    initDrag() {
      let that = this
      const btnEl = document.getElementById('drag-service')
      if (btnEl) {
        let disY
        let height
        btnEl.addEventListener('touchstart', function (e) {
          e.preventDefault()
          height = e.view.innerHeight - 40
          disY = e.touches[0].clientY - this.offsetTop
          that.initTop = this.offsetTop
        })

        btnEl.addEventListener('touchmove', function (e) {
          let top = e.touches[0].clientY - disY

          if (Math.abs(top - that.initTop) < 5) { // 兼容鸿蒙系统，点击时偏移几个像素处理成点击
            return
          }

          that.initTop = top
          that.isMoved = true

          if (top >= 0 && top <= height) {
            btnEl.style.top = top + 'px'
          }
        })
        btnEl.addEventListener('touchend', function (e) {
          // 判断是否有拖动，有则初始化为false，无则为点击
          if (that.isMoved) {
            that.isMoved = false
          } else {
            let paramStr = ''
            if (that.isCourse) {
              paramStr += `&from=CourseList&area_id=${that.$route.query.area_id}`
            }
            const seconds = Math.floor(that.$refs.videoRef.vedioPlayer.currentTime())
            window.open(`https://sdc.qq.com/s/sYkoua?scheme_type=listenAu&course_id=${that.course_id}${paramStr}&currentTime=${seconds}`)
          }
        })
      }
    },
    initLang() {
      let that = this
      const btnEl = document.getElementById('drag-lang')
      if (btnEl) {
        let disY
        let height
        btnEl.addEventListener('touchstart', function (e) {
          e.preventDefault()
          height = e.view.innerHeight - 40
          disY = e.touches[0].clientY - this.offsetTop
          that.initLangTop = this.offsetTop
        })
        btnEl.addEventListener('touchmove', function (e) {
          let top = e.touches[0].clientY - disY

          if (Math.abs(top - that.initLangTop) < 5) { // 兼容鸿蒙系统，点击时偏移几个像素处理成点击
            return
          }

          that.initLangTop = top
          that.isMoved = true

          if (top >= 0 && top <= height) {
            btnEl.style.top = top + 'px'
          }
        })
        btnEl.addEventListener('touchend', function (e) {
          // 判断是否有拖动，有则初始化为false，无则为点击
          if (that.isMoved) {
            that.isMoved = false
          } else {
            // 双语切换
            let lang = that.moocLang === 'en-us' ? 'zh-cn' : 'en-us'
            that.$store.commit('setMoocLang', lang)
            that.getMobileLangJS()
            window.parent &&
              window.parent.postMessage({
                lang,
                type: 'changeLang'
              })
          }
        })
      }
    },
    initAibot() {
      let that = this
      const btnEl = document.getElementById('drag-service-ai-bot')
      if (btnEl) {
        let disY
        let height
        btnEl.addEventListener('touchstart', function (e) {
          e.preventDefault()
          height = e.view.innerHeight - 40
          disY = e.touches[0].clientY - this.offsetTop
          that.initTop = this.offsetTop
        })

        btnEl.addEventListener('touchmove', function (e) {
          let top = e.touches[0].clientY - disY

          if (Math.abs(top - that.initTop) < 5) { // 兼容鸿蒙系统，点击时偏移几个像素处理成点击
            return
          }

          that.initTop = top
          that.isMoved = true

          if (top >= 0 && top <= height) {
            btnEl.style.top = top + 'px'
          }
        })
        btnEl.addEventListener('touchend', function (e) {
          // 判断是否有拖动，有则初始化为false，无则为点击
          if (that.isMoved) {
            that.isMoved = false
          } else {
            let url = process.env.NODE_ENV === 'production' ? 'https://learn.woa.com/mat/user/chat-ai?biz_type=NET_COURSE' : '//test-learn.woa.com/mat/user/chat-ai?biz_type=NET_COURSE&debugger=true'
            let paramStrUrl = `${url}&biz_id=${that.course_id}`
            window.open(paramStrUrl)
            // const seconds = Math.floor(that.$refs.videoRef.vedioPlayer.currentTime())
            // window.open(`https://sdc.qq.com/s/sYkoua?scheme_type=listenAu&course_id=${that.course_id}${paramStr}&currentTime=${seconds}`)
          }
        })
      }
    },
    // 关闭横屏提示
    verticalClose() {
      this.verticalTips = false
      const my_study_progress = this.courseData.my_study_progress
      if (my_study_progress > 0) {
        this.isCurrentTimeShow = true
      }
      this.toCurrentTime()
    },
    // 继续学习
    onContinue() {
      if (this.continueDisabled) {
        if (this.questionData?.continue_studying_tips) {
          Toast({
            className: 'interaction-toast',
            message: this.questionData?.continue_studying_tips
          })
        }
        return
      }
      const answers = this.questionData?.select_content.map((item, index) => {
        const active_answer =
          typeof this.selected[index] === 'string'
            ? this.selected[index]
            : this.selected[index].sort((a, b) => a * 1 - b * 1).join(',')
        return {
          question_id: item.question_id,
          active_answer
        }
      })
      saveInteractionRecord({
        interactive_config_id: this.interactionData.id,
        interactive_id: this.questionData?.interactive_id,
        record_id: this.learnRecordId || 0,
        answers
      }).then(() => {})
      this.showInteractive = false
      this.$refs.videoRef.vedioPlayer.play()
      if (this.isFormMooc) {
        MoocJs.postMessage('interactiveDialog', this.showInteractive)
      }
    },
    // 监听播放时长
    getCurrentTime(time) {
      // 显示字幕时视频中的字幕切换
      this.$refs.videoContralComponent && this.$refs.videoContralComponent.getCurrentTime(time)
      // 文稿滚动内容
      this.$nextTick(() => {
        if (this.$refs.textDraftRef) {
          this.$refs.textDraftRef.scrollTopContent()
        }
      })

      const curr = Math.floor(time)
      this.studyRecordQuery.my_study_progress = curr
      // 互动能力相关
      if (this.interactionTime === curr || this.showInteractive) return
      this.interactionTime = curr
      // 如果视频被拖动了，并且此时设置为不可拖动，则回到拖动之前的位置
      if (
        this.courseData.enable_interactive &&
        this.courseData.limit_progress_bar
      ) {
        // 记录熄屏的时间
        if (this.isPageHidden) {
          return
        } else {
          this.lastHiddenTime = curr
        }
        // 如果是快进或快退
        if (Math.abs(curr - this.lastTime) > 2) {
          this.$refs.videoRef.vedioPlayer.pause()
          this.$refs.videoRef.vedioPlayer.currentTime(this.lastTime)
          return
        } else {
          this.lastTime = curr
        }
      }
      let confSelect = this.interactionData.configurations_of_select || []
      this.questionData = confSelect.find(
        (item) => parseInt(item.active_time) === curr
      )
      // 如果没有问题
      if (
        !this.questionData ||
        this.questionData?.select_content.length === 0
      ) {
        return
      }
      this.selected = this.questionData?.select_content.map((item) => {
        return item.choose_type_config?.type === 'single' ? '' : []
      })
      this.showInteractive = true
      this.$refs.videoRef.vedioPlayer.pause()
      if (this.isFormMooc) {
        MoocJs.postMessage('interactiveDialog', this.showInteractive)
      }
    },
    // 播放
    onPlay() {
      if (this.isCurrentTimeShow) {
        setTimeout(() => {
          this.isCurrentTimeShow = false
        }, 5000)
      }
      // 有互动弹窗时，不允许继续播放
      if (this.courseData.enable_interactive && this.courseData.limit_progress_bar && this.showInteractive) {
        this.$refs.videoRef.vedioPlayer.pause()
        this.$refs.videoRef.vedioPlayer.currentTime(this.lastTime)
        return
      }
      this.$refs.videoContralComponent && this.$refs.videoContralComponent.onPlay()
      this.isPlaying = true
    },
    // 暂停
    onPause() {
      this.$refs.videoContralComponent && this.$refs.videoContralComponent.onPause()
      this.isPlaying = false
    },
    // 播放结束
    onEnded() {
      setTimeout(() => {
        this.lastTime = 0
        this.$refs.videoContralComponent && this.$refs.videoContralComponent.onEnded()
      }, 1000)
      this.isPlaying = false
    },
    // 全屏切换
    fullscreenchange(data) {
      // 横屏时关闭其他弹框
      if (this.$refs.desContentRef) {
        this.$refs.desContentRef.desDetailShow = false
        this.$refs.desContentRef.isShowChapter = false // 关闭查看全部章节弹框
      }
      // 全屏-横屏时获取章节数据
      if (data && !this.videoIsVertical && !this.chapterSummaryList.length && this.isChapterFirstRequest) {
        this.getChapterList()
      }
      this.isFullscreen = data
      this.$refs.videoContralComponent && this.$refs.videoContralComponent.fullscreenchange(data)
    },
    // ------------------网课新改版--------------------
    // 获取视频文件信息
    getVideoInfo(data) {
      this.videoInfo = data
      try {
        this.videoTotalTime = data.file_info.duration || this.courseData.duration || this.courseData.est_dur * 60
      } catch (error) {
        this.videoTotalTime = 0
      }
    },
    // 视频播放器初始化加载完成 跳转到上次学习时间节点
    loadVideoSucess({ videoDomId }) {
      this.isVideoLoade = true
      this.$nextTick(() => {
        this.$refs.videoContralComponent && this.$refs.videoContralComponent.initVideoDrag(videoDomId)
      })
      if (!this.courseData.enable_interactive) { // 跳转到上次的学习记录那里 互动课另外处理，为了解决直接跳转时提示弹窗和互动同时出现的问题
        this.toCurrentTime()
      }
    },
    // 秒转换时分秒
    timeToString(val) {
      val = parseInt(val)
      if (!val) {
        return '0:00'
      }
      // 小于60秒
      if (val < 60) {
        if (val < 10) {
          val = `0${val}`
        }
        return `0:${val}`
      }

      let newSecondTime = 0 // 秒
      let minuteTime = 0 // 分
      let hourTime = 0 // 小时
      let result = ''

      //  如果秒数大于60，将秒数转换成整数
      // 获取分钟，除以60取整数，得到整数分钟
      minuteTime = parseInt(String(val / 60))
      // 获取秒数，秒数取佘，得到整数秒数
      newSecondTime = parseInt(String(val % 60))
      // 如果分钟大于60，将分钟转换成小时
      if (minuteTime >= 60) {
        // 获取小时，获取分钟除以60，得到整数小时
        hourTime = parseInt(String(minuteTime / 60))
        // 获取小时后取佘的分，获取分钟除以60取佘的分
        minuteTime = parseInt(String(minuteTime % 60))
      }

      if (newSecondTime < 10) {
        newSecondTime = `0${newSecondTime}`
      }
      // 存在分钟
      if (minuteTime > 0) {
        if (hourTime > 0 && minuteTime < 10) {
          minuteTime = `0${minuteTime}`
        }
        result = `${minuteTime}:${newSecondTime}`
      }
      // 存在小时
      if (hourTime > 0) {
        result = `${hourTime}:${minuteTime}:${newSecondTime}`
      }
      return result
    },
    timeToSec(timeStr) {
      let time = timeStr.split(',')[0]
      let s = ''
      const hour = time.split(':')[0]
      const min = time.split(':')[1]
      const sec = time.split(':')[2]
      let misecond = timeStr.split(',')[1].substr(0, 2)
      misecond = Number(parseInt(misecond) / 100)
      s = Number(hour * 3600) + Number(min * 60) + Number(sec)
      return (s * 100 + misecond * 100) / 100
    },
    // 给webview传递封面参数，分享时需要用到
    postMessageToWebView() {
      let params = {
        data: {
          page: 'net',
          courseInfo: this.courseData
        }
      }
      window.wx && window.wx.miniProgram.postMessage(params)
    }
  }
}
</script>

<style lang="less" scoped>
.video-container {
  overflow: hidden;
  .h100vh {
    height: 100vh;
  }
  .main-fixed {
    position: fixed;
    z-index: 100;
    width: 100%;
    :deep(.video-component) {
      border: unset;
      border-radius: unset;
      background-color: black;
    }
  }
  .fixed-main-content {
    position: fixed;
    width: 100%;
    top: 260px;
    height: calc(100% - 260px);
    .comment {
      background: #fff;
      padding-top: 10px;
      height: 100%;
      overflow-y: auto;
    }
  }

  .play-video-video-isVertical { // 全屏-竖屏播放
    /deep/ .tcplayer {
      .vjs-poster { // 海报处理
        background-size: contain;
      }
    }
    // /deep/ .video-js {
    //   position: fixed;
    //   overflow: hidden;
    //   z-index: 1000;
    //   left: 0;
    //   top: 0;
    //   bottom: 0;
    //   right: 0;
    // }
  }

  .play-video {
    position: relative;
    height: 100%;

    /deep/ .vjs-control-bar { // 隐藏视频组件自带的控制栏
      display: none;
    }

    .exit-btn {
      display: inline-block;
      position: fixed;
      left: 6px;
      bottom: 12px;
      width: 24px;
      height: 24px;
      background: url('~@/assets/mooc-img/exit-btn.png') no-repeat center / cover;
    }
    .common-time-tips {
      position: absolute;
      color: #fff;
      padding: 0 6px;
      line-height: 20px;
      font-size: 12px;
      border-radius: 3px;
      background: #00000066;
      z-index: 999;
    }
    .current-time-tips {
      left: 16px;
      bottom: 58px;
    }
    .en-current-time-tips {
      // display: flex;
      // flex-direction: column;
      // align-items:unset;
      left: 16px;
      bottom: 58px;
    }
    .fullscreen-tips {
      top: 16px;
      left: 58px;
      white-space: nowrap;
      transform: translate(0, -50%) rotate(90deg);
      transform-origin: left bottom;
    }
    .en-fullscreen-tips {
      top: 16px;
      left: 58px;
      white-space: nowrap;
      transform: translate(0, -50%) rotate(90deg);
      transform-origin: left bottom;
    }
    .fullscreen-Vertical-tips {
      left: 16px;
      bottom: 69px;
      z-index: 1000;
    }
    .en-fullscreen-Vertical-tips {
      left: 16px;
      bottom: 69px;
      z-index: 1000;
    }
    .fullscreen-Vertical-tips-pad {
      top: auto;
      left: 16px;
      bottom: 69px;
      transform: none;
      z-index: 1000;
    }
    .en-fullscreen-Vertical-tips-pad {
      top: auto;
      left: 16px;
      bottom: 69px;
      transform: none;
      z-index: 1000;
    }
    .bottom-42 {
      bottom: 42px;
    }
  }
  .video-box,
  .scorm-box {
    height: 210px;
  }
  .fullScreen-video-box {
    height: 100vh;
  }
  .full-vertical-box {
    width: 100vh !important;
    height: 100vw !important;
  }
  .van-image {
    width: 100%;
    height: 100%;
  }
  .tabs-card {
    position: fixed;
    top: 210px;
    width: 100%;
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    height: 40px;
    .tabs {
      display: flex;
      align-items: center;
      padding-left: 20px;
      .item-tab {
        color: #00000099;
        font-size: 12px;
        line-height: 40px;
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
      }
      .item-tab + .item-tab {
        margin-left: 26px;
      }
      .active-item-tab {
        font-size: 14px;
        font-weight: bold;
        color: #0052d9;
      }
      .active-line {
        position: absolute;
        bottom: 0px;
        display: inline-block;
        width: 28px;
        height: 2px;
        background-color: #0052d9;
      }
    }
    .back-play {
      margin-right: 12px;
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #333333;
      .switch-content {
        height: 20px;
        margin-left: 4px;
        color: #ffffffe6;
        // /deep/.van-switch {
        //   position: relative;
        //   &::before {
        //     position: absolute;
        //     left: 5px;
        //     top: 50%;
        //     content: '开';
        //     font-size: 12px;
        //     transform: translateY(-50%);
        //     opacity: 1;
        //   }
        //   &::after {
        //     position: absolute;
        //     right: 5px;
        //     top: 50%;
        //     content: '关';
        //     font-size: 12px;
        //     transform: translateY(-50%);
        //     opacity: 0;
        //   }
        //   .van-switch__node {
        //     top: 1px;
        //     right: 2px;
        //     width: 15px;
        //     height: 15px;
        //   }
        // }
        // .van-switch--on .van-switch__node {
        //     transform: translateX(1.2em);
        // }
      }
      // .switch-content-close {
      //   /deep/.van-switch {
      //     &::before {
      //       opacity: 0;
      //     }
      //     &::after {
      //       opacity: 1;
      //     }
      //     .van-switch__node {
      //       top: 1px;
      //       left: 2px;
      //       right: auto;
      //       width: 15px;
      //       height: 15px;
      //     }
      //   }
      // }
    }
  }
  #drag-service {
    height: 40px;
    padding: 0 8px;
    background: #0052d9;
    border: 1px solid #dcdcdc;
    border-radius: 20px 0 0 20px;
    border-right-color: transparent;
    box-shadow: 0 0 24px 0 #dcdcdc99;
    position: fixed;
    top: 235px; // 前面有中英文切换按钮，培养项目按钮
    right: 0;
    z-index: 99;
    color: #fff;
    font-size: 10px;
    display: flex;
    align-items: center;
    user-select: none;
    .icon {
      width: 16px;
      height: 16px;
      background: url('~@/assets/img/mobile/switch-icon.png') no-repeat
        center/cover;
      margin-right: 3px;
      display: inline-block;
    }
  }
  #drag-service-ai-bot {
    position: fixed;
    top: calc(47% + 20px + 86px + 60px); // 前面有中英文切换按钮，培养项目按钮
    right: 8px;
    z-index: 99;
    padding: 5px;
    // background-color: #fff;
    //   border-radius: 46px;
    //   box-shadow: 0 0 24px 0 #dcdcdc99;
    .img_content {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      .chat_img1 {
        // width: 36px;
        // height: 58px;
        // display: flex;
        // align-items: center;
        // border-radius: 30px;
        // background-color: #D6F3FF;
        // background: url(./assets/image/AIBOT.png) no-repeat;
        // background-size: 100%;
         .bot-icon {
            width: 46px;
            height: 53px;
            background-size: cover
          }
          .bot-icon2 {
            position: absolute;
            width: 28px;
            height: 28px;
            top: -18px;
            right: 0px;
          }
      }
    }
  }
  .drag-lang {
    width: 60px;
    height: 40px;
    padding-left: 8px;
    background: #fff;
    border: 1px solid #dcdcdc;
    border-radius: 20px 0 0 20px;
    border-right-color: transparent;
    box-shadow: 0 0 24px 0 #dcdcdc99;
    position: fixed;
    top: 53%;
    right: 0;
    z-index: 99;
    font-size: 10px;
    display: flex;
    align-items: center;
    user-select: none;
    .el-icon-en {
      background: url('~@/assets/img/english.png') no-repeat center / cover;
    }
    .el-icon-zh {
      background: url('~@/assets/img/china.png') no-repeat center / cover;
    }
    .icon {
      width: 20px;
      height: 20px;
      margin-right: 4px;
      display: inline-block;
    }
  }
  .lang-icon-full {
    top: 16px;
    right: 196px;
    z-index: 9999;
    padding: 0;
    width: 50px;
    height: 50px;
    flex-shrink: 0;
    border-radius: 36px;
    background: #fff;
    box-shadow: 0 0 12px 0 #99999999;
    text-align: center;
    .icon {
      margin: 0;
      width: 24px;
      height: 23px;
      transform: translateX(12px) rotate(90deg);
    }
    .text {
      display: none;
    }
  }
  .vertical-popup {
    width: 310px;
    border-radius: 12px;
    .bg {
      height: 201px;
      line-height: 201px;
      background: linear-gradient(270deg, #3683ea 0%, #83b8ff 95.82%);
      text-align: center;
      .vertical {
        width: 163px;
        height: 145px;
      }
    }
    .bottom {
      padding: 24px 0;
      font-size: 16px;
      text-align: center;
      .text {
        color: #2c3645;
        line-height: 24px;
      }
      .confirm {
        display: inline-block;
        margin-top: 24px;
        width: 247px;
        height: 48px;
        line-height: 48px;
        border-radius: 24px;
        background: linear-gradient(90deg, #0e69ff 0%, #4f91ff 100%);
        color: #fff;
        font-weight: 600;
      }
    }
  }

  .interactive-popup {
    border-radius: 6px;
    width: 90vh;
    height: 90vw;
    transform-origin: 0 0;
    transform: translateX(45vw) translateY(-45vh) rotate(90deg);
    display: flex;
    flex-direction: column;
    .content {
      flex: 1;
      padding: 24px 32px;
      overflow-y: auto;
      .title {
        margin-bottom: 16px;
        color: #000000e6;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
      }
      .desc {
        padding: 12px;
        background: #fafafa;
        color: #00000099;
        line-height: 22px;
      }
      .question {
        .question-name {
          padding-top: 10px;
          font-weight: bold;
        }
        .van-checkbox-group,
        .van-radio-group {
          padding-top: 4px;
          :deep(.van-checkbox),
          :deep(.van-radio) {
            align-items: unset;
            margin-top: 12px;
            .van-checkbox__icon {
              padding-top: 2px;
              .van-icon {
                border-radius: 3px;
              }
            }
            .van-checkbox__label,
            .van-radio__label {
              color: #000000e6;
              line-height: 22px;
              white-space: pre-line;
            }
          }
        }
      }
    }
    .bottom {
      height: 64px;
      line-height: 64px;
      box-shadow: 0 -4px 12px 0 #0000000f;
      text-align: center;
      .continue {
        display: inline-block;
        height: 40px;
        line-height: 40px;
        width: 240px;
        border-radius: 3px;
        color: #ffffffe6;
        background: #0052d9;
      }
      .continue-disabled {
        background: #b5c7ff;
      }
    }
  }
  .interactive-popup-pad {
    width: 90vw;
    height: 90vh;
    transform: translateX(-45vw) translateY(-45vh);
  }
  :deep(.task-dialog) {
    text-align: center;
    .van-dialog__content {
      padding: 45px 20px;
    }
  }
}
</style>
