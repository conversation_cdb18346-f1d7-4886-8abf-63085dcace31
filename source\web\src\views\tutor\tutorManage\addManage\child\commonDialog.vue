<template>
  <el-Dialog 
  :visible="visible" 
  :title="dialogData.title" 
  width="600px" 
  :close-on-click-modal="false" 
  @close="cancel"
  class="common-dialog"
  >  
    <div class="common-body"></div>
    <div class="common-body">
      <div class="top-info">
        <span class="label">{{dialogData.addLabel}}<span class="info-value">{{dialogData.addName}}</span></span>
        <span class="label right-label">{{dialogData.timeLalbe}}<span class="info-value">{{dialogData.time}}</span></span>
      </div>
      <div class="reson-box">
        <div class="label">{{dialogData.resonLabel}}</div>
        <sdc-mce-preview
        class="reason-content"
        ref="editor"
        :urlConfig="editorConfig.urlConfig"
        :catalogue.sync="editorConfig.catalogue"
        :content="dialogData.reason || '-'"
        >
        </sdc-mce-preview>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button style="width:100px" @click="cancel" type="primary" size="small">知道了</el-button>
    </span>
  </el-Dialog>
</template>
<script>
import { getLatestStatus } from '../../../api/tutor.api.conf'
export default {
  props: {
    visible: Boolean,
    detailInfo: Object
  },
  data() {
    return {
      dialogData: {
        title: '',
        addLabel: '',
        addName: '',
        timeLalbe: '',
        time: '',
        resonLabel: '',
        reason: ''
      },
      editorConfig: {
        catalogue: false,
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/{contentId}`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview`
        }
      }
    }
  },
  mounted() {
    this.getStatus()
  },
  methods: {
    getStatus() {
      const { tutor_staff_id, window_type } = this.detailInfo
      const params = {
        tutor_staff_id,
        window_type // -1:禁用 0:重新认证 1:录入
      }
      getLatestStatus(params).then((res) => {
        this.dialogData = {
          ...this.detailInfo,
          reason: res.opt_reason,
          time: res.created_at,
          addName: res.creator_name
        }
      })
    },
    cancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.common-dialog {
  :deep(.el-dialog__header) {
    padding: 24px 32px 20px;
  }

  :deep(.el-dialog__body) {
    padding: 24px 32px;
  }

  :deep(.el-dialog__footer) {
    padding: 0 32px 24px;
  }
  .common-body {
    
    .label {
      color: #00000099;
    }
    .top-info {
      line-height: 22px;
      font-size: 14px;
      .right-label {
        margin-left: 36px;
      }
      .info-value {
        color: #0052D9;
      }
    }
    .reson-box {
      margin-top: 20px;
      .reason-content {
        border-radius: 6px;
        background: #F8F8F8;
        padding: 12px;
        margin-top: 8px;
        max-height: 500px;
        overflow: auto;
        :deep(.content-wrapper) {
          background-color: unset;
        }
      }
    }
  }
}
</style>
