<template>
  <div class="child-box">
    <span>子级</span>
    <el-button @click="handleChild">子级按钮</el-button>
  </div>
</template>
<script>
import MoocJs from 'sdc-moocjs-integrator'
export default {
  data () {
    return {
    }
  },
  mounted() {
    MoocJs.messageListener((res) => {
      let { params } = res
      console.log('父级iframe页面传过来的参数', res, params)
    })
  },
  methods: {
    handleChild() {
      setTimeout(() => {
        MoocJs.complete()
      }, 2000)
      MoocJs.postMessage('changeContent')
    }
  }
}
</script>
<style lang="less" scoped>
.child-box {
  background-color: yellow;
  height: 200px;
  width: 200px;
  line-height: 200px;
  text-align: center;
  font-size: 30px;
}
</style>
