<template>
  <div class="activity-container" :class="{ 'embedded-page': isEmbedded }">
    <div class="activity-container-header">
      <activity-steps :currentStep="currentStep" @stepChange="stepsChange"></activity-steps>
    </div>
    <div class="activity-container-content">
      <keep-alive>
        <activity-info v-show="currentStep === 1" ref="activityInfoRef" :baseInfo="baseInfo"></activity-info>
      </keep-alive>
      <keep-alive>
        <registration-settings ref="registrationSettings" v-show="currentStep === 2" :activityData="baseInfo"></registration-settings>
      </keep-alive>
      <keep-alive>
        <interactive-setting ref="interactiveSetting" v-show="currentStep === 3" :baseInfo="baseInfo"></interactive-setting>
      </keep-alive>
      <keep-alive>
        <initiate-enroll ref="initiateEnroll" v-show="currentStep === 4" :actId="activityId" :baseInfo="baseInfo"></initiate-enroll>
      </keep-alive>
    </div>
    <div class="activity-container-footer" v-if="currentStep !== 4">
      <floating-button-bar :float="false" :config="btnConfig" :conventionShow="conventionShow" @handleClick="handleBtnBarClick" @handleConventionClick="handleConventionChange"></floating-button-bar>
    </div>
  </div>
</template>

<script>
import activitySteps from './components/activitySteps.vue'
import activityInfo from './components/activityInfo.vue'
import registrationSettings from './components/registrationSettings.vue'
import floatingButtonBar from './components/floatingButtonBar.vue'
import InitiateEnroll from './components/InitiateEnroll.vue'
import interactiveSetting from './components/interactiveSetting.vue'
import { getActivityDetail, saveActivityBaseInfo, activitySaveDraftApi } from 'config/classroom.api.conf'
import { mapState } from 'vuex'
export default {
  name: '',
  components: {
    activitySteps,
    activityInfo,
    registrationSettings,
    InitiateEnroll,
    floatingButtonBar,
    interactiveSetting
  },
  data() {
    return {
      currentStep: 1,
      oldStep: 1,
      MaxStep: 4,
      btnConfig: [
        {
          type: 'cancel',
          show: true,
          disable: false,
          text: '取消',
          btnType: 'default'
        },
        {
          type: 'draft',
          show: true,
          disable: false,
          text: '存草稿',
          btnType: 'default'
        },
        {
          type: 'preview',
          show: true,
          disable: true,
          text: '预览',
          btnType: 'default'
        },
        {
          type: 'previous',
          show: false,
          disable: false,
          text: '上一步',
          btnType: 'primary'
        },
        {
          type: 'next',
          show: true,
          disable: false,
          text: '下一步',
          btnType: 'primary'
        },
        {
          type: 'submit',
          show: false,
          disable: true,
          text: '发布',
          btnType: 'primary'
        }
      ],
      baseInfo: {},
      activityData: {}, // 活动数据
      drawData: {
        step1: {
          isPass: false,
          index: 1,
          data: {}
        },
        step2: {
          isPass: false,
          index: 2,
          data: {
            surveys: []
          }
        },
        step3: {
          isPass: false,
          index: 3,
          data: {
            surveys: []
          }
        }
      },
      stepName: ['活动信息', '报名设置', '互动设置'],
      activityId: '',
      conventionShow: false,
      isChooseConvention: false,
      // 是否内嵌页面
      isEmbedded: false
    }
  },
  computed: {
    ...mapState({
      userInfo: state => state.userInfo
    }),
    pageId() {
      return this.$route.query.page_id || ''
    },
    activity_id() {
      return this.$route.query.activity_id || this.activityId || ''
    }
  },
  watch: {
    currentStep: {
      handler(newVal) {
        this.conventionShow && (this.conventionShow = false)
        if (newVal === 1) {
          this.btnConfig[3].show = false
          this.btnConfig[4].show = true
          this.btnConfig[5].show = false
        } else if (newVal === 3) {
          this.btnConfig[3].show = true
          this.btnConfig[4].show = false
          this.btnConfig[5].show = true
          this.conventionShow = true
        } else {
          this.btnConfig[3].show = true
          this.btnConfig[4].show = true
          this.btnConfig[5].show = false
        }
      }
    }
  },
  created() {
    this.isEmbedded = this.$route.path === '/manage/activity/activityinfo'
  },
  mounted() {
    this.activityId = this.activity_id
    if (this.activityId) {
      this.getActivityInfo()
    }
    if (!this.activityId) {
      localStorage.getItem('add_activity_form') && (this.baseInfo = JSON.parse(localStorage.getItem('add_activity_form')))
    }
  },
  beforeDestroy() {
    // 移除监听
    window.removeEventListener('beforeunload', this.beforeunloadHandler)
  },
  methods: {
    handleConventionChange(val) {
      this.isChooseConvention = val
      this.btnConfig[5].disable = !val && true
    },
    handleBtnBarClick(e) {
      switch (e.type) {
        case 'cancel':
          this.$router.back()
          break
        case 'draft':
          this.editActivity('draft')
          break
        case 'preview':
          if (!this.activityId) {
            this.$message.warning('请先保存草稿')
            return
          }
          let isTeacher = false
          if (this.baseInfo.head_teacher_id && this.userInfo.staff_id) {
            isTeacher = this.baseInfo.head_teacher_id.includes(this.userInfo.staff_id)
          }
          if (this.baseInfo.status === 4 && !isTeacher) {
            this.$message.warning('您不是活动负责人，无法预览')
            return
          }
          const baseUrl = window.location.origin
          window.open(`${baseUrl}/training/activity/detail?activity_id=${this.activityId}&preview=true`)
          break
        case 'previous':
          if (this.currentStep > 1) {
            --this.currentStep
          }
          break
        case 'next':
          this.currentStepCheck({ newIndex: this.currentStep + 1, oldIndex: this.currentStep })
          break
        case 'submit':
          this.sumbitCheck().then(res => {
            if (res.allPass) {
              console.log(this.activityId, 'this.activityId')
              if (!this.activityId) {
                this.createActivity()
              } else {
                this.editActivity('publish')
              }
            } else {
              this.$message.warning(`${this.stepName[res.step - 1]}还有必填项未填写`)
              this.currentStep = res.step
            }
          })
          break
        default:
          break
      }
    },
    stepsChange({ newIndex, oldIndex }) {
      // 未发布的活动不可跳转发起报名页
      if (newIndex === 4 && (!this.baseInfo?.status || this.baseInfo?.status === 4)) {
        this.$message.warning('活动发布后，即可访问此步骤，查看分享报名相关信息')
        return
      }
      this.currentStepCheck({ newIndex, oldIndex })
    },
    // 当前步骤的检查
    currentStepCheck({ newIndex, oldIndex }) {
      return new Promise((resolve) => {
        this.oldStep = oldIndex
        let p = this.checkStep()
        if (p) {
          p.then(res => {
            let { isPass, data } = res
            this.drawData[`step${oldIndex}`]['isPass'] = isPass
            this.drawData[`step${oldIndex}`]['data'] = data
            this.$store.commit('activity/SET_ACTIVITY_INFO', {
              ...this.baseInfo,
              ...this.drawData[`step${oldIndex}`]['data']
            })
            if (!isPass) {
              this.$confirm('当前步骤还有必填项未填写, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
              }).then(() => {
                this.currentStep = newIndex
                resolve({ next: true })
              }).catch(() => {
                this.currentStep = oldIndex
                resolve({ next: false })
              })
            } else {
              this.currentStep = newIndex
              resolve({ next: true })
            }
          }).catch(err => {
            console.log(err, '切换步骤失败--')
          })
        } else {
          this.currentStep = newIndex
          resolve({ next: true })
        }
      })
    },
    // 提交前的检查
    async sumbitCheck() {
      // 编辑，跳过第二步点击提交，先更新第二步数据，避免有数据但提交校验失败
      if (this.baseInfo?.activity_id && !this.drawData.step2.isPass) {
        await this.$refs.registrationSettings.submit().then(res => {
          if (res.isPass) {
            this.drawData.step2.isPass = res.isPass
            this.drawData.step2.data = res.data
          }
        })
      }
      
      // 活动开始时间不能早于当前
      if (new Date().getTime() > new Date(this.drawData.step2.data.start_time).getTime()) {
        this.$message.warning('活动开始时间不能早于当前时间')
      } else {
        return new Promise((resolve) => {
          let isPass = false
          let step = 1
          this.oldStep = this.currentStep
          // 先校验当前步骤必填项是否填写了，如果填写了，将当前步骤的drawData的isPass设置为true,并且再全部校验一遍，如果全部校验通过了，则返回allPass为true,否则返回allPass为false,并且返回当前步骤的index
          let p = this.checkStep()
          p.then(res => {
            if (!res.isPass) {
              this.$message.warning(`当前步骤还有必填项未填写`)
            } else {
              this.drawData[`step${this.oldStep}`] = {
                isPass: true,
                data: res.data
              }
              for (let key in this.drawData) {
                if (!this.drawData[key].isPass) {
                  isPass = false
                  step = this.drawData[key]['index']
                  break
                } else {
                  isPass = true
                  step = 4
                }
              }
              if (isPass) {
                resolve({ allPass: true, step })
              } else {
                resolve({ allPass: false, step })
              }
            }
          })
        })
      }
    },
    checkStep() {
      let p = null
      if (this.oldStep === 1) {
        p = this.$refs.activityInfoRef.submit()
      } else if (this.oldStep === 2) {
        p = this.$refs.registrationSettings.submit()
      } else if (this.oldStep === 3) {
        p = this.$refs.interactiveSetting.submit()
      }
      return p
    },
    checkAllStep({ isDraft }) {
      let p1 = this.$refs.activityInfoRef.submit({ isDraft })
      let p2 = this.$refs.registrationSettings.submit({ isDraft })
      let p3 = this.$refs.interactiveSetting.submit({ isDraft })
      return Promise.all([p1, p2, p3])
    },
    getActivityInfo() {
      getActivityDetail({ activity_id: this.activityId }).then(res => {
        this.baseInfo = res
        this.btnConfig[2].disable = false

        if (this.baseInfo.status === 1) {
          this.btnConfig[1].disable = true
        }
      })
    },
    vaildData() {
      let q2 = this.drawData[`step2`].data.surveys
      let q3 = this.drawData[`step3`].data.surveys
      this.activityData = { ...this.activityData, ...this.drawData[`step1`].data, ...this.drawData[`step2`].data, ...this.drawData[`step3`].data }
      this.activityData.surveys = [...q2, ...q3]
    },
    createActivity() {
      this.vaildData()
      // 校验活动形式
      if (!this.validateActivityData(this.activityData)) {
        this.currentStep = 2
        return
      }
      saveActivityBaseInfo(this.activityData).then(res => {
        this.activityId = res.activity_id
        console.log(res, 'res')
        this.$message.success('创建成功')
        ++this.currentStep
        this.getActivityInfo()
        if (res.live_apply_url) {
          window.open(res.live_apply_url, '_blank')
        }
        setTimeout(() => {
          this.$router.push({
            name: 'activityPage',
            query: {
              activity_id: res.activity_id
            }
          })
        }, 1000)
      })
    },
    editActivity(type) {
      if (this.baseInfo.apply_live) {
        const h = this.$createElement
        this.$msgbox({
          title: '提示',
          closeOnClickModal: false,
          customClass: 'activity-edit-msgbox',
          message: h('p', null, [
            h('span', null, '当前活动创建时曾申请直播，编辑活动信息后，如需修改直播申请相关信息，可访问 '),
            h('a', { attrs: { href: 'https://live.learn.woa.com' }, style: { color: '#409EFF' } }, 'https://live.learn.woa.com'),
            h('span', null, '。如有直播相关疑问，可企微咨询“小T(连线HR)”')
          ]),
          showCancelButton: true,
          confirmButtonText: '确定'
        }).then(action => {
          if (action === 'confirm') {
            this.handleEditActivity(type)
          }
        })
      } else {
        this.handleEditActivity(type)
      }
    },
    handleEditActivity(type) {
      if (type === 'publish') {
        this.editActivityInfo()
      } else if (type === 'draft') {
        this.saveDraft()
      }
    },
    editActivityInfo() {
      this.vaildData()
      // 校验活动形式
      if (!this.validateActivityData(this.activityData)) {
        this.currentStep = 2
        return
      }
      let status = this.baseInfo.status
      this.activityData.activity_id = this.activityId
      saveActivityBaseInfo(this.activityData).then(res => {
        let message = status === 4 ? '创建成功' : '编辑成功'
        this.$message.success(message)
        this.getActivityInfo()
        if (status === 4) ++this.currentStep
      })
    },
    saveDraft() {
      this.checkAllStep({ isDraft: true }).then(res => {
        const data = res.reduce((acc, cur) => ({ ...acc, ...cur.data }), {})
        res[0].data.surveys = []
        data.surveys = res.flatMap(item => item.data.surveys?.filter(Boolean) ?? [])
        data.activity_id = this.activityId || ''
        activitySaveDraftApi(data).then(res => {
          this.activityId = res.activity_id || ''
          this.$message.success('保存成功')
          if (this.activity_id) {
            this.getActivityInfo()
          } else {
            setTimeout(() => {
              this.$router.push({
                name: 'activityPage',
                query: {
                  activity_id: res.activity_id
                }
              })
            }, 1000)
          }
        })
      })
    },
    validateActivityData(data) {
      const { teaching_type, city, location, meeting_create_type, meeting_info } = data

      if (!teaching_type) {
        this.$message.warning('请选择活动形式')
        return false
      }
      const teachingArr = teaching_type.split(';')
      if (teachingArr.includes('1') && !this.validateOfflineFields(city, location)) {
        return false
      }
      if (teachingArr.includes('2') && !this.validateMeetingFields(meeting_create_type, meeting_info)) {
        return false
      }
      // 参与形式 去除未选中的
      const { teaching_type: teachingType, city_, meeting_info: meetingInfo } = this.activityData
      if (teachingType.indexOf('1') === -1 && city_) {
        this.activityData.city = ''
        this.activityData.location = ''
      }
      if (teachingType.indexOf('2') === -1 && meetingInfo?.meeting_code) {
        this.activityData.meeting_create_type = 0
        this.activityData.meeting_info = {}
      }
      return true
    },
    validateOfflineFields(city, location) {
      if (!city) {
        this.$message.warning('活动形式-线下授课所在城市不能为空')
        return false
      }
      if (!location) {
        this.$message.warning('活动形式-线下授课所在地点不能为空')
        return false
      }
      return true
    },
    validateMeetingFields(meeting_create_type, meeting_info) {
      const { meeting_code, meeting_creator, meeting_url } = meeting_info || {}
      if (meeting_create_type === 2) {
        if (!meeting_code || !meeting_creator) {
          this.$message.warning('活动形式-关联已有会议，会议号和创建人不能为空')
          return false
        }
        if (!meeting_url) {
          this.$message.warning('活动形式-关联已有会议，请对会议信息做校验')
          return false
        }
      }
      return true
    },
    beforeunloadHandler(e) {
      const confirmRefresh = confirm('当前页面还未保存，确定要刷新页面吗？')
      if (!confirmRefresh) {
        e.preventDefault()
        e.returnValue = ''
      } else {
        localStorage.setItem('add_activity_form', JSON.stringify(this.activityData))
      }
    }
  }
}
</script>
<style>
#app {
  height: calc(100% - 62px) !important;
}
.el-message-box.activity-edit-msgbox {
  width: 436px;
}
</style>
<style scoped lang="less">
.activity-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  .activity-container-header {
    height: 60px;
  }
  .activity-container-content {
    margin: 20px 0 0 0;
    background-color: #f6f7f9;
    border-radius: 4px;
  }
  .activity-container-footer {
    width: 100%;
    height: 56px;
    flex-shrink: 0;
    background-color: #fff;
    position: fixed;
    bottom: 0;
    z-index: 100;
    box-shadow: 1px 1px 2px 2px #f6f7f9;
  }
}

@media screen and (max-width: 1660px) {
  .embedded-page .activity-container-footer {
    width: 988px;
    left: calc(50% - 1180px / 2 + 180px + 10px);
  }
}
@media screen and (min-width: 1661px) {
  .embedded-page .activity-container-footer {
    width: 1228px;
    left: calc(50% - 1420px / 2 + 180px + 10px);
  }
}
</style>
