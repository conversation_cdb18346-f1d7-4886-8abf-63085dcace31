<template>
    <div>
        <form method="post" :action="formData.acsUrl" id="samlform">
            <input type="hidden" name="SAMLResponse" :value="formData.samlResponse" />
            <input type="hidden" name="RelayState" :value="formData.relayState" />
            <!-- <input type="hidden" name="RelayState" value="可选的中继状态" /> -->
            <input type="submit" value="Submit" v-if="formData.acsUrl" />
        </form>
    </div>
</template>
<script>
import { postSSO } from 'config/mooc.api.conf.js'
export default {
  name: '',
  data() {
    return {
      formData: {
        samlResponse: '',
        acsUrl: '',
        relayState: ''
      }
    }
  },
  mounted() {
    let { fullPath } = this.$route
    let param = ''
    if (fullPath.indexOf('?')) {
      param = fullPath.split('?')[1]
    }
    if (param) {
      this.requestApi(param)
    }
  },
  methods: {
    requestApi(params) {
      postSSO(params).then(res => {
        console.log(res, 'aaaaaaa~~~~~~~~~~~')
        this.formData = res
        // this.$nextTick(() => {
        //   document.getElementById('samlform').submit()
        // })
      })
    }
  }
}
</script>
