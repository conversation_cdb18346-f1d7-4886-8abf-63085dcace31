<template>
  <div class="student-manage-card activity-common">
    <div class="activity-title">活动名称：<span>{{ $activityInfo.activity_name || '-' }}</span></div>
    <div class="search-card">
      <div class="show-row">
        <div class="row-left">
          <div class="form-item">
            <span class="form-item-label">学员姓名</span>
            <div class="form-item-content w-240">
              <el-input placeholder="学员姓名，多位学员可用;分离" class="search-input-box" v-model="staff_names" size="small" clearable></el-input>
            </div>
          </div>
          <div class="form-item">
            <span class="form-item-label">组织架构</span>
            <div class="form-item-content w-240">
              <sdc-unit-selector
                class="dep-selector"
                ref="deptSelectorRef"
                v-model="orgIds"
                :props="deptProps" 
                placeholder="请选择所属BG"
                @change="changeManageOrg($event)"
              />
            </div>
          </div>
        </div>
        
        <div class="row-right">
          <div class="btn-box">
            <el-button class="text-btn" type="text" size="small" @click="isFold = !isFold">{{ isFold ? '展开' : '收起'}}查询条件</el-button>
            <el-button size="small" @click="onReset">重 置</el-button>
            <el-button type="primary" size="small" @click="onSearch(1)">查 询</el-button>
          </div>
        </div>
      </div>
      <el-collapse-transition>
        <div class="fold-row" v-if="!isFold">
          <div class="form-item">
            <span class="form-item-label line-32">报名时间</span>
            <div class="form-item-content">
              <el-date-picker
                class="w-big"
                v-model="regTime"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                value-format="yyyy-MM-dd"
                size="small">
              </el-date-picker>
            </div>
          </div>
          <div class="form-item ">
            <span class="form-item-label">参加形式</span>
            <div class="form-item-content m-0">
              <el-checkbox-group v-model="searchForm.join_type" size="small">
                <el-checkbox v-for="item in participationForm" :label="item.value" :key="item.value">{{ `${item.label}（${item.count}）` }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="form-item">
            <span class="form-item-label">报名状态</span>
            <div class="form-item-content m-0">
              <el-checkbox-group v-model="searchForm.reg_status" size="small">
                <el-checkbox v-for="item in registStatus" :label="item.value" :key="item.value">{{ `${item.label}（${item.count}）` }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <div class="form-item m-0">
            <span class="form-item-label">考勤状态</span>
            <div class="form-item-content m-0">
              <div class="flex-row flex-a-start flex-j-between">
                <el-checkbox-group v-model="searchForm.status" size="small" class="mr-12">
                  <el-checkbox v-for="item in signInStatus" :label="item.value" :key="item.value">{{ `${item.label}（${item.count}）` }}</el-checkbox>
                </el-checkbox-group>
                <el-popover
                  popper-class="attendance-status-description-popover-style"
                  placement="top"
                  width="352"
                  trigger="hover"
                  :offset="0"
                  content="这是一段内容,这是一段内容,这是一段内容,这是一段内容。">
                  <ul class="popver-content">
                    <li>全勤：签到记录完整，全程参加培训;</li>
                    <li>部分缺勤：签到记录达50%以上，缺勤超过30分钟但总体参与时间超过50% ;</li>
                    <li>临时取消：开课前3天内请假，或签到次数未达到50% ;</li>
                    <li>缺勤：未取消也无任何签到记录;</li>
                  </ul>
                  <el-button class="text-btn decoration-line ml-12" type="text" size="small" slot="reference">查看考勤状态详细说明</el-button>
                </el-popover>
              </div>
            </div>
          </div>
        </div>
      </el-collapse-transition>
    </div>

    <div class="action-bar">
      <div class="student-manage-row flex-row mb-12">
        <div class="row-label mr-12">学员管理</div>
        <el-button class="min-btn" size="small" @click="addStudentPopupVisible = true">添加学员</el-button>
        <el-button class="min-btn" size="small" @click="batchDeleteStudent()">移除学员（需要勾选学员）</el-button>
        <el-button class="min-btn" size="small" @click="studentStatusEdit('regist')">修改报名状态（需勾选学员）</el-button>
        <el-button class="min-btn" size="small" @click="exportActivityStudent()">导出全部学员</el-button>
      </div>
      <div class="sign-in-row flex-row">
        <div class="row-label mr-12">考勤管理</div>
        <el-button class="min-btn" size="small" @click="studentStatusEdit('attendance')">修改考勤（需要勾选学员）</el-button>
        <el-button class="min-btn" size="small" @click="signInQrCodePopupVisible = true">签到二维码</el-button>
        <el-button class="min-btn" size="small" @click="exportActivityStudentSignInList()">下载签到表</el-button>
        <el-button class="min-btn" size="small" @click="conferenceAttendanceDataPopupVisible = true">获取腾讯会议考勤数据</el-button>
      </div>
    </div>

    <div class="table-card">
      <div class="table-content">
        <el-table :data="tableData.records" header-row-class-name="table-header-style" row-class-name="table-row-style" class="content-table" height="100%" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="48" align="center"></el-table-column>
          <el-table-column prop="emp_name" label="姓名" width="112"></el-table-column>
          <el-table-column prop="reg_date" label="报名时间" width="178"></el-table-column>
          <el-table-column prop="dept_full_name" label="组织架构" min-width="180">
            <template slot-scope="scope">
              <el-tooltip class="item" effect="dark" :content="scope.row.dept_full_name" placement="top">
                <span class="text-box">{{ scope.row.dept_full_name }}</span>
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column prop="sign_count" label="签到次数" width="88" align="center">
            <template slot-scope="scope">
              <span class="text-box">{{ scope.row.sign_count || 0 }} / {{ $activityInfo.sign_count || 0 }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="reg_status_name" label="报名状态" width="122">
            <template slot-scope="scope">
              <span :class="['text-box', regStatusColor(scope.row.reg_status) ]">{{ regStatusName(scope.row.reg_status) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status_name" label="考勤状态" width="122">
            <template slot-scope="scope">
              <span :class="['text-box', attendanceStatusColor(scope.row.status)]">{{ attendanceStatusName(scope.row.status) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="join_type" label="参加形式" width="88" align="center">
            <template slot-scope="scope">
              <span class="text-box">{{ scope.row.join_type === 2 ? '在线会议' : '线下授课' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="更新时间" width="178"></el-table-column>
          <el-table-column prop="update_name" label="经手管理员" width="112"></el-table-column>
          <el-table-column prop="is_receive_survey" label="是否接收问卷" width="116"  align="center">
            <template slot-scope="scope">
              <span class="text-box content-url">{{ scope.row.is_receive_survey === 1 ? '是' : '否'}}</span>
              <el-button class="text-btn" type="text" size="medium" @click="isReceiveSurveyStatusEdit(scope.row)">（可点击修改）</el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="140" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="viewOperationLogs(scope.row)">查看操作日志</el-button>
              <el-button type="text" size="small" @click="deleteIndividualStudents(scope.row)" :disabled="scope.row.reg_status === 1">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <el-pagination
        v-if="tableData.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="current"
        :page-sizes="[5, 10, 20, 30, 50, 100]"
        :page-size="size"
        layout="total,  prev, pager, next, sizes, jumper"
        :total="tableData.total"
      >
      </el-pagination>
    </div>

    <!-- 添加学员 -->
    <add-student-popup v-if="addStudentPopupVisible" :visible.sync="addStudentPopupVisible" @success="onSearch(1)"></add-student-popup>
    <!-- 学员状态修改 -->
    <student-status-edit-popup v-if="studentStatusEditPopupVisible" :visible.sync="studentStatusEditPopupVisible" :configData="studentStatusEditConfig" @success="onSearch(1)"></student-status-edit-popup>
    <!-- 操作日志 -->
    <view-operation-logs-popup v-if="viewOperationLogsPopupVisible" :visible.sync="viewOperationLogsPopupVisible" :attId="currentAttId"></view-operation-logs-popup>
    <!-- 签到二维码 -->
    <sign-in-qr-code-popup v-if="signInQrCodePopupVisible" :visible.sync="signInQrCodePopupVisible"></sign-in-qr-code-popup>
    <!-- 腾讯会议考勤数据 -->
    <conference-attendance-data-popup v-if="conferenceAttendanceDataPopupVisible" :visible.sync="conferenceAttendanceDataPopupVisible" @saveMeetingAttendance="onSearch()"></conference-attendance-data-popup>
  </div>
</template>

<script>
import pager from '@/mixins/pager'
import addStudentPopup from './components/addStudentPopup.vue'
import studentStatusEditPopup from './components/studentStatusEditPopup.vue'
import viewOperationLogsPopup from './components/viewOperationLogsPopup.vue'
import signInQrCodePopup from './components/signInQrCodePopup.vue'
import conferenceAttendanceDataPopup from './components/conferenceAttendanceDataPopup.vue'
import { getActivityStudentListApi, deleteActivityStudentApi, updateActivityStudentQuestionnaireStatusApi } from '@/config/classroom.api.conf.js'
import { mapState } from 'vuex'
import env from 'config/env.conf.js'

const envName = env[process.env.NODE_ENV]

export default {
  name: 'student-manage',
  components: {
    addStudentPopup,
    studentStatusEditPopup,
    viewOperationLogsPopup,
    signInQrCodePopup,
    conferenceAttendanceDataPopup
  },
  mixins: [pager],
  data() {
    return {
      participationForm: [
        { label: '线下授课', value: 1, count: 0 },
        { label: '在线会议', value: 2, count: 0 }
      ],
      registStatus: [
        { label: '已报名', value: 0, count: 0 },
        { label: '未报名霸课', value: 6, count: 0 },
        { label: '排队候补', value: 2, count: 0 },
        { label: '待上级审核', value: 3, count: 0 },
        { label: '审核未通过', value: 11, count: 0 },
        { label: '已注销报名', value: 1, count: 0 }
      ],
      signInStatus: [
        { label: '暂无数据', value: -1, count: 0 },
        { label: '全勤', value: 4, count: 0 },
        { label: '缺勤', value: 5, count: 0 },
        { label: '部分缺勤', value: 18, count: 0 },
        { label: '临时取消', value: 19, count: 0 }
      ],
      isFold: true,
      searchForm: {
        staff_names: [],
        dept_full_name: '', // 组织架构
        join_type: [1, 2], // 参与方式
        reg_status: [0, 6, 2, 3, 11, 1], // 报名状态
        status: [-1, 4, 5, 18, 19] // 签到状态
      },
      regTime: [],
      staff_names: '',
      orgIds: [],
      deptProps: {
        UnitFullName: 'dept_name',
        unitID: 'dept_id'
      },

      tableSelect: [],
      addStudentPopupVisible: false,
      studentStatusEditPopupVisible: false,
      studentStatusEditConfig: {
        type: 'regist',
        ids: []
      },
      viewOperationLogsPopupVisible: false,
      currentAttId: '',
      signInQrCodePopupVisible: false,
      conferenceAttendanceDataPopupVisible: false
    }
  },
  watch: {},
  computed: {
    ...mapState({
      $activityInfo: state => state.activity.activityInfo
    }),
    activityId () {
      return this.$route.query.activity_id
    }
  },
  created() {
    this.onSearch()
  },
  mounted() { },
  beforeDestroy() { },
  methods: {
    onSearch(num = 1) {
      this.current = num
      this.getActivityStudentList()
    },
    onReset() {
      this.searchForm = {
        staff_names: [],
        dept_full_name: '',
        join_type: [1, 2],
        reg_status: [0, 6, 2, 3, 11, 1],
        status: [-1, 4, 5, 18, 19]
      }
      this.staff_names = ''
      this.regTime = []
      this.orgIds = []
      this.$refs.deptSelectorRef.clearSelected()
    },
    handleFilteringCriteria() {
      let regTime = this.regTime || ['', '']
      this.searchForm.staff_names = this.staff_names ? this.staff_names.split(/;|；/) : []
      return {
        ...this.searchForm,
        class_id: this.activityId,
        reg_date_start: regTime[0] ? regTime[0] + ' 00:00:00' : '',
        reg_date_end: regTime[1] ? regTime[1] + ' 23:59:59' : ''
      }
    },
    getActivityStudentList() {
      const params = this.handleFilteringCriteria()
      getActivityStudentListApi({
        ...params,
        act_type: 4,
        current: this.current,
        size: this.size
      }).then(res => {
        this.tableData.records = res.records
        this.tableData.total = res.total
        this.participationForm.map(v => {
          v.count = res.join_type_count[v.value] || 0
        })
        this.registStatus.map(v => {
          v.count = res.reg_status_count[v.value] || 0
        })
        this.signInStatus.map(v => {
          v.count = res.status_count[v.value] || 0
        })
      })
    },
    changeManageOrg(e) {
      this.searchForm.dept_full_name = e.UnitFullName
    },
    handleSelectionChange(e) {
      this.tableSelect = e
    },
    exportActivityStudent() {
      const params = this.handleFilteringCriteria()
      let url = `${envName.trainingPath}api/activity/manage/members/export-members`
      
      this.downloadFileWithPost(url, params)
    },
    downloadFileWithPost(url, params) {
      fetch(url, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json' // 明确指定 JSON 格式
        },
        body: JSON.stringify(params)
      }).then(response => response.blob()).then(blob => {
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${this.$activityInfo.activity_name}-学员列表.xls`
        a.click()
        window.URL.revokeObjectURL(url)
      })
    },
    exportActivityStudentSignInList() {
      const url = `${envName.trainingPath}api/activity/manage/members/export-sign-list?activity_id=${this.activityId}&act_type=4`
      window.location.href = url
    },
    studentStatusEdit(type) {
      if (this.tableSelect.length === 0) {
        this.$message.warning('请选择需要修改的学员')
        return
      }
      if (type === 'regist') {
        this.studentStatusEditConfig = {
          type: 'regist',
          list: this.tableSelect
        }
      } else {
        this.studentStatusEditConfig = {
          type: 'attendance',
          list: this.tableSelect
        }
      }
      this.studentStatusEditPopupVisible = true
    },
    batchDeleteStudent() {
      if (this.tableSelect.length) {
        let att_ids = this.tableSelect.map(v => v.att_id)
        this.studentDelete(true, att_ids)
      } else {
        this.$message.warning('请选择需要删除的学员')
      }
    },
    deleteIndividualStudents(e) {
      if (e.att_id) {
        let att_ids = [e.att_id]
        this.studentDelete(false, att_ids)
      }
    },
    studentDelete(batch, att_ids) {
      let tips = batch ? '确定删除选中的学员吗？' : '确定删除该学员吗？'
      this.$confirm(tips, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(res => {
        deleteActivityStudentApi({
          class_id: this.activityId,
          act_type: '4',
          att_ids
        }).then(res => {
          this.$message.success('删除成功')
          this.onSearch(1)
        })
      })
    },
    viewOperationLogs(e) {
      this.currentAttId = e.att_id
      this.viewOperationLogsPopupVisible = true
    },
    isReceiveSurveyStatusEdit(e) {
      const { att_id, class_id, is_receive_survey } = e
      updateActivityStudentQuestionnaireStatusApi({
        att_id,
        activity_id: class_id,
        receive: is_receive_survey ? 0 : 1
      }).then(res => {
        this.$message.success('修改成功')
        this.getActivityStudentList()
      })
    },
    regStatusColor(status) {
      switch (status) {
        case 0:
          return 'color-success'
        case 6:
          return 'color-warning'
        case 2:
          return 'color-primary'
        case 3:
          return 'color-primary'
        case 11:
          return 'color-fail'
        case 1:
          return 'color-danger'
        default:
          return ''
      }
    },
    regStatusName(status) {
      switch (status) {
        case 0:
          return '已报名'
        case 6:
          return '未报名霸课'
        case 2:
          return '排队候补'
        case 3:
          return '待上级审核'
        case 11:
          return '审核未通过'
        case 1:
          return '已注销报名'
        default:
          return ''
      }
    },
    attendanceStatusColor(status) {
      switch (status) {
        case 4:
          return 'color-success'
        case 5:
          return 'color-danger'
        case 18:
          return 'color-warning'
        case 19:
          return 'color-primary'
        default:
          return 'color-fail'
      }
    },
    attendanceStatusName(status) {
      switch (status) {
        case 4:
          return '全勤'
        case 5:
          return '缺勤'
        case 18:
          return '部分缺勤'
        case 19:
          return '临时取消'
        default:
          return '暂无数据'
      }
    }
  }
}
</script>

<style lang="less">
  .attendance-status-description-popover-style {
    padding: 16px !important;
    border-radius: 6px;
    border: 0.5px solid #DCDCDC;
    .popver-content {
      font-size: 12px;
      color: #000000e6;
      line-height: 20px;
      padding-left: 16px;
      & > li {
        list-style: disc;
      }
    }
  }
</style>

<style lang="less" scoped>
@import '~assets/css/activity-common.less';

.student-manage-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow-y: auto;
  :deep(.el-checkbox), :deep(.el-checkbox-button__inner), :deep(.el-radio) {
    font-weight: 400;
  }

  .activity-title {
    padding: 32px 28px 20px;
    color: #00000099;
    font-size: 14px;
    line-height: 22px;
    & > span {
      color: #000000e6;
    }
  }
  .search-card {
    padding: 16px 12px 16px;
    margin: 0 28px 20px;
    border-radius: 8px;
    background: #F8F8F8;
    .show-row {
      display: flex;
      justify-content: space-between;
      .row-left {
        flex: 1;
        display: flex;
      }
      .row-right {
        flex-shrink: 0;
      }
      .form-item {
        margin-bottom: 0px !important;
      }
    }
    .fold-row {
      margin-top: 16px;
      margin-bottom: -6px;
      .form-item {
        line-height: 22px;
        .form-item-label {
          line-height: inherit;
        }
        :deep(.el-checkbox-group) {
          height: 22px;
        }
        :deep(.el-checkbox) {
          margin-right: 16px;
          .el-checkbox__label {
            padding-left: 8px;
          }
          .el-checkbox__input.is-checked .el-checkbox__inner {
            border-radius: 4px;
          }
        }
      }
    }
    .form-item {
      display: flex;
      align-items: flex-start;
      line-height: 32px;
      &:not(:last-child) {
        margin-bottom: 16px;
      }
      .form-item-label {
        width: 56px;
        text-align: right;
        color: #00000099;
        font-size: 14px;
        line-height: 32px;
        margin-right: 12px;
        flex-shrink: 0;
      }
      .form-item-content {
        margin-right: 24px;
        flex: 1;
      }
    }
    .text-btn {
      color: #0052d9;
      &:hover {
        opacity: 0.9;
      }
    }
    .decoration-line {
      padding: 0;
      text-decoration-line: underline;
      text-underline-offset: 2px;
    }
    :deep(.sdc-selector) {
      .selector-container {
        height: 32px;
        padding-top: 0px;
      }
      .suffix-open {
        line-height: 32px;
        height: 32px;
        .el-button {
          height: 32px;
        }
      }
    }
    :deep(.el-checkbox-group) {
      height: 32px;
    }
  }
  .action-bar {
    background: #F8F8F8;
    padding: 16px 12px;
    margin: 0 28px;
    border-radius: 8px;
    .row-label {
      color: #00000099;
      font-size: 14px;
    }
    .min-btn {
      font-size: 14px;
      color: #000000e6;
      border-color: #fff;
      &:hover {
        color: #3464E0;
        border-color: #3464E0;
      }
    }
  }
  .table-card {
    flex: 1;
    padding: 20px 28px 20px;
    min-height: 400px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    .table-content {
      flex: 1;
      overflow: hidden;
    }
    :deep(.el-table__empty-block) {
      text-align: left;
    }
    :deep(.el-table__body-wrapper) {
      &::-webkit-scrollbar {
        height: 10px;
      }
    }
  }
  .text-box {
    max-height: 44px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
  .text-btn {
    padding: 0;
  }

  .flex-row {
    display: flex;
    align-items: center;
  }
  .flex-a-start {
    align-items: flex-start;
  }
  .flex-j-between {
    justify-content: space-between;
  }
  .w-280 {
    width: 280px;
  }
  .w-240 {
    width: 240px;
  }
  
  .min-btn {
    min-width: 88px;
  }

  .m-0 {
    margin: 0 !important;
  }

  .line-32 {
    line-height: 32px !important;
  }
}
</style>
