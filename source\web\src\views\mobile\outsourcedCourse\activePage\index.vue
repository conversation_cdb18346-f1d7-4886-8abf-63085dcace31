<template>
  <div class="active-page">
    <div class="img-content" @click="goTo">
      <van-image class="top-img" :src="topImgUrl">
        <template v-slot:error>
          <img
            class="top-img"
            src="../../../../assets/img/mobile/geekBang/active-top.png"
          />
        </template>
      </van-image>
    </div>
    <div class="content-main">
      <div class="my-card-info">
        <div class="top">
          <span class="title"
            >我的学霸卡:&nbsp;
            <span class="num">{{ cardInfo.balance || 0 }} </span>张
          </span>
          <!-- <span class="detail-link" @click="toActiveDetail"
            >点击查看此次赠课活动详情</span
          > -->
        </div>
        <div class="btns">
          <div class="btn" @click="toMooc">已兑课程</div>
          <div class="btn" @click="openDetailPop">领用详情</div>
          <div class="btn" @click="toRulesDetail">活动规则</div>
        </div>
      </div>

      <!-- --- -->
      <!-- <div class="card-info-common init-card-info">
        <div class="card-content">
          <div class="type">学霸卡</div>
          <span class="effect">可用于免费兑换外部付费好课</span>
          <div class="num-info">
            <div class="left">
              <span class="one-line"
                >可领 <span class="color-cupton-num">{{ cardInfo.can_get_geek_num || 0 }}</span> 张，库存 <span class="color-cupton-num">{{ cardInfo?.quantity || 0 }}</span> 张</span
              >
              <span class="one-line">领取后7个自然日内有效</span>
            </div>
            <div
              class="collect-btn"
              :class="{ 'init-btn-disabled': !initCardCanGet }"
              @click="throttleFn(1, initCardCanGet ? true : false)"
            >
              点击领取
            </div>
          </div>
        </div>
        <div
          class="desc"
          v-if="configInfo.top_info && configInfo.top_info.desc"
        >
          {{ configInfo.top_info && configInfo.top_info.desc }}
        </div>
      </div>
      <div class="card-info-common stand-card-info">
        <div class="card-content">
          <div class="type">学霸卡 · 学习达标奖励</div>
          <span class="effect">可用于免费兑换外部付费好课</span>
          <div class="num-info">
            <div class="left">
              <span class="one-line"
                >可领 <span class="color-cupton-num">{{ cardInfo.can_get_reward_num || 0 }}</span> 张，库存 <span class="color-cupton-num">{{ cardInfo?.quantity || 0 }}</span> 张</span
              >
              <span class="one-line">领取后7个自然日内有效</span>
            </div>
            <div
              class="collect-btn"
              :class="{ 'stand-btn-disabled': !standCardCanGet }"
              @click="throttleFn(2, standCardCanGet ? true : false)"
            >
              点击领取
            </div>
          </div>
        </div>
        <div
          class="desc"
          v-if="configInfo.my_ticket_info && configInfo.my_ticket_info.desc"
        >
          {{ configInfo.my_ticket_info && configInfo.my_ticket_info.desc }}
        </div>
      </div> -->
      <!-- --- -->

      <div class="card-info-common init-card-info-new">
        <div class="card-content" :class="getClass1(0)" :style="bgiStyle1()">
          <div class="type" :class="{'init-disable-class1': isDisableStyle1()}">学霸卡
            <!-- <img style="width:40px;height:16px;" :src="require('@/assets/img/mobile/geekBang/geek-font-m.png')" alt=""> -->
          </div>
          <span class="effect" :class="{'init-disable-class2': isDisableStyle1()}">可用于兑换外部付费好课</span>
          <div class="num-info">领取后7个自然日内有效</div>
          <div class="collect-btn-box">
            <div class="collect-btn" v-if="initCardCanGet" @click="throttleFn(1, true)">点击领取</div>
            <div class="collect-btn stand-btn-disabled" v-else-if="getNums > 0 && cardsOrder[0] && [0, 'consume', 'manage_deduct', undefined].includes(cardsOrder[0].verb_id)" @click="throttleFn(1, false)">已领取</div>
            <!-- 点击领取、已领取、抢光了 -->
            <div class="collect-btn stand-btn-gray" v-else @click="throttleFn(1, false)">{{ showText(0) }} </div>
          </div>
        </div>
        <div class="stand-card-info-new-box">
          <div class="card-content"  :class="getClass(item)" :style="bgiStyle(item)" v-for="item in 4" :key="item">
            <div class="type" :class="{'stand-disable-class1': isDisableStyle(item)}">学霸卡
              <!-- <img style="width:40px;height:16px;" :src="require('@/assets/img/mobile/geekBang/geek-font-m.png')" alt=""> -->
            </div>
            <span class="effect" :class="{'stand-disable-class2': isDisableStyle(item)}">可用于兑换外部付费好课</span>
            <div class="num-info">领取后7个自然日内有效</div>
            <div class="collect-btn-box">
              <div class="collect-btn" v-if="getNums >= 1 && item >= getNums && standCardCanGet"
                @click="throttleFn(2, true)"
              >
                点击领取
              </div>
              <div class="collect-btn stand-btn-disabled" v-else-if="getNums >= 1 && item < getNums && cardsOrder[item] && [0, 'consume', 'manage_deduct', undefined].includes(cardsOrder[item].verb_id)"
                @click="throttleFn(2, false)"
              >
                已领取
              </div>
              <!-- 点击领取、已领取、抢光了 -->
              <div class="collect-btn stand-btn-gray" v-else
                @click="throttleFn(2, false)"
              >
              {{ showText(item) }}
              </div>
            </div>
          </div>
        </div>
        <div class="text-box">
          <div><span class="text-title">领取说明：每月28日12:00开放学霸卡领取（如遇周末和法定节假日，将在假期前一天下发）；</span>每次可领取<span class="text-attention">1张</span>卡，完成解锁条件后可额外领取<span class="text-attention">1张</span>，活动期间每人最多领取<span class="text-attention">5张</span>卡，先到先得，领完即止；
          </div>
          <div>
            <span class="text-title">解锁条件：</span>每张学霸卡可兑换<span class="text-attention">1门</span>课，学习该课程中任意<span class="text-attention">5个任务</span>，每个任务学习时长不低于<span class="text-attention">3分钟</span>，即可解锁下一张学霸卡。
          </div>
          <!-- <div class="warm">
            无需使用学霸卡兑换的【极客时间】课程不在本次活动范围内
          </div> -->
        </div>
      </div>

      <div class="good-course-list" v-if="(courseList || []).length"
        :dt-areaid="dtListBodyList('area')"
        :dt-remark="dtListBodyList('remark')"
      >
        <div class="top">
          <span class="title">立即兑换课程</span>
          <span class="link" @click="toActiveDetail"
            :dt-areaid="dtMoerCourses('area', '1')"
            :dt-eid="dtMoerCourses('eid', '1')"
            :dt-remark="dtMoerCourses('remark', '1')"
            > >>>  查看更多可兑换好课 </span
          >
        </div>
        <div
          class="list-content"
        >
          <div
            class="item"
            v-for="(item, index) in courseList || []"
            :key="item.course_id"
            @click="toCourseDetail(item.course_url)"
            :dt-areaid="dtListBody(item, 'area', index)"
            :dt-eid="dtListBody(item , 'eid', index)"
            :dt-remark="dtListBody(item , 'remark', index)"
          >
            <div class="cover">
              <!-- 走内容中心 -->
              <van-image
                class="cover-img"
                :src="getCourseCoverUrl(item.course_pic_id)"
              >
                <template v-slot:error>
                  <img
                    class="cover-img"
                    src="../../../../assets/img/mobile/geekBang/err-cover-img.png"
                  />
                </template>
              </van-image>
              <div class="time" v-if="item.course_length">
                {{ item.course_length }}分钟
              </div>
            </div>
            <div class="text-main">
              <div class="two-line">
                <span class="course-type">{{getCourseName(item.course_from_name, item.course_from)}}</span>
                <span class="title">{{ item.course_name }}</span>
              </div>
              <div class="desc two-line">{{ processString(item.course_desc || '') }}</div>
              <!-- <div class="desc two-line" v-html="item.course_desc"></div> -->
            </div>
          </div>
        </div>
        <div class="link">
          <span
            @click="toActiveDetail"
            :dt-areaid="dtMoerCourses('area', '2')"
            :dt-eid="dtMoerCourses('eid', '2')"
            :dt-remark="dtMoerCourses('remark', '2')"
          > &gt;&gt;&gt;  查看更多可兑换好课 &lt;&lt;&lt; </span>
        </div>
      </div>
    </div>
    <!-- 领券详情弹窗 -->
    <van-popup
      class="card-detail-pop"
      v-model="showCardDetail"
      position="bottom"
      round
      closeable
      safe-area-inset-bottom
    >
      <div class="title">学霸卡领取和使用详情</div>
      <div style="overflow-y: auto;">
        <div class="table">
          <div class="table-title">
            <!-- <div class="no">序号</div> -->
            <div class="time">领取时间</div>
            <div class="type">获得方式</div>
            <div class="status">卡券状态</div>
            <div class="desc">说明</div>
          </div>
          <div class="item-content">
            <div
              class="item"
              v-for="(item, index) in cardDetailList"
              :key="index"
            >
              <!-- <div class="no">{{ ++index }}</div> -->
              <div class="time">{{ resolveTime(item.receive_time) }}</div>
              <div class="type">{{ item.receive_verb_name }}</div>
              <div class="status">
                <span class="status-text" :class="getCardStyle(item.verb_id)">
                  {{
                    getCardStatus(item.verb_id)
                  }}
                </span>
              </div>
              <div class="desc">{{ resolveDescData(item) }}</div>
              <!-- <div
                class="course-link"
                v-if="item.status !== 2"
                @click="toExchangeCourse"
              >
                去兑换课程
              </div> -->
            </div>
          </div>
        </div>
      </div>
      <div class="foot-page">
        <Pagination
          v-model="currentPage"
          :page-count="pageCount"
          mode="simple"
          @change="pageChange"
        >
          <template #prev-text>
            <span class="prev-btn">
              <van-icon name="arrow-left" style="margin-right: 4px" /> 上一页
            </span>
          </template>
          <template #next-text>
            <span class="next-btn">
              下一页<van-icon name="arrow" style="margin-left: 4px" />
            </span>
          </template>
          <template #pageDesc>
            <span class="page-desc">
              <span class="current">{{ currentPage }}</span> / {{ pageCount }}
            </span>
          </template>
        </Pagination>
      </div>
    </van-popup>
    <!-- 已兑换课程 -->
    <exchangedMobile :isExchanged.sync="isExchanged" ></exchangedMobile>
  </div>
</template>

<script>
// 移动端适配
import 'amfe-flexible/index.js'
import { mapState } from 'vuex'
import env from 'config/env.conf.js'
import translateLang from '@/views/mobile/mixins/translateLang.js'
import { Toast, Pagination } from 'vant'
import exchangedMobile from './exchangedMobile.vue'
import {
  getHomePageConfig,
  getHomePageInfo,
  claimPoints,
  queryGeekRecord,
  getUserActiveInfo
} from '@/config/mooc.api.conf.js'
import { throttle } from '@/utils/tools.js'
const HTTPS_REG = /^https:\/\//
// 映射对象
const STATUS_MAP = {
  0: '待兑换',
  consume: '已兑换',
  deduct_expire: '已失效',
  manage_deduct: '已兑换'
  // manage_deduct: '已使用'
}
const STATUS_STYLE_MAP = {
  0: 'status-waite-use',
  consume: 'status-oready-used',
  deduct_expire: 'status-no-effict',
  manage_deduct: 'status-oready-used'
}
export default {
  name: 'activePage',
  mixins: [translateLang],
  components: { Pagination, exchangedMobile },
  data () {
    return {
      configInfo: {}, // 配置信息
      cardInfo: {}, // 是否可领券、库存信息
      showCardDetail: false, // 领券弹窗是否显示
      currentPage: 1,
      pageSize: 5,
      pageCount: 0,
      cardDetailList: [], // 卡券详情列表
      isExchanged: false,
      courseList: [],
      xueBaCardConfig: {},
      activityId: ''
    }
  },
  computed: {
    ...mapState(['userInfo']),
    topImgUrl () {
      // 顶部图片
      //   const envName = env[process.env.NODE_ENV]
      //   if (this.configInfo.banner) {
      //     return (
      //       this.configInfo.banner.banner_pic_url ||
      //       `${envName.contentcenter}content-center/api/v1/content/imgage/${this.configInfo.banner.banner_pic_id}/preview`
      //     )
      //   }
      return require('../../../../assets/img/mobile/geekBang/active-top.png')
    },
    initCardCanGet () {
      // 能否领取初始卡券
      return this.cardInfo.can_get_geek_num > 0
    },
    standCardCanGet () {
      // 能否领取达标卡券
      return this.cardInfo.can_get_reward_num > 0 && this.cardInfo.can_get_geek
    },
    // 已经领取的数量
    getNums() {
      return this.cardDetailList.length || 0
    },
    // 领取顺序
    cardsOrder() {
      return JSON.parse(JSON.stringify(this.cardDetailList)).reverse()
    },
    dtListBodyList() {
      return (type) => {
        let { audience_id, audience_name } = this.configInfo
        if (type === 'area') {
          return `area_${audience_id}`
        } else if (type === 'eid') {
          return `element_${audience_id}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: '极客时间活动首页',
            page_type: '极客时间活动首页',
            container: audience_name,
            container_id: '',
            content_name: '课程推荐列表',
            terminal: 'H5'
          })
        }
      }
    },
    dtMoerCourses() {
      return (type, index) => {
        let { audience_id, audience_name } = this.configInfo
        if (type === 'area') {
          return `area_${audience_id}_more_${index}`
        } else if (type === 'eid') {
          return `element_${audience_id}_more_${index}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: '极客时间活动首页',
            page_type: '极客时间活动首页',
            container: audience_name,
            container_id: '',
            click_type: 'button',
            content_name: '查看更多可兑换好课' + index,
            terminal: 'H5'
          })
        }
      }
    },
    // 埋点
    dtListBody() {
      return (item, type, index) => {
        let dt_id = `${item.course_from}_${item.course_id}`
        if (type === 'area') {
          return `area_${dt_id}`
        } else if (type === 'eid') {
          return `element_${dt_id}`
        } else if (type === 'remark') {
          return JSON.stringify({
            page: '极客时间活动首页',
            page_type: '极客时间活动首页',
            container: index <= 7 ? `${this.configInfo.audience_name}_8` : `${this.configInfo.audience_name}_16`,
            container_id: '',
            click_type: 'data',
            content_type: '培养项目',
            act_type: '102',
            content_id: item.course_id,
            content_name: item.course_name,
            terminal: 'H5'
          })
        }
      }
    },
    getCourseName() {
      return (name, type) => {
        if (name) {
          return name
        } else if (type) {
          if (type === 'geekBang') {
            return '极客时间'
          } else if (type === 'sanjieke') {
            return '三节课'
          } else {
            return '极客时间'
          }
        } else {
          return '极客时间'
        }
      }
    }
  },
  watch: {
    'userInfo.staff_id': {
      handler (newValue) {
        if (newValue) {
          this.getHomePageInfoFn()
          this.getCardAgeAndUsedDetail()
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted () {
    this.init()
  },
  methods: {
    // 置灰的背景下按钮应该显示的文字
    showText(item) {
      if (this.cardsOrder[item] && this.cardsOrder[item].verb_id === 'deduct_expire') {
        return '已领取'
      } else if (!this.cardInfo.quantity) {
        return '抢光了'
      } else {
        return '点击领取'
      }
    },
    // 达标卡 右上角图片
    getClass(item) {
      if (this.getNums >= 1 && item >= this.getNums && this.standCardCanGet) {
        return `` // 待领取
      } else if (this.getNums >= 1 && item < this.getNums && this.cardsOrder[item] && [0, 'consume', 'manage_deduct', undefined].includes(this.cardsOrder[item].verb_id)) {
        if ([0, undefined].includes(this.cardsOrder[item].verb_id)) {
          return `card-content-1` // 待兑换
        } else {
          return `card-content-2` // 已兑换
        }
      } else {
        if (this.cardsOrder[item] && this.cardsOrder[item].verb_id === 'deduct_expire') {
          return `card-content-4` // 已失效
        } else {
          return `card-content-3` // 未解锁
        }
      }
    },
    // 初始卡 右上角图片
    getClass1() {
      if (this.initCardCanGet) {
        return `` // 待领取
      } else if (this.getNums > 0 && this.cardsOrder[0] && [0, 'consume', 'manage_deduct', undefined].includes(this.cardsOrder[0].verb_id)) {
        if ([0, undefined].includes(this.cardsOrder[0].verb_id)) {
          return `card-content-1` // 待兑换
        } else {
          return `card-content-2` // 已兑换
        }
      } else {
        return `card-content-4` // 已失效
      }
    },
    // 达标卡 背景图
    bgiStyle(item) {
      if (this.getNums >= 1 && item >= this.getNums && this.standCardCanGet) {
        return `background-image: url(${require('@/assets/img/mobile/geekBang/card-stand-new1.png')});`
      } else if (this.getNums >= 1 && item < this.getNums && this.cardsOrder[item] && [0, 'consume', 'manage_deduct', undefined].includes(this.cardsOrder[item].verb_id)) {
        return `background-image: url(${require('@/assets/img/mobile/geekBang/card-stand-new1.png')});`
      } else {
        return `background-image: url(${require('@/assets/img/mobile/geekBang/card-stand-no.png')});`
      }
    },
    // 初始卡券 背景图
    bgiStyle1() {
      if (this.initCardCanGet) {
        return `background-image: url(${require('@/assets/img/mobile/geekBang/stand-card-bg.png')});`
      } else if (this.getNums > 0 && this.cardsOrder[0] && [0, 'consume', 'manage_deduct', undefined].includes(this.cardsOrder[0].verb_id)) {
        return `background-image: url(${require('@/assets/img/mobile/geekBang/stand-card-bg.png')});`
      } else {
        return `background-image: url(${require('@/assets/img/mobile/geekBang/card-init-no-effict.png')});`
      }
    },
    // 达标卡 判断是否置灰 “学霸卡”和“可用于兑换外部付费好课”文字置灰
    isDisableStyle(item) {
      if (this.getNums >= 1 && item >= this.getNums && this.standCardCanGet) {
        return false
      } else if (this.getNums >= 1 && item < this.getNums && this.cardsOrder[item] && [0, 'consume', 'manage_deduct', undefined].includes(this.cardsOrder[item].verb_id)) {
        return false
      } else {
        return true
      }
    },
    // 初始卡券 判断是否置灰 “学霸卡”和“可用于兑换外部付费好课”文字置灰
    isDisableStyle1() {
      if (this.initCardCanGet) {
        return false
      } else if (this.getNums > 0 && this.cardsOrder[0] && [0, 'consume', 'manage_deduct', undefined].includes(this.cardsOrder[0].verb_id)) {
        return false
      } else {
        return true
      }
    },
    /**
     * 字符串去除标签类型的字符等
     * @param {string} str
     * @returns {string}
     */
    processString(str) {
      if (!str || typeof str !== 'string') {
        return ''
      }
      // 去除HTML标签
      str = str.replace(/<[^>]*>/g, '')
      // 去除特殊字符
      str = str.replace(/&nbsp;/g, ' ')
      str = str.replace(/&amp;/g, '&')
      str = str.replace(/&lt;/g, '<')
      str = str.replace(/&gt;/g, '>')
      // 可以继续添加其他特殊字符的替换规则
      return str
    },
    // 课程封面 内容中心逻辑
    getCourseCoverUrl (course_pic_id) {
      if (HTTPS_REG.test(course_pic_id)) {
        return course_pic_id
      }
      const envName = env[process.env.NODE_ENV]
      return `${envName.contentcenter}content-center/api/v1/content/imgage/${course_pic_id}/preview`
    },
    // 初始化数据
    init (update = false) {
      this.getHomePageData()
      if (update) {
        // 更新卡券数据
        this.getHomePageInfoFn()
        this.getCardAgeAndUsedDetail()
      }
    },
    // 获取活动详情数据
    async getHomePageData () {
      try {
        let res = await getHomePageConfig()
        let data = res ? JSON.parse(res) : {}
        this.configInfo = data
      } catch (error) {
      }
    },
    // 积分授予-查询是否可以领取geek
    async getHomePageInfoFn () {
      try {
        let res = await getHomePageInfo({ staff_id: this.userInfo.staff_id, acct_type_code: 'xuebaCommon' })
        this.cardInfo = res
        this.activityId = res.activity_id
        this.getUserActiveInfo()
      } catch (error) {
        console.error('积分授予-查询是否可以领取geek-error: ', error)
      }
    },
    // 发请求领取积分卡
    async claimPointsFn (grant_type = 'xuebaCommon') {
      try {
        let payload = {
          staff_id: this.userInfo.staff_id,
          staff_name: this.userInfo.staff_name,
          acct_type_code: 'xuebaCommon',
          busiId: this.activityId,
          grant_amt: '1',
          grant_type: grant_type
        }
        await claimPoints(payload)
        // 领取成功
        Toast({
          message: '领取成功',
          icon: 'passed'
        })
        // 初始化数据
        this.init(true)
      } catch (error) {
        // 领取失败
        Toast({
          message: '领取失败',
          icon: 'close'
        })
        console.error('发请求领取积分卡---error: ', error)
      }
    },
    // 获取学霸卡领取和使用详情
    async getCardAgeAndUsedDetail () {
      try {
        // 发请求获取分页数据
        let payload = {
          staff_id: this.userInfo.staff_id,
          current: this.currentPage,
          size: this.pageSize,
          acct_type_code: 'xuebaCommon'
        }
        let res = await queryGeekRecord(payload)
        this.cardDetailList = res.records || []
        this.pageCount = res.pages
      } catch (error) {
      }
    },
    // 点击领取卡券 节流方法
    throttleFn: throttle(function (code, status) {
      this.getCoupon(code, status)
    }, 500),
    // 点击领取卡券
    getCoupon(type, status) {
      switch (type) {
        case 1:
          // 初始卡
          if (status) {
            this.claimPointsFn('xuebaCommon')
          } else {
            // hasGetGeek-是否已经领取，canGetGeekNum-可领取的兑换券的数量，formalStaff-是否正式员工，totalBalance-已领取数量，limit-最大限制数量
            const { has_get_geek: hasGetGeek, can_get_geek_num: canGetGeekNum, formal_staff: formalStaff, total_balance: totalBalance, limit } = this.cardInfo
            if (!formalStaff) {
              Toast('非集团本部正式员工不可领取')
            } else if (totalBalance >= limit) {
              Toast('已达到领取数量上限')
            } else if (!hasGetGeek && canGetGeekNum <= 0) {
              Toast('本轮学霸卡已发放完毕，暂时无法领取，敬请期待后续轮次的发放活动')
            } else if (hasGetGeek) {
              Toast('你本轮次已领取过学霸卡，每人限领1次，敬请期待后续轮次的发放活动')
            } else {
            }
          }
          break
        case 2:
          // 达标卡
          if (status) {
            this.claimPointsFn('reward')
          } else {
            // hasGetGeekReward-是否已经领取，canGetRewardNum-可领取的兑换券的数量，canGetGeek-是否可以领取，formalStaff-是否正式员工，totalBalance-已领取数量，limit-最大限制数量
            const { has_get_geek_reward: hasGetGeekReward, can_get_reward_num: canGetRewardNum, can_get_geek: canGetGeek, formal_staff: formalStaff, total_balance: totalBalance, limit } = this.cardInfo
            let canBeClaimed = canGetRewardNum > 0 && canGetGeek
            if (!formalStaff) {
              Toast('非集团本部正式员工不可领取')
            } else if (totalBalance >= limit) {
              Toast('已达到领取数量上限')
            } else if (!canBeClaimed) {
              Toast('暂无领取资格，请查看活动规则并完成学习条件后再点击领取')
            } else if (!hasGetGeekReward && canGetRewardNum <= 0) {
              Toast('本轮学霸卡已发放完毕，暂时无法领取，敬请期待后续轮次的发放活动')
            } else if (hasGetGeekReward) {
              Toast('你本轮次已领取过学霸卡，每人限领1次，敬请期待后续轮次的发放活动')
            } else {
            }
          }
          break
        default:
          break
      }
    },
    // 处理列表的"说明"字段
    resolveDescData (item) {
      if (item.verb_id === 'consume' || item.verb_id === 'manage_deduct') {
        return `使用时间：${this.resolveTime(item.deduct_time)}`
      } else if (item.verb_id === 'deduct_expire') {
        return `失效时间：${this.resolveTime(item.expire_time)}`
      } else {
        return `领取7天后，卡券失效，请及时使用`
      }
    },
    // 打开领用详情弹窗
    openDetailPop () {
      this.showCardDetail = true
    },
    // 券领取详情分页改变
    pageChange (page) {
      this.currentPage = page
      this.getCardAgeAndUsedDetail()
    },
    // 获取卡券状态
    getCardStatus (code = 0) {
      let statusText = STATUS_MAP[code] || STATUS_MAP[0]
      return statusText
    },
    // 获取券状态样式
    getCardStyle (code = 0) {
      let statusStyle = STATUS_STYLE_MAP[code] || STATUS_STYLE_MAP[0]
      return statusStyle
    },
    // 点击顶部图片跳转
    goTo () {
      let url = this.configInfo.banner?.banner_pic_go_url || ''
      if (url) {
        window.open(url)
      }
    },
    // 查看更多可兑换好课 外部好课专区
    toActiveDetail () {
      // let url = 'https://sdc.qq.com/s/1ZyHTE?scheme_type=moduledetail&config_id=4785&name=%E5%A4%96%E9%83%A8%E6%8A%80%E6%9C%AF%E5%A5%BD%E8%AF%BE&page_id=107&lang=zh'
      let url = this.xueBaCardConfig.course_more_link || 'https://sdc.qq.com/s/sYkoua?scheme_type=homepage&page_id=512'
      window.open(url)
    },
    // 活动规则
    toRulesDetail () {
      let url = 'https://sdc.qq.com/s/LsraYG?scheme_type=graphic&graphic_id=38684&from_act_id=38684'
      window.open(url)
    },
    // 获取学霸卡基础信息
    async getUserActiveInfo() {
      try {
        let res = await getUserActiveInfo({ activity_id: this.activityId })
        this.xueBaCardConfig = res
        this.courseList = res.course_list || []
      } catch (error) {
        console.log('获取基础信息: ', error)
      }
    },
    // 已兑课程
    toMooc () {
      this.isExchanged = true
      // window.wx.miniProgram.navigateTo({
      //   url: `/pages/mooc/myProject/index`
      // })
    },
    // 跳转到课程详情
    toCourseDetail (href) {
      window.open(href)
      //   window.wx.miniProgram.reLaunch({
      //     url: href
      //   })
    },
    // 去兑换课程
    // toExchangeCourse () {
    //   window.wx.miniProgram.reLaunch({
    //     url: `/pages/mooc/projectList/index`
    //   })
    // }
    // 处理时间数据
    resolveTime(time) {
      if (!time) return ''
      let srt = time.split(':').slice(0, -1).join(':')
      return srt.replace(/-/g, '/')
    }
  }
}
</script>

<style lang="less" scoped>
* {
  box-sizing: border-box;
}
.active-page {
  overflow-y: auto;
  height: 100%;
  background: #f4faff;
  padding-bottom: 21px;
  .top-img {
    width: 100%;
  }
  .content-main {
    position: relative;
    padding: 0 16px;
    margin: -65px auto 0;
    & > div {
      padding: 20px;
      border-radius: 16px;
      background-color: #fff;
      box-shadow: 0 0 8px 0 #b5d0e30a;
      &:nth-child(n + 1) {
        margin-top: 12px;
      }
    }
    .color-cupton-num {
      color: #ed7b2f !important;
    }
    .my-card-info {
      .top {
        display: flex;
        font-size: 14px;
        line-height: 24px;
      }
      .title {
        position: relative;
        color: #000000e6;
        font-weight: 500;
        height: 24px;
        line-height: 24px;
        background: url('../../../../assets/img/mobile/geekBang/bd.png') no-repeat 0 18px;
      }
      .num {
        // color: #0052d9;
        color: #0052d9;
        font-size: 18px;
      }
      .detail-link {
        margin-left: 16px;
        color: #0052d9;
        text-decoration-line: underline;
        cursor: pointer;
      }
      .btns {
        margin-top: 16px;
        display: flex;
        justify-content: space-between;
      }
      .btn {
        font-size: 14px;
        color: #0052d9;
        font-weight: 500;
        width: 93px;
        height: 32px;
        line-height: 32px;
        text-align: center;
        border-radius: 6px;
        cursor: pointer;
        background: var(---Brand1-Light, #ecf2fe);
      }
    }
    .card-info-common {
      .card-content {
        height: 127px;
        padding: 12px;
        background-size: 100% 100%;
      }
      .type {
        font-size: 16px;
        font-weight: 700;
        line-height: 22px;
        font-family: "Source Han Sans CN";
        // color: #573a18;
        color: #0c3102;
        display: flex;
        align-items: center;
        img {
          margin-left: 4px;
        }
      }
      .effect {
        margin-top: 2px;
        padding-bottom: 6px;
        font-size: 10px;
        line-height: 16px;
        border-bottom: 1px dashed #90714b;
        color: #6e491f;
        // border-bottom-color: #90714b;
      }
      .num-info {
        margin-top: 10px;
        color: #ff6600;
        font-size: 10px;
        line-height: 16px;
      }
      .left {
        display: flex;
        flex-direction: column;
        font-size: 12px;
        line-height: 16px;
        max-width: 158px;
        span:last-child {
          margin-top: 4px;
        }
      }
      .collect-btn-box {
        margin-top: 13px;
        display: flex;
        justify-content: flex-end;
      }
      .collect-btn {
        width: 72px;
        height: 22px;
        line-height: 22px;
        color: #ffffff;
        font-size: 12px;
        font-weight: 500;
        text-align: center;
        border-radius: 64px;
        cursor: pointer;
      }
      .desc {
        margin-top: 12px;
        color: #00000099;
        font-size: 14px;
        line-height: 22px;
      }
    }
    // .init-card-info {
    //   .card-content {
    //     background: url('../../../../assets/img/mobile/geekBang/init-card-bg.png')
    //       no-repeat;
    //     background-size: 100% 100%;
    //   }
    //   .type {
    //     color: #023269;
    //   }
    //   .effect {
    //     color: #00355c;
    //     border-bottom: 1px dashed #486646;
    //   }
    //   .left {
    //     span:first-child {
    //       color: #3b5f96;
    //     }
    //     span:last-child {
    //       color: #00255c8c;
    //     }
    //   }
    //   .collect-btn {
    //     background: #2a648e;
    //   }
    //   .init-btn-disabled {
    //     background: #c2dbec;
    //   }
    // }
    // .stand-card-info {
    //   .card-content {
    //     background-image: url('../../../../assets/img/mobile/geekBang/stand-card-bg.png');
    //   }
    //   .type {
    //     color: #573a18;
    //   }
    //   .effect {
    //     color: #6e491f;
    //     border-bottom-color: #90714b;
    //   }
    //   .left {
    //     span:first-child {
    //       color: #6e491fe6;
    //     }
    //     span:last-child {
    //       color: #6e491f99;
    //     }
    //   }
    //   .collect-btn {
    //     background: linear-gradient(276deg, #8b5300 14.56%, #ae864a 95.9%);
    //   }
    //   .stand-btn-disabled {
    //     background: linear-gradient(276deg, #eac896 14.56%, #eedcb9 95.9%);
    //   }
    // }
    .init-card-info-new {
      .card-content {
        position: relative;
        background-image: url('../../../../assets/img/mobile/geekBang/stand-card-bg.png');
        &::after { // 待领取
          position: absolute;
          content: '';
          right: 0;
          top: 0;
          width: 40px;
          height: 40px;
          background: url('../../../../assets/img/mobile/geekBang/wait-get.png') no-repeat 0 0;
          background-size: 100%;
          z-index: 1;
        }
        .init-disable-class1 {
          color: #00000099;
        }
        .init-disable-class2 {
          color: #00000066;
        }
      }
      .type {
      }
      .effect {
      }
      .collect-btn {
        background: linear-gradient(276deg, #8B5300 14.56%, #AE864A 95.9%);
      }
      .stand-btn-disabled {
        background: linear-gradient(276deg, #EAC896 14.56%, #EEDCB9 95.9%);
      }
      .stand-btn-gray {
        background: linear-gradient(276deg, #D4D4D4 14.56%, #D8D8D8 95.9%);
      }
      .card-content-1::after { // 待兑换
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-1.png');
      }
      .card-content-2::after { // 已兑换
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-2.png');
      }
      .card-content-3::after { // 未解锁
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-3.png');
      }
      .card-content-4::after { // 已失效
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-4.png');
      }
    }
    .stand-card-info-new-box {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      .card-content {
        position: relative;
        margin-top: 16px;
        width: 146px;
        height: 127px;
        border-radius: 10px;
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-new1.png');
        &::after { // 待领取
          position: absolute;
          content: '';
          right: 0;
          top: 0;
          width: 40px;
          height: 40px;
          background: url('../../../../assets/img/mobile/geekBang/wait-get.png') no-repeat 0 0;
          background-size: 100%;
          z-index: 1;
        }
        .stand-disable-class1 {
          color: #00000099;
        }
        .stand-disable-class2 {
          color: #00000066;
        }
      }
      .card-content-1::after { // 待兑换
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-1.png');
      }
      .card-content-2::after { // 已兑换
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-2.png');
      }
      .card-content-3::after { // 未解锁
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-3.png');
      }
      .card-content-4::after { // 已失效
        background-image: url('../../../../assets/img/mobile/geekBang/card-stand-4.png');
      }
    }
    .text-box {
      margin-top: 16px;
      color: #00000099;
      font-size: 14px;
      line-height: 22px;
      .text-title {
        color: #00000099;
        font-weight: 500;
      }
      .warm {
        color: #ed7b2f;
      }
      .text-attention {
        color: #ed7b2f;
        font-weight: 500;
      }
    }

    .good-course-list {
      padding: 20px;
      border-radius: 16px;
      background: #fff;
      box-shadow: 0 0 8px 0 #b5d0e30a;
      .top {
        .title {
          color: #000000e6;
          font-size: 16px;
          font-weight: 600;
          line-height: 24px;
        }
        .link {
          margin-left: 20px;
          color: #0052d9;
          font-size: 14px;
          line-height: 22px;
          text-decoration-line: underline;
        }
      }
      .list-content {
        margin-top: 12px;
        .item {
          display: flex;
          align-items: center;
          border-top: 0.5px solid #eee;
          padding: 12px 0;
          .cover {
            position: relative;
            margin-right: 12px;
            border-radius: 3px;
          }
          .cover-img {
            width: 129px;
            height: 86px;
          }
          .time {
            position: absolute;
            right: 8px;
            bottom: 8px;
            padding: 2px 12px;
            height: 20px;
            line-height: 20px;
            color: #ffffff;
            font-size: 12px;
            border-radius: 12px;
            background: #00000066;
          }
          .text-main {
            .course-type {
              margin-right: 5px;
              padding: 3px 4px;
              color: #0052d9;
              font-size: 12px;
              line-height: 12px;
              border-radius: 2px;
              background: #ebeffc;
            }
            .title {
              color: #000000e6;
              font-size: 14px;
              line-height: 22px;
            }
            .desc {
              margin-top: 2px;
              height: 40px;
              color: #00000099;
              font-size: 12px;
              line-height: 20px;
            }
          }
        }
      }
      &>.link {
        text-align: center;
        span {
          cursor: pointer;
          color: #0052d9;
          font-size: 14px;
          line-height: 22px;
          text-decoration-line: underline;
        }
      }
    }
  }
  .one-line {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .two-line {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2; /*显示两行*/
  }
}
.card-detail-pop {
  //   max-height: 396px;
  padding: 16px 0;
  .title {
    color: #000000e6;
    text-align: center;
    font-size: 18px;
    font-weight: 600;
    line-height: 26px;
  }
  .table {
    margin-top: 16px;
    .table-title {
      display: flex;
      height: 38px;
      line-height: 38px;
      color: #00000099;
      font-size: 12px;
      font-weight: 500;
      background-color: #f5f5f5;
      & > div {
        padding-left: 16px;
      }
    }
    .item-content {
      max-height: 300px;
      overflow-y: auto;
    }
    .item {
      display: flex;
      align-items: center;
      color: #000000e6;
      padding: 16px 0;
      // height: 60px;
      // line-height: 60px;
      box-shadow: 0 -1px 0 0 #eee inset;
      & > div {
        font-size: 10px;
        padding-left: 14px;
        &:nth-child(n+3) {
          padding-left: 16px;
        }
      }
    }
    // .no {
    //   width: 56px;
    // }
    .time {
      width: 99px;
    }
    .type{
      width: 66px;
    }
    .status {
      width: 66px;
    }
    .status-text {
      line-height: 16px;
      text-align: center;
      border-radius: 4px;
      padding: 1px 8px;
      font-size: 10px;
    }
    .status-no-effict {
      color: #ed7b2f;
      border: 1px solid var(---Warning5-Normal, #ed7b2f);
      background: var(---Warning1-Light, #fef3e6);
    }
    .status-waite-use {
      color: #0052d9;
      border: 1px solid var(---Brand8-Normal, #0052d9);
      background: var(---Brand1-Light, #ecf2fe);
    }
    .status-oready-used {
      color: #00a870;
      border: 1px solid var(---Success5-Normal, #00a870);
      background: var(---Success1-Light, #e8f8f2);
    }
    .desc {
      padding: 0 16px;
      flex: 1;
      text-align: left !important;
      // width: 235px;
      // text-overflow: ellipsis;
      // overflow: hidden;
      // white-space: nowrap;
    }
    // .course-link {
    //   white-space: nowrap;
    //   cursor: pointer;
    //   color: #0052d9;
    //   padding-right: 16px;
    // }
  }
  .foot-page {
    margin-top: 20px;
    padding: 0 16px;
    .prev-btn {
      color: #00000099;
      font-size: 12px;
      line-height: 20px;
    }
    .next-btn {
      color: #0052d9;
      font-size: 12px;
      line-height: 20px;
    }
    .page-desc {
      color: #000000e6;
      font-size: 12px;
      line-height: 20px;
      padding: 6px 16px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      .current {
        color: #0052d9;
      }
    }
    /deep/.van-pagination__prev {
      height: 32px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      background: var(---White, #fff);
    }
    /deep/.van-pagination__page-desc {
      height: 32px;
    }
    /deep/.van-pagination__next {
      height: 32px;
      border-radius: 6px;
      border: 0.5px solid var(---Gray3-, #e7e7e7);
      background: #f3f7ff;
    }
  }
  /deep/.van-popup__close-icon--top-right {
    color: #000000;
    top: 23px;
    right: 22px;
  }
  /deep/.van-popup__close-icon {
    font-size: 12px;
    font-weight: 700;
  }
}
</style>
