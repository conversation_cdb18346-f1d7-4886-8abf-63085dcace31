
<template>
  <div class="doc-detail">
    <div class="doc-box">
      <!-- 文档 -->
      <div class="main" v-if="!isFormMooc">
        <div class="title overflow-l2">
          <span class="tag">文档</span>{{ courseData.file_show_name }}
        </div>
        <div class="user-date">
          <span class="user">{{ courseData.creator_name }}</span>
          <span class="date">{{ courseData.created_at }}</span>
        </div>
      </div>

      <!-- 文档内容 -->
      <div class="doc-content" :class="{'full-screen': isFullScreen}">
        <div class="info-right">
          <span v-show="!isFullScreen" class="fullscreen-btn"  @click="toggle"></span>
          <span v-show="isFullScreen" class="offscreen-btn" @click="toggle"></span>
        </div>
        <Scorm 
          class="scorm-box"
          :courseData="courseData"
          />
      </div>
      <!-- 简介 -->
      <div class="desc">
        <div class="desc-tab">
          <span class="tab-text">简介</span>
        </div>
        <div v-if="courseData.file_desc" class="des-content" v-html="courseData.file_desc"></div>
        <div class="des-content" v-else>暂无简介</div>
      </div>

    </div>
  </div>
</template>
<script>
import { Scorm } from '@/components/index'
import MoocJs from 'sdc-moocjs-integrator'
export default {
  components: {
    Scorm
  },
  props: {
    courseData: {
      type: Object
    }
  },
  data() {
    return {
      isFullScreen: false
    }
  },
  computed: {
    isFormMooc() {
      return this.$route.query.from === 'mooc' || this.$route.query.from === 'spoc'
    }
  },
  methods: {
    toggle() {
      this.isFullScreen = !this.isFullScreen
    }
  },
  mounted() {
    if (this.isFormMooc) {
      MoocJs.play()
    }
  }
}
</script>
<style lang='less' scoped>
.doc-detail {
  .doc-box{
    height: 100%;
  }
  .main {
    padding: 12px 16px 8px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    .title {
      line-height: 24px;
      color: #000000ff;
      font-size: 16px;
      font-weight: 600;
      .tag {
        margin-right: 12px;
        display: inline-block;
        width: 42px;
        height: 20px;
        line-height: 20px;
        border-radius: 2px;
        border: 1px solid #ff8b6cff;
        background: #ff8b6c33;
        color: #ff8b6cff;
        font-size: 14px;
        font-weight: 600;
        text-align: center;
      }
    }
    .user-date {
      margin-top: 8px;
      height: 20px;
      line-height: 20px;
      color: #00000099;
      font-size: 12px;
      .user {
        float: left;
      }
      .date {
        float: right;
      }
    }
    
  }
  // 文字超出两行省略号
  .overflow-l2 {
    text-overflow: -o-ellipsis-lastline;
    overflow: hidden; //溢出内容隐藏
    text-overflow: ellipsis; //文本溢出部分用省略号表示
    display: -webkit-box; //特别显示模式
    -webkit-line-clamp: 2; //行数
    line-clamp: 2;
    -webkit-box-orient: vertical; //盒子中内容竖直排列
  }
  .doc-content{
    padding: 16px;
    .scorm-box {
      height: 460px;
      border: none;
    }
    .info-right{
      text-align: right;
    }
    .fullscreen-btn {
      display: inline-block;
      width: 20px;
      height: 20px;
      background: url("~@/assets/img/mobile/full-screen.png") no-repeat center center/cover;
    }
  }
  .desc{
    height: 38px;
    line-height: 38px;
    .desc-tab{
      border-bottom: 1px solid #eee;
      padding: 0 16px;
    }
    .tab-text{
      color: #0052d9;
      font-size: 14px;
      font-weight: 600;
      display: inline-block;
      height: 38px;
      line-height: 38px;
      border-bottom: 2px solid #0052d9;
    }
    .des-content{
      padding: 16px;
      font-size: 12px;
       color: #00000066;
    }
  }
}
.full-screen{
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  padding: 16px 24px;
  .scorm-box{
    height: 100%!important;
  }
  .offscreen-btn{
    display: inline-block;
    background: url("~@/assets/img/mobile/off-screen.png") no-repeat center center/cover;
    width: 20px;
    height: 20px;
  }
}
</style>
