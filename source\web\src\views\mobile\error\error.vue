
<template>
  <div class="mobile-error">
    <div class="content">
      <img class="img" v-if="href" src="@/assets/img/mobile/mobile.png" />
      <img class="img" v-else src="@/assets/img/mobile/empty-note.png" />
      <div class="line">{{ info1 }}</div>
      <div class="line">{{ info2 }}</div>
      <div class="line" v-if="href">{{ $langue('Mooc_TaskDetail_CopyLinkToPC', { defaultText: '请点击复制链接，去PC端访问' }) }}</div>
      <div class="href line" v-if="href">
        <span class="text">{{ href }}</span>
      </div>
      <van-button v-if="href" type="info" color="#0052D9" @click="onCopy">{{ $langue('Mooc_TaskDetail_ThirdParty_CopyLink', { defaultText: '复制链接' }) }}</van-button>
      <div class="back" @click="onBack" v-if="from === 'mooc'">{{ $langue('Mooc_TaskDetail_BackProjectHomepage', { defaultText: '返回培养项目首页' }) }}</div>
      <div class="back" @click="onBack" v-else-if="from === 'spoc'">{{ $langue('Mooc_TaskDetail_BackHomepage', { defaultText: '返回首页' }) }}</div>
    </div>
  </div>
</template>
<script>
import { Toast } from 'vant'

export default {
  data() {
    return {
      href: '',
      moocCourseId: '',
      from: '',
      classId: '',
      info1: '',
      info2: ''
    }
  },
  methods: {
    setErrorType (errorType) {
      switch (errorType) {
        // NOT_TENCENT_EMPLOYEE
        case 1:
          this.info1 = '仅针对腾讯员工开放哦'
          this.info2 = '如果您是腾讯员工，请确保微信号填写正确'
          break
        // NOT_SUPPORT_MOBILE
        case 2:
          this.info1 = this.$langue('Mooc_TaskDetail_ContentNotSupportedPC1', { defaultText: '很抱歉，当前内容暂不支持移动端访问' })
          break
        // CONTENT_DO_NOT_EXIST
        case 3:
          this.info1 = this.$langue('Mooc_TaskDetail_ContentNotExist', { defaultText: '内容不存在或者已删除' })
          break
        // PAGE_DO_NOT_EXIST
        case 4:
          this.info1 = this.$langue('Mooc_TaskDetail_PageNotExist', { defaultText: '页面不存在' })
          break
        case 5:
          this.info1 = this.$langue('Mooc_TaskDetail_PermissionDenied', { defaultText: '无权限' })
          this.info2 = this.$langue('Mooc_TaskDetail_CoursePermissionDenied', { defaultText: '暂无此课程的访问权限，请联系项目管理员进行调整' })
          break
        case 6:
          this.info1 = this.$langue('Mooc_TaskDetail_PermissionDenied', { defaultText: '无权限' })
          this.info2 = this.$route.params.message || '当前页面无权限访问'
          break        
        default:
          break
      }
    },
    // 复制链接
    onCopy() {
      let newInput = document.createElement('input')
      newInput.value = this.href
      document.body.appendChild(newInput)
      newInput.select()
      document.execCommand('Copy')
      newInput.remove()
      Toast(this.$langue('Mooc_Common_Alert_CopySucessed', { defaultText: '复制成功' }))
    },
    onBack() {
      if (this.from === 'mooc') {
        window.wx.miniProgram.reLaunch({
          url: `/pages/mooc/projectDetails/index?mooc_course_id=${this.moocCourseId}`
        })
      } else if (this.from === 'spoc') {
        window.wx.miniProgram.reLaunch({
          url: `/pages/webview/spoc/evaluateList?classId=${this.classId}`
        })
      }
    }
  },
  mounted() {
    let { type, href = '', mooc_course_id = '', from = '', class_id = '' } = this.$route.query
    this.setErrorType(parseInt(type))
    this.href = decodeURIComponent(href)
    this.moocCourseId = mooc_course_id || ''
    this.from = from
    this.classId = class_id
  }
}
</script>

<style lang='less' scoped>
.mobile-error {
  height: 100%;
  background-color: #fff;
  padding-top: 100px;
  .content {
    color: #000000e6;
    font-size: 14px;
    text-align: center;

    .img {
      margin-bottom: 16px;
      width: 160px;
      height: 160px;
    }

    .line {
      line-height: 22px;
    }

    .href {
      margin: 16px 0 25px;
      color: #00000099;
      font-size: 12px;
      height: 20px;
      line-height: 20px;

      .text {
        display: inline-block;
        width: 250px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap; // 默认不换行；
      }
    }

    .van-button {
      height: 36px;
      border-radius: 4px;
    }
  }
  .back {
    margin-top: 20px;
    color: #0052d9ff;
    font-weight: 600;
    line-height: 22px;
  }
}
</style>
