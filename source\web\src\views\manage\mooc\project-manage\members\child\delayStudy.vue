<template>
  <el-dialog 
  class="delay-dialog" 
  :title="title" 
  width="450px" 
  :visible.sync="visible" 
  :before-close="closeDialog"
  >
    <div class="body">
      <div>
        <p class="item-tips">{{ showTimeTitle }}</p>
        <p class="item-tips">{{showTime}}</p>
        <p class="item-tips">{{ studentInfo }}</p>
      </div>
      <el-date-picker
        v-model="timer"
        type="datetime"
        placeholder="选择日期时间"
        value-format="yyyy-MM-dd HH:mm:ss"
        >
      </el-date-picker>
      <CustomTips
        v-if="multipleList?.length > 1"
        class="tips-box"
        lineHeight="30px" 
        title="仅对“已逾期”状态的学员生效" 
        IconName="el-icon-warning" 
        backgroundColor="#fdf6ec" 
        color="#FF7548"
        >
      </CustomTips>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="closeDialog" size="small">取 消</el-button>
      <el-button type="primary" @click="confirm" size="small">确定延期</el-button>
    </div>
  </el-dialog>
</template>
<script>
import CustomTips from '@/components/tips.vue'
import { batchDelayStudent } from '@/config/mooc.api.conf.js'
import { mapState } from 'vuex'
export default {
  components: {
    CustomTips
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      timer: '',
      multipleList: []
    }
  },
  computed: {
    ...mapState(['projectManageInfo']),
    title() {
      return this.multipleList?.length > 1 ? '批量延期学习' : '延期学习'
    },
    showTime() {
      return this.projectManageInfo.course_period_type === 3 ? '不限定项目学习时间' :
      this.projectManageInfo?.period_day ? this.projectManageInfo.period_day : `${this.projectManageInfo.start_time} 至 ${this.projectManageInfo.end_time}`
    },
    showTimeTitle() {
      return this.projectManageInfo?.period_day ? '项目学习周期' : '项目学习起止时间'
    },
    studentInfo() {
      return this.multipleList?.length === 1 ? `将学员${this.multipleList[0].staff_name}的学习结束时间延期至` : `将选中的${this.multipleList.length}位学员学习结束时间延期至`
    }
  },
  methods: {
    // 初始化数据
    initData(data) {
      this.multipleList = data
    },
    confirm() {
      const { mooc_course_id } = this.$route.query
      const staffId = this.multipleList?.length > 1 ? this.multipleList.map((e) => e.staff_id) : [this.multipleList[0].staff_id]
      batchDelayStudent({
        mooc_course_id,
        staff_ids: staffId,
        end_time: this.timer
      }).then((res) => {
        const msg = this.multipleList?.length > 1 ? '批量延期成功' : '延期成功'
        this.$message.success(msg)
        this.closeDialog()
        this.$emit('onSearch')
      })
    },
    closeDialog() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang='less'>
.delay-dialog {
  .el-input {
    .el-input__inner {
      padding-left: 30px;
      padding-right: 30px;
    }
  }
}
</style>
<style lang="less" scoped>
  .item-tips {
    margin-bottom: 20px;
  }
  .tips-box {
    margin-top: 20px;
  }
</style>
