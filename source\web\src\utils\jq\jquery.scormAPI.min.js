/* eslint-disable*/
!function($){function _addTimeSpan(a,b){var c=a.toString().split(":"),d=b.toString().split(":");if(3!=c.length||3!=d.length)return"0:0:0";var e=Number(c[2])+Number(d[2]);e=e.toFixed(2);var f=0;e>=60&&(e-=60,f=1);var g=Number(c[1])+Number(d[1])+f;g>=60?(g-=60,f=1):f=0;var h=Number(c[0])+Number(d[0])+f;return h.toString()+":"+g.toString()+":"+e.toString()}function _checkTime(a){if(a.indexOf(":")==-1)return!1;var b=a.split(":");if(b.length<3)return!1;if(isNaN(b[0])||isNaN(b[1])||isNaN(b[2]))return!1;if(parseInt(b[0])<0||parseInt(b[0])>23)return!1;if(parseInt(b[1])<0||parseInt(b[1])>59)return!1;if(b[2].indexOf(".")!=-1){var c=b[2].split(".");if(2!=c.length)return!1;if(isNaN(c[0])||isNaN(c[1]))return!1;if(2!=c[0].length)return!1;if(parseInt(c[0])>59)return!1;if(c[1].length>2)return!1}else{if(2!=b[2].length)return!1;if(parseInt(b[2])>59)return!1}return!0}function _dealWithSetCore(a,b){var c=a.toString().split(".");return c.length<3?(lastError="201","false"):"score"==c[2].toString()?c.length<4?(lastError="201","false"):"_children"==c[3].toString()&&4==c.length?(lastError="402","false"):"raw"==c[3].toString()&&4==c.length?isNaN(b)||b>100||b<0?(lastError="405","false"):(cmiModel.cmi.core.score.raw=b,lastError="0","true"):"min"==c[3].toString()&&4==c.length?isNaN(b)||b>100||b<0?(lastError="405","false"):(cmiModel.cmi.core.score.min=b,lastError="0","true"):"max"==c[3].toString()&&4==c.length?isNaN(b)||b>100||b<0?(lastError="405","false"):(cmiModel.cmi.core.score.max=b,lastError="0","true"):(lastError="201","false"):"_children"==c[2].toString()&&3==c.length?(lastError="402","false"):"student_id"==c[2].toString()&&3==c.length?(lastError="403","false"):"student_name"==c[2].toString()&&3==c.length?(lastError="403","false"):"lesson_location"==c[2].toString()&&3==c.length?b.toString().length>CMISTRING255LENGTH?(lastError="405","false"):(cmiModel.cmi.core.lesson_location=b,lastError="0","true"):"credit"==c[2].toString()&&3==c.length?(lastError="403","false"):"lesson_status"==c[2].toString()&&3==c.length?LESSONSTATUS.indexOf(b.toString())==-1?(lastError="405","false"):(cmiModel.cmi.core.lesson_status=b,lastError="0","true"):"entry"==c[2].toString()&&3==c.length?(lastError="403","false"):"total_time"==c[2].toString()&&3==c.length?(lastError="403","false"):"lesson_mode"==c[2].toString()&&3==c.length?(lastError="403","false"):"exit"==c[2].toString()&&3==c.length?EXIT.indexOf(b)!=-1?(cmiModel.cmi.core.exit=b,"suspend"==b?cmiModel.cmi.core.entry="resume":cmiModel.cmi.core.entry="",lastError="0","true"):(lastError="405","false"):"session_time"==c[2].toString()&&3==c.length?_checkTime(b)?(cmiModel.cmi.core.session_time=b,lastError="0","true"):(lastError="405","false"):(lastError="201","false")}function _dealWithGetCore(a){var b=a.toString().split(".");return b.length<3?(lastError="201",""):"_children"==b[2]&&3==b.length?(lastError="0",cmiModel.cmi.core._children):"student_id"==b[2]&&3==b.length?(lastError="0",cmiModel.cmi.core.student_id):"student_name"==b[2]&&3==b.length?(lastError="0",cmiModel.cmi.core.student_name):"lesson_location"==b[2]&&3==b.length?(lastError="0",cmiModel.cmi.core.lesson_location):"credit"==b[2]&&3==b.length?(lastError="0",cmiModel.cmi.core.credit):"lesson_status"==b[2]&&3==b.length?(lastError="0",cmiModel.cmi.core.lesson_status):"entry"==b[2]&&3==b.length?(lastError="0",cmiModel.cmi.core.entry):"score"==b[2]?b.length<4?(lastError="201",""):"raw"==b[3]&&4==b.length?(lastError="0",cmiModel.cmi.core.score.raw):"_children"==b[3]&&4==b.length?(lastError="0",cmiModel.cmi.core.score._children):"min"==b[3]&&4==b.length?(lastError="0",cmiModel.cmi.core.score.min):"max"==b[3]&&4==b.length?(lastError="0",cmiModel.cmi.core.score.max):(lastError="201",""):"total_time"==b[2]&&3==b.length?(lastError="0",cmiModel.cmi.core.total_time):"lesson_mode"==b[2]&&3==b.length?(lastError="0",cmiModel.cmi.core.lesson_mode):"exit"==b[2]&&3==b.length?(lastError="404",""):"session_time"==b[2]&&3==b.length?(lastError="404",""):(lastError="201","")}function _dealWithSetObjectives(a,b){var c=a.toString().split(".");if(c.length<3)return lastError="201","false";if("_children"==c[2].toString()&&3==c.length)return lastError="402","false";if("_count"==c[2].toString()&&3==c.length)return lastError="402","false";if(isNaN(c[2]))return lastError="201","false";if("id"==c[3]&&4==c.length){if(b.toString().indexOf(SPACE)!=-1)return lastError="405","false";var d=c[2],e=Number(cmiModel.cmi.objectives._count);if(void 0==cmiModel.cmi.objectives.objectiveModelArray[d]){var f={id:"",score:{_children:"raw,min,max",raw:"",min:"",max:""},status:""};f.id=b,cmiModel.cmi.objectives.objectiveModelArray[d]=f,e+=1,cmiModel.cmi.objectives._count=e.toString()}else cmiModel.cmi.objectives.objectiveModelArray[d].id=b;return lastError="0","true"}if("score"==c[3]&&5==c.length){if(c.length<5)return lastError="201","false";if("_children"==c[4]&&5==c.length)return lastError="402","false";if("raw"==c[4]){if(isNaN(b)||b>100||b<0)return lastError="405","false";var d=c[2],e=Number(cmiModel.cmi.objectives._count);if(void 0==cmiModel.cmi.objectives.objectiveModelArray[d]){var f={id:"",score:{_children:"raw,min,max",raw:"",min:"",max:""},status:""};f.score.raw=b,cmiModel.cmi.objectives.objectiveModelArray[d]=f,e+=1,cmiModel.cmi.objectives._count=e.toString()}else cmiModel.cmi.objectives.objectiveModelArray[d].score.raw=b;return lastError="0","true"}if("min"==c[4]){if(isNaN(b)||b>100||b<0)return lastError="405","false";var d=c[2],e=Number(cmiModel.cmi.objectives._count);if(void 0==cmiModel.cmi.objectives.objectiveModelArray[d]){var f={id:"",score:{_children:"raw,min,max",raw:"",min:"",max:""},status:""};f.score.min=b,cmiModel.cmi.objectives.objectiveModelArray[d]=f,e+=1,cmiModel.cmi.objectives._count=e.toString()}else cmiModel.cmi.objectives.objectiveModelArray[d].score.min=b;return lastError="0","true"}if("max"==c[4]){if(isNaN(b)||b>100||b<0)return lastError="405","false";var d=c[2],e=Number(cmiModel.cmi.objectives._count);if(void 0==cmiModel.cmi.objectives.objectiveModelArray[d]){var f={id:"",score:{_children:"raw,min,max",raw:"",min:"",max:""},status:""};f.score.max=b,cmiModel.cmi.objectives.objectiveModelArray[d]=f,e+=1,cmiModel.cmi.objectives._count=e.toString()}else cmiModel.cmi.objectives.objectiveModelArray[d].score.max=b;return lastError="0","true"}return lastError="201","false"}if("status"==c[3]&&4==c.length){if(LESSONSTATUS.indexOf(b.toString())==-1)return lastError="405","false";var d=c[2],e=Number(cmiModel.cmi.objectives._count);if(void 0==cmiModel.cmi.objectives.objectiveModelArray[d]){var f={id:"",score:{_children:"raw,min,max",raw:"",min:"",max:""},status:""};f.status=b,cmiModel.cmi.objectives.objectiveModelArray[d]=f,e+=1,cmiModel.cmi.objectives._count=e.toString()}else cmiModel.cmi.objectives.objectiveModelArray[d].status=b;return lastError="0","true"}return lastError="201","false"}function _dealWithGetObjectives(a){var b=a.toString().split(".");if(b.length<3)return lastError="201","";if("_children"==b[2]&&3==b.length)return lastError="0",cmiModel.cmi.objectives._children;if("_count"==b[2]&&3==b.length)return lastError="0",cmiModel.cmi.objectives._count;if(isNaN(b[2]))return lastError="201","";var c=Number(b[2]);return void 0==cmiModel.cmi.objectives.objectiveModelArray[c]?(lastError="101",""):"id"==b[3]&&4==b.length?(lastError="0",cmiModel.cmi.objectives.objectiveModelArray[c].id):"status"==b[3]&&4==b.length?(lastError="0",cmiModel.cmi.objectives.objectiveModelArray[c].status):"score"==b[3]&&5==b.length?b.length<5?(lastError="201",""):"_children"==b[4]&&5==b.length?(lastError="0",cmiModel.cmi.objectives.objectiveModelArray[c].score._children):"raw"==b[4]?(lastError="0",cmiModel.cmi.objectives.objectiveModelArray[c].score.raw):"min"==b[4]?(lastError="0",cmiModel.cmi.objectives.objectiveModelArray[c].score.min):"max"==b[4]?(lastError="0",cmiModel.cmi.objectives.objectiveModelArray[c].score.max):(lastError="201",""):(lastError="201","")}function _setIndexANewModel(a,b){cmiModel.cmi.interactions.interactionModelArray[a]=b;var c=Number(cmiModel.cmi.interactions._count);c+=1,cmiModel.cmi.interactions._count=c.toString()}function _isStudentResponseValid(a,b){if(""==b)return!0;var c=cmiModel.cmi.interactions.interactionModelArray[a].type;if("true-false"==c){var d=b.toString().charAt(0);if("0"==d||"1"==d||"t"==d||"f"==d)return!0}else if("choice"==c){if(INTERACTIONCHOICE.test(b))return!0}else if("fill-in"==c){if(" "!=b.toString().charAt(0))return!0}else if("matching"==c){if(INTERACTIONMATCHING.test(b))return!0}else if("performance"==c){if(b.toString().length<=CMISTRING255LENGTH)return!0}else{if("sequencing"==c)return!0;if("likert"==c)return!0;if("numeric"!=c)return!1;if(!isNaN(b))return!0}}function _dealWithSetInteractions(a,b){var c=a.toString().split(".");if(c.length<3)return lastError="201","false";if("_children"==c[2]&&3==c.length)return lastError="402","false";if("_count"==c[2]&&3==c.length)return lastError="402","false";if(isNaN(c[2]))return lastError="201","false";var d=Number(c[2]);if(void 0==cmiModel.cmi.interactions.interactionModelArray[d]){var e={id:"",objectives:{_count:"0",idModelArray:[]},time:"",type:"",correct_responses:{_count:"0",patternArray:[]},weighting:"",student_response:"",result:"",latency:""};if("id"==c[3]&&4==c.length)return b.toString().indexOf(SPACE)!=-1?(lastError="405","false"):(lastError="0",e.id=b,_setIndexANewModel(d,e),"true");if("time"==c[3]&&4==c.length)return _checkTime(b)?(lastError="0",e.time=b,_setIndexANewModel(d,e),"true"):(lastError="405","false");if("type"==c[3]&&4==c.length)return INTERACTIONTYPE.indexOf(b)!=-1?(lastError="0",e.type=b,_setIndexANewModel(d,e),"true"):(lastError="405","false");if("weighting"==c[3]&&4==c.length)return isNaN(b)?(lastError="405","false"):(lastError="0",e.weighting=b,_setIndexANewModel(d,e),"true");if("student_response"==c[3]&&4==c.length)return _isStudentResponseValid(d,b)?(lastError="0",e.student_response=b,_setIndexANewModel(d,e),"true"):(lastError="405","false");if("result"==c[3]&&4==c.length)return INTERACTIONRESULT.indexOf(b)==-1&&isNaN(b)?(lastError="405","false"):(lastError="0",e.result=b,_setIndexANewModel(d,e),"true");if("latency"==c[3]&&4==c.length)return _checkTime(b)?(lastError="0",e.latency=b,_setIndexANewModel(d,e),"true"):(lastError="405","false");if("objectives"==c[3]){if("_count"==c[4]&&5==c.length)return lastError="402","false";if(isNaN(c[4])||"id"!=c[5]||6!=c.length)return lastError="201","false";var f=Number(c[4]);if(b.toString().indexOf(SPACE)==-1){var g={id:b};return e.objectives.idModelArray[f]=g,_setIndexANewModel(d,e),lastError="0","true"}return lastError="405","false"}if("correct_responses"==c[3]){if("_count"==c[4]&&5==c.length)return lastError="402","false";if(isNaN(c[4])||"pattern"!=c[5]||6!=c.length)return lastError="201","false";var f=Number(c[4]);if(_isStudentResponseValid(d,b)){var h={pattern:b};return e.correct_responses.patternArray[f]=g,_setIndexANewModel(d,e),lastError="0","true"}return lastError="405","false"}return lastError="201","false"}if("id"==c[3]&&4==c.length)return b.toString().indexOf(SPACE)!=-1?(lastError="405","false"):(lastError="0",cmiModel.cmi.interactions.interactionModelArray[d].id=b,"true");if("time"==c[3]&&4==c.length)return _checkTime(b)?(lastError="0",cmiModel.cmi.interactions.interactionModelArray[d].time=b,"true"):(lastError="405","false");if("type"==c[3]&&4==c.length)return INTERACTIONTYPE.indexOf(b)!=-1?(lastError="0",cmiModel.cmi.interactions.interactionModelArray[d].type=b,"true"):(lastError="405","false");if("weighting"==c[3]&&4==c.length)return isNaN(b)?(lastError="405","false"):(lastError="0",cmiModel.cmi.interactions.interactionModelArray[d].weighting=b,"true");if("student_response"==c[3]&&4==c.length)return _isStudentResponseValid(d,b)?(lastError="0",cmiModel.cmi.interactions.interactionModelArray[d].student_response=b,"true"):(lastError="405","false");if("result"==c[3]&&4==c.length)return INTERACTIONRESULT.indexOf(b)==-1&&isNaN(b)?(lastError="405","false"):(lastError="0",cmiModel.cmi.interactions.interactionModelArray[d].result=b,"true");if("latency"==c[3]&&4==c.length)return _checkTime(b)?(lastError="0",cmiModel.cmi.interactions.interactionModelArray[d].latency=b,"true"):(lastError="405","false");if("objectives"==c[3]){if("_count"==c[4]&&5==c.length)return lastError="402","false";if(isNaN(c[4])||"id"!=c[5]||6!=c.length)return lastError="201","false";var f=Number(c[4]);if(b.toString().indexOf(SPACE)==-1){if(void 0==cmiModel.cmi.interactions.interactionModelArray[d].objectives.idModelArray[f]){var g={id:b};cmiModel.cmi.interactions.interactionModelArray[d].objectives.idModelArray[f]=g;var i=Number(cmiModel.cmi.interactions.interactionModelArray[d].objectives._count);i+=1,cmiModel.cmi.interactions.interactionModelArray[d].objectives._count=i.toString()}else cmiModel.cmi.interactions.interactionModelArray[d].objectives.idModelArray[f].id=b;return lastError="0","true"}return lastError="405","false"}if("correct_responses"==c[3]){if("_count"==c[4]&&5==c.length)return lastError="402","false";if(isNaN(c[4])||"pattern"!=c[5]||6!=c.length)return lastError="201","false";if(_isStudentResponseValid(d,b)){if(void 0==cmiModel.cmi.interactions.interactionModelArray[d].correct_responses.patternArray[f]){var h={pattern:b};cmiModel.cmi.interactions.interactionModelArray[d].correct_responses.patternArray[f]=h;var i=Number(cmiModel.cmi.interactions.interactionModelArray[d].correct_responses._count);i+=1,cmiModel.cmi.interactions.interactionModelArray[d].correct_responses._count=i.toString()}else cmiModel.cmi.interactions.interactionModelArray[d].correct_responses.patternArray[f].pattern=b;return lastError="0","true"}return lastError="405","false"}return lastError="201","false"}function _dealWithGetInteractions(a){var b=a.toString().split(".");if(b.length<3)return lastError="201","";if("_children"==b[2]&&3==b.length)return lastError="0",cmiModel.cmi.interactions._children;if("_count"==b[2]&&3==b.length)return lastError="0",cmiModel.cmi.interactions._count;if(isNaN(b[2]))return lastError="201","";var c=Number(b[2]);return void 0==cmiModel.cmi.interactions.interactionModelArray[c]?(lastError="101",""):"id"==b[3]&&4==b.length?(lastError="404",""):"time"==b[3]&&4==b.length?(lastError="404",""):"type"==b[3]&&4==b.length?(lastError="404",""):"weighting"==b[3]&&4==b.length?(lastError="404",""):"student_response"==b[3]&&4==b.length?(lastError="404",""):"result"==b[3]&&4==b.length?(lastError="404",""):"latency"==b[3]&&4==b.length?(lastError="404",""):"objectives"==b[3]?"_count"==b[4]&&5==b.length?(lastError="0",cmiModel.cmi.interactions.interactionModelArray[c].objectives._count):isNaN(b[4])||"id"!=b[5]||6!=b.length?(lastError="201",""):(lastError="404",""):"correct_responses"==b[3]?"_count"==b[4]&&5==b.length?(lastError="0",cmiModel.cmi.interactions.interactionModelArray[c].correct_responses._count):isNaN(b[4])||"pattern"!=b[5]||6!=b.length?(lastError="201",""):(lastError="404",""):(lastError="201","")}function _getErrorString(a){switch(a){case"0":return"No error";case"101":return"General exception";case"201":return"Invalid argument error";case"202":return"Element cannot have children";case"203":return"Element not an array - Cannot have count";case"301":return"Not initialized";case"401":return"Not implemented error";case"402":return"Invalid set value, element is a keyword";case"403":return"Element is read only";case"404":return"Element is write only";case"405":return"Incorrect Data Type";default:return""}}function _getDiagnostic(a){switch(""==a&&(a=lastError),a){case"0":return"No error. No errors were encountered. Successful API call.";case"101":return"General exception. An unexpected error was encountered.";case"201":return"Invalid argument error. A call was made to a DataModel element that does not exist.";case"202":return"Element cannot have children. A call was made to an Element that does not support _children";case"203":return"Element is not an array.  Cannot have count. A call was made to an Element that does not support _count.";case"301":return"Not initialized. The SCO has not yet been initialized.  It needs to call LMSInitialize() first.";case"401":return"Not implemented error.  A call was made to a DataModel element that is not supported.";case"402":return"Invalid set value, element is a keyword.  Keyword values cannot be changed";case"403":return"Element is read only.  A call was made to set the value of a read-only element.";case"404":return"Element is write only.  A call was made to get the value of a write-only element.";case"405":return"Incorrect Data Type.  The syntax of a call to change an element was incorrect.";default:return""}}function _setValue(a,b){if(b=b.toString(),!isInitialize)return lastError="301","false";var c=a.toString().split(".");return c.length<2||c.length>6||"cmi"!=c[0].toString()?(lastError="201","false"):"launch_data"==c[1].toString()&&2==c.length?(lastError="403","false"):"comments_from_lms"==c[1].toString()&&2==c.length?(lastError="403","false"):"suspend_data"==c[1].toString()&&2==c.length?b.toString().length>CMISTRING4096LENGTH?(lastError="405","false"):(cmiModel.cmi.suspend_data=b.toString(),lastError="0","true"):"comments"==c[1].toString()&&2==c.length?b.toString().length>CMISTRING4096LENGTH?(lastError="405","false"):(cmiModel.cmi.comments=b.toString(),lastError="0","true"):"core"==c[1].toString()?_dealWithSetCore(a,b):"objectives"==c[1].toString()?_dealWithSetObjectives(a,b):"student_data"==c[1].toString()?c.length<3?(lastError="201","false"):"_children"==c[2].toString()&&3==c.length?(lastError="402","false"):"mastery_score"!=c[2].toString()&&"max_time_allowed"!=c[2].toString()&&"time_limit_action"!=c[2].toString()||3!=c.length?(lastError="201","false"):(lastError="403","false"):"student_preference"==c[1].toString()?(lastError="401","false"):"interactions"==c[1].toString()?_dealWithSetInteractions(a,b):(lastError="201","false")}function _getValue(a){if(!isInitialize)return lastError="301","";var b=a.toString().split(".");return b.length<2||b.length>6||"cmi"!=b[0].toString()?(lastError="201",""):"cmi._version"==a&&2==b.length?(lastError="0",DATAMODELVERSION):"suspend_data"==b[1]&&2==b.length?(lastError="0",cmiModel.cmi.suspend_data):"launch_data"==b[1]&&2==b.length?(lastError="0",cmiModel.cmi.launch_data):"comments"==b[1]&&2==b.length?(lastError="0",cmiModel.cmi.comments):"comments_from_lms"==b[1]&&2==b.length?(lastError="0",cmiModel.cmi.comments_from_lms):"core"==b[1]?_dealWithGetCore(a):"objectives"==b[1]?_dealWithGetObjectives(a):"student_data"==b[1]?b.length>3?(lastError="201",""):"mastery_score"==b[2]&&3==b.length?(lastError="0",cmiModel.cmi.student_data.mastery_score):"max_time_allowed"==b[2]&&3==b.length?(lastError="0",cmiModel.cmi.student_data.max_time_allowed):"time_limit_action"==b[2]&&3==b.length?(lastError="0",cmiModel.cmi.student_data.time_limit_action):(lastError="201",""):"student_preference"==b[1]?(lastError="401",""):"interactions"==b[1]?_dealWithGetInteractions(a):(lastError="201","")}function _initialize(a,b){return isInitialize?(lastError="101","false"):""!=a?(lastError="201","false"):(lastError="0",isInitialize=!0,b())}function _finish(a,b){return isInitialize?""!=a?(lastError="201","false"):(cmiModel.cmi.core.total_time=_addTimeSpan(cmiModel.cmi.core.total_time,cmiModel.cmi.core.session_time),lastError="0",isInitialize=!1,b()):(lastError="301","false")}function _commit(a,b){return isInitialize?""!=a?(lastError="201","false"):(lastError="0",b()):(lastError="301","false")}var CMISTRING4096LENGTH=4096,CMISTRING255LENGTH=255,LESSONSTATUS="passed,completed,failed,incomplete,browsed,not attempted",EXIT="time-out,suspend,logout",SPACE=" ",DATAMODELVERSION="3.4",INTERACTIONTYPE="true-false,choice,fill-in,matching,performance,sequencing,likert,numeric",INTERACTIONCHOICE=/\w(,\w)*$/,INTERACTIONMATCHING=/\w.\w(,\w.\w)*$/,INTERACTIONRESULT="correct,wrong,unanticipated,neutral",idModel={id:""},patternModel={pattern:""},objectiveModel={id:"",score:{_children:"raw,min,max",raw:"",min:"",max:""},status:""},interactionModel={id:"",objectives:{_count:"0",idModelArray:[]},time:"",type:"",correct_responses:{_count:"0",patternArray:[]},weighting:"",student_response:"",result:"",latency:""},cmiModel={cmi:{suspend_data:"",launch_data:"",comments:"",comments_from_lms:"",core:{_children:"student_id,student_name,lesson_location,credit,lesson_status,entry,score,total_time,lesson_mode,exit,session_time",student_id:"",student_name:"",lesson_location:"",credit:"credit",lesson_status:"not attempted",entry:"ab-initio",score:{_children:"raw,min,max",raw:"",min:"",max:""},total_time:"0000:00:00.00",lesson_mode:"normal",exit:"",session_time:""},objectives:{_children:"id,score,status",_count:"0",objectiveModelArray:[]},student_data:{_children:"mastery_score, max_time_allowed, time_limit_action",mastery_score:"",max_time_allowed:"",time_limit_action:""},student_preference:{_children:"audio,language,speed,text",audio:"",language:"",speed:"",text:""},interactions:{_children:"id,objectives,time,type,correct_responses,weighting,student_response,result,latency",_count:"0",interactionModelArray:[]}}},lastError="0",isInitialize=!1;$.fn.scormAPI=function(a){var b={init:function(){return"true"},finish:function(){return"true"},commit:function(){return"true"}},c=jQuery.extend(!0,{},b,a);window.API={},API.LMSInitialize=function(a){return _initialize(a,c.init)},API.LMSFinish=function(a){return _finish(a,c.finish)},API.LMSCommit=function(a){return _commit(a,c.commit)},API.LMSGetValue=_getValue,API.LMSSetValue=_setValue,API.LMSGetLastError=function(){return lastError},API.LMSGetErrorString=_getErrorString,API.LMSGetDiagnostic=_getDiagnostic},$.fn.scormAPI.setScormModel=function(a){cmiModel=a},$.fn.scormAPI.getScormModel=function(){return cmiModel},$.fn.scormAPI.setScormModelString=function(a){try{cmiModel=JSON.parse(a)}catch(a){}},$.fn.scormAPI.getScormModelString=function(){try{return JSON.stringify(cmiModel)}catch(a){}},$.fn.scormAPI.customSetValue=function(element,data){eval("cmiModel."+element.toString()+'="'+data.toString()+'"')},$.fn.scormAPI.userErrorOccurs=function(){lastError="101"}}(jQuery);