<template>
  <common-error :code="errorInfo.code" class="detail-no-author-container">
    <div class="tips">{{ errorInfo.tips }}</div>
    <el-button type="text" @click="handleBack">返回培养项目首页</el-button>
    <div class="admin-title">联系管理员：</div>
    <div class="avator-content clearfix">
      <span 
      :class="[{'last-avator-box': index % 5 === 4 }, 'avator-box']" 
      v-for="(item, index) in errorInfo.adminList" 
      :key="item.admin_id"
      >
        <el-image lazy fit="fill" :src="item.url"  class="avator">
          <div class="image-slot" slot="placeholder">
            <i class="el-icon-loading"></i>
          </div>
          <div class="error-cover" slot="error">
            <img :src="cover" alt="" />
          </div>
        </el-image>
        <span class="avator-name">{{ item.admin_name }}</span>
      </span>
    </div>
  </common-error>
</template>
<script>
import commonError from './common-error.vue'

export default {
  name: 'error',
  components: {
    commonError
  },
  data() {
    return {
      cover: require('@/assets/mooc-img/user-avatar.png')
    }
  },
  mounted() {

  },
  computed: {
    errorInfo() {
      return JSON.parse(sessionStorage.getItem('detail-autor'))
    }
  },
  methods: {
    handleBack() {
      this.$router.replace({
        name: 'home'
      })
    }
  }
}
</script>
<style lang="less" scoped>
.detail-no-author-container {
  //清楚浮动
  .clearfix:after {
    content: "";
    display: block;
    height: 0;
    clear: both;
    visibility: hidden;
  }
  :deep(.wrapper) {
    flex-direction: column;
    .content {
      line-height: unset;
      width: 450px;
    }
    .tips {
      font-size: 18px;
      font-weight: 600;
      height: 25px;
      line-height: 25px;
      margin-top: 24px;
      margin-bottom: 24px;
    }
    img {
      width: 240px;
      height: 148px;
    }
    .admin-title {
      margin-top: 20px;
      margin-bottom: 20px;
    }
    .avator-content {
      height: 500px;
      overflow: auto;
      .last-avator-box{
        margin-right:unset
      }
      .avator-box {
        height: 32px;
        line-height: 32px;
        display: flex;
        align-items: center;
        color: #000000e6;
        font-size: 14px;
        font-weight: 400;
        width: 150px;
        float: left;
        .avator {
          img {
            width: 32px;
            height: 32px;
            border-radius: 80px;
            border: 2px solid #ffffffff;
          }
        }
      }
      .avator-name {
        overflow: hidden;
        display: inline-block;
        width: 120px;
        text-overflow: ellipsis;
        white-space: nowrap;
        margin-left: 8px;
      }
      .avator-box {
        margin-right: 36px;
        margin-bottom: 20px;
      }
    }
  }
}
</style>
