<template>
  <common-error>
    {{ msg ? msg : $t('Mooc_Common_Alert_HaveNoArthority') }}
  </common-error>
</template>

<script>
import commonError from './common-error.vue'

export default {
  name: 'no-permission',
  components: {
    commonError
  },
  data() {
    return {
      headClearTimer: null 
    }
  },
  computed: {
    msg() {
      return sessionStorage.getItem('401Msg')
    }
  },
  mounted() {
    this.headClearTimer = setInterval(() => {
      this.handleHeaderDom() 
    }, 50)
  },
  methods: {
    handleHeaderDom() {
      window.$qlCommonHeader && window.$qlCommonHeader.destroy()
      let chArr = document.body.getElementsByClassName('common-header')
      if (chArr?.length > 0) {
        for (let i = 0; i < chArr.length; i++) {
          if (chArr[i] !== null) chArr[i].parentNode.removeChild(chArr[i])
        }
        clearInterval(this.headClearTimer)
      }      
    }
  },
  beforeDestroy() {
    clearInterval(this.headClearTimer)
  }
}
</script>

<style lang="less" scoped>
</style>
