<template>
  <el-dialog 
    :visible.sync="dialogVisible" 
    title="更多设置" 
    custom-class="activity-info-more-setting-dialog" 
    :show-close="false" 
    :close-on-click-modal="false"
  >
    <el-form :model="form.course_statement" :rules="moreSettingRules" ref="moreSettingForm" label-width="120px">
      <div class="more-setting-container">
        <el-form-item label="运营标题">
          <el-input v-model="form.course_statement.operation_title" placeholder="请填写项目运营标题，可为空" style="max-width: 785px;"></el-input>
        </el-form-item>

        <el-form-item label="活动来源" prop="activity_source">
          <workSource ref="workSourceRef" :baseInfo="form" v-if="dialogVisible" />
        </el-form-item>

        <el-form-item label="人力成本" prop="human_cost">
          <el-input v-model="form.course_statement.human_cost" placeholder="请输入人力成本" style="width: 200px;margin-right: 12px;"></el-input>
          <span class="human-cost-unit">人天</span>
        </el-form-item>

        <el-form-item label="活动管理组织" prop="activity_manage_org">
          <sdc-unit-selector
            class="dep-selector"
            ref="deptSelectorRef"
            v-model="form.activity_manage_org"
            multiple
            placeholder="请选择活动管理组织"
            @change="changeActivityManageOrg($event)"
          />
          <div class="normal-tips" style="margin-top: 4px;">
            请选择负责项目运营/管理的组织，支持多选
          </div>
        </el-form-item>

        <el-form-item label="是否纳入必修">
          <el-radio-group v-model="form.course_statement.is_required">
            <el-radio :label="0">非必修</el-radio>
            <el-radio :label="1">必修</el-radio>
          </el-radio-group>
          <span class="normal-tips" style="margin-left: 28px;">
            请选择该培养项目是否针对某些人群必选
          </span>
        </el-form-item>

        <el-form-item label="认证等级" prop="activity_level" style="max-width: 785px;">
          <el-radio-group v-model="form.activity_level" @change="$refs.moreSettingForm && $refs.moreSettingForm.clearValidate(['operation_level'])">
            <el-radio v-for="item in levelList" :key="item.code" :label="item.code">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="是否加入推荐池">
          <div>
            <el-radio-group v-model="form.course_statement.join_recommend">
              <el-radio :label="0">否，不加入推荐池</el-radio>
              <el-radio :label="1">是，加入推荐池</el-radio>
            </el-radio-group>
            <span class="normal-tips" style="margin-left: 28px;">
              请选择是否加入千人千面推荐
            </span>
          </div>
          <orange-tips title="合规类、应知应会类、必修类课程等请勿选择加入推荐流。" style="margin-top: 16px;"></orange-tips>
        </el-form-item>

        <el-form-item label="运营分级" prop="operation_level" :rules="operationLevelRules">
          <el-radio-group v-model="form.course_statement.operation_level" @change="changeOperationType">
            <el-radio v-for="item in operation_type_options" :key="item.value" :value="item" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
          <div class="creation_source_sub_content">
            <div class="operation_level-sub-content">
              <span>分级项目</span>
              <el-select v-model="form.course_statement.operation_project_name" placeholder="请选择分级项目" style="width: 500px;margin-left: 8px;" class="operation_project_name_select">
                <el-option v-for="item2 in operation_project_name_options" :key="item2.item_id" :value="item2.item_name" >{{ item2.item_name }}</el-option>
              </el-select>
            </div>
          </div>
        </el-form-item>

        <el-form-item label="展示课程推荐">
          <el-checkbox v-model="form.is_show_recommend" style="font-weight: 500;">是</el-checkbox>
          <span class="normal-tips" style="margin-left: 16px;">
            （预计访问量高时，提前关闭该选项，提升页面加载速度）
          </span>
        </el-form-item>

        <el-form-item label="内容用户评分">
          <span>{{ form.course_statement.activity_score ? form.course_statement.activity_score : '-' }}</span>
          <span class="normal-tips" style="margin-left: 12px;">
            自动拉取用户评价分
          </span>
        </el-form-item>

        <el-form-item label="运营信息备注">
          <el-input
            resize="none"
            type="textarea"
            v-model="form.remark" 
            placeholder="请输入运营信息备注" 
            size="small"
            clearable
            class="remark-textarea"
            :autosize='{ minRows: 4.5, maxRows: 6}' 
          />
        </el-form-item>

        <el-form-item label="活动类型">
          <el-radio-group v-model="form.pdi_sub_level">
            <el-radio v-for="item in activityTypeList" :key="item.item_id" :label="item.item_value">{{ item.item_name }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">取 消</el-button>
      <el-button type="primary" @click="confirm">确 定</el-button>
    </span>
    <!-- <el-dialog :visible.sync="systemDialog" title="选择系统" width="40%" top="20px" :show-close="true" :close-on-click-modal="false" append-to-body>
      <el-table :data="systemList" style="width: 100%;height: 500px;overflow-y: auto;">
        <el-table-column prop="item_id" label="序号" width="120" align="center"></el-table-column>
        <el-table-column prop="item_name" label="系统名称" align="center"></el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleSelectSystem(scope.row)">选择</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog> -->
  </el-dialog>
</template>

<script>
import orangeTips from './orangeTips.vue'
import workSource from './workSource.vue'
import { operationInfo } from 'config/classroom.api.conf'
import { mapState } from 'vuex'
export default {
  name: 'MoreSettingDialog',
  components: {
    orangeTips,
    workSource
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    formData: {
      type: Object,
      required: true
    },
    baseInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        course_statement: {
          'operation_title': '',
          'creation_source': 2,
          'human_cost': '',
          'dept_id': '',
          'dept_full_name': '',
          'is_required': 0,
          'join_recommend': 1,
          'operation_level': 3,
          'operation_project_name': '',
          'pgc_creation_org': '',
          'pgc_creation_dept_id': '',
          'pgc_creation_dept_name': '',
          'pugc_creation_org': '',
          'pugc_joint_creation': '',
          'pugc_creation_dept_id': '',
          'pugc_creation_dept_name': '',
          'pugc_joint_creation_id': '',
          'pugc_joint_creation_name': '',
          'ugc_is_original': 1
        },
        activity_level: 0,
        activity_manage_org: [],
        join_recommend: 1,
        operation_title: '',
        creation_source: 2
      },
      moreSettingRules: {
        human_cost: [{ trigger: 'blur', validator: this.validHumanCost }],
        operation_project_name: [{ required: true, message: '请选择运营分级', trigger: 'blur' }],
        activity_level: [{ required: true, trigger: 'blur', validator: this.validActivityLevel }],
        activity_source: [{ required: true, message: '请选择活动来源', validator: this.validActivitySource }],
        activity_manage_org: [{ required: true, message: '请选择活动管理组织', trigger: 'change', validator: this.validActivityManageOrg }]
      },
      systemDialog: false,
      systemList: [],
      activityTypeList: [],
      operation_project_name_options: [],
      operation_project_name_options_map: {}
    }
  },
  computed: {
    ...mapState(['userDepInfo']),
    levelList() {
      return [
        { code: 0, name: '无', disabled: false }, 
        { code: 1, name: '公司级', disabled: false }, 
        { code: 2, name: 'BG级', disabled: false },
        { code: 3, name: '部门级', disabled: false }
      ]
    },
    operation_type_options() {
      let list
      if (process.env.NODE_ENV !== 'production') {
        list = [
          { label: '非体系', value: 3, pid: 625 },
          { label: '基础', value: 2, pid: 569 },
          { label: '中坚', value: 1, pid: 571 },
          { label: '头部', value: 0, pid: 573 }
        ]
      } else {
        list = [
          { label: '非体系', value: 3, pid: 528 },
          { label: '基础', value: 2, pid: 525 },
          { label: '中坚', value: 1, pid: 526 },
          { label: '头部', value: 0, pid: 527 }
        ]
      }
      return list
    },
    operationLevelRules() {
      return this.form.activity_level === 1 ? 
        [{ required: true, message: '请选择运营分级', trigger: 'change', validator: this.validOperationLevel }] : 
        [{ validator: this.validOperationLevel }]
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initData()
      }
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('update:visible', false)
      }
    },
    'form.activity_level': {
      handler(newVal) {
        // 当认证等级变化时，重新设置表单的验证规则
        this.$nextTick(() => {
          if (this.$refs.moreSettingForm) {
            // 需要重新验证operation_level字段
            this.$refs.moreSettingForm.clearValidate(['operation_level'])
          }
        })
      },
      immediate: true
    }
  },
  mounted() {
    this.getSystemList()
    this.getFormOptions()
  },
  methods: {
    initData() {
      this.$nextTick(() => {
        // 深拷贝表单数据, 还需要合并下this.form
        let obj = JSON.parse(JSON.stringify(this.formData))
        this.form = { ...this.form, ...obj }
        
        // 确保 course_statement 对象存在
        if (!this.form.course_statement) {
          this.$set(this.form, 'course_statement', {})
        }
        
        // 处理布尔值类型
        if (this.form.is_show_recommend instanceof Boolean || typeof this.form.is_show_recommend === 'boolean') {
          this.$set(this.form, 'is_show_recommend', this.form.is_show_recommend)
        } else {
          this.$set(this.form, 'is_show_recommend', this.form.is_show_recommend === 1)
        }

        // 处理其他字段
        this.$set(this.form.course_statement, 'join_recommend', this.form.course_statement.join_recommend ? 1 : 0)
        this.$set(this.form.course_statement, 'is_required', this.form.course_statement.is_required ? 1 : 0)
        this.$set(this.form.course_statement, 'ugc_is_original', this.form.course_statement.ugc_is_original === undefined ? 1 : (this.form.course_statement.ugc_is_original ? 1 : 0))
        this.$set(this.form, 'pdi_sub_level', this.form.pdi_sub_level ? String(this.form.pdi_sub_level) : '')
        
        // 设置默认的activity_level值
        if (this.form.activity_level === undefined) {
          this.$set(this.form, 'activity_level', 0)
        }
        
        // 设置默认的operation_level值
        if (this.form.course_statement.operation_level === undefined) {
          this.$set(this.form.course_statement, 'operation_level', 3) // 默认为"非体系"
        }

        // 处理活动管理组织
        let list = this.parseTeachers(this.form.dept_id, this.form.dept_full_name)
        if (list?.length > 0) {
          let newList = list.map(item => ({
            UnitID: item.StaffID || '',
            UnitFullName: item.StaffName || '',
            UnitName: this.processPath(item.StaffName) || ''
          }))
          this.$refs.deptSelectorRef && this.$refs.deptSelectorRef.setSelected(newList)
        } else if (!this.form.dept_id && this.userDepInfo) {
          // 设置默认活动管理组织 - 当没有设置过且存在用户部门信息时
          this.setDefaultActivityManageOrg()
        }

        // 尝试解析字符串格式的数据
        try {
          if (typeof this.form.course_statement.pgc_creation_org === 'string' && this.form.course_statement.pgc_creation_org) {
            this.form.course_statement.pgc_creation_org = JSON.parse(this.form.course_statement.pgc_creation_org)
          }
          if (typeof this.form.course_statement.pugc_creation_org === 'string' && this.form.course_statement.pugc_creation_org) {
            this.form.course_statement.pugc_creation_org = JSON.parse(this.form.course_statement.pugc_creation_org)
          }
          if (typeof this.form.course_statement.pugc_joint_creation === 'string' && this.form.course_statement.pugc_joint_creation) {
            this.form.course_statement.pugc_joint_creation = JSON.parse(this.form.course_statement.pugc_joint_creation)
          }
          if (typeof this.form.course_statement.ogc_purchase_org === 'string' && this.form.course_statement.ogc_purchase_org) {
            this.form.course_statement.ogc_purchase_org = JSON.parse(this.form.course_statement.ogc_purchase_org)
          }
        } catch (e) {
          console.error('Error parsing JSON data:', e)
        }

        // 手动触发一次运营分级选项加载
        if (this.form.course_statement.operation_level !== undefined) {
          this.changeOperationType(this.form.course_statement.operation_level)
        }
      })
    },
    getSystemList() {
      operationInfo({ key: 'class_system' }).then(res => {
        this.systemList = res
      })
    },
    getFormOptions() {
      operationInfo({ key: 'Code_PDI' }).then(res => {
        this.activityTypeList = res
        if (this.form.pdi_sub_level) {
          this.$set(this.form, 'pdi_sub_level', String(this.form.pdi_sub_level))
        } else {
          this.$set(this.form, 'pdi_sub_level', String(res[0].item_value))
        }
      })
    },
    validHumanCost(rule, value, callback) {
      // 允许为空
      if (value === undefined || value === null || value === '') {
        return callback()
      }
      // 不为空时必须是有效数字格式（整数或最多两位小数）
      if (!/^\d+(\.\d{1,2})?$/.test(value)) {
        return callback(new Error('请输入有效数字，可包含最多两位小数'))
      }
      callback()
    },
    validActivityLevel(rule, value, callback) {
      if (value === undefined || value === null) {
        return callback(new Error('请选择认证等级'))
      }
      callback()
    },
    validActivityManageOrg(rule, value, callback) {
      let list = this.form.dept_id ? this.form.dept_id.split(';') : ''
      if (!list.length) {
        return callback(new Error('请选择活动管理组织'))
      }
      callback()
    },
    validActivitySource(rule, value, callback) {
      callback()
    },
    validExpertScore(rule, value, callback) {
      if (!value) {
        return callback()
      }
      if (value > 100 || value < 0 || !/^\d+(\.\d{1,2})?$/.test(value) || String(value).startsWith('0')) {
        return callback(new Error('请输入0-100之间的数字，且输入的数字要符合格式'))
      }
      callback()
    },
    handleAddSystem() {
      this.systemDialog = true
    },
    changeActivityManageOrg(val) {
      if (val.length > 0 && !val[0].UnitID) return
      this.form.dept_id = val.map(item => item.UnitID).join(';')
      this.form.dept_full_name = val.map(item => item.UnitFullName).join(';')
      
      // 更新活动管理组织信息到course_statement
      this.form.course_statement.dept_id = this.form.dept_id
      this.form.course_statement.dept_full_name = this.form.dept_full_name
    },
    changeOperationType(val) {
      // 获取当前选中的项目名称，暂存以便后续恢复
      const currentProjectName = this.form.course_statement.operation_project_name
      let pid = this.operation_type_options.find(item => item.value === val).pid
      
      // 如果已经加载过这个分级的选项数据，直接使用缓存
      if (this.operation_project_name_options_map[pid]) {
        this.operation_project_name_options = this.operation_project_name_options_map[pid]
        
        // 如果当前没有选中值，或者当前值不在新的选项中，则清空
        if (currentProjectName) {
          const exists = this.operation_project_name_options.some(item => item.item_name === currentProjectName)
          if (!exists) {
            this.form.course_statement.operation_project_name = ''
          }
        }
        
        return
      }
      
      // 如果没有加载过，则异步加载选项数据
      operationInfo({ key: 'operations_level', pid }).then(res => {
        this.operation_project_name_options_map[pid] = res
        this.operation_project_name_options = res
        
        // 加载完后，检查当前选中的值是否在新选项中
        if (currentProjectName) {
          const exists = res.some(item => item.item_name === currentProjectName)
          if (!exists) {
            this.form.course_statement.operation_project_name = ''
          }
        }
      })
    },
    parseTeachers(id, name) {
      if (!id || !name) return []
      const idList = String(id).split(';').filter(item => item)
      const nameList = name.split(';').filter(item => item)

      // 创建结果数组
      const result = []

      // 遍历 id 和 name 列表,并组合成对象
      for (let i = 0; i < idList.length; i++) {
        const id = idList[i]
        const name = nameList[i]

        // 提取 id 和 name 中的实际值
        // 尝试从id中提取数字，如果失败则使用原始id
        let StaffID
        try {
          StaffID = parseInt(id.split('(')[0])
        } catch (e) {
          StaffID = id
        }
        const StaffName = name

        // 将数据添加到结果数组
        result.push({
          StaffID,
          StaffName
        })
      }

      return result
    },
    processPath(str) {
      if (!str) return ''
      
      const segments = str.split('/').filter(segment => segment !== '')

      if (segments.length === 0) return str

      const lastSegment = segments[segments.length - 1]

      return lastSegment === 'nn' ? str : lastSegment
    },
    cancel() {
      this.dialogVisible = false
      this.$emit('cancel')
    },
    validateForm() {
      // 验证人力成本, 可以为数字或者空字符串
      const humanCost = this.form.course_statement.human_cost
      // 如果不为空，必须是有效数字
      if (humanCost !== undefined && humanCost !== null && humanCost !== '' && !/^\d+(\.\d{1,2})?$/.test(humanCost)) {
        return { isValid: false, message: '人力成本格式不正确，请输入有效数字' }
      }
      
      // 验证活动级别
      if (this.form.activity_level === undefined || this.form.activity_level === null) {
        return { isValid: false, message: '请选择认证等级' }
      }
      
      // 验证活动管理组织
      const deptIds = this.form.dept_id ? this.form.dept_id.split(';').filter(id => id) : []
      if (!deptIds.length) {
        return { isValid: false, message: '请选择活动管理组织' }
      }
      
      // 验证运营分级
      if (![3].includes(this.form.course_statement.operation_level) && !this.form.course_statement.operation_project_name) {
        return { isValid: false, message: '请选择分级项目' }
      }
      // 所有验证通过
      return { isValid: true, message: '验证通过' }
    },

    confirm() {
      const validationResult = this.validateForm()
      let workFormInfo = this.$refs.workSourceRef.getWorkFormInfo()

      if (!validationResult.isValid) {
        this.$message.error(validationResult.message)
        return
      }

      if (!workFormInfo.isPass) {
        setTimeout(() => {
          this.$message.error('请完善活动来源信息')
        }, 1000)
        return
      }
      
      // 深拷贝当前表单数据
      const currentForm = JSON.parse(JSON.stringify(this.form))
      
      // 合并数据时保留原有的创作组织数据
      const mergedCourseStatement = {
        ...currentForm.course_statement,
        ...workFormInfo.info
      }
      
      // 创建新的表单对象
      const newForm = {
        ...currentForm,
        course_statement: mergedCourseStatement
      }
      
      this.dialogVisible = false
      this.$emit('confirm', newForm)
    },
    validOperationLevel(rule, value, callback) {
      // 只有当认证等级为公司级时，才验证运营分级是否填写
      if (this.form.activity_level !== 1) {
        return callback()
      }
      if (!this.form.course_statement.operation_level && this.form.course_statement.operation_level !== 0) {
        return callback(new Error('请选择运营分级'))
      }
      callback()
    },
    // 设置默认的活动管理组织
    setDefaultActivityManageOrg() {
      if (!this.userDepInfo || !this.userDepInfo.dept_full_name) return
      
      // 获取最后一级部门
      const deptFullName = this.userDepInfo.dept_full_name
      const deptName = this.getLastLevelDept(deptFullName)
      
      // 创建组织对象
      const deptObj = {
        UnitID: this.userDepInfo.dept_id || '',
        UnitFullName: deptFullName || '',
        UnitName: deptName || ''
      }
      
      // 设置到表单
      this.$refs.deptSelectorRef && this.$refs.deptSelectorRef.setSelected([deptObj])
      
      // 更新表单数据
      this.form.dept_id = this.userDepInfo.dept_id
      this.form.dept_full_name = this.userDepInfo.dept_full_name
    },
    // 获取部门全路径中的最后一级部门
    getLastLevelDept(deptFullName) {
      if (!deptFullName) return ''
      
      // 按照/分割路径，并过滤掉空字符串
      const segments = deptFullName.split('/').filter(segment => segment !== '')
      
      // 如果没有分段，直接返回原始值
      if (segments.length === 0) return deptFullName
      
      // 返回最后一个分段
      return segments[segments.length - 1]
    }
  }
}
</script>

<style lang="less" scoped>
.el-radio input[aria-hidden="true"] {
  display: none !important;
}

.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner {
  box-shadow: none !important;
}

:deep(.el-dialog.activity-info-more-setting-dialog) {
  border-radius: 8px;
  margin-top: 60px !important;
  width: 966px !important;
  padding: 0 0 0 10px;
  height: 80%;
  overflow-y: hidden;
  .el-dialog__body {
    padding: 30px 0;
    overflow-y: auto;
    height: calc(100% - 65px - 70px);
    &::-webkit-scrollbar {
      width: 8px; // 滚动条宽度
    }
  }
  .more-setting-container {
    padding-right: 40px;
  }
  .belongs-system-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .system-name-input {
      width: 90%;
    }
    .system-name-button {
      margin-left: 10px;
    }
  }
}

.remark-textarea {
  height: 100px;
  max-width: 800px;
  :deep(.el-textarea__inner) {
    height: 100%;
    max-width: 800px;
  }
}

.human-cost-unit {
  color: #00000066;
  font-family: "PingFang SC";
  font-size: 14px;
}

.normal-tips {
  color: #999999;
  font-family: "PingFang SC";
  font-size: 14px;
  line-height: 22px;
}

.creation_source_sub_content {
  background: #f9f9f9;
  border-radius: 4px;
  margin-top: 12px;
  padding: 12px;
  width: 626px;
  .operation_project_name_select {
    :deep(.el-input__icon) {
      line-height: 32px;
    }
  }
}
</style>
