<template>
  <div class="net-page">
    <div :class="['contain-main', {'article-fullen-main': !articleFullScreen || !isShowRight}]">
      <div class="left">
        <div class="content-top">
          <div class="name">
            <span class="model-tab" v-if="showAiGraphic && !isPreview && !isFormMooc">
              <span
                :class="['item-tab', {'active-model-tab': modelTabValue === v.value}]"
                v-for="v in modelTab"
                :key="v.label"
                @click="changeModelTab(v)"
                :dt-eid="dtCommon('eid', v.label)"
                :dt-remark="dtCommon('remark', v.label)"
                :dt-areaid="dtCommon('areaid', v.label)"
                >
                {{ $langue(v.tlabel, { defaultText: v.label }) }}
              </span>
              <div class="custom_tips_popover" v-if="articleTabTips">
                <i class="el-icon-caret-top"></i>
                <div class="custom-popover-content">
                  <span class="tips">{{$langue('NetCourse_ArticleTips', { defaultText: '点此查看此内容的文章版本' })}}</span>
                  <i class="el-icon-close" @click="handlePopover"></i>
                </div>
              </div>
            </span>
            <span class="overflow-l1">{{ courseData.course_name }}</span>
          </div>
          <div class="top-info" v-if="!isPreview">
            <dynamicsTag v-if="rankTag" :tagData="rankTag" :dtData="dtData"></dynamicsTag>
            <span class="top-iconInfo">
              <i class="icon-view"></i>
              <span>{{ courseData.view_count || 0 }} {{ $langue('NetCourse_Views', { defaultText: '次观看' }) }}</span>
              <span class="time">{{ courseData.created_at || $langue('NetCourse_CreateTime', { defaultText: '创建时间' }) + ':--' }}</span>
            </span>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content">
                <span class="create">{{ teacher_name }}</span>
              </div>
              <p class="create overflow-l1">
                <span>{{ teacher_name }}</span>
              </p>
            </el-tooltip>
          </div>
          <!-- 视频模式 -->
          <template v-if="modelTabValue === 'video_model'">
            <div v-if="isVideoType || courseData.course_type === 'Audio'" class="video-main">
              <Video
                class="video-box"
                :content_id.sync="courseData.content_id"
                @getCurCaption="getCurCaption"
                @handleRecord="handleRecord"
                @getCurrentTime="getCurrentTime"
                :source_src="courseData.file_url"
                :playTime="playTime"
                ref="vidioDOM"
                :fullscreenToggle="!enableInteractive"
                :progressControl="progressControl"
                :playbackRateMenuButton="!enableInteractive"
                @loadVideoSucess="loadVideoSucess"
                :autoPlay="autoPlay"
                @dtClickVideo="dtClickVideo"
                >
              </Video>
              <div class="current-time-tips" v-if="isCurrentTimeShow && playTime > 0">
                <i class="el-icon-close" @click="isCurrentTimeShow = false"></i>
                <span>
                  {{ moocLang === 'zh-cn' ? `上次播放至 ${transforNcTime(playTime)}` :
                      `Restart the playback where you left off at ${transforNcTime(playTime)}`
                  }}
                </span>
                <span
                class="tips-btn"
                @click="toCurrentTime"
                :dt-eid="dtCommon('eid', '点击跳转')"
                :dt-remark="dtCommon('remark', '点击跳转')"
                :dt-areaid="dtCommon('areaid', '点击跳转')" >{{ $langue('NetCourse_ClickGo', { defaultText: '点击跳转' })}}</span>
              </div>
              <!-- 视频播放互动弹窗 -->
              <InteractiveDialog
              v-if="enableInteractive"
              ref="interactiveDialog"
              class="interactive-dialog"
              :course_id="course_id"
              :record_id="learnRecordId"
              @changePlayStatus="changePlayStatus"
              />
            </div>
            <Scorm
            v-if="['Flash', 'Scorm', 'Doc'].includes(courseData.course_type)"
            class="scorm-box"
            :courseData="courseData"
            :scormType="courseData.course_type"
            @handleScormRecord="handleScormRecord"
            />
            <div v-else-if="!(isVideoType || courseData.course_type === 'Audio') && !isFormMooc" class="no-video-box">
              <el-image
              lazy
              fit="fill"
              :src="courseData.photo_url ? courseData.photo_url : require('@/assets/img/default_bg_img.png')"
              class="item-image"
              >
                <div slot="error" class="image-slot">
                  <i class="default-icon-picture"></i>
                </div>
              </el-image>
            </div>
          </template>
          <!-- 文章模式 -->
          <articleModel
          v-if="modelTabValue === 'article_model' && !isPreview"
          ref="netDraftRef"
          :courseData="courseData"
          :captionList="captionData"
          :captionCurTime="captionCurTime"
          :chapterSummaryList.sync="chapterSummaryList"
          :forMatTab="forMatTab"
          @toChaptersPosition="toChaptersPosition"
          />
          <!-- 章节 -->
          <netChapter
          v-if="chapterSummaryList.length && modelTabValue === 'video_model' && !isPreview"
          :chapterSummaryList="chapterSummaryList"
          :showChapterTips="showChapterTips"
          :studyRecordQuery="studyRecordQuery"
          :courseData="courseData"
          @toChaptersPosition="toChaptersPosition"
          />
          <!-- 点赞收藏分享 -->
          <div class="user-operat-box" v-if="articleFullScreen && !isPreview">
            <div class="operat-left">
              <div :class="['item-operat', {'icon-zan-active': zanAndcollect.isZan}]"  @click="handleLikeOrFav(1)" :dt-areaid="dtCommon('areaid', zanAndcollect.isZan ? '取消点赞' : '已点赞')" :dt-eid="dtCommon('eid', zanAndcollect.isZan ? '取消点赞' : '已点赞')" :dt-remark="dtCommon('remark', zanAndcollect.isZan ? '取消点赞' : '已点赞')">
                <i class="icon-zan"></i>
                <span >{{ courseData.praise_count || 0 }}</span>
              </div>
              <div v-if="!isFormMooc" class="item-operat shore-operat" @click="handleShow()" :dt-areaid="dtCommon('areaid', '分享')" :dt-eid="dtCommon('eid', '分享')" :dt-remark="dtCommon('remark', '分享')">
                <i class="icon-share"></i>
                <span>
                  {{ $langue('Article_Share', { defaultText: '分享'}) }}
                </span>
              </div>
              <div :class="['item-operat', {'active-isCollect-operat': zanAndcollect.isCollect}]" @click="handleLikeOrFav(2)" :dt-areaid="dtCommon('areaid',  zanAndcollect.isCollect ? '取消收藏' : '已收藏')" :dt-eid="dtCommon('eid', zanAndcollect.isCollect ? '取消收藏' : '已收藏')" :dt-remark="dtCommon('remark', zanAndcollect.isCollect ? '取消收藏' : '已收藏')">
                <i class="icon-collect"></i>
                {{ zanAndcollect.isCollect ? $langue('Mooc_ProjectDetail_BasicInfo_collected', { defaultText: '已收藏'}) : $langue('Mooc_ProjectDetail_BasicInfo_collect', { defaultText: '收藏'}) }}
              </div>
              <!-- 赞赏 -->
              <div
              v-if="showAppreciate"
              :class="['item-operat', {'active-appreciate-operat': is_appreciate}]"
              @click="handleAppreciate"
              :dt-areaid="dtCommon('areaid', is_appreciate ? '取消赞赏' : '已赞赏')" :dt-eid="dtCommon('eid', is_appreciate ? '取消赞赏' : '已赞赏')" :dt-remark="dtCommon('remark', is_appreciate ? '取消赞赏' : '已赞赏')"
              >
                <i class="icon-appreciate"></i>
                <span>{{ appreciatenumb || 0 }}</span>
              </div>
              <div class="jf-tip" v-if="isShowJfTip"><i class="jf-icon"></i>{{ $langue('Mooc_Common_Alert_CommonPoint', { point: 1, defaultText: '通用积分+1'}) }}</div>
              <div :class="['item-operat', {'add-operat':isAddCourse}]" @click="addCourseDialogShow = true" :dt-areaid="dtCommon('areaid', '添加到课单')" :dt-eid="dtCommon('eid', '添加到课单')" :dt-remark="dtCommon('remark', '添加到课单')">
                <i class="icon-add"></i>
                <span>{{ $langue('Article_AddCourseList', { defaultText: '添加到课单' }) }}</span>
              </div>
            </div>
            <div class="operat-right">
              <div class="item-operat white-note-btn" @click="createNote" :dt-areaid="dtCommon('areaid', '写笔记')" :dt-eid="dtCommon('eid', '写笔记')" :dt-remark="dtCommon('remark', '写笔记')">
                <i class="white-icon"></i>
                <span>{{ $langue('NetCourse_Notes', { defaultText: '写笔记' }) }}</span>
              </div>
            </div>
            <!-- <div class="item-operat dropdown-operat">
              <el-dropdown placement="bottom">
                <i class="more-btn"></i>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item icon="el-icon-plus">
                    <span
                      @click="addCourseDialogShow = true"
                      :dt-eid="dtAddCourse('eid')"
                      :dt-remark="dtAddCourse('remark')">
                      {{ $langue('Article_AddCourseList', { defaultText: '添加到课单' }) }}
                    </span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div> -->
          </div>
        </div>
        <div class="content-bottom" v-if="articleFullScreen">
          <el-tabs v-model="tabActiveName">
            <el-tab-pane
            :label="$langue(tabItem.label, { defaultText: tabItem.text })"
            :name="tabItem.name"
            v-for="(tabItem) in tabList"
            :key="tabKey(tabItem.name)"
            >
              <span slot="label" :dt-areaid="dtCommon('areaid', tabItem.text)" :dt-eid="dtCommon('eid', tabItem.text)" :dt-remark="dtCommon('remark', tabItem.text)">
                {{ tabItem.name === 'caption' ? tabItem.text :
                   $langue(tabItem.label, { defaultText: tabItem.text })
                }}
              </span>
            </el-tab-pane>
          </el-tabs>
          <!-- 介绍 -->
          <netDesc
          v-if="tabActiveName === 'desc'"
          :courseData="courseData"
          @handleCourseDetail="getCourseDetail"
          :specialUsers="specialUsers"
          :isPreview="isPreview"
          />
          <!-- 讨论 -->
          <netComment v-if="tabActiveName === 'comment'" :courseData="courseData"/>
          <!-- 字幕纪要 -->
          <articleModel
          v-if="modelTabValue === 'video_model' && tabActiveName === 'caption'"
          class="subtitle-summary-main"
          :courseData="courseData"
          :captionList="captionData"
          :captionCurTime="captionCurTime"
          :chapterSummaryList.sync="chapterSummaryList"
          cType="subtitle_summary"
          :forMatTab="forMatTab"
          @toChaptersPosition="toChaptersPosition"
          ref="articleModelRef"
          />
          <!-- 笔记 -->
          <netNotes v-if="tabActiveName === 'notes'" :courseData="courseData"></netNotes>
          <!-- 包含此内容 -->
          <incudesContent :courseData="courseData" v-if="!isPreview && !isFormMooc"/>
        </div>
      </div>
      <!-- 右侧 -->
      <rightSide
      v-if="!isFormMooc && articleFullScreen && isShowRight && !isPreview"
      ref="rightSideRef"
      :courseData="courseData"
      @noRightCard="noRightCard"
      >
      </rightSide>
      <!-- 右侧图标模块 -->
      <rightSideIcon v-if="!isPreview && !isFormMooc" :modelTabValue="modelTabValue"></rightSideIcon>
    </div>
    <AddCourseDialog :visible.sync="addCourseDialogShow" :itemData.sync="addCourseDialogData" @addedHandle="isAddCourse=true" />
    <ShareDialog ref="shareDialog" :isShow.sync="sharedialogShow" />
    <sdc-point-reward
      :dialogVisible.sync="doalpgVosoble"
      :params="paramslist"
      @close="appreciateclose"
    >
    </sdc-point-reward>
    <!-- 文章视频模块 -->
    <div class="move-area" ref="dragBox" @mousedown.stop="mouseDownHandler" v-if="modelTabValue === 'article_model' && showPictureData">
      <div class="custom-video">
        <Video
          class='picture-video-box'
          :content_id.sync="courseData.content_id"
          @getCurCaption="getCurCaption"
          @handleRecord="handleRecord"
          @getCurrentTime="getCurrentTime"
          @loadVideoSucess="loadVideoSucess"
          :source_src="courseData.file_url"
          :playTime="playTime"
          ref="pictureVidioDOM"
          :fullscreenToggle="false"
          :playbackRateMenuButton="false"
          :playToggle="false"
          :autoPlay="false"
          @dtClickVideo="dtClickVideo"
          >
        </Video>
      </div>
      <div class="out-mask">
        <span class="close-icon" @click="closePicture"></span>
        <!-- 暂停 -->
        <img v-if="['play'].includes(smallVideoStatus)" class="play-icon" @click="handleCustomPlay('pause')" src="@/assets/img/video-pause.png">
        <!-- 播放 -->
        <img v-else class="play-icon" @click="handleCustomPlay('play')" src="@/assets/img/video-play.png">
      </div>
    </div>
  </div>
</template>

<script>

import { AddCourseDialog, Video, Scorm } from '@/components/index'
import ShareDialog from '@/views/components/shareDialog'
import InteractiveDialog from '@/views/components/interactiveDialog'
import rightSide from './components/rightSide'
import netDesc from './components/netDesc'
import netComment from './components/netcomment'
import netNotes from './components/netNotes.vue'
import netChapter from './components/netChapter.vue'
import rightSideIcon from './components/rightSideIcon'
import incudesContent from './components/incudesContent'
import articleModel from './components/articleModel'
import { getCoursePreviewInfo } from 'config/api.conf.js'
import { pageExposure, transforNcTime } from '@/utils/tools.js'
import dynamicsTag from '@/components/dynamicsTag'
import {
  getNetCourseInfo,
  netViewRecord,
  netCheckPraised,
  netAddPraise,
  netDeletePraise,
  netCheckFavorited,
  netAddFavorite,
  netDeleteFavorite,
  getNetCourseChapterList,
  statususer,
  is_rewarded,
  reward_count,
  isFormalStaff,
  realtimeDocParse
} from 'config/api.conf'
import MoocJs from 'sdc-moocjs-integrator'
import translate from 'mixins/translate.vue'
import { getQueryVariable } from 'utils/tools'
import { getlabelSpecialUsers, getDynamicsTag } from 'config/mooc.api.conf.js'
import { mapState } from 'vuex'
import moment from 'moment'
import env from 'config/env.conf.js'
import playImg from '@/assets/img/audioPlay.png'
const envName = env[process.env.NODE_ENV]
export default {
  mixins: [translate],
  components: {
    Video,
    Scorm,
    AddCourseDialog,
    ShareDialog,
    InteractiveDialog,
    rightSide,
    netDesc,
    netComment,
    netNotes,
    netChapter,
    rightSideIcon,
    incudesContent,
    articleModel,
    dynamicsTag
  },
  data() {
    return {
      transforNcTime,
      isPreview: false, // 是否是预览
      commentParams: {},
      labelNodeEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test',
      loadComment: false,
      courseData: {
        content_type: '',
        comment_count: 0,
        fav_count: 0
      },
      extandList: [],
      chapterSummaryList: [],
      recommendList: [],
      addCourseDialogData: {
        module_id: 1,
        module_name: '网络课'
      },
      addCourseDialogShow: false,
      sharedialogShow: false,
      countTimer: null,
      zanAndcollect: {
        isZan: false,
        isCollect: false
      },
      isShowJfTip: false,
      tabActiveName: 'desc',
      captionData: [],
      captionCurTime: 0,
      studyRecordQuery: { // video学习记录
        act_id: getQueryVariable('course_id'),
        area_id: getQueryVariable('area_id'),
        from: getQueryVariable('jump_from') || getQueryVariable('from') || '',
        learn_record_id: 0,
        is_finish: 0,
        is_review: 0,
        total_study_time: 0,
        my_study_progress: 0,
        course_duration: 0
        // 下面是 scorm 课件时才需要
        // data_model: '',
        // scorm_item_id: 1,
      },
      learnRecordId: 0,
      scromRecordQuery: null,
      playTime: 0,
      isCurrentTimeShow: false,
      // curTimeShouTimer: null,
      progressControl: true,
      enableInteractive: false,
      viewRecordTime: null,
      chapterData: {
        chapter_content_list: [],
        chapter_ai_content_list: []
      },
      isAddCourse: false,
      specialUsers: false,
      modelTabValue: 'video_model',
      modelTab: [
        { label: '视频模式', value: 'video_model', tlabel: 'NetCourse_VideoMode' },
        { label: '文章模式', value: 'article_model', tlabel: 'NetCourse_ArticleMode' }
      ],
      smallVideoStatus: '',
      isDragging: false, // 是否可以移动
      offsetX: 0,
      offsetY: 0,
      autoPlay: true,
      isShowRight: true,
      articleTabTips: true,
      moveData: '',
      doalpgVosoble: false,
      paramslist: {},
      is_appreciate: false,
      allAuthorsHaveResigned: false,
      appreciatenumb: 0,
      showAppreciate: false,
      isPageExposure: false,
      rankTag: null
    }
  },
  watch: {
    'courseData.course_type'(value) {
      if (value === 'Audio') {
        // 音频模式tab文字修改
        this.modelTab.splice(0, 1, { label: '音频模式', value: 'video_model', tlabel: 'NetCourse_AudioMode' })
      } else {
        this.modelTab.splice(0, 1, { label: '视频模式', value: 'video_model', tlabel: 'NetCourse_VideoMode' })
      }
    },
    chapterSummaryList(val) {
      if (!val?.length) {
        this.tabActiveName = 'desc'
      }
    },
    showModelTab(value) {
      if (value && this.$route.query.view_type === 'graphic') {
        this.changeModelTab({ value: 'article_model' })
      }
    }
  },
  computed: {
    ...mapState({
      articleFullScreen: state => state.net.articleFullScreen,
      showPicture: state => state.net.showPicture,
      volume: state => state.net.volume,
      playbackRate: state => state.net.playbackRate,
      playStatus: state => state.net.playStatus,
      userInfo: state => state.userInfo,
      moocLang: (state) => state.moocLang,
      isBusy: state => state.isBusy
    }),
    isSpecialArea() {
      return this.$route.query.from === 'SpecialArea' && this.$route.query.area_id
    },
    swiper() {
      return this.$refs.mySwiper.$swiper
    },
    course_id() {
      return this.$route.query.course_id ? parseInt(this.$route.query.course_id) : ''
    },
    isFormMooc() {
      return this.$route.query.from === 'mooc'
    },
    isFormSpoc() {
      return this.$route.query.from === 'spoc'
    },
    teacher_name() {
      let { inner_teacher_names, out_teacher_names } = this.courseData
      let name = ''
      if (inner_teacher_names?.length) {
        inner_teacher_names.forEach((e) => {
          if (!e.teacher_name) return
          name += `${e.teacher_name}， `
        })
      }
      if (out_teacher_names?.length) {
        out_teacher_names.forEach((e) => {
          if (!e.teacher_name) return
          name += `${e.teacher_name}， `
        })
      }
      name = name.slice(0, -2)
      return name
    },
    isVideoType() {
      return ['Video', 'Video-2d', 'Video-3d', 'Video-ppt'].includes(this.courseData.course_type)
    },
    tabKey() {
      return (name) => {
        return name + new Date().getTime()
      }
    },
    showChapterTips() {
      const { chapter_ai_content_list, chapter_content_list } = this.chapterData
      return !!(!chapter_content_list?.length && chapter_ai_content_list?.length)
    },
    showPictureData() {
      return this.showPicture
    },
    isShowChapterSummary() {
      return this.chapterSummaryList.some(e => e.chapter_content)
    },
    forMatTab() {
      let text = ''
      let subtitle = ''
      let summary = ''
      if (this.courseData.show_comments !== false) { // 字幕，纪要都显示
        if ((this.courseData.captions?.length || this.captionData?.length) && this.showManuscript) { // 字幕显示
          subtitle = this.$langue('NetCourse_Subtitle', { defaultText: '字幕' })
        }
        if (this.chapterSummaryList?.length && this.isShowChapterSummary) { // 纪要显示
          summary = this.$langue('NetCourse_Summary', { defaultText: '纪要' })
        }
        // 数据拼接
        if (summary && subtitle) {
          text = `${subtitle} · ${summary}`
        } else if (summary && !subtitle) {
          text = summary
        } else if (!summary && subtitle) {
          text = subtitle
        }
      }
      return {
        text,
        subtitle,
        summary
      }
    },
    showManuscript() {
      // 只有当show_manuscript有值时 并且show_manuscript 为 false时才隐藏内容文稿，其他情况为显示
      return !(this.courseData.course_statement && !this.courseData.course_statement.show_manuscript)
    },
    tabList() {
      const tabArr = [
        { label: 'Mooc_ProjectDetail_Notice_Introduce', name: 'desc', text: '介绍' },
        { label: 'NetCourse_Note', name: 'notes', text: '笔记' }
      ]
      // 评论区是否显示
      if (this.courseData.show_comments !== false) {
        tabArr.splice(1, 0, { label: 'Mooc_ProjectDetail_Notice_Comments', name: 'comment', text: '讨论' })
      }
      // 显示文稿纪要
      const { subtitle, summary, text } = this.forMatTab
      if ((subtitle || summary) && this.modelTabValue === 'video_model') {
        tabArr.splice(2, 0, { name: 'caption', text })
      }
      return tabArr
    },
    showAiGraphic() {
      return !!(this.courseData.artificial_graphic_id || this.courseData.ai_graphic_id)
    },
    dtCommon() {
      return (type, val) => {
        if (type === 'remark') {
          return JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页-新版',
            container: '',
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: val,
            act_type: '',
            container_id: '',
            page_id: '',
            terminal: 'PC'
          })
        } else if (type === 'eid') {
          return `element_${this.course_id}_${val}`
        } else {
          return `area_${this.course_id}_${val}`
        }
      }
    },
    // 是否显示切换文章、视频的tab
    showModelTab() {
      return this.showAiGraphic && !this.isPreview && !this.isFormMooc
    },
    dtData() {
      return {
        page: this.courseData.course_name,
        page_type: '网课详情页-新版',
        container: '',
        click_type: 'data',
        content_type: '',
        content_id: '',
        content_name: '',
        act_type: '',
        container_id: '',
        page_id: '',
        terminal: 'PC',
        id: this.course_id
      }
    }
  },
  created() {
    try {
      this.isPreview = JSON.parse(this.$route.query.is_preview)
    } catch (error) {
      this.isPreview = false
    }
  },
  mounted() {
    this.realtimeDocParse()
    this.$store.dispatch('getIsBusy')
    // 文章模式提示
    const visible = localStorage.getItem('article_tab_visible')
    this.articleTabTips = Number(visible) !== 1

    let flag = /[a-zA-Z0-9]/g.test(this.$route.query.course_id)
    if (!flag) { // 非数字字母组合的的情况跳转
      sessionStorage.setItem('401Msg', this.$langue('Mooc_Common_Alert_DataNotExisit', { defaultText: '数据不存在' }))
      this.$router.replace({
        name: '401'
      })
      return
    }

    if (this.isPreview) { // 判断是否是预览
      this.getPreviewInfo()
      return
    }

    this.getChapterList()
    this.getlabelSpecialInfo()
    window.addEventListener('beforeunload', (e) => {
      // 离开当前页面学习记录归档
      if (((this.isVideoType || this.courseData.course_type === 'Audio') && this.studyRecordQuery.total_study_time) || this.scromRecordQuery) {
        let param = {}
        if (this.scromRecordQuery) {
          param = this.scromRecordQuery
        } else {
          param = this.studyRecordQuery
        }
        param.is_archive = true
        let blob = new Blob([JSON.stringify(param)], { type: 'application/json; charset=UTF-8' })
        navigator.sendBeacon('/training/api/netcourse/user/courseinfo/add-study-record', blob)
      }
    })

    this.getCourseDetail().then((res) => {
      // 获取动态标签
      if (this.isBusy !== '1') {
        this.getDynamicsTag()
      }
      this.loadComment = true
      // 任务已完成时，开启任务同步弹窗
      if (this.courseData.is_finish * 1 === 1 && (this.isFormMooc || this.isFormSpoc)) {
        MoocJs.complete('init')
      }
      this.getZanAndCollectStatus()
      // <---赞赏--->
      const params = {
        to: this.courseData.creator_id, // 给谁赞赏
        object_id: this.courseData.net_course_id,
        object_type: 'netCourse',
        object_type_name: '网络课',
        object_name: this.courseData.course_name
      }
      this.paramslist = {
        env: process.env.NODE_ENV,
        to_name: this.courseData.creator_name,
        ...params
      }
      console.log('参数哈哈哈', params)
      // 是否是正式员工
      isFormalStaff(this.courseData.creator_id).then((res) => {
        console.log('是否是正式员工', res)
        this.showAppreciate = res
      })
      // 是否赞赏
      is_rewarded(params).then((res) => {
        console.log('是否赞赏', res)
        this.is_appreciate = res
      })
      // 是否离职
      statususer(this.courseData.creator_id).then((res) => {
        this.allAuthorsHaveResigned = !(res !== 2)
        console.log('是否离职', res, this.allAuthorsHaveResigned)
      })
      // 赞赏量
      reward_count({
        object_id: this.courseData.net_course_id,
        object_type: 'netCourse'
      }).then((res) => {
        this.appreciatenumb = res
      })
    }).catch((err) => {
      if (err.code === 403 || err.code === 500) {
        sessionStorage.setItem('401Msg', err.message)
        this.$router.replace({
          name: '401'
        })
        if (this.isFormMooc || this.isFormSpoc) {
          MoocJs.sendErrorInfo(err.message)
        }
      }
    })
    if (this.isFormMooc || this.isFormSpoc) {
      MoocJs.setPause(() => {
        this.$refs.vidioDOM.vedioPlayer.pause()
      })
      MoocJs.setPlay(() => {
        this.$refs.vidioDOM.vedioPlayer.play()
      })
    }
    // 如果是v8跳转过来就定位v8的位置
    const { targetTime } = this.$route.query
    if (targetTime > -1) {
      const playTime = targetTime * 1
      this.toChaptersPosition(playTime)
    }
    MoocJs.messageListener((res) => {
      if (res.events === 'tencent-mooc-lang') {
        this.$store.commit('setMoocLang', res.params)
        this.getLangJS()
      }
    })
  },
  methods: {
    getDynamicsTag() {
      const dt = moment().subtract(2, 'days').format('YYYYMMDD')
      const param = {
        act_type: '2',
        course_id: this.courseData.net_course_id,
        p_dt: dt
      }
      getDynamicsTag(param).then((res) => {
        this.rankTag = res
      })
    },
    // 解析课程
    realtimeDocParse() {
      let params = {
        bizType: 'NET_COURSE',
        bizId: this.course_id,
        sessionId: '',
        model: 1
      }
      realtimeDocParse(params).then(res => {

      })
    },
    // 获取课程实时预览信息
    getPreviewInfo() {
      getCoursePreviewInfo(this.course_id).then(res => {
        document.title = `${res.course_name}_Q-Learning`
        this.courseData = res
        this.courseData.labels = res.course_labels
        // this.courseData.content_id = res.content_info.content_id
      }).catch(err => {
        console.log('err: ', err)
      })
    },
    handlePopover() {
      this.articleTabTips = false
      localStorage.setItem('article_tab_visible', 1)
    },
    noRightCard(val) {
      this.isShowRight = val
    },
    mouseDownHandler(event) {
      event.preventDefault()
      this.isDragging = true
      this.offsetX = event.clientX - this.$refs.dragBox.offsetLeft
      this.offsetY = event.clientY - this.$refs.dragBox.offsetTop
      document.addEventListener('mousemove', this.mouseMoveHandler)
      document.addEventListener('mouseup', this.mouseUpHandler)
    },
    mouseMoveHandler(event) {
      event.preventDefault()
      if (this.isDragging) {
        const x = event.clientX - this.offsetX
        const y = event.clientY - this.offsetY

        const box = this.$refs.dragBox
        const viewportWidth = window.innerWidth
        const viewportHeight = window.innerHeight
        // 边界检查, 只能在视口区域内移动
        const minX = 0
        const maxX = viewportWidth - box.offsetWidth
        const minY = 0
        const maxY = viewportHeight - box.offsetHeight
        this.moveData = x || y
        if (x <= 0) { // 解决translateX后无法拖动全屏问题
          box.style.left = `${Math.min(x, maxX)}px`
        } else {
          box.style.left = `${Math.min(Math.max(x, minX), maxX)}px`
        }
        box.style.top = `${Math.min(Math.max(y, minY), maxY)}px`
      }
    },
    mouseUpHandler(event) {
      event.preventDefault()
      this.isDragging = false
      document.removeEventListener('mousemove', this.mouseMoveHandler)
      document.removeEventListener('mouseup', this.mouseUpHandler)
      setTimeout(() => {
        this.moveData = ''
      })
    },
    // 该函数处理问题:
    // tab切换，video组件没有加载好，导致无法控制视频播放暂停
    // 该函数在视频加载好后使用则可以解决此问题
    loadVideoSucess() {
      let dom = this.modelTabValue === 'video_model' ? 'vidioDOM' : 'pictureVidioDOM'
      // 埋点曝光上报
      const info = this.modelTab.find((v) => v.value === this.modelTabValue)
      const playStatus = this.smallVideoStatus === 'play' ? '播放' : '暂停'
      const list = [playStatus, '全屏', '倍速', '音量']
      list.forEach((v) => {
        window.BeaconReport('at_show_area', {
          eid: `area_${this.course_id}_${v}`,
          remark: JSON.stringify({
            page: this.courseData.course_name,
            page_type: '网课详情页-新版',
            container: info.label,
            click_type: 'button',
            content_type: '',
            content_id: '',
            content_name: v,
            act_type: '',
            page_id: '',
            container_id: '',
            terminal: 'PC'
          })
        })
      })
      // 时间为0的时候默认暂停
      if (this.captionCurTime <= 0) {
        return
      }
      this.$refs[dom].vedioPlayer.volume(this.volume) // 声音
      this.$refs[dom].vedioPlayer.playbackRate(this.playbackRate)
      this.$refs[dom].vedioPlayer.currentTime(this.captionCurTime)
      const status = sessionStorage.getItem('changeTabPlayStatus')
      if (status && status !== 'false') {
        this.$refs[dom].vedioPlayer.play()
        return
      }
      this.$refs[dom].vedioPlayer.pause()
    },
    closePicture() {
      this.$store.commit('net/setPictureShow', false)
    },
    // 自定义图片播放暂停
    handleCustomPlay(val) {
      if (this.moveData) return // 正在移动那就不让播放
      if (val === 'play') {
        this.$refs.pictureVidioDOM.vedioPlayer.play()
        return
      }
      this.$refs.pictureVidioDOM.vedioPlayer.pause()
    },
    // 模式选择
    changeModelTab(v) {
      // 记录切换前的播放状态
      sessionStorage.setItem('changeTabPlayStatus', this.playStatus)
      this.modelTabValue = v.value
      // 视频模式
      if (this.modelTabValue === 'video_model') {
        // 初始化文章全屏
        this.$store.commit('net/setArticleFull', true)
        // 切换内容的时候不让自动播放
        this.autoPlay = false
        return
      }
      // 文章模式
      if (this.tabActiveName === 'caption') {
        this.tabActiveName = 'desc'
      }
      // 文章模式画中画开启
      this.$store.commit('net/setPictureShow', true)
    },
    // 创建笔记
    createNote() {
      const { href } = this.$router.resolve({
        name: 'create',
        query: {
          from: 'ql',
          id: this.$route.query.course_id,
          name: this.courseData.course_name,
          type: 3,
          module_id: 1
        }
      })
      window.open(href)
    },
    // 章节时间对应视频时间的位置
    toChaptersPosition(chapter_time_point) {
      // 互动优先级更高，开启互动后不可点击章节跳转进度
      if (this.enableInteractive) {
        this.$message.warning('已开启互动，不可跳转进度')
        return
      }
      this.captionCurTime = chapter_time_point
      this.$nextTick(() => {
        // 处理章节定位文章内容滚动到对应播放时间过慢问题
        const refName = this.modelTabValue === 'video_model' ? 'articleModelRef' : 'netDraftRef'
        this.$refs[refName] && this.$refs[refName].startAutoScroll() // 自动滚动内容
        if (this.modelTabValue === 'article_model') {
          this.$refs.pictureVidioDOM.vedioPlayer.currentTime(this.captionCurTime)
        } else {
          this.$refs && this.$refs.vidioDOM && this.$refs.vidioDOM.vedioPlayer && this.$refs.vidioDOM.vedioPlayer.currentTime(this.captionCurTime)
        }
      })
    },
    getRouterQuery() {
      let { mooc_course_id, task_id } = this.$route.query
      return {
        mooc_course_id: mooc_course_id || '',
        task_id: task_id || ''
      }
    },
    getCurCaption(data) {
      if (!this.captionData.length) this.captionData = data
    },
    handleRecord(param) {
      this.smallVideoStatus = param.evt
      if (param.evt === 'loadedmetadata') {
        // 视频加载完成后拿到播放时长
        let duration = 0
        if (param.duration) { duration = Math.floor(param.duration) }
        // 防止更换视频，过滤多余的章节时间节点
        if (duration) {
          this.chapterSummaryList = this.chapterSummaryList.filter(its => its.chapter_time_point <= duration)
        }
      }
      let status = ''
      if (param.evt === 'play') {
        this.studyRecordQuery.course_duration = Math.floor(param.duration)
        // 重新学习，重置数据
        if (this.studyRecordQuery.is_finish === 1) {
          this.studyRecordQuery.is_finish = 0
          this.studyRecordQuery.is_review = 1 // 重播
          this.studyRecordQuery.total_study_time = 0
          this.learnRecordId = 0
        }
        if (!this.countTimer) {
          this.creatViewTimer(param)
        }
        if (this.isFormMooc || this.isFormSpoc) {
          MoocJs.play()
        }
      }

      if (param.evt === 'pause' || param.evt === 'ended') {
        if (param.evt === 'ended') { // 学习完
          this.studyRecordQuery.is_finish = 1
          status = 'ended'
        }
        // 延迟1秒调用方法，（视频播放完成会先触发pause，再触发ended），解决同时触发pause、ended情况
        if (this.viewRecordTime) clearTimeout(this.viewRecordTime)
        this.viewRecordTime = setTimeout(() => {
          this.viewRecord('', '', status)
        }, 1000)
        clearInterval(this.countTimer)
        this.countTimer = null

        if (param.evt === 'pause' && (this.isFormMooc || this.isFormSpoc)) {
          MoocJs.pause()
        } else if (param.evt === 'ended' && (this.isFormMooc || this.isFormSpoc)) {
          MoocJs.complete()
        }
      }
    },
    handleScormRecord(scormdata) {
      if (scormdata.is_finish && (this.isFormMooc || this.isFormSpoc)) {
        MoocJs.complete()
      }
      this.viewRecord(scormdata, 'scorm')
    },
    getCurrentTime(curTime) {
      this.captionCurTime = Number(curTime.toFixed(2))
      this.studyRecordQuery.my_study_progress = Math.floor(this.captionCurTime) // 视频当前播放时长
      this.$refs.interactiveDialog && this.$refs.interactiveDialog.getVideoTime(curTime)
      // 处理章节定位文章内容滚动到对应播放时间过慢问题
      const refName = this.modelTabValue === 'video_model' ? 'articleModelRef' : 'netDraftRef'
      this.$refs[refName] && this.$refs[refName].startAutoScroll() // 自动滚动内容
    },
    changePlayStatus(status) {
      if (status === 'play') {
        this.$refs.vidioDOM.vedioPlayer.play()
      } else if (status === 'pause') {
        this.$refs.vidioDOM.vedioPlayer.pause()
      }
      if (this.isFormMooc) {
        MoocJs.postMessage('interactiveDialog', status === 'pause')
      }
    },
    creatViewTimer() {
      let _this = this
      let durtation = 0
      let { course_duration } = this.studyRecordQuery
      this.countTimer = setInterval(function () {
        let { my_study_progress, is_finish } = _this.studyRecordQuery
        durtation++
        _this.studyRecordQuery.total_study_time++
        if (durtation % 15 === 0) {
          _this.viewRecord() // 浏览器时长需每15秒记录一次
        }

        // 比视频时长提前10秒触发完成上报
        if (course_duration >= 60 && my_study_progress + 10 >= course_duration && !is_finish) {
          _this.studyRecordQuery.is_finish = 1
          _this.viewRecord()
          clearInterval(_this.countTimer)
          _this.countTimer = null
          if (_this.isFormMooc || _this.isFormSpoc) {
            MoocJs.complete()
          }
        }
      }, 1000)
    },
    viewRecord(videoParam, recordType, status) {
      const { previewType } = this.$route.query
      // 预览不做上报
      if (previewType === 'preview') {
        return
      }
      let recordParam = {}
      if (recordType && recordType === 'scorm') {
        recordParam = videoParam
        recordParam.act_id = this.course_id
        recordParam.learn_record_id = this.learnRecordId
        this.scromRecordQuery = recordParam
      } else {
        if (!this.studyRecordQuery.total_study_time) return
        this.studyRecordQuery.learn_record_id = this.learnRecordId
        recordParam = this.studyRecordQuery
      }
      netViewRecord(recordParam).then((data) => {
        if (data) {
          if (status) {
            this.learnRecordId = 0
          } else {
            this.learnRecordId = data
          }
        }
      })
    },
    getCourseDetail() {
      const { share_staff_id, share_staff_name } = this.$route.query
      const reFun = getNetCourseInfo({ act_id: this.course_id, share_staff_id: share_staff_id || '', share_staff_name: share_staff_name || '' }).then((data) => {
        if (data.status === 3) {
          sessionStorage.setItem('401Msg', `${this.$langue('NetCourse_CourseOffLine', { defaultText: '该课程已下架' })}！`)
          this.$router.replace({
            name: '401'
          })
          return
        }
        document.title = `${data.course_name}_Q-Learning`
        this.courseData = data
        this.courseData.labels = data.course_labels
        const { course_name, photo_url, course_desc, net_course_id, content_id } = data
        const net_url = location.hostname.endsWith('.woa.com') ? `${process.env.VUE_APP_PORTAL_HOST_WOA}/training/netcourse/play?course_id=${net_course_id}` : `${process.env.VUE_APP_PORTAL_HOST}/training/netcourse/play?course_id=${net_course_id}`
        this.addCourseDialogData = {
          ...this.addCourseDialogData,
          content_name: course_name,
          cover_img_url: photo_url,
          description: course_desc,
          href: net_url,
          item_id: net_course_id,
          origin: location.origin
        }
        this.courseData.content_id = content_id
        // 续播
        this.playTime = data.my_study_progress
        if (this.playTime) {
          this.isCurrentTimeShow = true
        }
        this.enableInteractive = data.enable_interactive
        // limit_progress_bar 是否限制进度条 true限制  false不限制
        this.progressControl = !data.limit_progress_bar
        /*
          course_type:
            Video 纯视频文件
            Flash 压缩包文件
            Scorm 标准课件
            CaseStudy 案例库
            Audio 纯音频文件
            Doc 文档
            Video-2d 视频文件(PPT-2D)
            Video-ppt 视频文件(PPT)
        */
        if (data.course_type === 'CaseStudy') {
          this.$messageBox(`${this.$langue('NetCourse_NotSupportRecourse', { defaultText: '暂不支持该类型资源查看！' })}！`, this.$langue('Mooc_TaskDetail_ThirdParty_Alert', { defaultText: '提示' }), {
            distinguishCancelAndClose: true,
            confirmButtonText: this.$langue('NetCourse_Ok', { defaultText: '好的' }),
            cancelButtonText: this.$langue('Mooc_Common_Alert_Cancel', { defaultText: '取消' })
          })
        }

        // 详情页曝光上报
        if (!this.isPageExposure) {
          this.isPageExposure = true
          pageExposure({
            page_type: '灰度网络课详情页',
            content_type: '网络课',
            act_type: '2',
            content_name: course_name,
            content_id: net_course_id
          })
        }
      })
      return reFun
    },
    secondsToMinutes(seconds) {
      let minutes = Math.floor(seconds / 60) // 计算分钟
      let remainingSeconds = seconds % 60 // 计算剩余秒数
      remainingSeconds = remainingSeconds.toString().padStart(2, '0')
      return {
        minutes: minutes,
        seconds: remainingSeconds
      }
    },
    // 获取网络课章节列表
    getChapterList() {
      getNetCourseChapterList(this.course_id).then((data) => {
        const { chapter_ai_content_list, chapter_content_list } = data
        const chapterList = chapter_content_list?.length ? chapter_content_list : chapter_ai_content_list?.length ? chapter_ai_content_list : []
        this.chapterData = data
        this.chapterSummaryList = (chapterList || []).map((item, i) => {
          let { minutes, seconds } = this.secondsToMinutes(item.chapter_time_point)
          const url = item.chapter_cover_content_id ? `${envName.contentcenter}content-center/api/v1/content/imgage/${item.chapter_cover_content_id}/preview` : (item.chapter_cover_url || playImg)
          const startTime = chapterList[i].chapter_time_point
          // 如果是最后一条数据，结束时间在开始时间基础上加1来处理
          const endTime = chapterList.length === (i + 1) ? chapterList[i].chapter_time_point + 1 : chapterList[i + 1].chapter_time_point
          return {
            ...item,
            chapter_content: item.chapter_content ? item.chapter_content.replace(/(\\r\\n|\\n|\n|\r\n)+/g, '<br>') : '',
            chapter_time: minutes + ':' + seconds,
            imgUrl: url,
            startTime,
            endTime
          }
        })
      })
    },
    // 跳转时间
    toCurrentTime() {
      this.isCurrentTimeShow = false
      this.$refs.vidioDOM.vedioPlayer.currentTime(this.playTime)
    },
    getZanAndCollectStatus() {
      const params = { net_course_id: this.course_id }
      netCheckPraised(params).then(res => {
        this.zanAndcollect.isZan = res
      })
      netCheckFavorited(params).then(res => {
        this.zanAndcollect.isCollect = res
      })
    },
    handleLikeOrFav(scene) {
      const params = { net_course_id: this.course_id }
      if (scene === 1) {
        netCheckPraised(params).then(res => {
          const PAndFCommonAPI = res ? netDeletePraise : netAddPraise
          const tip = res ? this.$langue('Mooc_Common_Alert_CancelPraiseSucessed', { defaultText: '取消点赞成功' }) : this.$langue('Mooc_Common_Alert_PraiseSucessed', { defaultText: '点赞成功' })
          this.courseData.praise_count = res ? (this.courseData.praise_count === null || this.courseData.praise_count === 0 ? 0 : this.courseData.praise_count - 1) : this.courseData.praise_count + 1
          this.handlerCommonInt(PAndFCommonAPI, params, tip)
        })
      } else {
        netCheckFavorited(params).then(res => {
          const PAndFCommonAPI = res ? netDeleteFavorite : netAddFavorite
          const tip = res ? this.$langue('Mooc_Common_Alert_CancelCollectSucessed', { defaultText: '取消收藏成功' }) : this.$langue('Mooc_Common_Alert_CollectSucessed', { defaultText: '收藏成功' })
          this.courseData.fav_count = res ? (this.courseData.fav_count === null || this.courseData.fav_count === 0 ? 0 : this.courseData.fav_count - 1) : this.courseData.fav_count + 1
          this.handlerCommonInt(PAndFCommonAPI, params, tip)
        })
      }
    },
    handlerCommonInt(PAndFCommonAPI, params, tip) {
      PAndFCommonAPI(params).then(data => {
        if (PAndFCommonAPI === netAddPraise || PAndFCommonAPI === netDeletePraise) this.zanAndcollect.isZan = PAndFCommonAPI === netAddPraise ? Boolean(true) : Boolean(false)
        if (PAndFCommonAPI === netAddFavorite || PAndFCommonAPI === netDeleteFavorite) this.zanAndcollect.isCollect = PAndFCommonAPI === netAddFavorite ? Boolean(true) : Boolean(false)
        if (PAndFCommonAPI === netAddPraise || PAndFCommonAPI === netAddFavorite) {
          if (data.credit && data.credit !== '0') {
            this.isShowJfTip = true
            setTimeout(() => {
              this.isShowJfTip = false
            }, 6000)
            this.$message.success(`${tip}，${this.$langue('Mooc_Common_Alert_CommonPoint', { point: +data.credit, defaultText: `通用积分+${+data.credit}` })}`)
          } else this.$message.success(tip)
        } else this.$message.success(tip)
      })
    },
    handleShow() {
      this.sharedialogShow = true
      const url = process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/b5GaSG' : 'http://s.test.yunassess.com/s/hoo9Gg'
      let userInfo = JSON.parse(sessionStorage.getItem('login_user'))
      this.$nextTick(() => {
        let href = `${url}?course_id=${this.course_id}&scheme_type=netcourse&share_staff_id=${userInfo.staff_id}&share_staff_name=${userInfo.staff_name}`
        if (this.modelTabValue === 'article_model') {
          href += '&view_type=graphic'
        }
        this.$refs.shareDialog.initCode({
          url: href,
          scene: `${this.course_id}_${userInfo.staff_id}`,
          page: 'pages/networkCourse/video/videoDetail',
          customText: `【${this.courseData.course_name}】${href}`
        })
      })
    },
    // 赞赏
    handleAppreciate() {
      let userInfo = JSON.parse(sessionStorage.getItem('login_user'))
      if (this.is_appreciate) {
        this.$message({
          type: 'warning',
          message: '您已经赞赏过了,不能重复赞赏',
          duration: 2000
        })
        return
      }
      if (Number(this.courseData.creator_id) === userInfo.staff_id) {
        this.$message({
          type: 'warning',
          message: '不能赞赏自己的文章~',
          duration: 2000
        })
        return
      }
      if (this.allAuthorsHaveResigned) {
        this.$message({
          type: 'warning',
          message: '作者已离职，无法赞赏',
          duration: 2000
        })
        return
      }
      this.doalpgVosoble = true
    },
    appreciateclose(e) {
      console.log('eeeeeeeeeeeee', e)
      if (e) {
        this.is_appreciate = true
        ++this.appreciatenumb
      }
    },
    // 获取用户是不是特殊人员
    getlabelSpecialInfo() {
      getlabelSpecialUsers().then(res => {
        this.specialUsers = res
      })
    },
    // 视频播放埋点
    dtClickVideo(val) {
      let strLabel = ''
      switch (val) {
        case 'play':
          strLabel = '视频播放'
          break
        case 'pause':
          strLabel = '视频暂停'
          break
        case 'fullscreen':
          strLabel = '全屏'
          break
        case 'rate':
          strLabel = '倍速'
          break
        case 'volume':
          strLabel = '音量'
          break
        default:
          break
      }
      window.BeaconReport('at_click', {
        eid: `element_${this.course_id}_${strLabel}`,
        remark: JSON.stringify({
          page: this.courseData.course_name,
          page_type: '网课详情页-新版',
          container: `视频模式`,
          click_type: 'button',
          content_type: '',
          content_id: '',
          content_name: strLabel,
          act_type: '',
          page_id: '',
          container_id: '',
          terminal: 'PC'
        })
      })
    }
  },
  beforeDestroy() {
    clearInterval(this.countTimer)
    this.countTimer = null
    MoocJs.removeEvent()
    window.removeEventListener('resize', () => { })
  }
}
</script>
<style lang="less"></style>
<style lang="less" scoped>
.project-add-label /deep/.cascader-component button>span>span {
  font-size: 12px;
}

.tag-form-item {
  :deep(.el-input) {
    min-height: 32px;
    height: unset;
  }
}

.net-page {
  :deep(.sdc-editor-preview) {
    width: 100%;

    .content-wrapper {
      width: 100%;
    }

    .file-count {
      padding: 0 0 8px 24px;
    }

    .editor-file-list {
      margin: 0 24px 36px 24px;
    }

    .desc,
    .editor-content {
      word-break: break-word;

      ol,
      ul {
        padding: revert;
      }
    }

    .desc {
      margin: 0 24px 0 24px;
    }

    // .editor-content {
    //   padding: 20px 24px 32px 24px;
    // }
  }

  .contain-main {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    padding-top: 10px;

    .left {
      margin-bottom: 20px;

      .content-top {
        padding: 24px;
        background-color: #fff;
        padding-bottom: 26px;
        border-radius: 8px;
        .name {
          color: #000000;
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 10px;
          word-break: break-word;
          display: flex;
          align-items: center;
          .model-tab {
            background-color: #EFEEEE;
            color: #666666;
            height: 32px;
            line-height: 32px;
            display: inline-block;
            font-size: 12px;
            border-radius: 6px;
            margin-right: 12px;
            font-weight: 500;
            flex-shrink: 0;
            position: relative;
            .item-tab {
              padding: 0 16px;
              cursor: pointer;
              height: 32px;
              display: inline-block;
            }
            .active-model-tab {
              background-color: #0052D9;
              color: white;
              border-radius: 6px;
            }
            .custom_tips_popover {
              background: #3C6EE0;
              border-radius: 6px;
              color: #fff;
              padding: 16px 18px 16px 16px;
              position: absolute;
              min-width: 236px;
              top: 40px;
              right: -75px;
              z-index: 999;
              .el-icon-caret-top {
                color: #3C6EE0;
                position: absolute;
                right: 118px;
                top: -7px;
              }
              .custom-popover-content {
                line-height: 22px;
                font-size: 14px;
                .el-icon-close {
                  font-size: 16px;
                  margin-left: 18px;
                  cursor: pointer;
                }
              }
            }
          }
        }
        .top-info {
          display: flex;
          align-items: center;
          color: #777777;
          // padding-bottom: 16px;
          margin-bottom: 10px;
          // border-bottom: 1px solid #EEEEEE;
          i {
            display: inline-block;
            margin-right: 4px;
            width: 16px;
            height: 16px;
          }
          .top-iconInfo {
            margin-right: 40px;
            flex-shrink: 0;
            display: flex;
            align-items: center;
            height: 20px;
            .icon-view {
              background: url("~@/assets/img/view.png") no-repeat center/cover;
            }
            .time {
              margin-left: 16px;
            }
          }
        }

        .video-main {
          position: relative;
          display: flex;
          :deep(.video-box) {
            border-radius: 4px;
            overflow: hidden;
            border: unset;
            box-shadow: 0 4px 12px #00000033;
            .video-js {
              // background-color: #fff;
              // background: url("~@/assets/img/audioPlay.png") no-repeat;
              background-size: 100%;
            }
          }

          .current-time-tips {
            position: absolute;
            left: 10px;
            bottom: 50px;
            background: #0F1010;
            color: #fff;
            height: 26px;
            line-height: 26px;
            font-size: 14px;
            display: flex;
            align-items: center;
            padding: 0 5px;
            border-radius: 2px;

            .el-icon-close {
              margin-right: 5px;
              cursor: pointer;
              font-size: 16px
            }

            .tips-btn {
              cursor: pointer;
              color: #3464e0;
              margin-left: 5px;
            }
          }

          .interactive-dialog {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            top: 0;
            background: #00000080;
          }
        }
        .no-video-box {
          .item-image {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;

            .default-icon-picture {
              display: inline-block;
              width: 400px;
              height: 300px;
              background: url('~@/assets/img/default_bg_img.png') no-repeat;
            }
          }
        }
        .user-operat-box {
          margin-top: 10px;
          // border-top: 1px solid #EEEEEE;
          // padding-top: 20px;
          color: #777777;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .operat-left, .operat-right {
            display: flex;
          }
          .item-operat {
            background-color: #F5F5F7;
            padding: 0 16px;
            margin-right: 16px;
            border-radius: 24px;
            height: 32px;
            line-height: 32px;
            display: flex;
            align-items: center;
            cursor: pointer;
            i {
              display: inline-block;
              width: 16px;
              height: 16px;
              margin-right: 4px;
            }
            .mid-line {
              margin-left: 12px;
              margin-right: 12px;
              color: #D9D9D9;
            }
          }
          .active-isCollect-operat {
            background: #F2F8FF;
            color: #0052D9;
            .icon-collect {
              background: url("~@/assets/img/active-fav.png") no-repeat center /cover;
            }
          }
          .white-note-btn {
            flex-shrink: 0;
            i {
              width: 20px;
              height: 20px;
              background: url("~@/assets/img/white-icon.png") no-repeat center /cover;
            }
          }
          .shore-operat:hover {
            background: #F2F8FF;
            color: #0052D9;
            .icon-share {
              background: url("~@/assets/img/share-hover.png") no-repeat center /cover;
            }
          }
          .add-operat:hover {
            background: #F2F8FF;
            color: #0052D9;
            .icon-add {
              background: url("~@/assets/img/active-add.png") no-repeat center / cover;
            }
          }
          .icon-zan-active {
            background: #F2F8FF;
            color: #0052D9;
            i {
              background: url("~@/assets/img/zan1-active.png") no-repeat center /cover;
            }
          }
          .white-note-btn:hover {
            background: #F2F8FF;
            color: #0052D9;
            i {
              background: url("~@/assets/img/white-icon.png") no-repeat center /cover;
            }
          }
          .active-appreciate-operat {
            background: #F2F8FF;
            color: #0052D9;
            i {
              background: url("~@/assets/img/do-admire-active.png") no-repeat center /cover;
            }
          }
          .icon-comment {
            background: url("~@/assets/img/comment.png") no-repeat center /cover;
          }
          .icon-zan {
            background: url("~@/assets/img/zan1.png") no-repeat center /cover;
          }
          .icon-share {
            background: url("~@/assets/img/share.png") no-repeat center /cover;
          }
          .icon-collect {
            background: url("~@/assets/img/fav2.png") no-repeat center /cover;
          }
          .icon-add {
            background: url("~@/assets/img/add.png") no-repeat center / cover;
          }
          .more-btn {
            background: url("~@/assets/img/more-line.png") no-repeat center /cover;
          }
          .icon-appreciate {
            background: url("~@/assets/img/icon_re.png") no-repeat center /cover;
          }
          .jf-tip {
            color: #ff7548;
            position: absolute;
            right: 20px;
            top: -22px;
            display: flex;
            align-items: center;
          }
          .jf-icon {
            background: url("~@/assets/img/integral-icon.png") no-repeat center / cover;
            display: block;
            width: 20px;
            height: 20px;
            margin-right: 4px;
          }
        }
      }

      .content-bottom {
        margin-top: 20px;
        border-radius: 8px;
        .subtitle-summary-main {
          background-color: #fff;
          padding: 16px 24px 24px;
        }
      }

      :deep(.el-tabs) {
        background-color: #fff;
        padding: 16px 24px 0 24px;
        border-radius: 8px 8px 0 0;

        .el-tabs__header {
          border-bottom: solid 1px #eeeeee;
          margin: 0px;
        }

        .el-tabs__item {
          color: rgba(0, 0, 0, 0.4);
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 10px
        }

        .is-active {
          color: #0052D9 !important;
          font-weight: 700;
        }
        .el-tabs__nav-wrap::after {
          height: 1px;
          background-color: #EEEEEE;
        }
      }
    }
  }
  .move-area {
    width: 320px;
    height: 180px;
    position: fixed;
    bottom: 355px;
    box-shadow: 0 4px 12px #00000033;
    border-radius: 8px;
    overflow: hidden;
    cursor: move;
    right: 50%;
    z-index: 99;
    transform: translateX(707px);
    @media screen and (max-width: 1660px) {
      transform: translateX(587px);
    }
    .custom-video {
      width: 320px;
      height: 180px;
      position: absolute;
      :deep(.picture-video-box) {
        width: 320px;
        height: 180px;
        border: unset;
        .video-js {
          // background-color: #fff;
          // background: url("~@/assets/img/audioPlay.png") no-repeat;
          background-size: 100%;
        }
        .vjs-big-play-button, .vjs-menu-button {
          display: none;
        }
        .vjs-volume-panel {
          position: absolute;
          right: 12px;
        }
      }
    }
    .out-mask {
      width: 320px;
      height: 180px;
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      .close-icon {
        position:absolute;
        top: 12px;
        right: 12px;
        background: url('~@/assets/img/video-bc-close.png') no-repeat center/cover;
        width: 24px;
        height: 24px;
        display: inline-block;
        z-index: 999;
        cursor: pointer;
      }
      .close-icon:hover {
        background: url('~@/assets/img/video-hover-close.png') no-repeat center/cover;
      }
      .play-icon {
        width: 80px;
        height: 80px;
        opacity: 0;
        cursor: pointer;
      }
    }
    .out-mask:hover {
      background: #00000033;
      border-radius: 8px;
      .play-icon {
        opacity: 1;
        transition: opacity 1s ease-out;
      }
    }
  }
}

@media screen and (max-width: 1660px) {
  .contain-main .left {
    width: 760px;
    .tag-list-box {
      width: 466px;
    }
    .video-box,
    .scorm-box {
      width: 711px;
      height: 401px;
    }
  }
  .article-fullen-main .left {
    width: 1180px;
  }
}

@media screen and (min-width: 1661px) {
  .contain-main .left {
    width: 1000px;
    .tag-list-box {
      width: 695px;
    }
    .video-box,
    .scorm-box {
      width: 951px;
      height: 536px;
    }
    .chapter-preview-box {
      width: 952px;
    }
  }
  .article-fullen-main .left {
    width: 1420px;
  }
}

</style>
