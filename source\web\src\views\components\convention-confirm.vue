<template>
  <div class="convention-confirm">
    <el-checkbox v-model="localChecked" @change="conventionChange">
      <p class="content">我已阅读并同意<a href="javascript:;" class="link" @click="goConvention">《腾讯学堂学习平台文明公约》</a>，自愿遵守相关约定。</p>
    </el-checkbox>
  </div>
</template>

<script>
import env from 'config/env.conf.js'
export default {
  name: 'conventionConfirm',
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      localChecked: this.value
    }
  },
  methods: {
    goConvention() {
      let envName = env[process.env.NODE_ENV]
      let url = `${envName.commonPath}common/convention.html`
      window.open(url, '_blank')
    },
    conventionChange() {
      this.$emit('input', this.localChecked)
    }
  }
}
</script>

<style lang="less" scoped>
.convention-confirm {
  .content {
    font-size: 14px;
    color: #000000e6;
  }
  .link {
    color: #266FE8;
    text-decoration: underline;
  }
}
</style>
