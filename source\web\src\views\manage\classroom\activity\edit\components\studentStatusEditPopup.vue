<template>
  <div class="student-status-edit-popup activity-common">
    <el-dialog :visible.sync="visible" width="800px" :close-on-click-modal="false" :before-close="handlerClose">
      <div slot="title" class="dialog-header">{{ dialogTitle }}</div>
      <div class="dialog-content">
        <div class="title-row">请选择此批学员修改后的状态<span class="serious-tips ml-24">未选中任意学员，请在学员列表中勾选学员后，再进行次操作</span></div>
        <div class="form-item" v-if="type === 'regist'">
          <span class="form-item-label">报名状态</span>
          <div class="form-item-content" >
            <el-checkbox-group v-model="reg_status" size="small" @change="handlerCheckboxChange($event, 'reg_status')">
              <el-checkbox v-for="item in registStatus" :label="item.value" :key="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </div>
        </div>
        <div class="form-item" v-if="type === 'attendance'">
          <span class="form-item-label">考勤状态</span>
          <div class="form-item-content">
            <div class="flex-row flex-a-start flex-j-between">
              <el-checkbox-group v-model="status" size="small" class="mr-12" @change="handlerCheckboxChange($event, 'status')">
                <el-checkbox v-for="item in signInStatus" :label="item.value" :key="item.value">{{ item.label }}</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button class="cancel-btn" @click="handlerClose" size="small">取消</el-button>
        <el-button class="confirm-btn" type="primary" @click="handlerConfirm" size="small">提交</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { updateActivityStudentStatusApi } from '@/config/classroom.api.conf.js'

export default {
  name: 'studentStatusEditPopup',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // regist: 报名 attendance: 考勤
    configData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      registStatus: [
        { label: '已报名', value: 0 },
        { label: '未报名霸课', value: 6 },
        { label: '排队候补', value: 2 },
        // { label: '待上级审核', value: 3 },
        { label: '审核未通过', value: 11 },
        { label: '已注销报名', value: 1 }
      ],
      signInStatus: [
        { label: '暂无数据', value: -1 },
        { label: '全勤', value: 4 },
        { label: '缺勤', value: 5 },
        { label: '部分缺勤', value: 18 },
        { label: '临时取消', value: 19 }
      ],
      submitForm: {
        reg_status: 0,
        status: 4
      },
      reg_status: [0],
      status: [4],
      selectStudentIds: []
    }
  },
  watch: {
    configData: {
      handler(val) {
        if (val.type) {
          this.type = val.type
          this.selectStudentlist = val.list
        }
      },
      immediate: true
    }
  },
  computed: {
    dialogTitle() {
      return this.configData.type === 'regist' ? '修改报名状态' : '修改考勤状态'
    },
    activityId () {
      return this.$route.query.activity_id
    }
  },
  created() { },
  mounted() { },
  beforeDestroy() { },
  methods: {
    handlerCheckboxChange(e, name) {
      if (!e.length) {
        this[name] = [this.submitForm[name] * 1]
      }
      if (e.length > 1) {
        let val = [e[e.length - 1]]
        this[name] = val
        this.submitForm[name] = val.join()
      }
    },
    handlerClose() {
      this.$emit('update:visible', false)
    },
    handlerConfirm() {
      let att_ids = this.selectStudentlist.map(v => v.att_id)
      const params = {
        class_id: this.activityId,
        act_type: '4',
        att_ids
      }
      this.type === 'regist' && (params.reg_status = this.submitForm.reg_status)
      this.type === 'attendance' && (params.status = this.submitForm.status)
      
      updateActivityStudentStatusApi(params).then(res => {
        let msg = this.type === 'regist' ? '修改报名状态成功' : '修改考勤状态成功'
        this.$message.success(msg)
        this.handlerClose()
        this.$emit('success', true)
      })
    }
  }
}
</script>

<style lang="less" scoped>
  @import '~assets/css/activity-common.less';
  .student-status-edit-popup {
    .title-row {
      color: #000000cc;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 500;
      line-height: 22px;
      margin-bottom: 24px;
      .serious-tips {
        color: #e34d59;
        display: inline-block;
      }
    }
  }
</style>
