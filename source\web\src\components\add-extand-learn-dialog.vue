<template>
  <div>
    <el-dialog :visible.sync="visibleShow" width="960px" height="618px" top="50px" :title="title" append-to-body
      custom-class="add-extand-learn-dialog dialog-center" :show-close="true" :close-on-click-modal="false"
      @close="cancel">
      <div class="content-add">
        <el-tabs v-model="source_from" @tab-click="tabChange">
          <el-tab-pane label="自定义内容" name="first" v-if="showFirst"></el-tab-pane>
          <el-tab-pane label="本站内容" name="second"></el-tab-pane>
        </el-tabs>
        <div class="source-from" v-if="source_from === 'first'">
          <el-form :model="customForm" :rules="customRules" class="custom-form" label-width="100px"
            label-position="right">
            <el-form-item label="内容类型：" prop="content_module_name">
              <el-input class="width600" v-model="customForm.content_module_name" placeholder="请输入内容类型" maxlength="10"
                show-word-limit></el-input>
            </el-form-item>
            <el-form-item label="标题：" prop="content_name">
              <el-input class="width600" v-model="customForm.content_name" placeholder="请输入内容标题" maxlength="50"
                show-word-limit></el-input>
            </el-form-item>
            <div>
              <el-form-item label="跳转链接：" prop="href">
                <el-input class="width600" v-model="customForm.href" placeholder="请输入链接" maxlength="500"
                  show-word-limit></el-input>
                <el-link class="test-link" type="primary" :underline="false" @click="urlTest()">测试链接</el-link>
              </el-form-item>
            </div>
            <el-form-item label="上传图片：" prop="content_cover_img_id" class="upload-image">
              <!-- <uploadImg
                  :file_id.sync="customForm.content_cover_img_id"
                  :fileList.sync="photoUrl"
                  :limit_height="175"
                  :limit_width="262"
                  typeClass=""
                ></uploadImg> -->
              <cut-img-upload ref="upload" @handleSuccess="handleSuccessImage" :dialogImageUrl="uploadObj.cover_image"
                :autoImgUrl="uploadObj.cover_image_id" @handleClearImg="handleClearImg" @handleImgEdit="handleImgEdit"
                :cover_imgage_storage_type="uploadObj.cover_imgage_storage_type">
                <!-- <template v-slot:text><p>建议图片尺寸：262*175px(15 : 10)图片小于500k</p></template> -->
                <template v-slot:text>
                  <p>建议图片尺寸：360*240px或3:2</p>
                </template>
                <template v-slot:createImg>
                  <p class="text-orange" style="display: flex; align-items: center" @click="handleAutoImg"><img class="icon" style="width:16px; height: 16px; margin-right: 4px" src="~@/assets/img/tips.png" alt="">快速生成封面图</p>
                </template>
              </cut-img-upload>
            </el-form-item>
          </el-form>
        </div>
        <div class="source-from qlcontent" v-else>
          <div class="contentSearch">
            <div class="condition">
              <div class="condition-item">
                <label>内容名称</label>
                <el-input v-model="qlcontentForm.keywords" placeholder="请输入课程名称"></el-input>
              </div>
              <div class="condition-item">
                <label>内容类型</label>
                <el-select v-model="qlcontentForm.moduleId" placeholder="请选择内容类型">
                  <el-option label="全部" value=""></el-option>
                  <el-option v-for="item in contentModuleTypes" :key="item.module_id" :label="item.module_name"
                    :value="item.module_id">
                  </el-option>
                </el-select>
              </div>
            </div>
            <div class="btns">
              <div @click="handleReset" class="reset"><i class="el-icon-refresh"></i><span>重置</span></div>
              <el-button @click="qlContentSearch" class="search" type="primary" size="small">搜索</el-button>
            </div>
          </div>
          <el-table :data="qlsearchContentTable" :header-cell-style="{ background: '#eef1f6' }" class="content-table"
            align="left" max-height="300" :row-key="setRowKeys" @selection-change="qlsearchSelectionChange">
            <el-table-column type="selection" :selectable="selectDisabled" :reserve-selection="true" width="55">
            </el-table-column>
            <el-table-column label="内容标题" prop="content_name" show-overflow-tooltip></el-table-column>
            <el-table-column label="内容类型" prop="module_name" width="150px"></el-table-column>
            <el-table-column label="是否已添加" prop="dup" width="150px">
              <template v-slot="prop">
                <span :style="{ color: prop.row.dup == 1 ? 'red' : '' }">
                  {{ prop.row.dup == 1 ? '已添加' : '-' }}
                </span>
              </template>
            </el-table-column>
            <el-table-column label="内容链接" prop="content_url" show-overflow-tooltip>
              <template v-slot="prop">
                <a class="content-url" target="_blank" :href="prop.row.content_url">{{ prop.row.content_url }}</a>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination @current-change="qlContentSearch" :current-page.sync="qlcontentForm.pageNum"
            :page-size="qlcontentForm.pageSize" layout="->, total, prev, pager, next, jumper"
            :total="qlcontentForm.pageTotal">
          </el-pagination>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel" size="small">取消</el-button>
        <el-button type="primary" @click="complete" size="small">添加</el-button>
      </div>
    </el-dialog>
    <!-- 一键生成封面图弹窗组件 -->
    <sdc-img-cover ref="sdcImgCoverRefExtand" :visible.sync="autoImgCoverShow" :imgInfo="imgInfo"
      @handleImgCoverOk="handleImgCoverOk">
    </sdc-img-cover>
  </div>
</template>
<script>
import { contentModuleTypes } from '@/utils/constant'
// import { UploadImg } from '@/components/index'
import { CutImgUpload } from '@/components/index'
import { getSearchsite, getGraphicSearchsite } from 'config/api.conf'
export default {
  name: 'AddExtandLearnDialog',
  components: {
    // UploadImg,
    CutImgUpload
  },
  props: {
    title: {
      type: String,
      default: '新增内容'
    },
    showFirst: {
      type: Boolean,
      default: true
    },
    checkedData: {
      type: Object
    },
    page: {
      type: String,
      default: ''
    },
    prodData: {
      type: Object,
      default: () => {
        return {
          prod_id: '', // 图文id,或者网课id等
          prod_type: '' // module_id
        }
      }
    }
  },
  watch: {
    prodData: {
      handler(newV) {
        const { prod_id, prod_type } = newV
        this.customForm.prod_id = prod_id ? parseInt(prod_id) : ''
        this.customForm.prod_type = prod_type
      },
      immediate: true
    }
  },
  data() {
    return {
      visibleShow: true,
      source_from: this.showFirst ? 'first' : 'second',
      // 自定义内容
      customForm: {
        prod_type: '',
        prod_id: '', // 图文的id，每五分钟自动保存后生成，若没有提前调取保存预览
        content_module_id: 99,
        content_module_name: '',
        content_item_id: '', // 自定义是没有值，本站内容是列表数据的item_id值
        content_type: 0, // 1：站内内容 2:自定义内容
        content_name: '',
        href: '',
        content_cover_img_id: ''
      },
      customRules: {
        content_module_name: [{ required: true, message: ' ', trigger: 'blur' }],
        content_name: [{ required: true, message: ' ', trigger: 'blur' }],
        href: [{ required: true, message: ' ', trigger: 'blur' }],
        content_cover_img_id: [{ required: true, message: ' ', trigger: 'blur' }]
      },

      // 本站内容
      qlcontentForm: {
        keywords: '',
        moduleId: '',
        pageSize: 5,
        pageNum: 1,
        pageTotal: 0
      },
      qlcontentCategoryOptions: [],
      contentModuleTypes, // 模块类型
      qlsearchContentTable: [], // 搜索的内容
      qlsearchContentSelect: [], // 勾选的内容

      importContentTable: [],
      contentTableHead: [
        { label: '内容标题', prop: 'content_name' },
        { label: '内容类型', prop: 'module_name' },
        { label: '内容链接', prop: 'content_url' }
      ],
      photoUrl: [],
      autoImgCoverShow: false, // 一键封面参数
      imgInfo: {},
      uploadObj: {
        cover_image: '',
        cover_image_id: '',
        cover_imgage_storage_type: ''
      }
    }
  },
  inject: ['handleCreateGraghic'],
  created() {
    if (!this.showFirst) {
      this.qlContentSearch()
    }
  },
  methods: {
    handleAutoImg() {
      this.autoImgCoverShow = true
      this.imgInfo = {
        title: this.customForm.content_name,
        env: process.env.NODE_ENV === 'production' ? 'production' : 'test',
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        showEdit: true
      }
    },
    // 图片编辑
    handleImgEdit() {
      this.$refs.sdcImgCoverRefExtand.outEdit({
        ...this.coverImgBackData,
        stamp: this.$store.state.userInfo.staff_name, // 当前用户名
        type: 'outEdit'
      })
    },
    // 一键封面回传
    handleImgCoverOk(row) {
      this.customForm.content_cover_img_id = row.url
      this.uploadObj.cover_image_id = row.url

      // 清空裁剪封面
      this.uploadObj.cover_image = ''
      this.uploadObj.cover_imgage_storage_type = 'zhihui'
      this.coverImgBackData = {
        id: row.id
      }
    },
    // 内容中心图片回传
    handleSuccessImage(url) {
      this.customForm.content_cover_img_id = url
      this.uploadObj.cover_image = url
      // 清空一键生成封面
      this.uploadObj.cover_image_id = ''
      this.uploadObj.cover_imgage_storage_type = 'contentcenter'
    },
    // 清空图片
    handleClearImg() {
      this.customForm.content_cover_img_id = ''
      this.uploadObj.cover_image = ''
      this.uploadObj.cover_image_id = ''
    },
    complete() {
      this.customForm.prod_id = parseInt(this.graphic_id)
      let data = []
      // 延伸学习添加自定义内容
      if (this.source_from === 'first') {
        if (!this.customForm.content_module_name) return this.$message.error('请输入内容类型')
        if (!this.customForm.content_name) return this.$message.error('请输入内容标题')
        const reg = /(http|https):\/\/([\w.]+\/?)\S*/
        if (!this.customForm.href) return this.$message.error('请输入链接')
        else if ((this.customForm.href && !reg.test(this.customForm.href))) return this.$message.error('原文链接请输入http://或https://开头的链接地址')
        if (!this.customForm.content_cover_img_id) return this.$message.error('请上传图片')
        this.customForm.content_type = 2
        data = new Array(this.customForm)
      } else {
        this.customForm.content_type = 1

        // 延伸学习添加本站内容
        if (!this.showFirst && this.checkedData) this.qlsearchContentSelect = this.qlsearchContentSelect.concat([this.checkedData])
        if (this.qlsearchContentSelect.length === 0) return this.$message.error('请搜索并勾选内容')
        else {
          if (!this.showFirst && this.qlsearchContentSelect.length > 1) return this.$message.error('只能选择一条内容')
          if (!this.showFirst && this.qlsearchContentSelect[0]?.prod_id && this.checkedData) data = [this.checkedData]
          else {
            this.qlsearchContentSelect.map(e => {
              const item = {
                prod_type: this.customForm.prod_type,
                prod_id: this.customForm.prod_id,
                content_module_id: e.module_id,
                content_module_name: e.module_name,
                content_item_id: e.item_id,
                content_created_time: e.created_at,
                content_type: this.customForm.content_type,
                content_name: e.content_name,
                href: e.content_url,
                content_cover_img_id: e.cover_img_file_id,
                view_count: e.view_count_uv,
                avg_score: e.avg_score

              }
              data.push(item)
            })
          }
        }
      }
      if (!this.showFirst) {
        this.$emit('closeAddExDialog', data)
      } else {
        const row = data && data.length > 0 ? data[0] : null
        if (row.content_type === 2) {
          const setData = {
            'act_type': 99, // 对应的业务类型
            'columns': [
              { 
                'column_code': 'content_name', // 请求接口的json对象的字段名，如果是文件类的字段上传该字段为文件地址的字段名
                'column_name': '标题', // 字段对应的页面字段名称
                'column_type': 'text', // 字段的类型，审核支持的类型枚举见下面的“column_type枚举”
                'call_type': ['sync'], // 调用类型，枚举为 sync(同步调用)、async(异步调用),如果同时都有需要先按类型分组，优先执行同步的接口” 
                'manual_review': false // 是否接入人工审核，一般情况下同步就没有人工审核
              },
              {
                'column_code': 'content_cover_img_id',
                'column_name': '上传图片',
                'column_type': 'image',
                'call_type': ['sync'],
                'manual_review': false
              }
            ]
          }
          window.$informationReview.contentReview(row, setData).then((res) => {
            if (res.status_code !== 1) {
              this.closeDialog(data)
            }
          })
          return
        }
        this.closeDialog(data)
      }
    },
    closeDialog(data) {
      let extandContents = JSON.parse(sessionStorage.getItem('extend_contents'))
      if (extandContents && extandContents.length > 0) {
        sessionStorage.setItem('extend_contents', JSON.stringify(data.concat(extandContents)))
      } else {
        sessionStorage.setItem('extend_contents', JSON.stringify(data))
      }
      this.$emit('closeAddExDialog', data)
      this.handleCreateGraghic()
    },
    // 本站内容搜索
    qlContentSearch() {
      const { keywords, moduleId, pageNum, pageSize } = this.qlcontentForm
      const data = {
        keywords,
        moduleId,
        pageNum,
        pageSize
      }
      const api = this.page === 'graphic' ? getGraphicSearchsite : getSearchsite
      api(data).then(res => {
        this.tableLoading = false
        this.qlsearchContentTable = res.records
        this.qlcontentForm.pageTotal = res.total
      })
    },
    qlsearchSelectionChange(val) {
      this.qlsearchContentSelect = val
    },
    urlTest() {
      const reg = /(http|https):\/\/([\w.]+\/?)\S*/
      if (!this.customForm.href) return this.$message.error('请输入链接')
      else if ((this.customForm.href && !reg.test(this.customForm.href))) return this.$message.error('原文链接请输入http://或https://开头的链接地址')
      window.open(this.customForm.href)
    },
    cancel() {
      this.visibleShow = false
      if (!this.showFirst && this.checkedData) this.$emit('closeAddExDialog', [this.checkedData])
      else this.$emit('closeAddExDialog')
    },
    tabChange() {
      if (this.source_from !== 'first') {
        this.qlContentSearch()
      }
    },
    handleReset() {
      this.qlcontentForm.keywords = ''
      this.qlcontentForm.moduleId = ''
      this.qlcontentForm.pageSize = 5
      this.qlcontentForm.pageNum = 1
      this.qlContentSearch()
    },
    setRowKeys(item) {
      return item.item_id
    },
    selectDisabled(row) {
      // 已选中不可选
      let handleData = []
      if (!this.showFirst) {
        handleData = this.checkedData ? [this.checkedData] : []
      } else {
        handleData = JSON.parse(sessionStorage.getItem('extend_contents'))
      }
      if (handleData && handleData.length > 0) {
        let retFlag = true
        handleData.map(e => {
          if (row.item_id === e.content_item_id) retFlag = false
        })
        return retFlag
      } else {
        return true
      }
    }
  }
}
</script>

<style lang="less" scoped>
.content-add {
  :deep(.el-dialog__title) {
    color: rgba(0, 0, 0, 0.9);
    font-size: 16px;
    font-weight: 500;
  }

  :deep(.el-form-item__error) {
    top: 93%;
    left: 10px;
  }

  .contentSearch {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 7px;
    margin-bottom: 16px;

    .condition {
      display: flex;

      .condition-item {
        margin-right: 36px;

        label {
          margin-right: 10px;
          color: rgba(0, 0, 0, 0.6);
        }
      }
    }

    :deep(.el-input, .el-select) {
      width: 230px !important;
    }

    .btns {
      display: flex;

      .reset,
      .search {
        width: 80px;
        height: 32px;
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .reset {
        cursor: pointer;
        margin-right: 20px;
        color: #0052D9;
        border: solid 1px #0052D9;
        border-radius: 3px;

        i {
          margin-right: 4px;
        }
      }
    }
  }

  .content-table {
    .content-url {
      color: #0052D9;
    }
  }

  .test-link {
    margin-left: 10px;
  }
}
</style>
