<template>
  <el-dialog 
  custom-class="information-safety-dialog dialog-center none-border-dialog" 
  title="提示" 
  :visible.sync="isShow"
  width="480px" 
  top="-150px" 
  :show-close="false" 
  :close-on-click-modal="false"
  >
    <span slot="title" class="custom-title">
      <span class="icon"></span>
      <span>提示</span>
    </span>
    <div class="project-body">
      <p class="tips">内容将提交后台审核，前端页面不可访问，请耐心等待，留意企微“小腾老师”机器人消息提醒。</p>
      <p class="tips">如有疑问，请联系graywu。</p>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel" size="small">取 消</el-button>
      <el-button type="primary" @click="confirm()" size="small">继续提交</el-button>
    </span>
  </el-dialog>
</template>
<script>
export default {
  props: {
    isShow: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
    }
  },
  methods: {
    cancel() {
      this.$emit('update:isShow', false)
    },
    confirm() {
      this.cancel()
      this.$emit('safeConfirm')
    }
  }
}
</script>
<style lang="less" scoped>
.information-safety-dialog {
  .custom-title {
    display: flex;
    align-items: center;
  }
  .icon {
    display: inline-block;
    width: 24px;
    height: 24px;
    background: url('~@/assets/img/tips-icon.png') no-repeat center/cover;
    margin-right: 8px;
  }
  .tips {
    font-size: 14px;
    color: #ED7B2F;
    line-height: 22px;
    padding-right: 20px;
  }
}
</style>
