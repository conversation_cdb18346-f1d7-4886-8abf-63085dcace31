<template>
  <van-popup class="copy-pop" v-model="show" closeable position="bottom">
    <div class="re-title">
      已邀请的同事
    </div>
    <div class="share-content">
      <div class="tab-content_item" v-for="(item, index) in presentRecordList" :key="index">
        <div class="tab-content_item-left"><img :src="userImgUrl(item.staff_name)" alt=""></div>
        <div class="tab-content_item-right">
          <div class="name">邀请对象：{{item.staff_name}}</div>
          <div class="number1">邀请时间：{{createTime(item.created_at)}}</div>
        </div>
      </div>
      <div class="loading-moer" @click="handlerLoadingMore" v-if="total > presentRecordList.length">点击加载更多</div>
    </div>
  </van-popup>
</template>

<script>
import { mapState } from 'vuex'
import moment from 'moment'
export default {
  props: {
    isShow: {
      type: Boolean,
      default: false
    },
    total: {
      type: Number,
      default: 0
    },
    presentRecordList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      qrUrl: ''
    }
  },
  mounted() {},
  computed: {
    ...mapState(['userInfo']),
    show: {
      set(val) {
        this.$emit('update:isShow', val)
      },
      get() {
        return this.isShow
      }
    },
    userImgUrl() {
      return (val) => {
        if (!val) return ''
        let staffname = val.split(',')[0].split('(')[0]
        return `https://rhrc.woa.com/photo/150/${staffname}.png`
      }
    },
    createTime() {
      return (date) => {
        return moment(date).format('YYYY-MM-DD')
      }
    }
  },
  methods: {
    handlerLoadingMore() {
      this.$emit('handlerLoadingMore')
    }
  }
}
</script>

<style lang="less" scoped>
/deep/.van-popup__close-icon {
  color: #000;
  top: 18px;
}
.copy-pop {
  border-radius: 12px 12px 0 0;
}
.red {
  color: red;
}
.re-title {
  color: #000000e6;
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  line-height: 26px;
  padding: 16px;
}
.share-content {
  padding: 0 28px;
  margin-bottom: 16px;
  overflow: auto;
  height: 430px;
  .loading-moer {
    padding-top: 10px;
    line-height: 22px;
    text-align: center;
    color: #0052d9ff;
  }
  .tab-content_item {
    display: flex;
    padding: 20px 12px;
    border-radius: 8px;
    border: 0.5px solid #eee;
    margin-top: 12px;
    background: linear-gradient(
      0deg,
      #fdffff 36.9%,
      #fbfeff 77.92%,
      #f9fffe 100%
    );
    &-left {
      margin-right: 6px;
      img {
        width: 40px;
        height: 40px;
        border-radius: 50%;
      }
    }
    &-right {
      margin-left: 16px;
      line-height: 20px;
      font-size: 12px;
      overflow: hidden;
      .name {
        display: block;
        color: #333333ff;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .number1 {
        align-self: stretch;
        color: #666666ff;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
      .number2 {
        color: #999999ff;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
      }
    }
  }
}
</style>
