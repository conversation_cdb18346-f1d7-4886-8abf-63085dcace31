<template>
    <div class="table-specialList-content">
        <el-tag v-for="(tag, index) in specialList" :key="index" :type="tag.type" :closable="close"  @close="cancelSubs(tag, index)" class="clicked-tag custom-tag margin-16" size="small" slot="reference">
            <el-popover
                placement="bottom-start"
                width="232"
                v-model="tag.visible"
                trigger="hover">
                <span slot="reference">{{tag.label_name}}</span>
                <article class="specialInfo">
                    <p>{{tag.label_name}}</p>
                    <div v-html="stripHtmlTagsButKeepCertainTags(tag.desc) || '暂无专区简介'" :title="stripHtmlTagsButKeepCertainTags2(tag.desc)"></div>
                    <section class="btns">
                        <el-button type="primary" class="cancelSubs" size="small" @click="tag.visible=false,cancelSubs(tag, index)">取消订阅</el-button>
                        <el-button type="primary" class="content" plain size="small" @click="specailJump(tag.label_jump_url, tag.label_id)">查看专区内容</el-button>
                    </section>
                </article>
            </el-popover>
        </el-tag>
    </div>
</template>
<script>
import specailJump from '../specailJump'
export default {
  props: {
    deitailInfo: {
      type: Boolean,
      default: false
    },
    specialList: {
      type: [],
      default: () => []
    },
    // X 关闭按钮
    close: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editable: {},
      isPopover: false,
      imgeuesr: require('../assets/img/usergroup.png'),
      imgeuesrto: require('../assets/img/usergroup (1).png')
    }
  },
  mixins: [specailJump],
  computed: {
    httpHost() {
      let httpHost = ''
      if (process.env.NODE_ENV === 'production') {
        httpHost = window.location.hostname.endsWith('.woa.com')
          ? '//portal.learn.woa.com'
          : '//portal.learn.oa.com'
      } else {
        httpHost = window.location.hostname.endsWith('.woa.com')
          ? '//test-portal-learn.woa.com'
          : '//test.portal.learn.oa.com'
      }
      return httpHost
    }
  },
  methods: {
    cancelSubs(tag, index) {
      this.$emit('cancelSubs', tag, 2, 2, index)
    }
  }
}
</script>
<style lang="less" scoped>
  .table-specialList-content {
    display: inline-block;
  }
  .margin-16 {
    margin-right: 16px;
  }
  .custom-tag {
    cursor: pointer;
    padding: 0 8px;
    margin-top: 15px;
    background: #f7f8fa;
    margin-right: 20px; /* 设置标签之间的间距 */
    // border-radius: 0 15px 15px 0; /* 添加右边的半圆效果 */
    color: #00000099;
    font-family: 'PingFang SC';
    font-size: 12px;
    font-style: normal;
    border: 1px solid #dcdcdc;
    // min-width: 0px;
    img {
      height: 16px;
      display: inline-block;
      vertical-align: middle;
      margin-bottom: 2px;
      margin-right: 5px;
    }
    /deep/ .el-tag__close {
      color: #0052d9;
      font-size: 16px;
    }
    /deep/ .el-tag__close:hover {
      background-color: #f5f7f9; /* 你想要的颜色 */
      color: #0052d9;
    }
  }
  .clicked-tag {
    margin-top: 15px;
    border-color: #0052d9;
    border: 1px solid #0052d9 !important;
    background: #f2f3ff !important;
    color: #0052d9 !important;
    font-weight: 400;
    // background: var(--brand-brand-1-light, #ECF2FE);
  }
  .labelDetailBox {
    > header {
      h4 {
        font-size: 14px;
        font-size: 12px;
      }
      a {
        color: #00000042;
        line-height: 20px;
        font-size: 12px;
      }
    }
    .labelDetailBox-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    .p1,
    .p2 {
      color: #00000066;
      margin-top: 6px;
      line-height: 20px;
      font-size: 12px;
      font-weight: 400;
      font-family: 'PingFang SC';
      font-style: normal;
    }
    .p2 {
      color: #00000099;
      align-self: stretch;
    }
    footer {
      margin-top: 6px;
      /deep/.el-button {
        padding: 2px 7px !important;
        line-height: 20px !important;
      }
      .el-button--danger.is-plain {
        background: none;
        color: #d54941;
        border-color: #d54941;
      }
      .el-button--danger.is-plain:hover {
        background: none;
        color: #d54941;
      }
    }
    .label-specialList {
      color: #000000e6;
      font-family: 'PingFang SC';
      font-size: 14px;
      font-style: normal;
      font-weight: 600;
      line-height: 22px;
    }
  }
  .specialInfo{
    p{
        line-height: normal;
        font-family: "PingFang SC";
        color: #333333;
        font-size: 14px;
        font-weight: 500;
    }
    div{
        // height: 48px;
        margin-top: 8px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 3;
        align-self: stretch;
        overflow: hidden;
        color: #777777;
        text-overflow: ellipsis;
        font-family: "PingFang SC";
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }
    .btns{
        margin-top: 8px;
        button{
            width: 86px;
            height: 24px;
            padding: 3px 0px;
            border-radius: 4px;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
        }
        button.subs{
            background: #006FFF;
            border-color: #006FFF;
        }
        button.cancelSubs{
            background: none;
            color: #D54941;
            border-color: #D54941;
        }
        button.content{
            background: none;
            color: #006FFF;
            border-color: #006FFF;
        }
        button + button{
            margin-left: 32px;
        }
    }
  }
</style>
