<template>
  <div class="activity-steps-container">
    <div class="activity-steps-container-header-left">
      创建活动
    </div>
    <div class="activity-steps-container-header-right">
      <div class="activity-steps-container-header-right-item" :class="{ active: currentStep === item.stepIndex }" @click="handleStepClick(item.stepIndex)" v-for="item in stepList" :key="item.stepIndex">
        <div class="activity-steps-container-header-right-item-line" v-if="item.stepIndex !== 1"></div>
        <div class="activity-steps-container-header-right-item-content">
          <span>
            <div class="big-circle">
              <div class="small-circle"></div>
            </div>
            {{ item.title }}
          </span>
          <span class="bottom-line" v-if="item.stepIndex === currentStep"></span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    currentStep: {
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      stepList: [
        {
          title: '活动信息',
          active: false,
          stepIndex: 1
        },
        {
          title: '报名设置',
          active: false,
          stepIndex: 2
        },
        {
          title: '互动设置',
          active: false,
          stepIndex: 3
        },
        {
          title: '发起报名',
          active: false,
          stepIndex: 4
        }
      ]
    }
  },
  methods: {
    handleStepClick(index) {
      this.$emit('stepChange', { newIndex: index, oldIndex: this.currentStep })
    }
  }
}
</script>

<style scoped lang="less">
.activity-steps-container {
  background: #fff;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: baseline;
  .activity-steps-container-header-left {
    line-height: 64px;
    font-size: 20px;
    font-weight: 600;
  }
  .activity-steps-container-header-right {
    display: flex;
    margin: 0 auto;
    .activity-steps-container-header-right-item {
      font-size: 16px;
      font-weight: 400;
      color: #333333;
      display: flex;
      align-items: flex-start;
      flex-shrink: 0;
      cursor: pointer;
      &.active {
        color: #0052D9;
        font-weight: 500;
        .activity-steps-container-header-right-item-content {
          span {
            color: #0052D9;
            .big-circle {
              background: #d4e3fc;
              .small-circle {
                background: #0052D9;
              }
            }
          }
        }
      }
      .activity-steps-container-header-right-item-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        span {
          display: flex;
          align-items: center;
          .big-circle {
            width: 13px;
            height: 13px;
            background: #ebecee;
            border-radius: 50%;
            margin: 0 5px 0 0;
            position: relative;
            .small-circle {
              width: 10px;
              height: 10px;
              background: #EBECEE;
              border-radius: 50%;
              position: absolute;
              left: 50%;
              top: 50%;
              transform: translate(-50%, -50%);
            }
          }
        }
        .bottom-line {
          width: 44px;
          height: 3px;
          background: #0052D9;
          margin: 12px 0 0 18px;
          display: block;
        }
      }
      .activity-steps-container-header-right-item-line {
        width: 40px;
        height: 1px;
        background: #CFCFCF;
        margin: 10px 15px 0;        
      }
    }
  }
}
</style>
