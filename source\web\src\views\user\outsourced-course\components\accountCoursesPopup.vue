<template>
  <div class="redeem-courses-pupop">
    <el-dialog custom-class="redeem-courses-dialog" :visible.sync="visible" width="400px" :show-close="false" @close="handleClose">
      <div class="redeem-courses-main">
        <img class="left-top-pic" src="@/assets/mooc-img/left-top-pic.png" alt="">
        <div class="body">
          <div class="title">已获得 <br /> “学霸卡-哈佛管理导师专用卡” </div>
          <div class="msg">使用该学霸卡，可兑换解锁 <br /> “哈佛管理导师”相关500+篇文章的学习权限</div>
          <div class="data">
            学霸卡7日后过期，请按需兑换
          </div>
          <div class="btn">
            <el-button size='medium' @click="handleClose">试读文章</el-button>
          </div>
          <div class="exchange-btn" @click="handleEnroll">不试读，直接兑换解锁平台权限</div>
          <div class="link">
            <span class="help-icon"></span>
            <a class="source-detail" target="_blank">想获得更多学霸卡？点此获取攻略</a>
          </div>
        </div>
      </div>
      <img class="close-img" src="@/assets/mooc-img/close-grey.png" alt="" @click="handleClose" />
    </el-dialog>
  </div>
</template>

<script>
// import env from 'config/env.conf.js'
// const envName = env[process.env.NODE_ENV]
import { moocEnroll, getAcctinfosActivity } from '@/config/mooc.api.conf.js'

export default {
  components: {},
  props: {
    visible: {
      type: Boolean
    }
  },
  computed: {},
  data() {
    return {}
  },
  watch: {},
  created() {},
  mounted() {},
  methods: {
    // 获取卡券信息
    async getAcctinfosActivity() {
      const result = await getAcctinfosActivity()
      console.log(result, '获取卡券信息')
    },
    // 报名
    handleEnroll() {
      const loading = this.$loading({
        lock: true,
        text: '课程兑换中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
        customClass: 'loading-mask-geek'
      })
      const params = {
        mooc_course_id: this.info.graphic_id,
        acct_type_code: this.info.recourse_from,
        join_type: '3'
      }
      moocEnroll(params, false)
        .then((res) => {
          loading.close()
          this.$message.success('兑换课程成功')
          this.$emit('handleRegistered')
          this.handleClose()
        })
        .catch((res) => {
          loading.close()
          if (res.code === 'ECONNABORTED') {
            this.$message.error('活动太火爆了，请稍后再试！')
          } else if (res.message) {
            this.$message.error(res.message)
          }
        })
    },
    handleClose() {
      this.$emit('handleClose')
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less">
.redeem-courses-dialog {
  background: transparent;
  box-shadow: none;
  .el-dialog__header {
    display: none;
  }
  .el-dialog__body {
    padding: 0;
  }
}
.loading-mask-geek {
  .el-icon-loading {
    font-size: 40px;
    color: #fff;
  }
  .el-loading-text {
    color: #e9e9e9;
  }
}
.popper_select_pupop {
  border-radius: 6px;
  background: #fff;
  box-shadow: 0 6px 30px 5px #0000000d, 0 16px 24px 2px #0000000a,
    0 8px 10px -5px #00000014;
  border: none;
  font-size: 12px;
  .popper__arrow {
    display: none;
  }
  .popper-content {
    font-size: 12px;
  }
  .el-select-dropdown__item {
    height: 52px;
    line-height: 20px;
    margin: 5px 12px;
    padding: 6px 8px;
    border-radius: 4px;
    border: 1px solid #eee;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    // background-color: #fff;
    // border-radius: 4px;
    // border: 1px solid var(---Brand7-Hover, #266FE8);
  }
  .el-select-dropdown__item.selected {
    color: unset;
    border-radius: 4px;
    border: 1px solid var(---Brand7-Hover, #266fe8);
    font-weight: 400;
  }
  .num {
    color: #00000066;
  }
  .num-b {
    color: #0052d9;
  }
}
</style>
<style lang="less" scoped>
.redeem-courses-pupop {
  text-align: center;
  .redeem-courses-main {
    position: relative;
    overflow: hidden;
    border-radius: 24px;
    background: linear-gradient(180deg, #e3f2ff 0%, #e3f2ff 0%, #eaf5ff 35.99%);
    .left-top-pic {
      position: absolute;
      width: 89px;
      left: 4px;
      top: 5px;
      z-index: 1;
    }
    .body::before,
    .body::after {
      content: '';
      position: absolute;
      top: 50%;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background-color: #e9f4ff;
    }
    .body::before {
      left: 0;
      transform: translate(-50%, -50%);
    }
    .body::after {
      right: 0;
      transform: translate(50%, -50%);
    }
    .body {
      position: relative;
      border-radius: 26px;
      margin: 34px 24px 24px;
      padding: 20px 15.5px;
      background: #fff;

      .title {
        color: #0052d9;
        text-align: center;
        font-size: 16px;
        font-weight: 600;
        line-height: 24px;
        margin-bottom: 14px;
      }
      .msg {
        color: #000000e6;
        text-align: center;
        font-size: 14px;
        line-height: 22px;
      }
      .btn {
        display: flex;
        justify-content: center;
        .el-button {
          width: 264px;
          height: 36px;
          border-radius: 24px;
          background: linear-gradient(90deg, #488dff 0%, #1766ff 100%);
          color: #ffffff;
          font-size: 14px;
          margin-bottom: 12px;
        }
        .disable {
          background: linear-gradient(90deg, #e0e0e0 0%, #a5a5a7 100%);
        }
      }
      .data {
        color: #00000099;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        padding: 20px 0;
        text-align: center;
      }
      .exchange-btn {
        margin-bottom: 12px;
        color: #0052d9;
        font-family: 'PingFang SC';
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        cursor: pointer;
      }
      .link {
        line-height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        .help-icon {
          background: url('~@/assets/mooc-img/help_circle.png') no-repeat
            center/cover;
          width: 14px;
          height: 14px;
          display: inline-block;
          margin-right: 5px;
        }
        .source-detail {
          font-size: 12px;
          color: #0052d9;
          cursor: pointer;
        }
      }
    }
  }
  .close-img {
    width: 28px;
    margin-top: 16px;
    cursor: pointer;
  }
}
</style>
