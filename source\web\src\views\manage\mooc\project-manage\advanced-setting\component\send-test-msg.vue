<template>
  <div class="send-test-dialog">
    <el-dialog 
      :title="dialogTitle" 
      :visible.sync="visible"
       width="430px" 
      :show-close="false"
      :close-on-click-modal="false"
      :before-close="closeDialog"
      >
        <div class="send-test-body">
          <el-form :model="form" ref="form"  label-width="94px">
            <el-form-item label="渠道：">
              <span>{{ form.type }}</span>
            </el-form-item>
            <el-form-item label="测试人员：">
              <sdc-staff-selector 
                ref="adminsSelectorRef" 
                v-model="form.target"
                size="small" 
                :props="adminProps" 
                placeholder="请选择测试人员" 
                @change="changeCourseAuth"
                />
            </el-form-item>
          </el-form>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="handleSave">确定发送</el-button>
        </div>
      </el-dialog>
  </div>
</template>
<script>
import { sendTestMsg } from '@/config/mooc.api.conf'
export default {
  components: {
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    dialogTitle() {
      return (this.moduleName === 'notify' ? '加入提醒' : '催办') + '消息发送测试'
    }
  },
  data() {
    return {
      moduleName: '',
      form: {
        type: '',
        typeVal: '',
        target: []
      },
      adminProps: {
        staffID: 'staff_id',
        staffName: 'staff_name'
      },
      targetArr: []
    }
  },
  mounted() {
  },
  methods: {
    initData(data) {
      this.moduleName = data.moduleName
      this.form.type = data.label
      this.form.typeVal = data.type
    },
    changeCourseAuth(e) {
      this.targetArr = [e]
    },
    // 催办
    handleSave() {
      const { mooc_course_id } = this.$route.query
      const { target, typeVal } = this.form
      if (target.length <= 0) {
        this.$message.warning('请选择测试人员')
        return
      }
      let targetArr = this.targetArr.map(v => {
        return {
          staff_name: v.staff_name.split('(')[0],
          staff_id: v.staff_id
        }
      })
      const params = {
        mooc_course_id,
        type: typeVal,
        send_type: this.moduleName,
        users: targetArr
      }
      sendTestMsg(params).then(res => {
        this.$message.success(this.dialogTitle + '成功')
        this.closeDialog()
        this.$emit('onSearch')
      })
    },
    closeDialog() {
      this.$refs.adminsSelectorRef.clearSelected()
      this.form = {
        type: '',
        typeVal: '',
        target: []
      }
      this.$emit('update:visible', false)
      this.$emit('close')
    }
  }
}
</script>
<style lang="less" scoped>
.send-test-dialog {
  :deep(.el-dialog) { 
    border-radius: 0;
    .el-dialog__header {
      padding: 28px 32px 12px;
      border-bottom: 0;
      .el-dialog__title {
        font-size: 16px;
        font-weight: 600;
        font-family: "PingFang SC";
        line-height: 24px;
      }
    }
    .el-dialog__body {
      padding: 0;
      .el-dialog__footer {
        padding: 8px 0 24px;
      }
    }
  }
  .send-test-body {
    padding: 10px 32px 0;
  }
}
</style>
