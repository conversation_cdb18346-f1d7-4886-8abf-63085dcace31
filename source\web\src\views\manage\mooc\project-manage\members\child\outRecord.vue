<template>
  <el-dialog title="学员退出记录" width="1000px" :visible.sync="visible" :before-close="closeDialog">
    <el-form :model="form" :inline="true">
      <el-form-item label="名称" >
         <el-input v-model="form.staff_name" style="width: 200px" placeholder="请输入学员名称"></el-input>
      </el-form-item>
      <el-form-item label="退出时间">
        <el-date-picker
          v-model="setTime"
          size="small"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :default-time="['00:00:00','23:59:59']"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="search(1)" size="small">搜索</el-button>
        <el-button @click="handleReset" size="small">重置</el-button>
      </el-form-item>
    </el-form>
    <el-table 
    :data="tableData.records"
    style="width: 100%"
    header-row-class-name="table-header-style"
    row-class-name="table-row-style"
    >
      <el-table-column property="staff_name" label="学员名称" width="150"></el-table-column>
      <el-table-column property="updated_at" label="退出时间" width="200"></el-table-column>
      <el-table-column property="address" label="类型">
        <template slot-scope="scope">
          <span>{{ out_type_info[scope.row.cancel_type] }}</span>
        </template>
      </el-table-column>
      <el-table-column property="cancel_reason" label="退出原因"></el-table-column>
    </el-table>
    <el-pagination
      v-if="tableData.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="current"
      :page-sizes="[5, 10, 20, 30, 50, 100]"
      :page-size="size"
      layout="total,  prev, pager, next, sizes, jumper"
      :total="tableData.total"
      >
    </el-pagination>
  </el-dialog>
</template>
<script>
import pager from '@/mixins/pager'
import { outRecords } from '@/config/mooc.api.conf.js'
const out_type_info = {
  1: '管理员移除',
  2: '主动退出'
}
export default {
  mixins: [pager],
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        staff_name: ''
      },
      setTime: '',
      tableData: {
        records: [],
        total: 0
      },
      out_type_info
    }
  },
  mounted() {
    this.search()
  },
  methods: {
    search(page_no = 1) {
      const { mooc_course_id } = this.$route.query
      const join_start_time = this.setTime?.length ? this.setTime[0] : ''
      const join_end_time = this.setTime?.length ? this.setTime[1] : ''
      const params = {
        page_no,
        page_size: this.size,
        join_start_time,
        join_end_time,
        staff_name: this.form.staff_name,
        mooc_course_id
      }
      outRecords(params).then((res) => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    // 重置
    handleReset() {
      this.form.staff_name = ''
      this.setTime = ''
      this.search()
    },
    closeDialog() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
