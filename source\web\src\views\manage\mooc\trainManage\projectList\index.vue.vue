<template>
  <div class="train-project-list">
    <div class="top-title">
      <span>培养项目列表</span>
    </div>
    <div class="search-content">
      <el-form ref="form" :model="form" inline>
        <el-tooltip :disabled="limitBtn" effect="dark" content="暂无权限" placement="top-start">
          <span><el-button class="top-btn" :disabled="!limitBtn" type="primary" @click="handleCreateProject" icon="el-icon-plus" size="small">创建项目</el-button></span>
        </el-tooltip>
        <div class="search-body">
          <el-form-item label="名称">
            <el-input v-model="form.course_title" placeholder="请输入项目名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="项目标签" class="project-label">
            <!-- <LabelSelectComponent
            v-model="form.labels"
            placeholder="请选择项目标签，支持多标签筛选"
            class="project-tag-box"
            :maxNum="0"
            @getSelectedLabelList="getSelectedLabelList"
            :disableCreate="true"
            >
          </LabelSelectComponent> -->
          <sdc-search-label 
          v-model="form.labels" 
          :labelNodeEnv="labelNodeEnv"
          @getSelectedLabelList="getSelectedLabelList"
          >
          </sdc-search-label>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="form.course_status" placeholder="请选择项目状态" style="width:280px" clearable>
              <el-option
                v-for="item in statusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="创建时间">
            <el-date-picker
              style="width:380px"
              size="small"
              v-model="createTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00','23:59:59']"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label="项目时间">
            <el-date-picker
              style="width:380px"
              size="small"
              v-model="projectTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              :default-time="['00:00:00','23:59:59']"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item class="boutique-box">
            <el-checkbox v-model="form.excellent_status">只看精品项目</el-checkbox>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="onSearch(1)" size='small'>搜索</el-button>
          </el-form-item>
          <el-form-item>
            <el-button @click="handleReset" size='small'>重置</el-button>
          </el-form-item>
        </div>
      </el-form>
      <el-table
        :data="tableData.records"
        style="width: 100%"
        header-row-class-name="table-header-style"
        row-class-name="table-row-style"
        class="table-content"
      > 
        <el-table-column prop="mooc_course_id" label="id" width="120"></el-table-column>
        <el-table-column prop="course_title" label="名称" show-overflow-tooltip>
          <template slot-scope="scope">
            <span class="boutique-tag" v-if="scope.row.excellent_status === 1">精</span>
            <span class="table-course-title" @click="toManagePage(scope.row, 'name')">{{ scope.row.course_title }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="labels" label="标签" show-overflow-tooltip></el-table-column>
        <el-table-column prop="info_sec_status" label="信安审核" width="150">
          <template slot-scope="scope">
            <span class="tool-tips-box">
              <span  :class="['icon-box', { 'deal-color': scope.row.info_sec_status === 0 }, {'no-pass-color': scope.row.info_sec_status === 2}]">{{ safeInfo[scope.row.info_sec_status] }}</span>
              <el-tooltip v-if="[0, 2].includes(scope.row.info_sec_status)" popper-class="safe-tool"  :content="secContent(scope.row.info_sec_status)" placement="top-end">
                <i class="el-icon-warning-outline"></i>
              </el-tooltip>
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="course_status_name" label="内容状态" width="100"></el-table-column>
        <el-table-column prop="name" label="项目时间" width="180">
          <template slot-scope="scope">
            <span>{{ projectTypeTime(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180"></el-table-column>
        <el-table-column prop="creator_name" label="创建人" width="120" show-overflow-tooltip></el-table-column>
        <el-table-column label="操作" width="280">
          <template slot-scope="scope">
            <div class="operat-btn-box">
              <el-link @click="toManagePage(scope.row)" type="primary" :underline="false">管理</el-link>
              <el-link 
              v-if="![null, 6, 8].includes(scope.row.approve_status)" 
              :type="scope.row.course_status === 3 ? 'primary': 'warning'" 
              @click="handleDown(scope.row, 1)" 
              :underline="false"
              >
                {{ scope.row.course_status === 3 ? '上架' : '下架' }}
              </el-link>
              <!-- course_status 0待发布, 3-已下架- approve_status 7-已审核通过 -->
              <el-link v-if="[0].includes(scope.row.course_status) && scope.row.approve_status === 7" @click="handlePublish(scope.row)" type="primary" :underline="false">发布</el-link>
              <el-link v-if="scope.row.course_status !== 0" @click="handleShare(scope.row)" type="primary" :underline="false">分享</el-link>
              <el-link 
                v-if="!scope.row.excellent_status && userLimitInfo.supper_admin && ![null, 6, 8].includes(scope.row.approve_status)" 
                type="primary" 
                :underline="false" 
                @click="handleGoods(scope.row)"
                >
                {{ scope.row.excellent_status ? '取消加精' : '加精' }}
              </el-link>
              <el-link v-if="userLimitInfo.supper_admin" type="primary" :underline="false" @click="handleCopy(scope.row)">复制</el-link>
              <!-- 审核通过-已发布 -->
              <el-link v-if="scope.row.approve_status === 7 && scope.row.course_status === 1 && scope.row.course_status_name !== '已结束'" @click="handleEnd(scope.row)" type="warning" :underline="false">结束</el-link>
              <el-link @click="handleDelete(scope.row, scope.$index)" type="danger" :underline="false">删除</el-link>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
      v-if="tableData.total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="current"
      :page-sizes="[5, 10, 20, 30, 50, 100]"       
      :page-size="size"
      layout="total,  prev, pager, next, sizes, jumper"
      :total="tableData.total"
      >
      </el-pagination>
    </div>
    <!-- 分享项目 -->
    <shareDialog :isShow.sync="isShareDialog" ref="shareDialogRef"></shareDialog>
    <el-dialog
    v-if="copyDialogShow"
    title="复制培训项目"
    width="460px"
    :close-on-click-modal="false"
    :before-close="cancelCopy"
    custom-class="none-border-dialog copy-dialog"
    :show-close="false"
    :visible.sync="copyDialogShow">
    <el-form :model="copyForm" :rules="rules" ref="copyForm" label-width="80px" label-position="top">
      <el-form-item label="项目名称" prop="course_title" class="input-style">
        <el-input v-model="copyForm.course_title" clearable></el-input>
        <span class="custom-el-input-count">{{handleValidor(copyForm.course_title, 50, '1')}}/50</span>
      </el-form-item>
    </el-form>
    <span class="warning-tips">
      <i class="el-icon-warning-outline"></i>
      默认复制项目的基础设置和任务安排，如需修改其他设置请进入项目管理页面
    </span>
    <div slot="footer" class="dialog-footer">
      <el-button size="small" @click="cancelCopy">取 消</el-button>
      <el-button size='small' @click="submit" type="primary">确 定</el-button>
    </div>
  </el-dialog>
  </div>
</template>

<script>
import pager from '@/mixins/pager'
import { projectList, deleteProject, addGoods, copyProjectList, activateProject, deactivateProject, endProject } from '@/config/mooc.api.conf.js'
import shareDialog from '@/views/components/shareDialog.vue'
import { mapState } from 'vuex'
// import LabelSelectComponent from '@/views/components/LabelSelectComponent'
const safeInfo = {
  'null': '审核通过',
  '0': '待审核',
  '1': '审核通过',
  '2': '审核不通过'
}
export default {
  mixins: [pager],
  components: {
    shareDialog
    // LabelSelectComponent
  },
  data() {
    return {
      form: {
        course_title: '',
        course_status: '',
        labels: [],
        excellent_status: ''
      },
      isShareDialog: false,
      createTime: [],
      projectTime: [],
      statusOptions: [
        { label: '未开始', value: 10 },
        { label: '待发布', value: 0 },
        { label: '进行中', value: 1 },
        { label: '已结束', value: 2 },
        { label: '停用', value: 3 },
        { label: '待审核', value: 6 }
      ],
      tableData: {
        records: [],
        total: 0
      },
      copyForm: {},
      copyDialogShow: false,
      safeInfo,
      labelNodeEnv: process.env.NODE_ENV === 'production' ? 'production' : 'test',
      rules: {
        course_title: [{ required: true, message: '请输入项目名称', trigger: 'blur' }]
      }
    }
  },
  computed: {
    ...mapState(['userLimitInfo']),
    limitBtn() {
      const { supper_admin, mooc_bgadmin, mooc_company_admin, mooc_dept_admin } = this.userLimitInfo
      return [supper_admin, mooc_bgadmin, mooc_company_admin, mooc_dept_admin].includes(true)
    },
    projectTypeTime() {
      return (row) => {
        return row.course_period_type === 1 ? `${row.start_time} ~ ${row.end_time}` : 
          row.course_period_type === 2 ? `限定学习周期：${row.period_day || 0}天` :
            row.course_period_type === 3 ? '不限定项目学习时间' : '-'
      }
    },
    secContent() {
      return (val) => {
        return val === 0 ? '请留意企微“小腾老师”机器人消息提醒。如有疑问，可联系graywu。' : '提交信息包含敏感内容，请仔细仔细检查并重新修改后提交。如有疑问，可联系graywu。'
      }
    }
  },

  mounted() {
    this.onSearch(1)
  },
  methods: {
    // 培养项目查询
    onSearch(page_no = 1) {
      const start_time = this.projectTime?.length ? this.projectTime[0] : ''
      const end_time = this.projectTime?.length ? this.projectTime[1] : ''
      const create_time_left = this.createTime?.length ? this.createTime[0] : ''
      const create_time_right = this.createTime?.length ? this.createTime[1] : ''
      // if (!this.form.category_id) {
      //   this.form.category_name = ''
      // }
      let labels = ''
      this.form.labels.forEach((e) => {
        labels += `${e.label_id};`
      })
      labels = labels.slice(0, -1)
      const params = {
        ...this.form,
        start_time,
        end_time,
        create_time_left,
        create_time_right,
        page_no,
        page_size: this.size,
        labels,
        excellent_status: this.form.excellent_status ? 1 : ''
      }
      projectList(params).then((res) => {
        this.tableData.records = res.records
        this.tableData.total = res.total
      })
    },
    getSelectedLabelList(val) {
      this.form.labels = val
    },
    // 分享
    handleShare(row) {
      this.isShareDialog = true
      const url = process.env.NODE_ENV === 'production' ? 'https://sdc.qq.com/s/Zkma1k' : 'http://s.test.yunassess.com/s/UxEYPE'
      this.$nextTick(() => {
        const href = `${url}?scheme_type=mooc&mooc_course_id=${row.mooc_course_id}`
        this.$refs.shareDialogRef.initCode({
          mooc_course_id: row.mooc_course_id,
          url: href,
          taskTitle: row.course_title,
          scene: row.mooc_course_id,
          page: 'pages/mooc/projectDetails/index',
          customText: `【${row.course_title}】${href}`
        })
      })
    },
    // 删除
    handleDelete({ mooc_course_id }, index) {
      this.$messageBox
        .prompt(
          '删除操作无法撤销，请谨慎处理！',
          {
            closeOnClickModal: false,
            customClass: 'confirm-input-message-box',
            iconClass: 'el-icon-warning-outline',
            cancelButtonClass: 'el-button--default',
            confirmButtonClass: 'el-button--danger',
            confirmButtonText: '删除',
            cancelButtonText: '取消',
            inputPlaceholder: '请输入“确认删除”确认此次删除操作',
            inputErrorMessage: '请输入“确认删除”',
            inputValidator: function (val) {
              return !!val && val.trim() === '确认删除'
            }
          }
        ).then(({ value }) => {
          if (value && value.trim() === '确认删除') {
            deleteProject(mooc_course_id).then((res) => {
              this.tableData.records.splice(index, 1)
              this.$message.success('删除成功')
            })
          }
        })
    },
    // 上架-下架
    handleDown({ mooc_course_id, course_status }) {
      // 进行中-1，已结束-2，0-待发布
      if ([0, 1, 2, 10].includes(course_status)) {
        this.$messageBox.confirm('下架后将不可使用，不能被展示和搜索。', '下架确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消'
        }).then(() => {
          deactivateProject(mooc_course_id).then((res) => {
            this.onSearch(1)
            this.$message.success('下架成功')
          })
        })
        return
      }
      activateProject(mooc_course_id).then((res) => {
        this.onSearch(1)
        this.$message.success('上架成功')
      })
    },
    // 结束
    handleEnd({ mooc_course_id }) {
      this.$messageBox.confirm('项目结束后，学员将无法继续学习，且不能重新发布项目，确定结束吗？', '结束项目', {
        confirmButtonText: '结束项目',
        cancelButtonText: '取消'
      }).then(() => {
        endProject({
          mooc_course_id,
          course_status: 2
        }).then((res) => {
          this.onSearch(1)
          this.$message.success('结束成功')
        })
      })
    },
    // 发布
    handlePublish({ mooc_course_id, end_time, task_count, course_period_type }) {
      const publishEndTime = new Date(end_time).getTime()
      const currentTime = new Date().getTime()
      if (task_count) { // 配置了任务
        if (Number(course_period_type) === 3) { // 不限定项目时间
          activateProject(mooc_course_id).then((res) => {
            this.onSearch(1)
            this.$message.success('发布成功')
          })
        } else if (publishEndTime < currentTime) { // 发布时间小于结束时间
          this.$messageBox.confirm('当前项目设置的结束时间小于当前时间，请调整项目起止时间后再发布项目', '发布项目', {
            confirmButtonText: '知道啦',
            showCancelButton: false
          })
        } else {
          this.$messageBox.confirm('项目发布后，学员可加入项目进行学习，确认发布吗？', '发布项目', {
            confirmButtonText: '发布项目',
            cancelButtonText: '取消'
          }).then(() => {
            activateProject(mooc_course_id).then((res) => {
              this.onSearch(1)
              this.$message.success('发布成功')
            })
          })
        }
      } else { // 未配置任务
        this.$messageBox.confirm('当前项目未配置任务，无法发布，请先完成任务组织再发布项目', '发布项目', {
          confirmButtonText: '知道啦',
          showCancelButton: false
        })
      }
    },
    // 管理
    toManagePage({ mooc_course_id, course_status }, value) {
      if (value === 'name' && course_status !== 0) { // 名称跳转-除了未发布的都跳转到详情
        const { href } = this.$router.resolve({ 
          name: 'projectDetail',
          query: { mooc_course_id }
        })
        window.open(href)
        return
      }
      const { href } = this.$router.resolve({
        name: 'basic-setting',
        query: { mooc_course_id }
      })
      window.open(href)
    },
    // 加精
    handleGoods({ mooc_course_id, excellent_status }) {
      const params = {
        mooc_course_id,
        excellent_status
      }
      addGoods(params).then((res) => {
        const msg = excellent_status ? '加精成功' : '已取消加精'
        this.$message.success(msg)
        this.onSearch()
      })
    },
    // 复制
    handleCopy(row) {
      this.copyDialogShow = true
      this.copyForm = {
        ...row,
        course_title: row.course_title ? row.course_title + '【复制】' : ''
      }
    },
    cancelCopy() {
      this.copyDialogShow = false
    },
    submit() {
      const { mooc_course_id, course_title } = this.copyForm
      this.$refs['copyForm'].validate((valid) => {
        if (valid) {
          const params = {
            mooc_course_id,
            course_title
          }
          copyProjectList(params).then((res) => {
            this.cancelCopy()
            this.onSearch()
          })
        } else {
          return false
        }
      })
    },
    // 重置
    handleReset() {
      this.form = {
        course_title: '',
        labels: [],
        excellent_status: ''
      }
      this.createTime = []
      this.projectTime = []
      this.onSearch()
    },
    // 创建项目
    handleCreateProject() {
      const { href } = this.$router.resolve({
        name: 'project-create'
      })
      window.open(href)
    },
    handleValidor(value, num) {
      if (value) {
        const china = value.match(/[\u4e00-\u9fa5]/g)
        const zhCount = china && china.join('').length
        const enCount = Math.ceil((value.length - zhCount) / 2)
        const total = zhCount + enCount
        if (total > num) {
          this.copyForm.course_title = value.slice(0, -1)
        }
        return zhCount + enCount 
      }
      return 0
    }
  }
}
</script>
<style lang="less">
.el-tooltip__popper.safe-tool {
  background: #fff;
  color: #D35A21;
  line-height: 16px;
  box-shadow: 0 3px 20px 2px #5d5d5d38;
  width: 352px;
  padding: 16px;
  .popper__arrow::after {
    border-top-color: #fff
  }
}
</style>
<style lang="less" scoped>
.train-project-list {
  padding: 4px 0;
  height: 100%;
  background: #fff;
  border-radius: 4px;
  opacity: 1;
  box-shadow: 0 0 8px 0 #eeeeeeff;
  .project-tag-box {
    width: 280px;
  }
  .boutique-tag {
    background: #FF7548;
    color: #fff;
    font-size: 12px;
    height: 16px;
    line-height: 16px;
    padding: 0 2px;
    display: inline-block;
    margin-right: 10px;
    border-radius:  2px;
  }
  .top-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #f3f3f3ff;
    padding: 16px 20px;
    span {
      color: #000000e6;
      font-size: 24px;
      font-weight: 600;
    }
  }
  .search-content{
    padding: 18px 20px;
    .top-btn {
      margin-bottom: 8px;
    }
    .search-body{
      background: #f9f9f9ff;
      padding: 16px 16px 0px 16px;
      // display: flex;
    }
    .table-content{
      margin-top: 15px;
      border-radius: 4px;
      opacity: 1;
      border-top: 1px solid #eeeeeeff;
      border-left: 1px solid #eeeeeeff;
      border-right: 1px solid #eeeeeeff;
    }
  }
  .table-course-title {
    color: #0052D9;
    cursor: pointer;
  }
  .operat-btn-box {
    .el-link + .el-link {
      margin-left: 10px;
    }
  }
  .tool-tips-box {
    display: flex;
    align-items: center;
    // .icon-box {
    //   // color: rgba(0, 0, 0, 0.4);
    //   font-size: 12px;
    // }
    .deal-color {
      color:rgba(0,82,217,1);
    }
    .no-pass-color {
      color: #E34D59;
    }
    .el-icon-warning-outline {
      font-size: 16px;
      color: #E34D59;
      margin-left: 4px;
    }
  }
  .boutique-box {
    margin-left: 30px;
    margin-right: 30px;
  }
}
.project-label {
  :deep(.el-form-item__content) {
    width: 300px;
  }
}
:deep(.el-form-item) {
  margin-bottom: 16px;
}
:deep(.el-form-item__content){
  width: 80%;
}
:deep(.el-message-box__message){
  color: #00000099;
}
.copy-dialog {
  .custom-el-input-count {
    color: #ACACAC;
    background: #FFF;
    position: absolute;
    font-size: 12px;
    bottom: 6px;
    right: 6px;
    line-height: 20px;
  }
  .input-style {
    position: relative;
    :deep(.el-form-item__content) {
      width: 390px;
    }
    :deep(.el-input) {
      .el-input__inner {
        padding-right: 70px;
      }
      .el-input__suffix {
        position: absolute;
        right: 43px;
      }
    }
  }
  .course-name-input {
    width: 390px;
  }
  .warning-tips {
    color: #FF7548;
    display: inline-block;
    line-height: 20px;
    margin-top: 5px;
    i {
      margin-right: 3px;
    }
  }
}
</style>
