<template>
    <div v-if="(courseData.support_type && [2, 3].includes(courseData.support_type)) || !courseData.support_type"></div>
    <notSupported v-else :courseInfo="courseData" />
</template>
  
<script>
import { 
  getGeekCourseDetail
} from 'config/mooc.api.conf.js'
import { mapState } from 'vuex'
import translate from 'mixins/translate.vue'
import notSupported from './notSupported.vue'
export default {
  mixins: [translate],
  data() {
    return {
      courseData: {},
      act_type: '102', // 课程类型 102-极客时间
      countTimer: null // 15s上报的timeId
    }
  },
  components: {
    notSupported
  },
  computed: {
    ...mapState(['userInfo', 'moocLang']),
    // 课程id
    course_id() {
      return this.$route.query.course_id || ''
    }
  },
  mounted() {
    // 获取课程详情
    this.getCourseInfo()
  },
  methods: {
    // 课程详情
    getCourseInfo() {
      getGeekCourseDetail(this.course_id, { loading: true }).then(async (data) => {
        // console.log('课程详情：data: ', data)
        document.title = `${data.course_title}_Q-Learning`
        this.courseData = data
        if (this.courseData.recourse_type === 'outside' && ((data.support_type && [2, 3].includes(data.support_type)) || !data.support_type)) {
          let obj = data.recourse_iframe_url && JSON.parse(data.recourse_iframe_url)
          window.location.href = obj.pcUrl
        }
      }).catch((err) => {
        if (err.code === 403 || err.code === 500) {
          sessionStorage.setItem('401Msg', err.message)
          this.$router.replace({
            name: '401'
          })
        }
      })
    }
  }
}
</script>
