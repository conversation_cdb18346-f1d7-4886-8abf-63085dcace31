<!-- 培养项目任务列表 -->
<script>
import { getTaskList } from '@/config/mooc.api.conf.js'
import { mapState } from 'vuex'
export default {
  data() {
    return {
      taskTreeData: [],
      taskAllData: [],
      taskRequireData: [],
      required: false,
      unFinished: false,
      isExpandAll: true,
      // unlocked_by_step: false, // 项目是否按顺序解锁
      showExpandBtn: false // 是否展示全部收起/展开按钮
    }
  },
  filters: {
    filterTaskDuration(duration) {
      // 转换为时分秒
      let m = parseInt(duration / 60)
      let s = parseInt(duration % 60)
      s = m && s < 10 ? '0' + s : s

      let result = ''
      if (m) {
        result = `${m}${this.$langue('Mooc_ProjectDetail_TaskList_Minute', { defaultText: '分钟' })}`
      }
      if (s && s !== '00') {
        result += `${s}${this.$langue('Mooc_Common_Seconds', { defaultText: '秒' })}`
      }
      return result
    }
  },
  computed: {
    ...mapState(['userInfo']),
    mooc_course_id() {
      return this.$route.query?.mooc_course_id || ''
    },
    task_id() {
      return this.$route.query?.task_id || ''
    },
    resourceTypeIcon() {
      return (type) => {
        let className = ''
        let langName = ''
        let defaultText = ''
        switch (type) {
          case 'Video':
            className = 'video-icon' // 视频
            langName = 'Mooc_Common_ResourceType_Video'
            defaultText = '视频'
            break
          case 'Audio':
            className = 'audio-icon' // 音频
            langName = 'Mooc_Common_ResourceType_Audio'
            defaultText = '音频'
            break
          // case 'Image':
          //   className = '' // 图片
          //   langName = 'Mooc_TaskDetail_HomeWork_Image'
          //   defaultText = '图片'
          //   break
          case 'Zip':
            className = 'zip-icon' // 压缩文件
            langName = 'Mooc_Common_ResourceType_Zip'
            defaultText = '压缩文件'
            break
          case 'Doc':
            className = 'doc-icon' // 文档
            langName = 'Mooc_Common_ResourceType_Doc'
            defaultText = '文档'
            break
          case 'Exam':
            className = 'exam-icon' // 考试
            langName = 'Mooc_Common_ResourceType_Exam'
            defaultText = '考试'
            break
          case 'Practice':
            className = 'practise-icon' // 练习
            langName = 'Mooc_Common_ResourceType_Practice'
            defaultText = '练习'
            break
          case 'Article':
            className = 'article-icon' // 文章
            langName = 'Mooc_Common_ResourceType_Article'
            defaultText = '文章'
            break
          case 'Scorm':
            className = 'scorm-icon' // scorm
            langName = 'Scorm'
            defaultText = 'Scorm'
            break
          case 'Series':
            className = 'series-icon' // 系列课
            langName = 'Series'
            defaultText = '系列课'
            break
          case 'Other':
            className = 'link-icon' // 外链
            langName = 'Mooc_Common_ResourceType_ExternalLinks'
            defaultText = '外链'
            break
          case 'HomeWork':
            className = 'work-icon' // 作业
            langName = 'Mooc_Common_ResourceType_HomeWork'
            defaultText = '作业'
            break
          case 'ThirdParty':
            className = 'third-party-icon' // 第三方任务
            langName = 'Mooc_Common_ResourceType_ThirdParty'
            defaultText = '第三方任务'
            break
          case 'Survey':
            className = 'exam-icon' // 问卷
            // langName = 'Mooc_Common_ResourceType_Exam'
            langName = '问卷'
            defaultText = '问卷'
            break
        }
        return { className, langName, defaultText }
      }
    },
    taskProcessIcon() {
      return (data, register) => {
        let className = ''
        let tooltips = ''
        // 未完成的情况，先判断任务是否被锁定 锁定状态（1 解锁任务  2 锁定任务）
        if (!register) {
          className = 'lock-icon'
          tooltips = this.$langue('Mooc_ProjectDetail_BasicInfo_NotInProject', { defaultText: '暂未加入培养项目' })
        } else if (data.lock_status === 2) {
          className = 'lock-icon'
          tooltips = this.$langue('Mooc_ProjectDetail_TaskList_TaskLocked', { defaultText: '任务未解锁' })
        } else if (this.unlocked_by_step && data.preReqTaskFinished === false) {
          className = 'lock-icon'
          tooltips = this.$langue('Mooc_ProjectDetail_TaskList_LockedPreTaskUnfinish', { defaultText: '上一个应学任务未完成被锁定' })
        } else if (data.is_finished === true) {
          className = 'done-icon' // 已完成
          tooltips = this.$langue('Mooc_ProjectDetail_TaskList_TaskFinished', { defaultText: '任务已完成' })
        } else {
          // loading-icon 进行中 / wait-icon 未开始
          className = data.is_finished === false ? 'loading-icon' : 'wait-icon'
          tooltips = data.is_finished === false ? this.$langue('Mooc_ProjectDetail_TaskList_TaskInProgress', { defaultText: '任务进行中' }) : this.$langue('Mooc_ProjectDetail_TaskList_TaskNotStart', { defaultText: '任务未开始' })
        }
        return { className, tooltips }
      }
    },
    taskCanPreviewIcon() {
      return (data) => {
        let className = ''
        let tooltips = ''
        
        if (data.is_finished === true) {
          className = 'done-icon' // 已完成
          tooltips = this.$langue('Mooc_ProjectDetail_TaskList_TaskFinished', { defaultText: '任务已完成' })
        } else {
          // loading-icon 进行中 / wait-icon 未开始
          className = data.is_finished === false ? 'loading-icon' : 'wait-icon'
          tooltips = data.is_finished === false ? this.$langue('Mooc_ProjectDetail_TaskList_TaskInProgress', { defaultText: '任务进行中' }) : this.$langue('Mooc_ProjectDetail_TaskList_TaskNotStart', { defaultText: '任务未开始' })
        }
        return { className, tooltips }
      }
    }
  },
  methods: {
    getTaskData() {
      // let params = {
      //   mooc_course_id: this.mooc_course_id
      // }
      // // 只看应学任务
      // if (required) {
      //   params.required = required
      // }
      // // 只看未完成任务
      // if (undone) {
      //   params.unFinished = undone
      // }
      return new Promise((resolve, reject) => {
        const { previewType } = this.$route.query
        const val = previewType === 'preview' ? 1 : 0
        getTaskList({ mooc_course_id: this.mooc_course_id, preview: val }).then(res => {
          // 树形结构
          this.taskTreeData = res || []

          this.showExpandBtn = false
          // 递归处理，拿到所有task
          this.taskAllData = this.traverseArr(this.taskTreeData)
          // 过滤出应学且未锁定的任务
          this.taskRequireData = this.taskAllData.filter(item => {
            if (item.required) {
              item.preReqTaskFinished = false
            }
            return item.required && item.lock_status === 1
          })
          this.taskRequireData.map((item, index) => {
            if (index > 0) {
              // 上一个应学且未锁定的任务是否已完成
              // preReqTaskFinished：null表示选学任务，true表示上一个应学且未锁定任务已完成，false表示上一个应学且未锁定任务未完成
              item.preReqTaskFinished = !!this.taskRequireData[index - 1].is_finished
            } else {
              // 默认第一个应学未锁定的任务为true
              item.preReqTaskFinished = true
            }
          })
          
          this.$emit('getTaskList', this.taskAllData)

          resolve()
        }).catch(() => {
          this.$sdc.loading.hide()
        })
      })
    },
    traverseArr(data) {
      let result = []
      data.map(item => {
        if (item.task_type === 'task') {
          item.disabled = true
          item.preReqTaskFinished = null // 初始化
          result.push(item)
        }
        if (item.task_type === 'group') {
          item.disabled = true
          item.iconName = 'tree-icon'
          this.showExpandBtn = true
        }
        if (item.task_type === 'stage') {
          // 在此设置disabled是为了设置task_type为stage时el-tree的背景颜色
          item.disabled = false
          item.iconName = 'tree-icon'
          this.showExpandBtn = true
        }
        if (item.sub_tasks?.length) {
          result.push(...this.traverseArr(item.sub_tasks))
        }
      })
      return result
    },
    // 全部展开/收起
    expandHandle() {
      this.isExpandAll = !this.isExpandAll
      let node = this.$refs.taskTree.store.root
      this.expandNodes(node)
    },
    expandNodes(node) {
      node.expanded = this.isExpandAll
      for (let i = 0; i < node.childNodes.length; i++) {
        node.childNodes[i].expanded = this.isExpandAll
        if (node.childNodes[i].childNodes.length > 0) {
          this.expandNodes(node.childNodes[i])
        }
      }
    },
    // 勾选应学/未完成按钮，将值存储在store里
    changeCheckbox() {
      this.$store.commit('setTaskListFilter', {
        required: this.required, // 只看应学任务
        unFinished: this.unFinished // 只看未完成任务
      })
      this.setFilterRef()
    },
    // 对列表进行应学/未完成筛选操作
    setFilterRef() {
      this.$refs.taskTree && this.$refs.taskTree.filter({
        required: this.$store.state.taskListFilterInfo.required,
        unFinished: this.$store.state.taskListFilterInfo.unFinished
      })
      this.required = this.$store.state.taskListFilterInfo.required
      this.unFinished = this.$store.state.taskListFilterInfo.unFinished
    },
    // 对列表进行筛选时执行数据过滤方法
    filterNode(params, data) {
      let requiredFlag = true
      let unFinishedFlag = true
      if (params.required) {
        requiredFlag = data.required
      }
      if (params.unFinished) {
        unFinishedFlag = !data.is_finished
      }
      return requiredFlag && unFinishedFlag
    },
    handleTask(data, courseFrom) {
      console.log(data, 'aaa')
      // 极客试学
      if (data.geekPreview) {
        this.$router.push({
          name: 'taskDetail',
          query: {
            mooc_course_id: data.mooc_course_id,
            task_id: data.task_id,
            from: courseFrom
          }
        })
      } else {
        const { previewType } = this.$route.query
        if (data.task_status === 2) {
          this.$message.warning(this.$langue('Mooc_Common_Authority_TaskDIsabled', { defaultText: '任务已失效，无法学习，请联系管理员调整' }))
          return
        }
        if (data.task_type === 'task') {
          if (data.lock_status === 2) {
            if (data.unlock_time) {
              this.$message.warning(`${this.$langue('Api_Mooc_Project_TaskUnLocked', { defaultText: '任务暂未解锁，解锁时间' })}：${data.unlock_time}`)
            } else {
              this.$message.warning(this.$langue('Mooc_Common_Authority_NotStudyByAdminLocked', { defaultText: '任务已被管理员锁定，无法学习' }))
            }
          } else if (this.unlocked_by_step && data.preReqTaskFinished === false) { // preReqTaskFinished：null, false, true三种状态，应学判断为false
            this.$message.warning(this.$langue('Mooc_ProjectDetail_TaskList_UnlockByPreTask', { defaultText: '完成上一个应学任务后解锁' }))
          } else {
            this.$router.push({
              name: 'taskDetail',
              query: {
                mooc_course_id: data.mooc_course_id,
                task_id: data.task_id,
                previewType,
                from: 'mooc'
              }
            })
          }
        }
      }
    }
  }
}
</script>
<style lang="less">
.video-icon {
  background: url("~@/assets/mooc-img/video.png") no-repeat center / cover;
}

.audio-icon {
  background: url("~@/assets/mooc-img/audio.png") no-repeat center / cover;
}

.exam-icon {
  background: url("~@/assets/mooc-img/exam-task-round.png") no-repeat center / cover;
}

.practise-icon {
  background: url("~@/assets/mooc-img/practise.png") no-repeat center / cover;
}

.scorm-icon {
  background: url("~@/assets/mooc-img/scorm.png") no-repeat center / cover;
}
.series-icon {
  background: url("~@/assets/mooc-img/series.png") no-repeat center / cover;
}
.link-icon {
  background: url("~@/assets/mooc-img/outlink.png") no-repeat center / cover;
}

.zip-icon {
  background: url("~@/assets/mooc-img/zip.png") no-repeat center / cover;
}

.article-icon {
  background: url("~@/assets/mooc-img/article.png") no-repeat center / cover;
}
.doc-icon {
  background: url('~@/assets/mooc-img/file.png') no-repeat center / cover;
}
.wait-icon {
  background: url("~@/assets/mooc-img/wait.png") no-repeat center / cover;
}

.loading-icon {
  background: url("~@/assets/mooc-img/loading.png") no-repeat center / cover;
}

.done-icon {
  background: url("~@/assets/mooc-img/done.png") no-repeat center / cover;
}

.lock-icon {
  background: url("~@/assets/mooc-img/lock-on.png") no-repeat center / cover;
}
.work-icon {
  background: url("~@/assets/mooc-img/work-icon.png") no-repeat center / cover;
}
.third-party-icon {
  background: url("~@/assets/mooc-img/third-party-icon.png") no-repeat center / cover;
}
</style>
