<template>
    <div class="labelCardItem">
        <div class="courseImage">
            <defaultImage :info="info"/>
            <span class="duration">{{ info.origin_data.est_dur || 0 }}分钟</span>
        </div>
        <div  class="courseInfo">
            <moduleTitle :info="info" :name="moduleName"/>
            <div class="courseView">
                <div class="flex align-center liveTime" v-if="info.origin_data?.start_time">
                    <img :src="require('@/assets/img/label/time2x.png')">
                    <span class="timeRang">{{info.origin_data.start_time?.substring(0,info.origin_data.start_time.lastIndexOf(':'))}}~{{info.origin_data.end_time?.substring(0,info.origin_data.end_time.lastIndexOf(':'))}}</span>
                </div>
                <div class="flex align-center local" v-if="info.origin_data.location" >
                    <img :src="require('@/assets/img/label/local2x.png')">
                    <span>{{info.origin_data.location}}</span>
                </div>
            </div>
            <relatedContent :info="info" :curModuleId="curModuleId" :curLabelId="curLabelId" :sortSubsIds="sortSubsIds" :subsType="subsType" :contentType="contentType"></relatedContent>
        </div>
    </div>
</template>
<script>
import defaultImage from './child/defaultImage.vue'
import moduleTitle from './child/moduleTitle.vue'
import relatedContent from './child/relatedContent.vue'
export default {
  name: 'activeItem',
  props: ['info', 'curModuleId', 'curLabelId', 'sortSubsIds', 'subsType', 'contentType'],
  data() {
    return {
      nowTime: new Date().getTime()
    }
  },
  components: {
    defaultImage,
    moduleTitle,
    relatedContent
  },
  computed: {
    moduleName() {
      let { regist_end_time } = this.info.origin_data
      if (!regist_end_time) return '活动'
      if (regist_end_time && (new Date(regist_end_time).getTime() > this.nowTime)) {
        return '活动报名'
      } else {
        return '活动'
      }
    }
  }
}
</script>
<style lang="less" scoped>
@import '../style/item.less';
</style>
