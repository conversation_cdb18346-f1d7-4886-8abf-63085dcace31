<template>
  <div class="start-set">
    <el-form :model="form" label-width>
      <el-form-item
        label="启动方式"
        required
        class="radio-form"
        :rules="{ required: true, message: '请选择启动方式' }"
      >
        <el-radio-group v-model="form.startMode" class="radio-group">
          <div class="radio-item">
            <el-radio :label="1">自动启动：授课完成后，自动开始推送问卷</el-radio>
            <div class="notice">注意:如创建问卷时授课已完成，则将于问卷创建次日，开始推送问卷</div>
          </div>
          <div class="radio-item">
            <el-radio :label="0">手动启动：在班级/活动管理页-问卷管理中，点击启动后，开始推送问卷</el-radio>
          </div>
        </el-radio-group>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    startMode: {
      type: Number,
      default: 1 // 0 手动 1 自动
    }
  },
  watch: {
    startMode: {
      handler(newVal) {
        this.form.startMode = newVal
      },
      immediate: true
    }
  },
  data() {
    return {
      form: {
        startMode: 1
      }
    }
  },
  methods: {
    getStartMode() {
      return this.form.startMode
    }
  }
}
</script>

<style lang="less" scoped>
.start-set {
  :deep(.el-form-item) {
    display: flex;
    align-items: baseline;
  }
  :deep(.el-form-item__label) {
    flex-shrink: 0;
  }
  .radio-form {
    :deep(.el-form-item__content) {
      display: flex;
      align-items: baseline;
    }
  }
  .form-label {
    font-size: 14px;
    margin-bottom: 20px;

    .required {
      color: #f56c6c;
      margin-right: 4px;
    }
  }

  .radio-group {
    display: flex;
    flex-direction: column;
    margin-left: 12px;
    font-size: 14px;
    color: #000000e6;
    line-height: 22px;
    :deep(.el-radio__label) {
      padding-left: 8px;
    }
  }

  .radio-item {
    margin-bottom: 14px;
    &:last-child {
      margin-bottom: 0;
    }
    .el-radio {
      margin-right: 0;
      font-weight: normal;
    }
  }

  .notice {
    margin-top: 4px;
    margin-left: 28px;
    color: #e34d59;
    font-size: 12px;
    line-height: 20px;
  }
}
</style>
