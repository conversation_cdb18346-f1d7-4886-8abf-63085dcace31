<template>
  <div v-if="(articleInfo.support_type && [2, 3].includes(articleInfo.support_type)) || !articleInfo.support_type" class="preview-page" id="previewPage" :dt-areaid="dtOutCoursePage('area')" :dt-remark="dtOutCoursePage('remark')">
    <div class="contain-main" :class="{'contain-main-1330': articleInfo.recourse_from === 'harvard'}">
      <div class="left">
        <div
          id="graphic-preview"
          class="left-contain"
        >
        <div class="editor-header">
          <div class="name">
            <span v-if="articleInfo.recourse_from !== 'sanjieke'" class="tag word">{{ $langue('Mooc_Common_ResourceType_Article', { defaultText: '文章' }) }}</span>
            <!-- <img class="geek-time" src="@/assets/mooc-img/comment/geek-time.png" alt="极客时间"> -->
            <img class="geek-time" :src="typeImg" alt="">
            <span
              >{{ articleInfo.course_title }}
            </span>
          </div>
          <div class="info info1">
            <div class="info-left">
              <span class="time">{{ articleInfo.created_at }}</span>
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content">
                  <span>{{ articleInfo.author }}</span><span v-if="articleInfo.author_intro">({{ articleInfo.author_intro }})</span>
                </div>
                <p class="create">
                  <span>{{ articleInfo.author }}</span><span v-if="articleInfo.author_intro">({{ articleInfo.author_intro | nameFilter }})</span>
                </p>
              </el-tooltip>
            </div>
            <div class="info-right">
              <!-- 观看 -->
              <span
                ><i class="icon-view"></i>({{
                  zanAndcollect.view_count | countFilter
                }})</span
              >
              <!-- 点赞 -->
              <span v-if="isIndependent_course"
                @click="handleLikeOrFav(1)"
                :class="[zanAndcollect.isZan ? 'icon-zan-active' : '']"
                ><i class="icon-zan"></i>({{
                  zanAndcollect.praise_count | countFilter
                }})</span
              >
              <!-- 评论 -->
              <span v-if="isIndependent_course"
                ><i class="icon-comment"></i>({{
                  zanAndcollect.comment_count | countFilter
                }})</span
              >
              <!-- 收藏 -->
              <span v-if="isIndependent_course"
                @click="handleLikeOrFav(2)"
                :class="[
                  zanAndcollect.isCollect ? 'icon-collect-active' : ''
                ]"
                ><i class="icon-collect"></i> ({{
                  zanAndcollect.fav_count | countFilter
                }})</span
              >
              <div class="jf-tip" v-if="isShowJfTip">
                <i class="jf-icon"></i>{{ $langue('Mooc_Common_Alert_CommonPoint', { point: +1, defaultText: `通用积分+1` }) }}
              </div>
            </div>
          </div>
          <div class="info info2" v-if="labelList.length">
            <div class="info-left">
              <div class="info-label">
                <span class="label">{{$langue('Article_Lable', { defaultText: '标签' })}}：</span>
                <div class="tag-list-box">
                  <div class="tag-list" v-for="(item, index) in labelList" :key="index">
                    <el-tooltip effect="dark" :content="item.category_full_name" placement="top-start">
                      <span class="tag-value" @click="searchGo(item)">{{ item.label_name }}</span>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 极客文章-->
        <iframe v-if="loadfinished && isShowIframe" class="geek-task-iframe" id="geekTaskIframe" :src="iframeUrl + `&pm=${geekConfig.pm}&scrolltop=${geekConfig.scrolltop}`" frameborder="0" allowfullscreen></iframe>
        <!-- 无权限-->
        <div class="study-tips-card-vh" v-if="isHarvard && !isPurchased">
          <div class="study-tips-card" >
            <div class="tips-content">
              <div class="title">提示：暂无权限浏览当前内容</div>
              <div class="msg">兑换解锁“哈佛精品文库”账号后，即可学习500+门更多好文</div>
              <div class="btn">
                <span class="btn-text" size='medium' @click="openActivity" >暂未开启学习权限，点此去领取权限</span>
              </div>
              <!-- <div class="data">
                <p>
                  <span class="text">需使用 <span  style="color:#ED7B2F">1</span>  张学霸卡-哈佛管理导师专用卡</span>
                </p>
                <p>
                  <span class="text">我的学霸卡：<span style="color:#ED7B2F">0</span> 张</span>
                </p>
              </div>
              <div class="link">
                <span class="help-icon"></span>
                <a class="source-detail" target="_blank" :href="linkConfig.rule_page_link">想要赢取更多学霸卡？点击查看指引</a>
              </div> -->
            </div>
          </div>
        </div>
       <div class="study-tips-card" v-if="isHarvard && isPurchased && shareAuth && isQuantity">
          <div class="tips-content">
            <div class="title">文章不错，我要分享!</div>
            <div class="msg">分享给同事，即可帮他开通与您同等的权限，一起学习，共同探讨。</div>
            <div class="btn">
              <span class="btn-text btn-text_270" size='medium' @click="handlerShareFixed">复制链接，转发分享</span>
            </div>
            <div class="btn">
              <span class="btn-text btn-text_270 btn-text_more" size='medium' @click="openActivity">更多好文</span>
            </div>
          </div>
        </div>
        <div v-if="isShowIframe">
          <Watermark
            ref="watermark" 
            v-if="watermark.textContent"
            :targetId="watermark.targetId"
            :text="watermark.textContent"
            :canvasUserOptions="watermark.canvasUserOpt"
            :wmUserOptions="watermark.wmUserOpt"
            :isManualInit="false"
          />
          <!-- 评论 -->
          <div class="comment-box" v-if="isIndependent_course">
              <div v-if="loadComment" id="commentBox">
                <sdc-comment
                  :params="commentParams"
                  @setCommentCount="setCommentCount"
                />
              </div>
          </div>
        </div>
        </div>
      </div>
    </div>
      <!-- <accountCoursesPopup v-if="isHarvard" :info="{ graphic_id: graphic_id, recourse_from: articleInfo.recourse_from }" :visible.sync="accountCoursesShow" @handleRegistered="handleRegistered"></accountCoursesPopup> -->
      <div class="share-fixed fixed-more"  v-if="shareAuth && isQuantity">
      <div @click="openActivity" class="share-fixed-btn">
        <img src="@/assets/outsourcedCourse/more-icon.png" alt="">
        <span class="share-text">更多 <br /> 好文</span>
      </div>
    </div>
    <div class="share-fixed"  v-if="shareAuth && isQuantity">
      <div @click="handlerShareFixed" class="share-fixed-btn">
        <img src="@/assets/outsourcedCourse/share-hover1.png" alt="">
        <span class="share-text">分享 <br /> 阅读</span>
      </div>
      <div class="share-f-tips" :style="isShowFixed ? 'display: block' : 'display: none' ">
       分享给同事，即可帮TA开通与您同等的权限 ，一起学习，共同探讨。
        <span class="tips-close" @click.stop="closeShareFixed"><i class="el-icon-close"></i></span>
      </div>
    </div>
    <pickUpPC :isGraphic="true" :visible.sync="pickUpVisible" :isQuantity="isQuantity" @handlerReceive="handlerReceive"></pickUpPC>
     <harvardShare :isShow.sync="giveDialog" :isGraphic="true" :course_id="graphic_id" :course_title="harvardShareTitle"></harvardShare>
  </div>
  <notSupported v-else :courseInfo="articleInfo" />
</template>

<script>
import { throttle, pageExposure } from '@/utils/tools.js'
import Watermark from '@/components/watermark.vue'
// import giveDialog from './components/giveDialog.vue'
import harvardShare from './components/harvardShare.vue'
import pickUpPC from './components/pickUpPC.vue'
// import accountCoursesPopup from './components/accountCoursesPopup.vue'
import { 
  getAcctinfos,
  getGeekCourseDetail, 
  getCoursePurchaseInfo, 
  checkPraised, addPraise, 
  deletePraise, 
  checkFavorited, 
  addFavorited,
  getHarvardActivityInfo,
  deleteFavorite, 
  geekStudyRecord, 
  getSummaryData, 
  purchaseSourceFromConfig,
  checkShareAuth, 
  moocEnroll, 
  getOutsourceLinkConfig,
  activityPresent
  // getRequestRecord
} from 'config/mooc.api.conf.js'
import MoocJs from 'sdc-moocjs-integrator'
import translate from 'mixins/translate.vue'
import { detailLogo } from '@/utils/outsourcedCourseMap.js'
import notSupported from './notSupported.vue'

export default {
  mixins: [translate],
  components: {
    Watermark,
    harvardShare,
    pickUpPC,
    notSupported
  },
  data() {
    return {
      pickUpVisible: false,
      record: false,
      consumePoint: 0, // 可赠送的张数
      harvardInfo: {},
      giveDialog: false,
      loadfinished: false, // 是否请求完文章详情接口
      graphic_access_record_id: '', // 上报id
      geekConfig: { // 极客时间配置
        pm: 'parent', // postmessage的传递层级
        scrolltop: true, // scrollTop控制模式
        archive: false // 记忆文章阅读位置
      },
      accountCoursesShow: false,
      cardInfo: {},
      act_type: '102', // 课程类型 102-极客时间
      articleInfo: {}, // 文章信息
      geekPurchaseInfo: {}, // 极客课程购买信息
      iframeUrl: '',
      zanAndcollect: { // 点赞数据
        isZan: false,
        isCollect: false,
        praise_count: 0, // 点赞数
        fav_count: 0, // 收藏数
        view_count: 0, // 浏览量
        comment_count: 0 // 评论量
      },
      commentParams: {}, // 评论的数据
      labelList: [], // 标签列表
      viewTimer: null,
      fileInRow: 4,
      loadComment: false, // 评论loading
      isShowJfTip: false, // 显示积分
      reportFlag: false, 
      watermark: {
        targetId: 'graphic-preview', // 水印目标元素id
        textContent: '',
        canvasUserOpt: {
          width: 200,
          height: 160,
          fillStyle: 'rgba(153, 153, 153, 0.1)',
          font: '20px Microsoft Yahei',
          rotateDegree: 39,
          translate: {
            x: 0,
            y: 0
          }
        },
        wmUserOpt: {
          'z-index': 99999
        }
      },
      studyRecordErrNum: 0,
      clearTimeId: null, // 停止上报的计时器id
      clear3sTimeId: null,
      sourceFromConfig: [],
      isShowFixed: true,
      shareAuth: false,
      linkConfig: {}
    }
  },
  created() {
    this.accountCoursesShow = true
    this.handleViewGraphicRecord()
    // 首次进入显示分享文字
    // if (localStorage.getItem('graphic_share_tips') !== 'true') {
    //   this.isShowFixed = true
    // }
  },
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          this.watermark.textContent = val.staff_name
          const hostUrl = location.hostname.endsWith('.woa.com')
            ? process.env.VUE_APP_PORTAL_HOST_WOA
            : process.env.VUE_APP_PORTAL_HOST
            
          this.commentParams = {
            userName: val.staff_name,
            actId: this.graphic_id,
            appId: 'A9BiosXihR0h46ThNsAX',
            scrollTarget: '.graphic-user-page',
            urlConfig: {
              getComments: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/${this.mooc_course_id}/comment-page`,
              addComment: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/add-comment`,
              deleteComment: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/delete-comment/`,
              like: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.act_type}/praised-comment`, 
              sticky: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/sticky-comment`,
              show: `${hostUrl}/training/api/outsourcedCourse/user/interaction/${this.mooc_course_id}/show-comment`
            }
          }
        }
      },
      immediate: true
    }
  },
  computed: {
    // 是否是外部课程
    isGeek() {
      return this.sourceFromConfig.includes(this.articleInfo?.recourse_from)
    },
    // 有无分享名额
    isQuantity() {
      const { quantity = 0 } = this.harvardInfo
      return quantity > 0
    },
    harvardShareTitle() {
      const { course_title = '' } = this.articleInfo
      if (course_title.length > 12) {
        return course_title.slice(0, 12) + '...'
      }
      return course_title
    },
    // 是否哈商
    isHarvard() {
      return ['harvard'].includes(this.articleInfo?.recourse_from)
    },
    // 是否购买
    isPurchased() {
      return this.articleInfo?.purchased
    },
    // 独立课程
    isIndependent_course() {
      return this.articleInfo.independent_course === 1
    },
    // mooc_id
    mooc_course_id() {
      return this.$route.query.mooc_course_id || '-1'
    },
    // 课程id
    graphic_id() {
      return this.$route.query.course_id || this.articleInfo.outsourced_course_id
    },
    isShowIframe() {
      return !this.isHarvard || (this.isHarvard && this.isPurchased)
    },
    // graphic_id() {
    //   return (
    //     this.$route.query.graphic_id ||
    //     sessionStorage.getItem('graphic_id') ||
    //     this.formData.graphic_id 
    //   )
    // },
    clearTime() { // 文章时长的2.5倍后停止上报 单位是秒 计时器需要*1000
      return (this.articleInfo.total_time || 0) * 2.5
    },
    // 外部课程试学 （外部课程recourse_from有值 && 课程需要购买 && 未购买 && 支持试学）
    isGeekFreeLearningSection() {
      return this.isGeek && this.articleInfo?.course_acquisition_type === 2 && !this.articleInfo?.purchased && this.articleInfo?.can_preview
    },
    typeImg() {
      return detailLogo[this.articleInfo.recourse_from] || detailLogo['geekBang']
    },
    windowHeight() {
      return window.innerHeight + 134
    },
    // 页面曝光
    dtOutCoursePage() {
      return (type) => {
        const { course_title } = this.articleInfo
        if (type === 'area') {
          return `area_${this.graphic_id}`
        } else if (type === 'remark') {
          return JSON.stringify({ 
            page: course_title,
            page_type: '外部课程详情页',
            container: course_title,
            act_type: '102',
            content_type: '文章',
            terminal: 'PC'
          })
        } else {
          return ``
        }
      }
    },
    urlText() {
      let userInfo = JSON.parse(sessionStorage.getItem('login_user'))
      let url = process.env.NODE_ENV === 'production' ? 'https://portal.learn.woa.com/training/outsourcedCourse/graphic/play' : `https://test-portal-learn.woa.com/training/outsourcedCourse/graphic/play`
      return `${url}?course_id=${this.graphic_id}&staff_name=${userInfo.staff_name}&staff_id=${userInfo.staff_id}`
    }
  },
  async mounted() {
    // 获取文章数据
    const _this = this
    await this.purchaseSourceFromConfig()
    await this.getArticleDetail().then(() => {
      this.loadComment = true

      document.addEventListener(
        'visibilitychange',
        this.handleViewGraphicRecord
      )

      this.clear3sTimeId = setTimeout(() => {
        window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
      }, 3000)

      // 极客时间 文章加载完成后开始计时
      window.addEventListener('message', (event) => {
        let { action, vendor, params } = event.data
        vendor === 'sanjieke' && console.log('三节课--课程: ', event.data)
        let vendorArray = Object.keys(detailLogo) || []
        if ([...vendorArray, 'geekbang'].includes(vendor)) {
          switch (action) {
            case 'article:mounted': // iframe执行mounted
              let iframeDom = document.querySelector('#geekTaskIframe')
              iframeDom && iframeDom.contentWindow.postMessage(`window:height:${_this.windowHeight}`, '*')
              window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
              this.creatViewTimer()
              this.createClearTime()
              MoocJs.play()
              break
            case 'article:loaded': // iframe执行loaded
              if (params.height) {
                console.log('graphic页面收到外部课程传递过来的iframe的高度参数params.height: ', params.height)
                let rongInstance = 10 // 缓冲误差
                _this.iframeAutoFit(params.height + rongInstance + 'px')
              }
              break
            case 'login:error': // iframe执行登录失败
              window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
              break
            case 'article:error': // 文章加载失败
              window.parent && window.parent.postMessage({ page: 'iframe', loading: false }, '*')
              MoocJs.onErrorInfo()
              break
            default:
              break
          }
        }
      })
    })

    this.getZanAndCollectStatus()
    this.getSummaryInfo()

    if (document.body.clientWidth > 1440) {
      this.fileInRow = 4
    } else {
      this.fileInRow = 3
    }

    window.addEventListener('resize', function () {
      return (() => {
        let iframeDom = document.querySelector('#geekTaskIframe')
        iframeDom && iframeDom.contentWindow.postMessage(`window:height:${_this.windowHeight}`, '*')
        if (document.body.clientWidth > 1440) {
          _this.fileInRow = 4
        } else {
          _this.fileInRow = 3
        }
      })()
    })

    document.addEventListener('copy', function (event) {
      let clipboardData = event.clipboardData
      clipboardData.setData('text/plain', document.getSelection().toString())
      event.preventDefault()
    })
    
    MoocJs.messageListener((res) => {
      if (res.events === 'tencent-mooc-lang') {
        localStorage.setItem('sdc-sys-def-lang', res.params)
        this.getLangJS()
      } 
    })

    // 监听iframe滚动
    let iframeDom = document.querySelector('#geekTaskIframe')
    let scrollDom = document.querySelector('.graphic-user-page')
    let titleDom = document.querySelector('.editor-header')
    let height = titleDom ? titleDom.clientHeight : 0
    let instance = 12 // 误差容错
    scrollDom && scrollDom.addEventListener('scroll', throttle(() => {
      console.log('graphic-user-page元素滚动scrollDom.scrollTop - height - instance: ', scrollDom.scrollTop - height - instance)
      iframeDom && iframeDom.contentWindow.postMessage(`scroll:top:${scrollDom.scrollTop - height - instance}`, '*')
    }, 0))
  },
  methods: {
    // 被赠送点击接受邀请按钮
    handlerReceive() {
      this.activityPresent()
    },
    openActivity() {
      const { activity_id } = this.harvardInfo
      let url = process.env.NODE_ENV === 'production' ? `https://sdc.qq.com/s/PTUrrm?scheme_type=harvard&activityId=${activity_id}` : `//test-portal-learn.woa.com/training/outsourcedCourse/user/activePage/harvard?activityId=${activity_id}`
      window.open(url)
    },
    // 提交申请
    applyperm() {    
      // if (this.record) return
      // let params = {
      //   recourse_from: this.articleInfo.recourse_from,
      //   course_id: this.graphic_id
      // }
      // getRequestRecord(params).then(res => {
      //   this.$message.success('提交成功')
      //   this.record = true
      // })
    },
    // 获取他人赠送的卡券
    activityPresent() {
      const userInfo = JSON.parse(sessionStorage.getItem('login_user'))
      let params = {
        from: this.$route.query.staff_id || 0,
        from_name: this.$route.query.staff_name || '腾讯学堂',
        acct_type_code: this.harvardInfo.acct_type_code,
        to_batch: [userInfo.staff_id],
        object_id: this.harvardInfo.activity_id,
        object_name: this.harvardInfo.activity_name,
        object_type: 'XuebaActivity',
        object_type_name: '活动',
        trans_amt: '1',
        notify_type: 0 // 手动赠送1 ，用户进入页面自动领的 0
      }
      console.log(params, '领卡参数')
      activityPresent(params).then(res => {
        if (res.success_count) {
          this.$message.success('成功解锁权限')
          this.getArticleDetail()
          this.pickUpVisible = false
        } else {
          this.$message.error('活动太火爆了，请稍后再试！')
          this.getArticleDetail()
        }
        console.log(res, '赠送接口')
        // this.getArticleDetail()
      }).catch(() => {
        this.$message.error('活动太火爆了，请稍后再试！')
      })
    },
    getLink() {
      getOutsourceLinkConfig({ resourceConfig: this.articleInfo.recourse_from }).then((res) => {
        this.linkConfig = res || {}
      })
    },
    // 赠送后刷新可赠送卡券数量
    handlerGiveXuebaka() {
      this.getAcctinfos()
    },
    async getAcctinfos() {
      let params = {
        acct_type_codes: this.articleInfo.recourse_from + 'Trans'
      }
      const result = await getAcctinfos(params)
      console.log(result, '查看有没有余额有余额解锁赠送劝学卡')
      //   查看有没有余额有余额解锁赠送劝学卡
      // this.consumePoint = Number(result.consume_point)
    },
    // 报名
    handleEnroll() {
      const loading = this.$loading({
        lock: true,
        text: '课程兑换中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
        customClass: 'loading-mask-geek'
      })
      const params = {
        mooc_course_id: this.graphic_id,
        acct_type_code: this.articleInfo?.recourse_from,
        join_type: '3'
      }
      moocEnroll(params, false)
        .then((res) => {
          loading.close()
          this.$message.success('兑换课程成功')
          this.handleRegistered()
        })
        .catch((res) => {
          loading.close()
          if (res.code === 'ECONNABORTED') {
            this.$message.error('活动太火爆了，请稍后再试！')
          } else if (res.message) {
            this.$message.error(res.message)
          }
        })
    },
    // 分享权限
    async checkShareAuth() {
      const result = await checkShareAuth({ type: this.articleInfo.recourse_from })
      this.shareAuth = result
    },
    async purchaseSourceFromConfig() {
      const res = await purchaseSourceFromConfig()
      this.sourceFromConfig = res || []
    },
    // iframe高度自适应
    iframeAutoFit(height) {
      this.$nextTick(() => {
        let iframeObj = document.querySelector('#geekTaskIframe')
        if (!iframeObj) return
        iframeObj.style.minHeight = height
      })
    },
    // 获取文章详情
    getArticleDetail() {
      window.parent && window.parent.postMessage({ page: 'iframe', loading: true, geekSourceLoading: true }, '*')
      let reFun = getGeekCourseDetail(this.graphic_id, { loading: false }).then(async (data) => {
        this.articleInfo = data
        this.labelList = data.labels || []
        this.iframeUrl = this.isGeekFreeLearningSection ? data.recourse_iframe_url + '&preview=true' : data.recourse_iframe_url
        // 详情页曝光上报
        pageExposure({
          page_type: '外购课文章详情页',
          content_type: '网络课',
          act_type: '2',
          content_name: data.course_title,
          content_id: this.graphic_id
        })

        // if (this.isGeekFreeLearningSection) {
        //   this.getGeekCoursePurchaseInfo()
        // } 
        // 哈商的调用卡券信息
        if (this.isHarvard) {
          await this.getAcctinfos()
          await this.getHarvardActivityInfo()
          await this.checkShareAuth()
          await this.getLink()
        }
        this.loadfinished = true
      }).catch((err) => {
        this.loadfinished = true
        if (([403, 404, 500].includes(err.code))) {
          MoocJs.sendErrorInfo(err.message)
        }
      })
      return reFun
    },
    // 哈商活动信息
    getHarvardActivityInfo() {
      getHarvardActivityInfo().then(res => {
        console.log(res, '哈商活动信息')
        this.harvardInfo = res
        // 之前逻辑有赠送人并且没有购买权限打开弹窗this.$route.query.staff_id && this.$route.query.staff_name && 
        // 现在：没有（购买）权限打开领取弹窗
        if (!this.isPurchased) {
          console.log('dakai')
          this.pickUpVisible = true
          // this.activityPresent()
        }
      })
    },
    // 极客课程购买信息
    getGeekCoursePurchaseInfo() {
      getCoursePurchaseInfo(this.mooc_course_id).then(res => {
        if (res.course_acquisition_type === 2) {
          res.course_val = res.course_val ? res.course_val : 1
        }
        this.geekPurchaseInfo = res
      })
    },
    createClearTime() { // 2.52倍后停止上报
      let _this = this
      this.clearTimeId = setTimeout(() => {
        clearInterval(_this.viewTimer)
        _this.viewTimer = null
        clearTimeout(_this.clearTimeId)
        _this.clearTimeId = null
      }, _this.clearTime * 1000)
    },
    creatViewTimer() {
      let _this = this
      this.viewTimer = setInterval(() => {
        _this.handleViewGraphicRecord() // 浏览器时长需每15秒记录一次
      }, 15000) // 15000
    },
    // 发请求记录学习时间
    handleViewGraphicRecord() {
      if (document.visibilityState === 'hidden') {
        return
      }
      if (this.reportFlag) {
        this.clearViewTimer()
        return
      }
      
      const recordParam = {
        from: this.$route.query.from || '',
        area_id: this.$route.query.area_id || '',
        course_id: this.graphic_id,
        sharer_id: this.$route.query.share_staff_id || '',
        sharer_name: this.$route.query.share_staff_name || '',
        from_type: this.articleInfo.recourse_from || 'geekBang',
        record_id: this.graphic_access_record_id
      }
      geekStudyRecord(recordParam).then((data) => {
        if (data) {
          this.graphic_access_record_id = data
        } else {
          this.reportFlag = true
        }
        this.studyRecordErrNum = 0
      }).catch(() => {
        this.studyRecordErrNum++
        if (this.studyRecordErrNum >= 3) {
          this.clearViewTimer()
          this.graphic_access_record_id = ''
        }
      })
    },
    // 清除15s计时器
    clearViewTimer () {
      clearInterval(this.viewTimer)
    },
    // 获取统计数据
    getSummaryInfo() {
      const params = { act_type: this.act_type, course_id: this.graphic_id }
      getSummaryData(params).then(res => {
        this.zanAndcollect = {
          ...this.zanAndcollect, ...res
        }
      })
    },
    // 点赞/收藏状态
    getZanAndCollectStatus() {
      const params = { act_type: this.act_type, course_id: this.graphic_id }
      checkPraised(params).then((res) => {
        this.zanAndcollect.isZan = res
      })
      checkFavorited(params).then((res) => {
        this.zanAndcollect.isCollect = res
      })
    },
    // 点赞和收藏前置
    handleLikeOrFav(scene) {
      const params = { act_type: this.act_type, course_id: this.graphic_id }
      if (scene === 1) {
        // 点赞/取消点赞
        checkPraised(params).then((res) => {
          const PAndFCommonAPI = res ? deletePraise : addPraise
          const tip = res ? this.$langue('Mooc_Common_Alert_CancelPraiseSucessed', { defaultText: '取消点赞成功' }) : this.$langue('Mooc_Common_Alert_PraiseSucessed', { defaultText: '点赞成功' })
          this.zanAndcollect.praise_count = res ? (this.zanAndcollect.praise_count === null || this.zanAndcollect.praise_count === 0 ? 0 : this.zanAndcollect.praise_count - 1) : this.zanAndcollect.praise_count + 1
          this.handlerCommonInt(PAndFCommonAPI, params, tip)
        })
      } else if (scene === 2) {
        // 收藏/取消收藏
        checkFavorited(params).then((res) => {
          const PAndFCommonAPI = res ? deleteFavorite : addFavorited
          const tip = res ? this.$langue('Mooc_Common_Alert_CancelCollectSucessed', { defaultText: '取消收藏成功' }) : this.$langue('Mooc_Common_Alert_CollectSucessed', { defaultText: '收藏成功' })
          this.zanAndcollect.fav_count = res ? (this.zanAndcollect.fav_count === null || this.zanAndcollect.fav_count === 0 ? 0 : this.zanAndcollect.fav_count - 1) : this.zanAndcollect.fav_count + 1
          this.handlerCommonInt(PAndFCommonAPI, params, tip)
        })
      }
    },
    // 点赞和收藏
    handlerCommonInt(PAndFCommonAPI, params, tip) {
      PAndFCommonAPI(params).then((data) => {
        if (PAndFCommonAPI === addPraise || PAndFCommonAPI === deletePraise) {
          // 点赞
          this.zanAndcollect.isZan = PAndFCommonAPI === addPraise ? Boolean(true) : Boolean(false)
        }
        if (
          // 收藏
          PAndFCommonAPI === addFavorited || PAndFCommonAPI === deleteFavorite
        ) {
          this.zanAndcollect.isCollect = PAndFCommonAPI === addFavorited ? Boolean(true) : Boolean(false)
        }
        if (PAndFCommonAPI === addPraise || PAndFCommonAPI === addFavorited) { 
          if (data.credit && data.credit !== '0') { // 点赞或者收藏 增加通用积分
            this.isShowJfTip = true
            setTimeout(() => {
              this.isShowJfTip = false
            }, 6000)
            this.$message.success(`${tip}， ${this.$langue('Mooc_Common_Alert_CommonPoint', { point: +data.credit, defaultText: `通用积分+${+data.credit}` })}`)
          } else this.$message.success(tip)
        } else this.$message.success(tip)
      })
    },
    // 点击标签跳转
    searchGo(item) {
      let href = location.hostname.endsWith('.woa.com') ? process.env.VUE_APP_V8_HOST_WOA : process.env.VUE_APP_V8_HOST
      href = `${href}/mat/user/search?keywords=${item.label_name}&from_page=ql新首页&type=label`
      window.open(href)
    },
    doCopy() {
      // 创建输入框元素
      const input = document.createElement('input')
      // 将想要复制的值
      input.value = this.urlText
      // 页面底部追加输入框
      document.body.appendChild(input)
      // 选中输入框
      input.select()
      // 执行浏览器复制命令
      document.execCommand('Copy')
      // 弹出复制成功信息
      this.$message.success(
        this.$langue('Mooc_Common_Alert_CopySucessed', {
          defaultText: '复制成功'
        })
      )
      // 复制后移除输入框
      input.remove()
    },
    closeShareFixed() {
      this.isShowFixed = false
      // localStorage.setItem('graphic_share_tips', true)
    },
    // 报名初始化数据
    handleRegistered() {
      this.getArticleDetail()
      this.checkShareAuth()
      this.getGeekCoursePurchaseInfo()
    },
    // 分享阅读
    handlerShareFixed() {
      this.giveDialog = true
    },
    // 评论的回调
    setCommentCount() {}
  },
  beforeDestroy() {
    clearInterval(this.viewTimer)
    this.viewTimer = null
    clearTimeout(this.clearTimeId)
    this.clearTimeId = null
    clearTimeout(this.clear3sTimeId)
    this.clear3sTimeId = null
    document.removeEventListener(
      'visibilitychange',
      this.handleViewGraphicRecord
    )
    if (sessionStorage.getItem('user_graphic_access_record_id')) {
      sessionStorage.removeItem('user_graphic_access_record_id')
    }
  },
  filters: {
    countFilter(val) {
      let str = ''
      str =
        val > 0 ? (val >= 10000 ? `${(val / 10000).toFixed(1)}w` : val) : '0'
      return str
    },
    nameFilter(value) {
      let limitNum = 40
      if (value.length > limitNum) {
        return value.substr(0, limitNum / 2) + '...' + value.substr(value.length - limitNum / 2)
      }
      return value
    }
  }
}
</script>

<style lang="less" scoped>
.preview-page {
  .encourage {
    .el-button {
      font-size: 12px;
    }
    z-index: 9999;
    top: 62px;
    right: 18%;
    position: absolute;
    background-color: #fff;
    width: 315px;
    height: 86px;
    .border {
      width: 0;
      height: 0;
      /*在三角形底边设置一个边界颜色/
            border-top: 20px solid red;
            /*其它3边设置相同颜色，*/
      border-bottom: 9px solid #fff;
      border-left: 9px solid transparent;
      border-right: 9px solid transparent;
      position: absolute;
      top: -9px;
      left: 84px;
    }
    .closeencourage {
      top: 11px;
      position: absolute;
      right: 13px;
      color: #a0a1a3;
      font-size: 21px;
      cursor: pointer;
    }
    .encourage_top {
      .first_share {
        margin-right: 5px;
        width: 20px;
        height: 20px;
        display: inline-block;
        background: url('~@/assets/img/first_share.png') no-repeat center /
          cover;
        background-size: 18px 18px;
      }
      .encourage_p1 {
        font-family: 'PingFang SC';
        line-height: 22px;
        margin-right: 10px;
        font-weight: 400;
        color: #ed7b2fff;
        font-size: 14px;
      }
      .encourage_p2 {
        color: #00000066;
        font-size: 12px;
        font-weight: 400;
        font-family: 'PingFang SC';
        line-height: 22px;
      }
      padding: 15px 10px 2px 10px;
      display: flex;
      font-size: 15px;
    }
    .encourage_center {
      margin-left: 40px;
      text-align: left;
      font-size: 12px;
      color: #00000066;
      font-weight: 400;
      font-family: 'PingFang SC';
      line-height: 20px;
    }
    .encourage_bottom {
      .el-button {
        font-weight: 600;
      }
      text-align: end;
      margin-top: 2px;
      margin-right: 20px;
    }
  }
  .contain-main {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    padding-top: 10px;
    .left {
      height: 100%;
      //   width: 1148px;
      .left-contain {
        position: relative;
        .comment-box #commentBox {
          padding: 0 24px 24px 24px;
          background-color: #fff;
        }
      }
      .editor-header {
        overflow: hidden;
        padding: 24px 24px 0 24px;
        background-color: #fff;
        .geek-time {
          height: 20px;
          margin-right: 10px;
        }
        .name {
          display: flex;
          align-items: center;
          color: #000000;
          font-size: 18px;
          font-weight: bold;
          margin-bottom: 22px;
          word-break: break-word;
          span:last-child {
            flex: 1;
          }
        }
        .Refinement {
          width: 26px;
          height: 26px;
          margin-left: 10px;
          margin-bottom: 2px;
          object-fit: contain;
        }
        .tag {
          padding: 0 6px;
          border-radius: 2px;
          margin-right: 10px;
        }
        .word {
          color: rgba(255, 139, 108, 1);
          border: 1px solid rgba(255, 139, 108, 1);
          background: rgba(255, 139, 108, 0.2);
        }
        .label-icon {
          display: inline-block;
          width: 42px;
          height: 20px;
          line-height: 20px;
          margin-right: 10px;
          font-style: normal;
          color: #fff;
          font-size: 14px;
          text-align: center;
          &.label_activity {
            background: url('~@/assets/img/label_activity.png') no-repeat center /
              cover;
          }
          &.label_originate {
            background: url('~@/assets/img/label_originate.png') no-repeat
              center / cover;
          }
          &.label_reprint {
            background: url('~@/assets/img/label_reprint.png') no-repeat center /
              cover;
          }
        }
        .info {
          display: flex;
          justify-content: space-between;
          margin-bottom: 16px;
          .info-left,
          .info-right {
            display: flex;
          }
          .info-left {
            .create,
            .time {
              text-overflow: ellipsis;
              overflow: hidden;
              word-break: break-all;
              white-space: nowrap;
              color: rgba(0, 0, 0, 0.6);
            }
            .time {
              width: 140px;
            }
            .create {
              margin-left: 16px;
            }
            .info-classify,
            .info-label {
              display: flex;
              .label {
                flex-shrink: 0;
                color: rgba(0, 0, 0, 0.4) !important;
              }
              p {
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: break-all;
                white-space: nowrap;
                span {
                  cursor: pointer;
                  color: #3464e0;
                  i {
                    font-style: normal;
                  }
                }
              }
            }
            .info-label {
              line-height: 20px;
              .tag-list-box {
                display: flex;
                flex-wrap: wrap;
              }
              .tag-value {
                background-color: rgba(235, 239, 252, 1);
                height: 20px;
                font-size: 12px;
                color: rgba(0, 82, 217, 1);
                padding: 4px;
                border-radius: 2px;
                display: inline-block;
                margin-right: 12px;
                line-height: 10px;
                cursor: pointer
              }
            }
            .fh {
              color: rgba(0, 0, 0, 0.4);
            }
          }
          .info-right {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.4);
            span {
              display: flex;
              align-items: center;
              justify-content: center;
              width: fit-content;
              margin-right: 18px;
            }
            span:nth-child(2),
            span:nth-child(4),
            span:nth-child(5),
            .editor {
              cursor: pointer;
            }
            .goback {
              cursor: pointer;
              color: #3464e0;
            }
            i {
              display: inline-block;
              width: 14px;
              height: 14px;
            }
            .icon-view {
              background: url('~@/assets/img/watch.png') no-repeat center /
                cover;
            }
            .icon-zan {
              background: url('~@/assets/img/zan1.png') no-repeat center / cover;
            }
            .icon-zan-active {
              color: #3464e0;
              i {
                background: url('~@/assets/img/zan1-active.png') no-repeat
                  center / cover;
              }
            }
            .icon-comment {
              background: url('~@/assets/img/comment.png') no-repeat center /
                cover;
            }
            .icon-collect {
              background: url('~@/assets/img/fav2.png') no-repeat center / cover;
            }
            .icon_reward {
              background: url('~@/assets/img/icon_reward.png') no-repeat center /
                cover;
            }
            .icon-collect-active {
              color: #3464e0;
              i {
                background: url('~@/assets/img/fav2-active.png') no-repeat
                  center / cover;
              }
            }
            .icon_reward-active {
              color: #3464e0;
              i {
                background: url('~@/assets/img/icon_reward_active.png')
                  no-repeat center / cover;
              }
            }
            .icon-edit {
              background: url('~@/assets/img/edit.png') no-repeat center / cover;
            }
            .icon-add {
              background: url('~@/assets/img/add.png') no-repeat center / cover;
            }
            .icon-share {
              background: url('~@/assets/img/share.png') no-repeat center /
                cover;
            }
            .icon-mobile {
              background: url('~@/assets/img/mobile.png') no-repeat center /
                cover;
            }
            .jf-icon {
              background: url('~@/assets/img/integral-icon.png') no-repeat
                center / cover;
              display: block;
              width: 20px;
              height: 20px;
              margin-right: 4px;
            }
            .jf-tip {
              color: #ff7548;
              position: absolute;
              right: 20px;
              top: -22px;
              display: flex;
              align-items: center;
            }
          }
          .right-icons {
            color: rgba(0, 0, 0, 0.6);
            span {
              margin-right: 16px;
              cursor: pointer;
            }
          }
        }
        .info1 .info-right {
          position: relative;
        }
        .info2 {
          border-bottom: solid 1px #eeeeee;
          padding-bottom: 8px;
          margin-bottom: 0;
        }
      }
      .geek-task-iframe {
        width: 100%;
        height: 600px;
        background-color: #fff;
        display: block;
      }
      .original-text-link {
        margin-bottom: 20px;
        display: flex;
        color: rgba(0, 0, 0, 0.6);
        span {
          width: 70px;
        }
        .icon-note {
          display: inline-block;
          width: 42px;
          height: 20px;
          border-radius: 2px;
          border: 1px solid#0052D9;
          color: #0052d9;
          background: rgba(52, 100, 224, 0.1);
          margin-right: 12px;
          font-style: normal;
          font-weight: bold;
          line-height: 18px;
          text-align: center;
        }
        .el-link {
          flex: 1;
          overflow: hidden; //溢出内容隐藏
          text-overflow: ellipsis; //文本溢出部分用省略号表示
          display: -webkit-box; //特别显示模式
          -webkit-line-clamp: 1; //行数
          line-clamp: 1;
          -webkit-box-orient: vertical; //盒子中内容竖直排列
        }
      }
      :deep(.sdc-editor-preview) {
        width: 100%;
        .content-wrapper {
          width: 100%;
        }
        .file-count {
          padding: 0 0 8px 24px;
        }
        .editor-file-list {
          margin: 0 24px 36px 24px;
        }
        .desc,
        .editor-content {
          -webkit-user-select: none;
          -moz-user-select: none;
          -ms-user-select: none;
          user-select: none;
          word-break: break-word;
          ol,
          ul {
            padding: revert;
          }
        }
        .desc {
          margin: 0 24px 0 24px;
        }
        .editor-content {
          padding: 20px 24px 32px 24px;
        }
      }
      :deep(.sdc-comment) {
        padding-top: 32px;
        border-top: solid 1px #eeeeee;
      }
    }
    .right {
      width: 272px;
      height: 100%;
      margin-left: 20px;
      .mbt-20 {
        margin-bottom: 20px;
      }
    }
    .right-fix-btns {
      position: fixed;
      bottom: 80px;
      // right: 34px;
      div {
        width: 50px;
        height: 50px;
        box-shadow: 0 0 4px 0 rgba(102, 102, 102, 0.3);
        border-radius: 50%;
        cursor: pointer;
        margin-bottom: 20px;
      }
      div:last-child {
        margin: 0;
      }
      .do-zan {
        background: url('~@/assets/img/do-zan.png') no-repeat center / cover;
      }
      .do-zan:hover,
      .do-zan-active:hover {
        background: url('~@/assets/img/do-zan-hover.png') no-repeat center /
          cover;
      }
      .do-zan-active {
        background: url('~@/assets/img/do-zan-active.png') no-repeat center /
          cover;
      }
      .do-collect {
        background: url('~@/assets/img/do-fav.png') no-repeat center / cover;
      }
      .do-collect:hover,
      .do-collect-active:hover {
        background: url('~@/assets/img/do-fav-hover.png') no-repeat center /
          cover;
      }
      .do-collect-active {
        background: url('~@/assets/img/do-fav-active.png') no-repeat center /
          cover;
      }
      .do-admire-active {
        background: url('~@/assets/img/do-admire-active.png') no-repeat center / cover;
      }
      .do-admire-active:hover {
        background: url('~@/assets/img/do-admire.png') no-repeat center / cover;
      }
    }
    .have-catalogue {
      .comment-box #commentBox {
        margin-left: 220px;
      }
    }
  }
  .study-tips-card-vh {
    height: calc(100vh - 60px);
    background-color: #fff;
  }
  .study-tips-card {
    padding: 40px 24px 26px;
    background: #fff;
    display: flex;
    justify-content: center;
    .tips-content {
      width: 580px;
      padding: 20px 0;
      border-radius: 8px;
      background: #F8FBFF;
    }
    .title {
      color: #0052d9;
      text-align: center;
      
      font-size: 20px;
      font-weight: 600;
      line-height: 28px;
      margin-bottom: 8px;
    }
    .msg {
      color: #000000e6;
      text-align: center;
      font-size: 16px;
      line-height: 24px;
      margin-bottom: 16px;
    }
    .btn {
      display: flex;
      justify-content: center;
      .btn-text {
        display: inline-block;
        width: 304px;
        height: 44px;
        line-height: 44px;
        text-align: center;
        cursor: pointer;
        border-radius: 24px;
        background: linear-gradient(90deg, #488DFF 0%, #1766FF 100%);
        color: #ffffff;
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 16px;
      }
      .btn-text_270 {
        width: 270px;
      }
      .btn-text_more {
        border: 1px solid #006DFF;
        background: #FFF;
        color: #006DFF;
      }
    }
    .data {
      width: 304px;
      margin: 0 auto;
      margin-bottom: 16px;
      p {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        span {
          line-height: 22px;
          color: #0052d9;
        }
      }
      p:last-child {
        margin-top: 8px;
        justify-content: center;
      }
      .spot {
        color: #00000066;
        text-decoration-line: line-through;
      }
    }
    .link {
      margin-top: 8px;
      font-size: 14px;
      line-height: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      .help-icon {
        background: url('~@/assets/mooc-img/help_circle.png') no-repeat center/cover;
        width: 16px;
        height: 16px;
        display: inline-block;
        margin-right: 4px;
      }
      .source-detail {
        color: #0052d9;
        text-decoration-line: underline;
        cursor: pointer;
      }
    }
  }

  .contain-main-1330 .left{
    width: 1330px !important;
  }
  .share-fixed {
    position: fixed;
    top: 200px;
    right: 50%;
    -webkit-transform: translateX(720px);
    transform: translateX(720px);

    .share-fixed-btn {
      width: 46px;
      height: 106px;
      padding: 0 5px;
      border-radius: 24px;
      background-color: #006DFF;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      cursor: pointer;
     
    }
    img {
      width: 24px;
      height: 24px;
      margin-bottom: 6px;
      display: block;
    }
    .share-text {
       color: #fff;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 18px;
    }
    .share-f-tips {
      position: absolute;
      bottom: -80px;
      left: -220px;
      color: #fff;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      width: 266px;
      height: 66px;
      padding: 12px 10px;
      text-align: left;
      flex-shrink: 0;
      border-radius: 8px;
      background: #006DFF;
      .tips-close {
        position: absolute;
        right: 8px;
        top: 8px;
        .el-icon-close {
          color: #FFF;
          font-weight: 600;
          cursor: pointer;
        }
      }

    }
    .share-f-tips::after {
      position: absolute;
      top: -18px;
      right: 15px;
        content: "";
        display: inline-block;
        width: 0;
        height: 0;
        border-top: 10px solid transparent; 
        border-right: 10px solid transparent;
        border-left: 10px solid transparent;
        border-bottom: 10px solid #006DFF;
      }
  }
  .fixed-more {
     top: 80px;
    .share-fixed-btn {
      background-color: #fff;
    }
    .share-text {
      color: #006DFF;
    }
  }
}
@media screen and (max-width: 1660px) {
  .have-catalogue {
    .editor-header,
    .comment-box #commentBox {
      width: 654px;
    }
    .info {
      .info-left {
        .info-classify p,
        .info-label p {
          max-width: 160px !important;
        }
        .create {
          max-width: 90px !important;
        }
      }
    }
  }
  .contain-main .left {
    width: 866px !important;
    .info {
      .info-left {
        .info-classify p,
        .info-label p {
          max-width: 200px;
        }
        .create {
          max-width: 270px;
        }
      }
    }
  }
  .create {
    max-width: 420px;
  }
}
@media screen and (min-width: 1661px) {
  .have-catalogue {
    .editor-header,
    .comment-box #commentBox {
      width: 928px;
    }
    .info {
      .info-left {
        .info-classify p,
        .info-label p {
          max-width: 240px !important;
        }
        .create {
          max-width: 350px !important;
        }
      }
    }
  }

  .contain-main .left {
    width: 1148px !important;
    .info {
      .info-left {
        .info-classify p,
        .info-label p {
          max-width: 360px;
        }
        .create {
          max-width: 570px;
        }
      }
    }
  }
  .create {
    max-width: 690px;
  }
}
</style>
