<template>
    <el-dialog
      :visible.sync="visible"
      :before-close="handleClose"
      :close-on-click-modal="false"
      :append-to-body="true"
      custom-class="subsDialog"
      width="480px">
      <header slot="title">
        <img :src="require('@/assets/img/warn.png')" alt="">
        删除官方标签
      </header>
      <div class="text">删除官方标签后，该标签将不再支持用户订阅，同时会为已订阅该标签的用户推送标签失效提醒并自动取消订阅，确定删除吗？</div>
      <p class="subsCount">当前订阅人数：<a>{{itemData.subscribe_count}}</a></p>
      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="out">取消</el-button>
        <el-button size="small" type="primary" @click="del">确定删除</el-button>
      </span>
    </el-dialog>
</template>
<script>
import { del_label } from 'config/api.conf'
export default {
  props: {
    visible: {
      type: Boolean,
      default: true
    },
    itemData: {}
  },
  data() {
    return {
      classification: '',
      click_modal: false
    }
  },
  methods: {
    handleClose() {
      this.$emit('update:visible', false)
    },
    del() {
      del_label(this.itemData.label_id).then((res) => {
        this.$message({
          type: 'success',
          message: '已删除'
        })
        this.$parent.getlist()
      })
      this.$emit('update:visible', false)
    },
    out() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
/deep/.el-dialog.subsDialog {
  font-family: "PingFang SC";
  border-radius: 9px;
  .el-dialog__header{
    padding: 32px 32px 16px;
    border: none;
    header {
      display: flex;
      align-items: center;
      height: 24px;
      color: #000000e6;
      text-overflow: ellipsis;
      white-space: nowrap;
      font-family: "PingFang SC";
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      img{
        width: 24px;
        margin-right: 8px;
      }
    }
    .el-dialog__headerbtn{
      top: 36px;
      right: 34px;
    }
  }
  .el-dialog__body {
    padding: 0 32px;
    .text {
      color: #00000099;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
    }
    .subsCount {
      margin-top: 10px;
      color: #000000e6;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      line-height: 22px;
      a{
        color: #0052d9;
      }
    }
  }
  .el-dialog__footer{
    padding: 0 32px 32px;
    margin-top: 24px;
    .el-button--default{
      width: 60px;
      color: #000000e6;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      border: none;
      background: var(--Gray-Gray3-, #E7E7E7);
    }
    .el-button--primary{
      width: 88px;
      color: #ffffff;
      text-align: center;
      font-family: "PingFang SC";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
      border: none;
      background: var(--Brand-Brand7-Normal, #0052D9);
    }
    .el-button + .el-button {
      margin-left: 8px;
    }
  }
}
</style>
