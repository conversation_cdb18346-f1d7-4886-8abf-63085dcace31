<template>
    <aside class="labelGather">
        <section class="pad labelGatherInfo" style="padding-right:24px;">
            <header>
                <h4>{{labelName || ''}}</h4>
                <p>{{ labelGatherPageData.category_full_name || '' }}</p>
            </header>
            <div class="flex gCount">
                <div class="center">
                    <p>{{labelGatherPageData.subscribe_count || 0}}</p>
                    <p>订阅人数</p>
                </div>
                <div class="center">
                    <p>{{labelGatherPageData.content_count || 0}}</p>
                    <p>相关内容</p>
                </div>
            </div>
            <el-button 
                v-if="isSubs" 
                type="danger" 
                size="mini" 
                class="subsBtn danger" 
                :dt-eid="dtEid('取消订阅')" 
                :dt-remark="dtRemark('取消订阅')"
                @click="cancelSubs(labelGatherPageData,null,null)"
            >取消订阅</el-button>
            <el-button 
                v-else 
                type="primary" 
                size="mini" 
                class="subsBtn" 
                @click="cancelSubs(labelGatherPageData, 1,null)"
                :dt-eid="dtEid('立即订阅')" 
                :dt-remark="dtRemark('立即订阅')"
            >立即订阅</el-button>
        </section>
        <section class="pad labelGatherCorr" v-if="relatedLabels.length>0 && labelGatherPageData.category_full_name.indexOf('其他/自定义') === -1">
            <h4>相关标签</h4>
            <div class="label-items-list">
              <div class="label-items-label" v-for="(item,index) in relatedLabels" :key="item.label_id + index" @mouseenter="getRelativeLabelDetail(item, index, $event)" @mouseleave="labelOut(item,index)">
                <span class="span_text" 
                    @click.stop="checkLabelInfo(item)"
                    :dt-eid="dtEid(item, '相关标签')" 
                    :dt-remark="dtRemark(item, '相关标签')"
                    :dt-areaid="dataAreaid(item, '相关标签')" 
                >{{ item.label_name}}
              </span>
                <article class="labelDetailBox" v-show="item.isShow" :style="{'top':item.top||0,'left':item.left|| 0}">
                    <header class="flex justify-between align-center">
                        <h3>{{item.label_name}}</h3>
                        <a href="javascript:;" @click.stop="setSubs">管理标签</a>
                    </header>
                    <p class="p1">{{ relatedLabelDetail.category_full_name }}</p>
                    <p class="p2">{{ relatedLabelDetail.subscribe_count }}订阅&nbsp; · &nbsp;{{ relatedLabelDetail.content_count }}内容</p>
                    <footer>
                        <el-button 
                            v-if="labelIds.includes(item.label_id)" 
                            type="danger" 
                            plain 
                            size="mini" 
                            @click.stop="cancelSubs(item,null,null)"
                        >取消订阅</el-button>
                        <el-button 
                            v-else 
                            type="primary" 
                            size="mini" 
                            @click.stop="cancelSubs(item, 1,null)"
                        >立即订阅</el-button>
                        <el-button 
                            plain 
                            size="mini" 
                            @click.stop="checkLabelInfo(item)" 
                        >查看相关内容</el-button>
                    </footer>
                </article>
              </div>
            </div>
        </section>
        <section class="pad labelGatherSubs">
            <h4>我订阅的标签</h4>
            <section class="flex align-center labelGatherBtnTool">
                <div 
                @click="setSubs" 
                >
                    <img 
                        :src="require('@/assets/img/setting.png')" 
                        alt=""
                        :dt-eid="dtEid('管理订阅标签', '我订阅的标签')" 
                        :dt-remark="dtRemark('管理订阅标签', '我订阅的标签')"
                    >
                    <span 
                        :dt-eid="dtEid('管理订阅标签', '我订阅的标签')" 
                        :dt-remark="dtRemark('管理订阅标签', '我订阅的标签')"
                    >管理订阅标签</span>
                </div>
                <div @click="toContentPage" style="margin-left:12px;">
                    <img 
                        :src="require('@/assets/img/wallet.png')" 
                        alt=""  
                        :dt-eid="dtEid('查看订阅内容', '我订阅的标签')" 
                        :dt-remark="dtRemark('查看订阅内容', '我订阅的标签')"
                    >
                    <span  
                        :dt-eid="dtEid('查看订阅内容', '我订阅的标签')" 
                        :dt-remark="dtRemark('查看订阅内容', '我订阅的标签')"
                    >查看订阅内容</span>
                </div>
            </section>
            <div class="label-items-list" v-if="labels?.length>0" style="max-height: calc(100% - 70px);">
              <div class="label-items-label" v-for="(item,index) in labels" :key="item.label_id + '11'" @mouseenter="getLabelDetail(item, index, false, $event)" @mouseleave="labelOut(item,index)">
                <span class="span_text" 
                    @click.stop="checkLabelInfo(item)"
                    :dt-eid="dtEid(item, '我订阅的标签')" 
                    :dt-remark="dtRemark(item, '我订阅的标签')"
                    :dt-areaid="dataAreaid(item, '我订阅的标签')" 
                >{{ item.label_name}}</span>
                <article class="labelDetailBox" v-show="item.isShow" :style="{'top':item.top||0,'left':item.left|| 0}">
                    <header class="flex justify-between align-center">
                        <h3>{{item.label_name}}</h3>
                        <a href="javascript:;" @click.stop="setSubs">管理标签</a>
                    </header>
                    <p class="p1">{{ labelDetail.category_full_name }}</p>
                    <p class="p2">{{ labelDetail.subscribe_count }}订阅&nbsp; · &nbsp;{{ labelDetail.content_count }}内容</p>
                    <footer>
                        <el-button type="danger" plain size="mini" @click.stop="cancelSubs(item,null,index)">取消订阅</el-button>
                        <el-button plain size="mini" style="margin-left:24px;" @click.stop="checkLabelInfo(item)">查看相关内容</el-button>
                    </footer>
                </article>
              </div>
            </div>
        </section>
      <!-- <labelSubsProps v-if="dialogVisible" :radioType="radioType" @input="subsLabelClose"></labelSubsProps> -->
    </aside>
</template>
<script>
import { getLabelDetail, getLabelBasicinfo } from '@/config/mooc.api.conf.js'
// import labelSubsProps from '../../src/index.vue'
export default {
  name: 'labelGather',
  props: ['labels', 'labelIds'],
  data() {
    return {
      // dialogVisible: false,
      radioType: 1,
      // 相关标签
      relatedLabels: [],
      // 相关标签详情
      relatedLabelDetail: {},
      labelGatherPageData: {},
      labelDetail: {}
    }
  },
  components: {
    // labelSubsProps
  },
  computed: {
    isSubs() {
      return this.labelIds.includes(Number(this.$route.query.label_id))
    },
    labelName() {
      return this.labelGatherPageData.label_name || this.$route.query.label_name
    },
    dtEid() {
      return (val, container) => {
        let label = container ? `_${container}` : ''
        if (typeof val === 'string') {
          return `element_${val}`
        }
        return `element_${val.label_id}${label}`
      }
    },
    dataAreaid() {
      return (val, container) => {
        let label = container ? `_${container}` : '' 
        if (typeof val === 'string') {
          return `area_${val}`
        }
        return `area_${val.label_id}${label}`
      }
    },
    dtRemark() {
      return (val, container) => {
        if (typeof val === 'string') {
          return JSON.stringify({ 
            page: '标签订阅内容', 
            page_type: '标签订阅内容',
            container: container || '',
            click_type: 'button',
            content_name: val,
            terminal: 'PC'
          })
        }
        return JSON.stringify({ 
          page: '标签订阅内容', 
          page_type: '标签订阅内容',
          container,
          click_type: 'data',
          content_name: val.label_name,
          content_id: val.label_id,
          terminal: 'PC',
          content_type: '标签'
        })
      }
    }
  },
  created() {
    let label_id = this.$route.query.label_id
    this.getLabelDetail({ label_id }, null, true, null)
  },
  methods: {
    // 获取相关标签
    getLabelBasicinfo() {
      this.relatedLabels = []
      let labelListDom = document.getElementsByClassName('labelGatherSubs')[0]
      if (this.labelGatherPageData.category_full_name.indexOf('其他/自定义') !== -1) {
        labelListDom.style.height = 'calc(100% - ' + 189 + 'px)'
        return
      }
      getLabelBasicinfo({ page_no: 1, page_size: 40, label_type: 1, category_id: this.labelDetail.category_id }).then(res => {
        res.records.forEach((item) => {
          item.isShow = false
        })
        let data = res.records.filter(ele => {
          return ele.label_id !== Number(this.labelGatherPageData.label_id)
        })
        this.relatedLabels = data
        this.$nextTick(() => {
          let h2 = document.getElementsByClassName('labelGatherCorr')?.[0].clientHeight + 1
          let top_h = document.getElementsByClassName('labelGatherInfo')?.[0].clientHeight + 1
          labelListDom.style.height = 'calc(100% - ' + (top_h + h2) + 'px)'
        })
      })
    },
    // 相关标签详情
    getRelativeLabelDetail(item, index, event) {
      let { top, left, height } = event.target.getBoundingClientRect()
      item.isShow = true
      item.top = top + height + 'px'
      item.left = left + 'px'
      if (index !== null && this.relatedLabels[index]['details']) {
        this.relatedLabelDetail = this.relatedLabels[index]['details']
        return
      }
      getLabelDetail({ label_id: item.label_id }).then(res => {
        this.relatedLabelDetail = res
        this.relatedLabelDetail.subscribe_type = 1
        this.relatedLabels[index]['details'] = { ...this.relatedLabelDetail }
      })
    },
    getLabelDetail(item, index = null, flag = false, event = null) {
      if (event) {
        let { top, left, height, y } = event.target.getBoundingClientRect()
        if ((document.body.clientHeight - y) < 150) {
          console.log(event.target.getBoundingClientRect())
          event.target.getElementsByClassName('labelDetailBox')[0].className = 'labelDetailBox labelDetailBox2'
          event.target.getElementsByClassName('labelDetailBox')[0].style.top = 'inherit'
          event.target.getElementsByClassName('labelDetailBox')[0].style.bottom = document.body.clientHeight - y + 'px'
        } else {
          event.target.getElementsByClassName('labelDetailBox')[0].style.bottom = 'inherit'
          event.target.getElementsByClassName('labelDetailBox')[0].className = 'labelDetailBox'
          item.top = top + height + 'px'
        }
        item.left = left + 'px'
        item.isShow = true
      }
      getLabelDetail({ label_id: item.label_id }).then(res => {
        this.labelDetail = res
        if (flag) {
          this.labelGatherPageData = {
            ...res
          }
          this.getLabelBasicinfo()
          return
        }
        this.$parent.labels[index]['details'] = res
      })
    },
    // 取消订阅
    cancelSubs(item, type = null, index = null) {
      this.$parent.cancelSubs(item, type, index)
    },
    labelOut(item, index) {
      item.isShow = false
    },
    // 查看订阅内容
    toContentPage() {
      window.open(window.location.href.split('?')[0])
    },
    // 管理订阅标签
    setSubs() {
      this.$parent.dialogVisible = true
    },
    // 查看相关内容
    checkLabelInfo(item, flag = false) {
      if (flag) {
        let data = []
        this.labels.forEach(ele => {
          if (item.label_id.includes(String(ele.label_id))) {
            data.push(ele)
          }
        })
        item = {
          label_id: data[0]?.label_id || '',
          label_name: data[0]?.label_name || ''
        }
      }
      if (!item?.label_id) return
      let url = ''
      if (window.location.href.indexOf('?')) {
        url = window.location.href.split('?')[0]
      } else {
        url = window.location.href
      }
      window.open(url + '?isLabelGatherPage=' + true + '&label_id=' + item.label_id + '&label_name=' + item.label_name)
    }
    // 监听订阅标签弹窗关闭
    // subsLabelClose() {
    //   this.dialogVisible = false
    // }
  }
}
</script>
<style lang="less" scoped>
@import '../../style/init.less';
.labelGather{
    position: relative;
    width: 248px;
    height: 100%;
    line-height: 24px;
    border-right: 1px solid var(--gray-gray-3, #E7E7E7);;
    padding: 0;
    .pad{
      padding: 16px 4px 16px 24px;
      h4 {
        line-height: 22px;
        font-weight: 600;
      }
    }
    .labelGatherInfo,.labelGatherCorr{
        border-bottom: 1px solid var(--gray-gray-3, #E7E7E7);
    }
    .labelGatherInfo{
        header{
            h4{
                font-weight: 600;
                color: #000000e6;
                line-height: 22px;
                font-size: 14px;
            }
            p{
                margin-top: 6px;
                color: #00000066;
                font-family: "PingFang SC";
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 20px;
            }
        }
        .gCount{
            margin: 12px 0;
            justify-content: space-around;
            div>p:nth-child(1){
                color: #0052d9;
                font-size: 24px;
                font-weight: 600;
                line-height: 32px;
            }
            div>p:nth-child(2){
                align-self: stretch;
                color: #00000099;
                font-size: 12px;
                font-weight: 400;
                line-height: 20px;
                margin-top: 4px;
            }
        }
        /deep/ .subsBtn{
            width: 100%;
            border-radius: 3px;
            background: var(--brand-brand-7-normal, #0052D9);
            font-size: 12px;
        }
        /deep/ .subsBtn.danger{
            background: #D54941;
            margin-left: 0;
        }

    }
    .label-items-list{
        max-height: 195px;
        margin-top: 10px;
        display: flex;
        flex-wrap: wrap;
        overflow: auto;
        .label-items-label{
          position: relative;
          span.span_text{
            display: inline-block;
            padding: 4px;
            border-radius: 2px;
            background: #EBEFFC;
            color: #0052d9;
            font-size: 12px;
            margin: 6px 12px 6px 0;
            line-height: 12px;
            cursor: pointer;
          }
        }
    }
    .labelGatherBtnTool{
        margin: 12px 0;
        color: #0052d9;
        font-size: 12px;
        line-height: 20px;
        margin-right: 20px;
        border-bottom: 1px solid #eee;
        padding-bottom: 14px;
        div{
            display: flex;
            align-items: center;
            cursor: pointer;
            span{
              line-height: 20px;
            }
        }
        img{
            padding: 3px 0;
            width: 14px;
            margin-right: 4px;
        }
    }
}
.labelDetailBox::before{
  content: '';
  position: absolute;
  border-bottom: 7px solid #fff;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  top: -7px;
}
.labelDetailBox2::before{
  content: '';
  position: absolute;
  border-bottom: none;
  border-top: 7px solid #fff;
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  top: inherit;
  bottom: -7px;
}
.labelDetailBox,.labelDetailBox2{
  position: fixed;
  z-index: 10;
  bottom: inherit;
  top: 32px;
  left: 0px;
  font-family: "PingFang SC";
  width: auto;
  min-width: 200px;
  height: auto;
  background: #fff;
  border-radius: 6px;
  padding: 12px;
  box-shadow: 0 0 10px #ccc;
    >header{
        h3{
            color: #000000e6;
            font-size: 14px;
            font-weight: bold;
            line-height: 22px;
            white-space:nowrap
        }
        a{
            color: #00000042;
            line-height: 20px;
            font-size: 12px;
            margin-left: 10px;
            white-space:nowrap
        }
    }
    .p1,.p2{
        color: #00000066;
        margin-top: 6px;
        line-height: 20px;
        font-size: 12px;
        white-space:nowrap
    }
    .p2{
        color: #00000099;
    }
    footer{
        display: flex;
        margin-top: 6px;
        :deep(.el-button){
            padding: 2px 7px !important;
            line-height: 20px !important;
            font-family: "PingFang SC";
        }
        .el-button.el-button--primary {
          color: #FFF;
          background-color: #0052D9;
          border-color: #0052D9;
        }
        .el-button--danger.is-plain{
            background: none;
            color: #D54941;
            border-color: #D54941;
        }
        .el-button--danger.is-plain:hover{
            background: none;
            color: #D54941;
        }
        .el-button--default{
          color: #000000e6;
          margin-left: 24px;
        }
    }
}
.labelDetailBox2{
  top:inherit !important;
  bottom: 0;
}
</style>
