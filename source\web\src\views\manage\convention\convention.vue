<template>
  <div style="background-color: #F6F7F9;width: 100%;overflow: auto;padding: 20px 0;height:100%">
    <div class="convention-container">
      <div class="header">
        <h1>腾讯学堂学习平台文明公约</h1>
      </div>

      <div class="section">
          <div class="section-title">一、目的</div>
          <div class="content">
              为了维护腾讯学堂学习平台的健康、有序环境，确保员工能够在平台上进行有效的分享、学习和交流，我们制定《腾讯学堂学习平台文明公约》。本公约旨在为员工提供一个清晰的行为准则，避免对员工、公司造成不良影响。
          </div>
      </div>

      <div class="section">
          <div class="section-title">二、适用人群</div>
          <div class="content">
              本公约适用于所有能够访问腾讯学堂学习平台及其相关内容的腾讯员工和相关人员，包括但不限于正式员工、实习生、顾问、外包人员、合作伙伴等。
          </div>
      </div>

      <div class="section">
          <div class="section-title">三、适用范围</div>
          <div class="sub-section">
              <div class="sub-title">3.1 腾讯学堂学习平台</div>
              <div class="content">
                  包括但不限于<strong>Q-Learning主站、慕课、案例、行家、课单、文章笔记、专区、直播、面授课、考试、游戏测评、岗前培训、知点</strong>等站点。
              </div>
          </div>
          <div class="sub-section">
              <div class="sub-title">3.2 学习内容</div>
              <div class="content">
                  指上传至腾讯学堂学习平台并创建的所有内容信息，形式包括但不限于<strong>网课、文章、直播、案例、慕课、考试/测试、测评、面授课、活动、行家、SPOC、专区、班级、各类素材</strong>等。
              </div>
          </div>
          <div class="sub-section">
              <div class="sub-title">3.3 评论、讨论、提问、答题等互动内容</div>
              <div class="content">
                  指在腾讯学堂学习台内发表的包括但不限于<strong>课后评论/讨论、问卷反馈评价/评语、直播讨论/提问、考试/问卷中答题</strong>等互动内容。
              </div>
          </div>
          <div class="sub-section">
              <div class="sub-title">3.4 内部宣推素材</div>
              <div class="content">
                  指发布在腾讯学堂对接的各内部渠道宣传推广的素材内容，学堂宣推渠道包括但不限于<strong>邮件、企微消息提醒tips、企微机器人、企微应用号、微信公众号、学堂K吧、HR助手、myOA提醒</strong>等。
              </div>
          </div>
      </div>

      <div class="section">
          <div class="section-title">四、角色与责任</div>
          <table>
              <tr>
                  <th>人员角色</th>
                  <th>角色说明</th>
                  <th>主要责任</th>
              </tr>
              <tr>
                  <td>内容发布人</td>
                  <td>包括但不限于<strong>发布学习内容、内部宣推素材</strong>的所有人员</td>
                  <td>
                      <ul>
                          <ul class="with-bullets">
                          <li>对所发表的内容负有主要责任，内容发布前务必自检自查，确保内容质量，不触犯国家、公司的相关规章制度、平台管理规范和公约。</li>
                          <li>内容的<strong>访问权限和推广对象</strong>人群设定，遵循<strong>最小化原则</strong>，避免过度打扰。</li>
                          </ul>
                      </ul>
                  </td>
              </tr>
              <tr>
                  <td>平台管理员</td>
                  <td>包括但不限于腾讯学堂学习平台的<strong>内容审核、系统操作</strong>的人员</td>
                  <td>
                      <ul>
                          <ul class="with-bullets">
                          <li>对入库Q-Learning的所有内容和宣推素材进行基础审核。</li>
                          <li>协助排查、处理违规内容。</li>
                          </ul>
                      </ul>
                  </td>
              </tr>
          </table>
      </div>

      <div class="section">
          <div class="section-title">五、违规行为</div>
          <div class="content">
              腾讯学堂学习平台不是法外之地，所有在腾讯学堂学习平台发表的学习内容、评论\讨论\提问\答题内容，以及通过腾讯学堂腾讯学堂学习平台对接的各渠道发布的推广素材，均需严格遵守法律法规，不得出现以下各类违规的内容：
          </div>
          
          <div class="sub-section">
              <div class="sub-title">5.1 违反法律法规、政治敏感类内容。</div>
              <ul>
                  <li>1）反对宪法所确定的基本原则的；</li>
                  <li>2）危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一，损害国家荣誉和利益的；</li>
                  <li>3）侮辱、滥用英烈形象，否定英烈事迹，美化侵略战争；</li>
                  <li>4）煽动民族仇恨、民族歧视，破坏民族团结的；</li>
                  <li>5）破坏国家宗教政策，宣扬邪教和封建迷信的；</li>
                  <li>6）散布谣言，扰乱社会秩序，破坏社会稳定的；</li>
                  <li>7）散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；</li>
                  <li>8）侮辱或诽谤他人，泄露他人隐私，侵害他人合法权益的；</li>
                  <li>9）含有法律、行政法规禁止的其他内容的；</li>
              </ul>
          </div>

          <div class="sub-section">
              <div class="sub-title">5.2 色情低俗暴力内容。</div>
              <div class="content">禁止发布任何形式的色情淫秽内容，令人不适的血腥、恐怖内容，包括但不限于：</div>
              <ul>
                  <li>1）具有性暗示，直接或隐晦表现性行为、性过程的内容，包括但不限于动图、截图、文字、链接、视频、图片等；</li>
                  <li>2）涉及色情段子、两性笑话，或庸俗、挑性的图片、文字等内容；</li>
                  <li>3）虐杀动物或宠物的图片、视频、动图等；</li>
                  <li>4）严重的事故现场图片、视频、动图等；</li>
                  <li>5）自杀、自残、自毁等类型的图片、视频、动图等；</li>
                  <li>6）恐怖袭击，行刑，血腥战争等相关内容。</li>
              </ul>
          </div>

          <div class="sub-section">
              <div class="sub-title">5.3 不友善的内容。</div>
              <div class="content">禁止发布人身攻击、歧视、挑衅、引战等不友善内容，包括但不限于：</div>
              <ul>
                  <li>1）针对国籍、地域、性别、性别认同、种族、疾病、宗教、残障等身份或归类，发表歧视、诅咒、挑衅的内容；</li>
                  <li>2）人肉搜索，或未获他人允许，偷拍、偷录他人，侵害他人合法权利的内容；</li>
                  <li>3）针对他人或他人劳动成果，对个人或某个群体，发表羞辱、谩骂、贬低、讽刺、挖苦、威胁等的内容；</li>
                  <li>4）将具体问题上升、扩大为一般性问题，挑动个人与组织对立，对他人恶意贴标签，进行上纲上线式批判的内容；</li>
                  <li>5）与工作关联不大的争议性话题，发布后引发大规模跟帖、对立等情况的内容；</li>
                  <li>6）属于个人纠纷，利用内部社区和舆论进行道德审判的内容。</li>
              </ul>
          </div>

          <div class="sub-section">
              <div class="sub-title">5.4 申诉、举报类内容。</div>
              <div class="content">
                  公司为申诉和举报类问题提供专有反馈渠道：<a href="http://shensu.woa.com" target="_blank">shensu.woa.com</a> 和 <a href="mailto:<EMAIL>"><EMAIL></a>，由相关团队专人调查跟进。基于前述腾讯学堂学习平台的定位，禁止在非专有渠道发布申诉、举报类内容，包括但不限于：
              </div>
              <ul>
                  <li>1）对绩效、职级、离职、试用期考核等评定结果或相关人的申诉；</li>
                  <li>2）违反《腾讯管理干部违纪处罚办法》的投诉；</li>
                  <li>3）违反《腾讯阳光行为准则》的违规行为举报；</li>
                  <li>4）不属于以上情况，但涉及对具体同事、组织违反公司相关管理规范、制度的投诉和举报；</li>
              </ul>
          </div>

          <div class="sub-section">
              <div class="sub-title">5.5 造谣、造假类内容。</div>
              <div class="content">禁止发布任何形式的造谣、造假等不实信息，包括但不限于：</div>
              <ul>
                  <li>1）毫无事实根据地恶意猜测、诽谤、污蔑、诋毁，导致他人或组织名誉受损；</li>
                  <li>2）造谣传谣，发布虚假或无法确认真假的信息，扰乱内部社区和公司秩序；</li>
                  <li>3）夸大、歪曲、隐藏实际情况，或伪造虚假经历等，误导他人判断；</li>
                  <li>4）恶意编辑、清空、删除有效内容，影响他人阅读，误导他人判断；</li>
                  <li>5）发布内容与标题之间存在严重不符，或不合常理的表现手法等，诱导用户；</li>
              </ul>
          </div>

          <div class="sub-section">
              <div class="sub-title">5.6 其他不合适的内容，包括但不限于：</div>
              <ul>
                  <li>1）违反公司相关管理规定</li>
                  <li>2）频繁发布，或者在不同分类、板块下重复发布相同或近似的内容，影响平台体验；</li>
                  <li>3）其他违反法律法规或社会公序良俗、道德制度的。</li>
              </ul>
          </div>
      </div>

      <div class="section">
          <div class="section-title">六、违规处理</div>
          <div class="sub-section">
              <div class="sub-title">6.1 访问权限调整、内容出库、内容删除、取消权限</div>
              <div class="content">
                  如发布了本公约禁止的内容，将根据情节严重程度进行处罚，包括但不限于对内容进行访问权限调整、内容出库、内容删除处理；如程度严重，将取消内容发布人的发布权限。
              </div>
          </div>

          <div class="sub-section">
              <div class="sub-title">6.2 违规处理</div>
              <div class="content">
                  若存在以下情形的，公司还将依据情节<strong>严重程度</strong>定位并追究内容发布人相应责任，具体处理措施参照<strong>《腾讯阳光行为准则》</strong>及其他相关规定执行。
              </div>
              <ul class="with-bullets">
                  <li>违反《互联网信息服务管理办法》《网络安全法》等相关法律法规的；</li>
                  <li>违反《腾讯阳光行为准则》等公司规章制度的；</li>
                  <li>严重扰乱公司管理秩序，损害公司利益或对公司造成不良影响的。</li>
              </ul>
          </div>
      </div>

      <div class="footer">
          本公约为动态更新的文档，我们会根据新出现的问题、相关法律法规、公司要求更新或运营的需要来对其内容进行修改并更新。请各位员工定期查看以便获得最新信息。
      </div>
    </div>
  </div>
</template>

<script>
export default {
  components: {},
  watch: {
    '$store.state.userInfo': {
      handler(val) {
        if (val.staff_name) {
          const commonHeaderJsUrl = window.location.hostname.endsWith('.woa.com') ? `https:${process.env.VUE_APP_PORTAL_HOST_WOA}/commonHeader.js` : `http:${process.env.VUE_APP_PORTAL_HOST}/commonHeader.js`
          this.loadHeadJS(commonHeaderJsUrl, function() {
            if (window.$qlCommonHeader) {
              window.$qlCommonHeader.create({
                staff_name: val.staff_name,
                dev: !(process.env.NODE_ENV === 'production')
              })
              setTimeout(() => {
                let el = document.getElementsByClassName('common-header-occupy')
                if (el.length > 0) {
                  console.log('El', el[0].offsetHeight)
                  document.getElementById('app').style.height = `calc(100% - ${el[0].offsetHeight}px)`
                }
              }, 500)
            }
          })
        }
      },
      immediate: true
    }
  },
  mounted() {
    // 禁用右键菜单
    // document.addEventListener('contextmenu', function(e) {
    //   e.preventDefault()
    // })

    // // 禁用键盘快捷键
    // document.addEventListener('keydown', function(e) {
    //   // 禁用 Ctrl + C, Ctrl + V, Ctrl + S, Ctrl + P, Ctrl + Shift + I
    //   if (e.ctrlKey && (
    //     e.keyCode === 67 || // C
    //     e.keyCode === 86 || // V
    //     e.keyCode === 83 || // S
    //     e.keyCode === 80 || // P
    //     e.keyCode === 73 // I
    //   )) {
    //     e.preventDefault()
    //   }
    //   // 禁用 F12
    //   if (e.keyCode === 123) {
    //     e.preventDefault()
    //   }
    // })

    // // 禁用开发者工具快捷键
    // document.addEventListener('keydown', function(e) {
    //   if (e.ctrlKey && e.shiftKey && (
    //     e.keyCode === 73 || // I
    //     e.keyCode === 74 // J
    //   )) {
    //     e.preventDefault()
    //   }
    // })
  },
  methods: {
    // 获取登陆用户信息
    loadHeadJS (url, callback) {
      var script = document.createElement('script')
      var fn = callback || function() {}
      script.type = 'text/javascript'
      script.id = 'graphic-common-head'
      // IE
      if (script.readyState) {
        script.onreadystatechange = function() {
          if (script.readyState === 'loaded' || script.readyState === 'complete') {
            script.onreadystatechange = null
            fn()
          }
        }
      } else {
        // 其他浏览器
        script.onload = function() {
          fn()
        }
      }
      script.src = url
      document.getElementsByTagName('head')[0].appendChild(script)
    }
  }
}
</script>

<style lang="less" scoped>
.convention-container {
    max-width: 1200px;
    margin: 0 auto;
}
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: "PingFang SC-Medium", Helvetica;
    line-height: 1.6;
    color: #333;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f5f5f5;
    font-size: 16px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.header h1 {
    font-size: 24px;
    color: #333;
}

.header {
    background: linear-gradient(to bottom, #E8F0FD, #ffffff);
    color: #333;
    padding: 20px;
    text-align: center;
    border-radius: 8px 8px 0 0;
    margin-bottom: 20px;
}

.section {
    background: white;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.section-title {
    font-size: 20px;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
}

.sub-title {
    font-size: 20px;
    font-weight: bold;
    margin: 10px 0;
}

.content {
    margin-bottom: 15px;
    padding: 15px;
    border-radius: 4px;
    font-size: 16px;
    a {
        color: #0000ee;
        text-decoration: underline;
    }
}

table {
    width: 100%;
    border-collapse: collapse;
    margin: 15px 0;
    font-size: 16px;
}

/* 设置第一列的宽度 */
table th:first-child,
table td:first-child {
    min-width: 80px;  /* 可以根据需要调整这个值 */
    width: 10%;        /* 设置为总宽度的20% */
}

/* 设置第二列的宽度 */
table th:nth-child(2),
table td:nth-child(2) {
    width: 30%;
}

/* 设置第三列的宽度 */
table th:last-child,
table td:last-child {
    width: 50%;
}

th, td {
    border: 1px solid #ddd;
    padding: 12px;
    text-align: left;
}

th {
    background: #f8f9fa;
}

/* 默认移除列表项的圆点 */
ul {
    list-style-type: none;
    padding-left: 20px;
    font-size: 16px;
}

/* 为6.2部分的列表添加圆点 */
ul.with-bullets {
    list-style-type: disc;
    padding-left: 20px;
}

/* 只在6.2部分保留列表项的圆点 */
.section:last-child .sub-section:last-child ul {
    list-style-type: disc;
}

li {
    margin: 5px 0;
}

.footer {
    font-style: normal;
    text-align: left;
    margin-top: 30px;
    padding: 20px;
    background: #f5f5f5;
    border-radius: 4px;
    font-size: 16px;
}

@media (max-width: 768px) {
    body {
        padding: 10px;
    }

    .header {
        padding: 15px;
    }

    .section {
        padding: 15px;
    }

    th, td {
        padding: 8px;
    }
}

</style>
