<template>
  <el-Dialog 
  :visible="visible" 
  title="" 
  width="600px" 
  :close-on-click-modal="false" 
  @close="cancel"
  class="common-dialog"
  >
    <div slot="title">
      <div class="title-content">
        <span class="title">匹配风险详情</span>
        <span class="tip">（均为建立辅导关系时的切片数据）</span>
      </div>
    </div>
    <div class="common-body">
      <div class="risk-desc-item" v-if="noThreshold">
        <div class="risk-desc-title">{{ noList.indexOf('threshold') }}. 不满足以下门槛资格：</div>
        <div class="risk-desc-subitem">
          <div class="risk-desc-subitem-text" v-for="(item, index) in detailInfo.threshold_risk_detail" :key="index">
            <el-tooltip class="item" effect="dark" :content="item.requirements_content" placement="top-end" :disabled="calcStrNum(item.requirements_content) < minHidNum">
              <div class="risk-tooltip-inner">
                <img class="risk-item-icon" :src="require('@/assets/tutor/error-icon.png')" alt="">{{ item.requirements_content }}
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="risk-desc-item" v-if="noCert">
        <div class="risk-desc-title">{{ noList.indexOf('cert') }}. 不满足以下认证要求：</div>
        <div class="risk-desc-subitem">
          <div class="risk-desc-subitem-text" v-for="(item, index) in detailInfo.cert_risk_detail" :key="index">
            <el-tooltip class="item" effect="dark" :content="item.requirements_content" placement="top-end" :disabled="calcStrNum(item.requirements_content) < minHidNum">
              <div class="risk-tooltip-inner">
                <img class="risk-item-icon" :src="require('@/assets/tutor/error-icon.png')" alt=""><span @click="toAuthenticationPage(item.course_url)" class="risk-2-link">{{ item.requirements_content }}</span>
              </div>
            </el-tooltip>
          </div>
        </div>
      </div>
      <div class="risk-desc-item" v-if="moreStudents">
        <div class="risk-desc-title"><span class="q-no">{{ noList.indexOf('student') }}. </span><span>导师正在辅导{{moreStudents > 3 ? 3 : moreStudents}}位及以上处于辅导期的新员工：{{ detailInfo.student_names.join('、') }}</span></div>
      </div>
      <div class="risk-desc-item" v-if="isDiffDepart">
        <div class="risk-desc-title">
          <span class="q-no">{{ noList.indexOf('depart') }}.</span>
          <span>
              导师与新员工部门不同：<br />
              导师所属部门：
              <template v-if="detailInfo.tutor_dept">{{ detailInfo.tutor_dept }}</template>
              <span v-else class="empty-text">暂无数据</span>
              <br />
              新人所属部门：
              <template v-if="detailInfo.student_dept_name">{{ detailInfo.student_dept_name }}</template>
              <span v-else class="empty-text">暂无数据</span>
          </span>
        </div>
      </div>
      <div class="risk-desc-item" v-if="isDiffWorkPlace">
        <div class="risk-desc-title">
          <span class="q-no">{{ noList.indexOf('workPlace') }}.</span>
          <span>
            导师与新员工工作地不同：<br/>
              导师工作地：
              <template v-if="detailInfo.tutor_work_place"> {{ detailInfo.tutor_work_place }}</template> 
              <span v-else class="empty-text">暂无数据</span>
              <br/>
              新员工工作地：
              <template v-if="detailInfo.student_work_place_name">{{ detailInfo.student_work_place_name }}</template>
              <span v-else class="empty-text">暂无数据</span>
          </span>
        </div>
      </div>
      <div class="reson-box" v-if="detailInfo.tutor_match_reason">
        <div class="label">匹配原因：</div>
        <sdc-mce-preview
        class="reason-content"
        ref="editor"
        :urlConfig="editorConfig.urlConfig"
        :catalogue.sync="editorConfig.catalogue"
        :content="detailInfo.tutor_match_reason || '-'"
        >
        </sdc-mce-preview>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button style="width:100px" @click="cancel" type="primary" size="small">知道了</el-button>
    </span>
  </el-Dialog>
</template>
<script>
export default {
  props: {
    visible: Boolean,
    detailInfo: Object
  },
  data() {
    return {
      minHidNum: 44, // 超过44个字时超出隐藏显示tooltip
      editorConfig: {
        catalogue: false,
        urlConfig: {
          operatesignature: `/training/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `/content-center/api/v1/content/{contentId}`, // 获取内容id
          preview: `/content-center/api/v1/content/imgage/{contentId}/preview`
        }
      }
    }
  },
  computed: {
    // 不满足门槛资格
    noThreshold() {
      return this.detailInfo.threshold_risk_detail && this.detailInfo.threshold_risk_detail.length
    },
    // 不满足认证要求
    noCert() {
      return this.detailInfo.cert_risk_detail && this.detailInfo.cert_risk_detail.length
    },
    // 辅导多位新员工
    moreStudents() {
      return this.detailInfo.student_names && this.detailInfo.student_names.length
    },
    // 导师与新员工部门不同
    isDiffDepart() {
      try {
        return ![undefined, null].includes(this.detailInfo.tutor_dept_id) && this.detailInfo.tutor_dept_id !== this.detailInfo.student_dept_id
      } catch (error) {
        return false
      }
    },
    // 导师与新员工工作地不同
    isDiffWorkPlace() {
      try {
        return ![undefined, null].includes(this.detailInfo.tutor_work_place_id) && this.detailInfo.tutor_work_place_id !== this.detailInfo.student_work_place_id
      } catch (error) {
        return false
      }
    },
    noList() {
      const list = ['占位符']
      if (this.noThreshold) {
        list.push('threshold')
      }
      if (this.noCert) {
        list.push('cert')
      }
      if (this.moreStudents) {
        list.push('student')
      }
      if (this.isDiffDepart) {
        list.push('depart')
      }
      if (this.isDiffWorkPlace) {
        list.push('workPlace')
      }
      return list
    }
  },
  mounted() {},
  methods: {
    calcStrNum(value) {
      if (value) {
        const china = value.match(/[\u4e00-\u9fa5]/g)
        const zhCount = china && china.join('').length
        const enCount = Math.ceil((value.length - zhCount) / 2)
        const total = zhCount + enCount
        return total || 0
      }
      return 0
    },
    toAuthenticationPage(course_url) {
      if (!course_url) return
      course_url && window.open(course_url)
    },
    cancel() {
      this.$emit('update:visible', false)
    }
  }
}
</script>
<style lang="less" scoped>
.common-dialog {
  :deep(.el-dialog__header) {
    padding: 20px;
    .title-content {
      .title {
        color: #333333;
        font-size: 16px;
        font-weight: 600;
      }
      .tip {
        color: #00000099;
        font-size: 12px;
      }
    }
    
  }
  :deep(.el-dialog__body) {
    padding: 20px 2px 0 20px;
  }
  :deep(.el-dialog__footer) {
    padding: 0 20px 20px;
  }

  .common-body {
    padding-right: 6px;
    max-height: 378px;
    overflow-y: auto;
    .risk-desc-item {
      margin-bottom: 12px;
      .risk-desc-title {
        color: #000000e6;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        .q-no {
          margin-right: 3px;
        }
        .empty-text {
          color: #d54941;
        }
      }
      .risk-desc-subitem {
        display: flex;
        flex-wrap: wrap;
        .risk-desc-subitem-text {
          margin: 8px 8px 0 0;
          overflow: hidden;
          .risk-tooltip-inner {
            display: flex;
            align-items: center;
            padding: 2px 8px;
            line-height: 20px;
            font-size: 12px;
            color: #d54941;
            border-radius: 3px;
            background: #FFF0ED;
            .risk-item-icon {
              width: 14px;
              margin-right: 4px;
            }
            .risk-2-link {
              cursor: pointer;
              text-decoration: underline;
              text-decoration-skip-ink: auto;
              text-underline-offset: 0.25em;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              display: inline-block;
            }
          }
        }
      }

    }
    .reson-box {
      margin-bottom: 20px;
      .label {
        color: #00000099;
        font-size: 14px;
        line-height: 22px;
      }
      .reason-content {
        margin-top: 4px;
        :deep(.editor-content) {
          font-size: 14px;
          color: #040000;
          line-height: 22px;
        }
      }
    }
  }
}
</style>
