<template>
  <div id="tutorApp">
    <router-view></router-view>
  </div>
</template>
  
<script>
import { refresh, resize } from 'sdc-vue'
import translate from 'mixins/translate.vue'
import { getLoginUser } from './api/tutor.api.conf.js'
export default {
  name: 'app',
  mixins: [refresh, resize, translate],
  mounted() {
    this.getUser()
  },
  watch: {
  },
  methods: {
    // 获取登陆用户信息
    getUser() {
      getLoginUser().then((res) => {
        const userInfo = {
          staff_id: res.staff_id,
          staff_name: res.staff_name
        }
        this.$store.commit('setUserInfo', userInfo)
        sessionStorage.setItem('login_user', JSON.stringify(userInfo))
      })
    }
  }
}
</script>
  
<style lang="less">
@import '~assets/css/app.less';
@import '~assets/css/el-style.less';
@import '~assets/css/common.less';

#tutorApp {
  font-family: @PingFangSC;
  font-weight: 400;
  height: 100%;
}
</style>
