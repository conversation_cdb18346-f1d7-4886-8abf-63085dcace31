<!-- //聚合报名详情 -->
<template>
  <div class="aggregate-page" :style="styleBgColor">
    <div class="aggregate-head">
      <img :src="polymerInfo.pc_bg_photo_url">
    </div>
    <div class="aggregate-content">
      <div>
        <div class="aggregate-content_top">
          <div class="top-info">
            <div class="top_title">
              {{ polymerInfo.polymer_name }}
            </div>
            <div class="info-item">
              <span>{{ polymerInfo.creator_name }}</span>
              <span>{{ polymerInfo.created_at || '' }}</span>
              <span class="no-copy">浏览（{{ polymerInfo.view_count || '0' }}）</span>
              <span @click="handleShare(polymerInfo)" class="pointer no-copy"><i class="icon-shear"></i>分享</span>
            </div>
          </div>
        </div>
        <div class="desc-text">
          <div :class="['desc-content', { 'more-des-content': !isMore }]">
            <sdc-mce-preview ref="preview" class="text-preview" :isShowFileContent="false"
              :urlConfig="editorConfig.urlConfig" :catalogue.sync="editorConfig.catalogue" :content="polymerInfo.brief">
            </sdc-mce-preview>
            <!-- <OteamRichText class="text-preview" :textValue="testValue" ref="oteamRichTextRef2" mode="preview" /> -->
            <div v-if="isPreviewMore" :class="['transparent-more', { 'pack-des-content': !isMore }]">
              <span class="more-text no-copy" @click="isMore = !isMore">
                {{ isMore ? '展开查看更多...' : '收起' }}
              </span>
            </div>
          </div>
        </div>
        <div class="options-class">
          <div class="aggregate_head">
            <img src="@/assets/img/error-circle-filled.png"><span class="no-copy">请在以下内容中，选中想要报名的内容后，点击页面底部按钮提交报名</span>
          </div>
          <div v-for="(contentsInfo, indexInfo) in polymerInfo.category_contents" :key="indexInfo" class="directory">
            <div class="directoryTitle">
              <span v-show="contentsInfo.show_name">{{ contentsInfo.category_name }}</span><span class="chekAll">
                <el-checkbox @input="handelrAllChangeRadio($event, contentsInfo)"
                  v-model="contentsInfo.selectAll">全选此模块</el-checkbox></span><span class="span_nav">共 {{
                    contentsInfo.contents.length }} 门内容</span><span
                class="no-copy">注意：使用全选功能时，如内容同时支持多种参加形式，将默认选中其中第一种形式</span>
              <span :class="['transparent-more', { 'pack-des-content': !contentsInfo.listIsMore }]">
                <span class="more-text no-copy" @click="isMoreNextTick(contentsInfo)">
                  {{ !contentsInfo.listIsMore ? '展开' : '收起' }}<i
                    :class="!contentsInfo.listIsMore ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
                </span>
              </span>
            </div>
            <template v-if="polymerInfo.style_type === 1">
              <div v-show="contentsInfo.listIsMore" class="directoryList">
                <div class="options-item" v-for="(item, index) in contentsInfo.contents"
                  :key="`${item.act_id}_${item.category_id}_${item.act_type}`">
                  <div class="title">
                    <span class="title_text"><span class="color005">{{ index + 1 }}.</span>
                      <!-- <el-tooltip content="异地报名、跨天班级需要上级审批" placement="top" popper-class="sub-poper-class">
                    <span class="review-r" v-if="[3, 4].includes(item.act_type) && item.need_appovel">审</span>
                  </el-tooltip> -->
                      {{ item.content_info.content_name }}</span>
                    <span class="see_detail no-copy" @click="handlerDialog(item.content_info)">查看详情</span>
                  </div>
                  <div class="item-content">
                    <div class="item-content_left">
                      <div class="info-lable"><i class="icon icon-time"></i> {{
                        formatTimeSafely(item.content_info.origin_data.start_time)
                      }} - {{
                          formatTimeSafely(item.content_info.origin_data.end_time) }}</div>
                      <div class="info-lable"><i class="icon icon-name"></i> {{
                        teacherName(item.content_info.origin_data, 3)
                      }}</div>
                      <div class="info-lable"><i class="icon icon-city"></i>在线直播： <el-link v-if="item.act_type === 5"
                          class="a-link" :href="item.content_info.content_url" type="primary" target="_blank">{{
                            item.content_info.content_url }}</el-link>
                      </div>
                    </div>
                    <div class="item-content_right">
                      <div class="content_right-group no-copy">
                        <el-radio-group :disabled="isPreview" v-model="item.join_type" class="content-right-group-radio"
                          @input="handelrActivityChangeRadio($event, item)">
                          <el-radio :label="0" :disabled="disableDadioAct(item, 2)">不报名</el-radio>
                          <el-radio v-if="item.act_type === 3 || item.act_type === 4" :label="1"
                            :disabled="disableDadioAct(item, 0)">线下参加（剩余名额：100）</el-radio>
                          <el-radio v-if="item.act_type === 3 || item.act_type === 4" :label="2"
                            :disabled="disableDadioAct(item, 1)">线上参加</el-radio>
                          <el-radio v-if="item.act_type === 5" :label="3"
                            :disabled="disableDadioAct(item, 3)">预约观看直播和回看</el-radio>
                        </el-radio-group>
                      </div>
                    </div>
                  </div>
                </div>

              </div>
            </template>
            <template v-if="polymerInfo.style_type === 2">
              <div v-show="contentsInfo.listIsMore" class="directoryCard">
                <div class="options-item " :class="item.join_type !== 0 ? 'borderIsCheke' : ''"
                  v-for="item in contentsInfo.contents" :key="`${item.act_id}_${item.category_id}_${item.act_type}`">
                  <img v-if="item.content_info.photo_url" :src="item.content_info.photo_url">
                  <img v-else :src="formatModuleMap(item.content_info.module_id)" />
                  <div class="cardContent">
                    <div class="cardTitle">
                      <span class="title1">{{ item.content_info.module_name }}</span>{{ item.content_info.content_name
                      }}
                    </div>
                  </div>
                  <div class="cardDetails">
                    <div>
                      <img src="@/assets/img/time-class.png">
                      <span>{{ formatTimeSafely(item.content_info.origin_data.start_time)
                      }} - {{
                          formatTimeSafely(item.content_info.origin_data.end_time) }}</span>
                    </div>
                    <div>
                      <img src="@/assets/img/local-class.png">
                      <span v-if="item.act_type === 5">QLEARNING在线直播</span>
                    </div>
                    <div>
                      <img src="@/assets/img/teacher-class.png">
                      <span>{{ teacherName(item.content_info.origin_data, 1) }}</span>
                      <span @click="handlerDialog(item.content_info)" class="details no-copy">查看详情</span>
                    </div>
                  </div>
                  <div v-if="item.act_type === 5" class="cardRadio">
                    <div @click="handelrActivityChangeRadio(3, item)" v-show="item.join_type === 0">
                      <div class="radius1"><span class="radius"></span><span
                          class="radiusLabel no-copy">勾选并提交，即可预约直播&回看</span>
                      </div>
                      <!-- <div class="color000">剩余名额：100</div> -->
                    </div>
                    <div @click="handelrActivityChangeRadio(0, item)" class="is_cheke" v-show="item.join_type === 3">
                      <div><img src="@/assets/img/is_cheke.png"><span class="isLabel no-copy">已选中，提交即可预约直播&回看</span>
                      </div>
                    </div>
                  </div>
                  <div v-if="item.act_type === 3 || item.act_type === 4" class="cardRadio">
                    <div @click="handelrActivityChangeRadio(1, item)"
                      v-show="item.join_type === 0 || item.join_type === 2">
                      <div><span class="radius"></span><span class="radiusLabel no-copy">线下参加</span></div>
                      <div class="color000 no-copy">剩余名额：100</div>
                    </div>
                    <div @click="handelrActivityChangeRadio(0, item)" class="is_chekes" v-show="item.join_type === 1">
                      <div><img src="@/assets/img/is_cheke.png"><span class="isLabel no-copy">线下参加</span></div>
                      <div class="colorfff no-copy">已报满，加入候补</div>
                    </div>
                    <div @click="handelrActivityChangeRadio(2, item)"
                      v-show="item.join_type === 0 || item.join_type === 1">
                      <div><span class="radius"></span><span class="radiusLabel no-copy">在线参加</span></div>
                      <!-- <div class="color000">剩余名额：100</div> -->
                    </div>
                    <div @click="handelrActivityChangeRadio(0, item)" class="is_chekes" v-show="item.join_type === 2">
                      <div><img src="@/assets/img/is_cheke.png"><span class="isLabel no-copy">在线参加</span></div>
                      <!-- <div class="colorfff">剩余名额：100</div> -->
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
        </div>
      </div>
      <!-- <div class="question-content" v-if="!isPreview">
        <div class="aggregate_head">
          *关联问卷：请填写以下问卷问题
        </div>
        <div class="task-content">
          <iframe v-if="isShowIframe" class="task-iframe" id="taskIframe" :src="questionUrl" frameborder="0"
            allowfullscreen></iframe>
          <div v-else class="task-error">
            <img class="error-img" src="@/assets/mooc-img/page-inaccessible.png" alt="无法访问">
          </div>
        </div>
      </div> -->
      <div class="submit">
        <div>
          <div>
            <div>
              <div><span class="but1 no-copy font_600">已选 {{ reg_contents.length }} 门内容</span><span
                  @click="viewSelectedContent(1)" class="but2">查看已选内容</span>
              </div>
              <div class="but3 no-copy">注意：使用全选功能时，如内容同时支持多种参加形式，将默认选中其中第一种形式</div>
            </div>
          </div>
          <div>
            <div class="no-copy">
              <div class="submitButtonTooltip " v-show="reg_contents.length === 0">
                <el-tooltip class="item" effect="dark" content="请选中内容后提交报名" placement="top">
                  <el-button disabled @click="viewSelectedContent(2)" class="but4">提交报名</el-button>
                </el-tooltip>
              </div>
              <el-button v-show="reg_contents.length > 0" @click="viewSelectedContent(2)" class="but4">提交报名</el-button>
              <el-button class="but5" @click="viewSelectedContent(3)" type="primary">一键全选并提交报名</el-button>
            </div>

          </div>
        </div>
      </div>
    </div>
    <introDialog :isShow.sync="introVisible" :intro="intro" />
    <!-- 报名弹窗 -->
    <el-dialog :title="viewTitle" :visible.sync="dialogVisibleApply" width="960px" class="logo-out-dialog face-dialog"
      @close="visibleApply(0)">
      <div class="dialogTitle no-copy">
        <div>你已选中 {{ reg_contents.length }} 门内容,是否确认提交</div>
        <div>注意：使用全选功能时，如内容同时支持多种参加形式，将默认选中其中第一种形式</div>
        <div><span>已选内容明细</span><span @click="dialogExpandCollapse = !dialogExpandCollapse">{{ !dialogExpandCollapse ?
          '展开明细' : '收起明细' }}</span></div>
      </div>
      <div v-show="dialogExpandCollapse">
        <el-table header-row-class-name="table-header-style" row-class-name="table-row-style" :data="paginatedData"
          style="width: 100%" class="agg-table">
          <el-table-column label="所属目录">
            <template slot-scope="scope">
              <span class="table-course-title">{{ scope.row.category_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="标题">
            <template slot-scope="scope">
              <span class="table-course-title">{{ scope.row.content_info.content_name || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column width="60" label="类型">
            <template slot-scope="scope">
              <span class="table-course-title">{{ scope.row.content_info.module_name || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="开班时间">
            <template slot-scope="scope">
              <span class="table-course-title">{{ scope.row.content_info.origin_data.start_time || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="参与形式">
            <template slot-scope="scope">
              <span class="table-course-title">{{ teachingList[scope.row.join_type - 1] }}</span>
            </template>
          </el-table-column>
          <el-table-column width="60" label="操作">
            <template slot-scope="scope">
              <div class="operat-btn-box">
                <el-link type="primary" @click="deleteRegContents(scope.row)" :underline="false">删除</el-link>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="currentPage"
          :page-sizes="[5]" :page-size="pageSize" layout="total,   prev,  pager,  next,   jumper"
          :total="reg_contents.length" class="pagination-wrapper">
        </el-pagination>
      </div>
      <div class="confirmbut" v-show="viewType !== 1">
        <el-button size="small" @click="dialogVisibleApply = false">取 消</el-button>
        <el-button size='small' @click="handelrConfirm()" type="primary">确认提交</el-button>
      </div>
    </el-dialog>
    <QrCodeDialog v-if="copyShow.show" :visible.sync="copyShow.show" :url="copyShow.url" :copyTitle="copyShow.title"
      isAppletQrCode :appletPath="copyShow.appletPath" :scene="copyShow.scene" />
    <div class="dialogBorder">
      <!-- 修改报名 -->
      <el-dialog title="修改报名" :visible.sync="dialogVisibleLogoOut" width="540px" class="logo-out-dialog face-dialog">
        <div class="modifyRegistration no-copy">
          <div>你此前曾通过此页面提交报名数据。如需修改报名，可继续访问此页面，修改选中内容后，点击提交</div>
          <div>是否需要再次访问此页面，以修改已提交的报名数据？</div>
        </div>
        <div class="confirmbut">
          <el-button size="small" @click="dialogVisibleLogoOut = false">取 消</el-button>
          <el-button size='small' @click="dialogVisibleLogoOut = false, dialogVisibleApply = true" type="primary">确
            认</el-button>
        </div>
      </el-dialog>
      <!-- 报名成功 -->
      <el-dialog title="提交成功" :visible.sync="successDialog" width="540px" class="logo-out-dialog face-dialog">
        <div class="modifyRegistration no-copy">
          <div>你已成功提交报名<br />
            如需追加报名，可再次访问页面进行追加</div>
        </div>
        <div class="confirmbut">
          <el-button size='small' @click="successOpen" type="primary">确
            认</el-button>
        </div>
      </el-dialog>
    </div>
    <div class="right">
      <!-- 右侧 -->
      <!-- <rightArea  ref="rightSideRef"></rightArea> -->
      <!-- 右侧图标模块 -->
      <rightSideIcon :modelTabValue="modelTabValue"></rightSideIcon>
    </div>
  </div>
</template>

<script>

import { getPolymerInfo, polymerRegist } from 'config/api.conf'
import { formatModuleMap } from 'utils/tools'
import introDialog from './components/introDialog.vue'
// import OteamRichText from '@/components/o-team-rich-text.vue'
// import rightArea from '@/views/user/activity/components/rightArea'
import rightSideIcon from '@/views/user/netcourse/grayPlay/components/rightSideIcon'
import env from 'config/env.conf.js'
import QrCodeDialog from '@/views/components/qrCodeDialog'
const envName = env[process.env.NODE_ENV]
export default {
  components: {
    // rightArea,
    rightSideIcon,
    // OteamRichText,
    introDialog,
    QrCodeDialog
  },
  data() {
    return {
      successDialog: false,
      formatModuleMap,
      dialogExpandCollapse: false,
      currentPage: 1, // 当前页码
      pageSize: 5, // 每页显示条数
      copyShow: {
        show: false,
        title: '',
        url: '',
        scene: '',
        appletPath: ''
      },
      teachingList: ['线下参加', '线上参加', '预约观看直播和回看'],
      modelTabValue: 'video_model',
      // testValue: JSON.stringify({
      //   version: 1,
      //   type: 'doc',
      //   content: [
      //     {
      //       type: 'paragraph',
      //       content: [
      //         {
      //           type: 'text',
      //           text: '具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此入此工作具体信息)与重要工作冲突(请输入此工作具体信息)具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)突(请输入此工作具体信息)与重要工作冲突(请输入此工作具体信息)'
      //         }
      //       ]
      //     }
      //   ]
      // }), // KM编辑器
      cardRadio: false,
      polymerInfo: {
        pc_bg_photo_url: '',
        pc_bg_color: '',
        brief: ''
      },
      intro: {},
      isPreviewMore: false,
      introVisible: false,
      isShowIframe: false,
      questionUrl: '',
      isMore: false,
      dialogVisibleApply: false,
      dialogVisibleLogoOut: false,
      form: {
        polymer_id: '',
        survey_id: '',
        reg_contents: []
      },
      viewTitle: '',
      reg_contents: [],
      viewType: '',
      polymer_registed: false, // 是否提交过报名数据
      editorConfig: {
        appId: 'mentor_management',
        catalogue: false,
        options: {
          // 非必传字段
          selector: '#course_make',
          menubar: false,
          block_formats: '标题1=h1; 标题2=h2; 标题3=h3; 正文=p',
          formats: {
            removeformat: [
              {
                selector:
                  'b,strong,em,i,font,u,strike,s,sub,sup,dfn,code,samp,kbd,var,cite,mark,q,del,ins,small',
                remove: 'all',
                split: true,
                block_expand: true,
                expand: false,
                deep: true
              },
              {
                selector: 'span',
                attributes: ['style', 'class'],
                remove: 'empty',
                split: true,
                expand: false,
                deep: true
              },
              {
                selector: '*',
                attributes: ['style', 'class'],
                split: false,
                expand: false,
                deep: true
              }
            ]
          },
          paste_data_images: true,
          plugins: [
            'noneditable advlist lists charmap paste print',
            'preview anchor visualblocks insertdatetime',
            'media table code wordcount formatpainter',
            'codesample'
          ],
          noneditable_noneditable_class: 'mceNonEditable',
          language: 'zh_CN',
          toolbar: `toc undo redo formatpainter removeformat |
                    formatselect fontsizeselect lineheight |
                    bold italic underline strikethrough |
                    forecolor backcolor |
                    dent align bullist numlist |
                    insert codesample |
                    fullScreenButton copyall`,
          toolbar_mode: 'wrap'
        },
        urlConfig: {
          operatesignature: `${location.protocol}${location.hostname.endsWith('.woa.com')
            ? process.env.VUE_APP_EDTUP_HOST_WOA
            : process.env.VUE_APP_EDTUP_HOST
          }/api/sso/know-service/api/businessCommon/common/content/operatesignature`, // 获取鉴权
          uploadSignatures: `${location.protocol}${location.hostname.endsWith('.woa.com')
            ? process.env.VUE_APP_EDTUP_HOST_WOA
            : process.env.VUE_APP_EDTUP_HOST
          }/api/sso/content-center/api/v1/content/file/uploadSignatures`, // 上传
          contentinfo: `${location.protocol}${location.hostname.endsWith('.woa.com')
            ? process.env.VUE_APP_EDTUP_HOST_WOA
            : process.env.VUE_APP_EDTUP_HOST
          }/api/sso/content-center/api/v1/content/{contentId}`, // 获取内容id
          preview: `${location.protocol}${location.hostname.endsWith('.woa.com')
            ? process.env.VUE_APP_EDTUP_HOST_WOA
            : process.env.VUE_APP_EDTUP_HOST
          }/api/sso/content-center/api/v1/content/imgage/{contentId}/preview` // 预览
        },
        is_open_catalogue: false
      }
    }
  },
  watch: {
    polymerInfo: {
      immediate: true,
      deep: true,
      handler(val) {
        if (val.brief) {
          // 添加窗口大小改变监听器
          this.$nextTick(() => { // 确保加载dom后执行
            setTimeout(() => { // 前面的方法无效继续加延迟确保渲染完成
              const wt = document.querySelector('.text-preview').offsetHeight
              if (wt > 360) {
                this.isPreviewMore = true
                this.isMore = true
              }
            }, 500)
          })
        }
      }
    }
  },
  computed: {
    // 计算分页后的数据
    paginatedData() {
      const start = (this.currentPage - 1) * this.pageSize
      const end = start + this.pageSize
      return this.reg_contents.slice(start, end)
    },
    polymer_id() {
      return this.$route.query.polymer_id || ''
    },
    styleBgColor() { // 背景色和默认背景色
      return `background-color: ${this.polymerInfo.pc_bg_color || '#F5F5F7'} `
    },
    teacherName() { // 讲师名称处理
      return (val, num) => {
        const { teacher_names } = val
        if (!teacher_names || typeof teacher_names !== 'string') {
          return ''
        }

        // 分割并过滤空值
        const names = teacher_names.split(';')
          .map(name => name.trim())
          .filter(name => name.length > 0)

        const count = names.length

        // 空数组处理
        if (count === 0) return ''

        // 不超过3人直接显示
        if (count <= num) return names.join(';')

        // 超过3人显示"张三等4人"格式
        return `${names[0]}等${count}人`
      }
    },
    // 活动禁用按钮
    disableDadioAct() {
      return (val, type) => {
        // const { my_status, join_type } = val
        return false
      }
    },
    // 是不是预览
    isPreview() {
      return !!this.$route.query.preview
    }
  },
  mounted() {
    // 监听loading变化
    // window.addEventListener('message', (e) => {
    //   // 来自问卷手动调用完成的方法
    //   const { event, type } = e.data
    //   if (type === 'questionnaire' && event === 'completeStatusUpdata') {
    //     this.handelrConfirm()
    //   }
    // })
    this.getPolymerInfo()
  },
  methods: {
    // 安全格式化时间，有秒时去掉秒，没有秒时保持原样
    formatTimeSafely(timeString) {
      if (!timeString) return '' // 处理null/undefined/空字符串

      // 检查是否包含秒部分（格式如 HH:mm:ss 或 H:mm:ss）
      const hasSeconds = /:\d{2}$/.test(timeString)

      // 如果有秒，去掉最后3个字符（冒号和2位秒数）
      return hasSeconds ? timeString.slice(0, -3) : timeString
    },
    // 分页大小变化
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1 // 重置到第一页
    },
    // 当前页变化
    handleCurrentChange(val) {
      this.currentPage = val
    },
    isMoreNextTick(item) {
      this.$nextTick(() => {
        item.listIsMore = !item.listIsMore
        this.$forceUpdate()
      })
    },
    viewSelectedContent(type) {
      this.viewType = type
      if (type === 1) {
        this.viewTitle = '已选内容'
        this.dialogExpandCollapse = true
      } else if (type === 2) {
        this.viewTitle = '确认报名'
        this.dialogExpandCollapse = false
      } else { // 全选全部目内容
        this.viewTitle = '确认报名'
        this.polymerInfo.category_contents.forEach(v => {
          v.selectAll = true
          this.handelrAllChangeRadio(true, v)
        })
      }
      if (type !== 1 && this.polymer_registed === true) {
        this.dialogVisibleLogoOut = true
      } else {
        this.dialogVisibleApply = true
      }
    },
    // 查看详情
    handlerDialog(val) {
      const newWindow = window.open('', '_blank')
      newWindow.location.href = val.content_url
    },
    successOpen() {
      const url = `${envName.courseWoaHost}user/home`
      window.location.href = url
    },
    async getPolymerInfo() {
      try {
        const data = await getPolymerInfo({ polymer_id: this.polymer_id })
        this.polymerInfo = data
        let { staff_id } = JSON.parse(sessionStorage.getItem('login_user'))
        this.reg_contents = []
        this.polymerInfo.category_contents = this.polymerInfo.category_contents.filter(v => {
          v.listIsMore = true
          v.selectAll = false
          v.contents = v.contents.filter(val => {
            if (!val.content_info) {
              return false // 删除该项
            }
            val.category_name = v.category_name
            val.join_type = val.polymer_join_type || 0
            // 是否提交过报名数据
            if (val.polymer_registed === true) this.polymer_registed = true
            if (val.join_type !== 0) {
              this.reg_contents.push(val)
            }
            return true
          })
          if (v.contents.length === 0) return false
          if (this.reg_contents.length === v.contents.length) {
            v.selectAll = true
          } else {
            v.selectAll = false
          }
          return true
        })
        let url = data.question.url || ''
        this.questionUrl = `${url}?user_id=${staff_id}&from=coregist&course_id=${this.polymer_id}&task_id=${this.polymer_id}`
        this.isShowIframe = true
      } catch (err) {
        if (err.code === 403 || err.code === 500) {
          sessionStorage.setItem('401Msg', err.message)
          this.$router.replace({
            name: '401'
          })
        }
      }
    },
    // 确认提交聚合报名
    handelrConfirm() {
      const { polymer_id, survey_id } = this.polymerInfo
      this.form.polymer_id = polymer_id
      this.form.survey_id = survey_id
      this.reg_contents.forEach(val => {
        this.form.reg_contents.push({
          act_id: val.act_id,
          act_type: val.act_type,
          content_name: val.content_info.content_name,
          join_type: val.join_type
        })
      })
      polymerRegist(this.form).then(res => {
        this.dialogVisibleApply = false
        this.successDialog = true
      }).catch((err) => {
        console.log(err)
        // this.isShowIframe = false
        // this.questionUrl = ''
        // // document.getElementById('taskIframe').contentWindow.location.reload(true)
        // setTimeout(() => {
        //   this.isShowIframe = true
        //   let url = this.polymerInfo.question.url
        //   let { staff_id } = JSON.parse(sessionStorage.getItem('login_user'))
        //   this.questionUrl = `${url}?user_id=${staff_id}&from=coregist&course_id=${this.polymer_id}&task_id=${this.polymer_id}`
        // })
      })
    },
    // 分享
    handleShare(row) {
      this.copyShow.title = row.polymer_name
      this.copyShow.url = `https://sdc.qq.com/s/YJEYHb?scheme_type=polymer&polymer_id=${row.polymer_id}`
      this.copyShow.scene = row.polymer_id
      this.copyShow.appletPath = 'pages/polymer/index'
      this.copyShow.show = true
    },
    // 全选/取消全选单个目录
    handelrAllChangeRadio(value, category) {
      category.selectAll = value
      category.contents.forEach(item => {
        if (value) {
          if (item.act_type === 5) {
            item.join_type = 3
          } else if (item.act_type === 3 || item.act_type === 4) {
            item.join_type = 1
          } else {
            item.join_type = 0
          }
        } else {
          item.join_type = 0
        }
      })
      this.updateRegContents()
      this.$forceUpdate()
    },

    // 单个活动报名方式变更
    handelrActivityChangeRadio(value, item) {
      item.join_type = value
      // 更新所在目录的全选状态
      const category = this.findCategoryByItem(item)
      if (category) {
        category.selectAll = this.checkIfAllSelected(category)
      }

      this.updateRegContents()
      this.$forceUpdate()
    },
    // 删除已选内容
    deleteRegContents(row) {
      const index = this.reg_contents.findIndex(item => item.act_id === row.act_id)
      if (index !== -1) {
        this.reg_contents.splice(index, 1)

        // 更新分页逻辑
        if (this.paginatedData.length === 0 && this.currentPage > 1) {
          this.currentPage -= 1
        }
        // 更新对应的join_type和全选状态
        const category = this.findCategoryByItem(row)
        if (category) {
          const item = category.contents.find(c => c.act_id === row.act_id)
          if (item) item.join_type = 0
          category.selectAll = false
        }
      }
      this.$forceUpdate()
    },

    // 更新reg_contents数组
    updateRegContents() {
      this.reg_contents = []
      this.polymerInfo.category_contents.forEach(category => {
        category.contents.forEach(item => {
          if (item.join_type !== 0) {
            this.reg_contents.push(item)
          }
        })
      })
    },

    // 检查目录下是否全部选中
    checkIfAllSelected(category) {
      return category.contents.every(item => item.join_type !== 0)
    },

    // 根据item查找所属目录
    findCategoryByItem(item) {
      return this.polymerInfo.category_contents.find(
        category => category.contents.some(c => c.act_id === item.act_id)
      )
    },
    visibleApply(type) {
      this.dialogVisibleApply = false
    },
    formatDateTimeStartEnd(start, end) {
      const dateTimeStart = new Date(start)
      const dateTimeEnd = new Date(end)
      const formattedDateStart =
        this.$moment(dateTimeStart).format('YYYY/MM/DD')
      const formattedDateEnd = this.$moment(dateTimeEnd).format('YYYY/MM/DD')
      const ltStart = this.$moment(dateTimeStart).format('HH:mm')
      const ltEnd = this.$moment(dateTimeEnd).format('HH:mm')
      const weekArr = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const weekdayStart = weekArr[dateTimeStart.getDay()] // 获取星期信息
      const weekdayEnd = weekArr[dateTimeEnd.getDay()] // 获取星期信息
      const startDateJoin = `${formattedDateStart}(${weekdayStart}) ${ltStart}`
      const endDateJoin = `${formattedDateEnd}(${weekdayEnd}) ${ltEnd}`
      return `${startDateJoin} - ${endDateJoin}` // 组合日期和星期信息并返回
    }
  }
}
</script>
<style lang="less">
.dialogBorder {
  /deep/ .el-dialog__header {
    border: none;
  }
}

.font_600 {
  font-weight: 600;
}

// 处理提示框的颜色
.sub-poper-class.el-tooltip__popper {
  background: rgba(255, 255, 255, 1) !important;
  color: #000;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
  border: none;
  width: 160px;

  &>div {
    color: gray !important;
  }
}

.sub-poper-class.el-tooltip__popper[x-placement^='top'] .popper__arrow:after,
.el-tooltip__popper[x-placement^='top'] .popper__arrow {
  border-top-color: #fff;
  opacity: 1;
}

// 处理箭头的颜色
.sub-poper-class.el-tooltip__popper .popper__arrow {
  border: none;
  background: rgba(255, 255, 255, 1) !important;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
}

.custom-need-appovel.el-message-box--center {
  .el-message-box__header {
    padding-top: 0;
  }

  .el-message-box__title {
    justify-content: flex-start !important;
  }

  .el-message-box__content {
    text-align: left !important;
    padding-left: 0;
    padding-right: 0;

  }

  .el-message-box__btns {
    text-align: right !important;
  }
}
</style>
<style lang="less" scoped>
.borderIsCheke {
  border: 2px solid var(---Brand8-Normal, #0052D9) !important;
}

img {
  user-select: none;
  /* 标准属性 */
  -webkit-user-select: none;
  /* Safari/Chrome */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* IE10+ */
}

.no-copy {
  user-select: none;
  /* 标准属性 */
  -webkit-user-select: none;
  /* Safari/Chrome */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* IE10+ */
}

.dialogTitle {
  margin: 0px 0 16px 0;
}

.radius1 {
  line-height: 42px !important;
}

.modifyRegistration {
  div:nth-child(1) {
    opacity: 0.8;
    color: #000000CC;

  }

  font-size:14px;

  div:nth-child(2) {
    opacity: 0.9;
    margin-top: 16px;
    color: #000000;
  }
}

.right {
  /deep/ .right-side-main {
    bottom: 100px;
  }
}

.dialogTitle {
  >div {
    margin-bottom: 8px;
  }

  div:nth-child(1) {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;

  }

  div:nth-child(2) {
    font-size: 14px;
    opacity: 0.6;
    color: #00000099;
  }

  div:nth-child(3) {
    font-size: 14px;

    span:nth-child(1) {
      margin-right: 20px;
      opacity: 0.9;
      color: #000000;
    }

    span:nth-child(2) {
      color: #0052D9;
      cursor: pointer;
    }
  }
}

.directory {
  padding: 24px;
  background-color: #fff;
  border-radius: 4px;
  margin-bottom: 20px;

  .span_nav {
    border-radius: 24px;
    padding: 8px 16px;
    background-color: #F6F6F6;
  }

}

.directoryTitle {
  display: flex;
  flex-wrap: wrap;
  height: 30px;
  position: relative;
  align-items: center;

  >span {
    margin-right: 24px;
    display: flex;
    align-items: center;
  }

  >span:nth-child(1) {
    font-weight: 600;
  }

  span:nth-child(2) {
    color: #000000e6;
    line-height: 26px;
  }

  span:nth-child(3) {
    color: #00000099;
  }

  span:nth-child(4) {
    color: #00000099;
  }

  .transparent-more {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);

    .more-text {
      color: #0052D9;
      cursor: pointer;

      i {
        margin-left: 5px;
      }
    }
  }

  .chekAll {
    display: inline-block;
    width: 110px;
    border-radius: 4px;
    height: 30px;
    background-color: #F5F8FF;
    vertical-align: middle;

    /deep/ .el-checkbox__inner::after {
      width: 3px;
      height: 6px;
    }

    /deep/ .el-checkbox__inner {
      border-radius: 50%;
    }

    /deep/ .el-checkbox__label {
      padding-left: 8px;
      color: #000000e6;
      vertical-align: middle;
    }

    /deep/ .el-checkbox__input {
      margin-left: 8px;
      vertical-align: middle;
    }

  }

  vertical-align: middle;

}

.directoryCard {
  margin-top: 20px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;

  >.options-item {
    .cardRadio {
      width: calc(100% - 24px);
      margin: 0 12px;
      margin-top: 8px;
      height: 56px;
      display: flex;
      font-size: 12px;
      gap: 12px;

      .radius {
        width: 16px;
        display: inline-block;
        height: 16px;
        vertical-align: middle;
        border-radius: 50%;
        border: 1px solid #0052D9;
        background-color: #fff;
        margin-right: 8px;
      }

      .colorfff {
        color: #fff;
        margin-top: 6px;
        line-height: 11px;
      }

      .radiusLabel {
        vertical-align: middle;
        color: #0052D9;
        font-size: 12px;
      }

      .is_chekes {
        background-color: #0052D9;
        color: #fff;
      }

      .is_cheke {
        background-color: #0052D9;
        color: #fff;

        >div {
          line-height: 42px;
        }
      }

      .isLabel {
        color: #fff;
        vertical-align: middle;
      }

      img {
        width: 16px;
        height: 16px;
        vertical-align: middle;
        margin-right: 8px;
      }

      >div {
        >div {
          line-height: 20px;

        }

        display: flex;
        flex-direction: column;
        /* 垂直排列 */
        justify-content: center;
        /* 垂直居中（默认） */
        align-items: center;
        /* 水平居中 */
        cursor: pointer;
        flex: 1;
        padding: 6px;
        text-align: center;
        background-color: #ECF2FE;
        border-radius: 4px;

        .color000 {
          margin-top: 6px;
          color: #00000066;
          line-height: 11px;
        }
      }
    }

    >img {
      height: 217px;
      width: 100%;
      border-radius: 8px 8px 0 0
    }

    .cardDetails {
      vertical-align: middle;
      padding: 8px;
      width: calc(100% - 24px);
      margin: 0 12px;
      border-radius: 4px;
      background-color: #F9F9F9;

      >div:last-of-type {
        margin-bottom: 0;

        span {
          width: 70%;
        }
      }

      >div {
        margin-bottom: 6px;

        img {
          width: 20px;
          height: 20px;
          margin-right: 12px;
        }

        a {
          width: 100%;
        }

        /deep/ .el-link--inner {
          width: 100%;
          text-align: left;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          vertical-align: middle;
        }

        span {
          color: #00000099;
          font-size: 12px;
          display: inline-block;
          cursor: pointer;
          width: 80%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          vertical-align: middle;
        }

        .details {
          width: auto !important;
          color: #0052D9;
          font-family: PingFang SC;
          letter-spacing: 2%;
          text-decoration: underline;
          text-decoration-style: solid;
          text-decoration-offset: 0%;
          text-decoration-thickness: 0%;

        }
      }

    }

    .cardContent {
      padding: 4px 12px;

      .cardTitle {
        width: 100%;
        height: 48px;
        vertical-align: middle;
        font-size: 16px;
        color: #333333;
        font-weight: 500;
        cursor: pointer;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        line-height: 24px;
        flex-shrink: 0;

        .title1 {
          color: #777777;
          display: inline-block;
          border-radius: 2px;
          vertical-align: middle;
          margin-bottom: 4px;
          background-color: #F5F5F7;
          padding: 0px 4px;
          height: 18px;
          line-height: 18px;
          box-sizing: border-box;
          font-size: 12px;
          margin-right: 8px;
        }
      }
    }

    border-radius: 11px;
    flex: 1 0 calc(25% - 20px);
    min-width: calc(25% - 20px);
    max-width: calc(25% - 20px);
    background: #fff;
    box-sizing: border-box;
    height: 439px;
    border: 2px solid #EEEEEE;
  }

  >.options-item:nth-child(4n + 1):nth-last-child(1) {
    flex-grow: 0;
  }
}

.directoryList {
  >.options-item:last-of-type {
    .item-content {
      padding-bottom: 0 !important;
      border: none !important;
    }
  }

  .options-item {
    padding-top: 20px;

    .title {
      display: flex;
      justify-content: space-between;

      .review-r {
        color: #fff;
        background-color: #ff001e;
        border-radius: 4px;
        font-size: 12px;
        padding: 2px;
        display: inline-block;
        margin: 0 8px;
      }

      .title_text {
        height: 18px;
        width: 90%;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .see_detail {
        color: #0052d9;
        font-family: 'PingFang SC';
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
        letter-spacing: 0.28px;
        padding: 0 8px;
        cursor: pointer;
      }
    }

    .item-content {
      display: flex;
      justify-content: space-between;
      padding: 12px 0 20px 0;
      border-bottom: 1px solid #eee;

      &_left {
        background: #F9F9F9;
        border-radius: 8px;
        padding: 10px 12px;
        flex: 1;

        .info-lable {
          display: flex;
          align-items: center;
          line-height: 22px;
          font-size: 14px;
          margin-bottom: 8px;

          .icon {
            display: block;
            width: 24px;
            height: 24px;
            margin-right: 12px;
          }

          /deep/.el-icon-warning {
            font-size: 18px;
            color: red;
            display: block;
            width: 24px;
            height: 24px;
            text-align: center;
            line-height: 24px;
            margin-right: 12px;
          }

          .icon-time {
            background: url('~@/assets/img/time-class.png') no-repeat center / cover;
          }

          .icon-name {
            background: url('~@/assets/img/teacher-class.png') no-repeat center / cover;
          }

          .icon-city {
            background: url('~@/assets/img/local-class.png') no-repeat center / cover;
          }
        }

        .info-lable:last-child {
          margin-bottom: 0;
        }
      }

      &_right {
        width: 324px;
        margin-left: 8px;
        background-color: #F9F9F9;
        border-radius: 8px;
        padding: 10px 12px;
        padding-left: 4px;

        .content_right-group {
          height: 100%;

          /deep/.el-radio {
            margin-left: 16px;
          }

          .content-right-group-radio {
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 8px;
            height: 100%;
          }
        }
      }
    }
  }
}

.submit {
  left: 0;
  height: 78px;
  position: fixed;
  bottom: 0;
  width: 100%;
  background: #FFF;
  display: flex;
  justify-content: center;

  /deep/ .is-disabled {
    color: #999 !important;
    border-color: #DCDCDC !important;
  }

  .submitButtonTooltip {
    display: inline-block;
  }

  >div {
    width: 1420px;
    height: 100%;
    display: flex;

    .but4 {
      border: 1px solid #0052D9;
      color: #0052D9;
    }

    button {
      font-size: 16px;
      width: 200px;
    }

    .but5 {
      color: #ffffffe6;
    }

    >div:first-child {
      justify-content: flex-start;

      .but1 {
        opacity: 0.9;
        color: #000;
        font-size: 16px;
        margin-right: 20px;
      }

      .but2 {
        font-size: 14px;
        color: #0052D9;
        cursor: pointer;
      }

      .but3 {
        font-size: 14px;
        color: #000;
        opacity: 0.6;
        margin-top: 8px;
      }

    }

    >div:last-child {
      justify-content: end;
    }

    >div {
      flex: 1;
      display: flex;
      align-items: center;

    }
  }

}

// input[aria-hidden="true"] {
//     display: none !important;
// }
// /deep/.el-radio__original {
//     display: none !important; /* 隐藏原生 radio 输入,但仍然允许交互 */
// }
.color005 {
  color: #0052d9;
}

.aggregate-page {
  position: relative;

  .aggregate-head {
    width: 100%;
    display: flex;
    justify-content: center;

    img {
      max-height: 192px;
      width: 1420px;
    }
  }

  /deep/ .el-dialog__body {
    padding: 24px 32px;
  }

  /deep/ .el-pagination__jump {
    margin-left: 12px;
  }

  /deep/.el-table {
    border-radius: 8px !important;

    .table-header-style th {
      background: #F5F5F5;
      color: #00000099;
    }

    .cell {
      padding: 0 16px;
    }
  }

  .confirmbut {
    display: flex;
    justify-content: flex-end;
    margin-top: 24px;
  }

  .pointer {
    cursor: pointer;
  }

  .aggregate-bg {
    // height: 300px;
    background-position: 50%;
    background-size: 100% 100%;
    background-repeat: no-repeat;
    display: flex;
    justify-content: center;
  }

  min-height: calc(100%);

  .aggregate-content {

    // background-color: #fff;
    >div {
      // background-color: #F5F5F7;
    }

    width: 1420px;
    height: 100%;
    margin: auto;
    position: relative;
    padding: 0px 0 70px 0;

    &_top {
      padding: 24px 32px 11px 32px;
      border-radius: 8px 8px 0 0;
      background-color: #fff;

      .top_title {
        align-self: stretch;
        color: #333333;
        font-family: 'PingFang SC';
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: 32px;
        letter-spacing: 0.48px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
        border-radius: 8px;
      }

      .info-item {
        color: #00000099;
        font-family: 'PingFang SC';
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0.32px;
        display: flex;
        padding-top: 10px;

        span {
          margin-right: 20px;
          display: flex;
          align-items: center;
        }

        .icon-shear {
          background: url('~@/assets/img/share-one.png') no-repeat center/cover;
        }

        i {
          display: inline-block;
          margin-right: 4px;
          width: 16px;
          height: 16px;
        }
      }
    }

    .desc-text {
      padding: 16px 24px;
      background-color: #fff;
      position: relative;
      border-radius: 0 0 8px 8px;

      .desc-content {
        padding: 16px;
        border-radius: 8px;
        background: #f6f6f6;
        height: 144px;
        overflow: hidden;
        position: relative;

        /deep/ .content-wrapper {
          background-color: #f6f7f9;
        }

        /deep/.o-team-rich-text-container {
          background: #f6f6f6;
          height: auto;
          width: auto;
          border: none;
        }
      }

      .transparent-more {
        height: unset;
        overflow: unset;
        height: 50px;
        line-height: 50px;
        text-align: center;
        flex-shrink: 0;
        background-image: linear-gradient(0deg,
            #fff 0,
            #fff 30%,
            rgba(255, 255, 255, 0.8) 65%,
            rgba(255, 255, 255, 0) 100%);
        position: absolute;
        bottom: -15px;
        left: 0;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: flex-end;
        cursor: pointer;

        .more-text {
          color: #0052D9;
          cursor: pointer;

        }
      }

      .more-des-content {
        height: unset;
        overflow: unset;

        .text-preview {
          padding-bottom: 24px;
        }
      }

      .pack-des-content {
        background-image: unset;
        bottom:0;
      }
    }
  }

  .options-class {
    padding-bottom: 16px;

    .aggregate_head {
      img {
        vertical-align: middle;
        display: inline-block;
        text-align: center;
        width: 14px;
        height: 14px;
        margin-right: 5px;
        margin-left: 13px;
      }

      border-radius: 4px;
      line-height: 20px;
      margin: 12px 0;

      span {
        vertical-align: middle;
      }

      background: #ffffffb3;
      height: 46px;
      line-height: 43px;

      color: #0052D9;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 400;
    }

  }

  .question-content {
    border-radius: 0 0 8px 8px;
    background-color: #fff;

    .aggregate_head {
      color: #333333;
      font-family: 'PingFang SC';
      font-size: 16px;
      font-style: normal;
      font-weight: 600;
      line-height: 24px;
      padding: 16px;
      line-height: 20px;

      span {
        vertical-align: middle;
      }
    }
  }

  .task-content {
    position: relative;
    height: 100%;

    .task-iframe {
      width: 100%;
      height: 100vh;
    }
  }

  .task-error {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    text-align: center;

    .error-img {
      width: 240px;
      height: 148px;
    }

    .tips {
      font-size: 18px;
      font-weight: 600;
      margin: 24px 0;
    }
  }

  .logo-out-dialog {
    .red-fw {
      color: #fe3733;
      font-weight: 600;
    }

    .face-dialog-flex {
      display: flex;

      .el-icon-warning {
        color: #e6a23c;
        font-size: 24px;
        margin-right: 16px;
        margin-top: 3px;
      }
    }

    .apply-dialog {
      line-height: 28px;
      font-size: 16px;
      color: #00000099;

      .right {
        flex: 1;
      }

      ol {
        list-style: decimal;
        padding-left: 20px;

        li {
          list-style: decimal;
        }
      }

      .manage {
        width: 100%;
        border-top: 1px solid #eee;

        .title {
          font-size: 16px;
          font-weight: 500;
          color: #000;
          padding: 10px 0;
        }
      }

      .comfir-tips {
        color: #000000e6;
        font-family: 'PingFang SC';
        font-size: 16px;
        font-style: normal;
        font-weight: 500;
        line-height: 28px;
        padding: 10px 0;
      }

      .managerName {
        display: flex;
        align-items: center;
        margin-bottom: 14px;

        /deep/.sdc-staff-selector {
          width: 450px;
        }
      }

      .apply-type {
        padding: 16px;
        background: #f5f7f9;
        padding-top: 8px;
        text-align: left;
        margin-top: 10px;
        color: #000000e6;
      }

      .p0 {
        color: #ff8200;
        display: block;
        cursor: pointer;
      }

      .p1 {
        display: block;
        cursor: pointer;

        .details-sapn {
          margin-right: 10px;
        }

        span {
          a {
            color: #3464e0;
          }
        }
      }
    }

    .Reason-sel {
      margin: 10px 0;
      // font-size: 16px;
      // font-weight: 500;
    }

    .redio-content {
      margin: 10px 0;
    }
  }
}
</style>
