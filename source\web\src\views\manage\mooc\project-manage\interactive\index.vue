<template>
  <div class="interactive-manage">
    <el-tabs v-model="currentTab">
      <el-tab-pane label="公告管理" name="noticeManage">
        <notice-manage v-if="currentTab === 'noticeManage'"></notice-manage>
      </el-tab-pane>
      <el-tab-pane label="评论管理" name="commentManage">
        <comment-manage v-if="currentTab === 'commentManage'"></comment-manage>
      </el-tab-pane>
      <el-tab-pane label="评分详情" name="scoreDetails">
        <score-details v-if="currentTab === 'scoreDetails'"></score-details>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import NoticeManage from './notice-manage.vue'
import CommentManage from './comment-manage.vue'
import ScoreDetails from './score-details.vue'

export default {
  components: {
    NoticeManage,
    CommentManage,
    ScoreDetails
  },
  data () {
    return {
      currentTab: 'noticeManage'
    }
  },
  created () {
    if (this.$route.query.tab) {
      this.currentTab = this.$route.query.tab
    }
  }
}
</script>

<style lang="less" scoped>
  .interactive-manage {
    height: 100%;
    background: #fff;
  }
  :deep(.el-tabs) {
    height: 100%;
    display: flex;
    flex-direction: column;
    .el-tabs__header {
      padding: 20px 20px 0;
      margin: 0;
      border-bottom: 1px solid #f3f3f3ff;
    }
    .el-tabs__content {
      flex: 1;
      padding: 20px;
      overflow: auto;
    }
  }
</style>
